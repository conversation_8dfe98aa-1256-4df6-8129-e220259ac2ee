package com.tzx.report.po.springjdbc.dao.report;


import net.sf.json.JSONObject;

/**
 * Created by zds on 2018-11-06.
 */
public interface RequestUrlCountDao {
    String NAME = "com.tzx.report.po.springjdbc.dao.imp.report.RequestUrlCountDaoImpl";
    void saveCount(JSONObject jsonObject) throws Exception;
    void updateCount(JSONObject jsonObject) throws Exception;
    void updateCount(String tenancyId,String hashUrl) throws Exception;
    int getCountByUrl(String tenancyId,String hashUrl) throws Exception;
}
