package com.tzx.task.po.redis.dao.imp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import net.sf.json.JSONObject;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.task.po.redis.dao.TaskRedisDao;

@Repository(TaskRedisDao.NAME)
public class TaskRedisDaoImpl implements TaskRedisDao
{

	@Resource(name="saasRedisTemplate")
	private RedisTemplate<String, JSONObject>	redisTemplate;
	
	@Resource(name="saasRedisTemplate")
	private RedisTemplate<String, Object>	redisTemplatekk;
	
	private static final String				hqmaintaskkey = "hqmaintaskkey_b";
	
	private static final String				hqmaintaskkeywake = "hqmaintaskkey_wake";
	
	private static final String 			publictaskkey 	= "publicautotasklist";
	
	//private static final String 			publictaskkeywake 	= "publicautotasklistwake";
	
	@Override
	public void save(final byte[] type, final JSONObject jsonObject)
	{
		redisTemplate.execute(new RedisCallback<Object>()
		{
			@Override
			public Object doInRedis(RedisConnection connection) throws DataAccessException
			{
				connection.rPush(type, redisTemplate.getStringSerializer().serialize(jsonObject.toString()));

				return null;
			}
		});

	}

	@Override
	public JSONObject read(final byte[] key)
	{
		return redisTemplate.execute(new RedisCallback<JSONObject>()
		{
			@Override
			public JSONObject doInRedis(RedisConnection connection) throws DataAccessException
			{
				if (connection.exists(key))
				{
					byte[] value = connection.rPop(key);
					String obj = redisTemplate.getStringSerializer().deserialize(value);
					// System.out.println("取出的值："+obj);
					JSONObject jo = JSONObject.fromObject(obj);
					return jo;
				}
				return null;
			}
		});

	}

	@Override
	public void clear(final byte[] key)
	{
		redisTemplate.execute(new RedisCallback<Object>()
		{
			public Object doInRedis(RedisConnection connection)
			{
				connection.del(key);
				return null;
			}
		});

	}

	@Override
	public List<JSONObject> get(final byte[] key)
	{
		return redisTemplate.execute(new RedisCallback<List<JSONObject>>()
		{
			@Override
			public List<JSONObject> doInRedis(RedisConnection connection) throws DataAccessException
			{
				List<JSONObject> list = new ArrayList<JSONObject>();

				while (connection.exists(key) && list.size() < 10000)
				{
					byte[] value = connection.rPop(key);
					String obj = redisTemplate.getStringSerializer().deserialize(value);
					JSONObject jo = JSONObject.fromObject(obj);
					list.add(jo);
				}
				return list;
			}
		});
	}

	@Override
	public boolean exists(final byte[] key)
	{
		return redisTemplate.execute(new RedisCallback<Boolean>()
		{
			@Override
			public Boolean doInRedis(RedisConnection connection) throws DataAccessException
			{

				return connection.exists(key);
			}
		});

	}

	@Override
	public Long len(final byte[] key)
	{

		return redisTemplate.execute(new RedisCallback<Long>()
		{
			@Override
			public Long doInRedis(RedisConnection connection) throws DataAccessException
			{

				return connection.lLen(key);
			}
		});
	}

	@Override
	public void getMainTaskAndPushTask(final byte[] key, final byte[] key1)
	{
		redisTemplate.execute(new RedisCallback<Boolean>()
		{
			@Override
			public Boolean doInRedis(RedisConnection connection) throws DataAccessException
			{
				Long indexz = connection.lLen(key);

				Long indext = connection.lLen(key1);

				Long kl = indexz - 1;

				Long kl1 = indext - 1;

				if (indexz > 0)
				{

					List<byte[]> list = connection.lRange(key, 0, kl);

					List<byte[]> list1 = new ArrayList<byte[]>();

					if (indext > 0)
					{
						list1 = connection.lRange(key1, 0, kl1);
					}

					List<JSONObject> list01 = new ArrayList<JSONObject>();
					int i1 = -1;
					for (byte[] b : list)
					{
						i1++;
						String obj = redisTemplate.getStringSerializer().deserialize(b);
						// System.out.println("取出的值："+obj);
						JSONObject jo2 = JSONObject.fromObject(obj);
						jo2.put("index", i1);
						list01.add(jo2);
					}

					List<JSONObject> list02 = new ArrayList<JSONObject>();
					int i2 = -1;
					for (byte[] b1 : list1)
					{
						String obj = redisTemplate.getStringSerializer().deserialize(b1);
						// System.out.println("取出的值："+obj);
						JSONObject jo2 = JSONObject.fromObject(obj);
						jo2.put("index", i2);
						list02.add(jo2);
					}
					// List<JSONObject> add1 = new ArrayList<JSONObject>();
					//
					//List<JSONObject> update = new ArrayList<JSONObject>();
					
					for (JSONObject joo1 : list01)
					{
						
						
						String[] times = joo1.optString("time").split(",");
						
						for(String time : times)
						{
							int flag = 0;
							for (JSONObject joo2 : list02)
							{
								if (!"".equals(joo1.optString("tenancy_id")) && joo1.optString("tenancy_id").equals(joo2.optString("tenancy_id")))
								{
									
									// joo2.put("uuid",joo1.optString("uuid"));
									if (!"".equals(joo1.optString("uuid")) && joo1.optString("uuid").equals(joo2.optString("uuid")))
									{
										if(time.equals(joo2.optString("tasktime")))
										{	
											flag ++;
											
										}
									} 
								}

							}
							if (flag == 0)
							{
								if(joo1.optInt("state")==1)
								{
									joo1.put("indext", indext);
									//joo1.put("state", 1);
									joo1.put("tasktime",time);
									save(key1, joo1);
									indext++;
								}
								
							}
							
							
							
						}
						
						
					}

				}

				return true;
			}
		});

	}

	@Override
	public void clearIdentificationTask(final byte[] key)
	{

		if (Constant.getSystemMap().containsKey("identification"))
		{

		}
		// TODO Auto-generated method stub

	}

	@Override
	public void clearOutOfDateInformation(final byte[] key)
	{
		// TODO Auto-generated method stub

	}

	@Override
	public List<JSONObject> getTaskList(final byte[] key) 
	{
		return redisTemplate.execute(new RedisCallback<List<JSONObject>>()
		{
			@Override
			public List<JSONObject> doInRedis(RedisConnection connection) throws DataAccessException
			{
				List<JSONObject> listreturn = new ArrayList<JSONObject>();

				Long indexz = connection.lLen(key);

				Long kl = indexz - 1;

				if (indexz > 0)
				{

					List<byte[]> list = connection.lRange(key, 0, kl);
					int size = 0;
					int i1 = -1;
					
					String  now1 = DateUtil.getNowDateHHMM();
					Date now = new Date();;
					try
					{
						now = DateUtil.parseDateHHMM(now1);
					}
					catch (Exception e1)
					{
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}
					for (byte[] b : list)
					{
						i1++;
						if (size < 10)
						{
							String obj = redisTemplate.getStringSerializer().deserialize(b);
							// System.out.println("取出的值："+obj);
							JSONObject jo2 = JSONObject.fromObject(obj);
							if (jo2.optInt("state") == 1)
							{
								Date tasktime;
								try
								{
									tasktime = DateUtil.parseDateHHMM(jo2.optString("tasktime"));
									if(!now.before(tasktime))
									{
										listreturn.add(jo2);
										size++;
									}
								}
								catch (Exception e)
								{
									// TODO Auto-generated catch block
									//e.printStackTrace();
								}
								
								
								//jo2.put("index", i1);
								
							}
						}
					}

				}

				return listreturn;
			}
		});
	}

	@Override
	public void update(final byte[] key,final Long index,final JSONObject jo1)
	{
		redisTemplate.execute(new RedisCallback<Object>()
		{
			@Override
			public Object doInRedis(RedisConnection connection) throws DataAccessException
			{
				
				connection.lSet(key, index,redisTemplate.getStringSerializer().serialize(jo1.toString()) );

				return null;
			}
		});
		
	}

	@Override
	public void lpush(final byte[] type, final JSONObject jsonObject)
	{
		redisTemplate.execute(new RedisCallback<Object>()
		{
			@Override
			public Object doInRedis(RedisConnection connection) throws DataAccessException
			{
				connection.lPush(type, redisTemplate.getStringSerializer().serialize(jsonObject.toString()));
				return null;
			}
		});

	}

	@Override
	public String getKey(final String key)
	{
		return redisTemplatekk.execute(new RedisCallback<String>()
		{
			@Override
			public String doInRedis(RedisConnection connection) throws DataAccessException
			{
				if(connection.exists(key.getBytes()))
				{
					return new String(connection.get(key.getBytes()));
				}
				else
				{
					return null;
				}
				
			}
		});
	}
	
	public void append(final String key,final String value)
	{
		redisTemplatekk.execute(new RedisCallback<Object>()
		{
			@Override
			public Object doInRedis(RedisConnection connection) throws DataAccessException
			{
				connection.append(key.getBytes(),value.getBytes());
				return null;
			}
		});
	}
	
	@Override
	public void setKey(final String key, final String value)
	{
		redisTemplatekk.execute(new RedisCallback<Object>()
		{
			@Override
			public Object doInRedis(RedisConnection connection) throws DataAccessException
			{
				connection.set(key.getBytes(), value.getBytes());
				return null;
			}
		});
	}

	@Override
	public boolean containskey(String key)
	{
		return redisTemplatekk.hasKey(key);
	}

	@Override
	public void dishmain(String tenancyID)
	{
		if(tenancyID!=null && tenancyID.length()>0)
		{
			String key = hqmaintaskkey+"_"+tenancyID;
			if(!redisTemplatekk.hasKey(key))
			{
				redisTemplatekk.opsForValue().set(key, "1");
				append(hqmaintaskkey, "_"+tenancyID);
			}
			else
			{
//				System.out.println("qqqq");
//				System.out.println(getKey(hqmaintaskkey));
//				
				
			}
		}
	}
	
	@Override
	public void wakeupmain(String tenancyID)
	{
		if(tenancyID!=null && tenancyID.length()>0)
		{
			String key = hqmaintaskkeywake+"_"+tenancyID;
			if(!redisTemplatekk.hasKey(key))
			{
				redisTemplatekk.opsForValue().set(key, "1");
				append(hqmaintaskkeywake, "_"+tenancyID);
			}
			else
			{
//				System.out.println("qqqq");
//				System.out.println(getKey(hqmaintaskkey));
//				
				
			}
		}
	}
	
	@Override
	public void plpush(String tenancyID, JSONObject jb)
	{
		try
		{
			lpush(publictaskkey.getBytes(), jb);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		
	}

	@Override
	public boolean haskeyS(final String key)
	{
		return redisTemplatekk.execute(new RedisCallback<Boolean>()
		{
			@Override
			public Boolean doInRedis(RedisConnection connection) throws DataAccessException
			{
				return connection.exists(key.getBytes());
			}
		});
	}
}
