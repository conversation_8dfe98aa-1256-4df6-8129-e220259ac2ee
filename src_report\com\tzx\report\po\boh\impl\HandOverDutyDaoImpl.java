package com.tzx.report.po.boh.impl;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.HandOverDutyDao;
import net.sf.json.JSONObject;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository(HandOverDutyDao.NAME)
public class HandOverDutyDaoImpl implements HandOverDutyDao{
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	 
	@Override
	public List<JSONObject> getHandOverDutyQuery(String tenancyID,JSONObject condition) throws Exception {
		JSONObject result = new JSONObject();
		List<JSONObject> list = new ArrayList<JSONObject>();
		String report_date_begin = condition.optString("p_report_date_begin");
		String report_date_end = condition.optString("p_report_date_begin");

		String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.HAND_OVER_DUTY_01);
		if(report_date_begin.length()>0 && report_date_end.length()>0 ) {
			String f_tmp_storejbcx = null;
			if(!("").equals(reportSql)){
				SqlRowSet column = dao.query(tenancyID, reportSql.toString());
				if(column.next()){
					f_tmp_storejbcx = column.getString("pos_storejbcx_fun");
					list = this.dao.query4Json(tenancyID, f_tmp_storejbcx.toString());
				}
			}
		}
		return list;
	}
}
