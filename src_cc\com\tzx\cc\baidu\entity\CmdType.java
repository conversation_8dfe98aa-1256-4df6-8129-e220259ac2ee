package com.tzx.cc.baidu.entity;

/**
 * <AUTHOR>
 *
 */
public enum CmdType {
	/**
	 * 商户列表
	 */
	SHOP_LIST,//"shop.list",
	/**
	 * 商户信息
	 */
	SHOP_GET,//"shop.get",
	/**
	 * 商户资质上传
	 */
	SHOP_PIC_UPLOAD,//"shop.pic.upload",
	/**
	 * 创建商户
	 */
	SHOP_CREATE,//"shop.create",
	/**
	 * 下线商户
	 */
	SHOP_OFFLINE,//"shop.offline",
	/**
	 * 修改商户
	 */
	SHOP_UPDATE,//"shop.update";
	/**
	 * 开始营业
	 */
	SHOP_OPEN,//shop.open
	/**
	 * 暂停营业
	 */
	SHOP_CLOSE,//shop.close
	
	/**
	 * 新增菜品分类
	 */
	DISH_CATEGORY_CREATE,//"dish.category.create";
	
	/**
	 * 修改菜品分类
	 */
	DISH_CATEGORY_UPDATE,//"dish.category.update";
	
	/**
	 * 查询菜品分类
	 */
	DISH_CATEGORY_ALL,//"dish.category.all";
	/**
	 * 菜品上传
	 */
	DISH_CREATE,//"dish.create";
	
	/**
	 * 菜品修改
	 */
	DISH_UPDATE,//"dish.update";
	
	/**
	 * 菜品上线
	 */
	DISH_ONLINE,//"dish.online";
	
	/**
	 * 菜品下线
	 */
	DISH_OFFLINE,//"dish.offline";
	
	/**
	 * 菜品删除
	 */
	DISH_DELETE,//"dish.delete";
	
	/**
	 * 确认订单
	 */
	ORDER_CONFIRM,//order.confirm
	/**
	 * 取消订单
	 */
	ORDER_CANCEL,//order.cancel
	/**
	 * 完成订单
	 */
	ORDER_COMPLETE,//order.complete
	/**
	 * 查询订单状态
	 */
	ORDER_STATUS_GET,//order.status.get
	/**
	 * 查询订单详情
	 */
	ORDER_GET,//order.get
	/**
	 * 查询菜品信息
	 */
	DISH_SHOW,//dish.show
	/**
	 * 创建订单(返回用)
	 */
	RESP_ORDER_CREATE,//resp.order.create
	/**
	 * 订单状态查询(返回用)
	 */
	RESP_ORDER_STATUS_GET,//resp.order.status.get
	/**
	 * 订单状态推送(返回用)
	 */
	RESP_ORDER_STATUS_PUSH,//resp.order.status.push
	/**
	 * 订单列表
	 * */
	ORDER_LIST
	
}
