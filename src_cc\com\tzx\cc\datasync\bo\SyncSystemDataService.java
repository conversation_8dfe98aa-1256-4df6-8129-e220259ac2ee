package com.tzx.cc.datasync.bo;

import com.tzx.framework.bo.dto.OrgMapping;
import net.sf.json.JSONObject;

import java.util.List;

public interface SyncSystemDataService
{
	String	NAME = "com.tzx.cc.bo.imp.SyncSystemDataServiceImpl";
	
	public  JSONObject dataTransfer(JSONObject serviceParams) throws Exception;

	String dataSourceSave(String tenantId, String tableKey, JSONObject data) throws Exception;


    JSONObject dataSourceLoad(String tenancyID, JSONObject condition) throws Exception;

    /**
     * 获取RIF机构树据
     * @param p
     * @return
     * @throws Exception
     */
    String getRifOrgData(JSONObject p) throws Exception;

    JSONObject ping(String tenentid, JSONObject p);

    List<JSONObject> getChanelInfo(String tenancyId) throws Exception;
}
