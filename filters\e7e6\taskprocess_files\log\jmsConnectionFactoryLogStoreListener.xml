<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
	      http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	      http://www.springframework.org/schema/context
          http://www.springframework.org/schema/context/spring-context-3.0.xsd
          http://www.springframework.org/schema/beans
          http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
          http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd 
          http://www.springframework.org/schema/tx 
		  http://www.springframework.org/schema/tx/spring-tx.xsd">
		  
    <!-- 属性文件读入 -->
    <bean id="propertyConfigurerLogStoreListener" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				 <value>classpath*:messageConfig.properties</value>  
			</list>
		</property>
	</bean>  

	<!-- 真正可以产生Connection的ConnectionFactory，由对应的 JMS服务厂商提供 -->
	<bean id="targetConnectionFactoryLogStoreListener" class="org.apache.activemq.ActiveMQConnectionFactory">
		<property name="brokerURL" value="${mqUrl}" />
	</bean>

	<!-- 通过往里面注入一个ActiveMQConnectionFactory可以用来将Connection、Session和MessageProducer池化，这样可以大大的减少我们的资源消耗。 -->
	<bean id="pooledConnectionFactoryLogStoreListener" class="org.apache.activemq.pool.PooledConnectionFactory">
		<property name="connectionFactory" ref="targetConnectionFactoryLogStoreListener" />
		<property name="maxConnections" value="10" />
		<!--<property name="maximumActive" value="10" />-->
	</bean>

	<!-- ConnectionFactory是用于产生到JMS服务器的链接的，Spring为我们提供了多个ConnectionFactory，有SingleConnectionFactory和CachingConnectionFactory。 
		SingleConnectionFactory对于建立JMS服务器链接的请求会一直返回同一个链接，并且会忽略Connection的close方法调用。 
		CachingConnectionFactory继承了SingleConnectionFactory， 所以它拥有SingleConnectionFactory的所有功能，同时它还新增了缓存功能，它可以缓存Session、MessageProducer和MessageConsumer。 -->
	<!-- Spring用于管理真正的ConnectionFactory的ConnectionFactory -->
	<bean id="connectionFactoryLogStoreListener"
		class="org.springframework.jms.connection.CachingConnectionFactory">
		<!-- 目标ConnectionFactory对应真实的可以产生JMS Connection的ConnectionFactory -->
		<property name="targetConnectionFactory" ref="pooledConnectionFactoryLogStoreListener" />
	</bean>
	
	<!--这个是队列目的地，点对点的-->
    <bean id="queueDestinationLogStoreListener" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg>
            <value>${mqName}</value>
        </constructor-arg>
    </bean>
    
    <!-- 消息监听器 -->  
	<bean id="consumerMessageListenerLogStoreListener" class="com.tzx.dfs.mq.listener.ConsumerMessageStoreListener"/> 
	
	<!-- 消息监听容器 -->  
	<bean id="jmsContainerLogStoreListener" class="org.springframework.jms.listener.DefaultMessageListenerContainer">  
	    <property name="connectionFactory" ref="connectionFactoryLogStoreListener" />  
	    <property name="destination" ref="queueDestinationLogStoreListener" />  
	    <property name="messageListener" ref="consumerMessageListenerLogStoreListener" />  
	</bean>

</beans>
