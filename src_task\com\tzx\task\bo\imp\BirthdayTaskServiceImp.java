package com.tzx.task.bo.imp;

import com.tzx.crm.base.Constant;
import com.tzx.crm.base.constant.CrmCode;
import com.tzx.crm.base.dao.HrBaseDao;
import com.tzx.crm.bo.BusinessSmsService;
import com.tzx.crm.bo.CouponsService;
import com.tzx.crm.bo.CrmCodeService;
import com.tzx.crm.bo.TransactionMessageSetService;
import com.tzx.framework.bo.CodeService;
import com.tzx.framework.bo.SendSmsService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.po.redis.dao.RedisTaskDao;
import com.tzx.newcrm.bo.MarketActivityService;
import com.tzx.newcrm.bo.WxMessageSendService;
import com.tzx.task.bo.BirthdayTaskService;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service(BirthdayTaskService.NAME)
public class BirthdayTaskServiceImp implements BirthdayTaskService
{
	@Resource(name = "hrBaseDaoImpl")
	private HrBaseDao						dao;

	private static final Logger	log	= Logger.getLogger(BirthdayTaskServiceImp.class);
	
	@Resource(name = RedisTaskDao.NAME)
	private RedisTaskDao					redisTaskDao;

	@Resource(name = "codeService")
	private CodeService						codeService;

	@Resource(name = "crmCodeService")
	private CrmCodeService					crmCodeService;

	@Resource(name = SendSmsService.NAME)
	private SendSmsService					sendSmsService;

	@Resource(name = BusinessSmsService.NAME)
	private BusinessSmsService				businessSmsService;

	@Resource(name = TransactionMessageSetService.NAME)
	private TransactionMessageSetService	transactionMessageSetService;

	@Resource(name = CouponsService.NAME)
	private CouponsService 			couponsService;
	@Resource(name = WxMessageSendService.NAME)
	private WxMessageSendService wxMessageSendService;
	@Resource(name=MarketActivityService.NAME)
	private MarketActivityService marketActivityService;
	
	private Logger logger = Logger.getLogger(this.getClass().getName());
	
	private static String BIRTHDAY_TASK_REDIS_LOCK = "birthday_task_redis_lock_";
	@Autowired
	private RedissonClient redissonClient;

	@Override
	public void task(String tenancyId, JSONObject jb) throws Exception
	{

		RLock lock = redissonClient.getLock(BIRTHDAY_TASK_REDIS_LOCK+tenancyId);
		try{
			lock.tryLock(5, TimeUnit.MINUTES);
			
			logger.info("开始执行生日营销定时任务:"+tenancyId+",time:"+System.currentTimeMillis());
			Date date = new Date();
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			String td = df.format(date);
			// 根据日期获取所有过生日的会员
			StringBuilder birthdayMemberSql = new StringBuilder();
			birthdayMemberSql.append("select * from (select max(cc.id) as aa from crm_coupons cc left join crm_coupons_type cct on cc.type_id = cct.id where (cc.send_time BETWEEN '" + td + " 00:00:00' and '" + td
					+ " 24:00:00') and cct.activity_id in(select ca.id from crm_activity  ca where ca.activity_type='birthday' and ca.valid_state='1' and ca.running_state='1')) as cc LEFT JOIN (select max(ccc.id) as bb  from crm_customer_credit ccc where ccc.business_date='" + td
					+ "'  and ccc.activity_id in(select ca.id from crm_activity  ca where ca.activity_type='birthday' and ca.valid_state='1' and ca.running_state='1')) as dd on 1=1");
			boolean flag = true;
			List<JSONObject> listzz = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());
			if (listzz != null && listzz.size() > 0)
			{
				JSONObject jo = listzz.get(0);
				if (jo.optInt("aa") > 0 || jo.optInt("bb") > 0)
				{
					flag = false;
				}
			}
			if (flag)
			{
				birthdayMemberSql.setLength(0);
				birthdayMemberSql.append("select ca.id from crm_activity  ca where ca.activity_type='birthday' and ca.valid_state='1' and ca.running_state='1' "
						+ "AND to_char(ca.start_time, 'yyyy-MM-dd') <= '" + td
						+ "'  AND to_char(COALESCE(ca.end_time,now()), 'yyyy-MM-dd') >= '" + td + "'");
				List<JSONObject> activityList = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());
				if(activityList == null || activityList.isEmpty() ){
					return ;
				}
				Integer activity_id = activityList.get(0).getInt("id");
				
				StringBuilder sb2 = new StringBuilder();
				Map<Integer, List<JSONObject>> maplistcc = new HashMap<Integer, List<JSONObject>>();
				Map<Integer, List<JSONObject>> maplistcr = new HashMap<Integer, List<JSONObject>>();
				int dd = 0;
				birthdayMemberSql.setLength(0);
				birthdayMemberSql.append("select para_value from sys_parameter where para_code='hysrtqts' and valid_state='1' and store_id=0 ");
				List<JSONObject> listts = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());
				if (listts.size() > 0)
				{
					dd = listts.get(0).optInt("para_value");
				}
				birthdayMemberSql.setLength(0);
				birthdayMemberSql.append("select a.*,(select count(ccb.*) from crm_customer_blacklist ccb where ccb.customer_id=a.id) as black,b.zero_type,b.credit_days from crm_customer_info a left join crm_level b on b.id = a.level where a.birthday ~ '^\\d{4}(\\-|\\/|\\.)\\d{1,2}\\1\\d{1,2}$' and to_char(to_date(a.birthday, 'yyyy-MM-dd'),'mm-dd')= to_char(CURRENT_DATE + interval '" + dd + " D','mm-dd')");
				//去掉已经参加过该活动的
				birthdayMemberSql.append(" and not EXISTS(select  1 from crm_customer_coupons t where t.activity_id = "+activity_id+" and t.customer_id = a.id ) "+
										 " and not EXISTS(select  1 from crm_customer_credit ccc where ccc.activity_id = "+activity_id+" and ccc.customer_id = a.id ) "+
										 " ");
				List<JSONObject> birthdayMemberList = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());// 查询过生日的会员
				birthdayMemberSql.setLength(0);
				birthdayMemberSql.append("select b.*,(select count(cab.*) from crm_activity_blacklist cab where cab.activity_id=d.id) as black,c.level_id,c.reward_coupon,ccc.class_name as cn  from (select ca.id from crm_activity ca where 	ca.activity_type = 'birthday' AND ca.running_state = '1' and ca.valid_state = '1' AND to_char(ca.start_time, 'yyyy-MM-dd') <= '" + td
						+ "'  AND to_char(COALESCE(ca.end_time,now()), 'yyyy-MM-dd') >= '" + td + "'  ) as d LEFT JOIN crm_activity_birthday C ON C.activity_id = d.id	LEFT JOIN crm_coupons_type b on b.activity_id=d.id and b.type_id=c.id left join crm_coupons_class ccc on ccc.id = b.class_id where c.reward_coupon>0 ");
				List<JSONObject> couponsTypeList = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());// 查询生日营销赠送的优惠券

				birthdayMemberSql.setLength(0);
				birthdayMemberSql
						.append("select DISTINCT(e.activity_id),(select count(cab.*) from crm_activity_blacklist cab where cab.activity_id=f.id) as black,e.reward_credit,e.level_id from crm_activity_birthday e left join crm_activity f  on f.id=e.activity_id where f.activity_type='birthday' and f.running_state='1' and f.valid_state='1' and e.reward_credit>0  and to_char(f.start_time,'yyyy-MM-dd') <= '"
								+ td + "'  and to_char(f.end_time,'yyyy-MM-dd') >=  '" + td + "'");
				List<JSONObject> creditList = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());// 查询生日营销赠送的积分（分会员等级）

				birthdayMemberSql.setLength(0);
				birthdayMemberSql.append("select * from crm_level where valid_state='1' ORDER BY level_order asc");// 查询所有会员等级
				List<JSONObject> upLevelList = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());
				for (JSONObject jo2 : birthdayMemberList)
				{
					String mobil = jo2.optString("mobil");
					if (mobil.length() > 6)
					{
						sb2.append(",'" + mobil + "'");
					}
				}
				/**
				 * 所有要过生日会员的手机号 sb2
				 * */
				for (JSONObject jo : couponsTypeList)
				{
					int level_id = jo.optInt("level_id");
					if (maplistcc.containsKey(level_id))
					{
						maplistcc.get(level_id).add(jo);
					}
					else
					{
						List<JSONObject> listc = new ArrayList<JSONObject>();
						listc.add(jo);
						maplistcc.put(level_id, listc);
					}
				}

				for (JSONObject jo : creditList)
				{
					int level_id = jo.optInt("level_id");
					if (maplistcr.containsKey(level_id))
					{
						maplistcr.get(level_id).add(jo);
					}
					else
					{
						List<JSONObject> listc = new ArrayList<JSONObject>();
						listc.add(jo);
						maplistcr.put(level_id, listc);
					}
				}
				//定义赠送的优惠券和积分  key  level_id
				Map<Integer,List<JSONObject>> sendCouponsMap = new HashMap<>();
				Map<Integer,List<JSONObject>> sendCreditMap = new HashMap<>();
				//拼接好的优惠券名称
				Map<Integer,String> couponsNameMap = new HashMap<>();
				
//				List<JSONObject> activityAssetList = new ArrayList<>();

				if (couponsTypeList.size() > 0 || creditList.size() > 0 && birthdayMemberList.size() > 0)
				{
					List<JSONObject> sendCouponsObjList = new ArrayList<JSONObject>();

					for (JSONObject birthdayMember : birthdayMemberList)
					{
						int level = birthdayMember.optInt("level");// 会员等级
						Integer mid = birthdayMember.optInt("id");// 会员id
						Integer m_black = birthdayMember.optInt("black");
						if (maplistcc.containsKey(level))// 判断该等级是否赠送优惠券
						{
							List<JSONObject> couponsTypeList2 = maplistcc.get(level);
							for (JSONObject couponsTypeObj : couponsTypeList2)// 该会员等级赠送了哪些类型的优惠券
							{
								Integer num = couponsTypeObj.optInt("reward_coupon");// 该类型优惠券的张数
								Integer tid = couponsTypeObj.optInt("id");
								Integer a_black = couponsTypeObj.optInt("black");
								
								if(m_black>0 && a_black>0)
								{
									continue;
								}
								/**
								 * 生成优惠券
								 * */
								for (int i = 0; i < num; i++)
								{
									JSONObject joc = new JSONObject();
									String code = this.crmCodeService.getCode(tenancyId, CrmCode.COUPONS_NUM, joc);
									joc.put("code", code);
									joc.put("state", "1");
									joc.put("tenancy_id", tenancyId);
									joc.put("send_store", 0);
									joc.put("send_chanel", "MD01");
									joc.put("customer_id", mid);
									joc.put("type_id", tid);
									joc.put("coupons_code", code);
									joc.put("activity_id", couponsTypeObj.optInt("activity_id"));
									joc.put("valid_state", "1");
									joc.put("send_time", DateUtil.getNowDateYYDDMMHHMMSS());
									joc.put("operator", "admin");
									sendCouponsObjList.add(joc);

									//微信推送的消息中的优惠券
									JSONObject coupon = new JSONObject();
									coupon.put("code",code);
									coupon.put("type",couponsTypeObj.optDouble("face_value") + "元代金券");
									
									List<JSONObject> sendCouponsList = sendCouponsMap.get(level); 
									if(sendCouponsList == null){
										sendCouponsList = new ArrayList<>();
									}
									sendCouponsList.add(coupon);
									
									sendCouponsMap.put(level, sendCouponsList);
								}
							}
							
							
							
							//定义多个优惠券的类型id
							if(couponsNameMap.get(level) == null){
								String couponsName = "";
								for (JSONObject coupon : couponsTypeList2) {
									couponsName += coupon.optDouble("face_value") + "元" + coupon.optString("cn")+";";
								}
								couponsNameMap.put(level, couponsName);
							}
							
							
						}
						if (maplistcr.containsKey(level))// 判断该等级是否赠送积分 以下逻辑和上面相同
						{
							List<JSONObject> creditList2 = maplistcr.get(level);
							for (JSONObject sendCreditObj : creditList2)
							{
								Integer a_black = sendCreditObj.optInt("black");
								
								if(m_black>0 && a_black>0)
								{
									continue;
								}
								int customer_id = mid;
								double reward_credit = sendCreditObj.optDouble("reward_credit", 0.0);
								Object creditId = null;
								JSONObject creditObj = new JSONObject();
								JSONObject creditListObj = new JSONObject();
								JSONObject newCustomerObj = new JSONObject();
								creditObj.put("tenancy_id", tenancyId);
								creditObj.put("customer_id", customer_id);
								long currentTime = (long) (System.currentTimeMillis());
								Date dates = new Date(currentTime);
								SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM-dd");
								creditObj.put("business_date", dfs.format(dates));
								String creadit_bill_code = codeService.getCode(tenancyId, Code.BILL_CODE_CREDIT, creditObj);
								creditObj.put("bill_code", creadit_bill_code);
								creditObj.put("chanel", "MD01");
								creditObj.put("store_id", jb.optInt("store_id"));
								creditObj.put("reason", "过生日赠送积分");
								creditObj.put("activity_id", sendCreditObj.optInt("activity_id"));
								creditObj.put("reward_credit", reward_credit);
								creditObj.put("credit", reward_credit);
								creditObj.put("valid_state", "1");
								creditObj.put("activity_id", sendCreditObj.optInt("activity_id"));
								// if(customerObj.get("zero_type").equals("forever")){
								if (birthdayMember.optString("zero_type").equals("forever"))
								{
									creditObj.put("end_date", "2099-01-01");
								}
								else if (birthdayMember.optString("zero_type").equals("rolling"))
								{
									long creditCurrentTime = (long) (System.currentTimeMillis() + Float.valueOf(birthdayMember.optString("credit_days").toString()) * 24 * 60 * 60 * 1000);
									Date creditDate = new Date(creditCurrentTime);
									SimpleDateFormat creditDf = new SimpleDateFormat("yyyy-MM-dd");
									creditObj.put("end_date", creditDf.format(creditDate));
								}
								creditId = this.dao.insertIgnorCase(tenancyId, "crm_customer_credit", creditObj);
								creditListObj.put("tenancy_id", tenancyId);
								creditListObj.put("customer_id", birthdayMember.optString("id"));
								creditListObj.put("business_date", df.format(date));
								creditListObj.put("bill_code", codeService.getCode(tenancyId, Code.BILL_CODE_CREDIT_LIST, creditListObj));
								creditListObj.put("chanel", "MD01");
								creditListObj.put("store_id", jb.optInt("store_id"));
								creditListObj.put("reason", "过生日赠送积分");
								creditListObj.put("total_credit", reward_credit);
								creditListObj.put("credit_id", creditId);
								creditListObj.put("credit", reward_credit);
								creditListObj.put("third_bill_code", creadit_bill_code);
								creditListObj.put("operate_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
								// creditListObj.put("operator",obj.optString("last_operator"));
								//creditListObj.put("operat_type", "13");
								creditListObj.put("operat_type", "16");
								this.dao.insertIgnorCase(tenancyId, "crm_customer_credit_list", creditListObj);
								newCustomerObj.put("id", birthdayMember.optString("id"));
								newCustomerObj.put("useful_credit", Scm.qadd(birthdayMember.optDouble("useful_credit", 0.0), reward_credit));
								newCustomerObj.put("total_credit", Scm.qadd(birthdayMember.optDouble("total_credit", 0.0), reward_credit));
								this.dao.updateIgnorCase(tenancyId, "crm_customer_info", newCustomerObj);

								//微信推送消息中的赠送积分列表
								JSONObject credit = new JSONObject();
								credit.put("reward_credit", reward_credit);
								
								List<JSONObject> sendCreditList = sendCreditMap.get(level);
								if(sendCreditList == null){
									sendCreditList = new ArrayList<>();
								}
								sendCreditList.add(credit);
								
								sendCreditMap.put(level, sendCreditList);
								

								if (customer_id != 0)
								{
									birthdayMemberSql.setLength(0);
									// int memberLevelId = le;

									// 查看满足条件的累计积分
									double totalCredit = newCustomerObj.optDouble("total_credit", 0.0);
									boolean zeroflag = false;
									double userCredit = newCustomerObj.optDouble("useful_credit", 0.0);
									String mobil = birthdayMember.optString("mobil");
									String c_name = birthdayMember.optString("name");

									List<JSONObject> newUpLevelList = new ArrayList<JSONObject>();
									int index = 0;
									for (int i = 0; i < upLevelList.size(); i++)
									{
										JSONObject upLevelObj = upLevelList.get(i);
										if (level == upLevelObj.optInt("id"))
										{
											index = i;
										}
									}
									String reason = "";
									String total_credit = "";
									String useful_credit = "";
									boolean creditFlag = false;
									boolean creditFlag2 = false;
									// 比对消费
									for (int j = 0; j < index; j++)
									{
										JSONObject upLevelObj = upLevelList.get(j);

										if (newUpLevelList.size() == 0 && upLevelObj.optDouble("user_credit", 0.0) != 0.0 && userCredit >= upLevelObj.optDouble("user_credit", 0.0))
										{
											newUpLevelList.add(upLevelObj);
											useful_credit = upLevelObj.optString("user_credit");
											reason = "可用积分达到";
											birthdayMemberSql.setLength(0);
											birthdayMemberSql.append("select id from sys_parameter where para_code='sjsfkjjf' and para_value='1' and valid_state='1' and store_id=0 ");
											List<JSONObject> zcl = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());
											if (zcl.size() > 0 && userCredit > 0)
											{
												newUpLevelList.add(upLevelObj);
												double upzerouc = Scm.pround(upLevelObj.optDouble("user_credit", 0.0));
												useful_credit = upzerouc + "";
												double consume_credit = Scm.padd(upzerouc, 0.0);
												reason = "可用积分达到";
												birthdayMemberSql.setLength(0);
												birthdayMemberSql.append("select id from sys_parameter where para_code='sjsfkjjf' and para_value='1' and valid_state='1' and store_id=0 ");
												List<JSONObject> zcl1 = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());
												if (zcl1.size() > 0 && userCredit > 0)
												{
													JSONObject zjo = new JSONObject();
													zjo.put("tenancy_id", tenancyId);
													zjo.put("customer_id", customer_id);
													zjo.put("clearzero_type", "升级积分清零");

													zjo.put("clearzero_credit", upzerouc);
													zjo.put("credit_enddate", DateUtil.getNowDateYYDDMM());
													zjo.put("running_state", '1');
													zjo.put("last_operator", "admin");
													zjo.put("last_updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
													this.dao.insertIgnorCase(tenancyId, "crm_clearzero_list", zjo);

													double consume_credit1 = 0.0;
													if (consume_credit > 0)
													{
														birthdayMemberSql.setLength(0);
														// 得到有效积分信息
														birthdayMemberSql.append("select id,credit from crm_customer_credit where customer_id=" + customer_id + " and credit>0 ORDER BY id asc");
														List<JSONObject> listcredit = this.dao.query4Json(tenancyId, birthdayMemberSql.toString());
														List<JSONObject> updatecredit = new ArrayList<JSONObject>();
														List<JSONObject> insertcreditlist = new ArrayList<JSONObject>();
														String op = "admin";
														String ot = DateUtil.getNowDateYYDDMMHHMMSS();
														String bd = DateUtil.getNowDateHHMM();
														String cn = "总部";
														int si = 0;
														String cd = "birthday";
														for (JSONObject jo : listcredit)
														{
															if (consume_credit - consume_credit1 == 0)
															{
																break;
															}

															Double jcredit = 0.0;
															if (jo.optDouble("credit", 0.0) >= (consume_credit - consume_credit1))
															{
																if (jo.optDouble("credit", 0.0) - (consume_credit - consume_credit1) == 0)
																{
																	jo.put("valid_state", "2");
																	jcredit = jo.optDouble("credit", 0.0);
																}
																else
																{
																	jcredit = consume_credit - consume_credit1;
																}

																jo.put("credit", Scm.qsub(jo.optDouble("credit", 0.0), (consume_credit - consume_credit1)));
																consume_credit1 = Scm.qadd(consume_credit1, (consume_credit - consume_credit1));

															}
															else
															{
																jcredit = jo.optDouble("credit", 0.0);
																consume_credit1 = Scm.qadd(consume_credit1, jcredit);
																jo.put("credit", 0.0);
																jo.put("valid_state", "2");
															}
															updatecredit.add(jo);
															JSONObject jo11 = JSONObject.fromObject("{}");
															jo11.put("tenancy_id", tenancyId);
															jo11.put("customer_id", customer_id);
															jo11.put("business_date", bd);
															jo11.put("store_id", si);
															jo11.put("bill_code", codeService.getCode(tenancyId, Code.BILL_CODE_CREDIT_LIST, new JSONObject()));
															jo11.put("chanel", cn);
															jo11.put("reason", "升级积分清零");
															jo11.put("total_credit", upzerouc);
															jo11.put("credit_id", jo.optInt("id"));
															jo11.put("credit", Scm.qsub(0.0, jcredit));
															jo11.put("third_bill_code", cd);
															jo11.put("operate_time", ot);
															jo11.put("last_updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
															jo11.put("operator", op);
															//jo11.put("operat_type", "16");
															jo11.put("operat_type", "21");
															jo11.put("store_updatetime", ot);
															insertcreditlist.add(jo11);
														}
														if (updatecredit.size() > 0)
														{
															this.dao.updateBatchIgnorCase(tenancyId, "crm_customer_credit", updatecredit);
														}
														if (insertcreditlist.size() > 0)
														{
															this.dao.insertBatchIgnorCase(tenancyId, "crm_customer_credit_list", insertcreditlist);
														}
													}

													// this.dao.insertIgnorCase(tenantId,
													// "crm_clearzero_list", nadd);

													userCredit = Scm.psub(userCredit, upzerouc);
													if (userCredit < 0)
													{
														userCredit = 0.0;
													}

													String[] mobils = new String[]
													{ mobil };
													String msg_info = "";
													String msg_info2 = "";

													List<JSONObject> listms = this.dao.query4Json(tenancyId, "select * from crm_info_trading where bussiness_id=4001 and valid_state='1'");
													if (listms.size() > 0 && mobil.length() == 11)
													{
														msg_info = listms.get(0).optString("info_content");
														msg_info2 = msg_info + "";
														msg_info2 = msg_info2.replaceAll(Constant.SMS_HYMC, c_name);
														msg_info2 = msg_info2.replaceAll(Constant.SMS_DIV, "");
														msg_info2 = msg_info2.replaceAll(Constant.SMS_NBSP, "");
														msg_info2 = msg_info2.replaceAll(Constant.SMS_BR, "");
														msg_info2 = msg_info2.replaceAll(Constant.SMS_BCQLJF, upzerouc + "");

														int s = 0;
														JSONObject smsCount = new JSONObject();
														smsCount.put("c", 1);
														try
														{
															s = sendSmsService.send(tenancyId, mobils, msg_info2,smsCount);
														}
														catch (Exception e)
														{

														}
														JSONObject jo = new JSONObject();
														jo.put("mobil", mobil);
														jo.put("trading_type", "升级积分清零");
														jo.put("info_content", msg_info2);
														jo.put("send_state", s + "");
														jo.put("send_time", DateUtil.getNowDateYYDDMMHHMMSS());
														jo.put("store_id", 0);
														jo.put("sms_nums", smsCount.optInt("c"));
														jo.put("sms_account", smsCount.optString("c_name"));
														jo.put("sms_platform_type", smsCount.optString("c_type"));
														this.dao.insertIgnorCase(tenancyId, "crm_info_send", jo);

													}
													zeroflag = true;
												}

												creditFlag2 = true;
												break;
											}

											if (newUpLevelList.size() == 0 && upLevelObj.optDouble("total_credit", 0.0) != 0.0 && totalCredit >= upLevelObj.optDouble("total_credit", 0.0))
											{
												newUpLevelList.add(upLevelObj);
												total_credit = upLevelObj.optString("total_credit");
												reason = "累计积分满";
												creditFlag = true;
												break;
											}
										}

										if (newUpLevelList.size() > 0)
										{
											String nowLevel = "";
											String nowLevelName = "";
											for (JSONObject nowLevelObj : newUpLevelList)
											{
												nowLevel = nowLevelObj.optString("id");
												nowLevelName = nowLevelObj.optString("name");
											}
											JSONObject crm_level_list_obj = new JSONObject();
											crm_level_list_obj.put("tenancy_id", tenancyId);
											crm_level_list_obj.put("customer_id", customer_id);
											crm_level_list_obj.put("original_level", level);
											crm_level_list_obj.put("level", nowLevel);
											crm_level_list_obj.put("type", "auto");
											crm_level_list_obj.put("time", DateUtil.format(new Timestamp(System.currentTimeMillis())));

											if (creditFlag2)
											{
												crm_level_list_obj.put("reason", reason + useful_credit + ",升级为" + nowLevelName);
											}
											if (creditFlag)
											{
												crm_level_list_obj.put("reason", reason + total_credit + ",升级为" + nowLevelName);
											}

											crm_level_list_obj.put("last_operator", Constant.LAST_OPERATOR);
											crm_level_list_obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
											this.dao.insertIgnorCase(tenancyId, "crm_level_list", crm_level_list_obj);
											JSONObject customerObj = new JSONObject();
											customerObj.put("id", customer_id);
											customerObj.put("level", nowLevel);
											if (zeroflag)
											{
												customerObj.put("useful_credit", 0.0);
											}

											this.dao.updateIgnorCase(tenancyId, "crm_customer_info", customerObj);
										}
									}
								}

							}

						}
					}

					if (sendCouponsObjList.size() > 0)
					{
						this.dao.insertBatchIgnorCase(tenancyId, "crm_coupons", sendCouponsObjList);
						this.dao.insertBatchIgnorCase(tenancyId, "crm_customer_coupons", sendCouponsObjList);

					}

				}

				if (sb2.length() > 0){
					sb2.delete(0, 1);
					log.info("---------------------生日短信-----------------------");
					String querySql = "SELECT cc.id,cc.level,m.nickname,cc.remark,cc.store_id,oo.address,cc.mobil,oo.phone as store_phone,(select count(*) from crm_customer_blacklist ccb where ccb.customer_id=ccb.id ) as black FROM crm_customer_info cc LEFT JOIN organ oo on oo.id = cc.store_id left join wx_member as m on cc.remark = m.openid where cc.mobil in (" + sb2.toString() + ")";
					List<JSONObject> memberinfolist = this.dao.query4Json(tenancyId, querySql);
					List<JSONObject> list2 = this.dao.query4Json(tenancyId, "select id from crm_activity_blacklist where activity_id=-3001");
					Boolean blacka = false;
					if(list2.size()>0)
					{
						blacka = true;
					}
					log.info("生日短信sql:"+querySql);
					if (null != memberinfolist && memberinfolist.size() > 0)
					{
						for (JSONObject jsonObject : memberinfolist)
						{
							int level = jsonObject.optInt("level");
							int black = jsonObject.optInt("black");
							try
							{
								if(black>0 && blacka)
								{
									continue;
								}
								jsonObject.put("type", "生日营销短信");
//								businessSmsService.send(tenancyId, 3001, jsonObject.optString("mobil"), jsonObject.optInt("store_id"), jsonObject.optInt("id"), jsonObject);
								
								List<JSONObject> activityAssetList = new ArrayList<>();
								
								//礼品名称（优惠券、积分，以分号隔开）如：10元优惠券;5元优惠券;3积分
								String type = couponsNameMap.get(level) == null ? "" : couponsNameMap.get(level);
								
								List<JSONObject> sendCreditList = sendCreditMap.get(level);
								if(sendCreditList != null){
									//赠送积分
									for (JSONObject credit : sendCreditList) {
										type += credit.optString("reward_credit") + "积分;";
										JSONObject activity = new JSONObject();
										activity.put("reward_credit", credit.optDouble("reward_credit"));
										activityAssetList.add(activity);
									}
								}
								
								if(maplistcc.get(level) != null){//赠送优惠券
									activityAssetList.addAll(maplistcc.get(level));
								}
								
								
								type = type.substring(0, type.length()-1);
								
								JSONObject customerObj = new JSONObject();
								customerObj.put("mobil", jsonObject.optString("mobil"));
								customerObj.put("store_id", jsonObject.optInt("store_id"));
								marketActivityService.sendSMSMsg(customerObj, 3002, activityAssetList, tenancyId);
								
								//在发送生日营销短信的时候，也要推送微信消息（2017-11里程碑C添加）
								String openId = jsonObject.optString("remark");
								//领取人微信昵称
								String toName = jsonObject.optString("nickname");
								
								JSONObject msgjb = new JSONObject();
								msgjb.put("type","birthday");

								//发送生日营销微信消息，对应的是BRITHDAY_ACTIVITY
								wxMessageSendService.sendCouponsMesg(tenancyId, openId, "BRITHDAY_ACTIVITY", type, "", msgjb, toName);

							}
							catch (Exception e)
							{
								// TODO Auto-generated catch block
								e.printStackTrace();
								log.info("发送生日短信时异常");
							}

						}
					}

				}
			}
		}catch(Exception e){
			log.error("生日营销定时任务出错:"+e);
		}finally{
			try {
				lock.unlock();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

}
