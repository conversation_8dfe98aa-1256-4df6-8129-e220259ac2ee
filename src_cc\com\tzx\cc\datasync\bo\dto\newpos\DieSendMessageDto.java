package com.tzx.cc.datasync.bo.dto.newpos;


import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class DieSendMessageDto
{
	private String	type;
	private String	oper;
	private boolean	success;
	private Object	msg;
	private String	sign;
	private String	userid;
	private String	t;
	private List<?>	data;
	private String	logkey;

	public String getType()
	{
		return type;
	}

	public void setType(String type)
	{
		this.type = type;
	}

	public String getOper()
	{
		return oper;
	}

	public void setOper(String oper)
	{
		this.oper = oper;
	}

	public boolean isSuccess()
	{
		return success;
	}

	public void setSuccess(boolean success)
	{
		this.success = success;
	}

	public Object getMsg()
	{
		return msg;
	}

	public void setMsg(Object msg)
	{
		this.msg = msg;
	}

	public String getSign()
	{
		return sign;
	}

	public void setSign(String sign)
	{
		this.sign = sign;
	}

	public String getUserid()
	{
		return userid;
	}

	public void setUserid(String userid)
	{
		this.userid = userid;
	}

	public String getT()
	{
		return t;
	}

	public void setT(String t)
	{
		this.t = t;
	}

	@JsonIgnore
	public List<?> getData()
	{
		return data;
	}

	@JsonIgnore
	public void setData(List<?> data)
	{
		this.data = data;
	}

	public String getLogkey()
	{
		return logkey;
	}

	public void setLogkey(String logkey)
	{
		this.logkey = logkey;
	}

}
