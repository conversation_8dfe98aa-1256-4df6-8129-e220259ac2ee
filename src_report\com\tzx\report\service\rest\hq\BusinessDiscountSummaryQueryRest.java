package com.tzx.report.service.rest.hq;

import com.tzx.report.bo.hq.BusinessDiscountSummaryQueryService;
import com.tzx.report.common.util.ConditionUtils;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;


/**
 * 营业折让汇总报表
 * <AUTHOR>
 *
 */

@Controller("BusinessDiscountSummaryQueryRest")
@RequestMapping("/report/businessDiscountSummaryQueryRest")
public class BusinessDiscountSummaryQueryRest
{
	
	@Resource
	private BusinessDiscountSummaryQueryService businessDiscountSummaryQueryService;
	
	
	@Resource(name = "conditionUtils")
	ConditionUtils conditionUtils;
	
	
	/**
	 * 营业折让汇总查询
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getBusinessDiscountSummaryQuery")
	public void getBusinessDiscountSummaryQuery(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
				if(p.optString("store_id").length()==0){
					p.element("store_id", session.getAttribute("user_organ_codes_group"));
				}
			}
			
			result = businessDiscountSummaryQueryService.getBusinessDiscountSummaryQuery((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
}
