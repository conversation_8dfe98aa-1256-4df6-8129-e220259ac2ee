package com.tzx.cc.report.po.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.cc.report.po.StoreOrderSummaryQueryDao;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;

@Repository(StoreOrderSummaryQueryDao.NAME)
public class StoreOrderSummaryQueryDaoImpl implements StoreOrderSummaryQueryDao{
	 
	
		@Resource(name = "genericDaoImpl")
		private GenericDao	dao;
		
		@Resource(name = "parameterUtils")
		ParameterUtils parameterUtils;
		
		@Resource
		ConditionUtils conditionUtils;
		 
		List<JSONObject> list =null;
		StringBuilder sb = new StringBuilder();
		
		private final String sqlTypeDateL1 ="select sql from saas_report_engine where report_num = 'SAAS_BI_2018_04' and sql_type='L1'";
		private final String sqlTypeDateL0 ="select sql from saas_report_engine where report_num = 'SAAS_BI_2018_04' and sql_type='L0'";
		@Override
		public JSONObject getTakeoutOrdersQueryReconciliation(String tenancyID, JSONObject condition) throws Exception{	
			List<JSONObject> list = new ArrayList<JSONObject>();
			List<JSONObject> footerList =new ArrayList<JSONObject>();
			List<JSONObject> structure =new ArrayList<JSONObject>();
			JSONObject result = new JSONObject();
			long total = 0L;
			
			if(!condition.optString("exportdataexpr").equals("''")){
				condition.element("exportdataexpr", "'"+condition.optString("exportdataexpr")+"'");
				int p = condition.optString("p_store_id").indexOf("'");
				if(p==-1){
					 condition.element("p_store_id", ""+condition.optString("p_store_id")+"");
				 }
			}
			String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,sqlTypeDateL1);
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
				
				list=this.dao.query4Json(tenancyID,reportSql);
				structure = conditionUtils.getSqlStructure(tenancyID,reportSql);
			}else{		
			
			list = this.dao.query4Json(tenancyID,reportSql);
			//合计
			String TotalSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,sqlTypeDateL0);
			footerList = this.dao.query4Json(tenancyID, TotalSql);
//			total = footerList.get(0).getLong("total_count");
			total = this.dao.countSql(tenancyID,reportSql);
			}

			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			result.put("rows", list);
			result.put("page", pagenum);
			result.put("footer", footerList);
			result.put("total",total);
			result.put("structure",structure);
			return result;
		}

		@Override
		public JSONObject getOrderDetailInquiry(String tenancyID,JSONObject condition) throws Exception {
			List<JSONObject> list = new ArrayList<JSONObject>();
			List<JSONObject> footerList =new ArrayList<JSONObject>();
			JSONObject result = new JSONObject();
			long total = 0L;
			
			String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKE_OUT_ORDER_DETAILS);
			total = this.dao.countSql(tenancyID,reportSql.toString());
			list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				
			footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKE_OUT_ORDER_DETAILS_TOTAL).toString());
			 
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			result.put("page", pagenum);
			result.put("total",total);	
			result.put("rows", list);
			result.put("footer", footerList);
			return result;
		}

		@Override
		public JSONObject getOrderDiscountInquiry(String tenancyID,JSONObject condition) throws Exception {
			List<JSONObject> list = new ArrayList<JSONObject>();
			List<JSONObject> footerList =new ArrayList<JSONObject>();
			JSONObject result = new JSONObject();
			long total = 0L;
			
			String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_PREFERENTIAL_QUERY);
			total = this.dao.countSql(tenancyID,reportSql.toString());
			list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				
			footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_PREFERENTIAL_QUERY_TOTAL).toString());
			 
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			result.put("page", pagenum);
			result.put("total",total);	
			result.put("rows", list);
			result.put("footer", footerList);
			return result;
		}

		@Override
		public JSONObject getOrderPaymentEnquiry(String tenancyID,JSONObject condition) throws Exception {
			List<JSONObject> list = new ArrayList<JSONObject>();
			List<JSONObject> footerList =new ArrayList<JSONObject>();
			JSONObject result = new JSONObject();
			long total = 0L;
			
			String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_PAYMENT_INQUIRY);
			total = this.dao.countSql(tenancyID,reportSql.toString());
			list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
			
			footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_PAYMENT_INQUIRY_TOTAL).toString());
			 
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			result.put("page", pagenum);
			result.put("total",total);	
			result.put("rows", list);
			result.put("footer", footerList);
			return result;
		}
}
