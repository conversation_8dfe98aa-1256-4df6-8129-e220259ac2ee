package com.tzx.report.common.util.redisUtil;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.task.common.util.TaskThreadPoolExecutor;
import com.tzx.wechat.bo.BaseInfoService;
import com.tzx.wechat.bo.WeChatMealOrderService;
import com.tzx.wechat.bo.WechatCardService;
import com.tzx.wechat.common.cont.RedisKeyConst;
import com.tzx.wechat.common.utils.TaskRedisUtils;
import com.tzx.wechat.common.utils.WechatJsonUtils;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.connection.jedis.JedisConnection;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StopWatch;

import redis.clients.jedis.Jedis;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * . 新闻点击率
 * 
 * <AUTHOR>
 * 
 */
public class NewsClickRateTask implements Runnable {
	
	
	/**
	 * . redisTemplete
	 */
	@Resource(name = "saasRedisTemplate")
	private RedisTemplate<String, Object> redisTemplate;
	
	/**
	 * connectionFactory
	 */
	@Resource(name = "connectionFactory")
	private JedisConnectionFactory connectionFactory;
	
 
    @Resource
    private CommonMethodAreaService commonMethodAreaService;
 
	//private static final Jedis jedis = new Jedis("192.168.40.13",6379);
	
	private final String  NEWS_CLICK_RATE ="NEWS_CLICK_RATE_00010101010110_";
	
	private final String table_name = "news_list";
	
	/**
	 * . logger
	 */
	private static final Logger logger = Logger.getLogger(NewsClickRateTask.class);

	@Resource(name = "genericDaoImpl")
	private GenericDao dao;

	@Override
	public void run() {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		try {
			autoEnsure();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e);
		}
		stopWatch.stop();
		logger.info("新闻点击率定时任务用时" + stopWatch.prettyPrint());
	}

	private void autoEnsure() throws Exception {
		
	    Set<String> news = redisTemplate.keys(NEWS_CLICK_RATE + "*");
		
		if (news.size()<=0) {
            return;
        }

        ThreadPoolExecutor executor = TaskThreadPoolExecutor.getThreadPool(this.getClass().getName(),
                5, 50, 3, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(200),
                new ThreadPoolExecutor.CallerRunsPolicy() );

		Runnable a = new Runnable(){
        @Override
        public void run() {
            try{
            	JedisConnection jedisConnection = (JedisConnection) connectionFactory.getConnection();
            	List<JSONObject> list = new ArrayList();
            	Set<String> news = redisTemplate.keys(NEWS_CLICK_RATE + "*");
            	 for (String str : news){
		     		try {
		     			String tid = str.substring(31);//获取商户号
		      		    DBContextHolder.setTenancyid(tid);//数据源切换
		      		    Map<byte[], byte[]> news_click = jedisConnection.hGetAll(new String(NEWS_CLICK_RATE+tid).getBytes());//取出新闻点击率
		          		for(Entry<byte[], byte[]> entry:news_click.entrySet()) {
							  JSONObject json = new JSONObject();
							  String key = new String(entry.getKey());  //取出当前key名称
							  String keyId = key.substring(3);//截取当前ID
							  String value = new String(entry.getValue());//取出当前value值
							  json.element("id", keyId);
							  json.element("click_record", value);
							  list.add(json);
						 }
		          	      logger.info("点击率同步数据库当前商户为:" + tid);
						  logger.info("点击率同步数据库当前数据集合为:" + list);
						  int[] rate = commonMethodAreaService.updNewsClickRate(tid, table_name, list);
						  if(rate.length>0){
							  for(int i = 0;i<rate.length;i++){
								  if(rate[i]==0){
									  logger.info("当前同步数据表为" + table_name+"数据同步出错：不清除redis缓存数据！");
									  return;
								  }
							  }
							  logger.info("当前同步数据表为" + table_name+"同步数据成功！");
						  	}
							  //Map<String, String> us = jedis.hgetAll(NEWS_CLICK_RATE+tid);//取出所有key
							  logger.info("清除redis缓存数据"+news);
								for(String newsReteKey: news){
									redisTemplate.delete(newsReteKey);
								}
								logger.info("清除"+news+"EKY成功！");
						   	}catch (Exception e) {
						   		logger.error(e);
						   	}finally{
						   		jedisConnection.close();
						   	}
			             }
		            }catch (Exception e) {
				   		logger.error(e);
				   	}
				}
			};
				executor.execute(a);
		}
	}
 
