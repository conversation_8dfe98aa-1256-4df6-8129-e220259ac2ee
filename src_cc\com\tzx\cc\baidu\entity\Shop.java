package com.tzx.cc.baidu.entity;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 *
 */
public class Shop{
	private String shop_id;//	string	是		合作方商户唯一 ID
	private String name;//	string	是		商户名称
	private String shop_logo;//	string	否		商户图片
	private String province;//	string	是		商户所在省名称，参见附录
	private String city;//	string	是		商户所在市名称，参见附录
	private String county;//	string	是		商户所在区县，参见附录
	private String address;//	string	是		商户地址
	private String brand;//	string	否		品牌，例如肯德基
	private List<Map<String,String>> categorys;//	array	是		分类集
	//categorys.{i}.category1	string	是		顶级分类，参见附录
	//categorys.{i}.category2	string	是		二级分类，参见附录
	//categorys.{i}.category3	string	是		三级分类，参见附录，如果附录中能找到对应的三级分类，则此项必填
	private String phone;//	string	是		商户电话，座机必须填写区号
	private String service_phone;//	string	是		客服电话
	private String longitude;//	string	是		经度，为空时自动根据 address 获取
	private String latitude;//	string	是		纬度
	private String coord_type;//	string	否	bdll	坐标类型，可选值：bdll(百度经纬度)；amap(高德经纬度)
	private List<DeliveryRegion> delivery_region;//	array	是		配送区域信息
	//delivery_region.{i}.name	string	是		配送区域名称
	//delivery_region.{i}.region.{j}.{k}.longitude	float	是		配送区域坐标点经度
	//delivery_region.{i}.region.{j}.{k}.latitude	float	是		配送区域坐标点纬度
	//delivery_region.{i}.delivery_time	int	是		配送时长 单位分钟
	//delivery_region.{i}.delivery_fee	int	是		配送费，单位：分
	private List<Map<String,String>> business_time;//	array	是		营业时间，如果开始时间大于结束时间则表示跨天
	//business_time.{i}.start	string	是		开始时间 ，格式HH:MM
	//business_time.{i}.end	string	是		结束时间
	private String book_ahead_time;//	int	否	0	提前下单时间，单位分钟，预留字段暂不生效
	private String invoice_support;//	int	是		是否可以提供发票，1：是 2：否
	private String min_order_price;//	int	是		外卖起送价，单位：分
	private String package_box_price;//	int	是		餐盒费，单位：分
	//threshold	array	否		订单阈值设置
	//threshold.{i}.num	int	是		初始值
	//threshold.{i}.time	string	是		初始化时间
	//public String shop_code	string	否		商户代码
	public String getShop_id() {
		return shop_id;
	}
	public void setShop_id(String shop_id) {
		this.shop_id = shop_id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getShop_logo() {
		return shop_logo;
	}
	public void setShop_logo(String shop_logo) {
		this.shop_logo = shop_logo;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getCounty() {
		return county;
	}
	public void setCounty(String county) {
		this.county = county;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public List<Map<String, String>> getCategorys() {
		return categorys;
	}
	public void setCategorys(List<Map<String, String>> categorys) {
		this.categorys = categorys;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getService_phone() {
		return service_phone;
	}
	public void setService_phone(String service_phone) {
		this.service_phone = service_phone;
	}
	public String getLongitude() {
		return longitude;
	}
	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}
	public String getLatitude() {
		return latitude;
	}
	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}
	public List<DeliveryRegion> getDelivery_region() {
		return delivery_region;
	}
	public void setDelivery_region(List<DeliveryRegion> delivery_region) {
		this.delivery_region = delivery_region;
	}
	public List<Map<String, String>> getBusiness_time() {
		return business_time;
	}
	public void setBusiness_time(List<Map<String, String>> business_time) {
		this.business_time = business_time;
	}
	public String getBrand() {
		return brand;
	}
	public void setBrand(String brand) {
		this.brand = brand;
	}
	
	public String getInvoice_support() {
		return invoice_support;
	}
	public void setInvoice_support(String invoice_support) {
		this.invoice_support = invoice_support;
	}
	public String getMin_order_price() {
		return min_order_price;
	}
	public void setMin_order_price(String min_order_price) {
		this.min_order_price = min_order_price;
	}
	public String getPackage_box_price() {
		return package_box_price;
	}
	public void setPackage_box_price(String package_box_price) {
		this.package_box_price = package_box_price;
	}
	public String getCoord_type() {
		return coord_type;
	}
	public void setCoord_type(String coord_type) {
		this.coord_type = coord_type;
	}
	public String getBook_ahead_time() {
		return book_ahead_time;
	}
	public void setBook_ahead_time(String book_ahead_time) {
		this.book_ahead_time = book_ahead_time;
	}
	
	public Shop JSONFormatBean(){
		return (Shop) JSONObject.toBean(JSONObject.fromObject(this), this.getClass());
	}
}


