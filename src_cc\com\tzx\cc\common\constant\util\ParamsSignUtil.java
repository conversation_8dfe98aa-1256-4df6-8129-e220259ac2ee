package com.tzx.cc.common.constant.util;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.bo.dto.Data;

/**
 * 接口调用签名生成工具类
 * <AUTHOR>
 *
 */
public class ParamsSignUtil{
	public static void main(String[] args){  
          
        //参数签名算法测试例子  
        HashMap<String, String> signMap = new HashMap<String, String>();  
        signMap.put("tenancy_id","wqlm");  
        signMap.put("jde_code","AAAAAA");  
        signMap.put("store_id","12");
        signMap.put("type","oc_basicdata");  
        signMap.put("oper","get");  
        signMap.put("data_type","item_list");  
        signMap.put("channel","WX02"); 
//        signMap.put("time","2017-09-18");
        signMap.put("ver","1.0");  
        
        String secret_key="$zhangbei$@#&"; //加密secret  
          
        System.out.println("请求发送的签名sign1:"+getSign(signMap,secret_key));  
        HashMap<String, String> SignHashMap=ParamsSignUtil.sign(signMap, secret_key);  
        //System.out.println("SignHashMap:"+SignHashMap);  
        String sign=(String)SignHashMap.get("appSign");  
        //获取参数签名字符串  
        System.out.println("根据参数计算得到签名sign2:"+sign);  
  
    }  
  
    public static HashMap<String, String> sign(Map<String, String> paramValues,  
            String secret) {  
        return sign(paramValues, null, secret);  
    }  
  
    /** 
     * @param paramValues 
     * @param ignoreParamNames 
     * @param secret 
     * @return 
     */  
    public static HashMap<String, String> sign(Map<String, String> paramValues,  
            List<String> ignoreParamNames, String secret) {  
        try {  
            HashMap<String, String> signMap = new HashMap<String, String>();  
            StringBuilder sb = new StringBuilder();  
            List<String> paramNames = new ArrayList<String>(paramValues.size());  
            paramNames.addAll(paramValues.keySet());  
            if (ignoreParamNames != null && ignoreParamNames.size() > 0) {  
                for (String ignoreParamName : ignoreParamNames) {  
                    paramNames.remove(ignoreParamName);  
                }  
            }  
            Collections.sort(paramNames);  
            sb.append(secret);  
            for (String paramName : paramNames) {  
                sb.append(paramName).append(paramValues.get(paramName));  
            }  
            sb.append(secret);  
            byte[] md5Digest = getMD5Digest(sb.toString());  
            String sign = byte2hex(md5Digest);  
            signMap.put("appParam", sb.toString());  
            signMap.put("appSign", sign);  
            return signMap;  
        } catch (IOException e) {  
            throw new RuntimeException("加密签名计算错误", e);  
        }  
          
    }  
  
    public static String utf8Encoding(String value, String sourceCharsetName) {  
        try {  
            return new String(value.getBytes(sourceCharsetName), "UTF-8");  
        } catch (UnsupportedEncodingException e) {  
            throw new IllegalArgumentException(e);  
        }  
    }  
  
    private static byte[] getMD5Digest(String data) throws IOException {  
        byte[] bytes = null;  
        try {  
            MessageDigest md = MessageDigest.getInstance("MD5");  
            bytes = md.digest(data.getBytes("UTF-8"));  
        } catch (GeneralSecurityException gse) {  
            throw new IOException(gse);  
        }  
        return bytes;  
    }  
  
    private static String byte2hex(byte[] bytes) {  
        StringBuilder sign = new StringBuilder();  
        for (int i = 0; i < bytes.length; i++) {  
            String hex = Integer.toHexString(bytes[i] & 0xFF);  
            if (hex.length() == 1) {  
                sign.append("0");  
            }  
            //sign.append(hex.toUpperCase());  
            sign.append(hex.toLowerCase());  
        }  
        return sign.toString();  
    }  
    public static String getSign(Map<String, String> params,String secret)  
    {  
        String ret="";  
        StringBuilder sb = new StringBuilder();  
        Set<String> keyset=params.keySet();  
        TreeSet<String> sortSet=new TreeSet<String>();  
        sortSet.addAll(keyset);  
        Iterator<String> it=sortSet.iterator();  
        sb.append(secret);  
        while(it.hasNext())  
        {  
            String key=it.next();  
            String value=params.get(key);  
            sb.append(key).append(value);  
        }  
        sb.append(secret);  
        byte[] md5Digest;  
        try {  
            md5Digest = getMD5Digest(sb.toString());  
            ret = byte2hex(md5Digest);  
        } catch (IOException e) {  
            e.printStackTrace();  
        }  
        return ret;  
    } 
    
    public static String getSignPb(Data param)  
    {
    	JSONObject jsonParam = new JSONObject();
		// 此处注意secret不参与传递
    	jsonParam.put("t", param.getT());
    	if(param.getTenancy_id().equals("zhumeichazai")){
    		jsonParam.put("secret", "91f04e12cdb5472c9278b9ec9929b88d");
    	}else if(param.getTenancy_id().equals("gadehao")){
    		jsonParam.put("secret", "c6af5060f8404606b341761b6d6189fb");
    	}else{
    		jsonParam.put("secret", "2b27d835a4b847aa8623c3b7b31cb0d8");
    	}
    	//测试
		//jsonParam.put("secret", "74b9550d8cf3494fb1423ca56b868c33");
		jsonParam.put("tenancy_id", param.getTenancy_id());
		jsonParam.put("type", param.getType()); 
		jsonParam.put("store_id", param.getStore_id());
		jsonParam.put("oper", param.getOper());
		jsonParam.put("data", param.getData());
	    Map<String, Object> resultMap = CommonUtil.sortMapByKey(jsonParam);
		String signJson = JSONObject.fromObject(resultMap).toString();
		// 对所有/进行转义
		signJson = signJson.replace("/", "\\/");
		// 中文字符转为unicode
		signJson = CommonUtil.chinaToUnicode(signJson);
		//计算签名
		String sign = CommonUtil.getMD5(signJson);
		// 准备生成请求数据
		jsonParam.put("sign", sign);
		// 此处注意secret不参与传递
		jsonParam.discard("secret");
		return jsonParam.optString("sign");
		
	} 

}
