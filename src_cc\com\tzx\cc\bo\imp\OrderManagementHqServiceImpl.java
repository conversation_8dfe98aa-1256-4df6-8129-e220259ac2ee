package com.tzx.cc.bo.imp;

import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.tzx.cc.baidu.util.OrderDeliveryUtils;
import com.tzx.task.po.redis.dao.TaskRedisDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.google.gson.reflect.TypeToken;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.bo.OrderManagementHqService;
import com.tzx.cc.bo.PlaceOrderManagementService;
import com.tzx.cc.bo.dto.OrderingAbnormalReason;
import com.tzx.cc.common.constant.OrderOper;
import com.tzx.cc.common.constant.util.CcPartitionUtils;
import com.tzx.cc.datasync.common.util.NewPosOrderUtil;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.crm.bo.BonusPointManageService;
import com.tzx.crm.bo.CouponsService;
import com.tzx.framework.bo.DataDictionaryService;
import com.tzx.framework.bo.dto.Combotree;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.MessageUtils;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.weixin.bo.NewWxDishService;
import com.tzx.weixin.po.springjdbc.dao.NewWxMemberDao;

@Service(OrderManagementHqService.NAME)
public class OrderManagementHqServiceImpl implements OrderManagementHqService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao					dao;
	
	@Resource(name=NewWxMemberDao.NAME)
	private NewWxMemberDao newWxMemberDao;

	@Resource(name = OrderManagementHqService.NAME)
	private OrderManagementHqService	orderManagementService;

	@Resource(name = PlaceOrderManagementService.NAME)
	private PlaceOrderManagementService	placeOrderManagementService;
	
	@Resource(name = NewWxDishService.NAME)
	private NewWxDishService newWxDishService;
	
	@Resource(name=CouponsService.NAME)
	private CouponsService					couponsService;

	@Autowired
	private DataDictionaryService		dataDictionaryService;
	
	@Resource(name=BonusPointManageService.NAME)
	private BonusPointManageService bonusPointManageService;

	@Resource(name = TaskRedisDao.NAME)
	private TaskRedisDao taskRedisDao;
	//private static final String PUSH_PLATFORM_MODE = "PUSH_PLATFORM_MODE";
	
	private static final Logger				logger	= Logger.getLogger(OrderManagementHqServiceImpl.class);
	
	static Map<String,String> duty_order_map=new HashMap<String,String>();

	@Override
	public JSONObject loadOrderingList(String tenancyID, JSONObject condition) throws Exception
	{
		String ymd=DateUtil.format(new Date(), "yyyy-MM-dd");
		String start_=ymd+" 00:00:00";
		String end_=ymd+" 23:59:59";
		
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		StringBuilder count_sql = new StringBuilder();
		count_sql.append("SELECT count(1) FROM cc_order_list A WHERE 1 = 1 ");
		sql.append("SELECT e.openid,c.class_item as order_state_name,a.*,b.org_uuid,b.org_full_name,b.organ_code,b.remark as org_remark,a.commission_amount as commission_amount,p.trade_no from cc_order_list a left join organ b on b.id=a.store_id left join sys_dictionary c on c.class_item_code=a.order_state LEFT JOIN crm_customer_info d on d.id=a.customer_id LEFT JOIN wx_member e on e.vip_mobile = d.mobil LEFT JOIN pos_payment_order P ON A.tenancy_id::text||'_'||A.store_id::text||'_'||A.order_code=P.order_num  AND P.status=1 where 1=1 and c.class_identifier_code='order_state' ");
		if (condition.containsKey("type") && "cur".equalsIgnoreCase(condition.optString("type")))
		{
			sql.append("and a.single_time between '"+start_+"' and '"+end_+"' ");
			count_sql.append("and a.single_time between '"+start_+"' and '"+end_+"' ");
			
			String tempSQL=CcPartitionUtils.makeSQL(tenancyID,false, "a", sql.toString(), "", CcPartitionUtils.TYPE_SINGLETIME_SYSTEM);
			String tempSQL_count=CcPartitionUtils.makeSQL(tenancyID,false, "a", count_sql.toString(), "", CcPartitionUtils.TYPE_SINGLETIME_SYSTEM);
			
			sql.setLength(0);
			count_sql.setLength(0);
			sql.append(tempSQL);
			count_sql.append(tempSQL_count);
		}
		if (condition.containsKey("type") && "his".equalsIgnoreCase(condition.optString("type")))
		{
			sql.append("and a.single_time<='"+start_+"' ");
			count_sql.append("and a.single_time<='"+start_+"' ");
			
			
			String tempSQL=CcPartitionUtils.makeSQL(tenancyID,false, "a", sql.toString(), ymd, CcPartitionUtils.TYPE_SINGLETIME_LESS_EQ);
			String tempSQL_count=CcPartitionUtils.makeSQL(tenancyID,false, "a", count_sql.toString(),ymd, CcPartitionUtils.TYPE_SINGLETIME_LESS_EQ);
			
			sql.setLength(0);
			count_sql.setLength(0);
			sql.append(tempSQL);
			count_sql.append(tempSQL_count);
		}

		if (condition.containsKey("consigner_phone") && !"".equals(condition.optString("consigner_phone")))
		{
			sql.append(" and  a.consigner_phone like '%" + condition.optString("consigner_phone") + "%'");
			count_sql.append(" and  a.consigner_phone like '%" + condition.optString("consigner_phone") + "%'");
		}
		if (condition.containsKey("organ_code") && !"".equals(condition.optString("organ_code")))
		{
			sql.append(" and  b.organ_code like '%" + condition.optString("organ_code") + "%'");
			// sql.append(" and b.id in (select * from get_oids_bycode('"+condition.get("organ_code").toString().trim()+"')) ");
		}
		
		//添加门店 权限       2016年8月12日14:52:29   xgy   begin
		if(condition.containsKey("authority_organ")) {
			String authority = condition.optString("authority_organ");
			if(StringUtils.isNotBlank(authority)) {
				sql.append(" and a.store_id in (").append(authority).append(")");
			}
		}
		//添加门店 权限       2016年8月12日14:52:29   xgy   end
		
		if (condition.containsKey("store_id") && !"".equals(condition.optString("store_id")) && !"0".equals(condition.optString("store_id")))
		{
			sql.append(" and  a.store_id = " + condition.optString("store_id") + "");
			count_sql.append(" and  a.store_id = " + condition.optString("store_id") + "");
			
		}
		if (condition.containsKey("order_code") && !"".equals(condition.optString("order_code")))
		{
			sql.append(" and  a.order_code = '" + condition.optString("order_code") + "'");
			count_sql.append(" and  a.order_code = '" + condition.optString("order_code") + "'");
			
			String tempSQL=CcPartitionUtils.makeSQL(tenancyID,false, "a", sql.toString(), condition.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
			String tempSQL_count=CcPartitionUtils.makeSQL(tenancyID,false, "a", count_sql.toString(), condition.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
			
			sql.setLength(0);
			count_sql.setLength(0);
			sql.append(tempSQL);
			count_sql.append(tempSQL_count);
		}
		
		if (condition.containsKey("trade_code") && !"".equals(condition.optString("trade_code")))
		{
			sql.append(" and  p.trade_no like '%" + condition.optString("trade_code") + "%'");
		}

		if (condition.containsKey("ifdd") && !"".equals(condition.optString("ifdd")))
		{
			if (condition.optString("ifdd").equals("0"))
			{
				sql.append(" and  a.receive_time is null ");
			}
			else if (condition.optString("ifdd").equals("1"))
			{
				sql.append(" and  a.receive_time is not null ");
			}
		}
		if (condition.containsKey("address") && !"".equals(condition.optString("address")))
		{
			sql.append(" and  a.address like '%" + condition.optString("address") + "%'");
		}

		if (condition.containsKey("store_name") && !"".equals(condition.optString("store_name")))
		{
			sql.append(" and  b.org_full_name like '%" + condition.optString("store_name") + "%'");
		}

		if (condition.containsKey("jxy") && !"".equals(condition.optString("jxy")))
		{
			sql.append(" and  a.entry_name like '%" + condition.optString("jxy") + "%'");
		}

		if (condition.containsKey("order_state") && !"".equals(condition.optString("order_state")) && !"0".equals(condition.optString("order_state")))
		{
			sql.append(" and  a.order_state = '" + condition.optString("order_state") + "'");
			count_sql.append(" and  a.order_state = '" + condition.optString("order_state") + "'");
			
		}
		if (condition.containsKey("payment_state") && !"".equals(condition.optString("payment_state")) && !"0".equals(condition.optString("payment_state")))
		{
			sql.append(" and  a.payment_state = '" + condition.optString("payment_state") + "'");
			count_sql.append(" and  a.payment_state = '" + condition.optString("payment_state") + "'");
		}
		if (condition.containsKey("channel") && !"".equals(condition.optString("channel")) && !"0".equals(condition.optString("channel")))
		{
			sql.append(" and  a.chanel = '" + condition.optString("channel") + "'");
			count_sql.append(" and  a.chanel = '" + condition.optString("channel") + "'");
		}
		if (condition.containsKey("single_time_from") && !"".equals(condition.optString("single_time_from")))
		{
			sql.append(" and  to_date(to_char(A.single_time,'YYYY-MM-DD'),  'YYYY-MM-DD') >= TO_DATE('" + condition.get("single_time_from") + "','YYYY-MM-DD') ");
			count_sql.append(" and  to_date(to_char(A.single_time,'YYYY-MM-DD'),  'YYYY-MM-DD') >= TO_DATE('" + condition.get("single_time_from") + "','YYYY-MM-DD') ");
		
			String tempSQL=CcPartitionUtils.makeSQL(tenancyID,false, "a", sql.toString(), condition.optString("single_time_from"), CcPartitionUtils.TYPE_SINGLETIME_GREATE_EQ);
			String tempSQL_count=CcPartitionUtils.makeSQL(tenancyID,false, "a", count_sql.toString(), condition.optString("single_time_from"), CcPartitionUtils.TYPE_SINGLETIME_GREATE_EQ);
			
			sql.setLength(0);
			count_sql.setLength(0);
			sql.append(tempSQL);
			count_sql.append(tempSQL_count);
		}
		if (condition.containsKey("single_time_to") && !"".equals(condition.optString("single_time_to")))
		{
			sql.append(" and  to_date(to_char(A.single_time,'YYYY-MM-DD'),  'YYYY-MM-DD') <= TO_DATE('" + condition.get("single_time_to") + "','YYYY-MM-DD') ");
			count_sql.append(" and  to_date(to_char(A.single_time,'YYYY-MM-DD'),  'YYYY-MM-DD') <= TO_DATE('" + condition.get("single_time_to") + "','YYYY-MM-DD') ");
		
			String tempSQL=CcPartitionUtils.makeSQL(tenancyID,false, "a", sql.toString(), condition.optString("single_time_to"), CcPartitionUtils.TYPE_SINGLETIME_LESS_EQ);
			String tempSQL_count=CcPartitionUtils.makeSQL(tenancyID,false, "a", count_sql.toString(), condition.optString("single_time_to"), CcPartitionUtils.TYPE_SINGLETIME_LESS_EQ);
			
			sql.setLength(0);
			count_sql.setLength(0);
			sql.append(tempSQL);
			count_sql.append(tempSQL_count);
		}

		/*Add by hebing start 20170614 */
		/*当日订单查询和历史订单查询界面，要求增加“第三方订单号”和“外卖序号”查询筛选条件 */
		if (condition.containsKey("third_order_code") && !"".equals(condition.optString("third_order_code")))
		{
			sql.append(" and  a.third_order_code = '" + condition.optString("third_order_code") + "'");
			count_sql.append(" and  a.third_order_code = '" + condition.optString("third_order_code") + "'");
		}
		
		if (condition.containsKey("chanel_serial_number") && !"".equals(condition.optString("chanel_serial_number")))
		{
			sql.append(" and  a.chanel_serial_number = '" + condition.optString("chanel_serial_number") + "'");
			count_sql.append(" and  a.chanel_serial_number = '" + condition.optString("chanel_serial_number") + "'");
		}
		/*Add by hebing end 20170614 */
		//at 20171215 记录数查询采用sql正常查询语句，解决记录数查询不准确的问题  张勇
		//String count_sql1=count_sql.toString();
		String sql1=this.dao.buildPageSql(condition, sql.toString());
		logger.info("sql:"+sql1);
		//logger.info("count_sql:"+count_sql1);
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenancyID, "select count(1) from ("+sql.toString()+") as tmp");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql1);
		List<JSONObject> update_list = new ArrayList<JSONObject>();
		Date cur_sys_date = new Date();
		long sys_time = System.currentTimeMillis();
		
		///添加取消原因字段
		for(JSONObject reasonJson: list){
			String order_code=reasonJson.optString("order_code");
			try {
				SqlRowSet rs=this.dao.query(tenancyID, "SELECT complaint_content from cc_order_reason_detail  where order_code='"+order_code+"'");
				if(rs.next()){
					reasonJson.put("cancle_reason", rs.getString("complaint_content"));
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		/*
		 * if(list.size()>0){ for(JSONObject obj: list){ JSONObject update_obj
		 * =new JSONObject(); if(obj.optString("order_state").equals("01")){
		 * String single_time=obj.optString("single_time"); Timestamp ts = new
		 * Timestamp(sys_time); ts =
		 * Timestamp.valueOf(single_time);//将字符串转换成Timestamp格式 long
		 * single_time_long=ts.getTime() + 5*60*1000;
		 * if(cur_sys_date.getTime()>=single_time_long &&
		 * obj.optString("order_state").equals("01")){ String
		 * receive_time=obj.optString("receive_time");
		 * if(receive_time.equals("") || receive_time.equals("null")){
		 * 
		 * update_obj.put("id", obj.optString("id"));
		 * update_obj.put("order_state", "07"); obj.put("order_state", "07");
		 * update_list.add(update_obj);
		 * 
		 * }else{ long
		 * receive_time_long=DateUtil.parseDate(receive_time).getTime();
		 * if(receive_time.equals("") || receive_time_long>single_time_long){
		 * update_obj.put("id", obj.optString("id"));
		 * update_obj.put("order_state", "07"); obj.put("order_state", "07");
		 * update_list.add(update_obj); } }
		 * 
		 * } } if(obj.optString("order_state").equals("06")){ String
		 * cancellation_time=obj.optString("cancellation_time"); Timestamp tss =
		 * new Timestamp(sys_time); tss =
		 * Timestamp.valueOf(cancellation_time);//将字符串转换成Timestamp格式 long
		 * cancellation_time_long=tss.getTime() + 5*60*1000;
		 * if(cur_sys_date.getTime()>=cancellation_time_long &&
		 * obj.optString("order_state").equals("06")){ String
		 * receive_time_cancellation=obj.optString("receive_time_cancellation");
		 * if(receive_time_cancellation.equals("") ||
		 * receive_time_cancellation.equals("null")){ update_obj.put("id",
		 * obj.optString("id")); update_obj.put("order_state", "08");
		 * obj.put("order_state", "08"); update_list.add(update_obj); }else{
		 * long receive_time_cancellation_long=DateUtil.parseDate(
		 * receive_time_cancellation).getTime();
		 * if(receive_time_cancellation.equals("") ||
		 * receive_time_cancellation_long>cancellation_time_long){
		 * update_obj.put("id", obj.optString("id"));
		 * update_obj.put("order_state", "08"); obj.put("order_state", "08");
		 * update_list.add(update_obj); } }
		 * 
		 * } } } if(update_list.size()>0){
		 * this.dao.updateBatchIgnorCase(tenancyID, "cc_order_list",
		 * update_list); } }
		 */
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}

	/**
	 * 查看当天订单下边的菜品信息
	 */

	public JSONObject loadOrderingDishsDetail1(String tenancyID, JSONObject condition) throws Exception, SystemException
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT b.item_code,a.number * a.price as small_total,a.*,b.item_name FROM cc_order_item a LEFT JOIN hq_item_info b on b.id = a.item_id where a.order_code ='" + condition.optString("order_code") + "'");

		String tmpSQL=CcPartitionUtils.makeSQL(tenancyID,false,"a",sql.toString(),  condition.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
		
		List<JSONObject> list = this.dao.query4Json(tenancyID, tmpSQL);
		result.put("rows", list);
		// footer 部分(合计)
		List<JSONObject> footer = new ArrayList<JSONObject>();
		double numbers = 0.00;
		double large_total = 0.00;
		if (list != null && list.size() > 0)
		{
			for (JSONObject obj : list)
			{
				numbers += obj.optDouble("number");
				large_total += obj.optDouble("small_total");
			}
			JSONObject foot = JSONObject.fromObject("{}");
			foot.put("number", numbers);
			foot.put("small_total", large_total);
			foot.put("item_name", "合计");
			footer.add(foot);
		}
		else
		{
			JSONObject foot = JSONObject.fromObject("{}");
			foot.put("number", 0.00);
			foot.put("small_total", 0.00);
			foot.put("item_name", "合计");
			footer.add(foot);
		}
		result.put("footer", footer);

		return result;
	}

	@Override
	public JSONObject loadOrderingDishsDetail(String tenancyID, JSONObject condition) throws Exception, SystemException
	{
		JSONObject result = new JSONObject();
		try {
			
			if (condition.containsKey("order_code") && !"".equals(condition.optString("order_code")))
			{	
				String order_code=condition.optString("order_code");
				
				StringBuilder sql = new StringBuilder();
				sql.append("SELECT a.* from cc_order_list a  where a.order_code='" + order_code + "' ");
				
				String tmpSQL=CcPartitionUtils.makeSQL(tenancyID,false,"a",sql.toString(), order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);
				
				List<JSONObject> order_base_info_list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, tmpSQL));
				JSONObject order_base_info_result = new JSONObject();
				if (order_base_info_list.size() > 0)
				{
					order_base_info_result = order_base_info_list.get(0);
				}
				sql.delete(0, sql.length());
				sql.append("SELECT  	(A .discount_amount + A .discountr_amount) as discount_amount_hj, aa.proportion_money,a.*,b.is_combo,c.unit_name,c.standard_price,(a.number * a.price) as small_total from cc_order_item a LEFT JOIN hq_item_info b on b.id=a.item_id LEFT JOIN hq_item_unit c on c.id=a.unit_id and c.item_id=a.item_id  left join cc_order_item_taste aa on aa.group_index=a.group_index and (aa.type='ZF01' OR aa.type='') and aa.order_code=a.order_code and aa.item_id =c.item_id where a.order_code='"
						+ order_code + "' ");
				
				tmpSQL=CcPartitionUtils.makeSQL(tenancyID,false,"a",sql.toString(), order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);
				
				List<JSONObject> order_item_list = this.dao.query4Json(tenancyID, tmpSQL);
				List<JSONObject> footer = new ArrayList<JSONObject>();
				double numbers = 0.00;
				double large_total = 0.00;
				double discount_amount_total = 0.00;
				double jia_price = 0.00;
				double total_jia_price = 0.00;
				double total = order_base_info_result.optDouble("total_money");
				double songcanfei = 0.00;
				DecimalFormat df = new DecimalFormat("######0.00");
				if (order_item_list != null && order_item_list.size() > 0)
				{
					for (JSONObject obj : order_item_list)
					{

						// if (!"null".equals(obj.optString("proportion_money")))
						// {
						// jia_price = obj.optDouble("proportion_money");
						// total_jia_price += jia_price;
						// obj.put("small_total", obj.optDouble("small_total") +
						// jia_price);
						// }
						// numbers += obj.optDouble("number");
						// large_total += obj.optDouble("small_total");
						if (!"null".equals(obj.optString("method_money")))
						{
							jia_price = obj.optDouble("method_money",0.0);
							total_jia_price += jia_price;
							obj.put("small_total", obj.optDouble("product_fee"));
						}
						numbers += obj.optDouble("number");
						large_total += obj.optDouble("small_total");
						
						discount_amount_total += obj.optDouble("discount_amount_hj",0.0);
						
					}
					JSONObject foot = JSONObject.fromObject("{}");
					foot.put("number", numbers);
					if (!"null".equals(order_base_info_result.optString("meal_costs")) && !"".equals(order_base_info_result.optString("meal_costs")))
					{
						foot.put("total", df.format(large_total + order_base_info_result.optDouble("meal_costs",0.0)-discount_amount_total));
					}
					else
					{
						foot.put("total", df.format(large_total-discount_amount_total));
					}
					foot.put("small_total", large_total);
					foot.put("method_money", total_jia_price);
					foot.put("item_name", "合计");
					if (!"null".equals(order_base_info_result.optString("meal_costs")) && !"".equals(order_base_info_result.optString("meal_costs")))
					{
						foot.put("songcanfei", df.format(order_base_info_result.optDouble("meal_costs")));
					}
					else
					{
						foot.put("songcanfei", 0.00);
					}
					// foot.put("songcanfei",df.format(total-large_total) );
					foot.put("discount_amount_hj", discount_amount_total);
					footer.add(foot);
				}
				else
				{
					JSONObject foot = JSONObject.fromObject("{}");
					foot.put("number", 0.00);
					foot.put("small_total", 0.00);
					foot.put("method_money", 0.00);
					foot.put("item_name", "合计");
					foot.put("total", 0.00);
					foot.put("songcanfei", 0.00);
					foot.put("discount_amount_hj", 0.00);
					footer.add(foot);
				}
				result.put("footer", footer);
				for (JSONObject order_item_obj : order_item_list)
				{
					String taste_id = "";
					String item_taste = "";
					String type = "";
					sql.delete(0, sql.length());
					sql.append("select * from cc_order_item_taste a where a.order_code='" + order_item_obj.optString("order_code") + "' and a.group_index=" + order_item_obj.optString("group_index") + "");
					List<JSONObject> order_item_taste_list = this.dao.query4Json(tenancyID, sql.toString());
					if (order_item_taste_list.size() > 0)
					{
						for (JSONObject order_item_taste_obj : order_item_taste_list)
						{
							taste_id += order_item_taste_obj.optString("taste_method_id") + ",";
							item_taste += order_item_taste_obj.optString("item_remark") + ",";
							type += order_item_taste_obj.optString("type") + ",";
						}
						if (item_taste.substring(0, 1).equals(","))
						{
							order_item_obj.put("taste_id", taste_id.substring(0, taste_id.length() - 1));
							order_item_obj.put("item_taste", item_taste.substring(0, item_taste.length() - 1));
							order_item_obj.put("type", type.substring(0, type.length() - 1));
						}
						else
						{
							order_item_obj.put("taste_id", taste_id.substring(0, taste_id.length() - 1));
							order_item_obj.put("item_taste", item_taste.substring(0, item_taste.length() - 1));
							order_item_obj.put("type", type.substring(0, type.length() - 1));
						}
					}
				}
				result.put("rows", order_item_list);
				result.put("order_base_info_result", order_base_info_result);
			}
			else
			{
				result.put("order_base_info_result", "");
				result.put("rows", "[]");
			}

			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public Boolean orderReIssued(String tenancyID, JSONObject obj) throws Exception, SystemException
	{
		boolean flag = true;
		if (obj.containsKey("order_list") && obj.get("order_list") != "")
		{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(obj.get("order_list").toString(), new TypeToken<List<JSONObject>>(){}.getType());
		Iterator<JSONObject> it2 = list.iterator();
		List<JSONObject> update_state_list = new ArrayList<JSONObject>();
		while (it2.hasNext())
		{
			JSONObject order = it2.next();
			int message_result=1;
			JSONObject query_result_obj =new JSONObject();
			List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
			if(order.optString("order_state").equals("03")||order.optString("order_state").equals("01")||order.optString("order_state").equals("02")||order.optString("order_state").equals("10")||order.optString("order_state").equals("12")){
				JSONObject update_state_result = new JSONObject();
				update_state_result.put("id", order.optInt("id"));
				//--2017-12-22 菅永义 修改订单重新下发可下发已到店数据
				if(!"02".equals(order.optString("order_state"))){
					update_state_result.put("order_state", "01");
				}
				update_state_list.add(update_state_result);
				List<JSONObject> same_store_list = new ArrayList<JSONObject>();
				JSONObject result = loadingOrderIssuedInformation(tenancyID, order);
				//修改重新下发订单状态为新订单 zhangy 20180305
				JSONObject orderInfoOj=result.optJSONObject("order_list");
				orderInfoOj.put("order_state", "01");
				
				same_store_list.add(result);
				Data d = Data.get();
				if (orderInfoOj.optString("org_remark").equals("read_from_rif"))
				{
					d.setType(Type.CALLCENTER_ORDER_UPDATE_STATE);
					query_result_obj.put("org_uuid", orderInfoOj.optString("org_uuid") + "_rif");
				}
				else
				{
					query_result_obj.put("org_uuid", orderInfoOj.optString("org_uuid"));
					d.setType(Type.ORDER);
				}
				jsonObjectList.add(result);
				MessageUtils mu = new MessageUtils();
				if (jsonObjectList.size() > 0)
				{
					d.setOper(Oper.add);
					d.setTenancy_id(tenancyID);
					d.setData(jsonObjectList);
					d.setStore_id(order.optInt("store_id"));
					logger.info ("总部重新下发给门店的订单信息为:" + JSONObject.fromObject(d).toString());
					//at 20180328 zhangy 采用新的工具类进行下发数据
					//message_result=mu.sendMessage(JSONObject.fromObject(d).toString(), query_result_obj.optString("org_uuid"),0,tenancyID,String.valueOf(order.optInt("store_id")));
					message_result = mu.sendMessage(JSONObject.fromObject(d).toString(), query_result_obj.optString("org_uuid"),0,tenancyID,String.valueOf(order.optInt("store_id"))
							,com.tzx.cc.baidu.util.Constant.DELIVER_DATA_TYPE,com.tzx.cc.baidu.util.Constant.BUSINESS_TYPE_FOUR);
					
					if(message_result!=1){
						String update_order_state_sql="update cc_order_list set order_state='03' where order_code='"+order.optString("order_code")+"'";
					    this.dao.execute(tenancyID, update_order_state_sql);
						flag = false;
					}else{
						if (update_state_list.size() > 0)
						{
							int a[] = this.dao.updateBatchIgnorCase(tenancyID, "cc_order_list", update_state_list);
							if (a.length > 0)
							{
								flag = true;
							}
							else
							{
								flag = false;
							}
						}
					}
				}
			}
		}
	}
	return flag;
	}

	public JSONObject loadingOrderIssuedInformation(String tenancyId, JSONObject param) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT a.*,b.org_uuid,b.remark as org_remark,c.class_item as channel_name from cc_order_list a left join organ b on b.id = a.store_id left join sys_dictionary c on c.class_item_code=a.chanel and c.class_identifier_code='chanel' where 1=1 ");
		if(param.containsKey("order_code")){
			sql.append(" and a.order_code in ('" + param.optString("order_code") + "')");
		}
		if(param.containsKey("mobil")){
			sql.append(" and a.consigner_phone = '"+ param.optString("mobil")+"' ORDER BY single_time desc limit 1");
		}
		List<JSONObject> list = this.dao.query4Json(tenancyId, sql.toString());
		if (list.size() >= 1)
		{
			for (JSONObject order_list_obj : list)
			{
				String order_code=order_list_obj.optString("order_code");
				result.put("order_list", order_list_obj);
				// 获取订单菜品
				sql.delete(0, sql.length());
				sql.append("SELECT b.*,c.item_code,c.fake_id from  cc_order_item b left join hq_item_info c on c.id=b.item_id  where  b.order_code= '" + order_code + "'");
				
				String tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(), order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);
				
				List<JSONObject> order_item_list = this.dao.query4Json(tenancyId, tmpSQL);
				result.put("order_item", order_item_list);
				// 获取订单里套餐明细
				sql.delete(0, sql.length());
				sql.append("SELECT b.*,c.item_code,c.fake_id from cc_order_item_details b  left join hq_item_info c on c.id=b.item_id  where  b.order_code= '" + order_code + "'");
				
				tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(), order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);
				
				List<JSONObject> order_item_details_list = this.dao.query4Json(tenancyId, tmpSQL);
				result.put("order_item_details", order_item_details_list);
				// 获取订单付款方式
				sql.delete(0, sql.length());
				if((!order_list_obj.optString("chanel").equalsIgnoreCase("WX02")&&!order_list_obj.optString("chanel").equalsIgnoreCase("WM10"))&&"PLATFORM".equals(order_list_obj.optString("settlement_type"))){
			    	sql.append("SELECT a.id as payment_id,b.tenancy_id,b.store_id,b.order_code,b.pay_money,b.pay_no,b.third_bill_code,b.remark,a.payment_name1 as pay_name ,a.payment_code,a.payment_class from cc_order_repayment b left join payment_way a on a.payment_class=b.remark  where  a.status='1' and b.order_code= '" + order_list_obj.optString("order_code") + "'");
			    	
					tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(), order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);
			    	
				}else{
			    	sql.append("SELECT b.*,a.payment_name1 as pay_name ,a.payment_code,a.payment_class from cc_order_repayment b left join payment_way a on a.id=b.payment_id  where  a.status='1' and  b.order_code= '" + order_list_obj.optString("order_code") + "'");
			   
					tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(), order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);

				}
				List<JSONObject> cc_order_repayment_list = this.dao.query4Json(tenancyId, tmpSQL);
				result.put("order_repayment", cc_order_repayment_list);
				// 获取菜品口味
				sql.delete(0, sql.length());
				sql.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '" + order_code + "'");
				List<JSONObject> cc_order_item_taste_list = this.dao.query4Json(tenancyId, sql.toString());
				result.put("order_item_taste", cc_order_item_taste_list);
				
				//获取订单优惠信息
				sql.delete(0, sql.length());
				sql.append("SELECT b.* from cc_order_discount b   where  b.order_code= '" + order_code + "'");
				
				tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(), order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);
				
				List<JSONObject> cc_order_discount_list = this.dao.query4Json(tenancyId, tmpSQL);
				result.put("cc_order_discount", cc_order_discount_list);
			}
		}
		return result;
	}

	@Override
	public JSONObject queryModifyOrderInformation(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		if (condition.containsKey("order_code") && !"".equals(condition.optString("order_code")))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT a.* from cc_order_list a  where a.order_code='" + condition.optString("order_code") + "' ");
			
			String tmpSQL=CcPartitionUtils.makeSQL(tenancyID,false, "a", sql.toString(), condition.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
			
			List<JSONObject> order_base_info_list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, tmpSQL));
			JSONObject order_base_info_result = new JSONObject();
			if (order_base_info_list.size() > 0)
			{
				order_base_info_result = order_base_info_list.get(0);
			}
			sql.delete(0, sql.length());
			sql.append("SELECT a.*,b.is_combo,b.item_name,c.unit_name,c.standard_price,(a.number * a.price) as small_total from cc_order_item a LEFT JOIN hq_item_info b on b.id=a.item_id LEFT JOIN hq_item_unit c on c.id=a.unit_id and c.item_id=a.item_id  where a.order_code='"
					+ condition.optString("order_code") + "' ");
			
			tmpSQL=CcPartitionUtils.makeSQL(tenancyID,false, "a", sql.toString(), condition.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
			
			List<JSONObject> order_item_list = this.dao.query4Json(tenancyID, sql.toString());
			sql.delete(0, sql.length());

			sql.append("select s.id,s.system_name,s.model_name,s.para_type,s.para_name,(case when ss.para_value is null then s.para_value else ss.para_value end) as para_value,s.para_defaut,s.values_name,s.valid_state,s.para_code from sys_parameter s");
			sql.append(" left join sys_parameter_detail ss on s.para_code=ss.para_code and s.para_value=ss.para_value_code");
			sql.append(" where s.para_code='CMJEWS'");
			List<JSONObject> list1 = this.dao.query4Json(tenancyID, sql.toString());
			sql.delete(0, sql.length());

			sql.append("select s.id,s.system_name,s.model_name,s.para_type,s.para_name,(case when ss.para_value is null then s.para_value else ss.para_value end) as para_value,s.para_defaut,s.values_name,s.valid_state,s.para_code from sys_parameter s");
			sql.append(" left join sys_parameter_detail ss on s.para_code=ss.para_code and s.para_value=ss.para_value_code");
			sql.append(" where s.para_code='ZDJEWS'");
			List<JSONObject> list2 = this.dao.query4Json(tenancyID, sql.toString());

			for (JSONObject order_item_obj : order_item_list)
			{
				String taste_id = "";
				String item_taste = "";
				String type = "";
				for (JSONObject jsonObject : list1)
				{
					order_item_obj.put("para_cm", jsonObject.optInt("para_value"));
				}
				for (JSONObject jsonObject : list2)
				{
					order_item_obj.put("para_zd", jsonObject.optInt("para_value"));
				}
				sql.delete(0, sql.length());
				sql.append("select * from cc_order_item_taste a where a.order_code='" + order_item_obj.optString("order_code") + "' and a.group_index=" + order_item_obj.optString("group_index") + "");
				List<JSONObject> order_item_taste_list = this.dao.query4Json(tenancyID, sql.toString());
				if (order_item_taste_list.size() > 0)
				{
					for (JSONObject order_item_taste_obj : order_item_taste_list)
					{
						if (order_item_taste_obj.optString("order_code").equalsIgnoreCase(order_item_obj.optString("order_code")) && order_item_taste_obj.optString("group_index").equalsIgnoreCase(order_item_obj.optString("group_index"))
								&& order_item_taste_obj.optString("item_id").equalsIgnoreCase(order_item_obj.optString("item_id")) && order_item_taste_obj.optString("type").equalsIgnoreCase("ZF01"))
						{
							order_item_obj.put("price", order_item_obj.optDouble("price") + (order_item_obj.optDouble("method_money") / order_item_obj.optDouble("number")));
							order_item_obj.put("small_total", order_item_obj.optDouble("price") * order_item_obj.optDouble("number"));
						}
						taste_id += order_item_taste_obj.optString("taste_method_id") + ",";
						item_taste += order_item_taste_obj.optString("item_remark") + ",";
						type += order_item_taste_obj.optString("type") + ",";
					}
					if (item_taste.substring(0, 1).equals(","))
					{
						order_item_obj.put("taste_id", taste_id.substring(0, taste_id.length() - 1));
						order_item_obj.put("item_taste", item_taste.substring(0, item_taste.length() - 1));
						order_item_obj.put("type", type.substring(0, type.length() - 1));
					}
					else
					{
						order_item_obj.put("taste_id", taste_id.substring(0, taste_id.length() - 1));
						order_item_obj.put("item_taste", item_taste.substring(0, item_taste.length() - 1));
						order_item_obj.put("type", type.substring(0, type.length() - 1));
					}

				}
			}
			result.put("rows", order_item_list);
			result.put("order_base_info_result", order_base_info_result);
		}
		else
		{
			result.put("order_base_info_result", "");
			result.put("rows", "[]");
		}

		return result;
	}

	@Override
	public Boolean saveUpdateOrderConfirm(String tenancyID, JSONObject obj) throws Exception, SystemException
	{
		obj.put("single_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
		obj.put("tenancy_id", obj.optString("update_tenancy_id"));
		obj.put("chanel", "CC04");
		// 先取消原先订单
		JSONObject order_list_result = new JSONObject();
		order_list_result.put("id", obj.optInt("update_order_id"));
		order_list_result.put("order_state", "08");
		JSONObject query_obj = new JSONObject();
		query_obj.put("order_code", obj.optString("order_code"));
		JSONObject result_obj = orderManagementService.loadOrderingList(obj.optString("tenancy_id").toString(), query_obj);
		List<JSONObject> query_list = result_obj.optJSONArray("rows");
		JSONObject order_infot_obj = query_list.get(0);
		if (order_infot_obj.optString("payment_state").equalsIgnoreCase("03"))
		{
			order_list_result.put("payment_state", "04");
		}
		order_list_result.put("cancellation_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
		order_list_result.put("cancle_name", obj.optString("entry_name"));
		JSONObject order_reason_detail = new JSONObject();
		order_reason_detail.put("tenancy_id", obj.optString("update_tenancy_id"));
		order_reason_detail.put("order_code", obj.optString("order_code"));
		order_reason_detail.put("reason_type", obj.optString("type"));
		order_reason_detail.put("type", "MD03");
		order_reason_detail.put("entry_name", obj.optString("entry_name"));
		order_reason_detail.put("complaints_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
		order_reason_detail.put("complaint_content", obj.optString("remark"));
		order_reason_detail.put("order_state", order_infot_obj.optString("order_state"));
		this.dao.updateIgnorCase(obj.optString("tenancy_id"), "cc_order_list", order_list_result);
		this.dao.insertIgnorCase(obj.optString("tenancy_id"), "cc_order_reason_detail", order_reason_detail);
		MessageUtils mu = new MessageUtils();
		List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
		JSONObject dataObj = new JSONObject();
		dataObj.put("order_code", obj.optString("order_code"));
		dataObj.put("cancellation_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
		dataObj.put("cancle_name", obj.optString("entry_name"));
		dataObj.put("order_code", obj.optString("order_code"));
		if (order_infot_obj.optString("payment_state").equalsIgnoreCase("03"))
		{
			dataObj.put("payment_state", "04");
		}
		else
		{
			dataObj.put("payment_state", order_infot_obj.optString("payment_state"));
		}
		List<JSONObject> order_reason_ist = new ArrayList<JSONObject>();
		JSONObject order_reason_obj = new JSONObject();
		order_reason_obj.put("reason_type", obj.optString("type"));
		order_reason_obj.put("type", "MD03");
		order_reason_obj.put("complaint_content", obj.optString("remark"));
		order_reason_obj.put("complaints_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
		order_reason_obj.put("order_state", order_infot_obj.optString("order_state"));
		order_reason_ist.add(order_reason_obj);
		dataObj.put("order_reason", order_reason_ist);
		jsonObjectList.add(dataObj);
		if (jsonObjectList.size() > 0)
		{
			// Data d = new Data();
			Data d = Data.get();
			d.setType(Type.ORDER);
			d.setOper(Oper.cancle);
			d.setTenancy_id(obj.optString("tenancy_id"));
			d.setData(jsonObjectList);
			d.setStore_id(obj.optInt("update_store_id"));
			logger.info("总部下发给门店的订单信息2:"+JSONObject.fromObject(d).toString());
			mu.sendMessage(JSONObject.fromObject(d).toString(), obj.optString("update_org_uuid"),0,obj.optString("tenancy_id"),obj.optString("update_store_id"));
		}
		String order_code = obj.optString("order_code");
		String new_order_code = order_code;
		if (order_code.contains("-"))
		{
			String order_code_arr[] = order_code.split("-");
			String a = order_code_arr[0].toString();
			int b = Integer.valueOf(order_code_arr[1].toString());
			new_order_code = a + "-" + (b + 1);

		}
		else
		{
			new_order_code = order_code + "-1";
		}
		obj.put("order_code", new_order_code);
		obj.put("send_time", "立即配送");
		if (obj.optString("order_type").equalsIgnoreCase("ZT01"))
		{
			obj.put("meals_id", "");
			obj.put("meal_costs", "");
			obj.put("service_id", "");
		}
		Boolean flag = true;
		Double total_money = 0.0;
		StringBuilder delete_sql = new StringBuilder();
		// delete_sql.delete(0, delete_sql.length());
		// delete_sql.append("delete from cc_order_item a where a.order_code= '"+obj.optString("order_code")+"' ");
		// this.dao.execute(tenancyID, delete_sql.toString());
		// delete_sql.delete(0, delete_sql.length());
		// delete_sql.append("delete  from cc_order_item_details a where a.order_code= '"+obj.optString("order_code")+"' ");
		// this.dao.execute(tenancyID, delete_sql.toString());
		// delete_sql.delete(0, delete_sql.length());
		// delete_sql.append("delete  from cc_order_item_taste a where a.order_code= '"+obj.optString("order_code")+"' ");
		// this.dao.execute(tenancyID, delete_sql.toString());
		placeOrderManagementService.hqOrderSave(tenancyID, obj);
		// obj.put("total_money", obj.optDouble("total_money"));
		// obj.put("meal_costs", obj.optDouble("total_money")-total_money);
		placeOrderManagementService.hqOrderIssued(tenancyID, obj);
		return flag;
	}

	@Override
	public JSONObject loadOrderRepayment(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT a.*,b.payment_name1,b.payment_class FROM cc_order_repayment  a left join payment_way b on b.id=a.payment_id  where a.order_code ='" + condition.optString("order_code") + "'");
		
		String tmpSQL=CcPartitionUtils.makeSQL(tenancyID,false,"a",sql.toString(), condition.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
		
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, tmpSQL));
		if (list.size() > 0)
		{
			result = list.get(0);
		}
		return result;
	}
	
	public List<JSONObject> loadOrderRepaymentList(String tenancyID, JSONObject condition) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT a.*,b.payment_name1,b.payment_class FROM cc_order_repayment  a left join payment_way b on b.id=a.payment_id  where a.order_code ='" + condition.optString("order_code") + "'");
		
		String tmpSQL=CcPartitionUtils.makeSQL(tenancyID,false,"a",sql.toString(), condition.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
		
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, tmpSQL));
		return list;
	}

	@Override
	public List<Combotree> getCancleComplaintTree(String tenancyId, String tableName, String fatherColumn, String nameColumn, String typeColumn, String type, String codeColumn, String store_id, JSONObject jb)
	{
		StringBuilder sb = new StringBuilder("select a.id,a." + fatherColumn + ",a." + nameColumn + ",a." + typeColumn + ",a." + codeColumn + " from " + tableName + " a LEFT JOIN hq_unusual_reason_org b on b.unusual_reason_id =a.id where a." + typeColumn + " ='" + type
				+ "' and a.valid_state='1' and a.id<>" + jb.optInt("uid") + " and b.store_id =" + store_id + "");
		JSONObject ses = this.getSysEncodeingScheme(tenancyId, tableName);
		int m = (ses == null) ? 5 : ses.getInt("max_level"); // 找不到默认5级

		try
		{
			if (tableName.equals("hq_unusual_reason"))
			{
				StringBuilder sql = new StringBuilder("select 0 as id,class_item as reason_name,class_item_code as code from sys_dictionary where class_identifier_code = 'unusual_type' ");

				@SuppressWarnings("unchecked")
				List<OrderingAbnormalReason> listDict = (List<OrderingAbnormalReason>) dao.query(tenancyId, sql.toString(), OrderingAbnormalReason.class);

				List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
				List<Combotree> list2 = new ArrayList<Combotree>();
				Map<Integer, Combotree> map = new HashMap<Integer, Combotree>();
				for (OrderingAbnormalReason ur : listDict)
				{
					if (ur.getCode().equals(type))
					{
						Combotree ct = new Combotree();
						ct.setId(ur.getId());
						ct.setText(ur.getReasonName());
						ct.setLevel(0);
						ct.setCode(ur.getCode());
						ct.setType(ur.getCode());
						ct.setFatherId(0);
						list2.add(ct);
					}
				}
				for (JSONObject jo : list)
				{
					Combotree ct = new Combotree();
					ct.setId(jo.getInt("id"));
					ct.setText(jo.getString(nameColumn));
					ct.setLevel(1);
					ct.setFatherId(jo.getInt(fatherColumn));
					ct.setType(jo.getString(typeColumn));
					ct.setCode(jo.getString(codeColumn));
					map.put(jo.getInt("id"), ct);
					for (Combotree cob : list2)
					{
						if (cob.getType().equals(jo.getString(typeColumn)) && jo.getInt(fatherColumn) != 0)
						{
							cob.getChildren().add(ct);
						}
					}
				}

				for (JSONObject jo : list)
				{
					if (jo.getInt(fatherColumn) != 0)
					{
						Combotree combotree = map.get(jo.getInt(fatherColumn));

						if (combotree != null)
						{
							if (combotree.getLevel() < (m - 1))
							{
								Combotree combotree2 = map.get(jo.get("id"));
								combotree2.setLevel(combotree.getLevel() + 1);
								combotree.getChildren().add(combotree2);
							}
						}

					}
				}

				return list2;

			}
			sb.delete(0, sb.length());
			sb.append("select id," + nameColumn + " as text," + fatherColumn + " as fatherId from " + tableName);
			@SuppressWarnings("unchecked")
			List<Combotree> list = (List<Combotree>) this.dao.query(tenancyId, sb.toString(), Combotree.class);
			List<Combotree> list2 = new ArrayList<Combotree>();
			Map<Integer, Combotree> map = new HashMap<Integer, Combotree>();
			for (Combotree jo : list)
			{
				map.put(jo.getId(), jo);

			}

			for (Combotree jo1 : list)
			{

				if (jo1.getFatherId() != 0)
				{
					if (map.get(jo1.getFatherId()) == null)
					{
						jo1.setLevel(1);
						list2.add(jo1);
					}

					if (map.get(jo1.getFatherId()) != null)
					{
						if (map.get(jo1.getFatherId()).getLevel() < m - 1)
						{
							map.get(jo1.getId()).setFatherId(map.get(jo1.getFatherId()).getLevel() + 1);
							map.get(jo1.getFatherId()).getChildren().add(map.get(jo1.getId()));
						}

					}
				}
			}

			return list2;

		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			// TODO Auto-generated method stub
			return null;
		}

	}

	@Override
	public JSONObject getSysEncodeingScheme(String tenancyId, String tableName)
	{
		StringBuilder sb = new StringBuilder();
		sb.append("select * from sys_encoding_scheme where table_name ='" + tableName + "' ");
		try
		{
			List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
			if (list.size() > 0)
			{
				return list.get(0);
			}
			return null;
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}

	}
	//总部取消订单下发
	public void hqCancelOrderIssued(String tenancyId,JSONObject update_order_list_result,JSONObject order_reason_detail,JSONObject qd_obj,JSONObject query_result_obj) throws Exception
		{
			CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
			try {
				Data d = Data.get();
				dataDictionaryService.save(tenancyId, "cc_order_list", update_order_list_result);
				dataDictionaryService.save(tenancyId, "cc_order_reason_detail", order_reason_detail);
				MessageUtils mu = new MessageUtils();
				List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
				JSONObject dataObj = new JSONObject();
				dataObj.put("order_code", order_reason_detail.optString("order_code"));
				//保存取消时间
				dataObj.put("cancellation_time",DateUtil.format(new Timestamp(System.currentTimeMillis())));
				List<JSONObject> order_reason_ist = new ArrayList<JSONObject>();
				JSONObject order_reason_obj = new JSONObject();
				order_reason_obj.put("reason_type", qd_obj.optString("type"));
				order_reason_obj.put("type", "MD03");
				order_reason_obj.put("complaint_content", qd_obj.optString("remark"));
				order_reason_obj.put("complaints_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
				order_reason_obj.put("order_state", "08");
				order_reason_ist.add(order_reason_obj);
				dataObj.put("order_reason", order_reason_ist);
				if (query_result_obj.optString("org_remark").equalsIgnoreCase("read_from_rif"))
				{
					d.setType(Type.CALLCENTER_ORDER_UPDATE_STATE);
					dataObj.put("ddzt", "7");
					dataObj.put("operate_code", "");
					dataObj.put("operate_name", "");
					dataObj.put("order_id", query_result_obj.optString("order_code"));
					query_result_obj.put("org_uuid", query_result_obj.optString("org_uuid") + "_rif");
				}
				else
				{
					d.setType(Type.ORDER);
				}
				jsonObjectList.add(dataObj);
				if (jsonObjectList.size() > 0)
				{
					d.setOper(Oper.cancle);
					d.setTenancy_id(tenancyId);
					d.setData(jsonObjectList);
					d.setStore_id(query_result_obj.optInt("store_id"));
					logger.info("总部下发给门店的取消订单信息8:" + JSONObject.fromObject(d).toString());
					ccBusniessLogBean=getOrderCancelLog(d,tenancyId,query_result_obj.optInt("store_id"),qd_obj,order_reason_detail);
					int message_result=1;
					String channel = query_result_obj.optString("chanel");
					String order_code=query_result_obj.optString("order_code");
					JSONObject order = JSONObject.fromObject("{}");
					order.element("tenancy_id", tenancyId);
					order.element("order_code", order_code);
					order.element("order_type", "1");
					order.element("push_count", 0);
					logger.info("总部下发saas 取消订单数据，渠道["+channel+"] 订单号:["+order_code+"]\n 信息:qd_obj: ["+qd_obj+"]\n " +
							"query_result_obj: ["+query_result_obj+"]");
					if(validOrderPushTypeFlag(tenancyId,channel)){
						try {
							message_result = mu.sendMessage(JSONObject.fromObject(d).toString(), query_result_obj.optString("org_uuid"),0,tenancyId,query_result_obj.optString("store_id")
									,com.tzx.cc.baidu.util.Constant.DELIVER_DATA_TYPE,com.tzx.cc.baidu.util.Constant.BUSINESS_TYPE_FOUR);
							if(message_result != 1){
								// 失败进入task队列
								logger.info("========= 总部下发saas 数据失败["+order_code+"]," +
										"类型[取消],进入redis错误队列");
								taskRedisDao.lpush(com.tzx.cc.baidu.util.Constant.PUSH_PLATFORM_MODE.getBytes(), order);
							}
						}catch (Exception e){
							// 失败进入task队列
							logger.error("========= 总部下发saas 数据失败["+order_code+"]," +
									"类型[取消],进入redis错误队列",e);
							message_result = 0;
							taskRedisDao.lpush(com.tzx.cc.baidu.util.Constant.PUSH_PLATFORM_MODE.getBytes(), order);
						}
						if(message_result != 1){
							refreshOrderPushLog(tenancyId,query_result_obj,JSONObject.fromObject(d));
						}
					}else{
						mu.sendMessage(JSONObject.fromObject(d).toString(), query_result_obj.optString("org_uuid"),0,tenancyId,query_result_obj.optString("store_id"));
					}
					//--2017-12-19 添加订单下发数据 到redis
					OrderDeliveryUtils.orderDelivery(JSONObject.fromObject(d).toString(),order_code,"",tenancyId,query_result_obj.optInt("store_id")+"",query_result_obj.optString("org_uuid"));
					//--end
				}
				ccBusniessLogBean.setResponseBody("OK");
			} catch (Exception e) {
				ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
				ccBusniessLogBean.setIsNormal("0");
				e.printStackTrace();
			}finally{
				KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			}
		};
		
	private CcBusniessLogBean getOrderCancelLog(Data d, String tenancyId,int shopId,JSONObject qd_obj,JSONObject order_reason_detail) {
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
	
		String orderCode=order_reason_detail.optString("order_code");
		ccBusniessLogBean.setRequestId(qd_obj.optString("requestId"));
		ccBusniessLogBean.setTzxId(orderCode);
		ccBusniessLogBean.setThirdId(qd_obj.optString("third_order_code"));
		ccBusniessLogBean.setTenancyId(tenancyId);
		ccBusniessLogBean.setShopId(String.valueOf(shopId));
		ccBusniessLogBean.setCategory("cc");
		
		if(orderCode.contains("BD06")){
		ccBusniessLogBean.setChannel("BD06");
		ccBusniessLogBean.setChannelName("BD06");
		}
		if(orderCode.contains("MT08")){
			ccBusniessLogBean.setChannel("MT08");
			ccBusniessLogBean.setChannelName("MT08");
		}
		if(orderCode.contains("EL09")){
			ccBusniessLogBean.setChannel("EL09");
			ccBusniessLogBean.setChannelName("EL09");
		}
		ccBusniessLogBean.setCmd("com.tzx.cc.bo.imp:hqCancelOrderIssued");
		ccBusniessLogBean.setRequestBody(JSONObject.fromObject(d).toString());

		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("0");
		ccBusniessLogBean.setType("cancelOrder");
		ccBusniessLogBean.setOperAction(OrderOper.cancelOrder.toString());
		return ccBusniessLogBean;
	}

	@Override
	public void orderCancel(String tenancyId, JSONObject obj) throws Exception
	{
		logger.info("取消订单传入的信息:" + obj.toString());
		try
		{
			JSONObject query_obj = new JSONObject();
			query_obj.put("order_code", obj.optString("order_code"));
			query_obj.put("third_order_code", obj.optString("third_order_code"));
			JSONObject result_obj = orderManagementService.loadOrderingByThirdCode(tenancyId, query_obj);
			JSONObject order_list_result = new JSONObject();
			order_list_result.put("id", result_obj.optInt("id"));
			order_list_result.put("order_state", "08");
			order_list_result.put("cancellation_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			JSONObject order_reason_detail = new JSONObject();
			order_reason_detail.put("tenancy_id", obj.optString("tenancy_id"));
			order_reason_detail.put("order_code", result_obj.optString("order_code"));
			order_reason_detail.put("reason_type", obj.optString("reason_type"));
			order_reason_detail.put("type", "MD03");
			order_reason_detail.put("complaints_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			order_reason_detail.put("complaint_content", "取消订单");
			order_reason_detail.put("order_state", result_obj.optString("order_state"));
			if (result_obj.optString("payment_state").equalsIgnoreCase("03"))
			{
				order_list_result.put("payment_state", "04");
			}
			if (obj.optString("channel").equalsIgnoreCase("WX02")||obj.optString("channel").equalsIgnoreCase("WM10"))
			{
				// 查询订单支付信息
				JSONObject order_repayment_obj = orderManagementService.loadOrderRepayment(obj.optString("tenancy_id"), obj);
				//先付有付款记录
				if (order_repayment_obj != null && !"{}".equalsIgnoreCase(order_repayment_obj.toString()))
				{
					if (order_repayment_obj.optString("payment_class").equalsIgnoreCase("wechat_pay"))
					{
						JSONObject jsonParam = new JSONObject();
						jsonParam.put("tenancy_id", obj.optString("tenancy_id"));
						jsonParam.put("oper", com.tzx.weixin.common.constant.WxRestOper.refundOrder);
						jsonParam.put("openid", result_obj.optString("openid"));
						jsonParam.put("order_code", obj.optString("order_code"));
						String resultString = newWxDishService.refundOrder(obj.optString("tenancy_id"), jsonParam).toString();
						JSONObject returnJson = JSONObject.fromObject(resultString);
						if (returnJson.optBoolean("success"))
						{
							this.hqCancelOrderIssued(tenancyId, order_list_result, order_reason_detail, obj, result_obj);
						}
						else
						{
							logger.info("微信退款异常:" + resultString);
						}
						// }
					}
					else if (order_repayment_obj.optString("payment_class").equalsIgnoreCase("card"))
					{
						JSONObject jsonParam = new JSONObject();
						jsonParam.put("tenancy_id", obj.optString("tenancy_id"));
						jsonParam.put("store_id", obj.optInt("store_id"));
						jsonParam.put("type", "CUSTOMER_CARD_CONSUME");
						jsonParam.put("secret", "123456");
						jsonParam.put("oper", "update");
						JSONObject obj_data = new JSONObject();
						obj_data.put("card_code", order_repayment_obj.optString("pay_no"));
						obj_data.put("old_bill_code", order_repayment_obj.optString("third_bill_code"));
						obj_data.put("chanel", "WX02");
						String operator = obj_data.optString("operator");
						if(StringUtils.isBlank(operator)) {
							obj_data.put("operator", "admin");
						}
						obj_data.put("updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
						JSONArray array = new JSONArray();
						array.add(obj_data);
						jsonParam.put("data", array);
						String resultString = HttpUtil.sendPostRequest(Constant.systemMap.get("crmUrl"), jsonParam.toString());
						JSONObject returnJson = JSONObject.fromObject(resultString);
						List<JSONObject> obj_list = (List<JSONObject>) returnJson.get("data");
						if (obj_list.size() > 0)
						{
							JSONObject return_sfzfwc_obj = obj_list.get(0);
							if (return_sfzfwc_obj.optBoolean("result"))
							{
								this.hqCancelOrderIssued(tenancyId, order_list_result, order_reason_detail, obj, result_obj);
								}
							else
							{
							}
						}
					}else if(order_repayment_obj.optString("payment_class").equalsIgnoreCase("coupons")){
						//有使用优惠劵
						com.tzx.crm.bo.dto.Data param_new=new com.tzx.crm.bo.dto.Data();
						param_new.setOper( com.tzx.crm.base.constant.Oper.init);
						param_new.setTenancy_id(tenancyId);
						param_new.setStore_id(result_obj.optInt("store_id"));
						List<JSONObject> list = new ArrayList<JSONObject>();
						JSONObject bean=new JSONObject();
						bean.put("chanel", "WX02");
						List<JSONObject> couponList= new ArrayList<JSONObject>();
							JSONObject bean1=new JSONObject();
							bean1.put("coupons_code", order_repayment_obj.optString("pay_no"));
							couponList.add(bean1);
						bean.put("couponslist", couponList);
						list.add(bean);
						param_new.setData(list);
						couponsService.coupons(param_new);
					}else if(order_repayment_obj.optString("payment_class").equalsIgnoreCase("card_credit")){
						//撤销积分消费
						JSONObject queryMemberBaseInfo = newWxMemberDao.queryMemberBaseInfo(tenancyId, result_obj.optString("openid"));
						String mobile_num = queryMemberBaseInfo.optString("mobile_num");
						String third_bill_code = result_obj.optString("third_bill_code");
						com.tzx.crm.bo.dto.Data data1 = new com.tzx.crm.bo.dto.Data();
						data1.setOper(com.tzx.crm.base.constant.Oper.update);
						data1.setStore_id(result_obj.optInt("store_id"));
						data1.setTenancy_id(tenancyId);
						JSONObject bean = new JSONObject();
						List<JSONObject> list = new ArrayList<JSONObject>();
						bean.put("mobil", mobile_num);// 电话
						bean.put("chanel", "WX02");// 渠道
						bean.put("operator", "admin");// 操作人员
						bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
						bean.put("old_bill_code", third_bill_code);//交易单号
						bean.put("business_date", DateUtil.getNowDateYYDDMMHHMMSS());
						bean.put("shift_id", "WX");
						bean.put("batch_no", "WX");
						bean.put("bill_code", result_obj.optString("order_code"));
						list.add(bean);
						data1.setData(list);
						bonusPointManageService.bonusPointConsume(data1);
					}
				}
				else
				{
					//无付款记录
					this.hqCancelOrderIssued(tenancyId, order_list_result, order_reason_detail, obj, result_obj);
					}
			}
			else{
				//百度/美团/饿了么取消订单处理逻辑
				this.hqCancelOrderIssued(tenancyId, order_list_result, order_reason_detail, obj, result_obj);
			}
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	
	}

	@Override
	public void orderComplete(String tenancyId, JSONObject obj) throws Exception
	{
		Data d = Data.get();
		JSONObject query_obj = new JSONObject();
		query_obj.put("third_order_code", obj.optString("third_order_code"));
		JSONObject result_obj = orderManagementService.loadOrderingByThirdCode(tenancyId, query_obj);
		MessageUtils mu = new MessageUtils();
		List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
		JSONObject dataObj = new JSONObject();
		dataObj.put("order_code", result_obj.optString("order_code"));
		dataObj.put("order_state", "10");
		dataObj.put("payment_state", "03");
		/*if (result_obj.optString("org_remark").equalsIgnoreCase("read_from_rif"))
		{
			d.setType(Type.CALLCENTER_ORDER_UPDATE_STATE);
			dataObj.put("ddzt", "5");
			dataObj.put("operate_code", "");
			dataObj.put("operate_name", "");
			dataObj.put("order_id", result_obj.optString("order_code"));
			result_obj.put("org_uuid", result_obj.optString("org_uuid")+"_rif");
		}
		else
		{
			d.setType(Type.ORDER);
		}
		jsonObjectList.add(dataObj);
		if (jsonObjectList.size() > 0)
		{
			d.setOper(Oper.complete);
			d.setTenancy_id(result_obj.optString("tenancy_id"));
			d.setData(jsonObjectList);
			d.setStore_id(result_obj.optInt("store_id"));
			logger.info("总部下发给门店的订单信息3:"+JSONObject.fromObject(d).toString());
			mu.sendMessage(JSONObject.fromObject(d).toString(), result_obj.optString("org_uuid"));
		}*/
	}

	@Override
	public JSONObject loadOrderingByThirdCode(String tenancyID, JSONObject condition) throws Exception
	{
		String tmpSQL="";
		
		StringBuilder sb = new StringBuilder();
		if(condition.containsKey("third_order_code")&&!StringUtils.isEmpty(condition.optString("third_order_code"))){
			sb.append("SELECT a.*,b.org_uuid,b.remark as org_remark from cc_order_list  a  left join organ b on a.store_id=b.id where a.third_order_code ='" + condition.optString("third_order_code") + "' ");	
		
			tmpSQL=CcPartitionUtils.makeSQL(tenancyID,sb.toString(), "", CcPartitionUtils.TYPE_ORDERCODE_NO);
		}else{
			sb.append("SELECT a.*,b.org_uuid,b.remark as org_remark from cc_order_list  a  left join organ b on a.store_id=b.id where a.order_code ='" + condition.optString("order_code") + "' ");
		
			tmpSQL=CcPartitionUtils.makeSQL(tenancyID,sb.toString(), condition.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
		}
		
		try
		{
			List<JSONObject> list = this.dao.query4Json(tenancyID, tmpSQL);
			if (list.size() > 0)
			{
				return list.get(0);
			}
			return null;
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}

	}

	@Override
	public JSONObject loadUnusualReason(String tenancyID, String reason_code, int store_id) throws Exception
	{
		StringBuilder sb = new StringBuilder();
		sb.append("select a.* from hq_unusual_reason a  LEFT JOIN hq_unusual_reason_org b on b.unusual_reason_id=a.id where a.reason_code='" + reason_code + "' and b.store_id= " + store_id);
		try
		{
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			if (list.size() > 0)
			{
				return list.get(0);
			}
			return null;
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}

	}

	@Override
	public boolean checkIsHaveRepayment(String tenentId, String order_code) throws Exception
	{
		try
		{

			if (Tools.hv(order_code) && Tools.hv(order_code))
			{
				StringBuilder sql = new StringBuilder();
				sql.append("select  a.* from cc_order_repayment a where a.order_code ='" + order_code + "'");
				
				String tmpSQL=CcPartitionUtils.makeSQL(tenentId,false,"a",sql.toString(), order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);
				
				long total = this.dao.countSql(tenentId, tmpSQL);
				if (total >= 1)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			return true;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			return false;
		}
	}
	
	@Override
	public List<JSONObject> loadOrderOrderCreditList(String tenancyID,
			JSONObject condition) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * FROM cc_order_credit  a  where a.order_code ='" + condition.optString("order_code") + "'");
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
		return list;
	}
	public List<JSONObject> loadOrderElectronicList(String tenancyID,
			JSONObject condition) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("select * from hq_electronic_invoice_info   a  where a.order_code ='" + condition.optString("order_code") + "'");
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
		return list;
	}

	/**
	 * 校验对应渠道是否使用新下发模式
	 */
	private boolean validOrderPushTypeFlag(String tanencyId,String channel) {
//		boolean is_start = false;
//		String sql = "select para_value from sys_parameter where para_code = 'order_issued_type' and store_id=0 and valid_state='"
//				+ com.tzx.cc.base.Constant.VALID_STATE_TRUE + "'";
//		List<net.sf.json.JSONObject> list;
//		try {
//			list = this.dao.query4Json(tanencyId, sql.toString());
//			if (list == null || list.size() == 0) {
//				is_start = false;
//			} else {
//				for (net.sf.json.JSONObject json : list) {
//					String pushType =json.optString("para_value");
//					String matchType = "0";
//					switch (channel)
//					{
//						case com.tzx.cc.baidu.util.Constant.BAIDU_CHANNEL:
//							matchType = pushType.substring(0,1);
//							break;
//						case com.tzx.cc.baidu.util.Constant.ELE_CHANNEL:
//							matchType = pushType.substring(1,2);
//							break;
//						case com.tzx.cc.baidu.util.Constant.MEITUAN_CHANNEL:
//							matchType = pushType.substring(2,3);
//							break;
//						case com.tzx.cc.baidu.util.Constant.XMDWM_CHANNEL:
//							matchType = pushType.substring(3);
//							break;
//						default:matchType = "0";
//					}
//					if ("1".equals(matchType)) {
//						is_start = true;
//						logger.info("----------- OrderPushType [" + tanencyId + "]推送类型：新模式  ------ ");
//					} else {
//						logger.info("----------- OrderPushType [" + tanencyId + "]推送类型：老模式  ------ ");
//						is_start = false;
//					}
//				}
//			}
//		} catch (Exception e) {
//			is_start = false;
//			logger.info("-----------CommentGrap: 未在数据库中检索到[" + tanencyId + "]商户的库信息  ---- ");
//			e.printStackTrace();
//		}
//		return is_start;
		return true;
	}

	/**
	 * 更新订单推送失败日志
	 * @param obj
	 * @throws Exception
	 */
	private void refreshOrderPushLog(String tenancyId, JSONObject obj,JSONObject pushData) throws Exception {
		String tmpSQL;
		//--------------更新订单状态-----------------
//		String update_order_state_sql = "update cc_order_list set order_state='09' where order_code='" + obj.optString("order_code") + "'";
//		tmpSQL = CcPartitionUtils.makeSQL(tenancyId, update_order_state_sql, obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
//		this.dao.execute(tenancyId, tmpSQL);

		// 记录日志
		JSONObject cc_order_push_log = JSONObject.fromObject("{}");

		cc_order_push_log.element("tenancy_id", tenancyId);
		cc_order_push_log.element("order_code", obj.optString("order_code"));
		cc_order_push_log.element("store_id", obj.optString("store_id"));
		cc_order_push_log.element("org_uuid", obj.optString("org_uuid"));
		cc_order_push_log.element("chanel", obj.optString("chanel"));
		cc_order_push_log.element("msg_body", pushData.toString());
		cc_order_push_log.element("status", 0);
		cc_order_push_log.element("create_time", DateUtil.getNowDateYYDDMMHHMMSS());
		cc_order_push_log.element("last_update_time", DateUtil.getNowDateYYDDMMHHMMSS());

		this.dao.insertIgnorCase(tenancyId, "cc_order_push_log", cc_order_push_log);
	}

}
