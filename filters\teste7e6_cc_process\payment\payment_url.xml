<?xml version="1.0" encoding="UTF-8"?>
<urls>
	<!-- 京东支付 -->
	<pay_type name="jd_pay">
		<!-- 获取支付二维码 -->
		<type name="GET_PREPAY_BARCODE" value="https://payscc.jdpay.com/req/code"></type>
		<!-- 获取支付二维码回调地址-->
		<type name="GET_PREPAY_BARCODE_NOTIFY_URL" value="http://127.0.0.1:9088/tzxsaas/payment/news/jdpay/%s/callback/GET_PREPAY_BARCODE"></type>
		<!-- 查询支付状态-->
		<type name="QUERY_PAY_STATE" value="https://payscc.jdpay.com/order/query"></type>
		<!-- 退款查询接口 -->
		<type name="QUERY_PAY_REFUND" value="https://payscc.jdpay.com/ order/query/refund"></type>
		<!-- 被扫付款 -->
		<type name="PAY_ORDER_BY_CUSTOMER" value="https://pcplatform.jdpay.com/api/pay"></type>
		<!-- 被扫付款 码回调地址-->
		<type name="PAY_ORDER_BY_CUSTOMER_NOTIFY_URL" value="http://www.jd.com"></type>
		<!-- 退款 -->
		<type name="REFUND_PAY_ORDER" value="https://payscc.jdpay.com/order/refund"></type>
		<!-- 退款 回调地址 -->
		<type name="REFUND_PAY_ORDER_NOTIFY_URL" value="http://www.jd.com"></type>
		<!-- 取消订单 -->
		<type name="CANCEL_PAY_ORDER" value="https://payscc.jdpay.com/order/cancel"></type>
	</pay_type>
	
	<pay_type name="ali_pay">
		<!-- 获取支付二维码 -->
		<type name="GET_PREPAY_BARCODE" value="https://openapi.alipaydev.com/gateway.do"></type>
		<type name="PAY_ORDER_BY_CUSTOMER" value="https://openapi.alipaydev.com/gateway.do"></type>
		<type name="CANCEL_PAY_ORDER" value="https://openapi.alipaydev.com/gateway.do"></type>
		<type name="CANCEL_PAY_ORDER_SUP" value="https://openapi.alipaydev.com/gateway.do"></type>
		<type name="REFUND_PAY_ORDER" value="https://openapi.alipaydev.com/gateway.do"></type>
		<type name="QUERY_PAY_STATE" value="https://openapi.alipaydev.com/gateway.do"></type>
		<type name="QUERY_PAY_REFUND" value="https://openapi.alipaydev.com/gateway.do"></type>
		<!---支付宝回调地址-->
		<type name="PAY_NOTIFY_URL" value="http://cs4.meishijia.com/payment/alipay/callback"></type>
	</pay_type>
	
	<!-- 美团闪惠 -->
	<pay_type name="shanhui_pay">
		<!-- 刷卡支付 -->
		<type name="PAY_ORDER_BY_CUSTOMER" value="http://api.open.cater.meituan.com/shanhui/order/create"></type>
		<type name="QUERY_PAY_STATE" value="http://api.open.cater.meituan.com/shanhui/order/queryByEorderId"></type>
		<type name="QUERY_PAY_STATE_DETAIL" value="http://api.open.cater.meituan.com/shanhui/order/queryTradeDetail"></type>
		<type name="CANCEL_PAY_ORDER" value="http://api.open.cater.meituan.com/shanhui/order/refund"></type>
	</pay_type>

	<!--秒付-->
	<pay_type name="second_pay">
		<!-- 退款 
		<type name="REFUND_PAY_ORDER" value="http://quickpayzc.meituan.com/quick/quickPayQuery/refundByPayOrderId"></type>-->
		<type name="REFUND_PAY_ORDER" value="http://quickpay.zc.st.meituan.com/quick/quickPayQuery/refundByPayOrderId"></type>
		<!--退款查询
		<type name="QUERY_PAY_REFUND" value="http://quickpayzc.meituan.com/quick/quickPayQuery/refundByPayOrderId"></type>-->
		<type name="QUERY_PAY_REFUND" value="http://quickpay.zc.st.meituan.com/quick/quickPayQuery/refundByPayOrderId"></type>
		<!--查询接口
		<type name="QUERY_PAY_STATE" value="http://quickpayzc.meituan.com/quick/quickPayQuery/getPayStatus"></type>-->
		<type name="QUERY_PAY_STATE" value="http://quickpay.zc.st.meituan.com/quick/quickPayQuery/getPayStatus"></type>
	</pay_type> 

	<!-- 微信支付 -->
	<pay_type name="wechat_pay">
		<!-- 获取支付二维码 -->
		<type name="GET_PREPAY_BARCODE" value="https://api.mch.weixin.qq.com/pay/unifiedorder"></type>
		<!-- 获取支付二维码回调地址-->
		<type name="GET_PREPAY_BARCODE_NOTIFY_URL" value="http://localhost:8080/tzxsaas/WechatPaymentRest/payment/wechat/notify"></type>
		<!-- 扫客码支付-->
		<type name="PAY_ORDER_BY_CUSTOMER" value="https://api.mch.weixin.qq.com/pay/micropay"></type>
		<!-- 查询订单 -->
		<type name="QUERY_PAY_STATE" value="https://api.mch.weixin.qq.com/pay/orderquery"></type>
		<!-- 退款 -->
		<type name="REFUND_PAY_ORDER" value="https://api.mch.weixin.qq.com/secapi/pay/refund"></type>
		<!-- 撤销 -->
		<type name="CANCEL_PAY_ORDER" value="https://api.mch.weixin.qq.com/secapi/pay/reverse"></type>
		<!-- 查询退款 -->
		<type name="QUERY_PAY_REFUND" value="https://api.mch.weixin.qq.com/pay/refundquery"></type>
		<!-- 关闭订单 -->
		<type name="CLOSE_PAY_ORDER" value="https://api.mch.weixin.qq.com/pay/closeorder"></type>
	</pay_type>

	<!-- 新美大支付 -->
	<pay_type name="xmd_pay">
		<!---回调地址-->
		<type name="PAY_NOTIFY_URL" value="http://msg.tzx.com.cn/tzxsaas/payment/xmdpay/callback"></type>
	</pay_type>

	<pay_type name="saas">
		<type name="url" value="http://cs4.meishijia.com"></type>
	</pay_type>
</urls>