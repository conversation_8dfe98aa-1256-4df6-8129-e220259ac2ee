package com.tzx.cc.baidu.test;

import net.sf.json.JSONObject;

import com.alibaba.fastjson.JSON;
import com.tzx.cc.baidu.cont.SuweiConst;
import com.tzx.framework.common.util.HttpUtil;

/**
 * <AUTHOR>
 *
 */
public class SuweiTest {

	/**
	 * 速位 生成订单并申请速位平台订单测试
	 * @throws Exception
	 */
	@org.junit.Test
	public void THIRD_SUWEI_APPLAY_ORDER() throws Exception {
		String url = "http://127.0.0.1:9088/tzxsaas/suweirest/suwei/post";
		JSONObject data = new JSONObject();
		data.put("type", SuweiConst.THIRD_SUWEI_APPLAY_ORDER);
		data.put("tenant_id", "hdl");
		data.put("store_id", "354");
		data.put("order_code", "BD0635420160708000001");
		String temp = JSON.toJSONString(data);
		String result = HttpUtil.sendPostRequest(url, temp);
		System.out.println(result+"结束了啊各位，我这边通了");
	}
	
	/**
	 * @throws Exception
	 */
	@org.junit.Test
	public void THIRD_SUWEI_NOTIFY_SAAS() throws Exception {
		String url = "http://127.0.0.1:9088/tzxsaas/suweinotify/suwei/post";
		JSONObject data = new JSONObject();
		data.put("oid", "1478596322578");
		data.put("status", "2");
		data.put("box_num", "A1");
		data.put("pass", "123456");	
		data.put("des", "米饭缺货");
		String temp = JSON.toJSONString(data);
		String result = HttpUtil.sendPostRequest(url, temp);
		System.out.println(result);
	}
	
	
}
