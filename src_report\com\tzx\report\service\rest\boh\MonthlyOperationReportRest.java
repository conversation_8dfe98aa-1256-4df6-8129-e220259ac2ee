package com.tzx.report.service.rest.boh;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.tzx.report.common.util.ReportExportUtils;
import jxl.write.WriteException;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.boh.MonthlyOperationReportService;
import com.tzx.report.common.util.ConditionUtils;

/**
 * 
 * 月度分析
 *
 */

@Controller("MonthlyOperationReportRest")
@RequestMapping("/report/monthlyOperationReportRest")
public class MonthlyOperationReportRest
{
	@Resource(name = MonthlyOperationReportService.NAME)
	private MonthlyOperationReportService		monthlyOperationReportService;
	
	@Resource
	ConditionUtils conditionUtils;
	
	@RequestMapping(value = "/getMonthlyOperationTopData")
	public void getMonthlyOperationTopData(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			if(session.getAttribute("valid_state") == null){
				String storeString = "store_id";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"99999999".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'99999999'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
			}else{
				String storeString = "store_id";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"99999999".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'99999999'".equals(p.optString(storeString)) 
						){
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}
			
			result = monthlyOperationReportService.getMonthlyOperationTopData((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	@RequestMapping(value = "/getMonthlyOperationData")
	public void getMonthlyOperationData(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();
			
			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			if(session.getAttribute("valid_state") == null){
				String storeString = "store_id";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"99999999".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'99999999'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
			}else{
				String storeString = "store_id";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"99999999".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'99999999'".equals(p.optString(storeString)) 
						){
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}
			
			result = monthlyOperationReportService.getMonthlyOperationData((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}
			
			try
			{
				out = response.getWriter();
				
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
		
	}
	 
		@RequestMapping(value = "/getClassAndBusinessTypeDetails")
		public void getClassAndBusinessTypeDetails(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
			       
				JSONObject p = JSONObject.fromObject("{}");
		
				Map<String, String[]> map = request.getParameterMap();
		
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				if(session.getAttribute("valid_state") == null){
					String storeString = "store_id";
					if(p.optString(storeString).length()==0 ||
							"0".equals(p.optString(storeString)) ||
							"'0'".equals(p.optString(storeString)) ||
							"99999999".equals(p.optString(storeString)) ||
							"''".equals(p.optString(storeString)) ||
							"'99999999'".equals(p.optString(storeString)) 
							){
						// 判断当前是门店还是总部
						if(session.getAttribute("organ_id").equals("0")) {
							//取所有门店
							p.element(storeString, session.getAttribute("user_organ_codes_group"));
						}else {
							// 取门店
							p.element(storeString, session.getAttribute("organ_id"));
						}
					}
				}else{
					String storeString = "store_id";
					if(p.optString(storeString).length()==0 ||
							"0".equals(p.optString(storeString)) ||
							"'0'".equals(p.optString(storeString)) ||
							"99999999".equals(p.optString(storeString)) ||
							"''".equals(p.optString(storeString)) ||
							"'99999999'".equals(p.optString(storeString)) 
							){
						p.element(storeString, session.getAttribute("user_organ"));
					}
				}
				
				//result = monthlyOperationReportService.getClassAndBusinessTypeDetails((String) session.getAttribute("tenentid"), p).toString();
				p.element("getName", "yes");
				List<JSONObject> loadDutyOrderNew = conditionUtils.loadDutyOrderNew((String) session.getAttribute("tenentid"), p);
				List<JSONObject> classData  = new JSONArray();
				JSONObject json = new JSONObject();
				String [] znameArr = new String[]{"￥营业实收","消费账单","实收占比%"};
				String [] znameCodeArr = new String[]{"bill_amount","bill_num","payment_rate"};
				
				for(JSONObject class1 :loadDutyOrderNew) {
					for(Integer i = 0 ; i<=2 ; i++) {
						json.clear();
						json.put("pname", class1.optString("name")+"_"+znameCodeArr[i]); // 编码_班次的id
						json.put("zname", znameArr[i]);
						json.put("payment_class", class1.optString("text"));
						json.put("title", znameArr[i]);
						classData.add(json);
					}
				}
				result =classData.toString() ; 
				 
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}


	@RequestMapping(value = "/exportData")
	public void exportData(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{

			workBook = new HSSFWorkbook();

			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}

			if(p.optString("store_id").length()==0){
				p.element("store_id", session.getAttribute("user_organ_codes_group"));
			}

			workBook = monthlyOperationReportService.exportData((String) session.getAttribute("tenentid"), p ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			ReportExportUtils.download(workBook,response,"月度营运报表");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
}
