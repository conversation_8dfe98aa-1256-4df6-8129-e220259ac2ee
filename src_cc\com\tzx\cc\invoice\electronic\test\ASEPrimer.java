package com.tzx.cc.invoice.electronic.test;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;

import com.tzx.cc.baidu.util.CommonUtil;

import sun.misc.BASE64Encoder;

public class ASEPrimer {

	/**
	 * @param args
	 * @throws NoSuchAlgorithmException 
	 * @throws Exception 
	 */
	public static void main(String[] args) throws NoSuchAlgorithmException, Exception {
		//确定计算方法
		MessageDigest md5=MessageDigest.getInstance("MD5");
		BASE64Encoder base64en = new BASE64Encoder();
		StringBuffer sb = new StringBuffer();
		sb.append("1234567890").append("#");
		sb.append("1000000009").append("#");
		sb.append("0003").append("#");
		sb.append("161123").append("#");
		sb.append("32.33").append("#");
		sb.append("测试新项目").append("#");
		sb.append("0.03");
//		sb.append("UITN25DZFPC222IM");
		String content = sb.toString();
		System.out.println(content);
		String password = "UITN25DZFPC222IM";
		byte[] encryptResult = encrypt(content, password);
		String encode2 = base64en.encode(encryptResult);
		System.out.println(encode2);
		String plainText = parseByte2HexStr(encryptResult);
		
		System.out.println(plainText);
        //加密后的字符串
        String newstr = parseByte2HexStr(md5.digest(plainText.getBytes()));
        System.out.println(newstr);
        sb.append("#").append(newstr);
        String encode = base64en.encode(sb.toString().getBytes());
        System.out.println(encode);
        
	}
	
	/** 
     * 加密 
     * 
     * @param content 需要加密的内容 
     * @param password  加密密码 
     * @return 
     */  
    public static byte[] encrypt2(String content, String password) {  
            try {  
                    SecretKeySpec key = new SecretKeySpec(password.getBytes(), "AES");  
                    Cipher cipher = Cipher.getInstance("AES/ECB/NoPadding");  
                    byte[] byteContent = content.getBytes("utf-8");  
                    cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化   
                    byte[] result = cipher.doFinal(byteContent);  
                    return result; // 加密   
            } catch (NoSuchAlgorithmException e) {  
                    e.printStackTrace();  
            } catch (NoSuchPaddingException e) {  
                    e.printStackTrace();  
            } catch (InvalidKeyException e) {  
                    e.printStackTrace();  
            } catch (UnsupportedEncodingException e) {  
                    e.printStackTrace();  
            } catch (IllegalBlockSizeException e) {  
                    e.printStackTrace();  
            } catch (BadPaddingException e) {  
                    e.printStackTrace();  
            }  
            return null;  
    }  
	
	 /**
	  * 获取加密后的字符串
	  * @param input
	  * @return
	  */
	 public static String stringMD5(String pw) {
	  try {  
	     
	        // 拿到一个MD5转换器（如果想要SHA1参数换成”SHA1”）  
	        MessageDigest messageDigest =MessageDigest.getInstance("MD5");  
	        // 输入的字符串转换成字节数组  
	        byte[] inputByteArray = pw.getBytes();  
	        // inputByteArray是输入字符串转换得到的字节数组  
	        messageDigest.update(inputByteArray);  
	        // 转换并返回结果，也是字节数组，包含16个元素  
	        byte[] resultByteArray = messageDigest.digest();  
	        // 字符数组转换成字符串返回  
	        return parseByte2HexStr(resultByteArray);  
	     } catch (NoSuchAlgorithmException e) {  
	        return null;  
	     }  
	 }

	/**
	 * 将二进制转换成16进制
	 * 
	 * @param buf
	 * @return
	 */
	public static String parseByte2HexStr(byte buf[]) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < buf.length; i++) {
			String hex = Integer.toHexString(buf[i] & 0xFF);
			if (hex.length() == 1) {
				hex = '0' + hex;
			}
			sb.append(hex.toUpperCase());
		}
		return sb.toString();
	}

	/**
	 * 将16进制转换为二进制
	 * 
	 * @param hexStr
	 * @return
	 */
	public static byte[] parseHexStr2Byte(String hexStr) {
		if (hexStr.length() < 1)
			return null;
		byte[] result = new byte[hexStr.length() / 2];
		for (int i = 0; i < hexStr.length() / 2; i++) {
			int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
			int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2),
					16);
			result[i] = (byte) (high * 16 + low);
		}
		return result;
	}

	/**
	 * 解密
	 * 
	 * @param content
	 *            待解密内容
	 * @param password
	 *            解密密钥
	 * @return
	 */
	public static byte[] decrypt(byte[] content, String password) {
		try {
			KeyGenerator kgen = KeyGenerator.getInstance("AES");
			kgen.init(128, new SecureRandom(password.getBytes()));
			SecretKey secretKey = kgen.generateKey();
			byte[] enCodeFormat = secretKey.getEncoded();
			SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
			Cipher cipher = Cipher.getInstance("AES");// 创建密码器
			cipher.init(Cipher.DECRYPT_MODE, key);// 初始化
			byte[] result = cipher.doFinal(content);
			return result; // 加密
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (NoSuchPaddingException e) {
			e.printStackTrace();
		} catch (InvalidKeyException e) {
			e.printStackTrace();
		} catch (IllegalBlockSizeException e) {
			e.printStackTrace();
		} catch (BadPaddingException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 加密
	 * 
	 * @param content
	 *            需要加密的内容
	 * @param password
	 *            加密密码
	 * @return
	 */
	public static byte[] encrypt(String content, String password) {
		try {
			KeyGenerator kgen = KeyGenerator.getInstance("AES");
			kgen.init(128, new SecureRandom(password.getBytes()));
			SecretKey secretKey = kgen.generateKey();
			byte[] enCodeFormat = secretKey.getEncoded();
			SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
			Cipher cipher = Cipher.getInstance("AES");// 创建密码器
			byte[] byteContent = content.getBytes("utf-8");
			cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化
			byte[] result = cipher.doFinal(byteContent);
			return result; // 加密
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (NoSuchPaddingException e) {
			e.printStackTrace();
		} catch (InvalidKeyException e) {
			e.printStackTrace();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (IllegalBlockSizeException e) {
			e.printStackTrace();
		} catch (BadPaddingException e) {
			e.printStackTrace();
		}
		return null;
	}
}
