package com.tzx.cc.invoice.electronic.dao;

import java.util.List;

import net.sf.json.JSONObject;


public interface ElecInvoiceDao {
	final static String NAME = "com.tzx.cc.invoice.electronic.dao.impl.ElecInvoiceDaoImpl";

	/**
	 * @param tenancy_id
	 * @param store_id
	 * @return
	 * @throws Exception
	 */
	JSONObject getRequestInfo(String tenancy_id, int store_id) throws Exception;

	/**
	 * 根据订单号查询
	 * @param tenancy_id
	 * @param dh
	 * @return
	 * @throws Exception
	 */
	JSONObject queryInfoByOrderNo(String tenancy_id, String dh) throws Exception;
	
	/**
	 * 根据流水号查询
	 * @param tenancy_id
	 * @param dh
	 * @return
	 * @throws Exception
	 */
	JSONObject queryInfoByFlowNumber(String tenancy_id, String flowNumber) throws Exception;
	
	
	/**
	 * 根据流水号查询
	 * @param tenancy_id
	 * @param electricId
	 * @return
	 * @throws Exception
	 */
	JSONObject queryDetailsByFlowNumber(String tenancy_id, int electricId) throws Exception;
	
	/**
	 * 加锁修改
	 * @param tenantId
	 * @param tableName
	 * @param json
	 * @return
	 * @throws Exception
	 */
	int updateIgnorCaseSyn(String tenantId,String tableName,JSONObject json) throws Exception;

    List<JSONObject> queryPaymentWay(String tenancy_id, int store_id, String paymentClassStr) throws Exception;
    
    /**
	 * 根据票通发票请求流水号查询(票通使用)
	 * @param tenancy_id
	 * @param dh
	 * @return
	 * @throws Exception
	 */
	JSONObject queryInfoByReqserialno(String tenancy_id, String reqserialno) throws Exception;
}
