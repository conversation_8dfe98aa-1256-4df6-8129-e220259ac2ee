package com.tzx.task.common.task;

import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.news.cont.Contant;
import com.tzx.payment.news.cont.StatusConstant;
import com.tzx.payment.news.dao.WechatDao;
import com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl;
import com.tzx.payment.news.service.impl.AlipayPaymentServiceImpl;
import com.tzx.payment.news.service.impl.WechatPaymentServiceImpl;
import com.tzx.payment.news.util.PaymentRedisCache;
import com.tzx.payment.news.util.PaymentUtils;
import com.tzx.payment.news.util.ThreadPool;
import com.tzx.payment.news.util.wechat.WechatClient;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

public class WechatPaymentAutoQueryTask implements Runnable{

	private static final Logger		logger	= Logger.getLogger(WechatPaymentAutoQueryTask.class);


    @Resource(name="com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl")
    private AlipayPaymentDaoImpl alipayPaymentDao;

    @Resource(name = WechatDao.NAME)
    private WechatDao dao;

    @Resource(name="com.tzx.payment.news.service.impl.WechatPaymentServiceImpl")
    private WechatPaymentServiceImpl wechatPaymentService;

    @Resource(name="com.tzx.payment.news.service.impl.AlipayPaymentServiceImpl")
    private AlipayPaymentServiceImpl aliPayMentService;



	@Override
	public void run() {
        try {
            autoQuery();
        } catch (Exception e) {
            logger.error(e);
        }

	}

	public void autoQuery(){
        ThreadPoolExecutor threadPool = ThreadPool.getThreadPool();
        List<JSONObject> wechat_pay = PaymentRedisCache.getOrderInfoByType("wechat_pay");
        if(wechat_pay==null) {
            return;
        }
        for(JSONObject json : wechat_pay) {
            logger.error("【json】 ====>> " + json);
            if(isExecute(json)) {
                logger.info("微信轮询开启一个新的线程，out_trade_no为"+json.optString("out_trade_no"));
                final JSONObject json2 = json;
                try {
                    Runnable a = new Runnable(){
                        @Override
                        public void run() {
                            try{
                                JSONObject saveJson = queryDataJson(json2);
                                if(saveJson != null){
                                    saveJson.element("pay_type", json2.optString("pay_type")); // 调微信获取URL所需要的参数
                                    JSONObject resultJson = reqWechat(saveJson);
                                    process(saveJson,resultJson);
                                    aliPayMentService.send2Md(saveJson, "【微信】");
                                }
                            }catch (Exception e) {
                                String out_trade_info = PaymentRedisCache.getInfoByOutTradeNo(json2.optString("out_trade_no"));
                                PaymentRedisCache.lPushOutTradeNo2Info(json2.optString("pay_type"), out_trade_info);
                                logger.warn("接口调用异常, 重新放入队列中... " + json2.toString());
                                logger.error(e);
                            }
                        }
                    };
                    threadPool.execute(a);
                } catch (Exception e) {
                    String out_trade_info = PaymentRedisCache.getInfoByOutTradeNo(json.optString("out_trade_no"));
                    PaymentRedisCache.lPushOutTradeNo2Info(json.optString("pay_type"), out_trade_info);
                    logger.error(e);
                    continue;
                }
            } else {
                String out_trade_info = PaymentRedisCache.getInfoByOutTradeNo(json.optString("out_trade_no"));
                PaymentRedisCache.lPushOutTradeNo2Info(json.optString("pay_type"), out_trade_info);
            }
        }
    }

    private void process(JSONObject saveJson, JSONObject resultObj) throws Exception {
        String trade_state = resultObj.optString("trade_state");
        // 更新订单状态，默认失败
        int status = StatusConstant.STATUS_PROGRESS;
        int final_state = StatusConstant.STATUS_PROGRESS;
        // 支付成功
        if ("SUCCESS".equals(trade_state)) {
            final_state = StatusConstant.STATUS_SUCCESS;
            status = StatusConstant.STATUS_SUCCESS;
        } else if ("NOTPAY".equals(trade_state)
                || "USERPAYING".equals(trade_state)) {
            // 处理中状态
            status = StatusConstant.STATUS_PROGRESS;
            final_state = StatusConstant.STATUS_PROGRESS;
        } else if ("CLOSED".equals(trade_state)
                || "REVOKED".equals(trade_state)) {
            final_state = StatusConstant.STATUS_CANCELED;
            // 取消状态
            status = StatusConstant.STATUS_PROGRESS;
        } else if ("REFUND".equals(trade_state)) {
            final_state = StatusConstant.STATUS_REFUNDED;
            // 退款状态
            status = StatusConstant.STATUS_SUCCESS;
        } else {
            logger.info("微信轮询返回状态失败");
           return;
        }
        //微信订单号
        String trade_no = resultObj.optString("transaction_id");
        saveJson.put("trade_no",trade_no);
        saveJson.put("status",status);
        saveJson.put("final_state",final_state);
        saveJson.put("buyer_loginid","");
        Double cash_fee = resultObj.optDouble("cash_fee");
        if(cash_fee!=null && !cash_fee.isNaN()) {
            saveJson.put("payment_amount",cash_fee);
        }
        Double coupon_fee = resultObj.optDouble("coupon_fee");
        if(coupon_fee!=null && !coupon_fee.isNaN()) {
            saveJson.put("coupon_fee",coupon_fee);
        }
        saveJson.put("trade_type",resultObj.optString("trade_type"));

        String time_end = resultObj.optString("time_end");
        if(StringUtils.isNotBlank(time_end)) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date parse = simpleDateFormat.parse(time_end);
            String trade_time = sdf.format(parse);
            saveJson.put("trade_time",trade_time);
        }
        saveJson.put("last_updatetime", PaymentUtils.currentTime2Str());
        if(final_state==1) {
            saveJson.put("finish_time", PaymentUtils.currentTime2Str());
        }
        alipayPaymentDao.updateIgnorCase(saveJson.optString("tenancy_id"),"pos_payment_order",saveJson);

        if (final_state != 2){// 状态不为处理中则清除任务
            PaymentRedisCache.deleteInfoByOutTradeNo(saveJson.optString("out_trade_no"));
        }

    }

    /**
     * 查询本地的信息
     * @param json
     * @return
     * @throws Exception
     */
    private JSONObject queryDataJson(JSONObject json) throws Exception {
        String out_trade_no = json.optString("out_trade_no");
        String tenancy_id = json.optString("tenancy_id");
        if(StringUtils.isBlank(tenancy_id)) {
            logger.info("微信轮训查询时redis中没找到out_trade_no="+out_trade_no+"所对应的信息");
            return null;
        }
        DBContextHolder.setTenancyid(tenancy_id);
        JSONObject jsonObject = alipayPaymentDao.queryOrderByOutTradeNo2Recently(tenancy_id, out_trade_no);
        return jsonObject;
    }

    /**
     * 请求微信服务器
     * @param saveJson
     * @return
     * @throws Exception
     */
    private JSONObject reqWechat(JSONObject saveJson) throws Exception {
        String out_trade_no = saveJson.optString("out_trade_no");
        String tenancy_id = saveJson.optString("tenancy_id");
        int store_id = saveJson.optInt("store_id");

        JSONObject param = new JSONObject();

        Map config = dao.getPaymentAccountConfig(tenancy_id,
                saveJson.optString("service_type"), store_id,
                Contant.PAYMENT_ACCOUNT_CONFIG_TYPE_WX);

        String sub_mch_id = config.get("partner").toString();
		// 证书密码
		Object cert_password = config.get("cert_password");
		if (cert_password != null
				&& StringUtils.isNotBlank(cert_password.toString())) {
			param.put("cert_password", cert_password);
		}
		param.put("sub_mch_id", sub_mch_id);
        param.put("out_trade_no", out_trade_no);
        param.put("url_type", Type.QUERY_PAY_STATE.toString());
        param.put("pay_type", saveJson.optString("pay_type"));
        JSONObject resultObj = WechatClient.orderquery(param);
        if (resultObj == null) {
            logger.info("轮询时调用微信接口失败");
            return null;
        }

        String return_code = resultObj.getString("return_code");
        if (!"SUCCESS".equals(return_code)) {
            return null;
        }

        return resultObj;
    }

    /**
     *  根据轮训的次数存储redis中的次数，并判断这个订单号是否需要执行
     * @param json
     * @return
     */
    public boolean isExecute(JSONObject json){
        String out_trade_no = json.optString("out_trade_no");
        int auto_num = json.optInt("auto_num");
        long time = json.optLong("time");
        boolean execute = isExecute(auto_num, time);
        if(execute) {
            auto_num++;
            json.put("auto_num",auto_num);
            if(!PaymentRedisCache.hasKey(out_trade_no)) {
                return false;
            }
            PaymentRedisCache.saveOutTradeNo2Info(out_trade_no,json.toString());// 更新执行次数
        }
        return execute;
    }

    /**
     * 是否已经到了该执行的时间
     * @param auto_num
     * @param time
     * @return
     */
    public boolean isExecute(int auto_num,long time){
        //   --------------查询发动频率15/15/30/180/1800/1800/1800/1800/3600/7200
        long currenttime = System.currentTimeMillis();

        auto_num  = auto_num - 50;

        if(auto_num < 0) {
            if (currenttime - time >= (long) (3 * 1000)) {
                return true;
            }
        }

        switch (auto_num) {
            case 0:
                if (currenttime - time >= (long) (15 * 1000)) {
                    return true;
                }
                break;
            case 1:
                if (currenttime - time >= (long) (30 * 1000)) {
                    return true;
                }
                break;
            case 2:
                if (currenttime - time >= (long) (60 * 1000)) {
                    return true;
                }
                break;
            case 3:
                if (currenttime - time >= (long) (240 * 1000)) {
                    return true;
                }
                break;
            case 4:
                if (currenttime - time >= (long) (2040 * 1000)) {
                    return true;
                }
                break;
            case 5:
                if (currenttime - time >= (long) (3840 * 1000)) {
                    return true;
                }
                break;
            case 6:
                if (currenttime - time >= (long) (5640 * 1000)) {
                    return true;
                }
                break;
            case 7:
                if (currenttime - time >= (long) (7440 * 1000)) {
                    return true;
                }
                break;
            case 8:
                if (currenttime - time >= (long) (11040 * 1000)) {
                    return true;
                }
                break;
            case 9:
                if (currenttime - time >= (long) (18240 * 1000)) {
                    return true;
                }
                break;
        }

        // 调试用
        //if (currenttime - time >= (long) (auto_num * 30 * 1000)) {
        //    return true;
        //}
        return false;
    }
}
