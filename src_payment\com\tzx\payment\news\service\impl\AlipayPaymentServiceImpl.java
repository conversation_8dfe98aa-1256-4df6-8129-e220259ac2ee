package com.tzx.payment.news.service.impl;

import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.MessageUtils;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.common.util.aliPay.UtilDate;
import com.tzx.payment.news.cache.PaymentUrlCache;
import com.tzx.payment.news.cont.Contant;
import com.tzx.payment.news.cont.StatusConstant;
import com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl;
import com.tzx.payment.news.util.PaymentJsonUtils;
import com.tzx.payment.news.util.PaymentRedisCache;
import com.tzx.payment.news.util.PaymentUtils;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
@Service("com.tzx.payment.news.service.impl.AlipayPaymentServiceImpl")
public class AlipayPaymentServiceImpl extends AbstractPaymentService {
	private static final Logger logger = Logger
			.getLogger(AlipayPaymentServiceImpl.class);
	
	@Resource(name="com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl")
	private AlipayPaymentDaoImpl paymentDao;
	
	/**
	 * 访问地址将结果存入json
	 * @param data
	 * @param result
	 * @param param
	 * @param reqJson
	 * @throws AlipayApiException 
	 */
	private void reqUrl(Data data, Data result, JSONObject param,
			JSONObject reqJson) throws AlipayApiException {
		logger.info("商户Id["+data.getTenancy_id()+"]机构Id["+data.getStore_id()+"]订单号["+param.optString("order_no")+"]请求支付宝给支付宝传递的参数为");
		logger.info(reqJson.toString());
		
		AlipayClient alipayClient = null;
        try{
            alipayClient = getAlipayClient(data, param);
        } catch (Throwable e) {
            logger.error(e);
        }
		Type type = data.getType();
		switch (type) {
		case GET_PREPAY_BARCODE:
			String isH5 = param.optString("isH5");
			if(StringUtils.equals(isH5,"1")) {
				getH5Req(alipayClient, reqJson, result);
			} else if(StringUtils.equals(isH5,"2")) {
                getJSAPIReq(alipayClient, reqJson, result);
            } else {
				getPrepayBarcodeReq(alipayClient, reqJson, result);
			}
			break;
		case PAY_ORDER_BY_CUSTOMER:
			payOrderByCustomerReq(alipayClient, reqJson, param,result);
			break;
		case CANCEL_PAY_ORDER:
			cancelPayOrderReq(alipayClient, reqJson,param, result);
			break;
		case CANCEL_PAY_ORDER_SUP:
			cancelPayOrderReq(alipayClient, reqJson,param, result);
			break;
		case REFUND_PAY_ORDER:
			refundPayOrderReq(alipayClient, reqJson, param,result);
			break;
		case QUERY_PAY_STATE:
			queryPayStateReq(alipayClient, reqJson, param,result,Boolean.FALSE);
			break;
		case QUERY_PAY_REFUND:
			queryPayStateReq(alipayClient, reqJson,param, result,Boolean.TRUE);
			break;
		default:
			break;
		}
	}

    private void getJSAPIReq(AlipayClient alipayClient, JSONObject reqJson, Data result) {
        AlipayTradeCreateRequest request = new AlipayTradeCreateRequest();
        request.setNotifyUrl(PaymentUrlCache.getUrl("ali_pay","PAY_NOTIFY_URL"));
        request.setBizContent(reqJson.toString());
        AlipayTradeCreateResponse response = null;
        try {
            response = alipayClient.execute(request);
            PaymentRedisCache.saveOutTradeNo2TenancyidAndTime("ali_pay",reqJson.optString("out_trade_no"),result.getTenancy_id());
        } catch (Exception e) {
            logger.error(e);
        }

        List<JSONObject> resultList = new ArrayList<JSONObject>();
        JSONObject json = new JSONObject();
        if (response==null) {
            json.put("payment_state", StatusConstant.STATUS_UNKNOW);
            resultList.add(json);
            result.setData(resultList);
            result.setMsg("网络请求错误");
            result.setSuccess(Boolean.FALSE);
            result.setCode(Contant.CALL_THIRD_FAILD);
            return;
        }
        logger.info("主扫支付宝返回的json串儿为"+response.getBody());
        result.setCode(Contant.SUCCESS);
        result.setSuccess(Boolean.TRUE);
        result.setMsg("调用成功");
        String fail_reason = response.getSubMsg();
        String fail_code = response.getSubCode();
        json.put("failure_code", fail_code);
        json.put("failure_msg", fail_reason);

        resultList.add(json);
        result.setData(resultList);
        if (response.isSuccess()) {
            String outTradeNo = response.getOutTradeNo();
            String tradeNo = response.getTradeNo();
            json.put("trade_no", tradeNo);
            json.put("out_trade_no", outTradeNo);
            json.put("payment_state", StatusConstant.STATUS_PROGRESS);
        } else {
            json.put("payment_state", StatusConstant.STATUS_FAIL);
            logger.info("扫描二维码失败，失败原因为:" + fail_reason);
        }
    }

    private void getH5Req(AlipayClient alipayClient, JSONObject reqJson, Data result) throws AlipayApiException {
		AlipayTradeWapPayRequest alipay_request=new AlipayTradeWapPayRequest();
		alipay_request.setBizContent(reqJson.toString());
		String form = alipayClient.pageExecute(alipay_request).getBody();
		try {
			PaymentRedisCache.saveOutTradeNo2TenancyidAndTime("ali_pay",reqJson.optString("out_trade_no"),result.getTenancy_id());
		} catch (Exception e) {
			logger.error(e);
		}
		JSONObject json = new JSONObject();
		json.put("form",form);
		List list = new ArrayList();
		list.add(json);
		result.setData(list);
	}

	/**
	 * 查询请求网关
	 * @param alipayClient
	 * @param reqJson
	 * @param param 
	 * @param result
	 */
	private void queryPayStateReq(AlipayClient alipayClient,
			JSONObject reqJson, JSONObject param, Data result,Boolean isRefund) {
		AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
		request.setBizContent(reqJson.toString());
		logger.info("查询请求参数："+reqJson.toString());
		AlipayTradeQueryResponse response = null;
		try {
			response = alipayClient.execute(request);
		} catch (Exception e) {
			logger.error(e);
		}
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		JSONObject json = new JSONObject();
		if (response==null) {
			json.put("payment_state", StatusConstant.STATUS_UNKNOW);
			resultList.add(json);
			result.setData(resultList);
			result.setMsg("网络请求错误");
			result.setCode(Contant.CALL_THIRD_FAILD);
			return;
		}
		logger.info("查询接口支付宝返回的josn串儿为"+response.getBody());
		result.setCode(Contant.SUCCESS);
		result.setSuccess(Boolean.TRUE);
		result.setMsg("调用成功");
		String fail_reason = response.getSubMsg();
		String fail_code = response.getSubCode();
		json.put("failure_code", fail_code);
		json.put("failure_msg", fail_reason);
		resultList.add(json);
		result.setData(resultList);
		
		if (response.isSuccess()) {
			String outTradeNo = response.getOutTradeNo();
			String tradeStatus = response.getTradeStatus();

			//WAIT_BUYER_PAY（交易创建，等待买家付款）
			//TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）
			//TRADE_SUCCESS（交易支付成功）
			//TRADE_FINISHED（交易结束，不可退款）
		
			if(isRefund.booleanValue()) {
				if (StringUtils.equals(tradeStatus, "TRADE_CLOSED")) {
					json.put("payment_state", StatusConstant.STATUS_REFUNDED);
				} else {
					json.put("payment_state", StatusConstant.STATUS_REFUND_FAIL);
				}
			} else {
				if(StringUtils.equals("TRADE_SUCCESS", tradeStatus) ||  StringUtils.equals("TRADE_FINISHED", tradeStatus) ) {
					json.put("payment_state", StatusConstant.STATUS_SUCCESS);
					com.alibaba.fastjson.JSONObject responseAliJson = com.alibaba.fastjson.JSONObject
							.parseObject(response.getBody());
					if(responseAliJson.containsKey("alipay_trade_query_response")) {
						responseAliJson = responseAliJson.getJSONObject("alipay_trade_query_response");
					}
					JSONObject responseJson = PaymentJsonUtils.convertAli2netJson(responseAliJson);
					PaymentJsonUtils.copySrc2Dest4jsonOption(responseJson,json,"receipt_amount","buyer_pay_amount"
					,"point_amount","invoice_amount","fund_bill_list","voucher_detail_list");
				} else if(StringUtils.equals("WAIT_BUYER_PAY", tradeStatus)){
					json.put("payment_state", StatusConstant.STATUS_PROGRESS);
				} else if(StringUtils.equals("TRADE_CLOSED", tradeStatus)){
					json.put("payment_state", StatusConstant.STATUS_REFUNDED);
				} else {
					json.put("payment_state", StatusConstant.STATUS_FAIL);
				}
			}
			json.put("transaction_no", outTradeNo);
			param.put("response", response.getBody());
		} else {
            json.put("payment_state", StatusConstant.STATUS_FAIL);
        }
	    logger.info("查询请求参数："+JSONObject.fromObject(result).toString());
	 }


    /**
     * 退款请求网关
     * @param reqJson
     * @param result
     * @throws AlipayApiException
     */
    private void cancel2refundPayOrderReq(
                                   JSONObject reqJson, JSONObject param,Data result) throws AlipayApiException {
        JSONObject paymentAccountConfig = (JSONObject) param.opt("paymentAccountConfig");
        // 欲访问的url
        String reqURL = PaymentUrlCache.getUrl(param.optString("pay_type"),"REFUND_PAY_ORDER");
        AlipayClient alipayClient = new DefaultAlipayClient(reqURL,paymentAccountConfig.optString("app_id"),
                paymentAccountConfig.optString("private_key"),
                Contant.PAYMENT_REQ_PARAM_TYPE,Contant.PAYMENT_REQ_CHARACTOR,
                paymentAccountConfig.optString("alipay_public_pay"));


        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        request.setBizContent(reqJson.toString());
        AlipayTradeRefundResponse response = null;
        try {
            response = alipayClient.execute(request);
        } catch (Exception e) {
            logger.error(e);
        }

        List<JSONObject> resultList = new ArrayList<JSONObject>();
        JSONObject json = new JSONObject();
        if (response==null) {
            json.put("payment_state", StatusConstant.STATUS_UNKNOW);
            resultList.add(json);
            result.setData(resultList);
            result.setMsg("网络请求错误");
            result.setCode(Contant.CALL_THIRD_FAILD);
            return;
        }
        logger.info("退款接口支付宝返回的json串儿为"+response.getBody());
        param.put("response",response.getBody());
        result.setCode(Contant.SUCCESS);
        result.setSuccess(Boolean.TRUE);
        result.setMsg("调用成功");
        String fail_reason = response.getSubMsg();
        String fail_code = response.getSubCode();
        json.put("failure_code", fail_code);
        json.put("failure_msg", fail_reason);

        resultList.add(json);
        result.setData(resultList);
        if (response.isSuccess()) {
            String outTradeNo = response.getTradeNo();
            json.put("payment_state", StatusConstant.STATUS_REFUNDED);
            json.put("transaction_no", outTradeNo);
            com.alibaba.fastjson.JSONObject responseAliJson = com.alibaba.fastjson.JSONObject
                    .parseObject(response.getBody());
        } else {
            json.put("payment_state", StatusConstant.STATUS_REFUND_FAIL);
        }
    }


	/**
	 * 退款请求网关
	 * @param alipayClient
	 * @param reqJson
	 * @param result
	 * @throws AlipayApiException
	 */
	private void refundPayOrderReq(AlipayClient alipayClient,
			JSONObject reqJson, JSONObject param,Data result) throws AlipayApiException {
		AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
		request.setBizContent(reqJson.toString());
		AlipayTradeRefundResponse response = null;
		try {
			response = alipayClient.execute(request);
		} catch (Exception e) {
			logger.error(e);
		}
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		JSONObject json = new JSONObject();
		if (response==null) {
			json.put("payment_state", StatusConstant.STATUS_UNKNOW);
			resultList.add(json);
			result.setData(resultList);
			result.setMsg("网络请求错误");
			result.setCode(Contant.CALL_THIRD_FAILD);
			return;
		}
        logger.info("退款接口支付宝返回的json串儿为"+response.getBody());
        param.put("response",response.getBody());
        result.setCode(Contant.SUCCESS);
        result.setSuccess(Boolean.TRUE);
        result.setMsg("调用成功");
        String fail_reason = response.getSubMsg();
        String fail_code = response.getSubCode();
        json.put("failure_code", fail_code);
        json.put("failure_msg", fail_reason);

        resultList.add(json);
        result.setData(resultList);
        if (response.isSuccess()) {
            String outTradeNo = response.getTradeNo();
            json.put("payment_state", StatusConstant.STATUS_SUCCESS);
            json.put("transaction_no", outTradeNo);
			com.alibaba.fastjson.JSONObject responseAliJson = com.alibaba.fastjson.JSONObject
					.parseObject(response.getBody());
			if(responseAliJson.containsKey("alipay_trade_refund_response")) {
				responseAliJson = responseAliJson.getJSONObject("alipay_trade_refund_response");
			}
			JSONObject responseJson = PaymentJsonUtils.convertAli2netJson(responseAliJson);
			PaymentJsonUtils.copySrc2Dest4jsonOption(responseJson,json,"refund_detail_item_list");
		} else {
			json.put("payment_state", StatusConstant.STATUS_FAIL);
		}
	}

	/**
	 * 取消订单请求网关
	 * @param alipayClient
	 * @param reqJson
	 * @param param
	 * @param result
	 * @throws AlipayApiException
	 */
	private void cancelPayOrderReq(AlipayClient alipayClient,
			JSONObject reqJson,JSONObject param, Data result) throws AlipayApiException {
		AlipayTradeCancelRequest request = new AlipayTradeCancelRequest();
		request.setBizContent(reqJson.toString());
		AlipayTradeCancelResponse response = null;
		try {
			response = alipayClient.execute(request);
		} catch (Exception e) {
			logger.error(e);
		}
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		JSONObject json = new JSONObject();
		if (response==null) {
			json.put("payment_state", StatusConstant.STATUS_UNKNOW);
			resultList.add(json);
			result.setData(resultList);
			result.setMsg("网络请求错误");
			result.setCode(Contant.CALL_THIRD_FAILD);
			return;
		}
		logger.info("取消接口支付宝返回的json串儿为"+response.getBody());
		result.setCode(Contant.SUCCESS);
		result.setSuccess(Boolean.TRUE);
		result.setMsg("调用成功");
		String fail_reason = response.getSubMsg();
		String fail_code = response.getSubCode();
		json.put("failure_code", fail_code);
		json.put("failure_msg", fail_reason);
		
		resultList.add(json);
		result.setData(resultList);
        String code = response.getCode();

        if (response.isSuccess() && StringUtils.equals(code,"10000")) {
            String outTradeNo = response.getTradeNo();
            json.put("transaction_no", outTradeNo);
            String action = response.getAction();
            if(StringUtils.equals("refund",action)) {
                json.put("payment_state", StatusConstant.STATUS_REFUNDED);
            } else {
                json.put("payment_state", StatusConstant.STATUS_CANCELED);
            }

            param.put("action",action);
            param.put("response",response.getBody());
		} else {
			json.put("payment_state", StatusConstant.STATUS_CANCEL_FAIL);

            JSONObject reqRefundJson = new JSONObject();
            reqRefundJson.put("out_trade_no", param.optString("out_trade_no"));
            String amount = param.optJSONObject("pos_payment_order").optString("total_amount");
            reqRefundJson.put("refund_amount",amount);
            cancel2refundPayOrderReq(reqRefundJson,param,result);
            /*Integer num = TimeUtils.getNum();
            logger.info("@@@@支付宝取消失败，第" +(num+1)+"次轮询" + response.getBody());
            if(num<3) {
                TimeUtils.addNum();
                cancelPayOrderReq(alipayClient,reqJson,param,result);
            }*/
		}
	}

	/**
	 * 被扫请求
	 * @param alipayClient
	 * @param reqJson
	 * @param param 
	 * @param result
	 * @throws AlipayApiException 
	 */
	private void payOrderByCustomerReq(AlipayClient alipayClient,
			JSONObject reqJson, JSONObject param, Data result) throws AlipayApiException {
		AlipayTradePayRequest request = new AlipayTradePayRequest();
		request.setBizContent(reqJson.toString());
		request.setNotifyUrl(PaymentUrlCache.getUrl(param.optString("pay_type"),"PAY_NOTIFY_URL"));
		AlipayTradePayResponse response = null;
		try {
			response = alipayClient.execute(request);
            PaymentRedisCache.saveOutTradeNo2TenancyidAndTime("ali_pay",reqJson.optString("out_trade_no"),result.getTenancy_id());
		} catch (Exception e) {
			logger.error(e);
		}
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		JSONObject json = new JSONObject();
		if (response==null) {
			json.put("payment_state", StatusConstant.STATUS_UNKNOW);
			resultList.add(json);
			result.setData(resultList);
			result.setMsg("网络请求错误");
			result.setCode(Contant.CALL_THIRD_FAILD);
			return;
		}
		logger.info("被扫支付宝返回的json串儿为"+response.getBody());
		result.setCode(Contant.SUCCESS);
		result.setSuccess(Boolean.TRUE);
		result.setMsg("调用成功");
		String fail_reason = response.getSubMsg();
		String fail_code = response.getSubCode();
		json.put("failure_code", fail_code);
		json.put("failure_msg", fail_reason);
		resultList.add(json);
		result.setData(resultList);
		
		if (response.isSuccess()) {
			String code = response.getCode();
			if(StringUtils.equals(code, "10000")) {
				json.put("payment_state", StatusConstant.STATUS_SUCCESS);
				com.alibaba.fastjson.JSONObject responseAliJson = com.alibaba.fastjson.JSONObject
						.parseObject(response.getBody());
				if(responseAliJson.containsKey("alipay_trade_pay_response")) {
					responseAliJson = responseAliJson.getJSONObject("alipay_trade_pay_response");
				}
				JSONObject responseJson = PaymentJsonUtils.convertAli2netJson(responseAliJson);
				PaymentJsonUtils.copySrc2Dest4jsonOption(responseJson,json,"receipt_amount","buyer_pay_amount"
						,"point_amount","invoice_amount","fund_bill_list","voucher_detail_list");
			} else {
				json.put("payment_state", StatusConstant.STATUS_PROGRESS);
			}
			String outTradeNo = response.getTradeNo();
			json.put("transaction_no", outTradeNo);
            param.put("response", response.getBody());
		} else {
			json.put("payment_state", StatusConstant.STATUS_FAIL);
			logger.info("调用第三方付失败:" + fail_reason);
		}
	}

	/**
	 * 扫客码请求
	 * @param alipayClient
	 * @param reqJson
	 * @param result
	 * @throws AlipayApiException
	 */
	private void getPrepayBarcodeReq(AlipayClient alipayClient,
			JSONObject reqJson, Data result) throws AlipayApiException {
		AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
		request.setNotifyUrl(PaymentUrlCache.getUrl("ali_pay","PAY_NOTIFY_URL"));
		request.setBizContent(reqJson.toString());
		AlipayTradePrecreateResponse response = null;
		try {
			response = alipayClient.execute(request);
            PaymentRedisCache.saveOutTradeNo2TenancyidAndTime("ali_pay",reqJson.optString("out_trade_no"),result.getTenancy_id());
		} catch (Exception e) {
			logger.error(e);
		}
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		JSONObject json = new JSONObject();
		if (response==null) {
			json.put("payment_state", StatusConstant.STATUS_UNKNOW);
			resultList.add(json);
			result.setData(resultList);
			result.setMsg("网络请求错误");
			result.setSuccess(Boolean.FALSE);
			result.setCode(Contant.CALL_THIRD_FAILD);
			return;
		}
		logger.info("主扫支付宝返回的json串儿为"+response.getBody());
		result.setCode(Contant.SUCCESS);
		result.setSuccess(Boolean.TRUE);
		result.setMsg("调用成功");
		String fail_reason = response.getSubMsg();
		String fail_code = response.getSubCode();
		json.put("failure_code", fail_code);
		json.put("failure_msg", fail_reason);
		
		resultList.add(json);
		result.setData(resultList);
		if (response.isSuccess()) {
			String outTradeNo = response.getOutTradeNo();
			String qrCode = response.getQrCode();
			json.put("qrcode", qrCode);
			json.put("payment_state", StatusConstant.STATUS_PROGRESS);
			json.put("transaction_no", outTradeNo);
		} else {
			json.put("payment_state", StatusConstant.STATUS_FAIL);
			logger.info("扫描二维码失败，失败原因为:" + fail_reason);
		}
	}

	/**
	 * 获取配置client
	 * @param data
	 * @param param
	 * @return
	 */
	private AlipayClient getAlipayClient(Data data, JSONObject param) {
		JSONObject paymentAccountConfig = (JSONObject) param.opt("paymentAccountConfig");
		// 欲访问的url
		String reqURL = PaymentUrlCache.getUrl(param.optString("pay_type"),data.getType().toString());
        String private_key = paymentAccountConfig.optString("private_key");
        AlipayClient alipayClient = null;
        if(StringUtils.isNotBlank(private_key) && private_key.length()>1024) {
            alipayClient = new DefaultAlipayClient(reqURL,paymentAccountConfig.optString("app_id"),
                    private_key,
                    Contant.PAYMENT_REQ_PARAM_TYPE,Contant.PAYMENT_REQ_CHARACTOR,
                    paymentAccountConfig.optString("alipay_public_pay"), "RSA2");
        } else {
            alipayClient = new DefaultAlipayClient(reqURL,paymentAccountConfig.optString("app_id"),
                    private_key,
                    Contant.PAYMENT_REQ_PARAM_TYPE,Contant.PAYMENT_REQ_CHARACTOR,
                    paymentAccountConfig.optString("alipay_public_pay"));
        }
		return alipayClient;
	}
	
	/* (non-Javadoc)
	 * @see com.tzx.payment.news.service.PaymentService#getPrepayBarcode(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void getPrepayBarcode(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
		getPrepayBarcodeSave4Previous(data, result, param);
		String timestamp = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss");
		param.put("timestamp", timestamp);
		String out_trade_no = UtilDate.getOutTradeOrder(data.getTenancy_id(), String.valueOf(data.getStore_id()));
		param.put("out_trade_no", out_trade_no);
		// 验证参数
		boolean validate = validate(param, result, "out_trade_no","total_amount","subject"
				,"service_type");
		if (!validate) {
			return;
		}
		JSONObject pakJSONObject = pakJSONObject(param, "out_trade_no","total_amount","subject");
		if(StringUtils.equals(param.optString("isH5"),"2")) {
            String buyer_id = param.optString("buyer_id");
            if(StringUtils.isBlank(buyer_id)) {
                result.setMsg("参数：buyer_id不允许为空");
                result.setCode(Contant.ILLEGAL_PARAM);
                logger.info("参数：buyer_id不允许为空");
                return;
            }
            pakJSONObject.put("buyer_id",buyer_id);
        }
		
		//extra信息
		JSONObject extraJsonObject = param.optJSONObject("extra");
		if(extraJsonObject!=null) {
			JSONObject aliPay = extraJsonObject.optJSONObject("ali_pay");
			if(aliPay!=null) {
				JSONArray goodDetail = aliPay.optJSONArray("goods_detail");
				if(goodDetail!=null) {
					pakJSONObject.put("goods_detail", goodDetail.toString());
				}
			}
		}
		
		//带给支付宝门店
		JSONObject paymentAccountConfig = (JSONObject) param.opt("paymentAccountConfig");
		
		if(StringUtils.equals(data.getTenancy_id(), "relyh")) {
			pakJSONObject.put("store_id", paymentAccountConfig.optString("organ_code"));
		} else {
			pakJSONObject.put("store_id", paymentAccountConfig.optString("org_full_name"));
		}
		
		//设置系统商编号
		JSONObject extend_params = new JSONObject();
		extend_params.put("sys_service_provider_id", Constant.systemMap.get("alipay_service_provider_id"));
		pakJSONObject.put("extend_params", extend_params);
		
		reqUrl(data, result, param, pakJSONObject);
		getPrepayBarcodeSave(data,result,param);
	}
	
	
	/**
	 * 主扫预订单
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception
	 */
	private void getPrepayBarcodeSave4Previous(Data data,Data result,JSONObject param) throws Exception {
		JSONObject json = new JSONObject();
		json.put("tenancy_id", data.getTenancy_id());
		json.put("store_id", data.getStore_id());
		PaymentJsonUtils.copySrc2Dest4jsonOption(param, json, "terminal_id","operator_id","out_trade_no","trade_no","total_amount","service_type","channel","report_date","shift");
		String orderNumStr = getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no"));
		
		int orderindex=orderNumStr.lastIndexOf("@");
		if(orderindex>0){
			json.put("order_num_pre", orderNumStr.substring(0, orderindex));
		}
		orderindex = param.optString("order_no").lastIndexOf("@");
		if(orderindex>0){
			json.put("bill_num", param.optString("order_no").substring(0, orderindex));
		}
		String billNum = param.optString("bill_num");
		if(StringUtils.isNotBlank(billNum)) {
			json.put("bill_num", billNum);
		}
		
		json.put("order_num", orderNumStr);
		String type = param.optJSONObject("paymentAccountConfig").optString("type");
		json.put("version", StatusConstant.PAYMENT_VERSION_ONE_ZERO);
		json.put("type", type);
		json.put("create_time", PaymentUtils.currentTime2Str());
		json.put("last_updatetime", PaymentUtils.currentTime2Str());
		json.put("status", StatusConstant.STATUS_PROGRESS);
		json.put("final_state", StatusConstant.STATUS_PROGRESS);
		json.put("sub_msg", "扫客码预下单");
		json.put("platform", 0); // 0 为原平台, 即付款平台方
        int id = (int) this.paymentDao.insertIgnorCase(data.getTenancy_id(), "pos_payment_order", json);
        json.put("id",id);
        param.put("pos_payment_order_insert",json);
	}

	/**
	 * 主扫保存数据
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception
	 */
	private void getPrepayBarcodeSave(Data data,Data result,JSONObject param) throws Exception {
		String orderNum = getOrderNum(data.getTenancy_id(), data.getStore_id(),param.optString("order_no") );
		JSONObject json = param.optJSONObject("pos_payment_order_insert");
		if(result.isSuccess()) {
			PaymentJsonUtils.copySrc2Dest4jsonOption(param, json, "out_trade_no","trade_no","total_amount","service_type");
			json.put("qr_code", getParam(result).optString("qrcode"));
			json.put("sub_code", result.getCode());
			json.put("sub_msg", result.getMsg());
			paymentDao.update4Json(data.getTenancy_id(), "pos_payment_order", json);
		}
	}

	/*
	 * 刷卡支付
	 * (non-Javadoc)
	 * @see com.tzx.payment.news.service.PaymentService#payOrderByCustomer(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void payOrderByCustomer(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
		//刷卡预支付
		payOrderByCustomerSave4pervious(data,result,param);
		//第三方订单号
		String out_trade_no = UtilDate.getOutTradeOrder(data.getTenancy_id(), String.valueOf(data.getStore_id()));
		param.put("out_trade_no", out_trade_no);
		// 验证参数
		param.put("scene", Contant.PAYMENT_REQ_SCENE_BAR_CODE);
		
		// 验证参数
		boolean validate = validate(param, result, "out_trade_no","scene","auth_code","subject"
				,"service_type");
		if (!validate) {
			return;
		}
		JSONObject pakJSONObject = pakJSONObject(param, "out_trade_no","scene","auth_code","subject","total_amount");
		
		//extra信息
		JSONObject extraJsonObject = param.optJSONObject("extra");
		if(extraJsonObject!=null) {
			JSONObject aliPay = extraJsonObject.optJSONObject("ali_pay");
			if(aliPay!=null) {
				JSONArray goodDetail = aliPay.optJSONArray("goods_detail");
				if(goodDetail!=null) {
					pakJSONObject.put("goods_detail", goodDetail.toString());
				}
			}
		}
		
		//带给支付宝门店
		JSONObject paymentAccountConfig = (JSONObject) param.opt("paymentAccountConfig");
		if(StringUtils.equals(data.getTenancy_id(), "relyh")) {
			pakJSONObject.put("store_id", paymentAccountConfig.optString("organ_code"));
		} else {
			pakJSONObject.put("store_id", paymentAccountConfig.optString("org_full_name"));
		}
		
		//设置系统商编号
		JSONObject extend_params = new JSONObject();
		extend_params.put("sys_service_provider_id", Constant.systemMap.get("alipay_service_provider_id"));
		pakJSONObject.put("extend_params", extend_params);
		
		
		//请求第三方
		reqUrl(data, result, param, pakJSONObject);
		//保存请求第三方后的信息
		payOrderByCustomerSave(data,result,param);
	}

	/**
	 * 被扫预支付
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception
	 */
	private void payOrderByCustomerSave4pervious(Data data, Data result,
			JSONObject param) throws Exception {
		JSONObject json = new JSONObject();
		json.put("tenancy_id", data.getTenancy_id());
		json.put("store_id", data.getStore_id());
		PaymentJsonUtils.copySrc2Dest4jsonOption(param, json, "terminal_id","operator_id","out_trade_no","trade_no","total_amount","service_type","channel","report_date","shift");
		String type = param.optJSONObject("paymentAccountConfig").optString("type");
		json.put("type", type);
		json.put("order_num", getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no")));
		json.put("bill_num",param.optString("order_no"));
		String billNum = param.optString("bill_num");
		if(StringUtils.isNotBlank(billNum)) {
			json.put("bill_num", billNum);
		}
		json.put("create_time", PaymentUtils.currentTime2Str());
		json.put("last_updatetime", PaymentUtils.currentTime2Str());
		
		//返回信息
		json.put("status", StatusConstant.STATUS_PROGRESS);
		json.put("final_state", StatusConstant.STATUS_PROGRESS);
		json.put("version", StatusConstant.PAYMENT_VERSION_ONE_ZERO);
		json.put("sub_msg", "预订单");
        json.put("platform", 0); // 0 为原平台, 即付款平台方
        int id = (int) this.paymentDao.insertIgnorCase(data.getTenancy_id(), "pos_payment_order", json);
        json.put("id",id);
        param.put("pos_payment_order_insert",json);
//        paymentDao.save4Json(data.getTenancy_id(), "pos_payment_order", json);
	}

	/**
	 * 被扫保存数据
	 * @param data
	 * @param param
	 * @throws Exception 
	 */
	private void payOrderByCustomerSave(Data data,Data result,JSONObject param) throws Exception {
		JSONObject json = param.optJSONObject("pos_payment_order_insert");
		//返回信息
		JSONObject resultJson = getParam(result);
		String paymentState = resultJson.optString("payment_state");
		PaymentJsonUtils.copySrc2Dest4jsonOption(param, json, "out_trade_no","trade_no","total_amount");
		json.put("status", paymentState);
		json.put("final_state", paymentState);
		if(StringUtils.equals(paymentState, String.valueOf(StatusConstant.STATUS_SUCCESS))) {
			json.put("finish_time", PaymentUtils.currentTime2Str());
		}
		json.put("sub_code", result.getCode());
		json.put("sub_msg", result.getMsg());
        addAssist("alipay_trade_pay_response",param,json);
		paymentDao.update4Json(data.getTenancy_id(), "pos_payment_order", json);
        queryPayState4SaveDetails("alipay_trade_pay_response",data,result,param);
    }

	/* 
	 * 查询交易状态
	 * (non-Javadoc)
	 * @see com.tzx.payment.news.service.PaymentService#queryPayState(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void queryPayState(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
        boolean validate = validate(param, result, "order_no");
        if (!validate) {
            return;
        }
		JSONObject json = paymentDao.queryOrderByOrderNo2Recently(data.getTenancy_id(), getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no")));
        if(json==null) {
            result.setCode(Contant.NO_ORDER);
            result.setMsg("订单不存在");
            List<JSONObject> resultList = new ArrayList();
            JSONObject resultJson = new JSONObject();
            resultJson.put("payment_state",StatusConstant.STATUS_UNKNOW);
            resultList.add(resultJson);
            result.setData(resultList);
            return;
        }

        //封装返回体
        List<JSONObject> resultList = new ArrayList<JSONObject>();
        result.setCode(Contant.SUCCESS);
        result.setSuccess(Boolean.TRUE);
        result.setMsg("调用成功");
        JSONObject resultJson = new JSONObject();
        //一码付需要告诉门店是支付宝支付
        resultJson.put("pay_type","ali_pay");
        resultJson.put("payment_state",json.optString("final_state"));
        resultJson.put("transaction_no",json.optString("trade_no"));
        resultJson.put("total_amount",json.optString("total_amount"));
        resultJson.put("receipt_amount",json.optString("shop_real_amount"));
        resultJson.put("buyer_pay_amount",json.optString("payment_amount"));
        resultJson.put("point_amount",json.optString("point_amount"));
        resultJson.put("invoice_amount",json.optString("invoice_amount"));
        List<JSONObject> list = paymentDao.queryOrderDetailsByOutTradeNo(data.getTenancy_id(), json.optString("out_trade_no"));
        if(!list.isEmpty()) {
            JSONArray array = new JSONArray();
            for(JSONObject j:list) {
                JSONObject newJson = new JSONObject();
                PaymentJsonUtils.copySrc2Dest4jsonOption(j,newJson,"fund_channel","amount","real_amount");
                array.add(newJson);
            }
            resultJson.put("fund_bill_list",array);
        }
        resultList.add(resultJson);
        result.setData(resultList);
	}

	/*
	 * 查询交易状态--老的
	 * (non-Javadoc)
	 * @see com.tzx.payment.news.service.PaymentService#queryPayState(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	public void queryPayStateOld(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
		JSONObject json = paymentDao.queryOrderByOrderNo2Recently(data.getTenancy_id(), getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no")));
		param.put("out_trade_no", json.optString("out_trade_no"));
		// 验证参数
		boolean validate = validate(param, result, "out_trade_no");
		if (!validate) {
			return;
		}
		JSONObject pakJSONObject = pakJSONObject(param, "out_trade_no");
		//请求第三方
		reqUrl(data, result, param, pakJSONObject);
		JSONObject resultJson = getParam(result);
		if(StringUtils.isNotBlank(json.optString("qr_code")) &&
				StringUtils.equals(json.optString("status"), String.valueOf(StatusConstant.STATUS_PROGRESS)) &&
					StringUtils.equals(resultJson.optString("failure_code"), "ACQ.TRADE_NOT_EXIST")) {
			@SuppressWarnings("unchecked")
			List<JSONObject> data2 = (List<JSONObject>) result.getData();
			JSONObject jsonObject = data2.get(0);
			JSONObject canceljson = paymentDao.queryCancelByOrderNo2Recently(data.getTenancy_id(), getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no")));
			logger.info("canceljson:"+JSONObject.fromObject(canceljson).toString());
			if(canceljson!=null){
				//如果此单在取消账单中有记录，则表名该账单已经取消过，此次查询是取消订单查询到此订单未交易的情况下则返回‘已取消’
				jsonObject.put("payment_state", StatusConstant.STATUS_CANCELED);
				result.setMsg("已取消");
			}else{
				jsonObject.put("payment_state", StatusConstant.STATUS_PROGRESS);
				result.setMsg("处理中");
			}
			jsonObject.put("transaction_no", pakJSONObject.optString("out_trade_no"));
			jsonObject.put("failure_code", Contant.SUCCESS);
			jsonObject.put("failure_msg", "成功");
			result.setCode(Contant.SUCCESS);

			result.setSuccess(Boolean.TRUE);
		} else {
			queryPayState4Save(data,result,param);
		}
	}


	/**
	 * 查询结果后更新数据库
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception
	 */
	private void queryPayState4Save(Data data, Data result, JSONObject param) throws Exception {
		String orderNum = getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no"));
		JSONObject resultJson = getParam(result);
		if(StringUtils.equals(resultJson.optString("payment_state"), String.valueOf(StatusConstant.STATUS_SUCCESS))
				|| StringUtils.equals(resultJson.optString("payment_state"), String.valueOf(StatusConstant.STATUS_FAIL))
				) {
			JSONObject json = paymentDao.queryOrderByOrderNo2Recently(data.getTenancy_id(), orderNum);
            String preStatus = json.optString("status");

            //增加buyer_loginid
			if(StringUtils.isBlank(json.optString("buyer_loginid"))  || StringUtils.equals("null", json.optString("buyer_loginid"))) {
				json.put("buyer_loginid", param.optString("buyer_loginid"));
			}
			//增加buyer_user_id
			if(StringUtils.isBlank(json.optString("buyer_user_id"))  || StringUtils.equals("null", json.optString("buyer_user_id"))) {
				json.put("buyer_user_id", param.optString("buyer_user_id"));
			}
			json.put("status", resultJson.optString("payment_state"));
            addAssist("alipay_trade_query_response",param,json);
			paymentDao.update4Json(data.getTenancy_id(), "pos_payment_order", json);
            //如果数据库中没有确定结果（不是已支付，已退款） 并且返回结果中确定了结果（已支付或已退款），保存明细
            if((!(StringUtils.equals(preStatus,String.valueOf(StatusConstant.STATUS_SUCCESS))
                    || StringUtils.equals(preStatus,String.valueOf(StatusConstant.STATUS_REFUNDED))))
                    && (StringUtils.equals(resultJson.optString("payment_state"),String.valueOf(StatusConstant.STATUS_SUCCESS))
                    || StringUtils.equals(resultJson.optString("payment_state"),String.valueOf(StatusConstant.STATUS_REFUNDED)))
                    ) {
                queryPayState4SaveDetails("alipay_trade_query_response",data,result,param);
            }
        }
	}

    /**
     * 增加辅助信息
     * @param param
     * @param json
     * @throws ParseException 
     */
    private void addAssist(String flagstr,JSONObject param, JSONObject json) {
        JSONObject queryJsonObject = getReturnJson(flagstr,param);
        if(queryJsonObject==null) {
        	return;
        }
        addAssist(json, queryJsonObject);


    }

    public void addAssist(JSONObject json, JSONObject queryJsonObject) {
        json.put("last_updatetime", PaymentUtils.currentTime2Str());
        String buyer_id = queryJsonObject.optString("buyer_user_id");
        String buyer_logon_id = queryJsonObject.optString("buyer_logon_id");
        //增加buyer_loginid
        if(StringUtils.isNotBlank(buyer_logon_id)  && !StringUtils.equals("null", buyer_logon_id)) {
            json.put("buyer_loginid", buyer_logon_id);
        }
        //增加buyer_user_id
        if(StringUtils.isNotBlank(buyer_id)  && !StringUtils.equals("null", buyer_id)) {
            json.put("buyer_user_id", buyer_id);
        }

        //支付宝交易号
        String trade_no = queryJsonObject.optString("trade_no");
        if(StringUtils.isNotBlank(trade_no)) {
            json.put("trade_no",trade_no);
        }
        //商家实收金额
        Double receipt_amount = queryJsonObject.optDouble("receipt_amount");
        if(receipt_amount!=null && !receipt_amount.isNaN()) {
            json.put("shop_real_amount",receipt_amount);
        }
        //买家实付金额
        Double buyer_pay_amount = queryJsonObject.optDouble("buyer_pay_amount");
        if(buyer_pay_amount!=null && !buyer_pay_amount.isNaN()) {
            json.put("payment_amount",buyer_pay_amount);
        }
        //积分支付金额
        Double point_amount = queryJsonObject.optDouble("point_amount");
        if(point_amount!=null && !point_amount.isNaN()) {
            json.put("point_amount",point_amount);
        }
        //可开具发票的金额
        Double invoice_amount = queryJsonObject.optDouble("invoice_amount");
        if(invoice_amount!=null && !invoice_amount.isNaN()) {
            json.put("invoice_amount",invoice_amount);
        }
        //scence支付场景
        json.put("trade_type", Contant.PAYMENT_REQ_SCENE_BAR_CODE);

        //平台优惠总额
        BigDecimal platformDiscountAmountSum = new BigDecimal(0);
        //商家优惠总额
        BigDecimal shopDiscountAmountSum = new BigDecimal(0);


        //交易的订单金额
        Double total_amount  = queryJsonObject.optDouble("total_amount");
        if(total_amount!=null && !total_amount.isNaN()) {
            json.put("total_amount",total_amount);
        }

        if(total_amount!=null && !total_amount.isNaN() && receipt_amount!=null && !receipt_amount.isNaN()) {
            BigDecimal total_amount_bigdecimal = new BigDecimal(total_amount);
            BigDecimal receipt_amount_bigdecimal = new BigDecimal(receipt_amount);
            shopDiscountAmountSum = total_amount_bigdecimal.subtract(receipt_amount_bigdecimal);
        }
        if(buyer_pay_amount!=null && !buyer_pay_amount.isNaN() && receipt_amount!=null && !receipt_amount.isNaN()) {
            BigDecimal receipt_amount_bigdecimal = new BigDecimal(receipt_amount);
            BigDecimal buyer_pay_amount_bigdecimal = new BigDecimal(buyer_pay_amount);
            platformDiscountAmountSum =receipt_amount_bigdecimal.subtract(buyer_pay_amount_bigdecimal);
        }

        platformDiscountAmountSum = platformDiscountAmountSum.setScale(2,BigDecimal.ROUND_HALF_UP);
        shopDiscountAmountSum = shopDiscountAmountSum.setScale(2,BigDecimal.ROUND_HALF_UP);
        BigDecimal settlement_total_fee = platformDiscountAmountSum.add(shopDiscountAmountSum);

        //平台优惠
        json.put("platform_discount_amount",platformDiscountAmountSum.toString());
        //商家优惠
        json.put("shop_discount_amount",shopDiscountAmountSum.toString());
        //应结订单金额
        json.put("settlement_total_fee",settlement_total_fee.toString());

        try {
        	//交易支付时间
        	String gmtPayment = queryJsonObject.optString("gmt_payment");
			Date gmsPaymentDate = DateUtils.parseDate(gmtPayment,
					new String[] { "yyyy-MM-dd HH:mm:ss" });
			//交易日期
			String trade_date = DateFormatUtils.format(gmsPaymentDate,
					"yyyy-MM-dd");
			json.put("trade_date", trade_date);
			json.put("trade_time", gmtPayment);
		} catch (Exception e) {
            json.put("trade_date", DateFormatUtils.format(System.currentTimeMillis(),
                    "yyyy-MM-dd"));
            json.put("trade_time", DateFormatUtils.format(System.currentTimeMillis(),
                     "yyyy-MM-dd HH:mm:ss"));
			logger.error(e);
		}
    }

    /**
	 * 查询后保存明细
	 * @param data
	 * @param result
	 * @param param
     */
	private void queryPayState4SaveDetails(String flagstr,Data data, Data result, JSONObject param) throws Exception {
        JSONObject queryJsonObject = getReturnJson(flagstr,param);
        if(queryJsonObject==null) {
        	return;
        }
        //交易支付使用的资金渠道
        if(queryJsonObject.containsKey("fund_bill_list")) {
            saveDetailItemList(data, param, queryJsonObject,Contant.PAYMENT_TRADE_REMARK_PAY);
        }
        //优惠券信息
        if(queryJsonObject.containsKey("voucher_detail_list")) {
            JSONArray voucherDetailArray = queryJsonObject.optJSONArray("voucher_detail_list");
            List<JSONObject> voucherDetailList = new ArrayList<JSONObject>();
            if(voucherDetailArray!=null && !voucherDetailArray.isEmpty()) {
                for(Object obj:voucherDetailArray){
                    JSONObject voucherDetail = (JSONObject) obj;
                    JSONObject saveJson = new JSONObject();
                    saveJson.put("tenancy_id",data.getTenancy_id());
                    saveJson.put("store_id",data.getStore_id());
                    String orderNum = getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no"));
                    saveJson.put("order_num",orderNum);
                    String type = param.optJSONObject("paymentAccountConfig").optString("type");
                    saveJson.put("type",type);
                    saveJson.put("out_trade_no",param.optString("out_trade_no"));
                    saveJson.put("discount_type",voucherDetail.optString("discount_type"));
                    Double amount = voucherDetail.optDouble("amount");
                    if(!amount.isNaN()) {
                        saveJson.put("real_amount",amount);
                    }
                    Double merchant_contribute = voucherDetail.optDouble("merchant_contribute");
                    if(!merchant_contribute.isNaN()) {
                        saveJson.put("merchant_contribute",merchant_contribute);
                    }
                    Double other_contribute = voucherDetail.optDouble("other_contribute");
                    if(!other_contribute.isNaN()) {
                        saveJson.put("other_contribute",other_contribute);
                    }
                    saveJson.put("memo",voucherDetail.optString("memo"));
                    voucherDetailList.add(saveJson);
                }
                this.paymentDao.insertBatchIgnorCase(data.getTenancy_id(),"pos_payment_alipay_discount",voucherDetailList);
            }

        }



    }

    /**
     * 保存支付来源信息
     * @param data
     * @param param
     * @param queryJsonObject
     * @param flag
     * @throws Exception
     */
    private void saveDetailItemList(Data data, JSONObject param, JSONObject queryJsonObject,String flag) throws Exception {
        JSONArray fundBillArray = null;
        if(StringUtils.equals(Contant.PAYMENT_TRADE_REMARK_PAY,flag)) {
            fundBillArray = queryJsonObject.optJSONArray("fund_bill_list");
        } else if(StringUtils.equals(Contant.PAYMENT_TRADE_REMARK_REFUND,flag)) {
            fundBillArray = queryJsonObject.optJSONArray("refund_detail_item_list");
        }
        List<JSONObject> fundBillList = new ArrayList<JSONObject>();
        if(fundBillArray!=null && !fundBillArray.isEmpty()) {
            for(Object fundBill:fundBillArray){
                JSONObject fundBillJson = (JSONObject) fundBill;
                JSONObject saveJson = new JSONObject();
                saveJson.put("tenancy_id",data.getTenancy_id());
                saveJson.put("store_id",data.getStore_id());
                String orderNum = getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no"));
                saveJson.put("order_num",orderNum);
                String type = param.optJSONObject("paymentAccountConfig").optString("type");
                saveJson.put("type",type);
                String fund_channel = fundBillJson.optString("fund_channel");
                saveJson.put("fund_channel",fund_channel);

                Double amount = fundBillJson.optDouble("amount");
                if(!amount.isNaN()) {
                    saveJson.put("amount",amount);
                }

                Double real_amount = fundBillJson.optDouble("real_amount");
                if(!real_amount.isNaN()) {
                    saveJson.put("real_amount",real_amount);
                }
                saveJson.put("out_trade_no",param.optString("out_trade_no"));
                saveJson.put("trade_mark", flag);
                fundBillList.add(saveJson);
            }
            this.paymentDao.insertBatchIgnorCase(data.getTenancy_id(),"pos_payment_flow",fundBillList);
        }
    }

    private JSONObject getReturnJson(String key,JSONObject param) {
        String body = param.optString("response");
        if(StringUtils.isBlank(body)) {
        	return null;
        }
        com.alibaba.fastjson.JSONObject responseAliJson = com.alibaba.fastjson.JSONObject
                .parseObject(body);
        JSONObject responseJson = PaymentJsonUtils.convertAli2netJson(responseAliJson);
        return responseJson.optJSONObject(key);
    }

    /* 退款
     * (non-Javadoc)
     * @see com.tzx.payment.news.service.PaymentService#refundPayOrder(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
     */
	@Override
	public void refundPayOrder(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
		refundPayOrderSave4Previous(data,result,param);
		//获取付款信息
		JSONObject json = paymentDao.queryOrderByOrderNo2Recently(data.getTenancy_id()
				,getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no")));

		param.put("pos_payment_order", json);
		
		param.put("out_trade_no", json.optString("out_trade_no"));
		String amount = param.optString("amount");
		if(StringUtils.isBlank(amount) || StringUtils.equals("0", amount)) {
			amount = json.optString("total_amount");
		}
		param.put("refund_amount",amount);
		// 验证参数
		boolean validate = validate(param, result, "out_trade_no","refund_amount"
				,"service_type","report_date","shift","channel");
		if (!validate) {
			return;
		}
		//封装第三方请求参数
		JSONObject pakJSONObject = pakJSONObject(param, "out_trade_no","refund_amount");
		//请求第三方
		reqUrl(data, result, param, pakJSONObject);
		//将请求结果保存到数据库中
		refundPayOrderSave(data,result,param);
	}


	/**
	 * 退款前置保存
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception
	 */
	private void refundPayOrderSave4Previous(Data data, Data result,
			JSONObject param) throws Exception {
		JSONObject json = new JSONObject();
		PaymentJsonUtils.copySrc2Dest4jsonOption(param, json, "out_trade_no","channel","report_date","shift","type","service_type");
		json.put("tenancy_id", data.getTenancy_id());
		json.put("store_id", data.getStore_id());
		json.put("order_num", getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no")));
        Double amount = param.optDouble("amount");
        if(!amount.isNaN()) {
            json.put("refund_amount", amount);
            json.put("refund_fee", amount);
        }
        json.put("bill_num", param.optString("order_no"));
        json.put("out_request_no", param.optString("out_trade_no"));
        json.put("out_trade_no", param.optString("out_trade_no"));
		json.put("terminal_id", param.optString("pos_num"));
		json.put("last_updatetime", PaymentUtils.currentTime2Str());
		String type = param.optJSONObject("paymentAccountConfig").optString("type");
		json.put("type", type);
		json.put("sub_msg", "预订单");
		json.put("status", StatusConstant.STATUS_PROGRESS);
		json.put("version", StatusConstant.PAYMENT_VERSION_ONE_ZERO);
		paymentDao.save4Json(data.getTenancy_id(), "pos_payment_refund", json);
	}

	/**
	 * 退款保存
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private void refundPayOrderSave(Data data, Data result, JSONObject param) throws Exception {
		JSONObject json = paymentDao.queryRefundByOrderNo2Recently(data.getTenancy_id(), 
				getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no")));
		json.put("tenancy_id", data.getTenancy_id());
		json.put("store_id", data.getStore_id());
		JSONObject resultJson = getParam(result);
		json.put("trade_no", resultJson.optString("transaction_no"));
		json.put("refund_amount", param.optString("refund_amount"));
		json.put("refund_fee", param.optString("refund_amount"));
		String paymentState = resultJson.optString("payment_state");
		json.put("status", paymentState);
		if(StringUtils.equals(paymentState, String.valueOf(StatusConstant.STATUS_SUCCESS))) {
			json.put("finish_time", PaymentUtils.currentTime2Str());
        }
		json.put("last_updatetime", PaymentUtils.currentTime2Str());
		
		try {
			JSONObject refundJsonObject = getReturnJson("alipay_trade_refund_response", param);
			if(refundJsonObject!=null) {
				String gmtRefundPay = refundJsonObject.optString("gmt_refund_pay");
				Date gmsPaymentDate = DateUtils.parseDate(gmtRefundPay,
						new String[] { "yyyy-MM-dd HH:mm:ss" });
				//交易日期
				String trade_date = DateFormatUtils.format(gmsPaymentDate,
						"yyyy-MM-dd");
				json.put("trade_date", trade_date);
				json.put("trade_time", gmtRefundPay);
			}
		} catch (Exception e) {
			logger.error(e);
		}
		
        paymentDao.update4Json(data.getTenancy_id(), "pos_payment_refund", json);
        refundPayOrderSaveDetails(data,result,param);
    }

    /**
     * 保存退款明细
     * @param data
     * @param result
     * @param param
     * @throws Exception
     */
    private void refundPayOrderSaveDetails(Data data, Data result, JSONObject param) throws Exception {
        JSONObject refundJsonObject = getReturnJson("alipay_trade_refund_response", param);
        if (refundJsonObject != null && refundJsonObject.containsKey("refund_detail_item_list")) {
            saveDetailItemList(data, param, refundJsonObject,Contant.PAYMENT_TRADE_REMARK_REFUND);
        }
    }

    /* (non-Javadoc)
     * @see com.tzx.payment.news.service.PaymentService#cancelPayOrder(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
     */
	@Override
	public void cancelPayOrder(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
		JSONObject json = null;
		String outTradeNo = param.optString("out_trade_no");
		if(StringUtils.isBlank(outTradeNo)) {
			String orderNum = param.optString("order_no");
			json = paymentDao.queryOrderByOrderNo2Recently(data.getTenancy_id(), getOrderNum(data.getTenancy_id(),
					data.getStore_id(), orderNum));
		} else {
			json = paymentDao.queryOrderByOutTradeNo2Recently(data.getTenancy_id(), outTradeNo);
		}

		if(json==null) {
            result.setCode(Contant.NO_ORDER);
            result.setMsg("订单不存在");
            List<JSONObject> resultList = new ArrayList();
            JSONObject resultJson = new JSONObject();
            resultJson.put("payment_state",StatusConstant.STATUS_UNKNOW);
            resultList.add(resultJson);
            result.setData(resultList);
			return;
		}
		param.put("pos_payment_order", json);
		param.put("out_trade_no", json.optString("out_trade_no"));
		
		// 验证参数
		boolean validate = validate(param, result, "out_trade_no","service_type");
		if (!validate) {
			return;
		}
		JSONObject pakJSONObject = pakJSONObject(param, "out_trade_no");
		reqUrl(data, result, param, pakJSONObject);
		cancelPayOrderSave(data,result,param);
	}


	public void cancelAllPayOrder(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
		List<JSONObject> list = null;
		String orderNum = param.optString("order_no");
		list = paymentDao.queryOrderByOrderNo(data.getTenancy_id(),param.optString("pay_type"), getOrderNum(data.getTenancy_id(),
				data.getStore_id(), orderNum));
		Map dataMap = (Map) data.getData().get(0);
		for(JSONObject json :list) {
			dataMap.put("out_trade_no", json.optString("out_trade_no"));
			cancelPayOrder(data,result);
		}
	}


	/**
	 * 取消订单保存
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private void cancelPayOrderSave(Data data, Data result, JSONObject param) throws Exception {
		JSONObject posPaymentOrder = param.optJSONObject("pos_payment_order");
		JSONObject json = new JSONObject();
		PaymentJsonUtils.copySrc2Dest4jsonOption(posPaymentOrder, json);
		JSONObject resultJson = getParam(result);
        String payment_state = resultJson.optString("payment_state");
        if(StringUtils.equals(payment_state,String.valueOf(StatusConstant.STATUS_CANCELED))) {
            json.put("status", StatusConstant.STATUS_SUCCESS);
        }
        if(StringUtils.equals(resultJson.optString("payment_state"), String.valueOf(StatusConstant.STATUS_SUCCESS))) {
            json.put("finish_time", PaymentUtils.currentTime2Str());
            json.put("trade_no", resultJson.optString("transaction_no"));
            json.put("action", param.optString("action"));
            json.put("trade_time", DateFormatUtils.format(System.currentTimeMillis(),
					"yyyy-MM-dd HH:mm:ss"));
            String trade_date = DateFormatUtils.format(System.currentTimeMillis(),
					"yyyy-MM-dd");
            json.put("trade_date", trade_date);

        }
        json.put("bill_num", posPaymentOrder.optString("bill_num"));
        json.put("last_updatetime", PaymentUtils.currentTime2Str());
        paymentDao.save4Json(data.getTenancy_id(), "pos_payment_cancel", json);

        //取消订单后修改主表的最终状态
        String action = param.optString("action");
        posPaymentOrder.put("final_state", resultJson.optString("payment_state"));
        posPaymentOrder.put("last_updatetime", PaymentUtils.currentTime2Str());
        paymentDao.updateIgnorCase(data.getTenancy_id(),"pos_payment_order",posPaymentOrder);
    }

	@Override
	public void queryPayRefund(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
		JSONObject json = paymentDao.queryOrderByOrderNo2Recently(data.getTenancy_id(), getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no")));
		param.put("out_trade_no", json.optString("out_trade_no"));
		// 验证参数
		boolean validate = validate(param, result, "out_trade_no");
		if (!validate) {
			return;
		}
		JSONObject pakJSONObject = pakJSONObject(param, "out_trade_no");
		//请求第三方
		reqUrl(data, result, param, pakJSONObject);
		queryPayRefund4Save(data,result,param);
	}
	
	
	/**
	 * 查询退款后保存退款数据
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception
	 */
	private void queryPayRefund4Save(Data data, Data result, JSONObject param) throws Exception {
		String orderNum = getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no"));
		JSONObject resultJson = getParam(result);
		if(StringUtils.equals(resultJson.optString("payment_state"), String.valueOf(StatusConstant.STATUS_SUCCESS))
				|| StringUtils.equals(resultJson.optString("payment_state"), String.valueOf(StatusConstant.STATUS_FAIL))
				) {
			JSONObject json = paymentDao.queryRefundByOrderNo2Recently(data.getTenancy_id(), orderNum);
			json.put("status", resultJson.optString("payment_state"));
			paymentDao.update4Json(data.getTenancy_id(), "pos_payment_refund", json);
		}
	}


    public void saveDetails(JSONObject jsonObject,JSONArray fundBillArray) throws Exception {
        List<JSONObject> fundBillList = new ArrayList<JSONObject>();
        String tenancy_id = jsonObject.optString("tenancy_id");
        if(fundBillArray!=null && !fundBillArray.isEmpty()) {
            for(Object fundBill:fundBillArray){
                JSONObject fundBillJson = (JSONObject) fundBill;
                JSONObject saveJson = new JSONObject();
                saveJson.put("tenancy_id",tenancy_id);
                saveJson.put("store_id",jsonObject.optString("store_id"));
                String orderNum = jsonObject.optString("order_num");
                saveJson.put("order_num",orderNum);
                saveJson.put("type",jsonObject.optString("type"));
                String fund_channel = fundBillJson.optString("fund_channel");
                saveJson.put("fund_channel",fund_channel);

                Double amount = fundBillJson.optDouble("amount");
                if(!amount.isNaN()) {
                    saveJson.put("amount",amount);
                }

                Double real_amount = fundBillJson.optDouble("real_amount");
                if(!real_amount.isNaN()) {
                    saveJson.put("real_amount",real_amount);
                }
                saveJson.put("out_trade_no",jsonObject.optString("out_trade_no"));
                saveJson.put("trade_mark", 1);
                fundBillList.add(saveJson);
            }
            this.paymentDao.insertBatchIgnorCase(tenancy_id,"pos_payment_flow",fundBillList);
        }
    }


    /**
     * 支付宝回调
     * @param json
     * @return
     * @throws Exception
     */
    @Override
    public String callback(JSONObject json) throws Exception {
        String out_trade_no = json.optString("out_trade_no");
        String trade_status = json.optString("trade_status");
        String tenancyId = PaymentRedisCache.getOutTradeNo2Tenancyid(out_trade_no);
        if(StringUtils.isBlank(tenancyId)) {
            logger.info("支付宝回调redis中没找到out_trade_no="+out_trade_no+"所对应的信息");
            return null;
        }
        try {
			logger.info("<"+tenancyId+">"+"哆啦宝支付成功回调:"+json.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}

        DBContextHolder.setTenancyid(tenancyId);
        JSONObject jsonObject = paymentDao.queryOrderByOutTradeNo2Recently(tenancyId, out_trade_no);
        if(!StringUtils.equals(trade_status,"TRADE_SUCCESS") || StringUtils.isBlank("TRADE_FINISHED")) {
            return null;
        }
        if(jsonObject.optInt("status") == StatusConstant.STATUS_SUCCESS) {
            PaymentRedisCache.deleteInfoByOutTradeNo(out_trade_no);
            return "success";
        }

        String buyer_id = json.optString("buyer_id");
        String buyer_logon_id = json.optString("buyer_logon_id");
        //增加buyer_loginid
        if(StringUtils.isNotBlank(buyer_logon_id)  && !StringUtils.equals("null", buyer_logon_id)) {
            jsonObject.put("buyer_loginid", buyer_logon_id);
        }
        //增加buyer_user_id
        if(StringUtils.isNotBlank(buyer_id)  && !StringUtils.equals("null", buyer_id)) {
            jsonObject.put("buyer_user_id", buyer_id);
        }
        addAssist(jsonObject,json);
        jsonObject.put("status",StatusConstant.STATUS_SUCCESS);

        if(jsonObject.optInt("final_state")==StatusConstant.STATUS_PROGRESS) {
            jsonObject.put("final_state", StatusConstant.STATUS_SUCCESS);
            jsonObject.put("finish_time", PaymentUtils.currentTime2Str());
        }

        this.paymentDao.updateIgnorCase(tenancyId,"pos_payment_order",jsonObject);

        List<JSONObject> list = paymentDao.queryOrderDetailsByOutTradeNo(tenancyId, out_trade_no);
        if(!list.isEmpty()) {
            PaymentRedisCache.deleteInfoByOutTradeNo(out_trade_no);
            return "success";
        }
        JSONArray fundBillArray = json.optJSONArray("fund_bill_list");
        saveDetails(jsonObject,fundBillArray);
        PaymentRedisCache.deleteInfoByOutTradeNo(out_trade_no);
        send2Md(jsonObject, "【支付宝】");
        return "success";
    }


    /**
     * 支付获取到支付结果后给门店下发。。
     * @param json
     * @param channel
     * @throws Exception
     */
    public void send2Md(JSONObject json, String channel) throws Exception {
        int final_state = json.optInt("final_state");
        if(!(final_state == StatusConstant.STATUS_SUCCESS
                || final_state == StatusConstant.STATUS_CANCELED
                || final_state == StatusConstant.STATUS_REFUNDED)) {
            logger.info("推送数据为:" + json);
            logger.info("未获得到明确的结果，不下发门店单号为："+json.optString("order_num"));
            //轮询没有得到明确结果，不下发到门店
            String out_trade_info = PaymentRedisCache.getInfoByOutTradeNo(json.optString("out_trade_no"));
            JSONObject task_json = JSONObject.fromObject(out_trade_info);
            PaymentRedisCache.lPushOutTradeNo2Info(task_json.optString("pay_type"), out_trade_info);
            return;
        }

        //封装下发数据
        List<JSONObject> resultList = new ArrayList<JSONObject>();
        JSONObject resultJson = new JSONObject();
        String tenancy_id = json.optString("tenancy_id");
        int store_id = json.optInt("store_id");

        resultJson.put("order_no",json.optString("bill_num"));

        //C扫B的时候推送的订单号是门店传递过来的单号
        String qr_code = json.optString("qr_code");
        if(StringUtils.isNotBlank(qr_code) && !StringUtils.equals("null",qr_code)) {
            String order_num = json.optString("order_num");
            String qrorder_before = tenancy_id+"_"+store_id+"_";
            String qrorder_behind = order_num.replaceAll(qrorder_before, StringUtils.EMPTY);
            resultJson.put("order_no",qrorder_behind);

            if(json.optInt("final_state")==StatusConstant.STATUS_CANCELED) {
                PaymentRedisCache.deleteInfoByOutTradeNo(json.optString("out_trade_no"));
            }
        }

		if(setSynReturn(tenancy_id, store_id, json)) {
			return;
		}

		int type = json.optInt("type");
		String payType = null;
		if(Contant.PAYMENT_ACCOUNT_CONFIG_TYPE_WX==type) {
			payType = "wechat_pay";
		} else if(Contant.PAYMENT_ACCOUNT_CONFIG_TYPE_Ali==type) {
			payType = "ali_pay";
		}
		resultJson.put("pay_type", payType);
		resultJson.put("payment_state",json.optString("final_state"));
        resultJson.put("transaction_no",json.optString("trade_no"));
        resultJson.put("total_amount",json.optString("total_amount"));
        resultJson.put("receipt_amount",json.optString("shop_real_amount"));
        resultJson.put("buyer_pay_amount",json.optString("payment_amount"));
        resultJson.put("point_amount",json.optString("point_amount"));
        resultJson.put("invoice_amount",json.optString("invoice_amount"));
        List<JSONObject> list = paymentDao.queryOrderDetailsByOutTradeNo(resultJson.optString("tenancy_id"), json.optString("out_trade_no"));
        if(!list.isEmpty()) {
            JSONArray array = new JSONArray();
            for(JSONObject j:list) {
                JSONObject newJson = new JSONObject();
                PaymentJsonUtils.copySrc2Dest4jsonOption(j,newJson,"fund_channel","amount","real_amount");
                array.add(newJson);
            }
            resultJson.put("fund_bill_list",array);
        }
        resultList.add(resultJson);




        String sql = "select org_uuid,id from organ where id = ?";
        List<JSONObject> organInfos = paymentDao.query4Json(tenancy_id, sql, new Object[]{store_id});
        if(organInfos.isEmpty()) {
            logger.info(channel + "获取到付款结果后没有找到该门店  门店id为"+store_id);
            return;
        }
        String org_uuid = organInfos.get(0).optString("org_uuid");

        MessageUtils message = new MessageUtils();
        Data d = Data.get();
        d.setType(Type.QUERY_PAY_STATE);
        d.setTenancy_id(tenancy_id);
        d.setData(resultList);
        d.setStore_id(store_id);
        logger.info(channel + "获取到付款结果后回传门店信息为为"+JSONObject.fromObject(d).toString());
        int returnId = message.sendMessage(JSONObject.fromObject(d).toString(), org_uuid, 1 , tenancy_id , String.valueOf("store_id"));
        PaymentRedisCache.deleteInfoByOutTradeNo(json.optString("out_trade_no"));
        if(returnId == 1){
            logger.info("=======================>> 轮询推送结果到门店成功!");
        } else {
            logger.info("=======================>> 轮询推送结果到门店失败!");
        }
    }

	public boolean setSynReturn(String tenancy_id, int store_id, JSONObject json) throws Exception {
		List<JSONObject> synList = paymentDao.queryOrderByOrderNo(tenancy_id, json.optString("order_num"));
		String pay_type = null;
		if(synList!=null && !synList.isEmpty()) {
			int successnum = 0;
			for(JSONObject synJson : synList) {
				int type1 = synJson.optInt("type");
				int finalState = synJson.optInt("final_state");
				if(finalState == StatusConstant.STATUS_SUCCESS) {
					successnum++;
					if(type1 == Contant.PAYMENT_ACCOUNT_CONFIG_TYPE_WX) {
						pay_type = "wechat_pay";
					} else if(type1 == Contant.PAYMENT_ACCOUNT_CONFIG_TYPE_Ali) {
						pay_type = "ali_pay";
					}
				}
			}
			if(successnum<2) {
				return false;
			}
			logger.info("回调的时候发现订单已经有别的支付记录，本支付的订单号为"+json.optString("order_num")+"out_trade_no="+json.optString("out_trade_no"));
			Data data = new Data();
			data.setType(Type.CANCEL_PAY_ORDER_SUP);
			data.setTenancy_id(tenancy_id);
			data.setStore_id(store_id);
			List<JSONObject> list = new ArrayList<JSONObject>();
			JSONObject object = new JSONObject();
			int type1 = json.optInt("type");
			if(type1 == Contant.PAYMENT_ACCOUNT_CONFIG_TYPE_WX) {
				object.put("pay_type","wechat_pay");
			} else if(type1 == Contant.PAYMENT_ACCOUNT_CONFIG_TYPE_Ali) {
				object.put("pay_type","ali_pay");
			}
			object.put("out_trade_no", json.optString("out_trade_no"));
			object.put("order_no", json.optString("order_num"));
			object.put("service_type", "pos04");
			list.add(object);
			data.setData(list);
			String url = PaymentUrlCache.getUrl("saas","url")+"/payment/news/post";
			String temp = JSON.toJSONString(data);
			String resultstr = HttpUtil.sendPostRequest(url, temp);
			logger.error("一码付该取消的订单"+resultstr);
		}
		return true;
	}
}
