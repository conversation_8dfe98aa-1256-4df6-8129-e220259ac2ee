package com.tzx.boh.service.rest;

import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.boh.bo.BusinessinComeManagementService;
import com.tzx.framework.bo.DataDictionaryService;
import com.tzx.framework.common.util.DateUtil;
/**
 * <AUTHOR>
 */
@Controller("BusinessinComeManagementContraller")
@RequestMapping("/boh/businessinComeManagementContraller")
public class BusinessinComeManagementRest
{
	@Resource(name = BusinessinComeManagementService.NAME)
	private BusinessinComeManagementService	businessinComeManagementService;

	@Autowired
	private DataDictionaryService	dataDictionaryService;

	/**
	 * 营业外收入信息列表
	 */
	@RequestMapping(value = "/loadBusinessinComeInformation")
	public void loadBusinessinComeInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("organ_code")){
				obj.put("organ_code",(String) session.getAttribute("organ_code"));
			}
			obj.put("is_zb",(String) session.getAttribute("organ_code"));
			result = businessinComeManagementService.loadBusinessinComeInformation((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 保存营业外收入信息
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/saveBusinessinComeInformation")
	public void saveBusinessinComeInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String,String[]> map = request.getParameterMap();
			for(String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			obj.put("store_id", (String) session.getAttribute("organ_id"));
			obj.put("inout_property", "SR01");
			Object dic = dataDictionaryService.save((String) session.getAttribute("tenentid"), "boh_inout_info", obj);
			if (dic != null) result = "{\"success\": true, \"id\" : \"" + dic.toString() + "\"}";
		}
		catch (Exception e)
		{
			result = "{\"success\": false, \"msg\" : \"" + e.getMessage() + "\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	@RequestMapping(value = "/checkUnique")
	public void checkUnique(HttpServletRequest request, HttpServletResponse response)
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			boolean rs = businessinComeManagementService.checkUnique((String) session.getAttribute("tenentid"),obj);

			if(rs)
			{
				result = "{\"success\": true}";
			}else
			{
				result = "{\"success\": false}";
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	/**
	 * 查询收支类型名称
	 */
	@RequestMapping(value = "/findInOutTypeName")
	public void findInOutTypeName(HttpServletRequest request, HttpServletResponse response)
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("store_id",(String) session.getAttribute("organ_id"));
			
			result =businessinComeManagementService.findInOutTypeName((String) session.getAttribute("tenentid"),p.get("type").toString(),p);

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	//查询本机构的付款方式
	
	@RequestMapping(value = "/findPayStyle")
	public void findPayStyle(HttpServletRequest request, HttpServletResponse response)
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("store_id",(String) session.getAttribute("organ_id"));
			
			result =businessinComeManagementService.findPayStyle((String) session.getAttribute("tenentid"),p.get("type").toString(),p);

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
}
