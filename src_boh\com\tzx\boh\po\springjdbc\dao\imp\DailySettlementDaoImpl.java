package com.tzx.boh.po.springjdbc.dao.imp;

import com.tzx.boh.po.springjdbc.dao.DailySettlementDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 日结DAO实现
 * Created by <PERSON> on 2016-08-22.
 */
@Repository(DailySettlementDao.Name)
public class DailySettlementDaoImpl implements DailySettlementDao {


    @Override
    public void getRepairPayment(StringBuilder sql, Map<String, String> map, List param) throws Exception {
        sql.append("update pos_bill_payment p set\n");
        sql.append("  currency_amount=round((p.amount * (case when p.rate=0 or p.rate is null then 1 else p.rate end)),2)\n");
        sql.append("where p.bill_num in (\n");
        sql.append("  select bill_num from pos_bill\n");
        sql.append("  where 1=1\n");

        // TODO 修改添加的条件
        sql.append("  and store_id=5 and report_date='2016-08-07' and bill_property='CLOSED'\n");


        sql.append(")");
    }

    @Override
    public void getInsertPosBill(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("insert into pos_bill2 (\n");
        sql.append("   tenancy_id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time\n");
        sql.append("  ,payment_num,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id\n");
        sql.append("  ,service_amount,service_discount,order_num,print_time,print_count,subtotal,bill_amount,payment_amount\n");
        sql.append("  ,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount\n");
        sql.append("  ,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,discount_rate,billfree_reason_id\n");
        sql.append("  ,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,deposit_count,copy_bill_num\n");
        sql.append("  ,source,opt_login_number,guest_msg,integraloffset,remark,return_amount,advance_payment_amt,advance_refund_amt\n");
        sql.append("  ,is_refund,payment_id,pay_no,third_bill_code\n");
        sql.append(") select\n");
        sql.append("   tenancy_id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time\n");
        sql.append("  ,payment_num,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id\n");
        sql.append("  ,service_amount,service_discount,order_num,print_time,print_count,subtotal,bill_amount,payment_amount\n");
        sql.append("  ,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount\n");
        sql.append("  ,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,discount_rate,billfree_reason_id\n");
        sql.append("  ,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,deposit_count,copy_bill_num\n");
        sql.append("  ,source,opt_login_number,guest_msg,integraloffset,remark,return_amount,advance_payment_amt,advance_refund_amt\n");
        sql.append("  ,is_refund,payment_id,pay_no,third_bill_code\n");
        sql.append("from pos_bill\n");
        sql.append("where 1=1\n");

        // TODO 替换条件
        sql.append(" and store_id=5 and report_date='2016-08-07' and bill_property='CLOSED'\n");

        sql.append(" order by id\n");
    }

    @Override
    public void getInsertPosBillItem(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("insert into pos_bill_item2 (\n");
        sql.append("   tenancy_id,store_id,rwid,yrwid,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id\n");
        sql.append("  ,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price\n");
        sql.append("  ,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state\n");
        sql.append("  ,discount_rate,discount_mode_id,item_class_id,item_property,item_remark,print_tag,waitcall_tag,setmeal_id\n");
        sql.append("  ,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,report_date,item_shift_id,item_mac_id,item_taste\n");
        sql.append("  ,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag\n");
        sql.append("  ,assist_item_id,integraloffset,remark,setmeal_group_id,group_id,third_price,returngive_reason_id,manager_num\n");
        sql.append("  ,return_type,return_count,method_money\n");
        sql.append(") select\n");
        sql.append("   d.tenancy_id, d.store_id, d.rwid, d.yrwid, d.bill_num, d.details_id, d.item_id, d.item_num, d.item_name\n");
        sql.append("  ,d.item_english, d.item_unit_id, d.item_unit_name, d.stable_code, d.table_code, d.pushmoney_way, d.proportion\n");
        sql.append("  ,d.assist_num, d.assist_money, d.waiter_num, d.item_price, d.item_count, d.item_amount, d.real_amount\n");
        sql.append("  ,d.discount_amount, d.single_discount_amount, d.discountr_amount, d.discount_state, d.discount_rate\n");
        sql.append("  ,d.discount_mode_id, d.item_class_id, d.item_property, d.item_remark, d.print_tag, d.waitcall_tag, d.setmeal_id\n");
        sql.append("  ,d.setmeal_rwid, d.is_setmeal_changitem, d.item_time, d.item_serial, d.report_date, d.item_shift_id\n");
        sql.append("  ,d.item_mac_id, d.item_taste, d.order_remark, d.seat_num, d.ticket_num, d.sale_mode, d.gqcj_tag, d.kvscp_tag\n");
        sql.append("  ,d.discount_reason_id, d.is_showitem, d.upload_tag, d.assist_item_id, d.integraloffset, d.remark, d.setmeal_group_id\n");
        sql.append("  ,d.group_id, d.third_price, d.returngive_reason_id, d.manager_num, d.return_type, d.return_count, d.method_money\n");
        sql.append("from pos_bill_item d\n");
        sql.append("left join pos_bill m on d.bill_num=m.bill_num\n");
        sql.append("where 1=1\n");

        // TODO 替换条件
        sql.append(" and m.store_id=5 and m.report_date='2016-08-07' and m.bill_property='CLOSED' ");

        sql.append("order by d.rwid\n");
    }

    @Override
    public void getInsertPosBillPayment(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("insert into pos_bill_payment2 (\n");
        sql.append("   tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date\n");
        sql.append("  ,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code\n");
        sql.append("  ,remark\n");
        sql.append(") select\n");
        sql.append("    d.tenancy_id, d.store_id, d.bill_num, d.table_code, d.type, d.jzid, d.name, d.name_english, d.amount, d.count\n");
        sql.append("    ,d.number, d.phone, d.report_date, d.shift_id, d.pos_num, d.cashier_num, d.last_updatetime, d.is_ysk, d.rate\n");
        sql.append("    ,d.currency_amount, d.upload_tag, d.customer_id, d.bill_code, d.remark\n");
        sql.append("from pos_bill_payment d\n");
        sql.append("left join pos_bill m on d.bill_num=m.bill_num\n");
        sql.append("where 1=1\n");

        // TODO 替换条件
        sql.append(" and m.store_id=5 and m.report_date='2016-08-07' and m.bill_property='CLOSED'\n");

        sql.append("order by d.id\n");

    }

    @Override
    public void getUnusualReason(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("insert into hq_daycount_unusual(\n");
        sql.append("  tenancy_id,store_id,day_count,unusual_type,unusual_id,bill_num,bill_money,unusual_money,remark\n");
        sql.append(") select\n");

        // TODO
        sql.append("    'gzbljl' as tenancy_id,5 as store_id,to_date('2016-08-07', 'YYYY-MM-DD') as day_count\n");

        sql.append("    ,r1.unusual_type as unusual_type,m1.billfree_reason_id as unusual_id,count(m1.bill_num) as bill_num\n");
        sql.append("    ,sum(m1.bill_amount) as bill_money,sum(m1.bill_amount) as unusual_money,'免单原因统计' as remark\n");
        sql.append("from pos_bill m1\n");
        sql.append("left join hq_unusual_reason r1 on m1.billfree_reason_id = r1.id\n");

        // TODO
        sql.append("where m1.billfree_reason_id is not null and m1.billfree_reason_id != 0\n");
        sql.append("  and m1.store_id = 5 and m1.report_date = '2016-08-07' and m1.bill_property = 'CLOSED'\n");
        sql.append("group by m1.billfree_reason_id, r1.unusual_type\n");
        sql.append("union all\n");
        sql.append("select\n");

        // TODO
        sql.append("  'gzbljl' as tenancy_id,5 as store_id,to_date('2016-08-07', 'YYYY-MM-DD') as day_count,m.unusual_type,m.unusual_id\n");
        sql.append("  ,count(m.bill_num) as bill_num,sum(m.bill_amount) as bill_money,sum(m.unusual_money) as unusual_money,'退菜奉送原因统计' as remark\n");
        sql.append("from (\n");
        sql.append("  select r1.unusual_type as unusual_type,d1.returngive_reason_id as unusual_id\n");
        sql.append("    ,m1.bill_num,m1.bill_amount,sum(d1.real_amount) as unusual_money\n");
        sql.append("  from pos_bill_item d1\n");
        sql.append("  join pos_bill m1 on d1.bill_num = m1.bill_num\n");
        sql.append("  left join hq_unusual_reason r1 on d1.returngive_reason_id = r1.id\n");

        // TODO
        sql.append("  where d1.returngive_reason_id is not null and d1.returngive_reason_id != 0 and m1.store_id = 5\n");
        sql.append("    and m1.report_date = '2016-08-07' and m1.bill_property = 'CLOSED'\n");
        sql.append("  group by m1.bill_num,m1.bill_amount,d1.returngive_reason_id,r1.unusual_type\n");
        sql.append(") m  group by m.unusual_id, m.unusual_type\n");
        sql.append("union all\n");
        sql.append("select \n");
        sql.append("  'gzbljl' as tenancy_id,5 as store_id,to_date('2016-08-07', 'YYYY-MM-DD') as day_count,m.unusual_type,m.unusual_id\n");
        sql.append("  ,count(m.bill_num) as bill_num,sum(m.bill_amount) as bill_money,sum(m.unusual_money) as unusual_money,'折扣原因统计' as remark\n");
        sql.append("from (\n");
        sql.append("  select r1.unusual_type as unusual_type,d1.discount_reason_id as unusual_id\n");
        sql.append("    ,m1.bill_num,m1.bill_amount,(sum(d1.discount_amount) + sum(d1.discountr_amount)) as unusual_money\n");
        sql.append("  from pos_bill_item d1\n");
        sql.append("  join pos_bill m1 on d1.bill_num = m1.bill_num\n");
        sql.append("  left join hq_unusual_reason r1 on d1.discount_reason_id = r1.id\n");
        sql.append("  where d1.discount_reason_id is not null and d1.discount_reason_id != 0 and m1.store_id = 5\n");
        sql.append("  and m1.report_date = '2016-08-07' and m1.bill_property = 'CLOSED'\n");
        sql.append("  group by m1.bill_num,m1.bill_amount,d1.discount_reason_id,r1.unusual_type\n");
        sql.append(") m group by m.unusual_id, m.unusual_type\n");
    }

    @Override
    public void getPayment(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("insert into hq_payment_count(\n");
        sql.append("  tenancy_id,store_id,day_count,payment_class,payment_id,pay_money,local_currency,sale_total\n");
        sql.append(") select\n");

        // TODO
        sql.append("  'gzbljl' as tenancy_id,5 as store_id,to_date('2016-08-07', 'YYYY-MM-DD') as day_count,w.payment_class \n");
        sql.append("  ,p.jzid as payment_id,sum(p.amount) as pay_money,sum(p.currency_amount) as local_currency,sum(m.bill_amount) as sale_total \n");
        sql.append("from pos_bill_payment p \n");
        sql.append("join payment_way w on p.jzid = w.id \n");
        sql.append("left join pos_bill m on p.bill_num=m.bill_num \n");

        // TODO
        sql.append("where p.store_id = 5 and p.report_date = to_date('2016-08-07','yyyy-mm-dd') and m.bill_property = 'CLOSED' \n");
        sql.append("group by w.payment_class, p.jzid \n");

    }

    @Override
    public void getItem(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("insert into hq_daycount_item(\n");
        sql.append("   tenancy_id,store_id,day_count,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id\n");
        sql.append("  ,item_unit_price,item_pro,list_num, list_money,sales_num,sales_money,back_num,back_money,free_num,free_money\n");
        sql.append("  ,favor_money,discount_money,reduction_money,change_money,actual_num,actual_money,net_money,sale_billnum\n");
        sql.append("  ,sale_billaverage,table_property_id,business_area_id,guest_num,tables_num\n");
        sql.append(") select\n");

        // TODO
        sql.append("   'gzbljl' as tenancy_id\n");
        sql.append("  ,5 as store_id,to_date('2016-08-07', 'YYYY-MM-DD') as day_count,sale_model,combo_id,combo_unit_id,combo_price\n");

        sql.append("  ,item_id,item_unit_id,item_unit_price,item_pro,sum(list_num) as list_num,sum(list_money) as list_money\n");
        sql.append("  ,sum(sales_num) as sales_num,sum(sales_money) as sales_money,sum(back_num) as back_num\n");
        sql.append("  ,sum(back_money) as back_money,sum(free_num) as free_num,sum(free_money) as free_money,sum(favor_money) as favor_money\n");
        sql.append("  ,sum(discount_money) as discount_money,sum(reduction_money) as reduction_money,sum(change_money) as change_money\n");
        sql.append("  ,sum(actual_num) as actual_num,sum(actual_money) as actual_money,sum(net_money) as net_money,max(p.sale_billnum) sale_billnum\n");
        sql.append("  ,null sale_billaverage,table_property_id,business_area_id,coalesce(sum(guest_counts), 0) as guest_num\n");
        sql.append("  ,max(p.sale_billnum) as tables_num\n");
        sql.append("from (\n");
        sql.append("    select\n");
        sql.append("      d.store_id, d.report_date, count( d.bill_num) as sale_billnum\n");
        sql.append("    from pos_bill d\n");

        // TODO
        sql.append("    where d.store_id=5 and d.report_date='2016-08-07' and d.bill_property='CLOSED'\n");
        sql.append("      and coalesce(d.bill_state, 'zc') not in ('CJ01', 'ZDQX02')\n");
        sql.append("    group by d.store_id, d.report_date\n");
        sql.append(") p join (\n");
        sql.append("    select\n");
        sql.append("       d.store_id,d.report_date,d.sale_mode as sale_model,d.setmeal_id as combo_id\n");
        sql.append("      ,case d.item_property when 'SETMEAL' then d.item_unit_id end as combo_unit_id\n");
        sql.append("      ,case when d.item_property='SETMEAL' then d.item_price end as combo_price\n");
        sql.append("      ,d.item_id,d.item_unit_id,d.item_price as item_unit_price,d.item_property as item_pro\n");
        sql.append("      ,case when d.item_remark in ('CJ05') then 0 else d.item_count end as list_num\n");
        sql.append("      ,case when d.item_remark in ('CJ05') then 0 else d.item_amount end as list_money\n");
        sql.append("      ,d.item_count as sales_num,d.item_amount as sales_money\n");
        sql.append("      ,case when d.item_remark in ('TC01', 'QX04') then coalesce(item_count, 0) else 0 end as back_num\n");
        sql.append("      ,case when d.item_remark in ('TC01', 'QX04') then coalesce(real_amount, 0) else 0 end as back_money\n");
        sql.append("      ,case when d.item_remark='FS02' then coalesce(d.item_count, 0) else 0 end as free_num\n");
        sql.append("      ,case when d.item_remark='FS02' then coalesce(d.item_amount, 0) else 0 end as free_money\n");
        sql.append("      ,(d.discount_amount + d.discountr_amount - d.single_discount_amount) as favor_money\n");
        sql.append("      ,case when d.item_remark in ('TC01', 'FS02') then 0 else (d.discount_amount) end as discount_money\n");
        sql.append("      ,case when d.item_remark in ('TC01', 'FS02') then 0 else (d.discountr_amount) end as reduction_money\n");
        sql.append("      ,d.single_discount_amount as change_money\n");
        sql.append("      ,case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05')\n");
        sql.append("      then\n");
        sql.append("       0\n");
        sql.append("      else\n");
        sql.append("       d.item_count\n");
        sql.append("      end as actual_num\n");
        sql.append("      ,case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05')\n");
        sql.append("      then\n");
        sql.append("       0\n");
        sql.append("      else\n");
        sql.append("       d.real_amount\n");
        sql.append("      end as actual_money\n");
        sql.append("      ,case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05')\n");
        sql.append("      then\n");
        sql.append("       0\n");
        sql.append("      else\n");
        sql.append("       d.real_amount\n");
        sql.append("      end as net_money\n");
        sql.append("      ,m.bill_num,t.table_property_id,t.business_area_id, m.guest as guest_counts, m.bill_state\n");
        sql.append("    from pos_bill_item d\n");
        sql.append("    join pos_bill m on d.bill_num = m.bill_num\n");
        sql.append("    left join tables_info t on t.table_code = m.table_code and organ_id = 5\n");

        // TODO
        sql.append("    where m.store_id = 5 and m.report_date = '2016-08-07'and m.bill_property = 'CLOSED'\n");
        sql.append(") a on p.store_id = a.store_id and p.report_date = a.report_date \n");
        sql.append("group by sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,table_property_id,business_area_id\n");

    }

    @Override
    public void getUpItem(StringBuilder sql, Map<String, String> map, List param) throws Exception {
        sql.append("update hq_daycount_item t set\n");
        sql.append("   combo_unit_id = t1.combo_unit_id\n");
        sql.append("  ,combo_price = t1.combo_price \n");
        sql.append("from hq_daycount_item t1 \n");

        // TODO
        sql.append("where t.combo_id = t1.combo_id and t1.item_pro = 'SETMEAL' and t.item_pro = 'MEALLIST' \n");
        sql.append("  and t.day_count = '2016-08-07' and t.store_id=5\n");

    }

    @Override
    public void getItemTimes(StringBuilder sql, Map<String, String> map, List param) throws Exception {
        sql.append("insert into hq_daycount_item_times(\n");
        sql.append("   tenancy_id,store_id,day_count,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id\n");
        sql.append("  ,item_unit_price,item_pro,list_num, list_money,sales_num,sales_money,back_num,back_money,free_num,free_money\n");
        sql.append("  ,favor_money,change_money,actual_num,actual_money,net_money,sale_billnum,sale_billaverage,times_id\n");
        sql.append(") select\n");

        // TODO
        sql.append("  'gzbljl' as tenancy_id,5 as store_id,to_date('2016-08-07', 'YYYY-MM-DD') as day_count,sale_model,combo_id\n");
        sql.append("  ,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,sum(list_num) as list_num\n");
        sql.append("  ,sum(list_money) as list_money,sum(sales_num) as sales_num,sum(sales_money) as sales_money,sum(back_num) as back_num\n");
        sql.append("  ,sum(back_money) as back_money,sum(free_num) as free_num,sum(free_money) as free_money,sum(favor_money) as favor_money\n");
        sql.append("  ,sum(change_money) as change_money, sum(actual_num) as actual_num,sum(actual_money) as actual_money\n");
        sql.append("  ,sum(net_money) as net_money,max(sale_billnum) sale_billnum,null sale_billaverage,times_id\n");
        sql.append("from (\n");
        sql.append("  select\n");
        sql.append("    d.store_id, d.report_date,count( d.bill_num) as sale_billnum\n");
        sql.append("  from pos_bill d\n");

        // TODO
        sql.append("  where d.store_id=5 and d.report_date = '2016-08-07' and d.bill_property='CLOSED'\n");
        sql.append("     and coalesce(d.bill_state, 'zc') not in ('CJ01', 'ZDQX02')\n");
        sql.append("  group by d.store_id, d.report_date\n");
        sql.append(") p join (\n");
        sql.append("  select\n");
        sql.append("     d.store_id,d.report_date,m.sale_mode as sale_model,d.setmeal_id as combo_id\n");
        sql.append("    ,case d.item_property when 'SETMEAL' then d.item_unit_id end as combo_unit_id\n");
        sql.append("    ,case when d.item_property='SETMEAL' then d.item_price end as combo_price\n");
        sql.append("    ,d.item_id,d.item_unit_id,d.item_price as item_unit_price,d.item_property as item_pro\n");
        sql.append("    ,case when d.item_remark in ('CJ05') then 0 else d.item_count end as list_num\n");
        sql.append("    ,case when d.item_remark in ('CJ05') then 0 else d.item_amount end as list_money\n");
        sql.append("    ,d.item_count as sales_num,d.item_amount as sales_money\n");
        sql.append("    ,case when d.item_remark in ('TC01', 'QX04') then coalesce(return_count, 0) else 0 end as back_num\n");
        sql.append("    ,case when d.item_remark in ('TC01', 'QX04') then coalesce(real_amount, 0) else 0 end as back_money\n");
        sql.append("    ,case when d.item_remark = 'FS02' then coalesce(return_count, 0) else 0 end as free_num\n");
        sql.append("    ,case when d.item_remark = 'FS02' then coalesce(real_amount, 0) else 0 end as free_money\n");
        sql.append("    ,case when d.item_remark in ('TC01', 'FS02')\n");
        sql.append("    then\n");
        sql.append("     0\n");
        sql.append("    else \n");
        sql.append("     (d.discount_amount + d.discountr_amount - d.single_discount_amount)\n");
        sql.append("    end as favor_money\n");
        sql.append("    ,d.single_discount_amount as change_money\n");
        sql.append("    ,case when d.item_remark in ('TC01', 'FS02') then 0 else d.item_count end as actual_num\n");
        sql.append("    ,case when d.item_remark in ('TC01', 'FS02') then 0 else (d.item_amount - d.discount_amount - d.discountr_amount) end as actual_money\n");
        sql.append("    ,case when d.item_remark in ('TC01', 'FS02') then 0 else d.real_amount end as net_money\n");
        sql.append("    ,m.bill_num,s.time_id as times_id,m.bill_state\n");
        sql.append("  from pos_bill_item d\n");
        sql.append("  join pos_bill m on d.bill_num = m.bill_num\n");
        sql.append("  left join sys_times s on s.end_time =\n");
        sql.append("      CASE WHEN to_char(m.payment_time, 'mi') >= '30'\n");
        sql.append("      THEN\n");
        sql.append("        to_char(m.payment_time + interval '1 HOUR', 'hh24') || ':' || '00'\n");
        sql.append("      ELSE\n");
        sql.append("        to_char(m.payment_time, 'hh24') || ':' || '30'\n");
        sql.append("      END\n");

        // TODO
        sql.append("  where m.store_id = 5 and m.report_date = '2016-08-07'and m.bill_property = 'CLOSED' and s.time_type = 'half'\n");
        sql.append(") a on p.store_id = a.store_id and p.report_date = a.report_date\n");
        sql.append("group by sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,times_id\n");

    }

    @Override
    public void getUpItemTimes(StringBuilder sql, Map<String, String> map, List param) throws Exception {
        sql.append("update hq_daycount_item_times t set\n");
        sql.append("   combo_unit_id=t1.combo_unit_id\n");
        sql.append("  ,combo_price=t1.combo_price\n");
        sql.append("from hq_daycount_item_times t1\n");
        sql.append("where t.combo_id = t1.combo_id and t1.item_pro = 'SETMEAL' and t.item_pro = 'MEALLIST'\n");

        // TODO
        sql.append("  and t.day_count = '2016-08-07' and t.store_id=5\n");

    }

    @Override
    public void getOrganTimes(StringBuilder sql, Map<String, String> map, List param) throws Exception {
        sql.append("insert into hq_daycount_times (\n");
        sql.append("   tenancy_id, store_id, day_count, times_id, sale_total, sale_billnum, sale_billaverage, discount_money\n");
        sql.append("  ,reduction_money, coupons_money, customer_discount, customer_reward_sale, customer_cash_money, moling_money\n");
        sql.append("  ,free_money, item_list_money, item_actual_money, ts_actual_money , ws_actual_money , wd_actual_money\n");
        sql.append("  ,item_yh_money, item_discount_money, item_reduction_money, free_money_item, back_money_item, item_net_money\n");
        sql.append("  ,other_income, service_fee_income, yyw_income, accounts_receivable, customer_recharge_total\n");
        sql.append("  ,customer_recharge_income, customer_recharge_reward, customer_recharge_cash, customer_recharge_bank\n");
        sql.append("  ,customer_recharge_zfb, customer_recharge_wx, payment_total, coupons_ds, deposit_money_actual, sale_person_num\n");
        sql.append("  ,sale_person_average, attendance_num, remark\n");
        sql.append(") select\n");
        sql.append("   'gzbljl' as tenancy_id\n");
        sql.append("   ,a.store_id  as store_id,a.report_date as day_count,t.times_id as times_id ,sum(a.bill_amount) as sale_total\n");
        sql.append("   ,count(case when coalesce(a.bill_state,'zc') not in ('CJ01','ZDQX02') then a.bill_num end) as sale_billnum\n");
        sql.append("   ,round(sum(coalesce(a.bill_amount,0) + coalesce(a.more_coupon,0))\n");
        sql.append("     /coalesce(nullif(count(case when coalesce(a.bill_state,'zc') not in('CJ01','ZDQX02') then a.bill_num end),0),1),2) as sale_billaverage\n");
        sql.append("   ,sum(coalesce(a.discountk_amount,0)) as discount_money \n");
        sql.append("   ,sum(coalesce(a.discountr_amount,0)) as reduction_money\n");
        sql.append("   ,sum(coalesce(c.coupons_money,0)) as coupons_money\n");
        sql.append("   ,sum(coalesce(case when a.discount_mode_id = 5 then a.discountk_amount else 0 end,0)) as customer_discount \n");
        sql.append("   ,sum(coalesce(k.customer_reward_sale,0)) as customer_reward_sale\n");
        sql.append("   ,0  as customer_cash_money\n");
        sql.append("   ,sum(coalesce(a.maling_amount,0)) as moling_money\n");
        sql.append("   ,sum(coalesce(a.givi_amount,0)) as free_money\n");
        sql.append("   ,sum(coalesce(a.bill_amount,0) - coalesce(a.service_amount,0)) as item_list_money\n");
        sql.append("   ,sum(coalesce(a.payment_amount,0))  as item_actual_money\n");
        sql.append("   ,sum(coalesce(case when a.sale_mode='TS01' then a.payment_amount else 0 end,0)) as ts_actual_money\n");
        sql.append("   ,sum(coalesce(case when a.sale_mode='WS03' then a.payment_amount else 0 end,0)) as ws_actual_money\n");
        sql.append("   ,sum(coalesce(case when a.sale_mode='WD02' then a.payment_amount else 0 end,0)) as wd_actual_money\n");
        sql.append("   ,sum(coalesce(b.item_yh_money,0)) as item_yh_money\n");
        sql.append("   ,sum(coalesce(b.item_discount_money,0)) as item_discount_money\n");
        sql.append("   ,sum(coalesce(b.item_reduction_money,0)) as item_reduction_money\n");
        sql.append("   ,sum(coalesce(b.free_money_item,0)) as free_money_item\n");
        sql.append("   ,sum(coalesce(b.back_money_item,0)) as back_money_item\n");
        sql.append("   ,sum(coalesce(b.item_net_money,0)) as item_net_money\n");
        sql.append("   ,0 as other_income\n");
        sql.append("   ,sum(coalesce(a.service_amount,0)) as service_fee_income\n");
        sql.append("   ,0 yyw_income\n");
        sql.append("   ,sum(coalesce(k.accounts_receivable,0)) as accounts_receivable\n");
        sql.append("   ,sum(coalesce(k.customer_recharge_total,0)) as customer_recharge_total\n");
        sql.append("   ,sum(coalesce(k.customer_recharge_income,0)) as customer_recharge_income\n");
        sql.append("   ,sum(coalesce(k.customer_recharge_reward,0)) as customer_recharge_reward\n");
        sql.append("   ,sum(coalesce(k.customer_recharge_cash,0)) as customer_recharge_cash\n");
        sql.append("   ,sum(coalesce(k.customer_recharge_bank,0)) as customer_recharge_bank\n");
        sql.append("   ,sum(coalesce(k.customer_recharge_zfb,0)) as customer_recharge_zfb\n");
        sql.append("   ,sum(coalesce(k.customer_recharge_wx,0)) as customer_recharge_wx\n");
        sql.append("   ,sum(coalesce(c.currency_amount,0)) as payment_total\n");
        sql.append("   ,sum(coalesce(a.more_coupon,0))  as coupons_ds\n");
        sql.append("   ,0 as deposit_money_actual\n");
        sql.append("   ,sum(coalesce(a.guest,0)) as sale_person_num\n");
        sql.append("   ,round(sum(coalesce(a.bill_amount,0) + coalesce(more_coupon,0)) / coalesce(nullif(sum(a.guest),0),1),2) as sale_person_average\n");
        sql.append("   ,0 as attendance_num\n");
        sql.append("   ,''  as remark\n");
        sql.append("from (\n");
        sql.append("  SELECT\n");
        sql.append("    (CASE WHEN TO_CHAR(PB.PAYMENT_TIME+'30 MIN','HH24' )='00'\n");
        sql.append("      THEN\n");
        sql.append("       '24'\n");
        sql.append("      ELSE\n");
        sql.append("       TO_CHAR(PB.PAYMENT_TIME+'30 MIN','HH24' )\n");
        sql.append("      END\n");
        sql.append("    ) || ':' ||\n");
        sql.append("    (CASE WHEN TO_CHAR(PB.PAYMENT_TIME+'30 MIN','MI') >= '30'\n");
        sql.append("      THEN\n");
        sql.append("       '30'\n");
        sql.append("      ELSE\n");
        sql.append("       '00'\n");
        sql.append("      END\n");
        sql.append("    ) AS CTIM\n");
        sql.append("    ,pb.*\n");
        sql.append("  from pos_bill pb\n");

        // TODO
        sql.append("  where pb.store_id=5 and pb.report_date='2016-08-07' and pb.bill_property='CLOSED'\n");
        sql.append(") a left join (\n");
        sql.append("    select\n");
        sql.append("       a.bill_num\n");
        sql.append("      ,sum(coalesce(b.discount_amount,0) + coalesce(b.discountr_amount,0) - coalesce(b.single_discount_amount,0))  as item_yh_money \n");
        sql.append("      ,sum(coalesce(b.discount_amount,0)) as item_discount_money\n");
        sql.append("      ,sum(coalesce(b.discountr_amount,0)) as item_reduction_money\n");
        sql.append("      ,sum(coalesce(case when b.item_remark = 'FS02' then b.item_amount else 0 end,0)) as free_money_item\n");
        sql.append("      ,sum(coalesce(case when b.item_remark in ('TC01','QX04') and b.item_property in ('SINGLE','SETMEAL')\n");
        sql.append("        then b.item_amount else 0 end,0)) as back_money_item\n");
        sql.append("      ,sum(coalesce(b.item_amount,0)) as item_net_money \n");
        sql.append("    from pos_bill a\n");
        sql.append("    left join pos_bill_item b on a.bill_num = b.bill_num\n");

        // TODO
        sql.append("    where a.store_id=5 and a.report_date='2016-08-07' and a.bill_property='CLOSED'\n");
        sql.append("    group by a.bill_num\n");
        sql.append(") b on b.bill_num = a.bill_num left join (\n");
        sql.append("    select\n");
        sql.append("       a.bill_num\n");
        sql.append("      ,sum(coalesce(p.currency_amount,0)) as currency_amount \n");
        sql.append("      ,sum(coalesce(case when w.payment_class = 'coupons' then p.currency_amount else 0 end,0)) as coupons_money\n");
        sql.append("    from pos_bill a\n");
        sql.append("    join pos_bill_payment p on p.bill_num = a.bill_num\n");
        sql.append("    left join payment_way w on p.jzid=w.id\n");
        sql.append("    where a.store_id=5 and a.report_date='2016-08-07' and a.bill_property='CLOSED'\n");
        sql.append("    group by a.bill_num\n");
        sql.append(") c on c.bill_num = a.bill_num left join (\n");
        sql.append("    select\n");
        sql.append("       a.bill_num\n");
        sql.append("      ,sum(case when b.operat_type in ('03','05') then b.reward_trading else 0 end) as customer_reward_sale\n");
        sql.append("      ,sum(case when b.operat_type in ('02','04') then b.main_trading else 0 end) as accounts_receivable\n");
        sql.append("      ,sum(case when b.operat_type in ('02','04') then b.main_trading else 0 end) as customer_recharge_total\n");
        sql.append("      ,sum(case when b.operat_type in ('02','04') then b.main_trading else 0 end) as customer_recharge_income\n");
        sql.append("      ,sum(case when b.operat_type in ('02','04') then b.reward_trading else 0 end) as customer_recharge_reward\n");
        sql.append("      ,sum(case when w.payment_class = 'cash' then p.local_currency else 0 end) as customer_recharge_cash\n");
        sql.append("      ,sum(case when w.payment_class = 'bankcard' then p.local_currency else 0 end) as customer_recharge_bank\n");
        sql.append("      ,sum(case when w.payment_class = 'thirdparty' and w.payment_name2 = 'ali_pay' then p.local_currency else 0 end) as customer_recharge_zfb\n");
        sql.append("      ,sum(case when w.payment_class = 'thirdparty' and w.payment_name2 = 'wechat_pay' then p.local_currency else 0 end) as customer_recharge_wx\n");
        sql.append("    from pos_bill a\n");
        sql.append("    join crm_card_trading_list b on b.bill_code_original = a.bill_num\n");
        sql.append("    left join crm_card_payment_list p on p.bill_code = b.bill_code\n");
        sql.append("    left join payment_way w on w.id = p.payment_id\n");

        // TODO
        sql.append("    where b.store_id=5 and b.business_date='2016-08-07' and a.bill_property='CLOSED' \n");

        sql.append("    group by a.bill_num\n");
        sql.append(") k on k.bill_num = a.bill_num\n");
        sql.append("left join (\n");
        sql.append("  select  time_id as times_id,end_time from sys_times \n");
        sql.append("  where time_type='half'\n");
        sql.append(") t on t.end_time = a.ctim\n");
        sql.append("group by a.store_id,a.report_date,t.times_id\n");

    }

    @Override
    public void getItemShift(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("insert into hq_daycount_shift_item(\n");
        sql.append("\t tenancy_id,store_id,day_count,shift_id,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id\n");
        sql.append("\t,item_unit_price,item_pro,list_num,list_money,sales_num,sales_money,back_num,back_money,free_num,free_money\n");
        sql.append("\t,favor_money,discount_money,reduction_money,change_money,actual_num,actual_money,net_money,sale_billnum\n");
        sql.append("\t,sale_billaverage,table_property_id,business_area_id,guest_num,tables_num\n");
        sql.append(") select\n");
        sql.append("    'gzbljl' as tenancy_id\n");

        // TODO
        sql.append("    ,5 as store_id\n");
        sql.append("    ,to_date('2016-08-07', 'YYYY-MM-DD') as day_count\n");
        sql.append("    ,p.shift_id,sale_model ,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro\n");
        sql.append("    ,sum(list_num) as list_num,sum(list_money) as list_money ,sum(sales_num) as sales_num,sum(sales_money) as sales_money\n");
        sql.append("    ,sum(back_num) as back_num,sum(back_money) as back_money,sum(free_num) as free_num ,sum(free_money) as free_money\n");
        sql.append("    ,sum(favor_money) as favor_money,sum(discount_money) as discount_money,sum(reduction_money) as reduction_money \n");
        sql.append("    ,sum(change_money) as change_money,sum(actual_num) as actual_num,sum(actual_money) as actual_money\n");
        sql.append("    ,sum(net_money) as net_money ,max(p.sale_billnum) sale_billnum,null sale_billaverage,table_property_id\n");
        sql.append("    ,business_area_id,coalesce(sum(guest_counts), 0) as guest_num ,max(p.sale_billnum) as tables_num \n");
        sql.append("from (\n");

        // TODO
        sql.append("    'gzbljl' as tenancy_id\n");
        sql.append("    ,5 as store_id\n");
        sql.append("    ,to_date('2016-08-07', 'YYYY-MM-DD') as day_count\n");
        sql.append("    ,p.shift_id,sale_model ,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro\n");
        sql.append("    ,sum(list_num) as list_num,sum(list_money) as list_money ,sum(sales_num) as sales_num,sum(sales_money) as sales_money\n");
        sql.append("    ,sum(back_num) as back_num,sum(back_money) as back_money,sum(free_num) as free_num ,sum(free_money) as free_money\n");
        sql.append("    ,sum(favor_money) as favor_money,sum(discount_money) as discount_money,sum(reduction_money) as reduction_money \n");
        sql.append("    ,sum(change_money) as change_money,sum(actual_num) as actual_num,sum(actual_money) as actual_money\n");
        sql.append("    ,sum(net_money) as net_money ,max(p.sale_billnum) sale_billnum,null sale_billaverage,table_property_id\n");
        sql.append("    ,business_area_id,coalesce(sum(guest_counts), 0) as guest_num ,max(p.sale_billnum) as tables_num \n");
        sql.append("from (\n");
        sql.append("   select \n");
        sql.append("      d.store_id,d.report_date,d.shift_id,count(d.bill_num) as sale_billnum \n");
        sql.append("   from pos_bill d \n");

        // TODO
        sql.append("   where d.store_id =5 and d.report_date = '2016-08-07' and d.bill_property = 'CLOSED' \n");
        sql.append("     and coalesce(d.bill_state, 'zc') not in ('CJ01', 'ZDQX02')\n");
        sql.append("   group by d.store_id, d.report_date, d.shift_id \n");
        sql.append(") p join (\n");
        sql.append("   select \n");
        sql.append("      d.store_id,d.report_date,m.shift_id,d.sale_mode as sale_model,d.setmeal_id as combo_id \n");
        sql.append("     ,case d.item_property when 'SETMEAL' then d.item_unit_id end as combo_unit_id\n");
        sql.append("     ,case when d.item_property = 'SETMEAL' then d.item_price end as combo_price \n");
        sql.append("     ,d.item_id,d.item_unit_id,d.item_price as item_unit_price,d.item_property as item_pro \n");
        sql.append("     ,case when d.item_remark in ('CJ05') then 0 else d.item_count end as list_num\n");
        sql.append("     ,case when d.item_remark in ('CJ05') then 0 else d.item_amount end as list_money \n");
        sql.append("     ,d.item_count as sales_num,d.item_amount as sales_money\n");
        sql.append("     ,case when d.item_remark in ('TC01', 'QX04') then coalesce(return_count, 0) else 0 end as back_num \n");
        sql.append("     ,case when d.item_remark in ('TC01', 'QX04') then coalesce(real_amount, 0) else 0 end as back_money \n");
        sql.append("     ,case when d.item_remark = 'FS02' then coalesce(return_count, 0) else 0 end as free_num\n");
        sql.append("     ,case when d.item_remark = 'FS02' then coalesce(real_amount, 0) else 0 end as free_money \n");
        sql.append("     ,(d.discount_amount + d.discountr_amount - d.single_discount_amount) as favor_money\n");
        sql.append("     ,case when d.item_remark in ('TC01', 'FS02') then 0 else (d.discount_amount) end as discount_money \n");
        sql.append("     ,case when d.item_remark in ('TC01', 'FS02') then 0 else (d.discountr_amount) end as reduction_money \n");
        sql.append("     ,d.single_discount_amount as change_money\n");
        sql.append("     ,case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05') then 0 else d.item_count end as actual_num \n");
        sql.append("     ,case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05') then 0 else d.real_amount end as actual_money \n");
        sql.append("     ,case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05') then 0 else d.real_amount end as net_money \n");
        sql.append("     ,m.bill_num,t.table_property_id,t.business_area_id,m.guest as guest_counts,m.bill_state \n");
        sql.append("   from pos_bill_item d \n");
        sql.append("   join pos_bill m on d.bill_num = m.bill_num \n");

        // TODO
        sql.append("   left join tables_info t on t.table_code = m.table_code and organ_id = 5 \n");
        sql.append("   where m.store_id = 5 and m.report_date = '2016-08-07' and m.bill_property = 'CLOSED' \n");
        sql.append(") a on p.store_id = a.store_id and p.report_date = a.report_date and p.shift_id = a.shift_id \n");
        sql.append("group by p.shift_id,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,table_property_id,business_area_id\n");

    }

    @Override
    public void getUpdateItemShift(StringBuilder sql, Map<String, String> map, List param) throws Exception {
        sql.append("update hq_daycount_shift_item t set \n");
        sql.append("   combo_unit_id=t1.combo_unit_id\n");
        sql.append("  ,combo_price=t1.combo_price \n");
        sql.append("from hq_daycount_shift_item t1 \n");
        sql.append("where t.combo_id = t1.combo_id and t1.item_pro = 'SETMEAL' and t.item_pro = 'MEALLIST' \n");

        // TODO
        sql.append("  and t.day_count = '2016-08-07' and t.store_id = 5;\n");
    }

    @Override
    public void getDaucount(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("insert into hq_daycount (\n");
        sql.append("   tenancy_id,store_id,day_count,sale_total,sale_billnum,sale_billaverage,discount_money,reduction_money\n");
        sql.append("  ,coupons_money,total_card,main_trading,customer_discount,customer_reward_sale,customer_cash_money\n");
        sql.append("  ,moling_money,free_money,item_list_money,item_actual_money,ts_actual_money,ws_actual_money,wd_actual_money\n");
        sql.append("  ,item_yh_money,item_discount_money,item_reduction_money, free_money_item,back_money_item,item_net_money\n");
        sql.append("  ,other_income,service_fee_income,yyw_income,accounts_receivable,customer_recharge_total,customer_recharge_income\n");
        sql.append("  ,customer_recharge_reward,customer_recharge_cash,customer_recharge_bank,customer_recharge_zfb,customer_recharge_wx\n");
        sql.append("  ,payment_total,coupons_ds,deposit_money_actual,sale_person_num,sale_person_average,attendance_num,remark,tables_num\n");
        sql.append("  ,seat_num,backsingle_num,backwhole_num,backcopy_num\n");
        sql.append(") select\n");
        sql.append("    'gzbljl' as tenancy_id\n");

        // TODO
        sql.append("    ,5 as store_id,to_date('2016-08-07', 'YYYY-MM-DD') as day_count\n");
        sql.append("    ,p.sale_total,p.sale_billnum,p.sale_billaverage,p.discount_money,p.reduction_money,a.coupons_money\n");
        sql.append("    ,a.total_card,b.main_trading,a.customer_discount,b.customer_reward_sale,0 as customer_cash_money\n");
        sql.append("    ,p.moling_money,p.free_money,c.item_list_money,p.item_actual_money,c.ts_actual_money,c.ws_actual_money\n");
        sql.append("    ,c.wd_actual_money,c.item_yh_money,c.item_discount_money,c.item_reduction_money,c.free_money_item\n");
        sql.append("    ,c.back_money_item,c.item_net_money,coalesce(d.other_income, 0) other_income\n");
        sql.append("    ,coalesce(p.service_fee_income, 0) service_fee_income,coalesce(d.yyw_income, 0) yyw_income\n");
        sql.append("    ,coalesce(b.accounts_receivable, 0) accounts_receivable,coalesce(b.customer_recharge_total, 0) customer_recharge_total\n");
        sql.append("    ,coalesce(b.customer_recharge_income, 0) customer_recharge_income,coalesce(b.customer_recharge_reward, 0) customer_recharge_reward\n");
        sql.append("    ,coalesce(b.customer_recharge_cash, 0) customer_recharge_cash,coalesce(b.customer_recharge_bank, 0) customer_recharge_bank\n");
        sql.append("    ,coalesce(b.customer_recharge_zfb, 0) customer_recharge_zfb,coalesce(b.customer_recharge_wx, 0) customer_recharge_wx\n");
        sql.append("    ,a.payment_total,p.coupons_ds\n");
        sql.append("    ,(select\n");
        sql.append("      coalesce(sum(amount_deposited), 0)\n");
        sql.append("    from boh_bank_deposit\n");

        // TODO
        sql.append("    where store_id = 5 and business_date = '2016-08-07'\n");
        sql.append("    ) as deposit_money_actual\n");
        sql.append("    , p.sale_person_num,p.sale_person_average,0 as attendance_num,'' as remark\n");
        sql.append("    ,(select \n\n");
        sql.append("      count(t.id)\n");
        sql.append("    from tables_info t\n");

        // TODO
        sql.append("    where t.organ_id = 5 and t.valid_state = '1'\n");
        sql.append("    ) as tables_num\n");
        sql.append("    ,(select\n");
        sql.append("      coalesce(sum(t.seat_counts), 0)\n");
        sql.append("    from tables_info t\n");

        // TODO
        sql.append("    where t.organ_id = 5 and t.valid_state = '1'\n");
        sql.append("    ) as seat_num\n");
        sql.append("    ,(select\n");
        sql.append("      count(id)\n");
        sql.append("    from pos_bill\n");

        // TODO
        sql.append("    where bill_state='CJ01'\n");
        sql.append("      and copy_bill_num in(\n");
        sql.append("        select bill_num from pos_bill\n");

        // TODO
        sql.append("        where bill_state is null and report_date ='2016-08-07' and store_id =5 and bill_property='CLOSED'\n");
        sql.append("      ) and report_date ='2016-08-07' and store_id =5 and bill_property='CLOSED'\n");
        sql.append("    ) as backsingle_num\n");
        sql.append("    ,(select\n");
        sql.append("      count(id)\n");
        sql.append("    from pos_bill\n");

        // TODO
        sql.append("    where bill_state='CJ01'\n");
        sql.append("      and copy_bill_num in(\n");
        sql.append("        select bill_num from pos_bill\n");

        // TODO
        sql.append("        where bill_state='ZDQX02' and report_date='2016-08-07' and store_id =5 and bill_property='CLOSED'\n");
        sql.append("      ) and report_date ='2016-08-07' and store_id =5 and bill_property='CLOSED' \n");
        sql.append("      and copy_bill_num  not in (\n");
        sql.append("        select copy_bill_num from pos_bill\n");

        // TODO
        sql.append("        where (bill_state is null or bill_state='ZDQX02')\n");
        sql.append("          and copy_bill_num is not null and report_date ='2016-08-07' and store_id =5 and bill_property='CLOSED'\n");
        sql.append("      )\n");
        sql.append("    ) as backwhole_num\n");
        sql.append("    ,(select\n");
        sql.append("      count(id)\n");
        sql.append("    from pos_bill\n");

        // TODO
        sql.append("  where bill_state='CJ01'\n");
        sql.append("    and copy_bill_num in(\n");
        sql.append("      select bill_num from pos_bill\n");

        // TODO
        sql.append("      where bill_state='ZDQX02' and report_date='2016-08-07' and store_id =5 and bill_property='CLOSED'\n");
        sql.append("      ) and report_date ='2016-08-07' and store_id =5 and bill_property='CLOSED'\n");
        sql.append("      and copy_bill_num  in (\n");
        sql.append("        select copy_bill_num from pos_bill\n");

        // TODO
        sql.append("        where (bill_state is null or bill_state='ZDQX02') and copy_bill_num is not null\n");
        sql.append("        and report_date ='2016-08-07' and store_id =5 and bill_property='CLOSED'\n");
        sql.append("      )\n");
        sql.append("    ) as backcopy_num from (\n");
        sql.append("      select\n");
        sql.append("         store_id,report_date,coalesce(sum(m.bill_amount), 0) as sale_total\n");
        sql.append("        ,count(case when coalesce(m.bill_state, 'zc') not in ('CJ01', 'ZDQX02') then m.bill_num end) as sale_billnum\n");
        sql.append("        ,case when count(m.bill_num)=0\n");
        sql.append("          then\n");
        sql.append("           0\n");
        sql.append("          else\n");
        sql.append("           round((coalesce(sum(m.bill_amount), 0) + coalesce(sum(more_coupon), 0))\n");
        sql.append("             / (case when\n");
        sql.append("               coalesce(count(\n");
        sql.append("                 case when coalesce(bill_state, 'zc') not in ('CJ01', 'ZDQX02') then m.bill_num end), 0\n");
        sql.append("               ) = 0\n");
        sql.append("               then\n");
        sql.append("                1\n");
        sql.append("               else\n");
        sql.append("               coalesce(count(\n");
        sql.append("                 case when coalesce(bill_state, 'zc') not in ('CJ01', 'ZDQX02') then m.bill_num end), 0)\n");
        sql.append("               end), 2\n");
        sql.append("           )\n");
        sql.append("          end as sale_billaverage\n");
        sql.append("        ,case when sum(guest)=0\n");
        sql.append("         then\n");
        sql.append("          0\n");
        sql.append("         else\n");
        sql.append("          round((coalesce(sum(bill_amount), 0) + coalesce(sum(more_coupon), 0)) / sum(guest), 2)\n");
        sql.append("         end as sale_person_average\n");
        sql.append("         ,coalesce(sum(m.payment_amount), 0) as item_actual_money\n");
        sql.append("         ,coalesce(sum(discountk_amount), 0) as discount_money\n");
        sql.append("         ,coalesce(sum(discountr_amount), 0) as reduction_money\n");
        sql.append("         ,coalesce(sum(m.maling_amount), 0) as moling_money\n");
        sql.append("         ,coalesce(sum(m.givi_amount), 0) as free_money\n");
        sql.append("         ,coalesce(sum(m.service_amount), 0) as service_fee_income\n");
        sql.append("         ,coalesce(sum(m.more_coupon), 0) as coupons_ds\n");
        sql.append("         ,coalesce(sum(guest), 0) as sale_person_num\n");
        sql.append("      from pos_bill m\n");

        // TODO
        sql.append("      where m.store_id = 5 and m.report_date = '2016-08-07' and m.bill_property = 'CLOSED'\n");
        sql.append("      group by store_id, report_date\n");
        sql.append("    ) p join (\n");
        sql.append("      select\n");
        sql.append("         m.store_id\n");
        sql.append("        ,m.report_date\n");
        sql.append("        ,coalesce(sum(case when discount_mode_id=5 then coalesce(discountk_amount, 0) end), 0) as customer_discount\n");
        sql.append("        ,sum(case when w.payment_class = 'coupons' then coalesce(p.currency_amount, 0) end) as coupons_money\n");
        sql.append("        ,sum(case when w.payment_class = 'card' then coalesce(p.currency_amount, 0) end) as total_card\n");
        sql.append("        ,coalesce(sum(p.currency_amount),0) as payment_total\n");
        sql.append("      from pos_bill m\n");
        sql.append("      join pos_bill_payment p on m.bill_num = p.bill_num\n");
        sql.append("      left join payment_way w on p.jzid = w.id\n");

        // TODO
        sql.append("      where m.store_id = 5 and m.report_date = '2016-08-07' and m.bill_property = 'CLOSED'\n");
        sql.append("      group by m.store_id, m.report_date\n");
        sql.append("    ) a on p.store_id = a.store_id and p.report_date = a.report_date left join (\n");
        sql.append("        select t.store_id,t.business_date,sum(case when operat_type in ('03', '05') then coalesce(main_trading, 0) end) as main_trading\n");
        sql.append("          ,sum(case when operat_type in ('03', '05') then coalesce(reward_trading, 0) end) as customer_reward_sale\n");
        sql.append("          ,sum(case when operat_type in ('02', '04') then coalesce(main_trading, 0) end) as accounts_receivable\n");
        sql.append("          ,sum(case when operat_type in ('02', '04') then coalesce(main_trading, 0) end) as customer_recharge_total\n");
        sql.append("          ,sum(case when operat_type in ('02', '04') then coalesce(main_trading, 0) end) as customer_recharge_income\n");
        sql.append("          ,sum(case when operat_type in ('02', '04') then coalesce(reward_trading, 0) end) as customer_recharge_reward\n");
        sql.append("          ,sum(case when w.payment_class='cash' then coalesce(p.local_currency, 0) end) as customer_recharge_cash\n");
        sql.append("          ,sum(case when w.payment_class='bankcard' then coalesce(p.local_currency, 0) end) as customer_recharge_bank\n");
        sql.append("          ,sum(case when w.payment_class='thirdparty' and w.payment_name2='ali_pay' then coalesce(p.local_currency, 0) end) as customer_recharge_zfb\n");
        sql.append("          ,sum(case when w.payment_class='thirdparty' and w.payment_name2='wechat_pay' then coalesce(p.local_currency, 0) end) as customer_recharge_wx\n");
        sql.append("        from crm_card_trading_list t\n");
        sql.append("        left join crm_card_payment_list p on t.bill_code=p.bill_code\n");
        sql.append("        left join payment_way w on p.payment_id=w.id\n");

        // TODO
        sql.append("        where t.store_id = 5 and t.business_date='2016-08-07'\n");
        sql.append("        group by t.store_id, t.business_date\n");
        sql.append("    ) b on a.store_id=b.store_id and a.report_date=b.business_date join (\n");
        sql.append("        select\n");
        sql.append("           m.store_id,m.report_date\n");
        sql.append("          ,sum(case when d.item_property in ('SINGLE', 'SETMEAL')\n");
        sql.append("            then\n");
        sql.append("              (case when d.item_remark in ('CJ05')\n");
        sql.append("                then\n");
        sql.append("                 0\n");
        sql.append("                else\n");
        sql.append("                 d.item_amount\n");
        sql.append("                end\n");
        sql.append("              )\n");
        sql.append("            end) as item_list_money\n");
        sql.append("          ,coalesce(sum(case when d.item_property in ('SINGLE', 'SETMEAL')\n");
        sql.append("            then\n");
        sql.append("             (d.discount_amount + d.discountr_amount - d.single_discount_amount)\n");
        sql.append("            else\n");
        sql.append("             0\n");
        sql.append("            end\n");
        sql.append("          ), 0) as item_yh_money\n");
        sql.append("          ,coalesce(sum(d.discount_amount), 0) as item_discount_money\n");
        sql.append("          ,coalesce(sum(d.discountr_amount), 0) as item_reduction_money\n");
        sql.append("          ,sum(case when d.item_remark='FS02'\n");
        sql.append("              then\n");
        sql.append("               coalesce(d.item_amount, 0)\n");
        sql.append("              end\n");
        sql.append("          ) as free_money_item\n");
        sql.append("          ,sum(case when d.item_remark in ('TC01', 'QX04') and d.item_property in ('SINGLE', 'SETMEAL')\n");
        sql.append("            then\n");
        sql.append("             coalesce(d.real_amount, 0)\n");
        sql.append("            end\n");
        sql.append("          ) as back_money_item\n");
        sql.append("          ,coalesce(sum(d.real_amount), 0) as item_net_money\n");
        sql.append("          ,coalesce(sum(case when d.sale_mode='TS01' and d.item_property in ('SINGLE', 'SETMEAL')\n");
        sql.append("            then\n");
        sql.append("              coalesce(d.item_amount, 0)\n");
        sql.append("            end\n");
        sql.append("          ),0) as ts_actual_money\n");
        sql.append("          ,coalesce(sum(case when d.sale_mode = 'WS03' and d.item_property in ('SINGLE', 'SETMEAL')\n");
        sql.append("            then\n");
        sql.append("              coalesce(d.item_amount, 0)\n");
        sql.append("            end\n");
        sql.append("          ),0) as ws_actual_money\n");
        sql.append("          ,coalesce(sum(case when d.sale_mode = 'WD02' and d.item_property in ('SINGLE', 'SETMEAL')\n");
        sql.append("            then\n");
        sql.append("              coalesce(d.item_amount, 0)\n");
        sql.append("            end\n");
        sql.append("          ),0) as wd_actual_money\n");
        sql.append("        from pos_bill m\n");
        sql.append("        join pos_bill_item d on m.bill_num = d.bill_num\n");

        // TODO
        sql.append("        where m.store_id = 5 and m.report_date = '2016-08-07' and m.bill_property = 'CLOSED'\n");
        sql.append("        group by m.store_id, m.report_date\n");
        sql.append("    ) c on a.store_id = c.store_id and a.report_date = c.report_date left join (\n");
        sql.append("        select\n");
        sql.append("          store_id,business_date,sum(coalesce(bi.money, 0)) as other_income,coalesce(sum(bi.money), 0) as yyw_income\n");
        sql.append("        from boh_inout_info bi\n");

        // TODO
        sql.append("        where bi.store_id=5 and bi.business_date='2016-08-07'\n");
        sql.append("        group by store_id, business_date\n");
        sql.append("    ) d on a.store_id = d.store_id and a.report_date = d.business_date\n");

    }

    @Override
    public void getdeletePosBill(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("delete from pos_bill\n");

        // TODO 替换条件
        sql.append(" where store_id=5 and report_date='2016-08-07'\n");
    }

    @Override
    public void getDeletePosBillItem(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("delete from pos_bill_item\n");

        // TODO 替换条件
        sql.append(" where store_id=5 and report_date='2016-08-07'\n");
    }

    @Override
    public void getDeletePosBillPayment(StringBuilder sql, Map<String, String> map, List param) throws Exception {

        sql.append("delete from pos_bill_payment\n");

        // TODO 替换条件
        sql.append(" where store_id=5 and report_date='2016-08-07';\n");

    }
}
