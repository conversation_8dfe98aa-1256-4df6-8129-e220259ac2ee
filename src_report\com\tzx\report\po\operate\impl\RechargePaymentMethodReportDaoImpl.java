package com.tzx.report.po.operate.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.tools.Tool;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.google.gson.JsonObject;
import com.ibm.db2.jcc.t4.sb;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.operate.dao.DoBusinessPaymentDao;
import com.tzx.report.po.operate.dao.RechargePaymentMethodReportDao;

@Repository(RechargePaymentMethodReportDao.NAME)
public class RechargePaymentMethodReportDaoImpl implements RechargePaymentMethodReportDao{

	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	
	
	
	List<JSONObject> list =null;
	StringBuilder sb = new StringBuilder();
	@Override
	public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
		Integer type = condition.optInt("type");
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> array = new ArrayList<JSONObject>();
		String begindate = condition.optString("REPORT_DATE_BEGIN");
		String enddate = condition.optString("REPORT_DATE_END");
		JSONObject result = new JSONObject();
		StringBuilder footer = new StringBuilder();
		String sqlType = "";
		String reportCount = "";
		String reportSql="";
		long total = 0L;
		if(begindate.length()>0 && enddate.length()>0 )
		{
			switch (type)
			{
			//按日期汇总	
			case 1:
				if(condition.optInt("hierarchytype") ==1){
					if(condition.optString("EXPRO").equals("0")) {
						reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','v2l0','')";
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
						reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','vv00','')";
						footerList = this.dao.query4Json(tenancyID, reportSql.toString());
					}else {
						reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','d2l0','"+condition.optString("REPORT_DATE")+"')";
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
					}
					
				}else if(condition.optInt("hierarchytype") ==2 && condition.containsKey("REPORT_DATE")){
					 
					reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE")+"','"+condition.optString("REPORT_DATE")+"'," +
							""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','v2l1','')";
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					
				} else if(condition.optInt("hierarchytype") ==3 && condition.containsKey("store_id1")){
					reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE")+"','"+condition.optString("REPORT_DATE")+"'," +
							""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id1")+"','v2l2','')";
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					
				}else{
					break;
				}
				
				break;
				//按门店汇总
				case 2:
					if(condition.optInt("hierarchytype") ==1){
						reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','v1l0','')";
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
						reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','vv00','')";
						footerList = this.dao.query4Json(tenancyID, reportSql.toString());
						
					}else if(condition.optInt("hierarchytype") ==2 && condition.containsKey("store_id1")){
						
						reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id1")+"','v1l1','')";
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
						
					}else if(condition.optInt("hierarchytype") ==3 && condition.containsKey("store_id1")&& condition.containsKey("REPORT_DATE")){
						reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE")+"','"+condition.optString("REPORT_DATE")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id1")+"','v1l2','')";
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
					}else{
						  break;
					}
				break;
				//按付款方式汇总
				case 3:
					if(condition.optInt("hierarchytype") ==1){
						reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','v3l0','')";
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
						reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','vv00','')";
						footerList = this.dao.query4Json(tenancyID, reportSql.toString());
						
					}else if(condition.optInt("hierarchytype") ==2 ){
						 
						reportSql = "select null as payment_type,* from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','v3l1','')";
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
					} else if(condition.optInt("hierarchytype") ==3 ){
						reportSql = "select null as payment_type,null as payment_name,* from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
								""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','v3l2','')";
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
					}else{
						break;
					}
					//按付款方式明细
					break;
				case 4:
					if(condition.optInt("hierarchytype") ==1){
						if(condition.optString("EXPRO").equals("0")) {
							reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
									""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','v4l0','')";
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							
							reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
									""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','vv00','')";
							footerList = this.dao.query4Json(tenancyID, reportSql.toString());
						}else {
							reportSql = "select * from f_tab_crm_payment_tj('"+condition.optString("REPORT_DATE_BEGIN")+"','"+condition.optString("REPORT_DATE_END")+"'," +
									""+condition.optString("payment_name")+","+condition.optString("payment_type")+",'"+condition.optString("store_id")+"','d4l0','"+condition.optString("REPORT_DATE")+"')";
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						}
						
					}else{
						break;
					}
				break;
				default:
					break;
			}
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		return result;
 	}
 
}
