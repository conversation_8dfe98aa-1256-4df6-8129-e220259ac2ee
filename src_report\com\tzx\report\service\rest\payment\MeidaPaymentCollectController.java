package com.tzx.report.service.rest.payment;

import com.alibaba.fastjson.JSONObject;
import com.tzx.report.bo.payment.service.MeidaPaymentCollectService;
import com.tzx.report.common.util.ReportExportUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2018/6/14
 */
@Controller
@RequestMapping("/report/meidaPaymentCollect")
public class MeidaPaymentCollectController {

    @Autowired
    private MeidaPaymentCollectService meidaPaymentCollectService;
    /**
     * 列表集合
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("list")
    @ResponseBody
    public JSONObject list(HttpServletRequest request, HttpServletResponse response){
        JSONObject jsonObject = new JSONObject();

        net.sf.json.JSONObject p = new net.sf.json.JSONObject();
        Map<String, String[]> map = request.getParameterMap();
        for (String key : map.keySet())
        {
            p.put(key, map.get(key)[0]);
        }
        HttpSession session = request.getSession();
        try {
            String storeString = "storeId";
            if(p.getString(storeString).length()==0 ||
                    "0".equals(p.getString(storeString)) ||
                    "'0'".equals(p.getString(storeString)) ||
                    "99999999".equals(p.getString(storeString)) ||
                    "''".equals(p.getString(storeString)) ||
                    "'99999999'".equals(p.getString(storeString))
                    ){
                if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
                    // 判断当前是门店还是总部
                    if(session.getAttribute("organ_id").equals("0")) {
                        //取所有门店
                        p.put(storeString, session.getAttribute("user_organ_codes_group"));
                    }else {
                        // 取门店
                        p.put(storeString, session.getAttribute("organ_id"));
                    }
                }else{
                    p.put(storeString, session.getAttribute("user_organ"));
                }
            }
            //添加
            net.sf.json.JSONObject result = meidaPaymentCollectService.selectList(null,p);
            jsonObject = JSONObject.parseObject(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            jsonObject.put("success",false);
            jsonObject.put("msg",e.getMessage());
        }
        return jsonObject;
    }


    /**
     * 导出
     * @param request
     */
    @RequestMapping("exportDate")
    @ResponseBody
    public void exportDate(HttpServletRequest request,HttpServletResponse response){
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        HttpSession session = request.getSession();
        HSSFWorkbook workBook = null;
        try
        {
            workBook = new HSSFWorkbook();
            net.sf.json.JSONObject p = net.sf.json.JSONObject.fromObject("{}");
            Map<String, String[]> map = request.getParameterMap();
            for (String key : map.keySet())
            {
                if (map.get(key)[0] != "")
                {
                    p.put(key, map.get(key)[0]);
                }
            }

            String storeString = "storeId";
            if(p.optString(storeString).length()==0 ||
                    "0".equals(p.optString(storeString)) ||
                    "'0'".equals(p.optString(storeString)) ||
                    "99999999".equals(p.optString(storeString)) ||
                    "''".equals(p.optString(storeString)) ||
                    "'99999999'".equals(p.optString(storeString))
                    ){
                // 判断当前是门店还是总部
                if(session.getAttribute("organ_id").equals("0")) {
                    //取所有门店
                    p.element(storeString, session.getAttribute("user_organ_codes_group"));
                }else {
                    // 取门店
                    p.element(storeString, session.getAttribute("organ_id"));
                }
            }

            workBook = meidaPaymentCollectService.exportAccountData((String) session.getAttribute("tenentid"), p ,workBook);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        try
        {
            ReportExportUtils.download(workBook,response,"美大支付汇总报表");
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @RequestMapping("secondChild")
    @ResponseBody
    public JSONObject secondChild(HttpServletRequest request, HttpServletResponse response){
        JSONObject jsonObject = new JSONObject();

        JSONObject p = new JSONObject();
        Map<String, String[]> map = request.getParameterMap();
        for (String key : map.keySet())
        {
            p.put(key, map.get(key)[0]);
        }
        HttpSession session = request.getSession();
        try {
            String storeString = "storeId";
            if(p.getString(storeString).length()==0 ||
                    "0".equals(p.getString(storeString)) ||
                    "'0'".equals(p.getString(storeString)) ||
                    "99999999".equals(p.getString(storeString)) ||
                    "''".equals(p.getString(storeString)) ||
                    "'99999999'".equals(p.getString(storeString))
                    ){
                if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
                    // 判断当前是门店还是总部
                    if(session.getAttribute("organ_id").equals("0")) {
                        //取所有门店
                        p.put(storeString, session.getAttribute("user_organ_codes_group"));
                    }else {
                        // 取门店
                        p.put(storeString, session.getAttribute("organ_id"));
                    }
                }else{
                    p.put(storeString, session.getAttribute("user_organ"));
                }
            }

            net.sf.json.JSONObject result = meidaPaymentCollectService.selectSecondList(p);
            jsonObject = JSONObject.parseObject(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            jsonObject.put("success",false);
            jsonObject.put("msg",e.getMessage());
        }
        return jsonObject;
    }
}
