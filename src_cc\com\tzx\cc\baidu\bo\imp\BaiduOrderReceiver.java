package com.tzx.cc.baidu.bo.imp;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import scala.collection.generic.BitOperations.Int;
import sun.util.logging.resources.logging;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.cc.baidu.entity.CmdType;
import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.baidu.util.SignHolder;
import com.tzx.cc.common.constant.util.CcPartitionUtils;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

public class BaiduOrderReceiver extends ThirdPartyOrderReceiver
{

	private JSONObject			requestOrder;
	private JSONObject			requestUser;
	private int					discount_fee;																// 优惠总金额
	private int					product_amount;																// 订单商品总金额
	private int					send_fee;																	// 配送费
	private double				discountRate;																// 优惠比例=优惠总金额/订单商品总金额
	private double				comboDiscountRate;															// 套餐明细优惠比例=套餐优惠总金额/套餐商品总金额
	private double				sendFeeRate;																// 配送费比例=配送费/订单商品总金额
	private double				platform_side_discount_fee	= 0.0;											// 平台方承担的优惠总金额
	private double				takeawayBusinessIncome;														// 外卖的营业收入=用户实付-配送费+平台方承担的金额
	private double				food_sharing_date;															// 菜品分摊的比例=菜品总计/用户实付-配送费+平台方承担的金额
	private int 				total_baidu_rate;														    //平台承担
	private int                 total_shop_rate;															//商家承担
	private double total_shopRate=0.0;//商家承担
	private static final Logger	logger						= Logger.getLogger(BaiduOrderReceiver.class);

	public BaiduOrderReceiver()
	{
		super(Constant.BAIDU_CHANNEL);
	}

	public BaiduOrderReceiver(JSONObject order)
	{
		super(order, Constant.BAIDU_CHANNEL);
		// 订单信息
		requestOrder = thirdPartyOrder.optJSONObject("body").optJSONObject("order");
		// 顾客信息
		requestUser = thirdPartyOrder.optJSONObject("body").optJSONObject("user");
		JSONObject shop = order.optJSONObject("body").optJSONObject("shop");
		String[] shopId = shop.optString("id").split("@");
		this.storeId = shopId[0];
		this.tenantId = shopId[1];
		//20171218日志中存储商户号、门店号
		logger.info("================tenantId="+storeId+",storeId="+tenantId+",百度新订单:查询订单详情返回结果：["+thirdPartyOrder+"]===========================");
	}
	
	@Override
	protected void generateOrderCode()
	{
		String dateSerial=CommonUtil.formatTimestamp(requestOrder.optLong("create_time"), "yyyyMMddHHmm");
		int orderIndex =requestOrder.optInt("order_index");
		orderCode = channel+storeId+dateSerial+CommonUtil.zeroFill(orderIndex);
//		orderCode=UUID.randomUUID().toString();
		
	}

	@Override
	public JSONObject saveOrderList() throws Exception
	{

		JSONObject orderListParam = new JSONObject();
        //计算方式
        orderListParam.put("settlement_type",checkMode.getSettlement_type());
		// 商户ID
		orderListParam.put("tenancy_id", tenantId);
		// 商户id
		orderListParam.put("store_id", storeId);
		// 订单号
		orderListParam.put("order_code", orderCode);
		// 每天总的流水号
		orderListParam.put("serial_number", flow_code);
		// 每天分渠道的流水号
		orderListParam.put("chanel_serial_number", requestOrder.optString("order_index"));
		orderListParam.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));

		// 百度订单编号
		orderListParam.put("third_order_code", requestOrder.optString("order_id"));
		// 是否立即送餐
		orderListParam.put("send_immediately", requestOrder.optInt("send_immediately"));
		if (requestOrder.optInt("send_immediately") == 1)
		{
			// 期望送达时间???
			orderListParam.put("send_time", "立即配送");
		}
		else
		{
			/*
			 * long a = requestOrder.optLong("send_time"); String aa =
			 * DateUtil.format(new Timestamp(a)); String[] aaa = aa.split(" ");
			 * String b = aaa[1].substring(0, 5); // 期望送达时间???
			 * orderListParam.put("send_time", b.toString());
			 */
			orderListParam.put("send_time", CommonUtil.getDateFromSeconds(String.valueOf(requestOrder.optLong("send_time"))));

		}
		
		
      	double baiDuRate=0.0;
		// 平台方承担的金额
		JSONArray discount_info = thirdPartyOrder.optJSONObject("body").optJSONArray("discount");
		for (int i = 0; i < discount_info.size(); i++)
		{
			
			JSONObject dc = discount_info.getJSONObject(i);
			platform_side_discount_fee += (dc.optDouble("baidu_rate") / 100);
			
			baiDuRate+=Double.valueOf(CommonUtil.fen2Yuan(dc.optInt("baidu_rate")));
			total_shopRate+=Double.valueOf(CommonUtil.fen2Yuan(dc.optInt("shop_rate")));
		}

		// 配送费
		send_fee = requestOrder.optInt("send_fee");
		// 优惠总金额
		discount_fee = requestOrder.optInt("discount_fee");
		double result_discount_fee=Double.valueOf(CommonUtil.fen2Yuan(discount_fee));

		// 订单总价
		int total_fee = requestOrder.optInt("total_fee");
		//用户实付
		double actualPay=Double.valueOf(CommonUtil.fen2Yuan(requestOrder.optInt("user_fee")));

		order_total_price = Double.valueOf(CommonUtil.fen2Yuan(total_fee));
		user_actual_pay = actualPay;

		orderListParam.put("total_money", CommonUtil.fen2Yuan(total_fee));
		product_amount = total_fee - send_fee;
        // 物流
        int deliveryType=requestOrder.optInt("delivery_party");
        orderListParam.put("delivery_party", deliveryType);

        double sendFee=Double.valueOf(CommonUtil.fen2Yuan(send_fee));
        double shopDeliveryFee=sendFee;
        double specialShopFee=Double.valueOf(CommonUtil.fen2Yuan(total_fee))-result_discount_fee;
        double excess_discount=0;
        if(Constant.THIRD_DELIVERY==deliveryType){//平台配送
        	double platSpecialShopFee=specialShopFee-sendFee;
        	shopDeliveryFee=0;
        	if(platSpecialShopFee>0){
        		  shopFee = platSpecialShopFee;
        	}else{//计算shopFee为负数时
        		  shopFee = 0;
        		  result_discount_fee=result_discount_fee+platSpecialShopFee;
        		  excess_discount=0-platSpecialShopFee;
        	}
          
        }else{
        	if(specialShopFee>0){
        		shopFee=specialShopFee;
        	}else{
        		shopFee=0;
        		excess_discount=0-specialShopFee;
        	}
        }
        //at 20170908 解决shopFee因小数位产生计算错误的情况
        shopFee=CommonUtil.keepTwoDecimal(shopFee);
        
        orderListParam.put("discount_amount",CommonUtil.keepTwoDecimal(result_discount_fee));
        orderListParam.put("excess_discount", CommonUtil.keepTwoDecimal(excess_discount));
        // 用户实付总价
		orderListParam.put("actual_pay", CommonUtil.keepTwoDecimal(actualPay));
        // 商户实收总价
		orderListParam.put("shop_fee", CommonUtil.keepTwoDecimal(shopFee));
        //配送费
        orderListParam.put("meal_costs", CommonUtil.keepTwoDecimal(sendFee));
        //商家承担的配送费金额
        orderListParam.put("shop_delivery_fee",CommonUtil.keepTwoDecimal(shopDeliveryFee));

		//----------------------------------------
		//优惠总额=优惠金额+骑手结算时门店的折扣优惠金额
		if(checkMode.getSettlement_type().equals("RIDER")){
	    double tempAmount=shopFee*(100 - Double.valueOf(checkMode.getDiscount_rate())) / 100;
	    BigDecimal bg=new BigDecimal(tempAmount);
	    discountR_amount=Double.isNaN(bg.setScale(4, RoundingMode.HALF_UP).doubleValue())?0.0:bg.setScale(4, RoundingMode.HALF_UP).doubleValue();
		}
	    //----------------------------------------
	    
	    takeawayBusinessIncome = Scm.padd(Scm.psub(Double.valueOf(CommonUtil.fen2Yuan(requestOrder.optInt("user_fee"))), Double.valueOf(CommonUtil.fen2Yuan(send_fee))), platform_side_discount_fee);
		// 应收款receivable_amount
		// orderListParam.put("receivable_amount",
		// CommonUtil.fen2Yuan(requestOrder.optInt("total_fee")-requestOrder.optInt("discount_fee")-requestOrder.optInt("user_fee")));
		// 支付类型
		String payType = requestOrder.optString("pay_type");
		String isOnlinePayment = null;
		if (StringUtils.equals("1", payType))
		{
			isOnlinePayment = "0";
		}
		else if (StringUtils.equals("2", payType))
		{
			isOnlinePayment = "1";
		}
		String payment_state = null;
		if (StringUtils.equals("1", payType))
		{
			payment_state = "01";
		}
		else if (StringUtils.equals("2", payType))
		{
			payment_state = "03";
		}
		orderListParam.put("is_online_payment", isOnlinePayment);
		orderListParam.put("payment_state", payment_state);
		// 是否需要发票
		orderListParam.put("need_invoice",requestOrder.optInt("need_invoice")==2?0:requestOrder.optInt("need_invoice"));
		// 发票抬头
		orderListParam.put("invoice_title", CommonUtil.checkStringIsNotEmpty(requestOrder.optString("invoice_title"))?CommonUtil.replaceEvilChar(requestOrder.optString("invoice_title").trim()):"");
		// 订单备注
		orderListParam.put("remark", CommonUtil.replaceEvilChar(requestOrder.optString("remark").trim()));

		// 订单创建时间
		// orderListParam.put("single_time", DateUtil.format(new
		// Timestamp(System.currentTimeMillis())));
		orderListParam.put("single_time", CommonUtil.getDateFromSeconds(String.valueOf(requestOrder.optLong("create_time"))));
		if(requestUser.optString("name").contains("'")){
			requestUser.put("name", requestUser.optString("name").replaceAll("'", " "));
	     }
		// 顾客姓名
		orderListParam.put("consigner", CommonUtil.replaceEvilChar(requestUser.optString("name").trim()));
		// 顾客电话
		orderListParam.put("consigner_phone", requestUser.optString("phone"));

		// 订餐人
		orderListParam.put("order_name", CommonUtil.replaceEvilChar(requestUser.optString("name").trim()));
		// 订餐电话
		orderListParam.put("order_phone", requestUser.optString("phone"));
		// 顾客性别
		orderListParam.put("sex", requestUser.optInt("gender") == 1 ? "man" : "woman");
		// 渠道
		orderListParam.put("chanel", Constant.BAIDU_CHANNEL);
		// 送餐地址
		if(requestUser.optString("address").contains("'")){
			requestUser.put("recipient_address", requestUser.optString("address").replaceAll("'", " "));
	        }
		// 送餐地址
		orderListParam.put("address", CommonUtil.replaceEvilChar(requestUser.optString("address").trim()));
		// 送餐地址百度经度
		orderListParam.put("longitude", requestUser.optJSONObject("coord").optString("longitude"));
		// 送餐地址百度纬度
		orderListParam.put("latitude", requestUser.optJSONObject("coord").optString("latitude"));
		
	    // 餐盒费
	    orderListParam.put("package_box_fee", CommonUtil.keepTwoDecimal(Double.valueOf(CommonUtil.fen2Yuan(requestOrder.optInt("package_fee")))));
		
		// 订单状态
		orderListParam.put("order_state", "01");
		// 订单类型
		orderListParam.put("order_type", "WM02");
		if (discount_fee != 0)
		{
			// 优惠类型
			orderListParam.put("discount_mode_id", "7");
		}
		orderListParam.put("third_order_state", "1");
		orderListParam.put("order_state_desc", "待确认");
		
		

		 //------------start田老师对账报表佣金+实收不等于应收问题修复2017-06-06-------------------------
     	orderListParam.put("platform_rate", CommonUtil.keepTwoDecimal(baiDuRate));//优惠信息平台承担
     	orderListParam.put("shop_rate", CommonUtil.keepTwoDecimal(total_shopRate));//优惠信息商户承担
     	//------------end田老师对账报表佣金+实收不等于应收问题修复2017-06-06-------------------------
     	
     	
     	CcPartitionUtils.lackInsertParam(tenantId,orderListParam);
     	
		//保存订单信息
		orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_list", orderListParam) );
		
		
		// 保存到redis
		JSONObject shop = thirdPartyOrder.optJSONObject("body").optJSONObject("shop");
		JSONObject redisJson = new JSONObject();
		redisJson.put("shop_id", shop.optString("id"));
		logger.info("百度存入redis里的信息" + redisJson);
		redis.saveBykv(requestOrder.optString("order_id"), redisJson.toString(), 172800);
        return orderListParam;
	}

	@Override
	public void saveOrderItem() throws Exception
	{

		discountRate = (double) (discount_fee+discountR_amount) / product_amount;
		sendFeeRate = (double) send_fee / product_amount;
		double discountR_rate=discountR_amount/ product_amount;
		food_sharing_date = takeawayBusinessIncome/Double.valueOf(CommonUtil.fen2Yuan(product_amount));

		JSONArray requestProductsArray = thirdPartyOrder.optJSONObject("body").optJSONArray("products");
		JSONObject orderItemParam = null;
		int group_index = 1;
		//String price = CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(0).optInt("package_price"));
		int number = 0;// 餐盒费数量
	
		int productsListSize = requestProductsArray.size();
		double product_discount_amount = 0.0;// 商品优惠总金额
		double product_send_fee_amount = 0.0;// 商品总配送费
		double share_product_price_total = 0.0; // 总共已经摊多少钱
		String item_ids = "";
		for (int i = 0; i < productsListSize; i++)
		{
			item_ids += String.valueOf(requestProductsArray.getJSONObject(i).optInt("product_id",0)) + ",";
		}
		String query_sql = "SELECT item_code,is_charge_commission from cc_third_item_info a where cast(a.item_code as INT) in (" + item_ids.substring(0, item_ids.length() - 1) + ") and channel='BD06' and shop_id=" + storeId + " ";
		List<JSONObject> third_item_info_list = this.dao.query4Json(tenantId, query_sql);
		
		//at 2017-10-16解决数据第一个菜品没有餐盒，第二个菜品有餐盒引起的餐盒费用取餐盒费为0的问题；
		String price ="0";
		for (int j = 0; j < productsListSize; j++){
			int p=requestProductsArray.getJSONObject(j).optInt("package_price");
			if(p>0){
				price=CommonUtil.fen2Yuan(p);
				break;
			}
		}
		//end 2017-10-16
		
		Map<String, String> third_item_info_list_map = new HashMap<String, String>();

		if (third_item_info_list.size() > 0)
		{
			for (JSONObject third_item_obj : third_item_info_list)
			{
				third_item_info_list_map.put(third_item_obj.optString("item_code"), third_item_obj.optString("is_charge_commission"));
			}
		}
		Map<String, String> unitMap = new HashMap<>();
		//at 2017-11-09  张勇 SQL中增加默认规格条件
		String unit_query_sql = "select item_id,id as unit_id,unit_name from hq_item_unit where is_default='Y' and valid_state='1' and item_id in (" + item_ids.substring(0, item_ids.length() - 1) + ")";
		List<JSONObject> item_unit_list = this.dao.query4Json(tenantId, unit_query_sql);
		if (item_unit_list.size() > 0)
		{
			for (JSONObject item_unit_obj : item_unit_list)
			{
				unitMap.put(item_unit_obj.optString("item_id"), item_unit_obj.optString("unit_id")+"@"+item_unit_obj.optString("unit_name"));
			}
		}
		int box_num=0;
		for (int j = 0; j < productsListSize; j++)
		{
			int productAmount = requestProductsArray.getJSONObject(j).optInt("product_amount");
			box_num += (requestProductsArray.getJSONObject(j).optInt("package_amount") * productAmount);
		}
		for (int i = 0; i < productsListSize; i++)
		{
			orderItemParam = new JSONObject();
			
			int productAmount = requestProductsArray.getJSONObject(i).optInt("product_amount");
			if(requestProductsArray.getJSONObject(i).optString("product_name").contains("红包")&&CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(i).optInt("product_price")).equals("0")){
				continue;
			}
			number += (requestProductsArray.getJSONObject(i).optInt("package_amount") * productAmount);
						
			// 商户ID
			orderItemParam.put("tenancy_id", tenantId);
			// 明细索引
			orderItemParam.put("group_index", group_index++);
			
			orderItemParam.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
			// 订单号
			orderItemParam.put("order_code", orderCode);

			// 商品ID
			 int itemId =  requestProductsArray.getJSONObject(i).optInt("product_id",0);//取值改成int整数
//			orderItemParam.put("is_commission",  CommonUtil.checkStringIsNotEmpty(third_item_info_list_map.get(itemId))?third_item_info_list_map.get(itemId):"0");
			 orderItemParam.put("is_commission",  "1");
			orderItemParam.put("item_id", itemId);
			String item_menu_id_sql = "SELECT c.item_menu_id from hq_item_menu_details a  left join hq_item_menu_class d on a.id=d.details_id LEFT JOIN  hq_item_menu  b on b.id=a.item_menu_id LEFT JOIN hq_item_menu_organ c on c.item_menu_id=b.id where c.store_id=" + storeId + " and a.item_id="
					+ itemId + " and d.chanel='BD06'";
			List<JSONObject> item_menu_id_list = this.dao.query4Json(tenantId, item_menu_id_sql);
			if (item_menu_id_list.size() > 0)
			{
				orderItemParam.put("item_menu_id", item_menu_id_list.get(0).optInt("item_menu_id",0));
			}
			// 商品名称
			orderItemParam.put("item_name", requestProductsArray.getJSONObject(i).optString("product_name"));
			// 规格ID
			int unitId=0;
			String unitName="";
			if(CommonUtil.checkStringIsNotEmpty(unitMap.get(String.valueOf(itemId)))&&unitMap.get(String.valueOf(itemId)).contains("@")) {
				String[] unitArr=unitMap.get(String.valueOf(itemId)).split("@");
				unitId=Integer.parseInt(unitArr[0]);
				unitName=unitArr[1];
			}else {
				unitId=0;
				unitName="0";
			}
			orderItemParam.put("unit_id", unitId);
			orderItemParam.put("unit_name", unitName);
			// 份数
			orderItemParam.put("number", productAmount);
			//商品原价合计
			product_org_total_fee+=Double.valueOf(CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(i).optInt("product_price"))).doubleValue()*productAmount;
			// 商品价格
			orderItemParam.put("price", CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(i).optInt("product_price")));
			// 商品总价
			String product_fee = CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(i).optInt("product_fee"));
			orderItemParam.put("product_fee", CommonUtil.keepTwoDecimal(Double.valueOf(product_fee)));
			StringBuilder item_price_sql = new StringBuilder();
			item_price_sql.append("select c.id,c.unit_name,a.price from hq_item_pricesystem a left join organ b on cast(a.price_system as varchar) = b.price_system left JOIN hq_item_unit c on a.item_unit_id = c.id ");
			item_price_sql.append("where c.item_id='" + itemId + "' and b.id = '" + storeId + "' and a.chanel = 'BD06' and c.valid_state = '1'");
			List<JSONObject> item_price_list = this.dao.query4Json(tenantId, item_price_sql.toString());
			Double yuan_product_fee = 0.0d;
			if (item_price_list.size() > 0)
			{
				yuan_product_fee = Scm.pmui(item_price_list.get(0).optDouble("price"), productAmount * 1.0);
			}
			else
			{
				yuan_product_fee = Double.valueOf(product_fee);
			}

			// 优惠金额
			double discount_amount = Double.valueOf(product_fee)*discountRate;
			comboDiscountRate = (double) discount_amount / Double.valueOf(product_fee);
			double discountk_amount=Double.valueOf(product_fee)*discountR_rate;
			// 单品配送费
			double send_fee_amount = Scm.pmui(Double.valueOf(product_fee), sendFeeRate);
			product_discount_amount += discount_amount;
			product_send_fee_amount += send_fee_amount;
			orderItemParam.put("discount_amount", Double.isNaN(discount_amount)?"0":CommonUtil.keepTwoDecimal(discount_amount));
			orderItemParam.put("discountk_amount", Double.isNaN(discountk_amount)?0.0:CommonUtil.keepTwoDecimal(discountk_amount));
			orderItemParam.put("costs", CommonUtil.keepTwoDecimal(send_fee_amount));
			// 实收金额
			double real_amount = Scm.psub(Double.valueOf(product_fee), discount_amount);
			double costs_real_amount = Scm.padd(real_amount, send_fee_amount);
			orderItemParam.put("real_amount", CommonUtil.keepTwoDecimal(real_amount));
			// orderItemParam.put("share_amount", costs_real_amount);
			if(box_num==0&&share_product_price_total<takeawayBusinessIncome&&i==productsListSize-1){
				orderItemParam.put("share_amount", Scm.psub(takeawayBusinessIncome, share_product_price_total));
			} else if(share_product_price_total > takeawayBusinessIncome&&box_num!=0)
			{
				orderItemParam.put("share_amount",CommonUtil.keepTwoDecimal(Scm.pmui(Double.valueOf(yuan_product_fee), food_sharing_date * Double.valueOf("0.6"))));
				share_product_price_total = share_product_price_total - Scm.pmui(Double.valueOf(yuan_product_fee), food_sharing_date) + Scm.pmui(Double.valueOf(yuan_product_fee), food_sharing_date * Double.valueOf("0.6"));
			}else{
				orderItemParam.put("share_amount", CommonUtil.keepTwoDecimal(Scm.pmui(Double.valueOf(product_fee), food_sharing_date)));
			}
			share_product_price_total += orderItemParam.optDouble("share_amount");
			// 优惠价格
			// orderItemParam.put("discount_price", Scm.pdiv(costs_real_amount,
			// Double.valueOf(productAmount)));
			// 优惠方式
			orderItemParam.put("discount_mode_id", "7");
			orderItemParam.put("store_id", storeId);
			
			String item_infoSql = "select is_combo from hq_item_info a where a.id=" + itemId;
			List<JSONObject> list = this.dao.query4Json(tenantId, item_infoSql);
			if (list.size() > 0)
			{

				JSONObject item_info_obj = new JSONObject();
				item_info_obj = list.get(0);
				if (item_info_obj.optString("is_combo").equalsIgnoreCase("y"))
				{

					List<JSONObject> order_item_details_list = new ArrayList<JSONObject>();
					String hq_item_combo_details_sql = "select * from hq_item_combo_details a where a.iitem_id=" + itemId;
					List<JSONObject> item_combo_details_list = this.dao.query4Json(tenantId, hq_item_combo_details_sql);
					// 套餐明细表
					if (item_combo_details_list.size() > 0)
					{
						int b = 1;
						for (int k = 0; k < item_combo_details_list.size(); k++)
						{
							b++;
							JSONObject item_combo_details_obj = item_combo_details_list.get(k);
							double combo_discount_amount = 0.0;// 套餐已优惠总金额
							int combo_num = item_combo_details_obj.optInt("combo_num");
							if (b == item_combo_details_list.size())
							{
								// 套餐明细是项目组
								if (item_combo_details_obj.optString("is_itemgroup").equalsIgnoreCase("y"))
								{
									hq_item_combo_details_sql = "select a.* ,b.standard_price from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id  where a.item_group_id=" + item_combo_details_obj.optInt("details_id");
									List<JSONObject> item_group_details_list = this.dao.query4Json(tenantId, hq_item_combo_details_sql.toString());
									if (item_group_details_list.size() > 0)
									{
										for (int l = 0; l < combo_num; l++)
										{
											JSONObject item_group_details_obj = item_group_details_list.get(l);
											item_group_details_obj.put("group_index", group_index - 1);
											item_group_details_obj.put("order_code", orderCode);
											item_group_details_obj.put("item_id", item_group_details_obj.optInt("item_id",0));
											item_group_details_obj.put("unit_id", item_group_details_obj.optInt("item_unit_id",0));
											item_group_details_obj.put("price", CommonUtil.keepTwoDecimal(Double.valueOf(item_combo_details_obj.optString("standardprice"))));
											item_group_details_obj.put("number", productAmount);
											item_group_details_obj.put("product_fee", CommonUtil.keepTwoDecimal((item_combo_details_obj.optDouble("standardprice")) * productAmount));
											item_group_details_obj.put("discount_amount",CommonUtil.keepTwoDecimal(discount_amount - combo_discount_amount));
											item_group_details_obj.put("real_amount",CommonUtil.keepTwoDecimal(Scm.psub(item_group_details_obj.optDouble("product_fee"), item_group_details_obj.optDouble("discount_amount"))));
											item_group_details_obj.put("report_date",  "'"+DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10)+"'");
											item_group_details_obj.remove("id");
											order_item_details_list.add(item_group_details_obj);
										}

									}
								}
								else
								// 套餐明细不是项目组
								{
									item_combo_details_obj.remove("id");
									item_combo_details_obj.put("order_code", orderCode);
									item_combo_details_obj.put("unit_id", item_combo_details_obj.optInt("item_unit_id",0));
									item_combo_details_obj.put("price", CommonUtil.keepTwoDecimal(Double.valueOf(item_combo_details_obj.optString("standardprice"))));
									item_combo_details_obj.put("number", productAmount * combo_num);
									item_combo_details_obj.put("product_fee", CommonUtil.keepTwoDecimal((item_combo_details_obj.optDouble("standardprice")) * (item_combo_details_obj.optDouble("number"))));
									item_combo_details_obj.put("discount_amount", CommonUtil.keepTwoDecimal(discount_amount - combo_discount_amount));
									item_combo_details_obj.put("real_amount", CommonUtil.keepTwoDecimal(Scm.psub(item_combo_details_obj.optDouble("product_fee"), item_combo_details_obj.optDouble("discount_amount"))));
									item_combo_details_obj.put("item_id", item_combo_details_obj.optInt("details_id",0));
									item_combo_details_obj.put("group_index", group_index - 1);
									item_combo_details_obj.put("report_date",  "'"+DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10)+"'");
									order_item_details_list.add(item_combo_details_obj);
								}
							}
							else
							{
								// 套餐明细是项目组
								if (item_combo_details_obj.optString("is_itemgroup").equalsIgnoreCase("y"))
								{
									hq_item_combo_details_sql = "select a.* ,b.standard_price from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id  where a.item_group_id=" + item_combo_details_obj.optInt("details_id");
									List<JSONObject> item_group_details_list = this.dao.query4Json(tenantId, hq_item_combo_details_sql.toString());

									if (item_group_details_list.size() > 0)
									{
										for (int l = 0; l < combo_num; l++)
										{
											JSONObject item_group_details_obj = item_group_details_list.get(l);
											item_group_details_obj.put("group_index", group_index - 1);
											item_group_details_obj.put("order_code", orderCode);
											item_group_details_obj.put("item_id", item_group_details_obj.optInt("item_id",0));
											item_group_details_obj.put("unit_id", item_group_details_obj.optInt("item_unit_id",0));
											item_group_details_obj.put("price", CommonUtil.keepTwoDecimal(Double.valueOf(item_combo_details_obj.optString("standardprice"))));
											item_group_details_obj.put("number", productAmount);
											item_group_details_obj.put("product_fee", CommonUtil.keepTwoDecimal((item_combo_details_obj.optDouble("standardprice")) * productAmount));
											item_group_details_obj.put("discount_amount", CommonUtil.keepTwoDecimal(Scm.pmui(item_group_details_obj.optDouble("product_fee"), comboDiscountRate)));
											combo_discount_amount += Scm.pmui(item_group_details_obj.optDouble("product_fee"), comboDiscountRate);
											item_group_details_obj.put("real_amount", CommonUtil.keepTwoDecimal(Scm.psub(item_group_details_obj.optDouble("product_fee"), item_group_details_obj.optDouble("discount_amount"))));
											item_group_details_obj.put("report_date",  "'"+DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10)+"'");
											item_group_details_obj.remove("id");
											order_item_details_list.add(item_group_details_obj);
										}

									}
								}
								else
								// 套餐明细不是项目组
								{
									item_combo_details_obj.remove("id");
									item_combo_details_obj.put("order_code", orderCode);
									item_combo_details_obj.put("unit_id", item_combo_details_obj.optInt("item_unit_id",0));
									item_combo_details_obj.put("price", item_combo_details_obj.optString("standardprice"));
									item_combo_details_obj.put("number", productAmount * combo_num);
									item_combo_details_obj.put("product_fee", CommonUtil.keepTwoDecimal((item_combo_details_obj.optDouble("standardprice")) * (item_combo_details_obj.optDouble("number"))));
									item_combo_details_obj.put("discount_amount", CommonUtil.keepTwoDecimal(Scm.pmui(item_combo_details_obj.optDouble("product_fee"), comboDiscountRate)));
									combo_discount_amount += Scm.pmui(item_combo_details_obj.optDouble("product_fee"), comboDiscountRate);
									item_combo_details_obj.put("real_amount", CommonUtil.keepTwoDecimal(Scm.psub(item_combo_details_obj.optDouble("product_fee"), item_combo_details_obj.optDouble("discount_amount"))));
									item_combo_details_obj.put("item_id", item_combo_details_obj.optInt("details_id",0));
									item_combo_details_obj.put("group_index", group_index - 1);
									item_combo_details_obj.put("report_date",  "'"+DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10)+"'");
									order_item_details_list.add(item_combo_details_obj);
								}
							}

						}
					}
					
					CcPartitionUtils.lackInsertParam(tenantId,orderItemParam, order_item_details_list);
					
					this.dao.insertBatchIgnorCase(tenantId, "cc_order_item_details", order_item_details_list);
				}

			}
			
			
			CcPartitionUtils.lackInsertParam(tenantId,orderItemParam);
			
			orderBatchSql.append(CommonUtil.insertJSONParamsToSql( "cc_order_item", orderItemParam));
			
		}
		
		if(number>0){
			// 保存餐盒费信息
			JSONObject packageBoxFee = new JSONObject();
			packageBoxFee.put("tenancy_id", tenantId);
			packageBoxFee.put("group_index", group_index);
			packageBoxFee.put("order_code", orderCode);
			packageBoxFee.put("price", CommonUtil.keepTwoDecimal(Double.valueOf(price)));
			packageBoxFee.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
			packageBoxFee.put("number", number);
			packageBoxFee.put("is_commission", 0);
			packageBoxFee.put("store_id", storeId);
			// String sql =
			// "SELECT item_id,unit_id,item_name FROM cc_third_organ_info where
			// shop_id=".concat(storeId);
			String sql = "SELECT B.item_id,B.unit_id,C.item_name,d.item_menu_id FROM cc_meals_info A " + " LEFT JOIN cc_meals_info_default b ON A . ID = b.meals_id LEFT JOIN hq_item_info C ON C . ID = b.item_id LEFT JOIN hq_item_menu_details d on d.item_id=b.item_id"
					+ " LEFT JOIN hq_item_menu_class f on f.details_id=d.id  LEFT JOIN hq_item_menu_organ e on e.item_menu_id=d.item_menu_id" + " WHERE A .store_id = " + storeId + " AND A .channel = 'BD06' and f.chanel='BD06' AND A .meals_type = 'MR03' and e.store_id=" + storeId + " ";
			List<JSONObject> pb = this.dao.query4Json(tenantId, sql);
			if(null==pb||pb.isEmpty()){
				packageBoxFee.put("item_id", 0);
				packageBoxFee.put("unit_id", 0);
				packageBoxFee.put("item_name", "餐盒费");
				logger.info("[百度]收单异常:订单号[" + orderCode + "]->未正确获取餐盒费信息!");
				//throw new Exception("[百度]收单失败:未正确获取餐盒费信息!");
			}else {
				packageBoxFee.putAll(pb.get(0));
			}
			String package_fee = CommonUtil.fen2Yuan(requestOrder.optInt("package_fee"));
			packageBoxFee.put("product_fee", CommonUtil.keepTwoDecimal(Double.valueOf(package_fee)));
			// 优惠金额
			double packageBox_discount_amount = Scm.psub(Double.valueOf(CommonUtil.fen2Yuan(discount_fee)), product_discount_amount);
			packageBoxFee.put("discount_amount",  Double.isNaN(Double.valueOf(package_fee)*discountRate)?0.0:CommonUtil.keepTwoDecimal(Double.valueOf(package_fee)*discountRate));
			packageBoxFee.put("discountk_amount", Double.isNaN(discountR_rate*Double.valueOf(package_fee))?0.0:CommonUtil.keepTwoDecimal(discountR_rate*Double.valueOf(package_fee)));
			// 菜品摊的配送费
			double packageBox_send_fee_amount = Scm.psub(Double.valueOf(CommonUtil.fen2Yuan(send_fee)), product_send_fee_amount);
			packageBoxFee.put("costs", CommonUtil.keepTwoDecimal(packageBox_send_fee_amount));
			// 实收金额
			double packageBox_real_amount = Scm.psub(Double.valueOf(package_fee), packageBox_discount_amount);
			// packageBoxFee.put("real_amount",packageBox_real_amount);

			@SuppressWarnings("unused")
			double packageBox_send_fee_real_amount = Scm.padd(packageBox_real_amount, packageBox_send_fee_amount);
			packageBoxFee.put("real_amount", CommonUtil.keepTwoDecimal(packageBox_real_amount));
			// packageBoxFee.put("share_amount",
			// packageBox_send_fee_real_amount);
//			packageBoxFee.put("share_amount", Double.isNaN(Scm.pmui(Double.valueOf(package_fee), food_sharing_date))?0.0:Scm.pmui(Double.valueOf(package_fee), food_sharing_date));
			packageBoxFee.put("share_amount", CommonUtil.keepTwoDecimal(Scm.psub(takeawayBusinessIncome, share_product_price_total)));
			// 优惠价格
			// packageBoxFee.put("discount_price",
			// Scm.pdiv(packageBox_send_fee_real_amount,
			// Double.valueOf(number)));
			// 优惠方式
			// 优惠类型
				packageBoxFee.put("discount_mode_id", "7");

			// this.dao.insertIgnorCase(tenantId, "cc_order_item",
			// packageBoxFee);
				
			CcPartitionUtils.lackInsertParam(tenantId,packageBoxFee);
				
			orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_item", packageBoxFee));
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	public void saveOrderRepayment() throws Exception
	{
		Integer shop_real_amount=requestOrder.optInt("shop_fee");
		 double shop_real_amount_y=Double.valueOf(CommonUtil.fen2Yuan(shop_real_amount));
		
		JSONObject payment = new JSONObject();
		payment.put("tenancy_id", tenantId);
		payment.put("remark", "baidu_pay");
		payment.put("order_code", orderCode);
		payment.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
		payment.put("pay_money", CommonUtil.keepTwoDecimal(shopFee));
		payment.put("third_bill_code", requestOrder.optString("order_id"));
		payment.put("store_id", storeId);
		
		payment.put("shop_real_amount", shop_real_amount_y);
		payment.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
		//at 2017-08-18 此值与pay_money一致
		payment.put("local_currency", CommonUtil.keepTwoDecimal(shopFee));
		
		
		// 支付类型
		String payType = requestOrder.optString("pay_type");
		String store_sql = "select remark from organ where id = " + storeId + " ";
		JSONObject store_obj = this.dao.query4Json(tenantId, store_sql).get(0);
		if (store_obj.optString("remark").equals("read_from_rif")||StringUtils.equals("2", payType))
		{
			//JSONObject payment = new JSONObject();
			//payment.put("tenancy_id", tenantId);
			String paySql = "SELECT a.id as payment_id FROM payment_way a LEFT JOIN payment_way_of_ogran b on a.id=b.payment_id where a.payment_class='baidu_pay' and b.organ_id=" + storeId;
			List list = this.dao.query4Json(tenantId, paySql);
			if (!list.isEmpty())
			{
				//PAYMENT_ID 保存值
				payment.put("payment_id", ((JSONObject) list.get(0)).optString("payment_id"));
			}
			//payment.put("order_code", orderCode);
			//payment.put("pay_money", Double.valueOf(CommonUtil.fen2Yuan(requestOrder.optInt("total_fee"))) - Double.valueOf(CommonUtil.fen2Yuan(discount_fee)));
			//payment.put("third_bill_code", requestOrder.optString("order_id"));
//			this.dao.insertIgnorCase(tenantId, "cc_order_repayment", payment);
			//this.dao.execute(tenantId,CommonUtil.insertJSONParamsToSql( "cc_order_repayment", payment));
		}
		
		CcPartitionUtils.lackInsertParam(tenantId,payment);
		
		orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_repayment", payment));
	}

	@Override
	public void saveOrderDiscount() throws Exception
	{
		JSONArray requestDiscountArray = thirdPartyOrder.optJSONObject("body").optJSONArray("discount");

		List<JSONObject> discountList = new ArrayList<>();
		
		StringBuilder sql=new StringBuilder();

		for (int i = 0; i < requestDiscountArray.size(); i++)
		{
			sql.append("INSERT INTO cc_order_discount (tenancy_id,store_id,report_date,order_code,discount_type,activity_id,discount_desc,discount_fee,baidu_rate,shop_rate,agent_rate,operator_time,logistics_rate ) VALUES (");
			
			JSONObject discountParam = new JSONObject();
			
			JSONObject discount = requestDiscountArray.getJSONObject(i);
			// 商户ID
			discountParam.put("tenancy_id", tenantId);
			sql.append("'"+tenantId+"',");
			// 门店ID
			discountParam.put("store_id", storeId);
			sql.append("'"+storeId+"',");
			
			String report_date=DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10);
			discountParam.put("report_date",  report_date);
			sql.append("'"+report_date+"',");
			// 订单号
			discountParam.put("order_code", orderCode);
			sql.append("'"+orderCode+"',");
			// 优惠类型
			String discountType=discount.optString("type");
			discountParam.put("discount_type", discountType);
			sql.append("'"+discountType+"',");
			// 活动ID
			String activityId=discount.optString("activity_id");
			discountParam.put("activity_id", activityId);
			sql.append("'"+activityId+"',");
			// 优惠描述
			String desc=CommonUtil.replaceEvilChar(discount.optString("desc").trim());
			discountParam.put("discount_desc", desc);
			sql.append("'"+desc+"',");
			// 优惠金额
			String discountFee=CommonUtil.checkStringIsNotEmpty(CommonUtil.fen2Yuan(discount.optInt("fee")))?CommonUtil.fen2Yuan(discount.optInt("fee")):"0.0";
			discountParam.put("discount_fee", discountFee);
			sql.append("'"+CommonUtil.keepTwoDecimal(Double.valueOf(discountFee))+"',");
			 total_discount_fee+=Double.parseDouble(discountFee);
			// 百度承担金额
			String baiduRate=CommonUtil.fen2Yuan(discount.optInt("baidu_rate"));
			total_baidu_rate+=discount.optInt("baidu_rate");
			discountParam.put("baidu_rate", baiduRate);
			sql.append("'"+CommonUtil.keepTwoDecimal(Double.valueOf(baiduRate))+"',");
			// 商户承担金额
			String shopRate=CommonUtil.fen2Yuan(discount.optInt("shop_rate"));
			total_shop_rate+=discount.optInt("shop_rate");
			discountParam.put("shop_rate", shopRate);
			sql.append("'"+CommonUtil.keepTwoDecimal(Double.valueOf(shopRate))+"',");
			
			// 代理商承担金额
			String agentRate=CommonUtil.fen2Yuan(discount.optInt("agent_rate"));
			discountParam.put("agent_rate",agentRate) ;
			sql.append("'"+agentRate+"',");
			//操作时间
			discountParam.put("operator_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			sql.append("'"+DateUtil.format(new Timestamp(System.currentTimeMillis()))+"',");
			// 物流承担金额
			String logisticsRate=CommonUtil.fen2Yuan(discount.optInt("logistics_rate"));
			discountParam.put("logistics_rate", logisticsRate);
			sql.append("'"+logisticsRate+"');");

			CcPartitionUtils.lackInsertParam(tenantId,discountParam);
			
			discountList.add(discountParam);
			
			orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_discount", discountParam));
		}
		if(discountList.isEmpty()){
			return;
		}
		
		//orderBatchSql.append(sql.toString());
		logger.info("订单号:[" + orderCode + "]===>优惠信息:" + discountList + "百度优惠原始参数:" + requestDiscountArray+" SQL:"+sql.toString());
//		this.dao.execute(tenantId, sql.toString());
//		this.dao.insertBatchIgnorCase(tenantId, "cc_order_discount", discountList);
	}

	@Override
	public JSONObject thirdPartyResponse()
	{

		JSONObject body = new JSONObject();
		JSONObject data = new JSONObject();
		data.put("source_order_id", requestOrder.optString("order_id"));
		body.put("data", data);

		Sign sign = null;
		try
		{

			sign = SignHolder.getShopSign(tenantId, storeId, channel);

			if (0 == err.optInt("errno"))
			{
				body.put("errno", 0);
				body.put("error", "success");
			}
			else
			{
				body.putAll(err);
			}
			String resultString = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.RESP_ORDER_CREATE, body, "2.0");

			body = JSONObject.fromObject(resultString);

		}
		catch (Exception e)
		{
			e.printStackTrace();

			body.put("errno", 200);
			body.put("error", "生成响应数据时失败！");
			body.put("errmsg", e);
		}
		return body;
	}

	@SuppressWarnings("unchecked")
	@Override
	public JSONObject orderStatusPush(JSONObject params)
	{
		Map body = new TreeMap();
		String source = null, secrect = null;
		try
		{
			//at 20170822 改变redis的读取方式
			String thirdOrderCode = params.optJSONObject("body").optString("order_id");
			String redisJsonStr=redis.getByKey(thirdOrderCode);
			JSONObject redisJson=JSONObject.fromObject(redisJsonStr);
			//JSONObject redisJson = taskRedisDao.read(thirdOrderCode.getBytes());
			logger.info("百度从redis里取到的信息" + redisJson.toString());
			//taskRedisDao.save(thirdOrderCode.getBytes(), redisJson);
			logger.info("百度从redis里取到信息后执行");
			String[] shopId = redisJson.optString("shop_id").split("@");
			storeId = shopId[0];
			tenantId = shopId[1];
			DBContextHolder.setTenancyid(tenantId);
			Sign sign = SignHolder.getShopSign(tenantId, storeId, channel);
			source = sign.getSource();
			secrect = sign.getSecret();
			boolean flag=false;
			//取消状态
			int status = params.optJSONObject("body").optInt("status");
			logger.info("第三方请求的状态：=====================521=========="+status);
			String orderState = null;
			switch (status)
			{
//				case 1:
//					orderState = "01";
//					break;
//				case 5:
//					orderState = "04";
//					break;
//				case 7:
//					orderState = "06";
//					break;
//				case 8:
//					orderState = "07";
//					break;
				case 10:
					flag=true;
					/**start
					 * 新增百度取消状态 王俊辉 2017-11-3 
					 */
					this.bdCancel( tenantId, thirdOrderCode);
					//end
					
					orderState = "08";
					JSONObject dataObj = new JSONObject();
					dataObj.put("tenancy_id", tenantId);
					dataObj.put("store_id", storeId);
					dataObj.put("third_order_code", thirdOrderCode);
					dataObj.put("reason_code", "999801");
					dataObj.put("requestId", params.optString("requestId"));
					orderManagementService.orderCancel(tenantId, dataObj);
					break; 
				case 9:
					flag=false;
					orderState = "10";
					JSONObject data_complete_Obj = new JSONObject();
					data_complete_Obj.put("tenancy_id", tenantId);
					data_complete_Obj.put("store_id", storeId);
					data_complete_Obj.put("third_order_code", thirdOrderCode);
					data_complete_Obj.put("requestId", params.optString("requestId"));
					orderManagementService.orderComplete(tenantId, data_complete_Obj);
					break;
				default:
					break;
			}
			body.put("errno", 0);
			body.put("error", "success");

		}
		catch (Exception e)
		{
			e.printStackTrace();

			body.put("errno", 200);
			body.put("error", "生成响应数据时失败！");
			body.put("errmsg", e);
		}

		String resultString = CommonUtil.cmdFactory1(source, secrect, CmdType.RESP_ORDER_STATUS_PUSH, body);
		return JSONObject.fromObject(resultString);

	}
    
	/**
	 * 新增百度取消状态 王俊辉 2017-11-3 
	 */
	public void bdCancel(String tenantId,String thirdOrderCode){
			try {
				//判断百度订单取消(平台/用户)  10
					logger.info("============1521=========从百度里面拿取消的状态thirdOrderCode:="+thirdOrderCode);
					/*//在cc_order_reason_detail表里面查询是否有单
				    String selectYc ="SELECT s.cancel_type,s.order_code FROM cc_order_list s WHERE s.third_order_code = '"+thirdOrderCode+"'";
				    selectYc=CcPartitionUtils.makeSQL(tenantId,selectYc, "", CcPartitionUtils.TYPE_ORDERCODE_NO);
				    List<JSONObject> order_reason_detail = dao.query4Json(tenantId, selectYc);
				    //新增取消状态表 cc_order_reason_detail
				    if(!"0".equals(order_reason_detail.get(0).optString("cancel_type"))){
				       String insertSql = "INSERT INTO cc_order_reason_detail (order_code,TYPE,complaint_content,complaints_time,order_state)"
			                 +"VALUES ('"+order_reason_detail.get(0).optString("order_code")+"','QX01','"+reason+"','"+new Date()+"','08');";
				       insertSql=CcPartitionUtils.makeSQL(tenantId,insertSql, "", CcPartitionUtils.TYPE_ORDERCODE_NO);
						dao.execute(tenantId, insertSql);
				    }*/
					//服务员取消
				    String sql = "update cc_order_list set cancel_type='0',order_state='08',payment_state='04' where third_order_code='" + thirdOrderCode + "'";
					
					sql=CcPartitionUtils.makeSQL(tenantId,sql, "", CcPartitionUtils.TYPE_ORDERCODE_NO);
				dao.execute(tenantId, sql);
			} catch (Exception e) {
				e.printStackTrace();
		}
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public JSONObject orderStatusGet(JSONObject params)
	{
		Map body = null;
		Sign sign = null;
		JSONObject order = null;
		int status = 0;
		
		JSONObject result = new JSONObject();
		try
		{
			String thirdOrderCode = params.optJSONObject("body").optString("order_id");
			//at 20170822更改redis读取方式
			//JSONObject redisJson = taskRedisDao.read(thirdOrderCode.getBytes());
			//taskRedisDao.save(thirdOrderCode.getBytes(), redisJson);
	
			String redisJsonStr=redis.getByKey(thirdOrderCode);
			JSONObject redisJson=JSONObject.fromObject(redisJsonStr);
			
			String[] shopId = redisJson.optString("shop_id").split("@");
			String storeId = shopId[0];
			String tenantId = shopId[1];
	
			DBContextHolder.setTenancyid(tenantId);
		
		
		
			sign = SignHolder.getShopSign(tenantId, storeId, channel);

			String sql = "select order_code,order_state from cc_order_list where third_order_code='" + thirdOrderCode + "'";
			
			sql=CcPartitionUtils.makeSQL(tenantId,sql, "", CcPartitionUtils.TYPE_ORDERCODE_NO);
			
			order = dao.query4Json(tenantId, sql).get(0);

			String orderState = order.optString("order_state");

			switch (orderState)
			{
				case "01":
					status = 1;
					break;
				case "04":
					status = 5;
					break;
				case "06":
					status = 7;
					break;
				case "07":
					status = 8;
					break;
				case "08":
					status = 10;
					break;
				case "10":
					status = 9;
					break;
				default:
					break;
			}

			body = new TreeMap();
			body.put("errno", 0);
			body.put("error", "success");
		}
		catch (Exception e)
		{
			e.printStackTrace();

			body.put("errno", 200);
			body.put("error", "生成响应数据时失败！");
			body.put("errmsg", e);

		}

		Map data = new TreeMap();
		data.put("source_order_id", order.optString("order_code"));
		data.put("status", status);

		body.put("data", data);

		String resultString = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.RESP_ORDER_STATUS_GET, body);

		result = JSONObject.fromObject(resultString);

		return result;
	}
	
	//面香新的佣金计算方式
	 public void calcCommission() {
		 

			try
			{
				JSONObject param = new JSONObject();
				param.put("order_code", orderCode);
				param.put("tenentid", tenantId);
				Integer shop_real_amount=requestOrder.optInt("shop_fee");
				
		        Integer platform_charge_amount=CommonUtil.yuan2Fen(shopFee)-shop_real_amount;
		        double third_commission_amount = 0;
	            double boxFee=Double.valueOf(CommonUtil.fen2Yuan(requestOrder.optInt("package_fee")));
//	            if(delivery_party.equals("1")){
//	            	third_commission_amount=platform_charge_amount+total_baidu_rate-send_fee;
//	            	third_commission_amount=platform_charge_amount+total_baidu_rate;
//	            }else{
//	            	third_commission_amount=platform_charge_amount+total_baidu_rate;
//	            }
	            
	            
	            //at 20170904 优惠总额大于等订单总额于时，平台收取金额（platform_charge_amount)加上0.01,由于取值是分，固加1
	    		if(total_discount_fee>=order_total_price&&user_actual_pay==0.01){
	    			platform_charge_amount+=1;
	    		}
	            
	            
	            //-------------START 2017-07-13百度佣金计算公式修改为：百度佣金=餐盒费+菜品明细-门店实收-商家承担  
	            //-----start 2017-07-21
	    		double actualPay=Double.valueOf(CommonUtil.fen2Yuan(requestOrder.optInt("user_fee")));//用户实付
	    		int deliveryType=requestOrder.optInt("delivery_party"); 
	    		if(Constant.THIRD_DELIVERY==deliveryType){//平台配送
	    			//百度佣金=用户实付-门店实收-配送费+平台承担
	    			third_commission_amount=actualPay-Double.valueOf(CommonUtil.fen2Yuan(send_fee))-Double.valueOf(CommonUtil.fen2Yuan(shop_real_amount))+Double.valueOf(CommonUtil.fen2Yuan(total_baidu_rate));
	    		}else{
	    			//百度佣金=用户实付-门店实收+平台承担
	    			third_commission_amount=actualPay-Double.valueOf(CommonUtil.fen2Yuan(shop_real_amount))+Double.valueOf(CommonUtil.fen2Yuan(total_baidu_rate));
	    		}
	            //----end 2017-07-21
	            //---------------END 2017-07-13
				JSONObject result = orderDiscountCommissionReportService.getOrderDiscountCommission(tenantId, param);
				if (!result.has("discount_commission"))
				{
					return;
				}
				double discountCommission = result.optDouble("discount_commission");
				double commissionRate = result.optDouble("commission_rate");
				
				double package_fee=Double.valueOf(CommonUtil.fen2Yuan(requestOrder.optInt("package_fee")));
				
				
				//start at 2017-09-30 
				
				//本地门店实收=餐盒费+菜品明细-佣金（本地计算的值）-商家承担
				//double local_shop_real_amount=package_fee+product_org_total_fee-total_shopRate-discountCommission;
	    		
				double local_shop_real_amount=0;
				if(Constant.THIRD_DELIVERY==deliveryType){
					//本地门店实收（平台）=菜品合计+餐盒-商家承担-佣金	
					local_shop_real_amount=package_fee+product_org_total_fee-total_shopRate-discountCommission;
				}else{
					//本地门店实收（自配）=菜品合计+餐盒+配送费-商户承担-佣金
					local_shop_real_amount=package_fee+product_org_total_fee-total_shopRate-discountCommission+Double.valueOf(CommonUtil.fen2Yuan(send_fee));
				}
				
				//end at 2017-09-30 
	    		
	    		
				JSONObject paramsNew = new JSONObject();
				paramsNew.put("order_code", orderCode);
				paramsNew.put("tenentid", tenantId);
				paramsNew.put("discount_commission", CommonUtil.keepTwoDecimal(third_commission_amount));
				paramsNew.put("calculate_commission_amount", CommonUtil.keepTwoDecimal(discountCommission));
				paramsNew.put("local_shop_real_amount", CommonUtil.keepTwoDecimal(local_shop_real_amount));
				paramsNew.put("commission_rate", commissionRate);
				paramsNew.put("shop_real_amount", CommonUtil.keepTwoDecimal(Double.valueOf(CommonUtil.fen2Yuan(shop_real_amount))));
				paramsNew.put("platform_charge_amount", CommonUtil.keepTwoDecimal(Double.valueOf(CommonUtil.fen2Yuan(platform_charge_amount))));
				paramsNew.put("product_org_total_fee", CommonUtil.keepTwoDecimal(product_org_total_fee));
				orderDiscountCommissionReportService.updateYjxx4Order(paramsNew);
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.error(e.getMessage());
			}
	    }
	 
	 
	//at 2017-08-15
	 	@Override
		protected JSONObject createCustomerInfo() {
			String address=CommonUtil.replaceEvilChar(requestUser.optString("address").trim());
			String phone=requestUser.optString("phone");
			String sex=requestUser.optInt("gender") == 1 ? "man" : "woman";
			String name=CommonUtil.replaceEvilChar(requestUser.optString("name").trim());
			
			JSONObject customer=new JSONObject();
			customer.put("mobil", phone);
			customer.put("add_chanel", Constant.BAIDU_CHANNEL);
			customer.put("sex",sex);
			customer.put("name",name);

			JSONObject addresslist=new JSONObject();
			
			addresslist.put("sex",sex);
			addresslist.put("province", "");
			addresslist.put("city", "");
			addresslist.put("area", "");
			addresslist.put("address", address);
			addresslist.put("consignee_phone", phone);
			addresslist.put("consignee", name);
			addresslist.put("longitude", requestUser.optJSONObject("coord").optString("longitude") );
			addresslist.put("latitude",  requestUser.optJSONObject("coord").optString("latitude"));
			addresslist.put("baidu_location", "");
					
			
			customer.put("addresslist", addresslist);
			
			return customer;
		}
}
