package com.tzx.weixin.bo.imp;

import com.tzx.cc.base.CcExceptionUtil;
import com.tzx.cc.bo.OrderUpdateManagementService;
import com.tzx.cc.bo.dto.Data;
import com.tzx.crm.base.constant.Oper;
import com.tzx.crm.bo.BonusPointManageService;
import com.tzx.crm.bo.CardTransactionService;
import com.tzx.crm.bo.CouponsService;
import com.tzx.framework.common.exception.CcErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.exception.WxErrorCode;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.newcrm.bo.CardConsumeService;
import com.tzx.payment.common.constant.TradeStatusConstants;
import com.tzx.task.po.redis.dao.TaskRedisDao;
import com.tzx.wechat.po.springjdbc.dao.BaseInfoDao;
import com.tzx.weixin.bo.NewPaymentService;
import com.tzx.weixin.bo.NewWxDishService;
import com.tzx.weixin.common.constant.Constant;
import com.tzx.weixin.common.model.Gps;
import com.tzx.weixin.common.model.resp.TemplateMessage;
import com.tzx.weixin.common.model.resp.TemplateMessageItem;
import com.tzx.weixin.common.util.*;
import com.tzx.weixin.po.springjdbc.dao.*;
import common.Logger;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Service(NewWxDishService.NAME)
public class NewWxDishServiceImp implements NewWxDishService {
	@Resource(name = "genericDaoImpl")
	private GenericDao dao;

	@Resource(name = OrderUpdateManagementService.NAME)
	private OrderUpdateManagementService orderManagerService;

	@Resource(name = NewPaymentService.NAME)
	private NewPaymentService newPaymentService;

	@Resource(name = CardTransactionService.NAME)
	private CardTransactionService cardTransactionService;
	@Resource(name = CouponsService.NAME)
	private CouponsService couponsService;

	private Logger logger = Logger.getLogger(NewWxDishServiceImp.class);

	@Resource(name=NewWxMemberDao.NAME)
	private NewWxMemberDao newWxMemberDao;

	@Resource(name=NewWxOrderDao.NAME)
	private NewWxOrderDao newWxOrderDao;
	
	@Resource(name = TaskRedisDao.NAME)
	private TaskRedisDao		taskRedisDao;
	
	@Resource(name=NewWxPaymentWayDao.NAME)
	private NewWxPaymentWayDao newWxPaymentWayDao;
	
	@Resource(name=NewWxSystemParameterDao.NAME)
	private NewWxSystemParameterDao newWxSystemParameterDao;
	
	@Resource(name=BonusPointManageService.NAME)
	private BonusPointManageService bonusPointManageService;
	
	@Resource(name = NewWxCouponsTicketDao.NAME)
	private NewWxCouponsTicketDao newWxCouponsTicketDao;
	
	@Resource(name=TakeoutMenuMealsDao.NAME)
	private TakeoutMenuMealsDao takeoutMenuMealsDao;
	@Resource(name=CardConsumeService.NAME)
	private CardConsumeService cardConsumeService;
	
    @Autowired
    private BaseInfoDao baseInfoDao;
	
	@Override
	public JSONObject getDishClassTwoLevel(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		JSONObject result = new JSONObject();
		StringBuilder sb = new StringBuilder();
		sb.append("select id,itemclass_code,itemclass_name,phonetic_code,five_code,father_id from hq_item_class where chanel='WX02' and valid_state='1' ORDER BY id,father_id DESC");
		List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
		List<JSONObject> list2 = new ArrayList<JSONObject>();
		Map<Integer, List<JSONObject>> map2 = new HashMap<Integer, List<JSONObject>>();
		for (JSONObject joclass : list) {
			int father_id = joclass.optInt("father_id");
			int c_id = joclass.optInt("id");
			if (father_id == 0) {
				map2.put(c_id, new ArrayList<JSONObject>());

			} else {
				if (map2.containsKey(father_id)) {
					map2.get(father_id).add(joclass);
				}
			}
		}
		for (JSONObject joclass : list) {
			int father_id = joclass.optInt("father_id");
			int c_id = joclass.optInt("id");
			if (father_id == 0) {
				map2.put(c_id, new ArrayList<JSONObject>());
				if (map2.containsKey(c_id)) {
					joclass.put("children", map2.get(c_id));
				} else {
					joclass.put("children", new ArrayList<JSONObject>());
				}

				list2.add(joclass);
			}
		}
		result.put("rows", list2);
		return result;
	}

	@Override
	public JSONObject getDishClassOneLevel(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		JSONObject result = new JSONObject();// organ_id
//		StringBuilder sb = new StringBuilder();
		// sb.append("select id,itemclass_code,itemclass_name,phonetic_code,five_code,father_id from hq_item_class where chanel='WX02' and valid_state='1' and father_id<>0 ORDER BY id,father_id DESC");
//		sb.append("select hic1.id,hic1.itemclass_code,hic1.itemclass_name,hic1.phonetic_code,hic1.five_code,hic1.father_id from hq_item_class hic1 where hic1.id in (");
//		sb.append(" select hic.id from hq_item_class hic ");
//		sb.append(" LEFT JOIN hq_item_menu_class himc on himc.class=hic.id ");
//		sb.append(" LEFT JOIN hq_item_menu_details himd on himc.details_id=himd.id ");
//		sb.append(" LEFT JOIN hq_item_menu_organ himo on himd.item_menu_id=himo.item_menu_id");
//		sb.append(" where himc.chanel='WX02' and hic.valid_state='1' and  hic.father_id<>0 and himo.store_id="
//				+ jb.optString("organ_id") + " GROUP BY hic.id ORDER BY hic.id");
//		sb.append(")");
		int store_id = jb.optInt("organ_id");
		List<JSONObject> list = newWxOrderDao.getDishClassOneLevel(tenantId, store_id);

		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject getDishByClassId(String tenantId, JSONObject jb)
			throws SystemException, Exception {

		int organ_id = jb.optInt("organ_id");
		int class_id = jb.optInt("class_id");
		String openid = jb.optString("openid");
		StringBuilder sql = new StringBuilder();
		int limit = Constant.SEARCHPAGE;
		limit = 200;
		int pageNum = (jb.optInt("page") == 0) ? 1 : jb.optInt("page");
		long total = 0l;
//		String nowDateYYDDMM = DateUtil.getNowDateYYDDMM();
//		if (class_id > 0) {
//			sql.append("select t.id,t.item_id as item_id,m.id as unit_id,m.unit_name,t.item_menu_id,h.photo1,h.photo2,");
//			//返回销量字段
//			sql.append("coalesce(itemc.number,0) sales,");
//			
////			sql.append("(select hicd.is_itemgroup from hq_item_combo_details hicd where hicd.iitem_id=t.item_id  and hicd.is_itemgroup='Y'),");
//			sql.append("cl.item_name,h.spicy,h.item_class,m.standard_price,coalesce((select max(price) from hq_item_pricesystem hp");
//			sql.append(" where  cast(hp.price_system as varchar) =(select o.price_system from organ o where o.id="+ organ_id);
//			sql.append(") and hp.chanel='WX02' and hp.item_unit_id=m.id),m.standard_price) as price,h.is_combo,");
//			sql.append("(select count(*) from hq_item_unit hiu where t.item_id = hiu.item_id and hiu.valid_state='1') as unit_count,");
//			sql.append(" (select count(*) from wx_dish_good d where d.item_info_id=t.item_id) as zanall,");
//			sql.append(" (select count(*) from wx_dish_good a where a.item_info_id=t.item_id and a.openid='"+openid +"') as zan,");
//			sql.append("(select count(ps1.*) from pos_soldout ps1 ");
//			sql.append("where ps1.store_id="+organ_id+" and ps1.item_id=t.item_id and (ps1.soldout_type='1'");
//			sql.append(" or (ps1.soldout_type='0' and ps1.report_date='"+nowDateYYDDMM+"'))) as guqing");
//			sql.append(" from hq_item_menu_details t  ");
//			sql.append(" left join hq_item_menu_organ g on t.item_menu_id = g.item_menu_id");
//			sql.append(" LEFT JOIN hq_item_menu hm on g.item_menu_id = hm.id");
//			sql.append(" left join hq_item_unit m on t.item_id = m.item_id and m.valid_state='1'");
//			sql.append(" left join hq_item_info h on t.item_id = h.id");
//			sql.append(" left join hq_item_menu_class cl on cl.details_id = t.id ");
//			//增加销量查询
////			sql.append(" left join (select count(b.id) number,b.item_id from cc_order_list a left join cc_order_item b on a.order_code=b.order_code where to_char(a.single_time,'yyyy-mm')=to_char(current_date,'yyyy-mm') and b.store_id= "+ organ_id + " and a.chanel='WX02' group by b.item_id) itemc on t.item_id=itemc.item_id");
//			sql.append(" left join (select sum(sales) number,item_id from (select sum(sales_num) sales,item_id from hq_daycount_item where TO_CHAR(day_count,'yyyy-mm')=to_char(current_date,'yyyy-mm') and store_id = " + organ_id + " group by item_id union all select sum(item_count) number,item_id from pos_bill_item where TO_CHAR(report_date,'yyyy-mm')=to_char(current_date,'yyyy-mm') and store_id = " + organ_id + " group by item_id ) sales_tab group by item_id) itemc on t.item_id=itemc.item_id ");
//			
//			sql.append(" where h.valid_state='1' and  g.store_id = "+ organ_id+" and t.valid_state='1'");
//			sql.append(" and hm.valid_state='1' and m.is_default = 'Y' and cl.chanel='WX02' and cl.class='"+class_id+"'");
//			sql.append(" and g.id in (select max(himo1.id) from hq_item_menu_organ himo1 GROUP BY himo1.item_menu_id,himo1.store_id)");
//			
//			sql.append("select t.id,t.item_id as item_id,m.id as unit_id,m.unit_name,t.item_menu_id,h.photo1,h.photo2,cl.item_name,h.spicy,h.item_class,m.standard_price,coalesce((select max(price) from hq_item_pricesystem hp where  cast(hp.price_system as varchar) =(select o.price_system from organ o where o.id="
//					+ organ_id
//					+ ") and hp.chanel='WX02' and hp.item_unit_id=m.id),m.standard_price) as price,");
//			sql.append(" (select count(*) from wx_dish_good d where d.item_info_id=t.item_id) as zanall,");
//			sql.append(" (select count(*) from wx_dish_good a where a.item_info_id=t.item_id and a.openid='"
//					+ openid + "') as zan,");
//			sql.append("(select count(ps1.*) from pos_soldout ps1 ");
//			sql.append("where ps1.store_id="+organ_id+" and ps1.item_id=t.item_id and (ps1.soldout_type='1' or (ps1.soldout_type='0' and ps1.report_date='"+nowDateYYDDMM+"'))) as guqing");
//			sql.append(" from hq_item_menu_details t  ");
//			sql.append(" left join hq_item_menu_organ g on t.item_menu_id = g.item_menu_id");
//			sql.append(" LEFT JOIN hq_item_menu hm on g.item_menu_id = hm.id");
//			sql.append(" left join hq_item_unit m on t.item_id = m.item_id and m.valid_state='1'");
//			sql.append(" left join hq_item_info h on t.item_id = h.id");
//			sql.append(" left join hq_item_menu_class cl on cl.details_id = t.id ");
//			sql.append(" where h.valid_state='1' and  g.store_id = "
//					+ organ_id
//					+ " and t.valid_state='1' and hm.valid_state='1' and m.is_default = 'Y' and cl.chanel='WX02' and cl.class='"
//					+ class_id + "'");
//			sql.append(" and g.id in (select max(himo1.id) from hq_item_menu_organ himo1 GROUP BY himo1.item_menu_id,himo1.store_id)");
//			total = this.dao.countSql(tenantId, sql.toString());
//			sql.append("  order by t.id");
//			sql.append("  limit " + limit + " offset "
//					+ (limit * (pageNum - 1)));
//
//		} else {
//			sql.append("select t.id,t.item_id as item_id,m.id as unit_id,m.unit_name,t.item_menu_id,");
//			//返回销量字段
//			sql.append("coalesce(itemc.number,0) sales,");
//			
////			sql.append("(select hicd.is_itemgroup from hq_item_combo_details hicd where hicd.iitem_id=t.item_id  and hicd.is_itemgroup='Y'),");
//			sql.append("h.photo1,h.photo2,cl.item_name,h.spicy,h.item_class,coalesce((select max(price) from hq_item_pricesystem hp where  cast(hp.price_system as varchar) =(select o.price_system from organ o where o.id="
//					+ organ_id
//					+ ") and hp.chanel='WX02' and hp.item_unit_id=m.id),m.standard_price) as price,h.is_combo,");
//			sql.append("(select count(*) from hq_item_unit hiu where t.item_id = hiu.item_id and hiu.valid_state='1') as unit_count,");
//			sql.append(" (select count(*) from wx_dish_good d where d.item_info_id=t.item_id) as zanall,");
//			sql.append(" (select count(*) from wx_dish_good a where a.item_info_id=t.item_id and a.openid='"
//					+ openid + "') as zan,");
//			sql.append("(select count(ps1.*) from pos_soldout ps1 ");
//			sql.append("where ps1.store_id="+organ_id+" and ps1.item_id=t.id and (ps1.soldout_type='1' or (ps1.soldout_type='0' and ps1.report_date='"+nowDateYYDDMM+"'))) as guqing");
//			sql.append(" from hq_item_menu_details t  ");
//			sql.append(" left join hq_item_menu_organ g on t.item_menu_id = g.item_menu_id");
//			sql.append(" left join hq_item_unit m on t.item_id = m.item_id and m.valid_state='1'");
//			sql.append(" left join hq_item_info h on t.item_id = h.id");
//			sql.append(" left join hq_item_menu_class cl on cl.details_id = t.item_id ");
//			//增加销量查询
////			sql.append(" left join (select count(b.id) number,b.item_id from cc_order_list a left join cc_order_item b on a.order_code=b.order_code where to_char(a.single_time,'yyyy-mm')=to_char(current_date,'yyyy-mm') and b.store_id= "+ organ_id + " and a.chanel='WX02' group by b.item_id) itemc on t.item_id=itemc.item_id");
//			sql.append(" left join (select sum(sales) number,item_id from (select sum(sales_num) sales,item_id from hq_daycount_item where TO_CHAR(day_count,'yyyy-mm')=to_char(current_date,'yyyy-mm') and store_id = " + organ_id + " group by item_id union all select sum(item_count) number,item_id from pos_bill_item where TO_CHAR(report_date,'yyyy-mm')=to_char(current_date,'yyyy-mm') and store_id = " + organ_id + " group by item_id ) sales_tab group by item_id) itemc on t.item_id=itemc.item_id ");
//			
//			sql.append(" where h.valid_state='1' and  g.store_id = " + organ_id);
//			sql.append(" and g.item_menu_id in(select hm.id from hq_item_menu hm where hm.id in(select ho.item_menu_id from hq_item_menu_organ ho where ho.store_id="
//					+ organ_id + ") and hm.valid_state='1') ");
//			sql.append(" and m.is_default = 'Y' and cl.chanel='WX02' and t.valid_state='1'");
//			sql.append(" and g.id in (select max(himo1.id) from hq_item_menu_organ himo1 GROUP BY himo1.item_menu_id,himo1.store_id)");
//			sql.append(" order by h.item_class");
//
//			total = this.dao.countSql(tenantId, sql.toString());
//			sql.append("  limit " + limit + " offset "
//					+ (limit * (pageNum - 1)));
//		}

		List<JSONObject> list = newWxOrderDao.getDishByClassId(tenantId, organ_id, class_id, openid,null);
		for (JSONObject dish : list) {
			double price = dish.optDouble("price", 0.0);

			dish.put("priceFormat", Scm.pround(price));
		}

		JSONObject result = new JSONObject();
		result.put("page", pageNum);
		result.put("total", total);
		result.put("rows", list);

		return result;
	}

	@Override
	public JSONObject placeAnOrder(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		// 下单接口
		Data data = new Data();
		JSONObject result = new JSONObject();
		StringBuilder sb = new StringBuilder();
		String orderCode = "";
		String type = jb.optString("type");
		// 是否是先付
		boolean isPrepayment = false;
		
		//改： 加品牌 查询系统参数     ---wzx
		JSONObject wechat_payment_type = baseInfoDao.querySystemParameterByParaCode(tenantId, jb.optString("store_id","0"), "wechat_payment_type");
		if(wechat_payment_type==null){
			result.put("errorMsg", "下单失败");
			result.put("errCode", "2");
			return result;
		}
		//--wzx
		/*sb.append("select * from sys_parameter where para_code='wechat_payment_type' and store_id=0");
		List<JSONObject> query4Json6 = dao.query4Json(tenantId, sb.toString());
		if (query4Json6.size() < 1) {
			result.put("errorMsg", "下单失败");
			result.put("errCode", "2");
			return result;
		}
		JSONObject wechat_payment_type = query4Json6.get(0);*/
		String para_value = wechat_payment_type.optString("para_value");
		if (para_value.equals("0")) {
			// 后付
			isPrepayment = false;
		} else if (para_value.equals("1")) {
			// 先付
			isPrepayment = true;
		}
		sb.delete(0, sb.length());
		//整理从前台传来的下单的相关数据
		JSONObject order_list = new JSONObject();
		JSONObject order_list1 = new JSONObject();
		JSONArray order_item = new JSONArray();
		JSONArray order_item1 = new JSONArray();
		if (!"null".equals(jb.optString("order_list"))) {
			// data.set
			order_list = JSONObject.fromObject(jb.optString("order_list"));
		}
		JSONArray newOrderItem3 = new JSONArray();
		if(!"null".equals(jb.optJSONArray("order_item"))){
			newOrderItem3=jb.optJSONArray("order_item");
		}
		//判断下单的金额是否能对上
		double allPrice1=0.0;
		for(int i=0;i<newOrderItem3.size();i++){
			JSONObject order_item3= (JSONObject) newOrderItem3.get(i);
			int number = order_item3.optInt("number");
			double price = order_item3.optDouble("price",0.0);
			allPrice1+=(number*price);
		}
		double product_org_total_fee=order_list.optDouble("total_money",0);
		if(type.equals("WM10")){
			//有餐盒费
			double canhefei = order_list.optDouble("canhefei",0.0);
//			double meal_costs = order_list.optDouble("meal_costs");
			allPrice1+=canhefei;
//			allPrice1+=meal_costs;
			product_org_total_fee-=canhefei;
		}
		NumberFormat nf = NumberFormat.getNumberInstance();
		//保留两位小数
		nf.setMaximumFractionDigits(2);
		String format1 = nf.format(allPrice1);
		format1=format1.replace(",", "");
		float allPrice3 = Float.parseFloat(format1);
		double allPrice2 = order_list.optDouble("total_money",0.0);
		float allPrice4 = Float.parseFloat(nf.format(allPrice2).replace(",", ""));
		if(allPrice3!= allPrice4){
			result.put("errorMsg", "请重新下单");
			result.put("errCode", "3");
			result.put("allPrice2", allPrice2);
			result.put("allPrice1", allPrice1);
			return result;
		}
		// 判断是否是粉丝
		String openid = jb.optString("openid");
		if ("".equals(openid) || "null".equals(openid)) {
			result.put("errorMsg", "请关注公众号在下单");
			result.put("errCode", "1");
			return result;
		}
		StringBuilder sqlOpenId = new StringBuilder(
				"select * from wx_member where openid='");
		sqlOpenId.append(openid + "' and subscribe='t'");
		List<JSONObject> memberList = dao.query4Json(tenantId,
				sqlOpenId.toString());
		if (memberList.size() < 1) {
			result.put("errorMsg", "请关注公众号在下单");
			result.put("errCode", "1");
			return result;
		}
		JSONObject member = memberList.get(0);
		StringBuilder sb3 = new StringBuilder("select * from organ where id=");
		sb3.append(jb.optString("organ_id"));
		List<JSONObject> query4Json4 = dao.query4Json(tenantId, sb3.toString());
		if (query4Json4.size() < 1) {
			result.put("errorMsg", "请返回上一级选择门店");
			result.put("errCode", "0");
			return result;
		}
		JSONObject jsonObject2 = query4Json4.get(0);
		int is_wechatdddc = jsonObject2.optInt("is_wechatdddc");
		if(is_wechatdddc==0&&type.equals("WX02")){
			result.put("errorMsg", "该门店暂不支持微信店内点餐");
			result.put("errCode", "0");
			return result;
		}
		// logger.info(jb);
		// 定位
		if(type.equals("WX02")){
			if ("null".equals(jb.optString("lon"))
					|| "".equals(jb.optString("lon"))
					|| "null".equals(jb.optString("lot"))
					|| "".equals(jb.optString("lot"))) {
				if(!isPrepayment){
					result.put("errorMsg", "正在定位，请再次点击确认下单");
					result.put("errCode", "0");
					return result;
				}
			}else{
				if ("null".equals(jb.optString("organ_id"))) {
					result.put("errorMsg", "请选择门店");
					result.put("errCode", "0");
					return result;
				}
				Gps gps = MapUtil.gcj02_To_Bd09(
						Double.parseDouble(jb.optString("lot")),
						Double.parseDouble(jb.optString("lon")));
				Double bd_lat = gps.getWgLat();
				Double bd_lon = gps.getWgLon();
				// 判断距离longitude,latitude
				String temp_lon = jsonObject2.optString("longitude");
				String temp_lat = jsonObject2.optString("latitude");
				if (MyUtil.checkEmpty(temp_lon) && MyUtil.checkEmpty(temp_lat)
						&& !"null".equals(temp_lon) && !"null".equals(temp_lat)) {
					// lon,lat
					double tar_lon = jsonObject2.optDouble("longitude", 0.0);
					double tar_lat = jsonObject2.optDouble("latitude", 0.0);
					// 获取距离
					double distance = MapUtil.getDistance(bd_lon, bd_lat, tar_lon,
							tar_lat);
					// 两千米就不能下单
					if (distance > 20000) {
						result.put("errorMsg", "距离太远不能下单!");
						result.put("errCode", "0");
						return result;
					}
				} else {
					if(!isPrepayment){
						result.put("errorMsg", "距离太远不能下单");
						result.put("errCode", "0");
						return result;
					}
				}
			}
		}
		
		// 判断座位号是否正确
		String organId = jb.optString("organ_id");
		boolean isTableCode = false;//默认是不需要座位的
		String tableCode = jb.optString("tableCode");
		//是否设置座位号
		if(type.equals("WX02")){
			/*isTableCode= newWxOrderDao.queryIsSetTableCode(tenantId);*/
			if ("null".equals(organId)) {
				result.put("errorMsg", "请返回后选择门店");
				result.put("errCode", "0");
				return result;
			}
			
			//改： 加品牌 查询系统参数     ---wzx
			JSONObject wechat_table_code = baseInfoDao.querySystemParameterByParaCode(tenantId, organId, "wechat_table_code");
			if(wechat_table_code!=null){
				if(wechat_table_code.optInt("para_value")==1){
					isTableCode=true;
				}
			}
			//--wzx
			
			if(isTableCode){
				//需要座位号
				if ("null".equals(tableCode)) {
					result.put("errorMsg", "请先扫码，再下单");
					result.put("errCode", "0");
					return result;
				}
				// 查询出座位号是否正确
				String sql2 = "select table_code,table_name from tables_info where organ_id="
						+ organId
						+ " and valid_state='1' and table_code='"
						+ tableCode
						+ "'";
				List<JSONObject> query4Json2 = dao.query4Json(tenantId, sql2);
				if (query4Json2.size() < 1) {
					result.put("errorMsg", "座位号错误" + tableCode + "，请扫描桌位上的二维码");
					result.put("errCode", "0");
					return result;
				}
				if (!isPrepayment) {
					StringBuilder sb4 = new StringBuilder(
							"select * from cc_order_list where 1=1 ");
					sb4.append("and order_state not in('08','10') and table_code='"
							+ tableCode + "' ");
					sb4.append("and store_id=" + organId + " ");
					List<JSONObject> query4Json = dao.query4Json(tenantId,
							sb4.toString());
					if (query4Json.size() > 0) {
						//判断该桌位是否有人占用了
						sb.delete(0, sb.length());
						sb.append("select cc.id from crm_customer_info cc LEFT JOIN wx_member wx on cc.mobil=wx.mobile_num where openid='"
								+ openid + "'");
						List<JSONObject> query4Json3 = dao.query4Json(tenantId,
								sb.toString());
						if (query4Json3.size() < 1) {
							
							result.put("errorMsg", "不是会员");
							result.put("errCode", "0");
							return result;
						} else if (query4Json3.get(0).optInt("id") != query4Json.get(0)
								.optInt("customer_id")) {
							result.put("errorMsg", "本桌位已经有人占用了，请另外换一个桌位再次扫码下单");
							result.put("errCode", "0");
							return result;
						}
					}
				}
				JSONObject jsonObject = query4Json2.get(0);
				String table_name = jsonObject.optString("table_name");
				order_list.put("table_name", table_name);
			}
		}
		
		sb.delete(0, sb.length());
		//判断下单时间是否有效
		sb.append("select aa.* from (select (select * from to_char(now(),'hh24:mi')) as time1,lineup_starttime,lineup_endtime");
		sb.append(" from hq_lineup_time_org");
		sb.append(" where store_id=" + organId + " and valid_state='1' ) aa");
		sb.append(" where aa.time1 BETWEEN aa.lineup_starttime and aa.lineup_endtime");
		List<JSONObject> query4Json7 = dao.query4Json(tenantId, sb.toString());
		if (query4Json7.size() < 1&&type.equals("WX02")) {
			result.put("errorMsg", "用餐时间不在营业时间之内！");
			result.put("errCode", "0");
			return result;
		}
		String total_money = order_list.optString("total_money");
		if ("null".equals(total_money) || total_money.equals("0.00")
				|| total_money.equals("0.0") || total_money.equals("0")) {
			result.put("errorMsg", "亲，下单时金额要大于0哦");
			result.put("errCode", "0");
			return result;
		}
		// 会员卡
		String memberCard = jb.optString("memberCard");
		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(jb);
		// 关键信息 order_list{order_type：'',order_item:[]}

		// 下单的时间
		String hq_service_time = DateUtil.format(new Timestamp(System
				.currentTimeMillis()));
		if (jb.optString("dispatch_time").equalsIgnoreCase("")
				|| jb.optString("dispatch_time").equalsIgnoreCase("null")) {
			jb.put("dispatch_time", hq_service_time);
			jb.put("receive_time_dispatch", hq_service_time);
		}
		List<JSONObject> afterAddOrder = new ArrayList<JSONObject>();
		StringBuilder sbl = new StringBuilder();
		if (!isPrepayment&&isTableCode&&type.equals("WX02")) {
			// 后付可以加菜，先付是另一个订单
			// 后续添加菜时的改变
			sbl.append("select * from cc_order_list where 1=1 ");
			sbl.append("and table_code='" + tableCode + "' ");
			sbl.append("and store_id=" + organId + " ");
			sbl.append("and order_phone = (select cc.mobil from crm_customer_info cc LEFT JOIN wx_member wx on cc.mobil=wx.mobile_num where wx.openid='"
					+ openid + "') ");
			// 在3个小时以内
			sbl.append("and payment_state!='03'");
			sbl.append("and order_state not in ('08','05','10')");
			sbl.append("and single_time > (select now() -interval '3 H')");
			afterAddOrder = dao.query4Json(tenantId, sbl.toString());
		}

		if (afterAddOrder.size() > 0&&type.equals("WX02")) {
			// 加菜的要将之前下的所有的菜品都添加进去，同时将以前的服务费给减掉
			// meal_costs
			JSONObject oldOrder = afterAddOrder.get(0);
			double meal_costs = oldOrder.optDouble("meal_costs", 0);
			double old_total_money = oldOrder.optDouble("total_money", 0);
			if (old_total_money != 0) {
				old_total_money = old_total_money - meal_costs;
			}
			order_list.put("total_money", order_list.optDouble("total_money")
					+ old_total_money);

			// order_list1.put("order_code", oldOrder.optString("order_code"));
			orderCode = oldOrder.optString("order_code");
			order_list.put("order_code", oldOrder.optString("order_code"));
			sbl.delete(0, sbl.length());
			sbl.append("select * from cc_order_item where order_code='"
					+ oldOrder.optString("order_code") + "'");
			List<JSONObject> old_order_item = dao.query4Json(tenantId,
					sbl.toString());
			JSONArray newOrderItem = jb.optJSONArray("order_item");
			JSONArray newOrderItemTaste = jb.optJSONArray("order_item_taste");
			JSONArray newOrderItemDetails = jb.optJSONArray("order_item_details");
			int group_index = 1;
			for (int i = 0; i < old_order_item.size(); i++) {
				order_item1.add(old_order_item.get(i));
				if (group_index <= old_order_item.get(i).optInt("group_index")) {
					group_index = old_order_item.get(i).optInt("group_index");
				}
			}
			JSONArray newOrderItem1 = new JSONArray();
			JSONArray newOrderItemTaste1 = new JSONArray();
			JSONArray newOrderItemDetails1 = new JSONArray();
			for (int i = 0; i < newOrderItem.size(); i++) {
				group_index++;
				JSONObject object = (JSONObject) newOrderItem.get(i);
				int item_id = object.optInt("item_id");
				int unit_id = object.optInt("unit_id");
				int iscombo = object.optInt("iscombo");
				for (int j = 0; j < newOrderItemTaste.size(); j++) {
					JSONObject object2 = (JSONObject) newOrderItemTaste.get(j);
					
//					int item_id1 = object2.optInt("item_id");
					String orderItemId = object2.optString("orderItemId");
					String itemid_unitid=item_id+"_"+unit_id;
					String[] split = orderItemId.split("_");
					String itemid_unitid1=split[0]+"_"+split[1];
					if(newOrderItemDetails.size()>0&&iscombo==1){
						//是套餐
						int optInt = object.optInt("k");
						itemid_unitid+="_"+optInt;
						if(itemid_unitid.equals(orderItemId)){
							object2.put("group_index", group_index);
							object2.put("is_add_dish", "1");
							newOrderItemTaste1.add(object2);
						}
					}else{
						if(itemid_unitid.equals(orderItemId)){
							object2.put("group_index", group_index);
							object2.put("is_add_dish", "1");
							newOrderItemTaste1.add(object2);
						}
					}
					
				}
				for (int j = 0; j < newOrderItemDetails.size(); j++) {
					JSONObject object2 = (JSONObject) newOrderItemDetails.get(j);
					String orderItemId = object2.optString("orderItemId");
					String itemid_unitid=item_id+"_"+unit_id;
					if(itemid_unitid.equals(orderItemId)){
						object2.put("group_index", group_index);
						object2.put("is_add_dish", "1");
						newOrderItemDetails1.add(object2);
					}
				}
				object.put("group_index", group_index);
				object.put("is_add_dish", "1");
				order_item1.add(object);
				newOrderItem1.add(object);
			}
			jb.put("order_item", newOrderItem1);
			jb.put("order_item_taste", newOrderItemTaste1);
			jb.put("order_item_details", newOrderItemDetails1);
		}

		// 服务费用的查询处理（不管是加菜还是新下都应该查询服务费）
		StringBuilder addpriceSql = new StringBuilder();
		if (isTableCode&&order_list.optString("order_type").equals("DN03")) {
			// 到店点餐的服务费信息处理
			addpriceSql = new StringBuilder(
					"select fee.* from hq_service_fee_type fee");
			addpriceSql
					.append(" left join tables_info info on fee.id=info.fwfz_id");
			addpriceSql
					.append(" where info.table_code='"
							+ order_list.optString("table_code")
							+ "' and info.organ_id="
							+ order_list.optString("store_id"));
		}
		if (addpriceSql.length() > 0) {
			List<JSONObject> addPriceList = dao.query4Json(tenantId,
					addpriceSql.toString());
			if (addPriceList != null && addPriceList.size() > 0) {
				JSONObject bean = addPriceList.get(0);
				// 根据类型获取配送费用为多少
				double additional = 0;
				if ("GD01".equals(bean.optString("taken_mode"))) {
					// 固定金额
					additional = bean.optDouble("guding_jj");
				} else if ("FS03".equals(bean.optString("taken_mode"))) {
					// 按份数
					JSONArray order_item_arr = order_item1;
					int dishNumber = 0;
					for (int i = 0; i < order_item_arr.size(); i++) {
						dishNumber += order_item_arr.getJSONObject(i).optInt(
								"number");
					}
					additional = bean.optDouble("guding_jj") * dishNumber;
				} else {
					// 账单比例
					additional = order_list.optDouble("total_money")
							* bean.optDouble("fwfl") * 0.01;
				}
				order_list.put("meal_costs", additional);
				order_list.put("total_money",
						order_list.optDouble("total_money") + additional);
				order_list.put("actual_pay",
						order_list.optDouble("total_money"));
				order_list.put("shop_fee",
						order_list.optDouble("total_money"));
				if (order_list.optString("order_type").equals("WM02")) {
					order_list.put("service_id", bean.optString("service_id"));
					order_list.put("meals_id", bean.optInt("id"));
				} else if (order_list.optString("order_type").equals("DN03")) {
					 order_list.put("service_id", bean.optInt("id"));
				}
				// jb.put("order_list", order_list);
			}
		}
		//配送费
		if(type.equals("WM10")){
			int storeid = Integer.parseInt(organId);
			JSONObject newPostagePakingFee = takeoutMenuMealsDao.newPostagePakingFee(tenantId, storeid, "WM10");
			if(null!=newPostagePakingFee){
//				order_list.put("service_id", newPostagePakingFee.optString("service_id"));
				order_list.put("meals_id", newPostagePakingFee.optInt("id"));
				double additional = newPostagePakingFee.optDouble("money",0.0);
				order_list.put("meal_costs", additional);
				order_list.put("total_money",
						order_list.optDouble("total_money",0.0) + additional);
				order_list.put("actual_pay",
						order_list.optDouble("total_money",0.0));
				order_list.put("shop_fee",
						order_list.optDouble("total_money",0.0));
			}else{
				order_list.put("meal_costs", 0);
			}
			//送达时间
		    int room_times = jb.optInt("room_times");
		    room_times=(room_times==0)?30:room_times;
		    Date date = new Date();
		    long time = date.getTime();
		    time=time+(room_times*1000*60);
		    Date date2=new Date(time);
	    	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        String send_time = dateFormat.format(date2);
	        order_list.put("send_time", send_time);
	        jb.put("order_list", order_list);
		}
		String checkTable = jb.optString("checkTable");
		if(checkTable.equals("1")&&isTableCode&&type.equals("WX02")){
			JSONArray order_item_arr = jb.optJSONArray("order_item");
			//必点菜和禁点菜
			List<JSONObject> queryMustDishAndNotDish = newWxOrderDao.queryMustDishAndNotDish(tenantId, tableCode, organId);
			if(queryMustDishAndNotDish.size()>0){
				for(int j=0;j<queryMustDishAndNotDish.size();j++){
					JSONObject dish = queryMustDishAndNotDish.get(j);
					int z=0;
					for (int i = 0; i < order_item_arr.size(); i++) {
						JSONObject item = (JSONObject) order_item_arr.get(i);
						if(dish.optString("dishes_sign").equals("0")){
							//禁点菜
							if(dish.optString("item_id").equals(item.optString("item_id"))){
								result.put("errorMsg", "亲，对不起，由于店内设定，"+dish.optString("item_name")+"在本桌位不能点！");
								result.put("errCode", "0");
								return result;
							}else{
								z++;
							}
						}else if(dish.optString("dishes_sign").equals("1")){
							if(item.optString("item_id").equals(dish.optString("item_id"))){
								z++;
							}
						}
					}
					if(z==0){
						result.put("errorMsg", "亲，对不起，由于店内设定，"+dish.optString("item_name")+"在本桌位是必点菜点！");
						result.put("errCode", "0");
						return result;
					}
				}
			}
		}
		// 订单 点菜信息
		if (!"null".equals(jb.optString("order_item"))) {
			JSONArray order_item_arr = jb.optJSONArray("order_item");
			JSONArray order_item_arr1 = new JSONArray();
			for (int i = 0; i < order_item_arr.size(); i++) {
				JSONObject object = (JSONObject) order_item_arr.get(i);
				object.put("real_amount", object.optDouble("price",0.0));
				order_item_arr1.add(object);
				CcExceptionUtil.validContains(order_item_arr.getJSONObject(i),
						"group_index", CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
				CcExceptionUtil.validContains(order_item_arr.getJSONObject(i),
						"item_id", CcErrorCode.ITEM_ID_EMPTY_ERROE);
				CcExceptionUtil.validContains(order_item_arr.getJSONObject(i),
						"number", CcErrorCode.NUMBER_EMPTY_ERROE);
				CcExceptionUtil.validContains(order_item_arr.getJSONObject(i),
						"price", CcErrorCode.PRICE_EMPTY_ERROE);
				Integer item_id = order_item_arr.getJSONObject(i).optInt(
						"item_id");
				String item_name = order_item_arr.getJSONObject(i).optString("item_name");
				sb.setLength(0);
				String nowDateYYDDMM = DateUtil.getNowDateYYDDMM();
				sb.append("select count(ps1.*) as guqing from pos_soldout ps1 ");
				sb.append("where ps1.store_id="+organId+" and ps1.item_id="+item_id+" and (ps1.soldout_type='1' or (ps1.soldout_type='0' and ps1.report_date='"+nowDateYYDDMM+"'))");
				List<JSONObject> query4Json = dao.query4Json(tenantId, sb.toString());
				if(query4Json.size()>0){
					JSONObject jsonObject3 = query4Json.get(0);
					if(jsonObject3.optInt("guqing")>0){
						result.put("errorMsg", "对不起，您购买的"+item_name+"商品已经沽请。请另外选择商品下单！");
						result.put("errCode", "0");
						return result;
					}
				}
				jb.put("order_item", order_item_arr1);
//				StringBuilder sb2 = new StringBuilder(
//						"select  ZZ.item_id,ZZ.combo_item_id,ZZ.item_unit_id,count(ZZ.*) as num,0.1 as price ");
//				sb2.append("from (select Z.item_id,(case Z.is_itemgroup when 'Y' then Z.group_item_id else Z.details_id end) as combo_item_id, ");
//				sb2.append("(case Z.is_itemgroup when 'Y' then Z.hg_unit_id else Z.item_unit_id end) as item_unit_id ");
//				sb2.append("from (select A.item_id,hd.is_itemgroup,hd.details_id,hg.item_id as group_item_id,hd.item_unit_id,hg.item_unit_id as hg_unit_id  ");
//				sb2.append("from (select hi.id as item_id from hq_item_info hi where hi.is_combo='Y' and hi.id in("
//						+ item_id + ")) as A ");
//				sb2.append("LEFT JOIN hq_item_combo_details hd on A.item_id = hd.iitem_id ");
//				sb2.append("LEFT JOIN hq_item_group_details hg on hd.details_id = hg.item_group_id and hd.is_itemgroup='Y') as Z ");
//				sb2.append("ORDER BY Z.item_id) as  ZZ ");
//				sb2.append("GROUP BY ZZ.item_id,ZZ.combo_item_id,ZZ.item_unit_id ");
//				StringBuilder sb2 = new StringBuilder();
//				sb2.append("select  ZZ.item_id,ZZ.combo_item_id,ZZ.item_unit_id,count(ZZ.*) as num,0.1 as price,(case ZZ.is_itemgroup when 'Y' then 1 else sum(ZZ.combo_num) end) as combo_num ");
//				sb2.append("from (select Z.item_id,(case Z.is_itemgroup when 'Y' then Z.group_item_id else Z.details_id end) as combo_item_id, ");
//				sb2.append("(case Z.is_itemgroup when 'Y' then Z.hg_unit_id else Z.item_unit_id end) as item_unit_id,Z.combo_num,Z.is_itemgroup ");
//				sb2.append("from (select A.item_id,hd.is_itemgroup,hd.details_id,hg.item_id as group_item_id,hd.item_unit_id,hg.item_unit_id as hg_unit_id,hd.combo_num  ");
//				sb2.append("from (select hi.id as item_id from hq_item_info hi where hi.is_combo='Y' and hi.id in("+item_id+")) as A ");
//				sb2.append("LEFT JOIN hq_item_combo_details hd on A.item_id = hd.iitem_id ");
//				sb2.append("LEFT JOIN hq_item_group_details hg on hd.details_id = hg.item_group_id and hd.is_itemgroup='Y' and hg.isdefault='Y') as Z ");
//				sb2.append("ORDER BY Z.item_id) as  ZZ ");
//				sb2.append("GROUP BY ZZ.item_id,ZZ.combo_item_id,ZZ.item_unit_id,zz.is_itemgroup ");
//				List<JSONObject> query4Json3 = dao.query4Json(tenantId,
//						sb2.toString());
//				if (query4Json3.size() > 0) {
//					// 套餐
//					Integer number = order_item_arr.getJSONObject(i).optInt(
//							"number");
//					Integer groupIndex = order_item_arr.getJSONObject(i)
//							.optInt("group_index");
//					for (int j = 0; j < query4Json3.size(); j++) {
//						int number1 = number * query4Json3.get(j).optInt("combo_num");
//						JSONObject orderItemDetails = new JSONObject();
//						orderItemDetails.put("number", number1);
//						orderItemDetails.put("group_index", groupIndex);
//						orderItemDetails.put("item_id", query4Json3.get(j)
//								.optInt("combo_item_id"));
//						orderItemDetails.put("price", query4Json3.get(j)
//								.optDouble("price"));
//						orderItemDetails.put("unit_id", query4Json3.get(j)
//								.optInt("item_unit_id"));
//						orderItemDetails.put("tenancy_id", tenantId);
//						// 加菜
//						if (afterAddOrder.size() > 0) {
//							orderItemDetails.put("is_add_dish", "1");
//						}
//						order_item_details.add(orderItemDetails);
//					}
//				}
			}
		} else {
			CcExceptionUtil.validContains(jb, "order_item",
					CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
		}
		// 菜品口味做法信息
		if (!"null".equals(jb.optString("order_item_taste"))
				&& null != jb.optJSONArray("order_item_taste")) {
			JSONArray order_item_taste_arr = jb
					.optJSONArray("order_item_taste");
			JSONArray order_item_taste_arr1 = new JSONArray();
			for (int i = 0; i < order_item_taste_arr.size(); i++) {
				JSONObject object = (JSONObject) order_item_taste_arr.get(i);
				// 加菜
				if (afterAddOrder.size() > 0) {
					object.put("is_add_dish", "1");
				}
				order_item_taste_arr1.add(object);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i), "group_index",
						CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i),
						"taste_method_id",
						CcErrorCode.TASTE_METHOD_ID_EMPTY_ERROE);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i), "item_remark",
						CcErrorCode.ITEM_REMARK_EMPTY_ERROE);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i), "item_id",
						CcErrorCode.ITEM_ID_EMPTY_ERROE);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i), "type",
						CcErrorCode.TYPE_EMPTY_ERROE);
			}
			jb.put("order_item_taste", order_item_taste_arr1);

		}
		// 套餐明细信息
		if (!"null".equals(jb.optString("order_item_details"))
				&& null != jb.optJSONArray("order_item_details")) {
			JSONArray order_item_details = jb
					.optJSONArray("order_item_details");
			// 套餐明细信息
			if (order_item_details.size() > 0) {
				for (int i = 0; i < order_item_details.size(); i++) {
					CcExceptionUtil.validContains(
							order_item_details.getJSONObject(i), "group_index",
							CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
					CcExceptionUtil.validContains(
							order_item_details.getJSONObject(i), "item_id",
							CcErrorCode.ITEM_ID_EMPTY_ERROE);
					CcExceptionUtil.validContains(
							order_item_details.getJSONObject(i), "unit_id",
							CcErrorCode.UNIT_ID_EMPTY_ERROE);
					CcExceptionUtil.validContains(
							order_item_details.getJSONObject(i), "number",
							CcErrorCode.NUMBER_EMPTY_ERROE);
//					CcExceptionUtil.validContains(
//							order_item_details.getJSONObject(i), "price",
//							CcErrorCode.PRICE_EMPTY_ERROE);
				}
			}
		}
		order_list.put("actual_pay", order_list.optDouble("total_money"));
		order_list.put("shop_real_amount", order_list.optDouble("total_money"));
		order_list.put("shop_fee", order_list.optDouble("total_money"));
		
		if (order_list.optString("paystatus").length() == 0) {
			order_list.put("paystatus", "01");
		}
		if (isPrepayment&&type.equals("WX02")) {
			// 先付
			order_list.put("is_online_payment", "1");
		}else if(type.equals("WM10")){
			order_list.put("is_online_payment", "1");
		} else {
			// 后付
			order_list.put("is_online_payment", "0");
		}
		//菜品总价
		order_list.put("product_org_total_fee", product_org_total_fee);
		order_list.put("wx_open_id", openid);
		jb.put("order_list", order_list);
		List<JSONObject> list4 = new ArrayList<JSONObject>();
		JSONObject jsonob = new JSONObject();
		jsonob.put("order_list", jb.optJSONObject("order_list"));
		jsonob.put("order_code", jb.optJSONObject("order_list").optString("order_code"));
		jsonob.put("order_item", jb.optJSONArray("order_item"));
		jsonob.put("order_item_taste", jb.optJSONArray("order_item_taste"));
		jsonob.put("order_item_details", jb.optJSONArray("order_item_details"));
		jsonob.put("order_repayment", jb.optJSONArray("order_repayment"));
		jsonob.put("dispatch_time", jb.optString("dispatch_time"));
		jsonob.put("receive_time_dispatch",
				jb.optString("receive_time_dispatch"));
		list4.add(jsonob);
		data.setData(list4);
		data.setTenancy_id(tenantId);
		 logger.info("微信点餐下单的参数："+jsonob);
		 logger.info("微信点餐下单的参数order_list："+jb.optJSONObject("order_list"));
		 logger.info("微信点餐下单的参数order_code："+jb.optJSONObject("order_list").optString("order_code"));
		data.setStore_id(order_list.optInt("store_id"));
		if (afterAddOrder.size() > 0) {
			// 加菜
			orderManagerService.orderUpdateDish(data);
		} else {
			orderManagerService.orderSave(data);
		}
		
		// 给门店下发订单
		@SuppressWarnings("unchecked")
		List<Map<String, Object>> array1 = (List<Map<String, Object>>) data
				.getData();
		if (array1.size() > 0 && orderCode.equals("")) {
			Map<String, Object> obj1 = new HashMap<String, Object>();
			obj1 = array1.get(0);
			orderCode = (String) obj1.get("order_code");
		}
		 logger.info("微信点餐订单号orderCode:"+orderCode);
		if ("".equals(orderCode) || null == orderCode) {
			throw new SystemException(WxErrorCode.ORDER_FALSE);
		}
		;
		data.setTenancy_id(tenantId);
		List<JSONObject> list2 = new ArrayList<JSONObject>();
		JSONObject param = new JSONObject();
		param.put("order_code", orderCode);
		param.put("tenentid", tenantId);
		// JSONObject preOrder=new JSONObject();
		// preOrder.put("order_code", orderCode);
		// preOrder.put("offset_score", false);
		// preOrder.put("offset_card", false);
		// preOrder.put("offset_coupons", false);
		// preOrder.put("cost_card", memberCard);
		// preOrder.put("cost_apply_seq", "");
		// preOrder.put("cost_score", 20);
		// preOrder.put("coupon_list", "");
		// 跳转后获取
		// 加菜
		if (!member.optString("mac_openid").equals("null")) {
			result.put("mac_openid", member.optString("mac_openid"));
		}
		if (afterAddOrder.size() > 0) {
			// 加菜
			list2.add(jsonob);
			data.setData(list2);
			if (!isPrepayment) {
				JSONObject orderIssued = orderManagerService
						.orderUpdateDishIssued(data);
				result.put("orderIssued", orderIssued);

			} else {
				// 调用微信预下单接口
				result.put("order_code", orderCode);
				result.put("cost_card", memberCard);
				// result.put("preOrder", preOrder);
			}
		} else {
			list2.add(param);
			data.setData(list2);
			if (!isPrepayment&&type.equals("WX02")) {
				JSONObject orderIssued = orderManagerService.orderIssued(data);
				result.put("orderIssued", orderIssued);
			} else {
				// 调用微信预下单接口
				result.put("order_code", orderCode);
				result.put("cost_card", memberCard);
				// result.put("preOrder", preOrder);
			}
		}
		result.put("success", true);
		result.put("isPrepayment", isPrepayment);
		if (!isPrepayment&&type.equals("WX02")) {
			// 微信推送信息
			sendTemplatMsg(tenantId, openid, orderCode);
		}
		//延时下发
		if(isPrepayment){
			//先付
			JSONObject bean1=new JSONObject();
			sb.setLength(0);
			JSONObject queryOrderByOrderCode = newWxOrderDao.queryOrderByOrderCode(tenantId, orderCode);
			if(null==queryOrderByOrderCode){
				result.put("errorMsg", "未获取到订单的相关信息，请再次下单");
				result.put("errCode", "0");
				result.put("success", false);
				return result;
			}
			String orderId=queryOrderByOrderCode.optString("id");
			bean1.put("orderId", orderId);
			bean1.put("order_code", orderCode);
			bean1.put("openid", openid);
			bean1.put("tenantId", tenantId);
			bean1.put("type1", "orderFailure");//卡充值
			bean1.put("type", type);
			int count=20;
			//改： 加品牌 查询系统参数     ---wzx
			JSONObject jsonObject5 = baseInfoDao.querySystemParameterByParaCode(tenantId, organId,"wechat_payment_time");
			if(jsonObject5!=null){
				count = jsonObject5.optInt("para_value");
			}
			//--wzx

			/*List<JSONObject> queryWXSystemParameterByParaCode = newWxSystemParameterDao.queryWXSystemParameterByParaCode(tenantId, "wechat_payment_time");
			if(queryWXSystemParameterByParaCode.size()>0){
				JSONObject jsonObject5 = queryWXSystemParameterByParaCode.get(0);
				count = jsonObject5.optInt("para_value");
			}*/
			bean1.put("LongTime", System.currentTimeMillis()+count*60*1000);
			bean1.put("oper", 0);
			taskRedisDao.lpush("wxpaymentbillnum".getBytes(), bean1);
		}
		return result;
	}

	@Override
	public JSONObject preOrder(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		JSONObject result = new JSONObject();
		result.put("success", false);
		String type = jb.optString("type");
		// 预下单
		// 判断是否是会员
		String openId = jb.optString("openid");
		if (openId.equals("")) {
			result.put("errorMsg", "请关注公众号！");
			result.put("errCode", "0");
			return result;
		}
		int store_id = jb.optInt("store_id");
		StringBuilder sb = new StringBuilder();
		sb.append("select * from wx_member where openid='" + openId
				+ "' and subscribe=true");
		List<JSONObject> wx_member = dao.query4Json(tenantId, sb.toString());
		if (wx_member.size() < 1) {
			result.put("errorMsg", "请关注公众号！");
			result.put("errCode", "0");
			return result;
		}
		sb.delete(0, sb.length());
		// select cci.id,cl.rate from crm_customer_info cci LEFT JOIN crm_level
		// cl on cci.level= cl.id where cci.mobil='" + phone + "'
//		sb.append("select cci.id,cl.rate from crm_customer_info cci LEFT JOIN crm_level cl on cci.level= cl.id LEFT JOIN wx_member wm on cci.mobil=wm.mobile_num where wm.openid='"
//				+ openId + "' and wm.subscribe=true ");
//		List<JSONObject> memberList = dao.query4Json(tenantId, sb.toString());
//		if (memberList.size() < 1) {
//			result.put("errorMsg", "请绑定公众号！");
//			result.put("errCode", "1");
//			return result;
//		}
		sb.delete(0, sb.length());
		String dishOrderId = jb.optString("order_code");
		sb.append("select * from cc_order_list where order_code ='"
				+ dishOrderId + "'");
		List<JSONObject> orderList = dao.query4Json(tenantId, sb.toString());
		if (orderList.size() < 1) {
			result.put("errorMsg", "请返回到点菜页，再次下单");
			result.put("errCode", "2");
			return result;
		}
		
		sb.delete(0, sb.length());
		JSONObject order = orderList.get(0);
		if(order.optString("payment_state").equals("03")){
			result.put("errorMsg", "该订单已经支付了，请重新下单!");
			result.put("errCode", "2");
			return result;
		}
		// 计算订单理应支付多少钱
		double total_money = order.optDouble("total_money");// 总价格
		// 使用优惠劵兑换金额
		JSONObject deduct = new JSONObject();
		// 计算订单理应支付多少钱
		JSONObject paramJson = jb;
		// 获取支付订单信息
		double price = total_money;// 支付金额
		double card_cost_money = 0;
		double coupons_cost_money = 0;
		double discount_cost = 0;// 折扣金额
		// 获取用户的信息，包括积分、卡劵、会员卡信息
		// 用户表内没有手机号 则为未绑定用户 不用再去查询
//		JSONObject member = memberList.get(0);
//		sb.append("select card_code,main_balance,reward_balance from crm_customer_card  where card_state='1' and customer_id="
//				+ member.optInt("id"));
//		List<JSONObject> cardList = dao.query4Json(tenantId, sb.toString());
//		if (cardList.size() < 0) {
//
//		}
		sb.delete(0, sb.length());
		// 查询出所有的优惠劵（该用户相关的）（调接口）
		sb.append("select * from crm_activity_wxcupous");
		List<JSONObject> couponList = dao.query4Json(tenantId, sb.toString());
		// if(){}
		// 获取优惠折扣价格
//		int rate = member.optInt("rate") == 0 ? 100 : member.optInt("rate");
		// 打折金额只按菜品计算，不计算服务费、外送费等信息
		sb.delete(0, sb.length());
		sb.append("SELECT b.*,info.item_name,info.photo1,info.is_discount from  cc_order_item b");
		sb.append(" left join hq_item_info info on b.item_id=info.id where  b.order_code= '"
				+ dishOrderId + "'");
		List<JSONObject> order_item_list = dao.query4Json(tenantId,
				sb.toString());
		// 获取菜品口味
		sb.delete(0, sb.length());
		sb.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '"
				+ dishOrderId + "'");
		List<JSONObject> cc_order_item_taste_list = dao.query4Json(tenantId,
				sb.toString());

		float total = 0;
		if (order_item_list != null && order_item_list.size() > 0) {
			for (JSONObject bean : order_item_list) {
				if (bean.optString("is_discount").equals("Y")) {
					if (cc_order_item_taste_list != null
							&& cc_order_item_taste_list.size() > 0) {
						for (JSONObject taste : cc_order_item_taste_list) {
							if (bean.optInt("item_id") == taste
									.optInt("item_id")
									&& bean.optInt("group_index") == taste
											.optInt("group_index")) {
								total += (bean.optDouble("price") + taste
										.optDouble("proportion_money"))
										* bean.optInt("number");
							}
						}
					} else {
						total += bean.optDouble("price")
								* bean.optInt("number");
					}
				}
			}
		}
//		discount_cost = (float) (total * (100 - rate) * 0.01);
		discount_cost = 0;
		// 获取抵消顺序
		String cost_apply_seq_temp = jb.optString("cost_apply_seq");
		if (cost_apply_seq_temp.indexOf("card") < 0) {
			price = price - discount_cost;
		} else {
			discount_cost = 0;
		}
		// 计算抹零金额
		// 获取保留金额尾数
		double retainNum = 0;
		int retainOrder = 2;
		int retainDish = 2;
		// List<JSONObject> retainList =
		// dishDao.getOrderParamList(param.getTenantId());
		sb.delete(0, sb.length());
		List<JSONObject> retainList = newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, store_id);
		if(retainList.size()==0){
			retainList=newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, 0);
		}
		if (retainList != null && retainList.size() > 0) {
			for (JSONObject reatin : retainList) {
				if ("ZDJEWS".equals(reatin.optString("para_code"))) {
					retainOrder = reatin.optInt("para_value");
				} else if ("CMJEWS".equals(reatin.optString("para_code"))) {
					retainDish = reatin.optInt("para_value");
				}
			}
		}
		// 是否包含会员卡消费，如果有的话，不计算会员折扣
		NumberFormat nf = NumberFormat.getNumberInstance();
		nf.setMaximumFractionDigits(retainOrder);
		double retainMoney = Double.valueOf(nf.format(price).replace(",", ""));
		// retainNum = (float) (price - retainMoney);
		retainNum = retainMoney - price;
		price = price + retainNum;
		result.put("retain_money", retainNum);
		result.put("retain_order", retainOrder);
		result.put("retain_dish", retainDish);
		System.out.println("retain_money:"+retainNum);
		System.out.println("retain_order:"+retainOrder);
		System.out.println("retain_dish:"+retainDish);
		// 都没选择抵扣，则直接跳过
		result.put("total_money", total_money);
		result.put("actual_pay", price);
		result.put("shop_fee", price);
		result.put("discount_amount", discount_cost);
		result.put("card_cost_money", card_cost_money);
		result.put("coupons_cost_money", coupons_cost_money);
		// 订单号
		String order_num = order.getString("order_code");
		JSONObject reObject = new JSONObject();
		if (price <= 0) {
			// 0元的处理
			double discount_amount = discount_cost;
			// 实际支付金额
			// double actual_pay =
			// order.optDouble("actual_pay");//result.optString("total_amount")
			double actual_pay = price;
			JSONObject order1=new JSONObject();
			order1.put("discount_amount", discount_amount);
			// order.put("discount_mode_id", 5);
			// 抹零金额
			order1.put("maling_amount", result.optDouble("retain_money"));
			order1.put("actual_pay", actual_pay);
			order1.put("shop_fee", actual_pay);
			// 修改订单支付状态
			order1.put("payment_state", "03");
			// 设置是否是线上支付
			order1.put("is_online_payment", "1");
			order1.put("id", order.optInt("id"));
			dao.updateIgnorCase(tenantId, "cc_order_list", order1);
			reObject.put("success", true);
			reObject.put("surePay", true);
			reObject.put("bill_num", order_num);
			// 下发
			Data data = new Data();
			data.setTenancy_id(tenantId);
			List<JSONObject> list = new ArrayList<JSONObject>();
			JSONObject param = new JSONObject();
			param.put("order_code", dishOrderId);
			param.put("tenentid", tenantId);
			list.add(param);
			data.setData(list);
			result = orderManagerService.orderIssued(data);
			sb.delete(0, sb.length());
			sendTemplatMsg(tenantId, openId, order_num);
		} else {
			// 预下单
			reObject = newPaymentService.getPayMachJsBean(tenantId,
					order.optInt("store_id") + "", jb.optString("remoteIp"),
					order_num, "微信点餐订单支付", order.optString("actual_pay"),
					jb.optString("mac_openid"), null,
					TradeStatusConstants.PAY_SOURCE_WECHAT_DNDC);
			reObject.put("retain_money", result.optDouble("retain_money"));
			result.put("success", true);
			result.put("bill_num", order_num);
			//将生成好的订单放入redis中
			List<JSONObject> queryWXSystemParameterByParaCode = newWxSystemParameterDao.queryWXSystemParameterByParaCode(tenantId, "whether_wechat_card_menue");
			if(queryWXSystemParameterByParaCode.size()>0){
				JSONObject jsonObject = queryWXSystemParameterByParaCode.get(0);
				int para_value = jsonObject.optInt("para_value");
				if(para_value==1){
					JSONObject bean1=new JSONObject();
					bean1.put("order_code", order_num);
					bean1.put("storeId", order.optInt("store_id"));
					bean1.put("openid",  openId);
					bean1.put("tenantId", tenantId);
					bean1.put("type1", "order");
					bean1.put("type", type);
					bean1.put("oper", 1);
					//FromRedisUtil.setBillNum(bean1);
					taskRedisDao.lpush("wxpaymentbillnum".getBytes(), bean1);
				}
			}
			reObject.put("surePay", false);
		}
		reObject.put("success", true);
		return reObject;
	}

	@Override
	public JSONObject surePayOrder(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		// logger.info("surePayOrder");
		JSONObject result = new JSONObject();
		result.put("success", false);
		String orderCode = jb.optString("order_code");
		String type = jb.optString("type");
		int store_id = jb.optInt("store_id");
		if (orderCode.equals("null")) {
			result.put("msg", "请返回到点菜页面在次下单哦");
			return result;
		}
		StringBuilder sb = new StringBuilder();
		sb.append("select * from cc_order_list where order_code ='" + orderCode
				+ "'");
		List<JSONObject> orderList = dao.query4Json(tenantId, sb.toString());
		if (orderList.size() < 1) {
			result.put("msg", "请返回到点菜页面在次下单哦!");
			return result;
		}
		JSONObject order = orderList.get(0);
		if(order.optString("payment_state").equals("03")){
			result.put("success", true);
			result.put("payState",true);
			result.put("orderId", order.optInt("id"));
			return result;
		}
		// 计算订单理应支付多少钱
		double total_money = order.optDouble("total_money", 0);// 总价格
		int storeId = order.optInt("store_id");
		String openId = jb.optString("openid");
		// 计算订单理应支付多少钱
		JSONObject paramJson = jb;
		// 获取支付订单信息
		sb.delete(0, sb.length());
//		sb.append("select * from payment_way where payment_class='wechat_pay' and status='1'");
		sb.append("select cc.* from (select id,(select count(pwoo.*)");
		sb.append(" from payment_way_of_ogran pwoo where pwoo.organ_id="+storeId+"  and payment_id=pp.id)");
		sb.append(" as z from payment_way pp where pp.payment_class='wechat_pay' and pp.status='1') cc where cc.z>0 ");
		List<JSONObject> query4Json2 = dao.query4Json(tenantId, sb.toString());
		double price = total_money;// 支付金额
		double card_cost_money = 0;
		double coupons_cost_money = 0;
		double discount_cost = 0;// 折扣金额
		// 获取用户的信息，包括积分、卡劵、会员卡信息
		sb.delete(0, sb.length());
		sb.append("select id, money,nickname,card_id,mobile_num,head_img_url, level,score ,isbind,subscribe,vip_name,vip_sex,vip_birthday,vip_mobile,vip_province,vip_city ,iscomplete,bind_time,store_id,mac_openid from wx_member where openid='"
				+ openId + "' order by id desc");
		List<JSONObject> memberList = dao.query4Json(tenantId, sb.toString());
		JSONObject member = memberList.size() > 0 ? memberList.get(0) : null;
		// 用户表内没有手机号 则为未绑定用户 不用再去查询(可以不用试会员只要粉丝即可)
		if (member != null && member.optBoolean("subscribe")) {
			String mac_openid = member.optString("mac_openid");
			if (mac_openid.equals("null") || mac_openid.equals("")) {
				sb.delete(0, sb.length());
				mac_openid = jb.optString("mac_openid");
				sb.append("update wx_member set mac_openid='" + mac_openid
						+ "' where id=" + member.optInt("id"));
				dao.execute(tenantId, sb.toString());
				// member.put("mac_openid", mac_openid);
				// dao.updateIgnorCase(tenantId, "wx_member", member);
			}
//			sb.delete(0, sb.length());
//			sb.append("select cci.id,cl.rate from crm_customer_info cci LEFT JOIN crm_level cl on cci.level= cl.id where cci.mobil='"
//					+ member.optString("vip_mobile") + "'");
//			List<JSONObject> query4Json = dao.query4Json(tenantId,
//					sb.toString());
//			if (query4Json.size() < 1) {
//				result.put("msg", "亲，需要绑定成会员才能下单。");
//				return result;
//			}
//			JSONObject customer = query4Json.get(0);
			// 获取优惠折扣价格
//			int rate = customer.optInt("rate");
//			sb.delete(0, sb.length());
//			int cid = customer.optInt("id");
//			sb.append("select card_code,main_balance,reward_balance from crm_customer_card  where card_state='1' and customer_id="
//					+ cid);
//			List<JSONObject> cardList = dao.query4Json(tenantId, sb.toString());
			sb.delete(0, sb.length());
			// 获取票劵
			// sb.append("");
			// List<JSONObject> couponsList = dao.query4Json(tenantId,
			// sb.toString());
			List<JSONObject> couponsList = null;
			// 打折金额只按菜品计算，不计算服务费、外送费等信息
			sb.delete(0, sb.length());
			sb.append("SELECT b.*,info.item_name,info.photo1,info.is_discount from  cc_order_item b");
			sb.append(" left join hq_item_info info on b.item_id=info.id where  b.order_code= '"
					+ orderCode + "'");
			List<JSONObject> order_item_list = dao.query4Json(tenantId,
					sb.toString());
			// 获取菜品口味
			sb.delete(0, sb.length());
			sb.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '"
					+ orderCode + "'");
			List<JSONObject> cc_order_item_taste_list = dao.query4Json(
					tenantId, sb.toString());			
			int count=20;
			sb.setLength(0);
			//改： 加品牌 查询系统参数     ---wzx
			JSONObject query4Json26 = baseInfoDao.querySystemParameterByParaCode(tenantId,storeId+"","wechat_payment_time");
			if(query4Json26!=null){
				String optString = query4Json26.optString("para_value");
				count=Integer.parseInt(optString);
			}
			//--wzx

			
			/*sb.append("select para_value from sys_parameter where para_code='wechat_payment_time' and store_id in("+storeId+",0) ORDER BY store_id desc");
			List<JSONObject> query4Json26 = dao.query4Json(tenantId, sb.toString());
			if(query4Json26.size()>0){
				String optString = query4Json26.get(0).optString("para_value");
				count=Integer.parseInt(optString);
			}
			sb.setLength(0);*/
//			sb.append("select * from cc_order_list ");
//			sb.append("where payment_state='01' and customer_id=(select id from crm_customer_info where remark='"+openId+"')");
//			sb.append(" ORDER BY id DESC");
			sb.append("select * from cc_order_list where openid='"+openId+"'");
			sb.append(" ORDER BY id DESC");
			List<JSONObject> query4Json = dao.query4Json(tenantId, sb.toString());
			if(query4Json.size()>0){
				String single_time = query4Json.get(0).optString("single_time");
				Date c=DateUtil.parseDateAll(single_time);
				long time = new Date().getTime()-c.getTime();
				if(time>(count*60*1000)){
					
					JSONObject order1=new JSONObject();
					order1.put("order_state", "08");
					order1.put("payment_state", "05");
					order1.put("id", order.optInt("id"));
					order1.put("cancellation_time", DateUtil.getNowDateYYDDMMHHMMSS());
					dao.updateIgnorCase(tenantId, "cc_order_list", order1);
					String order_code = order.optString("order_code");
					store_id = order.optInt("store_id");
					//取消时将已经使用的优惠劵退还
					List<JSONObject> queryUseCouponByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, order_code,"coupons");
					if(queryUseCouponByOrder.size()>0){
						//有使用优惠劵
						com.tzx.crm.bo.dto.Data param=new com.tzx.crm.bo.dto.Data();
						param.setOper(Oper.init);
						param.setTenancy_id(tenantId);
						param.setStore_id(store_id);
						List<JSONObject> list = new ArrayList<JSONObject>();
						JSONObject bean=new JSONObject();
						bean.put("chanel", type);
						List<JSONObject> couponList= new ArrayList<JSONObject>();
						for (JSONObject jsonObject : queryUseCouponByOrder) {
							JSONObject bean1=new JSONObject();
							bean1.put("coupons_code", jsonObject.optString("pay_no"));
							couponList.add(bean1);
						}
						bean.put("couponslist", couponList);
						list.add(bean);
						param.setData(list);
						//撤销接口
						try {
							couponsService.coupons(param);
							boolean deleteOrderByOrderCode = newWxOrderDao.deleteOrderByOrderCode(tenantId, "cc_order_repayment", order_code);
							if(!deleteOrderByOrderCode){
								result.put("msg", "亲，撤销票劵失败");
								result.put("success", false);
								return result;
							}
						} catch (Exception e) {
							e.printStackTrace();
							result.put("msg", e.getMessage());
							result.put("success", false);
							return result;
						}
						
					}
					//还没有失效
					result.put("msg", "订单已经过期，将自动取消");
					return result;
				}
				time=c.getTime()+(count*60*1000);
				result.put("time", time);
				result.put("count", count);
			}
			
			result.put("order", order);
			result.put("order_item_list", order_item_list);
			float total = 0;
			if (order_item_list.size() > 0) {
				for (JSONObject bean : order_item_list) {
					if (bean.optString("is_discount").equals("Y")) {
						if (cc_order_item_taste_list != null
								&& cc_order_item_taste_list.size() > 0) {
							for (JSONObject taste : cc_order_item_taste_list) {
								if (bean.optInt("item_id") == taste
										.optInt("item_id")
										&& bean.optInt("group_index") == taste
												.optInt("group_index")) {
									total += (bean.optDouble("price") + taste
											.optDouble("proportion_money"))
											* bean.optInt("number");
								}
							}
						} else {
							total += bean.optDouble("price")
									* bean.optInt("number");
						}
					}
				}
			}
//			discount_cost = (float) (total * (100 - rate) * 0.01);
			discount_cost = 0;
			// 获取抵消顺序
			String cost_apply_seq_temp = paramJson.optString("cost_apply_seq");
			if (cost_apply_seq_temp.indexOf("card") < 0) {
				price = price - discount_cost;
			} else {
				discount_cost = 0;
			}
			// 计算抹零金额
			// 获取保留金额尾数
			double retainNum = 0;
			int retainOrder = 2;
			int retainDish = 2;
			sb.delete(0, sb.length());
			List<JSONObject> retainList = newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, store_id);
			if(retainList.size()==0){
				retainList=newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, 0);
			}
//			sb.append("select para_name,para_code,para_value from sys_parameter where valid_state='1' and  para_code in ('CMJEWS','ZDJEWS')");
//			List<JSONObject> retainList = dao.query4Json(tenantId,
//					sb.toString());
			if (retainList.size() > 0) {
				for (JSONObject reatin : retainList) {
					if ("ZDJEWS".equals(reatin.optString("para_code"))) {
						retainOrder = reatin.optInt("para_value");
					} else if ("CMJEWS".equals(reatin.optString("para_code"))) {
						retainDish = reatin.optInt("para_value");
					}
				}
			}
			// 是否包含会员卡消费，如果有的话，不计算会员折扣
			NumberFormat nf = NumberFormat.getNumberInstance();
			nf.setMaximumFractionDigits(retainOrder);
			double retainMoney = Double.valueOf(nf.format(price).replace(",", ""));
			// retainNum = (float) (price - retainMoney);
			retainNum = retainMoney - price;
			price = price + retainNum;
//			float retainMoney = Float.parseFloat(nf.format(price));
//			// retainNum = (float) (price - retainMoney);
//			retainNum = (float) (retainMoney - price);
//			price = price + retainNum;
			result.put("retain_money", retainNum);
			result.put("retain_order", retainOrder);
			result.put("retain_dish", retainDish);
			// 都没选择抵扣，则直接跳过
			if (MyUtil.checkEmpty(paramJson.optString("offset_card"))
					|| MyUtil.checkEmpty(paramJson.optString("offset_coupons"))) {
				String cost_card = paramJson.optString("cost_card");
				String cost_coupons = paramJson.optString("cost_coupons");
				// 用户表内没有手机号 则为未绑定用户 不用再去查询
				// 获取抵消顺序
				if (MyUtil.checkEmpty(cost_apply_seq_temp)) {
					String[] cost_apply_seq = cost_apply_seq_temp.split(",");
					for (int i = 0; i < cost_apply_seq.length; i++) {
						if (cost_apply_seq[i].equals("card")) {
							// 获取会员卡余额
//							for (int j = 0; j < cardList.size(); j++) {
//								if (cardList.get(j).optString("card_code")
//										.equals(cost_card)) {
//									// 折算金额
//									double card_total_money = cardList.get(j)
//											.optDouble("main_balance")
//											+ cardList.get(j).optDouble(
//													"reward_balance");
//									if (card_total_money <= price) {
//										card_cost_money = card_total_money;
//										price = price - card_total_money;
//									} else {
//										card_cost_money = price;
//										price = 0;
//									}
//									break;
//								}
//							}
						} else {
							// 获取优惠券信息
							for (int j = 0; j < couponsList.size(); j++) {
								if (couponsList.get(j).optString("code")
										.equals(cost_coupons)) {
									double coupons_money = couponsList.get(j)
											.optDouble("face_value");
									if (coupons_money <= price) {
										coupons_cost_money = coupons_money;
										price = price - coupons_money;
									} else {
										coupons_money = price;
										price = 0;
									}
									break;
								}
							}
						}
					}
				}
				nf.setMaximumFractionDigits(retainDish);
				result.put("cost_card", cost_card);
				result.put("cost_coupons", cost_coupons);
			}
		}
		sb.setLength(0);
		sb.append("select ccc.card_code,cci.id from (select * from crm_customer_info where remark='"+openId+"') cci ");
		sb.append("LEFT JOIN crm_customer_card ccc on cci.id=ccc.customer_id");
		List<JSONObject> query4Json = dao.query4Json(tenantId, sb.toString());
		if(query4Json.size()<1){
			result.put("canCardPay", false);
		}else{
			result.put("canCardPay", true);
		}
		sb.setLength(0);
		sb.append("select cc.* from (select id,(select count(pwoo.*) from payment_way_of_ogran pwoo where pwoo.organ_id="
				+ storeId
				+ "  and payment_id=pp.id) as z from payment_way pp where pp.payment_class='card' and pp.status='1') cc where cc.z>0 ");
		List<JSONObject> cardPayList = dao.query4Json(tenantId, sb.toString());
		if (cardPayList.size() < 1) {
			result.put("canCardPay", false);
		} else if(query4Json.size()>1){
			result.put("canCardPay", true);
		}
		sb.setLength(0);
		if(query4Json2.size()<1){
			//不支持微信支付
			result.put("canWechatPay", false);
		}else{
			result.put("canWechatPay", true);
		}
		//是否支持优惠劵支付
		boolean canCouponsPay = newWxPaymentWayDao.whetherSupportThisPayment(tenantId, "coupons", storeId+"");
		if(query4Json.size()>0){
			JSONObject customer = query4Json.get(0);
			int id = customer.optInt("id");
			int notUsed = newWxCouponsTicketDao.queryCouponsTickstNumByState(tenantId, id+"", "1");
			if(notUsed>0&&canCouponsPay){
				canCouponsPay=true;
			}else{
				canCouponsPay=false;
			}
		}else{
			canCouponsPay=false;
		}
		result.put("canCouponsPay", canCouponsPay);
		//是否支持使用积分付款
		boolean whetherSupportThisPayment = newWxPaymentWayDao.whetherSupportThisPayment(tenantId, "card_credit", storeId+"");
		result.put("canUseCredit", whetherSupportThisPayment);
		if(whetherSupportThisPayment){
			//使用积分
			JSONObject usefulCreditAndUseRule = newWxMemberDao.getUsefulCreditAndUseRule(tenantId, openId);
			if(null==usefulCreditAndUseRule){
				result.put("canUseCredit",false);
			}else{
				//兑换规则积分是多少
				double trading_credit = usefulCreditAndUseRule.optDouble("trading_credit",0.0);
				//兑换规则金额是多少
				double cash_money = usefulCreditAndUseRule.optDouble("cash_money",0.0);
				//用户所有的积分
				double useful_credit = usefulCreditAndUseRule.optDouble("useful_credit");
				if(useful_credit>0&&trading_credit!=0.0&&cash_money!=0.0){
					//计算出该用户可以使用的积分
					double usecredit = Scm.psub(useful_credit, (useful_credit % trading_credit));
					if(usecredit<=0||trading_credit==0.0||cash_money==0.0){
						//本次扣减积分不满足抵扣规则   
						result.put("canUseCredit",false);				
					}else{
						//用户可以兑换的金额
						//用户可以兑换的金额
						double cashmoney =Math.floor(Scm.pdiv(Scm.pmui(usecredit,cash_money),trading_credit)) ;
						double actual_pay = order.optDouble("actual_pay",0.0);
						double credit_cash_bill = usefulCreditAndUseRule.optDouble("credit_cash_bill",100.0);
						actual_pay=Scm.pdiv((actual_pay*credit_cash_bill),100.0);
						if(cashmoney>actual_pay){
							//更具账单金额计算出的积分
							double usecredit1=Scm.pmui(Scm.pdiv(actual_pay,cash_money),trading_credit);
							String[] split = String.valueOf(usecredit1).split("\\.");
							//实际兑换的积分
							usecredit=Double.valueOf(split[0]);
							usecredit = Scm.psub(usecredit, (usecredit % trading_credit));
							//实际兑换的金额
//							cashmoney = Math.floor(Scm.pdiv(Scm.pmui(usecredit,cash_money),trading_credit)) ;
							NumberFormat nf = NumberFormat.getNumberInstance();
							//保留两位小数
							nf.setMaximumFractionDigits(2);
							cashmoney = Double.valueOf(nf.format(Scm.pdiv(Scm.pmui(usecredit,cash_money),trading_credit)).replace(",", "")) ;
						}
						result.put("cashmoney", cashmoney);
						result.put("canUseCredit", true);
						result.put("usecredit", usecredit);
					}
				}else{
					result.put("canUseCredit",false);
				}
			}
		}
		//已经使用优惠劵付过的一部分款
		sb.setLength(0);
//		sb.append("select * from cc_order_repayment where order_code='"+orderCode+"' ORDER By id DESC");
		List<JSONObject> orderRepaymentList = newWxOrderDao.getOrderPaymentDetail(tenantId, orderCode);
		Double total_money1=0.0;
		String class_name="";
		for(int i=0;i<orderRepaymentList.size();i++){
			JSONObject jo=orderRepaymentList.get(i);
			double pay_money = jo.optDouble("pay_money",0.0);
			total_money1+=pay_money;
			if(jo.optString("payment_class").equals("coupons")){
				//使用优惠劵
				sb.setLength(0);
				sb.append("select ccc.class_name from crm_coupons_type cct");
				sb.append(" LEFT JOIN crm_coupons_class ccc on ccc.id=cct.class_id");
				sb.append(" where cct.id in(select type_id from crm_coupons where code='"+jo.optString("pay_no")+"')");
				List<JSONObject> couponList = dao.query4Json(tenantId, sb.toString());
				for(int k=0;k<couponList.size();k++){
					JSONObject j=couponList.get(k);
					String class_name1 = j.optString("class_name");
					if(i==(orderRepaymentList.size()-1)&&k==(couponList.size()-1)){
						class_name+=class_name1;
					}else{
						class_name+=class_name1+",";
					}
					
				}
			}else if(jo.optString("payment_class").equals("card_credit")){
				//使用积分
				result.put("changeCashMoney", pay_money);
				result.put("useCredit", jo.optString("remark"));
				result.put("isUseCredit", true);
			}
			
		}
		if(!class_name.equals("")){
			result.put("class_name", class_name);
		}
		if(total_money<=total_money1){
			//已经支付完成
			//修改订单状态
			//更改订单状态为已支付
			newWxOrderDao.updateOrderState(tenantId, "03", orderCode,total_money);
			//下发订单
			// 下发
			Data data1 = new Data();
			data1.setTenancy_id(tenantId);
			JSONObject queryOrderByOrderCode = newWxOrderDao.queryOrderByOrderCode(tenantId,orderCode);
			result.put("orderId", queryOrderByOrderCode.optInt("id"));
			List<JSONObject> list2 = new ArrayList<JSONObject>();
			JSONObject param = new JSONObject();
			param.put("order_code", orderCode);
			param.put("tenentid", tenantId);
			list2.add(param);
			data1.setData(list2);
			// 成功后更改订单状态，并下发订单
			JSONObject orderIssued = orderManagerService.orderIssued(data1);
			//微信推送信息
			this.sendTemplatMsg(tenantId, openId, orderCode);
			result.put("success", true);
			result.put("payState",true);
			return result;
		}
		
		result.put("total_money", total_money);
		result.put("actual_pay", order.optDouble("actual_pay"));
		
		result.put("shop_fee", order.optDouble("actual_pay"));
		result.put("discount_amount", discount_cost);
		result.put("card_cost_money", card_cost_money);
		result.put("coupons_cost_money", coupons_cost_money);
		// 放置订单状态
		result.put("payment_state", order.optString("payment_state"));
		result.put("payState",false);
		result.put("success", true);
		// logger.info(result);
		return result;
	}

	@Override
	public JSONObject orderSuccessTakeOut(String tenantId, JSONObject jb) throws Exception {
		// [第一步]查询支付状态
		JSONObject result = new JSONObject();
		result.put("success", false);
		String orderCode = jb.optString("order_code");
		String storeId = jb.optString("storeId");
		String openId = jb.optString("openid");
		String type="WM10";
		if (storeId.isEmpty()) {
			storeId = jb.optString("store_id");
		}
		String tenantIdOrderCode = tenantId + "" + orderCode;
		Boolean hasOrderNum = FromRedisUtil.hasOrderNum(tenantIdOrderCode);
		if (hasOrderNum) {
			result.put("success", true);
			return result;
		}
		// 查询订单支付状态
		result = newPaymentService.queryOrderStatus(tenantId, orderCode, storeId);
		if (!result.optBoolean("success") || !"finished".equals(result.optString("status"))) {
			return result;
		}
		StringBuilder sb = new StringBuilder();
		sb.append("select payment_state from cc_order_list where order_code='" + orderCode + "'");
		List<JSONObject> query4Json7 = dao.query4Json(tenantId, sb.toString());
		if (query4Json7.size() < 1) {
			result.put("errorMsg", "未获取到订单信息");
			result.put("errCode", "1");
			return result;
		}
		JSONObject jsonObject = query4Json7.get(0);
		String optString = jsonObject.optString("payment_state");
		if (optString.equals("03")) {
			result.put("errorMsg", "支付成功");
			result.put("errCode", "2");
			return result;
		} else if (optString.equals("05")) {
			result.put("errorMsg", "订单已失效");
			result.put("errCode", "1");
			return result;
		}
		sb.setLength(0);
		// [第二步]添加消费流水记录
		sb.append("select cc.* from (select id,(select count(pwoo.*)");
		sb.append(" from payment_way_of_ogran pwoo where pwoo.organ_id=" + storeId + ")");
		sb.append(" as z from payment_way where payment_class='wechat_pay' and status='1') cc where cc.z>0 ");
		List<JSONObject> query4Json = dao.query4Json(tenantId, sb.toString());
		JSONObject wechatWay = query4Json.size() > 0 ? query4Json.get(0) : null;
		JSONObject bean = new JSONObject();
		bean.put("tenancy_id", tenantId);
		bean.put("payment_id", wechatWay.optInt("id"));
		bean.put("order_code", orderCode);
		bean.put("pay_money", result.optString("total_amount"));
		bean.put("pay_no", result.optString("buyer_logon_id"));
		bean.put("third_bill_code", result.optString("trade_no"));
		bean.put("store_id",storeId);
		bean.put("remark","wechat_pay");
		dao.insertIgnorCase(tenantId, "cc_order_repayment", bean);
		// 获取支付订单信息
		JSONObject paramJson = jb;
		sb.delete(0, sb.length());
		sb.append("select * from cc_order_list where order_code ='" + orderCode + "'");
		List<JSONObject> query4Json2 = dao.query4Json(tenantId, sb.toString());
		if (query4Json2.size() < 1) {
			result.put("errorMsg", "请点击我的订单查询出，相关的订单信息");
			result.put("errCode", "2");
			return result;
		}
		JSONObject order = query4Json2.get(0);
		result.put("orderId", order.optInt("id"));
		double price = order.optDouble("total_money"); // 总价格
		if (order.optString("order_state").equals("10") || order.optString("order_state").equals("08")) {
			result.put("errorMsg", "支付成功");
			result.put("errCode", "2");
			return result;
		}
		
		JSONObject order1=new JSONObject();
		// [第三步]修改订单状态
		order1.put("payment_state", "03");
		// 设置是否是线上支付
		order1.put("is_online_payment", "1");
		order1.put("id", order.optInt("id"));
		dao.updateIgnorCase(tenantId, "cc_order_list", order1);
		
		//增加会员积分
//		creditDispose(tenantId, jb, result, "WM10");
		
		// [第四步]下发
		// JSONObject result = this.orderIssued(tenantId, jb.optString("order_code"));
		Data data = new Data();
		data.setTenancy_id(tenantId);
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject param = new JSONObject();
		param.put("order_code", orderCode);
		param.put("tenentid", tenantId);
		list.add(param);
		data.setData(list);
		result = orderManagerService.orderIssued(data);
		// [第五步]数据放入缓存服务器
		sb.delete(0, sb.length());
//		JSONObject bean1 = new JSONObject();
//		bean1.put("order_code", orderCode);
//		bean1.put("openid", openId);
//		bean1.put("tenantId", tenantId);
//		bean1.put("type1", "orderCancel");
//		bean1.put("type", type);
//		bean1.put("discount_amount", order.optDouble("actual_pay",0.0));
//		bean1.put("payment", "wechat");
//		int count = 20;
//		List<JSONObject> queryWXSystemParameterByParaCode = newWxSystemParameterDao.queryWXSystemParameterByParaCode(tenantId, "order_cancel_time");
//		if (queryWXSystemParameterByParaCode.size() > 0) {
//			JSONObject jsonObject2 = queryWXSystemParameterByParaCode.get(0);
//			count = jsonObject2.optInt("para_value");
//		}
//		bean1.put("LongTime", System.currentTimeMillis() + count * 60 * 1000);
//		bean1.put("oper", 0);
//		taskRedisDao.lpush("wxpaymentbillnum".getBytes(), bean1);
		result.put("orderId", order.optInt("id"));
		result.put("success", true);
		
		return result;
	}

	@Override
	public JSONObject orderSuccess(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		JSONObject result = new JSONObject();
		result.put("success", false);
		String billNum = jb.optString("bill_num");
		String orderCode = jb.optString("order_code");
		String storeId = jb.optString("storeId");
		String type = "WX02";
		if(null==storeId||storeId.equals("")){
			storeId=jb.optString("store_id");
		}
		// String total_amount=jb.optString("total_amount");
		String openId = jb.optString("openid");
		StringBuilder sb = new StringBuilder();
		String tenantIdOrderCode=tenantId+""+orderCode;
		Boolean hasOrderNum = FromRedisUtil.hasOrderNum(tenantIdOrderCode);
		sb.delete(0, sb.length());
		sb.append("select * from cc_order_list where order_code ='" + orderCode
				+ "'");
		List<JSONObject> query4Json2 = dao.query4Json(tenantId, sb.toString());
		if (query4Json2.size() < 1) {
			result.put("errorMsg", "请点击我的订单查询出，相关的订单信息");
			result.put("errCode", "2");
			return result;
		}
		JSONObject order = query4Json2.get(0);
		logger.info("微信点餐查询redis中是否还有该订单："+hasOrderNum);
		if(hasOrderNum){
			result.put("success", true);
			result.put("orderId", order.optInt("id"));
			result.put("need_invoice", order.optInt("need_invoice"));
			return result;
		}
		// 查询订单支付状态
		result = newPaymentService.queryOrderStatus(tenantId, orderCode,
				storeId);
		logger.info("微信点餐查询订单的支付结果："+result);
		if (!result.optBoolean("success")
				|| !"finished".equals(result.optString("status"))) {
			return result;
		}
		sb.setLength(0);
		sb.append("select payment_state from cc_order_list where order_code='"+orderCode+"'");
		List<JSONObject> query4Json7 = dao.query4Json(tenantId, sb.toString());
		if(query4Json7.size()<1){
			result.put("errorMsg", "未获取到订单信息");
			result.put("errCode", "1");
			return result;
		}
		logger.info("微信点餐查询订单的支付状态："+query4Json7);
		Boolean setOrderNum = FromRedisUtil.setOrderNum(tenantIdOrderCode);
		logger.info("微信点餐将订单号放入到redis中："+setOrderNum);
		JSONObject jsonObject = query4Json7.get(0);
		String optString = jsonObject.optString("payment_state");
		if(optString.equals("03")){
			result.put("orderId", order.optInt("id"));
			result.put("need_invoice", order.optInt("need_invoice"));
			result.put("success", true);
			result.put("errorMsg", "支付成功");
			result.put("errCode", "2");
			return result;
//		}else if(optString.equals("05")){
//			result.put("errorMsg", "订单已失效");
//			result.put("errCode", "1");
//			return result;
		}
		sb.setLength(0);
		// 添加消费流水记录、修改订单状态、下发菜单
		// JSONObject wechatWay = newPaymentService.getPaymentWay(tenantId,
		// "wechat_pay");
		JSONObject wechatWay = null;
//		List<JSONObject> wechat_payment_way = newWxSystemParameterDao.queryWXSystemParameterByParaCode(tenantId, "wechat_payment_way");
//		if(wechat_payment_way.size()>0){
//			JSONObject wechat_payment_way1 = wechat_payment_way.get(0);
//			int para_value = wechat_payment_way1.optInt("para_value");
//			if(para_value==1){
//				//多啦宝
//				sb.append("select cc.* from (select id,(select count(pwoo.*)");
//				sb.append(" from payment_way_of_ogran pwoo where pwoo.organ_id="+storeId+")");
//				sb.append(" as z from payment_way where payment_class='wechat_pay' and status='1') cc where cc.z>0 ");
//			}
//		}
		if(sb.length()==0){
			sb.append("select cc.* from (select id,(select count(pwoo.*)");
			sb.append(" from payment_way_of_ogran pwoo where pwoo.organ_id="+storeId+")");
			sb.append(" as z from payment_way where payment_class='wechat_pay' and status='1') cc where cc.z>0 ");
		}
		List<JSONObject> query4Json = dao.query4Json(tenantId, sb.toString());
		wechatWay = query4Json.size() > 0 ? query4Json.get(0) : null;
		JSONObject bean = new JSONObject();
		bean.put("tenancy_id", tenantId);
		bean.put("payment_id", wechatWay.optInt("id"));
		bean.put("order_code", orderCode);
		bean.put("pay_money", result.optString("total_amount"));
		bean.put("pay_no", result.optString("buyer_logon_id"));
		bean.put("third_bill_code", result.optString("trade_no"));
		bean.put("store_id", result.optString("store_id"));
		// dao.insertIgnorCase(tenantId, "cc_order_repayment", bean);
//		sb.delete(0, sb.length());
//		sb.append("select * from cc_order_repayment where order_code='"
//				+ orderCode + "'");
//		List<JSONObject> query4Json7 = dao.query4Json(tenantId, sb.toString());
//		if (query4Json7.size() > 0) {
//			bean.put("id", query4Json7.get(0).optInt("id"));
//			dao.updateIgnorCase(tenantId, "cc_order_repayment", bean);
//		} else {
			dao.insertIgnorCase(tenantId, "cc_order_repayment", bean);
			logger.info("微信点餐往cc_order_repayment表里面插入数据");
//		}
		// 更新
		// 获取支付订单信息
		JSONObject paramJson = jb;
		// JSONObject order = dishDao.getWhOrderBean(param.getTenantId(),
		// param.getRequestStr());
		
		result.put("orderId", order.optInt("id"));
		double price = order.optDouble("total_money");// 总价格
		logger.info("微信点餐订单的详情："+order);
		if (order.optString("order_state").equals("10")) {
			result.put("orderId", order.optInt("id"));
			result.put("need_invoice", order.optInt("need_invoice"));
			result.put("success", true);
			result.put("errorMsg", "支付成功");
			result.put("errCode", "2");
			return result;
		}
		// JSONObject paramElement = getDeductBean(param, price);
		// 计算订单理应支付多少钱
		// 获取支付订单信息
		// JSONObject order = dishDao.getWhOrderBean(param.getTenantId(),
		// paramJson.optString("order_code"));
		sb.delete(0, sb.length());
		// select cci.id,cl.rate from crm_customer_info cci LEFT JOIN crm_level
		// cl on cci.level= cl.id where cci.mobil='" + phone + "'
//		sb.append("select cci.id,cl.rate from crm_customer_info cci LEFT JOIN crm_level cl on cci.level= cl.id LEFT JOIN wx_member wm on cci.mobil=wm.mobile_num where wm.openid='"
//				+ openId + "' and wm.subscribe=true ");
//		List<JSONObject> memberList = dao.query4Json(tenantId, sb.toString());
//		if (memberList.size() < 1) {
//			result.put("errorMsg", "请绑定公众号！");
//			result.put("errCode", "1");
//			return result;
//		}
		String cost_apply_seq_temp = jb.optString("cost_apply_seq");
		double total_money = price;// 总价格
		double card_cost_money = 0;
		double coupons_cost_money = 0;
		double discount_cost = 0;// 折扣金额
		// 获取用户的信息，包括积分、卡劵、会员卡信息
		// 用户表内没有手机号 则为未绑定用户 不用再去查询
//		JSONObject member = memberList.get(0);
//		sb.delete(0, sb.length());
//		sb.append("select card_code,main_balance,reward_balance from crm_customer_card  where card_state='1' and customer_id="
//				+ member.optInt("id"));
//		List<JSONObject> cardList = dao.query4Json(tenantId, sb.toString());
//		if (cardList.size() < 0) {
//
//		}
		sb.delete(0, sb.length());
		// 查询出所有的优惠劵（该用户相关的）（调接口）
		sb.append("select * from crm_activity_wxcupous");
		List<JSONObject> couponList = dao.query4Json(tenantId, sb.toString());
		// if(){}
		// 获取优惠折扣价格
//		int rate = member.optInt("rate") == 0 ? 100 : member.optInt("rate");
		// 打折金额只按菜品计算，不计算服务费、外送费等信息
		sb.delete(0, sb.length());
		sb.append("SELECT b.*,info.item_name,info.photo1,info.is_discount from  cc_order_item b");
		sb.append(" left join hq_item_info info on b.item_id=info.id where  b.order_code= '"
				+ orderCode + "'");
		List<JSONObject> order_item_list = dao.query4Json(tenantId,
				sb.toString());
		// 获取菜品口味
		sb.delete(0, sb.length());
		sb.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '"
				+ orderCode + "'");
		List<JSONObject> cc_order_item_taste_list = dao.query4Json(tenantId,
				sb.toString());

		float total = 0;
		if (order_item_list != null && order_item_list.size() > 0) {
			for (JSONObject bean1 : order_item_list) {
				if (bean1.optString("is_discount").equals("Y")) {
					if (cc_order_item_taste_list != null
							&& cc_order_item_taste_list.size() > 0) {
						for (JSONObject taste : cc_order_item_taste_list) {
							if (bean1.optInt("item_id") == taste
									.optInt("item_id")
									&& bean1.optInt("group_index") == taste
											.optInt("group_index")) {
								total += (bean1.optDouble("price") + taste
										.optDouble("proportion_money"))
										* bean1.optInt("number");
							}
						}
					} else {
						total += bean1.optDouble("price")
								* bean1.optInt("number");
					}
				}
			}
		}
//		discount_cost = (float) (total * (100 - rate) * 0.01);
		discount_cost = 0;
		// 获取抵消顺序
		if (cost_apply_seq_temp.indexOf("card") < 0) {
			price = price - discount_cost;
		} else {
			discount_cost = 0;
		}
		// 计算抹零金额
		// 获取保留金额尾数
		double retainNum = 0;
		int retainOrder = 2;
		int retainDish = 2;
		// List<JSONObject> retainList =
		// dishDao.getOrderParamList(param.getTenantId());
		sb.delete(0, sb.length());
		int store_id = Integer.parseInt(storeId);
		List<JSONObject> retainList = newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, store_id);
		if(retainList.size()==0){
			retainList=newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, 0);
		}
//		sb.append("select para_name,para_code,para_value from sys_parameter where valid_state='1' and  para_code in ('CMJEWS','ZDJEWS')");
//		List<JSONObject> retainList = dao.query4Json(tenantId, sb.toString());
		if (retainList != null && retainList.size() > 0) {
			for (JSONObject reatin : retainList) {
				if ("ZDJEWS".equals(reatin.optString("para_code"))) {
					retainOrder = reatin.optInt("para_value");
				} else if ("CMJEWS".equals(reatin.optString("para_code"))) {
					retainDish = reatin.optInt("para_value");
				}
			}
		}
		// 是否包含会员卡消费，如果有的话，不计算会员折扣
		NumberFormat nf = NumberFormat.getNumberInstance();
		nf.setMaximumFractionDigits(retainOrder);
		double retainMoney = Double.valueOf(nf.format(price).replace(",", ""));
		// retainNum = (float) (price - retainMoney);
		retainNum = retainMoney - price;
		price = price + retainNum;
//		float retainMoney = Float.parseFloat(nf.format(price));
//		// retainNum = (float) (price - retainMoney);
//		retainNum = (float) (retainMoney - price);
//		price = price + retainNum;
		result.put("retain_money", retainNum);
		result.put("retain_order", retainOrder);
		result.put("retain_dish", retainDish);
		if (!paramJson.optString("offset_card").equals("null")
				&& !paramJson.optString("offset_coupons").equals("null")) {
			// 都没选择抵扣，则直接跳过
			if (MyUtil.checkEmpty(paramJson.optString("offset_card"))
					|| MyUtil.checkEmpty(paramJson.optString("offset_coupons"))) {
				String cost_card = paramJson.optString("cost_card");
				String cost_coupons = paramJson.optString("cost_coupons");
				// 用户表内没有手机号 则为未绑定用户 不用再去查询
				// 获取抵消顺序
				if (MyUtil.checkEmpty(cost_apply_seq_temp)) {
					String[] cost_apply_seq = cost_apply_seq_temp.split(",");
					for (int i = 0; i < cost_apply_seq.length; i++) {
						if (cost_apply_seq[i].equals("card")) {
							// 获取会员卡余额
//							for (int j = 0; j < cardList.size(); j++) {
//								if (cardList.get(j).optString("card_code")
//										.equals(cost_card)) {
//									// 折算金额
//									double card_total_money = cardList.get(j)
//											.optDouble("main_balance")
//											+ cardList.get(j).optDouble(
//													"reward_balance");
//									if (card_total_money <= price) {
//										card_cost_money = card_total_money;
//										price = price - card_total_money;
//									} else {
//										card_cost_money = price;
//										price = 0;
//									}
//									break;
//								}
//							}
						} else {
							// 获取优惠券信息
							for (int j = 0; j < couponList.size(); j++) {
								if (couponList.get(j).optString("code")
										.equals(cost_coupons)) {
									double coupons_money = couponList.get(j)
											.optDouble("face_value");
									if (coupons_money <= price) {
										coupons_cost_money = coupons_money;
										price = price - coupons_money;
									} else {
										coupons_money = price;
										price = 0;
									}
									break;
								}
							}
						}
					}
				}
				nf.setMaximumFractionDigits(retainDish);
				result.put("cost_card", cost_card);
				result.put("cost_coupons", cost_coupons);
			}
		}

		result.put("total_money", total_money);
		result.put("actual_pay", price);
		result.put("shop_fee", price);
		result.put("discount_amount", discount_cost);
		result.put("card_cost_money", card_cost_money);
		result.put("coupons_cost_money", coupons_cost_money);

		// 获取抵消顺序
		cost_apply_seq_temp = paramJson.optString("cost_apply_seq");
		// 会员卡、优惠券的消费 向支付方式表里填写其他支付方式流水
		// this.costOfCardAndCoupoons(tenantId, order.optInt("store_id"),
		// cost_apply_seq_temp, result, paramJson.optString("order_code"));
		if (!cost_apply_seq_temp.equals("null")
				&& MyUtil.checkEmpty(cost_apply_seq_temp)) {
			// 获取消费方式的消费记录
			// JSONObject couponspayWay =
			// newPaymentService.getPaymentWay(tenantId, "coupons");
			sb.delete(0, sb.length());
			sb.append("select * from payment_way where payment_class='coupons' and status='1'");
			
			List<JSONObject> query4Json3 = dao.query4Json(tenantId,
					sb.toString());
			JSONObject couponspayWay = query4Json3.size() > 0 ? query4Json3
					.get(0) : null;
			// JSONObject cardpayWay = newPaymentService.getPaymentWay(tenantId,
			// "card");
			sb.delete(0, sb.length());
			sb.append("select * from payment_way where payment_class='card' and status='1'");
			List<JSONObject> query4Json4 = dao.query4Json(tenantId,
					sb.toString());
			JSONObject cardpayWay = query4Json4.size() > 0 ? query4Json3.get(0)
					: null;
			// ================请求接口
			// 对积分、会员卡、优惠券进行消费处理==============================
			com.tzx.crm.bo.dto.Data basicData = new com.tzx.crm.bo.dto.Data();
			com.tzx.crm.bo.dto.Data basicData_new = new com.tzx.crm.bo.dto.Data();
			basicData.setTenancy_id(tenantId);
			basicData.setOper(Oper.add);
			basicData_new.setOper(Oper.update);
			basicData_new.setTenancy_id(tenantId);
			basicData.setStore_id(jb.optInt("storeId"));
			basicData_new.setStore_id(jb.optInt("storeId"));
			String cost_bill_num = "";
			JSONObject customerJson = new JSONObject();
			if (cost_apply_seq_temp.indexOf("card") >= 0) {
				// 获取支付密码
				// JSONObject cardDetail =
				// memberDao.getCardDetailByCode(tenantId,
				// result.optString("cost_card"));
				sb.delete(0, sb.length());
				sb.append("select * from crm_customer_card where card_code='"
						+ result.optString("cost_card") + "'");
				List<JSONObject> query4Json5 = dao.query4Json(tenantId,
						sb.toString());
				JSONObject cardDetail = query4Json5.size() > 0 ? query4Json5
						.get(0) : null;
				customerJson.put("card_code", result.optString("cost_card"));
				customerJson
						.put("cardpassword", cardDetail.opt("pay_password"));
				customerJson.put("consume_totalmoney",
						result.optDouble("total_money"));
				customerJson.put("consume_cardmoney",
						result.optDouble("card_cost_money"));
				customerJson.put("consume_creditmoney",
						result.optDouble("card_cost_money"));
				customerJson.put("bill_code", orderCode);
				customerJson.put("consume_credit", 0);
				customerJson.put("chanel", type);
				customerJson.put("operator", "wx_dish");
				customerJson.put("updatetime",
						DateUtil.getNowDateYYDDMMHHMMSS());
				List<JSONObject> basicParam = new ArrayList<JSONObject>();
				basicParam.add(customerJson);
				basicData.setData(basicParam);
				// 卡消费
//				cardTransactionService.customerCardConsume(basicData);
				cardConsumeService.requiresnewConsume(basicData);
				@SuppressWarnings("unchecked")
				Map<String, Object> reJson = (Map<String, Object>) basicData
						.getData().get(0);
				cost_bill_num = (String) reJson.get("bill_code");

				// 保存消费记录
				if (cardpayWay == null) {
					throw new Exception("商户未设定会员卡的消费方式、无法进行会员卡抵扣");
				}
				sb.delete(0, sb.length());
				// cc_order_repayment
				sb.append("select * from cc_order_repayment where order_code='"
						+ orderCode + "'");
				List<JSONObject> query4Json6 = dao.query4Json(tenantId,
						sb.toString());
				JSONObject bean2 = new JSONObject();
				bean2.put("tenancy_id", tenantId);
				bean2.put("payment_id", cardpayWay.optInt("id"));
				bean2.put("order_code", orderCode);
				bean2.put("pay_money", result.optDouble("card_cost_money"));
				bean2.put("pay_no", result.optString("cost_card"));
				bean2.put("third_bill_code", cost_bill_num);
				bean2.put("store_id", storeId);
//				if (query4Json6.size() > 0) {
//					bean2.put("id", query4Json6.get(0).optInt("id"));
//					dao.updateIgnorCase(tenantId, "cc_order_repayment", bean2);
//				} else {
					dao.insertIgnorCase(tenantId, "cc_order_repayment", bean2);
//				}
			}

			// 优惠券
			if (cost_apply_seq_temp.indexOf("coupons") >= 0) {
				customerJson.put("chanel", type);
				customerJson.put("bill_money",
						result.optDouble("coupons_cost_money"));
				customerJson.put("bill_code", orderCode);
				List<JSONObject> couponslist = new ArrayList<JSONObject>();
				JSONObject coupons = new JSONObject();
				coupons.put("coupons_code", result.optString("cost_coupons"));
				couponslist.add(coupons);
				customerJson.put("couponslist", couponslist);
				List<JSONObject> basicParam_new = new ArrayList<JSONObject>();
				basicParam_new.add(customerJson);
				basicData_new.setData(basicParam_new);
				couponsService.coupons(basicData_new);
				// 保存消费记录
				if (couponspayWay == null) {
					throw new Exception("商户未设定优惠券的消费方式、无法进行优惠券抵扣");
				}
				JSONObject bean4 = new JSONObject();
				bean4.put("tenancy_id", tenantId);
				bean4.put("payment_id", couponspayWay.optInt("id"));
				bean4.put("order_code", orderCode);
				bean4.put("pay_money", result.optDouble("coupons_cost_money"));
				bean4.put("pay_no", "");
				bean4.put("third_bill_code", cost_bill_num);
				bean4.put("store_id", storeId);
				dao.insertIgnorCase(tenantId, "cc_order_repayment", bean4);
			}
		}
		// 计算折扣优惠金额
		discount_cost = result.optDouble("discount_amount");
		// double discount_amount = price - discount_cost;
		// 计算折扣优惠金额
		double discount_amount = discount_cost;
		// 实际支付金额
		// double actual_pay =
		// order.optDouble("actual_pay");//result.optString("total_amount")
		double actual_pay = result.optDouble("total_amount", 0) != 0 ? result
				.optDouble("total_amount") : (price - discount_amount - result
				.optDouble("retain_money"));
		discount_amount = price - actual_pay - result.optDouble("retain_money");
		discount_amount = 0;
		JSONObject order1=new JSONObject();
		order1.put("discount_amount", discount_amount);
		// order.put("discount_mode_id", 5);
		// 抹零金额
		order1.put("maling_amount", result.optDouble("retain_money"));
		order1.put("actual_pay", actual_pay);
		order1.put("shop_fee", actual_pay);
		// 修改订单支付状态
		order1.put("payment_state", "03");
//		 order.put("order_state", "05");
//		order.put("order_state", "10");
		// 设置是否是线上支付
		order1.put("is_online_payment", "1");
		order1.put("id", order.optInt("id"));
		dao.updateIgnorCase(tenantId, "cc_order_list", order1);
		logger.info("微信点餐修改订单的状态成功");
		
		//增加会员积分处理
//		creditDispose(tenantId, jb, result, "WX02");
		
		// 下发
		// JSONObject result = this.orderIssued(tenantId,
		// jb.optString("order_code"));
		Data data = new Data();
		data.setTenancy_id(tenantId);
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject param = new JSONObject();
		param.put("order_code", orderCode);
		param.put("tenentid", tenantId);
		list.add(param);
		data.setData(list);
		logger.info("微信点餐下发订单给门店："+list);
		result = orderManagerService.orderIssued(data);
		sb.delete(0, sb.length());
		sendTemplatMsg(tenantId, openId, orderCode);
//		JSONObject bean1=new JSONObject();
//		bean1.put("order_code", orderCode);
//		bean1.put("openid", openId);
//		bean1.put("tenantId", tenantId);
//		bean1.put("type1", "orderCancel");
//		bean1.put("type", type);
//		bean1.put("discount_amount",  order.optDouble("actual_pay",0.0));
//		bean1.put("payment", "wechat");
//		int count=20;
//		List<JSONObject> queryWXSystemParameterByParaCode = newWxSystemParameterDao.queryWXSystemParameterByParaCode(tenantId, "order_cancel_time");
//		if(queryWXSystemParameterByParaCode.size()>0){
//			JSONObject jsonObject2 = queryWXSystemParameterByParaCode.get(0);
//			count = jsonObject2.optInt("para_value");
//		}
//		bean1.put("LongTime", System.currentTimeMillis()+count*60*1000);
//		bean1.put("oper", 0);
//		taskRedisDao.lpush("wxpaymentbillnum".getBytes(), bean1);
		result.put("orderId", order.optInt("id"));
		result.put("need_invoice", order.optInt("need_invoice"));
		result.put("success", true);
		
		return result;
	}

	@Override
	public JSONObject refundOrder(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		JSONObject result = new JSONObject();
		result.put("success", false);
		// 退单接口
		String orderCode = jb.optString("order_code");
		String type = jb.optString("type");
		StringBuilder sb = new StringBuilder();
		// sb.append("select * from cc_order_list_copy where order_code='"+orderCode+"'");
		sb.append("select * from cc_order_list where order_code='" + orderCode
				+ "'");
		//
		List<JSONObject> query4Json = dao.query4Json(tenantId, sb.toString());
		if (query4Json.size() < 1) {
			result.put("msg", "请查询我的订单");
			return result;
		}
		JSONObject order = query4Json.get(0);
//		// 获取子商户号
//		sb.delete(0, sb.length());
//		sb.append("select * from sys_payment_account_config");
//		sb.append(" where tenancy_id = '" + tenantId
//				+ "' and type=1 and valid_state='1'");
//		sb.append(" order by id");
//		List<JSONObject> query4Json2 = dao.query4Json(tenantId, sb.toString());
//		JSONObject config = query4Json2.size() > 0 ? query4Json2.get(0) : null;
//		if (null == config) {
//			result.put("msg", "商户未设置支付的信息");
//			return result;
//		}
//		String subMchId = config.optString("partner");
		String storeId = order.optString("store_id");
		if(!order.optString("order_state").equals("10")){
			if (order.optString("payment_state").equals("02")
					|| order.optString("payment_state").equals("03")) {
				// 已经支付过的，可以退款
				JSONObject refundOrder = newPaymentService.refundOrder(tenantId,
						storeId, orderCode, order.optDouble("actual_pay", 0),
						null);
				if (!refundOrder.optBoolean("success")) {
					// 退款失败
					result.put("msg", "退款失败");
					return result;
				}
			}
		}else{
			result.put("msg", "订单已完成不能退款");
			return result;
		}
		
		JSONObject order1=new JSONObject();
		// 修改订单支付状态
		order1.put("payment_state", "04");
		order1.put("order_state", "08");
		order1.put("id", order.optInt("id"));
		order1.put("cancellation_time", DateUtil.getNowDateYYDDMMHHMMSS());
		dao.updateIgnorCase(tenantId, "cc_order_list", order1);
		String order_code = order.optString("order_code");
		int store_id = order.optInt("store_id");
		//使用积分的需撤销掉
		List<JSONObject> queryUseCreditByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, order_code,"card_credit");
		if(queryUseCreditByOrder.size()>0){
			result.put("success", false);
			String bill_code = jb.optString("order_code");
			String openid = jb.optString("openid");
			//查询积分
			//有使用积分支付的
			JSONObject jsonObject = queryUseCreditByOrder.get(0);
			//撤销积分消费
			JSONObject queryMemberBaseInfo = newWxMemberDao.queryMemberBaseInfo(tenantId, openid);
			String mobile_num = queryMemberBaseInfo.optString("mobile_num");
			String third_bill_code = jsonObject.optString("third_bill_code");
			com.tzx.crm.bo.dto.Data data1 = new com.tzx.crm.bo.dto.Data();
			data1.setOper(com.tzx.crm.base.constant.Oper.update);
			data1.setStore_id(0);
			data1.setTenancy_id(tenantId);
			JSONObject bean = new JSONObject();
			List<JSONObject> list = new ArrayList<JSONObject>();
			bean.put("mobil", mobile_num);// 电话
			bean.put("chanel", type);// 渠道
			bean.put("operator", "admin");// 操作人员
			bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
			bean.put("old_bill_code", third_bill_code);//交易单号
			bean.put("business_date", DateUtil.getNowDateYYDDMMHHMMSS());
			bean.put("shift_id", "WX");
			bean.put("batch_no", "WX");
			bean.put("bill_code", bill_code);
			list.add(bean);
			data1.setData(list);
			//撤销接口
			try {
				bonusPointManageService.bonusPointConsume(data1);
			} catch (Exception e) {
				e.printStackTrace();
				result.put("msg", e.getMessage());
				result.put("success", false);
				return result;
			}
		}
		//取消时将已经使用的优惠劵退还
		List<JSONObject> queryUseCouponByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, order_code,"coupons");
		if(queryUseCouponByOrder.size()>0){
			//有使用优惠劵
			com.tzx.crm.bo.dto.Data param=new com.tzx.crm.bo.dto.Data();
			param.setOper(Oper.init);
			param.setTenancy_id(tenantId);
			param.setStore_id(store_id);
			List<JSONObject> list = new ArrayList<JSONObject>();
			JSONObject bean=new JSONObject();
			bean.put("chanel", type);
			List<JSONObject> couponList= new ArrayList<JSONObject>();
			for (JSONObject jsonObject : queryUseCouponByOrder) {
				JSONObject bean1=new JSONObject();
				bean1.put("coupons_code", jsonObject.optString("pay_no"));
				couponList.add(bean1);
			}
			bean.put("couponslist", couponList);
			list.add(bean);
			param.setData(list);
			//撤销接口
			try {
				couponsService.coupons(param);
				boolean deleteOrderByOrderCode = newWxOrderDao.deleteOrderByOrderCode(tenantId, "cc_order_repayment", order_code);
				if(!deleteOrderByOrderCode){
					result.put("msg", "亲，撤销票劵失败");
					result.put("success", false);
					return result;
				}
			} catch (Exception e) {
				e.printStackTrace();
				result.put("msg", e.getMessage());
				result.put("success", false);
				return result;
			}
			
		}
		// 下发到门店
		result.put("success", true);
		return result;
	}

	@Override
	public com.tzx.crm.bo.dto.Data surePayOrderByCard(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		jb.put("success", false);
		String type = jb.optString("type");
		String openId = jb.optString("openid");
		String card_code = jb.optString("card_code");
		Double allMoney = jb.optDouble("allPrice", 0.0);
		int store_id = jb.optInt("store_id");
		String pay_password = jb.optString("pay_password");
		String bill_code = jb.optString("order_code");
		StringBuilder sb = new StringBuilder();
		sb.append("select cc.* from (select id,(select count(pwoo.*) from payment_way_of_ogran pwoo where pwoo.organ_id="+store_id+") as z from payment_way where payment_class='card' and status='1') cc where cc.z>0 ");
		List<JSONObject> payCardList = dao.query4Json(tenantId, sb.toString());
		if(payCardList.size()<1){
			jb.put("msg_code", 1);
			jb.put("errmsg", "该店暂不支持卡支付");
			return null;
		}
		sb.setLength(0);
		sb.append("select (case length(pay_password) when 32 then true else false end) as isPassword,pay_password");
		sb.append(" from crm_customer_card");
		sb.append(" where card_code='" + card_code + "'");
		List<JSONObject> query4Json = dao.query4Json(tenantId, sb.toString());
		if (query4Json.size() < 1) {
			jb.put("msg_code", 1);
			jb.put("errmsg", "卡号信息不正确");
			return null;
		}
		JSONObject card = query4Json.get(0);
		boolean isPassword = card.optBoolean("ispassword");
		if (isPassword) {
			String passWord = card.optString("pay_password");
			if (!pay_password.equals(passWord)) {
				jb.put("msg_code", 1);
				jb.put("errmsg", "密码错误，请重新输入");
				return null;
			}
		}
		sb.setLength(0);
		sb.append("select * from cc_order_list where  order_code='" + bill_code
				+ "'");
		List<JSONObject> orderList = dao.query4Json(tenantId, sb.toString());
		if (orderList.size() < 1) {
			// 订单号错误
			jb.put("msg_code", 1);
			jb.put("errmsg", "未获取到订单信息！");
			return null;
		}
		JSONObject order = orderList.get(0);
		double total_money1 = order.optDouble("total_money",0.0);
		sb.delete(0, sb.length());
		// select cci.id,cl.rate from crm_customer_info cci LEFT JOIN crm_level
		// cl on cci.level= cl.id where cci.mobil='" + phone + "'
		sb.append("select cci.id,cl.rate from crm_customer_info cci LEFT JOIN crm_level cl on cci.level= cl.id LEFT JOIN wx_member wm on cci.mobil=wm.mobile_num where wm.openid='"
				+ openId + "' and wm.subscribe=true ");
		List<JSONObject> memberList = dao.query4Json(tenantId, sb.toString());
		if(memberList.size()<1){
			jb.put("msg_code", 1);
			jb.put("errmsg", "请注册成为会员！");
			return null;
		}
		JSONObject member=memberList.get(0);
		
		if (allMoney < 0.01) {
			// 直接付款成功(修改订单状态，并下发)
			// 0元的处理(会员的折扣)
			double discount_amount = 0;
			// 实际支付金额
			// double actual_pay =
			order.optDouble("actual_pay");// result.optString("total_amount")
			double actual_pay = allMoney;
			JSONObject order1=new JSONObject();
			order1.put("discount_amount", discount_amount);
			// order.put("discount_mode_id", 5);
			// 抹零金额
			order1.put("maling_amount", jb.optDouble("retain_money",0.0));
			order1.put("actual_pay", actual_pay);
			order1.put("shop_fee", actual_pay);
			// 修改订单支付状态
			order1.put("payment_state", "03");
			// 设置是否是线上支付
			order1.put("is_online_payment", "1");
			order1.put("id", order.optInt("id"));
			dao.updateIgnorCase(tenantId, "cc_order_list", order1);
			jb.put("success", true);
			jb.put("surePay", true);
			jb.put("bill_num", bill_code);
			// 下发
			Data data = new Data();
			data.setTenancy_id(tenantId);
			List<JSONObject> list = new ArrayList<JSONObject>();
			JSONObject param = new JSONObject();
			param.put("order_code", bill_code);
			param.put("tenentid", tenantId);
			list.add(param);
			data.setData(list);
			jb = orderManagerService.orderIssued(data);
			jb.put("orderId", order.optInt("id"));
			jb.put("bill_code", bill_code);
			String nowDateYYDDMMHHMMSS = DateUtil.getNowDateYYDDMMHHMMSS();
			String[] split = nowDateYYDDMMHHMMSS.split(":");
			String time=split[0]+":"+split[1];
			jb.put("exchangeTime", time);
			sendTemplatMsg(tenantId, openId, bill_code);
			jb.put("success", true);
			return null;
		}
		//判断该订单是否已经支付了
		JSONObject queryOrderStateByCard = newWxOrderDao.queryOrderStateByCard(tenantId, card_code, bill_code);
		if(null!=queryOrderStateByCard){
			String operat_type = queryOrderStateByCard.optString("operat_type");
			if(operat_type.equals("03")){
				//已经支付的
				String order_state = order.optString("order_state");
				String payment_state = order.optString("payment_state");
				if(order_state.equals("01")&&payment_state.equals("01")){
					//修改订单状态
					JSONObject bean=new JSONObject();
					JSONObject paymentWay = payCardList.get(0);
					bean.put("payment_id", paymentWay.optInt("id"));
					bean.put("order_code", bill_code);
					bean.put("pay_money", queryOrderStateByCard.optDouble("bill_amount",0.0));
					bean.put("pay_no", card_code);
					bean.put("third_bill_code", queryOrderStateByCard.optString("bill_code"));
					bean.put("store_id", store_id);
					bean.put("tenancy_id", tenantId);
					dao.insertIgnorCase(tenantId, "cc_order_repayment", bean);
					List<JSONObject> queryPaymentWay = newWxOrderDao.queryPaymentWay(tenantId, bill_code);
					double actual_pay=0.0;
					for (JSONObject pamentWay : queryPaymentWay) {
						double pay_money = pamentWay.optDouble("pay_money",0.0);
						actual_pay+=pay_money;
					}
					double total_money = order.optDouble("total_money",0.0);
					double maling_amount=total_money-actual_pay;
					JSONObject order1=new JSONObject();
					order1.put("discount_amount", 0);
					// order.put("discount_mode_id", 5);
					// 抹零金额
					order1.put("maling_amount", maling_amount);
					order1.put("actual_pay", actual_pay);
					order1.put("shop_fee", actual_pay);
					// 修改订单支付状态
					order1.put("payment_state", "03");
					// 设置是否是线上支付
					order1.put("is_online_payment", "1");
					order1.put("id", order.optInt("id"));
					dao.updateIgnorCase(tenantId, "cc_order_list", order1);
					// 下发
					Data data1 = new Data();
					data1.setTenancy_id(tenantId);
					List<JSONObject> list2 = new ArrayList<JSONObject>();
					JSONObject param = new JSONObject();
					param.put("order_code", order.optString("order_code"));
					param.put("tenentid", tenantId);
					list2.add(param);
					data1.setData(list2);
					JSONObject orderIssued = orderManagerService.orderIssued(data1);
					// 成功后更改订单状态，并下发订单
					sendTemplatMsg(tenantId, openId, bill_code);
//					JSONObject bean1=new JSONObject();
//					bean1.put("order_code", bill_code);
//					bean1.put("openid", openId);
//					bean1.put("tenantId", tenantId);
//					bean1.put("type1", "orderCancel");//卡充值
//					bean1.put("type", type);//卡充值
//					bean1.put("discount_amount",  order.optDouble("actual_pay",0.0));
//					bean1.put("payment", "card");
//					int count=20;
//					List<JSONObject> queryWXSystemParameterByParaCode = newWxSystemParameterDao.queryWXSystemParameterByParaCode(tenantId, "order_cancel_time");
//					if(queryWXSystemParameterByParaCode.size()>0){
//						JSONObject jsonObject2 = queryWXSystemParameterByParaCode.get(0);
//						count = jsonObject2.optInt("para_value");
//					}
//					bean1.put("LongTime", System.currentTimeMillis()+count*60*1000);
//					bean1.put("oper", 0);
//					taskRedisDao.lpush("wxpaymentbillnum".getBytes(), bean1);
					return null;
				}
			}else if(operat_type.equals("05")){
				//已经撤销的(做退款)
				List<JSONObject> queryPaymentWay = newWxOrderDao.queryPaymentWay(tenantId, bill_code);
				if(queryPaymentWay.size()<1){
					//已经退完款了
					return null;
				}
				for (JSONObject paymentWay : queryPaymentWay) {
					String payment_class = paymentWay.optString("payment_class");
					if(payment_class.equals("card_credit")){
						//积分抵现
						//查询积分
						//有使用积分支付的
						JSONObject jsonObject = paymentWay;
						//撤销积分消费
						JSONObject queryMemberBaseInfo = newWxMemberDao.queryMemberBaseInfo(tenantId, openId);
						String mobile_num = queryMemberBaseInfo.optString("mobile_num");
						String third_bill_code = jsonObject.optString("third_bill_code");
						com.tzx.crm.bo.dto.Data data1 = new com.tzx.crm.bo.dto.Data();
						data1.setOper(com.tzx.crm.base.constant.Oper.update);
						data1.setStore_id(store_id);
						data1.setTenancy_id(tenantId);
						JSONObject bean = new JSONObject();
						List<JSONObject> list = new ArrayList<JSONObject>();
						bean.put("mobil", mobile_num);// 电话
						bean.put("chanel", type);// 渠道
						bean.put("operator", "admin");// 操作人员
						bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
						bean.put("old_bill_code", third_bill_code);//交易单号
						bean.put("business_date", DateUtil.getNowDateYYDDMMHHMMSS());
						bean.put("shift_id", "WX");
						bean.put("batch_no", "WX");
						bean.put("bill_code", bill_code);
						list.add(bean);
						data1.setData(list);
						//撤销接口
						try {
							bonusPointManageService.bonusPointConsume(data1);
						} catch (Exception e) {
							e.printStackTrace();
							jb.put("msg", e.getMessage());
							jb.put("success", false);
							return null;
						}
					}
					if(payment_class.equals("coupons")){
						//优惠劵支付
						//优惠劵
						List<JSONObject> queryUseCouponByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, bill_code,"coupons");
						if(queryUseCouponByOrder.size()>0){
							//有使用优惠劵
							com.tzx.crm.bo.dto.Data param=new com.tzx.crm.bo.dto.Data();
							param.setOper(Oper.init);
							param.setTenancy_id(tenantId);
							
							List<JSONObject> list = new ArrayList<JSONObject>();
							JSONObject bean=new JSONObject();
							bean.put("chanel", type);
							List<JSONObject> couponList= new ArrayList<JSONObject>();
							for (JSONObject jsonObject : queryUseCouponByOrder) {
								JSONObject bean1=new JSONObject();
								store_id = jsonObject.optInt("store_id");
								bean1.put("coupons_code", jsonObject.optString("pay_no"));
								couponList.add(bean1);
							}
							param.setStore_id(store_id);
							bean.put("couponslist", couponList);
							list.add(bean);
							param.setData(list);
							//撤销接口
							try {
								couponsService.coupons(param);
							} catch (Exception e) {
								e.printStackTrace();
								jb.put("msg", e.getMessage());
								jb.put("success", false);
								return null;
							}
						}
					}
				}
				//newWxOrderDao.deleteOrderByOrderCode(tenantId, "cc_order_repayment", bill_code);
				jb.put("success", true);
				return null;
			}
		}
		// 计算抹零金额
		// 获取保留金额尾数
		double retainNum = 0;
		int retainOrder = 2;
		int retainDish = 2;
		sb.delete(0, sb.length());
		List<JSONObject> retainList = newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, store_id);
		if(retainList.size()==0){
			retainList=newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, 0);
		}
//		sb.append("select para_name,para_code,para_value from sys_parameter where valid_state='1' and  para_code in ('CMJEWS','ZDJEWS')");
//		List<JSONObject> retainList = dao.query4Json(tenantId, sb.toString());
		if (retainList != null && retainList.size() > 0) {
			for (JSONObject reatin : retainList) {
				if ("ZDJEWS".equals(reatin.optString("para_code"))) {
					retainOrder = reatin.optInt("para_value");
				} else if ("CMJEWS".equals(reatin.optString("para_code"))) {
					retainDish = reatin.optInt("para_value");
				}
			}
		}
		// 是否包含会员卡消费，如果有的话，不计算会员折扣
		NumberFormat nf = NumberFormat.getNumberInstance();
		nf.setMaximumFractionDigits(retainOrder);
		double price = allMoney;// 支付金额
		double retainMoney = Double.valueOf(nf.format(price).replace(",", ""));
		// retainNum = (float) (price - retainMoney);
		retainNum = retainMoney - price;
		price = price + retainNum;
//		float retainMoney = Float.parseFloat(nf.format(price));
//		// retainNum = (float) (price - retainMoney);
//		retainNum = (float) (retainMoney - price);
//		nf.setMaximumFractionDigits(2);
//		price = Float.parseFloat(nf.format(price + retainNum));
		jb.put("retain_money", retainNum);
		jb.put("retain_order", retainOrder);
		jb.put("retain_dish", retainDish);
		// 都没选择抵扣，则直接跳过
		jb.put("total_money", allMoney);
		jb.put("actual_pay", price);
		jb.put("shop_fee", price);
		// 订单号
		String order_num = order.getString("order_code");
		JSONObject reObject = new JSONObject();
		if (price <= 0) {
			// 0元的处理
			// 实际支付金额
			double actual_pay = 0;
			// 抹零金额
			JSONObject order1=new JSONObject();
			order1.put("maling_amount", jb.optDouble("retain_money",0.0));
			order1.put("actual_pay", actual_pay);
			order1.put("shop_fee", actual_pay);
			// 修改订单支付状态
			order1.put("payment_state", "03");
			// 设置是否是线上支付
			order1.put("is_online_payment", "1");
			order1.put("id", order.optInt("id"));
			dao.updateIgnorCase(tenantId, "cc_order_list", order1);
			reObject.put("success", true);
			reObject.put("surePay", true);
			reObject.put("bill_num", order_num);
			// 下发
			Data data = new Data();
			data.setTenancy_id(tenantId);
			List<JSONObject> list = new ArrayList<JSONObject>();
			JSONObject param = new JSONObject();
			param.put("order_code", order_num);
			param.put("tenentid", tenantId);
			list.add(param);
			data.setData(list);
			orderManagerService.orderIssued(data);
			sb.delete(0, sb.length());
			sendTemplatMsg(tenantId, openId, order_num);
			jb.put("orderId", order.optInt("id"));
			jb.put("bill_code", bill_code);
			String nowDateYYDDMMHHMMSS = DateUtil.getNowDateYYDDMMHHMMSS();
			String[] split = nowDateYYDDMMHHMMSS.split(":");
			String time=split[0]+":"+split[1];
			jb.put("exchangeTime", time);
			jb.put("success", true);
		} else{
		// 获取用户的信息，包括积分、卡劵、会员卡信息
		// 用户表内没有手机号 则为未绑定用户 不用再去查询
		sb.delete(0, sb.length());
		// 查询出所有的优惠劵（该用户相关的）（调接口）
		sb.append("select * from crm_activity_wxcupous");
		List<JSONObject> couponList = dao.query4Json(tenantId, sb.toString());
		// if(){}
		// 获取优惠折扣价格
		int rate = member.optInt("rate") == 0 ? 100 : member.optInt("rate");
		// 打折金额只按菜品计算，不计算服务费、外送费等信息
		sb.delete(0, sb.length());
		sb.append("SELECT b.*,info.item_name,info.photo1,info.is_discount from  cc_order_item b");
		sb.append(" left join hq_item_info info on b.item_id=info.id where  b.order_code= '"
				+ bill_code + "'");
		List<JSONObject> order_item_list = dao.query4Json(tenantId,
				sb.toString());
		// 获取菜品口味
		sb.delete(0, sb.length());
		sb.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '"
				+ bill_code + "'");
		List<JSONObject> cc_order_item_taste_list = dao.query4Json(tenantId,
				sb.toString());

		float total = 0;
		if (order_item_list != null && order_item_list.size() > 0) {
			for (JSONObject bean1 : order_item_list) {
				if (bean1.optString("is_discount").equals("Y")) {
					if (cc_order_item_taste_list != null
							&& cc_order_item_taste_list.size() > 0) {
						for (JSONObject taste : cc_order_item_taste_list) {
							if (bean1.optInt("item_id") == taste
									.optInt("item_id")
									&& bean1.optInt("group_index") == taste
											.optInt("group_index")) {
								total += (bean1.optDouble("price") + taste
										.optDouble("proportion_money"))
										* bean1.optInt("number");
							}
						}
					} else {
						total += bean1.optDouble("price")
								* bean1.optInt("number");
					}
				}
			}
		}
		
		Double consume_creditmoney=price;//可积分金额
		
		try{
			String countsql = "select sum(cor.pay_money) sum from cc_order_repayment cor "
					+ " LEFT JOIN payment_way pw on cor.payment_id=pw.id "
					+ " where cor.order_code ='" + bill_code + "'"
					+ " and pw.payment_class in ('coupons')"
					+ " and pw.status='1'";
			List<JSONObject> list2 = dao.query4Json(tenantId, countsql);
			double money = list2.get(0).optDouble("sum");
			if(money > 0.0){
			JSONObject couponswechatWay = null;
			sb.setLength(0);
			sb.append("select  pw.* from payment_way_of_ogran po  left join payment_way pw on po.payment_id=pw.id ");
			sb.append(" where organ_id='"+store_id+"' and payment_class='coupons' and pw.status='1' ");
			List<JSONObject> query41Json = dao.query4Json(tenantId, sb.toString());
			couponswechatWay = query41Json.size() > 0 ? query41Json.get(0) : null;
			if(couponswechatWay!=null){
				String if_jifen=couponswechatWay.optString("if_jifen");
				if("".equals(if_jifen)||if_jifen==null||"0".equals(if_jifen)){
					//如果优惠券付款方式是否积分字段为空或null或不允许积分，则不处理
				}else{
					consume_creditmoney+=money;
				}
			}
		 }

		}catch(Exception ex){
			System.out.println(bill_code+"订单处理可积积分时发生错误");
			ex.printStackTrace();
		}
		
		// 分装参数体
		com.tzx.crm.bo.dto.Data data = new com.tzx.crm.bo.dto.Data();
		data.setOper(com.tzx.crm.base.constant.Oper.add);
		data.setStore_id(store_id);
		data.setTenancy_id(tenantId);
		JSONObject bean = new JSONObject();
		List<JSONObject> list = new ArrayList<JSONObject>();
		bean.put("card_code", card_code);// 卡号
		bean.put("consume_totalmoney", total_money1);// 账单金额
		bean.put("consume_cardmoney", price);// 卡消费金额
		bean.put("consume_creditmoney", Double.valueOf(nf.format(consume_creditmoney).replace(",", "")));// 可积分金额
		bean.put("consume_credit", 0);// 使用积分
		
		bean.put("chanel", type);// 渠道
		bean.put("operator", "admin");// 操作人员
		bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
		bean.put("cardpassword", pay_password);
		bean.put("bill_code", bill_code);
		list.add(bean);
		data.setData(list);
		return data;
	}
		return null;
	}

	public void sendTemplatMsg(String tenantId, String openId, String orderCode) {
		try {
			StringBuilder sb = new StringBuilder();
			sb.append("select * from wx_template_relation where remark = '下单成功后推送的消息'");
			List<JSONObject> query4Json3 = dao.query4Json(tenantId,
					sb.toString());
			if (query4Json3.size() > 0) {
				sb.delete(0, sb.length());
				sb.append("select * from cc_order_list where order_code='"
						+ orderCode + "'");

				List<JSONObject> query4Json5 = dao.query4Json(tenantId,
						sb.toString());
				if (query4Json5.size() > 0) {
					JSONObject template = query4Json3.get(0);
					// 推送消息模板
					TemplateMessage message = new TemplateMessage();
					// 消息模板id
					// message.setTemplate_id("TFErs_zCBEns_QE1BIQLZN43KpkxbELoSNIbv9SxBp4");
					message.setTemplate_id(template.optString("template_id"));
					// 发送对象openID
					message.setTouser(openId);
					String url = template.optString("url");
					String orderId = query4Json5.get(0).optString("id");
					url = url + "&openId=" + openId+"&fromtype=1";
					message.setUrl(url);
					message.setTopcolor("#3d3d3d");
					String take_meal_number = query4Json5.get(0).optString("take_meal_number");//
					String chanel = query4Json5.get(0).optString("chanel");
					
					if(take_meal_number.equals("null")||take_meal_number.equals("")){
						take_meal_number="W"+orderCode.substring(orderCode.length()-2,orderCode.length());
					}
					LinkedHashMap<String, TemplateMessageItem> data1 = new LinkedHashMap<String, TemplateMessageItem>();//
					String first1="";
					if(chanel.equals("WX02")){
						first1="我们已经收到您的订单，请联系服务员取单\n取餐号："+take_meal_number;
					}else{
						first1="我们已经收到您的订单，请联系服务员取单";
					}
					TemplateMessageItem first = new TemplateMessageItem(
							first1, "#3d3d3d");
					data1.put("first", first);
					// 商户号名称
					sb.setLength(0);
					String storeId = query4Json5.get(0).optString("store_id");
					sb.append("select * from organ where id=" + storeId);
					List<JSONObject> query4Json4 = dao.query4Json(tenantId,
							sb.toString());
					TemplateMessageItem keyword1 = new TemplateMessageItem(
							query4Json4.get(0).optString("org_full_name"),
							"#3d3d3d");
					data1.put("keyword1", keyword1);
					// 就餐桌号：1号桌
					TemplateMessageItem keyword2 =null;
					
					//改： 加品牌 查询系统参数     ---wzx
					boolean isSetTableCode =false;
					JSONObject wechat_table_code = baseInfoDao.querySystemParameterByParaCode(tenantId, storeId, "wechat_table_code");
					if(wechat_table_code!=null){
						if(wechat_table_code.optInt("para_value")==1){
							isSetTableCode=true;
						}
					}
					//--wzx
					
					/*boolean isSetTableCode = newWxOrderDao.queryIsSetTableCode(tenantId);*/
					String optString = query4Json5.get(0).optString("table_name");
					if(isSetTableCode&&!optString.equals("null")&&!optString.equals("")){
						keyword2 = new TemplateMessageItem(
								query4Json5.get(0).optString("table_name"),
								"#3d3d3d");
					}else{
						keyword2 = new TemplateMessageItem("-","#3d3d3d");
					}
					
					data1.put("keyword2", keyword2);
					// 账单金额：100元
					TemplateMessageItem keyword3 = new TemplateMessageItem("￥"
							+ query4Json5.get(0).optDouble("actual_pay"),
							"#3d3d3d");
					data1.put("keyword3", keyword3);
					TemplateMessageItem remark = new TemplateMessageItem(
							"如有疑问致电商家客服", "#3d3d3d");
					data1.put("remark", remark);
					message.setData(data1);
					String access_token = null;
//					Boolean wechatAuthorize = FromRedisUtil
//							.getWechatAuthorize(tenantId);
//					if (wechatAuthorize) {
						access_token = WXUtil.getAccessToken(tenantId, "");
//					} else {
//						access_token = WXThridUtil.getAccessToken(tenantId);
//					}
					WechatMessageUtil
							.messageTemplateSend(access_token, message);
				}

			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public JSONObject placeOrderNotMember(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		// 下单接口
		Data data = new Data();
		JSONObject result = new JSONObject();
		StringBuilder sb = new StringBuilder();
		String orderCode = "";
		// 是否是先付
		boolean isPrepayment = false;
		
		//改： 加品牌 查询系统参数     ---wzx
		JSONObject wechat_payment_type = baseInfoDao.querySystemParameterByParaCode(tenantId, jb.optString("store_id","0"), "wechat_payment_type");
		if(wechat_payment_type==null){
			result.put("errorMsg", "下单失败");
			result.put("errCode", "2");
			return result;
		}
		//--wzx
		
		/*sb.append("select * from sys_parameter where para_code='wechat_payment_type' and store_id=0");
		List<JSONObject> query4Json6 = dao.query4Json(tenantId, sb.toString());
		if (query4Json6.size() < 1) {
			result.put("errorMsg", "下单失败");
			result.put("errCode", "2");
			return result;
		}
		JSONObject wechat_payment_type = query4Json6.get(0);*/
		String para_value = wechat_payment_type.optString("para_value");
		if (para_value.equals("0")) {
			// 后付
			isPrepayment = false;
		} else if (para_value.equals("1")) {
			// 先付
			isPrepayment = true;
		}
		// isPrepayment=true;
		sb.delete(0, sb.length());
		JSONObject order_list = new JSONObject();
		JSONObject order_list1 = new JSONObject();
		JSONArray order_item = new JSONArray();
		JSONArray order_item1 = new JSONArray();
		if (!"null".equals(jb.optString("order_list"))) {
			// data.set
			order_list = JSONObject.fromObject(jb.optString("order_list"));
		}
		double product_org_total_fee=order_list.optDouble("total_money",0);
		JSONArray newOrderItem3 = new JSONArray();
		if(!"null".equals(jb.optJSONArray("order_item"))){
			newOrderItem3=jb.optJSONArray("order_item");
		}
		double allPrice1=0.0;
		for(int i=0;i<newOrderItem3.size();i++){
			JSONObject order_item3= (JSONObject) newOrderItem3.get(i);
			int number = order_item3.optInt("number");
			double price = order_item3.optDouble("price",0.0);
			allPrice1+=(number*price);
		}
		NumberFormat nf = NumberFormat.getNumberInstance();
		nf.setMaximumFractionDigits(2);
		float allPrice3 = Float.parseFloat(nf.format(allPrice1).replace(",", ""));
		double allPrice2 = order_list.optDouble("total_money",0.0);
		float allPrice4 = Float.parseFloat(nf.format(allPrice2).replace(",", ""));
		if(allPrice4!= allPrice3){
			result.put("errorMsg", "请重新下单");
			result.put("errCode", "3");
			result.put("allPrice2", allPrice2);
			result.put("allPrice1", allPrice1);
			return result;
		}
		// 判断是否是粉丝
		String openid = jb.optString("openid");
		if ("".equals(openid) || "null".equals(openid)) {
			result.put("errorMsg", "请关注公众号在下单");
			result.put("errCode", "1");
			return result;
		}
		StringBuilder sqlOpenId = new StringBuilder(
				"select * from wx_member where openid='");
		sqlOpenId.append(openid + "' and subscribe='t'");
		List<JSONObject> memberList = dao.query4Json(tenantId,
				sqlOpenId.toString());
		if (memberList.size() < 1) {
			result.put("errorMsg", "请关注公众号在下单");
			result.put("errCode", "1");
			return result;
		}
		JSONObject member = memberList.get(0);
		StringBuilder sb3 = new StringBuilder("select * from organ where id=");
		sb3.append(jb.optString("organ_id"));
		List<JSONObject> query4Json4 = dao.query4Json(tenantId, sb3.toString());
		if (query4Json4.size() < 1) {
			result.put("errorMsg", "请返回上一级选择门店");
			result.put("errCode", "0");
			return result;
		}
		//判断下单的类型
		String type = jb.optString("type");
		JSONObject jsonObject2 = query4Json4.get(0);
		int is_wechatdddc = jsonObject2.optInt("is_wechatdddc");
		if(is_wechatdddc==0&&type.equals("WX02")){
			result.put("errorMsg", "该门店暂不支持微信店内点餐");
			result.put("errCode", "0");
			return result;
		}
		// logger.info(jb);
		
		// 定位
		if(type.equals("WX02")){
			if ("null".equals(jb.optString("lon"))
					|| "".equals(jb.optString("lon"))
					|| "null".equals(jb.optString("lot"))
					|| "".equals(jb.optString("lot"))) {
				if(!isPrepayment){
					//后付需要在门店端
					result.put("errorMsg", "正在定位，请再次点击确认下单");
					result.put("errCode", "0");
					return result;
				}
			}else{
				if ("null".equals(jb.optString("organ_id"))) {
					result.put("errorMsg", "请选择门店");
					result.put("errCode", "0");
					return result;
				}
				Gps gps = MapUtil.gcj02_To_Bd09(
						Double.parseDouble(jb.optString("lot")),
						Double.parseDouble(jb.optString("lon")));
				Double bd_lat = gps.getWgLat();
				Double bd_lon = gps.getWgLon();
				// 判断距离longitude,latitude
				String temp_lon = jsonObject2.optString("longitude");
				String temp_lat = jsonObject2.optString("latitude");
				if (MyUtil.checkEmpty(temp_lon) && MyUtil.checkEmpty(temp_lat)
						&& !"null".equals(temp_lon) && !"null".equals(temp_lat)) {
					// lon,lat
					double tar_lon = jsonObject2.optDouble("longitude", 0.0);
					double tar_lat = jsonObject2.optDouble("latitude", 0.0);
					// 获取距离
					double distance = MapUtil.getDistance(bd_lon, bd_lat, tar_lon,
							tar_lat);
					// 两千米就不能下单
					if (distance > 20000) {
						result.put("errorMsg", "距离太远不能下单!");
						result.put("errCode", "0");
						return result;
					}
				} else {
					if(!isPrepayment){
						result.put("errorMsg", "距离太远不能下单");
						result.put("errCode", "0");
						return result;
					}
				}
			}
		}
		// String
		// sql1="select * from wx_member where openid='"+openid+"' and tenancy_id='"+tenantId+"'";
		// List<JSONObject> query4Json = dao.query4Json(tenantId, sql1);
		// if(query4Json.size()<1){
		// result.put("errorMsg", "请关注公众号在下单");
		// result.put("errCode", "1");
		// return result;
		// }
		// 判断座位号是否正确
		boolean isTableCode = false;
		/*isTableCode= newWxOrderDao.queryIsSetTableCode(tenantId);*/
		String organId = jb.optString("organ_id");
		if ("null".equals(organId)) {
			result.put("errorMsg", "请返回后选择门店");
			result.put("errCode", "0");
			return result;
		}
		String tableCode = jb.optString("tableCode");
		
		//改： 加品牌 查询系统参数     ---wzx
		JSONObject wechat_table_code = baseInfoDao.querySystemParameterByParaCode(tenantId, organId, "wechat_table_code");
		if(wechat_table_code!=null){
			if(wechat_table_code.optInt("para_value")==1){
				isTableCode=true;
			}
		}
		//--wzx
		
		if(isTableCode&&type.equals("WX02")){
			if ("null".equals(tableCode)) {
				result.put("errorMsg", "请先扫码，再下单");
				result.put("errCode", "0");
				return result;
			}
			
			// 查询出座位号是否正确
			String sql2 = "select table_code,table_name from tables_info where organ_id="
					+ organId
					+ " and valid_state='1' and table_code='"
					+ tableCode
					+ "'";
			List<JSONObject> query4Json2 = dao.query4Json(tenantId, sql2);
			if (query4Json2.size() < 1) {
				result.put("errorMsg", "座位号错误" + tableCode + "，请扫描桌位上的二维码");
				result.put("errCode", "0");
				return result;
			}
			sb.delete(0, sb.length());
			// sb.append("select lineup_starttime,lineup_endtime from hq_lineup_time_org where store_id="+organId+" and valid_state='1'");
			sb.append("select aa.* from (select (select * from to_char(now(),'hh24:mi')) as time1,lineup_starttime,lineup_endtime");
			sb.append(" from hq_lineup_time_org");
			sb.append(" where store_id=" + organId + " and valid_state='1' ) aa");
			sb.append(" where aa.time1 BETWEEN aa.lineup_starttime and aa.lineup_endtime");
			List<JSONObject> query4Json7 = dao.query4Json(tenantId, sb.toString());
			if (query4Json7.size() < 1) {
				result.put("errorMsg", "用餐时间不在营业时间之内！");
				result.put("errCode", "0");
				return result;
			}
			JSONObject jsonObject = query4Json2.get(0);
			String table_name = jsonObject.optString("table_name");
			order_list.put("table_name", table_name);
		}
		if (!isPrepayment&&type.equals("WX02")) {
			result.put("errorMsg", "请注册成为会员！");
			result.put("errCode", "0");
			return result;
		}
		sb.delete(0, sb.length());
		// sb.append("select lineup_starttime,lineup_endtime from hq_lineup_time_org where store_id="+organId+" and valid_state='1'");
		sb.append("select aa.* from (select (select * from to_char(now(),'hh24:mi')) as time1,lineup_starttime,lineup_endtime");
		sb.append(" from hq_lineup_time_org");
		sb.append(" where store_id=" + organId + " and valid_state='1' ) aa");
		sb.append(" where aa.time1 BETWEEN aa.lineup_starttime and aa.lineup_endtime");
		List<JSONObject> query4Json7 = dao.query4Json(tenantId, sb.toString());
		if (query4Json7.size() < 1) {
			result.put("errorMsg", "用餐时间不在营业时间之内！");
			result.put("errCode", "0");
			return result;
		}
		String total_money = order_list.optString("total_money");
		if ("null".equals(total_money) || total_money.equals("0.00")
				|| total_money.equals("0.0") || total_money.equals("0")) {
			result.put("errorMsg", "亲，下单时金额要大于0哦");
			result.put("errCode", "0");
			return result;
		}
		// 会员卡
		// String memberCard = jb.optString("memberCard");

		// data.setType(Type.ORDER);
		// data.setOper(Oper.add);
		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(jb);
		// 关键信息 order_list{order_type：'',order_item:[]}

		// 下单的时间
		String hq_service_time = DateUtil.format(new Timestamp(System
				.currentTimeMillis()));
		if (jb.optString("dispatch_time").equalsIgnoreCase("")
				|| jb.optString("dispatch_time").equalsIgnoreCase("null")) {
			jb.put("dispatch_time", hq_service_time);
			jb.put("receive_time_dispatch", hq_service_time);
		}
		List<JSONObject> afterAddOrder = new ArrayList<JSONObject>();
		StringBuilder sbl = new StringBuilder();
		if (!isPrepayment&&type.equals("WX02")) {
			result.put("errorMsg", "请注册成为会员！");
			result.put("errCode", "0");
			return result;
		}

		if (afterAddOrder.size() > 0&&type.equals("WX02")) {
			// 加菜的要将之前下的所有的菜品都添加进去，同时将以前的服务费给减掉
			// meal_costs
			JSONObject oldOrder = afterAddOrder.get(0);
			double meal_costs = oldOrder.optDouble("meal_costs", 0);
			double old_total_money = oldOrder.optDouble("total_money", 0);
			if (old_total_money != 0) {
				old_total_money = old_total_money - meal_costs;
			}
			order_list.put("total_money", order_list.optDouble("total_money")
					+ old_total_money);

			// order_list1.put("order_code", oldOrder.optString("order_code"));
			orderCode = oldOrder.optString("order_code");
			order_list.put("order_code", oldOrder.optString("order_code"));
			sbl.delete(0, sbl.length());
			sbl.append("select * from cc_order_item where order_code='"
					+ oldOrder.optString("order_code") + "'");
			List<JSONObject> old_order_item = dao.query4Json(tenantId,
					sbl.toString());
			JSONArray newOrderItem = jb.optJSONArray("order_item");
			JSONArray newOrderItemTaste = jb.optJSONArray("order_item_taste");
			JSONArray newOrderItemDetails = jb.optJSONArray("order_item_details");
			// System.out.println(newOrderItem);
			// order_item1=newOrderItem;
			int group_index = 1;
			for (int i = 0; i < old_order_item.size(); i++) {
				order_item1.add(old_order_item.get(i));
				if (group_index <= old_order_item.get(i).optInt("group_index")) {
					group_index = old_order_item.get(i).optInt("group_index");
				}
			}
			JSONArray newOrderItem1 = new JSONArray();
			JSONArray newOrderItemTaste1 = new JSONArray();
			JSONArray newOrderItemDetails1 = new JSONArray();
			for (int i = 0; i < newOrderItem.size(); i++) {
				group_index++;
				JSONObject object = (JSONObject) newOrderItem.get(i);
				int item_id = object.optInt("item_id");
				int unit_id = object.optInt("unit_id");
				for (int j = 0; j < newOrderItemTaste.size(); j++) {
					JSONObject object2 = (JSONObject) newOrderItemTaste.get(j);
//					int item_id1 = object2.optInt("item_id");
					String orderItemId = object2.optString("orderItemId");
					String itemid_unitid=item_id+"_"+unit_id;
					if(newOrderItemDetails.size()>0){
						//是套餐
						int optInt = object.optInt("k");
						itemid_unitid+=""+optInt;
					}
					if(itemid_unitid.equals(orderItemId)){
						object2.put("group_index", group_index);
						object2.put("is_add_dish", "1");
						newOrderItemTaste1.add(object2);
					}
				}
				for (int j = 0; j < newOrderItemDetails.size(); j++) {
					JSONObject object2 = (JSONObject) newOrderItemDetails.get(j);
					String orderItemId = object2.optString("orderItemId");
					String itemid_unitid=item_id+"_"+unit_id;
					if(itemid_unitid.equals(orderItemId)){
						object2.put("group_index", group_index);
						object2.put("is_add_dish", "1");
						newOrderItemDetails1.add(object2);
					}
				}
				object.put("group_index", group_index);
				object.put("is_add_dish", "1");
				order_item1.add(object);
				newOrderItem1.add(object);
			}
			jb.put("order_item", newOrderItem1);
			jb.put("order_item_taste", newOrderItemTaste1);
			jb.put("order_item_details", newOrderItemDetails1);
			// System.out.println(jb.optJSONArray("order_item"));
		}

		// 服务费用的查询处理（不管是加菜还是新下都应该查询服务费）
		StringBuilder addpriceSql = new StringBuilder();
		if (order_list.optString("order_type").equals("DN03")&&isTableCode&&type.equals("WX02")) {
			// 到店点餐的服务费信息处理
			addpriceSql = new StringBuilder(
					"select fee.* from hq_service_fee_type fee");
			addpriceSql
					.append(" left join tables_info info on fee.id=info.fwfz_id");
			addpriceSql
					.append(" where info.table_code='"
							+ order_list.optString("table_code")
							+ "' and info.organ_id="
							+ order_list.optString("store_id"));
		}
		if (addpriceSql.length() > 0) {
			List<JSONObject> addPriceList = dao.query4Json(tenantId,
					addpriceSql.toString());
			if (addPriceList != null && addPriceList.size() > 0) {
				JSONObject bean = addPriceList.get(0);
				// 根据类型获取配送费用为多少
				double additional = 0;
				if ("GD01".equals(bean.optString("taken_mode"))) {
					// 固定金额
					additional = bean.optDouble("guding_jj");
				} else if ("FS03".equals(bean.optString("taken_mode"))) {
					// 按份数
					// JSONArray order_item_arr =
					// jb.optJSONArray("order_item");//order_item1
					JSONArray order_item_arr = order_item1;
					int dishNumber = 0;
					for (int i = 0; i < order_item_arr.size(); i++) {
						dishNumber += order_item_arr.getJSONObject(i).optInt(
								"number");
					}
					additional = bean.optDouble("guding_jj") * dishNumber;
				} else {
					// 账单比例
					additional = order_list.optDouble("total_money")
							* bean.optDouble("fwfl") * 0.01;
				}
				order_list.put("meal_costs", additional);
				order_list.put("total_money",
						order_list.optDouble("total_money") + additional);
				order_list.put("actual_pay",
						order_list.optDouble("total_money"));
				order_list.put("shop_fee",
						order_list.optDouble("total_money"));
				if (order_list.optString("order_type").equals("WM02")) {
					order_list.put("service_id", bean.optString("service_id"));
					order_list.put("meals_id", bean.optInt("id"));
				} else if (order_list.optString("order_type").equals("DN03")) {
					 order_list.put("service_id", bean.optInt("id"));
				}
				// jb.put("order_list", order_list);
			}
		}
		String checkTable = jb.optString("checkTable");
		if(checkTable.equals("1")&&isTableCode&&type.equals("WX02")){
			JSONArray order_item_arr = jb.optJSONArray("order_item");
			//必点菜和禁点菜
			List<JSONObject> queryMustDishAndNotDish = newWxOrderDao.queryMustDishAndNotDish(tenantId, tableCode, organId);
			if(queryMustDishAndNotDish.size()>0){
				for(int j=0;j<queryMustDishAndNotDish.size();j++){
					JSONObject dish = queryMustDishAndNotDish.get(j);
					int z=0;
					for (int i = 0; i < order_item_arr.size(); i++) {
						JSONObject item = (JSONObject) order_item_arr.get(i);
						if(dish.optString("dishes_sign").equals("0")){
							//禁点菜
							if(dish.optString("item_id").equals(item.optString("item_id"))){
								result.put("errorMsg", "亲，对不起，由于店内设定，"+dish.optString("item_name")+"在本桌位不能点！");
								result.put("errCode", "0");
								return result;
							}else{
								z++;
							}
						}else if(dish.optString("dishes_sign").equals("1")){
							if(item.optString("item_id").equals(dish.optString("item_id"))){
								z++;
							}
						}
					}
					if(z==0){
						result.put("errorMsg", "亲，对不起，由于店内设定，"+dish.optString("item_name")+"在本桌位是必点菜点！");
						result.put("errCode", "0");
						return result;
					}
				}
			}
		}
		// 订单 点菜信息
		if (!"null".equals(jb.optString("order_item"))) {
			JSONArray order_item_arr1 = new JSONArray();
			JSONArray order_item_arr = jb.optJSONArray("order_item");
			for (int i = 0; i < order_item_arr.size(); i++) {
				JSONObject object = (JSONObject) order_item_arr.get(i);
				object.put("real_amount", object.optDouble("price",0.0));
				order_item_arr1.add(object);
				CcExceptionUtil.validContains(order_item_arr.getJSONObject(i),
						"group_index", CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
				CcExceptionUtil.validContains(order_item_arr.getJSONObject(i),
						"item_id", CcErrorCode.ITEM_ID_EMPTY_ERROE);
				CcExceptionUtil.validContains(order_item_arr.getJSONObject(i),
						"number", CcErrorCode.NUMBER_EMPTY_ERROE);
				CcExceptionUtil.validContains(order_item_arr.getJSONObject(i),
						"price", CcErrorCode.PRICE_EMPTY_ERROE);
				Integer item_id = order_item_arr.getJSONObject(i).optInt(
						"item_id");
				String item_name = order_item_arr.getJSONObject(i).optString("item_name");
				sb.setLength(0);
				String nowDateYYDDMM = DateUtil.getNowDateYYDDMM();
				sb.append("select count(ps1.*) as guqing from pos_soldout ps1 ");
				sb.append("where ps1.store_id="+organId+" and ps1.item_id="+item_id+" and (ps1.soldout_type='1' or (ps1.soldout_type='0' and ps1.report_date='"+nowDateYYDDMM+"'))");
				List<JSONObject> query4Json = dao.query4Json(tenantId, sb.toString());
				if(query4Json.size()>0){
					JSONObject jsonObject3 = query4Json.get(0);
					if(jsonObject3.optInt("guqing")>0){
						result.put("errorMsg", "对不起，您购买的"+item_name+"商品已经沽请。请另外选择商品下单！");
						result.put("errCode", "0");
						return result;
					}
				}
				jb.put("order_item", order_item_arr1);
//				StringBuilder sb2 = new StringBuilder(
//						"select  ZZ.item_id,ZZ.combo_item_id,ZZ.item_unit_id,count(ZZ.*) as num,0.1 as price ");
//				sb2.append("from (select Z.item_id,(case Z.is_itemgroup when 'Y' then Z.group_item_id else Z.details_id end) as combo_item_id, ");
//				sb2.append("(case Z.is_itemgroup when 'Y' then Z.hg_unit_id else Z.item_unit_id end) as item_unit_id ");
//				sb2.append("from (select A.item_id,hd.is_itemgroup,hd.details_id,hg.item_id as group_item_id,hd.item_unit_id,hg.item_unit_id as hg_unit_id  ");
//				sb2.append("from (select hi.id as item_id from hq_item_info hi where hi.is_combo='Y' and hi.id in("
//						+ item_id + ")) as A ");
//				sb2.append("LEFT JOIN hq_item_combo_details hd on A.item_id = hd.iitem_id ");
//				sb2.append("LEFT JOIN hq_item_group_details hg on hd.details_id = hg.item_group_id and hd.is_itemgroup='Y') as Z ");
//				sb2.append("ORDER BY Z.item_id) as  ZZ ");
//				sb2.append("GROUP BY ZZ.item_id,ZZ.combo_item_id,ZZ.item_unit_id ");
//				StringBuilder sb2 = new StringBuilder();
//				sb2.append("select  ZZ.item_id,ZZ.combo_item_id,ZZ.item_unit_id,count(ZZ.*) as num,0.1 as price,(case ZZ.is_itemgroup when 'Y' then 1 else sum(ZZ.combo_num) end) as combo_num ");
//				sb2.append("from (select Z.item_id,(case Z.is_itemgroup when 'Y' then Z.group_item_id else Z.details_id end) as combo_item_id, ");
//				sb2.append("(case Z.is_itemgroup when 'Y' then Z.hg_unit_id else Z.item_unit_id end) as item_unit_id,Z.combo_num,Z.is_itemgroup ");
//				sb2.append("from (select A.item_id,hd.is_itemgroup,hd.details_id,hg.item_id as group_item_id,hd.item_unit_id,hg.item_unit_id as hg_unit_id,hd.combo_num  ");
//				sb2.append("from (select hi.id as item_id from hq_item_info hi where hi.is_combo='Y' and hi.id in("+item_id+")) as A ");
//				sb2.append("LEFT JOIN hq_item_combo_details hd on A.item_id = hd.iitem_id ");
//				sb2.append("LEFT JOIN hq_item_group_details hg on hd.details_id = hg.item_group_id and hd.is_itemgroup='Y' and hg.isdefault='Y') as Z ");
//				sb2.append("ORDER BY Z.item_id) as  ZZ ");
//				sb2.append("GROUP BY ZZ.item_id,ZZ.combo_item_id,ZZ.item_unit_id,ZZ.is_itemgroup ");
//				List<JSONObject> query4Json3 = dao.query4Json(tenantId,
//						sb2.toString());
//				if (query4Json3.size() > 0) {
//					// 套餐
//					Integer number = order_item_arr.getJSONObject(i).optInt(
//							"number");
//					Integer groupIndex = order_item_arr.getJSONObject(i)
//							.optInt("group_index");
//					for (int j = 0; j < query4Json3.size(); j++) {
//						int number1 = number * query4Json3.get(j).optInt("combo_num");
//						JSONObject orderItemDetails = new JSONObject();
//						orderItemDetails.put("number", number1);
//						orderItemDetails.put("group_index", groupIndex);
//						orderItemDetails.put("item_id", query4Json3.get(j)
//								.optInt("combo_item_id"));
//						orderItemDetails.put("price", query4Json3.get(j)
//								.optDouble("price"));
//						orderItemDetails.put("unit_id", query4Json3.get(j)
//								.optInt("item_unit_id"));
//						orderItemDetails.put("tenancy_id", tenantId);
//						// 加菜
//						if (afterAddOrder.size() > 0) {
//							orderItemDetails.put("is_add_dish", "1");
//						}
//						order_item_details.add(orderItemDetails);
//					}
//				}
			}
		} else {
			CcExceptionUtil.validContains(jb, "order_item",
					CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
		}
		// 菜品口味做法信息
		if (!"null".equals(jb.optString("order_item_taste"))
				&& null != jb.optJSONArray("order_item_taste")) {
			JSONArray order_item_taste_arr = jb
					.optJSONArray("order_item_taste");
			JSONArray order_item_taste_arr1 = new JSONArray();
			for (int i = 0; i < order_item_taste_arr.size(); i++) {
				JSONObject object = (JSONObject) order_item_taste_arr.get(i);
				// 加菜
				if (afterAddOrder.size() > 0) {
					object.put("is_add_dish", "1");
				}
				order_item_taste_arr1.add(object);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i), "group_index",
						CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i),
						"taste_method_id",
						CcErrorCode.TASTE_METHOD_ID_EMPTY_ERROE);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i), "item_remark",
						CcErrorCode.ITEM_REMARK_EMPTY_ERROE);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i), "item_id",
						CcErrorCode.ITEM_ID_EMPTY_ERROE);
				CcExceptionUtil.validContains(
						order_item_taste_arr.getJSONObject(i), "type",
						CcErrorCode.TYPE_EMPTY_ERROE);
			}
			jb.put("order_item_taste", order_item_taste_arr1);

		}
		// 套餐明细信息
		if (!"null".equals(jb.optString("order_item_details"))
				&& null != jb.optJSONArray("order_item_details")) {
			JSONArray order_item_details = jb
					.optJSONArray("order_item_details");
			// 套餐明细信息
			if (order_item_details.size() > 0) {
				for (int i = 0; i < order_item_details.size(); i++) {
					CcExceptionUtil.validContains(
							order_item_details.getJSONObject(i), "group_index",
							CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
					CcExceptionUtil.validContains(
							order_item_details.getJSONObject(i), "item_id",
							CcErrorCode.ITEM_ID_EMPTY_ERROE);
					CcExceptionUtil.validContains(
							order_item_details.getJSONObject(i), "unit_id",
							CcErrorCode.UNIT_ID_EMPTY_ERROE);
					CcExceptionUtil.validContains(
							order_item_details.getJSONObject(i), "number",
							CcErrorCode.NUMBER_EMPTY_ERROE);
//					CcExceptionUtil.validContains(
//							order_item_details.getJSONObject(i), "price",
//							CcErrorCode.PRICE_EMPTY_ERROE);
				}
			}
		}
		order_list.put("actual_pay", order_list.optDouble("total_money"));
		order_list.put("shop_real_amount", order_list.optDouble("total_money"));
		order_list.put("shop_fee", order_list.optDouble("total_money"));
		if (order_list.optString("paystatus").length() == 0) {
			order_list.put("paystatus", "01");
		}
		if (isPrepayment) {
			// 先付
			order_list.put("is_online_payment", "1");
		} else {
			// 后付
			order_list.put("is_online_payment", "0");
		}
		//菜品总价
		order_list.put("product_org_total_fee", product_org_total_fee);
		order_list.put("wx_open_id", openid);
		jb.put("order_list", order_list);
		List<JSONObject> list4 = new ArrayList<JSONObject>();
		JSONObject jsonob = new JSONObject();
		jsonob.put("order_list", jb.optJSONObject("order_list"));
		jsonob.put("order_item", jb.optJSONArray("order_item"));
		jsonob.put("order_item_taste", jb.optJSONArray("order_item_taste"));
		jsonob.put("order_item_details",  jb.optJSONArray("order_item_details"));
		jsonob.put("order_repayment", jb.optJSONArray("order_repayment"));
		jsonob.put("dispatch_time", jb.optString("dispatch_time"));
		jsonob.put("receive_time_dispatch",
		jb.optString("receive_time_dispatch"));
		list4.add(jsonob);
		data.setData(list4);
		data.setTenancy_id(tenantId);
		data.setStore_id(order_list.optInt("store_id"));
		if (afterAddOrder.size() > 0) {
			// 加菜
			orderManagerService.orderUpdateDish(data);
		} else {
			orderManagerService.orderSave(data);
		}
		logger.info("orderCode:");
		// 给门店下发订单
		@SuppressWarnings("unchecked")
		List<Map<String, Object>> array1 = (List<Map<String, Object>>) data
				.getData();
		if (array1.size() > 0 && orderCode.equals("")) {
			Map<String, Object> obj1 = new HashMap<String, Object>();
			obj1 = array1.get(0);
			orderCode = (String) obj1.get("order_code");
		}
		logger.info(orderCode);
		if ("".equals(orderCode) || null == orderCode) {
			throw new SystemException(WxErrorCode.ORDER_FALSE);
		}
		;
		data.setTenancy_id(tenantId);
		List<JSONObject> list2 = new ArrayList<JSONObject>();
		JSONObject param = new JSONObject();
		param.put("order_code", orderCode);
		param.put("tenentid", tenantId);
		// 跳转后获取
		// 加菜
		if (!member.optString("mac_openid").equals("null")) {
			result.put("mac_openid", member.optString("mac_openid"));
		}
		if (afterAddOrder.size() > 0) {
			// 加菜
			list2.add(jsonob);
			data.setData(list2);
			if (!isPrepayment) {
				JSONObject orderIssued = orderManagerService
						.orderUpdateDishIssued(data);
				result.put("orderIssued", orderIssued);

			} else {
				// 调用微信预下单接口
				result.put("order_code", orderCode);
				// result.put("cost_card", memberCard);
				// result.put("preOrder", preOrder);
			}
		} else {
			list2.add(param);
			data.setData(list2);
			if (!isPrepayment) {
				JSONObject orderIssued = orderManagerService.orderIssued(data);
				result.put("orderIssued", orderIssued);
			} else {
				// 调用微信预下单接口
				result.put("order_code", orderCode);
				// result.put("cost_card", memberCard);
				// result.put("preOrder", preOrder);
			}
		}
		result.put("success", true);
		result.put("isPrepayment", isPrepayment);
		if (!isPrepayment) {
			// 微信推送信息
			sendTemplatMsg(tenantId, openid, orderCode);
		}
		if(isPrepayment){
			JSONObject bean1=new JSONObject();
			JSONObject queryOrderByOrderCode = newWxOrderDao.queryOrderByOrderCode(tenantId, orderCode);
			if(null==queryOrderByOrderCode){
				result.put("errorMsg", "未获取到订单的相关信息，请再次下单");
				result.put("errCode", "0");
				result.put("success", false);
				return result;
			}
			String orderId=queryOrderByOrderCode.optString("id");
			bean1.put("orderId", orderId);
			bean1.put("order_code", orderCode);
			bean1.put("openid", openid);
			bean1.put("tenantId", tenantId);
			bean1.put("type1", "orderFailure");
			bean1.put("type",type);
			int count=20;
			//改： 加品牌 查询系统参数     ---wzx
			JSONObject jsonObject5 = baseInfoDao.querySystemParameterByParaCode(tenantId, organId,"wechat_payment_time");
			if(jsonObject5!=null){
				count = jsonObject5.optInt("para_value");
			}
			//--wzx

			/*List<JSONObject> queryWXSystemParameterByParaCode = newWxSystemParameterDao.queryWXSystemParameterByParaCode(tenantId, "wechat_payment_time");
			if(queryWXSystemParameterByParaCode.size()>0){
					JSONObject jsonObject5 = queryWXSystemParameterByParaCode.get(0);
					count = jsonObject5.optInt("para_value");
			}*/
			bean1.put("LongTime", System.currentTimeMillis()+count*60*1000);
			bean1.put("oper", 0);
			taskRedisDao.lpush("wxpaymentbillnum".getBytes(), bean1);
		}
		
		return result;
	}

	@Override
	public JSONObject queryItemTaste(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		JSONObject result=new JSONObject();
		result.put("success", false);
		String store_id=jb.optString("organ_id");
		StringBuilder sb=new StringBuilder();
//		sb.append("select * from item_taste where valid_state='1' and father_id!=0");
		sb.append("select * from item_taste where id in");
		sb.append(" ( select teste_id from item_taste_org where store_id="+store_id+") and");
		sb.append(" valid_state='1' and father_id!=0");
		List<JSONObject> tasteList = dao.query4Json(tenantId, sb.toString());
		if(tasteList.size()<1){
			result.put("err_msg", "没有口味可以选择！");
			return result;
		}
		result.put("tasteList", tasteList);
		result.put("success", true);
		return result;
	}

	@Override
	public JSONObject surePayOrderByCardSuccess(String tenantId, JSONObject jb,com.tzx.crm.bo.dto.Data data)
			throws SystemException, Exception {
		try {
			JSONObject result = new JSONObject();
			result.put("success", false);
			String openId = jb.optString("openid");
			String card_code = jb.optString("card_code");
			String bill_code = jb.optString("order_code");
			Double allMoney1 = jb.optDouble("allPrice", 0.0);
			String type = jb.optString("type");
			JSONObject queryOrderByOrderCode2 = newWxOrderDao.queryOrderByOrderCode(tenantId, bill_code);
			double allMoney=queryOrderByOrderCode2.optDouble("total_money",0.0);
			int store_id = jb.optInt("store_id");
			String pay_password = jb.optString("pay_password");
			
			StringBuilder sb = new StringBuilder();
			// 添加消费流水记录、修改订单状态、下发菜单
			// 更新
			// 获取支付订单信息
			JSONObject paramJson = jb;
			sb.append("select * from cc_order_list where order_code ='" + bill_code
					+ "'");
			List<JSONObject> query4Json2 = dao.query4Json(tenantId, sb.toString());
			if (query4Json2.size() < 1) {
				result.put("errorMsg", "请点击我的订单查询出，相关的订单信息");
				result.put("errCode", "2");
				return result;
			}
			JSONObject order = query4Json2.get(0);
			double price = allMoney1;// 总价格
			if (order.optString("order_state").equals("10")
					|| order.optString("order_state").equals("08")) {
				result.put("errorMsg", "支付成功");
				result.put("errCode", "2");
				return result;
			}
			// JSONObject paramElement = getDeductBean(param, price);
			// 计算订单理应支付多少钱
			// 获取支付订单信息
			// JSONObject order = dishDao.getWhOrderBean(param.getTenantId(),
			// paramJson.optString("order_code"));
			sb.delete(0, sb.length());
			// select cci.id,cl.rate from crm_customer_info cci LEFT JOIN crm_level
			// cl on cci.level= cl.id where cci.mobil='" + phone + "'
			sb.append("select cci.id,cl.rate from crm_customer_info cci LEFT JOIN crm_level cl on cci.level= cl.id LEFT JOIN wx_member wm on cci.mobil=wm.mobile_num where wm.openid='"
					+ openId + "' and wm.subscribe=true ");
			List<JSONObject> memberList = dao.query4Json(tenantId, sb.toString());
			if (memberList.size() < 1) {
				result.put("errorMsg", "请绑定公众号！");
				result.put("errCode", "1");
				return result;
			}
			String cost_apply_seq_temp = jb.optString("cost_apply_seq");
			double total_money = price;// 总价格
			double card_cost_money = 0;
			double coupons_cost_money = 0;
			double discount_cost = 0;// 折扣金额
			// 获取用户的信息，包括积分、卡劵、会员卡信息
			// 用户表内没有手机号 则为未绑定用户 不用再去查询
			JSONObject member = memberList.get(0);
			sb.delete(0, sb.length());
			sb.append("select card_code,main_balance,reward_balance from crm_customer_card  where card_state='1' and customer_id="
					+ member.optInt("id"));
			List<JSONObject> cardList = dao.query4Json(tenantId, sb.toString());
			if (cardList.size() < 0) {

			}
			sb.delete(0, sb.length());
			// 查询出所有的优惠劵（该用户相关的）（调接口）
			sb.append("select * from crm_activity_wxcupous");
			List<JSONObject> couponList = dao.query4Json(tenantId, sb.toString());
			// if(){}
			// 获取优惠折扣价格
			int rate = member.optInt("rate") == 0 ? 100 : member.optInt("rate");
			// 打折金额只按菜品计算，不计算服务费、外送费等信息
			sb.delete(0, sb.length());
			sb.append("SELECT b.*,info.item_name,info.photo1,info.is_discount from  cc_order_item b");
			sb.append(" left join hq_item_info info on b.item_id=info.id where  b.order_code= '"
					+ bill_code + "'");
			List<JSONObject> order_item_list = dao.query4Json(tenantId,
					sb.toString());
			// 获取菜品口味
			sb.delete(0, sb.length());
			sb.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '"
					+ bill_code + "'");
			List<JSONObject> cc_order_item_taste_list = dao.query4Json(tenantId,
					sb.toString());

			float total = 0;
			if (order_item_list != null && order_item_list.size() > 0) {
				for (JSONObject bean1 : order_item_list) {
					if (bean1.optString("is_discount").equals("Y")) {
						if (cc_order_item_taste_list != null
								&& cc_order_item_taste_list.size() > 0) {
							for (JSONObject taste : cc_order_item_taste_list) {
								if (bean1.optInt("item_id") == taste
										.optInt("item_id")
										&& bean1.optInt("group_index") == taste
												.optInt("group_index")) {
									total += (bean1.optDouble("price") + taste
											.optDouble("proportion_money"))
											* bean1.optInt("number");
								}
							}
						} else {
							total += bean1.optDouble("price")
									* bean1.optInt("number");
						}
					}
				}
			}
			discount_cost = (float) (total * (100 - rate) * 0.01);
			discount_cost = 0;
			// 获取抵消顺序
			if (cost_apply_seq_temp.indexOf("card") < 0) {
				price = price - discount_cost;
			} else {
				discount_cost = 0;
			}
			// 计算抹零金额
			// 获取保留金额尾数
			double retainNum = 0;
			int retainOrder = 2;
			int retainDish = 2;
			// List<JSONObject> retainList =
			// dishDao.getOrderParamList(param.getTenantId());
			sb.delete(0, sb.length());
			List<JSONObject> retainList = newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, store_id);
			if(retainList.size()==0){
				retainList=newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, 0);
			}
//			sb.append("select para_name,para_code,para_value from sys_parameter where valid_state='1' and  para_code in ('CMJEWS','ZDJEWS')");
//			List<JSONObject> retainList = dao.query4Json(tenantId, sb.toString());
			if (retainList != null && retainList.size() > 0) {
				for (JSONObject reatin : retainList) {
					if ("ZDJEWS".equals(reatin.optString("para_code"))) {
						retainOrder = reatin.optInt("para_value");
					} else if ("CMJEWS".equals(reatin.optString("para_code"))) {
						retainDish = reatin.optInt("para_value");
					}
				}
			}
			// 是否包含会员卡消费，如果有的话，不计算会员折扣
			NumberFormat nf = NumberFormat.getNumberInstance();
			nf.setMaximumFractionDigits(retainOrder);
			double retainMoney = Double.valueOf(nf.format(price).replace(",", ""));
			// retainNum = (float) (price - retainMoney);
			retainNum = retainMoney - price;
			price = price + retainNum;
//			float retainMoney = Float.parseFloat(nf.format(price));
//			// retainNum = (float) (price - retainMoney);
//			retainNum = (float) (retainMoney - price);
//			price = price + retainNum;
			result.put("retain_money", retainNum);
			result.put("retain_order", retainOrder);
			result.put("retain_dish", retainDish);
			result.put("total_money", total_money);
			result.put("actual_pay", price);
			result.put("shop_fee", price);
			
			@SuppressWarnings("unchecked")
			List<Map<String,Object>> list= (List<Map<String, Object>>) data.getData();
			if(list.size()<1){
				result.put("errorMsg", "支付失败，请重新支付哦亲。");
				result.put("errCode", "2");
				return result;
			}
			Map<String, Object> map = list.get(0);
			if(!map.containsKey("bill_code")){
				result.put("errorMsg", "亲，请重新支付哦。");
				result.put("errCode", "2");
				return result;
			}
			Object bill_code1 = map.get("bill_code");
			result.put("bill_code", bill_code1);
			String nowDateYYDDMMHHMMSS = DateUtil.getNowDateYYDDMMHHMMSS();
			String[] split = nowDateYYDDMMHHMMSS.split(":");
			String time=split[0]+":"+split[1];
			result.put("exchangeTime", time);
			result.put("orderId", order.optInt("id"));
			// 获取抵消顺序
			cost_apply_seq_temp = paramJson.optString("cost_apply_seq");
			// 会员卡、优惠券的消费 向支付方式表里填写其他支付方式流水
			JSONObject wechatWay = null;
			sb.setLength(0);
			sb.append("select cc.* from (select id,(select count(pwoo.*) from payment_way_of_ogran pwoo where pwoo.organ_id="+store_id+") as z from payment_way where payment_class='card' and status='1') cc where cc.z>0 ");
			List<JSONObject> query4Json = dao.query4Json(tenantId, sb.toString());
			wechatWay = query4Json.size() > 0 ? query4Json.get(0) : null;
			JSONObject bean = new JSONObject();
			bean.put("tenancy_id", tenantId);
			bean.put("payment_id", wechatWay.optInt("id"));
			bean.put("order_code", bill_code);
			bean.put("pay_money", price);
			bean.put("pay_no", card_code);
			bean.put("third_bill_code", bill_code1);//交易单号
			bean.put("store_id", store_id);
			dao.insertIgnorCase(tenantId, "cc_order_repayment", bean);
			// 计算折扣优惠金额
			discount_cost = result.optDouble("discount_amount",0.0);
			// double discount_amount = price - discount_cost;
			// 计算折扣优惠金额
			double discount_amount = discount_cost;
			// 实际支付金额
			double actual_pay = order.optDouble("total_money", 0.0) != 0 ? order
					.optDouble("total_money",0.0) : (price - discount_amount - order
					.optDouble("retain_money",0.0));
			discount_amount = price - actual_pay - result.optDouble("retain_money",0.0);
			discount_amount = 0;
			JSONObject order1=new JSONObject();
			order1.put("discount_amount", discount_amount);
			// order.put("discount_mode_id", 5);
			// 抹零金额
			order1.put("maling_amount", result.optDouble("retain_money",0.0));
			order1.put("actual_pay", actual_pay);
			order1.put("shop_fee", actual_pay);
			
			// 修改订单支付状态
			order1.put("payment_state", "03");
			// 设置是否是线上支付
			order1.put("is_online_payment", "1");
			order1.put("id", order.optInt("id"));
			dao.updateIgnorCase(tenantId, "cc_order_list", order1);
			// 下发
			Data data1 = new Data();
			data1.setTenancy_id(tenantId);
			List<JSONObject> list2 = new ArrayList<JSONObject>();
			JSONObject param = new JSONObject();
			param.put("order_code", order.optString("order_code"));
			param.put("tenentid", tenantId);
			list2.add(param);
			data1.setData(list2);
			JSONObject orderIssued = orderManagerService.orderIssued(data1);
			// 成功后更改订单状态，并下发订单
			sendTemplatMsg(tenantId, openId, bill_code);
//			JSONObject bean1=new JSONObject();
//			bean1.put("order_code", bill_code);
//			bean1.put("openid", openId);
//			bean1.put("tenantId", tenantId);
//			bean1.put("type1", "orderCancel");//卡充值
//			bean1.put("type", type);
//			bean1.put("discount_amount",  order.optDouble("actual_pay",0.0));
//			bean1.put("payment", "card");
//			int count=20;
//			List<JSONObject> queryWXSystemParameterByParaCode = newWxSystemParameterDao.queryWXSystemParameterByParaCode(tenantId, "order_cancel_time");
//			if(queryWXSystemParameterByParaCode.size()>0){
//				JSONObject jsonObject2 = queryWXSystemParameterByParaCode.get(0);
//				count = jsonObject2.optInt("para_value");
//			}
//			bean1.put("LongTime", System.currentTimeMillis()+count*60*1000);
//			bean1.put("oper", 0);
//			taskRedisDao.lpush("wxpaymentbillnum".getBytes(), bean1);
			result.put("success", true);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			throw new SystemException(WxErrorCode.USE_CARD_FOR_PAY);
		}
	}

	@Override
	public void notUseCardForPayOrder(com.tzx.crm.bo.dto.Data data,JSONObject jb) throws SystemException, Exception {
		List<Map<String,Object>> list= (List<Map<String, Object>>) data.getData();
		if(list.size()<1){
			jb.put("errorMsg", "支付失败，请重新支付哦亲。");
			jb.put("errCode", "2");
			data=null;
		}
		Map<String, Object> map = list.get(0);
		if(!map.containsKey("bill_code")){
			jb.put("errorMsg", "亲，请重新支付哦。");
			jb.put("errCode", "2");
			data=null;
		}
		Object bill_code1 = map.get("bill_code");
		String openId = jb.optString("openid");
		String card_code = jb.optString("card_code");
		Double allMoney = jb.optDouble("allPrice", 0.0);
		int store_id = jb.optInt("store_id");
		String type = jb.optString("type");
		String bill_code = jb.optString("order_code");
		//卡支付撤销接口
		data.setOper(Oper.update);
		data.setStore_id(store_id);
		JSONObject bean = new JSONObject();
		List<JSONObject> list1 = new ArrayList<JSONObject>();
		bean.put("card_code", card_code);// 卡号
		bean.put("old_bill_code", bill_code1);//交易流水单号
		bean.put("bill_code", bill_code);//订单号
		bean.put("chanel", type);// 渠道
		bean.put("operator", "admin");// 操作人员
		bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
		list1.add(bean);
		data.setData(list1);
	}

	@Override
	public JSONObject queryMustDishAndNotDish(String tenantId,JSONObject jb) throws SystemException, Exception {
		JSONObject result = new JSONObject();
		result.put("success", false);
		String table_code = jb.optString("table_code");
		String store_id = jb.optString("store_id");
		String openid =jb.optString("openid");
		/**
		 * 初始化加载界面时判断如果是后付，而且是加菜的情况下,则取出人数信息
		 */
		StringBuilder sb = new StringBuilder();
		boolean isPrepayment = false;
		//改： 加品牌 查询系统参数     ---wzx
		JSONObject wechat_payment_type = baseInfoDao.querySystemParameterByParaCode(tenantId, store_id, "wechat_payment_type");
		//--wzx
		
		sb.append("select * from sys_parameter where para_code='wechat_payment_type' and store_id=0");
		/*List<JSONObject> query4Json6 = dao.query4Json(tenantId, sb.toString());
		if (query4Json6.size() > 0) {
			JSONObject wechat_payment_type = query4Json6.get(0);*/
		
		if(wechat_payment_type!=null){
			String para_value = wechat_payment_type.optString("para_value");
			if (para_value.equals("0")) {
				// 后付
				isPrepayment = false;
			} else if (para_value.equals("1")) {
				// 先付
				isPrepayment = true;
			}
			List<JSONObject> afterAddOrder = new ArrayList<JSONObject>();
			StringBuilder sbl = new StringBuilder();
			if (!isPrepayment) {
				// 后付可以加菜，先付是另一个订单
				// 后续添加菜时的改变
				sbl.append("select * from cc_order_list where 1=1 ");
				sbl.append("and table_code='" + table_code + "' ");
				sbl.append("and store_id=" + store_id + " ");
				sbl.append("and order_phone = (select cc.mobil from crm_customer_info cc LEFT JOIN wx_member wx on cc.mobil=wx.mobile_num where wx.openid='"
						+ openid + "') ");
				// 在3个小时以内
				sbl.append("and payment_state!='03'");
				sbl.append("and order_state not in ('08','05','10')");
				sbl.append("and single_time > (select now() -interval '3 H')");
				 afterAddOrder = dao.query4Json(tenantId, sbl.toString());
				 if(afterAddOrder.size()>0){
					 JSONObject obj= afterAddOrder.get(0);
					String  deinner_number=obj.optString("deinner_number");
					if("".equals(deinner_number)||null==deinner_number){
						deinner_number="0";
					}
					result.put("deinner_number", Integer.parseInt(deinner_number));
				 }
			}

		}
		
		//改： 加品牌 查询系统参数     ---wzx
		boolean isTableCode =false;
		JSONObject wechat_table_code = baseInfoDao.querySystemParameterByParaCode(tenantId, store_id, "wechat_table_code");
		if(wechat_table_code!=null){
			if(wechat_table_code.optInt("para_value")==1){
				isTableCode=true;
			}
		}
		//--wzx
		
		/*boolean isTableCode = newWxOrderDao.queryIsSetTableCode(tenantId);*/
		if (!isTableCode) {
			result.put("checkTable", "0");
			result.put("success", true);
			return result;
		}
		List<JSONObject> queryMustDishAndNotDish = newWxOrderDao
				.queryMustDishAndNotDish(tenantId, table_code, store_id);
		if (queryMustDishAndNotDish.size() < 1) {
			result.put("checkTable", "0");
			result.put("success", true);
			return result;
		}
		result.put("checkTable", "1");
		result.put("dishList", queryMustDishAndNotDish);
		result.put("success", true);
		return result;
	}

	@Override
	public JSONObject queryMuchUnitForItem(String tenantId,
			JSONObject jb) throws SystemException, Exception {
		JSONObject result=new JSONObject();
		result.put("success", false);
		int item_id = jb.optInt("item_id");
		int store_id = jb.optInt("store_id");
		String type = jb.optString("type");
		if(item_id==0){
			result.put("errorMsg", "请再次点击该菜获取菜品的相关信息哦！");
			return result;
		}
		List<JSONObject> queryMuchUnitForItem = newWxOrderDao.queryMuchUnitForItem(tenantId, item_id,store_id,type);
		if(queryMuchUnitForItem.size()<1){
			result.put("errorMsg", "请刷新页面再次点击菜品获取信息！");
			return result;
		}
		JSONObject jsonObject = queryMuchUnitForItem.get(0);
		result.put("munchunit", jsonObject.optString("munchunit"));
		result.put("success", true);
		return result;
	}

	@Override
	public JSONObject getComboList(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		JSONObject result=new JSONObject();
		result.put("success", false);
		int item_id = jb.optInt("item_id");
		int store_id = jb.optInt("store_id");
		String type = jb.optString("type");
		if(item_id==0){
			result.put("errorMsg", "请再次点击该菜获取菜品的相关信息哦！");
			return result;
		}
		JSONObject comboName = newWxOrderDao.getComboName(tenantId, item_id,store_id,type);
		if(null==comboName){
			result.put("errorMsg", "未获取到套餐的相关信息");
			return result;
		}
		List<JSONObject> comboList = newWxOrderDao.getComboList(tenantId, item_id);
		if(comboList.size()<1){
			result.put("errorMsg", "获取的套餐信息有误，请刷新页面");
			return result;
		}
		List<Integer> comboList1=new ArrayList<Integer>();
		Map<String,List<JSONObject>> map=new HashMap<String, List<JSONObject>>();
		for(int i=0;i<comboList.size();i++){
			JSONObject jo=comboList.get(i);
			List<JSONObject> comboList2=new ArrayList<JSONObject>();
			int id = jo.optInt("id");
			if(map.containsKey(""+id)){
				comboList2 = map.get(""+id);
				comboList2.add(jo);
				map.put(""+id, comboList2);
			}else{
				comboList2.add(jo);
				map.put(""+id, comboList2);
				comboList1.add(id);
			}
		}
		result.put("comboName", comboName);
		result.put("comblist", map);
		result.put("idlist", comboList1);
		result.put("success", true);
		return result;
	}

	@Override
	public JSONObject notUseCardForPayOrder1(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		JSONObject result=new JSONObject();
		result.put("success", false);
		com.tzx.crm.bo.dto.Data data=new com.tzx.crm.bo.dto.Data();
		String bill_code = jb.optString("order_code");
		String type = jb.optString("type");
		List<JSONObject> orderPaymentDetail = newWxOrderDao.getOrderPaymentDetail(tenantId, bill_code);
		if(orderPaymentDetail.size()>0){
			for (JSONObject jsonObject : orderPaymentDetail) {
				if(jsonObject.optString("payment_class").equals("card")){
					//卡的
					Object bill_code1 = jsonObject.optString("third_bill_code");
					String card_code = jsonObject.optString("pay_no");
					Double allMoney = jsonObject.optDouble("pay_money", 0.0);
					int store_id = jsonObject.optInt("store_id");
					//卡支付撤销接口
					data.setOper(Oper.update);
					data.setStore_id(store_id);
					data.setTenancy_id(tenantId);
					JSONObject bean = new JSONObject();
					List<JSONObject> list1 = new ArrayList<JSONObject>();
					bean.put("card_code", card_code);// 卡号
					bean.put("old_bill_code", bill_code1);//交易流水单号
					bean.put("bill_code", bill_code);//订单号
					bean.put("chanel", type);// 渠道
					bean.put("operator", "admin");// 操作人员
					bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
					list1.add(bean);
					data.setData(list1);
					try {
//						cardTransactionService.customerCardConsume(data);
						cardConsumeService.requiresnewConsume(data);
					} catch (Exception e) {
						e.printStackTrace();
						result.put("msg", e.getMessage());
						result.put("success", false);
						return result;
					}
				}
			}
			//积分
			//使用积分的需撤销掉
			List<JSONObject> queryUseCreditByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, bill_code,"card_credit");
			if(queryUseCreditByOrder.size()>0){
				result.put("success", false);
				String openid = jb.optString("openid");
				//查询积分
				//有使用积分支付的
				JSONObject jsonObject = queryUseCreditByOrder.get(0);
				//撤销积分消费
				JSONObject queryMemberBaseInfo = newWxMemberDao.queryMemberBaseInfo(tenantId, openid);
				String mobile_num = queryMemberBaseInfo.optString("mobile_num");
				String third_bill_code = jsonObject.optString("third_bill_code");
				com.tzx.crm.bo.dto.Data data1 = new com.tzx.crm.bo.dto.Data();
				data1.setOper(com.tzx.crm.base.constant.Oper.update);
				data1.setStore_id(0);
				data1.setTenancy_id(tenantId);
				JSONObject bean = new JSONObject();
				List<JSONObject> list = new ArrayList<JSONObject>();
				bean.put("mobil", mobile_num);// 电话
				bean.put("chanel", type);// 渠道
				bean.put("operator", "admin");// 操作人员
				bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
				bean.put("old_bill_code", third_bill_code);//交易单号
				bean.put("business_date", DateUtil.getNowDateYYDDMMHHMMSS());
				bean.put("shift_id", "WX");
				bean.put("batch_no", "WX");
				bean.put("bill_code", bill_code);
				list.add(bean);
				data1.setData(list);
				//撤销接口
				try {
					bonusPointManageService.bonusPointConsume(data1);
				} catch (Exception e) {
					e.printStackTrace();
					result.put("msg", e.getMessage());
					result.put("success", false);
					return result;
				}
			}
			//优惠劵
			List<JSONObject> queryUseCouponByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, bill_code,"coupons");
			if(queryUseCouponByOrder.size()>0){
				//有使用优惠劵
				int store_id = 0;
				com.tzx.crm.bo.dto.Data param=new com.tzx.crm.bo.dto.Data();
				param.setOper(Oper.init);
				param.setTenancy_id(tenantId);
				
				List<JSONObject> list = new ArrayList<JSONObject>();
				JSONObject bean=new JSONObject();
				bean.put("chanel", type);
				List<JSONObject> couponList= new ArrayList<JSONObject>();
				for (JSONObject jsonObject : queryUseCouponByOrder) {
					JSONObject bean1=new JSONObject();
					store_id = jsonObject.optInt("store_id");
					bean1.put("coupons_code", jsonObject.optString("pay_no"));
					couponList.add(bean1);
				}
				param.setStore_id(store_id);
				bean.put("couponslist", couponList);
				list.add(bean);
				param.setData(list);
				//撤销接口
				try {
					couponsService.coupons(param);
				} catch (Exception e) {
					e.printStackTrace();
					result.put("msg", e.getMessage());
					result.put("success", false);
					return result;
				}
			}
			boolean deleteOrderByOrderCode = newWxOrderDao.deleteOrderByOrderCode(tenantId, "cc_order_repayment", bill_code);
			if(!deleteOrderByOrderCode){
				result.put("msg", "亲，撤销票劵失败");
				result.put("success", false);
				return result;
			}
			boolean updateOrderState1 = newWxOrderDao.updateOrderState1(tenantId, "08", "04", bill_code);
			if(!updateOrderState1){
				result.put("msg", "撤销失败");
				result.put("success", false);
				return result;
			}
			result.put("success", true);
		}
		return result;
	}

	@Override
	public JSONObject notUseCoupon(String tenantId, JSONObject jb)throws SystemException,Exception {
		JSONObject result=new JSONObject();
		result.put("success", false);
		String bill_code = jb.optString("order_code");
		String type = jb.optString("type");
		List<JSONObject> queryUseCouponByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, bill_code,"coupons");
		if(queryUseCouponByOrder.size()>0){
			//有使用优惠劵
			int store_id = 0;
			com.tzx.crm.bo.dto.Data param=new com.tzx.crm.bo.dto.Data();
			param.setOper(Oper.init);
			param.setTenancy_id(tenantId);
			
			List<JSONObject> list = new ArrayList<JSONObject>();
			JSONObject bean=new JSONObject();
			bean.put("chanel", type);
			List<JSONObject> couponList= new ArrayList<JSONObject>();
			for (JSONObject jsonObject : queryUseCouponByOrder) {
				JSONObject bean1=new JSONObject();
				store_id = jsonObject.optInt("store_id");
				bean1.put("coupons_code", jsonObject.optString("pay_no"));
				couponList.add(bean1);
			}
			param.setStore_id(store_id);
			bean.put("couponslist", couponList);
			list.add(bean);
			param.setData(list);
			//撤销接口
			try {
				couponsService.coupons(param);
			} catch (Exception e) {
				e.printStackTrace();
				result.put("msg", e.getMessage());
				result.put("success", false);
				return result;
			}
		}
		//使用积分的需撤销掉
		List<JSONObject> queryUseCreditByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, bill_code,"card_credit");
		if(queryUseCreditByOrder.size()>0){
			result.put("success", false);
			String openid = jb.optString("openid");
			//查询积分
			//有使用积分支付的
			JSONObject jsonObject = queryUseCreditByOrder.get(0);
			//撤销积分消费
			JSONObject queryMemberBaseInfo = newWxMemberDao.queryMemberBaseInfo(tenantId, openid);
			String mobile_num = queryMemberBaseInfo.optString("mobile_num");
			String third_bill_code = jsonObject.optString("third_bill_code");
			com.tzx.crm.bo.dto.Data data1 = new com.tzx.crm.bo.dto.Data();
			data1.setOper(com.tzx.crm.base.constant.Oper.update);
			data1.setStore_id(0);
			data1.setTenancy_id(tenantId);
			JSONObject bean = new JSONObject();
			List<JSONObject> list = new ArrayList<JSONObject>();
			bean.put("mobil", mobile_num);// 电话
			bean.put("chanel", type);// 渠道
			bean.put("operator", "admin");// 操作人员
			bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
			bean.put("old_bill_code", third_bill_code);//交易单号
			bean.put("business_date", DateUtil.getNowDateYYDDMMHHMMSS());
			bean.put("shift_id", "WX");
			bean.put("batch_no", "WX");
			bean.put("bill_code", bill_code);
			list.add(bean);
			data1.setData(list);
			//撤销接口
			try {
				bonusPointManageService.bonusPointConsume(data1);
			} catch (Exception e) {
				e.printStackTrace();
				result.put("msg", e.getMessage());
				result.put("success", false);
				return result;
			}
		}
		boolean deleteOrderByOrderCode = newWxOrderDao.deleteOrderByOrderCode(tenantId, "cc_order_repayment", bill_code);
		if(!deleteOrderByOrderCode){
			result.put("msg", "亲，撤销票劵失败");
			result.put("success", false);
			return result;
		}
		boolean updateOrderState1 = newWxOrderDao.updateOrderState1(tenantId, "08", "04", bill_code);
		if(!updateOrderState1){
			result.put("msg", "撤销失败");
			result.put("success", false);
			return result;
		}
		result.put("success", true);
		return result;
	}

	@Override
	public com.tzx.crm.bo.dto.Data surePayOrderByCredit(String tenantId,
			JSONObject jb) throws SystemException, Exception {
		jb.put("success", false);
		String openId = jb.optString("openid");
		Double allMoney = jb.optDouble("allPrice", 0.0);
		int store_id = jb.optInt("store_id");
		String bill_code = jb.optString("order_code");
		String type = jb.optString("type");
		StringBuilder sb = new StringBuilder();
		//该门店是否支持积分支付
		boolean whetherSupportThisPayment = newWxPaymentWayDao.whetherSupportThisPayment(tenantId, "card_credit", store_id+"");
		if(!whetherSupportThisPayment){
			//该门店不支持积分支付
			jb.put("msg_code", 1);
			jb.put("errmsg", "该店暂不支持积分支付");
			return null;
		}
		
		sb.setLength(0);
		sb.append("select * from cc_order_list where  order_code='" + bill_code
				+ "'");
		List<JSONObject> orderList = dao.query4Json(tenantId, sb.toString());
		if (orderList.size() < 1) {
			// 订单号错误
			jb.put("msg_code", 1);
			jb.put("errmsg", "未获取到订单信息！");
			return null;
		}
		JSONObject order = orderList.get(0);
		if(order.optString("payment_state").equals("03")){
			// 订单号错误
			jb.put("msg_code", 2);
			jb.put("errmsg", "该订单已经支付成功");
			return null;
		}
		List<JSONObject> queryPaymentWay = newWxOrderDao.queryPaymentWay(tenantId, bill_code);
		for(JSONObject o:queryPaymentWay){
			String payment_class = o.optString("payment_class");
			if(payment_class.equals("card_credit")){
				//已经使用了积分抵现
				jb.put("msg_code", 2);
				jb.put("errmsg", "请勿重复使用积分付款");
			}
		}
		sb.delete(0, sb.length());
		JSONObject queryMemberBaseInfo = newWxMemberDao.queryMemberBaseInfo(tenantId, openId);
		if(null==queryMemberBaseInfo){
			jb.put("msg_code", 1);
			jb.put("errmsg", "请关注公众号！");
			return null;
		}
		if(!queryMemberBaseInfo.optBoolean("isbind")||queryMemberBaseInfo.optString("mobile_num").equals("null")||queryMemberBaseInfo.optString("mobile_num").equals("")){
			jb.put("msg_code", 1);
			jb.put("errmsg", "请注册成会员！");
			return null;
		}
		String mobile_num = queryMemberBaseInfo.optString("mobile_num");
		String code = queryMemberBaseInfo.optString("code");
		jb.put("code", code);
		jb.put("mobile_num", mobile_num);
		//
		double price=allMoney;
		double total_money = price;// 总价格
		double card_cost_money = 0;
		double coupons_cost_money = 0;
		double discount_cost = 0;// 折扣金额
		// 计算抹零金额
		// 获取保留金额尾数
		double retainNum = 0;
		int retainOrder = 2;
		int retainDish = 2;
		sb.delete(0, sb.length());
		List<JSONObject> retainList = newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, store_id);
		if(retainList.size()==0){
			retainList=newWxSystemParameterDao.queryWXSystemParameterZDJE(tenantId, 0);
		}
//		sb.append("select para_name,para_code,para_value from sys_parameter where valid_state='1' and  para_code in ('CMJEWS','ZDJEWS')");
//		List<JSONObject> retainList = dao.query4Json(tenantId, sb.toString());
		if (retainList != null && retainList.size() > 0) {
			for (JSONObject reatin : retainList) {
				if ("ZDJEWS".equals(reatin.optString("para_code"))) {
					retainOrder = reatin.optInt("para_value");
				} else if ("CMJEWS".equals(reatin.optString("para_code"))) {
					retainDish = reatin.optInt("para_value");
				}
			}
		}
		// 是否包含会员卡消费，如果有的话，不计算会员折扣
		NumberFormat nf = NumberFormat.getNumberInstance();
		nf.setMaximumFractionDigits(retainOrder);
		double retainMoney = Double.valueOf(nf.format(price).replace(",", ""));
		// retainNum = (float) (price - retainMoney);
		retainNum = retainMoney - price;
		price = price + retainNum;
//		float retainMoney = Float.parseFloat(nf.format(price));
//		// retainNum = (float) (price - retainMoney);
//		retainNum = (float) (retainMoney - price);
//		price = price + retainNum;
		jb.put("retain_money", retainNum);
		jb.put("retain_order", retainOrder);
		jb.put("retain_dish", retainDish);
		if (allMoney < 0.01) {
			// 直接付款成功(修改订单状态，并下发)
			// 0元的处理(会员的折扣)
			double discount_amount = 0;
			// 实际支付金额
			// double actual_pay =
			JSONObject order1=new JSONObject();
			order1.optDouble("actual_pay");// result.optString("total_amount")
			double actual_pay = allMoney;
			order1.put("discount_amount", discount_amount);
			// order.put("discount_mode_id", 5);
			// 抹零金额
			order1.put("maling_amount", jb.optDouble("retain_money"));
			order1.put("actual_pay", actual_pay);
			order1.put("shop_fee", actual_pay);
			// 修改订单支付状态
			order1.put("payment_state", "03");
			// 设置是否是线上支付
			order1.put("is_online_payment", "1");
			order1.put("id", order.optInt("id"));
			dao.updateIgnorCase(tenantId, "cc_order_list", order1);
			jb.put("success", true);
			jb.put("surePay", true);
			jb.put("bill_num", bill_code);
			// 下发
			Data data = new Data();
			data.setTenancy_id(tenantId);
			List<JSONObject> list = new ArrayList<JSONObject>();
			JSONObject param = new JSONObject();
			param.put("order_code", bill_code);
			param.put("tenentid", tenantId);
			list.add(param);
			data.setData(list);
			jb = orderManagerService.orderIssued(data);
			sendTemplatMsg(tenantId, openId, bill_code);
			jb.put("success", true);
			return null;
		}
		
		//查询用户的积分和
		JSONObject usefulCreditAndUseRule = newWxMemberDao.getUsefulCreditAndUseRule(tenantId, openId);
		if(null==usefulCreditAndUseRule){
			jb.put("msg_code", 1);
			jb.put("errmsg", "该会员不能使用积分支付！");
			return null;
		}else{
			//兑换规则积分是多少
			double trading_credit = usefulCreditAndUseRule.optDouble("trading_credit",0.0);
			//兑换规则金额是多少
			double cash_money = usefulCreditAndUseRule.optDouble("cash_money",0.0);
			//用户所有的积分
			double useful_credit = usefulCreditAndUseRule.optDouble("useful_credit");
			//计算出该用户可以使用的积分
			double usecredit = Scm.psub(useful_credit, (useful_credit % trading_credit));
			if(usecredit<=0){
				//本次扣减积分不满足抵扣规则   
				jb.put("msg_code", 1);
				jb.put("errmsg", "本次扣减积分不满足抵扣规则  ！");
				return null;	
			}
			//用户可以兑换的金额
			double cashmoney =Math.floor(Scm.pdiv(Scm.pmui(usecredit,cash_money),trading_credit)) ;
			double actual_pay = order.optDouble("actual_pay",0.0);
			double credit_cash_bill = usefulCreditAndUseRule.optDouble("credit_cash_bill",100.0);
			actual_pay=Scm.pdiv((actual_pay*credit_cash_bill),100.0);
			if(cashmoney>actual_pay){
				//更具账单金额计算出的积分
				double usecredit1=Scm.pmui(Scm.pdiv(actual_pay,cash_money),trading_credit);
				String[] split = String.valueOf(usecredit1).split("\\.");
				//实际兑换的积分
				usecredit=Double.valueOf(split[0]);
				usecredit = Scm.psub(usecredit, (usecredit % trading_credit));
				//实际兑换的金额
//				Scm.pmui(Math.floor(Scm.pdiv(usecredit,trading_credit)),cash_money);
//				cashmoney = Math.floor(Scm.pdiv(Scm.pmui(usecredit,cash_money),trading_credit)) ;
				NumberFormat nf1 = NumberFormat.getNumberInstance();
				//保留两位小数
				nf1.setMaximumFractionDigits(2);
				cashmoney = Double.valueOf(nf1.format(Scm.pdiv(Scm.pmui(usecredit,cash_money),trading_credit))) ;
			}
			jb.put("totalMoney",allMoney);
			jb.put("coupons_cost_money", coupons_cost_money);
			jb.put("credit", usecredit);
			jb.put("cash_money", cashmoney);
			// 分装参数体
			com.tzx.crm.bo.dto.Data data = new com.tzx.crm.bo.dto.Data();
			data.setOper(com.tzx.crm.base.constant.Oper.add);
			data.setStore_id(store_id);
			data.setTenancy_id(tenantId);
			JSONObject bean = new JSONObject();
			List<JSONObject> list = new ArrayList<JSONObject>();
			jb.put("mobile_num", mobile_num);
			bean.put("mobil", mobile_num);// 电话
			bean.put("credit", usecredit);// 本次兑换积分
			bean.put("cash_money", cashmoney);// 积分兑换的金额
			bean.put("chanel", type);// 渠道
			bean.put("operator", "admin");// 操作人员
			bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
			bean.put("bill_code", bill_code);//交易单号
			bean.put("business_date", DateUtil.getNowDateYYDDMMHHMMSS());
			bean.put("shift_id", 0);
			bean.put("batch_no", "WX02");
			bean.put("bill_amount", allMoney);
			list.add(bean);
			data.setData(list);
			return data;
		}
	}

	@Override
	public JSONObject surePayOrderByCreditSuccess(String tenantId,
			JSONObject jb, com.tzx.crm.bo.dto.Data credit)
			throws SystemException, Exception {
		JSONObject result=new JSONObject();
		String type = jb.optString("type");
		result.put("success", false);
		List<Map<String,Object>>  data = (List<Map<String, Object>>) credit.getData();
		JSONObject queryPaymentWayByType = newWxOrderDao.queryPaymentWayByType(tenantId, "card_credit");
		if(null==queryPaymentWayByType){
			result.put("errmsg", "支付信息错误");
			result.put("msg_code", 1);
			return result;
		}
		String openId = jb.optString("openid");
		String order_code = jb.optString("order_code");
		int store_id = jb.optInt("store_id");
//		double totalMoney = jb.optDouble("totalMoney",0.0);
		JSONObject queryOrderByOrderCode2 = newWxOrderDao.queryOrderByOrderCode(tenantId, order_code);
		double totalMoney=queryOrderByOrderCode2.optDouble("total_money",0.0);
		int paymentId = queryPaymentWayByType.optInt("id");
		List<JSONObject> list=new ArrayList<JSONObject>();
		double couponCost=0.0;
		Map<String, Object> map2 = data.get(0);
		String third_bill_code="";
		@SuppressWarnings("unchecked")
		List<JSONObject> queryUseCouponByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, order_code,"coupons");
		for (JSONObject jsonObject : queryUseCouponByOrder) {
			double pay_money = jsonObject.optDouble("pay_money",0.0);
			couponCost+=pay_money;
		}
		//往订单支付
		third_bill_code=(String) map2.get("bill_code");
		Double change_money= (Double) map2.get("change_money");
		Double usecredit= (Double) map2.get("credit");
		couponCost+=change_money;
		JSONObject bean=new JSONObject();
		bean.put("tenancy_id", tenantId);
		bean.put("payment_id", queryPaymentWayByType.optInt("id"));
		bean.put("order_code", order_code);
		bean.put("third_bill_code", third_bill_code);
		bean.put("store_id", store_id);
		bean.put("pay_money", change_money);
		bean.put("pay_no", jb.optString("code"));
		bean.put("remark", 0-usecredit);
		newWxMemberDao.insertIgnorCase(tenantId, "cc_order_repayment", bean);
		if(couponCost>=totalMoney){
			//更改订单状态为已支付
			newWxOrderDao.updateOrderState(tenantId, "03", order_code,totalMoney);
			//下发订单
			// 下发
			Data data1 = new Data();
			data1.setTenancy_id(tenantId);
			JSONObject queryOrderByOrderCode = newWxOrderDao.queryOrderByOrderCode(tenantId,order_code);
			result.put("orderId", queryOrderByOrderCode.optInt("id"));
			List<JSONObject> list2 = new ArrayList<JSONObject>();
			JSONObject param = new JSONObject();
			param.put("order_code", order_code);
			param.put("tenentid", tenantId);
			list2.add(param);
			data1.setData(list2);
			// 成功后更改订单状态，并下发订单
			JSONObject orderIssued = orderManagerService.orderIssued(data1);
			//微信推送信息
			this.sendTemplatMsg(tenantId, openId, order_code);
//			JSONObject bean1=new JSONObject();
//			bean1.put("order_code", order_code);
//			bean1.put("openid", openId);
//			bean1.put("tenantId", tenantId);
//			bean1.put("type1", "orderCancel");//卡充值
//			bean1.put("type", type);
//			bean1.put("discount_amount",  totalMoney);
//			bean1.put("payment", "card");
//			int count=20;
//			List<JSONObject> queryWXSystemParameterByParaCode = newWxSystemParameterDao.queryWXSystemParameterByParaCode(tenantId, "order_cancel_time");
//			if(queryWXSystemParameterByParaCode.size()>0){
//				JSONObject jsonObject2 = queryWXSystemParameterByParaCode.get(0);
//				count = jsonObject2.optInt("para_value");
//			}
//			bean1.put("LongTime", System.currentTimeMillis()+count*60*1000);
//			bean1.put("oper", 0);
//			taskRedisDao.lpush("wxpaymentbillnum".getBytes(), bean1);
			result.put("success", true);
			result.put("state", "1");
			return result;
		}
		double actual_pay=totalMoney-couponCost;
		newWxOrderDao.updateOrderState(tenantId, "01", order_code,actual_pay);
		result.put("actual_pay", actual_pay);
		result.put("shop_fee", actual_pay);
		
		result.put("state", "0");
		result.put("success", true);
		return result;
	}

	@Override
	public com.tzx.crm.bo.dto.Data notUseCreditForOrder(String tenantId,
			JSONObject jb) throws SystemException, Exception {
		int store_id = jb.optInt("store_id");
		String mobile_num = jb.optString("mobile_num");
		String order_code=jb.optString("order_code");
		String type = jb.optString("type");
		List<JSONObject> orderPaymentDetail = newWxOrderDao.getOrderPaymentDetail(tenantId, order_code);
		for (JSONObject jsonObject : orderPaymentDetail) {
			String payment_class = jsonObject.optString("payment_class");
			if(payment_class.equals("card_credit")){
				//积分支付
				String third_bill_code = jsonObject.optString("third_bill_code");
				com.tzx.crm.bo.dto.Data data1 = new com.tzx.crm.bo.dto.Data();
				data1.setOper(com.tzx.crm.base.constant.Oper.update);
				data1.setStore_id(store_id);
				data1.setTenancy_id(tenantId);
				JSONObject bean = new JSONObject();
				List<JSONObject> list = new ArrayList<JSONObject>();
				bean.put("mobile", mobile_num);// 电话
				bean.put("chanel", type);// 渠道
				bean.put("operator", "admin");// 操作人员
				bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
				bean.put("old_bill_code", third_bill_code);//交易单号
				list.add(bean);
				data1.setData(list);
				return data1;
			}
		}
		return null;
	}

	@Override
	public JSONObject sureOrderState(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		String order_code = jb.optString("order_code");
		JSONObject queryOrderState = newWxOrderDao.queryOrderState(tenantId, order_code);
		return queryOrderState;
	}

	@Override
	public JSONObject notUseCredit(String tenantId, JSONObject jb)
			throws SystemException, Exception {
		JSONObject result=new JSONObject();
		result.put("success", false);
		String bill_code = jb.optString("order_code");
		String openid = jb.optString("openid");
		String type = jb.optString("type");
		//查询积分
		List<JSONObject> queryUseCreditByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, bill_code,"card_credit");
		if(queryUseCreditByOrder.size()>0){
			//有使用积分支付的
			int store_id = jb.optInt("store_id");
			JSONObject jsonObject = queryUseCreditByOrder.get(0);
			//撤销积分消费
			JSONObject queryMemberBaseInfo = newWxMemberDao.queryMemberBaseInfo(tenantId, openid);
			String mobile_num = queryMemberBaseInfo.optString("mobile_num");
			String third_bill_code = jsonObject.optString("third_bill_code");
			com.tzx.crm.bo.dto.Data data1 = new com.tzx.crm.bo.dto.Data();
			data1.setOper(com.tzx.crm.base.constant.Oper.update);
			data1.setStore_id(store_id);
			data1.setTenancy_id(tenantId);
			JSONObject bean = new JSONObject();
			List<JSONObject> list = new ArrayList<JSONObject>();
			bean.put("mobil", mobile_num);// 电话
			bean.put("chanel", type);// 渠道
			bean.put("operator", "admin");// 操作人员
			bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
			bean.put("old_bill_code", third_bill_code);//交易单号
			bean.put("business_date", DateUtil.getNowDateYYDDMMHHMMSS());
			bean.put("shift_id", "WX");
			bean.put("batch_no", "WX");
			bean.put("bill_code", bill_code);
			list.add(bean);
			data1.setData(list);
			//撤销接口
			try {
				bonusPointManageService.bonusPointConsume(data1);
			} catch (Exception e) {
				e.printStackTrace();
				result.put("msg", e.getMessage());
				result.put("success", false);
				return result;
			}
		}
		//优惠劵
		List<JSONObject> queryUseCouponByOrder = newWxOrderDao.queryUseCouponByOrder(tenantId, bill_code,"coupons");
		if(queryUseCouponByOrder.size()>0){
			//有使用优惠劵
			int store_id = jb.optInt("store_id");
			com.tzx.crm.bo.dto.Data param=new com.tzx.crm.bo.dto.Data();
			param.setOper(Oper.init);
			param.setTenancy_id(tenantId);
			
			List<JSONObject> list = new ArrayList<JSONObject>();
			JSONObject bean=new JSONObject();
			bean.put("chanel",type);
			List<JSONObject> couponList= new ArrayList<JSONObject>();
			for (JSONObject jsonObject : queryUseCouponByOrder) {
				JSONObject bean1=new JSONObject();
				store_id = jsonObject.optInt("store_id");
				bean1.put("coupons_code", jsonObject.optString("pay_no"));
				couponList.add(bean1);
			}
			param.setStore_id(store_id);
			bean.put("couponslist", couponList);
			list.add(bean);
			param.setData(list);
			//撤销接口
			try {
				couponsService.coupons(param);
			} catch (Exception e) {
				e.printStackTrace();
				result.put("msg", e.getMessage());
				result.put("success", false);
				return result;
			}
		}
		boolean deleteOrderByOrderCode = newWxOrderDao.deleteOrderByOrderCode(tenantId, "cc_order_repayment", bill_code);
		if(!deleteOrderByOrderCode){
			result.put("msg", "亲，撤销积分支付失败");
			result.put("success", false);
			return result;
		}
		boolean updateOrderState1 = newWxOrderDao.updateOrderState1(tenantId, "08", "04", bill_code);
		if(!updateOrderState1){
			result.put("msg", "撤销失败");
			result.put("success", false);
			return result;
		}
		result.put("success", true);
		return result;
	}
	

	//微信点餐增加积分新增处理
	private void creditDispose(String tenantId, JSONObject jb, JSONObject result, String chanel)
			throws SystemException, Exception{
		boolean isMember = false;	//是否会员
		JSONObject creditInfo = new JSONObject();
		try{	//增加积分处理
			isMember = addCredit(tenantId, jb, result, creditInfo, chanel);
			if(!creditInfo.containsKey("status")){
				creditInfo.put("status", 0);	//增加积分成功
			}
		} catch(Exception e) {
			creditInfo.put("status", 1);	//增加积分失败
			creditInfo.put("msg", "增加积分处理时出现异常");	
		}
		if(!isMember){	//不是会员不保存数据
			return;
		}
		
		try{	//保存数据处理
			dao.insertIgnorCase(tenantId, "wx_credit_log", creditInfo);
			//增加积分成功，才保存下发数据
			if(0 == creditInfo.getInt("status")){
				JSONObject data = issueData(jb, creditInfo);
				dao.insertIgnorCase(tenantId, "cc_order_credit", data);
			}
		} catch(Exception e) {
			e.printStackTrace();
			System.out.println("微信增加积分保存数据时失败：" + creditInfo.toString());
		}
		return;
	}

	//下发数据封装
	private JSONObject issueData(JSONObject jb, JSONObject creditInfo){
		JSONObject ret = new JSONObject();
		
		ret.put("tenancy_id", creditInfo.get("tenancy_id"));
		ret.put("store_id", creditInfo.get("store_id"));
		
		ret.put("bill_num", jb.get("bill_num"));//账单编号
		ret.put("order_code", jb.get("order_code"));//账单编号
		ret.put("bill_code", creditInfo.get("credit_code"));//增加积分账单号
		
		ret.put("report_date", DateUtil.getNowDateYYDDMM());
		ret.put("type", " JF01");//账单会员的操作类型: JF01 消费赠积分,ZK02 会员折扣与会员价 ,JZ03 会员卡结账
		
		ret.put("amount", creditInfo.get("total_amount"));
		ret.put("customer_code", creditInfo.get("customer_code"));
		ret.put("mobil", creditInfo.get("mobile"));
		
		ret.put("last_updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
		
		return ret;
	}
	
	//增加积分处理
	private boolean addCredit(String tenantId, JSONObject jb, JSONObject result, 
			JSONObject creditInfo, String chanel){
		String openId = jb.optString("openid");
		if(openId == null || "".equals(openId)){
			//没有openid非会员直接返回
			return false;
		}
		String orderCode = jb.optString("order_code");
		int storeId = jb.optInt("storeId");
		
		//保存到日志中的值
		creditInfo.put("tenancy_id", tenantId);
		creditInfo.put("store_id", storeId);
		creditInfo.put("bill_code", orderCode);
		creditInfo.put("openid", openId);
		creditInfo.put("chanel", chanel);
		creditInfo.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
		
		System.out.println("增加积分处理：" + creditInfo.toString());
		
		//取会员手机号,会员编号
		String mobileSql = "select a.mobile_num mobile_num,b.code code "
				+ " from wx_member a left join crm_customer_info b on "
				+ " a.mobile_num=b.mobil where a.isbind='true' and "
				+ " a.openid='" + openId + "'";
		try {
			List<JSONObject> list = dao.query4Json(tenantId, mobileSql);
			JSONObject object = list.get(0);
			creditInfo.put("mobile", object.getString("mobile_num"));
			creditInfo.put("customer_code", object.getString("code"));
		} catch (Exception e) {
			e.printStackTrace();
			//没有会员信息非会员处理
			return false;
		}
		//判断微信支付和优惠券是否可积分
		boolean wechat_pay = false,coupons = false;
		String paySql = "select payment_class,if_jifen from payment_way where payment_class in('coupons','wechat_pay')";
		try {
			List<JSONObject> list = dao.query4Json(tenantId, paySql);
			for(JSONObject obj : list){
				if("wechat_pay".equals(obj.getString("payment_class"))){
					if(obj.getInt("if_jifen") == 1){
						wechat_pay = true;
					}
				} else {
					if(obj.getInt("if_jifen") == 1){
						coupons = true;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		/////////计算可积分总金额////////
		double total_money = 0;
		//微信支付金额
		if(wechat_pay){
			total_money = result.getDouble("total_amount");
		}
		//优惠券金额
		if(coupons){
			total_money = BigDecimal.valueOf(total_money)
					.add(new BigDecimal(result.getDouble("coupons_cost_money")))
					.doubleValue();
		}
		creditInfo.put("total_amount", total_money);
		
		//准备调用接口参数
		com.tzx.crm.bo.dto.Data param = new com.tzx.crm.bo.dto.Data();
		param.setTenancy_id(tenantId);
		param.setStore_id(storeId);
		param.setOper(Oper.add);
		
		List<Map<Object, Object>> data = new ArrayList<Map<Object, Object>>(1);
		Map<Object, Object> map = new HashMap<Object, Object>();
		
		map.put("mobil", creditInfo.getString("mobile"));
		map.put("consume_creditmoney", total_money);
		map.put("reason", "微信会员增加积分");
		map.put("chanel", chanel);
		map.put("bill_code", orderCode);
		map.put("operator", "wx");
		map.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
		
		data.add(map);
		param.setData(data);
		
		//调用增加积分接口
		try {
			Map<String, Object> customerCredit = bonusPointManageService.customerCredit(param);
			//增加积分内部订单号，用于撤销积分用
			creditInfo.put("credit_code", customerCredit.get("bill_code"));
		} catch (Exception e) {
			e.printStackTrace();
			creditInfo.put("status", 1);	//增加积分失败
			creditInfo.put("msg", "调用增加积分接口失败");	
			return true;
		}
		
		return true;
	}
}
