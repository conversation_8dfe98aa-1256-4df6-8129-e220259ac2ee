package com.tzx.cc.invoice.electronic.service.impl;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import sun.misc.BASE64Encoder;

import com.alibaba.fastjson.JSON;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.Type;
import com.tzx.cc.invoice.electronic.cache.SysparamCache;
import com.tzx.cc.invoice.electronic.cont.ElectronicInvoiceConst;
import com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao;
import com.tzx.cc.invoice.electronic.service.ElecInvoiceService;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoicePropertyUtil;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoiceUtils;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoiceWebServiceUtils;
import com.tzx.cc.invoice.electronic.util.KeyUtils;
import com.tzx.cc.invoice.electronic.util.UUIDUtils;
import com.tzx.cc.invoice.electronic.util.XmlUtils;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.payment.news.cont.Contant;

/**
 * <AUTHOR>
 *
 */
@Service(ElecInvoiceService.NAME)
public class ElecInvoiceServiceImpl implements ElecInvoiceService {
	private static final Logger logger = Logger
			.getLogger(ElecInvoiceServiceImpl.class);
	
	@Autowired
	private ElecInvoiceDao elecInvoiceDao;
	
	@Resource(name = "genericDaoImpl")
	private GenericDao		dao;
	
	@Resource(name="saasRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

	/**
	 * 验证data属性
	 * @param data
	 * @param result
	 * @return
	 */
	private boolean validaData(Data data,Data result){
		String tenancyId = data.getTenancy_id();
		if(StringUtils.isBlank(tenancyId)) {
			result.setMsg("参数：tenancy_id不允许为空");
			result.setCode(Contant.ILLEGAL_PARAM);
			logger.info("参数：tenancy_id不允许为空");
			return false;
		}
		return true;
	}
	/**
	 * 生成电子发票
	 * @param data
	 * @param result
	 * @throws Exception
	 */
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#issueElectronicInvoice(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void issueElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		String serviceType = param.optString("SERVICE_TYPE");
		if(StringUtils.isBlank(serviceType)) {
			result.setMsg("业务类型不能为空");
            return;
		}
		
		Type type = data.getType();
		
		JSONObject json = elecInvoiceDao.getRequestInfo(data.getTenancy_id(),data.getStore_id());
		if(json==null) {
			result.setMsg("总部未配置纳税人识别号，税率等信息");
			return;
		}
		
		if(Type.ISSUE_ELECTRONIC_INVOICE.equals(type)) {
			String isEleServiceType = SysparamCache.getSysparam(data.getTenancy_id(), data.getStore_id(), serviceType);
            if(!(StringUtils.equals(isEleServiceType, "1")||StringUtils.equals(isEleServiceType, "4"))) {
				result.setMsg("对应业务类型不支持开通电子发票");
				return;
			}
			ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param);
		} else {
			ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param,"XSF_NSRSBH");
		}
		
		
		//验证参数是否全
		boolean validate = validate(param, result,"SERVICE_TYPE","XSF_NSRSBH","data","SL","DDRQ");
		if(!validate) {
			return;
		}
		validate = issueElectronicInvoiceValid(data,result,param);
		if(!validate) {
			return;
		}
		String reqUrl = buildUrl(data,result,param);
		System.out.println(reqUrl);
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		JSONObject resultJson = new JSONObject();
		resultJson.put("url", reqUrl);
		returnList.add(resultJson);
		result.setData(returnList);
	}
	
	/**
	 * @param data
	 * @param result
	 * @param param
	 * @throws ParseException 
	 * @throws UnsupportedEncodingException 
	 */
	private String buildUrl(Data data, Data result, JSONObject param) throws ParseException, UnsupportedEncodingException {
		StringBuffer sb = new StringBuffer();
		sb.append(param.optString("XSF_NSRSBH")).append("#");
		sb.append(param.optString("FPQQLSH")).append("#");
		sb.append(data.getStore_id()).append("#");
		//处理订单日期格式
		String dataorstr = param.optString("DDRQ");
		Date parseDate = DateUtils.parseDate(dataorstr, new String[]{"yyyy-MM-dd"});
		String datastr = DateFormatUtils.format(parseDate, "yyMMdd");
		
		sb.append(datastr).append("#");
		sb.append(param.optString("JSHJ")).append("#");
		sb.append(ElectronicInvoiceConst.ELECTRON_ICINVOICE_XMMC).append("#");
		sb.append(param.optString("SL"));
		
		String md5str = KeyUtils.encryptASE_MD5(sb.toString(),ElectronicInvoicePropertyUtil.getMsg("third_password"));
		sb.append("#").append(md5str);
		
		BASE64Encoder base64en = new BASE64Encoder();
		String newstr = base64en.encode(sb.toString().trim().getBytes("utf-8"));
		newstr = newstr.replace("\r\n", "");
		newstr = newstr.replace("\n", "");
		newstr = ElectronicInvoicePropertyUtil.getMsg("third_page_url") + newstr;
		return newstr;
	}
	
	/**
	 * 验证发票是否符合规则
	 * @param data
	 * @param result
	 * @param param
	 * @return
	 * @throws Exception 
	 */
	private boolean issueElectronicInvoiceValid(Data data, Data result,
			JSONObject param) throws Exception {
		String dh = param.optString("DH");
		JSONObject json = elecInvoiceDao.queryInfoByOrderNo(data.getTenancy_id(),dh);
		String jshj = calsJSHJ(data,result,param);
		if(json==null) {
			String FPQQLSH = String.valueOf(UUIDUtils.next());
			param.put("FPQQLSH", FPQQLSH);
			//计算价税合计
			param.put("JSHJ", jshj);
			json = issueElectronicInvoiceSave(data,result,param);
		} else {
		    param.put("FPQQLSH", json.optString("invoice_flow_number"));
		}
		json.put("total_tax_amount", jshj);
		if(StringUtils.isBlank(jshj) || StringUtils.equals(jshj, "null")) {
		    result.setMsg("计算出来的价税合计为空");
            return false;
		}
		//把计算的价税合计存入param中
		param.put("JSHJ", jshj);
		String state = json.optString("invoice_state");
//		if(StringUtils.equals(state, ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS)) {
//			result.setMsg("此发票单号已开具过");
//			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
//			return false;
//		}
		if(StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS, state)
				) {
			result.setMsg("此发票单号已经取消");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return false;
		}
		
		//二维码打印期限
		String ewmdyxq = SysparamCache.getSysparam(data.getTenancy_id(), "dzfp_ewmdyxq");
		String dataorstr = param.optString("DDRQ");
		if(StringUtils.isBlank(ewmdyxq)) {
			ewmdyxq = "7";
		}
		boolean expire = ElectronicInvoiceUtils.isExpire(dataorstr, Integer.parseInt(ewmdyxq));
		if(expire) {
			result.setMsg("二维码有效期为"+ewmdyxq+"天，现在已经失效了");
			return false;
		}
		
		//向redis中存储流水号和商户号的对应关系
		JSONObject redisJson = new JSONObject();
		redisJson.put("ID", json.optString("id"));
		redisJson.put("FPQQLSH", param.optString("FPQQLSH"));
		redisJson.put("XSF_NSRSBH", param.optString("XSF_NSRSBH"));
		redisJson.put("tenancy_id", data.getTenancy_id());
		redisTemplate.opsForHash().put(ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE, redisJson.optString("FPQQLSH"), redisJson.toString());
		//设置期限为24小时
		redisTemplate.expire(ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE, ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE_EXPIRE, TimeUnit.HOURS);
		
		String xml = ElectronicInvoiceWebServiceUtils.sendWebService(ElectronicInvoiceWebServiceUtils.FPCX(param.optString("XSF_NSRSBH"), param.optString("FPQQLSH")));
		if(xml==null) {
			result.setMsg("网络请求超时");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_CONNECT);
			return false;
		}
		String xmlData = XmlUtils.getReturnData(xml);
		Element rootdata = XmlUtils.parse(xmlData);
		Element returncode = XmlUtils.getElement(rootdata, "RESPONSE_COMMON_FPCX","RETURNCODE");
		if(StringUtils.equals(returncode.getText(), "0000")) {
//			result.setMsg("此单号已开具过");
//			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			json.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS);
			dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", json);
//			return false;
		}
		return true;
	}
	/**
	 * 生成电子发票保存到数据库
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private JSONObject issueElectronicInvoiceSave(Data data, Data result,
			JSONObject param) throws Exception {
		JSONObject json = new JSONObject();
		json.put("tenancy_id", data.getTenancy_id());
		json.put("organ_id", data.getStore_id());
		json.put("invoice_service_type",param.optString("SERVICE_TYPE"));
		//流水号
		json.put("invoice_flow_number",param.optString("FPQQLSH"));
		//纳税识别号
		json.put("tax",param.optString("XSF_NSRSBH"));
		//价税合计
		json.put("total_tax_amount",param.optString("JSHJ"));
		//税率
		json.put("tax_rate", param.optString("SL"));
		//订单号
		json.put("order_code",param.optString("DH"));
		json.put("order_date",param.optString("DDRQ"));
		json.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT);
		json.put("electric_use_choose", ElectronicInvoiceConst.ELECTRIC_USE_CHOOSE_RH);
		Integer id = (Integer) dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", json);
		json.put("id", id);
		JSONObject json2 = new JSONObject();
		json2.put("electronic_id", id);
		//税率
		json2.put("tax_rate", param.optString("SL"));
		//项目名称
		json2.put("name", ElectronicInvoiceConst.ELECTRON_ICINVOICE_XMMC);
		dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_details", json2);
		return json;
	}

	/**
	 * 计算价税合计
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private String calsJSHJ(Data data, Data result, JSONObject param) throws Exception {
	    String paymentclassJeStr = param.optString("data");
	    if(StringUtils.isBlank(paymentclassJeStr)) {
	        return null;
	    }
	    com.alibaba.fastjson.JSONArray paymentclassJeArray = JSON.parseArray(paymentclassJeStr);
	    if(paymentclassJeArray==null || paymentclassJeArray.isEmpty()) {
	        return null;
	    }
	    StringBuffer paymentClassBuffer = new StringBuffer();
	    
	    //如果是门店传递过来的付款方式和金额，不用验证总部这边是否配置，直接把金额加起来即可
	    if(Type.ISSUE_ELECTRONIC_INVOICE_SCC.equals(data.getType())) {
	    	 BigDecimal bigDecimal = new BigDecimal(0);
	 	    //遍历传过来的paymentClass
	 	    for(Object jsonObject :paymentclassJeArray){
	 	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	 	        BigDecimal je = json.getBigDecimal("JE");
	 	        bigDecimal = bigDecimal.add(je);
	 	    }
	 	    if(bigDecimal.compareTo(BigDecimal.ZERO)==0) {
	 	    	return null;
	 	    }
	 	    bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
	    	return bigDecimal.toString();
		} 
	    
	    
	    //遍历paymentclassJeArray 拼接paymentClass的sql条件
	    for(Object jsonObject : paymentclassJeArray){
	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	        if(!json.containsKey("PAYMENT_CLASS") || !json.containsKey("JE")) {
	            return null;
	        }
	        String paymentClass = json.getString("PAYMENT_CLASS");
	        paymentClassBuffer.append(paymentClass).append(",");
	    }
	    String paymentClassStr = paymentClassBuffer.toString();
	    paymentClassStr = paymentClassStr.substring(0,paymentClassStr.length()-1);
	    paymentClassStr = paymentClassStr.replaceAll(",", "','");
	    List<JSONObject> queryPaymentWay = elecInvoiceDao.queryPaymentWay(data.getTenancy_id(),data.getStore_id(),paymentClassStr);
	    
	    BigDecimal bigDecimal = new BigDecimal(0);
	    //遍历传过来的paymentClass
	    for(Object jsonObject :paymentclassJeArray){
	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	        String paymentClass = json.getString("PAYMENT_CLASS");
	        //遍历查出来的paymentClass
	        for(JSONObject queryJson:queryPaymentWay){
	            String ifInvoicing = queryJson.optString("if_invoicing");
	            String queryPaymentClass = queryJson.optString("payment_class");
	            //如果查出来的和传进来的一样并且已开启，就把金额相加
	            if(StringUtils.equals(paymentClass, queryPaymentClass) && StringUtils.equals(ifInvoicing, "1")) {
	                BigDecimal je = json.getBigDecimal("JE");
	                bigDecimal = bigDecimal.add(je);
	                break;
	            }
	        }
	    }
	    if(bigDecimal.compareTo(BigDecimal.ZERO)==0) {
 	    	return null;
 	    }
	    bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
        return bigDecimal.toString();
    }
    /* (non-Javadoc)
	 * 取消电子发票接口
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#cancelElectronicInvoice(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void cancelElectronicInvoice(Data data, Data result)
			throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		JSONObject json = elecInvoiceDao.getRequestInfo(data.getTenancy_id(),data.getStore_id());
		if(json==null) {
		    result.setMsg("取消订单总部未配置纳税人识别号，税率等信息");
		    return;
		}
        ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param);
		boolean validate = validate(param, result, "DH","XSF_NSRSBH");
		if(!validate) {
			return;
		}
		JSONObject queryInfoByOrderNo = elecInvoiceDao.queryInfoByOrderNo(data.getTenancy_id(), param.optString("DH"));
		if(queryInfoByOrderNo==null) {
			result.setMsg("订单号无效，创建一个订单号");
			queryInfoByOrderNo = new JSONObject();
			queryInfoByOrderNo.put("tenancy_id", data.getTenancy_id());
			queryInfoByOrderNo.put("organ_id", data.getStore_id());
			queryInfoByOrderNo.put("order_code", param.optString("DH"));
			queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL);
			queryInfoByOrderNo.put("invoice_cancel_time", ElectronicInvoiceUtils.currentTime2Str());
			this.dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);

	        List<JSONObject> returnList = new ArrayList<JSONObject>();
	        JSONObject resultJson = new JSONObject();
	        resultJson.put("code", "0000");
			resultJson.put("msg", "取消成功");
			returnList.add(resultJson);
			result.setData(returnList);
			result.setSuccess(Boolean.TRUE);
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
			result.setMsg("调用成功");
		    return;
		}

		String FPQQLSH = queryInfoByOrderNo.optString("invoice_flow_number");
		String XSF_NSRSBH = param.optString("XSF_NSRSBH");
		String xml = ElectronicInvoiceWebServiceUtils.sendWebService(ElectronicInvoiceWebServiceUtils.CANCEL(XSF_NSRSBH, FPQQLSH));
		if(StringUtils.isBlank(xml)) {
			result.setSuccess(Boolean.FALSE);
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_CONNECT);
			result.setMsg("连接超时");
			return;
		}
		String returndata = XmlUtils.getReturnData(xml);
		Element rootdata = XmlUtils.parse(returndata);
		Element returncode = XmlUtils.getElement(rootdata, "RETURNCODE");
		Element returnmsg = XmlUtils.getElement(rootdata, "RETURNMSG");
		
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		JSONObject resultJson = new JSONObject();
		String code = returncode.getTextTrim();
		String msg =returnmsg.getText();
		if(StringUtils.equals(code, "0000")) {
		    queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS);
		    queryInfoByOrderNo.put("invoice_cancel_time",ElectronicInvoiceUtils.currentTime2Str());
		    dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
			//上传数据到OM
//			ElectronicInvoiceUtils.uploadOm(queryInfoByOrderNo);
		} else if(StringUtils.equals(code, "0002")) {
			logger.info("此订单的发票需要冲正,订单号为："+ param.optString("DH"));
			czfp(data,result,param,queryInfoByOrderNo);
			queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS);
			if(StringUtils.isEmpty(queryInfoByOrderNo.optString("invoice_cancel_time")) || StringUtils.equals("null", queryInfoByOrderNo.optString("invoice_cancel_time"))) {
				queryInfoByOrderNo.put("invoice_cancel_time",ElectronicInvoiceUtils.currentTime2Str());
				saveHcxx(data, param, queryInfoByOrderNo);
			}
		    dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
			//上传数据到OM
			ElectronicInvoiceUtils.uploadOm(queryInfoByOrderNo);
			return;
		}
		resultJson.put("code", code);
		resultJson.put("msg", msg);
		returnList.add(resultJson);
		result.setData(returnList);
		result.setSuccess(Boolean.TRUE);
		result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
		result.setMsg("调用成功");
	}
	private void saveHcxx(Data data, JSONObject param,
			JSONObject queryInfoByOrderNo) throws Exception {
		JSONObject hcJson = param.optJSONObject("hcJson");
		hcJson.put("tenancy_id", data.getTenancy_id());
		hcJson.put("organ_id", data.getStore_id());
		hcJson.put("order_date", queryInfoByOrderNo.optString("order_date"));
		hcJson.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS);
		hcJson.put("order_code", queryInfoByOrderNo.optString("order_code"));
		hcJson.put("invoice_service_type", queryInfoByOrderNo.optString("invoice_service_type"));
		hcJson.put("invoice_time", ElectronicInvoiceUtils.currentTime2Str());
//		hcJson.put("invoice_cancel_time", ElectronicInvoiceUtils.currentTime2Str());
		int insertIgnorCase = (int) dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", hcJson);
		
		//向redis中存储流水号和商户号的对应关系
		JSONObject redisJson = new JSONObject();
		redisJson.put("ID", insertIgnorCase);
		redisJson.put("FPQQLSH", hcJson.optString("invoice_flow_number"));
		redisJson.put("XSF_NSRSBH", hcJson.optString("tax"));
		redisJson.put("tenancy_id", data.getTenancy_id());
		redisTemplate.opsForHash().put(ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE, redisJson.optString("FPQQLSH"), redisJson.toString());
		//设置期限为24小时
		redisTemplate.expire(ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE, ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE_EXPIRE, TimeUnit.HOURS);
		
		JSONObject hcDetailsJson = param.optJSONObject("hcDetailsJson");
		hcDetailsJson.put("electronic_id", insertIgnorCase);
		dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_details", hcDetailsJson);
	}

	/**
	 * 冲正发票
	 * @param data
	 * @param result
	 * @param param 
	 * @param queryInfoByOrderNo
	 * @throws DocumentException 
	 */
	private void czfp(Data data, Data result, JSONObject param, JSONObject queryInfoByOrderNo) throws DocumentException {
		String XSF_NSRSBH = param.optString("XSF_NSRSBH");
		String FPQQLSH = queryInfoByOrderNo.optString("invoice_flow_number");
		
		//发起webservice请求第三方的查询接口，查询此订单号所对应流水号真正状态及完整信息
		String xml = ElectronicInvoiceWebServiceUtils.sendWebService(ElectronicInvoiceWebServiceUtils.FPCX(XSF_NSRSBH, FPQQLSH));
		String xmlData = XmlUtils.getReturnData(xml);
		Element rootdata = XmlUtils.parse(xmlData);
		Element returncode = XmlUtils.getElement(rootdata, "RESPONSE_COMMON_FPCX","RETURNCODE");
		if(!StringUtils.equals(returncode.getTextTrim(), "0000")) {
			result.setMsg("冲正发票时调用第三方未查到数据");
			logger.info("冲正发票时调用第三方未查到数据");
			logger.info("发票流水号为"+ FPQQLSH);
			return;
		}
		try {
			String fpkjXml = getFPKJXml(rootdata,param,queryInfoByOrderNo);
			String xmlData2 = XmlUtils.getReturnData(fpkjXml);
			Element rootdata2 = XmlUtils.parse(xmlData2);
			Element returncode2 = XmlUtils.getElement(rootdata2, "RESPONSE_COMMON_FPKJ","RETURNCODE");
			Element returnMsg = XmlUtils.getElement(rootdata2, "RESPONSE_COMMON_FPKJ","RETURNMSG");
			List<JSONObject> returnList = new ArrayList<JSONObject>();
			JSONObject resultJson = new JSONObject();
			resultJson.put("code", returncode2==null?StringUtils.EMPTY:returncode2.getTextTrim());
			resultJson.put("msg", XmlUtils.getElement(rootdata2, returnMsg==null?StringUtils.EMPTY:returnMsg.getTextTrim()));
			returnList.add(resultJson);
			result.setData(returnList);
			result.setSuccess(Boolean.TRUE);
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
			result.setMsg("调用成功");
		} catch (IOException e) {
			logger.info(e);
		}
		return;
	}
	
	/**
	 * @param returncode
	 * @param data 
	 * @param param 
	 * @param json 
	 * @return
	 * @throws IOException 
	 * @throws DocumentException 
	 */
	private String getFPKJXml(Element returncode, JSONObject param, JSONObject json) throws IOException, DocumentException {
		String XSF_NSRSBH = param.optString("XSF_NSRSBH");
		Element eleInfoElement = XmlUtils.getElement(returncode, "RESPONSE_COMMON_FPCX","COMMON_FPCX_FPT");
	   	String XSF_MC = eleInfoElement.element("XSF_MC")==null?StringUtils.EMPTY:eleInfoElement.element("XSF_MC").getTextTrim();
	   	String XSF_DZDH = eleInfoElement.element("XSF_DZDH")==null?StringUtils.EMPTY:eleInfoElement.element("XSF_DZDH").getTextTrim();
	   	String GMF_MC = eleInfoElement.element("GMF_MC")==null?StringUtils.EMPTY:eleInfoElement.element("GMF_MC").getTextTrim();
	   	String KPR = eleInfoElement.element("KPR")==null?StringUtils.EMPTY:eleInfoElement.element("KPR").getTextTrim();
	   	String JSHJ = eleInfoElement.element("JSHJ")==null?StringUtils.EMPTY:eleInfoElement.element("JSHJ").getTextTrim();
	   	String HJJE = eleInfoElement.element("HJJE")==null?StringUtils.EMPTY:eleInfoElement.element("HJJE").getTextTrim();
	   	String HJSE = eleInfoElement.element("HJSE")==null?StringUtils.EMPTY:eleInfoElement.element("HJSE").getTextTrim();
	   	String FP_DM = eleInfoElement.element("FP_DM")==null?StringUtils.EMPTY:eleInfoElement.element("FP_DM").getTextTrim();
	   	String FP_HM = eleInfoElement.element("FP_HM")==null?StringUtils.EMPTY:eleInfoElement.element("FP_HM").getTextTrim();
	   	String JQBH = eleInfoElement.element("JQBH")==null?StringUtils.EMPTY:eleInfoElement.element("JQBH").getTextTrim();
	   	String YFP_DM = eleInfoElement.element("YFP_DM")==null?StringUtils.EMPTY:eleInfoElement.element("YFP_DM").getTextTrim();
	   	String YFP_HM = eleInfoElement.element("YFP_HM")==null?StringUtils.EMPTY:eleInfoElement.element("YFP_HM").getTextTrim();
	   	String KPLX = eleInfoElement.element("KPLX")==null?StringUtils.EMPTY:eleInfoElement.element("KPLX").getTextTrim();
	   	String KPRQ = eleInfoElement.element("KPRQ")==null?StringUtils.EMPTY:eleInfoElement.element("KPRQ").getTextTrim();
	   	
	   	//补全蓝字发票信息
	   	json.put("invoice_type", KPLX);
	   	json.put("seller_name", XSF_MC);
	   	json.put("seller_address", XSF_DZDH);
	   	json.put("buyer_name", GMF_MC);
	   	json.put("drawer", KPR);
	   	json.put("original_invoice_code", YFP_DM);
	   	json.put("original_invoice_number", YFP_HM);
	   	json.put("total_tax_amount", JSHJ);
	   	json.put("total_amount", HJJE);
	   	json.put("tax_amount", HJSE);
	   	json.put("control_device_code", JQBH);
	   	json.put("invoice_code", FP_DM);
	   	json.put("invoice_number", FP_HM);
	   	if(StringUtils.isNotBlank(KPRQ)) {
			try {
				Date parseDate = DateUtils.parseDate(KPRQ, new String[]{"yyyyMMddHHmmss"});
				KPRQ = DateFormatUtils.format(parseDate, "yyyy-MM-dd HH:mm:ss");
			} catch (ParseException e) {
				logger.error(e);
				e.printStackTrace();
			}
   		 	
	   	 }
	   	 json.put("invoice_time", KPRQ);
	   	
		Element root = XmlUtils.getFPKJXML();
		Element element = XmlUtils.getElement(root, "REQUEST_COMMON_FPKJ","COMMON_FPKJ_FPT");
		String FPQQLSH = String.valueOf(UUIDUtils.next());
		element.element("FPQQLSH").setText(FPQQLSH);
		element.element("KPLX").setText("1");
		element.element("XSF_NSRSBH").setText(XSF_NSRSBH);
		element.element("XSF_MC").setText(XSF_MC);
		element.element("XSF_DZDH").setText(XSF_DZDH);
		element.element("GMF_MC").setText(GMF_MC);
		element.element("KPR").setText(KPR);
		element.element("JSHJ").setText("-"+JSHJ);
		element.element("HJJE").setText("-"+HJJE);
		element.element("HJSE").setText("-"+HJSE);
		String DDRQ = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
		element.element("DDRQ").setText(DDRQ);
		element.element("YFP_DM").setText(FP_DM);
		element.element("YFP_HM").setText(FP_HM);
		
		Element eleInfoDetailElement = XmlUtils.getElement(returncode, "RESPONSE_COMMON_FPCX","COMMON_FPCX_XMXXS","COMMON_FPCX_XMXX");
    	String FPHXZ = eleInfoDetailElement.element("FPHXZ")==null?StringUtils.EMPTY:eleInfoDetailElement.element("FPHXZ").getTextTrim();
    	String XMJE = eleInfoDetailElement.element("XMJE")==null?null:eleInfoDetailElement.element("XMJE").getTextTrim();
    	String SL = eleInfoDetailElement.element("SL")==null?null:eleInfoDetailElement.element("SL").getTextTrim();
    	String SE = eleInfoDetailElement.element("SE")==null?null:eleInfoDetailElement.element("SE").getTextTrim();
    	String XMMC = eleInfoDetailElement.element("XMMC")==null?StringUtils.EMPTY:eleInfoDetailElement.element("XMMC").getTextTrim();
		
		Element mx = XmlUtils.getElement(root, "REQUEST_COMMON_FPKJ","COMMON_FPKJ_XMXXS","COMMON_FPKJ_XMXX");
		mx.element("FPHXZ").setText(FPHXZ);
		mx.element("XMMC").setText(XMMC);
		mx.element("XMJE").setText("-"+XMJE);
		mx.element("SL").setText(SL);
		mx.element("SE").setText("-"+SE);
		
		String context = root.asXML();
		context = context.replaceAll("<", ElectronicInvoiceConst.LESS_THAN_CODE);
		context = context.replaceAll(">", ElectronicInvoiceConst.GREATER_THAN_CODE);
		String xml = ElectronicInvoiceWebServiceUtils.sendWebService(ElectronicInvoiceWebServiceUtils.FPKJ(XSF_NSRSBH, context));
		
		JSONObject hcJson = new JSONObject();
		hcJson.put("invoice_flow_number", FPQQLSH);
		hcJson.put("invoice_type", "1");
		hcJson.put("tax", XSF_NSRSBH);
		hcJson.put("seller_name", XSF_MC);
		hcJson.put("seller_address", XSF_DZDH);
		hcJson.put("buyer_name", GMF_MC);
		hcJson.put("drawer", KPR);
		hcJson.put("total_tax_amount", "-"+JSHJ);
		hcJson.put("tax_amount", "-"+HJSE);
		hcJson.put("total_amount", "-"+HJJE);
		hcJson.put("original_invoice_code", FP_DM);
		hcJson.put("tax_rate", SL);
		param.put("hcJson", hcJson);
		
		JSONObject hcDetailsJson = new JSONObject();
		hcDetailsJson.put("invoice_type", "1");
		hcDetailsJson.put("name", XMMC);
		hcDetailsJson.put("amount", "-"+XMJE);
		hcDetailsJson.put("tax_rate", SL);
		hcDetailsJson.put("tax_amount", "-"+SE);
		param.put("hcDetailsJson", hcJson);
		
		return xml;
	}
	
	@Override
	public void queryElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		boolean validate = validate(param, result, "FPQQLSH","XSF_NSRSBH");
		if(!validate) {
			return;
		}
		String FPQQLSH = param.optString("FPQQLSH");
		String XSF_NSRSBH = param.optString("XSF_NSRSBH");
		String xml = ElectronicInvoiceWebServiceUtils.sendWebService(ElectronicInvoiceWebServiceUtils.FPCX(XSF_NSRSBH, FPQQLSH));
		String xmlData = XmlUtils.getReturnData(xml);
		
		Element rootdata = XmlUtils.parse(xmlData);
		Element returncode = XmlUtils.getElement(rootdata, "RESPONSE_COMMON_FPCX","RETURNCODE");
		Element returnmsg = XmlUtils.getElement(rootdata,"RESPONSE_COMMON_FPCX", "RETURNMSG");
		
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		JSONObject resultJson = new JSONObject();
		resultJson.put("code", returncode.getText());
		resultJson.put("msg", returnmsg.getText());
		returnList.add(resultJson);
		result.setData(returnList);
		result.setSuccess(Boolean.TRUE);
		result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
	}
	
	/**
	 * 获取到Data对象中的data  josn
	 * @param data
	 * @return
	 */
	public JSONObject getParam(Data data) {
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		if(list==null || list.isEmpty()) {
			return null;
		}
		// 获取到传入的参数
		JSONObject param = JSONObject.fromObject(list.get(0));
		return param;
	}
	
	
	/**
	 * 处理参数
	 * @param param
	 * @param columns
	 * @return
	 */
	public JSONObject pakJSONObject(JSONObject param,
			String... columns) {
		JSONObject json = new JSONObject();
		for (String column : columns) {
			if (param.containsKey(column)) {
				json.put(column, param.getString(column));
			}
		}
		return json;
	}
	
	/**
	 * 处理参数
	 * @param param
	 * @param columns
	 * @return
	 */
	public Map<String, String> pakMap(JSONObject param,
			String... columns) {
		Map<String, String> map = new TreeMap<String, String>();
		for (String column : columns) {
			if (param.containsKey(column)) {
				map.put(column, param.getString(column));
			}
		}
		return map;
	}
	
	/**
	 * 验证传进的参数格式是否正确
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public boolean validate(JSONObject param, Data result,
			String... columns) {
		for (String column : columns) {
			if (!param.containsKey(column)
					|| StringUtils.isBlank(param.getString(column))) {
				result.setMsg("参数：" + column + "不允许为空");
				result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_PARAM);
				logger.info("参数：" + column + "不允许为空");
				return false;
			}
		}
		return true;
	}
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#issueElectronicInvoice(com.tzx.cc.bo.dto.Data, com.tzx.cc.bo.dto.Data, javax.servlet.http.HttpServletResponse)
	 */
	@Override
	public void issueElectronicInvoice(Data data, Data result,
			HttpServletResponse response) throws Exception {
		issueElectronicInvoice(data, result);
		JSONObject param = getParam(result);
		if(param==null) {
			return;
		}
		String url = param.optString("url");
		response.sendRedirect(url);
	}
	@Override
	public void sqkp(JSONObject json, JSONObject result) throws Exception {
		// TODO Auto-generated method stub
		
	}
	@Override
	public void fpkj(JSONObject json, JSONObject result) throws Exception {
		// TODO Auto-generated method stub
		
	}
	@Override
	public void orderCallback(String xmlParam, JSONObject result)
			throws Exception {
		// TODO Auto-generated method stub
		
	}
}
