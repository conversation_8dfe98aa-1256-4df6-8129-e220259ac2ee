package com.tzx.report.common.constant;
 
/**
 * saas
 * <AUTHOR>
 *
 */
public class EngineConstantArea {


	/*************************************************************************SAAS_BI_2019_05**************************************************************************/


	/**
	 * 豪享来-集团营业分析
	 */
	public static final  String GROUP_BUSINESS_ANALYSIS = "SELECT sql from saas_report_engine WHERE  report_num = 'SAAS_BI_2019_05' AND sql_type = 'HXLYYFX00'";

	public static final  String GROUP_BUSINESS_ANALYSIS_01 = "SELECT sql from saas_report_engine WHERE  report_num = 'SAAS_BI_2019_05' AND sql_type = 'HXLYYFX01'";


	public static final  String GROUP_BUSINESS_ANALYSIS_02 = "SELECT sql from saas_report_engine WHERE  report_num = 'SAAS_BI_2019_05' AND sql_type = 'HXLYYFX02'";


	/**
	 * 豪享来--营业日报告
	 */

	public static final  String BUSINESS_DAY_BAO00 = "SELECT sql from saas_report_engine WHERE  report_num = 'SAAS_BI_2019_05' AND sql_type = 'HXLYYRBG00'";
	public static final  String BUSINESS_DAY_BAO01 = "SELECT sql from saas_report_engine WHERE  report_num = 'SAAS_BI_2019_05' AND sql_type = 'HXLYYRBG01'";
	public static final  String BUSINESS_DAY_BAO02 = "SELECT sql from saas_report_engine WHERE  report_num = 'SAAS_BI_2019_05' AND sql_type = 'HXLYYRBG02'";



	/**
	 * 豪享来--营业汇总统计查询
	 */

	public static final  String SUMMARY_OF_BUSINESS_00 = "SELECT sql from saas_report_engine WHERE  report_num = 'SAAS_BI_2020_02_25' AND sql_type = 'HXLYYHZTJCX00'";
	public static final  String SUMMARY_OF_BUSINESS_01 = "SELECT sql from saas_report_engine WHERE  report_num = 'SAAS_BI_2020_02_25' AND sql_type = 'HXLYYHZTJCX01'";
	public static final  String SUMMARY_OF_BUSINESS_02 = "SELECT sql from saas_report_engine WHERE  report_num = 'SAAS_BI_2020_02_25' AND sql_type = 'HXLYYHZTJCX02'";

	/**
	 * 豪享来--让利分析明细表
	 */

	public static final  String CONCESSION_00 = "SELECT sql FROM saas_report_engine WHERE report_num = 'SAAS_BI_2019_07' AND sql_type = 'HXLRLFXMX00'";//标题
	public static final  String CONCESSION_01 = "SELECT sql FROM saas_report_engine WHERE report_num = 'SAAS_BI_2019_07' AND sql_type = 'HXLRLFXMX01'";//明细
	public static final  String CONCESSION_02= "SELECT sql FROM saas_report_engine WHERE report_num = 'SAAS_BI_2019_07' AND sql_type = 'HXLRLFXMX02'";//汇总

	/*
	*
	*  豪享来 -门店交班报表
	*  */
	public static final String HAND_OVER_DUTY_01 = "  select sql from saas_report_engine   where report_num = 'SAAS_BI_2019_10' and sql_type = 'HXLMDSYBB01'";

/*************************************************************************SAAS_BI_2016_02**************************************************************************/
	
	/**
	 *	员工销售提成查询YGCPHZL1（按菜品汇总）
	 */
	public static final String ENGINE_CHECK_THE_FIRST_FLOOR_ACCORDING_TO_THE_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGCPHZL1'";
	
	/**
	 *	员工销售提成查询YGCPHZL2
	 */
	public static final String ENGINE_CHECK_THE_SECOND_LAYERS_ACCORDING_TO_THE_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGCPHZL2'";
	
	/**
	 *	员工销售提成查询YGCPHZL3
	 */
	public static final String ENGINE_CHECK_THE_THIRD_LAYERS_ACCORDING_TO_THE_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGCPHZL3'";
	
	/**
	 *	员工销售提成查询YGCPHZL0
	 */
	public static final String ENGINE_TOTAL_ACCORDING_TO_DISHES_QUERY_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGCPHZL0'";
	
	
	
	/**
	 *	员工销售提成查询YGYGHZL1（按员工汇总）
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_ACCORDING_TO_THE_STAFF = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGYGHZL1'";
	
	/**
	 *	员工销售提成查询YGYGHZL2
	 */
	public static final String ENGINE_COLLECT_SECOND_LAYERS_BY_STAFF = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGYGHZL2'";
	
	/**
	 *	员工销售提成查询YGYGHZL3
	 */
	public static final String ENGINE_COLLECT_THIRD_LAYERS_BY_STAFF = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGYGHZL3'";
	
	/**
	 *	员工销售提成查询YGYGHZL0
	 */
	public static final String ENGINE_TOTAL_COLLECT_BY_EMPLOYEE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGYGHZL0'";
	
	
	
	/**
	 *	员工销售提成查询YGRQHZL1   （按日期汇总）
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGRQHZL1'";
	
	/**
	 *	员工销售提成查询YGRQHZL2
	 */
	public static final String ENGINE_COLLECT_SECOND_LAYERS_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGRQHZL2'";
	
	/**
	 *	员工销售提成查询YGRQHZL3
	 */
	public static final String ENGINE_COLLECT_THIRD_LAYERS_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGRQHZL3'";
	
	/**
	 *	员工销售提成查询YGRQHZL0
	 */
	public static final String ENGINE_TOTAL_AGGREGATED_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGRQHZL0'";
	
	
	/**
	 *	员工销售提成查询YGJGHZL1（按机构汇总）
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_ACCORDING_TO_THE_ORGANIZATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGJGHZL1'";
	
	/**
	 *	员工销售提成查询YGJGHZL2
	 */
	public static final String ENGINE_COLLECT_SECOND_LAYERS_ACCORDING_TO_THE_ORGANIZATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGJGHZL2'";
	
	/**
	 *	员工销售提成查询YGJGHZL3
	 */
	public static final String ENGINE_COLLECT_THIRD_LAYERS_ACCORDING_TO_THE_ORGANIZATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGJGHZL3'";
	
	/**
	 *	员工销售提成查询YGJGHZL0
	 */
	public static final String ENGINE_TOTAL_COLLECT_BY_ORGANIZATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGJGHZL0'";
	
	//*********************************2017-11-08*******************************
	/**
	 *	员工销售提成查询YGRQHZL1   （按日期 大类 汇总）
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_AND_ITEMCLASS_BY_DATE1 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='RQLBL1'";
	
	/**
	 *	员工销售提成查询YGRQHZL2
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_AND_ITEMCLASS_BY_DATE2 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='RQLBL2'";
	
	/**
	 *	员工销售提成查询YGRQHZL3
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_AND_ITEMCLASS_BY_DATE3 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='RQLBL3'";
	
	/**
	 *	员工销售提成查询YGRQHZL0
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_AND_ITEMCLASS_BY_DATE0 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='RQLBL0'";
	
	/**
	 * 按照类别钻取 明细
	 */
	public static final String GET_FOODITEM_BY_ITEMCODE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='RQLBITEML1'";
	
	
	
	/*************************************************************************SAAS_BI_2016_11**************************************************************************/
	
	/**
	 * 菜品销售汇总查询函数
	 */
	
	public static final String FOOD_SALES_SUMMARY_REPORT = "select SQL from saas_report_engine where report_num='SAAS_BI_2016_11' and sql_type = 'FUNCTION'";

	/**
	 * 菜品销售汇总查询函数-区域
	 */
	public static final String FOOD_SALES_SUMMARY_REPORT_AREA = "select SQL from saas_report_engine where report_num='SAAS_BI_2021_07' and sql_type = 'FUNCTION'";

	/**
	 * 菜品销售汇总查询函数(味千版本)
	 */
	public static final String FOOD_SALES_SUMMARY_REPORT_WQ = "select sql from saas_report_engine where report_num='SAAS_BI_WQ_2016_11' and sql_type = 'Q4'";
	
	/**
	 * 菜品销售汇总查询函数(味千版本)
	 */
	public static final String FOOD_SALES_SUMMARY_REPORT_WQ_Q5 = "select sql from saas_report_engine where report_num='SAAS_BI_WQ_2016_11' and sql_type = 'Q5'";
	
	/**
	 * 菜品销售汇总第一层
	 */
	public static final String ENGINE_DISHES_SALES_SUMMARY_OF_THE_FIRST_LAYER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='L1'";
	
	/**
	 * 菜品销售汇总
	 */
	public static final String ENGINE_FOOD_SALES_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='L0'";
	
	/**
	 * 菜品销售类别第一层
	 */
	public static final String ENGINE_THE_FIRST_CATEGORY_OF_FOOD_SALES_CATEGORY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='LB1'";
	
	/**
	 * 菜品销售类别第二层
	 */
	public static final String ENGINE_FOOD_SALES_CATEGORY_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='LB2'";
	
	/**
	 * 菜品销售类别第三层
	 */
	public static final String ENGINE_FOOD_SALES_CATEGORY_THIRD = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='LB3'";
	
	/**
	 * 菜品销售类别合计
	 */
	public static final String ENGINE_TOTAL_SALES_CATEGORY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='LB0'";
	
	/************************************2017-10-30******************************************
	 * 菜品销售汇总按照门店查询第一层
	 */
	public static final String ENGINE_FOOD_SALES_BY_STORE_ONE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='MDL1'";
	
	/**
	 * 菜品销售汇总按照门店查询第二层
	 */
	public static final String ENGINE_FOOD_SALES_BY_STORE_TWO = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='MDL2'";
	
	/**
	 * 菜品销售汇总按照门店查询第三层
	 */
	public static final String ENGINE_FOOD_SALES_BY_STORE_THREE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='MDL3'";
	
	/**
	 * 菜品销售汇总按照门店查询合计
	 */
	public static final String ENGINE_FOOD_SALES_BY_STORE_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='MDL0'";
	
	
   /***************************按照日期查询*********************************
	* 菜品销售汇总按照日期查询第一层
	*/
	public static final String ENGINE_FOOD_SALES_BY_DATE_ONE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='DATEL1'";
	
	/**
	 * 菜品销售汇总按照日期查询第二层
	 */
	public static final String ENGINE_FOOD_SALES_BY_DATE_TWO = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='DATEL2'";
	
	/**
	 * 菜品销售汇总按照日期查询第三层
	 */
	public static final String ENGINE_FOOD_SALES_BY_DATE_THREE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='DATEL3'";
	
	/**
	 * 菜品销售汇总按照日期查询合计
	 */
	public static final String ENGINE_FOOD_SALES_BY_DATE_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='DATEL0'";
	
	/**↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑2017-10-30↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑***
	
	
	
	/**
	 * 根据菜品销售数量 查询菜品流水
	 */
	public static final String FOOD_FOLWING_BY_FOODSALENUM = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM= 'SAAS_BI_2016_11' AND SQL_TYPE='foodFlow1' ";
	
	public static final String FOOD_FOLWING_BY_FOODSALENUM_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM= 'SAAS_BI_2016_11' AND SQL_TYPE='foodFlow1' ";
	

	/*************************************************************************SAAS_BI_2016_16(门店营业实时查询)**************************************************************************/
	
	/**
	 * 会员活跃度信息查询
	 */
	public static final String ENGINE_MEMBERSHIP_ACTIVITY_INFORMATION_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='hyhydL1'";
 
	/**
	 * 会员活跃度数查询
	 */
	public static final String ENGINE_MEMBERSHIP_ACTIVITY_INFORMATION_NUM_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='hyhydL2'";
	
	
	/**
	 * 今日营业额查询
	 */
	public static final String ENGINE_TURNOVER_AND_CUSTOMER_TRAFFIC_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='jryyeL1'";
	
	
	/**
	 * 今日客流量查询
	 */
	public static final String ENGINE_PASSENGER_FLOW_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='jrkllL1'";
	
	/**
	 *  今日客单数TC
	 */
	public static final String ENGINE_OPERATING_CONDITION_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='jrkdsL1'";
	
	/**
	 *  今日客单价AC
	 */
	public static final String ENGINE_OPERATING_CONDITIONS_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='jrkdjL1'";
	
	/**
	 *  会员7日消费
	 */
	public static final String ENGINE_MEMBER7_DAY_CONSUMPTION_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='hy7rxfl1'";
	
	/**
	 *  会员7日消费数
	 */
	public static final String ENGINE_MEMBER7_DAY_CONSUMPTION_NUM_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='hy7rxfl2'";
	
	/**
	 *  营业应收/客流曲线
	 */
	public static final String ENGINE_OPERATING_ACCOUNT_RECEIVABLE_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='yyysklqxL1'";
	
	
	/**
	 *  菜品销售排行(今日)
	 */
	public static final String ENGINE_DAY_FOOD_SALES_RANKING_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='cpxsphL1'";
	
	/**
	 *  菜品销售排行(近一周)
	 */
	public static final String ENGINE_WEEK_FOOD_SALES_RANKING_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='cpxsphL2'";
	
	/**
	 *  菜品销售排行(近一月)
	 */
	public static final String ENGINE_MONTH_FOOD_SALES_RANKING_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_16' AND SQL_TYPE='cpxsphL3'";
	
	
	/*************************************************************************SAAS_BI_2016_19**************************************************************************/
	
	/**
	 * 菜品销售实时报表
	 */
	public static final String ENGINE_FOOD_SALES_REAL_TIME_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_19' AND SQL_TYPE='CPL1'";
	
	/**
	 * 菜品销售实时报表合计
	 */
	public static final String ENGINE_TOTAL_FOOD_SALES_REAL_TIME_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_19' AND SQL_TYPE='CPL0'";
	
	
	/*************************************************************************SAAS_BI_2016_18VUE版本团体挂账流水**************************************************************************/
	
	public static final String ENGINE_GROUP_PAYMENT_FLOW = "SELECT * FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM ='SAAS_BI_2016_18' AND SQL_TYPE='FUNCTION'";
	 	
	/**
	 * 
	 */
	
	/*************************************************************************SAAS_BI_2016_21**************************************************************************/
	
	/**
	 * 按菜品汇总
	 */
	public static final String ENGINE_ACCORDING_TO_DISHES_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='CPL0'";
	
	/**
	 * 按菜品汇总第一层
	 */
	public static final String ENGINE_ACCORDING_TO_THE_FIRST_LAYER_OF_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='CPL1'";
	
	/**
	 * 按菜品汇总第二层
	 */
	public static final String ENGINE_ACCORDING_TO_THE_SECOND_LAYERS_OF_DISHES_SUMMARY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='CPL2'";
	
	/**
	 * 按时段汇总
	 */
	public static final String ENGINE_ACCORDING_TO_TIME_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='SDL0'";
	
	/**
	 * 按时段汇总第一层
	 */
	public static final String ENGINE_ACCORDING_TO_THE_TIME_OF_THE_FIRST_LAYER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='SDL1'";
	
	/**
	 * 按时段汇总第二层
	 */
	public static final String ENGINE_SECOND_LAYERS_ACCORDING_TO_THE_TIME_SUMMARY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='SDL2'";
	
	
	/*************************************************************************SAAS_BI_2016_26**************************************************************************/
	/**
	 * 套餐销售汇总报表第一层
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='L1'";
	
	/**
	 * 套餐销售汇总报表汇总
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='L0'";
	
	/**
	 * 套餐销售汇总套餐明细第一层
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT_MEAL_SALEL1 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='TCMXL1'";
	
	/**
	 * 套餐销售汇总套餐明细第二层
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT_MEAL_SALEL2 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='TCMXL2'";
	
	/**
	 * 套餐销售汇总套餐明细汇总
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT_SUMMARYL0 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='TCMXL0'";
	
	/*************************************************************************SAAS_BI_2016_29**************************************************************************/
	/**
	 * 按日期汇总对比分析汇总
	 */
	public static  final String ENGINE_SUMMARY_OF_COMPARATIVE_ANALYSIS_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='RQL0'";
	
	/**
	 * 按日期汇总对比分析第一层
	 */
	public static  final String ENGINE_SUMMARIZE_AND_CONTRAST_THE_FIRST_LAYER_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='RQL1'";
	
	/**
	 * 按日期汇总对比分析第二层
	 */
	public static  final String ENGINE_SUMMARIZE_AND_CONTRAST_THE_SECOND_LAYE_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='RQL2'";
	
	/**
	 * 按日期汇总对比分析第三层
	 */
	public static  final String ENGINE_SUMMARIZE_AND_CONTRAST_THE_THIRD_LAYER_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='RQL3'";
	
	
	/**
	 * 按交易门店汇总对比分析汇总
	 */
	public static  final String ENGINE_SUMMARY_OF_COMPARATIVE_ANALYSIS_BY_THE_TRANSACTION_STORE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='JGL0'";
	
	/**
	 * 按交易门店对比分析第一层
	 */
	public static  final String ENGINE_A_COMPARATIVE_ANALYSIS_OF_THE_FIRST_LAYER_OF_THE_STORE_BY_THE_TRANSACTION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='JGL1'";
	
	/**
	 * 按交易门店对比分析第二层
	 */
	public static  final String ENGINE_ACCORDING_TO_THE_COMPARATIVE_ANALYSIS_OF_THE_SECOND_STORES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='JGL2'";
	
	/**
	 * 按交易门店对比分析第三层
	 */
	public static  final String ENGINE_ACCORDING_TO_THE_COMPARATIVE_ANALYSIS_OF_THE_THIRD_STORES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='JGL3'";
	
	
	/*************************************************************************SAAS_BI_2016_30**************************************************************************/
	
	/**
	 * 门店应收总部金额情况第一层
	 */
	public static  final String ENGINE_THE_FIRST_PART_OF_THE_TOTAL_AMOUNT_OF_RECEIVABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='MDL1'";
	
	/**
	 * 门店应收总部金额情况第二层
	 */
	public static  final String ENGINE_THE_SECOND_PART_OF_THE_TOTAL_AMOUNT_OF_ACCOUNTS_RECEIVABLE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='MDL2'";
	
	
	/**
	 * 门店应收总部金额情况汇总 
	 */
	public static  final String ENGINE_SUMMARY_OF_THE_TOTAL_RECEIVABLES_RECEIVABLE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='MDL0'";
	

	/**
	 * 总部应付门店资金情况第一层   
	 */
	public static  final String ENGINE_HEADQUARTERS_TO_DEAL_WITH_THE_FIRST_TIER_OF_STORE_FUNDS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='ZBL1'";
	
	/**
	 * 总部应付门店资金情况第二层   
	 */
	
	public static  final String ENGINE_HEADQUARTERS_TO_DEAL_WITH_THE_STORE_SECOND_LAYERS_OF_FUNDS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='ZBL2'";

	
	/**
	 * 总部应付门店资金情况汇总 
	 */
	public static  final String ENGINE_HEADQUARTERS_TO_DEAL_WITH_STORE_FUNDS_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='ZBL0'";
	
	
	
	/**
	 * 会员卡跨机构结算类型(本本消费或本本充值)
	 */
	public static  final String ENGINE_BOOKS_OR_BOOKS_RECHARGE_CONSUMPTION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='LXL1'";
	
	/**
	 * 会员卡跨机构结算类型（LXL1合计）
	 */
	
	public static  final String ENGINE_THE_BOOKS_OR_BOOKS_TOTAL_PREPAID_CONSUMPTION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='LXL10'";
	
	
	/**
	 * 会员卡跨机构结算类型（本他消费、本他充值，他本消费、他本充值第一层）
	 */
	
	public static  final String ENGINE_THIS_IS_THE_FIRST_TIME_HE_OR_HE_OR_HIS_CURRENT_CONSUMPTION_OR_RECHARGE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='LXL2'";
	
	
	/**
	 * 会员卡跨机构结算类型（LXL2合计）
	 */
	
	public static  final String ENGINE_THIS_IS_THE_SUM_OF_HIS_OR_HER_CURRENT_CONSUMPTION_OR_HIS_CURRENT_CONSUMPTION_OR_THE_TOTAL_VALUE_OF_HIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='LXL0'";
	
	
	
	

	/*************************************************************************SAAS_BI_2016_48**************************************************************************/
	/**
	 * 营业汇总班次查询
	 */
	public static final String ENGINE_BUSINESS_SUMMARY_FREQUENCY_OFFAST_FOOD = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_48' AND SQL_TYPE='Y'";
	
	/*************************************************************************SAAS_BI_2016_49**************************************************************************/
	/**
	 * 营业汇总时段查询
	 */
	public static final String ENGINE_BUSINESS_SUMMARY_PERIOD = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_49' AND SQL_TYPE='Y'";
	
	
	/*************************************************************************SAAS_BI_2016_47**************************************************************************/
	/**
	 * 营业汇总统计查询
	 */
	public static final String ENGINE_BUSINESS_SUMMARY_STATISTICS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_47' AND SQL_TYPE='Y'";
	
	
	/*************************************************************************SAAS_BI_2016_51**************************************************************************/
	
	/**
	 * 营销活动整体分析
	 */
	public static final String ENGINE_THE_WHOLE_ANALYSIS_OF_MARKETING_ACTIVITIES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动综合分析
	 */
	public static final String ENGINE_COMPREHENSIVE_ANALYSIS_OF_MARKETING_ACTIVITIES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G2'";
	
	/**
	 * 营销活动专项分析
	 */
	public static final String ENGINE_SPECIAL_ANALYSIS_OF_MMARKETINGAACTIVITIES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G3'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G4'";

	/**
	 * 日分析
	 */
	public static final String ENGINE_DAY_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G5'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_WEEKLY_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G6'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_MONTHLY_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G7'";
	
	
	/*************************************************************************SAAS_BI_2016_52**************************************************************************/
	
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G5'";
	
	/**
	 * 渠道日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G6'";
	
	/**
	 * 渠道周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G7'";
	
	/**
	 * 渠道月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G8'";
	
	/**
	 * 机构日分析
	 */
	public static final String ENGINE_INSTITUTIONAL_DAY_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G9'";
	
	/**
	 * 机构周分析
	 */
	public static final String ENGINE_MECHANISM_WEEK_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G10'";
	
	/**
	 * 机构月分析
	 */
	public static final String ENGINE_INSTITUTIONAL_MONTH_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G11'";
	
	
	
	/*************************************************************************SAAS_BI_2016_55**************************************************************************/
 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G8'";
	
	
	
	/*************************************************************************SAAS_BI_2016_56**************************************************************************/
	 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G8'";
	
	
	
	
	/*************************************************************************SAAS_BI_2016_57**************************************************************************/
	
	
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G8'";
	
	
	
	
	/*************************************************************************SAAS_BI_2016_58**************************************************************************/
	 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G8'";
	
	
	
	
	/*************************************************************************SAAS_BI_2016_59**************************************************************************/
	 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G8'";
	
	
	
	
	/*************************************************************************SAAS_BI_2016_60**************************************************************************/
	 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G8'";
	
	
	/*************************************************************************SAAS_BI_2016_64**************************************************************************/
	/**
	 * 菜品类别合并第一层
	 */
	public static final String ENGINE_THE_FIRST_LAYER_OF_THE_CATEGORY_OF_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='LB1'";
	
	
	/**
	 * 菜品类别合并第二层
	 */
	public static final String ENGINE_DISHES_CATEGORY_SMALL_CLASS_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='LB2'";
	
	/**
	 * 菜品类别合并合计
	 */
	public static final String ENGINE_FOOD_CATEGORY_CATEGORY_FIRST_TIER_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='LB0'";
	
	/**
	 * 菜品类别大类查询
	 */
	public static final String ENGINE_CATEGORY_CATEGORY_SALES_CATEGORY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='DL1'";
	
	/**
	 * 菜品类别大类合计
	 */
	public static final String ENGINE_CATEGORY_CATEGORY_SALES_CATEGORY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='DL0'";
	
	/**
	 * 菜品类别小类查询
	 */
	public static final String ENGINE_CATEGORY_SALES_CATEGORY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='XL1'";
	
	/**
	 * 菜品类别小类合计
	 */
	public static final String ENGINE_CATEGORY_SALES_CATEGORY_QUERY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='XL0'";
	
	
	/*************************************************************************SAAS_BI_2016_63**************************************************************************/
	/**
	 * 会员消费次数月报 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_63' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员消费次数月报 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_63' AND SQL_TYPE='ZL1'";
	
 
	/**
	 * 会员消费次数月报 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_63' AND SQL_TYPE='YL1'";
	
	/*************************************************************************SAAS_BI_2016_46**************************************************************************/
	/**
	 * 会员售卡趋势分析 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY_46 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_46' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员售卡趋势分析 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK_46 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_46' AND SQL_TYPE='ZL1'";
	
	/**
	 * 会员售卡趋势分析 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH_46 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_46' AND SQL_TYPE='YL1'";
	
	/*************************************************************************SAAS_DB_2016_62**************************************************************************/
	/**
	 * 会员操作分析报告 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY_62 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_62' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员操作分析报告 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK_62 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_62' AND SQL_TYPE='ZL1'";
	
	/**
	 * 会员操作分析报告 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH_62 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_62' AND SQL_TYPE='YL1'";
	
	/*************************************************************************SAAS_BI_2017_09**************************************************************************/
	/**
	 * 会员操作店间对比 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY_09 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_09' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员操作店间对比 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK_09 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_09' AND SQL_TYPE='ZL1'";
	
	/**
	 * 会员操作店间对比 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH_09 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_09' AND SQL_TYPE='YL1'";
	
	
	
	
	/*************************************************************************SAAS_BI_2017_10**************************************************************************/
	/**
	 * 外卖订单汇总查询按机构
	 */
	public static final String ENGINE_TAKEOUT_ORDER_SUMMARY_FIRST_LAYER_QUERY_MECHANISM = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='JGMXL1'";
	
	/**
	 * 外卖查询日期订单汇总按机构
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_SUMMARY_AGENCY_INQUIRY_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='JGMXL2'";
	
	/**
	 * 外卖订单汇总查询合计按机构
	 */
	public static final String ENGINE_DELIVERY_ORDER_SUMMARY_QUERY_MECHANISM_TOGETHER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='JGHJL0'";
	
	
	/**
	 * 外卖订单汇总查询按日期
	 */
	public static final String ENGINE_TAKEOUT_ORDER_SUMMARY_DATE_QUERY_FIRST_LAYER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='RQMXL1'";
	
	/**
	 * 外卖订单汇总查询按日期
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_SUMMARY_DATE_QUERY_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='RQMXL2'";
	
	/**
	 * 外卖订单汇总查询合计按日期
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_SUMMARY_DATE_INQUIRY_SECOND_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='RQHJL0'";
	
	
	
	/*************************************************************************SAAS_BI_2017_11**************************************************************************/
	/**
	 * 外卖订单流水查询
	 */
	public static final String ENGINE_TAKEOUT_ORDER_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='wmddlsL1'";
	
	/**
	 * 外卖订单流水查询合计
	 */
	public static final String ENGINE_TAKEOUT_ORDER_QUERY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='wmddlsL0'";
	
	/**
	 * 订单明细查询
	 */
	public static final String ENGINE_ORDER_DETAIL_INQUIRY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddmxL1'";
	
	/**
	 * 订单明细查询合计
	 */
	public static final String ENGINE_ORDER_DETAIL_INQUIRY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddmxL0'";
	
	/**
	 * 订单优惠查询
	 */
	public static final String ENGINE_ORDER_DISCOUNT_INQUIRY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddyhL1'";
	
	/**
	 * 订单优惠查询合计
	 */
	public static final String ENGINE_ORDER_DISCOUNT_INQUIRY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddyhL0'";
	
	/**
	 * 订单付款查询
	 */
	public static final String ENGINE_ORDER_PAYMENT_ENQUIRY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddfkL1'";
	
	/**
	 * 订单付款查询合计
	 */
	public static final String ENGINE_ORDER_PAYMENT_ENQUIRY__TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddfkL0'";
	
	
	
	/*************************************************************************SAAS_BI_2017_12**************************************************************************/
	/**
	 * 外卖订单对账查询
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_QUERY_RECONCILIATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmdddzL1'";
	
	/**
	 * 外卖订单对账查询合计
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_QUERY_RECONCILIATION_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmdddzL0'";
	
	/**
	 * 外卖订单明细查询
	 */
	public static final String ENGINE_TAKE_OUT_ORDER_DETAILS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddmxL1'";
	
	/**
	 * 外卖订单明细查询合计
	 */
	public static final String ENGINE_TAKE_OUT_ORDER_DETAILS_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddmxL0'";
	
	/**
	 * 外卖订单优惠查询
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_PREFERENTIAL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddyhL1'";
	
	/**
	 * 外卖订单优惠查询合计
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_PREFERENTIAL_QUERY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddyhL0'";
	
	/**
	 * 外卖订单付款查询
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_PAYMENT_INQUIRY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddfkL1'";
	
	/**
	 * 外卖订单付款查询合计
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_PAYMENT_INQUIRY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddfkL0'";
	
	
	
	/*************************************************************************SAAS_BI_2017_13**************************************************************************/
	/**
	 * 营业汇总实时查询(竖版)
	 */
	public static final String ENGINE_BUSINESS_SUMMARY_REAL_TIME_DINNER_FAST_QUERY = "select * from f_tab_pos_realtime_yyhz('%p_report_date%','%p_store_id%','%p_report_type%')";
	
	
	/*************************************************************************SAAS_BI_2017_14**************************************************************************/
	/**
	 * 营业班次实时查询(竖版)
	 */
	public static final String ENGINE_BUSINESS_HOURS_REAL_TIME_DINNER_FAST_QUERY = "select * from f_tab_pos_realtime_yyhz('%p_report_date%','%p_store_id%','%p_shift_id%', '%p_report_type%')";
	
	/*************************************************************************SAAS_BI_2017_15**************************************************************************/
	/**
	 * 营业时段实时查询(竖版)
	 */
	public static final String ENGINE_REAL_TIME_MEALS_DURING_BUSINESS_HOURS = "select * from f_tab_pos_realtime_yyhz('%p_report_date_begin%','%p_report_time_begin%','%p_report_date_end%','%p_report_time_end%','%p_store_id%','%p_report_type%')";
	
 
	
	/*************************************************************************SAAS_BI_2016_17**************************************************************************/
	/**
	 * 账单流水查询
	 */
	public static final String ENGINE_BILLING_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDLSL1'";
	
	/**
	 * 账单流水查询合计
	 */
	public static final String ENGINE_TOTAL_BILLING_INQUIRIES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDLSL0'";
	
	/**
	 * 账单明细
	 */
	public static final String ENGINE_BILL_DETAILS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDMXL1'";
	
	/**
	 * 账单明细合计
	 */
	public static final String ENGINE_TOTAL_BILL_DETAILS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDMXL0'";
	
	
	/**
	 * 账单付款
	 */
	public static final String ENGINE_BILL_PAYMENT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDFKL1'";
	
	/**
	 * 账单付款合计
	 */
	public static final String ENGINE_TOTAL_BILL_PAYMENT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDFKL0'";
	
	/**
	 * 账单流水查询 查询条件为 售卖类型
	 */
	public static final String ENGINE_BILLING_QUERY_SALEMODE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_40' AND SQL_TYPE='queryListL1'";
	
	/*************************************************************************SAAS_BI_2017_34**************************************************************************/
	/**
	 * 日营业指标综合分析
	 */
	public static final String ENGINE_DAY_COMPREHENSIVE_ANALYSIS_OF_BUSINESS_INDICATORS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_34' AND SQL_TYPE='DAYL1'";
	
	/**
	 * 周营业指标综合分析
	 */
	public static final String ENGINE_WEEK_COMPREHENSIVE_ANALYSIS_OF_BUSINESS_INDICATORS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_34' AND SQL_TYPE='WEEKL2'";
	
	/**
	 * 月营业指标综合分析
	 */
	public static final String ENGINE_MONTH_COMPREHENSIVE_ANALYSIS_OF_BUSINESS_INDICATORS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_34' AND SQL_TYPE='MONTHL3'";
	
	
	
	/*************************************************************************SAAS_BI_2017_39**************************************************************************/
	/**
	 * 恢复账单明细查询
	 */
	public static final String ENGINE_RESTORE_BILLING_DETAILS_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L1'";
	
	/**
	 * 恢复账单汇总查询
	 */
	public static final String ENGINE_RESTORE_BILLING_SUMMARY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L2'";
	
	/**
	 * 恢复账单菜品明细查询
	 */
	public static final String ENGINERESUME_BILLS_DISHES_DETAILS_INQUIRIES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L3'";
	
	/**
	 * 恢复账单菜品名称查询
	 */
	public static final String ENGINE_RESUME_BILLING_DISHES_INQUIRIES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L4'";
	
	/**
	 * 恢复账单付款方式查询
	 */
	public static final String ENGINE_RESUME_BILLING_DETAIL_INQUIRIES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L5'";
	
	
	
	
	/*************************************************************************SAAS_BI_2017_44**************************************************************************/
	/**
	 * 第三方支付查询
	 */
	public static final String ENGINE_THIRD_PARTY_PAYMENT_RECONCILIATION_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_44' AND SQL_TYPE='L1'";
	
	/**
	 * 第三方支付合计
	 */
	public static final String ENGINE_TOTAL_THIRD_PARTY_PAYMENT_RECONCILIATION_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_44' AND SQL_TYPE='L0'";
	
	
	/*************************************************************************SAAS_BI_2017_47**************************************************************************/
	/**
	 * 营业销售对比分析
	 */
	public static final String ENGINE_COMPARATIVE_ANALYSIS_OF_BUSINESS_SALES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_47' AND SQL_TYPE='L1'";
	
	/**
	 * 营业销售对比分析合计
	 */
	public static final String ENGINE_TOTAL_COMPARATIVE_ANALYSIS_OF_BUSINESS_SALES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_47' AND SQL_TYPE='L0'";
	
	/**
	 * 营业销售图形查询
	 */
	public static final String ENGINE_BUSINESS_TRADITION_SALES_INQUIRY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_47' AND SQL_TYPE='L2'";
	
	
	/*************************************************************************SAAS_BI_2017_48**************************************************************************/
	/**
	 * 销售模式对比分析
	 */
	public static final String ENGINE_COMPARATIVE_ANALYSIS_OF_SALES_MODEL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_48' AND SQL_TYPE='L1'";
	
	/**
	 * 销售模式对比分析合计
	 */
	public static final String ENGINE_TOTAL_COMPARATIVE_ANALYSIS_OF_SALES_MODEL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_48' AND SQL_TYPE='L0'";
	
	/**
	 * 销售模式堂外卖食查询
	 */
	public static final String ENGINE_DINE_IN_QUERY_SALES_MODEL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_48' AND SQL_TYPE='L2'";
	
	/*************************************************************************SAAS_BI_2017_49**************************************************************************/
	/**
	 * 传统网络销售分析
	 */
	public static final String ENGINE_TRADITIONAL_NETWORK_SALES_ANALYSIS_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_49' AND SQL_TYPE='L1'";
	
	/**
	 * 传统网络销售分析合计
	 */
	public static final String ENGINE_TOTAL_RADITIONAL_NETWORK_SALES_ANALYSIS_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_49' AND SQL_TYPE='L0'";
	
	/**
	 * 传统销售查询
	 */
	public static final String ENGINE_TRADITION_SALES_INQUIRY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_49' AND SQL_TYPE='L2'";
	
	
	/*************************************************************************SAAS_BI_2017_54**************************************************************************/
	/**
	 * 菜品菜品班次同比环比分析
	 */
	public static final String ENGINE_NUMBER_OF_DISHES_YEAR_ON_YEAR_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_54' AND SQL_TYPE='L1'";
	
	
	/**
	 * 菜品班次同比环比分析合计
	 */
	public static final String ENGINE_TOTAL_NUMBER_OF_DISHES_YEAR_ON_YEAR_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_54' AND SQL_TYPE='L0'";
	
	
	
	/*************************************************************************SAAS_BI_2017_56**************************************************************************/
	/**
	 * 多层级通用机构参数
	 */
	public static final String ENGINE_MULTI_LEVEL_GENERAL_AGENCY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_56' AND SQL_TYPE='L1'";
	
	/*************************************************************************SAAS_BI_2017_58**************************************************************************/
	/**
	 * 餐厅日报
	 */
	public static final String ENGINE_RESTAURANT_DAILY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_58' AND SQL_TYPE='L1'";
	
	
	/*************************************************************************SAAS_BI_2017_63**************************************************************************/
	/**
	 * 能源消耗汇总报表第一层
	 */
	public static final String ENGINE_THE_ENERGY_CONSUMPTION_REPORT_OF_THE_FIRST_LAYER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_63' AND SQL_TYPE='nyxhL1'";
	
	/**
	 * 能源消耗汇总报表第二层
	 */
	public static final String ENGINE_ENERGY_CONSUMPTION_SUMMARY_REPORT_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_63' AND SQL_TYPE='nyxhL2'";
	
	/**
	 * 能源消耗汇总报表第三层
	 */
	public static final String ENGINE_ENERGY_CONSUMPTION_SUMMARY_REPORT_THIRD = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_63' AND SQL_TYPE='nyxhL3'";
	
	/**
	 * 能源消耗汇总报表合计
	 */
	public static final String ENGINE_TOTAL_THE_TOTAL_ENERGY_CONSUMPTION_IN_THE_SUMMARY_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_63' AND SQL_TYPE='nyxhL0'";
	
	
	
	
	/*************************************************************************POS_BI_2017_01**************************************************************************/
	/**
	 * POS菜品类别大类
	 */
	public static final String ENGINE_POS_THE_FIRST_LAYER_OF_THE_CATEGORY_OF_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_01' AND SQL_TYPE='DL1'";
	
	/**
	 * POS菜品类别小类
	 */
	public static final String ENGINE_POS_DISHES_CATEGORY_SMALL_CLASS_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_01' AND SQL_TYPE='XL1'";
	
	
	/*************************************************************************POS_BI_2017_02**************************************************************************/
	/**
	 * POS菜品销售实时查询
	 */
	public static final String ENGINE_POS_FOOD_SALES_SUMMARY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_02' AND SQL_TYPE='CPHZL1'";
	
	
	/*************************************************************************POS_BI_2017_03**************************************************************************/
	/**
	 * POS套餐销售汇总查询
	 */
	public static final String ENGINE_POS_PACKAGE_SALES_SUMMARY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_03' AND SQL_TYPE='TCHZL1'";
	
	
	/*************************************************************************POS_BI_2017_04**************************************************************************/
	/**
	 * POS菜品销售实时查询
	 */
	public static final String ENGINE_POS_REAL_TIME_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_04' AND SQL_TYPE='CPSSL1'";
	
	/*************************************************************************POS_BI_2017_05**************************************************************************/
	/**
	 * POS营业餐位查询
	 */
	public static final String ENGINE_POS_BUSINESS_LUNCH_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_05' AND SQL_TYPE='YYCWL1'";
	
	/*************************************************************************POS_BI_2017_06**************************************************************************/
	/**
	 * POS营业区域查询
	 */
	public static final String ENGINE_POS_BUSINESS_AREA_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_06' AND SQL_TYPE='YYQYL1'";
	
	/*************************************************************************POS_BI_2017_07**************************************************************************/
	/**
	 * POS营业桌位查询
	 */
	public static final String ENGINE_POS_BUSINESS_TABLE_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_07' AND SQL_TYPE='YYZWL1'";
	
	/*************************************************************************POS_BI_2017_08**************************************************************************/
	/**
	 * POS会员交易流水查询
	 */
	public static final String ENGINE_POS_MEMBER_TRANSACTION_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_08' AND SQL_TYPE='HYJYL1'";
	
	/*************************************************************************POS_BI_2017_09**************************************************************************/
	/**
	 * POS会员积分流水查询第一级
	 */
	public static final String ENGINE_POS_MEMBER_INTEGRAL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_09' AND SQL_TYPE='HYJFL1'";
	
	/**
	 * POS会员积分流水查询第二级
	 */
	public static final String ENGINE_POS_MEMBER_INTEGRAL_QUERY_SECONDLEVEL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_09' AND SQL_TYPE='HYJFL2'";

	/*************************************************************************SCM_BI_2017_02**************************************************************************/

	/**
	 * 要货汇总表查询
	 */
	public static final String ENGINE_SCM_ORGAN_DEMAND_REPORT_QUERY = "select sql from saas_report_engine where report_num = 'SCM_BI_2017_01' and sql_type='JGYHL0'";

	
	/**
	 * 配送出库表查询
	 */
	public static final String ENGINE_SCM_DISTRIBUTION_DELIVERY_REPORT_QUERY = "select sql from saas_report_engine where report_num = 'SCM_BI_2017_02' and sql_type='PSCKL0'";

	/*************************************************************************SAAS_BI_2016_35**************************************************************************/
	/**
	 * 会员交易分析报告 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY_35 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_35' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员交易分析报告 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK_35 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_35' AND SQL_TYPE='ZL1'";
	
	/**
	 * 会员交易分析报告 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH_35 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_35' AND SQL_TYPE='YL1'";

	

	/*************************************************************************SAAS_BI_2016_44**************************************************************************/
	/**
	 * 会员增长趋势日查询
	 */
	public static final String ENGINE_MEMBERSHIP_GROWTH_TREND_ON_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_44' AND SQL_TYPE='ARL1'";
	
	/**
	 * 会员增长趋势周查询
	 */
	public static final String ENGINE_MEMBERSHIP_GROWTH_TREND_WEEK_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_44' AND SQL_TYPE='AZL1'";
	
	/**
	 * 会员增长趋势月查询
	 */
	public static final String ENGINE_MEMBERSHIP_GROWTH_TREND_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_44' AND SQL_TYPE='AYL1'";
	
	/*************************************************************************SAAS_BI_2017_51**************************************************************************/
	/**
	 * 菜品销售汇总分析
	 */
	public static final String ENGINE_ANALYSIS_OF_SALES_OF_VEGE_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_51' AND SQL_TYPE='CPL1'";

	/**
	 * 菜品销售汇总分析
	 */
	public static final String ENGINE_TOTAL_ENGINE_ANALYSIS_OF_SALES_OF_VEGE_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_51' AND SQL_TYPE='CPL0'";

	/*************************************************************************SAAS_BI_2017_55**************************************************************************/
	/**
	 * 菜品销售时段分析
	 */
	public static final String ENGINE_ANALYSIS_OF_DISHES_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_55' AND SQL_TYPE='L1'";

	/**
	 * 菜品销售时段分析
	 */
	public static final String ENGINE_TOTAL_ANALYSIS_OF_DISHES_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_55' AND SQL_TYPE='L0'";

	/*************************************************************************SAAS_BI_2017_52**************************************************************************/
	/**
	 * 菜品销售结构分析
	 */
	public static final String ENGINE_ANALYSIS_OF_SALES_STRUCTURE_OF_FOOD_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_52' AND SQL_TYPE='L1'";

	/**
	 * 菜品销售结构分析
	 */
	public static final String ENGINE_TOTAL_ANALYSIS_OF_SALES_STRUCTURE_OF_FOOD_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_52' AND SQL_TYPE='L0'";

    /*************************************************************************SAAS_BI_2017_50**************************************************************************/
    /**
     * 支付方式明细分析
     */
    public static final String ENGINE_PAYMENT_METHOD_DETAIL_ANALYSIS_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_50' AND SQL_TYPE='L1'";

    /**
     * 支付方式明细分析
     */
    public static final String ENGINE_TOTAL_PAYMENT_METHOD_DETAIL_ANALYSIS_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_50' AND SQL_TYPE='L0'";


    /**
     * 支付方式明细分析(图)
     */
    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_ANALYSIS_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_50' AND SQL_TYPE='L0'";

    /**
     * 菜品估清
     */

    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_Food_Evaluation = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_65' AND SQL_TYPE='L1'";
    
    /**
     * 按照门店
     */
    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_Food_Evaluation_STORE1 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_65' AND SQL_TYPE='STOREL1'";
    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_Food_Evaluation_STORE2 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_65' AND SQL_TYPE='STOREL2'";
    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_Food_Evaluation_STORE3 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_65' AND SQL_TYPE='STOREL3'";
    
    	
    /**
     * 按照日期
     */
    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_Food_Evaluation_REPORT_DATE1 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_65' AND SQL_TYPE='DATEL1'";
    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_Food_Evaluation_REPORT_DATE2 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_65' AND SQL_TYPE='DATEL2'";
    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_Food_Evaluation_REPORT_DATE3 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_65' AND SQL_TYPE='DATEL3'";
    
    /*************************************************************************SAAS_BI_2017_64**************************************************************************/
    /**
     * 充值金额排行
     */
    public static final String ENGINE_RECHARGE_AMOUNT_RANKING_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_64' AND SQL_TYPE='hycz'";
   	
   	/**
   	 * 交易次数排行
   	 */
   	public static final String ENGINE_NUMBER_OF_TRANSACTIONS_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_64' AND SQL_TYPE='jycs'";
   	
   	/**
   	 * 交易金额排行
   	 */
   	public static final String ENGINE_TRANSACTION_AMOUNT_RANKING_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_64' AND SQL_TYPE='jyje'";
    
   	
   	/*************************************************************************SAAS_BI_2017_67**************************************************************************/
    /**
     * 营业免单汇总
     * 按交易日期汇总
     */ 
    
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DATE1  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='RQL1'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DATE2  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='RQL2'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DATE3  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='RQL3'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DATE0  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='RQL0'";
    
    /**
     * 营业免单汇总
     * 按交易门店汇总
     */ 
    
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_STORE1  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='MDL1'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_STORE2  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='MDL2'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_STORE3  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='MDL3'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_STORE0  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='MDL0'";
    
    
    /**
     * 营业免单汇总
     * 按菜品汇总查询
     */ 
    
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DISHES1  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='CPL1'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DISHES2  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='CPL2'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DISHES3  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='CPL3'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DISHES0  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='CPL0'";
    
    
    /**
     * 营业免单汇总
     * 按菜品类别查询
     */ 
    
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DISHESCLASS1  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='LBL1'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DISHESCLASS2  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='LBL2'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DISHESCLASS3  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='LBL3'";
    public static final String BUSINESS_FREE_SINGLE_SUMMARY_DISHESCLASS0  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_67' AND SQL_TYPE='LBL0'";
    
    /**
     * 营业免单明细
     */
    public static final String BUSINESS_FREE_SINGLE_DETAILED_DISHESCLASS1  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_69' AND SQL_TYPE='MXL1'";
    public static final String BUSINESS_FREE_SINGLE_DETAILED_DISHESCLASS0  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_69' AND SQL_TYPE='MXL0'";
    
    /**
     * 导出条数配置页面
     */
    public static final String BUSINESS_EXPORTNUMBER_SELECT  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_ENGINE_2017_84' AND SQL_TYPE='ENGINEL1'";
   
    /**
     * 总体活动分析主页面
     */
    // 总体活动分析
    public static final String MASS_MARKETING1 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='MAINL1'";
    // 活动VS整体 (饼图)
    public static final String MASS_MARKETING2 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='MAINL2'";
    // 会员VS非会员(饼图)
    public static final String MASS_MARKETING3 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='MAINL3'";
    // 活动列表
    public static final String MASS_MARKETING4 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='MAINL4'";
    
    /*************************************************************************SAAS_BI_2017_117**************************************************************************/
    /**
     * 活动详情页面
     */
    //基本信息
    public static final String ACTICE_SHOW_DATA1 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL1'";
    
    //营销活动效果
    public static final String ACTICE_SHOW_DATA2 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL2'";
    
    //时间趋势分析
    public static final String ACTICE_SHOW_DATA3 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL3'";
    
    //会员活动VS非会员活动
    public static final String ACTICE_SHOW_DATA4 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL4'";
    
    //活动期间同环比分析
    public static final String ACTICE_SHOW_DATA5 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL5'";
    
    //活动销售模式分析
    public static final String ACTICE_SHOW_DATA6 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL6'";
    
    //活动周期时段分析
    public static final String ACTICE_SHOW_DATA7 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL7'";
   /* public static final String ACTICE_SHOW_ACTIVITYCYCLE_DATA11 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL7_1'";
    public static final String ACTICE_SHOW_ACTIVITYCYCLE_DATA12 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL7_2'";
    public static final String ACTICE_SHOW_ACTIVITYCYCLE_DATA13 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL7_3'";
    public static final String ACTICE_SHOW_ACTIVITYCYCLE_DATA14 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL7_4'";
    public static final String ACTICE_SHOW_ACTIVITYCYCLE_DATA15 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL7_5'";*/
    
    //活动参与次数分布
    public static final String ACTICE_SHOW_DATA8 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL8'";
    
    //活动单均消费分布
    public static final String ACTICE_SHOW_DATA9 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL9'";
    
    //活动人均消费分布
    public static final String ACTICE_SHOW_DATA10 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL10'";
    
    //活动门店排行
    public static final String ACTICE_SHOW_DATA11 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_117' AND SQL_TYPE='DETAILAL11'";
    
    
    /*************************************************************************SAAS_BI_2017_36**************************************************************************/
    /**
     * 模块自定义
     */
    
    /**
     * 折线图
     */
    // 营业收入走势
    public static final String QUERY_LINE_CHART_MODEL_OPERATING_INCOME_TRAND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='LINEL1'";
    
    // 会员增长趋势分析
    public static final String QUERY_LINE_CHART_MODEL_MEMBER_GROUTH_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='LINEL2'";
    
    // [周期]会员售卡数量趋势
    public static final String MEMBERSHIP_CARD_NUMBER_TRENDS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'ZHYSKSLQS'";
    
    // [周期]会员售卡金额趋势
    public static final String TREND_OF_EMBERSHIP_CARD_SALES_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'ZHYSKJEQS'";
    
    // [日期]上座率趋势
    public static final String ATTENDANCE_TRENDS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RSZLQS'";
    
    // [日期]翻台率趋势
    public static final String TURN_OVER_RATE_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RFTLQS'"; 
    
    // [时段]外卖时段完成订单数量趋势
    public static final String TIME_ORDER_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'SDWMWCDDSLQS'"; 
    
    // [时段]外卖取消订单数量趋势
    public static final String THE_TREND_OF_CANCELLCANCELLATION_OF_TAKEOUT_ORDERS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'SDWMQXDDSLQS'";
    
    // [时段]消费实收金额趋势 
    public static final String THE_TREND_OF_REAL_AMOUNT_OF_CONSUMPTION= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'SDXFSSJEQS'";
    
    // [时段]消费客数趋势 
    public static final String CONSUMPTION_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'SDXFKSQS'";
    
    // [日期]营业实收+账单均值趋势
    public static final String AVERAGE_TREND_OF_CASH_PLUE_BILLS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RYYSS_ZDJZQS'";
    
    // [日期]营业收实收+客流量趋势
    public static final String BUSINESS_INCOME_PASSENGER_FLOW_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RYYSS_KLLQS'";
     
    // [日期]营业收实收+人均消费金额 趋势
    public static final String BUSINESS_INCOME_PER_CAPITA_CONSUMPTION_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RYYSS_RJXFJEQS'";
    
    // [周期]营业收实收+账单均值趋势
    public static final String AVERAGE_TREND_OF_BUSINESS_INCOME_BILLl= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'ZYYSS_WRQS'";
    
    // [周期]营业收实收+客流量趋势
    public static final String BUSINESS_INCOME_PASSENGER_FLOW_TRENDS= "select * from saas_report_engine where report_num = 'SAAS_MK_2017_03' and sql_type = 'ZYYSS_KLLQS'";
    
    // [周期]营业收实收+人均消费金额 趋势
    public static final String BUSINESS_INCOME_PER_CAPITA_CONSUMPTION_TRENDS= "select * from saas_report_engine where report_num = 'SAAS_MK_2017_03' and sql_type = 'ZYYSS_RJXFJEQS'";
    
    // [日期]外卖渠道订单实收金额趋势
    public static final String TAKE_OUT_CHANNEL_ORDER_CASH_AMOUNT_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RWMQDDDSSJEQS'";
    
    // [日期]外卖渠道订单应收金额趋势
    public static final String TAKEAWAY_CHANNEL_ORDER_RECEIVABLE_AMOUNT_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RWMQDDDYSJEQS'";
    
    // [日期]销售模式消费账单数量趋势
    public static final String SALES_MODEL_CONSUMPTION_BILL_QUANTITY_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RXSMSXFZDSLQS'";
    
    // [日期]销售模式营业应收趋势
    public static final String SALES_TREND_RECEIVABLE_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RXSMSYYYSQS'";
    
    // [日期]销售模式营业实收趋势
    public static final String SALES_TREND_OF_SALES= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RXSMSYYSSQS'";
    
    // [周期]卡消费金额趋势 
    public static final String CARD_CONSUMPTION_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'ZKXFJEQS'";
    
    // [周期]卡消费次数趋势
    public static final String TREND_OF_CYCLE_CARD_CONSUMPTION= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'ZKXFCSQS'";
    
    // [日期]粉丝增长趋势 
    public static final String FAN_GROWTH_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RFSZZQS'";
    
    // [日期]会员数增长趋势
    public static final String THE_GROWTH_TREND_OF_THE_NUMBER_OF_MEMBERS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'RHYZZQS'";
    
    // [日期]会员增长率趋势
    public static final String TREND_OF_MEMBER_GROWTH_RATE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03' AND SQL_TYPE = 'HYZZLQS'";
    
    // 爆发问题数量/条数/复发问题数量趋势
    public static final String NUMBER_OF_OUTBREAKS_IN_THE_LAST_TWO_WEEKS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_144'  AND SQL_TYPE = 'BFFFWTSLQS'";

    
    //账单均值趋势
    public static final String BILL_MEAN_TREND= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'ZDJZQS'";

    //账单数趋势
    public static final String BILL_NUMBER_TREND= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'ZDSQS'";

    //套餐占比趋势
    public static final String TRENDS_PROPORTION_OF_PACKAGES= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'TCZBQS'";

    //退菜数量趋势
    public static final String RETURNING_VEGETABLE_QUANTITY_TREND= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'ZXMJT'";

    //会员与非会员营业实收占比趋势
    public static final String TREND_PROPORTION_OF_MEMBERS_MEMBERS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYYYSSZBQS'";

    //会员与非会员营业应收占比趋势
    public static final String SHARE_OF_BUSINESS_RECEIVABLE_BETWEEN_MEMBER= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYYYYSZBQS'";

    //会员与非会员营业净收占比趋势
    public static final String NET_SHARE_OF_BUSINESS_INCOME_BETWEEN_MEMBERS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYYYJSZBQS'";

    //会员与非会员账单数趋势
    public static final String Trends_member_bills= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYZDSQS'";

    //会员与非会员账单数占比趋势
    public static final String TRENDS_PROPORTION_MEMBER_BILLS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYZDSZBQS'";

    //会员与非会员账单均值趋势
    public static final String AVERAGE_TREND_OF_MEMBER_BILLS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYZDJZQS'";

    //会员与非会员消费客数趋势
    public static final String TRENDS_NUMBER_OF_MEMBERS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYXFKSQS'";

    //会员与非会员消费客数占比趋势
    public static final String TRENDS_PROPORTION_OF_MEMBERS_CONSUMERS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYXFKSZBQS'";

    //会员与非会员退菜数量趋势
    public static final String TRENDS_NUMBER_RETURNING_DISHES= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYTCSLQS'";

    //会员与非会员退菜数量占比趋势
    public static final String THE_TREND_RETURNING_FOOD_QUANTITY_BETWEEN_MEMBERS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYFHYTCSLQS'";

    //会员与非会员含套餐账单数量占比趋势
    public static final String TRENDS_PROPORTION_OF_BILLS_MEALS_BETWEEN_MEMBERS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYYFHYHTCZDSJZBQS'";

    //复购会员账单占比趋势
    public static final String TRENDS_PROPORTION_OF_REPURCHASED_MEMBERS_BILLS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'FGHYZDZBQS'";
 
    //新会员消费客数占比（新会员账单数/总账单数）
    public static final String NEW_MEMBERS_AS_PERCENTAGE_OF_CUSTOMERS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'XHYXFKSZB'";
 
    //会员首次消费菜品排行
    public static final String MEMBER_FIRST_LIST_OF_CONSUMER_DISHES= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'HYSCXFPH'";
 
    //粉丝增长与新增会员数量趋势
    public static final String FAN_GROWTH_AND_NEW_MEMBERSHIP_TRENDS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_03'  AND SQL_TYPE = 'FSZZYXZHYSLQS'";
 
    
    
    /**
     * 彩虹图模型（环形图）
     */
    // 营业班次售卖分析
    public static final String QUERY_RAINBOW_CHART_MODEL_BUSINESS_SHIFT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='RAINBOWL1'";
    
    // 销售模式实收金额占比
    public static final String SALES_MODEL_ACTUAL_INCOME= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04' AND SQL_TYPE = 'XSMSSSJEZB'";
    
    // 销售模式应收金额占比 
    public static final String SALES_MODEL_ACTUAL_XSMS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04' AND SQL_TYPE = 'XSMSYSJEZB'";
    
    // 营业班次消费账单数量占 
    public static final String NUMBER_OF_BUSINESS_SHIFTS_AND_CONSUMPTION_ACCOUNTS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04' AND SQL_TYPE = 'YYBCXFZDSLZB'";
    
    // 营业班次营业实收金额占 
    public static final String OPERATING_AMOUNT_ACCOUNTED_FOR_THE_AMOUNT_OF_BUSINESS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04' AND SQL_TYPE = 'YYBCYYSSJEZB'";
    
    // 营业退菜原因分类数量占 
    public static final String REASONS_FOR_CLASSIFICATION_OF_BUSINESS_RETURN= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04' AND SQL_TYPE = 'YYTCYYFLSLZB'";
    
    // 营业奉送原因分类数量占 
    public static final String GIVE_REASONS_ACCOUNTING_FOR_THE_NUMBER_OF_BUSINESS_CLASSIFICATION= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04' AND SQL_TYPE = 'YYFSYYFLSLZB'";
    
    // 营业付款类型金额占比 
    public static final String PROPORTION_OF_BUSINESS_PAYMENT_TYPE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04' AND SQL_TYPE = 'YYFKLXJEZB'";
    
    // 会员等级人数占比
    public static final String PERCENTAGE_OF_MEMBERSS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04' AND SQL_TYPE = 'HYDJRSZB'";
    
    // 卡充值付款类型金额占比 
    public static final String CARD_RECHARGE_PAYMENT_TYPE_AMOUNT_ACCOUNTED_FORS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04' AND SQL_TYPE = 'KCZFKLXJEZB'";
    
    // 问题类型爆发条数占比爆发总条数
    public static final String TOP6_PROBLEM_TYPE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_144'  AND SQL_TYPE = 'WTLXBFTSZBZTS'";
       
    //折扣原因账单应收金额占比
    public static final String DISCOUNTED_ACCOUNT_RECEIVABLE_PERCENTAGE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04'  AND SQL_TYPE = 'ZKFAXFYSJEZB'";
       
    //折扣原因优惠金额占比
    public static final String DISCOUNT_REASON_PERCENTAGE_OF_CONCESSIONARY_AMOUNT= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04'  AND SQL_TYPE = 'ZKFAYHJEZB'";
       
    //会员消费实收周期占比
    public static final String PROPORTION_OF_MEMBERS_REAL_INCOME_CYCLE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04'  AND SQL_TYPE = 'HYXFSHZQZB'";
     
    //会员消费账单数周期占比
    public static final String PROPORTION_OF_MEMBERS_CONSUMPTION_BILL_CYCLE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04'  AND SQL_TYPE = 'HYXFZDSZQZB'";
     
    //会员消费客数周期占比
    public static final String PROPORTION_OF_MEMBERS_CONSUMPTION_CYCLES= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04'  AND SQL_TYPE = 'HYXFKSZQZB'";
     
    //会员消费账单数各等级占比
    public static final String PROPORTION_OF_MEMBERS_GRADE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04'  AND SQL_TYPE = 'HYGDJXFZDSZB'";
     
    //会员各等级影响力占比
    public static final String PROPORTION_OF_MEMBER_RANK_INFLUENCE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04'  AND SQL_TYPE = 'HYGDJYXLZB'";
     
    //会员各等级卡充值占比
    public static final String PERCENTAGE_OF_EACH_MEMBER_RATING_CARD_RECHARGE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04'  AND SQL_TYPE = 'HYGDJKCZZB'";
     
    //会员各等级卡消费占比
    public static final String PROPORTION_OF_MEMBERS_CONSUMPTION_OF_EACH_GRADE_CARD= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_04'  AND SQL_TYPE = 'HYGDJKXFZB'";
    
       
    /**
     * 饼图模型
     */
    // 售卖模式分析
    public static final String QUERY_PIE_CHART_MODEL_BUSINESS_PATTEN= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='PIEL1'";
    
    // 支付方式分析
    public static final String QUERY_PIE_CHART_MODEL_PAUMENT_TYPE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='PIEL2'";
     
    // 会员结构分析
    public static final String QUERY_PIE_CHART_MODEL_MENBER_STRUCTURE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='PIEL3'";
    
    //折扣方案优惠金额占比
    public static final String DISCOUNT_RATE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'ZKFAYHJEZB'";
    
    //卡充值付款类型金额占比
    public static final String CARD_RECHARGE_PAYMENT_TYPE_AMOUNT_ACCOUNTED_FOR= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'KCZFKLXJEZB'";
    
    //销售模式实收金额占比
    public static final String SALES_MODEL_RECEIVABLES= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_02' AND SQL_TYPE='XSMSSSJEZB'";
     
    //销售模式应收金额占比 
    public static final String SALSE_MODEL_ACCOUNTS_RECEIVABLE_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_02' AND SQL_TYPE='XSMSYSJEZB'";
    
    //折扣方案消费应收金额占比 
    public static final String DISCOUNT_PROGRAM_CONSUMPTION_ACCOUNTS_RECEIVABLE_RATIO= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'ZKFAXFYSJEZB'";
    
    //折扣方案消费账单占比 
    public static final String DISCOUNT_BILL= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'ZKFAXFZDZB'";
    
    //会员营销活动参与人次占比 
    public static final String MEMBER_MARKETIING_ACTIVITIES_PARTICIPATION_RATIO= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'HYYXHDCYRCZB'";
    
    //会员注册渠道人数占比
    public static final String NUMBER_OF_MEMBERS_REGISTERED_CHANNELS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_02' AND SQL_TYPE='HYZCQDRSZB'";
    
    //会员等级人数占比
    public static final String PERCENTAGE_OF_MEMBERS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'HYDJRSZB'";
    
    //营业班次消费账单数量占比 
    public static final String NUMBER_OF_BUSINESS_BILLS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'YYBCXFZDSLZB'";
    
    //营业班次营业实收金额占比
    public static final String OPERATING_FREQUENCY_BUSINESS_AMOUNT_OF_ACTUAL_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'YYBCYYSSJEZB'";
    
    //营业退菜原因分类数量占比
    public static final String REASONS_CLASSIFICATION_AND_QUANTITY_PERPORTION_OF_BUSINESS_RETURN= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'YYTCYYFLSLZB'";
    
    //营业奉送原因分类数量占比
    public static final String A_CLASSIFICATION_OF_THE_PROPORTION_OF_THE_NUMBER_OF_BUSINESS_REASONS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'YYFSYYFLSLZB'";
    
    //外卖渠道有效单量占比
    public static final String EFFECTIVE_SINGLE_VOLUME_RATIO_OF_EXPORT_CHANNELS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'WMQDYXDLZB'";
    
    //外卖渠道退单量占比
    public static final String WITHDRAWAL_OF_SINGLE_VOLUME_RATIO= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'WMQDTDZB'";
    
    //外卖渠道会员转化占比
    public static final String CONVERSION_OF_EXPORT_CHANNEL_MEMBERS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02' AND SQL_TYPE = 'WMQDHYZHZB'";
    
    //业务模块问题类型占比
    public static final String SM_SERVICE_MODULE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_144'  AND SQL_TYPE = 'YWMKWTLXZB'";
    
    //会员退菜原因占比
    public static final String PROPORTION_OF_MEMBER_REASONS_FOR_FOOD_WITHDRAWAL= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_13'  AND SQL_TYPE = 'HHTCYYZB'";
    
    //会员消费实收周期占比
    public static final String PROPORTION_OF_MEMBERS_REAL_CYCLE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02'  AND SQL_TYPE = 'HYXFSHZQZB'";
    
    //会员消费账单数周期占比
    public static final String PROPORTION_CONSUMPTION_BILL_CYCLE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02'  AND SQL_TYPE = 'HYXFZDSZQZB'";
    
    //会员消费客数周期占比
    public static final String PPROPORTION_MEMBERS_CYCLESS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02'  AND SQL_TYPE = 'HYXFKSZQZB'";
    
    //会员各等级卡充值占比
    public static final String PERCENTAGE_OF_EACH_CARD_RECHARGE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02'  AND SQL_TYPE = 'HYGDJKCZZB'";
    
    //会员各等级卡消费占比
    public static final String PROPORTION_GRADE_CARD= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02'  AND SQL_TYPE = 'HYGDJKXFZB'";
    
    //会员各等级消费账单数占比
    public static final String PROPORTION_CONSUMPTION_GRADE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02'  AND SQL_TYPE = 'HYGDJXFZDSZB'";
    
    //会员各等级影响力占比
    public static final String PROPORTION_INFLUENCE_DIFFERENT_LEVELS= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_02'  AND SQL_TYPE = 'HYGDJYXLZB'";
    
    
    /**
     * 条形图分析（横向柱状图）
     */
   // 菜品单点数排行
    public static final String QUERY_BAR_CHART_MODEL_MENU_LIST= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='BARL1'";
    
   //卡消费次数排行
    public static final String NUMBER_OF_MEMBER_CONSUMPTION= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE='KXFCSPH'";
   
   //菜品销售实收金额排行
    public static final String LIST_OF_DISHES_SALES_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE='CPSSJEPH'";
    
   //门店退菜数量排行
    public static final String NUMBER_OF_STORES_RETURNING_DISHES= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'MDTCSLPH'";
    
   //卡消费金额排行
    public static final String MEMBER_CONUMPTION_AMOUNT_RANKING= "SELECT SQL from SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'KXFJEPH'";
    
   //门店消费客数排行
    public static final String NUMBER_OF_CUSTOMERS_IN_STORES= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'MDXFKSPH'";
   
   //门店营业实收排行
    public static final String STORE_BUSINESS_INCOME= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'MDYYSSPH'";
    
   //会员充值次数排行
    public static final String MEMBERSHIP_RECHARGE_RANKING= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'HYCZCSPH'";
    
   //门店充值次数排行
    public static final String NUMBER_OF_STORES_RECHARGE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'MDCZCSPH'";
    
   //门店充值金额排行
    public static final String STORE_RECHARGE_AMOUNT_RANKING= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND sql_type = 'MDCZJEPH'";
    
   //时段营业实收金额排行
    public static final String THE_AMOUNT_OF_CASH_PAID_DURING_THE_PERIOD= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'SDYYSSPH'";
    
   //时段营业应收金额排行
    public static final String THE_AMOUNT_OF_RECEIVEBLES_RECEIVABLE_DURING_THE_PERIOD= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'SDYYYSPH'";
    
   //会员充值金额排行
    public static final String MEMBERSHIP_RECHARGE_AMOUNT_RANKING= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'HYCZJEPH'";
  
   //菜品小类实收数量排行
    public static final String LIST_OF_SMALL_DISHES= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01' AND SQL_TYPE = 'CPXLSSSLPH'";
    
   //爆发问题类型排行
    public static final String TYPE_OF_EXPLSION_PROBLEM= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_144'  AND SQL_TYPE = 'BFWTLXPH'";
    
   //商户爆发问题条数排行
    public static final String NUMBER_OF_BUSINESS_OUTBREAK_ISSUSE= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_144'  AND SQL_TYPE = 'SHBFTSPH'";
    
    //菜品退菜排行
    public static final String RETURNING_DISHES_TO_THE_LIST= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01'  AND SQL_TYPE = 'CPTCPH'";
    
    //菜品点单率排行
    public static final String ORDER_RATE_OF_DISHES= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01'  AND SQL_TYPE = 'CPDJLPH'";
    
    //团体挂账排名
    public static final String GROUP_HANGING_RANKING= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01'  AND SQL_TYPE = 'TTGZPM'";
    
    //团体充值排名
    public static final String GROUP_RECHARGE_RANKING= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01'  AND SQL_TYPE = 'TTCZPH'";
    
    //会员退菜数量排行
    public static final String NUMBER_RANKING_RETURNING_VEGETABLES= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01'  AND SQL_TYPE = 'HYTCSLPH'";
    
    //会员价菜品数量排行
    public static final String NUMBER_OF_DISHES_AT_MEMBER_PRICES= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01'  AND SQL_TYPE = 'HYJCPSJPH'";
    
    //会员价菜品金额排行
    public static final String LIST_OF_MEMBER_PRICES_FOR_DISHES= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01'  AND SQL_TYPE = 'HYJCPJEPH'";
    
    //会员营销活动充值金额
    public static final String MEMBERSHIP_MARKETING_RECHARGE_AMOUNT= "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_01'  AND SQL_TYPE = 'HYYXHDCZJE'";
 
    
    
    /**
     * 水波百分比图
     */
    // TC完成率
    public static final String QUERY_WAVE_CHART_MODEL_TC_COMPLETION_RATE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='WAVEL1'";
    
    // AC完成率
    public static final String QUERY_WAVE_CHART_MODEL_AC_COMPLETION_RATE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='WAVEL2'";
    
    // 单指标翻台率
    public static final String QUERY_WAVE_CHART_MODEL_OVERT_AIWAN= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='WAVEL3'";
    
    // 营业数
    public static final String QUERY_WAVE_CHART_MODEL_BUSINESS_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='WAVEL4'";
    
    // 客流量
    public static final String QUERY_WAVE_CHART_MODEL_PASSENGER_FLOW= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='WAVEL5'";
    
    // 翻桌数
    public static final String QUERY_WAVE_CHART_MODEL_TABLES_TURNED= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='WAVEL6'";
    
    // 菜品实收数量
    public static final String NUMBER_OF_DISHES_RECEIVED= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND SQL_TYPE='CPSSSL'";
    
    // 菜品实收金额
    public static final String AMOUNT_PAID_FOR_DISHES= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND SQL_TYPE='CPSSJE'";
    
    // 堂食实收数量
    public static final String THE_AMOUNT_OF_FOOD_EATEN_IN_COURT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND SQL_TYPE='TSSSSL'";
    
    // 堂食实收金额
    public static final String ACTUAL_AMOUNT_OF_FOOD_EATEN= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND SQL_TYPE='TSSSJE'";
    
    // 外卖实收数量
    public static final String TAKE_OUT_QUANTITY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND SQL_TYPE='WMSSSL'";
    
    // 外卖实收金额
    public static final String TAKE_OUT_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND SQL_TYPE='WMSSJE'";
    
    // 外带实收数量
    public static final String QUANTITY_OF_TAKE_OUT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND SQL_TYPE='WDSSSL'";
    
    // 外带实收金额
    public static final String AMOUNT_PAID_IN_EXCESS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND SQL_TYPE='WDSSJE'";
    
    // 营业应收金额
    public static final String BUSINESS_RECEIVABLE_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'YYYSJE'";
    
    // 营业实收金额
    public static final String ACYUAL_AMOUNT_OF_BUSINESS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'YYSSJE'";
    
    // 营业净收金额 
    public static final String NET_AMOUNT_OF_BUSINESS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'YYJSJE'";
    
    // 消费账单数量
    public static final String CONSUMPTION_BILL_QUANTITY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'XFZD'";
    
    // 账单均值金额
    public static final String AVERAGE_AMOUNT_OF_BILL= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'ZDJZ'";
    
    // 人均消费金额
    public static final String PER_CAPITA_CONSUMPTION_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'RJXF'";
    
    // 上座率 
    public static final String UPPER_LIMB = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'SZL'";
    
    // 双指标翻台率 
    public static final String TURN_OVER_RATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'FTL'";
    
    // 退菜金额 
    public static final String WITHDRAWAL_AMOUNT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'TCJE'";
    
 // 退菜实收
    public static final String BACK_HARVEST = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND trim(SQL_TYPE)='TCSSSL'";
    
    // 菜品奉送 
    public static final String CPFX_HARVEST = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_06' AND SQL_TYPE='CPFSSL'";
    
    // 奉送金额
    public static final String THE_AMOUNT_OF_PROMOTIONS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'FSJE'";
    
    // 营业实收完成率
    public static final String CPMPLETION_RATE_OF_BUSINESS_INCMOE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'YYSSWCL'";
    
    // 营业应收完成率 
    public static final String OPERATING_RECEIVABLE_COMPLETION_RATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'YYYSWCL'";
    
    // 消费客数完成率
    public static final String CONSUMER_COUNT_COMPLETION_RATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'XFKSWCL'";
    
    // 消费账单完成率
    public static final String CONSUMPTION_BILL_COMELETION_RATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'XFZDWCL'";
    
    // 账单均值完成率
    public static final String BILL_AVERAGE_COMPLETION_RATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'ZDJZWCL'";
    
    // 人均消费完成率
    public static final String PER_CAPITA_CONSUMPTION_COMPLETION_RATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06' AND SQL_TYPE = 'RJXFWCL'";
    
    // 会员售卡金额
    public static final String MEMBERSHIP_CARD_AMOUNT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'HYSKJE'";
    
    // 会员售卡数量
    public static final String NUMBER_OF_MEMBERS_SELLING_CARDS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'HYSKSL'";
    
    // 会员退卡次数
    public static final String NUMBER_OF_MEMBER_WITHDRAWAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'HYTKCS'";
    
    // 会员退卡金额
    public static final String MEMBER_RECHARGE_TIMES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'HYTKJE'";
    
    // 会员充值次数
    public static final String MEMBER_RECHARGE_TIMESS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'HYCZCS'";
    
    // 会员充值金额
    public static final String MEMBER_RECHARGE_AMOUNT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'HYCZJE'";
    
    // 会员充值主账户金额
    public static final String MEMBER_RECHARGE_THE_MAIN_ACCOUNT_AMOUNT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'HYCZZZHJE'";
    
    // 会员充值副账户金额
    public static final String MEMBERS_RECHARGE_THE_AMOUNT_OF_THE_DEPUTY_ACCOUNT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'HYCZFZHJE'";
    
    // 卡消费次数
    public static final String NUMBER_OF_CARD_CONSUMPTION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'KXFCS'";
    
    // 卡消费金额
    public static final String CARD_CONSUMPTION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'KXFJE'";
    
    // 会员总人数
    public static final String TOTAL_NUMBER_OF_MEMBERS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE = 'HYZRS'";
    
  // 座位数
    public static final String SEATING  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='ZWS'";
    
    // 菜品销售应收金额
    public static final String AMOUNT_RECEIVABLE_FOR_SALE_OF_DISHES  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='CPXSYSJE'";
    
    // 菜品销售实收金额
    public static final String THE_AMOUNT_OF_THE_SALE_OF_THE_DISHES  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='CPXSSSJE'";
    
    // 菜品销售应收数量
    public static final String QUANTITY_OF_SALES_RECEIVABLE  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='CPXSYSSL'";
    
    // 菜品销售实收数量
    public static final String SALES_VOLUME_OF_VEGETABLE_PRODUCTS  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='CPXSSSSL'";
    
    // 菜品消费数量
    public static final String QUANTITY_OF_VEGETABLE_CONSUMPTION  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='CPXFSL'";
    
    // 菜品消费金额
    public static final String CONSUMPTION_OF_DISHES  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='CPXFJE'";
    
    // 菜品奉送数量
    public static final String A_NUMBER_OF_DISHES  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='CPFSSL'";
    
    // 菜品奉送金额
    public static final String AS_THE_AMOUNT_OF_FOOD  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='CPFSJE'";
    
    // 折扣金额
    public static final String DISCOUNT_AMOUNT  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='ZKJE'";
    
    // 折让金额
    public static final String DISCOUNT_AMOUNTS  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='ZRJE'";
    
    // 抹零金额 
    public static final String MALING_AMOUNT  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='MLJE'";
    
    // 多收礼券金额 
    public static final String PAY_THE_AMOUNT_OF_THE_GIFT_CERTIFICATE  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='DSLJJE'";
    
    // 账单消费金额 
    public static final String BILL_CONSUMPTION  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='ZDXFJE'";
    
    // 消费客数
    public static final String CONSUMPTION_NUMBER  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='XFKS'";
    
    // 桌台数量
    public static final String TABLE_NUMBER  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_06'  AND SQL_TYPE='ZTSL'";
    
    //会员账单数占比
    public static final String PROPORTION_OF_MEMBERS_BILLS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_13' AND SQL_TYPE ='HYZDSZB'";
    
    //会员账单客数占比
    public static final String PROPORTION_OF_PASSENGERS_MEMBERS_BILLS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_13' AND SQL_TYPE ='HYZDSLZB'";
    
    //优惠券回收占比
    public static final String COUPON_RECOVERY_RATIO= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_13' AND SQL_TYPE ='YHQHSZB'";
    
    //爆发数
    public static final String OUTBURST_NUMBER  = "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_144'  AND SQL_TYPE = 'BFS'";
    
    //复发数
    public static final String RECURRENCE_NUMBER  = "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_144'  AND SQL_TYPE = 'FFS'";
    
    //爆发率
    public static final String BURST_RATE  = "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_144'  AND SQL_TYPE = 'BFL'";
    
    //复发率
    public static final String RECURERNCE_RATE   = "SELECT SQL FROM HD_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_144'  AND SQL_TYPE = 'FFL'";
    
    
    
        /**
     * 柱状推挤图
     */
    //菜品销售结构分析
    public static final String QUERY_HISTOFRAM_CHART_MODEL_FOOD_SALES_STUCTURE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE='HISTOFRAML1'";
    
    //[时段]门店销售模式账单数排行 
    public static final String NUMBER_OF_STORE_SALES_MODEL_BILLS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE SQL_TYPE = 'SDMDXSMSZDS' AND REPORT_NUM = 'SAAS_MK_2017_05'";
    
    //[时段]门店销售模式实收金额排行
    public static final String THE_NUMBER_OF_SALES_MODE_IN_STORES= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE SQL_TYPE = 'SDMDXSMSSSJE' AND REPORT_NUM = 'SAAS_MK_2017_05'";
    
    //[时段]菜品销售模式实收金额排行
    public static final String SALES_MODEL_OF_VEGETABLE_PRODUCTS = "SELECT SQL FROM SAAS_REPORT_ENGINE where SQL_TYPE = 'SDCPXSMSSSJE' AND REPORT_NUM = 'SAAS_MK_2017_05'";
    
    //[时段]菜品销售模式实收数量排行
    public static final String THE_QUANTITY_RANKING_OF_THE_SALES_MODEL_OF_THE  = "SELECT SQL FROM SAAS_REPORT_ENGINE where SQL_TYPE = 'SDCPXSMSSSSL' AND REPORT_NUM = 'SAAS_MK_2017_05'";
    
    //[时段]退菜原因数量排行
    public static final String THE_NUMBER_OF_REASONS_FOR_RETURNING_VEGETABLES_DURING  = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE SQL_TYPE = 'SDTCYYSL' AND REPORT_NUM = 'SAAS_MK_2017_05'";
    
    /**
     * 南丁格尔玫瑰图
     */
    //班次营业实收金额占比
    public static final String NIGHTINGALE_ROSE_DIAGRAM= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_08'  AND SQL_TYPE = 'BCYYSSJEZB'";
    
    //付款类型金额占比
    public static final String PROPORTION_OF_PAYMENT_TYPE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_08'  and SQL_TYPE = 'FKLXJEZB'";
    
    /**
     * 柱状多系列彩虹
     */
    //门店销售模式营业应收金额排行
    public static final String STORE_SALES_MODEL_BUSINESS_RECEIVABLE_AMOUNT_RANKING= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_07' AND SQL_TYPE = 'MDXSMSYYYSPH'";
    
    //门店销售模式消费账单排行
    public static final String STORE_SALES_MODEL_CONSUMPTION_BILL_RANKING= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_07' AND SQL_TYPE = 'MDXSMSXFZDPH'";
    
    //[工作日/非工作日]会员菜品点击率排行（横坐标按总点击率排行选出菜品）
    public static final String MEMBER_MENU_CLICK_RATE_RANKING= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_07' AND SQL_TYPE = 'HYCPDJVPH_GZR'";
    
    //营业班次售卖分析
    public static final String BUSINESS_FREQUENCY_ANALYSIS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_36' AND SQL_TYPE = 'RAINBOWL1'";
    
    
    /**
     * 雷达图
     */
    //营业班次菜品实收金额分布
    public static final String DISTRIBUTION_OF_THE_AMOUNT_OF_MONEY_COLLECTED_FORM_BUSINESS_CLASS_DISHES= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_10' AND SQL_TYPE = 'HYDJRSFP'";
    
    //渠道销售模式营业实收分布
    public static final String DISTRIBUTION_DISTRIBUTION_OF_CHANNEL_SALES_MODEL= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_10' AND SQL_TYPE = 'QDXSMSYYSSFP'";

    //会员消费周期账单数
    public static final String MEMBER_CONSUMPTION_CYCLE_BILLS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_10' AND SQL_TYPE = 'HYXFZQZDS'";

    //会员消费周期消费客数
    public static final String NUMBER_CUSTOMERS_MEMBER_CONSUMPTION_CYCLE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_10' AND SQL_TYPE = 'HYXFZQXFKS'";

    //会员各等级消费账单数
    public static final String NUMBER_OF_MEMBERS_CONSUMPTION_BILLS_BY_GRADE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_10' AND SQL_TYPE = 'HYGDJXFZDS'";

    //会员各等级消费客数
    public static final String NUMBER_OF_CUSTOMERS_PER_CLASS_OF_MEMBERS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_10' AND SQL_TYPE = 'HYGDJXFKS'";

    
    /**
     * 折线标准面积图
     */
    //[日期]会员沉淀金额趋势
    public static final String MEMBER_PRECIPITATION_TREND_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_09' AND SQL_TYPE ='RHYCDJEQS'";
    
    //[日期]微信点餐单量占比趋势
    public static final String THE_TREND_OF_SINGLE_AMOUNT_OCCUPATION_OF_WECHAT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_09' AND SQL_TYPE ='RWXDCDLZBQS'";
    
    //[日期]微信点餐消费金额占比趋势
    public static final String WECHATS_CONSUMPTION_OF_ORDERED_MEALS_AS_A_PERCENTAGE_OF_THE_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_09' AND SQL_TYPE ='RWXDCXFJEZBQS'";
   
    //[日期]微信点餐数量趋势
    public static final String TRENDS_IN_THE_NUMBER_OF_WECHAT_ORDERS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_09' AND SQL_TYPE ='RWXDCSLQS'";
    
    //[日期]卡充值金额趋势
    public static final String CARD_RECHARGE_TREND= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_09' AND SQL_TYPE ='KCZJEQS'";
    
    //[日期]卡消费占比趋势
    public static final String CARD_CONSUMPTION_TRENDS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_09' AND SQL_TYPE ='KXFZBQS'";
    
    //团体挂账金额趋势
    public static final String CARDTHE_AMOUNT_OF_CREDIT_TRENDS_GROUP= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_09' AND SQL_TYPE ='TTGZJEQS'";
    
    
    /**
     * 双指标文字
     */
    //[日期]会员沉淀金额趋势
    public static final String PERCENTAGE_OF_NONDOCUMENTARY_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_11' AND SQL_TYPE = 'MDJEZB'";
    
    //奉送金额占比
    public static final String AS_THE_PROPORTION_OF_THE_AMOUNT= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_11' AND SQL_TYPE = 'FSJEZB'";
    
    //粉丝会员转化率
    public static final String FAN_MEMBERSHIP_CONVERSION_RATE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_11' AND SQL_TYPE = 'FSHYZHL'";
    
    //会员消费金额占比
    public static final String MEMBERSHIP_CONSUMPTION_RATIO= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_11' AND SQL_TYPE = 'HYXFSSJEZB'";
    
    //会员消费客数占比
    public static final String MEMBERSHIP_CONSUMPTION_RATIOS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_11' AND SQL_TYPE = 'HYXFKSZB'";
    
    //外卖入会会员占比 
    public static final String MEMBERSHIP_RATIO= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_11' AND SQL_TYPE = 'WMRHHYZB'";
    
    //门店入会会员占比
    public static final String MEMBERSHIP_RATIOS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_11' AND SQL_TYPE = 'MDRHHYZB'";
    
    //恢复账单数量占比
    public static final String RECOVERY_OF_BILLS_AS_A_PERCENTAGE= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_MK_2017_11' AND SQL_TYPE = 'HFZDSLZB'";
    
    /**
     * 列表
     */
    //菜品销售实收数量 
    public static final String SALES_VOLUME_OF_VEGETABLE_PRODUCTSS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_12' AND SQL_TYPE='CPSSSLPH'";
    
    //菜品销售实收金额
    public static final String THE_AMOUNT_OF_THE_SALE_OF_THE_DISHESS= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_12' AND SQL_TYPE='CPSSJEPH'";
    
    //付款方式金额排行
    public static final String Amount_of_payment= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_12' AND SQL_TYPE='FKFSJEPH'";
    
    //付款方式金额数量
    public static final String TERMS_OF_PAYMENT_RANKING= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM='SAAS_MK_2017_12' AND SQL_TYPE='FKJESLPH'";
    
/*************************************************************************SAAS_BI_2017_119**************************************************************************/
    			   /******************************** ************************代销菜品汇总报表*********** ***************************************/
	/**
	 * 代销菜品汇总报表第一层
	 */
	public static final String ENGINE_WHOLESALE_DISHES_SUMMARY_OF_THE_FIRST_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_119' AND SQL_TYPE='L1'";
	
	/**
	 * 代销菜品合计
	 */
	public static final String ENGINE_TOTAL_CONSIGNMENT_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_119' AND SQL_TYPE='L0'";
	
	/**
	 * 代销菜品汇总报表类别第一层
	 */
	public static final String ENGINE_WHOLESALE_DISHES_REPORT_FORMS_THE_FIRST_LAYER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_119' AND SQL_TYPE='LB1'";
	
	/**
	 * 代销菜品汇总报表类别第二层
	 */
	public static final String ENGINE_WHOLESALE_DISHES_REPORTS_CATEGORIES_SECOND_LAYERS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_119' AND SQL_TYPE='LB2'";
	
	/**
	 * 代销菜品汇总报表类别第三层
	 */
	public static final String ENGINE_WHOLESALE_DISHES_REPORTS_CATEGORIES_THIRD_LAYERS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_119' AND SQL_TYPE='LB3'";
	
	/**
	 * 代销菜品汇总报表类别合计
	 */
	public static final String ENGINE_CATEGORIES_OF_AGGREGATED_ITEMS_OF_DISHES_FOR_SALE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_119' AND SQL_TYPE='LB0'";
	
	/**
	 * 单个会员积分查询 明细
	 */
	public static final String MENBERSHIP_COMPREHENSIVE_TRANCATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_121' AND SQL_TYPE='L1'";
	/**
	 *  单个会员积分查询 合计
	 */
	public static final String MENBERSHIP_COMPREHENSIVE_TRANCATION_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_121' AND SQL_TYPE='L0'";

	/**
	 * 员工时效综合分析,服务员点餐时长
	 */
	public static final String ENGINE_ANALYSE_PERIOD_OF_WAITER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_129' AND SQL_TYPE='FXL0'";
	
	/**
	 * 员工时效综合分析，厨房加工时间
	 */
	public static final String ENGINE_ANALYSE_PERIOD_OF_KITCHEN = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_129' AND SQL_TYPE='FXL1'";
	
	/**
	 * 员工时效综合分许，快递员时长
	 */
	public static final String ENGINE_ANALYSE_PERIOD_OF_RIDE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_129' AND SQL_TYPE='FXL2'";
	
	/**
	 * 外卖顾客复购分析，按照日期查询
	 */
	public static final String OUTCUSTOMER_REPURCHASE_ANALYSE_OF_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_131' AND SQL_TYPE = 'L1'";

	/**
	 * 根据账单号查询账单表头数据公共sql
	 */
	public static final String SELECT_BILLDATATITLE_PUBLICE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_133' AND SQL_TYPE = 'L1'";
 
	
	/**
	 * 现金存款报表
	 */
	public static final String CASH_DEPOSIT_REPORT = "SELECT * FROM saas_report_engine where id =905";
	
	/**
	 * 贵宾券溢收差异
	 */
	public static final String VIP_COUPONS_OVERFLOW_DIFFERENCE_INQUIRIE_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE T WHERE REPORT_NUM='SAAS_BI_2017_137'";
	
	/**
	 * 账单出品时效查询
	 */
	public static final String BILLS_PRODUCED_AGING_INQUIRE = "SELECT * FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_147'";
	
	/****************************SAAS_BI_2017_134**************************************************************************/
	/***************************************门店日结状态查询*********** ***************************************/
	public static final String ENGINE_STORE_DAILY_SETTLEMENT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_134' AND SQL_TYPE = 'RJZT1'";
	/****************************SAAS_BI_2017_123**************************************************************************/
	/***************************************会员营销统计报表*********** ***************************************/
	public static final String ENGINE_MEMBER_MARKETING_STATISTICS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_123' AND SQL_TYPE = 'HYYX1'";


	/****************************SAAS_BI_2017_124**************************************************************************/
	/***************************************营销活动统计报表*********** ***************************************/
	public static final String ENGINE_MARKETING_ACTIVITY_STATISTICS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_124' AND SQL_TYPE = 'YXHD1'";
	/***************************************营销活动统计报表 标版*********** ***************************************/
	public static final String ENGINE_MARKETING_ACTIVITY_STATISTICS_BB = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_124' AND SQL_TYPE = 'YXHDBB'";
	
	/**
	 * 菜品转台查询     SAAS_BI_2017_150
	 */
	public static final String FOOD_CHANGE_TABLE_QUERY = "select sql from saas_report_engine  where report_num = 'SAAS_BI_2017_150' and sql_type = 'FUNCTION'";
	
	/****************************SAAS_BI_2017_184 营业折让查询**************************************************************************/
	public static final String ENGINE_BUSINESS_DISCOUNT_SUMMARY_QUERY = "select sql from saas_report_engine  where report_num = 'SAAS_BI_2017_184' and sql_type = 'FUNCTION'";
	
	
	/************************************味千报表************************************/
	/**
	 * 外卖平台销售分析
	 */
	public static final String OUT_PLATFORM_SALES_ANALYSE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_138'";
	
	/**
	 * 外卖平台销售明细
	 */
	public static final String OUT_PLATFORM_SALES_DETAIL = "select sql from saas_report_engine where report_num = 'SAAS_BI_2017_139'";
	
	/**
	 * 门店审核情况查询
	 */
	public static final String STORE_CHECK_CONDITION_QUERY = "select sql from saas_report_engine where report_num = 'SAAS_BI_2017_135'";
	
	/**
	 * 菜品出品时效查询
	 */
	public static final String DISHES_PRODUCE_PERIOD_QUERY = "select * from saas_report_engine where report_num = 'SAAS_BI_2017_148'";
	/**
	 * 菜品销售排行报表
	 */
	public static final String FOOD_SALES_RANKING = "select * from saas_report_engine where report_num='SAAS_BI_2016_14' and sql_type='CPXSPH'";
	/**
	 * 账单转台查询
	 */
	public static final String BILLING_TURNTABLE_QUERY = "SELECT * from saas_report_engine where report_num = 'SAAS_BI_2017_149'";

	/**
	 * 门店操作单据查询
	 */
	public static final String STORE_OPERATING_RECEIPT_QUERY = "select * from saas_report_engine where report_num='SAAS_BI_2017_145'";

	/**
	 * 门店支付方式报表
	 */
	public static final String STORE_PAYMENT_METHOD_WQ = "select * from saas_report_engine where report_num='SAAS_BI_2017_153' and sql_type='MDZFFSWQ'";
	/**
	 * 门店桌位报表
	 */
	public static final String STORE_TABLE_WQ = "select * from saas_report_engine where report_num='SAAS_BI_2017_155' and sql_type='MDZWBBWQ'";
	/**
	 * 菜品信息列表
	 */
	public static final String FOOD_INFORMATION_WQ = "select * from saas_report_engine where report_num = 'SAAS_BI_2017_160' and sql_type='FUNCTION'";
	/**
	 * 促销活动信息列表
	 */
	public static final String SALES_PROMOTION_WQ = "select * from saas_report_engine where report_num='SAAS_BI_2017_159' AND  sql_type='CXHDXXLB'";
	/**
	 * 门店菜谱查询
	 */
	public static final String STORE_MENU_REPORT = "select sql from saas_report_engine where report_num = 'SAAS_BI_2017_154' and sql_type = 'FUNCTION'";
	
	/**
	 * 门店销售排行报表 
	 */
	public static final String STORE_SALES_RANKING_WQ = "select * from saas_report_engine where report_num='SAAS_BI_2017_151'";

	/**
	 * 菜品销售排行(味千专版)
	 */
	public static final String FOOD_SALES_RANKING_WQ = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type = 'CPXSPHWQ'";
	
	/**
	 * 复华门店现金日结报表
	 */
	public static final String STORE_CASH_DAY_SUMMARY = "SELECT sql from saas_report_engine where report_num='SAAS_BI_2017_162' and sql_type = 'FUNCTION'";
	
	/**
	 * 菜品授权记录
	 */
	public static final String DISHES_PERMIT_RECORD_QUERY = "select SQL from saas_report_engine where report_num = 'SAAS_BI_2017_156' and sql_type = 'FUNCTION'";
	
	/**
	 * POS配置查询
	 */
	public static final String POS_CONFIG_INFORMATION = "select sql from saas_report_engine where report_num='SAAS_BI_2017_152' and sql_type = 'FUNCTION'";
	
	/**
	 * 门店打烊(函数)
	 */
	public static final String SHOP_CLOSE_QUERY = "select sql from saas_report_engine where report_num='SAAS_BI_2017_60' and sql_type = 'MDDY'";
	
	/**
	 * 门店价格体系
	 */
	public static final String STORE_PRICE_SYSTEM = "select sql from saas_report_engine where report_num='SAAS_BI_2017_161' and sql_type = 'FUNCTION'";
	
	/**
	 * 销售渠道统计报表
	 */
	public static final String SALES_CHANEL_COUNT = "select sql from saas_report_engine where report_num ='SAAS_BI_2017_179' and sql_type = 'FUNCTION'";
	
	
	/**
	 * 按卡充值机构沉淀 第1层 明细1层
	 */
	public static  final String ENGINE_THE_BOOKS_OR_BOOKS_TOTAL_PREPAID_CZMDL1 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='CZMDL1'";
	
	/**
	 * 按卡充值机构沉淀 第1层 合计
	 */
	public static  final String ENGINE_THE_BOOKS_OR_BOOKS_TOTAL_PREPAID_CZMDL0 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='CZMDL0'";
	
	/**
 
	 * 特殊品类查询
	 */
	public static final String SPECIAL_ITEM_SALES_REPORT = "select sql from saas_report_engine where report_num = 'SAAS_BI_2017_173' and sql_type = 'FUNCTION'";
	
	/**
	 * 门店基础信息
	 */
	public static final String STORE_BASE_INFORMATION = "select sql from saas_report_engine where report_num='SAAS_BI_2017_158' and sql_type='MDJCXXWQ'";
	
	/**
	 * 人员关系角色
	 */
	public static final String PERSON_RELATIONSHIP = "select sql from saas_report_engine WHERE report_num='SAAS_BI_2017_174' and sql_type='RYGXQXLB'";
	
	/**
	 * 门店口味列表
	 */
	public static final String STORE_TASTE_LIST = "SELECT sql from saas_report_engine where report_num = 'SAAS_BI_2017_176' and sql_type = 'FUNCTION'";
	
	/**
	 * 菜品变价
	 */
	public static final String DISHES_CHANGE_PRICE = "select sql from saas_report_engine where report_num = 'SAAS_BI_2017_157' and sql_type = 'CPBJ'";
	
	/**
	 * 按卡充值机构沉淀 2层
	 */
	public static  final String ENGINE_THE_BOOKS_OR_BOOKS_TOTAL_PREPAID_CZMDL2 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='CZMDL2'";
	
	/**
	 * 按卡充值机构沉淀 3层 明细1层
	 */
	public static  final String ENGINE_THE_BOOKS_OR_BOOKS_TOTAL_PREPAID_CZMDL3_L1 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='CZMDLXL1'";
	
	/**
	 * 按卡充值机构沉淀 3层 明细2层
	 */
	public static  final String ENGINE_THE_BOOKS_OR_BOOKS_TOTAL_PREPAID_CZMDL3_L2 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='CZMDLXL2'";
	
	/**
	 * 按卡充值机构沉淀 3层 合计
	 */
	public static  final String ENGINE_THE_BOOKS_OR_BOOKS_TOTAL_PREPAID_CZMDL3_L0 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='CZMDLXL0'";
	

	/**
	 * 营业汇总统计查询(外卖付款方式)
	 */
	public static final String ENGINE_BUSINESS_SUMMARY_TAKEOUT_STATISTICS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_47' AND SQL_TYPE='WML1'";
	
	/**
	 * 营业汇总统计查询(外卖付款方式)
	 */
	public static final String DISHES_SALES_MODE = "select sql from saas_report_engine where report_num = 'SAAS_BI_2017_171' and sql_type = 'FUNCTION'";
	
	
	/**
	 * 营业汇总统计查询(外卖付款方式)
	 */
	public static final String DISCARDED_DISHESREPO_REPORT = "select sql from saas_report_engine where report_num = 'SAAS_BI_2017_182' and sql_type = 'FUNCTION'";
	
	
	
	
	
	/************************************SAAS_MK_2018_02************************************/
	/**
	 * 嵌套环形图
	 */
	//周期模式应收金额
	public static final String PERIODIC_MODE_RECEIVABLES = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQMSYSJE'";
	 
	//周期模式实收金额
	public static final String CYCLICAL_MODEL_AMOUNT = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQMSSSJE'";
	
	//周期模式净收
	public static final String CYCLE_MODE_NET_INCOME = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQMSJS'";
	
	//周期模式账单数
	public static final String PERIODIC_MODE_BILL_NUMBER = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQMSZDS'";
	
	//周期模式消费客数
	public static final String  NUMBER_OF_CUSTOMERS_IN_PERIODIC_MODE = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQMSXFKS'";
	
	//周期班次应收金额
	public static final String CYCLE_SHIFT_RECEIVABLE_AMOUNT  = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQBCYSJE'";
	
	//周期班次实收金额
	public static final String  AMOUNT_RECEIVED_BY_CYCLE_SHIFT = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQBCSSJE'";
	
	//周期班次净收
	public static final String PERIODIC_SHIFT_NET_INCOME  = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQBCJS'";
	
	//周期班次账单数
	public static final String NUMBER_OF_BILLS_FOR_PERIODIC_SHIFTS  = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQBCZDS'";
	
	//周期班次消费客数
	public static final String NUMBER_OF_PERIODIC_SHIFTS_CONSUMED  = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQBCXFKS'";
	
	//周期会员级别应收金额
	public static final String  PERIODIC_MEMBER_LEVEL_RECEIVABLES = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQHYJBYSJE'";
	
	//周期会员级别实收金额
	public static final String AMOUNT_RECEIVED_AT_PERIODIC_MEMBER_LEVEL  = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQHYJBSSJE'";
	
	//周期会员级别净收
	public static final String  CYCLE_MEMBERSHIP_LEVEL_NET_INCOME = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQHYJBJS'";
	
	//周期会员级别账单数
	public static final String  NUMBER_OF_PERIODIC_MEMBER_LEVEL_BILLS = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQHYJBZDS'";
	
	//周期会员级别消费客数
	public static final String  NUMBER_OF_CUSTOMERS_AT_PERIODIC_MEMBER_LEVEL = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_02' and sql_type = 'ZQHYJBXFKS'";
	
	
	/************************************SAAS_MK_2018_03************************************/
	/**
	 * 漏斗图
	 */
	
	//收入漏斗分析
	public static final String INCOME_FUNNEL_ANALYSIS  = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_03' and sql_type = 'SRLDFX'";
	
	//账单漏斗分析
	public static final String BILL_FUNNEL_ANALYSIS  = "select * from saas_report_engine  where  report_num = 'SAAS_MK_2018_03' and sql_type = 'ZDLDFX'";
	
	
	/** 营业汇总统计查询-实时
	 * 普通函数
	 */
	public static final String BUSINESS_SUMMARY_FUNCTION_F1 = "select * from saas_report_engine where report_num = 'SAAS_BI_TIME_2016_01' and sql_type='F1'";
	
	/** 营业汇总统计查询-实时
	 * 自定义导出使用
	 */
	public static final String BUSINESS_SUMMARY_FUNCTION_F2 = "select * from saas_report_engine where report_num = 'SAAS_BI_TIME_2016_01' and sql_type='F2'";
	
	
	/************************************SAAS_BI_2018_16************************************/
	
	//营业区域汇总查询小计L1
	public static final String BUSINESS_AREA_SUMMARY_QUERY_subtotal = "select * from saas_report_engine where report_num = 'SAAS_BI_2018_16' and sql_type='FUNCTIONL1'";
	
	//营业区域汇总查询L0
	public static final String BUSINESS_AREA_SUMMARY_QUERY_amount_to = "select * from saas_report_engine where report_num = 'SAAS_BI_2018_16' and sql_type='FUNCTIONL0'";
		
	
	
	
	/************************************SAAS_BI_2018_19 菜品类别班次报表************************************/
	public static final String BUSINESS_DISHES_CLASSOFOCATION_CLASSES = "select * from saas_report_engine where report_num = 'SAAS_BI_2018_19' and sql_type='CPLBBC'";
	
	
	/************************************SAAS_BI_2018_20营业时段同比环比************************************/
	public static final String BUSINESS_HOURS_COMPARED_SAME_MONTH_LAST_YEAR = "select * from saas_report_engine where report_num = 'SAAS_BI_2018_20' and sql_type='YYSDTBHB'";
	
	
	/************************************SAAS_BI_2018_21营业加盟对账报表************************************/
	public static final String BUSINESS_ALLIANCE_RECONCILIATION_STATEMENT = "select * from saas_report_engine where report_num = 'SAAS_BI_2018_21' and sql_type='FUNCTION'";
	
	/************************************SAAS_BI_2018_23菜品销售时段流水************************************/
	public static final String FOOD_SALES_TIME_FLOW_QUERY = "select * from saas_report_engine where report_num = 'SAAS_BI_2018_23' and sql_type='CPXSSDLS'";
	
	/************************************SAAS_BI_2018_24AB帐报表************************************/
	public static final String SPECIAL_ACCOUNT_STATEMENT_QUERY = "select sql from saas_report_engine where report_num = 'SAAS_BI_2018_24' and sql_type='FUNCTION'";




	/************************************SAAS_BI_2019_01会员余额查询报表************************************/
	//明细
	public static  final  String MEMBER_CARD_BALANCE_QUERY_R1 = "SELECT SQL FROM saas_report_engine WHERE (report_num = 'SAAS_BI_2019_01' AND sql_type = 'R1')";

	//金额汇总
	public static  final  String MEMBER_CARD_BALANCE_QUERY_R2 = "SELECT SQL FROM saas_report_engine WHERE (report_num = 'SAAS_BI_2019_01' AND sql_type = 'R2')";



	/************************************Jasperreport************************************/
	
	public static final String JASPERREPORT_QUERY = "select * from saas_report_engine where report_num = 'SAAS_BI_JASPER_01' and sql_type='J1'";
	
	public static final String BUSINESS_SUMMARY_STATISTICAL_REPORT = "select * from saas_report_engine where report_num = 'SAAS_BI_JASPER_02' and sql_type='J2'";
	
	public static final String BLOCK_CONSUME_CONSUMPTION_times = "select * from saas_report_engine where report_num = 'SAAS_BI_JASPER_03' and sql_type='J3'";
	
	/**
	 * 八合里 新增净收两张报表
	 */
	public static final String BUSINESS_FOODDETAILSNET_FUNCTION_F1 = "select * from saas_report_engine where report_num = 'SAAS_BI_FUN_2018_01' and sql_type='F1'";
	
	public static final String BUSINESS_FOODCOUNTNET_FUNCTION_F1 = "select * from saas_report_engine where report_num = 'SAAS_BI_FUN_2018_02' and sql_type='F1'";
	
	public static final String BUSINESS_COUNUT_HUALAISHI_FUNCTION_F1 = "select * from saas_report_engine where report_num = 'SAAS_BI_HLS_2018_01' and sql_type='FUNCTION'";
	
	/**
	 * 菜品区域分类查询
	 */
	public static final String REGION_VARRIETY_FUNCTION_F1 = "select * from saas_report_engine where report_num='SAAS_BI_2018_08' and sql_type='zdycplbsr'";
	
	/**
	 * 菜品辅助数量报表
	 */
	public static final String AUXILLIARY_NUMBER_FIND1 = "SELECT * FROM SAAS_REPORT_ENGINE  WHERE REPORT_NUM= 'SAAS_BI_2019_01' AND SQL_TYPE='find1'";
}
