package com.tzx.cc.common.task.service;

import java.util.HashMap;
import java.util.List;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.cc.baidu.bo.imp.EleMeOrderReceiver;
import com.tzx.cc.baidu.bo.imp.EleMeOrderReceiverV2;
import com.tzx.cc.baidu.bo.imp.ThirdPartyOrderReceiver;
import com.tzx.cc.thirdparty.bo.imp.EleManager;
import com.tzx.cc.thirdparty.bo.imp.EleMeManager;
import com.tzx.cc.thirdparty.util.ElmUtils;

import eleme.openapi.sdk.api.entity.message.OMessage;

public class QueryElmOrderRunnable implements Runnable {
	private ThirdPartyOrderReceiver orderReceive;
	private static final Logger logger = Logger
			.getLogger(QueryElmOrderRunnable.class);

	@Override
	public void run() {
		// elm_orders_v1();

		elm_orders_v2();
	}

	public void elm_orders_v1() {
		try {
			EleManager eleManager = new EleManager("mrtian");
			JSONObject response_order_ids_obj = eleManager
					.queryEleOrdercmdPost("order/new/", "1204990380",
							"7ee0ad79a4bc80829d8b431dbe21899b5ec2a637",
							new HashMap<String, String>(), "get");
			if (response_order_ids_obj.optString("code").equals("200")) {
				String order_ids = response_order_ids_obj.optJSONObject("data")
						.optString("order_ids");
				String sub_order_ids = order_ids.substring(1,
						order_ids.length() - 1);
				if (StringUtils.isNotEmpty(sub_order_ids)) {
					String[] ids = sub_order_ids.split(",");
					if (ids.length > 0) {
						logger.info("主动拉取饿了么订单号为:" + order_ids);
						for (String id : ids) {
							JSONObject order = new JSONObject();
							order.put("source", "1204990380");
							order.put("secret",
									"7ee0ad79a4bc80829d8b431dbe21899b5ec2a637");
							order.put("eleme_order_id",
									id.substring(1, id.length() - 1));
							orderReceive = new EleMeOrderReceiver(order);
							orderReceive.receive();
						}
					}
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			logger.error("主动拉取饿了么订单保存失败:", ex);
		}
	}

	/**
	 * 接取饿了么2.0接口中的未送达订单信息
	 */
	private void elm_orders_v2() {
		try {
			List<String> tanencyList = ElmUtils.getAuthorizedTenancyList();
			for (String tanency_shop : tanencyList) {
				String[] arr = tanency_shop.split("_");
				String tanency = arr[0];
				String shopId = arr[1];
				EleMeManager eleMeManager = new EleMeManager(tanency, shopId);
				List<OMessage> messageList = eleMeManager
						.queryEleNonReachedOrder();
				
				if (messageList != null) {
					for (OMessage message : messageList) {
						ThirdPartyOrderReceiver orderReceiveV2 = new EleMeOrderReceiverV2(
								JSONObject.fromObject(message));

						orderReceiveV2.receive();
					}
				}
				
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}

	public void elm_orders_batch_get() {

		try {
			String restaurant_ids_str = "1194423,1194002,1193781,1194518,1195543,842068,842069,842074,1195816,1194946,842066,1194706,1194814,1194832,155172506,1195025,1195123,1195036,1195908,1195964,1193661,1194256,,1194408,1194378,1194321,1194419,1194333,1193955,1194914,1194922,1195589,1194921,,947955,1194742,,1233991,1233549,,1194036,1194821,1194448,842072,1195471,1194915,1194013,901139,1194552,1193714,1194049,1194137,1458836,1458689,1458898,154876307,150873498,152167655,152175925";
			String[] restaurant_ids_arr = restaurant_ids_str.split(",");
			for (String restaurant_id : restaurant_ids_arr) {
				EleManager eleManager = new EleManager("mrtian");
				HashMap<String, String> a = new HashMap<String, String>();
				a.put("day", "2017-05-24");
				a.put("restaurant_id", restaurant_id);
				a.put("statuses", "9");
				JSONObject response_order_ids_obj = eleManager
						.queryEleOrdercmdPost("orders/batch_get/",
								"1204990380",
								"7ee0ad79a4bc80829d8b431dbe21899b5ec2a637", a,
								"get");
				if (response_order_ids_obj.optString("code").equals("200")) {
					String order_ids = response_order_ids_obj.optJSONObject(
							"data").optString("order_ids");
					String sub_order_ids = order_ids.substring(1,
							order_ids.length() - 1);
					if (StringUtils.isNotEmpty(sub_order_ids)) {
						String[] ids = sub_order_ids.split(",");
						if (ids.length > 0) {
							logger.info("主动拉取饿了么订单号为:" + order_ids);
							for (String id : ids) {
								JSONObject order = new JSONObject();
								order.put("source", "1204990380");
								order.put("secret",
										"7ee0ad79a4bc80829d8b431dbe21899b5ec2a637");
								order.put("eleme_order_id", id);
								orderReceive = new EleMeOrderReceiver(order);
								orderReceive.receive();
							}
						}
					}
				}
			}

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
