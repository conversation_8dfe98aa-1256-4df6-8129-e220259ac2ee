package com.tzx.report.service.rest.commonreplace;

import com.tzx.report.bo.commonreplace.ExportReportService;
import com.tzx.report.common.util.ReportExportUtils;
import jxl.write.WriteException;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;

@Controller("ExportReportRest")
@RequestMapping("/report/export")
public class ExportReportRest {

    @Resource(name = ExportReportService.NAME)
    private ExportReportService exportReportService;

    //导出大量数据
    @RequestMapping(value = "/exportDataAll")
    public void exportDataAll(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        HttpSession session = request.getSession();
        HSSFWorkbook workBook = null;
        String exportName = null;
        Object[] o = null;
        try
        {
            workBook = new HSSFWorkbook();

            JSONObject p = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                p.put(key, map.get(key)[0]);
            }

            String session_user_organ_word = "user_organ";
            if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)||((Integer)session.getAttribute("valid_state")).equals(0))session_user_organ_word="user_organ_codes_group";

            if(p.optString("store_id").length()==0){
                p.element("store_id", session.getAttribute(session_user_organ_word));
            }

            if(p.optString("p_store_id").length()==0){
                p.element("p_store_id", session.getAttribute(session_user_organ_word));
            }
            if(p.optString("jy_store_id").length()==0){

                p.element("jy_store_id", session.getAttribute(session_user_organ_word));
            }
            if(p.optString("store_ids").length()==0){

                p.element("store_ids", session.getAttribute(session_user_organ_word));
            }
            // 填装当前登录人
            p.put("man", session.getAttribute("employeeName"));
            exportName = p.optString("exportName");

            o = exportReportService.exportDataAll((String) session.getAttribute("tenentid"), p ,workBook);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        try
        {
            if(o[1] == null){
                ReportExportUtils.download((HSSFWorkbook)(o[0]),response,exportName);
            }else{
                File f = (File)o[1];
                ReportExportUtils.downloadFile(f,response,exportName);
            }

        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

}
