package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.TakeoutOrdersQueryReconciliationDao;

@Repository(TakeoutOrdersQueryReconciliationDao.NAME)
public class TakeoutOrdersQueryReconciliationDaoImpl implements TakeoutOrdersQueryReconciliationDao{
 
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	 
	List<JSONObject> list =null;
	StringBuilder sb = new StringBuilder();
	 
	@Override
	public JSONObject getTakeoutOrdersQueryReconciliation(String tenancyID, JSONObject condition) throws Exception{	
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure =new ArrayList<JSONObject>();
		String f_tab_cc_wmdddz = null;
		String total_f_tab_cc_wmdddz = null;
		JSONObject result = new JSONObject();
		long total = 0L;
		
		if(!condition.optString("exportdataexpr").equals("''")){
			condition.element("exportdataexpr", "'"+condition.optString("exportdataexpr")+"'");
			int p = condition.optString("p_store_id").indexOf("'");
			if(p==-1){
				 condition.element("p_store_id", "'"+condition.optString("p_store_id")+"'");
			 }
		}
		/*// 大批量导出的时 page 要传1 这样才能查询处所有的
		if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
			condition.put("page", 1);
		}*/
		String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_QUERY_RECONCILIATION);
		SqlRowSet column = dao.query(tenancyID, reportSql.toString());
		if(column.next()){
			f_tab_cc_wmdddz = column.getString("f_tab_cc_wmdddz");
		}
		
		if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
			
			list=this.dao.query4Json(tenancyID,f_tab_cc_wmdddz);
			structure = conditionUtils.getSqlStructure(tenancyID,f_tab_cc_wmdddz);
			//合计
			String TotalSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_QUERY_RECONCILIATION_TOTAL);
			SqlRowSet columnTotal = dao.query(tenancyID, TotalSql.toString());
			if(columnTotal.next()){
				total_f_tab_cc_wmdddz = columnTotal.getString("f_tab_cc_wmdddz");
			}
			footerList = this.dao.query4Json(tenancyID, total_f_tab_cc_wmdddz);
			total = footerList.get(0).getLong("total_count");
		}else{
				list = this.dao.query4Json(tenancyID,f_tab_cc_wmdddz.toString());
				//合计
				String TotalSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_QUERY_RECONCILIATION_TOTAL);
				SqlRowSet columnTotal = dao.query(tenancyID, TotalSql.toString());
				if(columnTotal.next()){
					total_f_tab_cc_wmdddz = columnTotal.getString("f_tab_cc_wmdddz");
				}
				footerList = this.dao.query4Json(tenancyID, total_f_tab_cc_wmdddz);
				total = footerList.get(0).getLong("total_count");
	    	}

		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("rows", list);
		result.put("page", pagenum);
		result.put("footer", footerList);
		result.put("total",total);
		result.put("structure",structure);
		return result;
	}

	@Override
	public JSONObject getOrderDetailInquiry(String tenancyID,JSONObject condition) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		
		String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKE_OUT_ORDER_DETAILS);
		total = this.dao.countSql(tenancyID,reportSql.toString());
		list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
			
		footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKE_OUT_ORDER_DETAILS_TOTAL).toString());
		 
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		return result;
	}

	@Override
	public JSONObject getOrderDiscountInquiry(String tenancyID,JSONObject condition) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		
		String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_PREFERENTIAL_QUERY);
		total = this.dao.countSql(tenancyID,reportSql.toString());
		list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
			
		footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_PREFERENTIAL_QUERY_TOTAL).toString());
		 
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		return result;
	}

	@Override
	public JSONObject getOrderPaymentEnquiry(String tenancyID,JSONObject condition) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		
		String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_PAYMENT_INQUIRY);
		total = this.dao.countSql(tenancyID,reportSql.toString());
		list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
		
		footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TAKEOUT_ORDERS_PAYMENT_INQUIRY_TOTAL).toString());
		 
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		return result;
	}
}
