package com.tzx.cc.baidu.cont;

/**
 * <AUTHOR>
 *
 */
public class SuweiConst {
	/**
	 * 是
	 */
	public final static int Y = 1;
	
	/**
	 * 否
	 */
	public final static int N = 0;
	
	/**
	 * 速位平台
	 */
	public final static String REDIS_KEY_NAME_PRIVISOUS_SUWEI = "suwei_order_";
	
	/**
	 * 
	 */
	public final static Long REDIS_EXPIRE_SUWEI = Long.valueOf(60*60*24);
	/**
	 * 生成订单并申请速位平台订单
	 */
	public final static String THIRD_SUWEI_APPLAY_ORDER = "THIRD_SUWEI_APPLAY_ORDER";
	/**
	 * 速位通知saas状态变更
	 */
	public final static String THIRD_SUWEI_NOTIFY_SAAS = "THIRD_SUWEI_NOTIFY_SAAS";
	
	/**
	 * 鉴权错误， TOKEN 无效 
	 */
	public final static int ERROR_INVALID_TOKEN = 1001;
	
	/**
	 * saas系统内部错误
	 */
	public final static int ERROR_SAAS_INNER = 5;
	
	/**
	 * 网络连接失效
	 */
	public final static int ERROR_INVALID_CONNECT = 6;
	
	/**
	 * 错误码
	 */
	public final static String ERR_CODE = "err_code";
	
	/**
	 * 错误说明
	 */
	public final static String ERR_DESC = "err_desc";
	
	/**
	 * 放餐成功
	 */
	public final static String THIRD_SUWEI_STATUS_NOTIFY_PUT_MEAL = "2";
	
	/**
	 * 餐谱取消
	 */
	public final static String THIRD_SUWEI_STATUS_NOTIFY_CANCEL_MEAL = "4";
	
	
}
