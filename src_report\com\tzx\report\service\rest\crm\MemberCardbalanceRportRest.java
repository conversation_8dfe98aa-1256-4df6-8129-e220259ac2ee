package com.tzx.report.service.rest.crm;

/**
 * Created by gj on 2019-04-30.
 * 会员余额查询报表
 */

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import com.tzx.report.bo.crm.MemberCardbalanceRportService;


import com.tzx.report.common.util.ConditionUtils;


@Controller
@RequestMapping(value= "/report/MemberCardbalanceRportRest")
public class MemberCardbalanceRportRest {



    @Resource(name = MemberCardbalanceRportService.NAME)
    private MemberCardbalanceRportService memberCardbalanceRportService ;

    @Resource
    ConditionUtils conditionUtils;

    @RequestMapping(value = "/getMemberCardbalanceQuery")
    public void getMemberCardbalanceQuery(HttpServletRequest request, HttpServletResponse response)throws IOException, WriteException
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        InputStream in = null;
        HttpSession session = request.getSession();
        String result = "";

        try
        {

            JSONObject obj = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                obj.put(key, map.get(key)[0]);
            }

            if(obj.optString("store_ids").length()==0){
                obj.element("store_ids", session.getAttribute("user_organ_codes_group"));
            }

            result = memberCardbalanceRportService.getMemberCardbalanceQuery((String) session.getAttribute("tenentid"), obj).toString();
        }
        catch (Exception e)
        {
            result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
            e.printStackTrace();
        }
        finally
        {
            try
            {
                if (in != null)
                {
                    in.close();
                }
            }
            catch (Exception e)
            {
            }

            try
            {
                out = response.getWriter();
                out.print(result);
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }
}
