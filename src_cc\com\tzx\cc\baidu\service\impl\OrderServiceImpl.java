package com.tzx.cc.baidu.service.impl;

import java.sql.Timestamp;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.tzx.cc.baidu.entity.CmdType;
import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.baidu.service.OrderService;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.baidu.util.SignHolder;
import com.tzx.cc.bo.OrderManagementHqService;
import com.tzx.cc.bo.OrderUpdateManagementService;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.util.CcPartitionUtils;
import com.tzx.framework.bo.CodeService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.task.po.redis.dao.TaskRedisDao;

@Deprecated
@Service(OrderService.NAME)
public class OrderServiceImpl implements OrderService
{	
	@Resource(name = "genericDaoImpl")
	private GenericDao dao;
	
	@Resource(name = TaskRedisDao.NAME)
	private TaskRedisDao taskRedisDao;
	
	@Resource(name = "codeService")
	private CodeService codeService;
	
	@Resource(name=OrderUpdateManagementService.NAME)
	private OrderUpdateManagementService orderManagerService;
	
	@Resource(name = OrderManagementHqService.NAME)
	private OrderManagementHqService orderManagementService;
	
	@Override
	public JSONObject orderCreate(JSONObject condition) throws Exception
	{
		// 商户信息
		JSONObject requestShop = condition.optJSONObject("body").optJSONObject("shop");
		// 订单信息
		JSONObject requestOrder = condition.optJSONObject("body").optJSONObject("order");
		// 顾客信息
		JSONObject requestUser = condition.optJSONObject("body").optJSONObject("user");
		
		String[] shopId = requestShop.optString("id").split("@");
		
		String storeId=shopId[0];
		String tenancyId=shopId[1];
		
		DBContextHolder.setTenancyid(shopId[1]);
		
		JSONObject code_object = new JSONObject();
		code_object.element("store_id", shopId[0]);
		long creditCurrentTime = (long) (System.currentTimeMillis());
		Date creditDate = new Date(creditCurrentTime);
		SimpleDateFormat creditDf = new SimpleDateFormat("yyyyMMdd");   
		code_object.put("busi_date", creditDf.format(creditDate));
		code_object.put("dynamic", Constant.BAIDU_CHANNEL);
		String orderCode = codeService.getCode(shopId[0], Code.CC_ORDER_CODE, code_object);// 调用统一接口来实现
		
		JSONObject orderListParam = new JSONObject();
		// 商户ID
		orderListParam.put("tenancy_id", shopId[1]);
		// 商户id
		orderListParam.put("store_id", shopId[0]);
		// 订单号
		orderListParam.put("order_code", orderCode);
		// 百度订单编号
		orderListParam.put("third_order_code", requestOrder.optString("order_id"));
		// 是否立即送餐
		orderListParam.put("send_immediately", requestOrder.optInt("send_immediately"));
		// 期望送达时间???
		orderListParam.put("send_time", requestOrder.optString("send_time"));
		// 配送费
		orderListParam.put("meal_costs", CommonUtil.fen2Yuan(requestOrder.optInt("send_fee")));
		// 优惠总金额
		orderListParam.put("discount_amount", CommonUtil.fen2Yuan(requestOrder.optInt("discount_fee")));
		// 商户实收总价
		orderListParam.put("shop_fee", CommonUtil.fen2Yuan(requestOrder.optInt("shop_fee")));
		// 订单总价
		orderListParam.put("total_money", CommonUtil.fen2Yuan(requestOrder.optInt("total_fee")));
		// 用户实付总价
		orderListParam.put("actual_pay", CommonUtil.fen2Yuan(requestOrder.optInt("user_fee")));
		// 支付类型
		String payType = requestOrder.optString("pay_type");
		String isOnlinePayment = null;
		if (StringUtils.equals("1", payType)) {
			isOnlinePayment = "0";
		} else if (StringUtils.equals("2", payType)) {
			isOnlinePayment = "1";
		}
		String payment_state = null;
		if (StringUtils.equals("1", payType)) {
			payment_state = "01";
		} else if (StringUtils.equals("2", payType)) {
			payment_state = "03";
		}
		orderListParam.put("is_online_payment", isOnlinePayment);
		orderListParam.put("payment_state", payment_state);
		// 是否需要发票
		orderListParam.put("need_invoice", requestOrder.optInt("need_invoice"));
		// 发票抬头
		orderListParam.put("invoice_title", requestOrder.optString("invoice_title"));
		// 订单备注
		orderListParam.put("remark", requestOrder.optString("remark"));
		// 物流
		orderListParam.put("delivery_party", requestOrder.optInt("delivery_party"));
		// 订单创建时间
		orderListParam.put("single_time",DateUtil.format(new Timestamp(System.currentTimeMillis())));
		// 顾客姓名
		orderListParam.put("consigner", requestUser.optString("name"));
		// 顾客电话
		orderListParam.put("consigner_phone", requestUser.optString("phone"));
		
		// 订餐人
		orderListParam.put("order_name", requestUser.optString("name"));
		// 订餐电话
		orderListParam.put("order_phone", requestUser.optString("phone"));
		// 顾客性别
		
		

		int gender = requestUser.optInt("gender");
		String sex = null;
		if (gender == 1) {
			sex = "man";
		} else if (gender == 2) {
			sex = "woman";
		}
		orderListParam.put("sex", sex);
		// 渠道
		orderListParam.put("chanel", Constant.BAIDU_CHANNEL);
		// 送餐地址
		orderListParam.put("address", requestUser.optString("address"));
		// 送餐地址百度经度
		orderListParam.put("longitude", requestUser.optJSONObject("coord").optString("longitude"));
		// 送餐地址百度纬度
		orderListParam.put("latitude", requestUser.optJSONObject("coord").optString("latitude"));
		// 订单状态
		orderListParam.put("order_state", "01");
		// 订单类型
		orderListParam.put("order_type", "WM02");
		
		
		CcPartitionUtils.lackInsertParam(shopId[1],orderListParam);
		
		this.dao.insertIgnorCase(shopId[1], "cc_order_list", orderListParam);
		
		// 商品信息
		JSONArray requestProductsArray = condition.optJSONObject("body").optJSONArray("products");
		
		JSONObject orderItemParam = null;
		int group_index=1;
		String price=CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(0).optInt("package_price"));
		int number=0;
		for (int i = 0; i < requestProductsArray.size(); i++) {
			
			number+=requestProductsArray.getJSONObject(i).optInt("package_amount");
			
			orderItemParam = new JSONObject();
			// 商户ID
			orderItemParam.put("tenancy_id", shopId[1]);
			// 明细索引
			orderItemParam.put("group_index", group_index++);
			// 订单号
			orderItemParam.put("order_code", orderCode);
			// 商品ID
			String itemId=requestProductsArray.getJSONObject(i).optString("product_id");
			orderItemParam.put("item_id", itemId);

			String[] productNames=requestProductsArray.getJSONObject(i).optString("product_name").split("_");
			// 商品名称
			orderItemParam.put("item_name", productNames[0]);
			//规格ID
			String unitId=null;
			if(productNames.length==2){
				unitId=this.dao.query4Json(tenancyId, "select id from hq_item_unit where item_id='"+itemId+"' and unit_name='"+productNames[1]+"'").get(0).optString("id");
			}else{
				unitId=this.dao.query4Json(tenancyId, "select id from hq_item_unit where item_id='"+itemId+"'").get(0).optString("id");
			}
					
			orderItemParam.put("unit_id", unitId);
			// 份数
			orderItemParam.put("number", requestProductsArray.getJSONObject(i).optInt("product_amount"));
			// 商品价格
			orderItemParam.put("price", CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(i).optInt("product_price")));
			// 商品总价
			orderItemParam.put("product_fee", CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(i).optInt("product_fee")));
			
			CcPartitionUtils.lackInsertParam(shopId[1],orderItemParam);
			
			this.dao.insertIgnorCase(shopId[1], "cc_order_item", orderItemParam);
		}
		
		//保存餐盒费信息
		JSONObject packageBoxFee=new JSONObject();
		packageBoxFee.put("tenancy_id", tenancyId);
		packageBoxFee.put("group_index", group_index);
		packageBoxFee.put("order_code", orderCode);
		packageBoxFee.put("price", price);
		packageBoxFee.put("number", number);
		String sql="SELECT item_id,unit_id,item_name FROM cc_third_organ_info where shop_id=".concat(storeId);
		JSONObject pb=this.dao.query4Json(tenancyId, sql).get(0);
		packageBoxFee.putAll(pb);
		packageBoxFee.put("product_fee", CommonUtil.multiFloatValueOfString(price,String.valueOf(number)));
		
		CcPartitionUtils.lackInsertParam(shopId[1],packageBoxFee);
		
		this.dao.insertIgnorCase(shopId[1], "cc_order_item", packageBoxFee);
		
		//保存付款信息 INSERT INTO "public"."cc_order_repayment" ("tenancy_id", "id", "payment_id", "order_code", "pay_money", "pay_no", "rexchange_rate", "local_currency", "third_bill_code") VALUES ('hdl', '20', '202', 'WX0212920151230000007', '4.0000', 'card00003653', NULL, NULL, 'tl000002821');
		JSONObject payment=new JSONObject();
		payment.put("tenancy_id", tenancyId);
		String paySql="SELECT a.id payment_id FROM payment_way a LEFT JOIN payment_way_of_ogran b on a.id=b.payment_id where a.payment_class='baidu_pay' and b.organ_id="+storeId;
		List list=this.dao.query4Json(tenancyId, paySql);
		if(!list.isEmpty()){
			payment.put("payment_id", ((JSONObject)list.get(0)).optString("payment_id"));
		}
		payment.put("order_code", orderCode);
		payment.put("pay_money", CommonUtil.fen2Yuan(requestOrder.optInt("user_fee")));
		payment.put("third_bill_code", requestOrder.optString("order_id"));
		
		CcPartitionUtils.lackInsertParam(tenancyId,payment);
		
		this.dao.insertIgnorCase(tenancyId, "cc_order_repayment", payment);
		
		// 优惠信息
		JSONArray requestDiscountArray = condition.optJSONObject("body").optJSONArray("discount");
		
		JSONObject discountParam = null;
		for (int i = 0; i < requestDiscountArray.size(); i++) {
			discountParam = new JSONObject();
			// 商户ID
			discountParam.put("tenancy_id", shopId[1]);
			// 订单号
			discountParam.put("order_code", orderCode);
			// 优惠类型
			discountParam.put("discount_type", requestDiscountArray.getJSONObject(i).optString("type"));
			// 优惠金额
			discountParam.put("discount_fee", CommonUtil.fen2Yuan(requestDiscountArray.getJSONObject(i).optInt("fee")));
			// 活动ID
			discountParam.put("activity_id", requestDiscountArray.getJSONObject(i).optString("activity_id"));
			// 百度承担金额
			discountParam.put("baidu_rate", requestDiscountArray.getJSONObject(i).optString("baidu_rate"));
			// 商户承担金额
			discountParam.put("shop_rate", requestDiscountArray.getJSONObject(i).optString("shop_rate"));
			// 代理商承担金额
			discountParam.put("agent_rate", requestDiscountArray.getJSONObject(i).optString("agent_rate"));
			// 物流承担金额
			discountParam.put("logistics_rate", requestDiscountArray.getJSONObject(i).optString("logistics_rate"));
			// 优惠描述
			discountParam.put("discount_desc", requestDiscountArray.getJSONObject(i).optString("desc"));
			
			CcPartitionUtils.lackInsertParam(shopId[1],discountParam);
			
			this.dao.insertIgnorCase(shopId[1], "cc_order_discount", discountParam);
		}
		
		//调用下发门店接口
		orderIssued(tenancyId, orderCode);
		
		JSONObject redisJson = new JSONObject();
		redisJson.put("shop_id", requestShop.optString("id"));
		taskRedisDao.save(requestOrder.optString("order_id").getBytes(), redisJson);
		
		JSONObject result = new JSONObject();
		
		Sign sign = SignHolder.getShopSign(shopId[1], shopId[0]);
		
		JSONObject body = new JSONObject();

		JSONObject data = new JSONObject();
		data.put("source_order_id", requestOrder.optString("order_id"));
		
		body.put("data", data);
		body.put("errno", 0);
		body.put("error", "success");
		
		String resultString = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.RESP_ORDER_CREATE, body,"2.0");

		result = JSONObject.fromObject(resultString);
		
		
		return result;
	}
	@Deprecated
	@Override
	public JSONObject orderStatusSelect(JSONObject jsonObject) throws Exception
	{
		
		JSONObject result = new JSONObject();
		
		String thirdOrderCode = jsonObject.optJSONObject("body").optString("order_id");
		JSONObject redisJson = taskRedisDao.read(thirdOrderCode.getBytes());
		
		taskRedisDao.save(thirdOrderCode.getBytes(), redisJson);
		
		String[] shopId = redisJson.optString("shop_id").split("@");
		String storeId=shopId[0];
		String tenantId=shopId[1];
		
		DBContextHolder.setTenancyid(tenantId);
		
		Sign sign = SignHolder.getShopSign(tenantId, storeId);
		
		String sql="select order_code,order_state from cc_order_list where third_order_code='" + thirdOrderCode + "'";
		
		sql=CcPartitionUtils.makeSQL(tenantId,sql, "", CcPartitionUtils.TYPE_ORDERCODE_NO);
		
		JSONObject order = dao.query4Json(tenantId, sql).get(0);
		
		String orderState = order.optString("order_state");
		int status = 0;
		
		switch (orderState)
		{
			case "01":
				status = 1;
				break;
			case "04":
				status = 5;
				break;
			case "06":
				status = 7;
				break;
			case "07":
				status = 8;
				break;
			case "08":
				status = 10;
				break;
			case "10":
				status = 9;
				break;
			default:
				break;
		}
		
		Map body = new TreeMap();
		body.put("errno", 0);
		body.put("error", "success");
		
		Map data = new TreeMap();
		data.put("source_order_id", order.optString("order_code"));
		data.put("status", status);
		
		body.put("data", data);
		
		String resultString = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.RESP_ORDER_STATUS_GET, body);
	
		result = JSONObject.fromObject(resultString);
		
		return result;
	}
	@Deprecated
	@Override
	public JSONObject orderStatusPush(JSONObject jsonObject) throws Exception
	{
		JSONObject result = new JSONObject();
		
		String thirdOrderCode = jsonObject.optJSONObject("body").optString("order_id");
		JSONObject redisJson = taskRedisDao.read(thirdOrderCode.getBytes());
		
		taskRedisDao.save(thirdOrderCode.getBytes(), redisJson);
		
		String[] shopId = redisJson.optString("shop_id").split("@");
		String storeId=shopId[0];
		String tenantId=shopId[1];
		
		DBContextHolder.setTenancyid(tenantId);
		
		Sign sign = SignHolder.getShopSign(tenantId, storeId);
		
		int status = jsonObject.optJSONObject("body").optInt("status");
		String orderState = null;
		switch (status)
		{
			case 1:
				orderState = "01";
				break;
			case 5:
				orderState = "04";
				break;
			case 7:
				orderState = "06";
				break;
			case 8:
				orderState = "07";
				break;
			case 10:
				orderState = "08";
				JSONObject dataObj = new JSONObject();
				dataObj.put("tenancy_id", shopId[1]);
				dataObj.put("store_id", shopId[2]);
				dataObj.put("third_order_code", thirdOrderCode);
				orderManagementService.orderCancel(shopId[1].toString(), dataObj);
				break;
			case 9:
				orderState = "10";
				JSONObject data_complete_Obj = new JSONObject();
				data_complete_Obj.put("tenancy_id", shopId[1]);
				data_complete_Obj.put("store_id", shopId[2]);
				data_complete_Obj.put("third_order_code", thirdOrderCode);
				orderManagementService.orderComplete(shopId[1].toString(), data_complete_Obj);
				break;
			default:
				break;
		}
		
		String sql = "update cc_order_list set order_state='" + orderState + "' where third_order_code='" + thirdOrderCode + "'";
		
		sql=CcPartitionUtils.makeSQL(tenantId,sql, "", CcPartitionUtils.TYPE_ORDERCODE_NO);
		
		dao.execute(tenantId, sql);
		
		Map body = new TreeMap();
		body.put("errno", 0);
		body.put("error", "success");
		
		String resultString = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.RESP_ORDER_STATUS_PUSH, body);

		result = JSONObject.fromObject(resultString);
		
		return result;
	}
	
	/**
	 * 下发订单
	 * @return
	 * @throws Exception 
	 * @throws SystemException 
	 */
	private JSONObject orderIssued(String tenancy_id,String order_code) throws Exception{
		Data data = new Data();
		data.setTenancy_id(tenancy_id);
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject param = new JSONObject();
		param.put("order_code", order_code);
		param.put("tenentid", tenancy_id);
		list.add(param);
		data.setData(list);
		JSONObject result = orderManagerService.orderIssued(data);
		return result;
	}

}