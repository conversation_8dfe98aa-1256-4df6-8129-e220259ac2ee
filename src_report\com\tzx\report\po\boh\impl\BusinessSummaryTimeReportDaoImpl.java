package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.BusinessSummaryTimeReportDao;

import org.apache.commons.lang.StringUtils;

@Repository(BusinessSummaryTimeReportDao.NAME)
public class BusinessSummaryTimeReportDaoImpl implements BusinessSummaryTimeReportDao{

	// 按照日期查询
	private final String selectDate0 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_37' and sql_type='RQL0'";
	private final String selectDate1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_37' and sql_type='RQL1'";
	private final String selectDate2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_37' and sql_type='RQL2'";
	private final String selectDate3 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_37' and sql_type='RQL3'";
	
	//按照门店查询
	private final String selectStore0 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_37' and sql_type='MDL0'";
	private final String selectStore1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_37' and sql_type='MDL1'";
	private final String selectStore2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_37' and sql_type='MDL2'";
	private final String selectStore3 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_37' and sql_type='MDL3'";
	
	private final String sqlHelp0 ="select text from saas_report_help where  report_num ='SAAS_BI_2016_37'   ";
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;

	@Override
	public JSONObject getBusinessSummaryTime(String tenancyID, JSONObject condition) throws Exception {
		Integer type = condition.optInt("selectType");
		StringBuilder sb =new StringBuilder();
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		String begindate = condition.optString("begin_date");
		String enddate = condition.optString("end_date");
		JSONObject result = new JSONObject();
		String reportSql = "";
		String reportSqlCount = "";
		long total = 0L;
		StringBuilder sbstr= new StringBuilder();
		if(begindate.length()>0 && enddate.length()>0 && condition.optString("time_interval")!=null &&!condition.optString("time_interval").equals("") )
		{
			if(!condition.containsKey("exportdataexpr")){//兼容旧版
				condition.put("exportdataexpr", "'99999999'");
			}
			//调用时段筛选
			List<JSONObject> timesList = getTimes(tenancyID,condition);
			switch (type){
			//按照日期查询
			case 1: 
				if(condition.optInt("hierarchytype") ==1){
					reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDate1);
					sbstr.setLength(0);
					if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
						list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
						structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
						
						//拼接付款方式模型
						paymentType(tenancyID,condition,structure);
					}else{
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}
					List<JSONObject> listp = new ArrayList<JSONObject>();
					if(condition.optString("store_ids").length()>0)
					{
						// 查询 支付时段的id  包括分组
						for(JSONObject json:list)
						{
							String timeStr = "";
							for(JSONObject times: timesList) {
								//判断当前时段是否与数据时段一致
								if(times.optString("time_area").equals(json.optString("interval"))) {
									//获取时段ID
									timeStr+=","+times.opt("time_id");
									sbstr.append(","+times.opt("time_id"));
								}
							}
							if(timeStr!=null&&timeStr.length()>0) {
								json.put("times_ids", timeStr.substring(1));
							}
						}
						if(sbstr.length()>0){
							sbstr.delete(0, 1);
							sb.setLength(0);
							// 根据班次id查询所有班次
							sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,times_id from hq_payment_count_times_new " +
									"where store_id in(" + condition.optString("store_ids") + ") and  day_count between '"+begindate+"' and '"+enddate+"' and  times_id  in ("+sbstr+")"
											+ "GROUP BY payment_id,times_id");
							//food_time+=","+json.opt("times_ids");
							listp = this.dao.query4Json(tenancyID,sb.toString());
							if(listp.size()>0) {
								for(JSONObject json:list){
									String times_id = json.optString("times_ids");
									for(JSONObject jop:listp)
									{
										String timesId = jop.optString("times_id");
										if(timesId.equals(times_id) || times_id.indexOf(timesId)>0){
											double currency = jop.optDouble("local_currency", 0.00);
											String pid = jop.optString("pid");
											if (json.containsKey(pid)){
												currency += json.optDouble(pid, 0.00);
											}
											json.put(pid, currency);
										}
									}
								}
							}
						}
					}
				
					reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDate0);
					footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString());
					List<JSONObject> payfoot =new ArrayList<JSONObject>();
					if(sbstr.length()>0){
						payfoot = this.dao.query4Json(tenancyID, "SELECT concat('t',payment_id) AS pid,SUM(pay_money) AS local_currency FROM hq_payment_count_times_new WHERE store_id IN(" + condition.optString("store_ids") + ")   and  day_count between '"+begindate+"' and '"+enddate+"' GROUP BY payment_id");
					}
					for (JSONObject jso : payfoot)
					{
						footerList.get(0).put(jso.optString("pid"), jso.optDouble("local_currency", 0.00));
					}
					
					
			}else if (condition.optInt("hierarchytype") ==2 && !condition.optString("time_area").equals(null)) {
					reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDate2);
					if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
						list = this.dao.query4Json(tenancyID, reportSql.toString());
						structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
						
						//拼接付款方式模型
						paymentType(tenancyID,condition,structure);
					}else{
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}
					StringBuilder sbdate = new StringBuilder();
					for (JSONObject jo : list)
					{
						String bd = jo.optString("report_date");
						if(bd.length()>0)
						{
							sbdate.append(",'"+bd+"'");
						}
					}
					List<JSONObject> listp = new ArrayList<JSONObject>();
					if(sbdate.length()>0)
					{
						sbdate.delete(0,1);
						//清除重复数据
					    String datestr =repeatingData(sbdate.toString());

						String timeStr ="";
						for(JSONObject times: timesList) {
							String [] time_area = condition.optString("time_area").split(",");
					        for(int i = 0;i<time_area.length;i++){
					        	if(("'"+times.optString("time_area")+"'").equals(time_area[i])) {
									timeStr+=","+times.opt("time_id");
								}
					        }
						}
						if(timeStr!=null&&timeStr.length()>0) {
							timeStr=timeStr.substring(1);
						}



						
						sb.setLength(0);
						sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,day_count,times_id from hq_payment_count_times_new where store_id in(" + condition.optString("store_ids") + ") and times_id in ("+timeStr+") and day_count in  (" + datestr + ")  GROUP BY payment_id,day_count,times_id");
						listp = this.dao.query4Json(tenancyID,sb.toString());


						String[] timearr = timeStr.split(",");
						String str = null;
						String time = null;
						if(timearr.length>0){
							for(int i = 0;i<timearr.length;i++){
								time = timearr[i];  //时段id
									for(JSONObject json:list){
										if(json.optString("times_id").equals(time)){
											str = json.optString("report_date");//营业日期
											for(JSONObject jop:listp){
												if(jop.optString("times_id").equals(time)){
													String b_d = jop.optString("day_count");
													if(b_d.equals(str)){
														 json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
													}
												}
											}
										}
									}
							}
						}

					}
				}else if (condition.optInt("hierarchytype") ==3 && !condition.optString("report_date").equals(null)) {
					if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
						if(condition.containsKey("report_date")){
							String str = condition.getString("report_date");
							condition.put("report_date", str.substring(1,str.length()-1));
						}
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDate3);
						System.out.println(reportSql+"++++++++++++++++++++++++++++++++++++++++++L3");
						list = this.dao.query4Json(tenancyID, reportSql.toString());
						structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
						
						//拼接付款方式模型
						paymentType(tenancyID,condition,structure);
					}else{
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDate3);
						System.out.println(reportSql+"++++++++++++++++++++++++++++++++++++++++++L3");
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}
					String timeStr ="";
					for(JSONObject times: timesList) {
						String [] time_area = condition.optString("time_area").split(",");
				        for(int i = 0;i<time_area.length;i++){
				        	if(("'"+times.optString("time_area")+"'").equals(time_area[i])) {
								timeStr+=","+times.opt("time_id");
							}
				        }
					}
					if(timeStr!=null&&timeStr.length()>0) {
						timeStr=timeStr.substring(1);
					}
					List<JSONObject> listp = new ArrayList<JSONObject>();
					if(condition.optString("store_ids").length()>0)
					{
						//清除重复数据
						String dateStr = repeatingData("'"+condition.optString("report_date")+"'");
						sb.setLength(0);
						sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,store_id,times_id,day_count as report_date from hq_payment_count_times_new where store_id in(" + condition.optString("store_ids") + ") "
								+ " and times_id in ( "+timeStr+") ");
						if(dateStr.substring(0,1).equals("'")){
							sb.append(" and day_count in (" +dateStr+")");
						}else{
							sb.append(" and day_count in ('" +dateStr+"') ");
						}
						sb.append(" GROUP BY payment_id,store_id,times_id,day_count");
						listp = this.dao.query4Json(tenancyID,sb.toString());
						/*for(JSONObject json:list)
						{
							String str  = json.optString("store_id");
							for(JSONObject jop:listp)
							{
								String b_d = jop.optString("store_id");
								if(b_d.equals(str))
								{
									json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
								}
							}
						}*/
						String[] timearr = timeStr.split(",");
						String[] date = dateStr.split(",");
						String str = null;
						String time = null;
						String dt = null;
						if(timearr.length>0){
							for(int i = 0;i<timearr.length;i++){
								time = timearr[i];  //时段id
								for(int j=0;j<date.length;j++){
									dt = date[j];
									for(JSONObject json:list){
										if(json.optString("times_id").equals(time)){
											if(json.optString("report_date").equals(dt.substring(1, 11))){
												str = json.optString("store_id");//数据机构
												for(JSONObject jop:listp){
													if(jop.optString("times_id").equals(time)){
														if(jop.optString("report_date").equals(dt.substring(1, 11))){
															String b_d = jop.optString("store_id");
															if(b_d.equals(str)){
																 json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
																}
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
			break;	
			// 按照门店查询
			case 2: 
				if(condition.optInt("hierarchytype") ==1){
					reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore1);
					sbstr.setLength(0);
					if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
						list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
						structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
						
						//拼接付款方式模型
						paymentType(tenancyID,condition,structure);
					}else{
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}
					List<JSONObject> listp = new ArrayList<JSONObject>();
					String food_time="";
					if(condition.optString("store_ids").length()>0)
					{
						for(JSONObject json:list)
						{
							String timeStr ="";
							for(JSONObject times: timesList) {
								if(times.optString("time_area").equals(json.optString("interval"))) {
									timeStr+=","+times.opt("time_id");
									sbstr.append(","+times.opt("time_id"));
								}
							}
							if(timeStr!=null&&timeStr.length()>0) {
								json.put("times_ids", timeStr.substring(1));
							}
						}
					 
					if(sbstr.length()>0){
						sbstr.delete(0, 1);
						sb.setLength(0);
						// 根据班次id查询所有班次
						sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,times_id from hq_payment_count_times_new " +
								"where store_id in(" + condition.optString("store_ids") + ") and  day_count between '"+begindate+"' and '"+enddate+"' and  times_id  in ("+sbstr+")"
										+ "GROUP BY payment_id,times_id");
						//food_time+=","+json.opt("times_ids");
						listp = this.dao.query4Json(tenancyID,sb.toString());
						if(listp.size()>0) {
							for(JSONObject json:list){
								String times_id = json.optString("times_ids");
								for(JSONObject jop:listp)
								{
									if(times_id.equals(jop.optString("times_id"))){
										json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
									}
								}
							}
						}
					}
				}
				reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore0);
				footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString());
				List<JSONObject> payfoot =new ArrayList<JSONObject>();
				if(sbstr.length()>0){
					payfoot = this.dao.query4Json(tenancyID, "SELECT concat('t',payment_id) AS pid,SUM(pay_money) AS local_currency FROM hq_payment_count_times_new WHERE store_id IN(" + condition.optString("store_ids") + ")  and  day_count between '"+begindate+"' and '"+enddate+"' GROUP BY payment_id");
				}
				for (JSONObject jso : payfoot)
				{
					footerList.get(0).put(jso.optString("pid"), jso.optDouble("local_currency", 0.00));
				}
				}else if (condition.optInt("hierarchytype") ==2 && !condition.optString("time_area").equals(null)) {
					reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore2);
					if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
						list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level1")));
						structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
						
						//拼接付款方式模型
						paymentType(tenancyID,condition,structure);
					}else{
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}
					StringBuilder sbstore_id = new StringBuilder();
					for (JSONObject jo2 : list)
					{
						String str = jo2.optString("store_id");
						if(str.length()>0)
						{
							sbstore_id.append(","+str);
						}
					}
					List<JSONObject> listp = new ArrayList<JSONObject>();
					if(sbstore_id.length()>0)
					{
						String timeStr ="";
						for(JSONObject times: timesList) {
							String [] time_area = condition.optString("time_area").split(",");
					        for(int i = 0;i<time_area.length;i++){
					        	if(("'"+times.optString("time_area")+"'").equals(time_area[i])) {
									timeStr+=","+times.opt("time_id");
								}
					        }
						}
						if(timeStr!=null&&timeStr.length()>0) {
							timeStr=timeStr.substring(1);
						}
						
						sbstore_id.delete(0,1);
						sb.setLength(0);
						sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,store_id from hq_payment_count_times_new where store_id in(" + sbstore_id + ") and times_id in ("+timeStr+") and  day_count between '"+begindate+"' and '"+enddate+"' GROUP BY payment_id,store_id");
						
						listp = this.dao.query4Json(tenancyID,sb.toString());
						
						for(JSONObject json:list)
						{
							String str  = json.optString("store_id");
							for(JSONObject jop:listp)
							{
								String b_d = jop.optString("store_id");
								if(b_d.equals(str))
								{
									json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
								}
							}
						}
					}
			}else if (condition.optInt("hierarchytype") ==3 && !condition.optString("store_ids1").equals(null)) {
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					if(condition.containsKey("store_id")&& condition.optString("store_id").length()>0){
						condition.element("store_ids1", condition.optString("store_id"));
					}
					reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore3);
					list = this.dao.query4Json(tenancyID, reportSql.toString());
					structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
					
					//拼接付款方式模型
					paymentType(tenancyID,condition,structure);
				}else{
					reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore3);
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				String timeStr ="";
				for(JSONObject times: timesList) {
					String [] time_area = condition.optString("time_area").split(",");
			        for(int i = 0;i<time_area.length;i++){
			        	if(("'"+times.optString("time_area")+"'").equals(time_area[i])) {
							timeStr+=","+times.opt("time_id");
						}
			        }
				}
				if(timeStr!=null&&timeStr.length()>0) {
					timeStr=timeStr.substring(1);
				}
				StringBuilder sbdate = new StringBuilder();
				for (JSONObject jo : list)
				{
					String bd = jo.optString("report_date");
					if(bd.length()>0)
					{
						sbdate.append(",'"+bd+"'");
					}
				}
				List<JSONObject> listp = new ArrayList<JSONObject>();
				if(sbdate.length()>0)
				{
					sbdate.delete(0,1);
					sb.setLength(0);
					sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,day_count from hq_payment_count_times_new where store_id in(" + condition.optString("store_ids1") + ") and times_id in ( "+timeStr+" ) and day_count in( " + sbdate + ")  GROUP BY payment_id,day_count");
					
					listp = this.dao.query4Json(tenancyID,sb.toString());
					
					for(JSONObject json:list)
					{
						String str  = json.optString("report_date");
						for(JSONObject jop:listp)
						{
							String b_d = jop.optString("day_count");
							if(b_d.equals(str))
							{
								json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
							}
						}
					}
				}
			}
				break;
			default:
				break;
			}
		}
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}
	
	@Override
	public List<JSONObject> getExplain(String attribute, JSONObject p) throws Exception {
		return  dao.query4Json(attribute, sqlHelp0.toString());
	}
	
	
	/**
	 * 时段筛选
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getTimes(String tenancyID,JSONObject condition) throws Exception{
		StringBuilder sb =new StringBuilder();
		sb.setLength(0);
		sb.append("select tenancy_id, time_id, case when "+condition.optString("time_interval")+"=0.5 then time_area else  " +
				"(trunc(substring((case when end_time='24:00' then '23:59' else end_time end),1,2)::int/"+condition.optString("time_interval")+")*"+condition.optString("time_interval")+" )::text||'点-'||(trunc(substring((case " +
				"when end_time='24:00' then '23:59' else end_time end),1,2)::int/"+condition.optString("time_interval")+")*"+condition.optString("time_interval")+"+"+condition.optString("time_interval")+")::character(2)||'点' " +
				"end  as time_area from sys_times where time_type='half' order by 1");
		return this.dao.query4Json(tenancyID,sb.toString());
	}
	
	//付款方式类型
		/**
		 * 
		 * @param tenancyID 商户号
		 * @param condition 参数
		 * @param structure 导出数据类型
		 * @throws Exception
		 */
		public void paymentType(String tenancyID,JSONObject condition,List<JSONObject> structure) throws Exception{
			// 获取支付方式的返回类型
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
				List<JSONObject> paymentDetailsTreeOrderByClaas = conditionUtils.getPaymentDetailsTreeOrderByClaas(tenancyID, condition);
				JSONObject jj =  null;
				for(JSONObject jop:paymentDetailsTreeOrderByClaas){
					jj =  new JSONObject();
					jj.put("fieldname",jop.get("pname"));
					jj.put("fieldtype", "double");
					structure.add(jj);
				}
			}
		}
		
		
		/**
		 * 清除重复数据
		 * @param datestr
		 * @return
		 */
		public String repeatingData(String datestr){
			// 将数据添加到List中  
		    List<String> Liststr = Arrays.asList(datestr.toString().split(","));
		    //去掉重复数据
		    List<String> listWithoutDup = new ArrayList<String>(new HashSet<String>(Liststr));
		    //转换string
		    String str = StringUtils.join(listWithoutDup.toArray(), ",");
			return str;
		} 
 
}
