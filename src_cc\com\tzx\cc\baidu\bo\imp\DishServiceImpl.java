package com.tzx.cc.baidu.bo.imp;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.util.*;

import javax.annotation.Resource;

import com.tzx.cc.thirdparty.util.BaiduUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.params.CoreProtocolPNames;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.reflect.TypeToken;
import com.tzx.cc.baidu.bo.DishService;
import com.tzx.cc.baidu.entity.Category;
import com.tzx.cc.baidu.entity.CmdType;
import com.tzx.cc.baidu.entity.DishCategoryCreate;
import com.tzx.cc.baidu.entity.DishCategoryUpdate;
import com.tzx.cc.baidu.entity.DishCreateUpdate;
import com.tzx.cc.baidu.entity.DishOnOfflineDel;
import com.tzx.cc.baidu.entity.Norms;
import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.baidu.entity.Threshold;
import com.tzx.cc.baidu.entity.TimeRange;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.cc.thirdparty.util.PropertiesUtils;
import com.tzx.cc.baidu.util.SignHolder;
import com.tzx.cc.common.constant.Data;
import com.tzx.cc.common.constant.DishOper;
import com.tzx.cc.common.constant.Oper;
import com.tzx.cc.common.constant.Type;
import com.tzx.cc.common.constant.util.CcBusinessLogUtils;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.openapi.dao.OpenApiDao;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.hq.bo.ProjectTeamManagementService;

@Service(DishService.NAME)
public class DishServiceImpl implements DishService
{
	private static final Logger	logger	= Logger.getLogger(DishServiceImpl.class);
	@Resource(name = OpenApiDao.NAME)
	private OpenApiDao			dao;

	private ObjectMapper	objMapper	= new ObjectMapper();

	@Resource(name = ProjectTeamManagementService.NAME)
	private ProjectTeamManagementService	projectTeamManagementService;

	@Override
	public JSONObject loadDishCategoryList(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		StringBuilder update_sql = new StringBuilder();
		String channel="";
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			channel="BD06";
		}
		else
		{
			channel=condition.optString("channel");
		}
		/*
		 * sql.append(
		 * "select d.id,a.class as class_id,case when f.itemclass_name is null then e.itemclass_name else concat(e.itemclass_name) end as cur_class_name,d.last_send_class_name,d.rank,d.whether_push_over from hq_item_menu_class a left join hq_item_class e on a.class = e.id left join cc_third_item_class_info d on a.class = d.item_class_id and d.shop_id='"
		 * + condition.optInt("id") +
		 * "' left join hq_item_class f ON e.father_id = f.id"); sql.append(
		 * " where  a.details_id in ( select b.id from hq_item_menu_details b inner join hq_item_menu_organ c on b.item_menu_id = c.item_menu_id inner join hq_item_menu g on b.item_menu_id = g.id inner join cc_third_organ_info h on c.store_id = h.shop_id where c.store_id = '"
		 * + condition.optInt("id") +
		 * "'  and g.valid_state = '1') and a.chanel = '" +
		 * condition.optString("channel") + "'"); sql.append(
		 * " group by a.class,cur_class_name,d.last_send_class_name,d.rank,d.whether_push_over,d.id"
		 * );
		 */
		// 菜品类别名称不同修改推送状态
		update_sql.append("UPDATE  cc_third_item_class_info SET whether_push_over = '3'  WHERE id IN (SELECT a.id FROM cc_third_item_class_info A ");
		update_sql.append(" LEFT JOIN hq_item_class b ON A .item_class_id = b. ID");
		update_sql.append(" WHERE A .whether_push_over = '1' and b.chanel='BD06' AND A .last_send_class_name != b.itemclass_name ");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			update_sql.append(" and A.shop_id in (" + condition.optString("shop_id") + " ) ");
		}
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			update_sql.append(" and a.channel = 'BD06'  ");
		}
		else
		{
			update_sql.append(" and a.channel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id")))
		{
			update_sql.append(" and b.itemclass_name =  '" + condition.optString("class_id") + "' ");
//			update_sql.append(" and a.item_class_id =  " + condition.optString("class_id") + " ");
		}
		update_sql.append(")");
		this.dao.execute(tenancyID, update_sql.toString());
		update_sql.delete(0, update_sql.length());
		// 菜品类别的显示顺序不同修改推送状态
		update_sql.append("UPDATE cc_third_item_class_info SET whether_push_over = '3'  WHERE id IN (SELECT a.id FROM cc_third_item_class_info A ");
		update_sql.append(" LEFT JOIN hq_item_menu_class b ON A .item_class_id = b.class LEFT JOIN hq_item_menu_details c on c.id=b.details_id LEFT JOIN hq_item_menu_organ d on d.item_menu_id=c.item_menu_id ");
		update_sql.append(" WHERE A .whether_push_over = '1'  AND  a.rank != b.menu_class_rank");

		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			update_sql.append(" and A.shop_id in (" + condition.optString("shop_id") + " ) ");
		}
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			update_sql.append(" and a.channel = 'BD06'  ");
		}
		else
		{
			update_sql.append(" and a.channel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id")))
		{
			update_sql.append(" and b.itemclass_name =  '" + condition.optString("class_id") + "' ");
//			update_sql.append(" and a.item_class_id =  " + condition.optString("class_id") + " ");
		}
		update_sql.append(")");
		this.dao.execute(tenancyID, update_sql.toString());
		update_sql.delete(0, update_sql.length());
		// 20160513修改
		sql.append("SELECT d.id,A.class as class_id,e.itemclass_name as cur_class_name,d.last_send_class_name,a.menu_class_rank as rank, CASE WHEN d.whether_push_over IS NULL THEN '0' ELSE d.whether_push_over END AS whether_push_over,f.id as store_id,f.org_full_name,a.chanel as channel,d.third_class_id");
		sql.append(" FROM hq_item_menu_class A");
		sql.append(" LEFT JOIN hq_item_menu_details b ON A .details_id = b. ID");
		sql.append(" LEFT JOIN hq_item_menu_organ  c on c.item_menu_id= b.item_menu_id");
		sql.append(" LEFT JOIN cc_third_item_class_info d on d.item_class_id=a.class and d.shop_id=c.store_id");
		sql.append(" LEFT JOIN hq_item_class e on e.id=a.class");
		sql.append(" LEFT JOIN organ f on f.id=c.store_id where 1=1");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			sql.append(" and c.store_id in (" + condition.optString("shop_id") + " ) ");
		}else{
			if(CommonUtil.checkStringIsNotEmpty(getAlreadyPushShopIDs(tenancyID, channel))&&!"0".equals(getAlreadyPushShopIDs(tenancyID, channel))){
				sql.append(" and c.store_id in (" + getAlreadyPushShopIDs(tenancyID, channel) + " ) ");
			}

		}
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			sql.append(" and a.chanel = 'BD06'  ");
		}
		else
		{
			sql.append(" and a.chanel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("whether_push_over") && !StringUtils.isEmpty(condition.optString("whether_push_over")))
		{
			if (!condition.optString("whether_push_over").equals("9999") && !condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and d.whether_push_over = '" + condition.optString("whether_push_over") + "'  ");
			}
			else if (condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and d.whether_push_over is null ");
			}
		}
		/*
		 * if (condition.containsKey("cur_class_name") &&
		 * !StringUtils.isEmpty(condition.optString("cur_class_name"))) {
		 * sql.append(" and e.itemclass_name like  '%" +
		 * condition.optString("cur_class_name") + "%'  "); }
		 */
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id")))
		{
//			sql.append(" and a.class =  " + condition.optString("class_id") + " ");

			sql.append(" and e.itemclass_name =  '" + condition.optString("class_id") + "' ");
		}
		// sql.append(" where c.store_id in (40,183) and a.chanel='BD06' and d.whether_push_over in('1') and e.itemclass_name='盖饭'");
		sql.append(" GROUP BY  f.id ,f.org_full_name,d.whether_push_over,d.id,A.class,e.itemclass_name,d.last_send_class_name,a.menu_class_rank,a.chanel");
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenancyID, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
		if (list.size() > 0)
		{
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		}
		else
		{
			result.put("page", "1");
			result.put("total", "0");
			result.put("rows", "[]");
		}

		logger.info("[获取百度菜品分类列表]" + result);
		return result;
	}

	@Override
	public JSONObject loadDishList(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		String channel = condition.optString("channel");
		if (channel.equals(""))
		{
			condition.put("channel", "BD06");
		}
		int storeId = condition.optInt("id");
		//加载列表数据量大时执行很慢 （为提升菜品信息加载效率注释该sql）
		StringBuilder update_sql = new StringBuilder();
//		update_sql.append("UPDATE cc_third_item_info SET whether_push_over = '3' WHERE ID IN (SELECT A . ID FROM cc_third_item_info A LEFT JOIN hq_item_info b on cast(b.id as VARCHAR)=a.item_code LEFT JOIN hq_item_class f on f.id=b.item_class LEFT JOIN hq_item_menu_details c on b.id=c.item_id LEFT JOIN hq_item_menu_class e on e.details_id=c.id LEFT join hq_item_menu_organ d on d.item_menu_id=c.item_menu_id");
//		if (condition.containsKey("shop_id"))
//		{
//			update_sql.append(" and a.shop_id in (" + condition.optString("shop_id") + ")");
//		}
//		update_sql.append(" where (b.last_updatetime>a.send_time or c.menu_item_rank!=a.rank  OR f.last_updatetime > A.send_time) and a.whether_push_over='1' and a.channel='" + condition.optString("channel") + "')");
//		this.dao.execute(tenancyID, update_sql.toString());
		update_sql.delete(0, update_sql.length());
		switch (channel)
		{
			case "MT08":
				sql.append("select   DISTINCT(A . ID),'"
						+ condition.optString("channel")
						+ "' as channel,d.store_id,d.store_id AS shop_id,a.id as item_code,b.item_name,a.item_barcode,c.item_id,g.g_price as default_price,e.min_order_num,e.dish_available_time,e.package_box_num,e.whether_push_over,e.description,e.online_flag,e.item_pic as photo1,e.available_times_start,e.available_times_end,concat (f.itemclass_name,'-',ll.itemclass_name)as last_send_class_name,a.is_combo,case when a.is_combo='Y' then '套餐' else '单品' end as attr_name,e.item_status from hq_item_menu_class b inner join hq_item_menu_details c on b.details_id = c.id inner join hq_item_menu_organ d on c.item_menu_id = d.item_menu_id inner join hq_item_menu h on c.item_menu_id = h.id inner join cc_third_organ_info i on i.shop_id = d.store_id left join hq_item_info a on a.id = c.item_id left join cc_third_item_info e on e.item_code = CAST(A.id as VARCHAR) and e.shop_id="
						+ storeId + "  and e.channel=b.chanel");
				sql.append(" LEFT JOIN hq_item_class f ON b. CLASS = f.id LEFT JOIN hq_item_class ll ON f.father_id = ll.id");
				sql.append(" left join ( select c1.unit_name as g_unit_name,a1.price as g_price,	c1.item_id as g_item_id from hq_item_pricesystem a1 left join organ b1 on cast(a1.price_system as varchar) = b1.price_system left join hq_item_unit c1 on a1.item_unit_id = c1.id where b1.id='"
						+ condition.optInt("id") + "' and a1.chanel = '" + condition.optString("channel") + "' and c1.valid_state = '1' and c1.is_default = 'Y' ) g on g.g_item_id = c.item_id ");
				sql.append("where d.store_id = '" + condition.optInt("id") + "' and b.chanel = '" + condition.optString("channel") + "' and h.valid_state = '1'");
				break;
			default:

				sql.append("select  g.item_unit_id AS unit_id,  e.remark,e. ID,'"
						+ condition.optString("channel")
						+ "' as channel,z.org_full_name,d.store_id,d.store_id AS shop_id,a.id as item_code,b.item_name,a.item_barcode,c.item_id,g.g_price as default_price,e.min_order_num,e.dish_available_time,e.package_box_num,CASE WHEN e.whether_push_over IS NULL THEN '0' ELSE e.whether_push_over END AS whether_push_over,e.description,a.photo1 as wxxt,e.online_flag,e.item_pic as photo1,e.available_times_start,e.available_times_end, f.itemclass_name AS last_send_class_name,e.rank AS RANK,a.is_combo,case when a.is_combo='Y' then '套餐' else '单品' end as attr_name,e.item_status,e.is_charge_commission,CASE WHEN C .menu_item_rank IS NULL THEN e. RANK ELSE C .menu_item_rank END AS menu_item_rank,CASE WHEN b .menu_class_rank IS NULL THEN kk.RANK ELSE b .menu_class_rank END AS menu_class_rank,A.remark AS description  from hq_item_menu_class b inner join hq_item_menu_details c on b.details_id = c.id inner join hq_item_menu_organ d on c.item_menu_id = d.item_menu_id INNER JOIN organ z ON z.id= d.store_id inner join hq_item_menu h on c.item_menu_id = h.id  left join hq_item_info a on a.id = c.item_id LEFT JOIN cc_third_item_class_info kk ON kk.item_class_id=b.class and kk.channel=b.chanel and kk.shop_id=z.id left join cc_third_item_info e on e.item_code = CAST(A.id as VARCHAR) and z.id=e.shop_id and e.channel='BD06' and e.channel=b.chanel ");
				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and e.shop_id in (" + condition.optString("shop_id") + " ) ");
				}
				sql.append(" LEFT JOIN hq_item_class f ON b. CLASS = f. ID ");
				/*if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and f.shop_id in (" + condition.optString("shop_id") + " ) ");
				}*/
				sql.append(" left join (select  a1.item_unit_id, b1.id as store_id,c1.unit_name as g_unit_name,a1.price as g_price,	c1.item_id as g_item_id from hq_item_pricesystem a1 left join organ b1 on cast(a1.price_system as varchar) = b1.price_system left join hq_item_unit c1 on a1.item_unit_id = c1.id where  c1.valid_state = '1' and c1.is_default = 'Y' ");
				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and b1.id in (" + condition.optString("shop_id") + " ) ");
				}
				if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
				{
					sql.append(" and a1.chanel = 'BD06'  ");
				}
				else
				{
					sql.append(" and a1.chanel = '" + condition.optString("channel") + "'  ");
				}
				sql.append(" ) g on g.g_item_id = c.item_id  and g.store_id=d.store_id ");

				//20170209修改 显示没有做过数据推送的店（之前版本不显示）by
				sql.append(" where  h.valid_state = '1' ");
				//sql.append("where  h.valid_state = '1' and kk.third_class_id is not null");
				//添加门店 权限   2016年8月12日13:53:54   xgy  begin
				if(condition.containsKey("authority_organ")) {
					String authority = condition.optString("authority_organ");
					if(StringUtils.isNotBlank(authority)) {
						sql.append(" and d.store_id in (").append(authority).append(")");
					}
				}
				//添加门店 权限   2016年8月12日13:53:54   xgy  end

				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and d.store_id in (" + condition.optString("shop_id") + " ) ");
				}else{
					if(CommonUtil.checkStringIsNotEmpty(getAlreadyPushShopIDs(tenancyID, channel))&&!"0".equals(getAlreadyPushShopIDs(tenancyID, channel))){
						sql.append(" and d.store_id in (" + getAlreadyPushShopIDs(tenancyID, channel) + " ) ");
					}

				}
				if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
				{
					sql.append(" and b.chanel = 'BD06'  ");
				}
				else
				{
					sql.append(" and b.chanel = '" + condition.optString("channel") + "'  ");
				}
				if (condition.containsKey("whether_push_over") && !StringUtils.isEmpty(condition.optString("whether_push_over")))
				{
					if (!condition.optString("whether_push_over").equals("9999") && !condition.optString("whether_push_over").equals("0"))
					{
						sql.append(" and e.whether_push_over = '" + condition.optString("whether_push_over") + "'  ");
					}
					else if (condition.optString("whether_push_over").equals("0"))
					{
						sql.append(" and (e.whether_push_over is null or e.whether_push_over='0' )");
					}
				}
				if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id")))
				{
//					sql.append(" and b.class=" + condition.optString("class_id") + " ");
					sql.append(" and f.itemclass_name in('" + condition.optString("class_id") + "') ");
				}
				if (condition.containsKey("item_name") && !StringUtils.isEmpty(condition.optString("item_name")))
				{
					sql.append(" and b.item_name like '%" + condition.optString("item_name") + "%' ");
				}
				if (condition.containsKey("item_code") && !StringUtils.isEmpty(condition.optString("item_code")))
				{
					sql.append(" and a.id ='" + condition.optString("item_code") + "' ");
				}
				if (condition.containsKey("item_status") && !StringUtils.isEmpty(condition.optString("item_status")))
				{
					if (!condition.optString("item_status").equals("99999"))
					{
						if(condition.optString("item_status").equals("8")){
							sql.append(" and e.item_status = '1'  ");
						}else if(condition.optString("item_status").equals("9")){
							sql.append(" and e.item_status = '0'  ");
						}

					}

				}
//				sql.append("  order by b.menu_class_rank ,CAST (C .menu_item_rank AS INT) ASC");
		}

		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenancyID, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
		if (list.size() > 0)
		{
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		}
		else
		{
			result.put("page", "1");
			result.put("total", "0");
			result.put("rows", "[]");
		}

		logger.info("[获取百度菜品列表]" + result);
		return result;
	}

	@Override
	public JSONObject getPrice(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();

		if (condition.containsKey("store_id") && condition.optInt("store_id") > 0)
		{
			sql.append("select c.id,c.unit_name,a.price from hq_item_pricesystem a left join organ b on cast(a.price_system as varchar) = b.price_system left JOIN hq_item_unit c on a.item_unit_id = c.id ");
			sql.append("where c.is_default='Y' and c.item_id='" + condition.optInt("item_id") + "' and b.id = '" + condition.optString("store_id") + "' and a.chanel = '" + Constant.BAIDU_CHANNEL + "' and c.valid_state = '1'");

			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));

			String query_meal_sql= "SELECT B.item_id,B.price from cc_meals_info a LEFT JOIN cc_meals_info_default  b on b.meals_id=a.id where a.store_id=" + condition.optInt("store_id") + " and a.channel='" + Constant.BAIDU_CHANNEL + "' AND A.meals_type='MR03'";
			List<JSONObject> query_meal_list = this.dao.query4Json(tenancyID,  query_meal_sql.toString());
			if(query_meal_list.size()>0){
				result.put("box_price", query_meal_list.get(0).optDouble("price",0.0));
			}
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		}
		else
		{
			result.put("page", "1");
			result.put("total", "0");
			result.put("rows", "[]");
		}

		logger.info("[获取百度菜品价格体系]" + result);
		return result;
	}

	@Override
	public JSONObject dishDategoryCreate(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();

		String sql = null;

		if (condition != null)
		{
			if (condition.optString("send").equals("no"))
			{
				if (condition.optString("whether_push_over").equals("1"))
				{
					if(condition.optString("channel").equals("WM10")){
						condition.put("whether_push_over", "1");
					}else{
						condition.put("whether_push_over", "3");
					}

				}
				if(condition.optString("whether_push_over").equals("0")&&condition.optString("channel").equals("WM10")){
					condition.put("whether_push_over", "1");
				}
				if (condition.containsKey("id") && CommonUtil.checkStringIsNotEmpty(condition.optString("id")))
				{
					sql = "update cc_third_item_class_info set last_send_class_name='" + condition.optString("cur_class_name") + "',rank='" + condition.optInt("rank") + "',whether_push_over='" + condition.optString("whether_push_over") + "',send_operator='" + condition.optString("update_operator")
							+ "',update_time='" + condition.optString("update_time").trim() + "' where id=" + condition.optString("id") + "";
				}
				else
				{
					sql = "insert into cc_third_item_class_info(shop_id,item_class_id,last_send_class_name,rank,whether_push_over,update_operator,update_time,channel,tenancy_id) values('" + condition.optString("store_id") + "','" + condition.optString("class_id") + "','"
							+ condition.optString("cur_class_name") + "','" + condition.optInt("rank") + "','" + condition.optString("whether_push_over") + "','" + condition.optString("update_operator") + "','" + condition.optString("update_time").trim() + "','" + condition.optString("channel") + "','"
							+ tenancyID + "')";

				}
				dao.execute(tenancyID, sql);
				result.put("success", true);
			}
			else
			{
				CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
				ccBusniessLogBean.setRequestId(condition.optString("requestId"));
				ccBusniessLogBean.setTenancyId(tenancyID);
				ccBusniessLogBean.setCategory("cc");
				ccBusniessLogBean.setType("dishCategory");
				ccBusniessLogBean.setChannel(condition.optString("channel"));
				ccBusniessLogBean.setChannelName(condition.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
				ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.DishServiceImpl:dishDategoryCreate");
				ccBusniessLogBean.setRequestBody(condition.toString());

				ccBusniessLogBean.setCreateTime(new Date().getTime());
				ccBusniessLogBean.setIsNormal("1");
				ccBusniessLogBean.setIsThird("0");

				//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
				if(condition.containsKey("dishes_class")){
					ccBusniessLogBean.setThirdId(condition.optJSONArray("dishes_class").getJSONObject(0).optString("third_class_id"));
					ccBusniessLogBean.setTzxId(condition.optJSONArray("dishes_class").getJSONObject(0).optString("item_class_id"));
					ccBusniessLogBean.setTzxName(condition.optJSONArray("dishes_class").getJSONObject(0).optString("cur_class_name"));
					ccBusniessLogBean.setShopId(condition.optJSONArray("dishes_class").getJSONObject(0).optString("store_id"));
				}else{
					ccBusniessLogBean.setThirdId(condition.optString("third_class_id"));
					ccBusniessLogBean.setTzxId(condition.optString("item_class_id"));
					ccBusniessLogBean.setTzxName(condition.optString("cur_class_name"));
					ccBusniessLogBean.setShopId(condition.optString("store_id"));
				}

				ccBusniessLogBean.setOperAction(DishOper.pushDishCategory.toString());

				if (condition.optString("channel").equalsIgnoreCase("BD06"))
				{
					DishCategoryCreate dishCategoryCreate = new DishCategoryCreate();
					dishCategoryCreate.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
					dishCategoryCreate.setName(condition.optString("cur_class_name"));
					dishCategoryCreate.setRank(condition.optInt("rank") == 0 ? 1 : condition.optInt("rank"));

					Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));

					//changhui 2018-1-23 注释
					/*String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CATEGORY_CREATE, dishCategoryCreate);

					String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);*/

					//changhui 2018-1-23 添加 start
					Map<String, Object> getMap = getDishCategoryCreateMap(dishCategoryCreate);
					String resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.category.create", getMap, BaiduUtils.VERSION_3);
					//end

					JSONObject resultData = JSONObject.fromObject(resultString);

					int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");

					//2017-03-10如果菜品分类已存在直接更新start
					if(resultCode == 10213){
						DishCategoryUpdate dishCategoryUpdate=new DishCategoryUpdate();
						BeanUtils.copyProperties(dishCategoryUpdate, dishCategoryCreate);
						dishCategoryUpdate.setOld_name(dishCategoryCreate.getName());
						condition.put("cur_class_name", dishCategoryCreate.getName());
						/*requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CATEGORY_UPDATE, dishCategoryUpdate);
						resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);*/

						//changhui 2018-1-23 添加 start
						Map<String, Object> getUpdateMap = getDishCategoryUpdateMap(dishCategoryUpdate);
						resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.category.update", getUpdateMap, BaiduUtils.VERSION_3);
						//end
						resultData = JSONObject.fromObject(resultString);
						resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
					}
					//2017-03-10如果菜品分类已存在直接更新end

					// 20170224修改推送菜品时后台报错的问题begin by
					// 拿到第三方平台返回的data有可能为空，所以需要判断
					String data = resultData.optJSONObject("body").optString("data");
					JSONArray resultBodyDataArray = null;
					String thirdClassId = "";
					if (!"".equals(data)) {
						resultBodyDataArray = JSONArray.fromObject(data);
						thirdClassId = String.valueOf(resultBodyDataArray.getJSONObject(0).optInt("id"));
					}
					// 20170224修改推送菜品时后台报错的问题end
					//JSONArray resultBodyDataArray = JSONArray.fromObject(resultData.optJSONObject("body").optString("data"));
					//String thirdClassId=String.valueOf(resultBodyDataArray.getJSONObject(0).optInt("id"));
					//changhui 2018-1-23 注释并添加 start
					//logger.info("[创建百度菜品分类]上传数据:" + requestStr + "\n返回数据:" + resultString);
					logger.info("[创建百度菜品分类]上传数据:" + getMap.toString() + "\n返回数据:" + resultString);
					//end
					if (resultCode == 0)
					{
						if (condition.containsKey("id") && CommonUtil.checkStringIsNotEmpty(condition.optString("id")))
						{
							//20170224修改推送菜品成功后，之前推送失败的错误信息没清空的问题begin by
							sql = "update cc_third_item_class_info set last_send_class_name='" + condition.optString("cur_class_name") + "',rank='" + condition.optInt("rank") + "',whether_push_over='1',send_operator='" + condition.optString("send_operator") + "',send_time='"
									+ condition.optString("send_time").trim() + "', remark='' " + " where id=" + condition.optString("id") + "";
							//20170224修改推送菜品成功后，之前推送失败的错误信息没清空的问题end
						}
						else
						{
							sql = "insert into cc_third_item_class_info(shop_id,item_class_id,last_send_class_name,rank,whether_push_over,send_operator,send_time,channel,tenancy_id,third_class_id) values('" + condition.optString("store_id") + "','" + condition.optString("class_id") + "','"
									+ condition.optString("cur_class_name") + "','" + condition.optInt("rank") + "','1','" + condition.optString("send_operator") + "','" + condition.optString("send_time").trim() + "','" + Constant.BAIDU_CHANNEL + "','" + tenancyID + "','"+thirdClassId+"')";

						}
						dao.execute(tenancyID, sql);
						//2017-03-15 START(rif同步过来的数据保存菜品明细的顺序值到第三方菜品信息表)
						updateSaasDishCategoryRank(tenancyID, condition);
						//2017-03-15 END
						result.put("success", true);
					}else{
						// 20170224将推送菜品类别失败后返回错误信息修改显示为中文begin by
						// 解析平台返回的错误代码对应的错误信息
						String errMsg = PropertiesUtils.getErrMsgByErrno(condition.optString("channel"),
								resultData.optJSONObject("body").optString("errno"),
								resultData.optJSONObject("body").optString("error"));

						result.put("success", false);
						if (condition.containsKey("id") && CommonUtil.checkStringIsNotEmpty(condition.optString("id")))
						{
							sql = "update cc_third_item_class_info set rank='" + condition.optInt("rank") + "',whether_push_over='2',send_operator='" + condition.optString("send_operator") + "',send_time='" + condition.optString("send_time").trim() + "',remark='" + errMsg + "' where id=" + condition.optString("id") + "";
						}
						else
						{
							sql = "insert into cc_third_item_class_info(shop_id,item_class_id,rank,whether_push_over,send_operator,send_time,channel,tenancy_id,remark) values('" + condition.optString("store_id") + "','" + condition.optString("class_id") + "','" + condition.optInt("rank")
									+ "','2','" + condition.optString("send_operator") + "','" + condition.optString("send_time").trim() + "','" + Constant.BAIDU_CHANNEL + "','" + tenancyID + "','" + errMsg + "')";
						}
						dao.execute(tenancyID, sql);

						result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
						result.put("errno", JSONObject.fromObject(resultData.opt("body")).optString("errno"));
					}
					ccBusniessLogBean.setResponseBody(resultData.toString());
					KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
					// 20170224将推送菜品类别失败后返回错误信息修改显示为中文end
				}
				else if (condition.optString("channel").equalsIgnoreCase("DP07"))
				{
					if (CommonUtil.checkStringIsNotEmpty(condition.optString("id")))
					{

						Object obj_update = this.dao.updateIgnorCase(tenancyID, "cc_third_item_class_info", condition);
						if (obj_update != null)
						{
							result.put("success", true);
						}
					}
					else
					{
						Object obj_add = this.dao.insertIgnorCase(tenancyID, "cc_third_item_class_info", condition);
						if (obj_add != null)
						{
							result.put("success", true);
						}
						else
						{
							result.put("success", false);
							result.put("msg", "保存失败");
						}

					}

				}
				else if (condition.optString("channel").equalsIgnoreCase("WM10"))
				{
					sql = "insert into cc_third_item_class_info(shop_id,item_class_id,last_send_class_name,rank,whether_push_over,send_operator,send_time,channel,tenancy_id) values('" + condition.optString("store_id") + "','" + condition.optString("class_id") + "','"
							+ condition.optString("cur_class_name") + "','" + condition.optInt("rank") + "','1','" + condition.optString("send_operator") + "','" + condition.optString("send_time").trim() + "','" + Constant.WECHAT_CHANNEL + "','" + tenancyID + "')";
					dao.execute(tenancyID, sql);
					result.put("success", true);
				}
				else if (condition.optString("channel").equalsIgnoreCase("MT08"))
				{
					if (CommonUtil.checkStringIsNotEmpty(condition.optString("id")))
					{

						Object obj_update = this.dao.updateIgnorCase(tenancyID, "cc_third_item_class_info", condition);
						if (obj_update != null)
						{
							result.put("success", true);
						}
					}
					else
					{
						Object obj_add = this.dao.insertIgnorCase(tenancyID, "cc_third_item_class_info", condition);
						if (obj_add != null)
						{
							result.put("success", true);
						}
						else
						{
							result.put("success", false);
							result.put("msg", "保存失败");
						}

					}

				}
			}

		}
		return result;
	}

	private void updateSaasDishCategoryRank(String tenancyID,JSONObject condition) throws Exception {
		String sql="SELECT a.id,b.menu_class_rank FROM cc_third_item_class_info A LEFT JOIN hq_item_menu_class b ON A .item_class_id = b.class LEFT JOIN hq_item_menu_details c on c.id=b.details_id LEFT JOIN hq_item_menu_organ d on d.item_menu_id=c.item_menu_id LEFT join hq_item_class e on e.id=a.item_class_id WHERE A .whether_push_over = '1'  AND  a.rank != b.menu_class_rank and a.channel='"+Constant.BAIDU_CHANNEL+"' and a.shop_id='"+condition.optString(" store_id ")+"' and d.store_id='"+condition.optString("store_id")+"'";
		List<JSONObject> item_class_list=dao.query4Json(tenancyID, sql);
		if(item_class_list.size()>0){
			for(JSONObject itemclass:item_class_list){
				JSONObject rifDishCategory=new JSONObject();
				rifDishCategory.put("id", itemclass.optString("id"));
				rifDishCategory.put("rank", itemclass.optString("menu_class_rank"));
				this.dao.updateIgnorCase(tenancyID, "cc_third_item_class_info", rifDishCategory);
			}
		}
	}

	@Override
	public JSONObject dishCategoryUpdate(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();

		String sql = null;

		if (condition != null)
		{

			if (condition.optString("send").equals("no"))
			{
				if (condition.optString("whether_push_over").equals("1"))
				{
					if(condition.optString("channel").equals("WM10")){
						condition.put("whether_push_over", "1");
					}else{
						condition.put("whether_push_over", "3");
					}
				}
				if (condition.containsKey("id") && CommonUtil.checkStringIsNotEmpty(condition.optString("id")))
				{
					sql = "update cc_third_item_class_info set last_send_class_name='" + condition.optString("cur_class_name") + "',rank='" + condition.optInt("rank") + "',whether_push_over='" + condition.optString("whether_push_over") + "',update_operator='"
							+ condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "', remark='' where id=" + condition.optString("id") + "";
				}
				else
				{
					sql = "insert into cc_third_item_class_info(shop_id,item_class_id,last_send_class_name,rank,whether_push_over,update_operator,update_time,channel,tenancy_id) values('" + condition.optString("store_id") + "','" + condition.optString("class_id") + "','"
							+ condition.optString("cur_class_name") + "','" + condition.optInt("rank") + "','" + condition.optString("whether_push_over") + "','" + condition.optString("update_operator") + "','" + condition.optString("update_time").trim() + "','" + condition.optString("channel")+ "','"
							+ tenancyID + "')";

				}
				dao.execute(tenancyID, sql);
				result.put("success", true);
			}
			else
			{
				CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
				ccBusniessLogBean.setRequestId(condition.optString("requestId"));
				ccBusniessLogBean.setTenancyId(tenancyID);
				ccBusniessLogBean.setCategory("cc");
				ccBusniessLogBean.setType("dishCategory");
				ccBusniessLogBean.setChannel(condition.optString("channel"));
				ccBusniessLogBean.setChannelName(condition.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
				ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.DishServiceImpl:dishCategoryUpdate");

				ccBusniessLogBean.setCreateTime(new Date().getTime());
				ccBusniessLogBean.setIsNormal("1");
				ccBusniessLogBean.setIsThird("0");

				//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
				if(condition.containsKey("dishes_class")){
					ccBusniessLogBean.setThirdId(condition.optJSONArray("dishes_class").getJSONObject(0).optString("third_class_id"));
					ccBusniessLogBean.setTzxId(condition.optJSONArray("dishes_class").getJSONObject(0).optString("class_id"));
					ccBusniessLogBean.setTzxName(condition.optJSONArray("dishes_class").getJSONObject(0).optString("cur_class_name"));
					ccBusniessLogBean.setShopId(condition.optJSONArray("dishes_class").getJSONObject(0).optString("store_id"));
				}else{
					ccBusniessLogBean.setThirdId(condition.optString("third_class_id"));
					ccBusniessLogBean.setTzxId(condition.optString("class_id"));
					ccBusniessLogBean.setTzxName(condition.optString("cur_class_name"));
					ccBusniessLogBean.setShopId(condition.optString("store_id"));
				}

				ccBusniessLogBean.setOperAction(DishOper.pushDishCategory.toString());

				if (condition.optString("channel").equalsIgnoreCase("BD06"))
				{
					DishCategoryUpdate dishCategoryUpdate = new DishCategoryUpdate();
					dishCategoryUpdate.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
					dishCategoryUpdate.setOld_name(condition.optString("last_send_class_name"));
					dishCategoryUpdate.setName(condition.optString("cur_class_name"));
					dishCategoryUpdate.setRank(condition.optInt("rank") == 0 ? 1 : condition.optInt("rank"));

					Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));

					//changhui 2018-1-23 注释
					/*String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CATEGORY_UPDATE, dishCategoryUpdate);

					String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);*/

					//changhui 2018-1-23 添加 start
					Map<String, Object> getMap = getDishCategoryUpdateMap(dishCategoryUpdate);
					String resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.category.update", getMap, BaiduUtils.VERSION_3);
					//end

					JSONObject resultData = JSONObject.fromObject(resultString);

					int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");

					//2017-03-10修改菜品分类如果不存在新增start
					if(resultCode == 10214){
						DishCategoryCreate dishCategoryCreate=new DishCategoryCreate();
						BeanUtils.copyProperties(dishCategoryCreate, dishCategoryUpdate);
						//changhui 2018-1-23 注释
						 /*requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CATEGORY_CREATE, dishCategoryCreate);
						 resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);*/

						//changhui 2018-1-23 添加 start
						Map<String, Object> getCreateMap = getDishCategoryCreateMap(dishCategoryCreate);
						resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.category.create", getCreateMap, BaiduUtils.VERSION_3);
						//end

						resultData = JSONObject.fromObject(resultString);
						resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
					}
					//2017-03-10修改菜品分类如果不存在新增end

					//changhui 2018-1-23 注释并添加 start
					//logger.info("[更新百度商户菜品分类]上传数据:" + requestStr + "\n返回数据:" + resultString);
					logger.info("[更新百度商户菜品分类]上传数据:" + getMap.toString() + "\n返回数据:" + resultString);
					//ccBusniessLogBean.setRequestBody(requestStr.toString());
					ccBusniessLogBean.setRequestBody(getMap.toString());
					//end

					ccBusniessLogBean.setResponseBody(resultString.toString());
					KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
					if (resultCode == 0)
					{
						sql = "update cc_third_item_class_info set whether_push_over='1',last_send_class_name='" + condition.optString("cur_class_name") + "',rank='" + condition.optString("rank") + "',update_operator='" + condition.optString("update_operator") + "',update_time='"
								+ condition.optString("update_time").trim() + "', remark='' where shop_id='" + condition.optString("store_id") + "' and item_class_id='" + condition.optString("class_id") + "'";
						dao.execute(tenancyID, sql);
						result.put("success", true);
					}
					else
					{

						// 解析错误代码所对应的中文错误信息
						String errMsg = PropertiesUtils.getErrMsgByErrno(
								condition.optString("channel"),
								JSONObject.fromObject(resultData.opt("body")).optString("errno"),
								JSONObject.fromObject(resultData.opt("body")).optString("error"));

						sql = "update cc_third_item_class_info set whether_push_over='2',remark='" + errMsg + "',last_send_class_name='" + condition.optString("cur_class_name") + "',rank='" + condition.optString("rank")
								+ "',update_operator='" + condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_class_id='" + condition.optString("class_id") + "'";
						dao.execute(tenancyID, sql);
						result.put("success", false);
						result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
						result.put("errno", JSONObject.fromObject(resultData.opt("body")).optString("errno"));
					}
				}else if (condition.optString("channel").equalsIgnoreCase("WM10")){
					sql = "update cc_third_item_class_info set last_send_class_name='" + condition.optString("cur_class_name") + "',rank='" + condition.optString("rank") + "',update_operator='" + condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim()
							+ "', remark='' where shop_id='" + condition.optString("store_id") + "' and item_class_id='" + condition.optString("class_id") + "'";
					dao.execute(tenancyID, sql);
					result.put("success", true);
				}
			}
		}

		return result;
	}

	@Override
	public JSONObject dishCreate(String tenancyID, JSONObject condition) throws Exception
	{

		JSONObject result = new JSONObject();

		String sql = null;

		if (condition != null)
		{
			if (condition.optString("channel").equalsIgnoreCase("BD06"))
			{
				DishCreateUpdate dishCreate = new DishCreateUpdate();
				// 合作方商户唯一 ID
				dishCreate.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
				// 菜品唯一编号
				dishCreate.setDish_id(String.valueOf(condition.optInt("item_code")));
				// 菜品名称
				dishCreate.setName(condition.optString("item_name"));
				// 条形码编号
				dishCreate.setUpc(condition.optString("item_barcode"));
				// 菜品价格
				dishCreate.setPrice(CommonUtil.yuan2Fen(condition.optDouble("default_price",0.0)));
				// 菜品图片
				dishCreate.setPic(condition.optString("photo1"));
				if(StringUtils.isEmpty(condition.optString("is_charge_commission"))){
					condition.put("is_charge_commission","1");
				}
				// 最小起订份数

//				dishCreate.setMin_order_num((int) (condition.optInt("min_order_num") == 0 ? 1 : condition.optDouble("min_order_num")));
//				// 单份所需餐盒数
//				dishCreate.setPackage_box_num((int) (condition.optInt("package_box_num") == 0 ? 1 : condition.optDouble("package_box_num")));
				dishCreate.setMin_order_num(condition.optInt("min_order_num"));
				// 单份所需餐盒数
				dishCreate.setPackage_box_num(condition.optInt("package_box_num"));
				// 描述
				dishCreate.setDescription(condition.optString("description"));

				// 可售时间
//				if (condition.optString("available_times_start") != "" || condition.optString("available_times_end") != "")
//				{
//					Map<String, List<JSONObject>> timezone = new HashMap<String, List<JSONObject>>();
//					List<JSONObject> timeList = new ArrayList<JSONObject>();
//					TimeRange timeRange = new TimeRange();
//					timeRange.setStart(condition.optString("available_times_start").equals("") || condition.optString("available_times_start").equals("null") || condition.optString("available_times_start") == null ? "10:00" : condition.optString("available_times_start"));
//					timeRange.setEnd(condition.optString("available_times_end").equals("") || condition.optString("available_times_end").equals("null") || condition.optString("available_times_end") == null ? "22:00" : condition.optString("available_times_end"));
//					timeList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(JSONObject.fromObject(timeRange))));
//
//					timezone.put("*", timeList);
//
//					dishCreate.setAvailable_times(timezone);
//				}

				//2017-12-18 分时段售卖菜品
				if(StringUtils.isNotEmpty(condition.optString("dish_available_time"))) {
					Map<String, List<JSONObject>> timezone = new HashMap<String, List<JSONObject>>();
					String dish_available_time = condition.optString("dish_available_time");
					JSONObject obj = JSONObject.fromObject(dish_available_time);
					Iterator<String> iterator = obj.keys();
					int weekCount = 0;
					while(iterator.hasNext()){

						String weekDay = iterator.next();
						weekCount++;
						String times = obj.optString(weekDay,"");
						List<JSONObject> timeList = new ArrayList<JSONObject>();
						if(StringUtils.isNotEmpty(times)){
							String[] range = times.split(",");
							for(String temp :range){
								JSONObject rangeObj = JSONObject.fromObject("{}");
								String start = temp.substring(0,temp.indexOf("-"));
								String end = temp.substring(temp.indexOf("-")+1);
								rangeObj.put("start",start);
								rangeObj.put("end",end);
								timeList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(rangeObj)));
							}
							timezone.put("*",timeList);
							break;
						}
					}
					dishCreate.setAvailable_times(timezone);
				}else{
					Map<String, List<JSONObject>> timezone = new HashMap<String, List<JSONObject>>();
					List<JSONObject> timeList = new ArrayList<JSONObject>();
					JSONObject rangeObj = JSONObject.fromObject("{}");
					rangeObj.put("start","00:00");
					rangeObj.put("end","23:59");
					timeList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(rangeObj)));
					timezone.put("*",timeList);
					dishCreate.setAvailable_times(timezone);
				}

				// 分类信息
				List<JSONObject> categoryList = new ArrayList<JSONObject>();
				JSONObject category = new JSONObject();
				category.put("name",condition.optString("last_send_class_name"));
				category.put("rank",condition.optInt("rank"));
				categoryList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(JSONObject.fromObject(category))));

				dishCreate.setCategory(categoryList);

				// 菜品规格
				List<JSONObject> normsList = new ArrayList<JSONObject>();

				Norms norms = null;

				if (condition.containsKey("priceList") && condition.get("priceList") != "")
				{

					@SuppressWarnings("unchecked")
					List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(condition.get("priceList").toString(), new TypeToken<List<JSONObject>>()
					{
					}.getType());

					for (int i = 0; i < list.size(); i++)
					{
						norms = new Norms();
						norms.setName(list.get(i).optString("unit_name"));
						// norms.setValue(String.valueOf(list.get(i).optInt("id")));//百度外卖不支持规格ID
						norms.setValue(list.get(i).optString("unit_name"));
						norms.setPrice(CommonUtil.yuan2Fen(list.get(i).optDouble("price")));
						normsList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(JSONObject.fromObject(norms))));
					}
				}

				dishCreate.setNorms(normsList);

				// 菜品属性
				// List<Attr> attrList = new ArrayList<Attr>();
				// Attr attr = new Attr();
				// attr.setName(condition.optString("attr_name"));
				// attr.setValue("Y".equals(condition.optString("is_combo"))?"是":"否");
				// attrList.add(attr);
				//
				// dishCreate.setAttr(attrList);

				if ("no".equals(condition.optString("send")))
				{

					sql = "insert into cc_third_item_info(shop_id,min_order_num,package_box_num,available_times_start,available_times_end,rank,whether_push_over,description,item_pic,item_code,send_operator,send_time,channel,tenancy_id,is_charge_commission) values('"
							+ condition.optString("store_id") + "','" + condition.optInt("min_order_num") + "'," + condition.optInt("package_box_num") + ",'" + condition.optString("available_times_start") + "','" + condition.optString("available_times_end") + "','" + condition.optString("rank")
							+ "','0','" + condition.optString("description") + "','" + condition.optString("photo1") + "','" + condition.optString("item_code") + "','" + condition.optString("send_operator") + "','" + condition.optString("send_time").trim() + "','" + Constant.BAIDU_CHANNEL
							+ "','" + tenancyID + "','" + condition.optString("is_charge_commission") + "')";
					dao.execute(tenancyID, sql);
					result.put("success", true);
					return result;
				}

				//是否上传图片，1代表是,0或其它值代表不上传图片。at 2017-05-08
				if(!"1".equals(condition.optString("isUploadImg"))){
					//changhui 2018-1-29 注销
					//dishCreate.setPic("-1");
					//changhui 2018-1-29 增加 3.0版本
					dishCreate.setPic("1");
				}

				//changhui 2018-1-23 注释
				//String requestStr = "";
				Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));
				//changhui 2018-1-23 注释
				/*if ("1".equals(condition.optString("whether_push_over")) ||"3".equals(condition.optString("whether_push_over")) || ("1".equals(condition.optString("online_flag")) && "2".equals(condition.optString("whether_push_over"))))
				{
					requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_UPDATE, dishCreate);
				}
				else
				{
					requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CREATE, dishCreate);

				}*/

				CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
				ccBusniessLogBean.setRequestId(condition.optString("requestId"));
				ccBusniessLogBean.setTenancyId(tenancyID);
				ccBusniessLogBean.setShopId(String.valueOf(condition.optLong("store_id")));
				ccBusniessLogBean.setCategory("cc");
				ccBusniessLogBean.setType("dish");
				ccBusniessLogBean.setChannel(condition.optString("channel"));
				ccBusniessLogBean.setChannelName(condition.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
				ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.DishServiceImpl:dishCreate");
				ccBusniessLogBean.setRequestBody(condition.toString());


				ccBusniessLogBean.setCreateTime(new Date().getTime());
				ccBusniessLogBean.setIsNormal("1");
				ccBusniessLogBean.setIsThird("0");

				//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
				if(condition.containsKey("dishes")){
					ccBusniessLogBean.setThirdId(condition.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
					ccBusniessLogBean.setTzxId(String.valueOf(condition.optJSONArray("dishes").getJSONObject(0).optLong("item_id")));
					ccBusniessLogBean.setTzxName(condition.optJSONArray("dishes").getJSONObject(0).optString("item_name"));
				}else{
					ccBusniessLogBean.setThirdId(condition.optString("third_item_id"));
					ccBusniessLogBean.setTzxId(String.valueOf(condition.optLong("item_id")));
					ccBusniessLogBean.setTzxName(condition.optString("item_name"));
				}

				// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
				ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

				try
				{
					//changhui 2018-1-23 添加 start
					Map<String,Object> getMap = getDishCreateUpdateMap(dishCreate);
					ccBusniessLogBean.setRequestBody(getMap.toString());
					String resultString = "";
					if("1".equals(condition.optString("whether_push_over")) ||"3".equals(condition.optString("whether_push_over")) || ("1".equals(condition.optString("online_flag")) && "2".equals(condition.optString("whether_push_over")))){
						resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.update", getMap, BaiduUtils.VERSION_3);
					}else{
						resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.create", getMap, BaiduUtils.VERSION_3);
					}
					//end

					//changhui 2018-1-23 注释
					//String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);

					JSONObject resultData = JSONObject.fromObject(resultString);

					int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");

					//2017-03-10菜品新增结果可处理问题验证start
					if(resultCode == 10203){//2017-03-10菜品重复调修改接口
						//changhui 2018-1-23 注释
						/*requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_UPDATE, dishCreate);
						resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);*/

						//changhui 2018-1-23 添加 start
						resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.update", getMap, BaiduUtils.VERSION_3);
						//end

						resultData = JSONObject.fromObject(resultString);
						resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
					}
					if(resultCode == 10202){//平台特殊错误，菜品信息不存在
						//changhui 2018-1-23 注释
						/*requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CREATE, dishCreate);
						resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);*/

						//changhui 2018-1-23 添加 start
						resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.create", getMap, BaiduUtils.VERSION_3);
						//end

						resultData = JSONObject.fromObject(resultString);
						resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
					}

					//2017-03-10菜品新增结果可处理问题验证start

					//changhui 2018-1-23 注释并添加 start
					//logger.info("[创建百度菜品]上传数据:" + requestStr + "\n返回数据:" + resultString);
					logger.info("[创建百度菜品]上传数据:" + getMap.toString() + "\n返回数据:" + resultString);
					//end

					sql = "select id from cc_third_item_info where shop_id='" + condition.optString("store_id") + "' and item_code='" + condition.optString("item_code") + "' and channel='" + condition.optString("channel") + "'";
					List<JSONObject> json = dao.query4Json(tenancyID, sql);
					String id = null;
					for (JSONObject o : json)
					{
						id = o.optString("id");
					}

					if (resultCode == 0)
					{
						if (null == id)
						{
							sql = "insert into cc_third_item_info(shop_id,min_order_num,package_box_num,available_times_start,available_times_end,rank,whether_push_over,description,item_pic,item_status,item_code,send_operator,send_time,channel,tenancy_id,is_charge_commission,remark) values('"
									+ condition.optString("store_id") + "','" + condition.optInt("min_order_num") + "'," + condition.optInt("package_box_num") + ",'" + condition.optString("available_times_start") + "','" + condition.optString("available_times_end") + "','" + condition.optString("rank")
									+ "','1','" + condition.optString("description") + "','" + condition.optString("photo1") + "','1','" + condition.optString("item_code") + "','" + condition.optString("send_operator") + "','" + condition.optString("send_time").trim() + "','" + Constant.BAIDU_CHANNEL
									+ "','" + tenancyID + "','" + condition.optString("is_charge_commission") + "','')";//+JSONObject.fromObject(resultData.opt("body")).optString("error")+"')";

							dao.execute(tenancyID, sql);
						}
						else
						{
							sql = "update  cc_third_item_info set  whether_push_over='1',remark='' where  id='" + id + "'";
							//sql = "update  cc_third_item_info set  whether_push_over='1',remark='"+JSONObject.fromObject(resultData.opt("body")).optString("error")+"' where  id='" + id + "'";
							dao.execute(tenancyID, sql);
						}

						result.put("success", true);
					}
					else
					{
						//20170224将推送菜品失败后返回错误信息修改显示为中文 begin  by
						// 解析平台返回的错误代码对应的错误信息
						String errMsg = PropertiesUtils.getErrMsgByErrno(condition.optString("channel"),
								JSONObject.fromObject(resultData.opt("body")).optString("errno"),
								resultData.optJSONObject("body").optString("error"));

						if (null == id)
						{
							sql = "insert into cc_third_item_info(shop_id,min_order_num,package_box_num,available_times_start,available_times_end,rank,whether_push_over,description,item_pic,item_status,item_code,send_operator,send_time,channel,tenancy_id,is_charge_commission,remark) values('"
									+ condition.optString("store_id") + "','" + condition.optInt("min_order_num") + "'," + condition.optInt("package_box_num") + ",'" + condition.optString("available_times_start") + "','" + condition.optString("available_times_end") + "','" + condition.optString("rank")
									+ "','2','" + condition.optString("description") + "','" + condition.optString("photo1") + "','0','" + condition.optString("item_code") + "','" + condition.optString("send_operator") + "','" + condition.optString("send_time").trim() + "','" + Constant.BAIDU_CHANNEL
									+ "','" + tenancyID + "','" + condition.optString("is_charge_commission") + "','" + errMsg + "')";

							dao.execute(tenancyID, sql);
						}
						else
						{
							if("3".equals(condition.optString("whether_push_over")) || ("1".equals(condition.optString("online_flag")) && "2".equals(condition.optString("whether_push_over")))){
								sql = "update  cc_third_item_info set  whether_push_over='2',online_flag='1' ,remark='" + errMsg + "' where  id='" + id + "'";
							}else{
								sql = "update  cc_third_item_info set  whether_push_over='2' ,remark='" + errMsg + "' where  id='" + id + "'";
							}

							dao.execute(tenancyID, sql);
						}
						//20170224将推送菜品失败后返回错误信息修改显示为中文 end
						result.put("success", false);
						result.put("msg", errMsg);
					}
					ccBusniessLogBean.setResponseBody(resultString.toString());
				}catch(Exception e){
					ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
					ccBusniessLogBean.setIsNormal("0");
					e.printStackTrace();
				}finally{
					KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
				}
			}
			else if (condition.optString("channel").equalsIgnoreCase("WM10"))
			{
				sql = "insert into cc_third_item_info(shop_id,min_order_num,package_box_num,available_times_start,available_times_end,rank,whether_push_over,description,item_pic,item_status,item_code,send_operator,send_time,channel,tenancy_id) values('" + condition.optString("store_id") + "','"
						+ condition.optInt("min_order_num") + "'," + condition.optInt("package_box_num") + ",'" + condition.optString("available_times_start") + "','" + condition.optString("available_times_end") + "','" + condition.optString("rank") + "','1','" + condition.optString("description")
						+ "','" + condition.optString("photo1") + "','1','" + condition.optString("item_code") + "','" + condition.optString("send_operator") + "','" + condition.optString("send_time").trim() + "','" + Constant.WECHAT_CHANNEL + "','" + tenancyID + "')";
				dao.execute(tenancyID, sql);
				result.put("success", true);
			}
		}
		return result;

	}

	@Override
	public JSONObject dishUpdate(String tenancyID, JSONObject condition) throws Exception {
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();

		JSONObject result = new JSONObject();

		String sql = null;

		if (condition != null) {
			if (condition.optString("channel").equalsIgnoreCase("BD06"))
			{
				DishCreateUpdate dishUpdate = new DishCreateUpdate();
				// 合作方商户唯一 ID
				dishUpdate.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
				// 菜品唯一编号
				dishUpdate.setDish_id(condition.optString("item_code"));
				// 菜品名称
				dishUpdate.setName(condition.optString("item_name"));
				// 条形码编号
				dishUpdate.setUpc(condition.optString("item_barcode"));
				// 菜品价格
				dishUpdate.setPrice(CommonUtil.yuan2Fen(condition.optDouble("default_price")));
				// 菜品图片
				dishUpdate.setPic(condition.optString("photo1"));
				// 最小起订份数
				dishUpdate.setMin_order_num(condition.optInt("min_order_num"));
				// 单份所需餐盒数
				dishUpdate.setPackage_box_num(condition.optInt("package_box_num"));
				// 描述
				dishUpdate.setDescription(condition.optString("description"));
				if(StringUtils.isEmpty(condition.optString("is_charge_commission"))){
					condition.put("is_charge_commission","1");
				}
				// 可售时间
				if (condition.optString("available_times_start") != "" || condition.optString("available_times_end") != "")
				{
					Map<String, List<JSONObject>> timezone = new HashMap<String, List<JSONObject>>();
					List<JSONObject> timeList = new ArrayList<JSONObject>();
					TimeRange timeRange = new TimeRange();
					timeRange.setStart(condition.optString("available_times_start").equals("") || condition.optString("available_times_start").equals("null") || condition.optString("available_times_start") == null ? "10:00" : condition.optString("available_times_start"));
					timeRange.setEnd(condition.optString("available_times_end").equals("") || condition.optString("available_times_end").equals("null") || condition.optString("available_times_end") == null ? "22:00" : condition.optString("available_times_end"));
					timeList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(JSONObject.fromObject(timeRange))));

					timezone.put("*", timeList);

					dishUpdate.setAvailable_times(timezone);
				}

				// 分类信息
				List<JSONObject> categoryList = new ArrayList<JSONObject>();
				JSONObject category = new JSONObject();
				category.put("name",condition.optString("last_send_class_name"));
				category.put("rank",condition.optInt("rank"));
				categoryList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(JSONObject.fromObject(category))));

				dishUpdate.setCategory(categoryList);

				// 菜品规格
				List<JSONObject> normsList = new ArrayList<JSONObject>();

				Norms norms = null;

				if (condition.containsKey("priceList") && condition.get("priceList") != "")
				{

					@SuppressWarnings("unchecked")
					List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(condition.get("priceList").toString(), new TypeToken<List<JSONObject>>()
					{
					}.getType());
					// 菜品规格
					List<JSONObject> threshold_List = new ArrayList<JSONObject>();
					for (int i = 0; i < list.size(); i++)
					{
						norms = new Norms();
						norms.setName(list.get(i).optString("unit_name"));
						// norms.setValue(String.valueOf(list.get(i).optInt("id")));//百度外卖不支持规格ID
						norms.setValue(list.get(i).optString("unit_name"));
						norms.setPrice(CommonUtil.yuan2Fen(list.get(i).optDouble("price")));
						Threshold threshold=new Threshold();
						threshold.setNum(10000);
						threshold.setTime("0|8|*");
						threshold_List.add(JSONObject.fromObject(CommonUtil.sortMapByKey(JSONObject.fromObject(threshold))));
						norms.setThreshold(threshold_List);
						normsList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(JSONObject.fromObject(norms))));

					}
				}

				dishUpdate.setNorms(normsList);

				// 菜品属性
				// List<Attr> attrList = new ArrayList<Attr>();
				// Attr attr = new Attr();
				// attr.setName(condition.optString("attr_name"));
				// attr.setValue("Y".equals(condition.optString("is_combo"))?"是":"否");
				// attrList.add(attr);
				//
				// dishUpdate.setAttr(attrList);

				if("no".equals(condition.optString("send"))){
					if("1".equals(condition.optString("whether_push_over"))){
						sql = "update cc_third_item_info set whether_push_over='3',min_order_num='" + condition.optInt("min_order_num") + "',package_box_num='" + condition.optInt("package_box_num") + "',available_times_start='" + condition.optString("available_times_start")
								+ "',available_times_end='" + condition.optString("available_times_end") + "',rank='" + condition.optString("rank") + "',description='" + condition.optString("description") + "',item_pic='" + condition.optString("photo1") + "',update_operator='"
								+ condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "',is_charge_commission='" + condition.optString("is_charge_commission").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_code='"
								+ condition.optString("item_code") + "'";
						dao.execute(tenancyID, sql);
						result.put("success", true);
						return result;
					}else if("0".equals(condition.optString("whether_push_over"))){

						sql = "update cc_third_item_info set whether_push_over='0',min_order_num='" + condition.optInt("min_order_num") + "',package_box_num='" + condition.optInt("package_box_num") + "',available_times_start='" + condition.optString("available_times_start")
								+ "',available_times_end='" + condition.optString("available_times_end") + "',rank='" + condition.optString("rank") + "',description='" + condition.optString("description") + "',item_pic='" + condition.optString("photo1") + "',update_operator='"
								+ condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "',is_charge_commission='" + condition.optString("is_charge_commission").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_code='"
								+ condition.optString("item_code") + "'";
						dao.execute(tenancyID, sql);
						result.put("success", true);
						return result;
					}else {
						sql = "update cc_third_item_info set min_order_num='" + condition.optInt("min_order_num") + "',package_box_num='" + condition.optInt("package_box_num") + "',available_times_start='" + condition.optString("available_times_start")
								+ "',available_times_end='" + condition.optString("available_times_end") + "',rank='" + condition.optString("rank") + "',description='" + condition.optString("description") + "',item_pic='" + condition.optString("photo1") + "',update_operator='"
								+ condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "',is_charge_commission='" + condition.optString("is_charge_commission").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_code='"
								+ condition.optString("item_code") + "'";
						dao.execute(tenancyID, sql);
						result.put("success", true);
						return result;
					}
				}else{

					//是否上传图片，1代表是,0或其它值代表不上传图片。at 2017-05-08
					if(!"1".equals(condition.optString("isUploadImg"))){
						//changhui 2018-1-29 注销
						//dishCreate.setPic("-1");
						//changhui 2018-1-29 增加 3.0版本
						dishUpdate.setPic("1");
					}

					Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));
					//changhui 2018-1-23 注释
					/*String requestStr="";
					if(condition.optString("whether_push_over").equals("1")||condition.optString("whether_push_over").equals("3")||(condition.optString("whether_push_over").equals("2")&&condition.optString("online_flag").equals("1"))){
						requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_UPDATE, dishUpdate);
					}else{
						requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CREATE, dishUpdate);
					}*/

					ccBusniessLogBean.setRequestId(condition.optString("requestId"));
					ccBusniessLogBean.setTenancyId(tenancyID);
					ccBusniessLogBean.setShopId(condition.optString("store_id"));
					ccBusniessLogBean.setCategory("cc");
					ccBusniessLogBean.setType("dish");
					ccBusniessLogBean.setChannel(condition.optString("channel"));
					ccBusniessLogBean.setChannelName(condition.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
					ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.DishServiceImpl:dishUpdate");
					ccBusniessLogBean.setRequestBody(condition.toString());

					ccBusniessLogBean.setCreateTime(new Date().getTime());
					ccBusniessLogBean.setIsNormal("1");
					ccBusniessLogBean.setIsThird("0");

					//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
					if(condition.containsKey("dishes")){
						ccBusniessLogBean.setThirdId(condition.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
						ccBusniessLogBean.setTzxId(condition.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
						ccBusniessLogBean.setTzxName(condition.optJSONArray("dishes").getJSONObject(0).optString("item_name"));
					}else{
						ccBusniessLogBean.setThirdId(condition.optString("third_item_id"));
						ccBusniessLogBean.setTzxId(condition.optString("item_id"));
						ccBusniessLogBean.setTzxName(condition.optString("item_name"));
					}

					// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
					ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());
					//changhui 2018-1-23 注释
					//ccBusniessLogBean.setRequestBody(requestStr);

					try{

						/*String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);
						ccBusniessLogBean.setResponseBody(resultString.toString());

						JSONObject resultData = JSONObject.fromObject(resultString);*/

						//changhui 2018-1-23 添加 start
						Map<String,Object> getMap = getDishCreateUpdateMap(dishUpdate);
						String resultString="";
						if(condition.optString("whether_push_over").equals("1")||condition.optString("whether_push_over").equals("3")||(condition.optString("whether_push_over").equals("2")&&condition.optString("online_flag").equals("1"))){
							resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.update", getMap, BaiduUtils.VERSION_3);
						}else {
							resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.create", getMap, BaiduUtils.VERSION_3);
						}
						ccBusniessLogBean.setResponseBody(resultString.toString());

						JSONObject resultData = JSONObject.fromObject(resultString);
						int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");

						//2017-03-10菜品新增结果可处理问题验证start
						if (resultCode == 20270|| resultCode == 10202){//2017-03-10菜品不存在调新增接口
							//requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CREATE, dishUpdate);
							//resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);
							//changhui 2018-1-23 添加 start
							Map<String, Object> getUpdateMap = getDishCreateUpdateMap(dishUpdate);
							resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.create", getUpdateMap, BaiduUtils.VERSION_3);
							//end

							resultData = JSONObject.fromObject(resultString);
							resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
						}
						//2017-03-10菜品新增结果可处理问题验证start


						logger.info("[创建百度菜品]上传数据:" + resultData + "\n返回数据:" + resultString);
						if (resultCode == 0)
						{
							sql = "update cc_third_item_info set whether_push_over='1', min_order_num='" + condition.optInt("min_order_num") + "',package_box_num='" + condition.optInt("package_box_num") + "',available_times_start='" + condition.optString("available_times_start")
									+ "',available_times_end='" + condition.optString("available_times_end") + "',rank='" + condition.optString("rank") + "',description='" + condition.optString("description") + "',item_pic='" + condition.optString("photo1") + "',update_operator='"
									+ condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "' ,is_charge_commission='" + condition.optString("is_charge_commission").trim() + "',remark='"+JSONObject.fromObject(resultData.opt("body")).optString("error")+"' where shop_id='" + condition.optString("store_id")
									+ "' and item_code='" + condition.optString("item_code") + "'";
							dao.execute(tenancyID, sql);
							result.put("success", true);
						}
						else
						{
							sql = "update cc_third_item_info set whether_push_over='2', online_flag ='1',min_order_num='" + condition.optInt("min_order_num") + "',package_box_num='" + condition.optInt("package_box_num") + "',available_times_start='" + condition.optString("available_times_start")
									+ "',available_times_end='" + condition.optString("available_times_end") + "',rank='" + condition.optString("rank") + "',description='" + condition.optString("description") + "',item_pic='" + condition.optString("photo1") + "',update_operator='"
									+ condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "' ,is_charge_commission='" + condition.optString("is_charge_commission").trim() + "',remark='"+JSONObject.fromObject(resultData.opt("body")).optString("error")+"' where shop_id='" + condition.optString("store_id")
									+ "' and item_code='" + condition.optString("item_code") + "'";
							dao.execute(tenancyID, sql);
							result.put("success", false);
							if(20253==resultCode) {
								result.put("msg", "此门店还没有推送到百度。");
							} else {
								result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
							}
						}

					}catch(Exception e){
						ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
						ccBusniessLogBean.setIsNormal("0");
						e.printStackTrace();
					}finally{
						KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
					}

					return result;

				}

			}
			else if (condition.optString("channel").equalsIgnoreCase("WM10"))
			{
				sql = "update cc_third_item_info set min_order_num='" + condition.optInt("min_order_num") + "',package_box_num='" + condition.optInt("package_box_num") + "',available_times_start='" + condition.optString("available_times_start") + "',available_times_end='"
						+ condition.optString("available_times_end") + "',rank='" + condition.optString("rank") + "',description='" + condition.optString("description") + "',item_pic='" + condition.optString("photo1") + "',update_operator='" + condition.optString("update_operator")
						+ "',update_time='" + condition.optString("update_time").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_code='" + condition.optString("item_code") + "'";
				dao.execute(tenancyID, sql);
				result.put("success", true);
			}
		}
		return result;
	}

			@Override
			public JSONObject dishOnline(String tenancyID, JSONObject condition) throws Exception
			{
				JSONObject result = new JSONObject();

				String sql = null;

				if (condition != null)
				{
					DishOnOfflineDel dishOnOfflineDel = new DishOnOfflineDel();
					dishOnOfflineDel.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
					dishOnOfflineDel.setDish_id(condition.optString("item_code"));

					Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));

					//changhui 2018-1-23 注释
			/*String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_ONLINE, dishOnOfflineDel);

			String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);*/

					//changhui 2018-1-23 添加 start
					Map<String,Object> getMap = getDishOnOfflineDelMap(dishOnOfflineDel);

					String resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.online", getMap, BaiduUtils.VERSION_3);
					//end

					JSONObject resultData = JSONObject.fromObject(resultString);

					int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");

					//changhui 2018-1-23 注释并添加 start
					//logger.info("[百度菜品上线]上传数据:" + requestStr + "\n返回数据:" + resultString);
					logger.info("[百度菜品上线]上传数据:" + getMap.toString() + "\n返回数据:" + resultString);
					//end

					if (resultCode == 0)
					{
						sql = "update cc_third_item_info set item_status='1',update_operator='" + condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_code='"
								+ condition.optString("item_code") + "'";
						dao.execute(tenancyID, sql);
						result.put("success", true);
					}
					else
					{
						result.put("success", false);
						result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
					}

				}
				return result;
			}

			@Override
			public JSONObject dishOffline(String tenancyID, JSONObject condition) throws Exception
			{
				JSONObject result = new JSONObject();

				String sql = null;

				if (condition != null)
				{
					DishOnOfflineDel dishOnOfflineDel = new DishOnOfflineDel();
					dishOnOfflineDel.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
					dishOnOfflineDel.setDish_id(condition.optString("item_code"));

					Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));

					//changhui 2018-1-23 注释
			/*String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_OFFLINE, dishOnOfflineDel);

			String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);*/

					//changhui 2018-1-23 添加 start
					Map<String,Object> getMap = getDishOnOfflineDelMap(dishOnOfflineDel);

					String resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.offline", getMap, BaiduUtils.VERSION_3);
					//end

					JSONObject resultData = JSONObject.fromObject(resultString);

					int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");

					//changhui 2018-1-23 注释并添加 start
					//logger.info("[百度菜品下线]上传数据:" + requestStr + "\n返回数据:" + resultString);
					logger.info("[百度菜品下线]上传数据:" + getMap.toString() + "\n返回数据:" + resultString);
					//end

					if (resultCode == 0)
					{
						sql = "update cc_third_item_info set item_status='0',update_operator='" + condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_code='"
								+ condition.optString("item_code") + "'";
						dao.execute(tenancyID, sql);
						result.put("success", true);
					}
					else
					{
						result.put("success", false);
						result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
					}

				}
				return result;
			}

			@Override
			public JSONObject dishDelete(String tenancyID, JSONObject condition) throws Exception
			{

				CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();

				JSONObject result = new JSONObject();

				String sql = null;
				DishOnOfflineDel dishOnOfflineDel = new DishOnOfflineDel();
				dishOnOfflineDel.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
				dishOnOfflineDel.setDish_id(condition.optString("item_code"));

				Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));

				//changhui 2018-1-23 注释
				//String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_DELETE, dishOnOfflineDel);

				ccBusniessLogBean.setRequestId(condition.optString("requestId"));
				ccBusniessLogBean.setTenancyId(tenancyID);
				ccBusniessLogBean.setShopId(condition.optString("store_id"));
				ccBusniessLogBean.setCategory("cc");
				ccBusniessLogBean.setType("dish");
				ccBusniessLogBean.setChannel(condition.optString("channel"));
				ccBusniessLogBean.setChannelName(condition.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
				ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.DishServiceImpl:dishCreate");
				ccBusniessLogBean.setRequestBody(condition.toString());


				ccBusniessLogBean.setCreateTime(new Date().getTime());
				ccBusniessLogBean.setIsNormal("1");
				ccBusniessLogBean.setIsThird("1");

				//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
				if(condition.containsKey("dishes")){
					ccBusniessLogBean.setThirdId(condition.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
					ccBusniessLogBean.setTzxId(condition.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
					ccBusniessLogBean.setTzxName(condition.optJSONArray("dishes").getJSONObject(0).optString("item_name"));
				}else{
					ccBusniessLogBean.setThirdId(condition.optString("third_item_id"));
					ccBusniessLogBean.setTzxId(condition.optString("item_id"));
					ccBusniessLogBean.setTzxName(condition.optString("item_name"));
				}


				// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
				ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

				//ccBusniessLogBean.setRequestBody(requestStr);

				try{

					//changhui 2018-1-23 添加 start
					Map<String,Object> getMap = getDishOnOfflineDelMap(dishOnOfflineDel);
					ccBusniessLogBean.setRequestBody(getMap.toString());
					String resultString = BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("store_id"), "dish.delete", getMap, BaiduUtils.VERSION_3);
					//end

					//changhui 2018-1-23 注释
					//String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);

					ccBusniessLogBean.setResponseBody(resultString.toString());

					JSONObject resultData = JSONObject.fromObject(resultString);

					int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");

					//changhui 2018-1-23 注释并添加 start
					//logger.info("[百度菜品删除]上传数据:" + requestStr + "\n返回数据:" + resultString);
					logger.info("[百度菜品删除]上传数据:" + getMap.toString() + "\n返回数据:" + resultString);
					//end

					if (resultCode == 0)
					{
						// 20170224修改删除菜品时同名被误删的错误by
						sql = "delete from cc_third_item_info where shop_id='" + condition.optInt("store_id") + "' and item_code='" + condition.optInt("item_code") + "' and id='" + condition.optInt("id") + "'";
						dao.execute(tenancyID, sql);
						result.put("success", true);
					}
					else
					{
						String errMsg = PropertiesUtils.getErrMsgByErrno(condition.optString("channel"),JSONObject.fromObject(resultData.opt("body")).optString("errno"),
								resultData.optJSONObject("body").optString("error"));
						if(CommonUtil.checkStringIsNotEmpty(errMsg)){
							sql="update cc_third_item_info set remark='"+errMsg+"' where shop_id='"+condition.optString("store_id")+"' and channel='BD06' and item_code='"+condition.optString("item_code")+"';";
							dao.execute(tenancyID, sql);
						}
						result.put("success", false);
						result.put("msg", errMsg);
					}

				}catch(Exception e){
					ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
					ccBusniessLogBean.setIsNormal("0");
					e.printStackTrace();
				}finally{
					KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
				}
				return result;
			}

			@Override
			public JSONObject dishOnlineBatch(String tenancyId, JSONObject data)
			{
				List<JSONObject> list = null;
				int success = 0, fail = 0;
				String send_operator = data.optString("send_operator");
				String send_time = data.optString("send_time");
				if (!data.containsKey("dishes"))
				{
					try
					{
						data.put("oper", "online");
						JSONObject result_obj = loadDishListNoPage(tenancyId, data);
						list = result_obj.optJSONArray("rows");
					}
					catch (Exception e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				else
				{

					list = (List<JSONObject>) GsonUtil.toT(data.opt("dishes").toString(), new TypeToken<List<JSONObject>>()
					{
					}.getType());
				}
				if (list.size() > 0)
				{
					for (JSONObject dish : list)
					{

						try
						{
							dish.put("store_id", dish.optInt("store_id"));
							dish.put("item_code", dish.optInt("item_code"));
							dish.put("update_operator", data.optString("employeeName"));
							dish.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
							JSONObject result = dishOnline(tenancyId, dish);
							if (result.optBoolean("success"))
							{
								success++;
								result.put("success", true);
								return result;
							}
							else
							{
								success++;
								result.put("success", false);
								return result;
							}
						}
						catch (Exception e)
						{
							fail++;
							e.printStackTrace();
						}

					}
				}

				JSONObject result = new JSONObject();
				result.put("success", true);
				result.put("msg", "成功:" + success + "\t 失败:" + fail);
				return result;
			}

			@Override
			public JSONObject dishOfflineBatch(String tenantId, JSONObject obj)
			{
				List<JSONObject> list = null;
				int success = 0, fail = 0;
				if (!obj.containsKey("dishes"))
				{
					try
					{
						JSONObject result_obj = loadDishListNoPage(tenantId, obj);
						list = result_obj.optJSONArray("rows");
					}
					catch (Exception e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				else
				{
					list = (List<JSONObject>) GsonUtil.toT(obj.opt("dishes").toString(), new TypeToken<List<JSONObject>>()
					{
					}.getType());

				}
				if (list.size() > 0)
				{
					for (JSONObject dish : list)
					{
						try
						{
							dish.put("update_operator", obj.optString("employeeName"));
							dish.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
							dish.put("store_id", dish.optInt("store_id"));
							dish.put("item_code", dish.optInt("item_code"));
							JSONObject result = dishOffline(tenantId, dish);
							if (result.optBoolean("success"))
							{
								success++;
								result.put("success", true);
								return result;
							}
							else
							{
								fail++;
								result.put("success", false);
								return result;

							}
						}
						catch (Exception e)
						{
							fail++;
							e.printStackTrace();
						}
					}
				}

				JSONObject result = new JSONObject();
				result.put("success", true);
				result.put("msg", "成功:" + success + "\t 失败:" + fail);
				return result;
			}

			@Override
			public String findStoreChannelClass(String tenancyID, JSONObject condition) throws Exception
			{
				String result = "";
				StringBuilder sql = new StringBuilder();
//		sql.append("SELECT A.class as id,e.itemclass_name as text");
				sql.append("SELECT gg. TEXT AS ID,gg.text from ( ");
				sql.append("SELECT e.itemclass_name AS ID,e.itemclass_name as text");
				sql.append(" FROM hq_item_menu_class A ");
				sql.append(" LEFT JOIN hq_item_menu_details b ON A .details_id = b. ID");
				sql.append(" LEFT JOIN hq_item_menu_organ  c on c.item_menu_id= b.item_menu_id");
				if(!condition.optString("channel").equals("WM10")){
					sql.append(" LEFT JOIN cc_third_item_class_info d on d.item_class_id=a.class and d.shop_id=c.store_id");
				}
				sql.append(" LEFT JOIN hq_item_class e on e.id=a.class");
				sql.append(" LEFT JOIN organ f on f.id=c.store_id ");
				sql.append(" where 1=1 and e.itemclass_name is not null");
				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and c.store_id in (" + condition.optString("shop_id") + " ) ");
				}
				if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
				{
					sql.append(" and a.chanel = 'BD06'  ");
				}
				else
				{
					sql.append(" and a.chanel = '" + condition.optString("channel") + "'  ");
				}
				sql.append(" GROUP BY  A.class,e.id");
				sql.append(" ) as gg GROUP BY gg.text");

				List<JSONObject> list3 = this.dao.query4Json(tenancyID, sql.toString());
				result = com.tzx.framework.common.util.JsonUtils.list2json(list3);
				return result;

			}

			@Override
			public JSONObject loadDishListNoPage(String tenancyID, JSONObject condition) throws Exception
			{
				JSONObject result = new JSONObject();
				StringBuilder sql = new StringBuilder();

				int storeId = condition.optInt("id");

				String channel = condition.optString("channel");
				switch (channel)
				{
					case "MT08":
						sql.append("select   DISTINCT(A . ID),'"
								+ condition.optString("channel")
								+ "' as channel,d.store_id,d.store_id AS shop_id,a.id as item_code,b.item_name,a.item_barcode,c.item_id,g.g_price as default_price,e.min_order_num,e.package_box_num,e.whether_push_over,e.description,e.item_pic as photo1,e.available_times_start,e.available_times_end,concat (f.itemclass_name,'-',ll.itemclass_name)as last_send_class_name,a.is_combo,case when a.is_combo='Y' then '套餐' else '单品' end as attr_name,e.item_status from hq_item_menu_class b inner join hq_item_menu_details c on b.details_id = c.id inner join hq_item_menu_organ d on c.item_menu_id = d.item_menu_id inner join hq_item_menu h on c.item_menu_id = h.id inner join cc_third_organ_info i on i.shop_id = d.store_id left join hq_item_info a on a.id = c.item_id left join cc_third_item_info e on e.item_code = CAST(A.id as VARCHAR) and e.shop_id="
								+ storeId + "  and e.channel=b.chanel");
						sql.append(" LEFT JOIN hq_item_class f ON b. CLASS = f.id LEFT JOIN hq_item_class ll ON f.father_id = ll.id");
						sql.append(" left join ( select c1.unit_name as g_unit_name,a1.price as g_price,	c1.item_id as g_item_id from hq_item_pricesystem a1 left join organ b1 on cast(a1.price_system as varchar) = b1.price_system left join hq_item_unit c1 on a1.item_unit_id = c1.id where b1.id='"
								+ condition.optInt("id") + "' and a1.chanel = '" + condition.optString("channel") + "' and c1.valid_state = '1' and c1.is_default = 'Y' ) g on g.g_item_id = c.item_id ");
						sql.append("where d.store_id = '" + condition.optInt("id") + "' and b.chanel = '" + condition.optString("channel") + "' and h.valid_state = '1'");
						break;
					case "EL09":
						sql.append("select   DISTINCT(A . ID) as item_id,e.stock,e.max_stock,e.dish_available_time,e.is_new,e.is_featured,e.is_gum,e.is_spicy,f.third_class_id,e.third_item_id,e.is_charge_commission,e.box_price,'"
								+ condition.optString("channel")
								+ "' as channel,z.org_full_name,d.store_id,a.id as item_code,b.item_name,a.item_barcode,c.item_id,g.g_price as default_price,e.min_order_num,e.package_box_num,CASE WHEN e.whether_push_over IS NULL THEN '0' ELSE e.whether_push_over END AS whether_push_over,e.description,a.photo1 as wxxt,e.item_pic as photo1,e.available_times_start,e.available_times_end,f.last_send_class_name,CASE WHEN C .menu_item_rank IS NULL THEN e.rank ELSE C .menu_item_rank END AS RANK,a.is_combo,case when a.is_combo='Y' then '套餐' else '单品' end as attr_name,e.item_status from hq_item_menu_class b inner join hq_item_menu_details c on b.details_id = c.id inner join hq_item_menu_organ d on c.item_menu_id = d.item_menu_id INNER JOIN organ z ON z.id= d.store_id inner join hq_item_menu h on c.item_menu_id = h.id inner join cc_third_organ_info i on i.shop_id = d.store_id left join hq_item_info a on a.id = c.item_id left join cc_third_item_info e on e.item_code = CAST(A.id as VARCHAR) and e.channel=b.chanel ");
						if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
						{
							sql.append(" and e.shop_id in (" + condition.optString("shop_id") + " ) ");
						}

						//为了获取目标部门和源部门共有的菜品所添加
						if (condition.containsKey("dest_shop_ids") && !StringUtils.isEmpty(condition.optString("dest_shop_ids")))
						{
							String destShopIds = condition.optString("dest_shop_ids");
							sql.append(" INNER JOIN ( SELECT dest_b.item_id as item_id FROM hq_item_menu_class dest_a");
							sql.append(" inner join hq_item_menu_details dest_b on dest_a.details_id = dest_b.id");
							sql.append(" INNER JOIN hq_item_menu_organ dest_c ON dest_b .item_menu_id = dest_c.item_menu_id ");

							sql.append(" where dest_c.store_id in (").append(destShopIds).append(")");
							sql.append(" and dest_a.chanel = '").append(condition.optString("channel")).append("'");
							sql.append(" ) qq on  qq.item_id = c.item_id");
						}

						sql.append(" left join cc_third_item_class_info f on b.class = f.item_class_id ");
				/*if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and f.shop_id in (" + condition.optString("shop_id") + " ) ");
				}*/
						sql.append(" left join ( select  b1.id as store_id,c1.unit_name as g_unit_name,a1.price as g_price,	c1.item_id as g_item_id from hq_item_pricesystem a1 left join organ b1 on cast(a1.price_system as varchar) = b1.price_system left join hq_item_unit c1 on a1.item_unit_id = c1.id where  c1.valid_state = '1' and c1.is_default = 'Y' ");
						if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
						{
							sql.append(" and b1.id in (" + condition.optString("shop_id") + " ) ");
						}
						if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
						{
							sql.append(" and a1.chanel = 'BD06'  ");
						}
						else
						{
							sql.append(" and a1.chanel = '" + condition.optString("channel") + "'  ");
						}
						sql.append(" ) g on g.g_item_id = c.item_id  and g.store_id=d.store_id ");
//				sql.append("where  h.valid_state = '1'");
						sql.append("where  h.valid_state = '1' and f.last_send_class_name is not null  ");
						if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
						{
							sql.append(" and d.store_id in (" + condition.optString("shop_id") + " ) ");
						}
						if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
						{
							sql.append(" and b.chanel = 'BD06'  ");
						}
						else
						{
							sql.append(" and b.chanel = '" + condition.optString("channel") + "'  ");
						}
						if (condition.containsKey("oper") && !StringUtils.isEmpty(condition.optString("oper")))
						{
							if (condition.optString("oper").equals("delete"))
							{
								sql.append(" and e.whether_push_over ='1' ");
							}
							if (condition.optString("oper").equals("offline"))
							{
								sql.append(" and e.item_status ='1' ");
							}
							if (condition.optString("oper").equals("online"))
							{
								sql.append(" and e.item_status ='0'  and e.whether_push_over ='1' ");
							}
							if (condition.optString("oper").equals("push"))
							{
								sql.append(" and  (e.whether_push_over != '1' or e.whether_push_over is null)   ");
							}
						}
						if (condition.containsKey("oper_copy") && !StringUtils.isEmpty(condition.optString("oper_copy")))
						{
							if (condition.containsKey("whether_push_over") && !StringUtils.isEmpty(condition.optString("whether_push_over")))
							{
								if (!condition.optString("whether_push_over").equals("9999") && !condition.optString("whether_push_over").equals("0"))
								{
									sql.append(" and e.whether_push_over = '" + condition.optString("whether_push_over") + "'  ");
								}
								else if (condition.optString("whether_push_over").equals("0"))
								{
									sql.append(" and (e.whether_push_over IS NULL  or e.whether_push_over='0') ");
								}
							}
						}

						if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id"))&&!"==全部==".equals(condition.optString("class_id")))
						{
							sql.append(" and b.class in('" + condition.optString("class_id") + "') ");
						}
						if (condition.containsKey("item_name") && !StringUtils.isEmpty(condition.optString("item_name")))
						{
							sql.append(" and b.item_name like '%" + condition.optString("item_name") + "%' ");
						}
						break;
					default:
						sql.append("select   DISTINCT(A . ID),f.third_class_id,e.third_item_id,e.dish_available_time,e.is_charge_commission,e.box_price,'"
								+ condition.optString("channel")
								+ "' as channel,z.org_full_name,d.store_id,d.store_id AS shop_id,a.id as item_code,b.item_name,a.item_barcode,c.item_id,G.g_unit_id AS unit_id,g.g_price as default_price,e.min_order_num,e.package_box_num,CASE WHEN e.whether_push_over IS NULL THEN '0' ELSE e.whether_push_over END AS whether_push_over,e.description,a.photo1 as wxxt,e.item_pic as photo1,e.available_times_start,e.available_times_end,f.last_send_class_name,CASE WHEN C .menu_item_rank IS NULL THEN e.rank ELSE C .menu_item_rank END AS RANK,a.is_combo,case when a.is_combo='Y' then '套餐' else '单品' end as attr_name,e.item_status from hq_item_menu_class b inner join hq_item_menu_details c on b.details_id = c.id inner join hq_item_menu_organ d on c.item_menu_id = d.item_menu_id INNER JOIN organ z ON z.id= d.store_id inner join hq_item_menu h on c.item_menu_id = h.id inner join cc_third_organ_info i on i.shop_id = d.store_id left join hq_item_info a on a.id = c.item_id left join cc_third_item_info e on e.item_code = CAST(A.id as VARCHAR) and e.channel=b.chanel ");
						if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
						{
							sql.append(" and e.shop_id in (" + condition.optString("shop_id") + " ) ");
						}

						//为了获取目标部门和源部门共有的菜品所添加
						if (condition.containsKey("dest_shop_ids") && !StringUtils.isEmpty(condition.optString("dest_shop_ids")))
						{
							String destShopIds = condition.optString("dest_shop_ids");
							sql.append(" INNER JOIN ( SELECT dest_b.item_id as item_id FROM hq_item_menu_class dest_a");
							sql.append(" inner join hq_item_menu_details dest_b on dest_a.details_id = dest_b.id");
							sql.append(" INNER JOIN hq_item_menu_organ dest_c ON dest_b .item_menu_id = dest_c.item_menu_id ");

							sql.append(" where dest_c.store_id in (").append(destShopIds).append(")");
							sql.append(" and dest_a.chanel = '").append(condition.optString("channel")).append("'");
							sql.append(" ) qq on  qq.item_id = c.item_id");
						}

						sql.append(" left join cc_third_item_class_info f on b.class = f.item_class_id ");
				/*if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and f.shop_id in (" + condition.optString("shop_id") + " ) ");
				}*/
						sql.append(" left join ( select  b1.id as store_id,c1.unit_name as g_unit_name,c1.id AS g_unit_id,a1.price as g_price,	c1.item_id as g_item_id from hq_item_pricesystem a1 left join organ b1 on cast(a1.price_system as varchar) = b1.price_system left join hq_item_unit c1 on a1.item_unit_id = c1.id where  c1.valid_state = '1' and c1.is_default = 'Y' ");
						if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
						{
							sql.append(" and b1.id in (" + condition.optString("shop_id") + " ) ");
						}
						if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
						{
							sql.append(" and a1.chanel = 'BD06'  ");
						}
						else
						{
							sql.append(" and a1.chanel = '" + condition.optString("channel") + "'  ");
						}
						sql.append(" ) g on g.g_item_id = c.item_id  and g.store_id=d.store_id ");
//				sql.append("where  h.valid_state = '1'");
						sql.append("where  h.valid_state = '1' and f.last_send_class_name is not null  ");
						if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
						{
							sql.append(" and d.store_id in (" + condition.optString("shop_id") + " ) ");
						}
						if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
						{
							sql.append(" and b.chanel = 'BD06'  ");
						}
						else
						{
							sql.append(" and b.chanel = '" + condition.optString("channel") + "'  ");
						}
						if (condition.containsKey("oper") && !StringUtils.isEmpty(condition.optString("oper")))
						{
							if (condition.optString("oper").equals("delete"))
							{
								sql.append(" and e.whether_push_over ='1' ");
							}
							if (condition.optString("oper").equals("offline"))
							{
								sql.append(" and e.item_status ='1' ");
							}
							if (condition.optString("oper").equals("online"))
							{
								sql.append(" and e.item_status ='0'  and e.whether_push_over ='1' ");
							}
							//修改已推送的菜品也可以重复推送
					/*if (condition.optString("oper").equals("push"))
					{
						sql.append(" and  (e.whether_push_over != '1' or e.whether_push_over is null)   ");
					}*/
						}
						if (condition.containsKey("oper_copy") && !StringUtils.isEmpty(condition.optString("oper_copy")))
						{
							if (condition.containsKey("whether_push_over") && !StringUtils.isEmpty(condition.optString("whether_push_over")))
							{
								if (!condition.optString("whether_push_over").equals("9999") && !condition.optString("whether_push_over").equals("0"))
								{
									sql.append(" and e.whether_push_over = '" + condition.optString("whether_push_over") + "'  ");
								}
								else if (condition.optString("whether_push_over").equals("0"))
								{
									sql.append(" and (e.whether_push_over IS NULL  or e.whether_push_over='0') ");
								}
							}
						}

						if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id"))&&!"==全部==".equals(condition.optString("class_id")))
						{
							sql.append(" and b.class ('" + condition.optString("class_id") + "') ");
						}
						if (condition.containsKey("item_name") && !StringUtils.isEmpty(condition.optString("item_name")))
						{
							sql.append(" and b.item_name like '%" + condition.optString("item_name") + "%' ");
						}
				}

				List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
				if (list.size() > 0)
				{
					result.put("rows", list);
				}
				else
				{
					result.put("rows", "[]");
				}
				logger.info("[获取百度菜品列表]" + result);
				return result;
			}

			@Override
			public JSONObject batchDishDategoryPush(String tenancyID, JSONObject data) throws Exception
			{
				List<JSONObject> list = null;
				int success = 0, fail = 0;
				JSONObject result = new JSONObject();
				if (!data.containsKey("dishes_class"))
				{
					try
					{
						JSONObject result_obj = loadDishCategoryListNoPage(tenancyID, data);
						list = result_obj.optJSONArray("rows");
					}
					catch (Exception e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}

				}
				else
				{

					list = (List<JSONObject>) GsonUtil.toT(data.opt("dishes_class").toString(), new TypeToken<List<JSONObject>>()
					{
					}.getType());
				}
				if (list.size() > 0)
				{
					for (JSONObject dishcategory : list)
					{

						try
						{
							dishcategory.put("requestId", data.optString("requestId"));
							dishcategory.put("store_id", dishcategory.optInt("store_id"));
							dishcategory.put("class_id", dishcategory.optInt("class_id"));

							dishcategory.put("update_time", data.optString("send_time"));
							dishcategory.put("update_operator", data.optString("send_operator"));
							dishcategory.put("send_time", data.optString("send_time"));
							dishcategory.put("send_operator", data.optString("send_operator"));
							dishcategory.put("rank", dishcategory.optInt("rank") == 0 ? 1 : dishcategory.optInt("rank"));
							dishcategory.put("send", "yes");
							if (("0".equals(dishcategory.optString("whether_push_over"))) || ("2".equals(dishcategory.optString("whether_push_over")) && (dishcategory.optString("last_send_class_name") == null || "".equals(dishcategory.optString("last_send_class_name")))))
							{
								result = dishDategoryCreate(tenancyID, dishcategory);
								if (result.optBoolean("success"))
								{
									success++;
									result.put("success", true);
									return result;
								}
								else
								{
									if(result.optString("errno").equals("10213")){
										dishcategory.put("last_send_class_name", dishcategory.optString("cur_class_name"));
										result = dishCategoryUpdate(tenancyID, dishcategory);
										if (result.optBoolean("success"))
										{
											success++;
											result.put("success", true);
											return result;
										}
										else
										{
											fail++;
											result.put("success", false);
											return result;
										}

									}
//							fail++;
//							result.put("success", false);
//							return result;
								}
							}
							else
							{
								result = dishCategoryUpdate(tenancyID, dishcategory);
								if (result.optBoolean("success"))
								{
									success++;
									result.put("success", true);
									return result;
								}
								else
								{
									if(result.optString("errno").equals("10214")){
										dishcategory.remove("old_name");
										result = dishDategoryCreate(tenancyID, dishcategory);
										if (result.optBoolean("success"))
										{
											success++;
											result.put("success", true);
											return result;
										}
										else
										{
											fail++;
											result.put("success", false);
											return result;
										}
									}

//							fail++;
//							result.put("success", false);
//							return result;
								}
							}
						}
						catch (Exception e)
						{
							fail++;
							e.printStackTrace();
						}

					}
				}

				result.put("success", result.optBoolean("success"));
				result.put("msg", "成功:" + success + "\t 失败:" + fail);
				return result;
			}

			@Override
			public JSONObject loadDishCategoryListNoPage(String tenancyID, JSONObject condition) throws Exception
			{
				JSONObject result = new JSONObject();
				StringBuilder sql = new StringBuilder();

				// 20160513修改
				sql.append("SELECT d.id,A.class as class_id,e.itemclass_name as cur_class_name,d.last_send_class_name,a.menu_class_rank as rank, CASE WHEN d.whether_push_over IS NULL THEN '0' ELSE d.whether_push_over END AS whether_push_over,f.id as store_id,f.org_full_name,a.chanel as channel");
				sql.append(" FROM hq_item_menu_class A");
				sql.append(" LEFT JOIN hq_item_menu_details b ON A .details_id = b. ID");
				sql.append(" LEFT JOIN hq_item_menu_organ  c on c.item_menu_id= b.item_menu_id");
				sql.append(" LEFT JOIN cc_third_item_class_info d on d.item_class_id=a.class and d.shop_id=c.store_id");
				sql.append(" LEFT JOIN hq_item_class e on e.id=a.class");
				sql.append(" LEFT JOIN organ f on f.id=c.store_id where 1=1");
				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and c.store_id in (" + condition.optString("shop_id") + " ) ");
				}
				if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
				{
					sql.append(" and a.chanel = 'BD06'  ");
				}
				else
				{
					sql.append(" and a.chanel = '" + condition.optString("channel") + "'  ");
				}
				sql.append(" and (d.whether_push_over is null or d.whether_push_over !='1')");
				if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id")))
				{
					sql.append(" and a.class =  " + condition.optString("class_id") + " ");
				}
				// sql.append(" where c.store_id in (40,183) and a.chanel='BD06' and d.whether_push_over in('1') and e.itemclass_name='盖饭'");
				sql.append(" GROUP BY  f.id ,f.org_full_name,d.whether_push_over,d.id,A.class,e.itemclass_name,d.last_send_class_name,a.menu_class_rank,a.chanel");
				List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
				if (list.size() > 0)
				{
					result.put("rows", list);
				}
				else
				{
					result.put("rows", "[]");
				}
				logger.info("[获取百度菜品分类列表]" + result);
				return result;
			}

			@Override
			public JSONObject dishPushBatch(String tenancyId, JSONObject data)
			{
//		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
				List<JSONObject> list = null;
				int success = 0, fail = 0;
				String send_operator = data.optString("send_operator");
				String send_time = data.optString("send_time");
				if (!data.containsKey("dishes"))
				{
					try
					{
						JSONObject result_obj = loadDishListNoPage(tenancyId, data);
						list = result_obj.optJSONArray("rows");
					}
					catch (Exception e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				else
				{

					list = (List<JSONObject>) GsonUtil.toT(data.opt("dishes").toString(), new TypeToken<List<JSONObject>>()
					{
					}.getType());
				}
				if (list.size() > 0)
				{
					for (JSONObject dish : list)
					{

						if ("".equals(dish.optString("photo1")))
						{
							dish.put("photo1", dish.optString("wxxt"));
						}

						if ("".equals(dish.optString("available_times_start")))
						{
							dish.put("available_times_start", "10:00");
						}

						if ("".equals(dish.optString("available_times_end")))
						{
							dish.put("available_times_end", "22:00");
						}

						if (dish.optInt("min_order_num")==0)
						{
							dish.put("min_order_num", "1");
						}
						if(!dish.containsKey("package_box_num")){
							dish.put("package_box_num", "1");
						}
						if ("".equals(dish.optString("channel")))
						{
							dish.put("channel", "BD06");
						}

						if ("".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", send_operator);
						}

						if ("".equals(dish.optString("send_time")))
						{
							dish.put("send_time", send_time);
						}
						dish.put("store_id", dish.optInt("store_id"));
						dish.put("item_code", dish.optInt("item_code"));
						dish.put("requestId", data.optString("requestId"));
						try
						{
							JSONObject condition = new JSONObject();
							condition.put("shop_id", dish.optInt("store_id"));
							condition.put("item_id", dish.optInt("item_id"));
							dish.put("priceList", getPrice(tenancyId, condition).opt("rows"));

							// dish.put("send","no");
//					ccBusniessLogBean.setRequestId(data.optString("requestId"));
//					ccBusniessLogBean.setTenancyId(tenancyId);
//					ccBusniessLogBean.setShopId(condition.optString("store_id"));
//					ccBusniessLogBean.setCategory("cc");
//					ccBusniessLogBean.setType("dish");
//					ccBusniessLogBean.setChannel(condition.optString("channel"));
//					ccBusniessLogBean.setChannelName(condition.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
//					ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.DishServiceImpl:dishPushBatch");
//					ccBusniessLogBean.setRequestBody(condition.toString());
//
//
//					ccBusniessLogBean.setCreateTime(new Date().getTime());
//					ccBusniessLogBean.setIsNormal("1");
//					ccBusniessLogBean.setIsThird("0");

							//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
//					if(condition.containsKey("dishes")){
//					  ccBusniessLogBean.setThirdId(condition.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
//					  ccBusniessLogBean.setTzxId(condition.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
//				      ccBusniessLogBean.setTzxName(condition.optJSONArray("dishes").getJSONObject(0).optString("item_name"));
//					}else{
//					  ccBusniessLogBean.setThirdId(condition.optString("third_item_id"));
//					  ccBusniessLogBean.setTzxId(condition.optString("item_id"));
//				      ccBusniessLogBean.setTzxName(condition.optString("item_name"));
//					}


							// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
//					ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());
//
//					ccBusniessLogBean.setRequestBody(dish.toString());


							JSONObject result = dishCreate(tenancyId, dish);

//					ccBusniessLogBean.setResponseBody(result.toString());

							if (result.optBoolean("success"))
							{
								result.put("success", true);
								success++;
								return result;

							}
							else
							{
								fail++;
								result.put("success", false);
								return result;
							}
						}
						catch (Exception e)
						{
							fail++;
							e.printStackTrace();
//					ccBusniessLogBean.setErrorBody(e.getMessage());
//					ccBusniessLogBean.setIsNormal("0");
						}
//				System.out.println("BD06.......servcie批量推送ID："+data.optString("requestId")+".........................");
//				KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
					}
				}

				JSONObject result = new JSONObject();

				if(fail>0){
					result.put("success", false);
				}else{
					result.put("success", true);
				}
				result.put("msg", "成功:" + success + "\t 失败:" + fail);
				return result;
			}

			@Override
			public JSONObject dishDeleteBatch(String tenancyId, JSONObject data) throws Exception
			{


				int success = 0, fail = 0;
				if (data != null)
				{

					List<JSONObject> list = null;

					if (!data.containsKey("dishes"))
					{
						try
						{
							data.put("oper", "delete");
							JSONObject result_obj = loadDishListNoPage(tenancyId, data);
							list = result_obj.optJSONArray("rows");
						}
						catch (Exception e)
						{
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					else
					{

						list = (List<JSONObject>) GsonUtil.toT(data.opt("dishes").toString(), new TypeToken<List<JSONObject>>()
						{
						}.getType());
					}
					if (list.size() > 0)
					{
						for (JSONObject dish : list)
						{

							try
							{
								JSONObject condition = new JSONObject();
								condition.put("store_id", dish.optInt("store_id"));
								condition.put("item_code", dish.optInt("item_id"));
								dish.put("store_id", dish.optInt("store_id"));
								dish.put("item_code", dish.optInt("item_code"));
								dish.put("requestId", data.optString("requestId"));

//						ccBusniessLogBean.setRequestId(data.optString("requestId"));
//						ccBusniessLogBean.setTenancyId(tenancyId);
//						ccBusniessLogBean.setShopId(condition.optString("store_id"));
//						ccBusniessLogBean.setCategory("cc");
//						ccBusniessLogBean.setType("dish");
//						ccBusniessLogBean.setChannel(condition.optString("channel"));
//						ccBusniessLogBean.setChannelName(condition.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
//						ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.DishServiceImpl:dishDeleteBatch");
//						ccBusniessLogBean.setRequestBody(condition.toString());


//						ccBusniessLogBean.setCreateTime(new Date().getTime());
//						ccBusniessLogBean.setIsNormal("1");
//						ccBusniessLogBean.setIsThird("0");

								//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
//						if(condition.containsKey("dishes")){
//						  ccBusniessLogBean.setThirdId(condition.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
//						  ccBusniessLogBean.setTzxId(condition.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
//					      ccBusniessLogBean.setTzxName(condition.optJSONArray("dishes").getJSONObject(0).optString("item_name"));
//						}else{
//						  ccBusniessLogBean.setThirdId(condition.optString("third_item_id"));
//						  ccBusniessLogBean.setTzxId(condition.optString("item_id"));
//					      ccBusniessLogBean.setTzxName(condition.optString("item_name"));
//						}


								// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
//						ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());
//
//						ccBusniessLogBean.setRequestBody(dish.toString());

								JSONObject result = dishDelete(tenancyId, dish);

//						ccBusniessLogBean.setResponseBody(result.toString());

								if (result.optBoolean("success"))
								{

									success++;
									result.put("success", true);
									return result;
								}
								else
								{
									fail++;
									result.put("success", false);
									return result;
								}
							}
							catch (Exception e)
							{
								fail++;
								e.printStackTrace();

//						ccBusniessLogBean.setErrorBody(e.getMessage());
//						ccBusniessLogBean.setIsNormal("0");
							}
//					System.out.println("BD06....service删除ID："+ccBusniessLogBean.getRequestId()+"...................");
//					KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
						}
					}
				}
				JSONObject result = new JSONObject();
				result.put("success", true);
				result.put("msg", "成功:" + success + "\t 失败:" + fail);
				return result;
			}

			@Override
			public JSONObject batchIsCollectCommission(String tenancyId, JSONObject data) throws Exception
			{
				int success = 0, fail = 0;
				String sql = "";
				if (data != null)
				{

					List<JSONObject> list = null;

					if (!data.containsKey("dishes"))
					{
						if (data.containsKey("shop_id") && !StringUtils.isEmpty(data.optString("shop_id")))
						{
							sql = "update cc_third_item_info set is_charge_commission='" + data.optString("is_charge_commission") + "' where shop_id in (" + data.optString("shop_id") + " ) and channel='" + data.optString("channel") + "'";

						}
					}
					else
					{

						list = (List<JSONObject>) GsonUtil.toT(data.opt("dishes").toString(), new TypeToken<List<JSONObject>>()
						{
						}.getType());
						if (list.size() > 0)
						{
							String ids = "";
							for (JSONObject dish : list)
							{
								if(dish.containsKey("id")&&!"".equals(dish.optString("id"))){
									ids += dish.optString("id") + ",";
								}
							}
							if(ids.length()>0){
								sql = "update cc_third_item_info set is_charge_commission='" + data.optString("is_charge_commission") + "' where id in (" + ids.substring(0, ids.length() - 1) + ")";
							}
						}
					}

				}
				this.dao.execute(tenancyId, sql);
				JSONObject result = new JSONObject();
				result.put("success", true);
				result.put("msg", "保存成功");
				logger.info("[批量更新商户菜品佣金]上传数据:"+data+"\n返回数据:"+result);
				return result;
			}

			@Override
			public JSONObject batchPushProjectTeam(String tenancyID, JSONObject obj) throws Exception
			{
				JSONObject group = (JSONObject) obj.opt("group");
				List<JSONObject> list = GsonUtil.toT(projectTeamManagementService.customize(tenancyID, 2, JSONObject.fromObject("{\"id\":" + group.optInt("id") + ",\"type\":2}")), new TypeToken<List<JSONObject>>(){}.getType());
				group.put("item_list", list);
				group.put("tenancy_id", tenancyID);
				group.put("item_group_id", group.optInt("id"));
				group.put("last_operator",obj.optString("last_operator"));
				group.put("last_updatetime",obj.optString("last_updatetime"));

				Data data = new Data();
				if(group.optInt("whether_push_over")==0)
				{
					data.setOper(Oper.create);
					data.setType(Type.dish_group_create);
				}
				else
				{
					data.setOper(Oper.update);
					data.setType(Type.dish_group_update);
				}
				data.setBody(group);
				String res = doHttpPost(Constant.YICHI_API_URL,data);
				data = objMapper.readValue(res,Data.class);
				JSONObject result = JSONObject.fromObject(data.getBody());
				logger.info("[新增易吃平台菜品项目组]上传数据:" + data.getBody() + ",\n返回数据:"+JSONObject.fromObject(res));
				JSONObject update = JSONObject.fromObject("{}");
				if(result.containsKey("errno") && result.optInt("errno") == 0)
				{
					update.put("whether_push_over","1");
					result.put("success",true);
				}
				else
				{
					if(group.optInt("whether_push_over")==0)
					{
						update.put("whether_push_over","0");
					}
					else
					{
						update.put("whether_push_over", "2");
					}
					result.put("success",false);
				}
				update.put("id",group.optInt("id"));
				update.put("last_operator",obj.optString("last_operator"));
				update.put("last_updatetime",obj.optString("last_updatetime"));
				this.dao.updateIgnorCase(tenancyID, "hq_item_group", update);
				return result;
			}

			@Override
			public JSONObject delProjectTeam(String tenancyID, JSONObject obj) throws Exception
			{
				JSONObject body = new JSONObject();
				body.put("tenancy_id", tenancyID);
				body.put("item_group_code", obj.optString("item_group_code"));
				Data data = new Data();
				data.setOper(Oper.delete);
				data.setType(Type.dish_group_delete);
				data.setBody(body);
				String res = doHttpPost(Constant.YICHI_API_URL,data);
				data = objMapper.readValue(res,Data.class);
				JSONObject result = JSONObject.fromObject(data.getBody());
				logger.info("[删除易吃平台菜品项目组]上传数据:" + data.getBody() + ",\n返回数据:"+JSONObject.fromObject(res));
				if(result.containsKey("errno") && result.optInt("errno") == 0)
				{
					body.put("whether_push_over", 0);
					body.put("id", obj.optInt("id"));
					this.dao.updateIgnorCase(tenancyID, "hq_item_group", body);
				}
				return result;
			}

		private static String doHttpPost(String url,Data data) throws UnsupportedEncodingException, IOException, ClientProtocolException
		{
			DefaultHttpClient httpClient = new DefaultHttpClient();
			httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 20000);
			httpClient.getParams().setParameter(CoreProtocolPNames.HTTP_CONTENT_CHARSET, "UTF-8");
			httpClient.getParams().setParameter(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded");
			HttpPost httpPost = new HttpPost(url);
			StringEntity sentity = new StringEntity(JSONObject.fromObject(data).toString(),"UTF-8");//解决中文乱码问题
			sentity.setContentEncoding("UTF-8");
			sentity.setContentType("application/json");
			httpPost.setEntity(sentity);
			HttpResponse r = httpClient.execute(httpPost);
			HttpEntity entity = r.getEntity();
			String result = EntityUtils.toString(entity, "UTF-8");
			return result;
		}
		//查询已推送到第三方的shopIDs
		private String getAlreadyPushShopIDs(String tenantId,String channel) throws Exception{
		String sql="SELECT shop_id FROM cc_third_organ_info WHERE tenant_id='"+tenantId+"' AND channel='"+channel+"'";
		List<JSONObject> list=this.dao.query4Json(tenantId, sql);
		String shopIDs="";
		for(JSONObject jsonObject:list){
			shopIDs+=jsonObject.optInt("shop_id")+",";
		}
		if(shopIDs.length()>0){
			shopIDs=shopIDs.substring(0, shopIDs.length()-1);
		}
		return shopIDs;
	}

		//changhui 2018-1-23 调用百度3.0接口数据转换 start
		private Map<String,Object> getDishCreateUpdateMap(DishCreateUpdate dishUpdate){
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("shop_id", dishUpdate.getShop_id());
		paramMap.put("dish_id", dishUpdate.getDish_id());
		paramMap.put("name", dishUpdate.getName());
		//paramMap.put("upc", dishUpdate.getUpc());
		paramMap.put("price", dishUpdate.getPrice());
		paramMap.put("pic", dishUpdate.getPic());
		paramMap.put("min_order_num", dishUpdate.getMin_order_num());
		paramMap.put("package_box_num", dishUpdate.getPackage_box_num());
		paramMap.put("description", dishUpdate.getDescription());
		paramMap.put("available_times", dishUpdate.getAvailable_times());
		//paramMap.put("threshold", dishUpdate.getThreshold());
		paramMap.put("category", dishUpdate.getCategory());
		paramMap.put("norms", dishUpdate.getNorms());
		paramMap.put("stock", 20000);
		return paramMap;
	}

		//调用百度3.0接口数据转换
		private Map<String,Object> getDishOnOfflineDelMap(DishOnOfflineDel dishOnOfflineDel){
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("dish_id", dishOnOfflineDel.getDish_id());
		paramMap.put("shop_id", dishOnOfflineDel.getShop_id());
		return paramMap;
	}

		//调用百度3.0接口数据转换
		private Map<String,Object> getDishCategoryCreateMap(DishCategoryCreate dishCategoryCreate) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("shop_id", dishCategoryCreate.getShop_id());
		paramMap.put("name", dishCategoryCreate.getName());
		paramMap.put("rank", dishCategoryCreate.getRank());
		return paramMap;
	}

		//调用百度3.0接口数据转换
		private Map<String,Object> getDishCategoryUpdateMap(DishCategoryUpdate dishCategoryUpdate) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("shop_id", dishCategoryUpdate.getShop_id());
		paramMap.put("old_name", dishCategoryUpdate.getOld_name());
		paramMap.put("name", dishCategoryUpdate.getName());
		paramMap.put("rank", dishCategoryUpdate.getRank());
		return paramMap;
	}

		//end
	}
