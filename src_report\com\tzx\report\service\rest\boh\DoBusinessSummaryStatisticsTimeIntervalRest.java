package com.tzx.report.service.rest.boh;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.boh.DoBusinessSummaryStatisticsTimeIntervalService;
import com.tzx.report.common.util.ConditionUtils;


/**
 * 营业汇总统计时段报表
 *
 */
@Controller("DoBusinessSummaryStatisticsTimeIntervalRest")
@RequestMapping("/report/DoBusinessSummaryStatisticsTimeIntervalRest")
public class DoBusinessSummaryStatisticsTimeIntervalRest
{
	
	@Resource(name = DoBusinessSummaryStatisticsTimeIntervalService.NAME)
	private DoBusinessSummaryStatisticsTimeIntervalService doBusinessSummaryStatisticsTimeIntervalService;
	
	@Resource(name = "conditionUtils")
	ConditionUtils conditionUtils;

    /**
     * 获取主表内容
     */
	@RequestMapping(value = "/queryBusinessTime")
	@ApiOperation(value = "营业汇总时段占比",consumes= "multipart/form-data" ,httpMethod="POST",notes = "营业汇总时段占比")
	@ApiImplicitParams({
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "report_date_begin",value = "开始日期",required = true,defaultValue="2017-01-01"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "report_date_end",value = "结束日期",required = true,defaultValue="2017-12-31"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "store_id",value = "交易门店",defaultValue="0,1"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time1_start",value = "时段1开始",required = true,defaultValue="'00:00'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time1_end",value = "时段1结束",required = true,defaultValue="'01:00'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time2_start",value = "时段2开始",required = true,defaultValue="'02:00'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time2_end",value = "时段2结束",required = true,defaultValue="'03:00'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time3_start",value = "时段3开始",required = true,defaultValue="'04:00'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time3_end",value = "时段3结束",required = true,defaultValue="'05:00'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time4_start",value = "时段4开始",required = true,defaultValue="'06:00'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time4_end",value = "时段4结束",required = true,defaultValue="'07:00'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time5_start",value = "时段5开始",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time5_end",value = "时段5结束",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time6_start",value = "时段6开始",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time6_end",value = "时段6结束",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time7_start",value = "时段7开始",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time7_end",value = "时段7结束",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time8_start",value = "时段8开始",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "time8_end",value = "时段8结束",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "exportdataexpr",value = "导出",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "hierarchytype",value = "查询层级",required = true,defaultValue="1"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "queryWay",value = "按日期1 按机构2",required = true,defaultValue="1"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "page",value = "页码",required = true,defaultValue="1"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "rows",value = "每页行数",required = true,defaultValue="10"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "sort",value = "排序字段",defaultValue="report_date"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "sale_mode",value = "销售类型",defaultValue="''")
	})
	public void queryBusinessTime(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (!Objects.equals(map.get(key)[0], ""))
				{
					p.put(key, map.get(key)[0]);
				}
			}
			String storeString = "store_id";
			if(p.optString(storeString).length()==0 ||
					"0".equals(p.optString(storeString)) ||
					"'0'".equals(p.optString(storeString)) ||
					"99999999".equals(p.optString(storeString)) ||
					"''".equals(p.optString(storeString)) ||
					"'99999999'".equals(p.optString(storeString)) 
					){
				if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}else{
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}
			
			result = doBusinessSummaryStatisticsTimeIntervalService.queryBusinessTime((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception ignored)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}

    /**
     * 获取二级内容
     */
    @RequestMapping(value = "/querySecondLevel")
    public void querySecondLevel(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        String result = "";
        try
        {
            JSONObject p = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                if (!Objects.equals(map.get(key)[0], ""))
                {
                    p.put(key, map.get(key)[0]);
                }
            }
            String storeString = "store_id";
            if(p.optString(storeString).length()==0 ||
            		"0".equals(p.optString(storeString)) ||
            		"'0'".equals(p.optString(storeString)) ||
            		"99999999".equals(p.optString(storeString)) ||
            		"''".equals(p.optString(storeString)) ||
            		"'99999999'".equals(p.optString(storeString)) 
            		){
            	// 判断当前是门店还是总部
            	if(session.getAttribute("organ_id").equals("0")) {
            		//取所有门店
            		p.element(storeString, session.getAttribute("user_organ_codes_group"));
            	}else {
            		// 取门店
            		p.element(storeString, session.getAttribute("organ_id"));
            	}
            }
            

            result = doBusinessSummaryStatisticsTimeIntervalService.querySecondLevel((String) session.getAttribute("tenentid"), p).toString();
        }
        catch (Exception e)
        {
            result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
            e.printStackTrace();
        }
        finally
        {
            try
            {
                out = response.getWriter();

                out.print(result);
                out.flush();
                out.close();
            }
            catch (Exception ignored)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }

    }

}
