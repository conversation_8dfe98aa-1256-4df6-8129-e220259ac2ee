package com.tzx.cc.baidu.bo;

import java.util.List;

import com.tzx.framework.common.exception.SystemException;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * 
 * 商户相关接口
 *
 */
public interface  ShopService{
	
	public final String NAME="com.tzx.cc.baidu.bo.ShopService";
	
	
	/**获取商户信息
	 * @param tenantId
	 * @param shopId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	JSONObject getShopInitInfo(String tenantId, String shopId, String channel) throws Exception;

	/**查看商户列表
	 * @param tenantId
	 * @param shopId
	 * @param channel
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject shopList(String tenantId, JSONObject params) throws Exception;

	/**创建/修改商户
	 * @param tenantId
	 * @param params
	 * @param result
	 * @return
	 * @throws Exception
	 */
	JSONObject shopCreateOrUpdate(String tenantId, JSONObject params) throws Exception;

	/**更新商户状态
	 * @param tenantId
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	JSONObject shopSetStatus(String tenantId, JSONObject params) throws Exception;

	/**订单操作
	 * @param tenantId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	JSONObject orderOper(String tenantId, JSONObject params) throws Exception;

	/**获取餐盒费信息
	 * @param tenantId
	 * @param shopId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> initMealsInfo(String tenantId, String shopId, String channel) throws Exception;
	
	/**查看订单状态
	 * @return
	 * @throws Exception
	 */
	JSONObject orderStatusGet(String tenantId,JSONObject params) throws Exception;

	/**获取商户分类
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject getShopCategory(String tenantId, JSONObject params) throws Exception;

	/**保存预计送达时间
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject saveDeliveryTime(String tenantId, JSONObject params) throws Exception;

	/**保存配送范围
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject saveDeliveryRegion(String tenantId, JSONObject params) throws Exception;

	/**获取本地菜品分类列表
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject getLocalDishCategoryList(String tenantId, JSONObject params) throws Exception;

	/**保存菜品分类（创建/更新）
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject saveDishCategory(String tenantId, JSONObject params) throws Exception;
	
	/**保存菜品信息（创建/更新）
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject saveDish(String tenantId, JSONObject params) throws Exception;
	
	/**验证所选门店或选择的菜品是否与第三方平台菜品信息已都做映射
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject platformSaaSDisheIsMapped(String tenantId, JSONObject params) throws Exception;
	
	/**更新菜品信息（上线/下线）
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject updateDishState(String tenantId, JSONObject params) throws Exception;
	
	/**获取本地菜品列表
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject getLocalDishList(String tenantId,JSONObject params) throws Exception;
	

	/**获取本地价格体系
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject getLocalPriceSystem(String tenantId,JSONObject params) throws Exception;

	/**删除菜品
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject deleteDish(String tenantId, JSONObject params) throws Exception;

	/**上传菜品图片（美团）
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject imageUpload(String tenantId, JSONObject params) throws Exception;

	/**获取本地菜品列表不分页
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject getLocalDishNoPageList(String tenantId, JSONObject params) throws Exception;
	/**
	 * 获取菜品分类不分页
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject getLocalDishCategoryListNoPage(String tenantId, JSONObject params) throws Exception;
	/**
	 * 复制菜品信息
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public Boolean saveCopyDishInfo(String tenancyID, JSONObject obj) throws Exception,SystemException;
	
	/**
	 * 复制菜品类别信息
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public Boolean saveCopyDishCategoryInfo(String tenancyID, JSONObject obj) throws Exception,SystemException;
	/**获取美团团购账号信息
	 * @param tenantId
	 * @return
	 * @throws Exception
	 */
	JSONObject loadXmdAccountInfo(String tenantId) throws Exception;
	/**获取商户账号信息
	 * @param tenantId
	 * @param shopId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	JSONObject loadAccountInfo(String tenantId) throws Exception;

    /**保存美团团购账号信息
     * @param tenantId
     * @return
     * @throws Exception
     */
    Boolean saveXmdAccountInfo(String tenantId,JSONObject obj) throws Exception;
	
	/**保存商户账号信息
	 * @param tenantId
	 * @param shopId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	Boolean saveAccountInfo(String tenantId,JSONObject obj) throws Exception;

	public JSONObject deleteDishCategory(String tenantId, JSONObject params) throws Exception;

	
	String getEleRestaurantIDs(String tenantId, JSONObject params)throws Exception;

    /** 保存新美大apptoken
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject saveXmdCallBackToken(JSONObject params)throws Exception;

    /** 解除新美大绑定保存
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject saveXmdReleaseCallBack(JSONObject params)throws Exception;
    
    /**
     * 根据券码查询团购券详情
     *
     * @param tenantId
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject couponQueryById(String tenantId, String storeId,JSONObject params) throws Exception;

    /**
     * 新美大的验券历史
     *
     * @param tenantId
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject couponQueryListByDate(String tenantId,String storeId, JSONObject params) throws Exception;

    /**
     *  根据券码获取对应订单下，单次可验证的最大、最小张数
     *
     * @param tenantId
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject couponPrepare(String tenantId,String storeId, JSONObject params) throws Exception;

    /**
     *  根据券码一次验证掉同一订单下的若干张券
     *
     * @param tenantId
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject couponConsume(String tenantId, String storeId,JSONObject params) throws Exception;

    /**
     *  查询与撤销已验证券码（验证20分钟内券码）
     *
     * @param tenantId
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject couponCancel(String tenantId,String storeId, JSONObject params) throws Exception;
    
    /**
     * 券码对账接口
     * @param tenantId
     * @param storeId
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject couponTradeDetail(String tenantId,String storeId, JSONObject params)throws Exception;
    
    /**
     *  验证开店设置并推送是否已经存在你数据
     *
     * @param tenantId
     * @param params
     * @return
     * @throws Exception
     */
    List<JSONObject> getThrirdShopInfo(String tenantId,JSONObject params) throws Exception;


	/**
	 * @param tenantId
	 * @param param
	 * @throws Exception
	 */
    List<JSONObject> query4suweiNoCanT(String tenantId, JSONObject param) throws Exception;
    
    
    /**饿了么绑定商户
	 * @param tenantId
	 * @param params
	 * @param result
	 * @return
	 * @throws Exception
	 */
	JSONObject shopBind(String tenantId, JSONObject params) throws Exception;
	
	/**饿了么2.0商户解除绑定
	 * @param tenantId
	 * @param params
	 * @param result
	 * @return
	 * @throws Exception
	 */
	JSONObject shopUnBind(String tenantId, JSONObject params) throws Exception;
	/*
	 * //根据门店查询已经绑定的菜品分类
	 */
	List<JSONObject> getDishClassIsMapList(String tenantId, JSONObject params)
			throws Exception;
	/*
	 * //根据门店查询已绑定的菜品信息
	 */
	List<JSONObject> getDishIsMapList(String tenantId, JSONObject params)
			throws Exception;

	//获取平台菜品分类自动映射
	JSONObject saveDishCategoryAutomaticMap(String tenantId, JSONObject params) throws Exception;
	
	//获取平台菜品自动映射
	JSONObject saveDishAutomaticMap(String tenantId, JSONObject params) throws Exception;
	//获取平台未映射菜品
	public JSONObject getPlatNoMapDishList(JSONObject params)throws Exception;

	//建立菜品分类第三方数据与本地数据映射关系
	JSONObject saveDishCategoryThirdClassID(String tenantId, JSONObject params)throws Exception;
	
	//建立菜品第三方数据与本地数据映射关系
	JSONObject saveDishThirdItemId(String tenantId, JSONObject params)throws Exception;
	
	//设置饿了么渠道接单模式
	JSONObject postOrderModel(String tenantId, JSONObject param)throws Exception;
	
	//修改或保存菜品基本信息
	JSONObject saveOrUpdateDishInfo(String tenantId, JSONObject params)throws Exception;

	JSONObject setDishCategoryPosition(String tenantId, JSONObject params)throws Exception;

	//饿了么验证第三方店铺ID是否已经与已有的机构门店建立关系
	JSONObject validateThirdShopIDisEffective(String tenantId, JSONObject param);
	
	//回复店铺评论
	public JSONObject  addReply(String tenantId,String[] idArr,String content,JSONObject param);
	/**
	 * 获取评论统计信息
	 * @param tenantId
	 * @param timeBegin
	 * @param timeEnd
	 * @param channel
	 * @return
	 */
	public JSONObject getCommentStat(String tenantId,String timeBegin,String timeEnd,int channel,String organCode);
	
	/**
	 *     获取门店的第三方店状态信息
	 * @param tenantId
	 * @param orgCode
	 * @param condition
	 * @return
	 */
	public JSONObject getThirdShop(String tenantId,JSONObject condition);
	/**
	 * 获取单个店铺的第三方外卖列表
	 * @param tenantId
	 * @param shopId
	 * @return
	 */
	public JSONArray getThirdShopByShopId(String tenantId,int shopId);
	/**
	 * 获取美团店铺状态
	 * @param tenantId
	 * @param shop_id
	 * @return
	 */
	public Integer[]  grabMeituanStatus(String tenantId,String shop_id);
	/**
	 * 获取百度店铺状态
	 * @param tenantId
	 * @param shop_id
	 * @return
	 */
	public Integer[]  grabBaiduStatus(String tenantId,String shop_id);
	/**
	 * 获取饿了么店铺状态
	 * @param tenantId
	 * @param shop_id
	 * @return
	 */
	public Integer[]  grabEleStatus(String tenantId,String shop_id);
	
	
	
	/**
	 * 查询店铺评论信息列表
	 */
	JSONObject getCommentsList(JSONObject params) throws Exception;

	/**
	 * 更新三方店铺状态
	 * @param shopId
	 * @param channel
	 * @param status
	 * @return
	 */
	public int  storeShopStatus(String tenentid,String shopId,String channel,String status);

	/**
	 * 拉去新美大商户信息更新到本地
	 * @param obj
	 * @return
	 */
	JSONObject initShopInfoFromXMD(JSONObject obj);
	
	  /** 新美大外卖更改菜品映射信息状态 changhui 2017-11-20
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject updateXmdWhetherPushOver(JSONObject params, JSONArray dishList) throws Exception;
    
    /**
	 * 是否达到菜品映射要求，判断菜品数量是否一致
	 *  changhui add 2017-11-23
	 * @param params
	 * @return
	 */
    String  isAchieveMappingCondition(JSONObject params, JSONArray dishList) throws Exception;
    
    /** 保存菜品类别及菜品的映射关系 changhui 2017-12-15
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject saveEleDishAndDishCategoryMapping(JSONObject params, JSONArray dishList) throws Exception;
    
    /** 饿了么外卖更改菜品映射信息状态 changhui 2017-11-20
     * @param params
     * @return
     * @throws Exception
     */
    JSONObject updateWhetherPushOver(JSONObject params, JSONArray dishList) throws Exception;
    
    /** 获取饿了么菜品的信息 changhui 2017-12-20
     * @param params
     * @return
     * @throws Exception
     */
    JSONArray getEleDishInfoList(String tenantId, JSONObject params) throws Exception;

	JSONObject getSysParameterBySystemName(String tenantId, JSONObject jsonObj);

	/**
	 * 通过商户ID获取百度平台商户ID
	 * @param tenantId
	 * @param obj
	 * @return
	 */
	JSONObject getShopCode(String tenantId, JSONObject obj);

	/** 获取新美大外卖菜品的信息 changhui 2018-1-19
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONArray getXmdwmDishInfoList(String tenantId, String shopId, JSONObject params) throws Exception;

	/** 保存新美大外卖菜品类别及菜品的映射关系 changhui 2018-1-19
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject saveXmdwmDishAndDishCategoryMapping(JSONObject params, JSONArray dishList) throws Exception;

	/** 保存新美大外卖菜品类别及菜品的映射关系 changhui 2018-1-19
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONArray getXmdwmDishInfos(String tenantId, JSONObject params) throws Exception;
}
