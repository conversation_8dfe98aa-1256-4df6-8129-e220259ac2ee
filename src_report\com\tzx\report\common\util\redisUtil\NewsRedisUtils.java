package com.tzx.report.common.util.redisUtil;

import org.apache.log4j.Logger;
import org.springframework.data.redis.core.RedisTemplate;

import com.tzx.framework.common.util.SpringConext;

/**
 * Created by XUGY on 2017-07-17.
 */
public class NewsRedisUtils {
	/**
	 * 日志对象
	 */
	private static final Logger logger = Logger
			.getLogger(NewsRedisUtils.class);

	/**
	 * 获取redis模板对象
	 * 
	 * @return
	 */
	public static RedisTemplate getRedisTemplete() {
		RedisTemplate redisTemplate = (RedisTemplate) SpringConext
				.getApplicationContext().getBean("saasRedisTemplate");
		return redisTemplate;
	}

}
