package com.tzx.report.bo.payment.impl;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.UUIDUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.payment.service.MeidaPaymentOrderQueryService;
import com.tzx.report.common.util.ReportExportUtils;
import net.sf.json.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 */
@Service(MeidaPaymentOrderQueryService.NAME)
public class MeidaPaymentOrderQueryServiceImpl implements MeidaPaymentOrderQueryService
{
	private static final Logger log	= Logger.getLogger(MeidaPaymentOrderQueryServiceImpl.class);
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;


	@Override
	public List<JSONObject> queryOrganTrees(String tenancyId,String storeIds) {
		StringBuffer sb = new StringBuffer();
		sb.append("select oo.id,oo.organ_code,oo.org_full_name,zz.*,concat(organ_code,org_full_name,'  [',zz.merchant_id,']') as text " +
				"from (select store_id,merchant_id,COALESCE(max(service_provider),'0') as service_provider from sys_payment_account_config where valid_state='1' and payment_channel='6' and merchant_id is not null GROUP BY store_id,merchant_id " +
				") zz  LEFT JOIN organ oo on zz.store_id=oo.id where oo.valid_state='1' ");
		if(StringUtils.isNotBlank(storeIds)){
			sb.append(" and oo.id in ("+storeIds+") ");
		}
		sb.append("ORDER BY organ_code asc");
		try {
			List<JSONObject> list = dao.query4Json(tenancyId,sb.toString());
			return list;
		} catch (Exception e) {
			e.printStackTrace();
		}

		return new ArrayList<>();
	}

	@Override
	public com.alibaba.fastjson.JSONObject queryOrderLists(String tenancyId,com.alibaba.fastjson.JSONObject jsonObject) {
		com.alibaba.fastjson.JSONObject returnJson = new com.alibaba.fastjson.JSONObject();

		try {
			String startTime = jsonObject.getString("startDate");
			String endTime = jsonObject.getString("endDate");
			String merchantId = jsonObject.getString("merchantId");//"*********";
			String channel = jsonObject.getString("selectType");
			if(StringUtils.isBlank(channel)){
				channel = "wx_barcode_pay,ali_barcode_pay,wx_scan_pay,ali_scan_pay,mtdp_scan_pay";
			}
			String orderStatuses = jsonObject.getString("orderStatuses");
			List<String> orderStatusesList = new ArrayList<>();
			if(StringUtils.isNotBlank(orderStatuses)){
				orderStatusesList.add(orderStatuses);
			}

			String keyValue = Constant.getSystemMap().get("payment_xmd_key");
			String appid = Constant.getSystemMap().get("app_id");
			// 获取其他服务商的新美大app_id app_key  service_provider 0 代表天子星 其他>0
			int service_provider = 0;
			if(jsonObject.containsKey("service_provider")){
				service_provider = jsonObject.getInteger("service_provider");
			}
			if(service_provider>0)
			{
				List<JSONObject> listProvider = dao.query4Json(tenancyId,"select service_app_id,service_app_key from sys_payment_service_provider where type='"+service_provider+"'");
				if(listProvider.size()>0)
				{
					JSONObject providerJo = listProvider.get(0);
					keyValue = providerJo.getString("service_app_key");
					appid = providerJo.getString("service_app_id");
				}
			}


			String url = Constant.getSystemMap().get("payment_xmd_url")+"/api/query/payList";
			String page = jsonObject.getString("page");
			String rows = jsonObject.getString("rows");
			if(StringUtils.isBlank(page)){
				page = "1";
			}
			if(StringUtils.isBlank(rows)){
				rows = "10";
			}

			SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String resultJson = queryPayList(url,appid,merchantId,channel,sdf2.parse(startTime).getTime(),sdf2.parse(endTime).getTime(),Integer.parseInt(page)-1,
					Integer.parseInt(rows),keyValue,orderStatusesList);
			log.info("meida/api/query/payList,返回值："+resultJson);
			com.alibaba.fastjson.JSONObject resultJsonObj = com.alibaba.fastjson.JSONObject.parseObject(resultJson);
			String status = resultJsonObj.getString("status");
			int total = 0;
			List<com.alibaba.fastjson.JSONObject> list = new ArrayList<>();
			double countTotalFee = 0.00;
			double countDiscount = 0.00;

			if ("SUCCESS".equals(status)) {
				/*StringBuilder sb = new StringBuilder("select distinct (CASE aa.store_id when 0 then '总部' else oo.org_full_name end) as org_full_name from (select  spac.partner,spac.store_id from sys_payment_account_config spac  where spac.merchant_id ='"+merchantId+"' and spac.valid_state='1' ) aa LEFT JOIN organ oo on aa.store_id=oo.id");
				List<JSONObject> storeList = dao.query4Json(tenancyId,sb.toString());
				String storeName= "";
				for(int i=0;i<storeList.size();i++){
					JSONObject object = storeList.get(i);
					storeName += object.getString("org_full_name")+"/";
				}
				if(StringUtils.isNotBlank(storeName)){
					storeName = storeName.substring(0,storeName.length()-1);
				}*/

				com.alibaba.fastjson.JSONObject data = resultJsonObj.getJSONObject("data");
				total = data.getInteger("totalCount");
				com.alibaba.fastjson.JSONArray xmdOrderDetialarray = data.getJSONArray("orderDetails");
				for(int i=0;i<xmdOrderDetialarray.size();i++){
					com.alibaba.fastjson.JSONObject obj = xmdOrderDetialarray.getJSONObject(i);
					String outTradeNo = obj.getString("outTradeNo");
					String sql = "SELECT  substr(bill_num, 1,CASE WHEN position('_' in bill_num)>0 THEN (position('_' in bill_num)-1) WHEN position('@' in bill_num)>0 THEN (position('@' in bill_num)-1) ELSE length(bill_num) END)  as bill_num,(case ppo.store_id when '0' then '总部' else o.org_full_name end) as store_name FROM pos_payment_order ppo left join organ o on ppo.store_id=o.id::TEXT WHERE out_trade_no='"+outTradeNo+"' limit 1";
					List<JSONObject> resultlist = dao.query4Json(tenancyId,sql);
					if(resultlist.size()>0){
						obj.put("billNum",resultlist.get(0).optString("bill_num"));
						obj.put("storeName",resultlist.get(0).optString("store_name"));
					}else {
						obj.put("billNum","");
						obj.put("storeName","");
					}
					String orderStatus = obj.getString("status");

					if(StringUtils.isNotBlank(obj.getString("totalFee"))){
						double totalfee = Double.parseDouble(obj.getString("totalFee"));
						if(StringUtils.equals("8",orderStatus)){
							countTotalFee +=totalfee;
						}
					}
					if(StringUtils.isNotBlank(obj.getString("discount"))){
						double discount = Double.parseDouble(obj.getString("discount"));
						if(StringUtils.equals("8",orderStatus)) {
							countDiscount += discount;
						}
					}
				}
				list = xmdOrderDetialarray.toJavaList(com.alibaba.fastjson.JSONObject.class);
			}
			List<com.alibaba.fastjson.JSONObject> footerList =new ArrayList();
			com.alibaba.fastjson.JSONObject jsonCount = new com.alibaba.fastjson.JSONObject() ;
			jsonCount.put("totalFee", countTotalFee);
			jsonCount.put("discount",countDiscount);
			jsonCount.put("discount",countDiscount);
			footerList.add(jsonCount);
			returnJson.put("footer", footerList);
			returnJson.put("rows",list);
			returnJson.put("total",total);

		}catch (Exception e){
			e.printStackTrace();
		}
		return returnJson;
	}

	@Override
	public HSSFWorkbook export(String tenancyId, com.alibaba.fastjson.JSONObject p, HSSFWorkbook workBook) throws Exception{
		Integer rowNum=1;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",0);
		Integer stratIndex = 0 ;
		com.alibaba.fastjson.JSONObject findResult= queryOrderLists(tenancyId, p);
		com.alibaba.fastjson.JSONArray list1 = findResult.getJSONArray("rows");
		JSONObject out1Result =null;
		//创建sheet 表格   同时还可以设置名字!
		HSSFSheet sheet1=workBook.createSheet("美大实时订单");
		String [] listTitleName = {"交易机构","POIID","账单号","交易时间","支付类型","交易金额","优惠金额","交易类型","外部订单号"};
		String [] dataName ={"storeName","merchantId","billNum","orderTime","channel","totalFee","discount","statusName","outTradeNo"};
		String [] dataType ={"String","String","String","String","String","0.00","0.00","String","String"};

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if(list1.size()>0){
			for(int i=0;i<list1.size();i++) {
				com.alibaba.fastjson.JSONObject json1 = list1.getJSONObject(i);
				String orderTime = json1.getString("orderTime");
                if(StringUtils.isNotBlank(orderTime)){
                    Date d = new Date(Long.parseLong(orderTime));
                    json1.put("orderTime",sdf.format(d));
                }
				String channel = json1.getString("channel");
				if("wx_barcode_pay".equals(channel)){
					channel = "微信条码支付";
				}else if("ali_barcode_pay".equals(channel)){
					channel = "支付宝条码支付";
				}else if("wx_scan_pay".equals(channel)){
					channel = "微信扫码支付";
				}else if("ali_scan_pay".equals(channel)){
					channel = "支付宝扫码支付";
				}
				json1.put("channel",channel);

				String totalFee = json1.getString("totalFee");
				if(StringUtils.isNotBlank(totalFee)){
					json1.put("totalFee",new BigDecimal(totalFee).divide(BigDecimal.valueOf(100),2,BigDecimal.ROUND_HALF_UP));
				}
				String discount = json1.getString("discount");
				if(StringUtils.isNotBlank(discount)){
					json1.put("discount",new BigDecimal(discount).divide(BigDecimal.valueOf(100),2,BigDecimal.ROUND_HALF_UP));
				}
				JSONObject js = JSONObject.fromObject(json1);
				out1Result = ReportExportUtils.out1(js,workBook,sheet1,listTitleName,dataName,dataType,paramData);
				stratIndex =out1Result.optInt("rowNum");
				paramData.put("rowNum", out1Result.opt("rowNum"));
				paramData.put("jin", out1Result.optInt("jin"));
			}
		}

		sheet1.groupRow(1,out1Result.optInt("rowNum"));
		sheet1.setRowSumsBelow(false);
		sheet1.setRowSumsRight(false);
		return workBook;
	}


	/**
	 *
	 * @param url  新美大订单查询接口/api/query/payList
	 * @param appid 接入方APPID
	 * @param merchantId 商户门店ID
	 * @param startTime  开始时间 时间戳
	 * @param endTime  结束时间 时间戳
	 * @param pageNumber 当前页数
	 * @param pageSize  每页条数
	 * @param keyValue 接入方密钥
	 * @return
	 */
	public  String queryPayList(String url,String appid,String merchantId ,String channel,
								Long startTime,Long endTime,Integer pageNumber,Integer pageSize,String keyValue,List<String> orderStatuses)
	{
		SortedMap<String, Object> treeMap = new TreeMap<>();
		treeMap.put("appId", appid);
		treeMap.put("merchantId", merchantId);
		treeMap.put("startTime", startTime);
		treeMap.put("endTime", endTime);
		treeMap.put("orderStatuses",new ArrayList<String>());
		treeMap.put("pageNumber", pageNumber);
		treeMap.put("pageSize",pageSize);
		if(StringUtils.isNotBlank(channel)){
			treeMap.put("channel",channel);
		}
		if(orderStatuses!=null && orderStatuses.size()>0){
			treeMap.put("orderStatuses",orderStatuses);
		}
		treeMap.put("random", UUIDUtil.generateGUID());
		String sign = createSign(keyValue, treeMap);
		treeMap.put("sign", sign);
		String param = JSONObject.fromObject(treeMap).toString();
		log.info("meida/api/query/payList：url:"+url+";参数"+param);
		return HttpUtil.sendPostRequest(url,param);
	}

	/**
	 * sha256
	 * */
	public static String sha256Hex(String data){
		return DigestUtils.sha256Hex(data);
	}

	/**
	 * 新美大加密sign
	 * @return
	 */
	public static String  createSign(String key, SortedMap<String,Object> map){
		StringBuffer sb = new StringBuffer();
		Set set = map.entrySet();
		Iterator it = set.iterator();
		while (it.hasNext()){
			Map.Entry entry = (Map.Entry)it.next();
			String k = (String) entry.getKey();
			Object val = (Object) entry.getValue();
			if(StringUtils.isNotBlank(k) && null!=val){
				sb.append(k+"="+val+"&");
			}
		}

		sb.append("key="+key);
		String sign = sha256Hex(sb.toString());
		return sign;
	}
}
