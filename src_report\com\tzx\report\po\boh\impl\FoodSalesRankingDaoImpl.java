package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.FoodSalesRankingDao;

@Repository(FoodSalesRankingDao.NAME)
public class FoodSalesRankingDaoImpl implements FoodSalesRankingDao{
 
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	
	@Resource 
	ConditionUtils conditionUtils;
	
	
//	private final String sqlTypeCase1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGHZCPXLL1'";
//	private final String sqlTypeCase2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGHZCPXLL2'";
//	private final String reportCount = "select sql from saas_report_engine where  report_num ='SAAS_BI_2016_14' and sql_type='JGHZCPXLL0'";
//	
//	private final String sqlTypeCase3="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGMXCPXLL1'";
//	private final String sqlTypeCase4="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGMXCPXLL2'";
//	private final String sqlTypeCase5="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGMXCPXLL3'";
//	private final String reportCount1="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGMXCPXLL0'";
//	
//	private final String sqlTypeCase6="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGHZCPMXL1'";
//	private final String reportCount2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGHZCPMXL0'";
//	
//	private final String sqlTypeCase7="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGMXCPMXL1'";
//	private final String sqlTypeCase8="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGMXCPMXL2'";
//	private final String reportCount3 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_14' and sql_type='JGMXCPMXL0'";
//	List<JSONObject> list =null;
//	StringBuilder sb = new StringBuilder();
	 
	@Override
	public JSONObject getFoodSalesRanking(String tenancyID, JSONObject condition) throws Exception{	
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		List<JSONObject> returnList2 = new ArrayList<JSONObject>();
		List<JSONObject> footerList = new ArrayList<JSONObject>();
		List<JSONObject> structure =new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L; 
		String reportSql = "";
		int hierarchytype = condition.optInt("hierarchytype"); //第几层数据请求
		int type_columns = condition.optInt("type_columns");  //查询方式
		int rank_count = condition.optInt("rank_count");  //排行数据量
		
		if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2 && hierarchytype == 1 ){
			condition.put("rows", 200);
		}else if (condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2)  {
			condition.put("rows", 99999999);
		}
		
		if(rank_count!=99999999 && condition.optInt("rows")<=rank_count) {
			condition.put("rows", rank_count);
		}
		
		if(type_columns==1){
			if(hierarchytype ==1){
				condition.element("p_report_type", "L1X2");
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,reportSql.toString()));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					//按照输入条数过滤数据
					list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,reportSql.toString()));
					if(rank_count!=99999999 && rank_count <=list.size()){
						list= list.subList(0, rank_count);
					} 
					total = this.dao.countSql(tenancyID,reportSql.toString());
					condition.element("p_report_type", "L0X2");
					footerList = this.dao.query4Json(tenancyID, this.dao.getString(tenancyID,parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING)));
				}
				
			}else if(hierarchytype ==2){
				condition.element("p_report_type", "L2X2");
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				
			}
		}
		
		else if (type_columns==2) {
			
				condition.element("p_report_type", "L1D1");
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					//按照输入条数过滤数据
					list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,reportSql.toString()));
					if(rank_count!=99999999 && rank_count <=list.size()){
						list= list.subList(0, rank_count);
					} 
					total = this.dao.countSql(tenancyID,reportSql.toString());
					condition.element("p_report_type", "L0D1");
					footerList = this.dao.query4Json(tenancyID, this.dao.getString(tenancyID,parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING)));
				}
				
			
		}else if (type_columns==3) {
				condition.element("p_report_type", "L1L1");
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,reportSql.toString()));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					//按照输入条数过滤数据
					list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,reportSql.toString()));
					if(rank_count!=99999999 && rank_count <=list.size()){
						list= list.subList(0, rank_count);
					} 
					total = this.dao.countSql(tenancyID,reportSql.toString());
					condition.element("p_report_type", "L0L1");
					footerList = this.dao.query4Json(tenancyID, this.dao.getString(tenancyID,parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING)));
				}
				
		}else if (type_columns==4) {
			if(hierarchytype ==1){
				condition.element("p_report_type", "L1M3");
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					//按照输入条数过滤数据
					list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,reportSql.toString()));
					if(rank_count!=99999999 && rank_count <=list.size()){
						list= list.subList(0, rank_count);
					} 
					total = this.dao.countSql(tenancyID,reportSql.toString());
					condition.element("p_report_type", "L0M3");
					footerList = this.dao.query4Json(tenancyID, this.dao.getString(tenancyID,parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING)));
				}
				
			}else if(hierarchytype ==2){
				condition.element("p_report_type", "L2M3");
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				
			}else if(hierarchytype ==3){
				condition.element("p_report_type", "L3M3");
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				
			}
		}else if (type_columns==5) {
			if(hierarchytype ==1){
				condition.element("p_report_type", "L1C2");
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					//按照输入条数过滤数据
					list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,reportSql.toString()));
					if(rank_count!=99999999 && rank_count <=list.size()){
						list= list.subList(0, rank_count);
					} 
					total = this.dao.countSql(tenancyID,reportSql.toString());
					condition.element("p_report_type", "L0C2");
					footerList = this.dao.query4Json(tenancyID, this.dao.getString(tenancyID,parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING)));
				}
				
			}else if(hierarchytype ==2){
				condition.element("p_report_type", "L2C2");
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.FOOD_SALES_RANKING);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				
			}
		}
		
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		
		result.put("page", pagenum);	
		result.put("total",total);	
		/*if(rank_count!=99999999 && hierarchytype ==1 && condition.optInt("derivedtype")!=2){
			result.put("rows", returnList2);
		}else{*/
			result.put("rows", list);
		/*}*/
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
		
//		Integer type = condition.optInt("type");
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		List<JSONObject> footerList =new ArrayList<JSONObject>();
//		String begindate = condition.optString("REPORT_DATE_BEGIN");
//		String enddate = condition.optString("REPORT_DATE_END");
//		JSONObject result = new JSONObject();
//		StringBuilder footer = new StringBuilder();
//		long total = 0L;
//		if(begindate.length()>0 && enddate.length()>0 )
//		{
//			switch (type)
//			{
//				//按机构汇总菜品小类
//				case 1:
//					if(condition.optInt("hierarchytype") ==1){
//						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCase1);
//						total = this.dao.countSql(tenancyID,completionSql.toString());
//						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
//						
//						String completionCount= parameterUtils.parameterAutomaticCompletion(tenancyID, condition,reportCount);
//						footerList = this.dao.query4Json(tenancyID, completionCount.toString());
//						
//					}else if(condition.optInt("hierarchytype") ==2 && condition.containsKey("xlbh1")){
//						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCase2);
//						total = this.dao.countSql(tenancyID,completionSql.toString());
//						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
//					}
//					
//				break;
//				//按机构明细菜品小类
//				case 2:
//					if(condition.optInt("hierarchytype") ==1){
//						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCase3);
//						total = this.dao.countSql(tenancyID,completionSql.toString());
//						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
//						
//						String completionCount= parameterUtils.parameterAutomaticCompletion(tenancyID, condition,reportCount1);
//						footerList = this.dao.query4Json(tenancyID, completionCount.toString());
//					}else if(condition.optInt("hierarchytype") ==2 && condition.containsKey("store_id1")){
//						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCase4);
//						total = this.dao.countSql(tenancyID,completionSql.toString());
//						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
//					}else if(condition.optInt("hierarchytype") ==3 && condition.containsKey("xlbh1")){
//						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCase5);
//						total = this.dao.countSql(tenancyID,completionSql.toString());
//						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
//					}
//				break;
//				//按机构汇总菜品明细
//				case 3:
//					if(condition.optInt("hierarchytype") ==1){
//						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCase6);
//						total = this.dao.countSql(tenancyID,completionSql.toString());
//						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
//						
//						String completionCount= parameterUtils.parameterAutomaticCompletion(tenancyID, condition,reportCount2);
//						footerList = this.dao.query4Json(tenancyID, completionCount.toString());
//					}
//				break;
//				//按机构明细菜品明细
//				case 4:
//					if(condition.optInt("hierarchytype") ==1){
//						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCase7);
//						total = this.dao.countSql(tenancyID,completionSql.toString());
//						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
//						
//						String completionCount= parameterUtils.parameterAutomaticCompletion(tenancyID, condition,reportCount3);
//						footerList = this.dao.query4Json(tenancyID, completionCount.toString());
//					}else if(condition.optInt("hierarchytype") ==2 && condition.containsKey("store_id1")){
//						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCase8);
//						total = this.dao.countSql(tenancyID,completionSql.toString());
//						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
//					}
//				break;
//				default:
//					break;
//			}
//		}
//		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
//		result.put("page", pagenum);
//		result.put("total",total);	
//		result.put("rows", list);
//		result.put("footer", footerList);
//		return result;
		}
}
