package com.tzx.report.common.util.redisUtil;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.io.InputStream;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.data.redis.connection.jedis.JedisConnection;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.commonreplace.CommonMethodAreaService;


@Controller
@RequestMapping("/report/redisTemplateRest")
public class RedisTemplateRest{
	
   @Resource
   private CommonMethodAreaService commonMethodAreaService;
   
	
	/**
	 * connectionFactory
	 */
	@Resource(name = "connectionFactory")
	private JedisConnectionFactory connectionFactory;
	
   //private static final Jedis jedis = new Jedis("*************",6379);
   //private ApplicationContext context;
	
   private final String  NEWS_CLICK_RATE ="NEWS_CLICK_RATE_00010101010110_";
   
   private static final Logger logger = Logger.getLogger(RedisTemplateRest.class);
   
   @RequestMapping(value = "/saveNewsClickRate")
	@ApiOperation(value = "缓存新闻点击率",consumes= "multipart/form-data" ,httpMethod="POST",notes = "缓存新闻点击率")
	@ApiImplicitParams({
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "sys_id",value = "新闻ID"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenentid",value = "商户号"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "table_name",value = "表名"),
	})
   public void saveNewsClickRate(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		JSONObject obj = JSONObject.fromObject("{}");
		PrintWriter out = null;
		InputStream in = null;
		String result = "";
		Integer uid = null;
		JedisConnection jedisConnection = (JedisConnection) connectionFactory.getConnection();
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			Map<String, String[]> maps = request.getParameterMap();

			for (String key : maps.keySet())
			{
				if (maps.get(key)[0] != "")
				{
					p.put(key, maps.get(key)[0]);
				}
			}
			
			   //context = new ClassPathXmlApplicationContext("spring/spring-redis.xml");
			   //RedisTemplate redisTemplate = (RedisTemplate)context.getBean("saasRedisTemplate");
			   String sys_id = p.getString("sys_id");//前端传入新闻ID
			   String tenentid = p.getString("tenentid");
			   String table_name = p.getString("table_name");
			   boolean bool = false;
			 //jedis.flushDB(); 清空所有建
			   
			   Boolean is = jedisConnection.exists(new String(NEWS_CLICK_RATE+tenentid).getBytes());//新闻点击率hash是否存在
			   Map<byte[],byte[]> map = new HashMap<>();
			   Map<byte[], byte[]> news_click_rate = null;
			   boolean bis = false;
			   if(is){
				   news_click_rate = jedisConnection.hGetAll(new String(NEWS_CLICK_RATE+tenentid).getBytes());//取出所有得key
				   for(Map.Entry<byte[], byte[]> entry:news_click_rate.entrySet()) {
					   String key = new String(entry.getKey());
					   //当前新闻ID是否已经缓存
					   if(key.equals("id"+"_"+sys_id)){
						   bis = true; 
					   }
				   }
				 //如果当前key存在就取当前key得点击数
				   if(!bis){
					   //--添加数据
				       map.put(new String("id"+"_"+sys_id).getBytes(),new String("0").getBytes());
				       jedisConnection.hMSet(new String(NEWS_CLICK_RATE+tenentid).getBytes(), map); //jedis.hmset(NEWS_CLICK_RATE+tenentid,map);
				       news_click_rate = jedisConnection.hGetAll(new String(NEWS_CLICK_RATE+tenentid).getBytes());   //.hgetAll(NEWS_CLICK_RATE+tenentid);//取出所有得key
				   }
			   }else{
				 //--添加数据--如果不存在当前key添加当前key并赋予默认值
			       map.put(new String("id"+"_"+sys_id).getBytes(),new String("0").getBytes());
			       jedisConnection.hMSet(new String(NEWS_CLICK_RATE+tenentid).getBytes(), map);     //hmset("NEWS_CLICK_RATE_00010101010110_"+tenentid,map);
			       news_click_rate = jedisConnection.hGetAll(new String(NEWS_CLICK_RATE+tenentid).getBytes());    //.hgetAll(NEWS_CLICK_RATE+tenentid);//取出所有得key
			   }
			   
		       for(Map.Entry<byte[], byte[]> entry:news_click_rate.entrySet()) {
		    	   byte[] count_key = entry.getKey();
		    	   String strcount_key = new String(count_key);
		    		 //如果当前key存在并且与当前ID相同自动累加
			          if(strcount_key.equals("id"+"_"+sys_id)){
			        	  jedisConnection.hIncrBy(new String(NEWS_CLICK_RATE+tenentid).getBytes(), count_key, 1); //jedis.hincrBy(NEWS_CLICK_RATE+tenentid, count_key, 1);
			        	  logger.info("点击率：redis缓存数据成功！");
			        	  bool = true;
			        	  break;
				      }
		       	}
		       if(bool){
		    	   obj.put("success", bool);
		    	   obj.put("code", "0");
		       }else{
		    	   obj.put("success", bool);
		    	   obj.put("code", "1001");
		    	   obj.put("msg", "redis存储异常");
		       }
		       /*List<JSONObject> list = new ArrayList();
		       Set<String> news = redisTemplate.keys(NEWS_CLICK_RATE + "*");
		       if(news.size()>0){
		    	   for (String str : news)
						try {
								{
								  String tid = str.substring(31);//获取商户号
								  DBContextHolder.setTenancyid(tid);//数据源切换
								  Map<String, String> news_click = jedis.hgetAll(str);//取出新闻点击率
								  for(Map.Entry<String, String> entry:news_click.entrySet()) {
									  JSONObject json = new JSONObject();
									  String key = entry.getKey();  //取出当前key名称
									  String keyId = key.substring(3);//截取当前ID
									  String value = entry.getValue();//取出当前value值
									  json.element("id", keyId);
									  json.element("click_record", value);
									  list.add(json);
								  }
								  logger.info("点击率同步数据库当前商户为:" + tid);
								  logger.info("点击率同步数据库当前数据集合为:" + list);
								  int[] rate = commonMethodAreaService.updNewsClickRate(tid, table_name, list);
								  if(rate.length>0){
									  for(int i = 0;i<rate.length;i++){
										  if(rate[i]==0){
											  logger.info("当前同步数据表为" + table_name+"数据同步出错：不清除redis缓存数据！");
											  return;
										  }
									  }
									  logger.info("当前同步数据表为" + table_name+"同步数据成功！");
								  	}
							   	}
								Map<String, String> us = jedis.hgetAll(NEWS_CLICK_RATE+tenentid);//取出所有key
								logger.info("清除redis缓存数据"+news);
								for(String newsReteKey: news){
									jedis.del(newsReteKey);
								}
								logger.info("清除"+news+"EKY成功！");
						} catch (Exception e) {
							e.printStackTrace();
						}
		       		}*/
		}
		catch (Exception e)
		{
			obj.put("success", false);
			obj.put("msg", "发生错误!");
			e.printStackTrace();
			logger.info(e);
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				jedisConnection.close();//关闭jedis通道
				out = response.getWriter();
				out.print(obj);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
}
