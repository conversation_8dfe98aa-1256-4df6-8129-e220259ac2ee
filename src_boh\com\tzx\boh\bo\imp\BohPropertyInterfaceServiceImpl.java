package com.tzx.boh.bo.imp;

import java.io.File;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.boh.bo.BohPropertyInterfaceService;
import com.tzx.boh.po.springjdbc.dao.BohPropertyInterfaceDao;
import com.tzx.crm.bo.PropertyParametersService;
import com.tzx.crm.bo.imp.PropertyParametersServiceImp;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.FTPUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SFTPTest;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.WriteStringToTxt;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.weixin.po.redis.dao.RedisTemplateDao;

@Service(BohPropertyInterfaceService.NAME)
public class BohPropertyInterfaceServiceImpl implements BohPropertyInterfaceService
{
	@Resource(name = BohPropertyInterfaceDao.Name)
	private BohPropertyInterfaceDao	dao;
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	gdao;
	
	@Resource(name = PropertyParametersService.NAME)
	private PropertyParametersService propertyParametersService;
	
	@Resource(name = RedisTemplateDao.NAME)
    private RedisTemplateDao rdao;
	
	@Resource(name = PropertyParametersService.NAME)
	private PropertyParametersService  propertyparametersservice;
	private static final Logger		log	= Logger.getLogger(BohPropertyInterfaceServiceImpl.class);
	@Override
	public JSONObject load(String tenancyID, JSONObject condition,String oids) throws Exception
	{
		
		return dao.load(tenancyID, condition, oids);
	}

	@Override
	public JSONObject find(String tenancyID, JSONObject condition) throws Exception
	{
		return dao.find(tenancyID, condition);
	}

	@Override
	public JSONObject save(String tenancyID, JSONObject condition) throws Exception
	{
		return dao.save(tenancyID, condition);
	}

	@Override
	public List<JSONObject>  getProperty(String tenancyID, JSONObject condition) throws Exception
	{
		return dao.getProperty(tenancyID, condition);
	}

	@Override
	public JSONObject upload(String tenancyID, JSONObject condition) throws Exception
	{
		String typeId= condition.getString("type_id");
		if(typeId.equals("0")) 
		{
			return dao.upload(tenancyID, condition);
		}
		else if(typeId.equals("1")) {
			JSONObject sendParameters = propertyParametersService.sendParameters(tenancyID, condition);
			return sendParameters;
		}else if(typeId.equals("9")){
			//上传v332m接口数据
			return dao.uploadV332m(tenancyID, condition);
		}else if(typeId.equals("8")){
			return null;
		}
		return null;
	}

	@Override
	public JSONObject getUploadDetails(String tenancyID, JSONObject condition) throws Exception
	{
		return dao.getUploadDetails(tenancyID, condition);
	}

	@Override
	public List<JSONObject> getList(String tenancyID, JSONObject condition) throws Exception
	{
		return dao.getList(tenancyID, condition);
	}

	/* (non-Javadoc)
	 * @see com.tzx.boh.bo.BohPropertyInterfaceService#getMonth(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public List<JSONObject> getMonth(String tenancyID, JSONObject condition) throws Exception
	{
		return dao.getMonth(tenancyID, condition);
		
	}

	/* (non-Javadoc)
	 * @see com.tzx.boh.bo.BohPropertyInterfaceService#stop(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject stop(String tenancyID, JSONObject condition) throws Exception
	{
		 return dao.stop(tenancyID, condition);
	}

	@Override
	public JSONObject findTypeId(String attribute, JSONObject p) throws Exception {
		// TODO Auto-generated method stub
		return dao.findTypeId(attribute,p);
	}

	@Override
	public JSONObject findOrgan(String attribute, JSONObject p) throws Exception {
		// TODO Auto-generated method stub
		return dao.findOrgan(attribute,p);
	}

	@Override
	public JSONObject findErrDate(String attribute, JSONObject p)
			throws Exception {
		// TODO Auto-generated method stub
		JSONObject findErrDataJson = propertyParametersService.findErrDate(attribute, p);
		return findErrDataJson;
	}

	@Override
	public JSONObject clearReportDateData(String tenancyID, JSONObject obj) throws Exception
	{
		JSONObject findErrDataJson = propertyParametersService.clearReportDateData(tenancyID, obj);
		return findErrDataJson;
	}
	@Override
	public JSONObject uploadSFTP(String tenancyID,String uploadFile, JSONObject obj) throws Exception
	{
		//第一步根据日结日期和门店编号 调用接口 获得门店的日结数据  并且获取该门店的物业接口的信息 
		JSONObject result =new JSONObject();
		List<JSONObject> list = null;
		if(obj.containsKey("report_date")&&obj.containsKey("store_id"))
		{    
			//先根据门店编号和日结日期查询该门店是否日结 
			
			 list = propertyParametersService.queryDailyByStoreIdAndDate(tenancyID, obj);
			if(list.size()==0)
			{
				result.put("success", false);
				result.put("msg", "该门店还没有日结，请先日结！");
				return result;
			}
			//查询该门店此日期下的数据是否上传
			boolean b = propertyParametersService.queryDayilymessage(tenancyID, obj);
			if(b)
			{
				result.put("success", false);
				result.put("msg", "该门店该日期的数据已上传");
				return result;
			}
			
		}
		
		
		JSONObject params = null;
		try
		{
			params = propertyParametersService.queryParameterByStoreId(tenancyID, obj);
		}
		catch (Exception e1)
		{
			log.info("SFTP参数错误！");
			result.put("success",false);	
	        result.put("msg","SFTP参数错误！");
			e1.printStackTrace();
			return result;
		}
		// 设置主机ip，端口，用户名，密码
		 String host = params.optString("url");
		 String port = params.optString("port");
		 String username = params.optString("user_name");
		 String password = params.optString("password");
		 String storeCode =params.optString("storecode");
		 String Tillid  =params.optString("tillid");
		 String Vipcode = params.optString("vipcode");
		 String Plu   =params.optString("plu");
		 String key = "bohproperty_"+tenancyID+"_"+storeCode+"_"+obj.optString("store_id")+"_"+obj.optString("report_date")+"_lcd";
			
			try
			{
				if(rdao.hasKey(key))
				{
					result.put("success",false);
					result.put("msg","正在上传日结数据,请稍后再试");
					return result;
				}
				rdao.set(key,1);
				rdao.expire(key, 2*60l);
			}
			catch (Exception e)
			{
				
			}
		 
		 StringBuilder sb =new StringBuilder();
		for (JSONObject jb : list)
		{
			sb.append(storeCode);
			sb.append("\t"+Tillid);
			sb.append("\t"+jb.optString("txdate"));
			sb.append("\t"+jb.optString("txtime"));
			sb.append("\t"+jb.optString("docno"));
			if(Plu!=null&&!"".equals(Plu))
			{
		    sb.append("\t"+Plu);
			}
			else
			{
				sb.append("\t\t");
			}
			if(Vipcode!=null&&!"".equals(Vipcode))
			{
		    sb.append("\t"+Vipcode);
			}
			else
			{
				sb.append("\t\t");
			}
			if(jb.containsKey("ch")&&jb.optInt("ch")!=0)
			{  
				sb.append(Scm.roundByScale(jb.optDouble("bill_amount", 0.00), 2));
				sb.append("\t0.00");
				sb.append("\t0.00");
				sb.append("\t0.00");
			}
			else
			{
				sb.append("0.00");
				sb.append("\t0.00");
				sb.append("\t0.00");
				
				sb.append("\t"+Scm.roundByScale(jb.optDouble("bill_amount", 0.00), 2));
				
			}
		    sb.append("\t0.00");
		    sb.append("\t"+Scm.roundByScale(jb.optDouble("bill_amount", 0.00), 2));
		    sb.append("\t"+jb.optString("bill_num"));
			sb.append("\n");
		}
		
		
		String txtname = storeCode+obj.optString("report_date").replaceAll("-","");
		//解析获得的日结数据进行文本生成
		String filepath = "";
		try
		{   
			 filepath = WriteStringToTxt.WriteStringToFile(uploadFile,txtname,sb.toString());
		}
		catch (Exception e)
		{
			log.info("日结文本生成错误！");
			result.put("success",false);
			result.put("msg", "日结文本生成错误！");
			e.printStackTrace();
			return result;
		}
		//将生成的文本信息上传到侨福置业公司的服务器
		
		try
		{
			SFTPTest.uploadSFTP(host, username, password, port, filepath);//fileName
		}
		catch (Exception e)
		{
			log.info("文件上传错误！");
			result.put("success",false);
			result.put("msg","文件上传错误！");
			e.printStackTrace();
			return result;
		}
		//上传成功之后将生成的文件删除 
		try
		{
			WriteStringToTxt.deleteFile(filepath);
		}
		catch (Exception e)
		{
			log.info("文件删除失败！");
			e.printStackTrace();
		}
	
		//上传成功之后将上传的数据保存到一张表中 记录 crm_sftp_upload_record
	    JSONObject uploadRecord = new JSONObject();
	    uploadRecord.put("store_id", obj.optString("store_id"));
	    uploadRecord.put("tenancy_id", tenancyID);
	    uploadRecord.put("operator", obj.optString("last_operator"));
	    uploadRecord.put("report_date", obj.optString("report_date"));
	    uploadRecord.put("operater_time", obj.optString("last_updatetime")); 
	    boolean b =false;
		try
		{
			b = propertyParametersService.saveUploadRecord(tenancyID,uploadRecord);
		}
		catch (Exception e)
		{
			log.info("保存错误！");
			result.put("success",false);
			result.put("msg","数据保存错误,请检查表结构！");
			e.printStackTrace();
		}
	    if(b)
	    {
	    	result.put("success", true);
			result.put("msg","上传成功！");
	    }
	    else
	    {
	    	result.put("success",false);
			result.put("msg","数据保存错误！");
	    }
		
		result.put("success", true);
		result.put("msg","上传成功！");
		return result;
	}
	
	

	@Override
	public JSONObject uploadMISFTP(String tenancyID, String uploadFile,JSONObject obj) throws Exception {
		//第一步根据日结日期和门店编号 调用接口 获得门店的日结数据  并且获取该门店的物业接口的信息 
				JSONObject result =new JSONObject();
				List<JSONObject> list = null;
				if(obj.containsKey("report_date")&&obj.containsKey("store_id"))
				{    
					//先根据门店编号和日结日期查询该门店是否日结 
					
					 list = propertyParametersService.queryDailyByStoreIdAndDate(tenancyID, obj);
					if(list.size()==0)
					{
						result.put("success", false);
						result.put("msg", "该门店还没有日结，请先日结！");
						return result;
					}
					//查询该门店此日期下的数据是否上传
					boolean b = propertyParametersService.queryDayilymessage(tenancyID, obj);
					if(b)
					{
						result.put("success", false);
						result.put("msg", "该门店该日期的数据已上传");
						return result;
					}
					
				}
				
				
				JSONObject params = null;
				try
				{
					//查询连接服务用的参数
					params = propertyParametersService.queryParameterByStoreId(tenancyID, obj);
				}
				catch (Exception e1)
				{
					log.info("SFTP参数错误！");
					result.put("success",false);	
			        result.put("msg","SFTP参数错误！");
					e1.printStackTrace();
					return result;
				}
				log.info("MIS接口查询参数："+params.toString());
				// 设置主机ip，端口，用户名，密码
				 String host = params.optString("url");
				 Integer port = params.optInt("port");
				 String username = params.optString("user_name");
				 String password = params.optString("password");
				 String storeCode =params.optString("storecode");
				 String Tillid  =params.optString("tillid");
				 String Vipcode = params.optString("vipcode");
				 String Plu   =params.optString("plu");
				 //上传时存储状态
				 String key = "bohproperty_"+tenancyID+"_"+storeCode+"_"+obj.optString("store_id")+"_"+obj.optString("report_date")+"_mis";
					
					try
					{
						if(rdao.hasKey(key))
						{
							result.put("success",false);
							result.put("msg","正在上传日结数据,请稍后再试");
							return result;
						}
						rdao.set(key,1);
						//2分钟后过期
						rdao.expire(key, 2*60l);
					}
					catch (Exception e)
					{
						
					}
				 
				 StringBuilder sb =new StringBuilder();
				 Map<String,String> map = new HashMap<String,String>();
				 for (JSONObject jsonObject : list) {
					 map.put(jsonObject.optString("bill_num"),jsonObject.optString("copy_bill_num"));
				 }
				 System.out.println(map.toString());
				 Date today=new Date();
				 int index=0;//序号
				for (JSONObject jb : list)
				{   
					
					sb.append(storeCode);
					//store_code
//					sb.append("\t"+storeCode); 
					sb.append("\t"+Tillid);
					sb.append("\t"+jb.optString("txdate"));
					sb.append("\t"+jb.optString("txtime"));
					//单据号 Docno
//					String docNo=genDocNo(today, index++);
					sb.append("\t"+jb.optString("docno"));
					sb.append("\t"+Plu);
//					sb.append("\t"+jb.optString("bill_num"));									
					sb.append("\t"+Vipcode);
			        Double CH = 0.00;//付款方式（现金）
			        Double BK = 0.00;//付款方式（国内卡）
					Double BO = 0.00;//付款方式（国外卡）
					Double CZ = 0.00;//付款方式（储值卡）
					Double LQ = 0.00;//付款方式（礼券）
					Double OT = 0.00;//付款方式（其他）
					Double Ttldiscount = 0.00;//包含因促销而免费赠送的抵用券、抵用积分所代表的金额，以及直接折扣金额。没有折扣的需要保存0
					String remark = "";
						
						StringBuilder querysalesTender =new StringBuilder();
						querysalesTender.append(" select pbp.jzid,pbp.type,pbp.name as code ,pbp.currency_amount,pl.more_coupon coupon,pl.discount_amount from  pos_bill2 pl left   join pos_bill_payment2 pbp on pl.bill_num = pbp.bill_num");
						querysalesTender.append(" where pl.bill_num ='"+jb.optString("bill_num")+"' ");
						List<JSONObject> salesTenderList = this.gdao.query4Json(tenancyID, querysalesTender.toString());
					
						for (JSONObject tender : salesTenderList) {
							if(tender.optString("code").equals("人民币")||tender.optString("code").equals("找零"))
							{   
								
								CH += tender.optDouble("currency_amount");
							}
							else if(tender.optString("code").contains("银行卡")||tender.optString("code").contains("信用卡")||tender.optString("code").contains("银联卡"))
							{
								BK += tender.optDouble("currency_amount");
							}
							else if(tender.optString("code").contains("券"))
							{
								LQ += tender.optDouble("currency_amount");
							}
							else
							{
								OT += tender.optDouble("currency_amount");
								remark = tender.optString("code");
							}
							if(tender.optInt("discount_amount")!=0)
							{
								Ttldiscount += tender.optDouble("discount_amount");
							}
							
							
						}
						
						sb.append("\t"+Scm.roundByScale(CH, 2));
						sb.append("\t"+Scm.roundByScale(BK, 2));
						sb.append("\t"+Scm.roundByScale(BO, 2));
						//其它
						sb.append("\t"+Scm.roundByScale( (CZ+LQ+OT) , 2));
//						sb.append("\t"+Scm.roundByScale(CZ, 2));
//						sb.append("\t"+Scm.roundByScale(LQ, 2));
//						sb.append("\t"+Scm.roundByScale(OT, 2));
						sb.append("\t"+Scm.roundByScale(Ttldiscount, 2));
						Double  Netamt = CH+BK+BO+CZ+LQ+OT;
						sb.append("\t"+Scm.roundByScale(Netamt, 2));
						if(Netamt<0)
						{   
							String docno_copy = map.get(jb.optString("bill_num"));
							if(docno_copy!=""||docno_copy!=null)
							{
//								sb.append("\t"+docno_copy);
							}
							else
							{
//								sb.append("\t");
							}
							remark = "取消交易";
						}
						else if (Netamt>0)
						{
//							sb.append("\t");
						}
						else if(Netamt==0&&CZ==0&&LQ==0&&OT==0)
						{
//							sb.append("\t");
							remark = "撤单";
						}
//						sb.append("\t"+remark);				
					    sb.append("\r\n");
				}
				
				
				String txtname = storeCode+obj.optString("report_date").replaceAll("-","");
				//解析获得的日结数据进行文本生成
				
				String filepath = "";
				try
				{   
					String udata=sb.toString();
					log.info("上传数据："+udata);
					 filepath = WriteStringToTxt.WriteStringToFile(uploadFile,txtname,udata);
					log.info("uploadFile:"+uploadFile+"txtname:"+txtname+"sb:"+sb.toString());
					log.info("filepath:"+filepath);

				}
				catch (Exception e)
				{
					log.info("日结文本生成错误！");
					result.put("success",false);
					result.put("msg", "日结文本生成错误！");
					e.printStackTrace();
					return result;
				}
				
				try
				{
					/*SFTPTest.uploadSFTP(host, username, password, port, filepath);//fileName
*/			
					 FTPUtil fu = new FTPUtil(host, port, username,password);  
			         FTPClient client = fu.connectFTPServer();  
			         log.info("开始上传FTP文件"+filepath+txtname+".txt");
			         fu.uploadFile(filepath, txtname+".txt");
			         log.info("上传FTP文件"+filepath+txtname+".txt 成功");
			            	
				}
				catch (Exception e)
				{
					log.info("文件上传错误！");
					result.put("success",false);
					result.put("msg","文件上传错误！");
					e.printStackTrace();
					return result;
				}
				//上传成功之后将生成的文件删除 
				try
				{
					WriteStringToTxt.deleteFile(filepath);
				}
				catch (Exception e)
				{
					log.info("文件删除失败！");
					e.printStackTrace();
				}
			
				//上传成功之后将上传的数据保存到一张表中 记录 crm_sftp_upload_record
			    JSONObject uploadRecord = new JSONObject();
			    uploadRecord.put("store_id", obj.optString("store_id"));
			    uploadRecord.put("tenancy_id", tenancyID);
			    uploadRecord.put("operator", obj.optString("last_operator"));
			    uploadRecord.put("report_date", obj.optString("report_date"));
			    uploadRecord.put("operater_time", obj.optString("last_updatetime")); 
			    boolean b =false;
				try
				{
					b = propertyParametersService.saveUploadRecord(tenancyID,uploadRecord);
				}
				catch (Exception e)
				{
					log.info("保存错误！");
					result.put("success",false);
					result.put("msg","数据保存错误,请检查表结构！");
					e.printStackTrace();
				}
			    if(b)
			    {
			    	result.put("success", true);
					result.put("msg","上传成功！");
			    }
			    else
			    {
			    	result.put("success",false);
					result.put("msg","数据保存错误！");
			    }
				
				result.put("success", true);
				result.put("msg","上传成功！");
				return result;
	}
	//生成MIS的单据号
	private static String genDocNo(Date now,int index){
//		Date now =new Date();
		SimpleDateFormat sdf=new SimpleDateFormat("MMdd");
		String dateStr=sdf.format(now);
		String suffix="0000"+index;
		return "S".concat(dateStr).concat(suffix.substring(  suffix.length()-5 ));
	}
	//test
	public static void main(String[] args) {
		System.out.println(genDocNo(new Date(),11111));
	}
	
	
	@Override
	public JSONObject uploadxcSFTP(String tenancyID, String uploadFile,JSONObject obj) throws Exception {
		//第一步根据日结日期和门店编号 调用接口 获得门店的日结数据  并且获取该门店的物业接口的信息 
				JSONObject result =new JSONObject();
				List<JSONObject> list = null;
				if(obj.containsKey("report_date")&&obj.containsKey("store_id"))
				{    
					//先根据门店编号和日结日期查询该门店是否日结 
					
					 list = propertyParametersService.queryDailyByStoreIdAndDate(tenancyID, obj);
					if(list.size()==0)
					{
						result.put("success", false);
						result.put("msg", "该门店还没有日结，请先日结！");
						return result;
					}
					//查询该门店此日期下的数据是否上传
					boolean b = propertyParametersService.queryDayilymessage(tenancyID, obj);
					if(b)
					{
						result.put("success", false);
						result.put("msg", "该门店该日期的数据已上传");
						return result;
					}
					
				}
				
				
				JSONObject params = null;
				try
				{
					params = propertyParametersService.queryParameterByStoreId(tenancyID, obj);
				}
				catch (Exception e1)
				{
					log.info("SFTP参数错误！");
					result.put("success",false);	
			        result.put("msg","SFTP参数错误！");
					e1.printStackTrace();
					return result;
				}
				// 设置主机ip，端口，用户名，密码
				 String host = params.optString("url");
				 Integer port = params.optInt("port");
				 String username = params.optString("user_name");
				 String password = params.optString("password");
				 String storeCode =params.optString("storecode");
				 String Tillid  =params.optString("tillid");
				 String Vipcode = params.optString("vipcode");
				 String Plu   =params.optString("plu");
				 String key = "bohproperty_"+tenancyID+"_"+storeCode+"_"+obj.optString("store_id")+"_"+obj.optString("report_date")+"_lcd";
					
					try
					{
						if(rdao.hasKey(key))
						{
							result.put("success",false);
							result.put("msg","正在上传日结数据,请稍后再试");
							return result;
						}
						rdao.set(key,1);
						rdao.expire(key, 2*60l);
					}
					catch (Exception e)
					{
						
					}
				 
				 StringBuilder sb =new StringBuilder();
				 Map<String,String> map = new HashMap<String,String>();
				 for (JSONObject jsonObject : list) {
					 map.put(jsonObject.optString("bill_num"),jsonObject.optString("copy_bill_num"));
				 }
				 System.out.println(map.toString());
				for (JSONObject jb : list)
				{   
					
					sb.append(storeCode);
					sb.append("\t"+Tillid);
					sb.append("\t"+jb.optString("txdate"));
					sb.append("\t"+jb.optString("txtime"));
					sb.append("\t"+jb.optString("bill_num"));									
					sb.append("\t"+Vipcode);
			        Double CH = 0.00;//付款方式（现金）
			        Double BK = 0.00;//付款方式（国内卡）
					Double BO = 0.00;//付款方式（国外卡）
					Double CZ = 0.00;//付款方式（储值卡）
					Double LQ = 0.00;//付款方式（礼券）
					Double OT = 0.00;//付款方式（其他）
					Double Ttldiscount = 0.00;//包含因促销而免费赠送的抵用券、抵用积分所代表的金额，以及直接折扣金额。没有折扣的需要保存0
					String remark = "";
						
						StringBuilder querysalesTender =new StringBuilder();
						querysalesTender.append(" select pbp.jzid,pbp.type,pbp.name as code ,pbp.currency_amount,pl.more_coupon coupon,pl.discount_amount from  pos_bill2 pl left   join pos_bill_payment2 pbp on pl.bill_num = pbp.bill_num");
						querysalesTender.append(" where pl.bill_num ='"+jb.optString("bill_num")+"' ");
						List<JSONObject> salesTenderList = this.gdao.query4Json(tenancyID, querysalesTender.toString());
					
						for (JSONObject tender : salesTenderList) {
							if(tender.optString("code").equals("人民币")||tender.optString("code").equals("找零"))
							{   
								
								CH += tender.optDouble("currency_amount");
							}
							else if(tender.optString("code").contains("银行卡")||tender.optString("code").contains("信用卡")||tender.optString("code").contains("银联卡"))
							{
								BK += tender.optDouble("currency_amount");
							}
							else if(tender.optString("code").contains("券"))
							{
								LQ += tender.optDouble("currency_amount");
							}
							else
							{
								OT += tender.optDouble("currency_amount");
								remark = tender.optString("code");
							}
							if(tender.optInt("discount_amount")!=0)
							{
								Ttldiscount += tender.optDouble("discount_amount");
							}
							
							
						}
						
						sb.append("\t"+Scm.roundByScale(CH, 2));
						sb.append("\t"+Scm.roundByScale(BK, 2));
						sb.append("\t"+Scm.roundByScale(BO, 2));
						sb.append("\t"+Scm.roundByScale(CZ, 2));
						sb.append("\t"+Scm.roundByScale(LQ, 2));
						sb.append("\t"+Scm.roundByScale(OT, 2));
						sb.append("\t"+Scm.roundByScale(Ttldiscount, 2));
						Double  Netamt = CH+BK+BO+CZ+LQ+OT;
						sb.append("\t"+Scm.roundByScale(Netamt, 2));
						if(Netamt<0)
						{   
							String docno_copy = map.get(jb.optString("bill_num"));
							if(docno_copy!=""||docno_copy!=null)
							{
								sb.append("\t"+docno_copy);
							}
							else
							{
								sb.append("\t");
							}
							remark = "取消交易";
						}
						else if (Netamt>0)
						{
							sb.append("\t");
						}
						else if(Netamt==0&&CZ==0&&LQ==0&&OT==0)
						{
							sb.append("\t");
							remark = "撤单";
						}
						sb.append("\t"+remark);				
					    sb.append("\r\n");
				}
				
				
				String txtname = storeCode+obj.optString("report_date").replaceAll("-","");
				//解析获得的日结数据进行文本生成
				
				String filepath = "";
				try
				{   
					 filepath = WriteStringToTxt.WriteStringToFile(uploadFile,txtname,sb.toString());
					log.info("uploadFile:"+uploadFile+"txtname:"+txtname+"sb:"+sb.toString());
					log.info("filepath:"+filepath);

				}
				catch (Exception e)
				{
					log.info("日结文本生成错误！");
					result.put("success",false);
					result.put("msg", "日结文本生成错误！");
					e.printStackTrace();
					return result;
				}
				//将生成的文本信息上传到侨福置业公司的服务器
				
				try
				{
					/*SFTPTest.uploadSFTP(host, username, password, port, filepath);//fileName
*/			
					 FTPUtil fu = new FTPUtil(host, port, username,password);  
			         FTPClient client = fu.connectFTPServer();  
			         fu.uploadFile(filepath, txtname+".txt");
			            	
				}
				catch (Exception e)
				{
					log.info("文件上传错误！");
					result.put("success",false);
					result.put("msg","文件上传错误！");
					e.printStackTrace();
					return result;
				}
				//上传成功之后将生成的文件删除 
				try
				{
					WriteStringToTxt.deleteFile(filepath);
				}
				catch (Exception e)
				{
					log.info("文件删除失败！");
					e.printStackTrace();
				}
			
				//上传成功之后将上传的数据保存到一张表中 记录 crm_sftp_upload_record
			    JSONObject uploadRecord = new JSONObject();
			    uploadRecord.put("store_id", obj.optString("store_id"));
			    uploadRecord.put("tenancy_id", tenancyID);
			    uploadRecord.put("operator", obj.optString("last_operator"));
			    uploadRecord.put("report_date", obj.optString("report_date"));
			    uploadRecord.put("operater_time", obj.optString("last_updatetime")); 
			    boolean b =false;
				try
				{
					b = propertyParametersService.saveUploadRecord(tenancyID,uploadRecord);
				}
				catch (Exception e)
				{
					log.info("保存错误！");
					result.put("success",false);
					result.put("msg","数据保存错误,请检查表结构！");
					e.printStackTrace();
				}
			    if(b)
			    {
			    	result.put("success", true);
					result.put("msg","上传成功！");
			    }
			    else
			    {
			    	result.put("success",false);
					result.put("msg","数据保存错误！");
			    }
				
				result.put("success", true);
				result.put("msg","上传成功！");
				return result;
	}

	@Override
	public JSONObject uploadln(String tenancyID, JSONObject obj)
			throws Exception {
		JSONObject result =new JSONObject();
		List<JSONObject> list = null;
		if(obj.containsKey("report_date")&&obj.containsKey("store_id"))
		{    
			//先根据门店编号和日结日期查询该门店是否日结 
			
			 list = propertyParametersService.queryDailyByStoreIdAndDate(tenancyID, obj);
			if(list.size()==0)
			{
				result.put("success", false);
				result.put("msg", "该门店还没有日结，请先日结！");
				return result;
			}
			//查询该门店此日期下的数据是否上传
			boolean b = propertyParametersService.queryDayilymessage(tenancyID, obj);
			if(b)
			{
				result.put("success", false);
				result.put("msg", "该门店该日期的数据已上传");
				return result;
			}
			
		}
		
		
		JSONObject params = null;
		try
		{
			params = propertyParametersService.queryParameterByStoreId(tenancyID, obj);
		}
		catch (Exception e1)
		{
			log.info("查询上传的参数错误！");
			result.put("success",false);	
	        result.put("msg","查询上传的参数错误");
			e1.printStackTrace();
			return result;
		}
		JSONObject session = new JSONObject() ;
	    try {
             
			 session.put("FRESULT", "0");
			 session.put("LogIn", "登陆成功");
			 session.put("FDATA", "73735365443D36443634363146303743324534413546383232433930374445433844303238440D0A4F7065723D746573740D0A53746F72654769643D313030303030300D0A53746F7265436F64653D393235");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	    if(!session.optString("FRESULT").equals("0")){
	    	result.put("success", false);
			result.put("msg", session.optString("FMSG"));
			return result;
	    }
	    //开始上传数据
		List<JSONObject> listaddsend = new ArrayList<JSONObject>();
		List<JSONObject> listsenderror = new ArrayList<JSONObject>();
		  int send_total = 0;
		  int send_err14 =0;
	    	int send_total2 = 0;
	    	if("5".equals(params.optString("type_id"))&&!"".equals(params.optString("url"))) {	    		    		
	 	   		
	 	   		int startIndex =obj.optInt("startErrIndex") ;
	 	   		StringBuilder  sb = new StringBuilder();
	 	   		sb.setLength(0);
	 	   		sb.append("select id,bill_num,(to_char(report_date,'yyyyMMdd'))||(substr(bill_num,(length(bill_num)-3),4))  as report_date,to_char(payment_time, 'yyyyMMddHH24MIss') as  payment_time,payment_amount,bill_amount,copy_bill_num,COALESCE(COALESCE(copy_bill_num,remark),'') as ydh from   pos_bill2  where   report_date ='"+obj.optString("report_date")+"' and store_id ='"+obj.optString("store_id")+"' and id not in (select bpius.send_id from boh_property_interface_upload bpius where bpius.store_id='"+obj.optString("store_id")+"') ORDER BY payment_time  LIMIT 20 offset "+startIndex+" ");
 	    		List<JSONObject> bills=new ArrayList<JSONObject>();
 	    		try {	
 	    			 bills = this.gdao.query4Json(tenancyID, sb.toString());
				} catch (Exception e) {
					result.put("success",false);
	 				result.put("msg","查询bill数据错误");
	 				return result;
				}
 	    		JSONObject sendUploadRsultJson =new JSONObject();
 	    		try 
 	    		{
 	    			// 调用封装类;
 	    			/*sendUploadRsultJson = sendUpload(tenantId,json,bills,propers,jsonInfo);*/
 	    			
 	    			sendUploadRsultJson = getUploadMessage(tenancyID,bills,session,params);
 	    			send_total= sendUploadRsultJson.optInt("send_total");
 	    			obj.put("send_total", sendUploadRsultJson.optInt("send_total"));
 	    			obj.put("send_err14", sendUploadRsultJson.optInt("send_err14"));
 	    			if(sendUploadRsultJson.optInt("send_total")>0)
 	    			{
 	    				listaddsend= (List<JSONObject>) sendUploadRsultJson.opt("listaddsend");
 	    			}    			
 	    			if(sendUploadRsultJson.optInt("send_err14")>0)
 	    			{
 	    				listsenderror= (List<JSONObject>) sendUploadRsultJson.opt("listsenderror");
 	    			}	    			
				} 
 	    		catch (Exception e) {
 	    			System.out.println(e.toString());
					// TODO: handle exception
					result.put("success",false);
	 				result.put("msg",e.toString());
	 				return result;
				}
 	    		try 
 	    		{
 	    			JSONObject senAddResultJson = this.senAddResult(tenancyID, obj, listaddsend);
 	    			send_total2 =senAddResultJson.optInt("send_total2");
				} 
 	    		catch (Exception e) {
					// TODO: handle exception
					result.put("success",false);
	 				result.put("msg","保存数据记录错误");
	 				return result;
				}
	    	
	    	}
	    	
	    	result.put("success",true);
			result.put("now_size",send_total2);
			result.put("new_send_total", send_total);
			result.put("send_err14",send_err14);
			result.put("error", listsenderror);
			result.put("msg","上传完毕");
			return result;
	}
	private JSONObject senAddResult(String tenantId,JSONObject json,List<JSONObject>listaddsend) throws Exception {
		JSONObject result =new JSONObject();
		String  storeId =(String) json.opt("store_id");
		String reportDate =json.optString("report_date");
		String type_id =json.optString("type_id");
		int send_total=json.optInt("send_total");
		
		int send_total2 = 0;
		if(listaddsend.size()>0)
		{
			this.gdao.insertBatchIgnorCase(json.optString("tenancy_id"), "boh_property_interface_upload", listaddsend);
		}
    	
    	StringBuilder sb=new StringBuilder();
    	sb.setLength(0);//
		sb.append("select id,send_total from boh_property_interface_upload_summary where type_id="+type_id+" and store_id="+storeId+" and business_date='"+reportDate+"' ");
		List<JSONObject> listtt = this.gdao.query4Json(tenantId, sb.toString());
		if(listtt.size()==0)
		{
			send_total2 = send_total;
			JSONObject totjo = new JSONObject();
			totjo.put("tenancy_id",tenantId);
			totjo.put("type_id","1");
			totjo.put("store_id",storeId);
			totjo.put("business_date",reportDate);
			totjo.put("send_total",send_total);
			this.gdao.insertIgnorCase(tenantId, "boh_property_interface_upload_summary", totjo);
		}
		else
		{
			sb.setLength(0);
			send_total2 = listtt.get(0).optInt("send_total")+send_total;
			sb.append("update boh_property_interface_upload_summary set send_total="+send_total2+" where  store_id="+storeId+" and business_date='"+reportDate+"' ");
			this.gdao.execute(tenantId, sb.toString());
			
		}
		result.put("send_total2", send_total2);
		return result;
	}
	private JSONObject getUploadMessage(String tenantId,List<JSONObject> bills,JSONObject params,JSONObject obj) throws Exception
	{   
		JSONObject result =new JSONObject();
		List<JSONObject> listaddsend = new ArrayList<JSONObject>();
		List<JSONObject> listsenderror = new ArrayList<JSONObject>();
		int send_total= 0;
		int send_err14=0;
		for (JSONObject bill : bills)
		{	
			    Double payment_amount = bill.optDouble("payment_amount",0.0); 			
				JSONObject jsonResult = null;
				try
				{
					jsonResult = this.dao.sendGetMessage(tenantId,bill,params,obj);
				}
				catch (Exception e)
				{   
					
					throw new Exception("数据返回异常");
				}
				if (!jsonResult.equals(null))
				{
					if (!jsonResult.optString("FMSG").equals("0") )
					{					
							JSONObject joadd = new JSONObject();
							joadd.put("tenancy_id", obj.optString("tenancy_id"));
							joadd.put("type_id", obj.optString("type_id"));
							joadd.put("store_id", obj.optString("store_id"));
							joadd.put("send_id", bill.optString("id"));
							joadd.put("last_update_person", obj.optString("last_operator"));
							joadd.put("last_update_time", obj.optString("last_updatetime"));
							listaddsend.add(joadd);
							send_total++;	
					}
					else
					{   
						listsenderror.add(jsonResult);
						send_err14++;
					}
				}
				else
				{  
					log.info("返回数据：" + jsonResult);
					result.put("success", false);
					result.put("msg", "数据传输异常");
					return result;
				}
		}		
			result.put("send_total", send_total);
	 		result.put("send_err14", send_err14);
	 		result.put("listaddsend", listaddsend);
	 		result.put("listsenderror", listsenderror);
			return result;
			
		}
	
	
}
