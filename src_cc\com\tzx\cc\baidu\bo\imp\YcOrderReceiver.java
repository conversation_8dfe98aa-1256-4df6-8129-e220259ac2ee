package com.tzx.cc.baidu.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import sun.util.logging.resources.logging;

import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

public class YcOrderReceiver extends ThirdPartyOrderReceiver
{
	private JSONObject requestOrder;
	private JSONObject	requestUser;
	private int			discount_fee;		// 优惠总金额
	private int			product_amount;	// 订单商品总金额
	private int			send_fee;			// 配送费
	private double		discountRate;		// 优惠比例=优惠总金额/订单商品总金额
	private double		comboDiscountRate;	// 套餐明细优惠比例=套餐优惠总金额/套餐商品总金额
	private double		sendFeeRate;		// 配送费比例=配送费/订单商品总金额
	private double		platform_side_discount_fee= 0.0;	// 平台方承担的优惠总金额
	private double		takeawayBusinessIncome;		// 外卖的营业收入=用户实付-配送费+平台方承担的金额
	private double		food_sharing_date;		// 菜品分摊的比例=菜品总计/用户实付-配送费+平台方承担的金额
	public YcOrderReceiver()
	{
		super(Constant.YICHI_CHANNEL);
	}

	public YcOrderReceiver(JSONObject order)
	{
		super(order,Constant.YICHI_CHANNEL);
		JSONObject shop = order.optJSONObject("body").optJSONObject("shop");
		this.storeId = shop.optString("id");
		this.tenantId = order.optJSONObject("body").optString("tenancy_id");
	}

	
	@Override
	public JSONObject saveOrderList() throws Exception
	{
		JSONObject yichiorder = new JSONObject();
		// 订单信息
		requestOrder = thirdPartyOrder.optJSONObject("body").optJSONObject("order");
		
		requestUser = thirdPartyOrder.optJSONObject("body").optJSONObject("user");
		
		yichiorder.put("tenancy_id", tenantId);
		
		yichiorder.put("store_id", Integer.parseInt(storeId));
		//订单编号
		yichiorder.put("order_code", orderCode);
		//易吃订单ID
		yichiorder.put("third_order_code",requestOrder.optString("order_id"));
		//每天总的流水号
//		yichiorder.put("serial_number", flow_code);
		
		//是否立即派送
		yichiorder.put("send_immediately", requestOrder.optInt("send_immediately"));
		
		if (requestOrder.optInt("send_immediately") == 1)
		{
			// 期望送达时间???
			yichiorder.put("send_time", "立即配送");
		}
		else
		{
			long a = requestOrder.optLong("send_time");
			String aa = DateUtil.format(new Timestamp(a));
			String[] aaa = aa.split(" ");
			String b = aaa[1].substring(0, 5);
			// 期望送达时间???
			yichiorder.put("send_time", b.toString());
		}
		
		send_fee = requestOrder.optInt("send_fee");
		yichiorder.put("meal_costs", CommonUtil.fen2Yuan(send_fee));
		discount_fee = requestOrder.optInt("discount_fee");
		yichiorder.put("discount_amount", CommonUtil.fen2Yuan(discount_fee));
		int total_fee = requestOrder.optInt("total_fee");
		yichiorder.put("total_money", CommonUtil.yuan2Fen(total_fee));
		int actual_pay = requestOrder.optInt("user_fee");
		yichiorder.put("actual_pay",  CommonUtil.yuan2Fen(actual_pay));
		
		takeawayBusinessIncome=Scm.padd(Scm.psub(Double.valueOf(CommonUtil.fen2Yuan(requestOrder.optInt("user_fee"))), Double.valueOf(CommonUtil.fen2Yuan(send_fee))), platform_side_discount_fee);
		
		// 支付类型
		String payType = requestOrder.optString("pay_type");
		String isOnlinePayment = null;
		if (StringUtils.equals("1", payType))
		{
			isOnlinePayment = "0";
		}
		else if (StringUtils.equals("2", payType))
		{
			isOnlinePayment = "1";
		}
		String payment_state = null;
		if (StringUtils.equals("1", payType))
		{
			payment_state = "01";
		}
		else if (StringUtils.equals("2", payType))
		{
			payment_state = "03";
		}
		yichiorder.put("is_online_payment", isOnlinePayment);
		yichiorder.put("payment_state", payment_state);
		
		// 是否需要发票
		yichiorder.put("need_invoice", requestOrder.optInt("need_invoice"));
		// 发票抬头
		yichiorder.put("invoice_title", requestOrder.optString("invoice_title"));
		// 订单备注
		yichiorder.put("remark", requestOrder.optString("remark"));
		// 物流
		yichiorder.put("delivery_party", requestOrder.optInt("delivery_party"));
		// 订单创建时间
		yichiorder.put("single_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
		
		// 顾客姓名
		yichiorder.put("consigner", requestUser.optString("name"));
		// 顾客电话
		yichiorder.put("consigner_phone", requestUser.optString("phone"));
		// 订餐人
		yichiorder.put("order_name", requestUser.optString("name"));
		// 订餐电话
		yichiorder.put("order_phone", requestUser.optString("phone"));
		// 顾客性别
		yichiorder.put("sex", requestUser.optInt("gender") == 1 ? "man" : "woman");
		// 渠道
		yichiorder.put("chanel", Constant.YICHI_CHANNEL);
		// 送餐地址
		yichiorder.put("address", requestUser.optString("address"));
		// 送餐地址百度经度
		if(!"".equals(requestUser.optJSONObject("coord").optString("longitude")))
		{
			yichiorder.put("longitude", requestUser.optJSONObject("coord").optString("longitude"));
		}
		else
		{
			//高德
			yichiorder.put("longitude", requestUser.optJSONObject("coord_amap").optString("longitude"));
		}
		// 送餐地址百度纬度
		if(!"".equals(requestUser.optJSONObject("coord").optString("latitude")))
		{
			yichiorder.put("latitude", requestUser.optJSONObject("coord").optString("latitude"));
		}
		else
		{
			//高德
			yichiorder.put("latitude", requestUser.optJSONObject("coord_amap").optString("latitude"));
		}
		yichiorder.put("third_order_state", "1");
		yichiorder.put("order_state_desc", "待确认");
		String storeSql = "SELECT a.district_id,a.delivery_fee_id as meals_id,b.service_id from cc_third_organ_info a LEFT JOIN cc_meals_info b on b.id=a.delivery_fee_id where a.shop_id=" + storeId;
		List<JSONObject> list = this.dao.query4Json(tenantId, storeSql);
		if (!list.isEmpty())
		{
			yichiorder.put("district_id", ((JSONObject) list.get(0)).optString("district_id"));
			yichiorder.put("meals_id", ((JSONObject) list.get(0)).optString("meals_id"));
			yichiorder.put("service_id", ((JSONObject) list.get(0)).optString("service_id"));
		}
		this.dao.insertIgnorCase(tenantId, "cc_order_list", yichiorder);
		
		// 保存到redis
//		JSONObject shop = thirdPartyOrder.optJSONObject("body").optJSONObject("shop");
//		JSONObject redisJson = new JSONObject();
//		redisJson.put("shop_id", shop.optString("id"));
//		taskRedisDao.save(requestOrder.optString("order_id").getBytes(), redisJson);

        return yichiorder;
	}

	@Override
	public void saveOrderItem() throws Exception
	{
		discountRate = (double) discount_fee / product_amount;
		sendFeeRate = (double) send_fee / product_amount;
		food_sharing_date=Scm.qdiv(takeawayBusinessIncome, Double.valueOf(CommonUtil.fen2Yuan(product_amount)));

		JSONArray requestProductsArray = thirdPartyOrder.optJSONObject("body").optJSONArray("products");
		JSONObject orderItemParam = null;
		int group_index = 1;
		String price = CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(0).optInt("package_price"));
		int number = 0;// 餐盒费数量
		Map<String, String> unitMap = new HashMap<String, String>();
		int productsListSize = requestProductsArray.size();
		double product_discount_amount = 0.0;// 商品优惠总金额
		double product_send_fee_amount = 0.0;// 商品总配送费
		double share_product_price_total= 0.0;		//总共已经摊多少钱
		for (int i = 0; i < productsListSize; i++)
		{
			int productAmount = requestProductsArray.getJSONObject(i).optInt("product_amount");
			number += (requestProductsArray.getJSONObject(i).optInt("package_amount") * productAmount);

			orderItemParam = new JSONObject();
			// 商户ID
			orderItemParam.put("tenancy_id", tenantId);
			// 明细索引
			orderItemParam.put("group_index", group_index++);
			// 订单号
			orderItemParam.put("order_code", orderCode);

			// 商品ID
			String itemId = requestProductsArray.getJSONObject(i).optString("product_id");
			orderItemParam.put("item_id", itemId);
			String[] productNames = requestProductsArray.getJSONObject(i).optString("product_name").split("_");
			// 商品名称
			String itemName = productNames[0];
			orderItemParam.put("item_name", itemName);
			// 规格ID
			String unitName = "";
			if (productNames.length == 2)
			{
				unitName = productNames[1];
			}
			String unitId = unitMap.get(itemId + unitName);
			if (null == unitId)
			{
				String sql = "select id,unit_name from hq_item_unit where item_id='" + itemId + "'";
				List<JSONObject> jsons = this.dao.query4Json(tenantId, sql);
				for (JSONObject o : jsons)
				{
					unitMap.put(itemId + (CommonUtil.checkStringIsNotEmpty(unitName) ? o.optString("unit_name") : ""), o.optString("id"));
				}
				unitId = unitMap.get(itemId + unitName);
			}
			orderItemParam.put("unit_id", unitId);
			// 份数
			orderItemParam.put("number", productAmount);
			// 商品价格
			orderItemParam.put("price", CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(i).optInt("product_price")));
			// 商品总价
			String product_fee = CommonUtil.fen2Yuan(requestProductsArray.getJSONObject(i).optInt("product_fee"));
			orderItemParam.put("product_fee", product_fee);
			// 优惠金额
			double discount_amount = Scm.pmui(Double.valueOf(product_fee), discountRate);
			comboDiscountRate = (double) discount_amount / Double.valueOf(product_fee);
			// 单品配送费
			double send_fee_amount = Scm.pmui(Double.valueOf(product_fee), sendFeeRate);
			product_discount_amount += discount_amount;
			product_send_fee_amount += send_fee_amount;
			share_product_price_total +=Scm.pmui(Double.valueOf(product_fee), food_sharing_date);
			orderItemParam.put("discount_amount", discount_amount);
			orderItemParam.put("costs", send_fee_amount);
			// 实收金额
			double real_amount = Scm.psub(Double.valueOf(product_fee), discount_amount);
			double costs_real_amount = Scm.padd(real_amount, send_fee_amount);
			orderItemParam.put("real_amount", real_amount);
//			orderItemParam.put("share_amount", costs_real_amount);
			orderItemParam.put("share_amount", Scm.pmui(Double.valueOf(product_fee), food_sharing_date));
			
			// 优惠价格
			// orderItemParam.put("discount_price", Scm.pdiv(costs_real_amount,
			// Double.valueOf(productAmount)));
			// 优惠方式
			if(discount_fee!=0){
				// 优惠类型
				orderItemParam.put("discount_mode_id", "7");
			}
		
			String item_infoSql = "select is_combo from hq_item_info a where a.id=" + itemId;
			List<JSONObject> list = this.dao.query4Json(tenantId, item_infoSql);
			if (list.size() > 0)
			{

				JSONObject item_info_obj = new JSONObject();
				item_info_obj = list.get(0);
				if (item_info_obj.optString("is_combo").equalsIgnoreCase("y"))
				{

					List<JSONObject> order_item_details_list = new ArrayList<JSONObject>();
					String hq_item_combo_details_sql = "select * from hq_item_combo_details a where a.iitem_id=" + itemId;
					List<JSONObject> item_combo_details_list = this.dao.query4Json(tenantId, hq_item_combo_details_sql);
					// 套餐明细表
					if (item_combo_details_list.size() > 0)
					{
						int b = 1;
						for (int k = 0; k < item_combo_details_list.size(); k++)
						{
							b++;
							JSONObject item_combo_details_obj = item_combo_details_list.get(k);
							double combo_discount_amount = 0.0;// 套餐已优惠总金额
							int combo_num = item_combo_details_obj.optInt("combo_num");
							if (b == item_combo_details_list.size())
							{
								// 套餐明细是项目组
								if (item_combo_details_obj.optString("is_itemgroup").equalsIgnoreCase("y"))
								{
									hq_item_combo_details_sql = "select a.* ,b.standard_price from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id  where a.item_group_id=" + item_combo_details_obj.optInt("details_id");
									List<JSONObject> item_group_details_list = this.dao.query4Json(tenantId, hq_item_combo_details_sql.toString());
									if (item_group_details_list.size() > 0)
									{
										for (int l = 0; l < combo_num; l++)
										{
											JSONObject item_group_details_obj = item_group_details_list.get(l);
											item_group_details_obj.put("group_index", group_index - 1);
											item_group_details_obj.put("order_code", orderCode);
											item_group_details_obj.put("item_id", item_group_details_obj.optString("item_id"));
											item_group_details_obj.put("unit_id", item_group_details_obj.optString("item_unit_id"));
											item_group_details_obj.put("price", item_combo_details_obj.optString("standardprice"));
											item_group_details_obj.put("number", productAmount);
											item_group_details_obj.put("product_fee", (item_combo_details_obj.optDouble("standardprice")) * productAmount);
											item_group_details_obj.put("discount_amount", discount_amount - combo_discount_amount);
											item_group_details_obj.put("real_amount", Scm.psub(item_group_details_obj.optDouble("product_fee"), item_group_details_obj.optDouble("discount_amount")));
											item_group_details_obj.remove("id");
											order_item_details_list.add(item_group_details_obj);
										}

									}
								}
								else
								// 套餐明细不是项目组
								{
									item_combo_details_obj.remove("id");
									item_combo_details_obj.put("order_code", orderCode);
									item_combo_details_obj.put("unit_id", item_combo_details_obj.optString("item_unit_id"));
									item_combo_details_obj.put("price", item_combo_details_obj.optString("standardprice"));
									item_combo_details_obj.put("number", productAmount * combo_num);
									item_combo_details_obj.put("product_fee", (item_combo_details_obj.optDouble("standardprice")) * (item_combo_details_obj.optDouble("number")));
									item_combo_details_obj.put("discount_amount", discount_amount - combo_discount_amount);
									item_combo_details_obj.put("real_amount", Scm.psub(item_combo_details_obj.optDouble("product_fee"), item_combo_details_obj.optDouble("discount_amount")));
									item_combo_details_obj.put("item_id", item_combo_details_obj.optString("details_id"));
									item_combo_details_obj.put("group_index", group_index - 1);
									order_item_details_list.add(item_combo_details_obj);
								}
							}
							else
							{
								// 套餐明细是项目组
								if (item_combo_details_obj.optString("is_itemgroup").equalsIgnoreCase("y"))
								{
									hq_item_combo_details_sql = "select a.* ,b.standard_price from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id  where a.item_group_id=" + item_combo_details_obj.optInt("details_id");
									List<JSONObject> item_group_details_list = this.dao.query4Json(tenantId, hq_item_combo_details_sql.toString());

									if (item_group_details_list.size() > 0)
									{
										for (int l = 0; l < combo_num; l++)
										{
											JSONObject item_group_details_obj = item_group_details_list.get(l);
											item_group_details_obj.put("group_index", group_index - 1);
											item_group_details_obj.put("order_code", orderCode);
											item_group_details_obj.put("item_id", item_group_details_obj.optString("item_id"));
											item_group_details_obj.put("unit_id", item_group_details_obj.optString("item_unit_id"));
											item_group_details_obj.put("price", item_combo_details_obj.optString("standardprice"));
											item_group_details_obj.put("number", productAmount);
											item_group_details_obj.put("product_fee", (item_combo_details_obj.optDouble("standardprice")) * productAmount);
											item_group_details_obj.put("discount_amount", Scm.pmui(item_group_details_obj.optDouble("product_fee"), comboDiscountRate));
											combo_discount_amount += Scm.pmui(item_group_details_obj.optDouble("product_fee"), comboDiscountRate);
											item_group_details_obj.put("real_amount", Scm.psub(item_group_details_obj.optDouble("product_fee"), item_group_details_obj.optDouble("discount_amount")));
											item_group_details_obj.remove("id");
											order_item_details_list.add(item_group_details_obj);
										}

									}
								}
								else
								// 套餐明细不是项目组
								{
									item_combo_details_obj.remove("id");
									item_combo_details_obj.put("order_code", orderCode);
									item_combo_details_obj.put("unit_id", item_combo_details_obj.optString("item_unit_id"));
									item_combo_details_obj.put("price", item_combo_details_obj.optString("standardprice"));
									item_combo_details_obj.put("number", productAmount * combo_num);
									item_combo_details_obj.put("product_fee", (item_combo_details_obj.optDouble("standardprice")) * (item_combo_details_obj.optDouble("number")));
									item_combo_details_obj.put("discount_amount", Scm.pmui(item_combo_details_obj.optDouble("product_fee"), comboDiscountRate));
									combo_discount_amount += Scm.pmui(item_combo_details_obj.optDouble("product_fee"), comboDiscountRate);
									item_combo_details_obj.put("real_amount", Scm.psub(item_combo_details_obj.optDouble("product_fee"), item_combo_details_obj.optDouble("discount_amount")));
									item_combo_details_obj.put("item_id", item_combo_details_obj.optString("details_id"));
									item_combo_details_obj.put("group_index", group_index - 1);
									order_item_details_list.add(item_combo_details_obj);
								}
							}

						}
					}
					this.dao.insertBatchIgnorCase(tenantId, "cc_order_item_details", order_item_details_list);
				}

			}

			this.dao.insertIgnorCase(tenantId, "cc_order_item", orderItemParam);
		}
		// 保存餐盒费信息
		JSONObject packageBoxFee = new JSONObject();
		packageBoxFee.put("tenancy_id", tenantId);
		packageBoxFee.put("group_index", group_index);
		packageBoxFee.put("order_code", orderCode);
		packageBoxFee.put("price", price);
		packageBoxFee.put("number", number);
		String sql = "SELECT item_id,unit_id,item_name FROM cc_third_organ_info where shop_id=".concat(storeId);
		JSONObject pb = this.dao.query4Json(tenantId, sql).get(0);
		packageBoxFee.putAll(pb);
		String package_fee = CommonUtil.fen2Yuan(requestOrder.optInt("package_fee"));
		packageBoxFee.put("product_fee", package_fee);
		// 优惠金额
		double packageBox_discount_amount = Scm.psub(Double.valueOf(CommonUtil.fen2Yuan(discount_fee)), product_discount_amount);
		packageBoxFee.put("discount_amount", packageBox_discount_amount);
		// 菜品摊的配送费
		double packageBox_send_fee_amount = Scm.psub(Double.valueOf(CommonUtil.fen2Yuan(send_fee)), product_send_fee_amount);
		packageBoxFee.put("costs", packageBox_send_fee_amount);
		// 实收金额
		double packageBox_real_amount = Scm.psub(Double.valueOf(package_fee), packageBox_discount_amount);
		// packageBoxFee.put("real_amount",packageBox_real_amount);

		double packageBox_send_fee_real_amount = Scm.padd(packageBox_real_amount, packageBox_send_fee_amount);
		packageBoxFee.put("real_amount", packageBox_real_amount);
//		packageBoxFee.put("share_amount", packageBox_send_fee_real_amount);
		packageBoxFee.put("share_amount", Scm.psub(takeawayBusinessIncome, share_product_price_total));
		// 优惠价格
		// packageBoxFee.put("discount_price",
		// Scm.pdiv(packageBox_send_fee_real_amount, Double.valueOf(number)));
		// 优惠方式
		if(discount_fee!=0){
			// 优惠类型
			packageBoxFee.put("discount_mode_id", "7");
		}
		
		this.dao.insertIgnorCase(tenantId, "cc_order_item", packageBoxFee);

	}

	@Override
	public void saveOrderRepayment() throws Exception
	{
		// 支付类型
		String payType = requestOrder.optString("pay_type");
		if (StringUtils.equals("2", payType))
		{
			JSONObject payment = new JSONObject();
			payment.put("tenancy_id", tenantId);
			String paySql = "SELECT a.id payment_id FROM payment_way a LEFT JOIN payment_way_of_ogran b on a.id=b.payment_id where a.payment_class='baidu_pay' and b.organ_id=" + storeId;
			List<JSONObject> list = this.dao.query4Json(tenantId, paySql);
			if (!list.isEmpty())
			{
				payment.put("payment_id", ((JSONObject) list.get(0)).optString("payment_id"));
			}
			payment.put("order_code", orderCode);
			payment.put("pay_money", CommonUtil.fen2Yuan(requestOrder.optInt("user_fee")));
			payment.put("third_bill_code", requestOrder.optString("order_id"));
			this.dao.insertIgnorCase(tenantId, "cc_order_repayment", payment);
		}
	}

	@Override
	public void saveOrderDiscount() throws Exception
	{
		JSONArray requestDiscountArray = thirdPartyOrder.optJSONObject("body").optJSONArray("discount");
		JSONObject discountParam = null;
		if(requestDiscountArray!=null && requestDiscountArray.size()>0)
		{
			for (int i = 0; i < requestDiscountArray.size(); i++)
			{
				discountParam = new JSONObject();
				// 商户ID
				discountParam.put("tenancy_id", tenantId);
				// 订单号
				discountParam.put("order_code", orderCode);
				// 优惠类型
				discountParam.put("discount_type", requestDiscountArray.getJSONObject(i).optString("type"));
				// 优惠金额
				discountParam.put("discount_fee", CommonUtil.fen2Yuan(requestDiscountArray.getJSONObject(i).optInt("fee")));
				// 活动ID
				discountParam.put("activity_id", requestDiscountArray.getJSONObject(i).optString("activity_id"));
				// 百度承担金额
				discountParam.put("baidu_rate", requestDiscountArray.getJSONObject(i).optString("baidu_rate"));
				// 商户承担金额
				discountParam.put("shop_rate", requestDiscountArray.getJSONObject(i).optString("shop_rate"));
				// 代理商承担金额
				discountParam.put("agent_rate", requestDiscountArray.getJSONObject(i).optString("agent_rate"));
				// 物流承担金额
				discountParam.put("logistics_rate", requestDiscountArray.getJSONObject(i).optString("logistics_rate"));
				// 优惠描述
				discountParam.put("discount_desc", requestDiscountArray.getJSONObject(i).optString("desc"));
				
				this.dao.insertIgnorCase(tenantId, "cc_order_discount", discountParam);
			}
		}
	}
//at 2017-08-15
//	@Override
//	public void saveCustomerInfoAndAddress() 
//	{
//		try
//		{
//			JSONObject address = new JSONObject();
//			address.put("order_phone", requestUser.optString("phone"));
//			address.put("address", requestUser.optString("address"));
//			address.put("consignee", requestUser.optString("name"));
//			address.put("consignee_phone", requestUser.optString("phone"));
//			address.put("longitude", requestUser.optJSONObject("coord").optString("longitude"));
//			address.put("latitude", requestUser.optJSONObject("coord").optString("latitude"));
//			address.put("sex", requestUser.optInt("gender") == 1 ? "man" : "woman");
//			address.put("baidu_location", requestUser.optString("address"));
//			// 根据订餐电话判断是不是会员
//			String customer_id = placeOrderManagementService.loadCustomerByPhone(tenantId, address);
//			if (!"".equals(customer_id))
//			{
//				String sql = "update crm_customer_address set address='" + address.optString("address") + "',consignee='" + address.optString("consignee") + "',consignee_phone='" + address.optString("consignee_phone") + "',longitude='" + address.optString("longitude") + "',latitude='"
//						+ address.optString("latitude") + "',sex='" + address.optString("sex") + "',baidu_location='" + address.optString("address") + "' where customer_id='" + customer_id + "'";
//				this.dao.execute(tenantId, sql);
//			}
//			else
//			{
//				JSONObject info = new JSONObject();
//				info.put("tenancy_id", tenantId);
//				info.put("name", requestUser.optString("name"));
//				info.put("sex", requestUser.optInt("gender") == 1 ? "man" : "woman");
//				info.put("mobil", requestUser.optString("phone"));
//				info.put("add_chanel", channel);
//				info.put("add_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
//				info.put("store_id", storeId);
//				customer_id = this.dao.insertIgnorCase(tenantId, "crm_customer_info", info).toString();
//				address.put("customer_id", customer_id);
//				address.put("tenancy_id", tenantId);
//				this.dao.insertIgnorCase(tenantId, "crm_customer_address", address);
//			}
//		}
//		catch (Exception e)
//		{
//			e.printStackTrace();
//		}
//	}

	@Override
	public JSONObject orderStatusPush(JSONObject params)
	{
		JSONObject body = JSONObject.fromObject(params.optString("body"));
		String tenancy_id = body.optString("tenancy_id");
		String organ_id = body.optString("organ_id");
		try
		{
			String thirdOrderCode = body.optString("order_id");
			JSONObject redisJson = taskRedisDao.read(thirdOrderCode.getBytes());
			taskRedisDao.save(thirdOrderCode.getBytes(), redisJson);
			
			storeId = organ_id;
			tenantId = tenancy_id;

			DBContextHolder.setTenancyid(tenantId);

			int status = body.optInt("status");
			String orderState = null;
			switch (status)
			{
				case 10:
					orderState = "08";
					JSONObject dataObj = new JSONObject();
					dataObj.put("tenancy_id", tenantId);
					dataObj.put("store_id", storeId);
					dataObj.put("third_order_code", thirdOrderCode);
					orderManagementService.orderCancel(tenantId, dataObj);
					break;
				case 9:
					orderState = "10";
					JSONObject data_complete_Obj = new JSONObject();
					data_complete_Obj.put("tenancy_id", tenantId);
					data_complete_Obj.put("store_id", storeId);
					data_complete_Obj.put("third_order_code", thirdOrderCode);
					orderManagementService.orderComplete(tenantId, data_complete_Obj);
					break;
				default:
					break;
			}

			String sql = "update cc_order_list set order_state='" + orderState + "' where third_order_code='" + thirdOrderCode + "'";
			dao.execute(tenantId, sql);

			body.put("errno", 0);

		}
		catch (Exception e)
		{
			e.printStackTrace();

			body.put("errno", 200);
			body.put("error", "生成响应数据时失败！");
			body.put("errmsg", e);
		}

		return body;
	}

	@Override
	public JSONObject orderStatusGet(JSONObject params)
	{
		return null;
	}

	@Override
	JSONObject thirdPartyResponse()
	{
		return null;
	}

	@Override
	protected void generateOrderCode()
	{
		// TODO Auto-generated method stub
		
	}
}
