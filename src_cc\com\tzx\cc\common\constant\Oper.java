package com.tzx.cc.common.constant;

public enum Oper
{
	
	//电话外卖接口相关
	/**
	 * 登录（账号验证）
	 */
	check,
	/**
	 * 数据同步获取
	 */
	get,
	/**
	 * 地址信息新增
	 */
	add,
	/**
	 * 微信端加菜
	 */
	updatedish,
	/**
	 * 预下单支付
	 */
	scan,
	/**
	 * 支付超时修改订单支付状态为已失效
	 */
	paytimeout,
	/**
	 * 消费
	 */
	consume,
	/**
	 * 准备验劵
	 */
	prepare,
	
	/**
	 * 查询会员信息
	 */
     find,
	//--------------------------------------------
	/*
	 * 创建
	 */
	create,	
	/*
	 * 更新
	 */
	update,	
	/*
	 * 插入
	 */
	insert,	
	/*
	 * 删除
	 */
	delete,	
	/*
	 * 查询
	 */
	query,	
	trade_detail,
	/*
	 * 查询订单状态
	 */
	order_state_query,	
	/*
	 * 开业
	 */
	open,	
	/*
	 * 歇业
	 */
	close,	
	/*
	 * 下线
	 */
	offline,	
	/*
	 * 上线
	 */
	online,	
	/*
	 * 设置
	 */
	set,	
	/*
	 * 确认
	 */
	confirm,	
	/*
	 * 取消
	 */
	cancle,	
	/*
	 * 完成
	 */
	complete,	
	/*
	 * 启用
	 */
	start,
    /**
     * 升级反馈
     */
    feedback,
    query_mq_order
    ;
}
