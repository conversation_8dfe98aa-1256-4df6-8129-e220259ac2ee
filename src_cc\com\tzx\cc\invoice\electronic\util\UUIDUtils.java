package com.tzx.cc.invoice.electronic.util;

import java.util.Date;

/**
 * 生成唯一值工具类
 * <AUTHOR>
 *
 */
public class UUIDUtils {
	private static Date date = new Date();
	private static int seq = 0;
	private static final int ROTATION = 99999;

	public static synchronized long next() {
		if (seq > ROTATION)
			seq = 0;
		date.setTime(System.currentTimeMillis());
		String str = String.format("%1$tY%1$tm%1$td%1$tk%1$tM%1$tS%2$05d",
				date, seq++);
		return Long.parseLong(str);
	}
}
