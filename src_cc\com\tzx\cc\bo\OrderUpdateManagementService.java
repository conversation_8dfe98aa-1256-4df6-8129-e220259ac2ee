package com.tzx.cc.bo;

import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.tzx.cc.bo.dto.Data;
import com.tzx.framework.common.exception.SystemException;

public interface OrderUpdateManagementService {

	String NAME = "com.tzx.cc.bo.imp.OrderUpdateManagementServiceImpl";

	/**
	 * 订单状态修改信息（取单,派单,配送,完成）
	 * 
	 * @param Data
	 * @return Data
	 * @throws SystemException
	 */

	public Map<String,Object> orderManage(Data param) throws SystemException,Exception;
	
	//保存订单点菜
	public void orderSave(Data param) throws SystemException,Exception;
	
	//订单点菜下发
	public JSONObject orderIssued(Data param) throws SystemException,Exception;

	//订单查询
	void orderQuery(Data data) throws SystemException, Exception;


	List<JSONObject> getMemberIntegral(JSONObject order_list) throws Exception;

	List<JSONObject> getMemberCards(JSONObject order_list) throws Exception;
	//订单数据组装
	Data orderDataAssembly(Data data, JSONObject obj) throws SystemException, Exception;
	//订单加菜
	void orderUpdateDish(Data param) throws SystemException, Exception;
	//订单加菜下发
	JSONObject orderUpdateDishIssued(Data data) throws SystemException, Exception;

	void orderStateQuery(Data param) throws SystemException, Exception;
	//获取断号丢失断订单信息
	JSONArray getMissOrderInfo(JSONObject requestParams) throws Exception;
}
