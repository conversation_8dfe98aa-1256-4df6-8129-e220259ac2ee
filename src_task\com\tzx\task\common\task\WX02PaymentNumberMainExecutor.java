package com.tzx.task.common.task;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.log4j.Logger;

import net.sf.json.JSONObject;

import com.tzx.task.common.util.TaskThreadPoolExecutor;
import com.tzx.task.po.redis.dao.TaskRedisDao;
import com.tzx.weixin.po.redis.dao.RedisTemplateDao;

import net.sf.json.JSONObject;


public class WX02PaymentNumberMainExecutor implements Runnable{

	
	@Resource(name = TaskRedisDao.NAME)
	private TaskRedisDao					taskRedisDao;
	

	private static final Logger		log	= Logger.getLogger(WX02PaymentNumberMainExecutor.class);
	
	
	public final static String key = "wxpaymentbillnum";
	
	@Resource(name = RedisTemplateDao.NAME)
	private RedisTemplateDao redisTemplateDao;
	
	@Override
	public void run() {
		int count = 0;
		int count2 = 0;
		Long len = taskRedisDao.len(key.getBytes());
		if (taskRedisDao.exists(key.getBytes())){
			ThreadPoolExecutor executor = TaskThreadPoolExecutor.getThreadPool(this.getClass().getName(), 
					10, 15, 3, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(10),
					new ThreadPoolExecutor.CallerRunsPolicy() );
			while(count<10){
				JSONObject jsonobj = taskRedisDao.read(key.getBytes());
				try {
					if(null == jsonobj || jsonobj.isEmpty()){
						count++;
						continue ;
					}
					
					/**
					 * 当json对象中没有计数   或者执行次数小于4 或者不存在时间  或者当前时间大于存入时间 则执行  并且count++
					 * 通过count++保证任务能开启最大线程数
					 */
					
					if(jsonobj.optInt("oper")==0){
						//普通
						if( !jsonobj.containsKey("LongTime") || System.currentTimeMillis()> jsonobj.optLong("LongTime")){
							log.info("WX02PaymentNumberMainExecutor定时任务开始,redis中json为:"+jsonobj.toString()+"----------------当前毫秒值为"+System.currentTimeMillis());
							
							executor.execute(new WX02PaymentNumbertaskThread(jsonobj));
							log.info("开启线程成功");
							
							count++;
						}else{
							/**
							 * 不满足条件次数过多说明队列中没有新加入数据  或者没有时间条件满足的数据  计数20次后跳出循环
							 * 
							 * 满足执行次数条件但是还未到执行时间时 再次存入redis队列中
							 */
							taskRedisDao.lpush(key.getBytes(), jsonobj);
							count2++;
						}
					}else{
						if(!jsonobj.containsKey("count") || jsonobj.optInt("count")<10){
							if( !jsonobj.containsKey("LongTime") || System.currentTimeMillis()> jsonobj.optLong("LongTime")){
								log.info("WX02PaymentNumberMainExecutor定时任务开始,redis中json为:"+jsonobj.toString()+"----------------当前毫秒值为"+System.currentTimeMillis());
						
								executor.execute(new WX02PaymentNumbertaskThread(jsonobj));
								log.info("开启线程成功");
								
								
								count++;
							}else{
								/**
								 * 不满足条件次数过多说明队列中没有新加入数据  或者没有时间条件满足的数据  计数20次后跳出循环
								 * 
								 * 满足执行次数条件但是还未到执行时间时 再次存入redis队列中
								 */
								taskRedisDao.lpush(key.getBytes(), jsonobj);
								count2++;
							}
						}else{
							count2++;
						}
					}
					if(count2 > 20){//防止死循环
						break;
					}
				} catch (Exception e) {
					log.info("定时任务发生异常，该任务已经被重新扔回队列，json:"+jsonobj.toString());
					taskRedisDao.lpush(key.getBytes(), jsonobj);
					e.printStackTrace();
				}
			}
		}
	}

}
