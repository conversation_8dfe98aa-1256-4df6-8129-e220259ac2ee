package com.tzx.report.common.statistics.task;

import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.report.bo.report.RequestUrlCountService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by zds on 2018-11-08.
 */
public class RequestUrlTask implements Runnable {

    private static Logger LOG = LoggerFactory.getLogger(RequestUrlTask.class);

    private final JSONObject param ;

    private static  Executor executor;

    private RequestUrlCountService requestUrlCountService;

    public RequestUrlTask(JSONObject param) {
        this.param = param;
    }


    @Override
    public void run() {
        LOG.info("ThreadName:"+Thread.currentThread().getName());
        DBContextHolder.setTenancyid(param.optString("tenentid"));
        requestUrlCountService.updateCount(param);
    }

    public void startTask(){
        LOG.info("startTask param:{}"+param.toString());
        if(null ==executor){
            executor =CountUrlThreadPool.getDefaultPoolExecutor();
        }
        if(null ==requestUrlCountService){
            requestUrlCountService = (RequestUrlCountService) SpringConext.getBean(RequestUrlCountService.NAME);
        }
        executor.execute(this);
    }

     static class CountUrlThreadPool {
        private static  int corePoolSize =4;
        private static  int maximumPoolSize =10;
        private static  long keepAliveTime =3600;
        private static  int queryCapacity =128;
        private static ThreadPoolExecutor threadPoolExecutor;

        static ThreadPoolExecutor getDefaultPoolExecutor(){
            if (threadPoolExecutor == null) {
                synchronized (CountUrlThreadPool.class) {
                    if (threadPoolExecutor == null) {
                        threadPoolExecutor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS,
                                new ArrayBlockingQueue<Runnable>(queryCapacity),
                                new CountUrlThreadFactory(),  new ThreadPoolExecutor.CallerRunsPolicy());

                    }
                }
            }
            return threadPoolExecutor;
        }

    }

     static class CountUrlThreadFactory implements ThreadFactory {

        private static final AtomicInteger poolNumber = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        CountUrlThreadFactory () {
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() :
                    Thread.currentThread().getThreadGroup();
            namePrefix = "RequestUrlPool-" +
                    poolNumber.getAndIncrement() +
                    "-thread-";
        }

        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r,
                    namePrefix + threadNumber.getAndIncrement(),
                    0);
            if (t.isDaemon())
                t.setDaemon(false);
            if (t.getPriority() != Thread.NORM_PRIORITY)
                t.setPriority(Thread.NORM_PRIORITY);
            return t;
        }
    }

}
