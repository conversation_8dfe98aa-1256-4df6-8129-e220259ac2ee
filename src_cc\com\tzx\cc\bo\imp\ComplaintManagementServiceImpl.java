package com.tzx.cc.bo.imp;

import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.cc.bo.ComplaintManagementService;
import com.tzx.framework.common.util.dao.GenericDao;

@Service(ComplaintManagementService.NAME)
public class ComplaintManagementServiceImpl implements ComplaintManagementService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Override
	public JSONObject loadOrderReasonDetailList(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT DISTINCT(A.id) as distinct_id,a.*,b.consigner,b.consigner_phone,c.reason_name,d.org_full_name,d.organ_code,b.store_id from cc_order_reason_detail a  LEFT JOIN hq_unusual_reason c on c.id= a.reason_type left join cc_order_list b on b.order_code=a.order_code left join organ d on d.id = b.store_id where a.type='TS02' ");
		if (condition.containsKey("order_code") && !"".equals(condition.optString("order_code")))
		{
			sql.append(" and  b.order_code like '%" + condition.optString("order_code") + "%'");
		}
		if (condition.containsKey("consigner_phone") && !"".equals(condition.optString("consigner_phone")))
		{
			sql.append(" and  b.consigner_phone like '%" + condition.optString("consigner_phone") + "%'");
		}

		if (condition.containsKey("organ_code") && !"".equals(condition.optString("organ_code")))
		{
			sql.append(" and  d.organ_code like '%" + condition.optString("organ_code") + "%'");
//			sql.append(" and d.id in (select * from get_oids_bycode('"+condition.get("organ_code").toString().trim()+"')) ");
		}

		//
		// if (condition.containsKey("complaints_time_from") &&
		// !"".equals(condition.optString("complaints_time_from")) )
		// {
		// sql.append(" and  a.complaints_time >= TO_DATE('" +
		// condition.get("complaints_time_from") + "','YYYY-MM-DD') ");
		// }
		// if (condition.containsKey("complaints_time_to") &&
		// !"".equals(condition.optString("complaints_time_to")) )
		// {
		// sql.append(" and  a.complaints_time <= TO_DATE('" +
		// condition.get("complaints_time_to") + "','YYYY-MM-DD') ");
		// }
		if (condition.containsKey("complaints_time_from") && !"".equals(condition.optString("complaints_time_from")))
		{
			sql.append(" and  to_date(to_char(A.complaints_time,'YYYY-MM-DD'),  'YYYY-MM-DD') >= TO_DATE('" + condition.get("complaints_time_from") + "','YYYY-MM-DD') ");
		}
		if (condition.containsKey("complaints_time_to") && !"".equals(condition.optString("complaints_time_to")))
		{
			sql.append(" and  to_date(to_char(A.complaints_time,'YYYY-MM-DD'),  'YYYY-MM-DD') <= TO_DATE('" + condition.get("complaints_time_to") + "','YYYY-MM-DD') ");
		}

		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenancyID, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));

		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}

	public String saveOrUpdate(String tenancyID, JSONObject obj) throws Exception
	{
		String result = "";
		if (obj.optInt("id") > 0)
		{

			obj.put("type", "TS02");
			result = "{\"success\" : true , \"msg\" : \"修改成功!\"}";
			this.dao.updateIgnorCase(tenancyID, "cc_order_reason_detail", obj);

		}

		return result;
	}

}
