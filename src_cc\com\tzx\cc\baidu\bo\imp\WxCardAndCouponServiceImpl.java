package com.tzx.cc.baidu.bo.imp;

import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.cc.baidu.bo.WxCardAndCouponService;
import com.tzx.crm.bo.imp.CrmCardClassSetServiceImp;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.weixin.common.util.WXFront;

@Service(WxCardAndCouponService.NAME)
public class WxCardAndCouponServiceImpl extends GenericDaoImpl implements WxCardAndCouponService {

	
	private static final Logger		log	= Logger.getLogger(WxCardAndCouponServiceImpl.class);
	
	@Override
	public JSONObject findWXKQdglist(String tenancyId, JSONObject condition)
			throws Exception {
		JSONObject result = new JSONObject();
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		StringBuffer sb = new StringBuffer();
		long total = 0l;
		List<JSONObject> list = null;
		if(!condition.containsKey("store_id")){
			result.put("page", 1);
			result.put("total", 0);
			list = new ArrayList<JSONObject>();
			result.put("rows", list);
			return result;
		}
		sb.append("select  *,'update' as oper from wx_organ_details where 1=1 ");
		if(condition.containsKey("store_id") && condition.optInt("store_id")>0){
			sb.append(" and store_id = "+condition.optInt("store_id"));
		}
		total = this.countSql(tenancyId, sb.toString());
		if(total == 0 ){
			sb.setLength(0);
			sb.append("select id,id as store_id ,org_full_name as business_name ,phone as telephone,longitude ,latitude,last_operator,last_updatetime, 'add' as oper from organ  where 1=1 ");
			if(condition.containsKey("store_id") && condition.optInt("store_id")>0){
				sb.append(" and id = "+condition.optInt("store_id"));
			}
		}
		total = this.countSql(tenancyId, sb.toString());
		if(total>0){
			list = this.query4Json(tenancyId, this.buildPageSql(condition,sb.toString()));
			if(null != list && list.size() >0){
				JSONObject json = list.get(0);
				int success_state = json.optInt("success_state");
				String oper = json.optString("oper");
				
				if(success_state == 0 && "update".equals(oper)){//更新时 查询结果同步失败时
					//WXFront.调用旭哥接口拿到返回值做更新,然后填充至list中 返回前台
					
				}
			}
		}
	
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}

	@Override
	public List<JSONObject> findProvinceDetails(String tenantId, JSONObject obj)
			throws Exception {
		StringBuffer sb = new StringBuffer();
		
		sb.append("   select reg_code as id, reg_name as text from sys_regionalism where 1=1  "); 
		if(obj.containsKey("type") && StringUtils.isNotBlank(obj.optString("type"))){
			sb.append(" and reg_type = '"+obj.optString("type")+"'");
		}
		if(obj.containsKey("id") && StringUtils.isNotBlank(obj.optString("id"))){
			sb.append(" and father_id = '"+obj.optInt("id")+"'");
		}
		sb.append("  and valid_state='Y' ORDER BY reg_code ASC ");
		List<JSONObject> prolist = this.query4Json(tenantId, sb.toString());
		
	/*	sb.setLength(0);
		sb.append("   select * from sys_regionalism where reg_type = 'city' and valid_state='Y' ORDER BY reg_code ASC   ");   
		List<JSONObject> citylist = this.query4Json(tenantId, sb.toString());
		
		sb.setLength(0);
		sb.append("   select * from sys_regionalism where reg_type = 'county' and valid_state='Y' ORDER BY reg_code ASC   "); 
		List<JSONObject> counlist = this.query4Json(tenantId, sb.toString());
*/
		
		/*for(int i =0 ; i<citylist.size();i++){
			JSONObject city = citylist.get(i);
			int reg_code = city.optInt("reg_code");
			for(int a = 0 ;a <counlist.size();a++){
				JSONObject coun = counlist.get(a);
				int fid = coun.optInt("father_id");
				if(fid == reg_code){
					if(city.containsKey("children")){
						JSONArray arr = city.optJSONArray("children");
						arr.add(coun);
						city.put("children", arr);
					}else{
						JSONArray arr = new JSONArray();
						arr.add(coun);
						city.put("children", arr);
					}
				}
			}
		}
		
		
		for(int i =0 ; i<prolist.size();i++){
			JSONObject province = prolist.get(i);
			int reg_code = province.optInt("reg_code");
			for(int a = 0 ;a <citylist.size();a++){
				JSONObject city = citylist.get(a);
				int fid = city.optInt("father_id");
				if(fid == reg_code){
					if(province.containsKey("children")){
						JSONArray arr = city.optJSONArray("children");
						arr.add(city);
						province.put("children", arr);
					}else{
						JSONArray arr = new JSONArray();
						arr.add(city);
						province.put("children", arr);
					}
				}
			}
		}*/
		
		return prolist;
	}

	@Override
	public List<JSONObject> findStoreCategories(String tenantId, JSONObject obj)
			throws Exception {
		StringBuffer sb = new StringBuffer();
		sb.append("   select id ,category_name as text,father_id,category_name from wx_organ_category_details where category_level = 2 ORDER BY id ASC  "); 
		List<JSONObject> biglist = this.query4Json(tenantId, sb.toString());
		sb.setLength(0);
		sb.append("   select id ,category_name as text,father_id,category_name from wx_organ_category_details where category_level = 3 ORDER BY id ASC  ");   
		List<JSONObject> smalllist = this.query4Json(tenantId, sb.toString());

		for(int i =0 ; i<biglist.size();i++){
			JSONObject big = biglist.get(i);
			int id = big.optInt("id");
			for(int a = 0 ;a <smalllist.size();a++){
				JSONObject small = smalllist.get(a);
				int fid = small.optInt("father_id");
				if(fid == id){
					if(big.containsKey("children")){
						JSONArray arr = big.optJSONArray("children");
						arr.add(small);
						big.put("children", arr);
					}else{
						JSONArray arr = new JSONArray();
						arr.add(small);
						big.put("children", arr);
					}
				}
			}
		}
		return biglist;
	}

	@Override
	public void saveInfoUrl(String tenantId, JSONObject obj) throws Exception {
		StringBuffer sb =new StringBuffer();
		String oper = obj.optString("oper");
		String image_url = obj.optString("image_url");
		String[] image_urlArr = image_url.split(",");
		String wx_image_url="";
		for(int i=0;i<image_urlArr.length;i++){
			JSONObject upload1 = WXFront.upload1(image_urlArr[i], tenantId);
			String url = upload1.optString("url");
			if(null!=url&&!url.equals("")&&!url.equals("null")){
				if(i!=(image_urlArr.length-1)){
					wx_image_url+=url+",";
				}else{
					wx_image_url+=url;
				}
			}
		}
		if(!wx_image_url.equals("")){
			obj.put("wx_image_url", wx_image_url);
		}
		//根据状态判断是新增还是删除
		log.info("微信门店注册保存方法");
		if("add".equals(oper) && obj.optInt("subumit") == 0){//保存动作
			
			this.insertIgnorCase(tenantId, "wx_organ_details", obj);
			
		}else if("add".equals(oper) && obj.optInt("subumit") == 1){//保存并调用微信接口
			this.insertIgnorCase(tenantId, "wx_organ_details", obj);
			JSONObject addStoreList = WXFront.addStoreList(tenantId,obj);
			log.info("微信门店注册保存并调用微信接口"+obj.toString());
			log.info("微信门店注册保存并调用微信接口返回值"+addStoreList.toString());
			if(addStoreList.optString("errmsg").equals("ok")){
				//添加门店成功
				String poi_id = addStoreList.optString("poi_id");
				int store_id = obj.optInt("store_id");
				sb.setLength(0);
				sb.append("update wx_organ_details set");
				sb.append(" last_updatetime='"+DateUtil.getNowDateYYDDMMHHMMSS()+"'");
				sb.append(",poi_id='"+poi_id+"'");
				sb.append(" where store_id="+store_id);
				this.execute(tenantId, sb.toString());
			}else{
				String reason = addStoreList.optString("errmsg");
				int store_id = obj.optInt("store_id");
				sb.append("update wx_organ_details set");
				sb.append(" last_updatetime='"+DateUtil.getNowDateYYDDMMHHMMSS()+"'");
				sb.append(",reason='"+reason+"'");
				sb.append(" where store_id="+store_id);
				this.execute(tenantId, sb.toString());
			}
		}else if("update".equals(oper) && obj.optInt("subumit") == 0){//仅更新
			
			this.updateIgnorCase(tenantId, "wx_organ_details", obj);
			
			
		}else if("update".equals(oper) && obj.optInt("subumit") == 1){//更新并调用微信接口
		
			this.updateIgnorCase(tenantId, "wx_organ_details", obj);
			JSONObject updataStoreMsg = WXFront.updataStoreMsg(tenantId, obj);
			log.info("微信门店注册更新并调用微信接口"+obj.toString());
			log.info("微信门店注册更新并调用微信接口返回值"+updataStoreMsg.toString());
			System.out.println("修改时调用微信接口结果"+updataStoreMsg);
			if(!updataStoreMsg.optString("errmsg").equals("ok")){
				//修改门店失败
				String reason = updataStoreMsg.optString("errmsg");
				int store_id = obj.optInt("store_id");
				sb.append("update wx_organ_details set");
				sb.append(" last_updatetime='"+DateUtil.getNowDateYYDDMMHHMMSS()+"'");
				sb.append(",reason='"+reason+"'");
				sb.append(" where store_id="+store_id);
				this.execute(tenantId, sb.toString());
			}
		}else if("submit".equals(oper)){//表格上同步按钮  更新同步状态并且调用接口同步
		
			this.updateIgnorCase(tenantId, "wx_organ_details", obj);
			
			if(obj.optInt("poi_id")>0){
				JSONObject updataStoreMsg = WXFront.updataStoreMsg(tenantId, obj);
				log.info("微信门店注册同步按钮更新方法"+obj.toString());
				log.info("微信门店注册同步按钮更新方法返回值"+updataStoreMsg.toString());
				if(!updataStoreMsg.optString("errmsg").equals("ok")){
					//修改门店失败
					String reason = updataStoreMsg.optString("errmsg");
					int store_id = obj.optInt("store_id");
					sb.append("update wx_organ_details set");
					sb.append(" last_updatetime='"+DateUtil.getNowDateYYDDMMHHMMSS()+"'");
					sb.append(",reason='"+reason+"'");
					sb.append(" where store_id="+store_id);
					this.execute(tenantId, sb.toString());
				}
			}else{
				JSONObject addStoreList = WXFront.addStoreList(tenantId,obj);
				log.info("微信门店注册同步更新下面的方法"+obj.toString());
				log.info("微信门店注册同步更新下面的方法返回值"+addStoreList.toString());
				System.out.println("保存时调用微信接口结果："+addStoreList);
				if(addStoreList.optString("errmsg").equals("ok")){
					//添加门店成功
					String poi_id = addStoreList.optString("poi_id");
					int store_id = obj.optInt("store_id");
					sb.setLength(0);
					sb.append("update wx_organ_details set");
					sb.append(" last_updatetime='"+DateUtil.getNowDateYYDDMMHHMMSS()+"'");
					sb.append(",poi_id='"+poi_id+"'");
					sb.append(" where store_id="+store_id);
					this.execute(tenantId, sb.toString());
				}else{
					String reason = addStoreList.optString("errmsg");
					int store_id = obj.optInt("store_id");
					sb.append("update wx_organ_details set");
					sb.append(" last_updatetime='"+DateUtil.getNowDateYYDDMMHHMMSS()+"'");
					sb.append(",reason='"+reason+"'");
					sb.append(" where store_id="+store_id);
					this.execute(tenantId, sb.toString());
				}
			}
			
			
		}
		
		
	}

	@Override
	public JSONObject findAreaDetailsUrl(String tenantId, JSONObject obj)
			throws Exception {
		StringBuffer sb = new StringBuffer();
		JSONObject json = new JSONObject();
		sb.append("select  reg_code as id, reg_name as text from sys_regionalism where reg_type = 'provincial' and valid_state = 'Y' ORDER BY id ASC");
		List<JSONObject> prolist = this.query4Json(tenantId, sb.toString());
		json.put("prolist", prolist);
		
		sb.setLength(0);
		sb.append("select  reg_code as id, reg_name as text from sys_regionalism where father_id = '"+obj.optString("pid")+"' and reg_type = 'city' and valid_state = 'Y' ORDER BY id ASC");
		List<JSONObject> citylist = this.query4Json(tenantId, sb.toString());
		json.put("citylist", citylist);
		
		sb.setLength(0);
		sb.append("select  reg_code as id, reg_name as text from sys_regionalism where father_id = '"+obj.optString("cid")+"' and reg_type = 'county' and valid_state = 'Y' ORDER BY id ASC");
		List<JSONObject> counlist = this.query4Json(tenantId, sb.toString());
		json.put("counlist", counlist);
		
		return json;
	}

	@Override
	public JSONObject checkSynchroResult(String tenantId, JSONObject obj)
			throws Exception {
		JSONObject  jj = new JSONObject();
		StringBuffer sb = new StringBuffer();
		JSONObject param = new JSONObject();
		param.put("poi_id", obj.optString("poi_id"));
		log.info("wx门店查询审核参数："+param.toString());
		JSONObject jb = WXFront.queryStoreMsg(tenantId, param);
		log.info("wx门店查询审核结果"+jb.toString());
		int available_state = jb.optInt("available_state");
		boolean success = jb.optBoolean("success");
		if(success && available_state == 1){
			//审核通过
			sb.setLength(0);
			sb.append(" update wx_organ_details set success_state = '1' where poi_id = '"+obj.optString("poi_id")+"'");
			this.execute(tenantId, sb.toString());
			jj.put("success", true);
		}else{
			jj.put("success", false);
			jj.put("available_state", available_state);
		}
		
		return jj;
	}

	
}
