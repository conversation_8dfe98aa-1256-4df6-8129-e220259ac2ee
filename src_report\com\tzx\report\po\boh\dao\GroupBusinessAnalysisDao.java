package com.tzx.report.po.boh.dao;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */
public interface GroupBusinessAnalysisDao {

    String NAME = "com.tzx.report.po.boh.impl.GroupBusinessAnalysisDaoImpl";

    JSONObject find(String tenancyID, JSONObject condition) throws Exception;

    List<JSONObject> getClassItems(String tenancyID, JSONObject condition) throws Exception;

}
