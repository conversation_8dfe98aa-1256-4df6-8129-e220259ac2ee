package com.tzx.cc.baidu.rest.validate;

import java.sql.Timestamp;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.cc.baidu.bo.ShopService;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

@Controller("ThirdValidateRest")
@RequestMapping("/thirdparty/thirdValidateRest")
public class ThirdValidateRest {

	@Resource(name = ShopService.NAME)
	private ShopService shopService;
	
	/**
	 * 验证饿了么餐厅ID是否已被其他店使用
	 *
	 */
	@RequestMapping(value = "validateRestaurantID")
	public @ResponseBody
	JSONObject validateThirdPartRestaurantID(HttpServletRequest request,HttpServletResponse response, @RequestBody JSONObject param) {

		String tenantId = (String) request.getSession().getAttribute("tenentid");

		DBContextHolder.setTenancyid(tenantId);

		JSONObject json = new JSONObject();

		try {
			param.put("operator",request.getSession().getAttribute("employeeName"));
			param.put("operate_time",DateUtil.format(new Timestamp(System.currentTimeMillis())));
			json = shopService.validateThirdShopIDisEffective(tenantId, param);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "-1");
			json.put("error", json.optString("error"));
		}

		return json;
	}

	
}
