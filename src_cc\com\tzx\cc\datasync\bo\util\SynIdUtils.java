package com.tzx.cc.datasync.bo.util;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;

import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;

import com.tzx.cc.datasync.po.springjdbc.dao.DataTransferDao;
import com.tzx.cc.datasync.po.springjdbc.dao.impl.DataTransferDaoImpl;
import com.tzx.framework.common.util.SpringConext;


/**
 * <AUTHOR>
 * Rif同步操作ID的工具类
 */
public class SynIdUtils {
	
	@SuppressWarnings("unused")
	private static final Logger		logger	= Logger.getLogger(SynIdUtils.class);
	
	/**
	 * 获取某个序列名的num个序列值
	 * @param seqName
	 * @param num
	 * @return
	 * @throws Exception 
	 */
	private static Queue<String> generateIdBase(String tenantId,String seqName,int num) throws Exception{
		StringBuffer sb = new StringBuffer();
		for(int i=0; i<num;i++){
			if(i==0) {
				sb.append("select nextval('").append(seqName).append("') as seqnum");
			} else {
				sb.append(" union all");
				sb.append(" select nextval('").append(seqName).append("') as seqnum");
			}
		}
		
		DataTransferDao	dataTransferDao = SpringConext.getApplicationContext().getBean(DataTransferDaoImpl.class);
		List<Map<String, Object>> queryForList = dataTransferDao.getJdbcTemplate(tenantId).queryForList(sb.toString());
		
		Queue<String> returnList = new LinkedList<String>();
		for(Map<String, Object> map : queryForList){
			Object object = map.get("seqnum");
			Long seq = (Long) object;
			returnList.add(seq.toString());
		}
		return  returnList;
	}
	/**
	 * 获取某个序列名的num个序列值
	 * @param seqName
	 * @param num
	 * @return
	 * @throws Exception 
	 */
	public static Queue<String> generateIds(String tenantId,String seqName,int num) throws Exception{
		//最大的序列数字
		int maxnumber = 5000;
		Queue<String> returnList = new LinkedList<String>();
		while(num>maxnumber) {
			Queue<String> generateIds = generateIdBase(tenantId, seqName, maxnumber);
			num=num-maxnumber;
			returnList.addAll(generateIds);
		}
		if(num>0) {
			Queue<String> generateIds = generateIdBase(tenantId, seqName, num);
			returnList.addAll(generateIds);
		}
		return  returnList;
	}
	
	/**
	 * 记录是否是渠道映射的Map 
	 *  渠道映射的Map规则是  表名-->渠道名称-->伪Id（同步过来的Id）-->真实Id。     数据结构如为 ：map.get("表名").get("渠道名称").get("伪Id")
	 *  非渠道映射的Map的规则是 表名-->伪Id（同步过来的Id）-->真实Id。  数据结构如为 ：map.get("表名").get("伪Id")
	 * @param tenantId
	 * @param map map
	 * @param ischannel 是否属于渠道映射
	 * @param tableName 表名数组
	 * @throws DataAccessException
	 * @throws Exception
	 */
	public static void insertId2Maps(String tenantId,Map<String, Map<String, Object>> map,boolean ischannel,String ... tableNames) throws DataAccessException, Exception{
		for(String tableName:tableNames){
			insertId2Map(tenantId, map, ischannel, tableName);
		}
	}

	/**
	 * @param tenantId
	 * @param map
	 * @param ischannel 是否属于渠道映射
	 * @param tableName 表名
	 * @throws Exception
	 */
	private static void insertId2Map(String tenantId,
			Map<String, Map<String, Object>> map, boolean ischannel,
			String tableName) throws Exception {
		Map<String, Object> map2 = map.get(tableName);
		if(map2==null) {
			map2 = new HashMap<String, Object>();
			map.put(tableName, map2);
		}
		if(ischannel) {
			StringBuffer sb = new StringBuffer();
			sb.append("select chanel,fake_id,id from ");
			sb.append(tableName);
			sb.append(" where fake_id is not null");
			
			List<Map<String, Object>> queryForList = queryForList(tenantId, sb.toString());
			for(Map<String, Object> mapping : queryForList){
				Object channel_object = mapping.get("chanel");
				String channel = (String) channel_object;
				@SuppressWarnings("unchecked")
				Map<String, String> channel_map = (Map<String, String>) map2.get(channel);
				if(channel_map==null) {
					channel_map = new HashMap<String, String>();
					map2.put(channel, channel_map);
				}
				Object fake_id_object = mapping.get("fake_id");
				Object id_object = mapping.get("id");
				String fake_id = ((Integer) fake_id_object).toString();
				String id = ((Integer) id_object).toString();
				channel_map.put(fake_id, id);
			}
		} else {
			StringBuffer sb = new StringBuffer();
			sb.append("select fake_id,id from ");
			sb.append(tableName);
			sb.append(" where fake_id is not null");
			
			List<Map<String, Object>> queryForList = queryForList(tenantId, sb.toString());
			for(Map<String, Object> mapping : queryForList){
				Object fake_id_object = mapping.get("fake_id");
				Object id_object = mapping.get("id");
				String fake_id = ((Integer) fake_id_object).toString();
				String id = ((Integer) id_object).toString();
				map2.put(fake_id, id);
			}
		}
	}
	
	/**
	 * 获取某张表某列与某列的映射关系并插入到map中
	 * @param tenantId
	 * @param map
	 * @param tableName
	 * @param keyColumn
	 * @param valueColumn
	 * @throws Exception
	 */
	public static void insertId2Map(String tenantId,
			Map<String, Map<String, Object>> map,
			String fakeKey,String tableName,
			String keyColumn,String valueColumn,String wherestr) throws Exception {
		Map<String, Object> map2 = map.get(tableName);
		if(map2==null) {
			map2 = new HashMap<String, Object>();
			map.put(fakeKey, map2);
		}
		
		StringBuffer sb = new StringBuffer();
		sb.append("select ");
		sb.append(keyColumn);
		sb.append(",");
		sb.append(valueColumn);
		sb.append(" from ");
		sb.append(tableName);
		sb.append(" where fake_id is not null ");
		sb.append(wherestr);
		
		List<Map<String, Object>> queryForList = queryForList(tenantId, sb.toString());
		for(Map<String, Object> mapping : queryForList){
			Object fake_id_object = mapping.get(keyColumn);
			Object id_object = mapping.get(valueColumn);
			String fake_id2 = ((Integer) fake_id_object).toString();
			String id = ((Integer) id_object).toString();
			map2.put(fake_id2, id);
		}
	}

	/**
	 * 同 Jdbc的queryForList
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	private static List<Map<String, Object>> queryForList(String tenantId, String sql)
			throws Exception {
		DataTransferDao	dataTransferDao = SpringConext.getApplicationContext().getBean(DataTransferDaoImpl.class);
		List<Map<String, Object>> queryForList = dataTransferDao.getJdbcTemplate(tenantId).queryForList(sql);
		return queryForList;
	}
	
	/**
	 * 根据表名称、伪id、渠道获取真正的Id
	 * @param map
	 * @param tableName
	 * @param channel
	 * @param channel
	 * @return
	 */
	public static String getIdBytableChannelFakeId(Map<String, Map<String, Object>> map,String tableName,String channel,String fakeid){
		Map<String, Object> map2 = map.get(tableName);
		Object value = map2.get(channel);
		if(value instanceof String) {
			return (String)value;
		} else {
			@SuppressWarnings("unchecked")
			Map<String, String> channelmap = ((Map<String, String>) value);
			String realid = channelmap.get(fakeid);
			if(realid == null) {
				return "0";
			}
			return realid;
		}
	}
	/**
	 * 根据表名称和伪id获取真正的Id
	 * @param map
	 * @param tableName
	 * @param fakeid
	 * @return
	 */
	public static String getIdBytableFakeId(Map<String, Map<String, Object>> map,String tableName,String fakeid){
		Map<String, Object> map2 = map.get(tableName);
		Object value = map2.get(fakeid);
		if(value == null) {
			return "0";
		}
		return (String)value;
	}
}
