package com.tzx.report.po.boh.impl;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.BusinessTotalmoneyAnalysisDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;



import java.util.ArrayList;
import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */

@Repository(BusinessTotalmoneyAnalysisDao.NAME)
public class BusinessTotalmoneyAnalysisDaoImpl implements BusinessTotalmoneyAnalysisDao{

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Resource(name = "parameterUtils")
    ParameterUtils parameterUtils;

    @Resource
    ConditionUtils conditionUtils;

    private final String YYTJ01 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2019_05' and sql_type='YYTJ01'";
    private final String YYTJ00_COUNT = "select sql from saas_report_engine where report_num = 'SAAS_BI_2019_05' and sql_type='YYTJ00'";
    @Override
    public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
        List<JSONObject> list = new ArrayList<JSONObject>();
        List<JSONObject> footerList =new ArrayList<JSONObject>();
        List<JSONObject> structure = new ArrayList<JSONObject>();
        JSONObject result = new JSONObject();
        long total = 0L;
        String begindate = condition.optString("begin_date");
        String enddate = condition.optString("end_date");
        if(begindate.length()>0 && enddate.length()>0 ) {

            String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition, YYTJ01);
            if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) { //导出
                reportSql = "SELECT * FROM (" + reportSql.toString() + ") as temp1 WHERE  id::TEXT IN ( SELECT REGEXP_SPLIT_TO_TABLE('"+condition.optString("exportdataexpr")+"', ',' ))";
                list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
                structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
            }else{
                list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, reportSql.toString()));
                total = this.dao.countSql(tenancyID, reportSql.toString());
                String reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,YYTJ00_COUNT);
                footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString());
            }
        }
        int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
        result.put("page", pagenum);
        result.put("total",total);
        result.put("rows", list);
        result.put("footer", footerList);
        result.put("structure", structure);
        return result;
    }
}
