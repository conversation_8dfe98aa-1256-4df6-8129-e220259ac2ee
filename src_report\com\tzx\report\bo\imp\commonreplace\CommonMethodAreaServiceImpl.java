package com.tzx.report.bo.imp.commonreplace;

import com.alibaba.fastjson.JSONException;
import com.tzx.framework.bo.SystemUserService;
import com.tzx.framework.bo.dto.BasicCombobox;
import com.tzx.framework.bo.dto.Roles;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.hq.bo.dto.HqItemClass;
import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.common.entity.Organ;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ExportUtils;
import com.tzx.report.common.util.ReportExportUtils;
import com.tzx.report.po.boh.dao.BillingQueryDao;
import com.tzx.report.po.commonreplace.impl.CommonMethodAreaDaoImpl;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
public class CommonMethodAreaServiceImpl implements CommonMethodAreaService{

	private final Logger logger = LoggerFactory.getLogger(getClass());
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	private CommonMethodAreaDaoImpl commonMethodAreaDao;
	
	@Resource
	private BillingQueryDao billingQueryDao;
	
	@Resource
	private SystemUserService systemUserService;

	@Override
	public List<JSONObject> getPaymentDetails(String tenancyID,JSONObject condition) throws Exception {
		return commonMethodAreaDao.getPaymentDetails(tenancyID, condition);
	}
	
	@Override
	public List<JSONObject> getPaymentDetailsStatus2(String tenancyID,JSONObject condition) throws Exception{
		return commonMethodAreaDao.getPaymentDetailsStatus2(tenancyID, condition);
	}
	
	@Override
	public List<JSONObject> getExplain(String tenancyID, JSONObject condition) throws Exception {
		return commonMethodAreaDao.getExplain(tenancyID, condition);
	}

	@Override
	public List<JSONObject> getEnergyConsumption(String attribute, JSONObject p) throws Exception {
		return commonMethodAreaDao.getEnergyConsumption(attribute, p);
	}
	
	@Override
	public List<JSONObject> getTaxRate(String tenancyID, JSONObject condition) throws Exception {
		return commonMethodAreaDao.getTaxRate(tenancyID, condition);
	}
	
	
	@Override
	public Object saveTaxRateModification(String tenancyID,String tableName,List<JSONObject> array)throws Exception {
		JSONObject result = new JSONObject();
		int[] upd = (int[]) commonMethodAreaDao.saveTaxRateModification(tenancyID,tableName,array);
		if(upd.length>0){
			// 增加
			result.element("success", true);
		}else{
			/*Object[] insertBatchIgnorCase = dao.insertBatchIgnorCase(tenancyID, tableName, array);
			if(insertBatchIgnorCase.length>0){
				result.element("success", true);	
			}else {*/
				result.element("success", false);
			/*}*/
		}
		return result;
	}
	

	@Override
	public HSSFWorkbook exportDate(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception {
		Integer rowNum=2;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",1);
		JSONObject findResult= billingQueryDao.getBillingQuery(tenancyID, json);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		  JSONObject out1Result =null;
		  HSSFSheet sheet1;
		  int ArrWidth[] = null;
		  String ArrHeader[][];
			//创建sheet 表格   同时还可以设置名字!  
			  sheet1=workBook.createSheet(json.optString("exportName"));
			  String serviceOrBao ="服务费";
			  String tenentIdVar = "";
			  if(tenancyID.length()>3){
				  tenentIdVar = tenancyID.substring(0, 4);
			  }
			  if(tenentIdVar.equals("judh")||tenentIdVar.equals("jdht")||tenancyID.equals("emeijiujia")||tenancyID.equals("dianmenemeijiujia")||tenancyID.equals("deneiemeijiujia")) {
				  serviceOrBao="包间费";
			  }
			  if(json.optInt("reportType")==1){
				  ArrHeader =new String[3][34];
				  ArrHeader[0]=new String[] {"交易门店","报表日期","时段","桌位编号","桌位名称","账单编号" ,"三方订单号","流水单号","消费客数","锅底数","小料数","开单时间","结账时间","结账机号","服务员","开台员","收款员","结账班次","预订单号","预打时间","预打次数","账单应收","消费流水","菜品消费",serviceOrBao,"退菜","折扣","折让","奉送","抹零","多收礼券","账单实收","佣金","优惠平台承担","优惠商家承担","商家实收","实收人均","应收人均","折扣方案","折扣率","渠道","销售模式","付款方式"};
				  ArrHeader[1]=new String[] {"store_name","report_date","interval","table_code","table_name","bill_num","third_order_code","serial_num","guest","hotpot_table_num","hotpot_guest_num","opentable_time","payment_time","pos_num","waiter_num","open_opt","cashier_num","shift_id","order_num","print_time","print_count","bill_amount","item_sale","item_amount","service_fee_income","back_money_item","discount_money","reduction_money","free_money","moling_money","coupons_ds","real_amount","commission_amount","platform_rate","shop_rate","shop_real_amount","real_amount_avg","average_amount","discount_case_name","zkl","chanel","sale_mode","payment_way"};
				  ArrHeader[2]=new String[] {"String","String","String","String","String","String","String","String","0.00","String","String","String","String","String","String","String","String","String","String","String","String","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","String","String","String","String","String"};
				  ArrWidth = new int[] {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43};
			  }else{
				  ArrHeader=new String[3][28];
				  ArrHeader[0]=new String[] {"交易门店","报表日期","账单编号","流水单号","消费客数","开单时间","结账时间","结账机号","服务员","开台员","收款员","结账班次","账单应收","消费流水","菜品消费",serviceOrBao,"退菜","折扣","折让","奉送","抹零","多收礼券","账单实收","佣金","优惠平台承担","优惠商家承担","商家实收","实收人均","应收人均","折扣方案","折扣率","渠道","销售模式","付款方式"};
				  ArrHeader[1]=new String[] {"store_name","report_date","bill_num","serial_num","guest","opentable_time","payment_time","pos_num","waiter_num","open_opt","cashier_num","shift_id","bill_amount","item_sale","item_amount","service_fee_income","back_money_item","discount_money","reduction_money","free_money","moling_money","coupons_ds","real_amount","commission_amount","platform_rate","shop_rate","shop_real_amount","real_amount_avg","average_amount","discount_case_name","zkl","chanel","sale_mode","payment_way"};
				  ArrHeader[2]=new String[] {"String","String","String","String","String","String","String","String","String","String","String","String","0","0","0","0","0","0","0","0","0","0","0","0.00","0.00","0.00","0.00","0.00","0.00","0","String","String","String","String","String"};
				  ArrWidth=new int[] {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34};
			  }

				if(list1.size()>0){
					for(JSONObject json1 : list1) {
						// 调用到处方法；
						out1Result =ExportUtils.out1(json1,workBook,sheet1,ArrHeader,paramData);
						paramData.put("rowNum", out1Result.opt("rowNum"));
						paramData.put("jin", out1Result.optInt("jin"));
					}
				}
				HSSFRow rowtitle =sheet1.createRow(0);
				HSSFRow rowtitle2 =sheet1.getRow(1);
				//合并上下的行 限制一列
				sheet1 =ExportUtils.upOrDownMergr(workBook, sheet1, ArrWidth, rowtitle2, rowtitle, 0, 1);
				sheet1.groupRow(1,out1Result.optInt("rowNum"));
				sheet1.setRowSumsBelow(false);
				sheet1.setRowSumsRight(false);
				
		return workBook;
	}
	@Override
	public List<JSONObject> getPaymentDetailsOrderByClass(String tenancyID,JSONObject condition) throws Exception {
		return commonMethodAreaDao.getPaymentDetailsOrderByClass(tenancyID, condition);
	}

	@Override
	public JSONObject getPosDinnerSnack(String tenancyID, JSONObject condition)throws Exception {
		return commonMethodAreaDao.getPosDinnerSnack(tenancyID, condition);
	}
	
	
	public JSONObject exportContrastline(int rowNum,int jin){
    	JSONObject paramData =new JSONObject();
    	paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",rowNum);
		return paramData;
    }
	
	
	
	//从json中, 读取的数据, 到二维数组中  
	public String[][] readTwoDimensionData(String key,JSONObject json)  
	{  
	    String[][] ArrHeader = null;  
	    try   
	    {  
	        if(json.has(key))  
	        {  
	            JSONArray Array1 = json.getJSONArray(key);          //获取属性名对应的二维数组  
	            ArrHeader = new String[Array1.size()][];  
	            for(int i = 0; i < Array1.size(); i ++)  
	            {  
	                JSONArray Array2 = Array1.getJSONArray(i);      //获取一维数组  
	                  
	                ArrHeader[i] = new String[Array2.size()];  
	                for(int j = 0; j < Array2.size(); j ++)  
	                	ArrHeader[i][j] = Array2.getString(j);           //获取一维数组中的数据  
	            }  
	        }  
	    }   
	    catch (JSONException e)   
	    {  
	        e.printStackTrace();  
	    }  
	    return ArrHeader;  
	}  
	
	public List<JSONObject> structureOrderData(List<JSONObject> list){
		List<JSONObject> obj = new ArrayList<JSONObject>();
		if(list.get(list.size()-1)!=null){
			JSONArray arr = list.get(list.size()-2).getJSONArray("structure");
		 	for(int i=0;i<arr.size();i++){
				JSONObject job = new JSONObject();
				JSONObject info=arr.getJSONObject(i);
				job.put("fieldname", info.getString("fieldname"));
				job.put("fieldtype", info.getString("fieldtype"));
				obj.add(job);
		 	}
		}
		return obj;
	}
	
	public List<JSONObject> structureData(List<JSONObject> list){
		List<JSONObject> obj = new ArrayList<JSONObject>();
		if(list.get(list.size()-1)!=null){
			JSONArray arr = list.get(list.size()-1).getJSONArray("structure");
		 	for(int i=0;i<arr.size();i++){
				JSONObject job = new JSONObject();
				JSONObject info=arr.getJSONObject(i);
				job.put("fieldname", info.getString("fieldname"));
				job.put("fieldtype", info.getString("fieldtype"));
				obj.add(job);
		 	}
		}
		return obj;
	}
	

    
    //反射执行java方法
	public JSONObject getInvokeMethod(String tenancyID,JSONObject json) throws ClassNotFoundException, InstantiationException, 
	 				IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
		  String className = json.optString("className"); //类路径
    	  String methodName= json.optString("methodName"); //方法名
    	  //获取上下文
    	  Object obj = SpringConext.getApplicationContext().getBean(className);
    	  //获取方法  
    	  Method m = obj.getClass().getDeclaredMethod(methodName, String.class,JSONObject.class);
    	  //调用方法  
		return (JSONObject) m.invoke(obj, tenancyID,json);
	 } 
    
	public List<JSONObject>  getMethodOrderOne(String tenancyID,JSONObject json) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
		   List<JSONObject> data1= new ArrayList<JSONObject>();
				JSONObject findResult= getInvokeMethod(tenancyID,json);
	     		@SuppressWarnings("unchecked")
				List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
				List<JSONObject> footer_list1 =(List<JSONObject>) findResult.opt("footer");
	     		@SuppressWarnings("unchecked")
				List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");
				if(list1.size()>0){
					String key = json.optString("key");
					if(!key.equals("")){
						for(JSONObject json1 : list1) {
							JSONObject jsona = new JSONObject();
							//前台传入需要对比的字段
							jsona.put("exportdataexpr",json1.optString(key));

							jsona.put("json1", json1);  //获取第一层的选中数据
							
							
							data1.add(jsona);
						}
						JSONObject structures = new JSONObject();
						structures.put("structure", structure);
						data1.add(structures);
						JSONObject footer = new JSONObject();
						footer.put("footer", footer_list1);
						data1.add(footer);
						
					}else{
						return data1;
					}
				}
			return data1;
	   }
    
   @SuppressWarnings("unchecked")
   public List<JSONObject>  getMethodOne(String tenancyID,JSONObject json) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
	   List<JSONObject> data1= new ArrayList<JSONObject>();
	   List<JSONObject> footerList = null;
			JSONObject findResult= getInvokeMethod(tenancyID,json);
			//明细数据
			List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
     		
     		//数据类型
			List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");
     		
     		//合计
     		if(json.containsKey("total_line") && json.optInt("total_line")==1){
     			footerList =(List<JSONObject>) findResult.opt("footer");
     		}
			if(list1.size()>0){
				String key = json.optString("key");
				if(!key.equals("")){
					for(JSONObject json1 : list1) {
						JSONObject jsona = new JSONObject();
						//前台传入需要对比的字段
						jsona.put("exportdataexpr",json1.optString(key));

						jsona.put("json1", json1);  //获取第一层的选中数据
						
						data1.add(jsona);
					}
					JSONObject structures = new JSONObject();
					structures.put("structure", structure);
					structures.put("footerList", footerList);
					data1.add(structures);
				}else{
					return data1;
				}
			}
		return data1;
   }
   
   
   public List<JSONObject>  getMethodTwo(String tenancyID,JSONObject json,int stratIndex2) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
	   List<JSONObject> data2= new ArrayList<JSONObject>();
	   JSONObject findResult= getInvokeMethod(tenancyID,json);
	   @SuppressWarnings("unchecked")
	   List<JSONObject> list2 =(List<JSONObject>) findResult.opt("rows");
	   @SuppressWarnings("unchecked")
	   List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");
 		if(list2.size()>0){
 			String key = json.optString("key");   //和第一级需要对比的字段  举例  item_num
 			String key1 = json.optString("key1"); //第三及需要对比的字段  举例 report_date
 			if(!key.equals("")){
 				for(JSONObject json2 : list2) {
     				JSONObject jsonb = new JSONObject();
     				//前台传入需要对比的字段  第二层
     				jsonb.put("exportdataexpr1",json2.optString(key));   //item_num 的数据存入exportdataexpr1
     				jsonb.put(key1,json2.optString(key1));    //report_date 的数据存入key1
					jsonb.put("stratIndex2", stratIndex2);
					jsonb.put("json2", json2);
					data2.add(jsonb);
   	     		}
 				JSONObject structures = new JSONObject();
				structures.put("structure", structure);
				data2.add(structures);
 			}else{
 				return data2;
 			}
     	}
		return data2;
   }
   
   public List<JSONObject>  getMethodThree(String tenancyID,JSONObject json,int stratIndex3) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
	   List<JSONObject> data3= new ArrayList<JSONObject>();
	   JSONObject findResult= getInvokeMethod(tenancyID,json);
	   @SuppressWarnings("unchecked")
	   List<JSONObject> list3 =(List<JSONObject>) findResult.opt("rows");
	   @SuppressWarnings("unchecked")
	   List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");
     		if(list3.size()>0){
     			String key1 = json.optString("key");  //需要和第一级对比   举例   item_num
     			String key2 = json.optString("key1");  //需要和第二层对比  举例    report_date
     			//String key1 = json.optString("key2");//如果存在第四层那么获取到key2进行第四层对比
     			if(!key2.equals("") && !key1.equals("")){
     				for(JSONObject json3 : list3) {
   	     			   JSONObject jsonc = new JSONObject();
	     				//前台传入需要对比的字段  第二层
   	     			    jsonc.put(key2, json3.optString(key2));  //report_date的数据 存入key2
						jsonc.put("exportdataexpr2", json3.optString(key1)); //item_num 的数据存入exportdataexpr2
						//jsonc.put(key1,json3.optString(key1));
						jsonc.put("stratIndex3", stratIndex3);
						jsonc.put("json3", json3);
						data3.add(jsonc);
       	     	}
 				JSONObject structures = new JSONObject();
				structures.put("structure", structure);
				data3.add(structures);
     		}else{
     			return data3;
     		}
     	}
		return data3;
   }
   
    
 public File execlrp(String exportName,JSONObject json,List<JSONObject> data1,List<JSONObject> data2,List<JSONObject> data3,HSSFSheet sheet1,
		 HSSFWorkbook workBook,Integer stratIndex,Integer stratIndex2,
		 Integer stratIndex3,JSONObject out1Result,JSONObject out1Result2,JSONObject out1Result3,List<JSONObject> list,JSONObject paramData) throws IOException{
	 	boolean judge = true;
	 	int rowNo = 0;      //总行号  
	 	int rowline = 1; //sheet页最大得记录条数
	 	List<JSONObject>  structure = null;
	 	List<JSONObject>  structure1= null;
	 	List<JSONObject>  structure2= null;
	 	List<File> files = new ArrayList();
		File f = null;
		boolean b = false;
		//合计
		JSONArray footerArr = null;
		JSONObject footer = null;
		//服务器下载文件地址
    	File gue = fileCatalogue();
    	
	 	if(data1!=null&&data1.size()>0){
	 		structure = structureData(data1);
	 	}
	 	if(data2!=null&&data2.size()>0){
	 		structure1 = structureData(data2);
	 	}
	 	if(data3!=null&&data3.size()>0){
	 		structure2 = structureData(data3);
	 	}
	 	
	 try{	
	 	if(data1!=null&&data2!=null&&data3!=null){
		 if(data1.size()>0 && data2.size()>0 && data3.size()>0){
		   if(structure.size()>0 && structure1.size()>0 && structure2.size()>0){
			 for(int i=0;i<data1.size();i++){
				 for(int j=0;j<data2.size();j++){
					if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
						JSONObject obj = data1.get(i).getJSONObject("json1");
						JSONObject objs = data2.get(j).getJSONObject("json2");
						if(obj.size()>0 && objs.size()>0){
							if(judge){
								out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
								paramData.put("rowNum", out1Result.opt("rowNum"));
								paramData.put("jin", out1Result.optInt("jin"));
								stratIndex=out1Result.optInt("rowNum");
								judge = false;
							}
							if(!objs.optString(json.optString("key")).equals("")){
								objs.remove(json.optString("key"));
							}
						out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
						stratIndex2=out1Result2.optInt("rowNum");
						paramData.put("rowNum", out1Result2.opt("rowNum"));
						paramData.put("jin", out1Result2.optInt("jin"));
						
						for(int k=0;k<data3.size();k++){
							if(data2.get(j).optString(json.optString("key1")).equals(data3.get(k).optString(json.optString("key1")))){   //第二层对比
								if(data2.get(j).optString("exportdataexpr1").equals(data3.get(k).optString("exportdataexpr2"))){  //第一层对比
								JSONObject obj3 = data3.get(k).getJSONObject("json3");
								if(obj3.size()>0){
									if(!obj3.optString(json.optString("key")).equals("")){
										obj3.remove(json.optString("key"));
									}
									if(!obj3.optString(json.optString("key1")).equals("")){
										obj3.remove(json.optString("key1"));
									}
									out1Result3 =ExportUtils.out2(exportName,obj3,workBook,sheet1,list,structure2,paramData);
									stratIndex3=out1Result3.optInt("rowNum");
									paramData.put("rowNum", out1Result3.opt("rowNum"));
									paramData.put("jin", out1Result3.optInt("jin"));
							   }
							}
						 }
					  } 
						if(json.optInt("groupRow")==2){
							sheet1.groupRow(stratIndex2,stratIndex3);
							sheet1.setRowGroupCollapsed(stratIndex2, true);
						}else{
							sheet1.groupRow(stratIndex2,stratIndex3);
						}
					   } 
					}
				 }
				 if(json.optInt("groupRow")==2){
					 	sheet1.groupRow(stratIndex,stratIndex3);
						sheet1.setRowGroupCollapsed(stratIndex, true);
					}else{
						sheet1.groupRow(stratIndex,stratIndex3);
					}
				 	judge = true;
			 	 }
		   	   }
		     }
			}else if(data1!=null && data2!=null && data1.size()>0 && data2.size()>0){
				if(structure.size()>0 && structure1.size()>0){
				for(int i=0;i<data1.size();i++){
	  				for(int j=0;j<data2.size();j++){
	  					if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
	  							JSONObject obj = data1.get(i).getJSONObject("json1");
	  							JSONObject objs = data2.get(j).getJSONObject("json2");
	  							if(obj.size()>0 & objs.size()>0){
		  							if(judge){
		  								out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
		  								paramData.put("rowNum", out1Result.opt("rowNum"));
		  								paramData.put("jin", out1Result.optInt("jin"));
		  								stratIndex=out1Result.optInt("rowNum");
		  							judge = false;
		  							}
		 							
		  							if(!objs.optString(json.optString("key")).equals("")){
										objs.remove(json.optString("key"));
									}
		  							
		  							out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
		  							stratIndex2=out1Result2.optInt("rowNum");
		  							paramData.put("rowNum", out1Result2.opt("rowNum"));
		 							paramData.put("jin", out1Result2.optInt("jin"));
		  						}
	  						}
	  					}
	  				if(json.optInt("groupRow")==2){
					 	sheet1.groupRow(stratIndex,stratIndex2);
						sheet1.setRowGroupCollapsed(stratIndex, true);
					}else{
						sheet1.groupRow(stratIndex,stratIndex2);
					}
	  				judge = true;
	  				}
				}
			}else{
				if(data1!=null && data1.size()>0){
					if(data1.get(data1.size()-1).containsKey("footerList")){
						footerArr = data1.get(data1.size()-1).getJSONArray("footerList");
						footer = footerArr.getJSONObject(0);
					}
					if(structure.size()>0){
						Integer rowNum = null;
						Integer jin = null;
						JSONObject returnTitleJson = null;
						JSONObject obj = null;
						for(int i=0;i<data1.size();i++) {
							obj = data1.get(i).getJSONObject("json1");
								if(obj.size()>0){
									if(rowNo%60000==0){
						               // System.out.println("Current Sheet:" + rowNo/100);
						                sheet1 = workBook.createSheet(exportName);//建立新的sheet对象  
						                returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
						                rowNum= returnTitleJson.optInt("rowNum");
						        		jin=json.optInt("jin");
						        		paramData=exportContrastline(rowNum,jin);
						                //sheet1 = workBook.getSheetAt(rowNo/100);        //动态指定当前的工作表  
						            }
									rowNo++;
									// 调用导出方法；
									out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
									paramData.put("rowNum", out1Result.opt("rowNum"));
									paramData.put("jin", out1Result.optInt("jin"));
										
									if(rowNo%60000==0){
										b=true;
						                //System.out.println("row no: " + rowNo);
						                int a1 = out1Result==null?0:out1Result.optInt("rowNum");
						    		 	int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
						    		 	int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
						    		 	// 导出印记
					    			    JSONObject cc = new JSONObject();
					    			    cc.put("man", json.opt("man"));
					    			    cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
					    			    ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);
					    			    if(rowNo >= 60000){
					    			    	f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
						    			    OutputStream fout =  new FileOutputStream(f);
						    			    workBook.write(fout);
						    			    //不分sheet页就必须重新new该对象
						    			    workBook  = new HSSFWorkbook();
						    			    fout.close();
						    			    files.add(f);
					    			    }
					    			    
						            }
								}
							}
						}
					}
				}
	 } catch (Exception e) {
			// TODO Auto-generated catch block
			System.out.println(e.getMessage());
	}
	 finally {
		 	int a1 = out1Result==null?0:out1Result.optInt("rowNum");
		 	int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
		 	int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
		 	// 导出印记
			JSONObject cc = new JSONObject();
		    cc.put("man", json.opt("man"));
			cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
			ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);
			  
			if(b){
				f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
  			    OutputStream fout =  new FileOutputStream(f);
  			    workBook.write(fout);
  			    //不分sheet页就必须重新new该对象
  			    fout.close();
  			    files.add(f);
			  }
			  //如果大于6W数据进行压缩
			  if(files.size()>0){
					File zip = new File(gue+"/"+exportName+System.currentTimeMillis()+".zip");
					zipFiles(files.toArray(new File[files.size()]),zip);
					return zip;
				}

	 }
	return null;
 }
 //订单导出
 public File execlOrderRp(String exportName,JSONObject json,List<JSONObject> data1,List<JSONObject> data2,List<JSONObject> data3,HSSFSheet sheet1,
		 HSSFWorkbook workBook,Integer stratIndex,Integer stratIndex2,
		 Integer stratIndex3,JSONObject out1Result,JSONObject out1Result2,JSONObject out1Result3,List<JSONObject> list,JSONObject paramData) throws IOException{
	 	boolean judge = true;
	 	int rowNo = 0;      //总行号  
	 	int rowline = 1; //sheet页最大得记录条数
	 	List<JSONObject>  structure = null;
	 	List<JSONObject>  structure1= null;
	 	List<JSONObject>  structure2= null;
		JSONObject footer_jsonobject=null;
	 	List<File> files = new ArrayList();
		File f = null;
		boolean b = false;
		//合计
		JSONObject footer=null;
		//服务器下载文件地址
    	File gue = fileCatalogue();
    	logger.info("File路径1111======"+gue.getPath());

	 	if(data1!=null&&data1.size()>0){
	 		structure = structureOrderData(data1);
	 	}
	 	if(data2!=null&&data2.size()>0){
	 		structure1 = structureData(data2);
	 	}
	 	if(data3!=null&&data3.size()>0){
	 		structure2 = structureData(data3);
	 	}
	 	
	 try{	
	 	if(data1!=null&&data2!=null&&data3!=null){
			logger.info("===============mmmmmmmmmmmmmm");
		 if(data1.size()>0 && data2.size()>0 && data3.size()>0){
		   if(structure.size()>0 && structure1.size()>0 && structure2.size()>0){
			 for(int i=0;i<data1.size();i++){
				 for(int j=0;j<data2.size();j++){
					if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
						JSONObject obj = data1.get(i).getJSONObject("json1");
						JSONObject objs = data2.get(j).getJSONObject("json2");
						if(obj.size()>0 && objs.size()>0){
							if(judge){
								out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
								paramData.put("rowNum", out1Result.opt("rowNum"));
								paramData.put("jin", out1Result.optInt("jin"));
								stratIndex=out1Result.optInt("rowNum");
								judge = false;
							}
							if(!objs.optString(json.optString("key")).equals("")){
								objs.remove(json.optString("key"));
							}
						out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
						stratIndex2=out1Result2.optInt("rowNum");
						paramData.put("rowNum", out1Result2.opt("rowNum"));
						paramData.put("jin", out1Result2.optInt("jin"));
						
						for(int k=0;k<data3.size();k++){
							if(data2.get(j).optString(json.optString("key1")).equals(data3.get(k).optString(json.optString("key1")))){   //第二层对比
								if(data2.get(j).optString("exportdataexpr1").equals(data3.get(k).optString("exportdataexpr2"))){  //第一层对比
								JSONObject obj3 = data3.get(k).getJSONObject("json3");
								if(obj3.size()>0){
									if(!obj3.optString(json.optString("key")).equals("")){
										obj3.remove(json.optString("key"));
									}
									if(!obj3.optString(json.optString("key1")).equals("")){
										obj3.remove(json.optString("key1"));
									}
									out1Result3 =ExportUtils.out2(exportName,obj3,workBook,sheet1,list,structure2,paramData);
									stratIndex3=out1Result3.optInt("rowNum");
									paramData.put("rowNum", out1Result3.opt("rowNum"));
									paramData.put("jin", out1Result3.optInt("jin"));
							   }
							}
						 }
					  } 
						if(json.optInt("groupRow")==2){
							sheet1.groupRow(stratIndex2,stratIndex3);
							sheet1.setRowGroupCollapsed(stratIndex2, true);
						}else{
							sheet1.groupRow(stratIndex2,stratIndex3);
						}
					   } 
					}
				 }
				 if(json.optInt("groupRow")==2){
					 	sheet1.groupRow(stratIndex,stratIndex3);
						sheet1.setRowGroupCollapsed(stratIndex, true);
					}else{
						sheet1.groupRow(stratIndex,stratIndex3);
					}
				 	judge = true;
			 	 }
		   	   }
		     }
			}else if(data1!=null && data2!=null && data1.size()>0 && data2.size()>0){
				logger.info(">>>>>>>>>>>>bbbbbbbbbbbbbbb");
				if(structure.size()>0 && structure1.size()>0){
				for(int i=0;i<data1.size();i++){
	  				for(int j=0;j<data2.size();j++){
	  					if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
	  							JSONObject obj = data1.get(i).getJSONObject("json1");
	  							JSONObject objs = data2.get(j).getJSONObject("json2");
	  							if(obj.size()>0 & objs.size()>0){
		  							if(judge){
		  								out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
		  								paramData.put("rowNum", out1Result.opt("rowNum"));
		  								paramData.put("jin", out1Result.optInt("jin"));
		  								stratIndex=out1Result.optInt("rowNum");
		  							judge = false;
		  							}
		 							
		  							if(!objs.optString(json.optString("key")).equals("")){
										objs.remove(json.optString("key"));
									}
		  							
		  							out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
		  							stratIndex2=out1Result2.optInt("rowNum");
		  							paramData.put("rowNum", out1Result2.opt("rowNum"));
		 							paramData.put("jin", out1Result2.optInt("jin"));
		  						}
	  						}
	  					}
	  				if(json.optInt("groupRow")==2){
					 	sheet1.groupRow(stratIndex,stratIndex2);
						sheet1.setRowGroupCollapsed(stratIndex, true);
					}else{
						sheet1.groupRow(stratIndex,stratIndex2);
					}
	  				judge = true;
	  				}
				}
			}else{
				logger.info("aaaaaaaaaaaa>>>>>>>>>");
				if(data1!=null && data1.size()>0){
					logger.info("data1里有数据>>>>>>>>>>"+data1.toString());
					JSONArray footerList = data1.get(data1.size()-1).getJSONArray("footer");
					footer_jsonobject=footerList.getJSONObject(0);
					logger.info("footer_jsonobject里有数据>>>>>>>>>>");
					if(structure.size()>0){
						logger.info("footer_jsonobject里有数据>>>>>>>>>>"+structure.size());
						Integer rowNum = null;
						Integer jin = null;
						JSONObject returnTitleJson = null;
						JSONObject obj = null;
						for(int i=0;i<data1.size();i++) {
							obj = data1.get(i).getJSONObject("json1");
								if(obj.size()>0){
									if(rowNo%60000==0){
						               // System.out.println("Current Sheet:" + rowNo/100);
										logger.info("111111111111-----exportName>>>>>>>>>>>>>>>"+exportName);
						                sheet1 = workBook.createSheet(exportName);//建立新的sheet对象
										logger.info("建立新的sheet对象>>>>"+sheet1.toString());
						                returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
						                logger.info("returnTitleJson==========>>>>>>>>>>>"+returnTitleJson.toString());
						                rowNum= returnTitleJson.optInt("rowNum");
						        		jin=json.optInt("jin");
						        		paramData=exportContrastline(rowNum,jin);
						                //sheet1 = workBook.getSheetAt(rowNo/100);        //动态指定当前的工作表  
						            }
									rowNo++;
									// 调用导出方法；
									out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
									logger.info("调用导出方法>>>>>>>>>>"+out1Result.toString());
									paramData.put("rowNum", out1Result.opt("rowNum"));
									paramData.put("jin", out1Result.optInt("jin"));
										
									if(rowNo%60000==0){
										b=true;
						                //System.out.println("row no: " + rowNo);
						                int a1 = out1Result==null?0:out1Result.optInt("rowNum");
						    		 	int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
						    		 	int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
						    		 	// 导出印记
					    			    JSONObject cc = new JSONObject();
					    			    cc.put("man", json.opt("man"));
					    			    cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
										logger.info("=====444444444444======="+structure.toString());
										ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);
										logger.info("=====5555555555555=======");
										if(rowNo >= 60000){
					    			    	f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
					    			    	logger.info("**************>>>>>>>>>>"+f.getPath());
						    			    OutputStream fout =  new FileOutputStream(f);
						    			    workBook.write(fout);
						    			    //不分sheet页就必须重新new该对象
						    			    workBook  = new HSSFWorkbook();
						    			    fout.close();
						    			    files.add(f);
					    			    }
					    			    
						            }
								}
							}
						}
					}
					logger.info("data1没有数据..................");
				}
	 } catch (Exception e) {
			// TODO Auto-generated catch block
			System.out.println(e.getMessage());
	}
	 finally {
		 	int a1 = out1Result==null?0:out1Result.optInt("rowNum");
		 	int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
		 	int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
		 	// 导出印记
			JSONObject cc = new JSONObject();
		    cc.put("man", json.opt("man"));
			cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
			logger.info("开始设置》》》》》》》》》》》》》》");
			ReportExportUtils.setManAndTimeOrder( workBook, sheet1, cc,json,footer_jsonobject);
		 	logger.info("8888888888888888888888888");
		 if(b){
				f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
  			    OutputStream fout =  new FileOutputStream(f);
  			    workBook.write(fout);
  			    //不分sheet页就必须重新new该对象
  			    fout.close();
  			    files.add(f);
			  }
			  //如果大于6W数据进行压缩
			  if(files.size()>0){
					File zip = new File(gue+"/"+exportName+System.currentTimeMillis()+".zip");
					zipFiles(files.toArray(new File[files.size()]),zip);
					return zip;
				}

	 }
	return null;
 }
 
 
 /**
  * 导出生成文件目录
  *
  */
 
 public File fileCatalogue(){
	 File[] roots = File.listRoots();
	 // 该方法查询机器的根级 目录 并且在DownExcleFiles文件夹中建立文件夹
	 String uuid=UUID.randomUUID().toString();
	 String pathFolder = roots[0]+"DownExcleFilesZip\\"+uuid+"\\";// uuid 的文件名字
	 File filePath = new File(pathFolder);
	  if(!filePath.exists()) {
		  logger.info("文件不存在，开始创建文件");
		  // 设置权限
		  filePath.setWritable(true, false);
		 // 如果不存在
		  filePath.mkdirs(); //开始创建
		  
		  logger.info("导出文件地址为:"+filePath);
	  }
	  return filePath;
 }

    
 /**  
  *   
  * @param srcfile 文件名数组  
  * @param zipfile 压缩后文件  
  */  
 public void zipFiles(java.io.File[] srcfile, java.io.File zipfile) {  
     byte[] buf = new byte[1024];  
     try {  
         ZipOutputStream out = new ZipOutputStream(new FileOutputStream(  
                 zipfile));  
         for (int i = 0; i < srcfile.length; i++) {  
             FileInputStream in = new FileInputStream(srcfile[i]);  
             out.putNextEntry(new ZipEntry(srcfile[i].getName()));  
             int len;  
             while ((len = in.read(buf)) > 0) {  
                 out.write(buf, 0, len);  
             }  
             out.closeEntry();  
             in.close();  
         }  
         out.close();  
     } catch (IOException e) {  
         e.printStackTrace();  
     }  
 }  
 
	 /**
	  * 自动导出
	  */
	@SuppressWarnings("null")
	public Object[] exportDateNew(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception {
		HSSFSheet sheet1 = null;
		File f = null;
		String exportName = json.optString("exportName");//名称
		JSONObject paramData = null;
		//如果为第一层不进行创建sheet页
		if(json.optInt("hierarchy")>1){
			sheet1=workBook.createSheet(exportName);
			JSONObject returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
			Integer rowNum= returnTitleJson.optInt("rowNum");
			Integer jin=json.optInt("jin");
			paramData=exportContrastline(rowNum,jin);
		}
		StringBuilder sbdate = new StringBuilder();
		StringBuilder sbdatekey = new StringBuilder();
	    Integer stratIndex =0;
	    Integer stratIndex2 =0;
	    Integer stratIndex3 =0;
	    JSONObject out1Result2 =null;
	    JSONObject out1Result =null;
	    JSONObject out1Result3 =null;
	    List<JSONObject> objs = new ArrayList<JSONObject>();
	    List<JSONObject> data = null;
	    List<JSONObject> data2 = null;
	    List<JSONObject> data3 = null;
	    String parametertype = "";
	    String parametertype1 = "";
	    String parametertype2 = "";
	
		  if(json.containsKey("rowcolumns") && json.getJSONObject("rowcolumns")!=null){
			  JSONObject obj = json.getJSONObject("rowcolumns");
			  JSONArray list=obj.getJSONArray("key");
			  for(int i=0;i<list.size();i++){
				  JSONObject job = new JSONObject();
				  JSONObject info=list.getJSONObject(i);
				  job.put("field", info.getString("field"));
				  objs.add(job);
			  }

			  if(json.optInt("hierarchy")==3){
				  data =  getMethodOne(tenancyID,json);  //第一层返回集合
				    if(data.size()>0){
				    	if(json.containsKey("hierarchytype") && json.optInt("hierarchytype")==1){   //是否存在第二层
				    		json.put("hierarchytype", 2);
				    		if(json.containsKey("p_report_type")){
				    			json.put("p_report_type", "L"+json.optInt("selecttype")+"J"+json.optInt("hierarchytype"));
				    		}
				    	}
				    	if(json.containsKey("exportdataexpr") && !json.optString("exportdataexpr").equals("")){    //第一层查询字段
				    		json.put(json.optString("key"), json.optString("exportdataexpr"));
				    	}
						if(json.containsKey("sortKey1") && !json.optString("sortKey1").equals("")){      //第二层排序
							//前台传入是否需要后台进行数据排序
							json.put("sortKey1",json.optString("sortKey1"));
						}
				    }
				    data2 =  getMethodTwo(tenancyID,json,stratIndex2);  //第二层返回集合
				    if(data2.size()>0){
				    	sbdate.delete( 0,sbdate.length());
					    sbdatekey.delete( 0,sbdatekey.length());
				    	if(json.containsKey("hierarchytype") && json.optInt("hierarchytype") ==2){
				    		json.put("hierarchytype", 3);
				    		
				    		/**
				    		 * 用函数导出时需要传入参数p_report_level_two代表函数sql使用层级
				    		 * selecttype为查询方式
				    		 * hierarchytype为当前查询得层级
				    		 */
				    		if(json.containsKey("p_report_type")){
				    			json.put("p_report_type", "L"+json.optInt("selecttype")+"J"+json.optInt("hierarchytype"));
				    		}
				    	}
				    	for(int i=0;i<data2.size();i++){
				    		String key1 = json.optString("key1");
				    		if(data2.get(i).containsKey(key1)){
				    			if(!data2.get(i).optString(key1).equals("")&&!data2.get(i).optString(key1).equals(null)){
				    				sbdatekey.append(","+data2.get(i).optString(key1));
					    		}
				    		}
				    		if(!data2.get(i).optString("exportdataexpr1").equals("")){
				    			sbdate.append(","+data2.get(i).optString("exportdataexpr1"));
				    		}
				    	}
				    	
				    	if(sbdatekey.length()>0)
						{
							if(json.optString("parametertype1").equals("String")){   //第二层查询是否为字符串
								sbdatekey.delete(0,1);
				    			parametertype1 = ConditionUtils.spilt(sbdatekey.toString());
								json.put(json.optString("key1"), parametertype1);
				    		}else{
				    			sbdatekey.delete(0,1);
								json.put(json.optString("key1"), sbdatekey.toString());
				    		}
						}else{
							json.put(json.optString("key1"), sbdatekey.toString());
						}
				    	
				    	if(sbdate.length()>0)
						{
				    		if(json.optString("parametertype").equals("String")){   //第一层查询是否为字符串
				    			sbdate.delete(0,1);
				    			parametertype = ConditionUtils.spilt(sbdate.toString());
								json.put("exportdataexpr1", parametertype);
				    		}else{
				    			sbdate.delete(0,1);
								json.put("exportdataexpr1", sbdate.toString());
				    		}
						}else{
							json.put("exportdataexpr1", sbdate.toString());
						}
						if(json.containsKey("sortKey2") && !json.optString("sortKey2").equals("")){
							//前台传入是否需要后台进行数据排序
							json.put("sortKey2",json.optString("sortKey2"));
						}
				    }
				    data3 =  getMethodThree(tenancyID,json,stratIndex3);  //第三层返回集合
				    if(data3.size()>0){
				    	sbdate.delete( 0,sbdate.length());   //清空sbdate     (存放第一层数据)
				    	sbdatekey.delete( 0,sbdatekey.length()); //清空sbdatekey(存放第二层数据)
				    	for(int i=0;i<data3.size();i++){
				    		String key2 = json.optString("key1");  //获取第二层导出字段  举例 report_date
				    		if(data3.get(i).containsKey(key2)){
				    			if(!data3.get(i).optString(key2).equals("")&&!data3.get(i).optString(key2).equals(null)){
				    				sbdatekey.append(","+data3.get(i).optString(key2));
					    		}
				    		}
				    		if(!data3.get(i).optString("exportdataexpr2").equals("")){
				    			sbdate.append(","+data3.get(i).optString("exportdataexpr2"));
				    		}
				    	}
				    	/*if(sbdate.length()>0)
						{
				    		if(json.optString("parametertype2").equals("String")){   //第三层查询是否为字符串
				    			sbdate.delete(0,1);
				    			parametertype2 = ConditionUtils.spilt(sbdate.toString());
								json.put("exportdataexpr2", parametertype2);
				    		}else{
				    			sbdate.delete(0,1);
								json.put("exportdataexpr2", sbdate.toString());
				    		}
						}else{
							json.put("exportdataexpr2", sbdate.toString());
						}*/
				    	if(sbdatekey.length()>0)
						{
							if(json.optString("parametertype1").equals("String")){   //第二层查询是否为字符串
								sbdatekey.delete(0,1);
				    			parametertype1 = ConditionUtils.spilt(sbdatekey.toString());
								json.put(json.optString("key2"), parametertype1);
				    		}else{
				    			sbdatekey.delete(0,1);
								json.put(json.optString("key2"), sbdatekey.toString());
				    		}
						}else{
							json.put(json.optString("key2"), sbdatekey.toString());
						}
				    	
				    	if(sbdate.length()>0)
						{
				    		if(json.optString("parametertype").equals("String")){   //第一层查询是否为字符串
				    			sbdate.delete(0,1);
				    			parametertype = ConditionUtils.spilt(sbdate.toString());
								json.put("exportdataexpr2", parametertype);
				    		}else{
				    			sbdate.delete(0,1);
								json.put("exportdataexpr2", sbdate.toString());
				    		}
						}else{
							json.put("exportdataexpr2", sbdate.toString());
						}
				    } 
			  }else if(json.optInt("hierarchy")==2){
				  data =  getMethodOne(tenancyID,json);  //第一层返回集合
				    if(data.size()>0){
				    	if(json.containsKey("hierarchytype") && json.optInt("hierarchytype")==1){
				    		json.put("hierarchytype", 2);
				    		if(json.containsKey("p_report_type")){
				    			json.put("p_report_type", "L"+json.optInt("selecttype")+"J"+json.optInt("hierarchytype"));
				    		}
				    	}
				    	if(json.containsKey("exportdataexpr") && !json.optString("exportdataexpr").equals("")){
				    		json.put(json.optString("key"), json.optString("exportdataexpr"));
				    	}
						if(json.containsKey("sortKey1") && !json.optString("sortKey1").equals("")){
							//前台传入是否需要后台进行数据排序
							json.put("sort1",json.optString("sortKey1"));
						}
				    }
				    data2 =  getMethodTwo(tenancyID,json,stratIndex2);  //第二层返回集合
			  }else{
				  data =  getMethodOne(tenancyID,json);  //第一层返回集合
			  }
			  if(json.containsKey("TenThousandExcle")) {
				  paramData.put("TenThousandExcle", json.optString("TenThousandExcle"));
				  paramData.put("maxExcleNum", json.optString("maxExcleNum") ==  null ? 1000 : json.optInt("maxExcleNum"));
			  }
			  f = execlrp(exportName,json,data, data2, data3, sheet1, workBook, stratIndex, stratIndex2, stratIndex3, out1Result, out1Result2, out1Result3, objs, paramData);
		  }
		  if(json.optInt("hierarchy")>1){
			  sheet1.setRowSumsBelow(false);
			  sheet1.setRowSumsRight(false);
		  }
		 Object[] o = new Object[2];
		 o[0] = workBook;
		 o[1] = f;
		return o;
	}
	
	 /**
	  * 订单自动导出
	  */
	@SuppressWarnings("null")
	public Object[] exportDateNewOrder(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception {
		HSSFSheet sheet1 = null;
		File f = null;
		String exportName = json.optString("exportName");//名称
		JSONObject paramData = null;
		//如果为第一层不进行创建sheet页
		if(json.optInt("hierarchy")>1){
			sheet1=workBook.createSheet(exportName);
			JSONObject returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
			Integer rowNum= returnTitleJson.optInt("rowNum");
			Integer jin=json.optInt("jin");
			paramData=exportContrastline(rowNum,jin);
		}
		StringBuilder sbdate = new StringBuilder();
		StringBuilder sbdatekey = new StringBuilder();
	    Integer stratIndex =0;
	    Integer stratIndex2 =0;
	    Integer stratIndex3 =0;
	    JSONObject out1Result2 =null;
	    JSONObject out1Result =null;
	    JSONObject out1Result3 =null;
	    List<JSONObject> objs = new ArrayList<JSONObject>();
	    List<JSONObject> data = null;
	    List<JSONObject> data2 = null;
	    List<JSONObject> data3 = null;
	    String parametertype = "";
	    String parametertype1 = "";
	    String parametertype2 = "";
	
		  if(json.containsKey("rowcolumns") && json.getJSONObject("rowcolumns")!=null){
			  JSONObject obj = json.getJSONObject("rowcolumns");
			  JSONArray list=obj.getJSONArray("key");
			  for(int i=0;i<list.size();i++){
				  JSONObject job = new JSONObject();
				  JSONObject info=list.getJSONObject(i);
				  job.put("field", info.getString("field"));
				  objs.add(job);
			  }

			  if(json.optInt("hierarchy")==3){
				  data =  getMethodOne(tenancyID,json);  //第一层返回集合
				    if(data.size()>0){
				    	if(json.containsKey("hierarchytype") && json.optInt("hierarchytype")==1){   //是否存在第二层
				    		json.put("hierarchytype", 2);
				    	}
				    	if(json.containsKey("exportdataexpr") && !json.optString("exportdataexpr").equals("")){    //第一层查询字段
				    		json.put(json.optString("key"), json.optString("exportdataexpr"));
				    	}
						if(json.containsKey("sortKey1") && !json.optString("sortKey1").equals("")){      //第二层排序
							//前台传入是否需要后台进行数据排序
							json.put("sortKey1",json.optString("sortKey1"));
						}
				    }
				    data2 =  getMethodTwo(tenancyID,json,stratIndex2);  //第二层返回集合
				    if(data2.size()>0){
				    	sbdate.delete( 0,sbdate.length());
					    sbdatekey.delete( 0,sbdatekey.length());
				    	if(json.containsKey("hierarchytype") && json.optInt("hierarchytype") ==2){
				    		json.put("hierarchytype", 3);
				    	}
				    	for(int i=0;i<data2.size();i++){
				    		String key1 = json.optString("key1");
				    		if(data2.get(i).containsKey(key1)){
				    			if(!data2.get(i).optString(key1).equals("")&&!data2.get(i).optString(key1).equals(null)){
				    				sbdatekey.append(","+data2.get(i).optString(key1));
					    		}
				    		}
				    		if(!data2.get(i).optString("exportdataexpr1").equals("")){
				    			sbdate.append(","+data2.get(i).optString("exportdataexpr1"));
				    		}
				    	}
				    	
				    	if(sbdatekey.length()>0)
						{
							if(json.optString("parametertype1").equals("String")){   //第二层查询是否为字符串
								sbdatekey.delete(0,1);
				    			parametertype1 = ConditionUtils.spilt(sbdatekey.toString());
								json.put(json.optString("key1"), parametertype1);
				    		}else{
				    			sbdatekey.delete(0,1);
								json.put(json.optString("key1"), sbdatekey.toString());
				    		}
						}else{
							json.put(json.optString("key1"), sbdatekey.toString());
						}
				    	
				    	if(sbdate.length()>0)
						{
				    		if(json.optString("parametertype").equals("String")){   //第一层查询是否为字符串
				    			sbdate.delete(0,1);
				    			parametertype = ConditionUtils.spilt(sbdate.toString());
								json.put("exportdataexpr1", parametertype);
				    		}else{
				    			sbdate.delete(0,1);
								json.put("exportdataexpr1", sbdate.toString());
				    		}
						}else{
							json.put("exportdataexpr1", sbdate.toString());
						}
						if(json.containsKey("sortKey2") && !json.optString("sortKey2").equals("")){
							//前台传入是否需要后台进行数据排序
							json.put("sortKey2",json.optString("sortKey2"));
						}
				    }
				    data3 =  getMethodThree(tenancyID,json,stratIndex3);  //第三层返回集合
				    if(data3.size()>0){
				    	sbdate.delete( 0,sbdate.length());   //清空sbdate     (存放第一层数据)
				    	sbdatekey.delete( 0,sbdatekey.length()); //清空sbdatekey(存放第二层数据)
				    	for(int i=0;i<data3.size();i++){
				    		String key2 = json.optString("key1");  //获取第二层导出字段  举例 report_date
				    		if(data3.get(i).containsKey(key2)){
				    			if(!data3.get(i).optString(key2).equals("")&&!data3.get(i).optString(key2).equals(null)){
				    				sbdatekey.append(","+data3.get(i).optString(key2));
					    		}
				    		}
				    		if(!data3.get(i).optString("exportdataexpr2").equals("")){
				    			sbdate.append(","+data3.get(i).optString("exportdataexpr2"));
				    		}
				    	}
				    	/*if(sbdate.length()>0)
						{
				    		if(json.optString("parametertype2").equals("String")){   //第三层查询是否为字符串
				    			sbdate.delete(0,1);
				    			parametertype2 = ConditionUtils.spilt(sbdate.toString());
								json.put("exportdataexpr2", parametertype2);
				    		}else{
				    			sbdate.delete(0,1);
								json.put("exportdataexpr2", sbdate.toString());
				    		}
						}else{
							json.put("exportdataexpr2", sbdate.toString());
						}*/
				    	if(sbdatekey.length()>0)
						{
							if(json.optString("parametertype1").equals("String")){   //第二层查询是否为字符串
								sbdatekey.delete(0,1);
				    			parametertype1 = ConditionUtils.spilt(sbdatekey.toString());
								json.put(json.optString("key2"), parametertype1);
				    		}else{
				    			sbdatekey.delete(0,1);
								json.put(json.optString("key2"), sbdatekey.toString());
				    		}
						}else{
							json.put(json.optString("key2"), sbdatekey.toString());
						}
				    	
				    	if(sbdate.length()>0)
						{
				    		if(json.optString("parametertype").equals("String")){   //第一层查询是否为字符串
				    			sbdate.delete(0,1);
				    			parametertype = ConditionUtils.spilt(sbdate.toString());
								json.put("exportdataexpr2", parametertype);
				    		}else{
				    			sbdate.delete(0,1);
								json.put("exportdataexpr2", sbdate.toString());
				    		}
						}else{
							json.put("exportdataexpr2", sbdate.toString());
						}
				    } 
			  }else if(json.optInt("hierarchy")==2){
				  data =  getMethodOne(tenancyID,json);  //第一层返回集合
				    if(data.size()>0){
				    	if(json.containsKey("hierarchytype") && json.optInt("hierarchytype")==1){
				    		json.put("hierarchytype", 2);
				    	}
				    	if(json.containsKey("exportdataexpr") && !json.optString("exportdataexpr").equals("")){
				    		json.put(json.optString("key"), json.optString("exportdataexpr"));
				    	}
						if(json.containsKey("sortKey1") && !json.optString("sortKey1").equals("")){
							//前台传入是否需要后台进行数据排序
							json.put("sort1",json.optString("sortKey1"));
						}
				    }
				    data2 =  getMethodTwo(tenancyID,json,stratIndex2);  //第二层返回集合
			  }else{
			  		logger.info("第一层返回集合.....11111111");
				  data =  getMethodOrderOne(tenancyID,json);  //第一层返回集合
				  logger.info("第一层返回集合.....22222222222");
			  }
			  if(json.containsKey("TenThousandExcle")) {
				  paramData.put("TenThousandExcle", json.optString("TenThousandExcle"));
				  paramData.put("maxExcleNum", json.optString("maxExcleNum") ==  null ? 1000 : json.optInt("maxExcleNum"));
			  }
			  logger.info("1111111111111>>>>>exportName="+exportName);
			  f = execlOrderRp(exportName,json,data, data2, data3, sheet1, workBook, stratIndex, stratIndex2, stratIndex3, out1Result, out1Result2, out1Result3, objs, paramData);
		  }
		  if(json.optInt("hierarchy")>1){
			  sheet1.setRowSumsBelow(false);
			  sheet1.setRowSumsRight(false);
		  }
		 Object[] o = new Object[2];
		 o[0] = workBook;
		 o[1] = f;
		return o;
	}

	@Override
	public Integer updateFileListState(String tenancy_id,JSONObject json) {
		// TODO Auto-generated method stub
		int c = 0 ;
		try {
			c=dao.updateIgnorCase(tenancy_id, "hq_export_file", json);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return c;
	}

	@Override
	public  JSONObject getFilesNames(String tenancyID, JSONObject json) {
		JSONObject data = new JSONObject();
		List<JSONObject> fileStateList =new ArrayList<>();
			 // 生成名字
				
				// 总批次
				Integer forNum = (int) Math.ceil((double)json.optInt("totolNum")/(double)json.optInt("pageNum"));
				
				// 文件基础名字
				String fileName = json.optString("exportName");
				
				JSONObject fileState = null;
				SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-ddHHmmss");
				String dataString = sdf.format(new Date());
				for(Integer i = 1 ; i <=forNum ; i++) {
					fileState = new JSONObject();
					fileState.put("filename", fileName+""+i+"-"+forNum);
					fileState.put("filestate", "0");// 初始化
					fileState.put("store_id", json.optString("store_id") );//门店
					fileState.put("usernum", json.optString("usernum") );//操作人
					fileState.put("tenancy_id", json.optString("tenancy_id") );//商户号
					fileState.put("exportname", fileName );//基础表名
					fileState.put("zipname", fileName+dataString+".zip");//基础表名
					fileStateList.add(fileState);
				}
				//将生成的表名 批量插入到表中
				try {
					Object[] insertBatchIgnorCase = dao.insertBatchIgnorCase(json.optString("tenancy_id"),"hq_export_file",fileStateList);
					for(int i = 0 ; i <fileStateList.size() ; i++){
						fileStateList.get(i).put("id",insertBatchIgnorCase[i]);
					}
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				int cc = (int) (fileStateList.size()>json.optInt("rows")? json.optInt("rows"):fileStateList.size());
				data.put("list", fileStateList.subList(0, cc));
				data.put("totalCount", fileStateList.size());
				return data ;
	}

	@Override
	public JSONObject selectFilesStates(String attribute, JSONObject p) {
		// TODO Auto-generated method stub
		return commonMethodAreaDao.selectFilesStates(attribute,p);
	}

	@Override
	public List<JSONObject> selectFilesStatesById(String attribute, JSONObject p) {
		// TODO Auto-generated method stub
		return commonMethodAreaDao.selectFilesStatesById(attribute,p);
	}
	
	@Override
	public JSONObject cancelFilesStates(String tenancy_id, JSONObject json) {
		// TODO Auto-generated method stub
		// 修改状态
		JSONObject retrunJson = new JSONObject();
		int c = 0 ;
		try {
			json.put("filestate", "2");
			c=dao.updateIgnorCase(tenancy_id, "hq_export_file", json);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		retrunJson.put("updateNum", c);
		return retrunJson;
	}

	@Override
	public JSONObject selectAllFilesStates(String attribute, JSONObject p) {
		// TODO Auto-generated method stub
		return commonMethodAreaDao.selectAllFilesStates(attribute,p);
	}

	@Override
	public Object deleteFilesList(String tenantId, JSONObject p) {
		// TODO Auto-generated method stub
		String deleteString = " DELETE from hq_export_file where id  in ("+p.optString("deleteIds")+")";
		boolean execute =true ;
		try {
			  execute = dao.execute(tenantId, deleteString);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return execute;
	}
	
	
	@Override
	public Object deleteAllFilesList(String tenantId, JSONObject p) {
		// TODO Auto-generated method stub
		String deleteString = " DELETE from hq_export_file where 1=1";
		// 删除的历史数据
		if(p.optString("exportName").length()>0){
			deleteString+= " and exportName = '"+p.optString("exportName")+"'";
			deleteString+= " and store_id = "+p.optInt("store_id");
		}
		// 删除实时数据
		if(p.optString("zipname").length()>0){
			deleteString+= " and zipname = '"+p.optString("zipname")+"'";
		}
		boolean execute =true ;
		try {
			  execute = dao.execute(tenantId, deleteString);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return execute;
	}
	
	@Override
	public List<JSONObject> getCategorySelect(String tenancyId, JSONObject obj)throws Exception {
		return commonMethodAreaDao.getCategorySelect(tenancyId, obj);
	}

	@Override
	public String loadCategoryTree(String tenancyId, JSONObject obj)throws Exception {
		return commonMethodAreaDao.loadCategoryTree(tenancyId, obj);
	}
	
	@Override
	public String customize(String tenancyId, Integer type, Object param)
			throws Exception {
		// TODO Auto-generated method stub
		return commonMethodAreaDao.customize(tenancyId,type,param);
	}

	@Override
	public String getChanelType(String attribute, int parseInt, JSONObject p){
		// TODO Auto-generated method stub
		return commonMethodAreaDao.getChanelType(attribute,parseInt,p);
	}

	@Override
	public String getOrgansTreeByConditios(String attribute, String attribute2,
			String attribute3, JSONObject p, String conditions) throws Exception {
		// TODO Auto-generated method stub
		return commonMethodAreaDao.getOrgansTreeByConditios( attribute, attribute2, attribute3, p, conditions);
	}

	@Override
	public JSONObject getBillTitleData(String attribute, JSONObject p)
			throws Exception {
		// TODO Auto-generated method stub
		return commonMethodAreaDao.getBillTitleData(attribute, p);
	} 
	
	@Override
	public List<JSONObject> getCoupon(String tenancyID,JSONObject condition) throws Exception {
		return commonMethodAreaDao.getCoupon(tenancyID, condition);
	}
	@Override
	public List<JSONObject> getItemDl(String tenancyID,JSONObject condition) throws Exception {
		return commonMethodAreaDao.getItemDl(tenancyID, condition);
	}
	@Override
	public List<JSONObject> getItemXl(String tenancyID,JSONObject condition) throws Exception {
		return commonMethodAreaDao.getItemXl(tenancyID, condition);
	}
	@Override
	public List<JSONObject> getItemName(String tenancyID,JSONObject condition) throws Exception {
		return commonMethodAreaDao.getItemName(tenancyID, condition);
	}
	
	@Override
	public String getOrgansTreeByConditionsV2(String tenancyId, String organId, String organCode, JSONObject params, String conditions) throws Exception {
		DBContextHolder.setTenancyid(tenancyId);

		StringBuilder sb = new StringBuilder();
		Organ organ = new Organ();;
		sb.append("select id,org_full_name as label,top_org_id as fatherId from organ where 1=1 and valid_state='1'"); 
		if(null != conditions && StringUtils.isNotBlank(conditions)){
			sb.append("and id in("+conditions+")    ");
		}
		sb.append(" order by organ_code  ");
		
		List<Organ> list = (List<Organ>) this.dao.query(tenancyId, sb.toString(), Organ.class);
		List<Organ> list2 = new ArrayList<Organ>();
		Map<Integer, Organ> map = new HashMap<Integer, Organ>();
		for (Organ jo : list){
			jo.setLabelc(jo.getLabel());
			map.put(jo.getId(), jo);
		}
		for (Organ jo1 : list){
			if (map.get(jo1.getFatherId()) == null){
				list2.add(jo1);
			}
			if (map.get(jo1.getFatherId()) != null){
				map.get(jo1.getFatherId()).getChildren().add(map.get(jo1.getId()));
			}
		}
		for(Integer key : map.keySet()){
			int size =map.get(key).getChildren()==null?0:map.get(key).getChildren().size();
			map.get(key).setCount(size);
		}
		boolean flag = false;
		if("0".equals(organId))
		{
			flag = true;
		}
		else
		{
			String[] oids = conditions.split(",");
			for(String oid:oids)
			{
				if("0".equals(oid))
				{
					flag = true;
					break;
				}
			}
		}
		
		if (flag){
			organ.setChildren(list2);
//			organ.setLevel(0);
//			organ.setCode("");
			organ.setId(0);
//			organ.setType("1");
//			organ.setText(organ.getCode() + "总部");
			organ.setLabel("总部");
			
			organ.setCity("");
			organ.setLabelc("总部");
			organ.setPlabels("");
			
			List<Organ> flist = new ArrayList<Organ>();
			flist.add(organ);	
			return JsonUtils.list2json(flist);
		}
		return JsonUtils.list2json(list2);
	}
	
	@Override
	public List<JSONObject> getReportTH(String attribute, JSONObject p) throws Exception {
		return commonMethodAreaDao.getReportTH(attribute,p);
	}
	
	@Override
	public JSONObject addOrUpdTH(String tenancyID,String tableName,List<JSONObject> condition,String type)throws Exception {
		JSONObject result = new JSONObject();
		if(type.equals("add")){
			Object[] add = (Object[]) commonMethodAreaDao.addOrUpdTH(tenancyID,tableName,condition,type);
			
			//将数据id与pid更新成一致的值
			StringBuffer sql = new StringBuffer();
			StringBuffer idStr = new StringBuffer("");
			for (int i=0; i < add.length; i++) {
				idStr.append(add[i]).append(",");
			}
			String ids = idStr.substring(0, idStr.length()-1);
			sql.append("select * from rpt_free where id in (").append(ids).append(")");
			List<JSONObject> updList = this.dao.query4Json(tenancyID, sql.toString());
			for (int i = 0; i < updList.size(); i++) {
				JSONObject obj = updList.get(i);
				obj.put("pid", obj.optInt("id"));
				obj.put("forbid_edit",obj.optBoolean("forbid_edit")==true?1:0);
				obj.put("forbid_sort",obj.optBoolean("forbid_sort")==true?1:0);
				obj.put("enabled",obj.optBoolean("enabled")==true?1:0);
				obj.put("is_sort",obj.optBoolean("is_sort")==true?1:0);
				obj.put("is_fixed",obj.optBoolean("is_fixed")==true?1:0);
			}
			int[] upd = (int[]) commonMethodAreaDao.addOrUpdTH(tenancyID,tableName,updList,"upd");
			
			if(add.length>0){
				result.element("success", true);
			}else{
				result.element("success", false);
			}
		}else{
			int[] upd = (int[]) commonMethodAreaDao.addOrUpdTH(tenancyID,tableName,condition,type);
			if(upd.length>0){
				result.element("success", true);
			}else{
				result.element("success", false);
			}
		}
		return result;
	}

	@Override
	public Boolean getHBASEPrivilege(String tenancyID, JSONObject condition)throws Exception {
		return commonMethodAreaDao.getHBASEPrivilege(tenancyID, condition);
	}

	@Override
	public Boolean getHBASEPrivileges() throws Exception {
		return commonMethodAreaDao.getHBASEPrivileges();
	}
	
	
	@Override
	public String getChanelTypeByBrand(String attribute,JSONObject p) throws Exception{
		// TODO Auto-generated method stub
		return commonMethodAreaDao.getChanelTypeByBrand(attribute,p);
	}
	
	@Override
	public String loadCategoryTreeByBrand(String tenancyId,  Object param)
			throws Exception {
		// TODO Auto-generated method stub
		return commonMethodAreaDao.loadCategoryTreeByBrand(tenancyId, param);
	}
	
	@Override
	public List<JSONObject> findDicVal(String tenancyId,String field) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("select class_item_code as id,class_item as text from sys_dictionary where 1=1 and valid_state = '1' ");
		if(StringUtils.isNotBlank(field))
		{
			sql.append(" and class_identifier_code = '");
			sql.append(field);
			sql.append("'");
			sql.append(" order by id asc");
		}
		else
		{
			return new ArrayList<JSONObject>();
		}
		List<JSONObject> list = dao.query4Json(tenancyId, sql.toString());
		if(list!=null&& list.size()>0)
		{
			return list;
		}
		return new ArrayList<JSONObject>();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public String registerChanel(String tenancyId, Integer type, Object param)
	{
		JSONObject jb = (JSONObject) param;
		StringBuilder sb = new StringBuilder();
		// String result = "";
		switch (type)
		{
		// 查询数据字段
			case 0:
				if (jb.containsKey("code"))
				{
					if (jb.containsKey("y"))
					{
						if (jb.getInt("y") == 1)
						{
							sb.append("select sd.class_item_code as id,sd.class_item as text from hq_brand_info_channel hbi LEFT JOIN sys_dictionary sd on hbi.channel_id = sd.id where hbi.brand_info_id = "+jb.optString("fatherId")+" and sd.class_identifier_code = '" + jb.get("code").toString() + "' and sd.is_sys='Y' and sd.valid_state='" + Constant.BOOL_TRUE + "' ");
						}
						else
						{
							sb.append("select sd.class_item_code as id,sd.class_item as text from hq_brand_info_channel hbi LEFT JOIN sys_dictionary sd on hbi.channel_id = sd.id where hbi.brand_info_id = "+jb.optString("fatherId")+" and sd.class_identifier_code = '" + jb.get("code").toString() + "' and sd.is_sys='Y'");							
						}

					}
					else
					{
						sb.append("select sd.id,sd.class_item as text from hq_brand_info_channel hbi LEFT JOIN sd.sys_dictionary sd on hbi.channel_id = sd.id where hbi.brand_info_id = "+jb.optString("fatherId")+" and sd.class_identifier_code = '" + jb.get("code").toString() + "' and sd.is_sys='N' and sd.valid_state='" + Constant.BOOL_TRUE + "' ");
					}

					try
					{
						List<BasicCombobox> list = (List<BasicCombobox>) this.dao.query(tenancyId, sb.toString(), BasicCombobox.class);
						return com.tzx.framework.common.util.JsonUtils.list2json(list);
					}
					catch (Exception e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				break;

			default:
				break;
		}
		return null;
	}
	
	@Override
	public String getComboTree(String tenancyId, String chanel, String fatherId) throws Exception
	{
		try
		{
			StringBuilder sql = new StringBuilder();

			sql.append("select 0 as id,0 as father_id,sd.class_item_code as chanel,sd.class_item_code as code ,sd.class_item,sd.class_item as itemclass_name,sd.class_item as text from hq_brand_info_channel hbi LEFT JOIN sys_dictionary sd on hbi.channel_id = sd.id where hbi.brand_info_id = "+fatherId+" and sd.class_identifier_code = 'chanel' and sd.valid_state='1'");
			if (Tools.hv(chanel))
			{
				sql.append(" and class_item_code ='"+chanel+"'");
			}
			sql.append(" order by chanel");

			@SuppressWarnings("unchecked")
			List<HqItemClass> listDict = (List<HqItemClass>) dao.query(tenancyId, sql.toString(), HqItemClass.class);

			sql.delete(0, sql.length());

			sql.append("select a.id,a.chanel,a.father_id,a.itemclass_name,a.itemclass_code, a.itemclass_code as code,");
			sql.append("b.class_item as class_item from hq_item_class a ");
			sql.append(" join sys_dictionary b on (b.class_item_code=a.chanel and b.class_identifier_code='chanel')");
			sql.append(" left join hq_brand_info_channel hbi on hbi.channel_id = b.id ");
			sql.append("where 1=1 and a.valid_state='1' and hbi.brand_info_id=").append(fatherId);
			if (Tools.hv(chanel))
			{
				sql.append(" and a.chanel ='"+chanel+"'");
			}
			sql.append(" order by itemclass_code");
			@SuppressWarnings("unchecked")
			List<HqItemClass> listRes = (List<HqItemClass>) dao.query(tenancyId, sql.toString(), HqItemClass.class);

			Map<Object, HqItemClass> map = new HashMap<Object, HqItemClass>();

			for (HqItemClass json : listRes)
			{
				json.setChildren(new ArrayList<HqItemClass>());
				map.put(json.getId(), json);
			}
			//设置上下级关系
			for (HqItemClass json : listRes)
			{
				String itemclassCode = json.getItemclassCode();
				if ("".equals(itemclassCode)){
					json.setText(json.getItemclassName());
				}else{
					json.setText("(" + json.getItemclassCode() + ")" + json.getItemclassName());
				}

				if (map.containsKey(json.getFatherId()))
				{
					List<HqItemClass> l1 = map.get(json.getFatherId()).getChildren();
					l1.add(json);
				}
			}

			for (HqItemClass gO : listDict)
			{
				gO.setChildren(new ArrayList<HqItemClass>());
				for (HqItemClass j1 : listRes)
				{

					if (j1.getFatherId().equals(0) && j1.getChanel().equals(gO.getChanel()))
					{

						List<HqItemClass> l2 = gO.getChildren();
						l2.add(j1);
					}

				}

			}

			return com.tzx.framework.common.util.JsonUtils.list2json(listDict);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			return null;
		}
	}
	
	@Override
	public List<JSONObject> loadFirstLevel(String tenentid,String sysuser, JSONObject condition) throws Exception
	{
		
		StringBuilder sql = new StringBuilder();
		
		if(sysuser!=null && "1".equals(sysuser))
		{
			sql.append("select id,module_name,module_type,create_date,create_person,states,module_level,father_module_id,module_link_url,module_use_img,module_class from sys_modules where  module_level in (1,2) and states='" + Constant.BOOL_TRUE + "' and (module_type is null or module_type!='pos')");
			sql.append(" order by order_num,module_level, father_module_id, id");
//			sql.append(" order by module_level, father_module_id, id");
		}
		else
		{
			//ua.id,
			sql.append("select distinct (sm.id),ua.store_id,sm.module_name,sm.module_level,sm.module_link_url,sm.father_module_id,sm.if_super_module,sm.states,sm.module_use_img,sm.module_class ");
			sql.append(",sm.order_num ");
			sql.append("from user_authority ua join user_authority_roles uar on uar.user_id = ua.id ");
			sql.append("join roles r on uar.roles_id = r.id ");
			sql.append("join role_module_ref rmr on rmr.role_id = r.id ");
			sql.append("join sys_modules sm on rmr.sys_module_id = sm.id ");
			sql.append("where 1=1  and sm.module_level in (1,2,3,4) and sm.states='");
			sql.append(Constant.BOOL_TRUE);
			sql.append("'");
			sql.append(" and (sm.module_type is null or sm.module_type!='pos')");
			@SuppressWarnings("unchecked")
			Set<String> keys1 = condition.keySet();
			for(String key:keys1)
			{
				if("store_id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.store_id = ");
					sql.append(condition.get(key).toString());
				}
				if("id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.id = ");
					sql.append(condition.get(key).toString());
				}
			}
			sql.append(" order by sm.order_num, sm.module_level, sm.father_module_id, sm.id");
//			sql.append(" order by sm.module_level, sm.father_module_id, sm.id");
		}
		List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
		if(list!=null && list.size()>0){
			return list;
		}
		return null;
	}
	
	@Override
	public Integer getUserId(String tenancyId, String username) throws Exception{
		String sql = "select * from user_authority where user_name='"+username+"'";
		List<JSONObject> list = this.dao.query4Json(tenancyId, sql);
		if(list!=null && list.size()>0){
			return Integer.valueOf(list.get(0).getString("id"));
		}
		return null;
	}

	@Override
	public JSONObject localSession(HttpSession session, JSONObject condition)throws Exception {
		JSONObject result = new JSONObject();
		try {
			JSONObject param = new JSONObject();
		/*	param.put("tenancyId", tenentid);
			param.put("userName", userName);*/
			String themes = "";
			String tenentid = condition.optString("tenentid");
			param.put("tenentid", condition.optString("tenentid"));
			param.put("loginUserName", condition.optString("userName"));
			JSONObject userInfoJosonObje = systemUserService.chekUserLogin(tenentid, param);

			if (param.optString("loginUserName").equals("system") ){

				JSONObject jsonObject = new JSONObject();
				jsonObject.put("employeeName", "system");
				jsonObject.put("userName", "system");
				jsonObject.put("organName", "总部");
				jsonObject.put("organ_code", "0");
				jsonObject.put("organ_id", "0");
				jsonObject.put("store_id", "0");
				jsonObject.put("employeeId", "0");
				jsonObject.put("e_id", "0");
				jsonObject.put("organ_brief_code", "CN0000");
				jsonObject.put("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
				jsonObject.put("name", "系统管理员");
				jsonObject.put("org_type", "0");
				jsonObject.put("da_date", "");
				jsonObject.put("day_count", "");
				// 用于判断是否是管理员
				jsonObject.put("sysuser", "1");
				jsonObject.put("tenentid", tenentid);

				jsonObject.put("organName", "总部");
				jsonObject.put("organ_code", "0");
				jsonObject.put("organ_id", "0");
				jsonObject.put("store_id", "0");
				jsonObject.put("employeeId", "0");
				jsonObject.put("e_id", "0");
				jsonObject.put("organ_brief_code", "CN0000");
				jsonObject.put("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
				jsonObject.put("name", "系统管理员");
				jsonObject.put("org_type", "0");
				jsonObject.put("da_date", "");
				jsonObject.put("day_count", "");
				// 用于判断是否是管理员
				jsonObject.put("sysuser", "1");


				session.setAttribute("isLogin", "true");
				session.setAttribute("tenentid", tenentid);
				session.setAttribute("employeeName", "system");
				session.setAttribute("userName", "system");
				session.setAttribute("organName", "总部");
				session.setAttribute("organ_code", "0");
				session.setAttribute("organ_id", "0");
				session.setAttribute("store_id", "0");// 机构ID
				session.setAttribute("employeeId", "0");
				session.setAttribute("e_id", "0");
				session.setAttribute("organ_brief_code", "CN0000");
				session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
				session.setAttribute("name", "系统管理员");
				session.setAttribute("org_type", "0");
				session.setAttribute("da_date", "");
				session.setAttribute("day_count", "");
				// 用于判断是否是管理员
				session.setAttribute("sysuser", "1");
				//查询用户是否具有特殊权限,如果没有则返回普通权限
				String sysuser = (String) session.getAttribute("sysuser");
				String organ_codes= systemUserService.findOrganCodes(tenentid,sysuser,userInfoJosonObje);
				String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenentid,sysuser,userInfoJosonObje);
				session.setAttribute("user_organ_codes_group_invalid", organ_codes_invalid);
				session.setAttribute("user_organ_codes_group", organ_codes);
				
				session.setAttribute("themes", (themes == null || themes.isEmpty()) ? "metro-gray" : themes);
				// session.setAttribute("roles_id", "admin");// 以 ” ，"分开的IDS
				Map<Integer, Integer> map = new HashMap<Integer, Integer>();
				map.put(0, 1);
				session.setAttribute("authMap", map);
				JSONObject p = JSONObject.fromObject("{}");
				p.put("tenancy_id", (String) session.getAttribute("tenentid"));
				p.put("id", (String) session.getAttribute("employeeId"));
				p.put("store_id", (String) session.getAttribute("store_id"));
				session.setAttribute("moduleMap", systemUserService.getRoleAuthorutyModule(tenentid, (String) session.getAttribute("sysuser"), p));
				
				//多品牌标识 0单一品牌  1多品牌
				session.setAttribute("valid_state", systemUserService.checkIsMultiBrand(tenentid));
				result.put("success", true);
			}else{
				if (userInfoJosonObje != null){
					session.setAttribute("isLogin", "true");
					session.setAttribute("tenentid", tenentid);
					// 用于判断是否是管理员
					if(param.optString("loginUserName").equals(Constant.ADMIN)){
						session.setAttribute("sysuser", "1");
					}else{
						session.setAttribute("sysuser", "0");
					}
					
					
					String sysuser = (String) session.getAttribute("sysuser");
					// session.setAttribute("login_account",
					// userInfoJosonObje.getString("login_account"));
					session.setAttribute("employeeName", userInfoJosonObje.optString("user_name"));
					session.setAttribute("userName", userInfoJosonObje.optString("user_name"));
					session.setAttribute("employeeId", userInfoJosonObje.optString("employee_id"));// 用户id
					session.setAttribute("e_id", userInfoJosonObje.optString("employee_id"));
					session.setAttribute("organ_id", userInfoJosonObje.optString("store_id"));
					session.setAttribute("organName", userInfoJosonObje.optString("org_full_name"));
					session.setAttribute("organ_code", userInfoJosonObje.optString("organ_code"));
					session.setAttribute("organ_brief_code", userInfoJosonObje.optString("organ_brief_code"));
					session.setAttribute("name", userInfoJosonObje.optString("name"));
					session.setAttribute("org_type", userInfoJosonObje.optString("org_type"));
					session.setAttribute("da_date", "null".equals(userInfoJosonObje.optString("da_date"))?"":userInfoJosonObje.optString("da_date"));
					session.setAttribute("day_count", "null".equals(userInfoJosonObje.optString("day_count"))?"":userInfoJosonObje.optString("day_count"));

					if ("0".equals(userInfoJosonObje.optString("store_id"))){
						session.setAttribute("org_type", "0");
						session.setAttribute("da_date", "");
						session.setAttribute("day_count", "");
						session.setAttribute("organ_code", "0");
						session.setAttribute("organName", "总部");
						session.setAttribute("organ_brief_code", "CN0000");
					}

					String organ_codes= systemUserService.findOrganCodes(tenentid,sysuser,userInfoJosonObje);
					session.setAttribute("user_organ_codes_group", organ_codes);
					String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenentid,sysuser,userInfoJosonObje);
					session.setAttribute("user_organ_codes_group_invalid", organ_codes_invalid);
					session.setAttribute("user_id", userInfoJosonObje.optInt("id"));
					session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
					// session.setAttribute("roles_id",
					// userInfoJosonObje.getString("role_id"));//
					session.setAttribute("store_id", userInfoJosonObje.getString("store_id"));// 机构ID
					session.setAttribute("themes", (themes == null || themes.isEmpty()) ? "metro-gray" : themes);
					// 加载用户的权限信息
					JSONObject p = JSONObject.fromObject("{}");
					p.put("tenancy_id", (String) session.getAttribute("tenentid"));
					p.put("id", (String) session.getAttribute("employeeId"));
					p.put("store_id", (String) session.getAttribute("store_id"));
					session.setAttribute("authMap", systemUserService.getRoleAuthoruty(tenentid, (String) session.getAttribute("sysuser"), p));
					session.setAttribute("moduleMap", systemUserService.getRoleAuthorutyModule(tenentid, (String) session.getAttribute("sysuser"), p));
					session.setAttribute(" ", userInfoJosonObje); // 把整个用户信息的JSONObject对象放入JSON
					
					//多品牌标识 0单一品牌  1多品牌
					session.setAttribute("valid_state", systemUserService.checkIsMultiBrand(tenentid));
					result.put("success", true);
				}else{
					logger.info("loadSession userInfo is empty!");
					result.put("success", false);
				}
			}
			
		} catch (Exception e) {
			logger.error("loadSession异常, tenentid: {}, userName{}", condition.optString("tenentid"), condition.optString("userName"));
			logger.error("loadSession异常", e);
			throw new IllegalStateException("查询用户信息异常");
		}
		return result;
	}

	@Override
	public int[] updNewsClickRate(String tenancyID, String tableName,List<JSONObject> array) throws Exception {
		return commonMethodAreaDao.updNewsClickRate(tenancyID, tableName, array);
	}
	
	@Override
	public JSONObject getPosLimitReportQueryArea(String tenancyID, JSONObject condition)throws Exception {
		return commonMethodAreaDao.getPosLimitReportQueryArea(tenancyID, condition);
	}
	
	
	@Override
	public JSONArray getPaymentRange(String tenancyID, JSONObject condition)throws Exception {
	    return commonMethodAreaDao.getPaymentRange(tenancyID, condition);
	}

	@Override
	public Roles getUserRoles(String tenancyId, String employeeId) throws Exception {
		StringBuffer sb = new StringBuffer();
		sb.append("SELECT r.* FROM user_authority ua JOIN roles r ON ua.roles_id=r.id WHERE ua.employee_id=");
		sb.append(employeeId);
		System.out.println("===getUserRoles:"+sb.toString());

		List<Roles> roleList = (List<Roles>) this.dao.query(tenancyId,sb.toString(),Roles.class);
		if(roleList.size()>0){
			return roleList.get(0);
		}
		return null;
	}
}
