package com.tzx.cc.invoice.electronic.test;

import com.tzx.cc.invoice.electronic.bworiginal.*;
import com.tzx.cc.invoice.electronic.cache.BWConfigCache;
import com.tzx.cc.invoice.electronic.util.BwElectronicInvoiceWebServiceUtils;
import com.tzx.cc.invoice.electronic.util.UUID4EWMUtils;
import com.tzx.cc.invoice.electronic.util.UUIDUtils;
import com.tzx.cc.invoice.electronic.util.XmlUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.xmlbeans.impl.util.Base64;
import org.dom4j.Element;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;

import java.net.URLEncoder;
import java.security.SecureRandom;

public class BaiwangTest {
	String publicUrl = "http://localhost:9090/tzxsaas/";

	public static String KEY_STORE_FILE = "D:\\keys\\test\\testISSUE.pfx";
	public static String KEY_STORE_PASS = "123456";
	public static String TRUST_STORE_FILE = "d:/keys/fapiao2017client.truststore";
	public static String TRUST_STORE_PASS = "123456";

	
	@org.junit.Test
	public void testaa() throws Exception {
		long next = UUID4EWMUtils.next();
		System.out.println(next);
	}
	@org.junit.Test
	public void test() throws Exception {
		String requestUrl = "https://dev.fapiao.com:18943/fpt-dsqz/";//初始化地址
		String appid = "6d29f136114544bcc73edcce960c430231183cc192c433e2b9ebcad56e8ceb08";//appid
		String contentPassword = "5EE6C2C11DD421F2";//AES加密密钥
		String fpqqlsh = "TEST2017022415272502";// 需要查询发票的流水号
		String nsrsbh = "110109500321655";
//		String fpdm = "050003521333";
//		String fphm = "85004524";
        String content = kp();
//        content = "eyJGUFFRTFNIIjoiMjAxNzA1MzExNzQ2NDkwMDAwMCIsIktQTFgiOiIwIiwiWlNGUyI6IjAiLCJYU0ZfTlNSU0JIIjoiMTEwMTA5NTAwMzIxNjU1IiwiWFNGX01DIjoi55m+5pe655S15a2Q5rWL6K+VMiIsIlhTRl9EWkRIIjoi5Y2X5bGx5Yy66JuH5Y+jLDgzNDg0OTQ5IiwiWFNGX1lIWkgiOiIxMjM0IiwiR01GX01DIjoi5YyX5Lqs5pmu54Wn5aSp5pifIiwiR01GX1lIWkgiOiIiLCJHTUZfU0pIIjoiMTMzNDEwMjIxMjYiLCJHTUZfRFpZWCI6IiIsIktQUiI6Iuezu+e7nyIsIkpTSEoiOiI1MCIsIkhKU0UiOiI4LjUwIiwiSEpKRSI6IjQxLjUwIiwiSFlMWCI6MCwiQ09NTU9OX0ZQS0pfWE1YWFMiOlt7IkZQSFhaIjoiMCIsIlhNTUMiOiLppJDppa4iLCJYTVNMIjoiMSIsIlhNREoiOiI1MCIsIlhNSkUiOiI1MCIsIlNMIjowLjE3LCJTRSI6IjguNTAifV19";
        System.out.println("aaa:"+content);
//        String content = BwElectronicInvoiceWebServiceUtils.getSearchContent(nsrsbh,fpqqlsh);
//		String content = BwElectronicInvoiceWebServiceUtils.getHcContext(nsrsbh,fpqqlsh,"百旺电子测试2","","");
		String xml = BwElectronicInvoiceWebServiceUtils.getSendXml(Utils.dfxj1001, appid, contentPassword, content);
		
		/*System.setProperty("javax.net.ssl.trustStore", TRUST_STORE_FILE);
		System.setProperty("javax.net.ssl.trustStorePassword", TRUST_STORE_PASS);
		System.setProperty("javax.net.ssl.keyStoreType","PKCS12") ;
		System.setProperty("javax.net.ssl.keyStore", KEY_STORE_FILE);
		System.setProperty("javax.net.ssl.keyStorePassword", KEY_STORE_PASS);
		System.out.println(xml);
		System.out.println("---------------------------------------");*/
		String s=null;
		try{
//		   s = BwElectronicInvoiceWebServiceUtils.sendWebService(requestUrl+"invoice", xml,nsrsbh);
		} catch(Exception e){
			e.printStackTrace();
		}
		Element parse = XmlUtils.parse(s);
		Element returncode = XmlUtils.getElement(parse, "returnStateInfo","returnCode");
		Element returnsmg = XmlUtils.getElement(parse, "returnStateInfo","returnMessage");
		System.out.print(returncode.getTextTrim());
		System.out.print(returnsmg.getTextTrim());
		/*Element returncode = XmlUtils.getElement(parse, "Data","content");
		System.out.println(s);
		System.out.println(returncode.getTextTrim());


		byte []tmp = MyAES.decryptBASE64(returncode.getTextTrim());
		System.out.println("原文111=" + new String(tmp));*/
	}



	public static byte[] desCrypto(byte[] datasource, String password) {
		try{
			SecureRandom random = new SecureRandom();
			DESKeySpec desKey = new DESKeySpec(password.getBytes());
			//创建一个密匙工厂，然后用它把DESKeySpec转换成
			SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
			SecretKey securekey = keyFactory.generateSecret(desKey);
			//Cipher对象实际完成加密操作
			Cipher cipher = Cipher.getInstance("DES");
			//用密匙初始化Cipher对象
			cipher.init(Cipher.ENCRYPT_MODE, securekey, random);
			//现在，获取数据并加密
			//正式执行加密操作
			return cipher.doFinal(datasource);
		}catch(Throwable e){
			e.printStackTrace();
		}
		return null;
	}

	private static byte[] decrypt(byte[] src, String password) throws Exception {
		// DES算法要求有一个可信任的随机数源
		SecureRandom random = new SecureRandom();
		// 创建一个DESKeySpec对象
		DESKeySpec desKey = new DESKeySpec(password.getBytes());
		// 创建一个密匙工厂
		SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
		// 将DESKeySpec对象转换成SecretKey对象
		SecretKey securekey = keyFactory.generateSecret(desKey);
		// Cipher对象实际完成解密操作
		Cipher cipher = Cipher.getInstance("DES");
		// 用密匙初始化Cipher对象
		cipher.init(Cipher.DECRYPT_MODE, securekey, random);
		// 真正开始解密操作
		return cipher.doFinal(src);
	}

	@org.junit.Test
	public void test2() throws Exception {
		//待加密内容
		//r=门店号,订单号,金额,小票日期,纳税人识别号
		String str = "17805,201706051841,1.22,20140505,110109500321655";
		//密码，长度要是8的倍数
		String password = "feNAMWpm";
		String r = URLEncoder.encode(DesEencryptUtil.encryptDES(str, password), "utf-8");
		System.out.println(r);
	}

    public static String kp() throws Exception {
        JSONObject json = new JSONObject();
        json.put("FPQQLSH","TEST2017022415272589");
        json.put("KPLX","0");
        json.put("ZSFS","0");
        json.put("XSF_NSRSBH","110109500321655");
        json.put("XSF_MC","百旺电子测试2");
        json.put("XSF_DZDH","南山区蛇口、83484949");
        json.put("XSF_YHZH","xx银行、88888888888");
        json.put("GMF_NSRSBH","");
        json.put("GMF_MC","张三");
        json.put("GMF_DZDH","");
        json.put("GMF_YHZH","");
        json.put("KPR","开票人");
        json.put("SKR","收款人");
        json.put("FHR","复核人");
        json.put("YFP_DM","");
        json.put("YFP_HM","");
        json.put("JSHJ","117");
        json.put("HJJE","100");
        json.put("HJSE","17");
        json.put("BZ","备注");
        json.put("GMF_SJH","");
        json.put("GMF_DZYX","");
        json.put("FPT_ZH","");
        json.put("HYLX","0");

        JSONArray arr = new JSONArray();
        JSONObject j = new JSONObject();
        j.put("FPHXZ","0");
        j.put("SPBM","1010101050000000000");
        j.put("XMMC","红高粱");
        j.put("GGXH","500克");
        j.put("DW","袋");
        j.put("XMSL","1");
        j.put("XMDJ","50");
        j.put("XMJE","50");
        j.put("SL","0.17");
        j.put("SE","8.5");
        arr.add(j);
        j = new JSONObject();
        j.put("FPHXZ","0");
        j.put("SPBM","1010101050000000000");
        j.put("XMMC","大米");
        j.put("GGXH","500克");
        j.put("DW","袋");
        j.put("XMSL","1");
        j.put("XMDJ","50");
        j.put("XMJE","50");
        j.put("SL","0.17");
        j.put("SE","8.5");
        arr.add(j);
        json.put("COMMON_FPKJ_XMXXS",arr);
        String s = BwElectronicInvoiceWebServiceUtils.json2Xml("<REQUEST_COMMON_FPKJ class='REQUEST_COMMON_FPKJ'>",
                "</REQUEST_COMMON_FPKJ>", json);
        return new BASE64Encoder().encodeBuffer(s.toString().getBytes("UTF-8"));
    }
}
