package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.SpecialAccountStatementDao;

@Repository(SpecialAccountStatementDao.NAME)
public class SpecialAccountStatementDaoImpl implements SpecialAccountStatementDao{
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;

	@Override
	public JSONObject getCashierShift(String tenancyID, JSONObject condition) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		String tmp = "";
		String tmpfooter = "";
		//查询函数
		String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID,condition,EngineConstantArea.SPECIAL_ACCOUNT_STATEMENT_QUERY);
		//执行函数获取临时表
		List<JSONObject> tmpList = this.dao.query4Json(tenancyID, completionSql.toString());
		if(tmpList.size()>0){
			//获取当前函数临时表
			//明细
			tmp = tmpList.get(0).getString("sql_text");
			//合计
			if(condition.containsKey("hierarchytype") && condition.optInt("hierarchytype")==1){
				tmpfooter =  tmpList.get(1).getString("sql_text");
			}
		}
		if(tmp.length()>0){
			//导出
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
				//具体数据
				list = this.dao.query4Json(tenancyID,tmp);
				//映射实体类型
				structure = conditionUtils.getSqlStructure(tenancyID,tmp);
			}else{
				//统计条数
				total = this.dao.countSql(tenancyID,tmp);
				//具体数据
				list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,tmp.toString()));
				if(tmpfooter.length()>0){
					//合计
					footerList = this.dao.query4Json(tenancyID, tmpfooter);	
				}
			}
		}
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);		
		result.put("rows", list);
		result.put("structure", structure);
		result.put("footer", footerList);
		return result;
	}
}
