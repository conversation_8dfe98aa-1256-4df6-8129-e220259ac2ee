package com.tzx.boh.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.tzx.boh.bo.MeterPayManagementNewService;
import com.tzx.boh.bo.MeterreadingManagementNewService;
import com.tzx.boh.bo.MeterreadingManagementService;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
@Service(MeterPayManagementNewService.NAME)
public class MeterPayManagementNewServiceImpl implements MeterPayManagementNewService
{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	@Override
	public String  loadMeterreadingInformation(String tenancyID, JSONObject condition) throws Exception
	{
		String lastReading="0";
		if(condition.containsKey("business_date") && condition.containsKey("water_utilit_id")&&!condition.get("water_utilit_id").equals("")&&!condition.get("business_date").equals("")&&!condition.get("business_date").equals("null")){
			StringBuilder sql = new StringBuilder();
			sql.append("select * from boh_water_utility_record");
			sql.append(" where 1=1 and recprd_type='pay' and  water_utilit_id = '" + condition.get("water_utilit_id") + "' and business_date=(select max(business_date) from boh_water_utility_record where water_utilit_id='" + condition.get("water_utilit_id") + "' and business_date < '" + condition.get("business_date") + "' and recprd_type='pay' )");
			if(condition.optString("sort")!=null && !"".equals(condition.optString("sort")))
			{
				sql.append(" order by "+condition.optString("sort")+" "+condition.optString("order"));
			}
			else
			{
				sql.append(" order by tenancy_id");
			}
			List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		
			if(list.size()>=1){
				  for(JSONObject result : list){
					  if(result.getString("reading")!=null&&!"null".equals(result.getString("reading"))&&!"".equals(result.getString("reading"))){
						  lastReading=result.getString("reading");
					  }
	              }
			}
		}
		return lastReading;
	}

	@Override
	public JSONObject loadAddMeterreadingInformation(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		if(!condition.get("organ_code").equals("0")&&!"".equals(condition.get("organ_code")) &&!"0".equals(condition.get("is_zb"))){
			sql.append("select q.*,o.org_full_name from (");	
			sql.append("SELECT A .tenancy_id,A .id,A .store_id,D.water_utility_code,D.water_utility_name,D.default_coefficient,A .remark,A .last_operator,");
			sql.append(" A .last_updatetime ,D.water_utility_type,D.unit,C .class_item AS water_utility_type_name,a.money,COALESCE(A.unit_price,d.unit_price) as unit_price,");
			sql.append(" b.org_short_name AS organ_name,A .recprd_type,A .business_date,A .water_utilit_id,A .reading,COALESCE(d.arithmetic,'add') as arithmetic,COALESCE(a.beginning,0) as beginning, ");
			sql.append(" (select count(*)  from boh_water_utility_record ss  where ss.water_utilit_id = a.water_utilit_id and a.business_date <ss.business_date and ss.recprd_type = 'pay' ) as num ,  ");
			sql.append("  abs((COALESCE(a.reading,0)-COALESCE(a.beginning,0))) as payamount");
			sql.append(" FROM boh_water_utility_record A");
			sql.append(" JOIN organ b ON b. ID = A .store_id");
			sql.append(" JOIN hq_water_utility D ON D. ID = A .water_utilit_id and d.valid_state = '1'");
			sql.append(" JOIN sys_dictionary C ON C .class_identifier_code = 'water_utility_type'");
			sql.append(" AND C .class_item_code = D.water_utility_type WHERE 1 = 1 and a.recprd_type='pay' ");
			sql.append(" and b.id in ("+condition.optString("user_organ_codes_group")+")");
			if (condition.containsKey("t1")){
				sql.append(" and  a.business_date >= TO_DATE('" + condition.get("t1") + "','YYYY-MM-DD') ");
			}
			if (condition.containsKey("t2")){
				sql.append(" and  a.business_date <= TO_DATE('" + condition.get("t2") + "','YYYY-MM-DD') ");
			}
			if (condition.containsKey("t3")&&!"".equals(condition.get("t3"))&&!"--全部--".equalsIgnoreCase(condition.optString("t3"))){
				sql.append(" and d.water_utility_type = ('" + condition.get("t3")+ "')");
			}
			if(condition.optString("sort")!=null && !"".equals(condition.optString("sort"))){
				sql.append(" order by "+condition.optString("sort")+" "+condition.optString("order"));
			}else{
				sql.append(" order by tenancy_id,business_date desc,last_updatetime desc");
			}
			sql.append(" )q,organ o where o.id=q.store_id ");
			if(condition.containsKey("store_id") && StringUtils.isNotBlank("store_id")){
				sql.append(" and o.id = '"+condition.optString("store_id")+"'");
			}
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
		
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		}
		return result;
	}

	@Override
	public boolean checkUnique(String tenentId, JSONObject param) throws Exception
	{
		try{

			String business_date = param.optString("business_date");
			String water_utilit_id = param.optString("water_utilit_id");
			String oldId = param.optString("id");
			if (Tools.hv(business_date) && Tools.hv(water_utilit_id))
			{
				StringBuilder sql = new StringBuilder();
				sql.append("select id from boh_water_utility_record info where info.recprd_type='pay' and info.business_date= '"+business_date+"' and info.water_utilit_id= '"+water_utilit_id+"'");
				long total = this.dao.countSql(tenentId, sql.toString());
				List<JSONObject> list = this.dao.query4Json(tenentId, this.dao.buildPageSql(param,sql.toString()));
				String id="";
				if(list.size()>=1){
					  for(JSONObject result : list){
		                	id=result.getString("id");
		                }
				}
              
                if(!oldId.equals(id)&& total >= 1){
                	return true;
                }else if(oldId.equals(id)&& total ==1){
                	return false;
                }else if( total <1){
                	return false;
                }
			}

			return true;
		}catch(Exception e)
		{
			e.printStackTrace();
			return true;
		}
	}

	@Override
	public void delete(String tenantId, String tableKey, List<JSONObject> keyList) throws Exception
	{
		if (!Tools.hv(tableKey) || !Tools.hv(keyList))
		{
			return;
		}

		this.dao.deleteBatchIgnorCase(tenantId, tableKey, keyList);
	}

	@Override
	public JSONObject findMeterTypeByStoreId(String tenancyId,
			JSONObject obj) throws Exception {
		StringBuffer sb = new StringBuffer();
		JSONObject returnJson = new JSONObject();
		sb.append("select id,water_utility_name as text from hq_water_utility where store_id = '"+obj.optString("store_id")+"' and valid_state = '1'  GROUP BY water_utility_name,water_utility_code,id ORDER BY water_utility_type,id");
		List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
		if(null != list && list.size() >0){
			returnJson.put("list", list);
			return returnJson;
		}
		returnJson.put("list", new ArrayList<JSONObject>());
		return returnJson;
	}

	@Override
	public List<JSONObject> findLastDataByStoreId(String tenancyId,
			JSONObject obj) throws Exception {
		StringBuffer sb = new StringBuffer();
		JSONObject returnJson = new JSONObject();
		//从对应最新记录表中查询出该门店中对应的所有记录 如果没有则替换成初始化的值
		sb.append("   select ww.id as water_utilit_id ,COALESCE(rr.business_date,CURRENT_DATE) as business_date,ww.water_utility_type ,ww.water_utility_code,ww.water_utility_name,    ");
		sb.append("   		ww.default_coefficient,ww.unit,'0' as reading,    ");
		sb.append("  CASE WHEN COALESCE(rr.arithmetic,'add') = 'add' THEN COALESCE(rr.reading,0) ");
		sb.append("   WHEN COALESCE (rr.arithmetic, 'add') = 'sub' THEN '0' END AS beginning,");
		sb.append("  '0' as payamount,");
		sb.append("   		COALESCE(ww.arithmetic,'add') as arithmetic ,COALESCE(ww.unit_price,0) as unit_price ,'0' as money,    ");
		sb.append("   		oo.org_full_name ,rr.remark ,(select count(1) from boh_water_utility_record uu where uu.water_utilit_id = ww.id and uu.recprd_type = 'pay' ) as num ,ww.store_id ");
		sb.append("   from hq_water_utility ww LEFT JOIN  boh_water_utility_lastreading_record rr on rr.water_utilit_id = ww.id  and rr.recprd_type ='pay'  ");
		sb.append("   			LEFT JOIN organ oo on oo.id = ww.store_id    ");
		sb.append("   where 1=1 and ww.valid_state = '1' and ww.store_id = '"+obj.optString("store_id")+"' order by ww.water_utility_type  ");
		List<JSONObject> dglist = this.dao.query4Json(tenancyId, sb.toString());
		return dglist;
	}

	@Override
	public void saveNewInfo(String tenancyId, JSONObject obj) throws Exception {
		//保存正常记录表的数据
		//obj.put("recprd_type", "read");
		String last_operator = obj.optString("last_operator");
		String last_updatetime = obj.optString("last_updatetime");
		JSONArray arr = JSONArray.fromObject(obj.get("rows"));
		StringBuffer sb = new StringBuffer();
		List<JSONObject> insertList = new ArrayList<JSONObject>();
		if(null != arr && arr.size()>0){
			for (int i = 0; i < arr.size(); i++) {
				JSONObject item = JSONObject.fromObject(arr.get(i));
				item.put("last_operator", last_operator);
				item.put("last_updatetime", last_updatetime);
				item.put("recprd_type", "pay");
				item.put("tenancy_id", tenancyId);
				insertList.add(item);
				sb.append("'"+item.optString("water_utilit_id")+"'");
				if(i != arr.size()-1){
					sb.append(",");
				}
			}
			if(insertList.size() >0){
				this.dao.insertBatchIgnorCase(tenancyId, "boh_water_utility_record", insertList);
			}
			
			/**
			 * 以下是操作日志表
			 */
			String ids = sb.toString();
			sb.setLength(0);
			sb.append("select * from boh_water_utility_lastreading_record where water_utilit_id in ("+ids+") and recprd_type ='pay'");
			List<JSONObject> returnlist = this.dao.query4Json(tenancyId, sb.toString());
			List<JSONObject> updatelist = new ArrayList<JSONObject>();
			
			if(null != returnlist && returnlist.size() >0){
				for(int i=0;i<returnlist.size();i++){
					int rid = returnlist.get(i).optInt("water_utilit_id");
					Iterator<JSONObject> it = insertList.iterator();
					while(it.hasNext()){
						JSONObject addjson = it.next();
						int nrid =addjson.optInt("water_utilit_id");
						if(rid == nrid){
							addjson.put("id",  returnlist.get(i).optInt("id"));
							addjson.put("recprd_type", "pay");
							updatelist.add(addjson);
							it.remove();
						}
					}
				}
				if(updatelist.size()>0){ 
					this.dao.updateBatchIgnorCase(tenancyId,  "boh_water_utility_lastreading_record", updatelist);
				}
				if(insertList.size()>0){
					this.dao.insertBatchIgnorCase(tenancyId, "boh_water_utility_lastreading_record", insertList);
				}
			}else{
				if(insertList.size()>0){
					this.dao.insertBatchIgnorCase(tenancyId, "boh_water_utility_lastreading_record", insertList);
				}
			}
			
		}
	}

	@Override
	public JSONObject checkBsDate(String tenancyId, JSONObject param)
			throws Exception {
		JSONObject json = new JSONObject();
		json.put("success", true);
		StringBuilder sql = new StringBuilder();
		try {
			JSONArray arr = JSONArray.fromObject(param.get("rows"));
			if(null != arr  && arr.size() >0){
				for (int i=0;i<arr.size();i++) {
					JSONObject jj = JSONObject.fromObject(arr.get(i));
					String business_date = jj.optString("business_date");
					String water_utilit_id = jj.optString("water_utilit_id");
					if(i >0){
						sql.append(" \n  union \n");
					}
					sql.append("SELECT COUNT (1) AS COUNT, '"+business_date+"' AS business_date, MAX (info.business_date) AS maxdate,max(ww.water_utility_name) AS NAME FROM boh_water_utility_record info LEFT JOIN hq_water_utility ww on ww.id = info.water_utilit_id  WHERE info.recprd_type = 'pay' AND to_date( to_char( info.business_date, 'YYYY-MM-DD' ), 'YYYY-MM-DD' ) >= to_date('"+business_date+"', 'YYYY-MM-DD') AND info.water_utilit_id = '"+water_utilit_id+"'");
					if(jj.containsKey("id") && StringUtils.isNotBlank(jj.optString("id"))){
						sql.append("   and info.id <> '"+jj.optString("id")+"'");
					}
				}
			}
			List<JSONObject> list = this.dao.query4Json(tenancyId, sql.toString());
			if(null != list && list.size() >0){
				boolean mark = false;
				StringBuffer msg = new StringBuffer();
				for (int x = 0; x < list.size(); x++) {
					JSONObject jb = JSONObject.fromObject(list.get(x));
					if(jb.optInt("count")>0){
						mark = true;
						msg.append(jb.optString("name")+"抄表日期应大于"+jb.optString("maxdate"));
						if(x != list.size()-1){
							msg.append(",");
						}
					}
				}
				if(mark){
					json.put("success", false);
					json.put("msg", msg.toString());
				}
			}
		} catch (Exception e) {
			json.put("success", false);
			json.put("msg", "校验时sql查询异常");
			e.printStackTrace();
		}
		return json;
	}

	@Override
	public void updateUrl(String tenancyId, JSONObject obj) throws Exception {
		String business_date1 = obj.optString("business_date");
		this.dao.updateIgnorCase(tenancyId, "boh_water_utility_record", obj);
		
		/**
		 * 操作日志表
		 */
		StringBuffer sb = new StringBuffer();
		sb.append(" select * from boh_water_utility_lastreading_record ss where ss.water_utilit_id = '"+obj.optString("water_utilit_id")+"' and recprd_type = 'pay' ORDER BY ss.business_date DESC LIMIT 1");
		List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
		if(null != list && list.size()>0){
			JSONObject json = list.get(0);
			String business_date2 = json.optString("business_date");
			int result = DateUtil.daysBetween(DateUtil.parseDate(business_date1), DateUtil.parseDate(business_date2));
			if(result<=0){
				obj.put("id", json.optInt("id"));
				obj.put("recprd_type", "pay");
				this.dao.updateIgnorCase(tenancyId, "boh_water_utility_lastreading_record", obj);
			}
		}else{
			obj.remove("id");
			obj.put("recprd_type", "pay");
			this.dao.insertIgnorCase(tenancyId, "boh_water_utility_lastreading_record", obj);
		}
		
	}

	@Override
	public void deleteNew(String tenancyId, JSONObject obj) throws Exception {
		int id = obj.optInt("id");
		int water_utilit_id = obj.optInt("water_utilit_id");
		StringBuffer sb = new StringBuffer();
		//查询出倒数第二条新纪录
		sb.append("select * from boh_water_utility_record  where water_utilit_id = '"+water_utilit_id+"' and recprd_type = 'pay' ORDER BY business_date desc LIMIT 1 OFFSET 1");
		List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
		if(null != list && list.size()>0){
			JSONObject json = list.get(0);
			sb.setLength(0);
			sb.append(" select * from boh_water_utility_lastreading_record where  water_utilit_id = '"+water_utilit_id+"' and recprd_type = 'pay'");
			List<JSONObject> recordlist = this.dao.query4Json(tenancyId, sb.toString());
			if(null != recordlist && recordlist.size()>0){
				JSONObject record = recordlist.get(0);
				json.put("id", record.optInt("id"));
				json.put("recprd_type", "pay");
				this.dao.updateIgnorCase(tenancyId, "boh_water_utility_lastreading_record", json);
			}
			
		}else{
			sb.setLength(0);
			sb.append(" delete  from boh_water_utility_lastreading_record where water_utilit_id = '"+water_utilit_id+"' and recprd_type = 'pay'");
			this.dao.execute(tenancyId, sb.toString());
		}
		
		
		this.dao.delete(tenancyId, "boh_water_utility_record", id);
		
	}

	@Override
	public void updateBeginningUrl(String tenancyId, JSONObject obj)
			throws Exception {
		StringBuffer sb = new StringBuffer();
		int type = obj.optInt("type");
		int id = obj.optInt("id");
		String beginning = obj.optString("beginning");
		String reading = obj.optString("reading");
		String water_utilit_id = obj.optString("water_utilit_id");
		String business_date = obj.optString("business_date");
		String remark = obj.optString("remark");
		obj.put("recprd_type", "pay");
		if(type == 0){
			sb.append("  update  boh_water_utility_record  set beginning = '"+beginning+"' ,remark = '"+remark+"',reading='"+reading+"' where id ='"+id+"' ");
			this.dao.execute(tenancyId, sb.toString());
			
			sb.setLength(0);
			sb.append(" select * from boh_water_utility_lastreading_record where recprd_type = 'pay'  and water_utilit_id = '"+water_utilit_id+"' and business_date = '"+business_date+"' ");
			List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
			if(null != list && list.size()>0){
				JSONObject json = list.get(0);
				json.put("beginning", beginning);
				json.put("reading", reading);
				json.put("recprd_type", "pay");
				this.dao.updateIgnorCase(tenancyId, "boh_water_utility_lastreading_record", json);
			}else{
				obj.remove("id");
				obj.remove("remark");
				this.dao.insertIgnorCase(tenancyId, "boh_water_utility_lastreading_record", obj);
			}
			obj.remove("id");
			obj.remove("remark");
			obj.put("recprd_type", "pay");
			this.dao.insertIgnorCase(tenancyId, "boh_water_utility_record", obj);
		}else{
			obj.remove("id");
			this.dao.insertIgnorCase(tenancyId, "boh_water_utility_record", obj);
			
			sb.setLength(0);
			sb.append(" select * from boh_water_utility_lastreading_record where water_utilit_id = '"+water_utilit_id+"' and recprd_type='pay'");
			List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
			if(null != list && list.size()>0){
				JSONObject json = list.get(0);
				json.put("beginning", beginning);
				json.put("reading", reading);
				json.put("money", 0);
				json.put("business_date", business_date);
				this.dao.updateIgnorCase(tenancyId, "boh_water_utility_lastreading_record", json);
			}else{
				obj.remove("id");
				obj.put("money", 0);
				this.dao.insertIgnorCase(tenancyId, "boh_water_utility_lastreading_record", obj);
			}
			obj.remove("id");
			obj.put("recprd_type", "pay");
			this.dao.insertIgnorCase(tenancyId, "boh_water_utility_record", obj);
		}
		
	}
	
}
