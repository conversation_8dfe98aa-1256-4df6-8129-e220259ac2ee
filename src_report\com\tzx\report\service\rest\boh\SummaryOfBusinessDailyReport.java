package com.tzx.report.service.rest.boh;
/**
 * Created by gj on 2019-05-30.
 */

import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.report.bo.boh.SummaryOfBusinessDaily;
import com.tzx.report.common.util.ConditionUtils;
import jxl.write.WriteException;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

/**
 *
 * 好享来-营业日报汇总表
 *
 */

@Controller("SummaryOfBusinessDailyReport")
@RequestMapping("/report/SummaryOfBusinessDailyReport")
public class SummaryOfBusinessDailyReport
{
    private static final Logger LOGGER = Logger.getLogger(SummaryOfBusinessDailyReport.class);

    @Resource(name = SummaryOfBusinessDaily.NAME)
    private SummaryOfBusinessDaily summaryOfBusinessDaily;

    @Resource
    ConditionUtils conditionUtils;



    @RequestMapping(value = "/getPayMentType")
    public void getPayMentType(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        InputStream in = null;
        HttpSession session = request.getSession();
        String result = "";
        try
        {
            JSONObject p = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                p.put(key, map.get(key)[0]);
            }

            if(p.optString("p_store_id").length()==0){
                p.element("p_store_id", session.getAttribute("user_organ_codes_group"));
            }

            result = summaryOfBusinessDaily.getPayMentType((String) session.getAttribute("tenentid"), p).toString();
        }
        catch (Exception e)
        {
            result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
            e.printStackTrace();
        }
        finally
        {
            try
            {
                if (in != null)
                {
                    in.close();
                }
            }
            catch (Exception e)
            {
            }

            try
            {
                out = response.getWriter();

                out.print(result);
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }

    }



    @RequestMapping(value = "/find")
    public void find(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        InputStream in = null;
        HttpSession session = request.getSession();
        String result = "";
        try
        {
            JSONObject p = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                p.put(key, map.get(key)[0]);
            }

            if(p.optString("p_store_id").length()==0){
                p.element("p_store_id", session.getAttribute("user_organ_codes_group"));
            }

            result = summaryOfBusinessDaily.find((String) session.getAttribute("tenentid"), p).toString();
        }
        catch (Exception e)
        {
            result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
            LOGGER.error("summaryOfBusinessDaily.find方法异常>>>>>" + ExceptionMessage.getExceptionMessage(e));
        }
        finally
        {
            try
            {
                if (in != null)
                {
                    in.close();
                }
            }
            catch (Exception e)
            {
            }

            try
            {
                out = response.getWriter();

                out.print(result);
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }

    }
}
