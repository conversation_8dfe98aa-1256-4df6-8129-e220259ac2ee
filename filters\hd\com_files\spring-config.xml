<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans 
	   http://www.springframework.org/schema/beans/spring-beans-4.0.xsd 
       http://www.springframework.org/schema/cache 
       http://www.springframework.org/schema/cache/spring-cache.xsd 
       http://www.springframework.org/schema/tx 
       http://www.springframework.org/schema/tx/spring-tx-4.0.xsd 
       http://www.springframework.org/schema/aop 
       http://www.springframework.org/schema/aop/spring-aop-4.0.xsd 
       http://www.springframework.org/schema/context 
       http://www.springframework.org/schema/context/spring-context-4.0.xsd 
       http://www.springframework.org/schema/task 
       http://www.springframework.org/schema/task/spring-task-4.0.xsd"
       default-autowire="byName">
    <!-- 加载配置文件 -->
    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath*:redis.properties</value>
                <value>classpath*:redis-cluster.properties</value>
                <value>classpath*:mongo.properties</value>
                <value>classpath*:messageConfig.properties</value>
                <value>classpath:/waimai/code/config.properties</value>
            </list>
        </property>
    </bean>

    <!-- 使用annotation 自动注册bean,并检查@Required,@Autowired的属性已被注入 -->
    <context:component-scan base-package="com"/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    
    <!-- 自动创建代理织入切面  默认使用jdk动态代理织入-->
    <aop:aspectj-autoproxy/> 

    <!-- 统一处理json问题 -->
    <context:annotation-config/>
    <bean class="org.springframework.web.servlet.mvc.annotation.DefaultAnnotationHandlerMapping"/>
    <bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
        <property name="messageConverters">
            <ref bean="jacksonMessageConverter"/>
        </property>
    </bean>
    <bean id="jacksonMessageConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
        <property name="supportedMediaTypes">
            <value>application/json;charset=UTF-8</value>
        </property>
        <property name="objectMapper">
            <bean class="org.springframework.http.converter.json.Jackson2ObjectMapperFactoryBean">
                <property name="failOnEmptyBeans" value="false"/>
                <property name="serializers">
                    <array>
                        <bean class="com.tzx.framework.common.util.JSONObjectSerializer"/>
                    </array>
                </property>
            </bean>
        </property>
    </bean>

    <bean id="viewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
        <property name="prefix" value="/"></property>
    </bean>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver"/>

    <!-- 导入配置文件 -->
    <import resource="spring/spring-service.xml"/>
    
    <import resource="spring/spring-redis-cluster.xml"/>
    
    <!--
    <import resource="spring/spring-redis.xml"/>
	-->
    <import resource="spring/spring-mongo.xml"/>
    <import resource="spring/spring-dao.xml"/>
    
	<!--
	<import resource="spring/spring-task.xml"/>
	<import resource="spring/spring-pos.xml"/>
	-->
    
    
    <import resource="log/jmsConnectionFactoryLogStoreListener.xml"/>
    
    <!-- 作为消费端，如果不想作为mq消费端，请注释掉下边的配置文件引入 -->
    <!-- <import resource="spring/jmsConnectionFactoryPosStoreListener.xml"/> -->
</beans>