package com.tzx.report.bo.boh;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */
public interface ConcessionAnalysisDetailsService
{

    String NAME = "com.tzx.report.bo.imp.boh.ConcessionAnalysisDetailsServiceImp";

    JSONObject find(String tenancyID, JSONObject condition) throws Exception;

    List<JSONObject> getConcessionTitle(String tenancyID, JSONObject condition) throws Exception;
}
