package com.tzx.cc.datasync.common.util;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;

import com.tzx.cc.common.constant.util.CcPartitionUtils;
import com.tzx.cc.datasync.bo.dto.newpos.BtYddDetailSendDto;
import com.tzx.cc.datasync.bo.dto.newpos.BtYddDetailTcmxDto;
import com.tzx.cc.datasync.bo.dto.newpos.BtYddSendDto;
import com.tzx.cc.datasync.bo.dto.newpos.CallCenterConstant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import org.apache.log4j.Logger;

public class NewPosOrderUtil {
	private static final Logger logger = Logger.getLogger(NewPosOrderUtil.class);
	
	private static GenericDaoImpl dao = (GenericDaoImpl) SpringConext
			.getBean("genericDaoImpl");

	private static double meal_costs = 0.0; // 配送费

	/**
	 * 转换CC订单数据
	 * 
	 * @param ccOrder
	 * 
	 */
	public static JSONObject ccOrder2NewPos(JSONObject ccOrder)
			throws Exception {
		StringBuilder sb = new StringBuilder();
		;
		try {
			double platform_side_discount_fee = 0.0; // 平台方承担的优惠总金额
			BtYddSendDto main = new BtYddSendDto();
			List<BtYddDetailSendDto> items = new ArrayList<BtYddDetailSendDto>();
			List<BtYddDetailTcmxDto> tcmx_items = new ArrayList<BtYddDetailTcmxDto>();
			List<BtYddSendDto> resList = new ArrayList<BtYddSendDto>();

			// 订单信息(cc_order_list)
			JSONObject order = ccOrder.optJSONObject("order_list");
			main.setMerchant_id(order.optString("tenancy_id"));// 商户ID
			main.setStoreid(order.optString("store_id"));// 商户id
			main.setShopid(order.optString("store_id"));// 商户id
			main.setOrder_id(order.optString("order_code"));// 订单号
			main.setYl5(order.optString("third_order_code"));// 第三方订单号
			// main.setYl4(CallCenterConstant.CALLCENTER);//数据来源
			main.setYl4(order.optString("chanel"));// 数据来源
			main.setRecieptdept(order.optString("invoice_title"));
			main.setYdrs(String.valueOf(order.optInt("deinner_number")));
			main.setCommission_amount(order.optString("commission_amount"));
			main.setShop_real_amount(order.optString("shop_real_amount"));
			// order.optString("third_order_code");// 百度订单编号
			// order.optString("send_immediately");// 是否立即送餐
			// order.optString("send_time");// 期望送达时间???
			// order.optString("meal_costs");// 配送费
			// order.optString("discount_amount");// 优惠总金额
			// order.optString("shop_fee");// 商户实收总价
			JSONArray discount_info = JSONArray.fromObject(ccOrder
					.optString("cc_order_discount"));
			if(discount_info.size()>0){
				if (order.optString("chanel").equalsIgnoreCase("BD06")||order.optString("chanel").equalsIgnoreCase("MT08")) {
					// 平台方承担的金额
					for (int i = 0; i < discount_info.size(); i++) {
							JSONObject dc = discount_info.getJSONObject(i);
							platform_side_discount_fee += Double.valueOf(dc.optDouble(
									"baidu_rate", 0.0));
					}
				} else if(order.optString("chanel").equalsIgnoreCase("EL09")){
						JSONObject dc = discount_info.getJSONObject(0);
						platform_side_discount_fee = Double.valueOf(dc.optDouble("shop_rate", 0.0));
				}
			}
			if (order.optString("chanel").equalsIgnoreCase("WX02")) {
				main.setTotalprice(String.valueOf(order
						.optDouble("total_money")));
				
			} else if(order.optString("chanel").equalsIgnoreCase("BD06")||order.optString("chanel").equalsIgnoreCase("MT08")){
				Double money = Scm.psub(Scm.padd(order.optDouble("actual_pay"),
						platform_side_discount_fee), order.optDouble(
						"meal_costs", 0));
				main.setTotalprice(money.toString());// 用户实付+平台方优惠金额-配送费
			}else {
//				Double money = Scm.psub(Scm.psub(order.optDouble("total_money"),order.optDouble("meal_costs",0.0)),platform_side_discount_fee);
				Double money = Scm.padd(order.optDouble("shop_real_amount",0.0),order.optDouble("commission_amount",0.0));
				main.setTotalprice(money.toString());// 用户实付+平台方优惠金额-配送费
			}

			main.setYl3(order.optString("meal_costs"));// 配送费
			meal_costs = order.optDouble("meal_costs");
			main.setYl2(order.optString("discount_amount"));// 优惠金额
			/* 20180309 对于微信点餐优惠券作为支付存在；  
			 * 1、订单总金额-总会员价 转为优惠金额体现
			 * 2、对于存在抹零金额的订单 重新计算优惠，最终的优惠 包含抹零金额（）
			 * 
			 * */
			if (order.optString("chanel").equalsIgnoreCase("WX02")) {
				Double realDiscount = 0.0;
				double malingAmount = order.optDouble("maling_amount", 0.0);
				//订单采用会员价(无其他优惠)
				if(order.optDouble("vip_price", 0.0)!= order.optDouble("total_money", 0.0) 
						&& order.optDouble("vip_price", 0.0)>0 
						&& order.optDouble("discount_amount", 0.0)==0){
					//会员优惠
					realDiscount = Scm.psub(order.optDouble("total_money", 0.0), order.optDouble("vip_price", 0.0));
				}else{
					//满减满折优惠
					realDiscount = order.optDouble("discount_amount", 0.0);
				}
				
				//存在抹零金额,则从优惠金额中减去
				if(malingAmount != 0){
					realDiscount = Scm.psub(realDiscount,malingAmount);
				}
				main.setYl2(realDiscount.toString());
				logger.info("【微信点餐"+ order.optString("order_code") +"】提取优惠：" + main.getYl2() 
							+ ", 订单总额：" + order.optDouble("total_money", 0.0) + ", 抹零金额："+order.optDouble("maling_amount", 0.0));
			}
			
			
			main.setYl1(order.optString("total_money"));// 订单总额
			// main.setYl1(order.optString("meal_costs"));//
			// order.optString("actual_pay");// 用户实付总价
			// order.optString("is_online_payment");// 支付类型
			// 0代表货到付款 1代表线上支付
			if (order.optString("is_online_payment").equalsIgnoreCase("0")) {
				main.setJk("货到付款");
				main.setPaychannel("0");// 支付方式
			} else if (order.optString("is_online_payment").equalsIgnoreCase(
					"1")) {
				main.setJk("在线支付");
				main.setPaychannel("1");// 支付方式
			}

			main.setPaystatus(order.optString("payment_state"));// 支付状态
			main.setInvoice(order.optString("need_invoice"));// 是否需要发票
			main.setInvoice(order.optString("order_state"));// 是否需要发票
			main.setTableinfo_id(order.optString("table_code"));
			main.setWarning("");//
			main.setCancel_type("");// 取消原因
			// order.optString("invoice_title");// 发票抬头

			main.setShippingmethod(order.optString("delivery_party"));// 物流
			main.setQdrbh("");// 签单人编号
			main.setShrbh("");// 审核人编号
			main.setQdrbh1("");// 取单人编号
			main.setDdzt("0");// 订单状态
			main.setSyrbh("");// 收银人编号
			main.setPdrbh("");// 派单人编号
			main.setSbsqrbh("");// 失败申请人编号
			main.setXsqxrbh("");// 线上取消人编号
			main.setQdrmc("");// 签单人名称
			main.setShrmc("");// 审核人名称
			main.setQdrmc1("");// 取单人名称
			main.setSyrmc("");// 收银人名称
			main.setPdrmc("");// 派单人名称
			main.setSbsqrmc("");// 失败申请人名称
			main.setXsqxrmc("");// 线上取消人名称
			main.setCreateUserName(order.optString("entry_name"));// 下单人
			main.setQdsj("");// 签单时间
			main.setYshsj("");// 已审核时间
			main.setQdsj1(order.optString("take_time"));// 取单时间
			main.setSysj("");// 收银时间
			main.setPdsj(order.optString("dispatch_time"));// 派单时间
			main.setWcsj(order.optString("finish_time"));// 完成（成功/失败）时间
			main.setSbsqsj("");// 失败申请时间
			main.setXsqxsj("");// 线上取消时间
			main.setBlrq("");// 保留日期
			/*if (order.optInt("send_immediately") == 1) {
				main.setYdrq(formatDate(order.optString("single_time")));// 获取的下单时间
			} else {
				main.setYdrq(formatDate(order.optString("send_time")));// 获取的送达时间
			}*/
			main.setYdrq(formatDate(order.optString("single_time")));// 获取的下单时间
			// main.setDdsj(DateUtil.format(new
			// Timestamp(System.currentTimeMillis())));//
			main.setDdsj(order.optString("single_time"));// 下单时间
			main.setBeforeOrderType(CallCenterConstant.OUTSIDE_ORDER);
			// main.setBeforeOrderSource(CallCenterConstant.APP);
			main.setBeforeOrderSource(CallCenterConstant.CALLCENTER);
			main.setKhtmtz(order.optString("channel_name"));
			main.setKhjlbh(order.optString("chanel_serial_number"));// 每天的流水号分渠道
			main.setKhjlmc(order.optString("serial_number"));// 每天的流水号不分渠道
			main.setMemo(order.optString("send_time"));// 送达时间
			main.setKwxh(StringEscapeUtils.escapeSql(order.optString("remark")));// 订单备注客户口味喜好
			// order.optString("single_time");// 订单创建时间
			// order.optString("consigner");// 顾客姓名
			main.setMobile(order.optString("consigner_phone"));// 顾客电话
			main.setContact(StringEscapeUtils.escapeSql(order.optString("consigner")));// 订餐人
			main.setPeople("");
			// order.optString("order_phone");// 订餐电话
			// order.optString("sex");// 顾客性别
			// order.optString("chanel");// 渠道
			main.setMember_address1(StringEscapeUtils.escapeSql(order.optString("address")));// 送餐地址

			// order.optString("longitude"); // 送餐地址百度经度
			// order.optString("latitude");// 送餐地址百度纬度
			// order.optString("order_state"); // 订单状态
			// order.optString("order_type");// 订单类型
			// order.optString("district_id");// 商圈ID
			// order.optString("meals_id");// 起送价ID
			// order.optString("service_id");// 配送费ID

			
			String tmpSQL="";
			
			// 订单菜品信息
			JSONArray products = ccOrder.optJSONArray("order_item");
			JSONArray order_item_taste_arr = ccOrder.optJSONArray("order_item_taste");
			for (Object o : products) {
				JSONObject product = (JSONObject) o;
				// 查询某个订单明细的套餐明细
				String tcmx_sql = "SELECT b.*,c.item_code,c.fake_id from cc_order_item_details b  left join hq_item_info c on c.id=b.item_id  where  b.order_code= '"
						+ order.optString("order_code")
						+ "' and  group_index="
						+ product.optInt("group_index") + "";
				
				tmpSQL=CcPartitionUtils.makeSQL(order.optString("tenancy_id"),false,"b",tcmx_sql, order.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
				
				List<JSONObject> order_item_details_list = dao.query4Json(
						order.optString("tenancy_id"), tmpSQL);
				// 查询某个订单明细的套餐明细 是项目组的数量
				String tcmx_xmzsl_sql = "SELECT b.*, C .item_code,C .fake_id,a.price as price_ys FROM cc_order_item_details b LEFT JOIN cc_order_list d on d.order_code=b.order_code LEFT JOIN organ e on e.id=d.store_id LEFT JOIN hq_item_info C ON C . ID = b.item_id  LEFT JOIN hq_item_pricesystem a on a.item_unit_id=b.item_id and a.price_system=cast(e.price_system as int) AND A .chanel = 'MD01' where b.order_code= '"
						+ order.optString("order_code")
						+ "' and  b.group_index="
						+ product.optInt("group_index") + " and b.is_itemgroup ='Y'";
				
				tmpSQL=CcPartitionUtils.makeSQL(order.optString("tenancy_id"),false,"b",tcmx_xmzsl_sql, order.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
				tmpSQL=CcPartitionUtils.makeSQL(order.optString("tenancy_id"),false,"d",tmpSQL, order.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
				
				List<JSONObject> tcmx_xmzs_list = dao.query4Json(
						order.optString("tenancy_id"), tmpSQL);
				Map<String, String> item_unit_price_map = new HashMap<String, String>();
				// 获取项目组菜品原始金额和 选择项目里每个菜品的原始金额
				double tcmx_xmzs_zje = 0;
				if (tcmx_xmzs_list.size() > 0) {
					for (JSONObject tcmx_xmzs_obj : tcmx_xmzs_list) {
						tcmx_xmzs_zje += tcmx_xmzs_obj.optDouble("price_ys",0.0);
						item_unit_price_map.put(tcmx_xmzs_obj
								.optString("item_id"), String
								.valueOf(tcmx_xmzs_obj.optDouble("price_ys")));
					}
				}

				if (order_item_details_list.size() > 0) {
					for (Object o_mx : order_item_details_list) {
						double jsdzje = 0;
						JSONObject product_mx = (JSONObject) o_mx;
						String iitem_id = "0";
						iitem_id = product.optString("item_id");// 菜品ID
						//根据订单套餐明细表 查询hq_item_combo_details表里的原始价格
						String hq_item_combo_details_sql = "select * from hq_item_combo_details where iitem_id="
								+ iitem_id
								+ " and details_id="
								+ product_mx.optString("item_group_id") + "";
						List<JSONObject> hq_item_combo_details_list = dao
								.query4Json(order.optString("tenancy_id"),
										hq_item_combo_details_sql);
						JSONObject is_itemgroup_n_obj = hq_item_combo_details_list
								.get(0);
						int is_itemgroup_count = 0;
						//判断订单套餐明细是否是项目组
						if (product_mx.optString("is_itemgroup").equals("Y")) {
							is_itemgroup_count++;
							Double item_group_standardprice = is_itemgroup_n_obj
									.optDouble("standardprice");
							if (is_itemgroup_count == tcmx_xmzs_list.size()) {
								//项目组最后一个菜品价格等于项目组在套餐中实际配比的价格-之前项目组每个菜品分摊的钱的合计
								BtYddDetailTcmxDto dto = new BtYddDetailTcmxDto();
								if (product_mx.containsKey("order_code")) {
									dto.setOrder_id(product_mx
											.optString("order_code"));// 订单编号
								} else {
									dto.setOrder_id(order
											.optString("order_code"));// 订单编号
								}
								//如果项目组里边就选了一个菜品 价格=项目组在套餐中实际配比的价格/数量
								if(is_itemgroup_count==1){
									// 价格
									dto.setPrice(String.valueOf(Scm.pdiv(
											item_group_standardprice, product_mx
											.optDouble("number"))));// 价格
									dto.setTotalprice(item_group_standardprice);// 实结金额
									dto.setCmje(item_group_standardprice);// 实结金额
									dto.setYl3(item_group_standardprice);// 原菜目小计
								}else{
									// 价格
									dto.setPrice(String.valueOf(Scm.psub(
											item_group_standardprice, jsdzje)));// 价格
									dto.setTotalprice(Scm.pmui(product_mx
											.optDouble("number"), Scm.psub(
											item_group_standardprice, jsdzje)));// 实结金额
									dto.setCmje(Scm.pmui(product_mx
											.optDouble("number"), Scm.psub(
											item_group_standardprice, jsdzje)));// 实结金额
									dto.setYl3(Scm.pmui(product_mx
											.optDouble("number"), Scm.psub(
											item_group_standardprice, jsdzje)));// 原菜目小计
								}
							

								dto.setItemid(product_mx.optString("item_code"));// 菜品编号
								if (product_mx.containsKey("fake_id")
										&& !product_mx.optString("fake_id")
												.equals("null")) {
									dto.setXmid(product_mx.optString("fake_id"));// 菜品ID
								} else {
									dto.setXmid(product_mx.optString("item_id"));// 菜品ID
								}
								dto.setItemname(product_mx
										.optString("item_name"));// 菜品名称
								dto.setItemcount(product_mx.optString("number"));// 数量

								dto.setYl2(Double.isNaN(product_mx
										.optDouble("discount_amount")) ? 0.0
										: product_mx
												.optDouble("discount_amount"));// 优惠金额
								dto.setYl4(Double.isNaN(product_mx
										.optDouble("costs")) ? 0.0 : product_mx
										.optDouble("costs"));// 配送费
								dto.setYl5(product_mx.optString("group_index"));// 微信店内点餐的点菜顺序

								// product.optString("unit_id");// 规格

								dto.setSkuinfo("");

								dto.setXmsx("明细");
								dto.setTcid("0");
								dto.setTcbh("");
								dto.setTcdch("0");
								tcmx_items.add(dto);
							} else {
								//项目组前几个菜品按权重分摊
								BtYddDetailTcmxDto dto = new BtYddDetailTcmxDto();
								if (product_mx.containsKey("order_code")) {
									dto.setOrder_id(product_mx
											.optString("order_code"));// 订单编号
								} else {
									dto.setOrder_id(order
											.optString("order_code"));// 订单编号
								}
								// 价格
								dto.setPrice(String.valueOf(Scm.pmui(
										Double.valueOf(item_unit_price_map
												.get(product_mx
														.optString("item_id"))),
										Scm.pdiv(item_group_standardprice,
												tcmx_xmzs_zje))));// 价格
								jsdzje += Scm
										.pmui(Double
												.valueOf(item_unit_price_map.get(product_mx
														.optString("item_id"))),
												Scm.pdiv(
														item_group_standardprice,
														tcmx_xmzs_zje));
								dto.setTotalprice(Scm.pmui(
										product_mx.optDouble("number"),
										Scm.pmui(
												Double.valueOf(item_unit_price_map.get(product_mx
														.optString("item_id"))),
												Scm.pdiv(
														item_group_standardprice,
														tcmx_xmzs_zje))));// 实结金额
								dto.setCmje(Scm.pmui(
										product_mx.optDouble("number"),
										Scm.pmui(
												Double.valueOf(item_unit_price_map.get(product_mx
														.optString("item_id"))),
												Scm.pdiv(
														item_group_standardprice,
														tcmx_xmzs_zje))));// 实结金额
								dto.setYl3(Scm.pmui(
										product_mx.optDouble("number"),
										Scm.pmui(
												Double.valueOf(item_unit_price_map.get(product_mx
														.optString("item_id"))),
												Scm.pdiv(
														item_group_standardprice,
														tcmx_xmzs_zje))));// 原菜目小计

								dto.setItemid(product_mx.optString("item_code"));// 菜品编号
								if (product_mx.containsKey("fake_id")
										&& !product_mx.optString("fake_id")
												.equals("null")) {
									dto.setXmid(product_mx.optString("fake_id"));// 菜品ID
								} else {
									dto.setXmid(product_mx.optString("item_id"));// 菜品ID
								}
								dto.setItemname(product_mx
										.optString("item_name"));// 菜品名称
								dto.setItemcount(product_mx.optString("number"));// 数量

								dto.setYl2(Double.isNaN(product_mx
										.optDouble("discount_amount")) ? 0.0
										: product_mx
												.optDouble("discount_amount"));// 优惠金额
								dto.setYl4(Double.isNaN(product_mx
										.optDouble("costs")) ? 0.0 : product_mx
										.optDouble("costs"));// 配送费
								dto.setYl5(product_mx.optString("group_index"));// 微信店内点餐的点菜顺序

								// product.optString("unit_id");// 规格

								dto.setSkuinfo("");
								dto.setXmsx("明细");
								dto.setTcid("0");
								dto.setTcbh("");
								dto.setTcdch("0");

								tcmx_items.add(dto);

							}
						} else {
							//套餐下如果是单品直接查价格
							BtYddDetailTcmxDto dto = new BtYddDetailTcmxDto();
							if (product_mx.containsKey("order_code")) {
								dto.setOrder_id(product_mx
										.optString("order_code"));// 订单编号
							} else {
								dto.setOrder_id(order.optString("order_code"));// 订单编号
							}
							// 价格
							dto.setPrice(String.valueOf(Scm.pdiv(is_itemgroup_n_obj
									.optDouble("standardprice"), product_mx
									.optDouble("number"))));// 价格
							dto.setTotalprice(is_itemgroup_n_obj
									.optDouble("standardprice"));// 实结金额
							dto.setCmje(is_itemgroup_n_obj
									.optDouble("standardprice"));// 实结金额
							dto.setYl3(is_itemgroup_n_obj
									.optDouble("standardprice"));// 原菜目小计

							dto.setItemid(product_mx.optString("item_code"));// 菜品编号
							if (product_mx.containsKey("fake_id")
									&& !product_mx.optString("fake_id").equals(
											"null")) {
								dto.setXmid(product_mx.optString("fake_id"));// 菜品ID
							} else {
								dto.setXmid(product_mx.optString("item_id"));// 菜品ID
							}
							dto.setItemname(product_mx.optString("item_name"));// 菜品名称
							dto.setItemcount(product_mx.optString("number"));// 数量

							dto.setYl2(Double.isNaN(product_mx
									.optDouble("discount_amount")) ? 0.0
									: product_mx.optDouble("discount_amount"));// 优惠金额
							dto.setYl4(Double.isNaN(product_mx
									.optDouble("costs")) ? 0.0 : product_mx
									.optDouble("costs"));// 配送费
							dto.setYl5(product_mx.optString("group_index"));// 微信店内点餐的点菜顺序

							// product.optString("unit_id");// 规格

							dto.setSkuinfo("");
							dto.setXmsx("明细");
							dto.setTcid("0");
							dto.setTcbh("");
							dto.setTcdch("0");

							tcmx_items.add(dto);

						}
					}
				}
				/*
				 * //均摊逻辑 if (order_item_details_list.size() > 0) { //每个套餐明细占的钱
				 * 不除以数量 Double
				 * mgmxjg_price=Scm.pdiv(product.optDouble("product_fee"),
				 * Double.valueOf(order_item_details_list.size()));
				 * //order_item表里的合计 Double
				 * order_item_product_fee=product.optDouble("product_fee"); int
				 * for_count=0; for (Object o_mx : order_item_details_list) {
				 * for_count++; JSONObject product_mx = (JSONObject) o_mx;
				 * //套餐明细数量不为1的时候进行循环 if(product_mx.optInt("number")>1){
				 * //每个套餐明细占的钱 不除以数量 Double
				 * mgmxjg_cf_price=Scm.pdiv(mgmxjg_price,
				 * Double.valueOf(product_mx.optInt("number")));
				 * //order_item表里的合计 // Double
				 * order_item_cf_product_fee=product.optDouble("product_fee");
				 * int for_count_tcmx=0; for(int
				 * i=0;i<product_mx.optInt("number");i++){ for_count_tcmx++;
				 * 
				 * BtYddDetailTcmxDto dto = new BtYddDetailTcmxDto(); if
				 * (product_mx.containsKey("order_code")) {
				 * dto.setOrder_id(product_mx.optString("order_code"));// 订单编号 }
				 * else { dto.setOrder_id(order.optString("order_code"));// 订单编号
				 * } // 价格 if(for_count_tcmx==product_mx.optInt("number")+1){
				 * dto.setPrice(String.valueOf(Scm.psub(mgmxjg_price,
				 * Scm.pmui(mgmxjg_cf_price,
				 * Double.valueOf(for_count_tcmx-1)))));// 价格
				 * dto.setTotalprice(Scm.psub(mgmxjg_price,
				 * Scm.pmui(mgmxjg_cf_price,
				 * Double.valueOf(for_count_tcmx-1))));// 实结金额
				 * dto.setCmje(Scm.psub(mgmxjg_price, Scm.pmui(mgmxjg_cf_price,
				 * Double.valueOf(for_count_tcmx-1))));// 实结金额
				 * dto.setYl3(Scm.psub(mgmxjg_price, Scm.pmui(mgmxjg_cf_price,
				 * Double.valueOf(for_count_tcmx-1))));// 原菜目小计 }else{
				 * dto.setPrice(String.valueOf(mgmxjg_cf_price));// 价格
				 * dto.setTotalprice(mgmxjg_cf_price);// 实结金额
				 * dto.setCmje(mgmxjg_cf_price);// 实结金额
				 * dto.setYl3(mgmxjg_cf_price);// 原菜目小计 }
				 * 
				 * dto.setItemid(product_mx.optString("item_code"));// 菜品编号
				 * if(product_mx
				 * .containsKey("fake_id")&&!product_mx.optString("fake_id"
				 * ).equals("null")){
				 * dto.setXmid(product_mx.optString("fake_id"));// 菜品ID }else{
				 * dto.setXmid(product_mx.optString("item_id"));// 菜品ID }
				 * dto.setItemname(product_mx.optString("item_name"));// 菜品名称
				 * dto.setItemcount("1");// 数量
				 * 
				 * 
				 * dto.setYl2(Double.isNaN(product_mx.optDouble("discount_amount"
				 * )) ? 0.0: product_mx.optDouble("discount_amount"));// 优惠金额
				 * dto.setYl4(Double.isNaN(product_mx.optDouble("costs")) ? 0.0:
				 * product_mx.optDouble("costs"));// 配送费
				 * dto.setYl5(product_mx.optString("group_index"));//
				 * 微信店内点餐的点菜顺序
				 * 
				 * // product.optString("unit_id");// 规格
				 * 
				 * dto.setSkuinfo("");
				 * if(product_mx.optString("is_combo").equalsIgnoreCase("y")){
				 * dto.setXmsx("套菜"); }else{ dto.setXmsx("单品"); }
				 * dto.setTcid("0"); dto.setTcbh(""); dto.setTcdch("0");
				 * 
				 * tcmx_items.add(dto);
				 * 
				 * 
				 * } }else{ BtYddDetailTcmxDto dto = new BtYddDetailTcmxDto();
				 * if (product_mx.containsKey("order_code")) {
				 * dto.setOrder_id(product_mx.optString("order_code"));// 订单编号 }
				 * else { dto.setOrder_id(order.optString("order_code"));// 订单编号
				 * } // 价格 if(for_count==order_item_details_list.size()){
				 * dto.setPrice(String.valueOf(Scm.psub(order_item_product_fee,
				 * Scm.pmui(mgmxjg_price, Double.valueOf(for_count-1)))));// 价格
				 * dto.setTotalprice(Scm.psub(order_item_product_fee,
				 * Scm.pmui(mgmxjg_price, Double.valueOf(for_count-1))));// 实结金额
				 * dto.setCmje(Scm.psub(order_item_product_fee,
				 * Scm.pmui(mgmxjg_price, Double.valueOf(for_count-1))));// 实结金额
				 * dto.setYl3(Scm.psub(order_item_product_fee,
				 * Scm.pmui(mgmxjg_price, Double.valueOf(for_count-1))));//
				 * 原菜目小计 }else{ dto.setPrice(String.valueOf(mgmxjg_price));// 价格
				 * dto.setTotalprice(mgmxjg_price);// 实结金额
				 * dto.setCmje(mgmxjg_price);// 实结金额 dto.setYl3(mgmxjg_price);//
				 * 原菜目小计 }
				 * 
				 * dto.setItemid(product_mx.optString("item_code"));// 菜品编号
				 * if(product_mx
				 * .containsKey("fake_id")&&!product_mx.optString("fake_id"
				 * ).equals("null")){
				 * dto.setXmid(product_mx.optString("fake_id"));// 菜品ID }else{
				 * dto.setXmid(product_mx.optString("item_id"));// 菜品ID }
				 * dto.setItemname(product_mx.optString("item_name"));// 菜品名称
				 * dto.setItemcount("1");// 数量
				 * 
				 * 
				 * dto.setYl2(Double.isNaN(product_mx.optDouble("discount_amount"
				 * )) ? 0.0: product_mx.optDouble("discount_amount"));// 优惠金额
				 * dto.setYl4(Double.isNaN(product_mx.optDouble("costs")) ? 0.0:
				 * product_mx.optDouble("costs"));// 配送费
				 * dto.setYl5(product_mx.optString("group_index"));//
				 * 微信店内点餐的点菜顺序
				 * 
				 * // product.optString("unit_id");// 规格
				 * 
				 * dto.setSkuinfo("");
				 * if(product_mx.optString("is_combo").equalsIgnoreCase("y")){
				 * dto.setXmsx("套菜"); }else{ dto.setXmsx("单品"); }
				 * dto.setTcid("0"); dto.setTcbh(""); dto.setTcdch("0");
				 * 
				 * tcmx_items.add(dto);
				 * 
				 * }}
				 * 
				 * }
				 */
				if (tcmx_items.size() > 0) {
					main.setItems_tcmx(tcmx_items);
				}
				BtYddDetailSendDto dto = new BtYddDetailSendDto();
				if (product.containsKey("order_code")) {
					dto.setOrder_id(product.optString("order_code"));// 订单编号
				} else {
					dto.setOrder_id(order.optString("order_code"));// 订单编号
				}

				// product.optString("tenancy_id");// 商户ID
				// product.optString("group_index");// 明细索引
				// dto.setPrice(String.valueOf(Scm.pdiv(new_product_fee,product.optDouble("number"))));//
				// 价格
				dto.setPrice(product.optString("price"));// 价格
				dto.setItemid(product.optString("item_code"));// 菜品编号
				if (product.containsKey("fake_id")
						&& !product.optString("fake_id").equals("null")) {
					dto.setXmid(product.optString("fake_id"));// 菜品ID
				} else {
					dto.setXmid(product.optString("item_id"));// 菜品ID
				}
				dto.setItemname(product.optString("item_name"));// 菜品名称
				dto.setItemcount(product.optString("number"));// 数量
				String kwxh=" ";
				if(order_item_taste_arr!=null && order_item_taste_arr.size()>0){
					String query_cc_order_item_taste_sql="SELECT item_remark from cc_order_item_taste where order_code='"+product.optString("order_code")+"' and item_id='"+product.optInt("item_id")+"' and group_index="+product.optInt("group_index")+" and type='KW02'";
					List<JSONObject> query_cc_order_item_taste_list=dao.query4Json(product.optString("tenancy_id"), query_cc_order_item_taste_sql);
					if(query_cc_order_item_taste_list.size()>0){
						for(JSONObject cc_order_item_taste_obj:query_cc_order_item_taste_list){
							kwxh +=cc_order_item_taste_obj.optString("item_remark")+" ";
						}
					}
				}
				if(!StringUtils.isEmpty(order.optString("remark"))){
					kwxh += order.optString("remark");
				}
				dto.setKwbh(kwxh);
				// dto.setTotalprice(new_product_fee);// 商品总价小计
				if (product.containsKey("share_amount")
						&& product.optString("share_amount") != null
						&& ("BD06".equals(order.optString("chanel")) || "MT08"
								.equals(order.optString("chanel"))|| "EL09"
								.equals(order.optString("chanel")))) {
					dto.setTotalprice(Double.isNaN(product
							.optDouble("share_amount")) ? 0.0 : product
							.optDouble("share_amount"));// 实结金额
					dto.setCmje(Double.isNaN(product.optDouble("share_amount")) ? 0.0
							: product.optDouble("share_amount"));// 实结金额
				} else {
					dto.setTotalprice(Scm.pmui(product.optDouble("price"),
							product.optDouble("number")));// 实结金额
					dto.setCmje(Scm.pmui(product.optDouble("price"),
							product.optDouble("number")));// 实结金额
				}

				dto.setYl2(Double.isNaN(product.optDouble("discount_amount")) ? 0.0
						: product.optDouble("discount_amount"));// 优惠金额
				dto.setYl4(Double.isNaN(product.optDouble("costs")) ? 0.0
						: product.optDouble("costs"));// 配送费
				dto.setYl5(product.optString("group_index"));// 微信店内点餐的点菜顺序
				if (product.containsKey("product_fee")) {
					dto.setYl3(product.optDouble("product_fee"));// 原菜目小计
				} else {
					dto.setYl3(Scm.pmui(product.optDouble("price"),
							product.optDouble("number")));// 原菜目小计
				}

				// product.optString("unit_id");// 规格

				dto.setSkuinfo("");
				if (product.optString("is_combo").equalsIgnoreCase("y")) {
					dto.setXmsx("套菜");
				} else {
					dto.setXmsx("单品");
				}
				dto.setTcid("0");
				dto.setTcbh("");
				dto.setTcdch("0");

				items.add(dto);

			}

			if (items.size() > 0) {
				main.setItems(items);
			}

			// 订单支付信息
			JSONArray saas_payment_arr = ccOrder
					.optJSONArray("order_repayment");
			JSONArray new_pose_payment_arr = new JSONArray();
			if (saas_payment_arr.size() > 0) {
				for (Object o : saas_payment_arr) {
					JSONObject saas_payment = (JSONObject) o;
					JSONObject new_pose_payment = new JSONObject();
					new_pose_payment.put("paychannel",
							saas_payment.optString("payment_code"));
					new_pose_payment.put("payname",
							saas_payment.optString("pay_name"));
					new_pose_payment.put("pay_no",
							saas_payment.optString("payment_id"));
					new_pose_payment.put("vcardid", "");
					new_pose_payment.put("vtele", "");
					// new_pose_payment.put("paycount",Double.isNaN(saas_payment.optDouble("pay_money"))
					// ? 0.0: saas_payment.optDouble("pay_money")-
					// (Double.isNaN(meal_costs) ? 0.0: meal_costs)+
					// (Double.isNaN(platform_side_discount_fee) ? 0.0:
					// platform_side_discount_fee));
					if (saas_payment_arr.size() >= 1&&(order.optString("chanel").equalsIgnoreCase("WM10")||order.optString("chanel").equalsIgnoreCase("WX02"))) {
						new_pose_payment.put("paycount",
								Scm.padd(saas_payment.optDouble("pay_money"), saas_payment.optDouble("more_coupon",0.0)));
					} else if (order.optString("chanel").equalsIgnoreCase("BD06")||order.optString("chanel").equalsIgnoreCase("MT08")) {
						Double money = Scm.psub(Scm.padd(
								order.optDouble("actual_pay"),
								platform_side_discount_fee), order.optDouble(
								"meal_costs", 0));
						new_pose_payment.put("paycount", money.toString());
					}else{
//						Double money = Scm.psub(Scm.psub(order.optDouble("total_money"),order.optDouble("meal_costs",0.0)),platform_side_discount_fee);
						Double money = Scm.padd(order.optDouble("shop_real_amount",0.0),order.optDouble("commission_amount",0.0));
						new_pose_payment.put("paycount", money.toString());
					}
					new_pose_payment.put("paybb", "0");
					new_pose_payment.put("vocount", "0");
					new_pose_payment.put("fzzhje", "0");
					new_pose_payment.put("flhl", "0");
					new_pose_payment.put("pay_memo",
							saas_payment.optString("payment_class"));
					new_pose_payment.put("opttime", DateUtil
							.format(new Timestamp(System.currentTimeMillis())));
					new_pose_payment_arr.add(new_pose_payment);
				}
				// payment.optString("tenancy_id");// 商户ID
				// TODO 付款方式需要对应
				main.setPayments(new_pose_payment_arr);// 付款方式ID
			}

			// payment.optString("order_code");// 订单号
			// payment.optString("pay_money");// 付款金额
			// payment.optString("third_bill_code");// 付款交易流水号

			// // 订单优惠信息
			// JSONArray discounts = ccOrder.optJSONArray("cc_order_discount");
			// for (Object o : discounts) {
			// JSONObject discount = (JSONObject) o;
			// discount.optString("tenancy_id");// 商户ID
			// discount.optString("order_code");// 订单号
			// discount.optString("discount_type");// 优惠类型
			// discount.optString("discount_fee");// 优惠金额
			// discount.optString("activity_id");// 活动ID
			// discount.optString("baidu_rate");// 百度承担金额
			// discount.optString("shop_rate");// 商户承担金额
			// discount.optString("agent_rate");// 代理商承担金额
			// discount.optString("logistics_rate");// 物流承担金额
			// discount.optString("discount_desc");// 优惠描述
			//
			// }
			if (ccOrder.containsKey("cc_order_discount")) {
				JSONArray order_discount_arr = ccOrder
						.optJSONArray("cc_order_discount");
				JSONArray new_pose_order_discount_arr = new JSONArray();
				if (order_discount_arr.size() > 0) {
					for (Object o : order_discount_arr) {
						JSONObject order_discount = (JSONObject) o;
						JSONObject new_pose_order_discount = new JSONObject();
						new_pose_order_discount.put("order_code",
								order_discount.optString("order_code"));
						String discountType=order_discount.optString("discount_type");
						if("privilege_mian".equals(discountType)){
							order_discount.put("discount_type", "pri_mian");
						}else{
							order_discount.put("discount_type", discountType);
						}
						new_pose_order_discount.put("discount_type",
								order_discount.optString("discount_type"));
						new_pose_order_discount.put("activity_id",
								order_discount.optString("activity_id"));
						new_pose_order_discount.put("discount_desc",
								order_discount.optString("discount_desc"));
						new_pose_order_discount.put("discount_fee", Double
								.valueOf(order_discount.optDouble(
										"discount_fee", 0.0D)));
						new_pose_order_discount.put("platform_rate", Double
								.valueOf(order_discount.optDouble("baidu_rate",
										0.0D)));
						new_pose_order_discount.put("shop_rate", Double
								.valueOf(order_discount.optDouble("shop_rate",
										0.0D)));
						new_pose_order_discount.put("agent_rate", Double
								.valueOf(order_discount.optDouble("agent_rate",
										0.0D)));
						new_pose_order_discount.put("logistics_rate", Double
								.valueOf(order_discount.optDouble(
										"logistics_rate", 0.0D)));
						new_pose_order_discount_arr
								.add(new_pose_order_discount);
					}
					main.setDiscount(new_pose_order_discount_arr);
				}
			}
			resList.add(main);
			sb.append("{");
			sb.append("type : '" + CallCenterConstant.MQ_TYPE_CALLCENTER_ORDER
					+ "'");
			sb.append(",msg : 'CC新增订单'");
			sb.append(",oper : 'add'");
			sb.append(",sign : ''");
			sb.append(",success : true");
			sb.append(",userid : 'cc'");
			sb.append(",storeid : '" + main.getStoreid() + "'");
			sb.append(",t : " + System.currentTimeMillis());
			sb.append(",logkey : 'cc'");
			sb.append(",data : " + JSONArray.fromObject(resList));
			sb.append("}");
		} catch (Exception e) {
			logger.error("ccOrder2NewPos 转换异常：", e);
			e.printStackTrace();
		}

		return JSONObject.fromObject(sb.toString());
	}

	/**
	 * 发送请求到DIE
	 * 
	 * @param btYddBtydxm2Dto
	 */
	// public void sendHttpClickForDie(BtYdd ydd, List<BtYdxm2> detailList)
	// throws Exception {
	// List<BtYddDetailSendDto> items = new ArrayList<BtYddDetailSendDto>();
	// for (BtYdxm2 btYdxm2 : detailList) {
	// BtYddDetailSendDto dto = new BtYddDetailSendDto();
	// dto.setItemid(btYdxm2.getXmbh());
	// dto.setItemname(btYdxm2.getXmmc());
	// dto.setItemcount(Scm.hv(btYdxm2.getXmsl()) ? btYdxm2.getXmsl().toString()
	// : "0");
	// dto.setOrder_id(ydd.getYddh());
	// dto.setPrice(btYdxm2.getXmdj() == null ? "0" :
	// btYdxm2.getXmdj().toString());
	// dto.setSkuinfo("");
	//
	// dto.setTotalprice(Scm.hv(btYdxm2.getTotalprice()) ?
	// btYdxm2.getTotalprice().doubleValue() : 0);
	//
	// dto.setXmid(btYdxm2.getXmid() == null ? "0" :
	// btYdxm2.getXmid().toString());
	// dto.setXmsx(btYdxm2.getXmsx() == null ? "未知" : btYdxm2.getXmsx());
	// dto.setTcid(btYdxm2.getLy1() == null ? "0" : btYdxm2.getLy1());
	// dto.setTcbh(btYdxm2.getTcbh() == null ? "" : btYdxm2.getTcbh());
	// dto.setTcdch(btYdxm2.getTcdch() == null ? "0" :
	// btYdxm2.getTcdch().toString());
	//
	// items.add(dto);
	// }
	// BtYddSendDto main = new BtYddSendDto();
	// main.setKwbh("1");// 口味编号
	// // main.setStoreid("JTZB00900101");// 门店ID
	// // main.setShopid("JTZB00900101");// 门店ID
	// main.setStoreid(ydd.getShopsId());// 门店ID
	// main.setShopid(ydd.getShopsId());// 门店ID
	// // main.setMerchant_id(ydd.getMerchantId().toString());// 企业ID
	// main.setMerchant_id("ZKF");// 企业ID 暂时写死 ydd.getMerchantId()没值
	// main.setOrder_id(Scm.hv(ydd.getYddh()) ? ydd.getYddh() : "");// 订单ID
	// main.setTotalprice(ydd.getTotalprice().toString());// 合计金额
	// main.setContact(Scm.hv(ydd.getLxr()) ? ydd.getLxr() : "");// 联系人
	// main.setMobile(Scm.hv(ydd.getLxrdh()) ? ydd.getLxrdh() : "");// 联系人电话
	// main.setPeople(Scm.hv(ydd.getYdrs()) ? ydd.getYdrs().toString() : "");//
	// 就餐人数
	// main.setMember_address1(Scm.hv(ydd.getMemberAddress()) ?
	// ydd.getMemberAddress() : "");// 地址
	// main.setKwbh(Scm.hv(ydd.getKwxh()) ? ydd.getKwxh() : "");
	// main.setMemo(Scm.hv(ydd.getBz()) ? ydd.getBz() : "");// 备注
	// main.setInvoice("");// 。。
	// main.setDdzt(ydd.getDdzt());// 状态
	// main.setTableinfo_id("0");// 桌类ID
	// main.setWarning(Scm.hv(ydd.getWarning()) ? ydd.getWarning() : "");// 。。。
	// main.setCancel_type(Scm.hv(ydd.getCancelType()) ?
	// ydd.getCancelType().toString() : "");// 取消原因
	// main.setPaychannel(Scm.hv(ydd.getPaychannel()) ? ydd.getPaychannel() :
	// "");// 支付方式
	// main.setPaystatus(Scm.hv(ydd.getPaystatus()) ? ydd.getPaystatus() :
	// "");// 支付状态
	// main.setShippingmethod(Scm.hv(ydd.getShippingmethod()) ?
	// ydd.getShippingmethod().toString() : "");// 外卖单配送方式
	// main.setQdrbh("");// 签单人编号
	// main.setShrbh("");// 审核人编号
	// main.setQdrbh1("");// 取单人编号
	// main.setSyrbh("");// 收银人编号
	// main.setPdrbh("");// 派单人编号
	// main.setSbsqrbh("");// 失败申请人编号
	// main.setXsqxrbh("");// 线上取消人编号
	// main.setQdrmc("");// 签单人名称
	// main.setShrmc("");// 审核人名称
	// main.setQdrmc1("");// 取单人名称
	// main.setSyrmc("");// 收银人名称
	// main.setPdrmc("");// 派单人名称
	// main.setSbsqrmc("");// 失败申请人名称
	// main.setXsqxrmc("");// 线上取消人名称
	// main.setCreateUserName(Scm.hv(ydd.getCreateUserName()) ?
	// ydd.getCreateUserName() : ""); // 创建人名称
	// main.setQdsj("");// 签单时间
	// main.setYshsj(Scm.hv(Scm.fd(ydd.getCreateTime())) ?
	// Scm.fd(ydd.getCreateTime()) : "");// 已审核时间
	// main.setQdsj1("");// 取单时间
	// main.setSysj("");// 收银时间
	// main.setPdsj("");// 派单时间
	// main.setWcsj("");// 完成（成功/失败）时间
	// main.setSbsqsj("");// 失败申请时间
	// main.setXsqxsj("");// 线上取消时间
	// main.setBlrq(Scm.hv(ydd.getBlrq()) ? ydd.getBlrq() : "");// 保留日期
	// main.setYdrq(Scm.hv(ydd.getYdrq()) ? ydd.getYdrq() : "");// 预定日期
	// main.setDdsj("");// 到店时间
	// main.setItems(items);// 菜品明细
	// main.setBeforeOrderType(CallCenterConstant.OUTSIDE_ORDER);
	// main.setBeforeOrderSource(CallCenterConstant.CALLCENTER);
	//
	// List<BtYddSendDto> resList = new ArrayList<BtYddSendDto>();
	// resList.add(main);
	//
	// StringBuilder sb = new StringBuilder();
	// sb.append("{");
	// sb.append("type : '" + CallCenterConstant.MQ_TYPE_CALLCENTER_ORDER +
	// "'");
	// sb.append(",msg : 'CC新增订单'");
	// sb.append(",oper : 'add'");
	// sb.append(",sign : ''");
	// sb.append(",success : true");
	// sb.append(",userid : 'cc'");
	// sb.append(",storeid : '" + ydd.getShopsId() + "'");
	// sb.append(",t : " + System.currentTimeMillis());
	// sb.append(",logkey : 'cc'");
	// sb.append(",data : " + JSONArray.fromObject(resList));
	// sb.append("}");
	//
	// String jsonStr =
	// HttpUtil.sendPostRequest(PropertiesLoader.getProperty("MqHttpUrl.cc"),
	// sb.toString());
	// DieSendMessageDto dto = (DieSendMessageDto)
	// JSONObject.toBean(JSONObject.fromObject(jsonStr),
	// DieSendMessageDto.class);
	// if (!dto.isSuccess()) {
	// throw new Exception("" + dto.getMsg());
	// }

	// }
	public static String formatDate(String time) {
		String times = "";
		SimpleDateFormat ss = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat dd = new SimpleDateFormat("yyyy-MM-dd");
		try {
			times = dd.format(ss.parse(time));
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return times;
	}

}