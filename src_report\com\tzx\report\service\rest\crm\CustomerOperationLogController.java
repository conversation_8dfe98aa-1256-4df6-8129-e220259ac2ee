package com.tzx.report.service.rest.crm;

import com.tzx.report.bo.crm.CustomerOperationLogService;
import jxl.write.WriteException;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Map;

/**
 * @Author: zhanglg
 * @Date: 2019-09-24 16:34
 */
@Controller
@RequestMapping("/report/customerOperationLog")
public class CustomerOperationLogController {


    @Resource(name = CustomerOperationLogService.NAME)
    private CustomerOperationLogService operationLogService;

    @RequestMapping(value = "/find")
    @ResponseBody
    public JSONObject find(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
    {
        HttpSession session = request.getSession();
        JSONObject result = new JSONObject();
        try
        {
            JSONObject p = JSONObject.fromObject("{}");
            Map<String, String[]> map = request.getParameterMap();
            for (String key : map.keySet())
            {
                if (map.get(key)[0] != "")
                {
                    p.put(key, map.get(key)[0]);
                }
            }

            if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
                if(p.optString("store_ids").length()==0){
                    p.element("store_ids", session.getAttribute("user_organ_codes_group"));
                }
            }else{
                if(p.optString("store_ids").length()==0){
                    p.element("store_ids", session.getAttribute("user_organ"));
                }
            }
            String tenancyId = (String) session.getAttribute("tenentid");
            result = operationLogService.find(tenancyId,p);
        }catch (Exception e){
            e.printStackTrace();
        }

        return  result;
    }
}
