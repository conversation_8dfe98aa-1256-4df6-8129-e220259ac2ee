package com.tzx.report.bo.imp.boh;

import javax.annotation.Resource;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;
import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.bo.boh.BusinessBackdishIndetailsReportService;
import com.tzx.report.po.boh.dao.BusinessBackdishIndetailsReportDao;

@Service(BusinessBackdishIndetailsReportService.NAME)
public class BusinessBackdishIndetailsReportServiceImp implements BusinessBackdishIndetailsReportService
{
	 @Resource(name = BusinessBackdishIndetailsReportDao.NAME)
	 BusinessBackdishIndetailsReportDao businessBackdishIndetailsReportDao;
	 
		@Resource
		private CommonMethodAreaService commonMethodAreaService;
	@Override
	public JSONObject getRestoreBill(String attribute, JSONObject p) throws Exception {
		return businessBackdishIndetailsReportDao.getBusinessInformation(attribute,p);
	}

}