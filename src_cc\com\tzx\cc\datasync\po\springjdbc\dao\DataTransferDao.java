package com.tzx.cc.datasync.po.springjdbc.dao;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;

public interface DataTransferDao extends GenericDao{
	
	String	NAME	= "com.tzx.cc.po.springjdbc.dao.impl.DataTransferDaoImpl";
	
	
	List<JSONObject> findSAASData4Compare(JSONObject serviceParams, String fromTableName, String toTableName) throws Exception;

	List<JSONObject> findPreTransferData(JSONObject serviceParams, String tableName) throws Exception;

    /**
     * 获取RIF机构信息
     * @param serviceParams
     * @param tableName
     * @return
     * @throws Exception
     */
    List<JSONObject> findRifOrgList(JSONObject serviceParams, String tableName) throws Exception;

    /**
     * 更新数据通过 同步过来的伪Id    fake_id
     * @param tenancyId
     * @param toTableName
     * @param list
     * @param ignoreColumns 忽略要更新的列
     * @return
     * @throws Exception
     */
    int[] updateBatchIgnorCaseByfakeId(String tenancyId, String toTableName,
			List<JSONObject> list,String ... ignoreColumns) throws Exception;
    
    
    /**
     * oracel 菜品类别专用
     * @param tenancyId
     * @param whereAnd   条件
     * @param toTableName
     * @param list
     * @param ignoreColumns
     * @return
     * @throws Exception
     */
    int[] updateBatchIgnorCaseByfakeId(String tenancyId,String whereAnd, String toTableName,
    		List<JSONObject> list,String ... ignoreColumns) throws Exception;

    /**
     * 测试是否可以获取连接
     * @param serviceParams
     * @return
     */
    boolean testConnect(JSONObject serviceParams,JSONObject result_flag);
}
