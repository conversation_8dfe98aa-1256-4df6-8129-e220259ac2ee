package com.tzx.cc.baidu.service;

import net.sf.json.JSONObject;

/**
 * 菜品接口
 * <AUTHOR>
 *
 */
@Deprecated
public interface DishService {
  
	/**菜品分类列表取得
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject loadDishCategoryList(String tenancyID,JSONObject condition) throws Exception;
	
	/**菜品列表取得
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject loadDishList(String tenancyID,JSONObject condition) throws Exception;
	
	/**价格列表取得
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPrice(String tenancyID,JSONObject condition) throws Exception;
  
	/**新增菜品分类
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishDategoryCreate(String tenancyID,JSONObject condition) throws Exception;
	/**修改菜品分类
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishCategoryUpdate(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品上传	
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishCreate(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品修改
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishUpdate(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品上线
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishOnline(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品下线
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishOffline(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品删除
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishDelete(String tenancyID,JSONObject condition) throws Exception;	
}
