package com.tzx.report.bo.imp.boh;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.*;

import javax.annotation.Resource;

import com.tzx.framework.common.util.DateUtil;
import com.tzx.newcrm.base.utils.excel.CreatExcelUtil;
import com.tzx.report.bo.boh.TheCashierOverDinnerFilterReportService;
import com.tzx.report.common.util.ConditionUtils;
import net.sf.json.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;


import com.tzx.framework.bo.dto.Organ;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.boh.SpecialFilteringService;

@Service(SpecialFilteringService.NAME)
public class SpecialFilteringServiceImpl implements SpecialFilteringService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	@Resource
	private ConditionUtils conditionUtils;

	@Resource
	private TheCashierOverDinnerFilterReportService theCashierOverDinnerFilterReportService;

	@SuppressWarnings("unchecked")
	@Override
	public JSONObject loadOperationInformation(String tenancyID, JSONObject condition) throws Exception
	{

		StringBuilder sql = new StringBuilder();
		sql.append(" with t1 as (" +
				" select o.id, " +
				" o.organ_code as store_code , " +
				" o.org_full_name as store_name ," +
				" type_name, " +
				" item_type " +
				" from saas_special_filter  ssf" +
				" LEFT JOIN organ o on o.id = ssf.store_id ");
			if(condition.containsKey("store_ids") && !condition.optString("store_ids").equals("0")){
				sql.append(" where  ssf.store_id  in ("+condition.optString("store_ids")+") " );
			}
			sql.append("GROUP BY o.id,type_name, " +
				" item_type  )," +
				" t2 as ( " +
				" select ssf.store_id, ssf.item_type," +
				" string_agg(item_code,',') as  item_code, " +
				" string_agg(item_name,',') as item_name " +
				" from saas_special_filter  ssf " +
				" LEFT JOIN organ o on o.id = ssf.store_id  " );
				if(condition.containsKey("store_ids") && !condition.optString("store_ids").equals("0")){
					sql.append(" where  ssf.store_id  in ("+condition.optString("store_ids")+") " );
				}
				sql.append(" GROUP  BY ssf.store_id, ssf.item_type ) select * from t1,t2 where t1. item_type = t2.item_type and t2.store_id = t1.id " );
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenancyID, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
		JSONObject result = new JSONObject();
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}
	@Override
	public String getCheakTree(String tenantId, JSONObject p) throws Exception {
		// TODO Auto-generated method stub
		List<JSONObject> list =new ArrayList<JSONObject>();
		String cheakCode =p.optString("cheakCode");
        String selectCheakCode = p.optString("selectCheakCode");
        String store_ids = p.optString("store_ids");

		StringBuilder sb = new StringBuilder();

		if(p.containsKey("selectType")) {
				sb.append(" select item_code as id ,item_name as text from saas_special_filter " +
						  " where store_id in ("+ store_ids +") and item_type = '"+p.optString("selectType")+"' ");
				if ("rate_payname_filter".equals(p.optString("selectType"))){ // 实收占比过滤 item_type=rate_payname_filter
					sb.append(" order by is_run asc, id asc ");
				} else {
					sb.append(" order by id ");
				}
		}else {
			if(cheakCode.equals("table_code")) {
				// 桌位查询
				sb.append(" select  table_code as id, table_name as text from tables_info  where organ_id in ("+ store_ids +") ");
				sb.append(" and table_code not in (select item_code from saas_special_filter f where f.store_id in (").append(store_ids).append(") and f.item_type = 'table_filter') ");
				sb.append(" order by table_code ");

			} else if (cheakCode.equals("pay_code")) {
				// 付款方式
				sb.append(" select payment_id as id, payment_name as text from v_payment_way where 1 = 1  ");
				if ("payname_filter".equals(selectCheakCode)){ // 支付方式过滤
                    sb.append(" and payment_id not in (select item_code::int from saas_special_filter f where f.store_id in (").append(store_ids).append(") and f.item_type = 'payname_filter') ");
                } else if ("rate_payname_filter".equals(selectCheakCode)) { // 实收占比过滤
                    sb.append(" and payment_id not in (select item_code::int from saas_special_filter f where f.store_id in (").append(store_ids).append(") and f.item_type = 'rate_payname_filter' and f.is_run = '1') ");
                }
			}
		}

		list =  this.dao.query4JSONArray(tenantId, sb.toString());
		return JsonUtils.list2json(list);
	}
	@Override
	public void delete(String tenantId, String tableName, JSONObject obj)
			throws Exception {
		// TODO Auto-generated method stub
		String sql = "DELETE FROM "+tableName+" WHERE store_id = "+obj.optString("store_id")+" and item_type = '"+obj.optString("item_type")+"'";
		this.dao.execute(tenantId,sql);
	}
	@Override
	public Object updateSaveRun(String tenantId, String tableName, JSONObject obj)
			throws Exception {
		// TODO Auto-generated method stub
		String sql ="";
		sql=" select is_run from saas_special_filter_run where tenancy_id = '"+tenantId+"' ";
		List<JSONObject> list = this.dao.query4Json(tenantId, sql.toString());
		if(list.size()>0) {
			  sql = "update  saas_special_filter_run set is_run ="+obj.optString("is_run")+" WHERE tenancy_id = '"+tenantId+"'";
			  this.dao.execute(tenantId,sql);

			  // 屏蔽saas 目录
			  sql = "update  sys_modules set states =0 WHERE id = 69021 ";
			  this.dao.execute(tenantId,sql);

			  // 屏蔽pos 目录
			  sql = "update  sys_modules set states =0 WHERE id = 90116 ";
			  this.dao.execute(tenantId,sql);

		}else {
			  sql = "insert into   saas_special_filter_run (tenancy_id ,is_run) values ('"+obj.optString("tenancy_id")+"',"+obj.optString("is_run")+") ";
			  this.dao.execute(tenantId,sql);



		}
		// 屏蔽报表
		if(obj.optString("is_run").equals("1")) {
			List<JSONObject> query4Json = dao.query4Json(tenantId, "select sql from saas_report_engine where report_num ='SAAS_BI_2017_35' and sql_type ='QY' ");
			if(query4Json.size()>0) {
				sql =query4Json.get(0).optString("sql");
			}
		}else {
			List<JSONObject> query4Json = dao.query4Json(tenantId, "select sql from saas_report_engine where report_num ='SAAS_BI_2017_35' and sql_type ='SX' ");
			if(query4Json.size()>0) {
				sql =query4Json.get(0).optString("sql");
			}
		}
		this.dao.execute(tenantId,sql);
		return null;
	}
	@Override
	public JSONObject selectRun(String tenancyID, JSONObject obj) throws Exception {
		// TODO Auto-generated method stub
		String sql ="";
		sql=" select is_run from saas_special_filter_run where tenancy_id = '"+tenancyID+"' ";
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		JSONObject result = new JSONObject();
		if(list.size()>0) {
			result.put("runIs", list.get(0).opt("is_run"));
		}
		return result;
	}

	@Override
	public void saveOperationInformation(String tenancyID, JSONObject param, List<JSONObject> list, JSONObject result) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" delete from saas_special_filter where store_id = ").append(param.optInt("store_id")).append(" and item_type = '").append(param.optString("item_type")).append("' ");
		this.dao.execute(tenancyID, sql.toString());

		if (list.size() > 0){
			this.dao.insertBatchIgnorCase(tenancyID, "saas_special_filter", list);
		}
		result.put("success", true);
	}

	@Override
	public JSONObject getBillInfo(String tenentid, JSONObject obj) throws Exception {
        StringBuilder sb = new StringBuilder();
        String cheakCode = obj.optString("cheak_code");
        String storeIds = obj.optString("store_ids");

        if(cheakCode.equals("bills")){//账务查询
			JSONObject result = new JSONObject();
            sb.setLength(0);
            sb.append("select org.id,org.org_full_name,pb2.bill_num,pb2.payment_amount,pb2.report_date from pos_bill2 pb2 LEFT JOIN organ org on pb2.store_id = org.id  ");
            sb.append("where  org.id in ("+storeIds+") ");
            if(obj.containsKey("begin_date")&&obj.containsKey("end_date")){
            	sb.append("and pb2.report_date BETWEEN '"+obj.optString("begin_date")+"' and '"+obj.optString("end_date")+"'");
			}
			sb.append("and pb2.bill_num = '"+obj.optString("bill_num")+"' ");

			List<JSONObject> billsListJo = this.dao.query4Json(tenentid,sb.toString());
			if(billsListJo.size()>0){
				result.put("success",true);
				result.put("msg","查询账单成功");
				result.put("bill",billsListJo.get(0));
			}else{
				result.put("success",false);
				result.put("msg","在当前门店范围内未找到该账单");
			}

			return result;

		}else if(cheakCode.equals("special")){//特殊场景
			StringBuilder countSb = new StringBuilder();
			int ordercount = 0;
			double ordersum = 0.0;
			int invoicecount = 0;
			double invoicesum = 0.0;
            //外卖平台账单  count(cor.id) as ordercount,COALESCE(sum(cor.shop_real_amount),0) as ordersum
			sb.setLength(0);
			sb.append("select count(pb3.id) as ordercount,COALESCE(sum(pb3.payment_amount),0) as ordersum from pos_bill2 pb3 where pb3.bill_num in ( select pb2.bill_num from  cc_order_list col LEFT JOIN  pos_bill2 pb2 on pb2.bill_num = col.bill_num where pb2.store_id in ("+storeIds+") and pb2.report_date BETWEEN '"+obj.optString("begin_date")+"' and '"+obj.optString("end_date")+"') or pb3.copy_bill_num in ( select pb2.bill_num from  cc_order_list col LEFT JOIN  pos_bill2 pb2 on pb2.bill_num = col.bill_num where pb2.store_id in ("+storeIds+") and pb2.report_date BETWEEN '"+obj.optString("begin_date")+"' and '"+obj.optString("end_date")+"') ");
			JSONArray orderBill = this.dao.query4JSONArray(tenentid,sb.toString());
			if(orderBill.size()>0){
				ordercount = orderBill.optJSONObject(0).optInt("ordercount");
				if(ordercount>0){
					ordersum = orderBill.optJSONObject(0).optDouble("ordersum");
				}
			}
			sb.setLength(0);
			sb.append("select count(pb3.id) as countids from pos_bill2 pb3 where (pb3.bill_num in ( select pb2.bill_num from  cc_order_list col LEFT JOIN  pos_bill2 pb2 on pb2.bill_num = col.bill_num where pb2.store_id in ("+storeIds+") and pb2.report_date BETWEEN '"+obj.optString("begin_date")+"' and '"+obj.optString("end_date")+"') or pb3.copy_bill_num in ( select pb2.bill_num from  cc_order_list col LEFT JOIN  pos_bill2 pb2 on pb2.bill_num = col.bill_num where pb2.store_id in ("+storeIds+") and pb2.report_date BETWEEN '"+obj.optString("begin_date")+"' and '"+obj.optString("end_date")+"') ) and pb3.bill_state is null");
           JSONArray countorderJa = this.dao.query4JSONArray(tenentid,sb.toString());
           if(countorderJa.size()>0){
			   ordercount = countorderJa.optJSONObject(0).optInt("countids");
		   }


			//已开发票账单  count(pbi.id) as invoicecount,COALESCE(sum(pbi.invoice_amount),0) as invoicesum
			sb.setLength(0);
			sb.append("select count(pb2.id) as invoicecount,sum(pb2.payment_amount) as invoicesum from pos_bill2 pb2 where bill_num in (select bill_num from pos_bill_invoice pbi where pbi.store_id in ("+storeIds+") and  pbi.report_date BETWEEN '"+obj.optString("begin_date")+"' and '"+obj.optString("end_date")+"') or pb2.copy_bill_num in (select bill_num from pos_bill_invoice pbi where pbi.store_id in ("+storeIds+")  and  pbi.report_date BETWEEN '"+obj.optString("begin_date")+"' and '"+obj.optString("end_date")+"')");
			JSONArray invoiceBill = this.dao.query4JSONArray(tenentid,sb.toString());
			if(invoiceBill.size()>0){
				invoicecount = invoiceBill.optJSONObject(0).optInt("invoicecount");
				if(invoicecount>0){
					invoicesum = invoiceBill.optJSONObject(0).optDouble("invoicesum");
				}
			}

			sb.setLength(0);
			sb.append("select count(pb3.id) as countid from pos_bill2 pb3 where pb3.bill_num in (select pb2.bill_num from pos_bill2 pb2 where bill_num in (select bill_num from pos_bill_invoice pbi where pbi.store_id in ("+storeIds+") and  pbi.report_date BETWEEN '"+obj.optString("begin_date")+"' and '"+obj.optString("end_date")+"') or pb2.copy_bill_num in (select bill_num from pos_bill_invoice pbi where pbi.store_id in ("+storeIds+")  and  pbi.report_date BETWEEN '"+obj.optString("begin_date")+"' and '"+obj.optString("end_date")+"') ) and (pb3.bill_state = 'DPQX03' or pb3.bill_state is null) ");
           JSONArray countinvoiceJa = this.dao.query4JSONArray(tenentid,sb.toString());
           if(countinvoiceJa.size()>0){
			   invoicecount = countinvoiceJa.optJSONObject(0).optInt("countid");
		   }

            double sumAmount = ordersum + invoicesum ;

			BigDecimal b = new BigDecimal(sumAmount);
			sumAmount = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			JSONObject result = new JSONObject();

			JSONObject amount = new JSONObject();
			amount.put("sumAmount",sumAmount);
			amount.put("ordercount",ordercount);
			amount.put("ordersum",ordersum);
			amount.put("invoicecount",invoicecount);
			amount.put("invoicesum",invoicesum);

			result.put("success",true);
			result.put("msg","获取特殊账务成功");
			result.put("specialinfo",amount);

			return result;
		}else if(cheakCode.equals("bill_amount")){//账单金额
			return null;
		}else if(cheakCode.equals("opentable_time")){//开台时间
			return null;
		}

		return null;
	}

	@Override
	public Object getOrgantree(String tenentid, JSONObject obj) throws Exception {
		StringBuilder sb = new StringBuilder();
		String storeIds = obj.optString("store_ids");
        int special_info_id = obj.optInt("id");
		String conditions = "";

		sb.setLength(0);
		if(special_info_id==0){
			sb.append("select distinct(store_id) from saas_special_info_organ ");
		}else{
			sb.append("select distinct(store_id) from saas_special_info_organ where  special_info_id <> "+special_info_id+" ");
		}


		JSONArray ja = this.dao.query4JSONArray(tenentid,sb.toString());

		StringBuilder storeIdSb = new StringBuilder();
		for (int i = 0; i < ja.size(); i++) {
			int storeid = ja.optJSONObject(i).optInt("store_id");
			storeIdSb.append(String.valueOf(storeid)).append(",");
		}

		if(storeIdSb.length()>0){
			conditions = storeIdSb.toString().substring(0,storeIdSb.toString().length()-1);
		}

// else{
//			sb.append("select distinct(store_id) from saas_special_filter where special_info_id = "+special_info_id+" ");
//		}



		Organ organ = new Organ();
		sb.setLength(0);
		sb.append("select id,CONCAT(organ_code,org_full_name) as text,top_org_id as fatherId,level,organ_code as code,org_type as type,tenant_state as tenantstate,org_uuid as orguuid from organ where 1=1 and valid_state='1'");
		if(null != conditions && StringUtils.isNotBlank(conditions)){
			sb.append("and id not in("+conditions+")    ");
		}
		sb.append(" order by organ_code  ");

		List<Organ> list = (List<Organ>) this.dao.query(tenentid, sb.toString(), Organ.class);
		List<Organ> list2 = new ArrayList<Organ>();
		Map<Integer, Organ> map = new HashMap<Integer, Organ>();
		for (Organ jo : list){
			map.put(jo.getId(), jo);
		}

		for (Organ jo1 : list){
			if (map.get(jo1.getFatherId()) == null){

				list2.add(jo1);
			}

			if (map.get(jo1.getFatherId()) != null){

				map.get(jo1.getFatherId()).getChildren().add(map.get(jo1.getId()));

			}
		}
		boolean flag = false;

		if("0".equals(storeIds))
		{
			flag = true;
		}
		else
		{
			String[] oids = conditions.split(",");
			for(String oid:oids)
			{
				if("0".equals(oid))
				{
					flag = true;
					break;
				}
			}
		}

		if (flag){
			organ.setChildren(list2);
			organ.setLevel(0);
			organ.setCode("");
			organ.setId(0);
			organ.setType("1");
			organ.setText(organ.getCode() + "总部");
			List<Organ> flist = new ArrayList<Organ>();
			flist.add(organ);
			return JsonUtils.list2json(flist);

		}
		return JsonUtils.list2json(list2);
	}

	@Override
	public JSONObject saveBaccountRule(String tenentid, JSONObject obj) throws Exception {
		JSONObject result = new JSONObject();
		StringBuilder sqlSb = new StringBuilder();

		JSONObject publicparam = new JSONObject();
		String id = obj.optString("id");
		String store_id = obj.optString("store_ids");
		String rule_name = obj.optString("rule_name");
		String rule_type = obj.optString("rule_type");
        String begin_report_date = obj.optString("begin_date");
        String end_report_date = obj.optString("end_date");

		JSONObject json = new JSONObject();
		json.put("rule_name",rule_name);
		json.put("rule_type",rule_type);
		json.put("states","1");
		json.put("begin_report_date",begin_report_date);
		json.put("end_report_date",end_report_date);
		json.put("last_operator",obj.optString("last_operator"));
		json.put("last_update_time",DateUtil.format(new Timestamp(System.currentTimeMillis())));
        json.remove("id");
		Object special_info_id = null;
		if(id==null|| StringUtils.isBlank(id)){
			special_info_id = this.dao.insertIgnorCase(tenentid,"saas_special_info",json);
		}else{
			special_info_id = id ;
		}

		sqlSb.setLength(0);
		sqlSb.append("delete from saas_special_filter where special_info_id = "+special_info_id+" ");
		this.dao.execute(tenentid,sqlSb.toString());

			//公共参数供每个规则使用
			publicparam.put("special_info_id",special_info_id);
			publicparam.put("store_id",store_id);
			publicparam.put("begin_report_date",begin_report_date);
			publicparam.put("end_report_date",end_report_date);

			if(rule_type.equals("1")){//普通规则
				if(obj.containsKey("bill_filter")){//指定账单筛选规则
					JSONObject param = obj.optJSONObject("bill_filter");
					this.saveBruleBill(tenentid,param,publicparam);
				}
				if(obj.containsKey("special_scene_filter")){//特殊场景账单
					JSONObject param = obj.optJSONObject("special_scene_filter");
					this.saveBruleSpecialscene(tenentid,param,publicparam);
				}
				if(obj.containsKey("table_filter")){//桌位类型过滤
					JSONObject param = obj.optJSONObject("table_filter");
					this.getAssembleParams(tenentid,param,publicparam);
				}
				if(obj.containsKey("payname_filter")){//支付方式过滤
					JSONObject param = obj.optJSONObject("payname_filter");
					this.getAssembleParams(tenentid,param,publicparam);
				}
				if(obj.containsKey("bill_amount_filter")){//账单金额区间
					JSONObject param = obj.optJSONObject("bill_amount_filter");
					this.saveBruleBillamountsection(tenentid,param,publicparam);
				}
				if(obj.containsKey("opentable_time_filter")){//开台时间区间
					JSONObject param = obj.optJSONObject("opentable_time_filter");
					this.saveBruleOpentabletimesection(tenentid,param,publicparam);
				}
			}else if(rule_type.equals("2")){//特殊规则
				if(obj.containsKey("rate_payname_filter")){
					JSONObject param = obj.optJSONObject("rate_payname_filter");
					this.getAssembleParams(tenentid,param,publicparam);
				}
			}


		if(id!=null&& !StringUtils.isBlank(id)){
			json.put("id",id);
			//重新点击保存清除账务金额和账务生成时间
//			json.put("amount","");
//			json.put("generate_amount_time","");
//			this.dao.updateIgnorCase(tenentid,"saas_special_info",json);
			sqlSb.setLength(0);
			sqlSb.append("update saas_special_info set rule_name = '"+rule_name+"',rule_type = '"+rule_type+"',begin_report_date = '"+begin_report_date+"',end_report_date = '"+end_report_date+"',amount = null,states = '1',last_operator = '"+obj.optString("last_operator")+"',generate_amount_time = null,last_update_time = '"+DateUtil.format(new Timestamp(System.currentTimeMillis()))+"' where id = "+id+" ");
			this.dao.execute(tenentid,sqlSb.toString());
		}

		if(special_info_id!=null){
			StringBuilder sb = new StringBuilder();

			sb.setLength(0);
			sb.append("delete from saas_special_info_organ where special_info_id = "+special_info_id+" ");
			this.dao.execute(tenentid,sb.toString());

			sb.setLength(0);
			String[] store_ids = store_id.split(",");
			for (int i = 0; i < store_ids.length; i++) {
				int storeid = Integer.valueOf(store_ids[i]);
				sb.append("insert into saas_special_info_organ(special_info_id,store_id) values("+special_info_id+","+storeid+") ;");
			}
			if(sb.toString().length()>0){
				this.dao.execute(tenentid,sb.toString());
			}

		}

		if(rule_type.equals("1")){
			String updateBillStateSql = "update pos_bill2 set ab_flag = null where store_id in ("+store_id+") and report_date  BETWEEN '"+begin_report_date+"' AND '"+end_report_date+"' ";
			this.dao.execute(tenentid,updateBillStateSql);
		}


		if(id==null|| StringUtils.isBlank(id)){
			result.put("success",true);
			result.put("msg","保存成功");
		}else {
			result.put("success",true);
			result.put("msg","修改成功");
		}

		return result;
	}

	/**
	 * 查询商户的规则  rule_type 1:普通规则   2:特殊规则
	 * @param tenentid
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	@Override
	public Object findBaccountRule(String tenentid, JSONObject obj) throws Exception {
		StringBuilder sb = new StringBuilder();
		StringBuilder storeIdSb = new StringBuilder();
		StringBuilder storeNameSb = new StringBuilder();

		sb.setLength(0);
		sb.append("select * from saas_special_info where states = '1' ");
		List<JSONObject> listJo = this.dao.query4Json(tenentid,sb.toString());

		for (int i = 0; i < listJo.size(); i++) {
			JSONObject jo = listJo.get(i);
			int id = jo.optInt("id");
			storeIdSb.setLength(0);
			storeNameSb.setLength(0);
			sb.setLength(0);
			sb.append("select ssio.store_id,org.org_full_name from saas_special_info_organ ssio  ");
			sb.append("LEFT JOIN organ org on ssio.store_id = org.id and org.valid_state = '1'  ");
			sb.append("where ssio.special_info_id = "+id+" ");
			List<JSONObject> listJo2 = this.dao.query4Json(tenentid,sb.toString());

			for (int j = 0; j < listJo2.size(); j++) {
				JSONObject jo1 = listJo2.get(j);
				storeIdSb.append(jo1.optInt("store_id")).append(",");
				storeNameSb.append(jo1.optString("org_full_name")).append(",");
			}
			jo.put("store_id",storeIdSb.toString().substring(0,storeIdSb.toString().length()-1));
			jo.put("org_full_name",storeNameSb.toString().substring(0,storeNameSb.toString().length()-1));
			jo.put("organ_rows",listJo2);
		}

		return JsonUtils.list2json(listJo);
	}

	@Override
	public JSONObject getAccountingAmount(String tenentid, JSONObject obj) throws Exception {
		JSONObject result = new JSONObject();
		StringBuilder sb = new StringBuilder();
        StringBuilder paramSb = new StringBuilder();

		int special_info_id = obj.optInt("id");//AB帐规则的id
		if(special_info_id==0){
			result.put("success",false);
			result.put("msg","规则id不正确");
			return result;
		}

		String falseMsg = "";

		double accounting_amount = 0.0;
		if(special_info_id!=0){
			sb.setLength(0);
			sb.append("select * from saas_special_info where id = "+special_info_id+" ");
			List<JSONObject> listJo = this.dao.query4Json(tenentid,sb.toString());
			JSONObject specialInfoJo = listJo.get(0);
			String rule_type = specialInfoJo.optString("rule_type");
			String begin_report_date = specialInfoJo.optString("begin_report_date");
			String end_report_date = specialInfoJo.optString("end_report_date");

			String ruleStoreIds = this.getRuleInfo(tenentid,special_info_id).optString("store_id");

			obj.put("rule_type",rule_type);
			obj.put("store_id",ruleStoreIds);
			obj.put("begin_report_date",begin_report_date);
			obj.put("end_report_date",end_report_date);
			if(rule_type.equals("1")){//普通规则
				//将符合规则的账单做标记
				this.getRuleBillnum(tenentid,obj);

                sb.setLength(0);
                sb.append("select coalesce(sum(payment_amount),0) as amount_total from pos_bill2 where store_id in ("+ruleStoreIds+") and ab_flag is null and report_date between '"+begin_report_date+"' and '"+end_report_date+"' ");
                List<JSONObject> sumAmount = this.dao.query4Json(tenentid,sb.toString());
				accounting_amount = sumAmount.get(0).optDouble("amount_total");

			}else if(rule_type.equals("2")){//实收占比
                sb.setLength(0);
                sb.append("select distinct(store_id) from saas_special_info_organ where special_info_id = "+special_info_id+" ");
                List<JSONObject> storeIdListJo = this.dao.query4Json(tenentid,sb.toString());
				paramSb.setLength(0);
				for (int i = 0; i < storeIdListJo.size(); i++) {
					String storeId = storeIdListJo.get(i).optString("store_id");
					paramSb.append(storeId).append(",");
				}
				if(paramSb.toString().length()>0){
					String organ_id = paramSb.toString().substring(0,paramSb.toString().length()-1);
					JSONObject dutyOrderJo = new JSONObject();
					dutyOrderJo.put("organ_id",organ_id);
					//获取该规则门店的班次
					List<JSONObject> shiftOrderListJo = conditionUtils.loadDutyOrderNew(tenentid,dutyOrderJo);
					paramSb.setLength(0);
					for (int j = 0; j < shiftOrderListJo.size(); j++) {
						String shiftId = shiftOrderListJo.get(j).optString("id");
						paramSb.append(shiftId).append(",");
					}
					String shift_id = "";
					if(paramSb.toString().length()>0){
						shift_id = paramSb.toString().substring(0,paramSb.toString().length()-1);
					}else{
						falseMsg += "该规则的门店没有班次；";
					}

					//获取该规则门店的收银人员
					List<JSONObject> cashierOrderListJo = conditionUtils.loadCashierNum(tenentid,dutyOrderJo);
					paramSb.setLength(0);
					for (int k = 0; k < cashierOrderListJo.size(); k++) {
						String cashierId = cashierOrderListJo.get(k).optString("id");
						paramSb.append(cashierId).append(",");
					}
					String cashier_id = "";
					if(paramSb.toString().length()>0){
						cashier_id = paramSb.toString().substring(0,paramSb.toString().length()-1);
					}else{
						falseMsg += "该规则的门店没有收银人员；";
					}

					if(!StringUtils.isBlank(shift_id)&&!StringUtils.isBlank(cashier_id)){
						JSONObject shiftJo = new JSONObject();
						shiftJo.put("day_count",listJo.get(0).optString("begin_report_date"));
						shiftJo.put("date2",listJo.get(0).optString("end_report_date"));
						shiftJo.put("store_id",organ_id);
						shiftJo.put("shift_id",shift_id);
						shiftJo.put("cashier_id",cashier_id);
						shiftJo.put("type","1");

						JSONObject shiftReturnJo = theCashierOverDinnerFilterReportService.getTheCashierOverDinner(tenentid,shiftJo);
						accounting_amount = shiftReturnJo.optJSONArray("rows").optJSONObject(0).optDouble("feildval");
					}

				}else{
					falseMsg = "该规则没有门店在使用";
				}
			}
		}

		String last_operator = obj.optString("last_operator");
		String generate_amount_time = DateUtil.format(new Timestamp(System.currentTimeMillis()));
		String last_update_time = DateUtil.format(new Timestamp(System.currentTimeMillis()));
		//修改账务金额
		sb.setLength(0);
		sb.append("update saas_special_info set last_operator = '"+last_operator+"',generate_amount_time = '"+generate_amount_time+"',last_update_time = '"+last_update_time+"',amount = "+accounting_amount+" where id = "+special_info_id+" ");
		this.dao.execute(tenentid,sb.toString());

		result.put("success",true);
		result.put("msg","生成账务金额成功");

		return result;
	}

	@Override
	public JSONObject findBaccountRuleDetails(String tenentid, JSONObject obj) throws Exception {
		JSONObject result = new JSONObject();
		int special_info_id = obj.optInt("id");
		String storeid = obj.optString("store_id");

		JSONArray bill_filterJa = new JSONArray();//账单规则
		JSONObject bill_filterJo = new JSONObject();
		JSONArray special_scene_filterJa = new JSONArray();//特殊场景
		JSONObject special_scene_filterJo = new JSONObject();
		JSONArray table_filterJa = new JSONArray();//桌位规则
		JSONObject table_filterJo = new JSONObject();
		JSONArray payname_filterJa = new JSONArray();//付款方式
		JSONObject payname_filterJo = new JSONObject();
		JSONArray bill_amount_filterJa = new JSONArray();//账单金额区间
		JSONObject bill_amount_filterJo = new JSONObject();
		JSONArray opentable_time_filterJa = new JSONArray();//开台时间区间
		JSONObject opentable_time_filterJo = new JSONObject();
		JSONArray rate_payname_filterJa = new JSONArray();//实收占比
		JSONObject rate_payname_filterJo = new JSONObject();

		JSONArray optionJa = new JSONArray();
		StringBuilder sb = new StringBuilder();
		StringBuilder fiterSb = new StringBuilder();
		Map<Integer,String> map = new HashMap<Integer,String>();

		sb.setLength(0);
		sb.append("select * from organ where valid_state = '1' ");
		List<JSONObject> orgList = this.dao.query4Json(tenentid,sb.toString());
		for (int i = 0; i < orgList.size(); i++) {
			JSONObject orgJo = orgList.get(i);
			map.put(orgJo.optInt("id"),orgJo.optString("org_full_name"));
		}

		sb.setLength(0);
		sb.append("select * from saas_special_info where id = "+special_info_id+" ");
		List<JSONObject> listRuleJo = this.dao.query4Json(tenentid,sb.toString());

		sb.setLength(0);
		sb.append("select * from saas_special_filter where special_info_id = "+special_info_id+" ");
		List<JSONObject> listJo = this.dao.query4Json(tenentid,sb.toString());
		for (int i = 0; i < listJo.size(); i++) {
			JSONObject jo = listJo.get(i);
			String store_id = jo.optString("store_id");
			String item_type = jo.optString("item_type");
			String type_name = jo.optString("type_name");
            if(item_type.equals("bill_filter")){
				bill_filterJo.put("item_type",item_type);
				bill_filterJo.put("type_name",type_name);
            	JSONObject bill_filterJo1 = new JSONObject();
				bill_filterJo1.put("store_id",jo.optString("store_id"));
				bill_filterJo1.put("org_full_name",map.get(Integer.valueOf(jo.optString("store_id"))));
				bill_filterJo1.put("bill_num",jo.optString("bill_num"));
				bill_filterJo1.put("payment_amount",jo.optString("payment_amount"));
				bill_filterJo1.put("report_date",jo.optString("report_date"));
				bill_filterJo1.put("add_bill_time",jo.optString("add_bill_time"));
				bill_filterJa.add(bill_filterJo1);
			}else if(item_type.equals("special_scene_filter")){
            	if(store_id.equals(storeid)){
					special_scene_filterJo.put("item_type",item_type);
					special_scene_filterJo.put("type_name",type_name);
					special_scene_filterJo.put("peculiar_setup_invoice",jo.optString("peculiar_setup_invoice"));
					special_scene_filterJo.put("peculiar_setup_order",jo.optString("peculiar_setup_order"));
					special_scene_filterJo.put("type_name",jo.optString("peculiar_setup_invoice"));
				}
			}
			else if(item_type.equals("table_filter")){
				if(store_id.equals(storeid)){
					table_filterJo.put("item_type",item_type);
					table_filterJo.put("type_name",type_name);
					JSONObject table_filterJo1 = new JSONObject();
					table_filterJo1.put("id",jo.optString("item_code"));
					table_filterJo1.put("text",jo.optString("item_name"));
					table_filterJa.add(table_filterJo1);
				}
			}
			else if(item_type.equals("payname_filter")){
				if(store_id.equals(storeid)){
					payname_filterJo.put("item_type",item_type);
					payname_filterJo.put("type_name",type_name);
					JSONObject payname_filterJo1 = new JSONObject();
					payname_filterJo1.put("id",jo.optString("item_code"));
					payname_filterJo1.put("text",jo.optString("item_name"));
					payname_filterJa.add(payname_filterJo1);
				}
			}
			else if(item_type.equals("bill_amount_filter")){
				if(store_id.equals(storeid)){
					bill_amount_filterJo.put("item_type",item_type);
					bill_amount_filterJo.put("type_name",type_name);
					bill_amount_filterJo.put("bill_amount_start",jo.optString("bill_amount_start"));
					bill_amount_filterJo.put("bill_amount_end",jo.optString("bill_amount_end"));
					bill_amount_filterJo.put("is_cz_bill",jo.optString("is_cz_bill"));
				}
			}
			else if(item_type.equals("opentable_time_filter")){
				if(store_id.equals(storeid)){
					opentable_time_filterJo.put("item_type",item_type);
					opentable_time_filterJo.put("type_name",type_name);
					opentable_time_filterJo.put("time_interval_start",jo.optString("time_interval_start"));
					opentable_time_filterJo.put("time_interval_end",jo.optString("time_interval_end"));
				}
			}
			else if(item_type.equals("rate_payname_filter")){
				if(store_id.equals(storeid)){
					rate_payname_filterJo.put("item_type",item_type);
					rate_payname_filterJo.put("type_name",type_name);
					if(jo.optString("is_run").equals("2")){
						rate_payname_filterJo.put("amount_rate",jo.optString("item_code"));
						continue;
					}
					JSONObject rate_payname_filterJo1 = new JSONObject();
					rate_payname_filterJo1.put("id",jo.optString("item_code"));
					rate_payname_filterJo1.put("text",jo.optString("item_name"));
					rate_payname_filterJa.add(rate_payname_filterJo1);
				}
			}
		}

		result.put("id",special_info_id);
		result.put("rule_name",listRuleJo.size()>0?listRuleJo.get(0).optString("rule_name"):"");
		result.put("begin_report_date",listRuleJo.size()>0?listRuleJo.get(0).optString("begin_report_date"):"");
		result.put("end_report_date",listRuleJo.size()>0?listRuleJo.get(0).optString("end_report_date"):"");

		if(rate_payname_filterJa.size()>0){
			rate_payname_filterJo.put("list",rate_payname_filterJa);
			JSONArray ja = new JSONArray();
			ja.add("rate_payname_filter");
			result.put("store_ids",this.getRuleInfo(tenentid,special_info_id).optString("store_id"));
			result.put("rule_type",2);
			result.put("option",ja);
			result.put("rate_payname_filter",rate_payname_filterJo);
			result.put("success",true);
			result.put("msg","查询特殊规则成功");
		}else{
			if(bill_filterJo.size()>0&&bill_filterJa.size()>0){
				optionJa.add("bill_filter");
				bill_filterJo.put("bills",bill_filterJa);
				result.put("bill_filter",bill_filterJo);
			}
			if(special_scene_filterJo.size()>0){
				optionJa.add("special_scene_filter");
				result.put("special_scene_filter",special_scene_filterJo);
			}
			if(table_filterJo.size()>0&&table_filterJa.size()>0){
				optionJa.add("table_filter");
				table_filterJo.put("list",table_filterJa);
				result.put("table_filter",table_filterJo);
			}
			if(payname_filterJo.size()>0&&payname_filterJa.size()>0){
				optionJa.add("payname_filter");
				payname_filterJo.put("list",payname_filterJa);
				result.put("payname_filter",payname_filterJo);
			}
			if(bill_amount_filterJo.size()>0){
				optionJa.add("bill_amount_filter");
				result.put("bill_amount_filter",bill_amount_filterJo);
			}
			if(opentable_time_filterJo.size()>0){
				optionJa.add("opentable_time_filter");
				result.put("opentable_time_filter",opentable_time_filterJo);
			}

			result.put("store_ids",this.getRuleInfo(tenentid,special_info_id).optString("store_id"));
			result.put("rule_type",1);
			result.put("option",optionJa);
			result.put("success",true);
			result.put("msg","查询普通规则成功");
		}

		return result;
	}

	@Override
	public JSONObject getSysParam(String tenentid, JSONObject obj) throws Exception {
		// TODO Auto-generated method stub
		List<JSONObject> list =new ArrayList<JSONObject>();
		String cheakCode =obj.optString("cheakCode");
		String selectCheakCode = obj.optString("selectCheakCode");
		String store_ids = obj.optString("store_ids");

		StringBuilder sb = new StringBuilder();

		if(obj.containsKey("selectType")) {
			sb.append(" select item_code as id ,item_name as text from saas_special_filter " +
					" where store_id in ("+ store_ids +") and item_type = '"+obj.optString("selectType")+"' ");
			if ("rate_payname_filter".equals(obj.optString("selectType"))){ // 实收占比过滤 item_type=rate_payname_filter
				sb.append(" order by is_run asc, id asc ");
			} else {
				sb.append(" order by id ");
			}
		}else {
			if(cheakCode.equals("table_code")) {
				// 桌位查询
//				sb.append(" select  table_code as id, table_name as text from tables_info  where organ_id in ("+ store_ids +") ");
//				sb.append(" and table_code not in (select item_code from saas_special_filter f where f.store_id in (").append(store_ids).append(") and f.item_type = 'table_filter') ");
//				sb.append(" order by table_code ");

				sb.setLength(0);
				sb.append("select id,class_identifier_code,class_item as text from sys_dictionary where class_identifier_code = 'table_property' ");

			} else if (cheakCode.equals("pay_code")) {
				// 付款方式
				sb.setLength(0);
				sb.append(" select payment_id as id, payment_name as text from v_payment_way where 1 = 1  ");
				if ("payname_filter".equals(selectCheakCode)){ // 支付方式过滤
					sb.append(" and payment_id not in (select item_code::int from saas_special_filter f where f.store_id in (").append(store_ids).append(") and f.item_type = 'payname_filter') ");
				} else if ("rate_payname_filter".equals(selectCheakCode)) { // 实收占比过滤
					sb.setLength(0);
					sb.append(" and payment_id not in (select item_code::int from saas_special_filter f where f.store_id in (").append(store_ids).append(") and f.item_type = 'rate_payname_filter' and f.is_run = '1') ");
				}
			}
		}

		list =  this.dao.query4JSONArray(tenentid, sb.toString());

		JSONObject result = new JSONObject();


		result.put("success",true);
		result.put("msg","获取系统字典成功");
		result.put("rows",list);



		return result;
	}

	@Override
	public JSONObject deleteSpecialFilter(String tenentid, JSONObject obj) throws Exception {
		JSONObject result = new JSONObject();

		int special_info_id = obj.optInt("id");
		JSONObject spJo = this.getRuleInfo(tenentid,special_info_id);

		//删除saas_special_info saas_special_filter  saas_special_info_organ
		String saas_special_infoSql = "delete from saas_special_info where id = "+special_info_id+"";
		this.dao.execute(tenentid,saas_special_infoSql);
		String saas_special_filter_newSql = "delete from saas_special_filter where special_info_id = "+special_info_id+" ";
		this.dao.execute(tenentid,saas_special_filter_newSql);
		String saas_special_info_organSql = "delete from saas_special_info_organ where special_info_id = "+special_info_id+" ";
		this.dao.execute(tenentid,saas_special_info_organSql);


		//修改订单状态  将ab_falg设置为1
//		String updateAbFlagSql = "update pos_bill2 set ab_flag = '1' where store_id in ("+spJo.optString("store_id")+") and report_date BETWEEN '"+spJo.optString("begin_report_date")+"' and '"+spJo.optString("end_report_date")+"' ";
		String updateAbFlagSql = "";
		if("1".equals(spJo.optString("rule_type"))){
			updateAbFlagSql = "update pos_bill2 set ab_flag = null where store_id in ("+spJo.optString("store_id")+") and report_date BETWEEN '"+spJo.optString("begin_report_date")+"' and '"+spJo.optString("end_report_date")+"'  ";
			this.dao.execute(tenentid,updateAbFlagSql);
		}

		result.put("success",true);
		result.put("msg","删除规则成功");

		return result;
	}

	public  JSONObject getRuleInfo(String tenancyId,int special_info_id) throws  Exception{
		JSONObject jo = new JSONObject();
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		sb.append("select * from saas_special_info where id = "+special_info_id+" ");
		List<JSONObject> listJo = this.dao.query4Json(tenancyId,sb.toString());
		if(listJo.size()>0){
			jo = listJo.get(0);
		}

		sb.setLength(0);
		sb.append("select distinct(store_id) from saas_special_info_organ where special_info_id = "+special_info_id+" ");
		List<JSONObject> listJo2 = this.dao.query4Json(tenancyId,sb.toString());
		sb.setLength(0);
		for (int i = 0; i < listJo2.size(); i++) {
			int store_id = listJo2.get(i).optInt("store_id");
			sb.append(store_id).append(",");
		}

		if(sb.toString().length()>0){
			String store_ids = sb.toString().substring(0,sb.toString().length()-1);
			jo.put("store_id",store_ids);
		}else{
			jo.put("store_id",0);
		}

		return jo;
	}

	/**
	 * 保存账单的规则
	 * @param tenancyID
	 * @param obj
	 * @throws Exception
	 */
	public void saveBruleBill(String tenancyID,JSONObject obj,JSONObject publicparam) throws  Exception{
		List<JSONObject> addJson =new ArrayList<JSONObject>();
		String store_ids = publicparam.optString("store_id");
		JSONArray billsJa = obj.optJSONArray("bills");

		for (int k = 0; k < billsJa.size(); k++) {
			JSONObject billsJo = billsJa.optJSONObject(k);
			JSONObject json = new JSONObject();
			int store_id = billsJo.optInt("id");
			String bill_num = billsJo.optString("bill_num");
			String add_bill_time = billsJo.optString("add_bill_time");

			json.put("special_info_id",publicparam.optInt("special_info_id"));
			json.put("store_id",store_id);
			json.put("item_type",obj.optString("item_type"));
			json.put("type_name",obj.optString("type_name"));
			json.put("bill_num",bill_num);
			json.put("add_bill_time",add_bill_time);
			json.put("payment_amount",billsJo.optDouble("payment_amount"));
			json.put("report_date",billsJo.optString("report_date"));
			json.put("is_run",1);
			json.put("business_date_start",publicparam.optString("business_date_start"));
			json.put("business_date_end",publicparam.optString("business_date_end"));
			json.put("last_update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));

			addJson.add(json);
		}

		StringBuilder sql = new StringBuilder();
		sql.append(" delete from saas_special_filter where store_id in ").append("(").append(store_ids).append(")").append(" and item_type = '").append(obj.optString("item_type")).append("' ");
		this.dao.execute(tenancyID, sql.toString());

		if (addJson.size() > 0){
			this.dao.insertBatchIgnorCase(tenancyID, "saas_special_filter", addJson);
		}

	}

	/**
	 * 保存特殊场景的账单规则
	 * @param tenancyID
	 * @param obj
	 * @throws Exception
	 */
	public void saveBruleSpecialscene(String tenancyID,JSONObject obj,JSONObject publicparam) throws  Exception{
		List<JSONObject> addJson =new ArrayList<JSONObject>();
        String store_id = publicparam.optString("store_id");
        String[] store_ids = store_id.split(",");
		for (int k = 0; k < store_ids.length; k++) {
			JSONObject json = new JSONObject();
			int storeId = Integer.valueOf(store_ids[k]);

			json.put("special_info_id",publicparam.optInt("special_info_id"));
			json.put("store_id",storeId);
			json.put("item_type",obj.optString("item_type"));
			json.put("type_name",obj.optString("type_name"));
			json.put("peculiar_setup_invoice",obj.optString("peculiar_setup_invoice"));//1  代表进入B
			json.put("peculiar_setup_order",obj.optString("peculiar_setup_order"));//1  代表进入B
			json.put("is_run",1);
			json.put("business_date_start",publicparam.optString("business_date_start"));
			json.put("business_date_end",publicparam.optString("business_date_end"));
			json.put("last_update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));

			addJson.add(json);
		}

		StringBuilder sql = new StringBuilder();
		sql.append(" delete from saas_special_filter where store_id in ").append("(").append(store_id).append(")").append(" and item_type = '").append(obj.optString("item_type")).append("' ");
		this.dao.execute(tenancyID, sql.toString());

		if (addJson.size() > 0){
			this.dao.insertBatchIgnorCase(tenancyID, "saas_special_filter", addJson);
		}

	}

	/**
	 * 保存桌位、支付方式、实收占比的规则
	 * @param tenancyID
	 * @param obj
	 * @throws Exception
	 */
	public void getAssembleParams(String tenancyID,JSONObject obj,JSONObject publicparam) throws  Exception{
		String storeId = publicparam.optString("store_id");
		List<JSONObject> addJson =new ArrayList<JSONObject>();
		JSONArray ja = obj.optJSONArray("list");

		String [] storeIds =storeId.split(",");
		if(obj.optString("item_type").equals("rate_payname_filter")){
			if(obj.containsKey("amount_rate")){
				this.saveRateFilterRule(tenancyID,obj,publicparam);
			}
		}
		for (int j = 0; j < storeIds.length; j++) {
			String store_id = storeIds[j];
			for (int i = 0; i < ja.size(); i++) {
				JSONObject jo = ja.optJSONObject(i);
				String item_code = jo.optString("id");
				String item_name = jo.optString("text");

				JSONObject json =new JSONObject();
				json.put("special_info_id",publicparam.optInt("special_info_id"));
				json.put("store_id", store_id);
				json.put("item_type", obj.optString("item_type"));
				json.put("type_name", obj.optString("type_name"));
				json.put("item_name", item_name);
				json.put("item_code", item_code);
				json.put("business_date_start",publicparam.optString("begin_report_date"));
				json.put("business_date_end",publicparam.optString("end_report_date"));
				json.put("is_run", 1);
				json.put("last_update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
				addJson.add(json);
		}
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" delete from saas_special_filter where store_id in ").append("(").append(storeId).append(")").append(" and item_type = '").append(obj.optString("item_type")).append("' and  is_run = '1' ");
		this.dao.execute(tenancyID, sql.toString());

		if (addJson.size() > 0){
			this.dao.insertBatchIgnorCase(tenancyID, "saas_special_filter", addJson);
		}

//		String storeId = publicparam.optString("store_id");
//		List<JSONObject> addJson =new ArrayList<JSONObject>();
//		String [] codeArr =obj.opt("idStr").toString().split(",");
//		String [] nameArr =obj.opt("nameStr").toString().split(",");
//		String [] storeIds =storeId.split(",");
//		for (int j = 0; j < storeIds.length; j++) {
//			String store_id = storeIds[j];
//			for(int i = 0 ; i<codeArr.length ;i++) {
//				JSONObject json =new JSONObject();
//				json.put("special_info_id",publicparam.optInt("special_info_id"));
//				json.put("store_id", store_id);
//				json.put("item_type", obj.optString("item_type"));
//				json.put("type_name", obj.optString("type_name"));
//				json.put("item_name", nameArr[i]);
//				json.put("item_code", codeArr[i]);
//				json.put("business_date_start",publicparam.optString("business_date_start"));
//				json.put("business_date_end",publicparam.optString("business_date_end"));
//				if(obj.optString("item_type").equals("rate_payname_filter") && i==codeArr.length-1 ) {
//					// 当前数值加上唯一标示 2
//					json.put("is_run", 2);
//				}else {
//					json.put("is_run", 1);
//				}
//				json.put("last_update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
//				addJson.add(json);
//			}
//		}
//
//		StringBuilder sql = new StringBuilder();
//		sql.append(" delete from saas_special_filter where store_id in ").append("(").append(storeId).append(")").append(" and item_type = '").append(obj.optString("item_type")).append("' ");
//		this.dao.execute(tenancyID, sql.toString());
//
//		if (addJson.size() > 0){
//			this.dao.insertBatchIgnorCase(tenancyID, "saas_special_filter", addJson);
//		}

	}

	public void saveRateFilterRule(String tenancyID,JSONObject obj,JSONObject publicparam) throws  Exception{
		String storeId = publicparam.optString("store_id");
		List<JSONObject> addJson =new ArrayList<JSONObject>();
		String [] storeIds =storeId.split(",");

		for (int j = 0; j < storeIds.length; j++) {
			String store_id = storeIds[j];
			JSONObject json =new JSONObject();
			json.put("special_info_id",publicparam.optInt("special_info_id"));
			json.put("store_id", store_id);
			json.put("item_type", obj.optString("item_type"));
			json.put("type_name", obj.optString("type_name"));
			json.put("item_code",obj.optString("amount_rate"));
			json.put("item_name","营业实收过滤比例："+obj.optString("amount_rate")+"%");
			json.put("business_date_start",publicparam.optString("business_date_start"));
			json.put("business_date_end",publicparam.optString("business_date_end"));
			json.put("is_run", 2);
			json.put("last_update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			addJson.add(json);
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" delete from saas_special_filter where store_id in ").append("(").append(storeId).append(")").append(" and item_type = '").append(obj.optString("item_type")).append("' and is_run = '2' ");
		this.dao.execute(tenancyID, sql.toString());

		if (addJson.size() > 0){
			this.dao.insertBatchIgnorCase(tenancyID, "saas_special_filter", addJson);
		}

	}

	public void saveBruleBillamountsection(String tenancyID,JSONObject obj,JSONObject publicparam) throws  Exception{
		List<JSONObject> addJson =new ArrayList<JSONObject>();

		String storeId = publicparam.optString("store_id");
		String[] store_ids = storeId.split(",");

		for (int k = 0; k < store_ids.length; k++) {
			JSONObject json = new JSONObject();
			int store_id = Integer.valueOf(store_ids[k]);

			json.put("special_info_id",publicparam.optInt("special_info_id"));
			json.put("store_id",store_id);
			json.put("item_type",obj.optString("item_type"));
			json.put("type_name",obj.optString("type_name"));
			json.put("bill_amount_start",obj.optInt("bill_amount_start"));
			json.put("bill_amount_end",obj.optInt("bill_amount_end"));
			json.put("is_cz_bill",obj.optString("is_cz_bill"));
			json.put("is_run",1);
			json.put("business_date_start",publicparam.optString("business_date_start"));
			json.put("business_date_end",publicparam.optString("business_date_end"));
			json.put("last_update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));

			addJson.add(json);
		}

		StringBuilder sql = new StringBuilder();
		sql.append(" delete from saas_special_filter where store_id in ").append("(").append(storeId).append(")").append(" and item_type = '").append(obj.optString("item_type")).append("' ");
		this.dao.execute(tenancyID, sql.toString());

		if (addJson.size() > 0){
			this.dao.insertBatchIgnorCase(tenancyID, "saas_special_filter", addJson);
		}

	}


	public void saveBruleOpentabletimesection(String tenancyID,JSONObject obj,JSONObject publicparam) throws  Exception{
		List<JSONObject> addJson =new ArrayList<JSONObject>();

		String storeId = publicparam.optString("store_id");
		String[] store_ids = storeId.split(",");

		for (int k = 0; k < store_ids.length; k++) {
			JSONObject json = new JSONObject();
			int store_id = Integer.valueOf(store_ids[k]);

			json.put("special_info_id",publicparam.optInt("special_info_id"));
			json.put("store_id",store_id);
			json.put("item_type",obj.optString("item_type"));
			json.put("type_name",obj.optString("type_name"));
			json.put("time_interval_start",obj.optString("time_interval_start"));
			json.put("time_interval_end",obj.optString("time_interval_end"));
			json.put("is_run",1);
			json.put("business_date_start",publicparam.optString("business_date_start"));
			json.put("business_date_end",publicparam.optString("business_date_end"));
			json.put("last_update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));

			addJson.add(json);
		}

		StringBuilder sql = new StringBuilder();
		sql.append(" delete from saas_special_filter where store_id in ").append("(").append(storeId).append(")").append(" and item_type = '").append(obj.optString("item_type")).append("' ");
		this.dao.execute(tenancyID, sql.toString());

		if (addJson.size() > 0){
			this.dao.insertBatchIgnorCase(tenancyID, "saas_special_filter", addJson);
		}

	}

	public String subStringBillNum(String fromBillNum){
		String toBillNum = "";
		if(fromBillNum.length()>0){
			toBillNum =  fromBillNum.substring(0,fromBillNum.toString().length()-1);
		}
		return toBillNum;
	}

	public void getRuleBillnum(String tenancyId,JSONObject obj) throws  Exception{
		StringBuilder sqlSb = new StringBuilder();//sql
		StringBuilder paraSb = new StringBuilder();//参数
		StringBuilder aBillSb = new StringBuilder();//进入a账的账单
		StringBuilder bBillSb = new StringBuilder();//进入b账的账单
		List<String> ls = new ArrayList<String>();//
		String store_id = obj.optString("store_id");
		String begin_report_date = obj.optString("begin_report_date");
		String end_report_date = obj.optString("end_report_date");
		//先修改pos_bill2中的标志
		sqlSb.setLength(0);
		sqlSb.append("update pos_bill2 set ab_flag = null where store_id in ("+store_id+") and report_date between  '"+begin_report_date+"' and  '"+end_report_date+"' ");
		this.dao.execute(tenancyId,sqlSb.toString());

		String[] store_ids = store_id.split(",");

		for (int f = 0; f < store_ids.length; f++) {
			aBillSb.setLength(0);
			bBillSb.setLength(0);
			int storeid = Integer.valueOf(store_ids[f]);
			sqlSb.setLength(0);
			sqlSb.append("select distinct(item_type) from saas_special_filter where store_id = "+storeid+" ");
			JSONArray ruleJa = this.dao.query4JSONArray(tenancyId,sqlSb.toString());
			for (int i = 0; i < ruleJa.size(); i++) {
               String item_type = ruleJa.optJSONObject(i).optString("item_type");
				//指定账单筛选
               if(item_type.equals("bill_filter")){
				   ls.add("bill_filter");
			   }
			    //特殊场景账单
			   if(item_type.equals("special_scene_filter")){
					ls.add("special_scene_filter");
			   }
				//桌位类型过滤
			   if(item_type.equals("table_filter")){
					ls.add("table_filter");
			   }
				//支付方式过滤
               if(item_type.equals("payname_filter")){
					ls.add("payname_filter");
			   }
				//账单金额区间
			   if(item_type.equals("bill_amount_filter")){
					ls.add("bill_amount_filter");
			   }
				//开台时间区间
			   if(item_type.equals("opentable_time_filter")){
					ls.add("opentable_time_filter");
			   }
				//实收占比
			   if(item_type.equals("rate_payname_filter")){
					ls.add("rate_payname_filter");
			   }

			}

			if(ls.contains("bill_filter")){
				sqlSb.setLength(0);
				sqlSb.append("select bill_num from saas_special_filter where item_type = 'bill_filter' and store_id = "+storeid+" ");
				List<JSONObject> bfListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
				for (int i = 0; i < bfListJo.size(); i++) {
					JSONObject bfJo = bfListJo.get(i);
                    bBillSb.append("'").append(bfJo.optString("bill_num")).append("'").append(",");
				}
			}

			if(ls.contains("special_scene_filter")){
				sqlSb.setLength(0);
				sqlSb.append("select peculiar_setup_invoice,peculiar_setup_order from saas_special_filter where item_type = 'special_scene_filter' and store_id = "+storeid+" ");
				List<JSONObject> ssfListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
				JSONObject ssfJo = ssfListJo.get(0);
				if("1".equals(ssfJo.optString("peculiar_setup_invoice"))){
					sqlSb.setLength(0);
					sqlSb.append("select pb2.bill_num from pos_bill2 pb2 where (bill_num in (select bill_num from pos_bill_invoice pbi where pbi.store_id in ("+storeid+") and  pbi.report_date BETWEEN '"+begin_report_date+"' and '"+end_report_date+"') or pb2.copy_bill_num in (select bill_num from pos_bill_invoice pbi where pbi.store_id in ("+storeid+")  and  pbi.report_date BETWEEN '"+begin_report_date+"' and '"+end_report_date+"'))");
//					sqlSb.append("select pb2.bill_num from  pos_bill_invoice pbi left join pos_bill2 pb2 on pb2.bill_num = pbi.bill_num where pb2.store_id in ("+storeid+") and pb2.report_date between '"+begin_report_date+"' and '"+end_report_date+"'");
					if(bBillSb.toString().length()>0){
						sqlSb.append(" and pb2.bill_num not in ("+this.subStringBillNum(bBillSb.toString())+")");
					}
					List<JSONObject> ssfInListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
					for (int i = 0; i < ssfInListJo.size(); i++) {
						JSONObject ssfInJo = ssfInListJo.get(i);
						bBillSb.append("'").append(ssfInJo.optString("bill_num")).append("'").append(",");
					}
				}
				if("1".equals(ssfJo.optString("peculiar_setup_order"))){
					sqlSb.setLength(0);
//					sqlSb.append("select pb2.bill_num from pos_bill2 pb2 where (pb2.bill_num in (select col.bill_num from cc_order_list col where col.store_id in ("+storeid+") and col.report_date between '"+begin_report_date+"' and '"+end_report_date+"' and (col.bill_num IS NOT NULL AND col.bill_num <> '')) or pb2.copy_bill_num in (select col.bill_num from cc_order_list col where col.store_id in ("+storeid+") and col.report_date between '"+begin_report_date+"' and '"+end_report_date+"' and (col.bill_num IS NOT NULL AND col.bill_num <> '')))  ");
					sqlSb.append("select pb3.bill_num from pos_bill2 pb3 where (pb3.bill_num in ( select pb2.bill_num from  cc_order_list col LEFT JOIN  pos_bill2 pb2 on pb2.bill_num = col.bill_num where pb2.store_id in ("+storeid+") and pb2.report_date BETWEEN '"+begin_report_date+"' and '"+end_report_date+"') or pb3.copy_bill_num in ( select pb2.bill_num from  cc_order_list col LEFT JOIN  pos_bill2 pb2 on pb2.bill_num = col.bill_num where pb2.store_id in ("+storeid+") and pb2.report_date BETWEEN '"+begin_report_date+"' and '"+end_report_date+"') ) ");
					if(bBillSb.toString().length()>0){
						sqlSb.append(" and pb3.bill_num not in ("+this.subStringBillNum(bBillSb.toString())+")");
					}
					List<JSONObject> ssfOrListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
					for (int i = 0; i < ssfOrListJo.size(); i++) {
						JSONObject ssfOrJo = ssfOrListJo.get(i);
						bBillSb.append("'").append(ssfOrJo.optString("bill_num")).append("'").append(",");
					}
				}
			}

			if(ls.contains("table_filter")){
				String table_id = "";
				paraSb.setLength(0);
				sqlSb.setLength(0);
				sqlSb.append("select item_code as table_id from saas_special_filter where item_type = 'table_filter' and store_id = "+storeid+"");
				List<JSONObject> tfParamListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
				for (int i = 0; i < tfParamListJo.size(); i++) {
					paraSb.append(Integer.valueOf(tfParamListJo.get(i).optString("table_id"))).append(",");
				}
				if(paraSb.toString().length()>0){
					table_id = this.subStringBillNum(paraSb.toString());
				}
				if(ls.contains("payname_filter")||ls.contains("bill_amount_filter")||ls.contains("opentable_time_filter")){
					if(table_id!=null&&!StringUtils.isBlank(table_id)){
						sqlSb.setLength(0);
						sqlSb.append("select bill_num from pos_bill2 where store_id in ("+storeid+") and report_date between '"+begin_report_date+"' and '"+end_report_date+"' and   table_code in (select table_code from tables_info where table_property_id in ("+table_id+") and organ_id in ("+storeid+") ) ");
						List<JSONObject> aBillListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
						for (int i = 0; i < aBillListJo.size(); i++) {
							aBillSb.append("'").append(aBillListJo.get(i).optString("bill_num")).append("'").append(",");
						}
					}
				}else{
					if(table_id!=null&&!StringUtils.isBlank(table_id)){
						sqlSb.setLength(0);
						sqlSb.append("select bill_num from pos_bill2 where store_id in ("+storeid+") and report_date between '"+begin_report_date+"' and '"+end_report_date+"' and  bill_num not in (select bill_num from pos_bill2 where store_id in ("+storeid+") and report_date between '"+begin_report_date+"' and '"+end_report_date+"'  and  table_code in (select table_code from tables_info where table_property_id in ("+table_id+") and organ_id in ("+storeid+") )) ");
						if(bBillSb.toString().length()>0){
							sqlSb.append(" and bill_num not in ("+this.subStringBillNum(bBillSb.toString())+")");
						}
						List<JSONObject> tfBListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
						for (int i = 0; i < tfBListJo.size(); i++) {
							bBillSb.append("'").append(tfBListJo.get(i).optString("bill_num")).append("'").append(",");
						}
					}
				}
			}

			if(ls.contains("payname_filter")){
				String jzids = "";
				paraSb.setLength(0);
				sqlSb.setLength(0);
				sqlSb.append("select item_code as jzid from saas_special_filter where item_type = 'payname_filter' and store_id = "+storeid+" ");
				List<JSONObject> pfParamListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
				for (int i = 0; i < pfParamListJo.size(); i++) {
					paraSb.append(Integer.valueOf(pfParamListJo.get(i).optString("jzid"))).append(",");
				}
				if(paraSb.toString().length()>0){
					jzids = this.subStringBillNum(paraSb.toString());
				}
				if(ls.contains("bill_amount_filter")||ls.contains("opentable_time_filter")){
					sqlSb.setLength(0);
					sqlSb.append("select distinct(bill_num) from pos_bill_payment2 where store_id in ("+storeid+") and jzid  in ("+jzids+")  and report_date between '"+begin_report_date+"' and '"+end_report_date+"' ");
					List<JSONObject> pfAListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
					for (int i = 0; i < pfAListJo.size(); i++) {
						aBillSb.append("'").append(pfAListJo.get(i).optString("bill_num")).append("'").append(",");
					}
				}else{
					sqlSb.setLength(0);
					sqlSb.append("select bill_num from pos_bill2 where store_id in ("+storeid+")  and report_date between '"+begin_report_date+"' and '"+end_report_date+"' and bill_num not in (select distinct(bill_num) from pos_bill_payment2 where store_id in ("+storeid+") and jzid in ("+jzids+") and report_date between '"+begin_report_date+"' and '"+end_report_date+"') ");
					if(bBillSb.toString().length()>0){
						sqlSb.append(" and bill_num not in ("+this.subStringBillNum(bBillSb.toString())+")");
					}
					if(aBillSb.toString().length()>0){
						sqlSb.append(" and bill_num not in ("+this.subStringBillNum(aBillSb.toString())+")");
					}
					List<JSONObject> pfBListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
					for (int i = 0; i < pfBListJo.size(); i++) {
						bBillSb.append("'").append(pfBListJo.get(i).optString("bill_num")).append("'").append(",");
					}
				}
			}

			if(ls.contains("bill_amount_filter")){
				sqlSb.setLength(0);
				sqlSb.append("select bill_amount_start,bill_amount_end,is_cz_bill from saas_special_filter where item_type = 'bill_amount_filter' and store_id = "+storeid+" ");
				List<JSONObject> bafListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
				JSONObject bafJo = bafListJo.get(0);
				if("1".equals(bafJo.optString("is_cz_bill"))){
					sqlSb.setLength(0);
					sqlSb.append("select aa.bill_num from (SELECT bill_num FROM pos_bill2 WHERE store_id IN ("+storeid+") AND (report_date between  '"+begin_report_date+"' and '"+end_report_date+"') AND payment_amount between "+bafJo.optDouble("bill_amount_start")+" and "+bafJo.optDouble("bill_amount_end")+"  UNION  SELECT bill_num FROM pos_bill2 WHERE store_id IN ("+storeid+") AND (report_date between  '"+begin_report_date+"' and '"+end_report_date+"') and copy_bill_num in (SELECT bill_num FROM pos_bill2 WHERE store_id IN ("+storeid+") AND (report_date between  '"+begin_report_date+"' and '"+end_report_date+"') AND payment_amount between "+bafJo.optDouble("bill_amount_start")+" and "+bafJo.optDouble("bill_amount_end")+"  ) ) as aa");
					if(aBillSb.toString().length()>0||bBillSb.toString().length()>0){
						sqlSb.append(" where 1=1 ");
					}
					if(aBillSb.toString().length()>0){
						sqlSb.append(" and aa.bill_num not in ("+this.subStringBillNum(aBillSb.toString())+") ");
					}
					if(bBillSb.toString().length()>0){
						sqlSb.append(" and aa.bill_num not in ("+this.subStringBillNum(bBillSb.toString())+") ");
					}
					List<JSONObject> bafBListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
					for (int i = 0; i < bafBListJo.size(); i++) {
						bBillSb.append("'").append(bafBListJo.get(i).optString("bill_num")).append("'").append(",");
					}
				}else{
					sqlSb.setLength(0);
					sqlSb.append("select bill_num from pos_bill2 where store_id in ("+storeid+") and (report_date between  '"+begin_report_date+"' and '"+end_report_date+"') and  payment_amount between "+bafJo.optDouble("bill_amount_start")+" and "+bafJo.optDouble("bill_amount_end")+" ");
					if(aBillSb.toString().length()>0){
						sqlSb.append(" and bill_num not in ("+this.subStringBillNum(aBillSb.toString())+") ");
					}
					if(bBillSb.toString().length()>0){
						sqlSb.append(" and bill_num not in ("+this.subStringBillNum(bBillSb.toString())+") ");
					}
					List<JSONObject> bafBListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
					for (int i = 0; i < bafBListJo.size(); i++) {
						bBillSb.append("'").append(bafBListJo.get(i).optString("bill_num")).append("'").append(",");
					}
				}
			}

			if(ls.contains("opentable_time_filter")){
				sqlSb.setLength(0);
				sqlSb.append("select time_interval_start,time_interval_end from saas_special_filter where item_type = 'opentable_time_filter' and store_id = "+storeid+" ");
				List<JSONObject> otfParamListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
				JSONObject otfParamJo = otfParamListJo.get(0);
				sqlSb.setLength(0);
				sqlSb.append("with tt as (select to_char(opentable_time, 'HH24:MI') as report_date_time,bill_num from pos_bill2 where store_id in ("+storeid+") and (report_date between  '"+begin_report_date+"' and '"+end_report_date+"') )   select t.bill_num from tt t where t.report_date_time between '"+otfParamJo.optString("time_interval_start")+"' and '"+otfParamJo.optString("time_interval_end")+"' ");
				if(aBillSb.toString().length()>0){
					sqlSb.append(" and bill_num not in ("+this.subStringBillNum(aBillSb.toString())+") ");
				}
				if(bBillSb.toString().length()>0){
					sqlSb.append(" and bill_num not in ("+this.subStringBillNum(bBillSb.toString())+") ");
				}
				List<JSONObject> otfListJo = this.dao.query4Json(tenancyId,sqlSb.toString());
				for (int i = 0; i < otfListJo.size(); i++) {
					bBillSb.append("'").append(otfListJo.get(i).optString("bill_num")).append("'").append(",");
				}
			}


			if(bBillSb.toString().length()>0){
				sqlSb.setLength(0);
				if("2".equals(obj.optString("rule_type"))){
					sqlSb.append("update pos_bill2 set ab_flag = '1'  where  store_id in ("+storeid+") and  report_date between  '"+begin_report_date+"' and '"+end_report_date+"' and bill_num  not in ("+this.subStringBillNum(bBillSb.toString())+") ");
				}else if("1".equals(obj.optString("rule_type"))){//普通规则
					sqlSb.append("update pos_bill2 set ab_flag = '-1'  where  store_id in ("+storeid+") and  report_date between  '"+begin_report_date+"' and '"+end_report_date+"' and bill_num  not in ("+this.subStringBillNum(bBillSb.toString())+") ");
				}
				this.dao.execute(tenancyId,sqlSb.toString());
			}else{
				sqlSb.setLength(0);
				if("1".equals(obj.optString("rule_type"))){//普通规则
					sqlSb.append("update pos_bill2 set ab_flag = '-1'  where  store_id in ("+storeid+") and  report_date between  '"+begin_report_date+"' and '"+end_report_date+"' ");
				}
				this.dao.execute(tenancyId,sqlSb.toString());
			}

		}

    }

}
