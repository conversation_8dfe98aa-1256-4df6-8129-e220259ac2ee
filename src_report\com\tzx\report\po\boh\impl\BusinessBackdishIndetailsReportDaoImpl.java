package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.BusinessBackdishIndetailsReportDao;

@Repository(BusinessBackdishIndetailsReportDao.NAME)
public class BusinessBackdishIndetailsReportDaoImpl implements BusinessBackdishIndetailsReportDao{

	// 按照日期查询
	private final String selectDateItme0 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_40' and sql_type='MXL0'";
	private final String selectDateItme1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_40' and sql_type='MXL1'";
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;

	@Override
	public JSONObject getBusinessInformation(String tenancyID, JSONObject condition) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		String begindate = condition.optString("begin_date");
		String enddate = condition.optString("end_date");
		String reportSql = "";
		long total = 0L;
		if(begindate.length()>0 && enddate.length()>0 )
		{
			reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDateItme1);
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
				list = this.dao.query4Json(tenancyID,reportSql.toString());
				structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
			}else{
				total = this.dao.countSql(tenancyID,reportSql.toString());
				list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
			}
			//合计
			footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDateItme0));
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("structure", structure);
		result.put("footer", footerList);
		return result;
	}

 
}
