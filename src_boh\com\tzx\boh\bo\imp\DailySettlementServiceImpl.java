
package com.tzx.boh.bo.imp;

import com.tzx.boh.bo.DailySettlementService;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ParameterUtils;
import net.sf.json.JSONObject;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.*;

@Service(DailySettlementService.NAME)
public class DailySettlementServiceImpl implements DailySettlementService
{
	private final String sqlType = "select sql from saas_report_engine where report_name = '机构日营业汇总表'";
	private final String sqlTypePaymentCount = "select sql from saas_report_engine where report_name = '机构付款方式汇总表'";
	private final String sqlTypeDaycountShift = "select sql from saas_report_engine where report_name = '机构日营业班次汇总表'";
	private final String sqlTypeDaycountTimes = "select sql from saas_report_engine where report_name = '机构日营业时段汇总表'";
	private final String sqlTypePaymentcountShift = "select sql from saas_report_engine where report_name = '机构付款方式班次汇总表'";
	private final String sqlTypePaymentcountTimes = "select sql from saas_report_engine where report_name = '机构付款方式时段汇总表'";
    private final String hqDaycountItem = "select sql from saas_report_engine where report_name = '机构菜品汇总表' and sql_type='L1'";
    private final String hqDaycountItemUpdate = "select sql from saas_report_engine where report_name = '机构菜品汇总表' and sql_type='L2'";
    private final String hqDaycountItemTimes = "select sql from saas_report_engine where report_name = '机构菜品时段汇总表' and sql_type='L1'";
    private final String hqDaycountItemTimesUpdate = "select sql from saas_report_engine where report_name = '机构菜品时段汇总表' and sql_type='L2'";

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;

	@Override
	public JSONObject findDailySettlementStatus(String tenantid, JSONObject param) throws Exception
	{
		String sql = "select * from hq_daycount_info where store_id=? and day_count=?";
		List<JSONObject> list = dao.query4Json(tenantid, sql, new Object[]
				{ param.optInt("store_id"), Date.valueOf(param.optString("day_count")) });

		if (list.size() > 0)
		{
			return list.get(0);
		}

		return new JSONObject();
	}

    @Override
    public JSONObject getCrossValue(String tenentid, String store_id) throws Exception {

        StringBuilder sql = new StringBuilder();

        sql.append(" select\n");
        sql.append("   substr(remark, 5) as remark \n");
        sql.append(" from pos_opt_state po\n");
        sql.append(" where 1=1\n");
        sql.append(" and po.store_id=" + store_id + "\n");
        sql.append(" and po.report_date>(select max(hd.day_count) from hq_daycount_info hd where hd.store_id=" + store_id + " and hd.finish_sign='1')\n");
        sql.append(" and po.content='DAYBEGAIN'\n");
        sql.append(" and left(po.remark, 4)='跨天营业'\n");
        sql.append(" order by po.report_date limit 1\n");

        List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());

        if (list != null && list.size() > 0) {

            JSONObject json = list.get(0);
            String remark = json.optString("remark");

            if(remark.length() > 0){
                return JSONObject.fromObject(remark);
            }else{
                return null;
            }
        }

        return null;
    }

    @Override
	public void dailySettlement(String tenantid, JSONObject param) throws Exception
	{

		String sql = "select * from hq_daycount_info where store_id=? and day_count=?";
		List<JSONObject> list = dao.query4Json(tenantid, sql, new Object[]
				{ param.optInt("store_id"), Date.valueOf(param.optString("day_count")) });

		JSONObject ds = null;

		if (list.size() > 0)
		{
			ds = list.get(0);
		}
		else
		{
			sql = "insert into hq_daycount_info(tenancy_id,store_id,day_count,operator,operate_time) values('" + tenantid + "'," + param.optInt("store_id") + ",'" + Date.valueOf(param.optString("day_count")) + "','" + param.optString("employeeName") + "',CURRENT_TIMESTAMP)";
			dao.execute(tenantid, sql);

			ds = new JSONObject();
		}

        boolean isOpenHq = isOpenHq(tenantid, param);
        boolean isCountCrm = isCountCrm(tenantid, param);
        boolean isOpenScm = isOpenSCM(tenantid, param);

		String dstype = param.optString("dstype");
		if ("hr".equalsIgnoreCase(dstype) && !"1".equals(ds.optString("hr_sign")))
		{
			hrDailySettlement(tenantid, param);
			ds.element("hr_sign", param.optString("status"));
		}
		else if ("energy".equalsIgnoreCase(dstype) && !"1".equals(ds.optString("energy_sign")))
		{
			energyDailySettlement(tenantid, param);
			ds.element("energy_sign", param.optString("status"));
		}
		else if ("cost".equalsIgnoreCase(dstype) && !"1".equals(ds.optString("cost_sign")))
		{
			costDailySettlement(tenantid, param);
			ds.element("cost_sign", param.optString("status"));
		}
		else if ("cash".equalsIgnoreCase(dstype) && !"1".equals(ds.optString("cash_sign")))
		{
			cashDailySettlement(tenantid, param);
			ds.element("cash_sign", param.optString("status"));
		}
		else if ("hq".equalsIgnoreCase(dstype) && !"1".equals(ds.optString("hq_sign")))
		{
//			 hqDailySettlement(tenantid, param);

			if(isOpenHq)
			{
				//if (getScmDaDate(tenantid, param) != null)
				//{
				//	if (!isDataFinish(tenantid, param))
				//	{
				//		throw new Exception("该机构没有在日期为：【" + param.optString("day_count") + "】进行数据整理，禁止日结");
				//	}
				//}

				newHqDailySettlement(tenantid, param);
				ds.element("hq_sign", param.optString("status"));
			}
			else
			{
				dao.execute(tenantid, "update hq_daycount_info set hq_sign='0' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'");
			}

		}
		else if ("scm".equalsIgnoreCase(dstype) && !"1".equals(ds.optString("scm_sign")))
		{
			if(isOpenHq && !"1".equals(ds.optString("hq_sign"))){
				throw new Exception("请先日结营业部分数据!");
			}
			scmDailySettlement(tenantid, param);
			ds.element("scm_sign", param.optString("status"));
		}
		else if ("crm".equalsIgnoreCase(dstype) && !"1".equals(ds.optString("crm_sign")))
		{
			if(isOpenHq && !"1".equals(ds.optString("hq_sign"))){
				throw new Exception("请先日结营业部分数据!");
			}
			crmDailySettlement(tenantid, param);
			ds.element("crm_sign", param.optString("status"));
		}


		//String[] items = new String[]
		//		{ "hr_sign", "energy_sign", "cost_sign", "cash_sign", "hq_sign", "scm_sign", "crm_sign"};

        List<String> items = new ArrayList<>();
        items.add("hr_sign");
        if(isOpenHq){ items.add("hq_sign");}
        if(isOpenScm){ items.add("scm_sign");}
        if(isCountCrm){ items.add("crm_sign");}

		boolean isFinish = true;
		for (String itm : items)
		{
			if ("".equals(ds.optString(itm)) || "null".equals(ds.optString(itm)))
			{
				isFinish = false;
				break;
			}
		}
		if (isFinish)
		{
			sql = "update hq_daycount_info set finish_sign='1' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
			dao.execute(tenantid, sql);
		}
	}

	@SuppressWarnings("null")
	@Override
	public void batchDailySettlement(String tenantid, JSONObject param) throws Exception {

        // 准备数据 (store_id, day_count, dstype)
        //String [] typeArr = new String[]
        //        { "hr", "energy", "cost", "cash", "hq", "scm", "crm"};
		String [] typeArr = param.optString("dstype").split(",");

		if(typeArr == null && typeArr.length == 0) {
			throw new Exception("无日结业务!");
		}

        String startDateStr = param.optString("start_date");
        String endDateStr = param.optString("end_date");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();

        java.util.Date startDate = sdf.parse(startDateStr);
        java.util.Date endDate = sdf.parse(endDateStr);

        calendar.setTime(startDate);

        // 遍历日期
        for (;startDate.compareTo(endDate) <= 0;){

            param.element("day_count", sdf.format(startDate));
            mockDataSorting(tenantid, param);
            // 遍历日结数据类型
            for(String type : typeArr){

                param.element("dstype", type);

                // 执行日结
                dailySettlement(tenantid, param);
            }

            calendar.add(Calendar.DATE, 1);
            startDate = calendar.getTime();
        }

	}

    /**
     * 数据整理 -- 手工插数据
     * @param tenantid
     * @param obj
     * @throws Exception
     */
    private void mockDataSorting(String tenantid, JSONObject obj) throws Exception {

        // 判断是否已经日结
        SqlRowSet rs = null;
        StringBuilder sql = new StringBuilder();
        sql.append(" select i.scm_sign,i.hq_sign from hq_daycount_info i where i.tenancy_id='");
        sql.append(tenantid);
        sql.append("' and i.store_id=");
        sql.append(obj.optInt("store_id"));
        sql.append(" and i.day_count='");
        sql.append(obj.optString("day_count"));
        sql.append("'");
        rs = this.dao.query(tenantid, sql.toString());
        if(rs!=null && rs.next()) {
            if("1".equals(rs.getString("scm_sign"))) {
                return ;
            }
        }

        // 手工插数据 -- 空数据整理
        sql.setLength(0);
        sql.append(" select id as warehouse_id,store_id from scm_warehouse w where w.warehouse_type!='warehouse' and w.valid_state = '1' and w.store_id = " + obj.optInt("store_id"));
        List<JSONObject> warehouseList = this.dao.query4Json(tenantid, sql.toString());
        for (JSONObject j : warehouseList) {
            j.put("tenancy_id", tenantid);
            j.put("cost_count_date", obj.optString("day_count"));
            j.put("is_cost_acount", "0");
            j.put("day_end", "1");
        }
        sql.setLength(0);
        sql.append(" delete from scm_storage_day_acount s where s.store_id=");
        sql.append(obj.optInt("store_id"));
        sql.append(" and s.cost_count_date='");
        sql.append(obj.optString("day_count"));
        sql.append("'");
        this.dao.execute(tenantid, sql.toString());
        this.dao.insertBatchIgnorCase(tenantid, "scm_storage_day_acount", warehouseList);

    }

	private boolean isOpenHq(String tenantId, JSONObject condition) throws Exception {

		StringBuilder sql = new StringBuilder();

		sql.append(" select");
		sql.append("  sp.para_value");
		sql.append(" from sys_parameter sp");
		sql.append(" where sp.system_name='hq' and sp.para_code='openhq' and sp.valid_state='1'");
		sql.append(" and (sp.store_id=0");

		if(condition.optString("store_id").length() > 0)
		{
			sql.append("or sp.store_id=" + condition.optString("store_id") + ")");
		}
		else
		{
			sql.append(")");
		}

		sql.append("  order by sp.store_id desc");

		List<JSONObject> list = this.dao.query4Json(tenantId, sql.toString());

		if(list.size() > 0 && "1".equals(list.get(0).optString("para_value").trim())){
			return true;
		}

		return false;
	}

	/**
	 * 是否统计会员数据
	 *
	 * @param tenantId
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	private boolean isCountCrm(String tenantId, JSONObject condition) throws Exception {

		StringBuilder sql = new StringBuilder();

		sql.append(" select");
		sql.append("  sp.para_value");
		sql.append(" from sys_parameter sp");
		sql.append(" where sp.system_name='hq' and sp.para_code='countcrm' and sp.valid_state='1'");
		sql.append(" and (sp.store_id=0");

		if(condition.optString("store_id").length() > 0)
		{
			sql.append("or sp.store_id=" + condition.optString("store_id") + ")");
		}
		else
		{
			sql.append(")");
		}

		sql.append("  order by sp.store_id desc");

		List<JSONObject> list = this.dao.query4Json(tenantId, sql.toString());

		if(list.size() > 0 && "1".equals(list.get(0).optString("para_value").trim())){
			return true;
		}

		return false;
	}

    public boolean isOpenSCM(String tenantId, JSONObject param) {
        StringBuilder sql = new StringBuilder();
        sql.append("select init_date,da_date from scm_organ where tenancy_id='" + tenantId + "' and store_id=" + param.optString("store_id") + " and is_init=1");
        try {
            List<JSONObject> list = dao.query4Json(tenantId, sql.toString());
            if(list != null && list.size() > 0){
                return true;
            }
        } catch (Exception e) {
           return false;
        }

        return false;
    }

	/**
	 * 人事
	 *
	 * @param tenantid
	 * @param param
	 * @throws Exception
	 */
	private void hrDailySettlement(String tenantid, JSONObject param) throws Exception
	{
		String sql = "select id from boh_employee_changes where (store_id=? and change_state='cs02') or (store_befor=? and change_state='cs02')";
		List<JSONObject> list = dao.query4Json(tenantid, sql, new Object[]
				{ param.optInt("store_id"), param.optInt("store_id") });

		if (list.size() > 0)
		{
			throw new Exception("人员调入或调出中，存在【待接收】的人员，请处理后再日结！");
		}

		sql = "update hq_daycount_info set hr_sign='1' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
		dao.execute(tenantid, sql);

		param.element("status", "1");
	}

	/**
	 * 水电气数据
	 *
	 * @param tenantid
	 * @param param
	 * @throws Exception
	 */
	private void energyDailySettlement(String tenantid, JSONObject param) throws Exception
	{
		String sql = "select id from boh_water_utility_record where store_id=? and business_date=?";

		List<JSONObject> list = dao.query4Json(tenantid, sql, new Object[]
				{ param.optInt("store_id"), Date.valueOf(param.optString("day_count")) });

		String status = "0";
		if (list.size() > 0)
		{
			status = "1";
		}
		param.element("status", status);

		sql = "update hq_daycount_info set energy_sign='" + status + "' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
		dao.execute(tenantid, sql);
	}

	/**
	 * 费用
	 *
	 * @param tenantid
	 * @param param
	 * @throws Exception
	 */
	private void costDailySettlement(String tenantid, JSONObject param) throws Exception
	{
		String sql = "select id from boh_inout_info where store_id=? and business_date=?";

		List<JSONObject> list = dao.query4Json(tenantid, sql, new Object[]
				{ param.optInt("store_id"), Date.valueOf(param.optString("day_count")) });

		String status = "0";
		if (list.size() > 0)
		{
			status = "1";
		}
		param.element("status", status);

		sql = "update hq_daycount_info set cost_sign='" + status + "' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
		dao.execute(tenantid, sql);
	}

	/**
	 * 现金
	 *
	 * @param tenantid
	 * @param param
	 * @throws Exception
	 */
	private void cashDailySettlement(String tenantid, JSONObject param) throws Exception
	{
		String sql = "select id from boh_bank_deposit where store_id=? and business_date=?";

		List<JSONObject> list = dao.query4Json(tenantid, sql, new Object[]
				{ param.optInt("store_id"), Date.valueOf(param.optString("day_count")) });

		String status = "0";
		if (list.size() > 0)
		{
			status = "1";
		}
		param.element("status", status);

		sql = "update hq_daycount_info set cash_sign='" + status + "' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
		dao.execute(tenantid, sql);
	}

	/**
	 * 营业数据
	 *
	 * @param tenantid
	 * @param param
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private void hqDailySettlement(String tenantid, JSONObject param) throws Exception
	{
		int store_id = param.optInt("store_id");
		Date day_count = Date.valueOf(param.optString("day_count"));
		// 更改状态-正在统计
		String sql = "update hq_daycount_info set hq_sign='100' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
		dao.execute(tenantid, sql);

		sql = "select remark from pos_opt_state where store_id=? and report_date=? and content='DAYEND'";
		List<JSONObject> list = dao.query4Json(tenantid, sql, new Object[]
				{ store_id, day_count });

		if (list.size() == 0)
		{
			sql = "update hq_daycount_info set hq_sign=null where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
			dao.execute(tenantid, sql);
			throw new Exception("门店未打烊，不能进行日结操作！");
		}

		StringBuilder mqsql = new StringBuilder("select 'billCount' as k, count(*) as v from pos_bill where report_date='" + param.optString("day_count") + "' and bill_property='CLOSED' and store_id=" + store_id + " union all");
		mqsql.append(" select 'billItemsCount' as k, count(*) as v from pos_bill_item where report_date='" + param.optString("day_count") + "' and store_id=" + store_id + " union all");
		mqsql.append(" select 'paymentCount' as k, count(*) as v from pos_bill_payment where report_date='" + param.optString("day_count") + "' and store_id=" + store_id + " union all");
		mqsql.append(" select 'paymentAmount' as k, sum(payment_amount) as v from pos_bill where report_date='" + param.optString("day_count") + "' and bill_property='CLOSED' and store_id=" + store_id);

		JSONObject mqObj = new JSONObject();
		SqlRowSet mqRs = dao.query(tenantid, mqsql.toString());
		while (mqRs.next())
		{
			mqObj.put(mqRs.getString("k"), mqRs.getDouble("v"));
		}

		JSONObject remark = JSONObject.fromObject(list.get(0).optString("remark").replace("$$$", ""));

		if (mqObj.optInt("billCount") == remark.optInt("billCount") && mqObj.optInt("billItemsCount") == remark.optInt("billItemsCount") && mqObj.optInt("paymentCount") == remark.optInt("paymentCount") && mqObj.optInt("paymentAmount") == remark.optInt("paymentAmount"))
		{

			// 临时性解决措施，修复门店错误的支付记录数据
			String repairPayment = "update pos_bill_payment p set currency_amount=round((p.amount * (case when p.rate=0 or p.rate is null then 1 else p.rate end)),2) where p.bill_num in (select bill_num from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count")
					+ "' and bill_property='CLOSED')";

			// 导入2
			String insertPosBill = "insert into pos_bill2 (tenancy_id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time,payment_num,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id,service_amount,service_discount,order_num,print_time,print_count,subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,discount_rate,billfree_reason_id,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,deposit_count,copy_bill_num,source,opt_login_number,guest_msg,integraloffset,remark,return_amount,advance_payment_amt,advance_refund_amt,is_refund,payment_id,pay_no,third_bill_code,payment_state,payment_manager_num,shop_real_amount,total_fees,platform_charge_amount,tax_rate,tax_money,service_tax_rate,service_tax_money,tax_amount,no_tax_amount,payment_tax_money, payment_notax,bill_tax_money,bill_notax,service_notax,settlement_price) select tenancy_id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time,payment_num,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id,service_amount,service_discount,order_num,print_time,print_count,subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,discount_rate,billfree_reason_id,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,deposit_count,copy_bill_num,source,opt_login_number,guest_msg,integraloffset,remark,return_amount,advance_payment_amt,advance_refund_amt,is_refund,payment_id,pay_no,third_bill_code,payment_state,payment_manager_num,shop_real_amount,total_fees,platform_charge_amount,tax_rate,tax_money,service_tax_rate,service_tax_money,tax_amount,no_tax_amount,payment_tax_money, payment_notax,bill_tax_money,bill_notax,service_notax,settlement_price from pos_bill where store_id="
					+ store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED' order by id";
			String insertPosBillItem = "insert into pos_bill_item2 (tenancy_id,store_id,rwid,yrwid,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_remark,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,report_date,item_shift_id,item_mac_id,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag,assist_item_id,integraloffset,remark,setmeal_group_id,group_id,third_price,returngive_reason_id,manager_num,return_type,return_count,method_money,batch_num,tax_rate,tax_money,item_notax,payment_tax_money,payment_notax,opt_num,item_remark_his,is_consignment,settlement_price) select d.tenancy_id, d.store_id, d.rwid, d.yrwid, d.bill_num, d.details_id, d.item_id, d.item_num, d.item_name, d.item_english, d.item_unit_id, d.item_unit_name, d.stable_code, d.table_code, d.pushmoney_way, d.proportion, d.assist_num, d.assist_money, d.waiter_num, d.item_price, d.item_count, d.item_amount, d.real_amount, d.discount_amount, d.single_discount_amount, d.discountr_amount, d.discount_state, d.discount_rate, d.discount_mode_id, d.item_class_id, d.item_property, d.item_remark, d.print_tag, d.waitcall_tag, d.setmeal_id, d.setmeal_rwid, d.is_setmeal_changitem, d.item_time, d.item_serial, d.report_date, d.item_shift_id, d.item_mac_id, d.item_taste, d.order_remark, d.seat_num, d.ticket_num, d.sale_mode, d.gqcj_tag, d.kvscp_tag, d.discount_reason_id, d.is_showitem, d.upload_tag, d.assist_item_id, d.integraloffset, d.remark, d.setmeal_group_id, d.group_id, d.third_price, d.returngive_reason_id, d.manager_num, d.return_type, d.return_count, d.method_money, d.batch_num,d.tax_rate,d.tax_money,d.item_notax,d.payment_tax_money,d.payment_notax,d.opt_num,d.item_remark_his,d.is_consignment,d.settlement_price from pos_bill_item d left join pos_bill m on d.bill_num=m.bill_num where m.store_id="
					+ store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED' order by d.rwid";
			String insertPosBillPayment = "insert into pos_bill_payment2 (tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,param_cach,more_coupon,batch_num,fee,fee_rate) select d.tenancy_id, d.store_id, d.bill_num, d.table_code, d.type, d.jzid, d.name, d.name_english, d.amount, d.count, d.number, d.phone, d.report_date, d.shift_id, d.pos_num, d.cashier_num, d.last_updatetime, d.is_ysk, d.rate, d.currency_amount, d.upload_tag, d.customer_id, d.bill_code, d.remark,d.payment_state,d.param_cach,d.more_coupon,d.batch_num,d.fee,d.fee_rate from pos_bill_payment d left join pos_bill m on d.bill_num=m.bill_num where m.store_id="
					+ store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED' order by d.id";

			// 统计

			// 异常原因统计
			StringBuilder unusualReason = new StringBuilder();
			unusualReason.append("insert into hq_daycount_unusual(tenancy_id,store_id,day_count,unusual_type,unusual_id,bill_num,bill_money,unusual_money,remark)");
			unusualReason.append(" select '"
					+ tenantid
					+ "' as tenancy_id, "
					+ store_id
					+ " as store_id, to_date('"
					+ param.optString("day_count")
					+ "','YYYY-MM-DD') as day_count,r1.unusual_type as unusual_type, m1.billfree_reason_id as unusual_id,count(m1.bill_num) as bill_num, sum(m1.bill_amount) as bill_money , sum(m1.bill_amount) as unusual_money,'免单原因统计' as remark from pos_bill m1 left join hq_unusual_reason r1 on m1.billfree_reason_id=r1.id where m1.billfree_reason_id is not null and m1.billfree_reason_id !=0 and m1.store_id="
					+ store_id + " and m1.report_date='" + param.optString("day_count") + "' and m1.bill_property='CLOSED' group by m1.billfree_reason_id, r1.unusual_type");
			unusualReason.append(" union all");
			unusualReason.append(" select '"
					+ tenantid
					+ "' as tenancy_id, "
					+ store_id
					+ " as store_id, to_date('"
					+ param.optString("day_count")
					+ "','YYYY-MM-DD') as day_count,m.unusual_type,m.unusual_id,count(m.bill_num) as bill_num,sum(m.bill_amount) as bill_money , sum(m.unusual_money) as unusual_money,'退菜奉送原因统计' as remark from (select r1.unusual_type as unusual_type,d1.reason_id as unusual_id,m1.bill_num,m1.bill_amount , sum(d1.amount) as unusual_money from pos_returngive_item d1 join pos_bill m1 on d1.bill_num=m1.bill_num left join hq_unusual_reason r1 on d1.reason_id=r1.id where d1.reason_id is not null and d1.reason_id !=0 and m1.store_id="
					+ store_id + " and m1.report_date='" + param.optString("day_count") + "' and m1.bill_property='CLOSED' group by m1.bill_num,m1.bill_amount,d1.reason_id, r1.unusual_type) m group by m.unusual_id, m.unusual_type");
			unusualReason.append(" union all");
			unusualReason.append(" select '"
					+ tenantid
					+ "' as tenancy_id, "
					+ store_id
					+ " as store_id, to_date('"
					+ param.optString("day_count")
					+ "','YYYY-MM-DD') as day_count,m.unusual_type,m.unusual_id,count(m.bill_num) as bill_num,sum(m.bill_amount) as bill_money , sum(m.unusual_money) as unusual_money,'折扣原因统计' as remark from (select r1.unusual_type as unusual_type,d1.discount_reason_id as unusual_id,m1.bill_num,m1.bill_amount , (sum(d1.discount_amount) + sum(d1.discountr_amount)) as unusual_money from pos_bill_item d1 join pos_bill m1 on d1.bill_num=m1.bill_num left join hq_unusual_reason r1 on d1.discount_reason_id=r1.id where d1.discount_reason_id is not null and d1.discount_reason_id !=0 and m1.store_id="
					+ store_id + " and m1.report_date='" + param.optString("day_count") + "' and m1.bill_property='CLOSED' group by m1.bill_num,m1.bill_amount,d1.discount_reason_id, r1.unusual_type) m group by m.unusual_id, m.unusual_type");

			// 付款方式统计
			StringBuilder payment = new StringBuilder();
			payment.append("insert into hq_payment_count(tenancy_id,store_id,day_count,payment_class,payment_id,pay_money,local_currency,sale_total) select '"
					+ tenantid
					+ "' as tenancy_id, "
					+ store_id
					+ " as store_id, to_date('"
					+ param.optString("day_count")
					+ "','YYYY-MM-DD') as day_count,w.payment_class, p.jzid as payment_id, sum(p.amount) as pay_money, sum(p.currency_amount) as local_currency,sum(m.bill_amount) as sale_total from pos_bill_payment p join payment_way w on p.jzid=w.id left join pos_bill m on p.bill_num=m.bill_num where p.store_id="
					+ store_id + " and p.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED' group by w.payment_class, p.jzid");

			// 菜品日营业汇总
			StringBuilder item = new StringBuilder();
			item.append("insert into hq_daycount_item(tenancy_id,store_id,day_count,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,list_num,list_money,sales_num,sales_money,back_num,back_money,free_num,free_money,favor_money,discount_money,reduction_money,change_money,actual_num,actual_money,net_money,sale_billnum,sale_billaverage,table_property_id,business_area_id,guest_num,tables_num)");
			item.append(" select '"
					+ tenantid
					+ "' as tenancy_id, "
					+ store_id
					+ " as store_id, to_date('"
					+ param.optString("day_count")
					+ "','YYYY-MM-DD') as day_count,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,sum(list_num) as list_num,sum(list_money) as list_money,sum(sales_num) as sales_num,sum(sales_money) as sales_money,sum(back_num) as back_num,sum(back_money) as back_money,sum(free_num) as free_num,sum(free_money) as free_money,sum(favor_money) as favor_money,sum(discount_money) as discount_money,sum(reduction_money) as reduction_money,sum(change_money) as change_money,sum(actual_num) as actual_num,sum(actual_money) as actual_money,sum(net_money) as net_money");
			item.append(",(select case when sum(sales_num)=0 then 0 else count(bill_num) end from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count")
					+ "' and bill_property='CLOSED' and (bill_state is null or (bill_state!='CJ01' and bill_state!='ZDQX02'))) as sale_billnum, round((sum(sales_money) + sum(change_money)) / coalesce((select case when count(bill_num)=0 then 1 else count(bill_num) end from pos_bill where store_id="
					+ store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED' and (bill_state is null or (bill_state!='CJ01' and bill_state!='ZDQX02'))),1), 2) as sale_billaverage");
			item.append(",table_property_id,business_area_id,coalesce(sum(guest_counts),0) as guest_num");
			item.append(",(select case when sum(sales_num)=0 then 0 else count(bill_num) end from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count")
					+ "' and bill_property='CLOSED' and (bill_state is null or (bill_state!='CJ01' and bill_state!='ZDQX02'))) as tables_num");
			item.append(" from");
			item.append(" (select m.sale_mode as sale_model, d.setmeal_id as combo_id,");
			item.append(" case when d.item_property='SETMEAL' then d.item_unit_id else (select distinct dd.item_unit_id from pos_bill_item dd where dd.bill_num=d.bill_num and dd.setmeal_id=d.setmeal_id and dd.setmeal_rwid=d.setmeal_rwid and dd.item_property='SETMEAL') end as combo_unit_id,");
			item.append(" case when d.item_property='SETMEAL' then d.item_price else (select distinct dd.item_price from pos_bill_item dd where dd.bill_num=d.bill_num and dd.setmeal_id=d.setmeal_id and dd.setmeal_rwid=d.setmeal_rwid and dd.item_property='SETMEAL') end as combo_price,");
			item.append(" d.item_id,d.item_unit_id,d.item_price as item_unit_price,d.item_property as item_pro,");
			// item.append(" d.item_count as list_num,d.item_amount as list_money,");
			item.append(" case when d.item_remark in ('CJ05') then 0 else d.item_count end as list_num,case when d.item_remark in ('CJ05') then 0 else d.item_amount end as list_money,");
			item.append(" d.item_count as sales_num,d.item_amount as sales_money,");
			item.append(" case when d.item_remark in ('TC01','QX04') then coalesce((select r.count from pos_returngive_item r where r.bill_num=d.bill_num and r.store_id=d.store_id and r.rwid=d.rwid and r.type='TC01'),0) else 0 end as back_num,");
			item.append(" case when d.item_remark in ('TC01','QX04') then coalesce((select r.amount from pos_returngive_item r where r.bill_num=d.bill_num and r.store_id=d.store_id and r.rwid=d.rwid and r.type='TC01'),0) else 0 end as back_money,");
			item.append(" case when d.item_remark='FS02' then coalesce((select r.count from pos_returngive_item r where r.bill_num=d.bill_num and r.store_id=d.store_id and r.rwid=d.rwid and r.type='FS02'),0) else 0 end as free_num,");
			item.append(" case when d.item_remark='FS02' then coalesce((select r.amount from pos_returngive_item r where r.bill_num=d.bill_num and r.store_id=d.store_id and r.rwid=d.rwid and r.type='FS02'),0) else 0 end as free_money,");
			item.append(" (d.discount_amount + d.discountr_amount - d.single_discount_amount) as favor_money,");

			item.append(" case when d.item_remark in ('TC01','FS02') then 0 else (d.discount_amount ) end as discount_money ,");
			item.append(" case when d.item_remark in ('TC01','FS02') then 0 else (d.discountr_amount ) end as reduction_money,");

			item.append(" d.single_discount_amount as change_money,");
			item.append(" case when d.item_remark in ('TC01','FS02','QX04','CJ05') then 0 else d.item_count end as actual_num,case when d.item_remark in ('TC01','FS02','QX04','CJ05') then 0 else d.real_amount end as actual_money,");
			item.append(" case when d.item_remark in ('TC01','FS02','QX04','CJ05') then 0 else d.real_amount end as net_money,");
			item.append(" m.bill_num,");
			item.append(" t.table_property_id,");
			item.append(" t.business_area_id,");
			item.append(" m.guest as guest_counts");
			item.append(" from pos_bill_item d join pos_bill m on d.bill_num=m.bill_num");
			item.append(" left join tables_info t on t.table_code=m.table_code and organ_id=" + store_id);
			item.append(" where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED'");
			item.append(" ) a group by sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,table_property_id,business_area_id");
			// 菜品日营业汇总分时段
			StringBuilder itemTimes = new StringBuilder();
			itemTimes
					.append("insert into hq_daycount_item_times(tenancy_id,store_id,day_count,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,list_num,list_money,sales_num,sales_money,back_num,back_money,free_num,free_money,favor_money,change_money,actual_num,actual_money,net_money,sale_billnum,sale_billaverage,times_id)");
			itemTimes
					.append(" select '"
							+ tenantid
							+ "' as tenancy_id, "
							+ store_id
							+ " as store_id, to_date('"
							+ param.optString("day_count")
							+ "','YYYY-MM-DD') as day_count,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,sum(list_num) as list_num,sum(list_money) as list_money,sum(sales_num) as sales_num,sum(sales_money) as sales_money,sum(back_num) as back_num,sum(back_money) as back_money,sum(free_num) as free_num,sum(free_money) as free_money,sum(favor_money) as favor_money,sum(change_money) as change_money,sum(actual_num) as actual_num,sum(actual_money) as actual_money,sum(net_money) as net_money,(select case when sum(sales_num)=0 then 0 else count(bill_num) end from pos_bill where store_id="
							+ store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED' and (bill_state is null or (bill_state!='CJ01' and bill_state!='ZDQX02'))) as sale_billnum");
			itemTimes.append(", round((sum(sales_money) + sum(change_money)) / coalesce((select case when count(bill_num)=0 then 1 else count(bill_num) end from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count")
					+ "' and bill_property='CLOSED' and (bill_state is null or (bill_state!='CJ01' and bill_state!='ZDQX02'))),1), 2) as sale_billaverage,times_id");
			itemTimes.append(" from");
			itemTimes.append(" (select m.sale_mode as sale_model, d.setmeal_id as combo_id,");
			itemTimes.append(" case when d.item_property='SETMEAL' then d.item_unit_id else (select distinct dd.item_unit_id from pos_bill_item dd where dd.bill_num=d.bill_num and dd.setmeal_id=d.setmeal_id and dd.setmeal_rwid=d.setmeal_rwid and dd.item_property='SETMEAL') end as combo_unit_id,");
			itemTimes.append(" case when d.item_property='SETMEAL' then d.item_price else (select distinct dd.item_price from pos_bill_item dd where dd.bill_num=d.bill_num and dd.setmeal_id=d.setmeal_id and dd.setmeal_rwid=d.setmeal_rwid and dd.item_property='SETMEAL') end as combo_price,");
			itemTimes.append(" d.item_id,d.item_unit_id,d.item_price as item_unit_price,d.item_property as item_pro,");

			// itemTimes.append(" d.item_count as list_num,d.item_amount as list_money,");
			itemTimes.append(" case when d.item_remark in ('CJ05') then 0 else d.item_count end as list_num,");
			itemTimes.append(" case when d.item_remark in ('CJ05') then 0 else d.item_amount end as list_money,");
			itemTimes.append(" d.item_count as sales_num,d.item_amount as sales_money,");

			itemTimes.append(" case when d.item_remark in ('TC01','QX04') then coalesce((select r.count from pos_returngive_item r where r.bill_num=d.bill_num and r.store_id=d.store_id and r.rwid=d.rwid and r.type='TC01'),0) else 0 end as back_num,");
			itemTimes.append(" case when d.item_remark in ('TC01','QX04') then coalesce((select r.amount from pos_returngive_item r where r.bill_num=d.bill_num and r.store_id=d.store_id and r.rwid=d.rwid and r.type='TC01'),0) else 0 end as back_money,");
			itemTimes.append(" case when d.item_remark='FS02' then coalesce((select r.count from pos_returngive_item r where r.bill_num=d.bill_num and r.store_id=d.store_id and r.rwid=d.rwid and r.type='FS02'),0) else 0 end as free_num,");
			itemTimes.append(" case when d.item_remark='FS02' then coalesce((select r.amount from pos_returngive_item r where r.bill_num=d.bill_num and r.store_id=d.store_id and r.rwid=d.rwid and r.type='FS02'),0) else 0 end as free_money,");
			itemTimes.append(" case when d.item_remark in ('TC01','FS02') then 0 else (d.discount_amount + d.discountr_amount - d.single_discount_amount) end as favor_money,");
			itemTimes.append(" d.single_discount_amount as change_money,");
			itemTimes.append(" case when d.item_remark in ('TC01','FS02') then 0 else d.item_count end as actual_num,case when d.item_remark in ('TC01','FS02') then 0 else (d.item_amount-d.discount_amount - d.discountr_amount) end as actual_money,");
			itemTimes.append(" case when d.item_remark in ('TC01','FS02') then 0 else d.real_amount end as net_money,");
			itemTimes.append(" m.bill_num,");
			itemTimes.append(" (select time_id from sys_times where time_type='half' and end_time=to_char(m.payment_time,'hh24') || ':' ||(case when to_char(m.payment_time,'mi') >= '30' then '30' else '00' end)) as times_id");
			itemTimes.append(" from pos_bill_item d join pos_bill m on d.bill_num=m.bill_num");
			itemTimes.append(" where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED'");
			itemTimes.append(" ) a group by sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,times_id");
			// 现金日报
			StringBuilder daucount = new StringBuilder();
			daucount.append("insert into hq_daycount(tenancy_id,store_id,day_count,sale_total,sale_billnum,sale_billaverage,discount_money,reduction_money,coupons_money,total_card,main_trading,customer_discount,customer_reward_sale,customer_cash_money,moling_money,free_money,item_list_money,item_actual_money,ts_actual_money,ws_actual_money,wd_actual_money,item_yh_money,item_discount_money,item_reduction_money,free_money_item,back_money_item,item_net_money,other_income,service_fee_income,yyw_income,accounts_receivable,customer_recharge_total,customer_recharge_income,customer_recharge_reward,customer_recharge_cash,customer_recharge_bank,customer_recharge_zfb,customer_recharge_wx,payment_total,coupons_ds,deposit_money_actual,sale_person_num,sale_person_average,attendance_num,remark,tables_num,seat_num,backsingle_num,backwhole_num,backcopy_num)");
			daucount.append(" select '" + tenantid + "' as tenancy_id, " + store_id + " as store_id, to_date('" + param.optString("day_count") + "','YYYY-MM-DD') as day_count,");
			daucount.append(" (select coalesce(sum(bill_amount),0) from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED') as sale_total,");
			daucount.append(" (select count(bill_num) from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED' and (bill_state is null or (bill_state!='CJ01' and bill_state!='ZDQX02'))) as sale_billnum,");
			daucount.append(" (select case when count(bill_num)=0 then 0 else round((coalesce(sum(bill_amount),0) + coalesce(sum(more_coupon),0)) / coalesce((select case when count(bill_num)=0 then 1 else count(bill_num) end from pos_bill where store_id=" + store_id + " and report_date='"
					+ param.optString("day_count") + "' and bill_property='CLOSED' and (bill_state is null or (bill_state!='CJ01' and bill_state!='ZDQX02'))),1), 2) end from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count")
					+ "' and bill_property='CLOSED' ) as sale_billaverage,");
			daucount.append(" (select coalesce(sum(discountk_amount),0) from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED') as discount_money,");
			daucount.append(" (select coalesce(sum(discountr_amount),0) from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED') as reduction_money,");
			daucount.append(" (select coalesce(sum(p.currency_amount),0) from pos_bill m join pos_bill_payment p on m.bill_num=p.bill_num left join payment_way w on p.jzid=w.id where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count")
					+ "' and m.bill_property='CLOSED' and w.payment_class='coupons') as coupons_money,");

			daucount.append(" (select coalesce(sum(p.currency_amount),0) from pos_bill m join pos_bill_payment p on m.bill_num=p.bill_num left join payment_way w on p.jzid=w.id where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count")
					+ "' and m.bill_property='CLOSED'and w.payment_class='card') as total_card ,");
			daucount.append(" (select coalesce(sum(main_trading),0) from crm_card_trading_list where operat_type in ('03','05') and store_id=" + store_id + " and business_date='" + param.optString("day_count") + "') as main_trading ,");
			daucount.append(" (select coalesce(sum(discountk_amount),0) from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "'and bill_property='CLOSED'and discount_mode_id=5 ) as customer_discount,");

			daucount.append(" (select coalesce(sum(reward_trading),0) from crm_card_trading_list where operat_type in ('03','05') and store_id=" + store_id + " and business_date='" + param.optString("day_count") + "') as customer_reward_sale,");
			daucount.append(" 0 as customer_cash_money,");
			daucount.append(" (select coalesce(sum(maling_amount),0) from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED') as moling_money,");

			daucount.append(" (select coalesce(sum(givi_amount),0) from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED') as free_money,");
			daucount.append(" (select sum (a .item_amount) as item_list_money from (select case when d.item_remark in ('CJ05') then 0 else (d .item_amount) end as item_amount from pos_bill_item d join pos_bill m on d.bill_num = m .bill_num and m.store_id= " + store_id + " where m .store_id = "
					+ store_id + " and m .report_date = '" + param.optString("day_count") + "'and m .bill_property = 'CLOSED' and (d.item_property = 'SINGLE' or d.item_property = 'SETMEAL')) a) as item_list_money,");
			// daucount.append(" (select coalesce(sum(d.item_amount),0) from pos_bill m join pos_bill_item d on d.bill_num=m.bill_num where m.store_id="
			// + store_id + " and m.report_date='" +
			// param.optString("day_count") +
			// "' and m.bill_property='CLOSED' and d.item_remark='FS02') as free_money,");
			// daucount.append(" (select coalesce(sum(m.subtotal),0) from pos_bill m where m.store_id="
			// + store_id + " and m.report_date='" +
			// param.optString("day_count") +
			// "' and m.bill_property='CLOSED') as item_list_money,");
			daucount.append(" (select coalesce(sum(m.payment_amount),0) from pos_bill m where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED') as item_actual_money,");
			daucount.append(" (select coalesce(sum(m.payment_amount),0) from pos_bill m where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED' and sale_mode='TS01') as ts_actual_money,");
			daucount.append(" (select coalesce(sum(m.payment_amount),0) from pos_bill m where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED' and sale_mode='WS03') as ws_actual_money,");
			daucount.append(" (select coalesce(sum(m.payment_amount),0) from pos_bill m where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED' and sale_mode='WD02') as wd_actual_money,");
			daucount.append(" (select coalesce(sum(d.discount_amount + d.discountr_amount - d.single_discount_amount),0) from pos_bill m join pos_bill_item d on m.bill_num=d.bill_num where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count")
					+ "' and m.bill_property='CLOSED') as item_yh_money,");
			daucount.append(" (select coalesce(sum(d.discount_amount),0) from pos_bill m join pos_bill_item d on m.bill_num=d.bill_num where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED') as item_discount_money,");

			daucount.append(" (select coalesce(sum(d.discountr_amount),0) from pos_bill m join pos_bill_item d on m.bill_num=d.bill_num where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED') as item_reduction_money,");

			daucount.append(" (select coalesce(sum(d.item_amount),0) from pos_bill m join pos_bill_item d on m.bill_num=d.bill_num where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count")
					+ "' and m.bill_property='CLOSED' and d.item_remark='FS02' and (d.item_property = 'SINGLE' or d.item_property = 'SETMEAL')) as free_money_item,");
			daucount.append(" (select coalesce(sum(d.real_amount),0) from pos_bill m join pos_bill_item d on m.bill_num=d.bill_num where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count")
					+ "' and m.bill_property='CLOSED' and d.item_remark in ('TC01','QX04') and (d.item_property = 'SINGLE' or d.item_property = 'SETMEAL')) as back_money_item,");
			daucount.append(" (select coalesce(sum(d.real_amount),0) from pos_bill m join pos_bill_item d on m.bill_num=d.bill_num where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED') as item_net_money,");
			daucount.append(" (select coalesce(sum(bi.money),0) from boh_inout_info bi where bi.store_id=" + store_id + " and bi.business_date='" + param.optString("day_count") + "') as other_income,");
			daucount.append(" (select coalesce(sum(m.service_amount),0) from pos_bill m where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED') as service_fee_income,");
			daucount.append(" (select coalesce(sum(bi.money),0) from boh_inout_info bi where bi.store_id=" + store_id + " and bi.business_date='" + param.optString("day_count") + "') as yyw_income,");
			// daucount.append(" (select coalesce(sum(m.money),0) from boh_inout_info m where m.store_id="
			// + store_id + " and m.business_date='" +
			// param.optString("day_count") +
			// "' and m.inout_property='SR01') as yyw_income,");
			daucount.append(" (select coalesce(sum(main_trading),0) from crm_card_trading_list where operat_type in ('02','04') and store_id=" + store_id + " and business_date='" + param.optString("day_count") + "') as accounts_receivable,");
			daucount.append(" (select coalesce(sum(main_trading),0) from crm_card_trading_list where operat_type in ('02','04') and store_id=" + store_id + " and business_date='" + param.optString("day_count") + "') as customer_recharge_total,");
			daucount.append(" (select coalesce(sum(main_trading),0) from crm_card_trading_list where operat_type in ('02','04') and store_id=" + store_id + " and business_date='" + param.optString("day_count") + "') as customer_recharge_income,");
			daucount.append(" (select coalesce(sum(reward_trading),0) from crm_card_trading_list where operat_type in ('02','04') and store_id=" + store_id + " and business_date='" + param.optString("day_count") + "') as customer_recharge_reward,");
			daucount.append(" (select coalesce(sum(p.local_currency),0) from crm_card_trading_list t join crm_card_payment_list p on t.bill_code=p.bill_code left join payment_way w on p.payment_id=w.id where t.store_id=" + store_id + " and t.business_date='" + param.optString("day_count")
					+ "' and w.payment_class='cash') as customer_recharge_cash,");
			daucount.append(" (select coalesce(sum(p.local_currency),0) from crm_card_trading_list t join crm_card_payment_list p on t.bill_code=p.bill_code left join payment_way w on p.payment_id=w.id where t.store_id=" + store_id + " and t.business_date='" + param.optString("day_count")
					+ "' and w.payment_class='bankcard') as customer_recharge_bank,");
			daucount.append(" (select coalesce(sum(p.local_currency),0) from crm_card_trading_list t join crm_card_payment_list p on t.bill_code=p.bill_code left join payment_way w on p.payment_id=w.id where t.store_id=" + store_id + " and t.business_date='" + param.optString("day_count")
					+ "' and w.payment_class='thirdparty' and w.payment_name2='ali_pay') as customer_recharge_zfb,");
			daucount.append(" (select coalesce(sum(p.local_currency),0) from crm_card_trading_list t join crm_card_payment_list p on t.bill_code=p.bill_code left join payment_way w on p.payment_id=w.id where t.store_id=" + store_id + " and t.business_date='" + param.optString("day_count")
					+ "' and w.payment_class='thirdparty' and w.payment_name2='wechat_pay') as customer_recharge_wx,");
			daucount.append(" (select coalesce(sum(p.currency_amount),0) from pos_bill m join pos_bill_payment p on m.bill_num=p.bill_num where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED') as payment_total,");
			daucount.append(" (select coalesce(sum(m.more_coupon),0) from pos_bill m where m.store_id=" + store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED') as coupons_ds,");
			daucount.append(" (select coalesce(sum(amount_deposited),0) from boh_bank_deposit where store_id=" + store_id + " and business_date='" + param.optString("day_count") + "') as deposit_money_actual,");
			daucount.append(" (select coalesce(sum(guest),0) from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED') as sale_person_num,");
			daucount.append(" (select case when sum(guest)=0 then 0 else round((coalesce(sum(bill_amount),0) + coalesce(sum(more_coupon),0)) / sum(guest),2) end from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count")
					+ "' and bill_property='CLOSED') as sale_person_average,");
			daucount.append(" 0 as attendance_num,");
			daucount.append(" '' as remark,");
			daucount.append(" (select count(t.id) from tables_info t where t.organ_id=" + store_id + " and t.valid_state='1') as tables_num,");
			daucount.append(" (select coalesce(sum(t.seat_counts),0) from tables_info t where t.organ_id=" + store_id + " and t.valid_state='1') as seat_num,");
			daucount.append(" (select count(id) from pos_bill  where bill_state='CJ01' and copy_bill_num in(select bill_num from pos_bill where bill_state is null  and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED') and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED') as backsingle_num ,");
			daucount.append(" (select count(id) from pos_bill  where bill_state='CJ01' and copy_bill_num in(select bill_num from pos_bill where bill_state  ='ZDQX02'  and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED') and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED' and copy_bill_num  not in (select copy_bill_num from pos_bill where (bill_state is null or bill_state='ZDQX02' )  and copy_bill_num is not null and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED')) as backwhole_num ,");
			daucount.append(" (select count(id) from pos_bill  where bill_state='CJ01' and copy_bill_num in(select bill_num from pos_bill where bill_state  ='ZDQX02'  and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED') and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED' and copy_bill_num  in (select copy_bill_num from pos_bill where (bill_state is null or bill_state='ZDQX02' )  and copy_bill_num is not null and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED')) as backcopy_num ");


			// 机构日营业汇总分时段
			StringBuilder organTimes = new StringBuilder();
			organTimes
					.append("insert into hq_daycount_times (tenancy_id, store_id, day_count, times_id, sale_total, sale_billnum, sale_billaverage, discount_money, reduction_money, coupons_money, customer_discount, customer_reward_sale, customer_cash_money, moling_money, free_money, item_list_money, item_actual_money, ts_actual_money");
			organTimes
					.append(" , ws_actual_money , wd_actual_money, item_yh_money, item_discount_money, item_reduction_money, free_money_item, back_money_item, item_net_money, other_income, service_fee_income, yyw_income, accounts_receivable, customer_recharge_total, customer_recharge_income, customer_recharge_reward, customer_recharge_cash, customer_recharge_bank");
			organTimes.append(" , customer_recharge_zfb, customer_recharge_wx, payment_total, coupons_ds, deposit_money_actual, sale_person_num, sale_person_average, attendance_num, remark)");

			organTimes.append(" select '" + tenantid + "' as tenancy_id,a.store_id  as store_id,a.report_date as day_count,t.times_id as times_id");
			organTimes.append(" ,sum(a.bill_amount) as sale_total");
			organTimes.append(" ,count(case when coalesce(a.bill_state,'zc') not in ('CJ01','ZDQX02') then a.bill_num end) as sale_billnum");
			organTimes.append(" ,round(sum(coalesce(a.bill_amount,0) + coalesce(a.more_coupon,0))/coalesce(nullif(count(case when coalesce(a.bill_state,'zc') not in('CJ01','ZDQX02') then a.bill_num end),0),1),2) as sale_billaverage");
			organTimes.append(" ,sum(coalesce(a.discountk_amount,0)) as discount_money");
			organTimes.append(" ,sum(coalesce(a.discountr_amount,0)) as reduction_money");
			organTimes.append(" ,sum(coalesce(c.coupons_money,0)) as coupons_money");
			organTimes.append(" ,sum(coalesce(case when a.discount_mode_id = 5 then a.discountk_amount else 0 end,0)) as customer_discount");
			organTimes.append(" ,sum(coalesce(k.customer_reward_sale,0)) as customer_reward_sale");
			organTimes.append(" ,0  as customer_cash_money");
			organTimes.append(" ,sum(coalesce(a.maling_amount,0)) as moling_money");
			organTimes.append(" ,sum(coalesce(a.givi_amount,0))   as free_money");
			organTimes.append(" ,sum(coalesce(a.bill_amount,0) - coalesce(a.service_amount,0)) as item_list_money");
			organTimes.append(" ,sum(coalesce(a.payment_amount,0))  as item_actual_money");
			organTimes.append(" ,sum(coalesce(case when a.sale_mode='TS01' then a.payment_amount else 0 end,0)) as ts_actual_money");
			organTimes.append(" ,sum(coalesce(case when a.sale_mode='WS03' then a.payment_amount else 0 end,0)) as ws_actual_money");
			organTimes.append(" ,sum(coalesce(case when a.sale_mode='WD02' then a.payment_amount else 0 end,0)) as wd_actual_money");
			organTimes.append(" ,sum(coalesce(b.item_yh_money,0)) as item_yh_money");
			organTimes.append(" ,sum(coalesce(b.item_discount_money,0)) as item_discount_money");
			organTimes.append(" ,sum(coalesce(b.item_reduction_money,0)) as item_reduction_money");
			organTimes.append(" ,sum(coalesce(b.free_money_item,0)) as free_money_item");
			organTimes.append(" ,sum(coalesce(b.back_money_item,0)) as back_money_item");
			organTimes.append(" ,sum(coalesce(b.item_net_money,0)) as item_net_money");
			organTimes.append(" ,0 as other_income");
			organTimes.append(" ,sum(coalesce(a.service_amount,0)) as service_fee_income");
			organTimes.append(" ,0 yyw_income");
			organTimes.append(" ,sum(coalesce(k.accounts_receivable,0)) as accounts_receivable");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_total,0)) as customer_recharge_total");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_income,0)) as customer_recharge_income");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_reward,0)) as customer_recharge_reward");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_cash,0)) as customer_recharge_cash");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_bank,0)) as customer_recharge_bank");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_zfb,0)) as customer_recharge_zfb");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_wx,0)) as customer_recharge_wx");
			organTimes.append(" ,sum(coalesce(c.currency_amount,0)) as payment_total");
			organTimes.append(" ,sum(coalesce(a.more_coupon,0))  as coupons_ds");
			organTimes.append(" ,0 as deposit_money_actual");
			organTimes.append(" ,sum(coalesce(a.guest,0)) as sale_person_num");
			organTimes.append(" ,round(sum(coalesce(a.bill_amount,0) + coalesce(more_coupon,0)) / coalesce(nullif(sum(a.guest),0),1),2) as sale_person_average");
			organTimes.append(" ,0 as attendance_num");
			organTimes.append(" ,''  as remark");
			organTimes.append(" from (");
			organTimes.append(" SELECT (CASE WHEN TO_CHAR(PB.PAYMENT_TIME+'30 MIN','HH24' )='00' THEN '24' ELSE TO_CHAR(PB.PAYMENT_TIME+'30 MIN','HH24' ) END ) || ':' || (CASE WHEN TO_CHAR(PB.PAYMENT_TIME+'30 MIN','MI') >= '30' THEN '30' ELSE '00' END) AS CTIM");
			organTimes.append(" ,pb.* from pos_bill pb");
			organTimes.append(" where pb.store_id=" + store_id + "and pb.report_date='" + param.optString("day_count") + "' and pb.bill_property='CLOSED'");
			organTimes.append(" ) a left join");
			organTimes.append(" (select a.bill_num");
			organTimes.append(" ,sum(coalesce(b.discount_amount,0) + coalesce(b.discountr_amount,0) - coalesce(b.single_discount_amount,0))  as item_yh_money");
			organTimes.append(" ,sum(coalesce(b.discount_amount,0)) as item_discount_money");
			organTimes.append(" ,sum(coalesce(b.discountr_amount,0)) as item_reduction_money");
			organTimes.append(" ,sum(coalesce(case when b.item_remark = 'FS02' then b.item_amount else 0 end,0)) as free_money_item");
			organTimes.append(" ,sum(coalesce(case when b.item_remark in ('TC01','QX04') and b.item_property in ('SINGLE','SETMEAL') then b.item_amount else 0 end,0)) as back_money_item");
			organTimes.append(" ,sum(coalesce(b.item_amount,0)) as item_net_money");
			organTimes.append(" from pos_bill a left join");
			organTimes.append(" pos_bill_item b on a.bill_num = b.bill_num");
			organTimes.append(" where a.store_id=" + store_id + "and a.report_date='" + param.optString("day_count") + "' and a.bill_property='CLOSED' group by a.bill_num");
			organTimes.append(" ) b on b.bill_num = a.bill_num left join");
			organTimes.append(" (select a.bill_num");
			organTimes.append(" ,sum(coalesce(p.currency_amount,0)) as currency_amount");
			organTimes.append(" ,sum(coalesce(case when w.payment_class = 'coupons' then p.currency_amount else 0 end,0)) as coupons_money");
			organTimes.append(" from pos_bill a join");
			organTimes.append(" pos_bill_payment p on p.bill_num = a.bill_num left join");
			organTimes.append(" payment_way w on p.jzid=w.id");
			organTimes.append(" where a.store_id=" + store_id + "and a.report_date='" + param.optString("day_count") + "' and a.bill_property='CLOSED' group by a.bill_num");
			organTimes.append(" ) c on c.bill_num = a.bill_num left join");
			organTimes.append(" (select a.bill_num");
			organTimes.append(" ,sum(case when b.operat_type in ('03','05') then b.reward_trading else 0 end) as customer_reward_sale");
			organTimes.append(" ,sum(case when b.operat_type in ('02','04') then b.main_trading else 0 end) as accounts_receivable");
			organTimes.append(" ,sum(case when b.operat_type in ('02','04') then b.main_trading else 0 end) as customer_recharge_total");
			organTimes.append(" ,sum(case when b.operat_type in ('02','04') then b.main_trading else 0 end) as customer_recharge_income");
			organTimes.append(" ,sum(case when b.operat_type in ('02','04') then b.reward_trading else 0 end) as customer_recharge_reward");
			organTimes.append(" ,sum(case when w.payment_class = 'cash' then p.local_currency else 0 end) as customer_recharge_cash");
			organTimes.append(" ,sum(case when w.payment_class = 'bankcard' then p.local_currency else 0 end) as customer_recharge_bank");
			organTimes.append(" ,sum(case when w.payment_class = 'thirdparty' and w.payment_name2 = 'ali_pay' then p.local_currency else 0 end) as customer_recharge_zfb");
			organTimes.append(" ,sum(case when w.payment_class = 'thirdparty' and w.payment_name2 = 'wechat_pay' then p.local_currency else 0 end) as customer_recharge_wx");
			organTimes.append(" from");
			organTimes.append(" pos_bill a join");
			organTimes.append(" crm_card_trading_list b on b.bill_code_original = a.bill_num left join");
			organTimes.append(" crm_card_payment_list p on p.bill_code = b.bill_code left join");
			organTimes.append(" payment_way w on w.id = p.payment_id");
			organTimes.append(" where b.store_id=" + store_id + "and b.business_date='" + param.optString("day_count") + "' and a.bill_property='CLOSED' group by a.bill_num");
			organTimes.append(" ) k on k.bill_num = a.bill_num left join");
			organTimes.append(" (select  time_id as times_id,end_time from sys_times where time_type='half') t on t.end_time = a.ctim");
			organTimes.append(" group by a.store_id,a.report_date,t.times_id");

			// 现金日报分时段

			// 会员日统计分机构交易
			// StringBuilder crmTradeorg = new StringBuilder();

			// 会员日统计分交易机构等级

			// 会员日统计分注册机构

			// 会员日统计分注册机构等级

			// 会员日统计分渠道

			// 会员日统计分等级

			// 删昨天的
			String deletePosBill = "delete from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "'";
			String deletePosBillItem = "delete from pos_bill_item where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "'";
			String deletePosBillPayment = "delete from pos_bill_payment where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "'";

			dao.getJdbcTemplate(tenantid).batchUpdate(new String[]
					{ repairPayment, insertPosBill, insertPosBillItem, insertPosBillPayment, unusualReason.toString(), payment.toString(), item.toString(), itemTimes.toString(), organTimes.toString(), daucount.toString(), deletePosBill, deletePosBillItem, deletePosBillPayment });

			String status = "1";
			param.element("status", status);

			sql = "update hq_daycount_info set hq_sign='" + status + "' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
			dao.execute(tenantid, sql);

		}
		else
		{
			// sql = "update hq_daycount_info set hq_sign=null where store_id="
			// + param.optInt("store_id") + " and day_count='" +
			// param.optString("day_count") + "'";
			// dao.execute(tenantid, sql);
			throw new Exception("门店上传数据不完全，请使用【数据上传】功能后重试！");
		}

	}

	/**
	 * 库存数据
	 *
	 * @param tenantid
	 * @param param
	 * @throws Exception
	 */
	private void scmDailySettlement(String tenantid, JSONObject param) throws Exception
	{

		int store_id = param.optInt("store_id");
		Date day_count = Date.valueOf(param.optString("day_count"));

		StringBuilder sql = new StringBuilder();
		String status = "0";

		// 判断门店是否暂停营业？
		int daily_step = 1;

		try
		{

			sql.append("select init_date,da_date from scm_organ where tenancy_id='" + tenantid + "' and store_id=" + store_id + " and is_init=1 and init_date<='"+param.optString("day_count")+"'");
			SqlRowSet rs = dao.query(tenantid, sql.toString());
			if (rs.next())
			{
				Date lastDaily = rs.getDate("da_date");


				if (lastDaily != null && day_count.compareTo(DateUtils.addDays(lastDaily, daily_step)) != 0)
				{
					throw new Exception("该机构物流业务的日结日期不连续，无法进行日结操作！上一次日结日期为：【" + DateUtil.format(lastDaily, "yyyy-MM-dd") + "】");
				}


				if(isOpenHq(tenantid, param))
				{
					sql.setLength(0);
					if(lastDaily != null)
					{
						sql.append(" select 1 from scm_storage_day_acount t where t.tenancy_id='"+tenantid+"' and t.store_id="+store_id+" and t.cost_count_date='"+param.optString("day_count")+"' and t.day_end = '1'");
						long total = this.dao.countSql(tenantid, sql.toString());
						if(total<=0)
						{
							throw new Exception("该机构没有在日期为：【"+param.optString("day_count")+"】进行数据整理，禁止日结");
						}
					}
				}

				// 判定有无未审核单据
				sql.setLength(0);
				sql.append("select i.bill_no,i.bill_type,'期初单' AS bill_type_name from scm_init_storage i where i.tenancy_id='" + tenantid + "' and i.store_id=" + store_id + " and i.business_date='" + param.optString("day_count") + "' and i.bill_state!='2' and i.bill_state!='9'");
				sql.append(" union all");
				sql.append(" select s.bill_no,s.bill_type,s.bill_type_name from scm_supply_in s where s.bill_type = 'supply_in' and s.tenancy_id='" + tenantid + "' and s.store_id=" + store_id + " and s.in_date='" + param.optString("day_count") + "' and s.bill_status!='2' and s.bill_status!='9'");
				sql.append(" union all");
				sql.append(" select t.bill_no,t.bill_type,t.bill_type_name from scm_transfer t where t.tenancy_id='" + tenantid + "' and t.out_store_id=" + store_id + " and t.busi_date='" + param.optString("day_count") + "' and t.bill_status!='2' and t.bill_status!='9'");
				sql.append(" union all");
				sql.append(" select p.bill_no,p.bill_type,p.bill_type_name from scm_profit p where p.tenancy_id='" + tenantid + "' and p.store_id=" + store_id + " and p.busi_date='" + param.optString("day_count") + "' and p.bill_status!='2' and p.bill_status!='9'");
				sql.append(" union all");
				sql.append(" select r.bill_no,r.bill_type,r.bill_type_name from scm_supply_return r where r.tenancy_id='" + tenantid + "' and r.store_id=" + store_id + " and r.out_date='" + param.optString("day_count") + "' and r.bill_status!='2' and r.bill_status!='9'");
				sql.append(" union all");
				sql.append(" select l.bill_no,l.bill_type,l.bill_type_name from scm_loss l where l.tenancy_id='" + tenantid + "' and l.store_id=" + store_id + " and l.busi_date='" + param.optString("day_count") + "' and l.bill_status!='2' and l.bill_status!='9'");
				sql.append(" union all");
				sql.append(" select k.bill_no,k.bill_type,k.bill_type_name from scm_stocktaking k where k.tenancy_id='" + tenantid + "' and k.store_id=" + store_id + " and k.busi_date='" + param.optString("day_count") + "' and k.bill_status!='2' and k.bill_status!='9'");
				sql.append(" union all");
				sql.append(" select e.bill_no,e.bill_type,e.bill_type_name from scm_settlement e where e.tenancy_id='" + tenantid + "' and e.store_id=" + store_id + " and e.settle_date='" + param.optString("day_count") + "' and e.bill_status!='2' and e.bill_status!='9'");
				sql.append(" union all");
				sql.append(" select d.bill_no,d.bill_type,d.bill_type_name from scm_dc_out d where d.tenancy_id='"+tenantid+"' and d.store_id="+store_id+" and d.out_date='"+param.optString("day_count")+"' and d.bill_status!='2' and d.bill_status!='9'");
				sql.append(" union all");
				sql.append(" select bill_no,'store_in' as bill_type,'未收货门店调拨单 ' as bill_type_name from scm_transfer_history where tenancy_id='"+tenantid+"' and bill_type = 'store_out' and store_id="+store_id+" and busi_date <= '"+param.optString("day_count")+"' and in_status = '1'");
				sql.append(" union all");
				sql.append(" select bill_no,'no_link' as bill_type,'对方未收货门店调拨单' as bill_type_name from scm_transfer_history where tenancy_id='"+tenantid+"' and bill_type = 'store_out' and in_store_id="+store_id+" and busi_date <= '"+param.optString("day_count")+"' and in_status = '1'");
				List<JSONObject> list = dao.query4Json(tenantid, sql.toString());

				if (list.size() > 0)
				{
					Map<String,String> map = new HashMap<String,String>();
					for(int i=0;i<list.size();i++)
					{
						String key = list.get(i).optString("bill_type_name") + "#" + list.get(i).optString("bill_type");
						if(map.containsKey(key))
						{
							String bill_nos = map.get(key);
							bill_nos+=list.get(i).optString("bill_no")+",";
							map.put(key, bill_nos);
						}
						else
						{
							String bill_nos = list.get(i).optString("bill_no");
							map.put(key, bill_nos+",");
						}
					}
					list.removeAll(list);
					Iterator<String> ite = map.keySet().iterator();
					while(ite.hasNext())
					{
						String key = ite.next();
						JSONObject row = JSONObject.fromObject("{}");
						row.put("bill_type_name",key.split("#")[0]);
						row.put("bill_type",key.split("#")[1]);
						row.put("bill_no", map.get(key).substring(0, map.get(key).length()-1));
						list.add(row);
					}
					throw new Exception(list.toString());
					
					/*sql.setLength(0);

					String billType = "";
					int i = 0;
					for (JSONObject json : list)
					{
						i++;
						if (i > 10)
						{
							sql.append("\n...");
							break;
						}

						String bt = BillType.getName(json.optString("bill_type"));
						if (billType.equals(bt))
						{
							sql.append(";" + json.optString("bill_no"));
						}
						else
						{
							sql.append("\n" + json.optString("bt") + ":");
							sql.append(json.optString("bill_no"));
						}
					}

					throw new Exception("有未审核单据，" + sql.toString() + "\n不能进行日结操作！");*/
				}

				// 开始统计
				sql.setLength(0);
				sql.append("insert into scm_inout_daily_acount(\n");
				sql.append("   tenancy_id,store_id,store_name,warehouse_id,warehouse_name,busi_date,materclass_id,materclass_name,materclass_num,material_id,material_name,material_model,material_num,unit_id,unit_name,stand_price,init_qty,init_amt\n");
				sql.append("  ,init_avgprice,inqty_supply,inamt_supply,inqty_center,inamt_center,inqty_store,inamt_store,inqty_request,inamt_request,inqty_proce,inamt_proce,inqty_other,inamt_other,inqty_overflow,inamt_overflow,in_avgprice,outqty_supply\n");
				sql.append("  ,outamt_supply,outqty_center,outamt_center,outqty_store,outamt_store,outqty_request,outamt_request,outqty_consume,outamt_consume,outqty_other,outamt_other,outqty_stock_loss,outamt_stock_loss,outqty_discard,outamt_discard\n");
				sql.append("  ,outqty_proc,outamt_proc,outqty_sales,outamt_sales,out_avgprice,qty_adjust,amt_adjust,stock_qty,stock_amt,stock_avgprice,end_qty,end_amt,sales_qty1,sales_qty2,sales_qty3,sales_qty4,sales_qty5\n");

				sql.append("  ,taxed_init_amt,tax_init_amt,taxed_inamt_supply,taxed_inamt_center,taxed_inamt_store,taxed_inamt_request,taxed_inamt_other,taxed_inamt_proce,taxed_outamt_supply,taxed_outamt_center\n");
				sql.append("  ,taxed_outamt_store,taxed_outamt_request,taxed_outamt_other,taxed_outamt_discard,taxed_outamt_consume,taxed_inamt_overflow,taxed_outamt_stock_loss,taxed_stock_amt,taxed_end_amt,tax_end_amt\n");
				sql.append("  )\n");

				sql.append(" select \n");
				sql.append("    '" + tenantid + "' as tenancy_id");
				sql.append("   ," + store_id + " as store_id");
				sql.append("   ,(select o.org_full_name from organ o where o.id=" + store_id + ") as store_name\n");
				sql.append("   , w.id as warehouse_id\n");
				sql.append("   , w.warehouse_name\n");
				sql.append("   , '" + day_count + "' as busi_date\n");
				sql.append("   , mc.id as materclass_id\n");
				sql.append("   , mc.mateclass_name as materclass_name\n");
				sql.append("   , mc.mateclass_num as materclass_num\n");
				sql.append("   , m.id as material_id\n");
				sql.append("   , m.material_name\n");
				sql.append("   , m.material_model\n");
				sql.append("   , m.material_num\n");
				sql.append("   , m.unit_id\n");
				sql.append("   , u.unit_name\n");
				sql.append("   , mp.stand_price as stand_price\n");
				sql.append("   , sf.init_qty\n");
				sql.append("   , sf.init_amt\n");
				sql.append("   , sf.init_avgprice\n");
				sql.append("   , ii.inqty_supply\n");
				sql.append("   , ii.inamt_supply\n");
				sql.append("   , ii.inqty_center\n");
				sql.append("   , ii.inamt_center\n");
				sql.append("   , ii.inqty_store\n");
				sql.append("   , ii.inamt_store\n");
				sql.append("   , ii.inqty_request\n");
				sql.append("   , ii.inamt_request\n");
				sql.append("   , ii.inqty_proce\n");
				sql.append("   , ii.inamt_proce\n");
				sql.append("   , ii.inqty_other\n");
				sql.append("   , ii.inamt_other\n");
				sql.append("   , ii.inqty_overflow\n");
				sql.append("   , ii.inamt_overflow\n");
				sql.append("   , ii.in_avgprice\n");
				sql.append("   , oo.outqty_supply\n");
				sql.append("   , oo.outamt_supply\n");
				sql.append("   , oo.outqty_center\n");
				sql.append("   , oo.outamt_center\n");
				sql.append("   , oo.outqty_store\n");
				sql.append("   , oo.outamt_store\n");
				sql.append("   , oo.outqty_request\n");
				sql.append("   , oo.outamt_request\n");
				sql.append("   , oo.outqty_consume\n");
				sql.append("   , oo.outamt_consume\n");
				sql.append("   , oo.outqty_other\n");
				sql.append("   , oo.outamt_other\n");
				sql.append("   , oo.outqty_stock_loss\n");
				sql.append("   , oo.outamt_stock_loss\n");
				sql.append("   , oo.outqty_discard\n");
				sql.append("   , oo.outamt_discard\n");
				sql.append("   , oo.outqty_proc\n");
				sql.append("   , oo.outamt_proc\n");
				sql.append("   , oo.outqty_sales\n");
				sql.append("   , oo.outamt_sales\n");
				sql.append("   , oo.out_avgprice\n");
				sql.append("   , 0 as qty_adjust\n");
				sql.append("   , 0 as amt_adjust\n");
				sql.append("   ,coalesce(ssh.stock_qty, 0) as stock_qty\n");
				sql.append("   ,coalesce(ssh.stock_amt, 0) as stock_amt\n");
				sql.append("   ,coalesce(ssh.stock_avgprice, 0) as stock_avgprice\n");
				sql.append("   ,coalesce(ssd.quantity, 0) as end_qty\n");
				sql.append("   ,coalesce(ssd.amount, 0) as end_amt\n");
				sql.append("   , 0 as sales_qty1\n");
				sql.append("   , 0 as sales_qty2\n");
				sql.append("   , 0 as sales_qty3\n");
				sql.append("   , 0 as sales_qty4\n");
				sql.append("   , 0 as sales_qty5\n");

				sql.append("   , sf.taxed_init_amt\n");
				sql.append("   , sf.tax_init_amt\n");
				sql.append("   , ii.taxed_inamt_supply\n");
				sql.append("   , ii.taxed_inamt_center\n");
				sql.append("   , ii.taxed_inamt_store\n");
				sql.append("   , ii.taxed_inamt_request\n");
				sql.append("   , ii.taxed_inamt_other\n");
				sql.append("   , ii.taxed_inamt_proce\n");
				sql.append("   , oo.taxed_outamt_supply\n");
				sql.append("   , oo.taxed_outamt_center\n");
				sql.append("   , oo.taxed_outamt_store\n");
				sql.append("   , oo.taxed_outamt_request\n");
				sql.append("   , oo.taxed_outamt_other\n");
				sql.append("   , oo.taxed_outamt_discard\n");
				sql.append("   , oo.taxed_outamt_consume\n");
				sql.append("   , ii.taxed_inamt_overflow\n");
				sql.append("   , oo.taxed_outamt_stock_loss\n");
				sql.append("   ,coalesce(ssh.taxed_stock_amt, 0) as taxed_stock_amt\n");
				sql.append("   ,coalesce(ssd.taxed_total_amt, 0) as taxed_end_amt\n");
				sql.append("   ,coalesce(ssd.tax, 0) as tax_end_amt\n");
				sql.append("  from scm_storage_detail sto\n");
				sql.append("  left join scm_material m on m.id=sto.material_id\n");
				sql.append("  left join scm_material_class mc on m.mateclass_id=mc.id\n");
				sql.append("  left join scm_material_price mp on mp.material_id=m.id and mp.store_id=sto.store_id\n");//新增关联查询出标准单价存入，原先为0
				sql.append("  left join scm_mate_unit u on u.id=m.unit_id\n");
				sql.append("  left join scm_warehouse w on w.id=sto.warehouse_id\n");
				sql.append("  left join ( \n");
				sql.append("    select\n");
				sql.append("       max(d.end_qty) as init_qty\n");
				sql.append("      ,max(d.end_amt) as init_amt\n");
				sql.append("      ,max(d.taxed_end_amt) as taxed_init_amt\n");
				sql.append("      ,max(d.tax_end_amt) as tax_init_amt\n");
				sql.append("      ,case when max(d.end_qty)=0 then 0 else round(coalesce((max(d.end_amt) / max(d.end_qty)),0),6) end as init_avgprice\n");
				sql.append("      ,d.warehouse_id\n");
				sql.append("      ,d.material_id \n");
				sql.append("    from scm_inout_daily_acount d\n");
				sql.append("    where d.tenancy_id='" + tenantid + "' and d.store_id=" + store_id + " \n");
				sql.append("      and d.busi_date=(\n");
				sql.append("        select \n");
				sql.append("          max(o.da_date) \n");
				sql.append("        from scm_organ o \n");
				sql.append("        where o.tenancy_id='" + tenantid + "' and o.store_id=" + store_id + "\n");
				sql.append("      )\n");
				sql.append("    group by d.warehouse_id,d.material_id\n");
				sql.append("  ) sf on sf.warehouse_id=w.id and sf.material_id=m.id\n");
				sql.append("  left join (\n");
				sql.append("    select \n");
				sql.append("       i.warehouse_id\n");
				sql.append("      , d.material_id\n");
				sql.append("      , sum(case when (i.in_type='supply_in' or i.in_type='order_dc') then d.qty else 0 end) as inqty_supply\n");
				sql.append("      , sum(case when (i.in_type='supply_in' or i.in_type='order_dc') then d.amt else 0 end) as inamt_supply\n");
				sql.append("      , sum(case when (i.in_type='supply_in' or i.in_type='order_dc') then d.taxed_total_amt else 0 end) as taxed_inamt_supply\n");
				sql.append("      , sum(case when i.in_type='dc_in' then d.qty else 0 end) as inqty_center\n");
				sql.append("      , sum(case when i.in_type='dc_in' then d.amt else 0 end) as inamt_center\n");
				sql.append("      , sum(case when i.in_type='dc_in' then d.taxed_total_amt else 0 end) as taxed_inamt_center\n");
				sql.append("      , sum(case when i.in_type='store_in' then d.qty else 0 end) as inqty_store\n");
				sql.append("      , sum(case when i.in_type='store_in' then d.amt else 0 end) as inamt_store\n");
				sql.append("      , sum(case when i.in_type='store_in' then d.taxed_total_amt else 0 end) as taxed_inamt_store\n");
				sql.append("      , sum(case when i.in_type in ('transfer','direct_stock','picking','return') then d.qty else 0 end) as inqty_request\n");
				sql.append("      , sum(case when i.in_type in ('transfer','direct_stock','picking','return') then d.amt else 0 end) as inamt_request\n");
				sql.append("      , sum(case when i.in_type in ('transfer','direct_stock','picking','return') then d.taxed_total_amt else 0 end) as taxed_inamt_request\n");
				sql.append("      , sum(case when i.in_type='process_in' then d.qty else 0 end) as inqty_proce, sum(case when i.in_type='process_in' then d.amt else 0 end) as inamt_proce\n");
				sql.append("      , sum(case when i.in_type='process_in' then d.taxed_total_amt else 0 end) as taxed_inamt_proce\n");
				sql.append("      , sum(case when i.in_type='profit' then d.qty else 0 end) as inqty_other, sum(case when i.in_type='profit' then d.amt else 0 end) as inamt_other\n");
				sql.append("      , sum(case when i.in_type='profit' then d.taxed_total_amt else 0 end) as taxed_inamt_other");
				sql.append("      , sum(case when i.in_type='stock_profit' then d.qty else 0 end) as inqty_overflow\n");
				sql.append("      , sum(case when i.in_type='stock_profit' then d.amt else 0 end) as inamt_overflow\n");
				sql.append("      , sum(case when i.in_type='stock_profit' then d.taxed_total_amt else 0 end) as taxed_inamt_overflow\n");
				sql.append("      , case when sum(d.qty) = 0 or sum(d.qty) is null then 0 else round((sum(d.amt) / sum(d.qty)),6) end as in_avgprice\n");
				sql.append("    from scm_in i \n");
				sql.append("    join scm_in_detail d on i.id=d.bill_id \n");
				sql.append("    where i.tenancy_id='" + tenantid + "' and i.store_id=" + store_id + " and i.in_date='" + day_count + "' \n");
				sql.append("    group by i.warehouse_id, d.material_id\n");
				sql.append("  ) ii on ii.warehouse_id=w.id and ii.material_id=m.id\n");
				sql.append("  left join (\n");
				sql.append("    select \n");
				sql.append("       i.warehouse_id\n");
				sql.append("      , d.material_id\n");
				sql.append("      , sum(case when i.out_type='supply_return' then d.qty else 0 end) as outqty_supply\n");
				sql.append("      , sum(case when i.out_type='supply_return' then d.amt else 0 end) as outamt_supply\n");
				sql.append("      , sum(case when i.out_type='supply_return' then d.taxed_total_amt else 0 end) as taxed_outamt_supply\n");
				sql.append("      , sum(case when i.out_type='dc_out' then d.qty else 0 end) as outqty_center\n");
				sql.append("      , sum(case when i.out_type='dc_out' then d.amt else 0 end) as outamt_center\n");
				sql.append("      , sum(case when i.out_type='dc_out' then d.taxed_total_amt else 0 end) as taxed_outamt_center\n");
				sql.append("      , sum(case when i.out_type='store_out' then d.qty else 0 end) as outqty_store\n");
				sql.append("      , sum(case when i.out_type='store_out' then d.amt else 0 end) as outamt_store\n");
				sql.append("      , sum(case when i.out_type='store_out' then d.taxed_total_amt else 0 end) as taxed_outamt_store\n");
				sql.append("      , sum(case when i.out_type  in ('transfer','direct_stock','picking','return') then d.qty else 0 end) as outqty_request\n");
				sql.append("      , sum(case when i.out_type  in ('transfer','direct_stock','picking','return') then d.amt else 0 end) as outamt_request\n");
				sql.append("      , sum(case when i.out_type  in ('transfer','direct_stock','picking','return') then d.taxed_total_amt else 0 end) as taxed_outamt_request\n");
				sql.append("      , sum(case when i.out_type='consume' then d.qty else 0 end) as outqty_consume\n");
				sql.append("      , sum(case when i.out_type='consume' then d.amt else 0 end) as outamt_consume\n");
				sql.append("      , sum(case when i.out_type='consume' then d.taxed_total_amt else 0 end) as taxed_outamt_consume\n");
				sql.append("      , sum(case when i.out_type='other_out' then d.qty else 0 end) as outqty_other\n");
				sql.append("      , sum(case when i.out_type='other_out' then d.amt else 0 end) as outamt_other\n");
				sql.append("      , sum(case when i.out_type='other_out' then d.taxed_total_amt else 0 end) as taxed_outamt_other\n");
				sql.append("      , sum(case when i.out_type='stock_loss' then d.qty else 0 end) as outqty_stock_loss\n");
				sql.append("      , sum(case when i.out_type='stock_loss' then d.amt else 0 end) as outamt_stock_loss\n");
				sql.append("      , sum(case when i.out_type='stock_loss' then d.taxed_total_amt else 0 end) as taxed_outamt_stock_loss\n");
				sql.append("      , sum(case when i.out_type='loss' then d.qty else 0 end) as outqty_discard\n");
				sql.append("      , sum(case when i.out_type='loss' then d.amt else 0 end) as outamt_discard\n");
				sql.append("      , sum(case when i.out_type='loss' then d.taxed_total_amt else 0 end) as taxed_outamt_discard\n");
				sql.append("      , sum(case when i.out_type='process_out' then d.qty else 0 end) as outqty_proc\n");
				sql.append("      , sum(case when i.out_type='process_out' then d.amt else 0 end) as outamt_proc\n");
				sql.append("      , sum(case when i.out_type='sales_out' then d.qty else 0 end) as outqty_sales\n");
				sql.append("      , sum(case when i.out_type='sales_out' then d.amt else 0 end) as outamt_sales\n");
				sql.append("      , case when sum(d.qty) = 0 or sum(d.qty) is null then 0 else round((sum(d.amt) / sum(d.qty)),6) end as out_avgprice\n");
				sql.append("    from scm_out i \n");
				sql.append("    join scm_out_detail d on i.id=d.bill_id \n");
				sql.append("    where i.tenancy_id='" + tenantid + "' and i.store_id=" + store_id + " and i.out_date='" + day_count + "' \n");
				sql.append("    group by i.warehouse_id, d.material_id \n");
				sql.append("  ) oo on oo.warehouse_id=w.id and oo.material_id=m.id\n");
				sql.append("  left join (\n");
				sql.append("    select  \n");
				sql.append("      xx.sn,xx.material_id,xx.warehouse_id,xx.stock_qty,xx.stock_amt,xx.taxed_stock_amt,xx.stock_avgprice\n");
				sql.append("    from (\n");
				sql.append("       select\n");
				sql.append("           row_number() over(partition by warehouse_id,material_id order by d.id desc) as sn\n");
				sql.append("          ,d.material_id\n");
				sql.append("          ,s.warehouse_id\n");
				sql.append("          ,d.fact_qty as stock_qty\n");
				sql.append("          ,d.stock_amt as stock_amt\n");
				sql.append("          ,d.taxed_total_amt as taxed_stock_amt\n");
				sql.append("          ,round(case when d.fact_qty=0 then 0 else (d.stock_amt / d.fact_qty) end, 6) as stock_avgprice\n");
				sql.append("       from scm_stocktaking_history s\n");
				sql.append("       join scm_stocktaking_detail_history d on s.id=d.bill_id \n");
				sql.append("       where s.tenancy_id='" + tenantid + "' and s.store_id=" + store_id + " and s.busi_date='" + day_count + "'\n");
				sql.append("     ) xx where sn=1\n");
				sql.append("  ) ssh on ssh.warehouse_id=w.id and ssh.material_id=m.id\n");
				sql.append("  left join scm_storage_detail ssd on ssd.tenancy_id='" + tenantid + "' and ssd.store_id=" + store_id + " and ssd.warehouse_id=w.id and ssd.material_id=m.id\n");
				sql.append("  where sto.tenancy_id='" + tenantid + "' and sto.store_id=" + store_id + "\n");

				dao.execute(tenantid, sql.toString());
				
				
				//每月最后一天，记录下个月的期初数据  暂时供chanpinzhichiceshi1商户
				if("chanpinzhichiceshi1".equals(tenantid)){
					
					String date=String.valueOf(day_count).substring(0,7);
					JSONObject dateJson=DateUtil.getFirstAndLastDate(date);
					if(String.valueOf(day_count).equals(dateJson.optString("end_day"))){//判断是否月末
						
						sql.setLength(0);
						sql.append("insert into scm_daily_month_init( tenancy_id, store_id, warehouse_id, material_id, theory_qty, theory_amt, fact_qty, fact_amt, theory_diff_qty, theory_diff_amt, fact_diff_qty, fact_diff_amt, init_date)\n");
						
						sql.append(" select ");
						sql.append("    '" + tenantid + "' as tenancy_id\n");
						sql.append("   ," + store_id + " as store_id\n");
						sql.append("   ,t.warehouse_id, t.material_id\n");
						sql.append("   ,case when t.last_theory_qty<0 then 0 else t.last_theory_qty end as last_theory_qty --理论 期初 数量\n");
						sql.append("   ,case when t.last_theory_qty<0 then 0 else t.rk_theory_amt-round(t.ck_theory_qty*t.rk_theory_price,6) end as last_theory_amt -- 理论 期初 金额\n");
						sql.append("   ,case when t.last_fact_qty<0 then 0 else t.last_fact_qty end as last_fact_qty --实际 期初 数量\n");
						sql.append("   ,case when t.last_fact_qty<0 then 0 else t.rk_fact_amt-round(t.ck_fact_qty*t.rk_fact_price,6) end as last_fact_amt -- 实际 期初 金额\n");
						sql.append("   ,case when t.last_theory_qty<0 then t.last_theory_qty else 0 end as last_theory_diff_qty --理论 差异 期初 数量\n");
						sql.append("   ,case when t.last_theory_qty<0 then t.rk_theory_amt-round(t.ck_theory_qty*t.rk_theory_price,6) else 0 end as last_theory_diff_amt -- 理论 差异 期初 金额\n");
						sql.append("   ,case when t.last_fact_qty<0 then t.last_fact_qty else 0 end as last_fact_diff_qty --实际 差异 期初 数量\n");
						sql.append("   ,case when t.last_fact_qty<0 then t.rk_fact_amt-round(t.ck_fact_qty*t.rk_fact_price,6) else 0 end as last_fact_diff_amt -- 实际 差异 期初 金额\n");
						sql.append("   ,'"+DateUtil.getNextDayYYYYMMDD(day_count)+"' as init_date\n");
						sql.append("    from  (select sd.store_id,sd.warehouse_id, sd.material_id\n");
						sql.append("   ,coalesce(rksj.m_in_qty,0) rk_qty --入库数量\n");
						sql.append("   ,coalesce(cksj.m_out_qty,0)+coalesce(xhsj.stand_consumption_qty,0) as ck_theory_qty --理论 出库 数量\n");
						sql.append("   ,coalesce(cksj.m_out_qty,0)+coalesce(cksj_sj.m_out_qty_sj,0) as ck_fact_qty --实际 出库 数量\n");
						sql.append("   ,coalesce(qcsj.theory_qty,0)+coalesce(rksj.m_in_qty,0)-coalesce(cksj.m_out_qty,0)-coalesce(xhsj.stand_consumption_qty,0) as last_theory_qty --理论计算数量\n");
						sql.append("   ,coalesce(qcsj.fact_qty,0)+coalesce(rksj.m_in_qty,0)-coalesce(cksj.m_out_qty,0)-coalesce(cksj_sj.m_out_qty_sj,0) as last_fact_qty --实际计算数量\n");
						sql.append("   ,coalesce(qcsj.theory_amt,0)+coalesce(rksj.m_in_amt,0) as rk_theory_amt --理论 入库+期初 金额\n");
						sql.append("   ,coalesce(qcsj.fact_amt,0)+coalesce(rksj.m_in_amt,0) as rk_fact_amt --实际 入库 金额\n");
						sql.append("   ,case when (coalesce(qcsj.theory_qty,0)+coalesce(rksj.m_in_qty,0))=0 then 0 else round((coalesce(qcsj.theory_amt,0)+coalesce(rksj.m_in_amt,0))/(coalesce(qcsj.theory_qty,0)+coalesce(rksj.m_in_qty,0)),6) end as rk_theory_price --理论 入库 单价\n");
						sql.append("   ,case when (coalesce(qcsj.fact_qty,0)+coalesce(rksj.m_in_qty,0))=0 then 0 else round((coalesce(qcsj.fact_amt,0)+coalesce(rksj.m_in_amt,0))/(coalesce(qcsj.fact_qty,0)+coalesce(rksj.m_in_qty,0)),6) end as rk_fact_price --实际 入库 单价\n");
						sql.append("   from (select sd.store_id,sd.warehouse_id, sd.material_id from scm_storage_detail sd  \n");
						sql.append("   where sd.tenancy_id='" + tenantid + "' and sd.store_id=" + store_id + " group by sd.store_id,sd.warehouse_id,sd.material_id ) sd\n");
						sql.append("   left join \n");
						sql.append("   (select dmi.store_id,dmi.warehouse_id, dmi.material_id\n");
						sql.append("   ,dmi.theory_qty --期初数量 理论\n");
						sql.append("   ,dmi.theory_amt --期初金额 理论\n");
						sql.append("   ,dmi.fact_qty --期初数量 实际\n");
						sql.append("   ,dmi.fact_amt --期初金额 实际\n");
						sql.append("   from scm_daily_month_init dmi\n");
						sql.append("    where dmi.init_date='" + dateJson.optString("start_day") + "' and dmi.tenancy_id='" + tenantid + "' and dmi.store_id=" + store_id + ") qcsj\n");
						sql.append("    on qcsj.store_id=sd.store_id and qcsj.warehouse_id=sd.warehouse_id and qcsj.material_id=sd.material_id\n");
						sql.append("    left join\n");
						
						sql.append("   (select i.store_id,i.warehouse_id, d.material_id\n");
						sql.append("   ,sum(d.qty) as m_in_qty  --月入库数量 (配送、直配、采购、平调、其他、盘盈、手动报溢)\n");
						sql.append("   ,sum(d.amt) as m_in_amt  --月入库金额\n");
						sql.append("   from scm_in i join scm_in_detail d on i.id=d.bill_id\n");
						sql.append("   where i.in_date>='" + dateJson.optString("start_day") + "' and i.in_date<='" + dateJson.optString("end_day") + "' and i.tenancy_id='" + tenantid + "' and i.store_id=" + store_id + "\n");
						sql.append("   and i.in_type in ('dc_in','supply_in','store_in','order_dc','other_in','stock_profit','profit')\n");
						sql.append("   group by i.store_id,i.warehouse_id, d.material_id) rksj\n");
						sql.append("   on rksj.store_id=sd.store_id and rksj.warehouse_id=sd.warehouse_id and rksj.material_id=sd.material_id\n");
						
						sql.append("   left join\n");
						sql.append("   (select i.store_id,i.warehouse_id, d.material_id\n");
						sql.append("   ,sum(d.qty) as m_out_qty  --月出库数量 (平调出库、手工报损、配送退货、供应商退货、其他退货)\n");
						sql.append("    from scm_out i join scm_out_detail d on i.id=d.bill_id\n");
						sql.append("    where i.out_date>='" + dateJson.optString("start_day") + "' and i.out_date<='" + dateJson.optString("end_day") + "' and i.tenancy_id='" + tenantid + "' and i.store_id=" + store_id + "  and i.out_type in ('store_out','loss','dc_out','supply_return','other_out')\n");
						sql.append("   group by i.store_id,i.warehouse_id, d.material_id) cksj\n");
						sql.append("    on cksj.store_id=sd.store_id and cksj.warehouse_id=sd.warehouse_id and cksj.material_id=sd.material_id\n");
						
						sql.append("   left join\n");
						sql.append("   (select i.store_id,i.warehouse_id, d.material_id\n");
						sql.append("   ,sum(d.qty) as m_out_qty_sj  --月出库数量 (实际消耗)\n");
						sql.append("    from scm_out i join scm_out_detail d on i.id=d.bill_id\n");
						sql.append("    where i.out_date>='" + dateJson.optString("start_day") + "' and i.out_date<='" + dateJson.optString("end_day") + "' and i.tenancy_id='" + tenantid + "' and i.store_id=" + store_id + "  and i.out_type in ('stock_loss','consume')\n");
						sql.append("   group by i.store_id,i.warehouse_id, d.material_id) cksj_sj\n");
						sql.append("    on cksj_sj.store_id=sd.store_id and cksj_sj.warehouse_id=sd.warehouse_id and cksj_sj.material_id=sd.material_id\n");
						
						sql.append("   left join ( select stc.store_id ,stc.warehouse_id  ,stc.material_id\n");
						sql.append("   ,coalesce(sum(stc.theory_consumption_amt), 0) as stand_consumption_qty ----月出库数量 (理论消耗)\n");
						sql.append("   from scm_thery_consumption stc\n");
						sql.append("   where stc.tenancy_id='" + tenantid + "' and stc.store_id=" + store_id + " and stc.busi_date>='" + dateJson.optString("start_day") + "' and stc.busi_date<='" + dateJson.optString("end_day") + "'\n");
						sql.append("   group by stc.store_id,stc.warehouse_id,stc.material_id) xhsj\n");
						sql.append("   on xhsj.store_id=sd.store_id and xhsj.warehouse_id=sd.warehouse_id and xhsj.material_id=sd.material_id) t\n");
						dao.execute(tenantid, sql.toString());
					}
				}
				// 修改标记
				sql.setLength(0);
				sql.append("update scm_organ set da_date='" + param.optString("day_count") + "' where tenancy_id='" + tenantid + "' and store_id=" + store_id);
				dao.execute(tenantid, sql.toString());

				status = "1";
			}

			param.element("status", status);

			sql.setLength(0);
			sql.append("update hq_daycount_info set scm_sign='" + status + "' where store_id=" + store_id + " and day_count='" + param.optString("day_count") + "'");
			dao.execute(tenantid, sql.toString());
		}
		catch (Exception e)
		{
			// sql.setLength(0);
			// sql.append("update hq_daycount_info set scm_sign=null where store_id="
			// + store_id + " and day_count='" + param.optString("day_count") +
			// "'");
			// dao.execute(tenantid, sql.toString());
			throw e;
		}
	}

	/**
	 * 会员数据
	 *
	 * @param tenantid
	 * @param param
	 * @throws Exception
	 */
	private void crmDailySettlement(String tenantid, JSONObject param) throws Exception
	{
		String status = "0";

		if(isCountCrm(tenantid, param)){

			status = "1";
			StringBuilder sql = new StringBuilder();

			sql.append("select f_int_crm_daily_count(" + param.optInt("store_id") + ",'" + param.optString("day_count") + "'::date);");
			dao.execute(tenantid, sql.toString());

			sql.setLength(0);
			sql.append("update hq_daycount_info set crm_sign='" + status + "' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'");
			dao.execute(tenantid, sql.toString());

		}
		param.element("status", status);
	}

	/**
	 * 营业数据(优化版 )
	 *
	 * @param tenantid
	 * @param param
	 * @throws Exception
	 */
	@SuppressWarnings("null")
	private void newHqDailySettlement(String tenantid, JSONObject param) throws Exception
	{
		int store_id = param.optInt("store_id");
		Date day_count = Date.valueOf(param.optString("day_count"));
		// 更改状态-正在统计
		String sql = "update hq_daycount_info set hq_sign='100' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
		dao.execute(tenantid, sql);

		sql = "select remark from pos_opt_state where store_id=? and report_date=? and content='DAYEND'";
		List<JSONObject> list = dao.query4Json(tenantid, sql, new Object[]
				{ store_id, day_count });

		if (list.size() == 0)
		{
			sql = "update hq_daycount_info set hq_sign=null where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
			dao.execute(tenantid, sql);
			throw new Exception("门店 '" + param.optString("day_count") + "' 未打烊，不能进行日结操作！");
		}

		StringBuilder mqsql = new StringBuilder("select 'billCount' as k, count(*) as v from pos_bill where report_date='" + param.optString("day_count") + "' and bill_property='CLOSED' and store_id=" + store_id + " union all");
		mqsql.append(" select 'billItemsCount' as k, count(*) as v from pos_bill_item where report_date='" + param.optString("day_count") + "' and store_id=" + store_id + " union all");
		mqsql.append(" select 'paymentCount' as k, count(*) as v from pos_bill_payment where report_date='" + param.optString("day_count") + "' and store_id=" + store_id + " union all");
		mqsql.append(" select 'paymentAmount' as k, sum(payment_amount) as v from pos_bill where report_date='" + param.optString("day_count") + "' and bill_property='CLOSED' and store_id=" + store_id);

		JSONObject mqObj = new JSONObject();
		SqlRowSet mqRs = dao.query(tenantid, mqsql.toString());
		while (mqRs.next())
		{
			mqObj.put(mqRs.getString("k"), mqRs.getDouble("v"));
		}

		JSONObject remark = JSONObject.fromObject(list.get(0).optString("remark").replace("$$$", ""));

		if (mqObj.optInt("billCount") == remark.optInt("billCount") && mqObj.optInt("billItemsCount") == remark.optInt("billItemsCount") && mqObj.optInt("paymentCount") == remark.optInt("paymentCount") && mqObj.optInt("paymentAmount") == remark.optInt("paymentAmount"))
		{

			// 临时性解决措施，修复门店错误的支付记录数据
			String repairPayment = "update pos_bill_payment p set currency_amount=round((p.amount * (case when p.rate=0 or p.rate is null then 1 else p.rate end)),2) where p.bill_num in (select bill_num from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count")
					+ "' and bill_property='CLOSED')";

			// 导入2
			String insertPosBill = "insert into pos_bill2 (tenancy_id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time,payment_num,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id,service_amount,service_discount,order_num,print_time,print_count,subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,discount_rate,billfree_reason_id,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,deposit_count,copy_bill_num,source,opt_login_number,guest_msg,integraloffset,remark,return_amount,advance_payment_amt,advance_refund_amt,is_refund,payment_id,pay_no,third_bill_code,payment_state,payment_manager_num,shop_real_amount,total_fees,platform_charge_amount,tax_rate,tax_money,service_tax_rate,service_tax_money,tax_amount,no_tax_amount,payment_tax_money, payment_notax,bill_tax_money,bill_notax,service_notax,settlement_price) select tenancy_id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time,payment_num,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id,service_amount,service_discount,order_num,print_time,print_count,subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,discount_rate,billfree_reason_id,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,deposit_count,copy_bill_num,source,opt_login_number,guest_msg,integraloffset,remark,return_amount,advance_payment_amt,advance_refund_amt,is_refund,payment_id,pay_no,third_bill_code,payment_state,payment_manager_num,shop_real_amount,total_fees,platform_charge_amount,tax_rate,tax_money,service_tax_rate,service_tax_money,tax_amount,no_tax_amount,payment_tax_money, payment_notax,bill_tax_money,bill_notax,service_notax,settlement_price from pos_bill where store_id="
					+ store_id + " and report_date='" + param.optString("day_count") + "' and bill_property='CLOSED' order by id";
			String insertPosBillItem = "insert into pos_bill_item2 (tenancy_id,store_id,rwid,yrwid,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_remark,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,report_date,item_shift_id,item_mac_id,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag,assist_item_id,integraloffset,remark,setmeal_group_id,group_id,third_price,returngive_reason_id,manager_num,return_type,return_count,method_money,batch_num,tax_rate,tax_money,item_notax,payment_tax_money,payment_notax,opt_num,item_remark_his,is_consignment,settlement_price) select d.tenancy_id, d.store_id, d.rwid, d.yrwid, d.bill_num, d.details_id, d.item_id, d.item_num, d.item_name, d.item_english, d.item_unit_id, d.item_unit_name, d.stable_code, d.table_code, d.pushmoney_way, d.proportion, d.assist_num, d.assist_money, d.waiter_num, d.item_price, d.item_count, d.item_amount, d.real_amount, d.discount_amount, d.single_discount_amount, d.discountr_amount, d.discount_state, d.discount_rate, d.discount_mode_id, d.item_class_id, d.item_property, d.item_remark, d.print_tag, d.waitcall_tag, d.setmeal_id, d.setmeal_rwid, d.is_setmeal_changitem, d.item_time, d.item_serial, d.report_date, d.item_shift_id, d.item_mac_id, d.item_taste, d.order_remark, d.seat_num, d.ticket_num, d.sale_mode, d.gqcj_tag, d.kvscp_tag, d.discount_reason_id, d.is_showitem, d.upload_tag, d.assist_item_id, d.integraloffset, d.remark, d.setmeal_group_id, d.group_id, d.third_price, d.returngive_reason_id, d.manager_num, d.return_type, d.return_count, d.method_money, d.batch_num,d.tax_rate,d.tax_money,d.item_notax,d.payment_tax_money,d.payment_notax,d.opt_num,d.item_remark_his,d.is_consignment,d.settlement_price from pos_bill_item d left join pos_bill m on d.bill_num=m.bill_num where m.store_id="
					+ store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED' order by d.rwid";
			String insertPosBillPayment = "insert into pos_bill_payment2 (tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,param_cach,more_coupon,batch_num,fee,fee_rate) select d.tenancy_id, d.store_id, d.bill_num, d.table_code, d.type, d.jzid, d.name, d.name_english, d.amount, d.count, d.number, d.phone, d.report_date, d.shift_id, d.pos_num, d.cashier_num, d.last_updatetime, d.is_ysk, d.rate, d.currency_amount, d.upload_tag, d.customer_id, d.bill_code, d.remark,d.payment_state,d.param_cach,d.more_coupon,d.batch_num,d.fee,d.fee_rate from pos_bill_payment d left join pos_bill m on d.bill_num=m.bill_num where m.store_id="
					+ store_id + " and m.report_date='" + param.optString("day_count") + "' and m.bill_property='CLOSED' order by d.id";

			// 统计

			// 异常原因统计
			StringBuilder unusualReason = new StringBuilder();

			unusualReason.append(" insert into hq_daycount_unusual(tenancy_id,store_id,day_count,unusual_type,unusual_id,bill_num,bill_money,unusual_money,remark)");
			unusualReason.append(" select '" + tenantid + "' as tenancy_id," + store_id + " as store_id,to_date('" + param.optString("day_count") + "', 'YYYY-MM-DD') as day_count,");
			unusualReason.append(" r1.unusual_type as unusual_type,m1.billfree_reason_id as unusual_id,count(m1.bill_num) as bill_num,sum(m1.bill_amount) as bill_money,sum(m1.bill_amount) as unusual_money,'免单原因统计' as remark");
			unusualReason.append(" from pos_bill m1 ");
			unusualReason.append(" left join hq_unusual_reason r1 on m1.billfree_reason_id = r1.id");
			unusualReason.append(" where m1.billfree_reason_id is not null");
			unusualReason.append(" and m1.billfree_reason_id != 0");
			unusualReason.append(" and m1.store_id = " + store_id);
			unusualReason.append(" and m1.report_date = '" + param.optString("day_count") + "'");
			unusualReason.append(" and m1.bill_property = 'CLOSED'");
			unusualReason.append(" group by m1.billfree_reason_id, r1.unusual_type");
			unusualReason.append(" union all");
			unusualReason.append(" select '" + tenantid + "' as tenancy_id," + store_id + " as store_id,to_date('" + param.optString("day_count")
					+ "', 'YYYY-MM-DD') as day_count,m.unusual_type,m.unusual_id,count(m.bill_num) as bill_num,sum(m.bill_amount) as bill_money,sum(m.unusual_money) as unusual_money,'退菜奉送原因统计' as remark");
			unusualReason
					.append(" from (select r1.unusual_type as unusual_type,d1.returngive_reason_id as unusual_id,m1.bill_num,m1.bill_amount,sum(d1.real_amount) as unusual_money from pos_bill_item d1 join pos_bill m1 on d1.bill_num = m1.bill_num left join hq_unusual_reason r1 on d1.returngive_reason_id = r1.id");
			unusualReason.append("   where d1.returngive_reason_id is not null and d1.returngive_reason_id != 0 and m1.store_id = " + store_id + " and m1.report_date = '" + param.optString("day_count") + "' and m1.bill_property = 'CLOSED'");
			unusualReason.append("   group by m1.bill_num,m1.bill_amount,d1.returngive_reason_id,r1.unusual_type");
			unusualReason.append(" ) m ");
			unusualReason.append(" group by m.unusual_id, m.unusual_type");
			unusualReason.append(" union all");
			unusualReason.append(" select '" + tenantid + "' as tenancy_id," + store_id + " as store_id,to_date('" + param.optString("day_count")
					+ "', 'YYYY-MM-DD') as day_count,m.unusual_type,m.unusual_id,count(m.bill_num) as bill_num,sum(m.bill_amount) as bill_money,sum(m.unusual_money) as unusual_money,'折扣原因统计' as remark");
			unusualReason
					.append(" from (select r1.unusual_type as unusual_type,d1.discount_reason_id as unusual_id,m1.bill_num,m1.bill_amount,(sum(d1.discount_amount) + sum(d1.discountr_amount)) as unusual_money from pos_bill_item d1 join pos_bill m1 on d1.bill_num = m1.bill_num left join hq_unusual_reason r1 on d1.discount_reason_id = r1.id");
			unusualReason.append("   where d1.discount_reason_id is not null and d1.discount_reason_id != 0 and m1.store_id = " + store_id + " and m1.report_date = '" + param.optString("day_count") + "' and m1.bill_property = 'CLOSED'");
			unusualReason.append("   group by m1.bill_num,m1.bill_amount,d1.discount_reason_id,r1.unusual_type) m");
			unusualReason.append(" group by m.unusual_id, m.unusual_type");

			// 付款方式统计
			StringBuilder payment = new StringBuilder();

			payment.append(" insert into hq_payment_count(tenancy_id,store_id,day_count,payment_class,payment_id,pay_money,local_currency,sale_total)");
			payment.append(" select '" + tenantid + "' as tenancy_id," + store_id + " as store_id,to_date('" + param.optString("day_count") + "', 'YYYY-MM-DD') as day_count,");
			payment.append(" w.payment_class,p.jzid as payment_id,sum(p.amount) as pay_money,sum(p.currency_amount) as local_currency,sum(m.bill_amount) as sale_total");
			payment.append(" from pos_bill_payment p");
			payment.append(" join payment_way w on p.jzid = w.id");
			payment.append(" left join pos_bill m");
			payment.append(" on p.bill_num = m.bill_num");
			payment.append(" where p.store_id = " + store_id + " and p.report_date = to_date('" + param.optString("day_count") + "','yyyy-mm-dd') and m.bill_property = 'CLOSED'");
			payment.append(" group by w.payment_class, p.jzid");

			// 菜品日营业汇总
			/*StringBuilder item = new StringBuilder();

			item.append(" insert into hq_daycount_item(tenancy_id,store_id,day_count,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,list_num,");
			item.append(" list_money,sales_num,sales_money,back_num,back_money,free_num,free_money,favor_money,discount_money,reduction_money,change_money,actual_num,actual_money,net_money,");
			item.append(" sale_billnum,sale_billaverage,table_property_id,business_area_id,guest_num,tables_num)");
			item.append(" select '" + tenantid + "' as tenancy_id," + store_id + " as store_id,to_date('" + param.optString("day_count") + "', 'YYYY-MM-DD') as day_count,");
			item.append(" sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,sum(list_num) as list_num,sum(list_money) as list_money,");
			item.append(" sum(sales_num) as sales_num,sum(sales_money) as sales_money,sum(back_num) as back_num,sum(back_money) as back_money,sum(free_num) as free_num,");
			item.append(" sum(free_money) as free_money,sum(favor_money) as favor_money,sum(discount_money) as discount_money,sum(reduction_money) as reduction_money,sum(change_money) as change_money,");
			item.append(" sum(actual_num) as actual_num,sum(actual_money) as actual_money,sum(net_money) as net_money,max(p.sale_billnum) sale_billnum,null sale_billaverage,table_property_id,business_area_id,");
			item.append(" coalesce(sum(guest_counts), 0) as guest_num, max(p.sale_billnum) as tables_num");
			item.append(" from (select d.store_id, d.report_date, count( d.bill_num) as sale_billnum from pos_bill d");
			item.append(" where d.store_id = " + store_id + " and d.report_date = '" + param.optString("day_count") + "'and d.bill_property = 'CLOSED'and coalesce(d.bill_state, 'zc') not in ('CJ01', 'ZDQX02')");
			item.append(" group by d.store_id, d.report_date ) p");
			item.append(" join (select d.store_id,d.report_date,d.sale_mode as sale_model,d.setmeal_id  as combo_id,case d.item_property when 'SETMEAL' then d.item_unit_id end as combo_unit_id,");
			item.append(" case when d.item_property = 'SETMEAL' then d.item_price end as combo_price,d.item_id,d.item_unit_id,d.item_price as item_unit_price,d.item_property as item_pro,case when d.item_remark in ('CJ05') then 0 else d.item_count end as list_num,");
			item.append(" case when d.item_remark in ('CJ05') then 0 else d.item_amount end as list_money,d.item_count as sales_num,d.item_amount as sales_money,");
			item.append(" case when d.item_remark in ('TC01', 'QX04') then coalesce(item_count, 0) else 0 end as back_num,");
			item.append(" case when d.item_remark in ('TC01', 'QX04') then coalesce(real_amount, 0) else 0 end as back_money,");
			item.append(" case when d.item_remark = 'FS02' then coalesce(d.item_count, 0) else 0 end as free_num,");
			item.append(" case when d.item_remark = 'FS02' then coalesce(d.item_amount, 0) else 0 end as free_money,");
			item.append(" (d.discount_amount + d.discountr_amount - d.single_discount_amount) as favor_money, case when d.item_remark in ('TC01', 'FS02') then 0 else (d.discount_amount) end as discount_money,");
			item.append(" case when d.item_remark in ('TC01', 'FS02') then 0 else (d.discountr_amount) end as reduction_money,d.single_discount_amount as change_money,");
			item.append(" case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05') then 0 else d.item_count end as actual_num,");
			item.append(" case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05') then 0 else d.real_amount end as actual_money,");
			item.append(" case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05') then 0 else d.real_amount end as net_money,");
			item.append(" m.bill_num, t.table_property_id, t.business_area_id, m.guest as guest_counts, m.bill_state");
			item.append(" from pos_bill_item d");
			item.append(" join pos_bill m on d.bill_num = m.bill_num");
			item.append(" left join tables_info t on t.table_code = m.table_code and organ_id = " + store_id + "");
			item.append(" where m.store_id = " + store_id + " and m.report_date = '" + param.optString("day_count") + "'and m.bill_property = 'CLOSED') a");
			item.append(" on p.store_id = a.store_id and p.report_date = a.report_date");
			item.append(" group by sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,table_property_id,business_area_id");

			StringBuilder upItem = new StringBuilder();

			upItem.append(" update hq_daycount_item t set combo_unit_id = t1.combo_unit_id,combo_price = t1.combo_price");
			upItem.append(" from hq_daycount_item t1");
			upItem.append(" where t.combo_id = t1.combo_id");
			upItem.append(" and t1.item_pro = 'SETMEAL' and t.item_pro = 'MEALLIST'");
			upItem.append(" and t.day_count = '" + param.optString("day_count") + "' and t.store_id=" + store_id);
*/
			// 菜品日营业汇总分时段
			/*StringBuilder itemTimes = new StringBuilder();

			itemTimes.append(" insert into hq_daycount_item_times(tenancy_id,store_id,day_count,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,list_num,");
			itemTimes.append(" list_money,sales_num,sales_money,back_num,back_money,free_num,free_money,favor_money,change_money,actual_num,actual_money,net_money,sale_billnum,sale_billaverage,times_id)");
			itemTimes.append(" select '" + tenantid + "' as tenancy_id," + store_id + " as store_id,to_date('" + param.optString("day_count") + "', 'YYYY-MM-DD') as day_count,sale_model,combo_id,combo_unit_id,combo_price,");
			itemTimes.append(" item_id,item_unit_id,item_unit_price,item_pro,sum(list_num) as list_num,sum(list_money) as list_money,sum(sales_num) as sales_num,sum(sales_money) as sales_money,");
			itemTimes.append(" sum(back_num) as back_num,sum(back_money) as back_money,sum(free_num) as free_num,sum(free_money) as free_money,sum(favor_money) as favor_money,sum(change_money) as change_money,");
			itemTimes.append(" sum(actual_num) as actual_num,sum(actual_money) as actual_money,sum(net_money) as net_money,max(sale_billnum) sale_billnum,null sale_billaverage,times_id");
			itemTimes.append(" from (select d.store_id, d.report_date,count( d.bill_num) as sale_billnum from pos_bill d");
			itemTimes.append(" where d.store_id = " + store_id + " and d.report_date = '" + param.optString("day_count") + "' and d.bill_property = 'CLOSED' and coalesce(d.bill_state, 'zc') not in ('CJ01', 'ZDQX02')");
			itemTimes.append(" group by d.store_id, d.report_date) p");
			itemTimes.append(" join (select d.store_id,d.report_date,m.sale_mode as sale_model,d.setmeal_id as combo_id,case d.item_property when 'SETMEAL' then d.item_unit_id end as combo_unit_id,");
			itemTimes.append(" case when d.item_property = 'SETMEAL' then d.item_price end as combo_price,d.item_id,d.item_unit_id,d.item_price as item_unit_price,d.item_property as item_pro,");
			itemTimes.append(" case when d.item_remark in ('CJ05') then 0 else d.item_count end as list_num,");
			itemTimes.append(" case when d.item_remark in ('CJ05') then 0 else d.item_amount end as list_money,");
			itemTimes.append(" d.item_count as sales_num, d.item_amount as sales_money,");
			itemTimes.append(" case when d.item_remark in ('TC01', 'QX04') then coalesce(return_count, 0) else 0 end as back_num,");
			itemTimes.append(" case when d.item_remark in ('TC01', 'QX04') then coalesce(real_amount, 0) else 0 end as back_money,");
			itemTimes.append(" case when d.item_remark = 'FS02' then coalesce(return_count, 0) else 0 end as free_num,");
			itemTimes.append(" case when d.item_remark = 'FS02' then coalesce(real_amount, 0) else 0 end as free_money,");
			itemTimes.append(" case when d.item_remark in ('TC01', 'FS02') then 0 else (d.discount_amount + d.discountr_amount - d.single_discount_amount) end as favor_money,");
			itemTimes.append(" d.single_discount_amount as change_money,");
			itemTimes.append(" case when d.item_remark in ('TC01', 'FS02') then 0 else d.item_count end as actual_num,");
			itemTimes.append(" case when d.item_remark in ('TC01', 'FS02') then 0 else (d.item_amount - d.discount_amount - d.discountr_amount) end as actual_money,");
			itemTimes.append(" case when d.item_remark in ('TC01', 'FS02') then 0 else d.real_amount end as net_money,");
			itemTimes.append(" m.bill_num,");
			itemTimes.append(" s.time_id as times_id,");
			itemTimes.append(" m.bill_state");
			itemTimes.append(" from pos_bill_item d join pos_bill m on d.bill_num = m.bill_num");
			itemTimes.append(" left join sys_times s on s.end_time = CASE WHEN to_char(m.payment_time, 'mi') >= '30' THEN to_char(m.payment_time + interval '1 HOUR', 'hh24') || ':' || '00' ELSE to_char(m.payment_time, 'hh24') || ':' || '30' END");
			itemTimes.append(" where m.store_id = " + store_id + " and m.report_date = '" + param.optString("day_count") + "'and m.bill_property = 'CLOSED' and s.time_type = 'half') a");
			itemTimes.append(" on p.store_id = a.store_id and p.report_date = a.report_date");
			itemTimes.append(" group by sale_model, combo_id, combo_unit_id, combo_price, item_id, item_unit_id, item_unit_price, item_pro, times_id");

			StringBuilder upItemTimes = new StringBuilder();

			upItemTimes.append(" update hq_daycount_item_times t set combo_unit_id = t1.combo_unit_id,combo_price= t1.combo_price");
			upItemTimes.append(" from hq_daycount_item_times t1");
			upItemTimes.append(" where t.combo_id = t1.combo_id");
			upItemTimes.append(" and t1.item_pro = 'SETMEAL' and t.item_pro = 'MEALLIST'");
			upItemTimes.append(" and t.day_count = '" + param.optString("day_count") + "' and t.store_id=" + store_id);
*/
			// 现金日报
			StringBuilder daucount = new StringBuilder();

			daucount.append(" insert into hq_daycount(tenancy_id,store_id,day_count,sale_total,sale_billnum,sale_billaverage,discount_money,reduction_money,");
			daucount.append(" coupons_money,total_card,main_trading,customer_discount,customer_reward_sale,customer_cash_money,moling_money,free_money,");
			daucount.append(" item_list_money,item_actual_money,ts_actual_money,ws_actual_money,wd_actual_money,item_yh_money,item_discount_money,item_reduction_money,");
			daucount.append(" free_money_item,back_money_item,item_net_money,other_income,service_fee_income,yyw_income,accounts_receivable,customer_recharge_total,");
			daucount.append(" customer_recharge_income,customer_recharge_reward,customer_recharge_cash,customer_recharge_bank,customer_recharge_zfb,customer_recharge_wx,");
			daucount.append(" payment_total,coupons_ds,deposit_money_actual,sale_person_num,sale_person_average,attendance_num,remark,tables_num,seat_num,backsingle_num,backwhole_num,backcopy_num)");
			daucount.append(" select '" + tenantid + "' as tenancy_id," + store_id + " as store_id,to_date('" + param.optString("day_count") + "', 'YYYY-MM-DD') as day_count,");
			daucount.append(" p.sale_total,p.sale_billnum,p.sale_billaverage,p.discount_money,p.reduction_money,a.coupons_money,a.total_card,b.main_trading,a.customer_discount,b.customer_reward_sale,");
			daucount.append(" 0 as customer_cash_money,p.moling_money,p.free_money,c.item_list_money,p.item_actual_money,c.ts_actual_money,c.ws_actual_money,c.wd_actual_money,");
			daucount.append(" c.item_yh_money,c.item_discount_money,c.item_reduction_money,c.free_money_item,c.back_money_item,c.item_net_money,coalesce(d.other_income, 0) other_income,coalesce(p.service_fee_income, 0) service_fee_income,");
			daucount.append(" coalesce(d.yyw_income, 0) yyw_income,coalesce(b.accounts_receivable, 0) accounts_receivable,coalesce(b.customer_recharge_total, 0) customer_recharge_total,");
			daucount.append(" coalesce(b.customer_recharge_income, 0) customer_recharge_income,coalesce(b.customer_recharge_reward, 0) customer_recharge_reward,coalesce(b.customer_recharge_cash, 0) customer_recharge_cash,");
			daucount.append(" coalesce(b.customer_recharge_bank, 0) customer_recharge_bank,coalesce(b.customer_recharge_zfb, 0) customer_recharge_zfb,");
			daucount.append(" coalesce(b.customer_recharge_wx, 0) customer_recharge_wx,a.payment_total,p.coupons_ds,");
			daucount.append(" (select coalesce(sum(amount_deposited), 0) from boh_bank_deposit where store_id = " + store_id + " and business_date = '" + param.optString("day_count") + "') as deposit_money_actual,");
			daucount.append(" p.sale_person_num,p.sale_person_average,0 as attendance_num,'' as remark,(select count(t.id) from tables_info t where t.organ_id = " + store_id + " and t.valid_state = '1') as tables_num, ");
			daucount.append(" (select coalesce(sum(t.seat_counts), 0) from tables_info t where t.organ_id = " + store_id + " and t.valid_state = '1') as seat_num,");

			daucount.append(" (select count(id) from pos_bill  where bill_state='CJ01' and copy_bill_num in(select bill_num from pos_bill where bill_state is null  and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED') and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED') as backsingle_num ,");
			daucount.append(" (select count(id) from pos_bill  where bill_state='CJ01' and copy_bill_num in(select bill_num from pos_bill where bill_state  ='ZDQX02'  and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED') and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED' and copy_bill_num  not in (select copy_bill_num from pos_bill where (bill_state is null or bill_state='ZDQX02' )  and copy_bill_num is not null and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED')) as backwhole_num ,");
			daucount.append(" (select count(id) from pos_bill  where bill_state='CJ01' and copy_bill_num in(select bill_num from pos_bill where bill_state  ='ZDQX02'  and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED') and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED' and copy_bill_num  in (select copy_bill_num from pos_bill where (bill_state is null or bill_state='ZDQX02' )  and copy_bill_num is not null and report_date ='"+param.optString("day_count")+"' and store_id ="+store_id+" and bill_property='CLOSED')) as backcopy_num ");

			daucount.append(" from");
			daucount.append(" (select store_id,");
			daucount.append("    report_date,coalesce(sum(m.bill_amount), 0) as sale_total,count(case when coalesce(m.bill_state, 'zc') not in ('CJ01', 'ZDQX02') then m.bill_num end) as sale_billnum,");
			daucount.append("	 case when count(m.bill_num) = 0 then 0 else round((coalesce(sum(m.bill_amount), 0) + coalesce(sum(more_coupon), 0)) / (case when coalesce(count(case when coalesce(bill_state, 'zc') not in ('CJ01', 'ZDQX02') then m.bill_num end), 0) = 0 then 1 else coalesce(count(case when coalesce(bill_state, 'zc') not in ('CJ01', 'ZDQX02') then m.bill_num end), 0) end), 2) end as sale_billaverage,");
			daucount.append("	 case when sum(guest) = 0 then 0 else round((coalesce(sum(bill_amount), 0) + coalesce(sum(more_coupon), 0)) / sum(guest), 2) end as sale_person_average, coalesce(sum(m.payment_amount), 0) as item_actual_money,");
//			daucount.append("	 sum(case when sale_mode = 'TS01' then coalesce(m.payment_amount, 0) end) as ts_actual_money, ");
//			daucount.append("	 coalesce(sum(case when sale_mode = 'WS03' then coalesce(m.payment_amount, 0) end), 0) as ws_actual_money,");
//			daucount.append("	 coalesce(sum(case when sale_mode = 'WD02' then coalesce(m.payment_amount, 0) end), 0) as wd_actual_money,");
			daucount.append("	 coalesce(sum(discountk_amount), 0) as discount_money,");
			daucount.append("	 coalesce(sum(discountr_amount), 0) as reduction_money,");
			daucount.append("	 coalesce(sum(m.maling_amount), 0) as moling_money,");
			daucount.append("	 coalesce(sum(m.givi_amount), 0) as free_money,");
			daucount.append("	 coalesce(sum(m.service_amount), 0) as service_fee_income,");
			daucount.append("	 coalesce(sum(m.more_coupon), 0) as coupons_ds,");
			daucount.append("	 coalesce(sum(guest), 0) as sale_person_num");
			daucount.append("  from pos_bill m");
			daucount.append("  where m.store_id = " + store_id + " and m.report_date = '" + param.optString("day_count") + "' and m.bill_property = 'CLOSED' group by store_id, report_date) p");
			daucount.append("  join");
			daucount.append("	(select m.store_id,m.report_date,");
			daucount.append("	 coalesce(sum(case when discount_mode_id = 5 then coalesce(discountk_amount, 0) end), 0) as customer_discount,");
			daucount.append("	 sum(case when w.payment_class = 'coupons' then coalesce(p.currency_amount, 0) end) as coupons_money,");
			daucount.append("	 sum(case when w.payment_class = 'card' then coalesce(p.currency_amount, 0) end) as total_card,");
			daucount.append("	coalesce(sum(p.currency_amount), 0) as payment_total");
			daucount.append("	from pos_bill m join pos_bill_payment p on m.bill_num = p.bill_num");
			daucount.append("	 left join payment_way w on p.jzid = w.id");
			daucount.append("	 where m.store_id = " + store_id + " and m.report_date = '" + param.optString("day_count") + "' and m.bill_property = 'CLOSED' group by m.store_id, m.report_date) a");
			daucount.append("	on p.store_id = a.store_id and p.report_date = a.report_date");
			daucount.append("	");
			daucount.append("	left join (select t.store_id,t.business_date,");
			daucount.append("	sum(case when operat_type in ('03', '05') then coalesce(main_trading, 0) end) as main_trading,");
			daucount.append("	sum(case when operat_type in ('03', '05') then coalesce(reward_trading, 0) end) as customer_reward_sale,");
			daucount.append("	sum(case when operat_type in ('02', '04') then coalesce(main_trading, 0) end) as accounts_receivable,");
			daucount.append("	sum(case when operat_type in ('02', '04') then coalesce(main_trading, 0) end) as customer_recharge_total,");
			daucount.append("	sum(case when operat_type in ('02', '04') then coalesce(main_trading, 0) end) as customer_recharge_income,");
			daucount.append("	sum(case when operat_type in ('02', '04') then coalesce(reward_trading, 0) end) as customer_recharge_reward,");
			daucount.append("	sum(case when w.payment_class = 'cash' then coalesce(p.local_currency, 0) end) as customer_recharge_cash,");
			daucount.append("	sum(case when w.payment_class = 'bankcard' then coalesce(p.local_currency, 0) end) as customer_recharge_bank,");
			daucount.append("	sum(case when w.payment_class = 'thirdparty' and w.payment_name2 = 'ali_pay' then coalesce(p.local_currency, 0) end) as customer_recharge_zfb,");
			daucount.append("	sum(case when w.payment_class = 'thirdparty' and w.payment_name2 = 'wechat_pay' then coalesce(p.local_currency, 0) end) as customer_recharge_wx");
			daucount.append("	from crm_card_trading_list t");
			daucount.append("	left join crm_card_payment_list p on t.bill_code = p.bill_code");
			daucount.append("	left join payment_way w on p.payment_id = w.id");
			daucount.append("	where t.store_id = " + store_id + " and t.business_date = '" + param.optString("day_count") + "'");
			daucount.append("	group by t.store_id, t.business_date) b");
			daucount.append("	on a.store_id = b.store_id");
			daucount.append("	and a.report_date = b.business_date");
			daucount.append("	join (select m.store_id,m.report_date,");
			daucount.append("	sum(case when d.item_property in ('SINGLE', 'SETMEAL') then (case when d.item_remark in ('CJ05') then 0 else d.item_amount end) end) as item_list_money,");
			daucount.append("	coalesce(sum(case when d.item_property in ('SINGLE', 'SETMEAL') then (d.discount_amount + d.discountr_amount - d.single_discount_amount) else 0 end), 0) as item_yh_money,");
			daucount.append("	coalesce(sum(d.discount_amount), 0) as item_discount_money,");
			daucount.append("	coalesce(sum(d.discountr_amount), 0) as item_reduction_money,");
			daucount.append("	sum(case when d.item_remark = 'FS02' then coalesce(d.item_amount, 0) end) as free_money_item,");
			daucount.append("	sum(case when d.item_remark in ('TC01', 'QX04') and d.item_property in ('SINGLE', 'SETMEAL') then coalesce(d.real_amount, 0) end) as back_money_item,");
			daucount.append("	coalesce(sum(d.real_amount), 0) as item_net_money,");
			daucount.append("   coalesce( sum(case when d.sale_mode = 'TS01' and d.item_property in ('SINGLE', 'SETMEAL') then coalesce(d.item_amount, 0) end),0) as ts_actual_money,");
			daucount.append("   coalesce(sum(case when d.sale_mode = 'WS03' and d.item_property in ('SINGLE', 'SETMEAL') then coalesce(d.item_amount, 0)end),0) as ws_actual_money,");
			daucount.append("   coalesce(sum(case when d.sale_mode = 'WD02' and d.item_property in ('SINGLE', 'SETMEAL') then coalesce(d.item_amount, 0)end),0) as wd_actual_money");
			daucount.append("	from pos_bill m");
			daucount.append("	join pos_bill_item d on m.bill_num = d.bill_num");
			daucount.append("	where m.store_id = " + store_id + " and m.report_date = '" + param.optString("day_count") + "' and m.bill_property = 'CLOSED'");
			daucount.append("	group by m.store_id, m.report_date) c on a.store_id = c.store_id and a.report_date = c.report_date");
			daucount.append("	left join (select store_id,business_date,sum(coalesce(bi.money, 0)) as other_income,coalesce(sum(bi.money), 0) as yyw_income");
			daucount.append("	from boh_inout_info bi");
			daucount.append("	where bi.store_id = " + store_id + " and bi.business_date = '" + param.optString("day_count") + "' group by store_id, business_date) d on a.store_id = d.store_id and a.report_date = d.business_date");

			// 机构日营业汇总分时段
			StringBuilder organTimes = new StringBuilder();
			organTimes
					.append("insert into hq_daycount_times (tenancy_id, store_id, day_count, times_id, sale_total, sale_billnum, sale_billaverage, discount_money, reduction_money, coupons_money, customer_discount, customer_reward_sale, customer_cash_money, moling_money, free_money, item_list_money, item_actual_money, ts_actual_money");
			organTimes
					.append(" , ws_actual_money , wd_actual_money, item_yh_money, item_discount_money, item_reduction_money, free_money_item, back_money_item, item_net_money, other_income, service_fee_income, yyw_income, accounts_receivable, customer_recharge_total, customer_recharge_income, customer_recharge_reward, customer_recharge_cash, customer_recharge_bank");
			organTimes.append(" , customer_recharge_zfb, customer_recharge_wx, payment_total, coupons_ds, deposit_money_actual, sale_person_num, sale_person_average, attendance_num, remark)");

			organTimes.append(" select '" + tenantid + "' as tenancy_id,a.store_id  as store_id,a.report_date as day_count,t.times_id as times_id");
			organTimes.append(" ,sum(a.bill_amount) as sale_total");
			organTimes.append(" ,count(case when coalesce(a.bill_state,'zc') not in ('CJ01','ZDQX02') then a.bill_num end) as sale_billnum");
			organTimes.append(" ,round(sum(coalesce(a.bill_amount,0) + coalesce(a.more_coupon,0))/coalesce(nullif(count(case when coalesce(a.bill_state,'zc') not in('CJ01','ZDQX02') then a.bill_num end),0),1),2) as sale_billaverage");
			organTimes.append(" ,sum(coalesce(a.discountk_amount,0)) as discount_money");
			organTimes.append(" ,sum(coalesce(a.discountr_amount,0)) as reduction_money");
			organTimes.append(" ,sum(coalesce(c.coupons_money,0)) as coupons_money");
			organTimes.append(" ,sum(coalesce(case when a.discount_mode_id = 5 then a.discountk_amount else 0 end,0)) as customer_discount");
			organTimes.append(" ,sum(coalesce(k.customer_reward_sale,0)) as customer_reward_sale");
			organTimes.append(" ,0  as customer_cash_money");
			organTimes.append(" ,sum(coalesce(a.maling_amount,0)) as moling_money");
			organTimes.append(" ,sum(coalesce(a.givi_amount,0))   as free_money");
			organTimes.append(" ,sum(coalesce(a.bill_amount,0) - coalesce(a.service_amount,0)) as item_list_money");
			organTimes.append(" ,sum(coalesce(a.payment_amount,0))  as item_actual_money");
			organTimes.append(" ,sum(coalesce(case when a.sale_mode='TS01' then a.payment_amount else 0 end,0)) as ts_actual_money");
			organTimes.append(" ,sum(coalesce(case when a.sale_mode='WS03' then a.payment_amount else 0 end,0)) as ws_actual_money");
			organTimes.append(" ,sum(coalesce(case when a.sale_mode='WD02' then a.payment_amount else 0 end,0)) as wd_actual_money");
			organTimes.append(" ,sum(coalesce(b.item_yh_money,0)) as item_yh_money");
			organTimes.append(" ,sum(coalesce(b.item_discount_money,0)) as item_discount_money");
			organTimes.append(" ,sum(coalesce(b.item_reduction_money,0)) as item_reduction_money");
			organTimes.append(" ,sum(coalesce(b.free_money_item,0)) as free_money_item");
			organTimes.append(" ,sum(coalesce(b.back_money_item,0)) as back_money_item");
			organTimes.append(" ,sum(coalesce(b.item_net_money,0)) as item_net_money");
			organTimes.append(" ,0 as other_income");
			organTimes.append(" ,sum(coalesce(a.service_amount,0)) as service_fee_income");
			organTimes.append(" ,0 yyw_income");
			organTimes.append(" ,sum(coalesce(k.accounts_receivable,0)) as accounts_receivable");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_total,0)) as customer_recharge_total");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_income,0)) as customer_recharge_income");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_reward,0)) as customer_recharge_reward");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_cash,0)) as customer_recharge_cash");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_bank,0)) as customer_recharge_bank");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_zfb,0)) as customer_recharge_zfb");
			organTimes.append(" ,sum(coalesce(k.customer_recharge_wx,0)) as customer_recharge_wx");
			organTimes.append(" ,sum(coalesce(c.currency_amount,0)) as payment_total");
			organTimes.append(" ,sum(coalesce(a.more_coupon,0))  as coupons_ds");
			organTimes.append(" ,0 as deposit_money_actual");
			organTimes.append(" ,sum(coalesce(a.guest,0)) as sale_person_num");
			organTimes.append(" ,round(sum(coalesce(a.bill_amount,0) + coalesce(more_coupon,0)) / coalesce(nullif(sum(a.guest),0),1),2) as sale_person_average");
			organTimes.append(" ,0 as attendance_num");
			organTimes.append(" ,''  as remark");
			organTimes.append(" from (");
			organTimes.append(" SELECT (CASE WHEN TO_CHAR(PB.PAYMENT_TIME+'30 MIN','HH24' )='00' THEN '24' ELSE TO_CHAR(PB.PAYMENT_TIME+'30 MIN','HH24' ) END ) || ':' || (CASE WHEN TO_CHAR(PB.PAYMENT_TIME+'30 MIN','MI') >= '30' THEN '30' ELSE '00' END) AS CTIM");
			organTimes.append(" ,pb.* from pos_bill pb");
			organTimes.append(" where pb.store_id=" + store_id + "and pb.report_date='" + param.optString("day_count") + "' and pb.bill_property='CLOSED'");
			organTimes.append(" ) a left join");
			organTimes.append(" (select a.bill_num");
			organTimes.append(" ,sum(coalesce(b.discount_amount,0) + coalesce(b.discountr_amount,0) - coalesce(b.single_discount_amount,0))  as item_yh_money");
			organTimes.append(" ,sum(coalesce(b.discount_amount,0)) as item_discount_money");
			organTimes.append(" ,sum(coalesce(b.discountr_amount,0)) as item_reduction_money");
			organTimes.append(" ,sum(coalesce(case when b.item_remark = 'FS02' then b.item_amount else 0 end,0)) as free_money_item");
			organTimes.append(" ,sum(coalesce(case when b.item_remark in ('TC01','QX04') and b.item_property in ('SINGLE','SETMEAL') then b.item_amount else 0 end,0)) as back_money_item");
			organTimes.append(" ,sum(coalesce(b.item_amount,0)) as item_net_money");
			organTimes.append(" from pos_bill a left join");
			organTimes.append(" pos_bill_item b on a.bill_num = b.bill_num");
			organTimes.append(" where a.store_id=" + store_id + "and a.report_date='" + param.optString("day_count") + "' and a.bill_property='CLOSED' group by a.bill_num");
			organTimes.append(" ) b on b.bill_num = a.bill_num left join");
			organTimes.append(" (select a.bill_num");
			organTimes.append(" ,sum(coalesce(p.currency_amount,0)) as currency_amount");
			organTimes.append(" ,sum(coalesce(case when w.payment_class = 'coupons' then p.currency_amount else 0 end,0)) as coupons_money");
			organTimes.append(" from pos_bill a join");
			organTimes.append(" pos_bill_payment p on p.bill_num = a.bill_num left join");
			organTimes.append(" payment_way w on p.jzid=w.id");
			organTimes.append(" where a.store_id=" + store_id + "and a.report_date='" + param.optString("day_count") + "' and a.bill_property='CLOSED' group by a.bill_num");
			organTimes.append(" ) c on c.bill_num = a.bill_num left join");
			organTimes.append(" (select a.bill_num");
			organTimes.append(" ,sum(case when b.operat_type in ('03','05') then b.reward_trading else 0 end) as customer_reward_sale");
			organTimes.append(" ,sum(case when b.operat_type in ('02','04') then b.main_trading else 0 end) as accounts_receivable");
			organTimes.append(" ,sum(case when b.operat_type in ('02','04') then b.main_trading else 0 end) as customer_recharge_total");
			organTimes.append(" ,sum(case when b.operat_type in ('02','04') then b.main_trading else 0 end) as customer_recharge_income");
			organTimes.append(" ,sum(case when b.operat_type in ('02','04') then b.reward_trading else 0 end) as customer_recharge_reward");
			organTimes.append(" ,sum(case when w.payment_class = 'cash' then p.local_currency else 0 end) as customer_recharge_cash");
			organTimes.append(" ,sum(case when w.payment_class = 'bankcard' then p.local_currency else 0 end) as customer_recharge_bank");
			organTimes.append(" ,sum(case when w.payment_class = 'thirdparty' and w.payment_name2 = 'ali_pay' then p.local_currency else 0 end) as customer_recharge_zfb");
			organTimes.append(" ,sum(case when w.payment_class = 'thirdparty' and w.payment_name2 = 'wechat_pay' then p.local_currency else 0 end) as customer_recharge_wx");
			organTimes.append(" from");
			organTimes.append(" pos_bill a join");
			organTimes.append(" crm_card_trading_list b on b.bill_code_original = a.bill_num left join");
			organTimes.append(" crm_card_payment_list p on p.bill_code = b.bill_code left join");
			organTimes.append(" payment_way w on w.id = p.payment_id");
			organTimes.append(" where b.store_id=" + store_id + "and b.business_date='" + param.optString("day_count") + "' and a.bill_property='CLOSED' group by a.bill_num");
			organTimes.append(" ) k on k.bill_num = a.bill_num left join");
			organTimes.append(" (select  time_id as times_id,end_time from sys_times where time_type='half') t on t.end_time = a.ctim");
			organTimes.append(" group by a.store_id,a.report_date,t.times_id");

			// 菜品日班次营业汇总
			StringBuilder itemShift = new StringBuilder();

			itemShift.append(" insert into hq_daycount_shift_item(tenancy_id,store_id,day_count,shift_id,sale_model,combo_id,combo_unit_id,combo_price,item_id");
			itemShift.append(",item_unit_id,item_unit_price,item_pro,list_num,list_money,sales_num,sales_money,back_num,back_money,free_num,free_money,favor_money");
			itemShift.append(",discount_money,reduction_money,change_money,actual_num,actual_money,net_money,sale_billnum,sale_billaverage,table_property_id");
			itemShift.append(",business_area_id,guest_num,tables_num)");
			itemShift.append(" select '" + tenantid + "' as tenancy_id," + store_id + " as store_id,to_date('" + day_count + "', 'YYYY-MM-DD') as day_count,p.shift_id,sale_model");
			itemShift.append(" ,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,sum(list_num) as list_num,sum(list_money) as list_money");
			itemShift.append(" ,sum(sales_num) as sales_num,sum(sales_money) as sales_money,sum(back_num) as back_num,sum(back_money) as back_money,sum(free_num) as free_num");
			itemShift.append(" ,sum(free_money) as free_money,sum(favor_money) as favor_money,sum(discount_money) as discount_money,sum(reduction_money) as reduction_money");
			itemShift.append(" ,sum(change_money) as change_money,sum(actual_num) as actual_num,sum(actual_money) as actual_money,sum(net_money) as net_money");
			itemShift.append(" ,max(p.sale_billnum) sale_billnum,null sale_billaverage,table_property_id,business_area_id,coalesce(sum(guest_counts), 0) as guest_num");
			itemShift.append(" ,max(p.sale_billnum) as tables_num");
			itemShift.append(" from (");
			itemShift.append("   select d.store_id,d.report_date,d.shift_id,count(d.bill_num) as sale_billnum from pos_bill d where d.store_id =" + store_id);
			itemShift.append("    and d.report_date = '" + day_count + "' and d.bill_property = 'CLOSED' and coalesce(d.bill_state, 'zc') not in ('CJ01', 'ZDQX02')");
			itemShift.append("   group by d.store_id, d.report_date, d.shift_id");
			itemShift.append(" ) p join (");
			itemShift.append(" select d.store_id,d.report_date,m.shift_id,d.sale_mode as sale_model,d.setmeal_id as combo_id");
			itemShift.append(" ,case d.item_property when 'SETMEAL' then d.item_unit_id end as combo_unit_id,case when d.item_property = 'SETMEAL' then d.item_price end as combo_price");
			itemShift.append(" ,d.item_id,d.item_unit_id,d.item_price as item_unit_price,d.item_property as item_pro");
			itemShift.append(" ,case when d.item_remark in ('CJ05') then 0 else d.item_count end as list_num,case when d.item_remark in ('CJ05') then 0 else d.item_amount end as list_money");
			itemShift.append(" ,d.item_count as sales_num,d.item_amount as sales_money,case when d.item_remark in ('TC01', 'QX04') then coalesce(return_count, 0) else 0 end as back_num");
			itemShift.append(" ,case when d.item_remark in ('TC01', 'QX04') then coalesce(real_amount, 0) else 0 end as back_money");
			itemShift.append(" ,case when d.item_remark = 'FS02' then coalesce(return_count, 0) else 0 end as free_num,case when d.item_remark = 'FS02' then coalesce(real_amount, 0) else 0 end as free_money");
			itemShift.append(" ,(d.discount_amount + d.discountr_amount - d.single_discount_amount) as favor_money, case when d.item_remark in ('TC01', 'FS02') then 0 else (d.discount_amount) end as discount_money");
			itemShift.append(" ,case when d.item_remark in ('TC01', 'FS02') then 0 else (d.discountr_amount) end as reduction_money");
			itemShift.append(" ,d.single_discount_amount as change_money,case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05') then 0 else d.item_count end as actual_num");
			itemShift.append(" ,case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05') then 0 else d.real_amount end as actual_money");
			itemShift.append(" ,case when d.item_remark in ('TC01', 'FS02', 'QX04', 'CJ05') then 0 else d.real_amount end as net_money");
			itemShift.append(" ,m.bill_num,t.table_property_id,t.business_area_id,m.guest as guest_counts,m.bill_state");
			itemShift.append(" from pos_bill_item d");
			itemShift.append(" join pos_bill m on d.bill_num = m.bill_num");
			itemShift.append(" left join tables_info t on t.table_code = m.table_code and organ_id = " + store_id);
			itemShift.append(" where m.store_id = " + store_id + " and m.report_date = '" + day_count + "' and m.bill_property = 'CLOSED'");
			itemShift.append(" ) a on p.store_id = a.store_id and p.report_date = a.report_date and p.shift_id = a.shift_id");
			itemShift.append(" group by p.shift_id,sale_model,combo_id,combo_unit_id,combo_price,item_id,item_unit_id,item_unit_price,item_pro,table_property_id,business_area_id");

			StringBuilder updateItemShift = new StringBuilder();

			updateItemShift.append(" update hq_daycount_shift_item t set combo_unit_id = t1.combo_unit_id, combo_price = t1.combo_price from hq_daycount_shift_item t1");
			updateItemShift.append(" where t.combo_id = t1.combo_id and t1.item_pro = 'SETMEAL' and t.item_pro = 'MEALLIST' and t.day_count = '" + day_count + "' and t.store_id = " + store_id);
			updateItemShift.append("");

			// 现金日报分时段

			// 会员日统计分机构交易
			// StringBuilder crmTradeorg = new StringBuilder();

			// 会员日统计分交易机构等级

			// 会员日统计分注册机构

			// 会员日统计分注册机构等级

			// 会员日统计分渠道

			// 会员日统计分等级

			// 删昨天的
			String deletePosBill = "delete from pos_bill where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "';";
			String deletePosBillItem = "delete from pos_bill_item where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "';";
			String deletePosBillPayment = "delete from pos_bill_payment where store_id=" + store_id + " and report_date='" + param.optString("day_count") + "';";

			String reportSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, sqlType);
			String paymentCountSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, sqlTypePaymentCount);
			String daycountShiftSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, sqlTypeDaycountShift);
			String daycountTimesSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, sqlTypeDaycountTimes);
			String paymentcountShiftSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, sqlTypePaymentcountShift);
			String paymentcountTimesSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, sqlTypePaymentcountTimes);
			String hqDaycountItemSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, hqDaycountItem);
			String hqDaycountItemUpdateSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, hqDaycountItemUpdate);
			String hqDaycountItemTimesSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, hqDaycountItemTimes);
			String hqDaycountItemTimesUpdateSql = parameterUtils.parameterAutomaticCompletion(tenantid, param, hqDaycountItemTimesUpdate);

			if(reportSql == null || reportSql.length() == 0)
			{
				throw new Exception("日结过程'reportSql'不全,不能日结!");
			}

			if(paymentCountSql == null || paymentCountSql.length() == 0)
			{
				throw new Exception("日结过程'paymentCountSql'不全,不能日结!");
			}

			if(daycountShiftSql == null || daycountShiftSql.length() == 0)
			{
				throw new Exception("日结过程'daycountShiftSql'不全,不能日结!");
			}

			if(daycountTimesSql == null || daycountTimesSql.length() == 0)
			{
				throw new Exception("日结过程'daycountTimesSql'不全,不能日结!");
			}

			if(paymentcountShiftSql == null || paymentcountShiftSql.length() == 0)
			{
				throw new Exception("日结过程'paymentcountShiftSql'不全,不能日结!");
			}

			if(paymentcountTimesSql == null || paymentcountTimesSql.length() == 0)
			{
				throw new Exception("日结过程'paymentcountTimesSql'不全,不能日结!");
			}

            if(hqDaycountItemSql == null || hqDaycountItemSql.length() == 0)
            {
                throw new Exception("日结过程'hqDaycountItemSql'不全,不能日结!");
            }

            if(hqDaycountItemUpdateSql == null || hqDaycountItemUpdateSql.length() == 0)
            {
                throw new Exception("日结过程'hqDaycountItemUpdateSql'不全,不能日结!");
            }

            if(hqDaycountItemTimesSql == null || hqDaycountItemTimesSql.length() == 0)
            {
                throw new Exception("日结过程'hqDaycountItemTimesSql'不全,不能日结!");
            }

            if(hqDaycountItemTimesUpdateSql == null || hqDaycountItemTimesUpdateSql.length() == 0)
            {
                throw new Exception("日结过程'hqDaycountItemTimesUpdateSql'不全,不能日结!");
            }

			dao.getJdbcTemplate(tenantid).batchUpdate(new String[]
					{ repairPayment, insertPosBill, insertPosBillItem, insertPosBillPayment, unusualReason.toString(), payment.toString(), hqDaycountItemSql, hqDaycountItemUpdateSql, hqDaycountItemTimesSql, hqDaycountItemTimesUpdateSql, organTimes.toString(), itemShift.toString(), updateItemShift.toString(), daucount.toString(), reportSql, paymentCountSql, daycountShiftSql, daycountTimesSql, paymentcountShiftSql, paymentcountTimesSql, deletePosBill, deletePosBillItem, deletePosBillPayment});

//			dao.getJdbcTemplate(tenantid).batchUpdate(new String[]
//					{ repairPayment, insertPosBill, insertPosBillItem, insertPosBillPayment, unusualReason.toString(), payment.toString(), item.toString(), itemTimes.toString(), organTimes.toString(), daucount.toString(), deletePosBill, deletePosBillItem, deletePosBillPayment });

			String status = "1";
			param.element("status", status);

			sql = "update hq_daycount_info set hq_sign='" + status + "' where store_id=" + param.optInt("store_id") + " and day_count='" + param.optString("day_count") + "'";
			dao.execute(tenantid, sql);

		}
		else
		{
			// sql = "update hq_daycount_info set hq_sign=null where store_id="
			// + param.optInt("store_id") + " and day_count='" +
			// param.optString("day_count") + "'";
			// dao.execute(tenantid, sql);
			throw new Exception("门店上传数据不完全，请使用【数据上传】功能后重试！");
		}

	}

	/**
	 * 获取SCM日结日期,未做过日结为NULL
	 * @param tenantId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private Date getScmDaDate(String tenantId, JSONObject param) throws Exception
	{
		StringBuilder sb = new StringBuilder();

		sb.append("select init_date,da_date from scm_organ where tenancy_id='" + tenantId + "' and store_id=" + param.optString("store_id") + " and is_init=1 and init_date<'"+param.optString("day_count")+"'");
		SqlRowSet rs = dao.query(tenantId, sb.toString());

		if (rs.next())
		{
			return rs.getDate("da_date");
		}
		return null;
	}

	/**
	 * 判断当前业务日期是否做过数据整理
	 * @param tenantId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private boolean isDataFinish(String tenantId, JSONObject param) throws Exception
	{
		StringBuilder sb = new StringBuilder();

		sb.append(" select 1 from scm_storage_day_acount t where t.tenancy_id='" + tenantId + "' and t.store_id=" + param.optString("store_id") + " and t.cost_count_date='" + param.optString("day_count") + "'");
		long total = this.dao.countSql(tenantId, sb.toString());

		if(total > 0){
			return true;
		}

		return false;
	}

	@Override
	public void beforedailySettlement(String tenentid, JSONObject param) throws Exception 
	{
		//判断当前的业务日期是不是包含在跨天营业的范围内,如果是调用批量方法，否则调用单个方法
		StringBuilder sql = new StringBuilder();
		sql.append(" select (substr(remark,5) ::json->> 'daybegin')  AS daybegin,");
		sql.append(" (substr(remark,5) ::json->> 'dayend') as dayend");
		sql.append(" from pos_opt_state p");
		sql.append(" where p.store_id=");
		sql.append(param.optInt("store_id"));
		sql.append(" and (substr(remark,5) ::json->> 'daybegin')<='");
		sql.append(param.optString("day_count"));
		sql.append("'and (substr(remark,5) ::json->> 'dayend')>='");
		sql.append(param.optString("day_count"));
		sql.append("'");
		sql.append(" and left(p.remark, 5)='跨天营业{'");
		sql.append(" and p.tenancy_id='");
		sql.append(tenentid);
		sql.append("' group by daybegin,dayend");
		
		List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
		if(list!=null && list.size()>0)
		{
			param.put("start_date", list.get(0).optString("daybegin"));
			param.put("end_date", list.get(0).optString("dayend"));
			batchDailySettlement(tenentid,param);
		}
		else
		{
			dailySettlement(tenentid,param);
		}
		
		
	}

	@Override
	public JSONObject checkOpenHq(String tenantid, JSONObject obj)throws Exception 
	{
		JSONObject result = JSONObject.fromObject("{}");
		if(isOpenHq(tenantid, obj))
		{
			result.put("open", 1);
		}
		else
		{
			result.put("open", 0);
		}
		return result;
	}

    @Override
    public JSONObject checkOpenScm(String tenentid, JSONObject obj) throws Exception {
        JSONObject result = JSONObject.fromObject("{}");
        if(isOpenSCM(tenentid, obj))
        {
            result.put("open", 1);
        }
        else
        {
            result.put("open", 0);
        }
        return result;
    }


}
