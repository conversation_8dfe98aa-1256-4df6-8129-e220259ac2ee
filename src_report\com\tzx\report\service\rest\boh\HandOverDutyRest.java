package com.tzx.report.service.rest.boh;

import com.tzx.report.bo.boh.HandOverDutyService;
import jxl.write.WriteException;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;


/**
 * 豪享来-门店交班报表
 * <AUTHOR>
 *
 */


@Controller("HandOverDutyRest")
@RequestMapping("/report/HandOverDutyRest")
public class HandOverDutyRest
{
	
	@Resource(name = HandOverDutyService.NAME)
	private HandOverDutyService bandOverDutyService;
	
	@RequestMapping(value = "/getHandOverDutyQuery")
	public void getHandOverDutyQuery(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		JSONObject result = JSONObject.fromObject("{}");
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			if(p.optString("p_store_id").length()==0){
				p.element("p_store_id", session.getAttribute("user_organ_codes_group"));
			}
			
			result = bandOverDutyService.getHandOverDutyQuery((String) session.getAttribute("tenentid"), p);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result.element("success", false);
			result.element("msg", e.getMessage());
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
}
