package com.tzx.report.bo.payment.service;

import net.sf.json.JSONArray;

import java.text.ParseException;

/**
 *
 * <AUTHOR>
 * @date 2018/6/6
 */
public interface XmdApiService {

    /**
     * 查询三月内的数据
     */
    public JSONArray checkAndQueryApiDate(int chad,int type) throws Exception;

    public void updateSettlementDetil(String merchantId,String date) throws ParseException ;

    public void syncDate(String poiid,String date) throws Exception;

    public void syncPaymentData(String poiid,String date) throws Exception;

    public void repairReportDate() throws Exception;
}
