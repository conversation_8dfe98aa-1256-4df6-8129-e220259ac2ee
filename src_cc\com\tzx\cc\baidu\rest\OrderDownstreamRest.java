package com.tzx.cc.baidu.rest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.druid.util.StringUtils;
import com.tzx.cc.baidu.bo.OrderService;
import com.tzx.cc.baidu.bo.ShopService;
import com.tzx.cc.baidu.bo.ThirdPartyOrderService;
import com.tzx.cc.baidu.bo.imp.EleMeOrderReceiverTest;
import com.tzx.cc.baidu.bo.imp.ThirdPartyOrderReceiver;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.common.constant.OrderOper;
import com.tzx.cc.common.redis.service.CcRedisService;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.takeaway.service.CcTokenRefreshTaskRegisterServcie;
import com.tzx.cc.takeaway.unifiedreceiveorder.service.UnifiedReceiveOrderService;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.ElmUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;

/**
 * 百度外卖订单下行接口
 */
@Controller("OrderDownstreamRest")
@RequestMapping("/thirdpartydown/orderdownstreamrest")
public class OrderDownstreamRest {
	@Resource(name = OrderService.NAME)
	private OrderService orderService;

	@Resource(name = ShopService.NAME)
	private ShopService shopService;

	@Resource(name = "genericDaoImpl")
	private GenericDao dao;

	private static final Logger logger = LoggerFactory
			.getLogger(OrderDownstreamRest.class);

	/**
	 * 心跳监测
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/heartbeat", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject heartbeat(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		InputStream is = request.getInputStream();
		BufferedReader reader = new BufferedReader(new InputStreamReader(is));
		JSONObject result = new JSONObject();
		result.put("errno", "0");
		String jsobjStr = "";
		String tmp = null;
		while ((tmp = reader.readLine()) != null) {
			jsobjStr += tmp;
		}
		JSONObject jsobj = JSONObject.fromObject(jsobjStr);
		String sql = jsobj.optString("sql");
		String tenantId = jsobj.optString("tenantId");
		try {
			if (!"".equals(sql) && !"".equals("tenantId")) {
				DBContextHolder.setTenancyid(tenantId);
				dao.execute(tenantId, sql);
			}

		} catch (Exception e) {
			result.put("errno", "1");
			result.put("error", "sql执行失败:" + sql);
			result.put("errmsg", e);
		}
		stopWatch.stop();

		result.put("time", stopWatch.getTotalTimeMillis() + "ms");
		return result;
	}

	/**
	 * 新美大商户绑定回调
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/xmdCallBack", method = RequestMethod.POST)
	public void xmdCallBack(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		logger.info("调用新美大门店绑定回调地址：");
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		try {
			JSONObject param = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet()) {
				String value = new String(URLDecoder.decode(map.get(key)[0],
						"UTF-8").getBytes("UTF-8"));
				param.put(key, CommonUtil.replaceEvilChar(value));
			}
			JSONObject res = shopService.saveXmdCallBackToken(param);
			if (null != res) {
				result = res.toString();
			}
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
		} finally {
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 新美大商户解除绑定回调
	 * 
	 * @since 2017-07-31
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/xmdreleaseCallBack", method = RequestMethod.POST)
	public void xmdreleaseCallBack(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		logger.info("调用新美大门店解绑回调地址：");
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		try {
			JSONObject param = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet()) {
				String value = new String(URLDecoder.decode(map.get(key)[0],
						"UTF-8").getBytes("UTF-8"));
				param.put(key, CommonUtil.replaceEvilChar(value));
			}
			JSONObject res = shopService.saveXmdReleaseCallBack(param);
			if (null != res) {
				result = res.toString();
			}
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
		} finally {
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 百度外卖订单推送
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/baidu_order_push", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject baiduOrderPush(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		InputStream is = request.getInputStream();
		BufferedReader reader = new BufferedReader(new InputStreamReader(is));
		JSONObject result = new JSONObject();
		String jsobjStr = "";
		String tmp = null;
		UUID requestId=UUID.randomUUID();
		
		while ((tmp = reader.readLine()) != null) {
			jsobjStr += tmp;
		}
		JSONObject jsobj = JSONObject.fromObject(jsobjStr);
		
		JSONObject bdBody = jsobj.optJSONObject("body");
		
		CcBusniessLogBean ccBusniessLogBean=getBaiduOrderPushLog(requestId, jsobj, bdBody);
		jsobj.put("requestId",requestId.toString());
		
		String cmd = jsobj.optString("cmd");
		
		if (cmd.equals("order.status.get")) {
			try {
				result = orderService.orderStatusGet(jsobj,
						Constant.BAIDU_CHANNEL);
			} catch (Exception e) {
				e.printStackTrace();
				result.put("errno", "1");
				result.put("error", Constant.MSG_1);
			}
		} else {
			// 执行新的接单模式
			if (isStartNewReciveOrderModel()) {
				UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext.getBean(UnifiedReceiveOrderService.NAME);
				JSONObject resulet=receiveOrder.unifiedReciveOrder(Constant.BAIDU_CHANNEL,jsobj);
				ccBusniessLogBean.setResponseBody(resulet.toString());
				return resulet;
			} else {
				try {
					switch (cmd) {
					case "order.create":
						result = orderService.orderCreate(jsobj,
								Constant.BAIDU_CHANNEL);
						ccBusniessLogBean.setResponseBody(result.toString());
						break;
					// case "order.status.get":
					// result = orderService.orderStatusGet(jsobj,
					// Constant.BAIDU_CHANNEL);
					// break;
					case "order.status.push":
						result = orderService.orderStatusPush(jsobj,
								Constant.BAIDU_CHANNEL);
						break;
					default:
						break;
					}

				} catch (Exception e) {
					ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
					ccBusniessLogBean.setIsNormal("0");
					e.printStackTrace();
					result.put("errno", "1");
					result.put("error", Constant.MSG_1);
				}finally{
					KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
				}
			}
		}

		stopWatch.stop();
		logger.info("处理百度订单用时:" + stopWatch.prettyPrint());
		return result;
	}

	public CcBusniessLogBean getBaiduOrderPushLog(UUID requestId, JSONObject jsobj,JSONObject bdBody) throws Exception {
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		jsobj.put("requestId", requestId.toString());

		JSONObject order=null;
		JSONObject shop =null;
		String shopId="";
		String tanencyId="";
		try{
		if(jsobj.optString("cmd").equals("order.create")){
		order=bdBody.optJSONObject("order");
		shop = bdBody.optJSONObject("shop");
		String[] array = shop.optString("id").split("@");
		shopId = array[0];
		tanencyId = array[1];
		ccBusniessLogBean.setType("reviceOrder");
		ccBusniessLogBean.setOperAction(OrderOper.receiveOrder.toString());
		}
		if(order!=null){
			ccBusniessLogBean.setTzxId(LogUtils.getOrderCode(order, Constant.BAIDU_CHANNEL, shopId));
			ccBusniessLogBean.setThirdId(order.getString("order_id"));
		}
		if(jsobj.optString("cmd").equals("order.status.push")){
			String thirdOrderCode = jsobj.optJSONObject("body").optString("order_id");
			CcRedisService redis = (CcRedisService) SpringConext.getBean("ccRedisServiceImpl");
			String redisJsonStr=redis.getByKey(thirdOrderCode);
			JSONObject redisJson=JSONObject.fromObject(redisJsonStr);
			String[] shopIdArr = redisJson.optString("shop_id").split("@");
			shopId = shopIdArr[0];
			tanencyId = shopIdArr[1];
			String status = jsobj.optJSONObject("body").optString("status");
			if(CommonUtil.checkStringIsNotEmpty(status)){
				if(status.equals("10")){
					ccBusniessLogBean.setType("cancelOrder");
					ccBusniessLogBean.setOperAction(OrderOper.cancelOrder.toString());
				}
				if(status.equals("9")){
					ccBusniessLogBean.setType("finishOrder");
					ccBusniessLogBean.setOperAction(OrderOper.finishOrder.toString());	
				}
			}
				ccBusniessLogBean.setTzxId("");
				ccBusniessLogBean.setThirdId(thirdOrderCode);
		}
		}catch(Exception e){
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		}finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		
		if(CommonUtil.checkStringIsNotEmpty(tanencyId)){
			ccBusniessLogBean.setTenancyId(tanencyId);
		}
		if(CommonUtil.checkStringIsNotEmpty(shopId)){
			ccBusniessLogBean.setShopId(shopId);
		}
		ccBusniessLogBean.setCategory("cc");
		
		ccBusniessLogBean.setChannel(Constant.BAIDU_CHANNEL);
		ccBusniessLogBean.setChannelName("BD06");
	
		ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.OrderDownstreamRest:baiduOrderPush");
		ccBusniessLogBean.setRequestBody(jsobj.toString());

		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("0");
	
		return ccBusniessLogBean;
	}

	/**
	 * 美团外卖订单推送
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/mt_order_push", method = RequestMethod.POST)
	public void mtOrderPush(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		UUID requestId=UUID.randomUUID();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		try {
			JSONObject order = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();
			logger.info("美团推送订单信息map长度:" + map.size());
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					String value = new String(URLDecoder.decode(
							map.get(key)[0], "UTF-8").getBytes("UTF-8"));

					order.put(key, CommonUtil.replaceEvilChar(value));
				}

				//2017-11-06 订单日志start
				try {
				order.put("requestId", requestId.toString());
				String[] arr=order.optString("app_poi_code").split("@");
				ccBusniessLogBean.setRequestId(requestId.toString());
				if(order!=null){
					ccBusniessLogBean.setTzxId(LogUtils.getOrderCode(order, Constant.MEITUAN_CHANNEL, arr[0]));
					ccBusniessLogBean.setThirdId(order.getString("order_id"));
				}
				ccBusniessLogBean.setTenancyId(arr[1]);
				ccBusniessLogBean.setShopId(arr[0]);
				
				ccBusniessLogBean.setCategory("cc");
				ccBusniessLogBean.setType("reviceOrder");
				ccBusniessLogBean.setChannel(Constant.MEITUAN_CHANNEL);
				ccBusniessLogBean.setChannelName("MT08");
			
				ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.OrderDownstreamRest:mtOrderPush");
				ccBusniessLogBean.setRequestBody(order.toString());

				ccBusniessLogBean.setCreateTime(new Date().getTime());
				ccBusniessLogBean.setIsNormal("1");
				ccBusniessLogBean.setIsThird("0");
				ccBusniessLogBean.setOperAction(OrderOper.receiveOrder.toString());
				}catch (Exception e) {
					e.printStackTrace();
				}
				//2017-11-06 订单日志end

				// 执行新的接单模式
				if (isStartNewReciveOrderModel()) {
					UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext
							.getBean(UnifiedReceiveOrderService.NAME);
					JSONObject bodyJson = new JSONObject();
					bodyJson.put("body", order);
					bodyJson.put("type", "create");

					result = receiveOrder.unifiedReciveOrder(
							Constant.MEITUAN_CHANNEL, bodyJson).toString();
				} else {
					JSONObject res = orderService.orderCreate(order,
							Constant.MEITUAN_CHANNEL);
					if (null != res) {
						result = res.toString();
					}
				}
			}
			ccBusniessLogBean.setResponseBody(result);
			
			out = response.getWriter();
			stopWatch.stop();
			logger.info("处理美团订单用时:" + stopWatch.prettyPrint());
			out.print(result);
			out.flush();
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 美团用户或客服取消
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/mt_order_cancel", method = RequestMethod.GET)
	public void mtOrderCancel(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		UUID requestId=UUID.randomUUID();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		try {
			JSONObject order = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();
			logger.info("美团取消订单推送信息map长度:" + map.size());
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					logger.info("美团取消订单推送信息:"+ URLDecoder.decode(map.get(key)[0], "UTF-8").getBytes("UTF-8"));
					String value = new String(URLDecoder.decode(map.get(key)[0], "UTF-8").getBytes("iso-8859-1"));

					order.put(key, value);
				}
				logger.info("美团取消订单推送");
				order.put("requestId", requestId.toString());
				order.put("status", ThirdPartyOrderService.ORDER_CANCEL);
				
				//2017-11-06 订单日志start
				try {
				order.put("requestId", requestId.toString());
				String[] param=request.getParameterValues("params");
				String str=param[0];
				String[] keyText=str.substring(1, str.length()-2).split(",");
				JSONObject obj=new JSONObject();
				for(String tex:keyText) {
				    String[] keys=tex.split("=");
		            String key = keys[0].trim(); // key
		            String value = keys[1].trim(); // value
		            obj.put(key, value);
				}
				if(obj!=null) {
				String thirdOrderCode = obj.optString("order_id");
				CcRedisService redis = (CcRedisService) SpringConext.getBean("ccRedisServiceImpl");
				String redisJsonStr=redis.getByKey(thirdOrderCode);
				JSONObject redisJson=JSONObject.fromObject(redisJsonStr);
				String[] shopIdArr = redisJson.optString("app_poi_code").split("@");
			
				ccBusniessLogBean.setRequestId(requestId.toString());
				ccBusniessLogBean.setTzxId(LogUtils.getOrderCode(order, Constant.MEITUAN_CHANNEL, shopIdArr[0]));
				ccBusniessLogBean.setThirdId(order.optString("order_id"));
				
				ccBusniessLogBean.setTenancyId(shopIdArr[1]);
				ccBusniessLogBean.setShopId(shopIdArr[0]);
				}
				}catch (Exception e) {
					e.printStackTrace();
				}
				
				ccBusniessLogBean.setCategory("cc");
				ccBusniessLogBean.setType("cancelOrder");
				ccBusniessLogBean.setChannel(Constant.MEITUAN_CHANNEL);
				ccBusniessLogBean.setChannelName("MT08");
			
				ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.OrderDownstreamRest:mtOrderCancel");
				ccBusniessLogBean.setRequestBody(order.toString());

				ccBusniessLogBean.setCreateTime(new Date().getTime());
				ccBusniessLogBean.setIsNormal("1");
				ccBusniessLogBean.setIsThird("0");
				ccBusniessLogBean.setOperAction(OrderOper.cancelOrder.toString());
				
				//2017-11-06 订单日志end

				// 执行新的接单模式
				if (isStartNewReciveOrderModel()) {
					UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext
							.getBean(UnifiedReceiveOrderService.NAME);
					JSONObject bodyJson = new JSONObject();
					bodyJson.put("body", order);
					bodyJson.put("type", "cancel");

					result = receiveOrder.unifiedReciveOrder(
							Constant.MEITUAN_CHANNEL, bodyJson).toString();
					ccBusniessLogBean.setResponseBody(result);
				} else {
					result = orderService.orderStatusPush(order,Constant.MEITUAN_CHANNEL).toString();
				}
			}
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
	}

	/**
	 * 美团确认订单推送接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/mt_order_confirm_push", method = RequestMethod.POST)
	public void mt_order_confirm_push(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		try {
			JSONObject order = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();
			logger.info("美团确认推送订单信息map长度:" + map.size());
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					String value = new String(URLDecoder.decode(
							map.get(key)[0], "UTF-8").getBytes("UTF-8"));

					order.put(key, value);
				}
				// result =
				// orderService.orderCreate(order.optJSONObject("params"),
				// Constant.MEITUAN_CHANNEL).toString();
			}

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 大众点评订单推送
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/dp_order_push", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject dpOrderPush(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		InputStream is = request.getInputStream();
		BufferedReader reader = new BufferedReader(new InputStreamReader(is));
		String jsobjStr = "";
		String tmp = null;
		while ((tmp = reader.readLine()) != null) {
			jsobjStr += tmp;
		}
		JSONObject jsobj = JSONObject.fromObject(jsobjStr);

		JSONObject result = new JSONObject();
		try {
			result = orderService.orderCreate(jsobj, Constant.DIANPING_CHANNEL);
		} catch (Exception e) {
			e.printStackTrace();
			result.put("errno", "1");
			result.put("error", Constant.MSG_1);
		}
		return result;
	}

	/**
	 * 易吃订单推送
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/yichi_order_push", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject yichiOrderPush(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		InputStream is = request.getInputStream();
		BufferedReader reader = new BufferedReader(new InputStreamReader(is));
		String jsobjStr = "";
		String tmp = null;
		while ((tmp = reader.readLine()) != null) {
			jsobjStr += tmp;
		}
		JSONObject jsobj = JSONObject.fromObject(jsobjStr);

		JSONObject result = new JSONObject();
		String type = jsobj.optString("type").toLowerCase();
		try {
			switch (type) {
			case "order_create":
				result = orderService.orderCreate(jsobj, Constant.YICHI_CHANNEL);
				break;
			case "order_cancel":
				result = orderService.orderStatusPush(jsobj,Constant.YICHI_CHANNEL);
				break;
			default:
				break;
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.put("errno", "1");
			result.put("error", Constant.MSG_1);
		}
		return result;
	}

	/**
	 * 饿了么外卖订单推送
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/eleme_order_push/", method = RequestMethod.POST)
	public void eleMeOrderPush(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		JSONObject res = new JSONObject();
		try {
			JSONObject order = new JSONObject();
			Map<String, String[]> map = request.getParameterMap();
			logger.info("饿了么推送订单信息map长度:" + map.size());
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					String value = new String(URLDecoder.decode(
							map.get(key)[0], "UTF-8").getBytes("UTF-8"));

					order.put(key, CommonUtil.replaceEvilChar(value));
				}
				String pushAction = order.optString("push_action");
				order.put("source", "0635269318");// 5276817402
				order.put("secret", "4382092e5757c04f261d250b81c4407ea535539b");// 340885da38819eece75b3fb31bef07a98d5219b8

				// 执行新的接单模式
				if (isStartNewReciveOrderModel()) {
					UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext
							.getBean(UnifiedReceiveOrderService.NAME);
					res = receiveOrder.unifiedReciveOrder(Constant.ELE_CHANNEL,
							order);

					result = res.toString();
				} else {
					switch (pushAction) {
					case "1":
						res = orderService.orderCreate(order,
								Constant.ELE_CHANNEL);
						if (null != res) {
							result = res.toString();
						}
						break;
					case "2":
						res = orderService.orderStatusPush(order,
								Constant.ELE_CHANNEL);
						break;
					default:
						break;
					}
				}

			}

			out = response.getWriter();
			stopWatch.stop();
			logger.info("处理饿了么订单用时:" + stopWatch.prettyPrint());
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
		// return res;

		// System.out.println("================饿了么新订单:eleme_order_ids===========================");
		// StopWatch stopWatch = new StopWatch();
		// stopWatch.start();
		// InputStream is = request.getInputStream();
		// BufferedReader reader = new BufferedReader(new
		// InputStreamReader(is));
		// JSONObject result = new JSONObject();
		// String jsobjStr = "";
		// String tmp = null;
		// while ((tmp = reader.readLine()) != null)
		// {
		// jsobjStr += tmp;
		// }
		// System.out.println("jsobjStr:"+jsobjStr);
		// JSONObject jsobj = JSONObject.fromObject(jsobjStr);
		// System.out.println("================饿了么新订单:eleme_order_ids="+jsobj.optString("eleme_order_ids")+"===========================");
		// String push_action = jsobj.optString("push_action");
		// try
		// {
		// switch (push_action)
		// {
		// case "1"://新订单
		// result = orderService.orderCreate(jsobj, Constant.ELE_CHANNEL);
		// break;
		// case "2"://订单状态变更
		// result = orderService.orderStatusPush(jsobj,Constant.ELE_CHANNEL);
		// break;
		// // case "3"://退单
		// // result = orderService.orderStatusPush(jsobj,
		// Constant.ELE_CHANNEL);
		// // break;
		// // case "4"://配送状态
		// // result = orderService.orderStatusPush(jsobj,
		// Constant.ELE_CHANNEL);
		// // break;
		// default:
		// break;
		// }
		//
		// }
		// catch (Exception e)
		// {
		// e.printStackTrace();
		// result.put("errno", "1");
		// result.put("error", Constant.MSG_1);
		// }
		// stopWatch.stop();
		// logger.info("处理饿了么订单用时:"+stopWatch.prettyPrint());
		// return ;
	}

	/**
	 * 饿了么外卖测试帐号1.0版本订单推送
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/eleme_order_push_testV1/", method = RequestMethod.POST)
	public JSONObject eleMeOrderPush1(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		JSONObject res = new JSONObject();
		try {
			JSONObject order = new JSONObject();
			Map<String, String[]> map = request.getParameterMap();
			logger.info("饿了么推送订单信息map长度:" + map.size());
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					String value = new String(URLDecoder.decode(
							map.get(key)[0], "UTF-8").getBytes("UTF-8"));

					order.put(key, CommonUtil.replaceEvilChar(value));
				}

				String pushAction = order.optString("push_action");
				order.put("source", "5276817402");//
				order.put("secret", "340885da38819eece75b3fb31bef07a98d5219b8");//

				// 执行新的接单模式
				if (isStartNewReciveOrderModel()) {
					UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext
							.getBean(UnifiedReceiveOrderService.NAME);
					res = receiveOrder.unifiedReciveOrder(Constant.ELE_CHANNEL,
							order);

					result = res.toString();
				} else {

					switch (pushAction) {
					case "1":
						res = orderService.orderCreate(order,
								Constant.ELE_CHANNEL);
						if (null != res) {
							result = res.toString();
						}
						break;
					case "2":
						res = orderService.orderStatusPush(order,
								Constant.ELE_CHANNEL);
						break;
					default:
						break;
					}
				}

			}

			out = response.getWriter();
			stopWatch.stop();
			logger.info("处理饿了么订单用时:" + stopWatch.prettyPrint());
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
		return res;
	}

	/**
	 * 饿了么2.0授权完成后回调地址，用于接收授权码
	 * 
	 * @param request
	 * @param code
	 *            授权码
	 * @param state
	 * @param error
	 * @param error_description
	 * @return
	 */

	@RequestMapping("/elm/authCallBack")
	public void elmAuthCallBack(String code, String state, String error,
			String error_description, HttpServletResponse response) {
		String msg = "";
		try {
			logger.info(
					"饿了么2.0授权回调开始，参数：code：{},state:{},error:{},error_description:{}",
					code, state, error, error_description);

			String ele_oauth_callbackUrl = com.tzx.framework.common.constant.Constant.systemMap
					.get("ele_oauth_callbackUrl");

			if (StringUtils.isEmpty(error)) {
				String[] sateArray = state.split("_");
				String tenancyId = sateArray[0];
				String shopId = sateArray[1];
				String authLevel = sateArray[2];

				OAuthClient client = new OAuthClient(ElmUtils.getConfig(
						tenancyId, shopId));

				Token token = client
						.getTokenByCode(code, ele_oauth_callbackUrl);

				ElmUtils.saveToken(true, tenancyId, shopId, authLevel,
						ElmUtils.ELM_OAUTH_TYPE_ENTERPRISE, token);
				
				//注册token刷新任务
				CcTokenRefreshTaskRegisterServcie r = (CcTokenRefreshTaskRegisterServcie) SpringConext
						.getBean("ccTokenRefreshTaskRegisterServcieImpl");
				r.registerTokenTask(tenancyId, shopId);

				logger.info("饿了么2.0密钥：->>>" + token.toString());

				msg = "授权成功!";
			} else {
				logger.info("饿了么2.0授权出现错误：{}", error_description);

				msg = error_description;
			}
		} catch (Exception ex) {
			logger.info("饿了么2.0授权回调发生错误：{}", ex.getMessage());
			msg = "授权失败，请重新进行授权!";

		}

		PrintWriter out = null;
		try {
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			out = response.getWriter();
			out.print("<center>" + msg + "</center>");
			out.flush();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			if (out != null) {
				out.close();
			}
		}
	}

	/**
	 * 外卖接单2.0
	 * 
	 * @param body
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/eleme_order_push_v2")
	public void eleMeOrderPushV2(HttpServletRequest request,
			HttpServletResponse response) {
		UUID requestId=UUID.randomUUID();
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setChannel("EL09");
		ccBusniessLogBean.setChannelName("EL09");
		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setRequestId(requestId.toString());
		ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.OrderDownstreamRest.eleMeOrderPushV2");

		String elmShopId = "";// 饿了么门店id
		String tenancyId = "";// 天子星商户号
		String elmOrderId = "";// 饿了么订单id
		String shopId = "";// 天子星门店id

		JSONObject res = new JSONObject();
		res.put("message", "ok");
		PrintWriter out = null;
		int type =0;
		JSONObject requestOrderJson=null;
		try {

			InputStream is = request.getInputStream();
			BufferedReader reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String body = "";
			String tmp = null;
			while ((tmp = reader.readLine()) != null) {
				body += tmp;
			}
			is.close();

			ccBusniessLogBean.setRequestBody(body);

			if (!StringUtils.isEmpty(body)) {

				JSONObject bodyJson = JSONObject.fromObject(body);
				String signature = bodyJson.optString("signature");

				bodyJson.discard("signature");

				// 获取门店id
				elmShopId = bodyJson.optString("shopId");
				try{
					String[] arr = ElmUtils.getTenancyInfoByElmShopId(elmShopId);
					shopId = arr[0];
					tenancyId = arr[1];
				}catch(Exception ex){
					logger.error("无法正常获取授权信息：",ex);
					res.put("message", "ok");					
				}
				
				if(!StringUtils.isEmpty(shopId)){
					type = bodyJson.optInt("type");
					
					ccBusniessLogBean.setTenancyId(tenancyId);
					ccBusniessLogBean.setShopId(shopId);
					
					Map<String, T> treeMap = new TreeMap<String, T>();
					treeMap.putAll(bodyJson);
	
					StringBuffer sb = new StringBuffer();
	
					Iterator<Map.Entry<String, T>> iter = treeMap.entrySet().iterator();
					while (iter.hasNext()) {
						Map.Entry<String, T> entry = iter.next();
						sb.append(entry.getKey()).append("=").append(entry.getValue());
					}
					sb.append(ElmUtils.getKeySecret(tenancyId, shopId).get("secret"));
					// 对请求内容进行签权
					String tmpSignature = ElmUtils.MD5(sb.toString());
	
					if (signature.equals(tmpSignature)) {
						String message = bodyJson.getString("message");
						// 订单信息
						requestOrderJson= JSONObject.fromObject(message);
						elmOrderId = requestOrderJson.optString("orderId");
	
						logger.info("饿了么2.0接单验证通过，进入接单处理流程...接收到消体：商户:{}->消息：{}",tenancyId, bodyJson);
						// 添加版本信息，用于后端识别时所用
						bodyJson.put("version", Constant.ELM_API_VERSION_V2);
	
						// 执行新的接单模式
						if (isStartNewReciveOrderModel()) {
							UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext.getBean(UnifiedReceiveOrderService.NAME);
							res = receiveOrder.unifiedReciveOrder(Constant.ELE_CHANNEL, bodyJson);
						} else {
							
							if (type == 10) {
								// 创建订单
								ccBusniessLogBean.setCmd("接收订单-新订单");
								ccBusniessLogBean.setType(CcBusniessLogBean.TypeProperty.ORDER_RECIVE);
								res = orderService.orderCreate(bodyJson,
										Constant.ELE_CHANNEL);
							} else {
								// 14 订单被取消,15 订单置为无效,23 商户同意取消单,18 订单完结
								if (type == 14 || type == 15 || type == 17
										|| type == 23 || type == 33) {
									ccBusniessLogBean.setCmd("接收订单-取消订单");
								} else if (type == 18) {
									ccBusniessLogBean.setCmd("接收订单-订单完成");
								}
	
								ccBusniessLogBean.setType(CcBusniessLogBean.TypeProperty.ORDER_CANCEL);
								res = orderService.orderStatusPush(bodyJson,
										Constant.ELE_CHANNEL);
							}
						}
	
					} else {
						logger.info("商户{}->签名验证未通过.传递的签权值：{},天子星验证签权值:{}",tenancyId, signature, tmpSignature);
	
						res.put("success", false);
						res.put("msg", "签名验证未通过!");
						res.put("message", "fails");
					}
				}
				} else {
					logger.info("请求消息体为空");
				}
			
		} catch (Exception ex) {
			logger.info("饿了么外卖2.0收接异常：" + ex.getMessage());

			res.put("success", false);
			// res.put("msg", ex);
			res.put("msg", "饿了么2.0接单发生异常");
			res.put("message", "fails");
		}

		ccBusniessLogBean.setResponseBody(res.toString());
		ccBusniessLogBean.setErrorBody(res.optString("msg"));
		if(type == 14 || type == 15 || type == 17	|| type == 23 || type == 33) {
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.OrderDownstreamRest.cancelOrder");
			ccBusniessLogBean.setType("cancelOrder");
			ccBusniessLogBean.setOperAction("cancelOrder");
		}else {
			ccBusniessLogBean.setType("reviceOrder");
			ccBusniessLogBean.setOperAction(OrderOper.receiveOrder.toString());
		}
		if(requestOrderJson!=null) {
			ccBusniessLogBean.setTzxId(LogUtils.getOrderCode(requestOrderJson, Constant.ELE_CHANNEL, shopId));
		}
			ccBusniessLogBean.setThirdId(elmOrderId);

		try {

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			out = response.getWriter();
			out.print(res);
			out.flush();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			if (out != null) {
				out.close();
			}
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}

	}

	/**
	 * 田老师饿了么外卖订单推送
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/eleme_order_push_mrtian/")
	public JSONObject eleme_order_push_mrtian(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		JSONObject res = new JSONObject();
		try {
			JSONObject order = new JSONObject();
			Map<String, String[]> map = request.getParameterMap();
			logger.info("饿了么推送订单信息map长度:" + map.size());
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					String value = new String(URLDecoder.decode(map.get(key)[0], "UTF-8").getBytes("UTF-8"));
					order.put(key, CommonUtil.replaceEvilChar(value));
				}

				order.put("source", "1204990380");
				order.put("secret", "7ee0ad79a4bc80829d8b431dbe21899b5ec2a637");

				// 执行新的接单模式
				if (isStartNewReciveOrderModel()) {
					UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext.getBean(UnifiedReceiveOrderService.NAME);
					res = receiveOrder.unifiedReciveOrder(Constant.ELE_CHANNEL,order);
					result = res.toString();

				} else {

					String pushAction = order.optString("push_action");

					switch (pushAction) {
					case "1":
						res = orderService.orderCreate(order,Constant.ELE_CHANNEL);
						if (null != res) {
							result = res.toString();
						}
						break;
					case "2":
						logger.info("饿了么推送订单状态信息:" + order.toString());
						res = orderService.orderStatusPush(order,Constant.ELE_CHANNEL);
						logger.info("饿了么推送订单状态信息传入:" + order.toString() + "返回："+ res.toString());
						break;
					default:
						break;
					}
				}
			}

			out = response.getWriter();
			stopWatch.stop();
			logger.info("处理饿了么订单用时:" + stopWatch.prettyPrint());
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
		return res;

		// System.out.println("================饿了么新订单:eleme_order_ids===========================");
		// StopWatch stopWatch = new StopWatch();
		// stopWatch.start();
		// InputStream is = request.getInputStream();
		// BufferedReader reader = new BufferedReader(new
		// InputStreamReader(is));
		// JSONObject result = new JSONObject();
		// String jsobjStr = "";
		// String tmp = null;
		// while ((tmp = reader.readLine()) != null)
		// {
		// jsobjStr += tmp;
		// }
		// System.out.println("jsobjStr:"+jsobjStr);
		// JSONObject jsobj = JSONObject.fromObject(jsobjStr);
		// System.out.println("================饿了么新订单:eleme_order_ids="+jsobj.optString("eleme_order_ids")+"===========================");
		// String push_action = jsobj.optString("push_action");
		// try
		// {
		// switch (push_action)
		// {
		// case "1"://新订单
		// result = orderService.orderCreate(jsobj, Constant.ELE_CHANNEL);
		// break;
		// case "2"://订单状态变更
		// result = orderService.orderStatusPush(jsobj,Constant.ELE_CHANNEL);
		// break;
		// // case "3"://退单
		// // result = orderService.orderStatusPush(jsobj,
		// Constant.ELE_CHANNEL);
		// // break;
		// // case "4"://配送状态
		// // result = orderService.orderStatusPush(jsobj,
		// Constant.ELE_CHANNEL);
		// // break;
		// default:
		// break;
		// }
		//
		// }
		// catch (Exception e)
		// {
		// e.printStackTrace();
		// result.put("errno", "1");
		// result.put("error", Constant.MSG_1);
		// }
		// stopWatch.stop();
		// logger.info("处理饿了么订单用时:"+stopWatch.prettyPrint());
		// return ;
	}

	/**
	 * 饿了么外卖订单推送(压力测试接口) add by qinhulin on 2017-02-22
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/eleme_order_push_test", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject eleMeOrderPushTest(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		InputStream is = request.getInputStream();
		BufferedReader reader = new BufferedReader(new InputStreamReader(is));
		JSONObject result = new JSONObject();
		String jsobjStr = "";
		String tmp = null;
		while ((tmp = reader.readLine()) != null) {
			jsobjStr += tmp;
		}
		JSONObject jsobj = JSONObject.fromObject(jsobjStr);
		try {
			ThirdPartyOrderReceiver orderReceiver = new EleMeOrderReceiverTest(
					jsobj);
			result = orderReceiver.receive();
		} catch (Exception e) {
			e.printStackTrace();
			result.put("errno", "1");
			result.put("error", Constant.MSG_1);
		}
		stopWatch.stop();
		logger.info("[测试]饿了么收取订单用时:" + stopWatch.prettyPrint());
		return result;
	}

	/**
	 * 外卖接单测试2.0
	 * 
	 * @param body
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/eleme_order_push_test2")
	public void eleme_order_push_v2_test(HttpServletRequest request,
			HttpServletResponse response) {
		JSONObject res = new JSONObject();
		res.put("message", "ok");
		PrintWriter out = null;
		try {

			InputStream is = request.getInputStream();
			BufferedReader reader = new BufferedReader(new InputStreamReader(
					is, "UTF-8"));
			String body = "";
			String tmp = null;
			while ((tmp = reader.readLine()) != null) {
				body += tmp;
			}
			is.close();

			if (!StringUtils.isEmpty(body)) {

				JSONObject bodyJson = JSONObject.fromObject(body);

				logger.info("测试-->饿了么2.0接单验证通过，进入接单处理流程...接收到消体：{}", bodyJson);
				// 添加版本信息，用于后端识别时所用
				bodyJson.put("version", Constant.ELM_API_VERSION_V2);

				// 执行新的接单模式
				if (isStartNewReciveOrderModel()) {
					UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext
							.getBean(UnifiedReceiveOrderService.NAME);
					res = receiveOrder.unifiedReciveOrder(Constant.ELE_CHANNEL,
							bodyJson);
				} else {

					int type = bodyJson.optInt("type");
					if (type == 10) {
						// 创建订单
						res = orderService.orderCreate(bodyJson,
								Constant.ELE_CHANNEL);
					} else {
						res = orderService.orderStatusPush(bodyJson,
								Constant.ELE_CHANNEL);
					}
				}

			} else {
				logger.info("测试-->请求消息体为空");
				//res.put("msg", "测试-->请求消息体为空");
			}
		} catch (Exception ex) {
			logger.info("测试-->饿了么外卖2.0收接异常：" + ex.getMessage());

			res.put("success", false);
			// res.put("msg", ex);
			res.put("msg", "饿了么2.0接单发生异常");
			res.put("message", "fails");
		}

		try {

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			out = response.getWriter();
			out.print(res);
			out.flush();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			if (out != null) {
				out.close();
			}
		}

	}

	//at 2017-08-21 是否开启新的接单模式
	private boolean isStartNewReciveOrderModel() {
		if ("1".equals(com.tzx.framework.common.constant.Constant
				.getSystemMap().get("waimai_start"))) {
			return true;
		}
		return false;
	}
	
	
	/**
	 * 异常看板 （报表）
	 * 参数 店铺id（store_id） 日期传数字类型的chanelDate天数（今天、昨天、7日内、30日内、90日内）
	 * 
	 * @return param
	 * @throws Exception
	 */
	@RequestMapping(value = "/yc_order_list", method = RequestMethod.POST)
	@ResponseBody
	public void orderYcStatus(String code,int chanelDate,HttpServletRequest request,
			HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");
		PrintWriter writer = null;
		DBContextHolder.setTenancyid(tenantId);
		JSONObject orderYcStatus = null;
		try {
			 orderYcStatus = orderService.orderYcStatus(tenantId,code, chanelDate);
		} catch (Exception e) {
			e.printStackTrace();
		}
		logger.info("异常看板报表展示=====wjh");
		 try {
			writer = response.getWriter();
			writer.print(orderYcStatus.toString());
			writer.flush();
			writer.close();
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			if(null!=writer){
				writer.close();
				writer=null;
			}
		}
	}
	
	//changhui 2017/11/6 start
	/**
	 * 新美大外卖订单推送
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/xmd_order_push", method = RequestMethod.POST)
	@ResponseBody
	public void xmdOrderPush(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		logger.info("调用新美大外卖接收订单数据接口地址：");
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		UUID requestId=UUID.randomUUID();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		try {
			JSONObject order = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();
			logger.info("新美大外卖推送订单信息map长度:" + map.size());
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					String value = new String(URLDecoder.decode(
							map.get(key)[0], "UTF-8").getBytes("UTF-8"));

					order.put(key, CommonUtil.replaceEvilChar(value));
				}
				
				ccBusniessLogBean.setRequestId(requestId.toString());
				order.put("requestId", requestId);
				String[] arr=order.optString("ePoiId").split("@");
				if(order!=null){
					ccBusniessLogBean.setTzxId(LogUtils.getOrderCode(order, Constant.XMD_CHANNEL, arr[0]));
					ccBusniessLogBean.setThirdId(order.getJSONObject("order").getString("orderId"));
				}
				ccBusniessLogBean.setTenancyId(arr[1]);
				ccBusniessLogBean.setShopId(arr[0]);
				
				ccBusniessLogBean.setCategory("cc");
				ccBusniessLogBean.setType("reviceOrder");
				ccBusniessLogBean.setChannel(Constant.XMDWM_CHANNEL);
				ccBusniessLogBean.setChannelName(Constant.XMDWM_CHANNEL);
			
				ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.OrderDownstreamRest:xmdOrderPush");
				ccBusniessLogBean.setRequestBody(order.toString());

				ccBusniessLogBean.setCreateTime(new Date().getTime());
				ccBusniessLogBean.setIsNormal("1");
				ccBusniessLogBean.setIsThird("0");
				ccBusniessLogBean.setOperAction(OrderOper.receiveOrder.toString());
				
				// 执行新的接单模式
				if (isStartNewReciveOrderModel()) {
					logger.info("新美大外卖接收订单:执行新的接单模式！");
					UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext
							.getBean(UnifiedReceiveOrderService.NAME);
					JSONObject bodyJson = new JSONObject();
					bodyJson.put("body", order);
					bodyJson.put("type", "create");

					result = receiveOrder.unifiedReciveOrder(
							Constant.XMDWM_CHANNEL, bodyJson).toString();
				} else {
					JSONObject res = orderService.orderCreate(order,
							Constant.XMDWM_CHANNEL);
					if (null != res) {
						result = res.toString();
					}
				}
		}
			ccBusniessLogBean.setResponseBody(result);
			out = response.getWriter();
			stopWatch.stop();
			logger.info("处理新美大外卖订单用时:" + stopWatch.prettyPrint());
			out.print(result);
			out.flush();
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}
	//end
	
	//changhui 2017/11/7 start
	/**
	 * 新美大外卖确认订单推送接口
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/xmd_order_confirm_push", method = RequestMethod.POST)
	public void xmd_order_confirm_push(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		try {
			JSONObject order = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();
			logger.info("新美大确认推送订单信息map长度:" + map.size());
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					String value = new String(URLDecoder.decode(
							map.get(key)[0], "UTF-8").getBytes("UTF-8"));

					order.put(key, value);
				}
				// result =
				// orderService.orderCreate(order.optJSONObject("params"),
				// Constant.XMDWM_CHANNEL).toString();
			}

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}
	//end
	
	//changhui 2017/11/8 start
	/**
	 * 新美大用户或客服取消
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/xmd_order_cancel", method = RequestMethod.POST)
	public void xmdOrderCancel(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "";
		UUID requestId=UUID.randomUUID();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		try {
			JSONObject order = new JSONObject();
			Map<String, String[]> map = request.getParameterMap();
			logger.info("新美大取消订单推送信息map长度:" + map.size());
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					logger.info("新美大取消订单推送信息:"
							+ URLDecoder.decode(map.get(key)[0], "UTF-8")
									.getBytes("UTF-8"));
					String value = new String(URLDecoder.decode(
							map.get(key)[0], "UTF-8").getBytes("iso-8859-1"));

					order.put(key, value);
				}
				logger.info("新美大取消订单推送");
				order.put("status", ThirdPartyOrderService.ORDER_CANCEL);


				
				String[] shopIdArr = order.optString("ePoiId").split("@");
			
				ccBusniessLogBean.setRequestId(requestId.toString());
				ccBusniessLogBean.setTzxId(LogUtils.getOrderCode(order, Constant.XMD_CHANNEL, shopIdArr[0]));
				
				ccBusniessLogBean.setThirdId(order.optString("orderId"));
				
				ccBusniessLogBean.setTenancyId(shopIdArr[1]);
				ccBusniessLogBean.setShopId(shopIdArr[0]);
				
				ccBusniessLogBean.setCategory("cc");
				ccBusniessLogBean.setType("cancelOrder");
				ccBusniessLogBean.setChannel(Constant.XMD_CHANNEL);
				ccBusniessLogBean.setChannelName(Constant.XMD_CHANNEL);
			
				ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.OrderDownstreamRest:xmdOrderCancel");
				ccBusniessLogBean.setRequestBody(order.toString());

				ccBusniessLogBean.setCreateTime(new Date().getTime());
				ccBusniessLogBean.setIsNormal("1");
				ccBusniessLogBean.setIsThird("0");
				ccBusniessLogBean.setOperAction(OrderOper.cancelOrder.toString());
				
				// 执行新的接单模式
				if (isStartNewReciveOrderModel()) {
					UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext
							.getBean(UnifiedReceiveOrderService.NAME);
					JSONObject bodyJson = new JSONObject();
					bodyJson.put("body", order);
					bodyJson.put("type", "cancel");

					result = receiveOrder.unifiedReciveOrder(
							Constant.XMDWM_CHANNEL, bodyJson).toString();
				} else {
					result = orderService.orderStatusPush(order,
							Constant.XMDWM_CHANNEL).toString();
				}
			}
			ccBusniessLogBean.setResponseBody(result);
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}
	//end
	
	//changhui 2017/11/8 start
		/**
		 * 新美大用户申请订单退款取消订单
		 * 
		 * @param request
		 * @param response
		 */
		@RequestMapping(value = "/xmd_order_refund_cancel", method = RequestMethod.POST)
		public void xmdOrderRefundCancel(HttpServletRequest request,
				HttpServletResponse response) throws Exception {
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			String result = "";
			UUID requestId=UUID.randomUUID();
			CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
			try {
				JSONObject order = new JSONObject();

				Map<String, String[]> map = request.getParameterMap();
				logger.info("新美大用户申请订单退款取消订单推送信息map长度:" + map.size());
				if (map.size() > 0) {
					for (String key : map.keySet()) {
						logger.info("新美大用户申请订单退款取消订单推送信息:"
								+ URLDecoder.decode(map.get(key)[0], "UTF-8")
										.getBytes("UTF-8"));
						String value = new String(URLDecoder.decode(
								map.get(key)[0], "UTF-8").getBytes("iso-8859-1"));

						order.put(key, value);
					}
					logger.info("新美大用户申请订单退款取消订单推送");
					//
					String[] shopIdArr = order.optString("ePoiId").split("@");
					
					ccBusniessLogBean.setRequestId(requestId.toString());
					ccBusniessLogBean.setTzxId(LogUtils.getOrderCode(order, Constant.XMD_CHANNEL, shopIdArr[0]));
					
					ccBusniessLogBean.setTenancyId(shopIdArr[1]);
					ccBusniessLogBean.setShopId(shopIdArr[0]);
					
					ccBusniessLogBean.setCategory("cc");
					ccBusniessLogBean.setType("cancelOrder");
					ccBusniessLogBean.setChannel(Constant.XMD_CHANNEL);
					ccBusniessLogBean.setChannelName(Constant.XMD_CHANNEL);
				
					ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.OrderDownstreamRest:xmdOrderRefundCancel");
					

					ccBusniessLogBean.setCreateTime(new Date().getTime());
					ccBusniessLogBean.setIsNormal("1");
					ccBusniessLogBean.setIsThird("0");
					ccBusniessLogBean.setOperAction(OrderOper.cancelOrder.toString());
					
					JSONObject orderInfo = new JSONObject();	
					orderInfo = order.optJSONObject("orderRefund");
					String orderRefund = orderInfo.optString("notifyType");
					
					ccBusniessLogBean.setThirdId(orderInfo.optString("orderId"));
					
					//判断是不是退款消息类型为确认退款的才去取消订单
					//“apply”	发起退款
					//“agree”	确认退款
					//“reject”	驳回退款
					//“cancelRefund“	用户取消退款申请
					//”cancelRefundComplaint”	取消退款申诉
					//后续还会有全部退款和部分退款
					if(orderRefund.equals("agree")){
						order.put("status", ThirdPartyOrderService.ORDER_CANCEL);
						//封装订单取消参数到order中
						JSONObject orderNew = new JSONObject();	
						orderNew.put("orderId", orderInfo.optString("orderId"));
						orderNew.put("reasonCode", "1103");
						orderNew.put("reason", "用户取消，用户退款取消");
						order.put("orderCancel", orderNew);
						// 执行新的接单模式
						if (isStartNewReciveOrderModel()) {
							UnifiedReceiveOrderService receiveOrder = (UnifiedReceiveOrderService) SpringConext
									.getBean(UnifiedReceiveOrderService.NAME);
							JSONObject bodyJson = new JSONObject();
							bodyJson.put("body", order);
							bodyJson.put("type", "cancel");

							result = receiveOrder.unifiedReciveOrder(
									Constant.XMDWM_CHANNEL, bodyJson).toString();
						} else {
							result = orderService.orderStatusPush(order,
									Constant.XMDWM_CHANNEL).toString();
						}
					}
				}
				ccBusniessLogBean.setRequestBody(order.toString());
				ccBusniessLogBean.setResponseBody(result);
				out = response.getWriter();
				out.print(result);
				out.flush();
			} catch (Exception e) {
				ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
				ccBusniessLogBean.setIsNormal("0");
				e.printStackTrace();
			} finally {
				KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
				if (null != out) {
					out.close();
				}
			}
		}
		//end
	
}
