package com.tzx.cc.bo;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONObject;

import com.tzx.cc.bo.dto.Data;
import com.tzx.framework.bo.dto.Version;
import com.tzx.framework.common.exception.SystemException;

public interface OcService {
	String	NAME = "com.tzx.cc.bo.imp.OcServiceImpl";
	
	
	/**登陆验证
	 * @param Data
	 * @return Data
	 * @throws SystemException
	 */
	public void oc_login(Data param) throws SystemException,Exception;

	/**
	 * 基础资料版本检查
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> basicVersion(Data param) throws Exception;
	/**
	 * 同步基础数据
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void syncBaseData(Data param) throws SystemException;
	
	/**
	 * 查询会员相关信息
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void findCustomerinfo(Data param) throws SystemException;
	
	/**会员地址信息维护
	 * @param Data
	 * @return Data
	 * @throws SystemException
	 */
	public void customerAddress(Data param) throws SystemException,Exception;
	/**订单信息维护
	 * @param Data
	 * @return Data
	 * @throws SystemException
	 */
	public void orderInfo(Data param) throws SystemException,Exception;
	
	/**统计订单总数
	 * @param Data
	 * @return Data
	 * @throws SystemException
	 */
	public void orderTotal(Data param) throws SystemException,Exception;
	
	/**
	 * //应用程序版本检测
	 * @param request
	 * @param param
	 * @return
	 * @throws Exception
	 */
	List<Version> findAppVersion(HttpServletRequest request, List<?> param) throws Exception;
}
