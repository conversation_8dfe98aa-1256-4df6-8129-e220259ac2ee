package com.tzx.report.po.boh.impl;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.DoBusinessSummaryStatisticsTimeIntervalDao;

@Repository(DoBusinessSummaryStatisticsTimeIntervalDao.NAME)
public class DoBusinessSummaryStatisticsTimeIntervalDaoImpl implements DoBusinessSummaryStatisticsTimeIntervalDao{

	private final String sqlDateL0 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_13' and sql_type='RQL0'";  // 合计
	private final String sqlDateL1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_13' and sql_type='RQL1'";  // 主记录
	private final String sqlDateL2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_13' and sql_type='RQL2'";  // 明细记录

    private final String sqlStoreL0 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_13' and sql_type='MDL0'"; // 合计
    private final String sqlStoreL1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_13' and sql_type='MDL1'"; // 主记录
    private final String sqlStoreL2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_13' and sql_type='MDL2'"; // 明细记录
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;

	@Resource
	ConditionUtils conditionUtils;
	
	
	private static final Logger logger = Logger.getLogger(DoBusinessSummaryStatisticsTimeIntervalDaoImpl.class);
	
	
	
    @Override
    public JSONObject queryBusinessTime(String tenentid, JSONObject p) throws Exception {

        JSONObject result = new JSONObject();
        long total = 0L;
        List<JSONObject> list = new ArrayList<JSONObject>();
        List<JSONObject> footerList =new ArrayList<JSONObject>();
        String queryWay = p.optString("queryWay");
        String hierarchytype = p.optString("hierarchytype");
        String begindate = p.optString("report_date_begin");
        String enddate = p.optString("report_date_end");
        List<JSONObject> structure = new ArrayList<JSONObject>();

        if(begindate.length()>0 && enddate.length()>0)
        {
        	
        	//判断当前是否存在多次时间处理
        	if(!p.containsKey("compare")){
        		//处理结束时间小于结束时间
        		p = changeEndTimeOrStartTime(p);
        	}
        	if(!p.containsKey("exportdataexpr")){
        		p.put("exportdataexpr", "'99999999'");
        	}
        	if(queryWay.equals("1")) {
        		if(hierarchytype.equals("1")) {
		            String reportSql = parameterUtils.parameterAutomaticCompletion(tenentid, p,sqlDateL1);
		            logger.info(reportSql+"营业汇总时段占比");
		            if(p.containsKey("derivedtype") && p.optInt("derivedtype")==2){
						list = this.dao.query4Json(tenentid, parameterUtils.buildPageSqlReportlLevel(p,reportSql.toString(),p.optInt("level")));
						structure = conditionUtils.getSqlStructure(tenentid, reportSql.toString());
					}else{
						list = this.dao.query4Json(tenentid,this.dao.buildPageSql(p,reportSql.toString()));
						total = this.dao.countSql(tenentid,reportSql.toString());
						String reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenentid, p, sqlDateL0);
						footerList = this.dao.query4Json(tenentid, reportSqlCount.toString());
					}
		            
        		}else if (hierarchytype.equals("2")){
        			String reportSql = parameterUtils.parameterAutomaticCompletion(tenentid, p, sqlDateL2 );
                    System.err.println(reportSql);
                    if(p.containsKey("derivedtype") && p.optInt("derivedtype")==2){
                    	list = this.dao.query4Json(tenentid, parameterUtils.buildPageSqlReportlLevel(p,reportSql.toString(),p.optInt("level1")));
                    	structure = conditionUtils.getSqlStructure(tenentid, reportSql.toString());
                    }else{
        				list = this.dao.query4Json(tenentid,this.dao.buildPageSql(p,reportSql.toString()));
        				total = this.dao.countSql(tenentid,reportSql.toString());
        			}
        		}
        	}else if (queryWay.equals("2")){
        		if(hierarchytype.equals("1")) {
		            String reportSql = parameterUtils.parameterAutomaticCompletion(tenentid, p,sqlStoreL1);
		            System.err.println(reportSql);
		            if(p.containsKey("derivedtype") && p.optInt("derivedtype")==2){
						list = this.dao.query4Json(tenentid, parameterUtils.buildPageSqlReportlLevel(p,reportSql.toString(),p.optInt("level")));
						structure = conditionUtils.getSqlStructure(tenentid, reportSql.toString());
					}else{
						list = this.dao.query4Json(tenentid,this.dao.buildPageSql(p,reportSql.toString()));
						total = this.dao.countSql(tenentid,reportSql.toString());
						String reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenentid, p, sqlStoreL0);
						footerList = this.dao.query4Json(tenentid, reportSqlCount.toString());
					}
		           
        		}else if (hierarchytype.equals("2")){
        			String reportSql = parameterUtils.parameterAutomaticCompletion(tenentid, p, sqlStoreL2 );
                    System.err.println(reportSql);
                    if(p.containsKey("derivedtype") && p.optInt("derivedtype")==2){
                    	list = this.dao.query4Json(tenentid, parameterUtils.buildPageSqlReportlLevel(p,reportSql.toString(),p.optInt("level1")));
                    	structure = conditionUtils.getSqlStructure(tenentid, reportSql.toString());
                    }else{
        				list = this.dao.query4Json(tenentid,this.dao.buildPageSql(p,reportSql.toString()));
        				total = this.dao.countSql(tenentid,reportSql.toString());
        			}
                    
        		}
        	}
        }

        int pagenum = p.containsKey("page") ? (p.getInt("page") == 0 ? 1 : p.getInt("page")) : 1;

        result.put("page", pagenum);
        result.put("total",total);
        result.put("rows", list);
        result.put("footer", footerList);
        result.put("structure", structure);
        return result;
    }

    @Override
    public JSONObject querySecondLevel(String tenentid, JSONObject p) throws Exception {

        JSONObject result = new JSONObject();
        long total = 0L;
        List<JSONObject> list = new ArrayList<JSONObject>();
        List<JSONObject> footerList =new ArrayList<JSONObject>();

        String begindate = p.optString("report_date_begin");
        String enddate = p.optString("report_date_end");

        if(begindate.length()>0 && enddate.length()>0)
        {
            String reportSql = parameterUtils.parameterAutomaticCompletion(tenentid, p, (p.optInt("queryWay") == 1) ? sqlDateL2 : sqlStoreL2);
            System.err.println(reportSql);
            total = this.dao.countSql(tenentid,reportSql.toString());
            list = this.dao.query4Json(tenentid,this.dao.buildPageSql(p,reportSql.toString()));

            String reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenentid, p, (p.optInt("queryWay") == 1) ? sqlDateL2 : sqlStoreL2);
            footerList = this.dao.query4Json(tenentid, reportSqlCount.toString());
        }

        int pagenum = p.containsKey("page") ? (p.getInt("page") == 0 ? 1 : p.getInt("page")) : 1;

        result.put("page", pagenum);
        result.put("total",total);
        result.put("rows", list);
        result.put("footer", footerList);

        return result;
    }
    
    
    public JSONObject changeEndTimeOrStartTime(JSONObject p) throws ParseException {
    	
    	//创建日期转换对象HH:mm:ss为时分秒
    	DateFormat df = new SimpleDateFormat("HH:mm");
    	
    	//time1_start 07:00
    	String timeStarKey = "time?_start";
    	String timeEndKey = "time?_end";
    	
    	String zoreString = "00:00";
    	String twentyFourString = "24:00";
    	
    	String  suffix ="_new";
    	
    	for(Integer i = 1 ; i<=8 ; i++) {
    		timeStarKey= timeStarKey.replace("?", i.toString());
    		timeEndKey=timeEndKey.replace("?", i.toString());
    	 
    		if(p.optString(timeStarKey).equals("''")||p.optString(timeStarKey).equals("'99999999'")) {
				p.put(timeStarKey+suffix, "''");
    			p.put(timeEndKey+suffix, "''");
    			timeStarKey = "time?_start";
    	    	timeEndKey = "time?_end";
    	    	continue ; 
    		}else{
    			String kstimeinterval = p.optString(timeStarKey).replace("'", "");
    			String jstimeinterval = p.optString(timeEndKey).replace("'", "");
    			java.util.Date starTime = df.parse(kstimeinterval);
        		java.util.Date endTime =  df.parse(jstimeinterval);
        		
        		if(starTime.before(endTime)){//开始时段在结束时段之前
        			p.put(timeStarKey+suffix, "''");
        			p.put(timeEndKey+suffix, "''");
        		}else if(!starTime.before(endTime)){//开始时段在结束时段之后
        			p.put(timeEndKey, "'"+twentyFourString+"'");  //结束时段改为23:59分
        			p.put(timeStarKey+suffix, "'"+zoreString+"'");//新new开始时段改为00:00
        			p.put(timeEndKey+suffix, "'"+jstimeinterval+"'");//新new结束时段改为大于开始时段值
        		}
        		timeStarKey = "time?_start";
    	    	timeEndKey = "time?_end";
    		}
    	}
    	p.element("compare", true);
    	return p;
    }
    
    
    
    
}
