package com.tzx.cc.baidu.entity;

import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

public class DishCreateUpdate {
	/* 合作方商户唯一ID */
	private String shop_id;
	/* 菜品唯一编号 */
	private String dish_id;
	/* 菜品名称 */
	private String name;
	/* 条形码编号 */
	private String upc;
	/* 菜品价格，单位：分 */
	private int price;
	/* 菜品图片 */
	private String pic;
	/* 最小起订份数 */
	private int min_order_num;
	/* 单份所需餐盒数 */
	private int package_box_num;
	/* 描述 */
	private String description;
	/* 可售时间 */
	private Map<String, List<JSONObject>> available_times;
	/* 菜品库存设置 */
	private List<Threshold> threshold;
	/* 分类信息 */
	private List<JSONObject> category;
	/* 菜品规格 */
	private List<JSONObject> norms;
	/* 菜品属性 */
	private List<Attr> attr;
	
	public String getShop_id()
	{
		return shop_id;
	}
	public void setShop_id(String shop_id)
	{
		this.shop_id = shop_id;
	}
	public String getDish_id()
	{
		return dish_id;
	}
	public void setDish_id(String dish_id)
	{
		this.dish_id = dish_id;
	}
	public String getName()
	{
		return name;
	}
	public void setName(String name)
	{
		this.name = name;
	}
	public String getUpc()
	{
		return upc;
	}
	public void setUpc(String upc)
	{
		this.upc = upc;
	}
	public int getPrice()
	{
		return price;
	}
	public void setPrice(int price)
	{
		this.price = price;
	}
	public String getPic()
	{
		return pic;
	}
	public void setPic(String pic)
	{
		this.pic = pic;
	}
	public int getMin_order_num()
	{
		return min_order_num;
	}
	public void setMin_order_num(int min_order_num)
	{
		this.min_order_num = min_order_num;
	}
	public int getPackage_box_num()
	{
		return package_box_num;
	}
	public void setPackage_box_num(int package_box_num)
	{
		this.package_box_num = package_box_num;
	}
	public String getDescription()
	{
		return description;
	}
	public void setDescription(String description)
	{
		this.description = description;
	}
	public Map<String, List<JSONObject>> getAvailable_times()
	{
		return available_times;
	}

	public void setAvailable_times(Map<String, List<JSONObject>> available_times)
	{
		this.available_times = available_times;
	}
	public List<Threshold> getThreshold()
	{
		return threshold;
	}
	public void setThreshold(List<Threshold> threshold)
	{
		this.threshold = threshold;
	}
	public List<JSONObject> getCategory()
	{
		return category;
	}
	public void setCategory(List<JSONObject> category)
	{
		this.category = category;
	}
	public List<JSONObject> getNorms()
	{
		return norms;
	}
	public void setNorms(List<JSONObject> norms)
	{
		this.norms = norms;
	}
	public List<Attr> getAttr()
	{
		return attr;
	}
	public void setAttr(List<Attr> attr)
	{
		this.attr = attr;
	}
}
