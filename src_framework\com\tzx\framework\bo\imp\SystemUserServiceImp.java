package com.tzx.framework.bo.imp;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import net.sf.json.JSONObject;

import com.tzx.framework.bo.SystemUserService;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.po.springjdbc.dao.SystemUserDao;

@Service(SystemUserService.NAME)
public class SystemUserServiceImp implements SystemUserService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao		dao;
	@Resource(name="systemUserDaoImpl")
	private SystemUserDao systemUserDao;
	@Override
	public JSONObject getSystemUser(String tenancyID, JSONObject condition,String organIds)
	{
		StringBuilder sql = new StringBuilder();
		
		sql.append(" select u.id,u.employee_id,u.user_name,u.remark,u.last_updatetime,u.last_operator,u.valid_state, ");
		sql.append(" e.name,e.employee_code,e.paper_no,e.phone,(case  when u.store_id>0  then (select org_full_name from organ org where org.id = u.store_id) when u.store_id=0 then '总部' end )as org_full_name ");
		sql.append(" from user_authority u ");
		sql.append(" join employee e on e.id = u.employee_id ");

		sql.append(" where 1=1 ");

		sql.append("  and e.store_id in ("+organIds+")   ");
		if(null != condition && condition.containsKey("organ_code") && StringUtils.isNotBlank(condition.getString("organ_code")) && !"0".equals(condition.getString("organ_code"))){
			sql.append(" and u.store_id in(select id from organ org_getid where org_getid.organ_code like'");
			sql.append(condition.get("organ_code").toString());
			sql.append("%' and org_getid.valid_state='1')");
		}
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		@SuppressWarnings("unchecked")
		Set<String> keys1 = condition.keySet();
		for(String key:keys1)
		{
			if ("page".equals(key) || "rows".equals(key) || "tableName".equals(key)||"sort".equals(key)||"order".equals(key))
			{
				continue;
			}
			if("valid_state".equals(key)&& !"".equals(condition.get(key).toString()))
			{
				sql.append("and u.valid_state ='");
				sql.append(condition.get(key).toString());
				sql.append("'");
			}
			/*if("organ_code".equals(key)&& !"".equals(condition.get(key).toString())&& !"0".equals(condition.get(key).toString()))
			{
//				sql.append(" and u.store_id in(select id from organ org_getid where org_getid.id in(select * from get_oids_bycode('");
//				sql.append(condition.get(key).toString());
//				sql.append("')) and org_getid.valid_state='1')");
				
				sql.append(" and u.store_id in(select id from organ org_getid where org_getid.organ_code like'");
				sql.append(condition.get(key).toString());
				sql.append("%' and org_getid.valid_state='1')");
			}*/
			
			if("employee_code".equals(key)&& !"".equals(condition.get(key).toString()))
			{
				
				sql.append(" and e.employee_code like '%");
				sql.append(condition.get(key).toString());
				sql.append("%' ");
			}
			if("user_name".equals(key)&& !"".equals(condition.get(key).toString()))
			{
				sql.append(" and u.user_name like '%");
				sql.append(condition.get(key).toString());
				sql.append("%' ");
			}
		}
		try
		{
			//System.out.println("u:"+sql.toString());
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));

			JSONObject result = new JSONObject();

			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
			return result;

		}
		catch (Exception e)
		{
			e.printStackTrace();
			return null;
		}
	}
	
	/**
	 * 用户登录验证
	 */
	@Override
	public JSONObject chekUserLogin(String tenentid, JSONObject longinJsonObject) throws Exception
	{
		DBContextHolder.setTenancyid(tenentid);

		//处理DAO 返回的数据进行是否可以合法登陆的判断逻辑编写
		List<JSONObject> userInfoJosonObjectList = systemUserDao.getUserInfo(tenentid,longinJsonObject);
		if(userInfoJosonObjectList!=null && userInfoJosonObjectList.size()>0){
		  JSONObject userInfoJosonObje = userInfoJosonObjectList.get(0);
			//if(userInfo.getString("id")!=null&&userInfo.getString("id").length()>0)
		   if(userInfoJosonObje!=null&&userInfoJosonObje.getString("id").length()>0)
			{
			   //查询角色的权限
			   return userInfoJosonObje;
			}	
	    }
		return null;
	}

	@Override
	public Map<Integer,Integer> getRoleAuthoruty(String tenentid, String sysuser, JSONObject condition) throws Exception
	{
		DBContextHolder.setTenancyid(tenentid);

		Map<Integer,Integer> map=new HashMap<Integer, Integer>();
		StringBuilder sql = new StringBuilder();
		sql.append("select distinct (sm.id) ");
		sql.append("from public.user_authority ua join public.user_authority_roles uar on uar.user_id = ua.id ");
		sql.append("join public.roles r on uar.roles_id = r.id ");
		sql.append("join public.role_module_ref rmr on rmr.role_id = r.id ");
		sql.append("join public.sys_modules sm on rmr.sys_module_id = sm.id ");
		sql.append("where 1=1 and sm.module_level = '5' and (sm.module_type is not null or sm.module_type!='pos') and sm.states = '");
		sql.append(Constant.BOOL_TRUE);
		sql.append("'");
		if(sysuser!=null && "0".equals(sysuser))
		{
			@SuppressWarnings("unchecked")
			Set<String> keys1 = condition.keySet();
			for(String key:keys1)
			{
				if("store_id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.store_id = ");
					sql.append(condition.get(key).toString());
				}
				if("id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.employee_id = ");
					sql.append(condition.get(key).toString());
				}
			}
		}
			
		List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
		if(list!=null && list.size()>0){
			for(JSONObject jo:list){
				map.put(jo.getInt("id"),1);
			}
		}
		return map;
	}

	@Override
	public List<JSONObject> loadFirstLevel(String tenentid, String sysuser, JSONObject condition) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		if(sysuser!=null && "1".equals(sysuser))
		{
			sql.append("select id,module_name,module_type,create_date,create_person,states,module_level,father_module_id,module_link_url,");
			sql.append("module_use_img,module_class from sys_modules where module_level in (2,3) and states='1'");
			//sql.append(" and module_type='"+condition.getString("module_type")+"' ");
			sql.append(" order by order_num,module_level, father_module_id, id");
		}
		else
		{
			sql.append("select distinct (sm.id),ua.store_id,sm.module_name,sm.module_level,sm.module_link_url,sm.father_module_id,");
			sql.append("sm.if_super_module,sm.states,sm.module_use_img,sm.module_class,sm.order_num");
			sql.append(" from user_authority ua join user_authority_roles uar on uar.user_id = ua.id ");
			sql.append(" join roles r on uar.roles_id = r.id ");
			sql.append(" join role_module_ref rmr on rmr.role_id = r.id ");
			sql.append(" join sys_modules sm on rmr.sys_module_id = sm.id ");
			sql.append(" where 1=1  and sm.module_level in (2,3) and sm.states='1'");
			//sql.append(" and sm.module_type='"+condition.getString("module_type")+"'");
			sql.append(" and (sm.module_type is null or sm.module_type!='pos') ");
			@SuppressWarnings("unchecked")
			Set<String> keys1 = condition.keySet();
			for(String key:keys1)
			{
				if("store_id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.store_id = ");
					sql.append(condition.get(key).toString());
				}
				if("id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.employee_id = ");
					sql.append(condition.get(key).toString());
				}
			}
			sql.append(" order by sm.order_num, sm.module_level, sm.father_module_id, sm.id");
		}
		List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
		if(list!=null && list.size()>0) {
			return list;
		}
		return null;
	}

	@Override
	public List<JSONObject> loadChild(String tenentid, String sysuser, JSONObject condition) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		if(sysuser!=null && "1".equals(sysuser))
		{
			sql.append("select id,module_name,module_type,create_date,create_person,states,module_level,father_module_id,module_link_url,");
			sql.append("module_use_img,module_class from sys_modules sm where 1=1 and sm.module_level in (4,5) and sm.states='1'");
			@SuppressWarnings("unchecked")
			Set<String> keys1 = condition.keySet();
			for(String key:keys1)
			{
				if("father_module_id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and ( sm.father_module_id = ");
					sql.append(condition.get(key).toString());
					sql.append(" or sm.father_module_id in (select id from sys_modules where father_module_id = ");
					sql.append(condition.get(key).toString());
					sql.append("))");
				}
			}
			sql.append(" order by order_num,module_level, father_module_id, id");
		}
		else
		{
			sql.append("select distinct (sm.id),ua.id as userid,ua.store_id,sm.module_name,sm.module_level,sm.module_link_url,sm.father_module_id,sm.if_super_module,sm.states,sm.module_use_img,sm.module_class ");
			sql.append(",sm.order_num ");
			sql.append("from public.user_authority ua join public.user_authority_roles uar on uar.user_id = ua.id ");
			sql.append("join public.roles r on uar.roles_id = r.id ");
			sql.append("join public.role_module_ref rmr on rmr.role_id = r.id ");
			sql.append("join public.sys_modules sm on rmr.sys_module_id = sm.id ");
			sql.append("where 1=1 and sm.module_level in (4,5) and sm.states='1'");
			sql.append(" and (sm.module_type is null or sm.module_type!='pos')");
			@SuppressWarnings("unchecked")
			Set<String> keys1 = condition.keySet();
			for(String key:keys1)
			{
				if("store_id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.store_id = ");
					sql.append(condition.get(key).toString());
				}
				if("id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.employee_id = ");
					sql.append(condition.get(key).toString());
				}
				
				if("father_module_id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and ( sm.father_module_id = ");
					sql.append(condition.get(key).toString());
					sql.append(" or sm.father_module_id in (select id from sys_modules where father_module_id = ");
					sql.append(condition.get(key).toString());
					sql.append("))");
				}
			}
			sql.append(" order by sm.order_num,sm.module_level, sm.father_module_id, sm.id");
//			sql.append(" order by sm.module_level, sm.father_module_id, sm.id");
		}
		List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
		if(list!=null && list.size()>0){
			return list;
		}
		return null;
	}

	@Override
	public String findOrganCodes(String tenentid,String sysuser, JSONObject userInfoJosonObje) throws Exception {
		DBContextHolder.setTenancyid(tenentid);

		StringBuffer str = new StringBuffer();
		StringBuffer sql = new StringBuffer();

		/**
		 * 当前用户为管理员权限 或者organ_code=0时 返回0+所有organ_codes
		 */
		if("1".equals(sysuser)||(null != userInfoJosonObje && "0".equals(userInfoJosonObje.optString("organ_code")))) {
			sql.setLength(0);
			sql.append("select array_to_string(ARRAY(select id from organ where valid_state = '1' ORDER BY ID ASC), ',') as organ_codes ");
			List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
			if(null != list && list.size()>0){
				str.setLength(0);
				str.append("0");
				JSONObject json = list.get(0);
				if(null != json && json.containsKey("organ_codes")&&StringUtils.isNotBlank(json.getString("organ_codes"))){
					String organ_codes = json.getString("organ_codes");
					str.append(",").append(organ_codes);
				}
			}	
		}else{
			/**
			 * 普通用户并且organ_code不为0 
			 */
			sql.setLength(0);
			str.setLength(0);
			/**
			 * 该步骤用来判断用户角色表中如果有0总部的话  organ_codes逻辑按管理员走0+所有organ_codes
			 */
			sql.append("select * from user_authority_roles  where user_id = "+userInfoJosonObje.optInt("id")+" and store_id = 0 and group_sign is not null");
			List<JSONObject> query4Json = this.dao.query4Json(tenentid, sql.toString());
			if(null != query4Json && query4Json.size()>0){
				/**
				 * 不为空 说明特殊权限勾选了总部,有store_id为0的权限
				 */
				sql.setLength(0);
				sql.append("select array_to_string(ARRAY(select id from organ where valid_state = '1' ORDER BY ID ASC), ',') as organ_codes ");
				List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
				if(null != list && list.size()>0){
					str.setLength(0);
					str.append("0");
					JSONObject json = list.get(0);
					if(null != json && json.containsKey("organ_codes")&&StringUtils.isNotBlank(json.getString("organ_codes"))){
						String organ_codes = json.getString("organ_codes");
						str.append(",").append(organ_codes);
						return str.toString();
					}
				}	
			}else{
				/**
				 * 不存在store_id为0情况 该用户为门店权限 并且特殊权限没有设置总部 因此没有总部
				 */
				sql.setLength(0);
				str.setLength(0);
				/**
				 * 查询设置的特殊权限
				 */
				sql.append("select array_to_string(ARRAY(select rr.store_id from  user_authority_roles rr where rr.user_id = "+userInfoJosonObje.optInt("id")+" and rr.group_sign is not NULL ORDER BY id ASC),',') as organ_codes ");
				List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
				if(null != list && list.size()>0){
					str.setLength(0);
					JSONObject json = list.get(0);
					if(null != json && json.containsKey("organ_codes")&&StringUtils.isNotBlank(json.getString("organ_codes"))){
						String organ_codes = json.getString("organ_codes");
						str.append(organ_codes);
					}else{
						str.setLength(0);
						sql.setLength(0);
						/**
						 * 如果无特殊权限,则查询该用户普通权限对应的机构及其下属机构
						 */
						sql.append("select array_to_string(ARRAY(select id from organ where organ_code like '"+userInfoJosonObje.optString("organ_code")+"%'  and valid_state = '1' ORDER BY ID ASC), ',') as organ_codes");
						List<JSONObject> resultlist = this.dao.query4Json(tenentid, sql.toString());
						if(null != resultlist && list.size()>0){
							JSONObject resultJson = resultlist.get(0);
							if(null != resultJson && resultJson.containsKey("organ_codes") && StringUtils.isNotBlank(resultJson.getString("organ_codes"))){
								str.append(resultJson.getString("organ_codes"));
							}else{
								//兼容历史数据 权限设置为总部时 并且没有设置特殊权限
								str.setLength(0);
								sql.setLength(0);
								sql.append("select * from user_authority_roles  where user_id = "+userInfoJosonObje.optInt("id")+" and store_id = 0 ");
								List<JSONObject> query4Json2 = this.dao.query4Json(tenentid, sql.toString());
								if(null != query4Json2 && query4Json2.size()>0){
									sql.setLength(0);
									sql.append("select array_to_string(ARRAY(select id from organ where valid_state = '1' ORDER BY ID ASC), ',') as organ_codes ");
									List<JSONObject> list1 = this.dao.query4Json(tenentid, sql.toString());
									if(null != list1 && list1.size()>0){
										str.setLength(0);
										str.append("0");
										JSONObject json1 = list1.get(0);
										if(null != json1 && json1.containsKey("organ_codes")&&StringUtils.isNotBlank(json1.getString("organ_codes"))){
											String organ_codes = json1.getString("organ_codes");
											str.append(",").append(organ_codes);
											return str.toString();
										}
									}	
								}
							}
						}
					}
				}
			}
		}
		return str.toString();
	}

	@Override
	public List<JSONObject> findAllStoreIds(String tenancy_id, String eid)
			throws Exception {
		//StringBuffer sql = new StringBuffer();
		int e_id = 0;
		try
		{
			e_id = Integer.parseInt(eid);
		}
		catch (Exception e)
		{
			// TODO: handle exception
		}
//		if(StringUtils.isNotBlank(eid) ){
//			if( !"0".equals(eid)){
//				sql.setLength(0);
//				sql.append("   SELECT    ");
//				sql.append("   	u.user_id,    ");
//				sql.append("   	u.store_id,    ");
//				sql.append("   	o.organ_code,    ");
//				sql.append("   	o.org_full_name,    ");
//				sql.append("   	o.valid_state,    ");
//				sql.append("   	u.group_sign    ");
//				sql.append("   FROM    ");
//				sql.append("   	user_authority_roles u    ");
//				sql.append("   INNER JOIN organ o ON u.store_id = o. ID    ");
//				sql.append("   where u.user_id = '"+eid+"'    ");
//			}else{
//				sql.setLength(0);
//				sql.append("   SELECT    ");
//				sql.append("   	'0' AS user_id,    ");
//				sql.append("   	ID AS store_id,    ");
//				sql.append("   	organ_code,    ");
//				sql.append("   	org_full_name,    ");
//				sql.append("   	valid_state,    ");
//				sql.append("   	'' AS group_sign    ");
//				sql.append("   FROM    ");
//				sql.append("   	organ    ");
//				sql.append("   WHERE    ");
//				sql.append("   	valid_state = '1'    ");
//			}
//		}else{
//			return null;
//		}
//		List<JSONObject> query4Json = this.dao.query4Json(tenancy_id, sql.toString());
//		return query4Json;
		return this.dao.query4Json(tenancy_id,"select oo.id,oo.organ_code,oo.org_full_name,concat(oo.organ_code,oo.org_full_name) as text from organ oo where oo.id in(select uar.store_id FROM user_authority_roles uar where uar.user_id="+e_id+") and oo.org_type='3'");
	}

	@Override
	public String findOrganCodesWithInvalid(String tenentid, String sysuser,
			JSONObject userInfoJosonObje) throws Exception {

		DBContextHolder.setTenancyid(tenentid);

		StringBuffer str = new StringBuffer();
		StringBuffer sql = new StringBuffer();
		/**
		 * 当前用户为管理员权限 或者organ_code=0时 返回0+所有organ_codes
		 */
		if("1".equals(sysuser)||(null != userInfoJosonObje && "0".equals(userInfoJosonObje.optString("organ_code")))){
		
			sql.setLength(0);
			sql.append("select array_to_string(ARRAY(select id from organ  ORDER BY ID ASC), ',') as organ_codes ");
			List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
			if(null != list && list.size()>0){
				str.setLength(0);
				str.append("0");
				JSONObject json = list.get(0);
				if(null != json && json.containsKey("organ_codes")&&StringUtils.isNotBlank(json.getString("organ_codes"))){
					String organ_codes = json.getString("organ_codes");
					str.append(",").append(organ_codes);
				}
			}	
		}else{
			/**
			 * 普通用户并且organ_code不为0 
			 */
			sql.setLength(0);
			str.setLength(0);
			/**
			 * 该步骤用来判断用户角色表中如果有0总部的话  organ_codes逻辑按管理员走0+所有organ_codes
			 */
			sql.append("select * from user_authority_roles  where user_id = "+userInfoJosonObje.optInt("id")+" and store_id = 0 and group_sign is not null");
			List<JSONObject> query4Json = this.dao.query4Json(tenentid, sql.toString());
			if(null != query4Json && query4Json.size()>0){
				/**
				 * 不为空 说明特殊权限勾选了总部,有store_id为0的权限
				 */
				sql.setLength(0);
				sql.append("select array_to_string(ARRAY(select id from organ  ORDER BY ID ASC), ',') as organ_codes ");
				List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
				if(null != list && list.size()>0){
					str.setLength(0);
					str.append("0");
					JSONObject json = list.get(0);
					if(null != json && json.containsKey("organ_codes")&&StringUtils.isNotBlank(json.getString("organ_codes"))){
						String organ_codes = json.getString("organ_codes");
						str.append(",").append(organ_codes);
						return str.toString();
					}
				}	
			}else{
				/**
				 * 不存在store_id为0情况 该用户为门店权限 并且特殊权限没有设置总部 因此没有总部
				 */
				sql.setLength(0);
				str.setLength(0);
				/**
				 * 查询设置的特殊权限
				 */
				sql.append("select array_to_string(ARRAY(select rr.store_id from  user_authority_roles rr where rr.user_id = "+userInfoJosonObje.optInt("id")+" and rr.group_sign is not NULL ORDER BY id ASC),',') as organ_codes ");
				List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
				if(null != list && list.size()>0){
					str.setLength(0);
					JSONObject json = list.get(0);
					if(null != json && json.containsKey("organ_codes")&&StringUtils.isNotBlank(json.getString("organ_codes"))){
						String organ_codes = json.getString("organ_codes");
						str.append(organ_codes);
					}else{
						str.setLength(0);
						sql.setLength(0);
						/**
						 * 如果无特殊权限,则查询该用户普通权限对应的机构及其下属机构
						 */
						sql.append("select array_to_string(ARRAY(select id from organ where organ_code like '"+userInfoJosonObje.optString("organ_code")+"%'   ORDER BY ID ASC), ',') as organ_codes");
						List<JSONObject> resultlist = this.dao.query4Json(tenentid, sql.toString());
						if(null != resultlist && list.size()>0){
							JSONObject resultJson = resultlist.get(0);
							if(null != resultJson && resultJson.containsKey("organ_codes") && StringUtils.isNotBlank(resultJson.getString("organ_codes"))){
								str.append(resultJson.getString("organ_codes"));
							}else{
								//兼容历史数据 权限设置为总部时 并且没有设置特殊权限
								str.setLength(0);
								sql.setLength(0);
								sql.append("select * from user_authority_roles  where user_id = "+userInfoJosonObje.optInt("id")+" and store_id = 0 ");
								List<JSONObject> query4Json2 = this.dao.query4Json(tenentid, sql.toString());
								if(null != query4Json2 && query4Json2.size()>0){
									sql.setLength(0);
									sql.append("select array_to_string(ARRAY(select id from organ where 1=1 ORDER BY ID ASC), ',') as organ_codes ");
									List<JSONObject> list1 = this.dao.query4Json(tenentid, sql.toString());
									if(null != list1 && list1.size()>0){
										str.setLength(0);
										str.append("0");
										JSONObject json1 = list1.get(0);
										if(null != json1 && json1.containsKey("organ_codes")&&StringUtils.isNotBlank(json1.getString("organ_codes"))){
											String organ_codes = json1.getString("organ_codes");
											str.append(",").append(organ_codes);
											return str.toString();
										}
									}	
								}
							}
						}
					}
				}
			}
		}
		return str.toString();
	}

	@Override
	public Object getRoleAuthorutyModule(String tenentid, String sysuser, JSONObject condition) throws Exception
	{
		DBContextHolder.setTenancyid(tenentid);

		Map<String, Integer> map=new HashMap<String, Integer>();
		StringBuilder sql = new StringBuilder();
		sql.append("select distinct (sm.module_link_url) as url");
		sql.append(" from user_authority ua join public.user_authority_roles uar on uar.user_id = ua.id");
		sql.append(" join roles r on uar.roles_id = r.id");
		sql.append(" join role_module_ref rmr on rmr.role_id = r.id");
		sql.append(" join sys_modules sm on rmr.sys_module_id = sm.id");
		sql.append(" where 1=1 and sm.module_level = '5' and (sm.module_type!='pos') and sm.states = '");
		sql.append(Constant.BOOL_TRUE);
		sql.append("'");
		if(sysuser!=null && "0".equals(sysuser))
		{
			@SuppressWarnings("unchecked")
			Set<String> keys1 = condition.keySet();
			for(String key:keys1)
			{
				if("store_id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.store_id = ");
					sql.append(condition.get(key).toString());
				}
				if("id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.employee_id = ");
					sql.append(condition.get(key).toString());
				}
			}
		}
		sql.append(" or(module_link_url in ('/pages/storebackground/inventorymanagement/costdaily/demandgoods.jsp','/pages/storebackground/inventorymanagement/costdaily/deliveryreceipt.jsp','/pages/storebackground/inventorymanagement/basicsettings/orderdcin.jsp'))");
			
		List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
		if(list!=null && list.size()>0){
			for(JSONObject jo:list){
				if(jo.optString("url")!=null && !"".equals(jo.optString("url")) && !"null".equals(jo.optString("url")))
				{
					map.put(jo.getString("url"), 1);
				}
			}
		}
		return map;
	}
	
	@Override
	public List<JSONObject> loadReportButton(String tenentid,String sysuser, JSONObject condition) throws Exception
	{
		
		StringBuilder sql = new StringBuilder();
		
//		if(sysuser!=null && "1".equals(sysuser))
//		{
//			sql.append("select id,module_name,module_type,create_date,create_person,states,module_level,father_module_id,module_link_url,module_use_img,module_class from sys_modules where  module_level in (9) and states='" + Constant.BOOL_TRUE + "' and (module_type is null or module_type!='pos')");
//			sql.append(" order by order_num,module_level, father_module_id, id");
////			sql.append(" order by module_level, father_module_id, id");
//		}
//		else
//		{
			//ua.id,
			sql.append("select distinct (sm.id),ua.store_id,sm.module_name,sm.module_level,sm.module_link_url,sm.father_module_id,sm.if_super_module,sm.states,sm.module_use_img,sm.module_class ");
			sql.append(",sm.order_num ");
			sql.append("from user_authority ua join user_authority_roles uar on uar.user_id = ua.id ");
			sql.append("join roles r on uar.roles_id = r.id ");
			sql.append("join role_module_ref rmr on rmr.role_id = r.id ");
			sql.append("join sys_modules sm on rmr.sys_module_id = sm.id ");
			sql.append("where 1=1  and sm.module_level in (9) and sm.states='");
			sql.append(Constant.BOOL_TRUE);
			sql.append("'");
			sql.append(" and (sm.module_type is null or sm.module_type!='pos')");
			@SuppressWarnings("unchecked")
			Set<String> keys1 = condition.keySet();
			for(String key:keys1)
			{
				if("store_id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.store_id = ");
					sql.append(condition.get(key).toString());
				}
				if("id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  ua.id = ");
					sql.append(condition.get(key).toString());
				}
				if("father_module_id".equals(key)&& !"".equals(condition.get(key).toString()))
				{
					sql.append(" and  sm.father_module_id = ");
					sql.append(condition.get(key).toString());
				}
			}
			sql.append(" order by sm.order_num, sm.module_level, sm.father_module_id, sm.id");
//			sql.append(" order by sm.module_level, sm.father_module_id, sm.id");
//		}
		List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
		if(list!=null && list.size()>0){
			return list;
		}
		return null;
	}
	
	
	@Override
	public List<JSONObject> loadReportButtonNew(String tenentid,String sysuser, JSONObject condition) throws Exception
	{
		
		StringBuilder sql = new StringBuilder();
		
//		if(sysuser!=null && "1".equals(sysuser))
//		{
//			sql.append("select id,module_name,module_type,create_date,create_person,states,module_level,father_module_id,module_link_url,module_use_img,module_class from sys_modules where  module_level in (9) and states='" + Constant.BOOL_TRUE + "' and (module_type is null or module_type!='pos')");
//			sql.append(" order by order_num,module_level, father_module_id, id");
////			sql.append(" order by module_level, father_module_id, id");
//		}
//		else
//		{
		//ua.id,
		sql.append("select distinct (sm.id),ua.store_id,sm.module_name,sm.module_level,sm.module_link_url,sm.father_module_id,sm.if_super_module,sm.states,sm.module_use_img,sm.module_class ");
		sql.append(",sm.order_num ");
		sql.append("from user_authority ua join user_authority_roles uar on uar.user_id = ua.id ");
		sql.append("join roles r on uar.roles_id = r.id ");
		sql.append("join role_module_ref rmr on rmr.role_id = r.id ");
		sql.append("join sys_modules sm on rmr.sys_module_id = sm.id ");
		sql.append("where 1=1  and sm.module_level in (5) and module_class='按钮' and sm.states='");//新版是5
		sql.append(Constant.BOOL_TRUE);
		sql.append("'");
		sql.append(" and (sm.module_type is null or sm.module_type!='pos')");
		@SuppressWarnings("unchecked")
		Set<String> keys1 = condition.keySet();
		for(String key:keys1)
		{
			if("store_id".equals(key)&& !"".equals(condition.get(key).toString()))
			{
				sql.append(" and  ua.store_id = ");
				sql.append(condition.get(key).toString());
			}
			if("id".equals(key)&& !"".equals(condition.get(key).toString()))
			{
				//新版是ua.employee_id，旧版是ua.id，因为旧版里，session里key为employee_id的值存的是user_authority表的id，而新版存的就是employee_id
				sql.append(" and  ua.employee_id = ");
				sql.append(condition.get(key).toString());
			}
			if("father_module_id".equals(key)&& !"".equals(condition.get(key).toString()))
			{
				sql.append(" and  sm.father_module_id = ");
				sql.append(condition.get(key).toString());
			}
		}
		sql.append(" order by sm.order_num, sm.module_level, sm.father_module_id, sm.id");
//			sql.append(" order by sm.module_level, sm.father_module_id, sm.id");
//		}
		List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
		if(list!=null && list.size()>0){
			return list;
		}
		return null;
	}

	@Override
	public int checkIsMultiBrand(String tenancyId) throws Exception {
		String sql = "select para_value  from sys_parameter where para_code = 'IS_MULTIBRAND' limit 1";
		List<JSONObject> list = this.dao.query4Json(tenancyId, sql);
		int flag = 0;//默认为0
		flag = list.get(0).optInt("para_value",0);
		return flag;
	}
	
	public static void main(String[] args) {
		StringBuffer sql = new StringBuffer();
		/*JSONObject condition = JSONObject.fromObject("{}");
		condition.put("version", "2.0");
		condition.put("module_type", "SCM");
		sql.append("select distinct (sm.id),ua.store_id,sm.module_name,sm.module_level,sm.module_link_url,sm.father_module_id,sm.if_super_module,sm.states,sm.module_use_img,sm.module_class ");
		sql.append(",sm.order_num ");
		sql.append("from user_authority ua join user_authority_roles uar on uar.user_id = ua.id ");
		sql.append("join roles r on uar.roles_id = r.id ");
		sql.append("join role_module_ref rmr on rmr.role_id = r.id ");
		sql.append("join sys_modules sm on rmr.sys_module_id = sm.id ");
		sql.append("where 1=1  and sm.module_level in (1,2) and sm.states='");
		sql.append(Constant.BOOL_TRUE);
		sql.append("' and module_type='"+condition.getString("module_type")+"' ");
		sql.append(" and (sm.module_type is null or sm.module_type!='pos') and sm.version=(case when '"+condition.getString("version")+"'!='' then '"+condition.getString("version")+"' else '1.0' end)");
		*/
		JSONObject p = JSONObject.fromObject("{}");
		p.put("tenancy_id", "hdl");
		p.put("user_name", "admin");
		p.put("version", "2.0");
		p.put("module_type", "SCM");
		sql.append("SELECT sm.id AS modulId,sm.module_name AS modulName,sm.module_type AS modulType,"
				+ "sm.states AS states,sm.module_level AS modulLevel,sm.father_module_id AS fatherModulId,"
				//+ "( case when sm.module_level!=5 then (CASE WHEN sm.version='2.0' THEN sm.new_url ELSE sm.module_link_url END)else  sm.button_sign end ) AS modulIdLinkUrl,"
				+ "( case when sm.module_level!=5 then sm.module_link_url else sm.button_sign end ) AS modulIdLinkUrl,"
				+ "sm.module_link_url AS oldLinkUrl,sm.module_use_img AS moduleUseImg,sm.order_num AS orderNum "
				+ "FROM sys_modules sm LEFT JOIN role_module_ref rmr ON rmr.sys_module_id=sm.id LEFT JOIN user_authority ua ON ua.roles_id=rmr.role_id "
				+ "WHERE sm.states='1' ");
		sql.append(" and ua.tenancy_id ='"+p.optString("tenancy_id")+ "' ");
		sql.append(" and ua.user_name ='"+p.optString("user_name")+ "' ");
		//sql.append(" and rmr.version=(case when '"+p.optString("version")+"'!='' then '"+p.optString("version")+"' else '1.0' end) and sm.version=(case when '"+p.optString("version")+"'!='' then '"+p.optString("version")+"' else '1.0' end) ");
		sql.append(" and sm.module_type ='"+p.optString("module_type")+ "' ");
		sql.append(" order by sm.module_level,sm.order_num");
		System.out.println(sql.toString());
	}

	@Override
	public List<JSONObject> queryUserAuthModulesInfo(JSONObject p) throws Exception {
		DBContextHolder.setTenancyid(p.optString("tenancy_id"));

		StringBuffer sql = new StringBuffer();
		sql.append("SELECT sm.id AS modulId,sm.module_name AS modulName,sm.module_type AS modulType,sm.states AS states,");
		sql.append("sm.module_level AS modulLevel,sm.father_module_id AS fatherModulId,");
		sql.append("sm.module_link_url AS modulIdLinkUrl,sm.module_use_img AS moduleUseImg,sm.order_num AS orderNum");
		sql.append(" FROM sys_modules sm LEFT JOIN role_module_ref rmr ON rmr.sys_module_id=sm.id");
		sql.append(" LEFT JOIN user_authority ua ON ua.roles_id=rmr.role_id WHERE sm.states='1' ");
		sql.append(" and ua.tenancy_id ='"+p.optString("tenancy_id")+ "' ");

		String userName = p.optString("user_name");
		if("system".equals(userName)) {
			sql.append(" and sm.module_type = '"+p.optString("module_type")+"'");
			sql.append(" group by sm.id");
		} else {
			sql.append(" and ua.user_name ='" + userName + "' ");
			sql.append(" and sm.module_type ='"+p.optString("module_type")+ "' ");
		}
		sql.append(" order by sm.module_level,sm.order_num,sm.id ");

		List<JSONObject> list = this.dao.query4Json(p.optString("tenancy_id"), sql.toString());
		if(list!=null && list.size()>0){
			return list;
		}
		return null;
	}

	@Override
	public JSONObject getEmpInfo(String tenantId, JSONObject loginJsonObject) throws Exception {
		DBContextHolder.setTenancyid(tenantId);

		List<JSONObject> userInfoJsonObjectList = systemUserDao.getEmpInfo(tenantId, loginJsonObject);
		if(userInfoJsonObjectList!=null && userInfoJsonObjectList.size()>0){
			JSONObject userInfoJson = userInfoJsonObjectList.get(0);
			if(userInfoJson!=null&&userInfoJson.getString("id").length()>0)
			{
				return userInfoJson;
			}
		}
		return null;
	}

	@Override
	public JSONObject findDefaultBrand(String tenantId, String userName) throws Exception {
		DBContextHolder.setTenancyid(tenantId);

		List<JSONObject> jsonObjectList = systemUserDao.findDefaultBrand(tenantId, userName);
		if(jsonObjectList!=null && jsonObjectList.size()>0){
			JSONObject jsonObject = jsonObjectList.get(0);
			if(jsonObject!=null&&jsonObject.getString("id").length()>0)
			{
				return jsonObject;
			}
		}
		return null;
	}

	@Override
	public List<JSONObject> findUserAuthModul(JSONObject param) throws Exception {
		DBContextHolder.setTenancyid(param.getString("tenancyId"));

		StringBuffer sql = new StringBuffer();
		sql.append("SELECT sm.id AS id, sm.module_name AS moduleName, sm.new_url AS moduleLinkUrl FROM");
		sql.append(" sys_modules sm LEFT JOIN role_module_ref rmr ON rmr.sys_module_id=sm.id");
		sql.append(" LEFT JOIN user_authority ua ON ua.roles_id=rmr.role_id WHERE");
		sql.append(" sm.states='1' AND sm.father_module_id=0 AND sm.id!=108");
		sql.append(" and ua.tenancy_id = '").append(param.getString("tenancyId")).append("'");
		sql.append(" and ua.user_name ='").append(param.getString("userName")).append("'");

		List<JSONObject> list = this.dao.query4Json(param.getString("tenancyId"), sql.toString());
		if(list != null && list.size() > 0) {
			return list;
		}

		return null;
	}
	
	
	@Override
	public List<JSONObject> queryUserModule(JSONObject p) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT sm.id AS id,sm.module_name AS moduleName,sm.module_link_url AS moduleLinkUrl FROM sys_modules sm");
		sql.append(" LEFT JOIN role_module_ref rmr ON rmr.sys_module_id=sm.id");
		sql.append(" LEFT JOIN user_authority ua ON ua.roles_id=rmr.role_id WHERE sm.states='1'");
		sql.append(" AND sm.father_module_id=0 and sm.module_link_url is not null AND sm.id!=108");
		sql.append(" and ua.tenancy_id ='"+p.optString("tenancy_id")+ "' ");
		sql.append(" and sm.module_type ='"+p.optString("module_type")+ "' ");

		String userName = p.optString("user_name");
		if("system".equals(userName)) {
			sql.append(" group by sm.id");
		} else {
			sql.append(" and ua.user_name ='" + userName + "' ");
		}

		List<JSONObject> list = this.dao.query4Json(p.optString("tenancy_id"), sql.toString());
		return list;
	}

}
