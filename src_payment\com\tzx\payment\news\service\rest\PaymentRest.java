
package com.tzx.payment.news.service.rest;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.ObjectMapper;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.UUIDUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.news.cache.PaymentConvertParam;
import com.tzx.payment.news.cont.Contant;
import com.tzx.payment.news.cont.StatusConstant;
import com.tzx.payment.news.service.PaymentService;
import com.tzx.payment.news.util.xmd.PaymentUtil;
import com.tzx.payment.news.util.xmd.XmdConstant;
import com.tzx.pos.base.controller.BaseController;

/**
 * <AUTHOR>
 *
 */
@Controller("PaymentNewRest")
@RequestMapping("/payment/news")
public class PaymentRest extends BaseController
{
	private static final Logger	logger	= Logger.getLogger(PaymentRest.class);

	@Resource(name = "com.tzx.payment.news.service.impl.JdPaymentServiceImpl")
	private PaymentService jdPaymentService;
	@Resource(name = "com.tzx.payment.news.service.impl.WechatPaymentServiceImpl")
	private PaymentService wechatPaymentService;
	@Resource(name = "com.tzx.payment.news.service.impl.DuoLaBaoPaymentServiceImpl")
	private PaymentService dlbPaymentService;
	@Resource(name="com.tzx.payment.news.service.impl.AlipayPaymentServiceImpl")
	private PaymentService aliPayMentService;
	@Resource(name="com.tzx.payment.news.service.impl.XmdPaymentServiceImpl")
	private PaymentService xmdPaymentService;
	@Resource(name="com.tzx.payment.news.service.impl.ShanhuiPaymentServiceImpl")
	private PaymentService shanhuiPayMentService;
	@Resource(name="com.tzx.payment.news.service.impl.SecondPaymentServiceImpl")
	private PaymentService secondPaymentService;
	@Resource(name="com.tzx.payment.news.service.impl.SyncreticPaymentServiceimpl")
	private PaymentService syncreticPaymentServiceimpl;
	
	

	/**
	 * 请求入口
	 * 
	 * @return JSONObject
	 * @throws Exception 
	 */
	@SuppressWarnings({ "unused", "unchecked", "rawtypes"})
	@RequestMapping(value = "post", method = RequestMethod.POST)
	// @ResponseBody
	public void post(HttpServletRequest request, HttpServletResponse response, @RequestBody
	JSONObject jsobj) throws Exception
	{
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		JSONObject responseJson = null;

        long startTime0 = System.currentTimeMillis();    //获取开始时间

		ObjectMapper objectMapper = new ObjectMapper();
		Data param = null;
		try
		{
			param = objectMapper.readValue(jsobj.toString(), Data.class);
			logger.info("请求： /payment/news/post");
			logger.info("参数： "+jsobj.toString());
		}
		catch (Exception se)
		{
			se.printStackTrace();
			logger.error("Fastjson 类型解析错误" + ExceptionMessage.getExceptionMessage(se));
			responseJson = buildErrorResult(Contant.ILLEGAL_PARAM, "json转data类型错误，请查看传入参数");
		}
        long startTime1 = System.currentTimeMillis();    //获取结束时间
        logger.info("==========================>> 第一阶段：" + (startTime1 - startTime0) + "ms");

		logger.info("接收的请求：" + jsobj);

		try
		{
			DBContextHolder.setTenancyid(param.getTenancy_id());
		}
		catch (Exception e)
		{
			logger.error("切换数据源错误：" + ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
			responseJson = buildErrorResult(Contant.CODE_CHANGE_DATASOURCE_FAILURE, "切换数据源失败");
		}

        long startTime2 = System.currentTimeMillis();    //获取结束时间
        logger.info("==========================>> 第二阶段：" + (startTime2 - startTime1) + "ms");

		Data result = null; // 以前用Data.get(param),现在换成clone
		if (param.getPagination() == null)
		{
			param.setPagination(new Pagination());
		}
		result = param.clone();
		result.setData(null);

		Data data = null;

		JSONObject json = new JSONObject();

		String message = "";


        long startTime3 = System.currentTimeMillis();    //获取结束时间
		//获取pay_type  确定走哪个分支
		List<JSONObject> list = (List<JSONObject>) param.getData();
		JSONObject jsonp = JSONObject.fromObject(list.get(0));
		Map mapJson = list.get(0);
		
		   //复合单号
        String mixOrderNo=  param.getTenancy_id()+"_"+param.getStore_id()+"_"+mapJson.get("order_no");
		logger.info("["+mixOrderNo+"]"+"支付请求数据"+jsobj.toString());
		String payType = jsonp.optString("pay_type");
		PaymentService paymentService = null;
		if(StringUtils.equals(payType, Contant.PAY_TYPE_JDPAY)) {
			paymentService = jdPaymentService;
		} else if(StringUtils.equals(payType, Contant.PAY_TYPE_WECHATPAY)) {
			paymentService = wechatPaymentService;
		} else if(StringUtils.equals(payType, Contant.PAY_TYPE_DLBPAY)) {
			paymentService = dlbPaymentService;
		} else if(StringUtils.equals(payType, Contant.PAY_TYPE_ALIPAY)) {
			paymentService = aliPayMentService;
		} else if(StringUtils.equals(payType, Contant.PAY_TYPE_SHANHUIPAY)) {
			paymentService = shanhuiPayMentService;
		} else if(StringUtils.equals(payType, Contant.PAY_TYPE_SECONDPAY)) {
			paymentService = secondPaymentService;
		} else if(StringUtils.equals(payType, Contant.PAY_TYPE_SYNCRETIC)) {
			paymentService = syncreticPaymentServiceimpl;
		} 
		if(paymentService==null) {
			logger.error("pay_type参数不符合格式");
			result.setCode(Contant.ILLEGAL_PARAM);
			result.setMsg("pay_type参数不符合格式");
		}
        long startTime4 = System.currentTimeMillis();    //获取结束时间
        logger.info("==========================>> 第三阶段：" + (startTime4 - startTime3) + "ms");
		
		
		JSONObject paymentAccountConfig = null;
		try {
			paymentAccountConfig = paymentService.getPaymentAccountConfig(param);
			if(StringUtils.equals(payType, Contant.PAY_TYPE_WECHATPAY) && 
					StringUtils.equals(paymentAccountConfig.optString("payment_channel"), String.valueOf(Contant.PAYMENT_ACCOUNT_CONFIG_TYPE_XMD))) {
				paymentService =  xmdPaymentService;
			}else if(StringUtils.equals(payType, Contant.PAY_TYPE_ALIPAY)
					&& StringUtils.equals(paymentAccountConfig.optString("payment_channel"), String.valueOf(Contant.PAYMENT_ACCOUNT_CONFIG_TYPE_XMD))){
				paymentService =  xmdPaymentService;
			}
			logger.info("当前支付渠道为："+paymentAccountConfig.optString("payment_channel")+",pay_type为:"+payType);
		} catch (Exception e1) {
			result.setCode(Contant.CODE_INNER_EXCEPTION);
			result.setMsg("没有机构所对应的 秘钥、 商户号等信息,机构ID为"+param.getStore_id());
			logger.error("没有机构所对应的 秘钥、 商户号信息,机构ID为"+param.getStore_id());
		}
		if(paymentAccountConfig==null) {
			result.setCode(Contant.CODE_INNER_EXCEPTION);
			result.setMsg("没有机构所对应的 秘钥、 商户号等信息,机构ID为"+param.getStore_id());
			logger.error("没有机构所对应的 秘钥、 商户号信息,机构ID为"+param.getStore_id());
		}

        long startTime5 = System.currentTimeMillis();    //获取结束时间
        logger.info("==========================>> 第四阶段：" + (startTime5 - startTime4) + "ms");
		
		if (paymentAccountConfig != null) {
			// 机构对应的商户号
			try {
				//从支付宝获取
				JSONObject paymentAccountConfig2 =aliPayMentService.getPaymentAccountConfig(param);
				String merchant_id = paymentAccountConfig2.optString("merchant_id");
				mapJson.put("merchant_id", merchant_id);
				paymentAccountConfig.put("type", paymentAccountConfig2.optString("type"));
				mapJson.put("paymentAccountConfig", paymentAccountConfig);
				String optString = paymentAccountConfig.optString("partner");
				mapJson.put("merchant_no", optString);
				
				
				String md5Key = paymentAccountConfig.optString("app_id");
				mapJson.put("md5Key", md5Key);
				PaymentConvertParam.loadParam2Map(payType, param.getType()
						.toString(), mapJson);
//				PaymentConvertParam.parseAccountConfig2CompleteParam(payType,paymentAccountConfig,param);
			} catch (Exception e1) {
				result.setCode(Contant.CODE_INNER_EXCEPTION);
				result.setMsg("发生未知异常");
				logger.info(e1);
			}

            long startTime6 = System.currentTimeMillis();    //获取结束时间
            logger.info("==========================>> 第五阶段：" + (startTime6 - startTime5) + "ms");

         
            
            
			switch (param.getType()) {
			case GET_PREPAY_BARCODE:
				try {
                    if(StringUtils.equals(payType, Contant.PAY_TYPE_WECHATPAY)) {
                        mapJson.put("reset_notifyUrl", true);
                    }
                    paymentService.getPrepayBarcode(param, result);
				} catch (SystemException se) {
					buildSysExceptionData(se, result, "["+mixOrderNo+"]"+"请求扫一扫内部错误");
				} catch (Exception e) {
					buildExceptionData(e, result,"["+mixOrderNo+"]"+ "请求扫一扫内部错误");
				}
				break;
			case PAY_ORDER_BY_CUSTOMER:
				try {
                    long startTime7 = System.currentTimeMillis();    //获取结束时间
					paymentService.payOrderByCustomer(param, result);
					
                    long startTime8 = System.currentTimeMillis();    //获取结束时间
                    logger.info("==========================>> 第五阶段：" + (startTime8 - startTime7) + "ms");
 
//                    randomResult(param, result);
				} catch (SystemException se) {
					buildSysExceptionData(se, result, "["+mixOrderNo+"]"+"被扫内部错误");
				} catch (Exception e) {
					buildExceptionData(e, result, "["+mixOrderNo+"]"+"被扫内部错误");
				}
				break;
			case QUERY_PAY_STATE:
				try {
					paymentService.queryPayState(param, result);

//                    randomResult(param, result);
				} catch (SystemException se) {
					buildSysExceptionData(se, result, "["+mixOrderNo+"]"+"订单查询内部错误");
				} catch (Exception e) {
					buildExceptionData(e, result, "["+mixOrderNo+"]"+"订单查询内部错误");
				}
				break;
			case REFUND_PAY_ORDER:
				try {
					paymentService.refundPayOrder(param, result);

//                    randomResult(param, result);
				} catch (SystemException se) {
					buildSysExceptionData(se, result, "["+mixOrderNo+"]"+"订单退款内部错误");
				} catch (Exception e) {
					buildExceptionData(e, result, "["+mixOrderNo+"]"+"订单退款内部错误");
				}
				break;
			case CANCEL_PAY_ORDER:
                    try {
                        paymentService.cancelPayOrder(param, result);

                        List<JSONObject> resultList = (List<JSONObject>) result.getData();
                        JSONObject resultRecord = null;
                        if(resultList != null && resultList.size() > 0) {
                            resultRecord = resultList.get(0);

                            int payment_state = resultRecord.optInt("payment_state");

                            switch (payment_state) {
                                case 7:
                                case 9:
                                    resultRecord.element("payment_state", 0);
                                    break;
                                case 4:
                                case 6:
                                    resultRecord.element("payment_state", 1);
                                    break;
                                case 5:
                                case 8:
                                    resultRecord.element("payment_state", 2);
                                    break;
                                default:
                                    break;
                            }

                        }

                    } catch (SystemException se) {
                        buildSysExceptionData(se, result,"["+mixOrderNo+"]"+ "订单取消内部错误");
                    } catch (Exception e) {
                        buildExceptionData(e, result, "["+mixOrderNo+"]"+"订单取消内部错误");
                    }
                    break;
            case CANCEL_PAY_ORDER_SUP:
                try {
                    paymentService.cancelPayOrder(param, result);
                } catch (SystemException se) {
                    buildSysExceptionData(se, result, "["+mixOrderNo+"]"+"订单取消内部错误");
                } catch (Exception e) {
                    buildExceptionData(e, result,"["+mixOrderNo+"]"+ "订单取消内部错误");
                }
                break;
			case QUERY_PAY_REFUND:
				try {
					paymentService.queryPayRefund(param, result);
				} catch (SystemException se) {
					buildSysExceptionData(se, result,"["+mixOrderNo+"]"+ "退款查询内部错误");
				} catch (Exception e) {
					buildExceptionData(e, result, "["+mixOrderNo+"]"+"退款查询内部错误");
				}
				break;
			default:
				logger.error("未找到对应的type");
				result.setCode(Contant.ILLEGAL_PARAM);
				result.setMsg("未找到对应的type");
				break;
			}
		}

		if (data != null)
		{
			result.setCode(data.getCode());
			result.setMsg(data.getMsg());
			result.setData(data.getData());
		}

		if (data != null && data.getPagination() == null)
		{
			Pagination page = result.getPagination();
			page.setPageno(1);
			List<?> lists = result.getData();
			if (lists == null)
			{
				lists = new ArrayList<Object>();
			}
			page.setPagesize(lists.size());
			page.setTotalcount(lists.size());
		}
		else if (data != null)
		{
			result.setPagination(data.getPagination());
		}

		if (result.getCode() == 0)
		{
			result.setSuccess(true);
		}
		else
		{
			result.setSuccess(false);
		}
		
		
		//为返回值没有状态 payment_state 的统一加一个 payment_state begin
		List<JSONObject> resultData = (List<JSONObject>) result.getData();
		if(resultData==null || resultData.isEmpty()) {
			resultData = new ArrayList<JSONObject>();
			JSONObject jsonR = new JSONObject();
			jsonR.put("payment_state", StatusConstant.STATUS_FAIL);
			resultData.add(jsonR);
			result.setData(resultData);
		} else {
			JSONObject jsonR = (JSONObject) resultData.get(0);
			if(StringUtils.isBlank(jsonR.optString("payment_state"))) {
				json.put("payment_state", StatusConstant.STATUS_FAIL);
			}
		}
		//为返回值没有状态 payment_state 的统一加一个 payment_state end
		
		
		responseJson = JSONObject.fromObject(result);
		logger.info("["+mixOrderNo+"]"+"支付响应："+responseJson.toString());
		
		logger.info(responseJson.toString().length() + "<==长度，POS查询结果==>" + responseJson.toString());
		try
		{
			out = response.getWriter();
			out.print(responseJson.toString());
			out.flush();
			out.close();
		}
		catch (Exception e)
		{
		}
		finally
		{
			if (out != null) out.close();
		}
	}

	public void buildSysExceptionData(SystemException se, Data result, String message)
	{
		ErrorCode error = se.getErrorCode();
		String msg = PropertiesLoader.getProperty(String.valueOf(error.getNumber()));
		Map<String, Object> map = se.getProperties();
		for (String key : map.keySet())
		{
			msg = msg.replace(key, String.valueOf(map.get(key)));
		}

		result.setCode(error.getNumber());
		result.setMsg(msg);
		logger.error(message + ",原因：" + msg + ",错误码：" + error.getNumber());
		logger.error(ExceptionMessage.getExceptionMessage(se));
	}

	public void buildExceptionData(Exception e, Data result, String message)
	{
		result.setCode(Contant.CODE_INNER_EXCEPTION);
		result.setMsg(message);
		logger.error("系统内部错误：" + ExceptionMessage.getExceptionMessage(e));
		e.printStackTrace();
	}


	public JSONObject buildErrorResult(int code, String msg)
	{
		JSONObject obj = new JSONObject();
		obj.put("code", code);
		obj.put("msg", msg);
		return obj;
	}

    private void randomResult(Data param, Data result){

        String source = param.getSource();
        List<JSONObject> data = (List<JSONObject>) result.getData();
        JSONObject fin = data.get(0);

        if(source != null && source.length() > 0) {

            switch (source){
                case "0":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("支付失败");
                    break;
                case "1":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("支付成功");
                    break;
                case "2":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("处理中");
                    break;
                case "3":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("未知");
                    break;
                case "4":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("已退款");
                    break;
                case "5":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("退款中");
                    break;
                case "6":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("已取消");
                    break;
                case "7":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("退款失败");
                    break;
                case "8":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("取消中");
                    break;
                case "9":
                    fin.element("payment_state", source);
                    result.setCode(0);
                    result.setMsg("取消失败");
                    break;
                case "10":
                    fin.element("payment_state", source);
                    result.setCode(1);
                    result.setMsg("失败");
                    break;
                default:
                    break;
            }
        }
    }
    
    
    //out_trade_no=aa6009bdc183189b9c49ff8b89a3d8a0&merchant_id=165742186
    public static void main(String[] args) {
    	JSONObject cancelJson=new JSONObject();
		cancelJson.put("outTradeNo", "d6979afd7ba00529ff24e37b9431c0e0");
		cancelJson.put("merchantId", Long.parseLong("164641002") );
		cancelJson.put("appId", 31101);
		cancelJson.put("random", UUIDUtil.generateGUID());
		JSONObject responseJson=null;
//			if(type!=null&&type.equals("1")){
//				responseJson = xmdPaymentService.orderClose(cancelJson);//取消请求
//			}else if(type!=null&&type.equals("2")){
//				responseJson = xmdPaymentService.orderClose(cancelJson);//取消请求
//			}else
		PaymentRest mm=new PaymentRest();
		
			 try {
				responseJson = mm.orderClose(cancelJson);
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			 
			 System.out.println(responseJson.toString());
	}
    
    /**
     * 手工退款
     * /payment/news/refund?out_trade_no=123&merchant_id=123454&cent=2000
     * @param request
     * @param response
     * out_trade_no来自pos_payment_order    merchant_id 来自sys_payment_account_config
     */
    @RequestMapping(value = "refund", method = RequestMethod.GET)
	// @ResponseBody
	public void refundOrder(HttpServletRequest request, HttpServletResponse response){
    	response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
    	logger.info("手工退款开始..");
    	String outTradeNo=request.getParameter("out_trade_no");
    	if(StringUtils.isEmpty(outTradeNo))
    		return;
    	
    	String merchant_id=request.getParameter("merchant_id");
    	if(StringUtils.isEmpty(merchant_id))
    		return;
    	
    	String cent=request.getParameter("cent");
    	
    	
		JSONObject cancelJson=new JSONObject();
		cancelJson.put("outTradeNo", outTradeNo);
		cancelJson.put("merchantId", Long.parseLong(merchant_id ) );
		cancelJson.put("appId", Long.parseLong(Constant.getSystemMap().get("app_id")));
		cancelJson.put("random", UUIDUtil.generateGUID());
		cancelJson.put("refundFee", Integer.parseInt(cent));
		logger.info("手工退款请求报文："+cancelJson.toString());
		JSONObject responseJson=null;
		try {
//			if(type!=null&&type.equals("1")){
//				responseJson = xmdPaymentService.orderClose(cancelJson);//取消请求
//			}else if(type!=null&&type.equals("2")){
//				responseJson = xmdPaymentService.orderClose(cancelJson);//取消请求
//			}else
			 responseJson = orderClose(cancelJson);//取消请求
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		try
		{
			out = response.getWriter();
			out.print(responseJson.toString());
			out.flush();
			out.close();
		}
		catch (Exception e)
		{
		}
		finally
		{
			if (out != null) out.close();
		}
    	
    }
    
    /**
	 * 订单关闭
	 */
	public JSONObject orderClose(JSONObject requestJson) throws IOException {
		
		
		requestJson.put("refundFee", 1500);
		String refundNo = ""+System.currentTimeMillis();
		requestJson.put("refundNo", refundNo);
		requestJson.put("refundReason",  "订单退款");
		
		String sign = PaymentUtil.generateSign(Constant.getSystemMap().get("payment_xmd_key"), requestJson);
		requestJson.put("sign", sign);
		return sendRequest(XmdConstant.URL_REFUND, requestJson);
	}
    
    /**
	 * 发起请求
	 *
	 * @param api
	 * @param params
	 * @return
	 */
	private JSONObject sendRequest(String api, JSONObject params) throws IOException {
		String url = Constant.getSystemMap().get("payment_xmd_url");
		String response = "";
		JSONObject responseJson = null;
		for (int i = 0; i < 3; i++) {
			if (response == null) {
				try {
					Thread.currentThread().sleep(200);
//					log.info("重试" + i + api);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			try {
				response = HttpUtil.sendPostRequest(url + api, params.toString());
				logger.info("手工退款第三方返回报文："+response);
				responseJson = JSONObject.fromObject(response);
				break;
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				response = null;
				responseJson = null;
			}
		}
		if (response == null) {
//			log.error("调用3次都失败了---" + api);
		}

		return responseJson;
	}
}
