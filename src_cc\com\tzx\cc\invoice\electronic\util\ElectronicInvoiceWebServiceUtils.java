package com.tzx.cc.invoice.electronic.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.net.HttpURLConnection;
import java.net.URL;

import org.apache.log4j.Logger;


/**
 * <AUTHOR>
 *
 */
public class ElectronicInvoiceWebServiceUtils {
	private static final Logger logger = Logger
			.getLogger(ElectronicInvoiceWebServiceUtils.class);
	
	/**
	 * @param xml
	 * @return
	 */
	public static String sendWebService(String xml) {
		return sendWebService(ElectronicInvoicePropertyUtil.getMsg("webservice_url"), xml);
	}
	
	/**
	 * 发送webservice
	 * @param urlstr  url
	 * @param xml	soap str
	 * @return
	 */
	public static String sendWebService(String urlstr, String xml) {
		logger.info("电子发票发送的webservice请求体为：\n");
		logger.info(xml);
		
		HttpURLConnection http = null;
		// 此方法默认会自动建立连接，即不需要http.connect()方法
		OutputStream out = null;
		try {
			URL url = new URL(urlstr);// 注意后面没有wsdl的后缀
			http = (HttpURLConnection) url.openConnection();
			http.setDoInput(true);// 设置输入
			http.setDoOutput(true);// 设置输出
			http.setRequestMethod("POST");
			http.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
			http.setRequestProperty("Accept",
					"application/soap+xml, application/dime, multipart/related, text/*");
			http.setRequestProperty("Cache-Control", "no-cache");
			http.setRequestProperty("Pragma", "no-cache");
			http.setRequestProperty("SOAPAction", "\"\"");// 注意必须要传一个空字符串
			http.setRequestProperty("Connection", "keep-alive");
			// 定义发出的SOAP
			out = http.getOutputStream();
			out.write(xml.getBytes("UTF-8"));// 发出消息
			// 以下为接收消息
			int code = http.getResponseCode();
			if (code == 200) {
				InputStream in = http.getInputStream();
				byte[] b = new byte[1024];
				int len = 0;
				StringWriter sw = new StringWriter();// 声明放数据的对象
				while ((len = in.read(b)) != -1) {
					sw.append(new String(b, 0, len, "utf-8"));
				}
				logger.info("电子发票发送的webservice相应体为：\n");
				logger.info(sw.toString());
				return sw.toString();
			} else {
				logger.info("访问连接超时");
			}
		} catch (Exception e) {
			logger.error(e);
		} finally {
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
					logger.error(e);
				} finally {
					out = null;
				}
			}
			if (http != null) {
				http.disconnect();
			}
		}
		return null;
	}
	
	/**
	 * 取消发票 soap str
	 * @param sh
	 * @param bill_no
	 * @return
	 */
	public static String CANCEL(String sh,String bill_no){
		StringBuffer xml = new StringBuffer();
		xml.append("&lt;business id=\"CANCEL_FPKJ\"&gt;");
		xml.append("&lt;XSF_NSRSBH&gt;").append(sh).append("&lt;/XSF_NSRSBH&gt;");
		xml.append("&lt;FPQQLSH&gt;").append(bill_no).append("&lt;/FPQQLSH&gt;");
		xml.append("&lt;/business&gt;");
		return getSoap(sh, xml.toString());
	}
	
	/**
	 * 获取发票查询的 soap str
	 * @param sh
	 * @param bill_no
	 * @return
	 */
	public static String FPCX(String sh,String bill_no){
		StringBuffer xml = new StringBuffer();
		xml.append("&lt;business id=\"FPCX\" comment=\"发票查询\"&gt;");
		xml.append("&lt;REQUEST_COMMON_FPCX class=\"REQUEST_COMMON_FPCX\"&gt;");
		xml.append("&lt;XSF_NSRSBH&gt;").append(sh).append("&lt;/XSF_NSRSBH&gt;");
		xml.append("&lt;FPQQLSH&gt;").append(bill_no).append("&lt;/FPQQLSH&gt;");
		xml.append("&lt;/REQUEST_COMMON_FPCX&gt; &lt;/business&gt;");
		return getSoap(sh, xml.toString());
	}
	
	
	/**
	 * 发票开具
	 * @param sh
	 * @param xml
	 * @return
	 */
	public static String FPKJ(String sh,String xml){
		return getSoap(sh, xml.toString());
	}
	/**
	 * 获取soap协议的请求体
	 * @param sh
	 * @param xmlstr
	 * @return
	 */
	private static String getSoap(String sh,String xmlstr){
		StringBuffer xml = new StringBuffer();
		xml.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:q0=\"http://impl.service.tax.inspur\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">");
		xml.append("<soapenv:Body>");
		xml.append("<q0:xmlString>");
		xml.append("<q0:xml>").append(xmlstr).append("</q0:xml>");
		xml.append("<q0:nsrsbh>").append(sh).append("</q0:nsrsbh>");
		xml.append("</q0:xmlString>");
		xml.append("</soapenv:Body>");
		xml.append("</soapenv:Envelope>");
		return xml.toString();
	}
}
