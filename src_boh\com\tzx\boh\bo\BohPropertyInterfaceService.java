package com.tzx.boh.bo;

import java.util.List;

import net.sf.json.JSONObject;

public interface BohPropertyInterfaceService
{
	  String NAME = "com.tzx.boh.bo.imp.BohPropertyInterfaceServiceImpl";
		
	   public JSONObject load(String tenancyID,JSONObject condition,String oids) throws Exception;
	   
	   public JSONObject find(String tenancyID,JSONObject condition) throws Exception;
	   
	   public JSONObject save(String tenancyID,JSONObject condition) throws Exception;
	   
	   public List<JSONObject>  getProperty(String tenancyID,JSONObject condition) throws Exception;
	   
	   public JSONObject upload(String tenancyID,JSONObject condition) throws Exception;

	   public JSONObject getUploadDetails(String tenancyID,JSONObject condition) throws Exception;
	   
	   public List<JSONObject>  getList(String tenancyID,JSONObject condition) throws Exception;
	   
	   public List<JSONObject>  getMonth(String tenancyID,JSONObject condition) throws Exception;
	   
	   public JSONObject  stop(String tenancyID,JSONObject condition) throws Exception;
	   // 查询type_id
	   public JSONObject findTypeId(String attribute, JSONObject p) throws Exception;

	   public JSONObject findOrgan(String attribute, JSONObject p) throws Exception;

	   public JSONObject findErrDate(String attribute, JSONObject p)throws Exception;

	/** 
	 * @Title: clearReportDateData 
	 * @Description: TODO(这里用一句话描述这个方法的作用) 
	 * @param @param attribute
	 * @param @param obj
	 * @param @return    设定文件 
	 * @return Object    返回类型 
	 * @throws 
	 */
	public JSONObject clearReportDateData(String tenancyID, JSONObject obj) throws Exception;

	/** 
	 * @Title: uploadSFTP 
	 * @Description: TODO(该方法用SFTP方式上传文件到指定服务器！) 
	 * @param @param tenancyID(商户号)
	 * @param @param obj(上传日期  门店号信息 )
	 * @param @return    设定文件 
	 * @return Object    返回类型 
	 * @throws 
	 */
	public JSONObject uploadSFTP(String tenancyID,String fileupload, JSONObject obj) throws Exception;
	/** 
	 * @Title: uploadSFTP 
	 * @Description: TODO(该方法用SFTP方式上传文件到指定服务器(西城都荟营业数据上传接口说明)！) 
	 * @param @param tenancyID(商户号)
	 * @param @param obj(上传日期  门店号信息 )
	 * @param @return    设定文件 
	 * @return Object    返回类型 
	 * @throws 
	 */
	public JSONObject uploadxcSFTP(String tenancyID, String fileuploadfileuploadfileuploadfileuploadfileuploadfileupload,JSONObject obj)throws Exception;

	public JSONObject uploadln(String tenancyID, JSONObject obj) throws Exception;
	
	/**
	 * 上传MIS物业接口
	 * @param tenancyID
	 * @param uploadFile
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public JSONObject uploadMISFTP(String tenancyID, String uploadFile,JSONObject obj) throws Exception;
}
