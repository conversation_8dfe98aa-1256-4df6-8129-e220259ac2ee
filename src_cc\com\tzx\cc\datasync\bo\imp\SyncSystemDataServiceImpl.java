package com.tzx.cc.datasync.bo.imp;

import com.tzx.cc.datasync.bo.SyncSystemDataService;
import com.tzx.cc.datasync.bo.dto.DataTransferDaoHelper;
import com.tzx.cc.datasync.bo.dto.PlanetVersionDataTransferDao;
import com.tzx.cc.datasync.bo.util.DriverUtils;
import com.tzx.cc.datasync.bo.util.SynIdUtils;
import com.tzx.cc.datasync.bo.util.SynUtils;
import com.tzx.cc.datasync.bo.util.TempTableUtils;
import com.tzx.cc.datasync.bo.util.serviceutil.hqLegalPerUtils;
import com.tzx.cc.datasync.bo.util.strategy.*;
import com.tzx.cc.datasync.po.springjdbc.dao.DataTransferDao;
import com.tzx.framework.bo.dto.Organ;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.MultiDatasourceContextHelper;
import com.tzx.hq.bo.SysDictionaryService;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

@Service(SyncSystemDataService.NAME)
public class SyncSystemDataServiceImpl implements SyncSystemDataService {
    private static final Logger logger = Logger.getLogger(SyncSystemDataServiceImpl.class);
    @Resource(name = DataTransferDao.NAME)
    private DataTransferDao dataTransferDao;

    @Resource(name = SysDictionaryService.NAME)
    private SysDictionaryService sysDictionaryService;

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    /* (non-Javadoc)
     * @see com.tzx.cc.datasync.bo.SyncSystemDataService#dataTransfer(net.sf.json.JSONObject)
     */
    @Override
    @Transactional
    public synchronized JSONObject dataTransfer(JSONObject serviceParams) throws Exception {
        JSONObject result_flag = new JSONObject();
        boolean flag = true;
        if (null == serviceParams) {
            String msg = "dataTransfer's serviceParams is null!";
            logger.info(msg);
            return null;
        }

        try {
            JSONObject tablesMap = new JSONObject();
            String db_driver = serviceParams.optString("db_driver");

            //设置数据源
            DriverUtils.setDriver(db_driver);
            boolean isConn = dataTransferDao.testConnect(serviceParams, result_flag);
            if (!isConn) {
                return result_flag;
            }
            Connection conn = dao.getJdbcTemplate(serviceParams.optString("tenancyid")).getDataSource().getConnection();
            TempTableUtils.setConnection(conn);
            String organstr = serviceParams.optString("organstr");
            if(StringUtils.isNotBlank(organstr)) {
                organstr = organstr.replaceAll(";","','");
            }

            if (db_driver.equals("com.ibm.db2.jcc.DB2Driver")) {

                // DB2数据库 取数方案
                tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
                String tenancyId = serviceParams.containsKey("tenancyid") ? serviceParams.getString("tenancyid") : "0";
                processAddChanel(tenancyId,serviceParams);

                List<JSONObject> rifData = null;
                List<JSONObject> saasData = null;
                Map<String, Map<String, Object>> mapIds = new HashMap<String, Map<String, Object>>();

                List<JSONObject> dic_result_list = getChanelInfo(tenancyId,serviceParams);

                SynIdUtils.insertId2Maps(tenancyId, mapIds, false, "hq_price_system", "organ", "employee", "user_authority"
                        , "tables_info", "hq_item_info", "hq_item_unit", "hq_item_menu", "hq_item_menu_organ", "hq_item_group", "hq_item_group_details");
                SynIdUtils.insertId2Maps(tenancyId, mapIds, true, "hq_item_class", "hq_item_pricesystem");
                SynIdUtils.insertId2Map(tenancyId, mapIds, "item_taste_type", "item_taste", "fake_id", "id", " and fake_type = 1");
                SynIdUtils.insertId2Map(tenancyId, mapIds, "item_taste", "item_taste", "fake_id", "id", " and fake_type = 2");


                for (Object key : tablesMap.keySet()) {
                    String sql = null;
                    List<String> listStringAdd = new ArrayList<String>();
                    try {
                        // 根据表名查询源表相应的数据
                        rifData = dataTransferDao.findPreTransferData(serviceParams, key.toString());

                        String toTableName = tablesMap.getJSONObject(key.toString()).getString(PlanetVersionDataTransferDao.TOTABLENAME);
                        // 根据表名查询目的表相应的数据
                        JSONObject compareFlag = JSONObject.fromObject("{}");
                        compareFlag.put("compareflag", "fake_id");
                        saasData = dataTransferDao.findSAASData4Compare(serviceParams, key.toString(), toTableName);
                        // 数据比较
                        Map<String, List<JSONObject>> resultMap = null;


                        if (StringUtils.equals("TZXERP.ERP_ORGSORTS", (String) key)
                                ) {
                            Context context = new Context(new CreateTempTableByQdCompareModified());
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, rifData, key.toString(), compareFlag, dic_result_list);
                        } else if (StringUtils.equals("TZXERP.ERP_MEALNOTESINFO", (String) key)) {
                            //餐谱明细
                            Context context = new Context(new MealMenuCompareModified());
                            String organsql = StringUtils.EMPTY;
                            String delsql = StringUtils.EMPTY;
                            if(StringUtils.isNotBlank(organstr)) {
                                organsql = " and sou.JGXH in ('"+organstr+"')";
                                delsql = getDelSql4Organ(serviceParams.optString("organstr"),"hq_item_menu","item_menu_id", mapIds);
                            }
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, mapIds, rifData, key.toString(), dic_result_list,organsql,delsql);
                        } else if (StringUtils.equals("TZXERP.ERP_ORGITEMINFO", (String) key)) {
                            //比较价格体系明细
                            Context context = new Context(new PriceSystemDetailsCompareModified());
                            String organsql = StringUtils.EMPTY;
                            String delsql = StringUtils.EMPTY;
                            if(StringUtils.isNotBlank(organstr)) {
                                organsql = " and sou.price_system in ('"+organstr+"')";
                                delsql = delsql = getDelSql4Organ(serviceParams.optString("organstr"),"hq_price_system","price_system", mapIds);
                            }
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, mapIds, rifData, key.toString(), dic_result_list,organsql,delsql);
                        } else if (StringUtils.equals("TZXUUS.UUS_ORGAN",(String)key)) {
                            //比较机构信息
                            Context context = new Context(new CreateTempTableCompareModified());
                            String organsql = StringUtils.EMPTY;
                            String delsql = StringUtils.EMPTY;
                            if(StringUtils.isNotBlank(organstr)) {
                                organsql = " and sou.id in ('"+organstr+"')";
                                delsql = delsql = getDelSql4Organ(serviceParams.optString("organstr"),"organ","id", mapIds);
                            }
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, rifData, key.toString(), compareFlag,organsql);
                        } else if (StringUtils.equals("TZXUUS.UUS_EMP_POST_VIEW",(String)key) ) {
                            //比较人员信息
                            Context context = new Context(new CreateTempTableCompareModified());
                            String organsql = StringUtils.EMPTY;
                            String delsql = StringUtils.EMPTY;
                            if(StringUtils.isNotBlank(organstr)) {
                                organsql = " and sou.store_id in ('"+organstr+"')";
                                delsql = delsql = getDelSql4Organ(serviceParams.optString("organstr"),"employee","store_id", mapIds);
                            }
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, rifData, key.toString(), compareFlag,organsql);
                        } else if (StringUtils.equals("TZXERP.ERP_ORGBUSINFO",(String)key) ) {
                            //比较桌位信息
                            Context context = new Context(new CreateTempTableCompareModified());
                            String organsql = StringUtils.EMPTY;
                            String delsql = StringUtils.EMPTY;
                            if(StringUtils.isNotBlank(organstr)) {
                                organsql = " and sou.organ_id in ('"+organstr+"')";
                                delsql = delsql = getDelSql4Organ(serviceParams.optString("organstr"),"tables_info","organ_id", mapIds);
                            }
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, rifData, key.toString(), compareFlag,organsql);
                        } else if (StringUtils.equals("TZXERP.TASTETYPE",(String)key) || StringUtils.equals("TZXERP.ERP_TASTE",(String)key) ) {
                            //比较口味信息
                            Context context = new Context(new DefaultCompareModified());
                            resultMap = (Map<String, List<JSONObject>>) context.compare(rifData, saasData, compareFlag);
                        } else if (StringUtils.equals("TZXERP.ERP_ORGBUSFIT",(String)key) ){
                            Context context = new Context(new DefaultCompareModified());
                            resultMap = (Map<String, List<JSONObject>>) context.compare(rifData, saasData, compareFlag);
                        	
                        }else {
                            Context context = new Context(new CreateTempTableCompareModified());
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, rifData, key.toString(), compareFlag,StringUtils.EMPTY);
                        }
                        logger.info("同步数据rif到saas商户id为" + tenancyId);
                        logger.info("update " + resultMap.get("update"));
                        logger.info("add " + resultMap.get("add"));
                        logger.info("delete " + resultMap.get("delete"));


                        Date date = new Date();
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                        String currentDateStr = format.format(date);
                        // 根据表名写入相应的数据
                        if (resultMap.isEmpty()) {
                            logger.info("与表：" + toTableName + "对应的数据没有发生变化,不需要同步！");
                            continue;
                        }

                        if (resultMap.containsKey("delete") && resultMap.get("delete").size() > 0) {
                            List<JSONObject> deleteList = resultMap.get("delete");
                            if (deleteList.size() > 0) {
                                this.deleteBatchIgnorCaseById(tenancyId, toTableName, deleteList);
                                if (StringUtils.equals(key.toString(), "TZXERP.ERP_ITEMINFO")) {
                                    this.deleteBatchIgnorCaseByRelevance(tenancyId, "hq_item_unit", deleteList, "item_id");
                                } else if (StringUtils.equals("TZXUUS.UUS_EMP_POST_VIEW", key.toString())) {
                                    this.deleteBatchIgnorCaseByRelevance(tenancyId, "user_authority", deleteList, "employee_id");
                                } else if (StringUtils.equals("TZXERP.ERP_MEALNOTESINFO", key.toString())) {
                                    //餐谱明细
                                    this.deleteBatchIgnorCaseByRelevance(tenancyId, "hq_item_menu_class", deleteList, "details_id");
                                    this.deleteOtherChanelInfo2MD01(tenancyId,toTableName);
                                    this.deleteOtherChanelInfo2MD01(tenancyId,"hq_item_menu_class");
                                } else if (StringUtils.equals("TZXERP.ERP_ORGSORTS",key.toString())) {
                                    //菜品类别
                                    this.deleteOtherChanelInfo2MD01(tenancyId,toTableName);
                                } else if (StringUtils.equals("TZXERP.ERP_ORGITEMINFO",key.toString())) {
                                    //价格体系明细
                                    this.deleteOtherChanelInfo2MD01(tenancyId,toTableName);
                                } else if(StringUtils.equals("TZXERP.ERP_ORGBUSFIT",key.toString())) {
                                    this.deleteOtherChanelInfo2MD01(tenancyId,toTableName);
                                }
                                logger.info("对saas表 " + toTableName + " 同步删除成功！");
                                logger.info("对saas表 " + toTableName + " 同步删除成功！");
                            }

                        }
                        // organ 自动生成价格体系
                        if (key.toString().equals("TZXUUS.UUS_ORGAN")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                //提前获取插入表的Id
                                Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.hq_price_system_id_seq", resultMap.get("add").size());
                                Queue<String> generateIds2 = SynIdUtils.generateIds(tenancyId, "public.organ_id_seq", resultMap.get("add").size());
                                Queue<String> generateIds3 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_id_seq", resultMap.get("add").size());
                                Queue<String> generateIds4 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_organ_id_seq", resultMap.get("add").size());
                                Map<String, Object> pricemap = mapIds.get("hq_price_system");
                                Map<String, Object> organmap = mapIds.get("organ");
                                Map<String, Object> hq_item_menu_map = mapIds.get("hq_item_menu");
                                Map<String, Object> hq_item_menu_organ_map = mapIds.get("hq_item_menu_organ");

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String price_id = generateIds.poll();
                                    pricemap.put(result_obj.optString("id"), price_id);
                                    String organ_id = generateIds2.poll();
                                    organmap.put(result_obj.optString("id"), organ_id);
                                    String hq_item_menu_map_id = generateIds3.poll();
                                    hq_item_menu_map.put(result_obj.optString("id"), hq_item_menu_map_id);
                                    String hq_item_menu_organ_id = generateIds4.poll();
                                    hq_item_menu_organ_map.put(result_obj.optString("id"), hq_item_menu_organ_id);
                                }

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sql = "insert into hq_price_system(tenancy_id,id,fake_id,price_system_name,valid_state) values('" + tenancyId + "','" + SynIdUtils.getIdBytableFakeId(mapIds, "hq_price_system", result_obj.optString("id")) + "','" + result_obj.optString("id") + "','" + result_obj.optString("org_full_name") + "价格体系" + "','1')";
                                    listStringAdd.add(sql);
                                    result_obj.put("org_uuid", tenancyId + "_" + result_obj.optString("organ_code"));
                                    sql = "insert into organ(tenancy_id,id,fake_id,organ_code,org_full_name,org_short_name,valid_state,top_org_id,level,org_type,remark,org_uuid,price_system,complain_phone,phone,address,tenant_state) values('" + tenancyId + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("id")) + "','"
                                            + result_obj.optString("id") + "','"
                                            + result_obj.optString("organ_code") + "','"
                                            + result_obj.optString("org_full_name") + "','"
                                            + result_obj.optString("org_short_name") + "','"
                                            + result_obj.optString("valid_state") + "','"
                                            + (StringUtils.equals(result_obj.optString("top_org_id"), "0") ? "0" : SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("top_org_id")))
                                            + "','1','"
                                            + result_obj.optString("org_type") + "','read_from_rif','"
                                            + tenancyId + "_"
                                            + result_obj.optString("organ_code") + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_price_system", result_obj.optString("id")) + "','"
                                            + result_obj.opt("complain_phone")
                                            + "','" + result_obj.opt("phone") + "','" + result_obj.optString("address")
                                            + "','1')";
                                    listStringAdd.add(sql);

                                    String orgType = result_obj.optString("org_type");

                                    if (StringUtils.equals(orgType, "3")) {
                                        //餐谱主表
                                        sql = "insert into hq_item_menu(tenancy_id,id,fake_id,item_menu_code,item_menu_name,startdate,starttime,remark,valid_state) values('" + tenancyId + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu", result_obj.optString("id")) + "','"
                                                + result_obj.optString("id") + "','"
                                                + result_obj.optString("organ_code") + "','"
                                                + result_obj.optString("org_full_name") + "@自动生成" + "','"
                                                + currentDateStr + "','"
                                                + "00:00','read_from_rif"
                                                + "','1')";
                                        listStringAdd.add(sql);
                                        //餐谱分配部门表
                                        sql = "insert into hq_item_menu_organ(tenancy_id,id,fake_id,item_menu_id,store_id) values('" + tenancyId + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu_organ", result_obj.optString("id")) + "','"
                                                + result_obj.optString("id") + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu", result_obj.optString("id")) + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("id"))
                                                + "')";
                                        listStringAdd.add(sql);
                                    }
                                }

                            }
                        }// 机构人员信息同步
                        else if (key.toString().equals("TZXUUS.UUS_EMP_POST_VIEW")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                //提前获取插入表的Id
                                Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.employee_id_seq", resultMap.get("add").size());
                                Queue<String> generateIds2 = SynIdUtils.generateIds(tenancyId, "public.user_authority_id_seq", resultMap.get("add").size());
                                Map<String, Object> employee_map = mapIds.get("employee");
                                Map<String, Object> user_authority_map = mapIds.get("user_authority");

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String employee_id = generateIds.poll();
                                    employee_map.put(result_obj.optString("id"), employee_id);
                                    String user_authority_id = generateIds2.poll();
                                    user_authority_map.put(result_obj.optString("id"), user_authority_id);

                                    sql = "insert into employee(tenancy_id,id,fake_id,store_id,name,states,sex,paper_no) values('" + tenancyId + "','" + employee_id + "','" + result_obj.optString("id") + "','" + SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("store_id")) + "','" + result_obj.optString("name") + "','1','" + result_obj.optString("sex") + "','')";
                                    listStringAdd.add(sql);
                                    sql = "insert into user_authority(tenancy_id,id,fake_id,employee_id,user_name,password,valid_state,store_id) values('" + tenancyId + "','" + user_authority_id + "','" + result_obj.optString("id") + "','" + employee_id + "','" + result_obj.optString("user_name") + "','e10adc3949ba59abbe56e057f20f883e','1','" + SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("store_id")) + "')";
                                    listStringAdd.add(sql);
                                }

                            }
                        }// 同步桌位信息--桌位类型 营业区域取自数据字典的初始化数据
                        else if (key.toString().equals("TZXERP.ERP_ORGBUSINFO")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                //提前获取插入表的Id
                                Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.tables_info_id_seq", resultMap.get("add").size());
                                Map<String, Object> tables_info_map = mapIds.get("tables_info");
                                StringBuffer str = new StringBuffer();
                                String table_property_id = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                	str.setLength(0);
                                	str.append("select id from sys_dictionary where 1=1 ");
                                	//根据table_property_id获取sys_dictionary餐位类型id,table_property_id即为sys_dictionary表的fake_id
                                	str.append("and fake_id ='" +result_obj.optString("table_property_id").trim()+"'");
                                	table_property_id = dataTransferDao.query4Json(tenancyId, str.toString()).get(0).optString("id");
                                	String tables_info_id = generateIds.poll();
                                    tables_info_map.put(result_obj.optString("id").trim(), tables_info_id);
                                    sql = "insert into tables_info(tenancy_id,fake_id,id,organ_id,table_code,table_property_id,business_area_id,table_name,valid_state,seat_counts,use_icon,fwfz_id,state ) values" + "('" + tenancyId + "','"
                                            + result_obj.optString("id").trim() + "','"
                                            + tables_info_id + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organ_id").trim()) + "','"
                                            + result_obj.optString("table_code").trim() + "','" + table_property_id + "','" + result_obj.optString("business_area_id").trim() + "','"
                                            + result_obj.optString("table_name").trim() + "','" + result_obj.optString("valid_state").trim() + " ','2','1','" + result_obj.optString("fwfz_id").trim() + " ','1')";
                                    listStringAdd.add(sql);
                                }
                            }
                        }// 口味类型
                        else if (key.toString().equals("TZXERP.TASTETYPE")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.item_taste_id_seq", resultMap.get("add").size());
                                Map<String, Object> item_taste_type_map = mapIds.get("item_taste_type");
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String item_taste_id = generateIds.poll();
                                    item_taste_type_map.put(result_obj.optString("id"), item_taste_id);
                                    StringBuffer sqlbuffer = new StringBuffer();
                                    sqlbuffer.append("insert into item_taste(tenancy_id,id,fake_id,fake_type,code,name,pinyin_sy,father_id,valid_state,last_updatetime)");
                                    sqlbuffer.append(" values(");
                                    sqlbuffer.append("'").append(tenancyId).append("',");
                                    sqlbuffer.append(item_taste_id).append(",");
                                    sqlbuffer.append(result_obj.optString("id")).append(",");
                                    sqlbuffer.append(1).append(",");
                                    sqlbuffer.append("'").append(result_obj.optString("code")).append("',");
                                    sqlbuffer.append("'").append(result_obj.optString("name")).append("',");
                                    sqlbuffer.append("'").append(result_obj.optString("pinyin_sy")).append("',");
                                    sqlbuffer.append("0").append(",");
                                    sqlbuffer.append("'").append("1").append("',");
                                    sqlbuffer.append("now()");
                                    sqlbuffer.append(")");
                                    sql = sqlbuffer.toString();
                                    listStringAdd.add(sql);
                                }
                            }
                        }
                        //口味明细
                        else if (key.toString().equals("TZXERP.ERP_TASTE")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.item_taste_id_seq", resultMap.get("add").size());
                                Map<String, Object> item_taste_type_map = mapIds.get("item_taste");

                                //机构对应关系
                                Map<String, Object> map = mapIds.get("organ");
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String item_taste_id = generateIds.poll();
                                    item_taste_type_map.put(result_obj.optString("id"), item_taste_id);
                                    StringBuffer sqlbuffer = new StringBuffer();
                                    sqlbuffer.append("insert into item_taste(tenancy_id,id,fake_id,fake_type,code,name,pinyin_sy,father_id,valid_state,last_updatetime)");
                                    sqlbuffer.append(" values(");
                                    sqlbuffer.append("'").append(tenancyId).append("',");
                                    sqlbuffer.append(item_taste_id).append(",");
                                    sqlbuffer.append(result_obj.optString("id")).append(",");
                                    sqlbuffer.append(2).append(",");
                                    sqlbuffer.append("'").append(result_obj.optString("code")).append("',");
                                    sqlbuffer.append("'").append(result_obj.optString("name")).append("',");
                                    sqlbuffer.append("'").append(result_obj.optString("pinyin_sy")).append("',");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "item_taste_type", result_obj.optString("father_id"))).append(",");
                                    sqlbuffer.append("'").append("1").append("',");
                                    sqlbuffer.append("now()");
                                    sqlbuffer.append(")");
                                    sql = sqlbuffer.toString();
                                    listStringAdd.add(sql);

                                    //为口味设置机构
                                    for (Entry<String, Object> entry : map.entrySet()) {
                                        sqlbuffer = new StringBuffer();
                                        Object value = entry.getValue();
                                        String organid = (String) value;
                                        sqlbuffer.append("insert into item_taste_org(tenancy_id,store_id,teste_id) values(");
                                        sqlbuffer.append("'").append(tenancyId).append("',");
                                        sqlbuffer.append(organid).append(",");
                                        sqlbuffer.append(item_taste_id);
                                        sqlbuffer.append(")");
                                        listStringAdd.add(sqlbuffer.toString());
                                    }
                                }
                            }
                        }
                        //菜品类别信息
                        else if (key.toString().equals("TZXERP.ERP_ORGSORTS")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                //提前获取插入表的Id
                                Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.hq_item_class_id_seq", resultMap.get("add").size());
                                Map<String, Object> hq_item_class_map = mapIds.get("hq_item_class");

                                pakQdMap(resultMap, generateIds, hq_item_class_map);

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sql = "insert into hq_item_class(tenancy_id,fake_id,id,father_id,chanel,itemclass_code,itemclass_name,valid_state) values('" + tenancyId + "','"
                                            + result_obj.optString("id") + "','"
                                            + SynIdUtils.getIdBytableChannelFakeId(mapIds, "hq_item_class", result_obj.optString("chanel").trim(), result_obj.optString("id")) + "','"
                                            + SynIdUtils.getIdBytableChannelFakeId(mapIds, "hq_item_class", result_obj.optString("chanel").trim(), result_obj.optString("father_id"))
                                            + "','" + result_obj.optString("chanel").trim() + "','"
                                            + result_obj.optString("itemclass_code") + "','" + result_obj.optString("itemclass_name") + "','1')";
                                    listStringAdd.add(sql);
                                }
                            }
                            // hq_item_info
                        } else if (key.toString().equals("TZXERP.ERP_ITEMINFO")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                //提前获取插入表的Id
                                Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.hq_item_info_id_seq", resultMap.get("add").size());
                                Queue<String> generateIds2 = SynIdUtils.generateIds(tenancyId, "public.hq_item_unit_id_seq", resultMap.get("add").size());
                                Map<String, Object> hq_item_info_map = mapIds.get("hq_item_info");
                                Map<String, Object> hq_item_unit_map = mapIds.get("hq_item_unit");

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_info_id = generateIds.poll();
                                    hq_item_info_map.put(result_obj.optString("id"), hq_item_info_id);
                                }

                                //总部菜品信息
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_unit_id = generateIds2.poll();
                                    hq_item_unit_map.put(result_obj.optString("id").trim(), hq_item_unit_id);

                                    sql = "insert into hq_item_info(tenancy_id,fake_id,id,item_code,item_name,item_class,valid_state,is_combo) values('" + tenancyId + "','"
                                            + result_obj.optString("id").trim() + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("id").trim()) + "','"
                                            + result_obj.optString("item_code").trim() + "','"
                                            + result_obj.optString("item_name").trim() + "','"
                                            + SynIdUtils.getIdBytableChannelFakeId(mapIds, "hq_item_class", "MD01", result_obj.optString("item_class").trim()) + "','1','"
                                            + result_obj.optString("is_combo").trim() + "')";
                                    listStringAdd.add(sql);

                                    sql = "insert into hq_item_unit(tenancy_id,fake_id,id,item_id,unit_name,standard_price,valid_state,is_default) values('" + tenancyId + "','"
                                            + result_obj.optString("id").trim() + "','"
                                            + hq_item_unit_id + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("id").trim()) + "','"
                                            + result_obj.optString("dw_name").trim()
                                            + "','" + result_obj.optString("xsdj").trim() + "','1','Y')";
                                    listStringAdd.add(sql);
                                }
                            }

                        } else if (key.toString().equals("TZXERP.ERP_ORGITEMINFO")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.hq_item_pricesystem_id_seq", resultMap.get("add").size());
                                Map<String, Object> hq_item_pricesystem_map = mapIds.get("hq_item_pricesystem");

                                pakQdMap(resultMap, generateIds, hq_item_pricesystem_map);

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sql = "insert into hq_item_pricesystem(tenancy_id,id,fake_id,item_unit_id,price_system,chanel,price) values" + "('" + tenancyId + "','"
                                            + SynIdUtils.getIdBytableChannelFakeId(mapIds, "hq_item_pricesystem", result_obj.optString("chanel").trim(), result_obj.optString("id").trim()) + "','"
                                            + result_obj.optString("id").trim() + "','"
                                            + result_obj.optString("item_unit_id_temp").trim() + "','"
                                            + result_obj.optString("price_system_temp").trim() + "','"
                                            + result_obj.optString("chanel").trim() + "','"
                                            + result_obj.optString("price").trim() + "')";
                                    listStringAdd.add(sql);
                                }
                            }
                        } else if (key.toString().equals("TZXERP.ERP_MEALNOTESINFO")) {
                            //餐谱
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_details_id_seq", resultMap.get("add").size());
                                Queue<String> generateIds2 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_class_id_seq", resultMap.get("add").size());

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String fake_id = result_obj.optString("id").trim();

                                    String hq_item_menu_details_id = generateIds1.poll();
                                    sql = "insert into hq_item_menu_details(tenancy_id,id,fake_id,item_menu_id,item_id,starttime,valid_state) values('" + tenancyId + "','"
                                            + hq_item_menu_details_id + "','"
                                            + fake_id + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu", result_obj.optString("jgxh").trim()) + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("item_id")) + "',"
                                            + "now()" + ",'1"
                                            + "')";
                                    logger.info("餐谱明细hq_item_menu_details新增sql为");
                                    logger.info(sql.toString());
                                    listStringAdd.add(sql);
                                    String hq_item_menu_class_id = generateIds2.poll();

                                    sql = "insert into hq_item_menu_class(tenancy_id,id,fake_id,details_id,chanel,class,item_name) values('" + tenancyId + "','"
                                            + hq_item_menu_class_id + "','"
                                            + fake_id + "','"
                                            + hq_item_menu_details_id + "','"
                                            + result_obj.optString("chanel").trim() + "','"
                                            + SynIdUtils.getIdBytableChannelFakeId(mapIds, "hq_item_class", result_obj.optString("chanel").trim(), result_obj.optString("class_id")) + "','"
                                            + result_obj.optString("item_name").trim()
                                            + "')";
                                    logger.info("餐谱明细hq_item_menu_class新增sql为");
                                    logger.info(sql.toString());
                                    listStringAdd.add(sql);
                                }
                            }
                        }
                        // 项目组hq_item_group
                        else if (key.toString().equals("TZXERP.ERP_ITEMGROUP")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_group_id_seq", resultMap.get("add").size());
                                Map<String, Object> hq_item_group = mapIds.get("hq_item_group");

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_group_id = generateIds1.poll();
                                    hq_item_group.put(result_obj.optString("id").trim(), hq_item_group_id);
                                }

                                for (JSONObject result_obj : resultMap.get("add")) {

                                    sql = "insert into hq_item_group(tenancy_id,fake_id,id,item_group_code,item_group_name,five_code,phonetic_code,item_group_price,valid_state) values" + "('" + tenancyId + "','"
                                            + result_obj.optString("id").trim() + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", result_obj.optString("id").trim()) + "','"
                                            + result_obj.optString("item_group_code").trim() + "','"
                                            + result_obj.optString("item_group_name").trim() + "','"
                                            + result_obj.optString("five_code").trim() + "','"
                                            + result_obj.optString("phonetic_code").trim() + "','"
                                            + result_obj.optString("item_group_price").trim() + "','"
                                            + result_obj.optString("valid_state").trim() + "')";
                                    listStringAdd.add(sql);
                                }

                            }
                        }
                        // 项目组明细hq_item_group_details
                        else if (key.toString().equals("TZXERP.ERP_ITEMGROUPLIST")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_group_details_id_seq", resultMap.get("add").size());
                                Map<String, Object> hq_item_group_details = mapIds.get("hq_item_group_details");

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_group_details_id = generateIds1.poll();
                                    hq_item_group_details.put(result_obj.optString("id").trim(), hq_item_group_details_id);
                                }
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    Double makeup_money = Double.isNaN(result_obj.optDouble("makeup_money")) ? 0.0 : result_obj.optDouble("makeup_money");
                                    Double quantity_limit = Double.isNaN(result_obj.optDouble("quantity_limit")) ? 0.0 : result_obj.optDouble("quantity_limit");
                                    sql = "insert into hq_item_group_details(tenancy_id,fake_id,id,item_group_id,item_id,item_unit_id,isdefault,makeup_money,quantity_limit) values" + "('" + tenancyId + "','"
                                            + result_obj.optString("id").trim() + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group_details", result_obj.optString("id").trim()) + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", result_obj.optString("item_group_id").trim()) + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("item_id").trim()) + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", result_obj.optString("item_id").trim()) + "','"
                                            + result_obj.optString("isdefault").trim() + "',"
                                            + makeup_money + ","
                                            + quantity_limit + ")";
                                    listStringAdd.add(sql);
                                }

                            }
                        }
                        // 菜品套餐明细hq_item_combo_details
                        else if (key.toString().equals("TZXERP.ERP_ORGSILIST")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sql = "insert into hq_item_combo_details(tenancy_id,fake_id,iitem_id,is_itemgroup,details_id,item_unit_id,combo_num,standardprice,combo_order,valid_state) values" + "('" + tenancyId + "','"
                                            + result_obj.optString("id").trim() + "','"
                                            + SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("iitem_id").trim()) + "','"
                                            + result_obj.optString("is_itemgroup").trim() + "','"
                                            +
                                            (StringUtils.equals("Y", result_obj.optString("is_itemgroup")) ?
                                                    SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", result_obj.optString("details_id").trim())
                                                    : SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("details_id").trim()))
                                            + "',"
                                            + (StringUtils.equals("Y", result_obj.optString("is_itemgroup")) ?
                                            "0"
                                            : SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", result_obj.optString("details_id").trim()))
                                            + ",'"
                                            + result_obj.optInt("combo_num") + "','"
                                            + result_obj.optString("standardprice").trim() + "','"
                                            + result_obj.optString("combo_order").trim() + "','1')";
                                    listStringAdd.add(sql);
                                }

                            }
                        }
                        // 同步付款方式
                        else if (key.toString().equals("TZXERP.ERP_PAYMENTS")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                List<String> currency_sys_dictionary_list = new ArrayList<String>();
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    result_obj.put("comment", result_obj.optString("payment_name1"));
                                    if (result_obj.optString("payment_class").equalsIgnoreCase("ERP_PAYMENTS_CASH")) {
                                        String currency_sys_dictionary_sql = "INSERT INTO  sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) VALUES ('" + tenancyId + "', 'boh', '现金管理', '币种', 'currency', '" + result_obj.optString("payment_name1") + "', '" + result_obj.optString("payment_code") + "', 'Y', '系统内部使用', '1', 'admin', '2015-10-20 03:29:16')";
                                        result_obj.put("payment_class", "cash");
                                        result_obj.put("payment_name1", result_obj.optString("payment_code"));
                                        currency_sys_dictionary_list.add(currency_sys_dictionary_sql);
                                        if (currency_sys_dictionary_list.size() == 0) {
                                            result_obj.put("is_standard_money", "1");
                                        }
                                    } else if (result_obj.optString("payment_class").equalsIgnoreCase("ERP_PAYMENTS_TICKET")) {
                                        result_obj.put("payment_class", "other");
                                        result_obj.put("payment_name1", result_obj.optString("payment_name1"));
                                    } else if (result_obj.optString("payment_class").equalsIgnoreCase("ERP_PAYMENTS_DISBILL")) {
                                        result_obj.put("payment_class", "freesingle");
                                        result_obj.put("payment_name1", result_obj.optString("payment_name1"));
                                    } else if (result_obj.optString("payment_class").equalsIgnoreCase("ERP_PAYMENTS_CREDIT")) {

                                        if (result_obj.optString("payment_code").equalsIgnoreCase("101")) {
                                            result_obj.put("payment_class", "ali_pay");
                                            result_obj.put("payment_name1", "支付宝支付");
                                        } else if (result_obj.optString("payment_code").equalsIgnoreCase("100")) {
                                            result_obj.put("payment_class", "wechat_pay");
                                            result_obj.put("payment_name1", "微信支付");
                                        } else {
                                            result_obj.put("payment_class", "other");
                                            result_obj.put("payment_name1", result_obj.optString("payment_name1"));
                                        }
                                    } else if (result_obj.optString("payment_class").equalsIgnoreCase("ERP_PAYMENTS_PUTACCOUNT")) {
                                        result_obj.put("payment_class", "incorporation");
                                        result_obj.put("payment_name1", "团体挂账");
                                    } else if (result_obj.optString("payment_class").equalsIgnoreCase("ERP_PAYMENTS_TRANSFER")) {
                                        result_obj.put("payment_class", "other");
                                        result_obj.put("payment_name1", result_obj.optString("payment_name1"));
                                    } else if (result_obj.optString("payment_class").equalsIgnoreCase("ERP_PAYMENTS_CHECK")) {
                                        result_obj.put("payment_class", "other");
                                        result_obj.put("payment_name1", result_obj.optString("payment_name1"));
                                    } else if (result_obj.optString("payment_class").equalsIgnoreCase("ERP_PAYMENTS_MEMCARD")) {
                                        result_obj.put("payment_class", "card");
                                        result_obj.put("payment_name1", "本系统卡");
                                    } else if (result_obj.optString("payment_class").equalsIgnoreCase("ERP_PAYMENTS_OTHER")) {
                                        result_obj.put("payment_class", "other");
                                        result_obj.put("payment_name1", result_obj.optString("payment_name1"));
                                    } else if (result_obj.optString("payment_class").equalsIgnoreCase("FKSX_SAS")) {
                                        result_obj.put("payment_class", "other");
                                        result_obj.put("payment_name1", result_obj.optString("payment_name1"));
                                    }

                                    sql = "insert into payment_way(tenancy_id,id,payment_code,payment_class,payment_name1,rate,if_invoicing,if_jifen,if_prepay,if_income,status,comment,is_standard_money) values" + "('" + tenancyId + "','" + result_obj.optString("id").trim() + "','" + result_obj.optString("payment_code").trim() + "','" + result_obj.optString("payment_class").trim() + "',"
                                            + "'" + result_obj.optString("payment_name1").trim() + "','" + result_obj.optString("rate").trim() + "','" + result_obj.optString("if_invoicing").trim() + "','" + result_obj.optString("if_jifen").trim() + "','" + result_obj.optString("if_prepay").trim()
                                            + "','" + result_obj.optString("if_income").trim() + "','" + result_obj.optString("status").trim() + "','" + result_obj.optString("comment").trim() + "','" + result_obj.optString("is_standard_money").trim() + "')";
                                    listStringAdd.add(sql);
                                }
                                if (currency_sys_dictionary_list.size() > 0) {
                                    try {
                                        String[] sqlArray = null;
                                        int size = currency_sys_dictionary_list.size();
                                        sqlArray = (String[]) currency_sys_dictionary_list.toArray(new String[size]);

                                        dataTransferDao.getJdbcTemplate(tenancyId).batchUpdate(sqlArray);
                                    } catch (Exception e2) {
                                        logger.error(e2);
                                        e2.printStackTrace();
                                    }
                                }
                            }
                        }
                        // 同步机构付款方式
                        else if (key.toString().equals("TZXERP.ERP_PAYMENT_ORG_REF")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sql = "insert into payment_way_of_ogran(tenancy_id,id,payment_id,organ_id) values" + "('" + tenancyId + "','" + result_obj.optString("id").trim() + "','" + result_obj.optString("payment_id").trim() + "'," + "'" + result_obj.optString("organ_id").trim() + "')";
                                    listStringAdd.add(sql);
                                }
                            }
                        }// 同步餐桌类型到sys_dictionary --废弃 取自初始化数据
                        else if (key.toString().equals("TZXERP.VST_ORGBUSFIT")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                int i = 1;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sql = "insert into sys_dictionary(tenancy_id,model_name,application_model,class_identifier,class_identifier_code,class_item,class_item_code,is_sys,valid_state) values" + "('" + tenancyId + "','" + result_obj.optString("model_name").trim() + "','"
                                            + result_obj.optString("application_model").trim() + "','" + result_obj.optString("class_identifier").trim() + "','" + result_obj.optString("class_identifier_code").trim() + "','" + result_obj.optString("class_item").trim() + "','" + (i++) + "','"
                                            + result_obj.optString("is_sys").trim() + "','" + result_obj.optString("valid_state").trim() + "')";
                                    listStringAdd.add(sql);
                                }

                            }
                        }// 同步营业区域到sys_dictionary --废弃 取自初始化数据
                        else if (key.toString().equals("TZXERP.ERP_ORGAREAS")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                int i = 1;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sql = "insert into sys_dictionary(tenancy_id,model_name,application_model,class_identifier,class_identifier_code,class_item,class_item_code,is_sys,valid_state) values" + "('" + tenancyId + "','" + result_obj.optString("model_name").trim() + "','"
                                            + result_obj.optString("application_model").trim() + "','" + result_obj.optString("class_identifier").trim() + "','" + result_obj.optString("class_identifier_code").trim() + "','" + result_obj.optString("class_item").trim() + "','" + (i++) + "','"
                                            + result_obj.optString("is_sys").trim() + "','" + result_obj.optString("valid_state").trim() + "')";
                                    listStringAdd.add(sql);
                                }

                            }
                        }// 同步服务费
                        else if (key.toString().equals("TZXERP.ERP_SERVICECHARGE")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sql = "insert into hq_service_fee_type(tenancy_id,id,service_type_code,name,fee_type,guding_jj,valid_state) values" + "('" + tenancyId + "','" + result_obj.optString("id").trim() + "','" + result_obj.optString("service_type_code").trim() + "','"
                                            + result_obj.optString("name").trim() + "','" + result_obj.optString("fee_type").trim() + "','" + result_obj.optString("guding_jj").trim() + "','1')";
                                    listStringAdd.add(sql);
                                }

                            }
                        }// 同步服务费机构关系表
                        else if (key.toString().equals("TZXERP.ERP_ORGSERVICECHARGE")) {
                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sql = "insert into hq_service_fee_of_organ(tenancy_id,id,service_fee_id,store_id) values" + "('" + tenancyId + "','" + result_obj.optString("id").trim() + "','" + result_obj.optString("service_fee_id").trim() + "','" + result_obj.optString("store_id").trim()
                                            + "')";
                                    listStringAdd.add(sql);
                                }

                            }
                        } else if(key.toString().equals("TZXERP.ERP_ORGBUSFIT")) {
                        	List <JSONObject>tableIdList = new ArrayList<JSONObject>();
                        	JSONObject jsonObj = null;
                        	//String valid_state = null;
                        	//更新餐位信息
                        	   if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                   for (JSONObject result_obj : resultMap.get("add")) {
                                	   jsonObj = new JSONObject();
                                	   /*if(result_obj.optString("cwzt").contains("Y")) {
                                		   valid_state = "1";
                                	   }else {
                                		   valid_state = "0";
                                	   }*/
                                       sql = "insert into sys_dictionary(valid_state,tenancy_id,class_item,application_model,model_name,class_identifier,class_identifier_code,is_sys,fake_id) values" + 
                                    		   	"('"+result_obj.optString("valid_state").trim()+"','"+ tenancyId + "','" + result_obj.optString("class_item").trim() + "','" + result_obj.optString("application_model").trim()+"','"
                                    		   	+ result_obj.optString("model_name").trim()+"','"
                                    		   		+ result_obj.optString("class_identifier").trim()+"','"
                                    		   		+ result_obj.optString("class_identifier_code").trim()+"','"
                                    		   		+ result_obj.optString("is_sys").trim()+"','"
                                    		   		+ result_obj.optString("fake_id").trim()+"'"
                                    		   		+")";
                                       //获取餐位对应id
                                       String id = "";
                                       jsonObj.put("table_property_id", id);
                                       listStringAdd.add(sql);
                                   }
                        	   }
                        	   //更新桌位与餐位对应关系
                               this.dataTransferDao.updateBatchIgnorCase(tenancyId,"tables_info",tableIdList);
                        }
                        if (listStringAdd.size() > 0) {
                            try {
                                String[] sqlArray = null;
                                int size = listStringAdd.size();
                                sqlArray = (String[]) listStringAdd.toArray(new String[size]);

                                dataTransferDao.getJdbcTemplate(tenancyId).batchUpdate(sqlArray);
                            } catch (Exception e2) {
                                logger.error(e2);
                                e2.printStackTrace();
                            }
                        }
                        if (resultMap.containsKey("update") && resultMap.get("update").size() > 0) {
                            try {
                                //转换数据所依赖的真实Id和伪Id的值   begin
                                List<JSONObject> list = resultMap.get("update");
                                String[] ignores = new String[]{"id"};
                                if (StringUtils.equals(key.toString(), "TZXUUS.UUS_ORGAN")) {
                                    for (JSONObject json : list) {
                                        json.put("top_org_id", SynIdUtils.getIdBytableFakeId(mapIds, "organ", json.optString("top_org_id")));
                                        json.put("hq_price_system", SynIdUtils.getIdBytableFakeId(mapIds, "hq_price_system", json.optString("id")));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXUUS.UUS_EMP_POST_VIEW")) {
                                    for (JSONObject json : list) {
                                        json.put("store_id", SynIdUtils.getIdBytableFakeId(mapIds, "organ", json.optString("store_id")));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ORGSORTS")) {
                                    ignores = new String[]{"id", "father_id", "chanel"};
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ITEMINFO")) {
                                    List<JSONObject> itemUnitList = new ArrayList<JSONObject>();
                                    JSONObject itemUnitJson = null;
                                    for (JSONObject json : list) {
                                        itemUnitJson = new JSONObject();
                                        //获取菜品规格的ID
                                        itemUnitJson.put("id",SynIdUtils.getIdBytableFakeId(mapIds,"hq_item_unit",json.optString("id")));
                                        //获取菜品规则的最新价格
                                        itemUnitJson.put("standard_price",json.optString("xsdj").trim());
                                        itemUnitList.add(itemUnitJson);

                                        //设置菜品信息的类别，fake_id向ID转换  hq_item_info
                                        json.put("item_class", SynIdUtils.getIdBytableChannelFakeId(mapIds, "hq_item_class", "MD01", json.optString("item_class").trim()));
                                    }
                                    //更新菜品规格的价格信息
                                    this.dao.updateBatchIgnorCase(tenancyId,"hq_item_unit",itemUnitList);
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ORGITEMINFO")) {
                                    for (JSONObject json : list) {
                                        json.put("id",json.optString("price_id"));
                                        json.put("item_unit_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", json.optString("item_id").trim()));
                                        json.put("price_system", SynIdUtils.getIdBytableFakeId(mapIds, "hq_price_system", json.optString("price_system").trim()));
                                    }
                                    ignores = new String[]{"id", "chanel"};
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ORGBUSINFO")) {
                                	StringBuffer str = new StringBuffer();
                                	String table_property_id = null;
                                    for (JSONObject json : list) {
                                    	//设置餐位id
                                    	str.setLength(0);
                                    	str.append("select id from sys_dictionary where 1=1 ");
                                    	//根据table_property_id获取sys_dictionary餐位类型id,table_property_id即为sys_dictionary表的fake_id
                                    	str.append("and fake_id ='" +json.optString("table_property_id").trim()+"'");
                                    	table_property_id = dataTransferDao.query4Json(tenancyId, str.toString()).get(0).optString("id");
                                    	json.put("table_property_id", table_property_id);
                                        json.put("organ_id", SynIdUtils.getIdBytableFakeId(mapIds, "organ", json.optString("organ_id").trim()));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ITEMGROUPLIST")) {
                                    ignores = new String[]{"id", "isdefault"};
                                    for (JSONObject json : list) {
                                        json.put("item_group_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", json.optString("item_group_id").trim()));
                                        json.put("item_unit_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", json.optString("item_id").trim()));
                                        json.put("item_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", json.optString("item_id").trim()));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ORGSILIST")) {
                                    for (JSONObject json : list) {
                                        String is_itemgroup = json.optString("is_itemgroup");
                                        if (StringUtils.equals("Y", is_itemgroup)) {
                                            json.put("details_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", json.optString("details_id").trim()));
                                        } else {
                                            json.put("item_unit_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", json.optString("details_id").trim()));
                                            json.put("details_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", json.optString("details_id").trim()));
                                        }
                                        json.put("iitem_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", json.optString("iitem_id").trim()));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_TASTE")) {
                                    for (JSONObject json : list) {
                                        json.put("father_id", SynIdUtils.getIdBytableFakeId(mapIds, "item_taste_type", json.optString("father_id")));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_MEALNOTESINFO")) {
                                    for (JSONObject json : list) {
                                        json.remove("cancel");
                                        json.remove("class_id");
                                        json.put("id", json.optString("menu_class_id"));
                                    }
                                }
                                //转换数据所依赖的真实Id和伪Id的值   end
                                /*if (StringUtils.equals(key.toString(), "TZXERP.ERP_ORGBUSINFO")) {
                                    //桌位信息
                                    Map<String, String> columnMap = new HashMap<String, String>();
                                    columnMap.put("fake_id", "int4");
                                    columnMap.put("organ_id", "int4");
                                    columnMap.put("table_code", "varchar(50)");
                                    columnMap.put("table_property_id", "int4");
                                    columnMap.put("business_area_id", "int4");
                                    columnMap.put("table_name", "varchar(50)");
                                    columnMap.put("fwfz_id", "int4");
                                    columnMap.put("valid_state", "varchar(10)");
                                    list = this.getRealUpdate(tenancyId, toTableName, list, columnMap);
                                } else */

                                if (StringUtils.equals(key.toString(), "TZXERP.ERP_MEALNOTESINFO")) {
                                    this.dao.updateBatchIgnorCase(tenancyId, "hq_item_menu_class", list);
                                } else if (StringUtils.equals("TZXERP.ERP_ORGITEMINFO",key.toString())){
                                    this.dao.updateBatchIgnorCase(tenancyId,toTableName,list);
                                } else if (StringUtils.equals(key.toString(), "TZXERP.TASTETYPE")) {
                                    this.dataTransferDao.updateBatchIgnorCaseByfakeId(tenancyId, " AND fake_type=1 ", toTableName, list, ignores);
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_TASTE")) {
                                    this.dataTransferDao.updateBatchIgnorCaseByfakeId(tenancyId, " AND fake_type=2 ", toTableName, list, ignores);
                                } else if(StringUtils.equals(key.toString(), "TZXERP.ERP_ORGBUSFIT")){
                                	System.out.println(list.toString());
                                    this.dataTransferDao.updateBatchIgnorCaseByfakeId(tenancyId,toTableName,list,ignores);
                                 }else {                             
                                    if (list != null && !list.isEmpty()) {
                                        this.dataTransferDao.updateBatchIgnorCaseByfakeId(tenancyId, toTableName, list, ignores);
                                    }
                                    logger.info("对saas表 " + toTableName + " 同步更新成功！");
                                }
                            } catch (Exception e2) {
                                logger.error(e2);
                                e2.printStackTrace();
                            }

                        }

                        //将菜品分组管理中同一菜品或同一分组中的第一条设置默认值为Y
                        if (key.toString().equals("TZXERP.ERP_ITEMGROUPLIST")) {
                            String correctiveSql = "select 'Y' as isdefault,min(id) as id from hq_item_group_details group by item_group_id having sum(case isdefault when 'Y' then 1 else 0 end) < 1";
                            List<JSONObject> query4Json = this.dao.query4Json(tenancyId, correctiveSql);
                            if (!query4Json.isEmpty()) {
                                dao.updateBatchIgnorCase(tenancyId, "hq_item_group_details", query4Json);
                            }
                        }
                    } catch (Exception e) {
                        flag = false;
                        logger.error(e);
                        logger.info("对表：" + key.toString() + "初始化到saas的操作失败！" + e);
                        throw new Exception("对表：" + key.toString() + "初始化到saas的操作失败！" + e);
                    }
                }
                result_flag.put("flag", flag);

            } else {
                tablesMap = DataTransferDaoHelper.INIT_TABLE;
                String tenancyId = serviceParams.containsKey("tenancyid") ? serviceParams.getString("tenancyid") : "0";

                List<JSONObject> rifData = null;
                List<JSONObject> saasData = null;
                Map<String, Map<String, Object>> mapIds = new HashMap<String, Map<String, Object>>();
                List<JSONObject> dic_result_list = getChanelInfo(tenancyId,serviceParams);

                SynIdUtils.insertId2Maps(tenancyId, mapIds, false, "hq_price_system", "organ"
                        , "hq_item_info", "hq_item_unit", "hq_item_menu", "hq_item_menu_organ", "payment_way", "payment_way_of_ogran"
                        , "hq_item_group", "hq_item_group_details","hq_legal_per","hq_unusual_reason");
//                
//                SynIdUtils.insertId2Maps(tenancyId, mapIds, false, "hq_unusual_reason");
                
                SynIdUtils.insertId2Map(tenancyId, mapIds, "hq_item_class_1", "hq_item_class", "fake_id", "id", " and fake_type = 1");
                SynIdUtils.insertId2Map(tenancyId, mapIds, "hq_item_class_2", "hq_item_class", "fake_id", "id", " and fake_type = 2");
                SynIdUtils.insertId2Map(tenancyId, mapIds, "hq_item_class_3", "hq_item_class", "fake_id", "id", " and fake_type = 3");
                fillHqItemInfoClassId(tenancyId, mapIds);

                for (Object key : tablesMap.keySet()) {
                    String sql = null;
                    List<String> listStringAdd = new ArrayList<String>();
                    try {
                        // 根据表名查询源表相应的数据
                        rifData = dataTransferDao.findPreTransferData(serviceParams, key.toString());

                        String toTableName = tablesMap.getJSONObject(key.toString()).getString(DataTransferDaoHelper.TOTABLENAME);
                        // 根据表名查询目的表相应的数据

                        JSONObject compareFlag = JSONObject.fromObject("{}");
                        compareFlag.put("compareflag", "fake_id");
                        saasData = dataTransferDao.findSAASData4Compare(serviceParams, key.toString(), toTableName);
                        Map<String, List<JSONObject>> resultMap = new HashMap<String, List<JSONObject>>();
                        if (key.toString().equals("TZXERP.ERP_ORGSORTS")
                                || key.toString().equals("TZXERP.VST_SAAS_ITEM_CLASS_LM")
                                || key.toString().equals("TZXERP.VST_SAAS_ITEM_CLASS_SM")
                                ) {
                            Context context = new Context(new DefaultCompareModified());
                            resultMap = (Map<String, List<JSONObject>>) context.compare(rifData, saasData, compareFlag);
                        } else if (key.toString().equals("TZXERP.ERP_ITEMPRICE_SYS")) {
                            Context context = new Context(new PriceSystemDetailsCompareModified());
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, mapIds, rifData, key.toString(), dic_result_list,StringUtils.EMPTY);
                        } else if (key.toString().equals("TZXERP.ERP_CPYSMX")) {
                            Context context = new Context(new MealMenuCompareModified());
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, mapIds, rifData, key.toString(), dic_result_list,StringUtils.EMPTY);
                        } else if (key.toString().equals("TZXUUS.UUS_ORGAN")) {
                            SynUtils.addNullKey(rifData,"price_system","phone","address");
                            Context context = new Context(new CreateTempTableCompareModified());
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, rifData, key.toString(), compareFlag,StringUtils.EMPTY);
                        } else {
                            Context context = new Context(new CreateTempTableCompareModified());
                            resultMap = (Map<String, List<JSONObject>>) context.compare(tenancyId, rifData, key.toString(), compareFlag,StringUtils.EMPTY);
                        }
                        logger.info("update " + resultMap.get("update"));
                        logger.info("add" + resultMap.get("add"));
                        logger.info("delete " + resultMap.get("delete"));
                        logger.info("对应rif表为" + key.toString() + "对应saas表为" + toTableName);

                        Date date = new Date();
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                        @SuppressWarnings("unused")
                        String currentDateStr = format.format(date);

                        int addsize = 0;
                        if (resultMap.get("add") != null) {
                            addsize = resultMap.get("add").size();
                        }

                        if (resultMap.containsKey("delete") && resultMap.get("delete").size() > 0) {
                            List<JSONObject> deleteList = resultMap.get("delete");
                            if (deleteList.size() > 0) {
                                this.deleteBatchIgnorCaseById(tenancyId, toTableName, deleteList);
                                if (key.toString().equals("TZXERP.ERP_CPYSMX")) {
                                    this.deleteBatchIgnorCaseByRelevance(tenancyId, "hq_item_menu_class", deleteList, "details_id");
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ITEMINFO")) {
                                    this.deleteBatchIgnorCaseByRelevance(tenancyId, "hq_item_unit", deleteList, "item_id");
                                } else if (StringUtils.equals("TZXERP.ERP_ORGCORP",key.toString())) {
                                    this.deleteBatchIgnorCaseByRelevance(tenancyId, "hq_legal_per_organ_ref", deleteList, "legal_per_id");
                                }
                                else if (StringUtils.equals("TZXERP.ERP_ORGBUSFIT",key.toString())) {
                                    this.deleteBatchIgnorCaseByRelevance(tenancyId, "sys_dictionary", deleteList, "id");
                                }
                                logger.info("对saas表 " + toTableName + " 同步删除成功！");
                            }
                        }
                        //价格体系
                        if (key.toString().equals("TZXERP.ERP_JGTXSZ")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.organ_id_seq", addsize);
                                Queue<String> generateIds2 = SynIdUtils.generateIds(tenancyId, "public.hq_price_system_id_seq", addsize);
                                Map<String, Object> organmap = mapIds.get("organ");
                                Map<String, Object> hq_price_system_map = mapIds.get("hq_price_system");
                                mapIds.put("organ", organmap);
                                mapIds.put("hq_price_system", hq_price_system_map);

                                StringBuffer sbsql = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sbsql = new StringBuffer();
                                    String hq_price_system_id = generateIds2.poll();
                                    String fake_id = result_obj.optString("id");
                                    hq_price_system_map.put(fake_id, hq_price_system_id);

                                    //hq_price_system insert sql
                                    sbsql = new StringBuffer();
                                    sbsql.append("insert into hq_price_system(tenancy_id,id,fake_id,price_system_name,valid_state) values('");
                                    sbsql.append(tenancyId).append("','");
                                    sbsql.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_price_system", result_obj.optString("id"))).append("','");
                                    sbsql.append(result_obj.optString("id")).append("','");
                                    sbsql.append(result_obj.optString("price_system_name"));
                                    sbsql.append("','1')");
                                    listStringAdd.add(sbsql.toString());
                                }
                            }
                        }
                        // organ
                        else if (key.toString().equals("TZXUUS.UUS_ORGAN")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.organ_id_seq", addsize);
                                Map<String, Object> organmap = mapIds.get("organ");
                                mapIds.put("organ", organmap);

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String organ_id = generateIds1.poll();
                                    String fake_id = result_obj.optString("id");
                                    organmap.put(fake_id, organ_id);
                                }

                                StringBuffer sbsql = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    //oragn insert sql
                                    sbsql = new StringBuffer();
                                    sbsql.append("insert into organ(tenancy_id,fake_id,id,organ_code,org_full_name,org_short_name,valid_state,top_org_id,level,org_type,remark,org_uuid,price_system,complain_phone,phone,address) values('");
                                    sbsql.append(tenancyId).append("','");
                                    sbsql.append(result_obj.optString("id")).append("','");
                                    sbsql.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("id"))).append("','");
                                    sbsql.append(result_obj.optString("organ_code")).append("','");
                                    sbsql.append(result_obj.optString("org_full_name")).append("','");
                                    sbsql.append(result_obj.optString("org_short_name")).append("','");
                                    sbsql.append(result_obj.optString("valid_state")).append("','");
                                    sbsql.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("top_org_id"))).append("','");
                                    sbsql.append(result_obj.optString("organ_level")).append("','");
                                    sbsql.append(result_obj.optString("org_type")).append("','read_from_rif','");
                                    sbsql.append(tenancyId + "_" + result_obj.optString("organ_code")).append("','");
                                    String organPriceSystemId = SynIdUtils.getIdBytableFakeId(mapIds, "hq_price_system", result_obj.optString("price_system"));
                                    sbsql.append(StringUtils.equals(organPriceSystemId, "0") ? null : organPriceSystemId).append("','");
                                    sbsql.append(result_obj.opt("complain_phone")).append("','");
                                    sbsql.append(result_obj.opt("phone")).append("','");
                                    sbsql.append(result_obj.optString("address")).append("')");
                                    logger.info("添加organ insert sql:");
                                    logger.info(sbsql.toString());
                                    listStringAdd.add(sbsql.toString());
                                }
                            }
                        }
                        // hq_item_class
                        else if (key.toString().equals("TZXERP.ERP_ORGSORTS")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_class_id_seq", addsize);
                                Map<String, Object> hq_item_class_map = mapIds.get("hq_item_class_1");
                                mapIds.put("hq_item_class_1", hq_item_class_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_class_id = generateIds1.poll();
                                    hq_item_class_map.put(result_obj.optString("id"), hq_item_class_id);
                                }
                                StringBuffer sqlbuffer = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sqlbuffer = new StringBuffer();
                                    sqlbuffer.append("insert into hq_item_class(tenancy_id,fake_id,fake_type,id,father_id,chanel,itemclass_code,itemclass_name,valid_state) values('");
                                    sqlbuffer.append(tenancyId).append("','").append(result_obj.optString("id"));
                                    sqlbuffer.append("','1','").append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_1", result_obj.optString("id")));
                                    sqlbuffer.append("','").append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_1", result_obj.optString("father_id")));
                                    sqlbuffer.append("','").append(result_obj.optString("chanel")).append("','");
                                    sqlbuffer.append(result_obj.optString("itemclass_code")).append("','");
                                    sqlbuffer.append(result_obj.optString("itemclass_name")).append("','1')");
                                    listStringAdd.add(sqlbuffer.toString());
                                }
                            }
                        }
                        // hq_item_class--第三方菜品大类
                        else if (key.toString().equals("TZXERP.VST_SAAS_ITEM_CLASS_LM")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_class_id_seq", addsize);
                                Map<String, Object> hq_item_class_map = mapIds.get("hq_item_class_2");
                                mapIds.put("hq_item_class_2", hq_item_class_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_class_id = generateIds1.poll();
                                    hq_item_class_map.put(result_obj.optString("id"), hq_item_class_id);
                                }

                                StringBuffer sqlbuffer = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sqlbuffer = new StringBuffer();
                                    sqlbuffer.append("insert into hq_item_class(tenancy_id,fake_id,fake_type,id,father_id,chanel,itemclass_code,itemclass_name,valid_state) values('");
                                    sqlbuffer.append(tenancyId).append("','").append(result_obj.optString("id")).append("','2','");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_2", result_obj.optString("id"))).append("','");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_2", result_obj.optString("father_id"))).append("','");
                                    sqlbuffer.append(result_obj.optString("chanel")).append("','");
                                    sqlbuffer.append(result_obj.optString("itemclass_code")).append("','");
                                    sqlbuffer.append(result_obj.optString("itemclass_name")).append("','1')");
                                    listStringAdd.add(sqlbuffer.toString());
                                }

                            }

                        }
                        // hq_item_class--第三方菜品小类
                        else if (key.toString().equals("TZXERP.VST_SAAS_ITEM_CLASS_SM")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_class_id_seq", addsize);
                                Map<String, Object> hq_item_class_map = mapIds.get("hq_item_class_3");
                                mapIds.put("hq_item_class_3", hq_item_class_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_class_id = generateIds1.poll();
                                    hq_item_class_map.put(result_obj.optString("id"), hq_item_class_id);
                                }

                                StringBuffer sqlbuffer = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sqlbuffer = new StringBuffer();
                                    sqlbuffer.append("insert into hq_item_class(tenancy_id,fake_id,fake_type,id,father_id,chanel,itemclass_code,itemclass_name,valid_state) values('");
                                    sqlbuffer.append(tenancyId).append("','");
                                    sqlbuffer.append(result_obj.optString("id")).append("','3','");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_3", result_obj.optString("id"))).append("','");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_2", result_obj.optString("father_id"))).append("','");
                                    sqlbuffer.append(result_obj.optString("chanel")).append("','");
                                    sqlbuffer.append(result_obj.optString("itemclass_code")).append("','");
                                    sqlbuffer.append(result_obj.optString("itemclass_name")).append("','1')");
                                    listStringAdd.add(sqlbuffer.toString());
                                }

                            }

                        }
                        // hq_item_info
                        else if (key.toString().equals("TZXERP.ERP_ITEMINFO")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_info_id_seq", addsize);
                                Queue<String> generateIds2 = SynIdUtils.generateIds(tenancyId, "public.hq_item_unit_id_seq", addsize);
                                Map<String, Object> hq_item_info_map = mapIds.get("hq_item_info");
                                Map<String, Object> hq_item_unit_map = mapIds.get("hq_item_unit");
                                mapIds.put("hq_item_info", hq_item_info_map);
                                mapIds.put("hq_item_unit", hq_item_unit_map);

                                //表中伪Id和classId的映射
                                Map<String, Object> hq_item_info_class_id = mapIds.get("hq_item_info_class_id");
                                mapIds.put("hq_item_info_class_id", hq_item_info_class_id);

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_info_id = generateIds1.poll();
                                    String hq_item_unit_id = generateIds2.poll();
                                    hq_item_info_map.put(result_obj.optString("id"), hq_item_info_id);
                                    hq_item_unit_map.put(result_obj.optString("id"), hq_item_unit_id);

                                    //为表中伪ID和classId建立映射
                                    String class_id = SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_1", result_obj.optString("item_class").trim());
                                    hq_item_info_class_id.put(result_obj.optString("id"), class_id);
                                }

                                StringBuffer sqlbuffer = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sqlbuffer = new StringBuffer();
                                    sqlbuffer.append("insert into hq_item_info(tenancy_id,fake_id,id,item_code,item_name,item_class,valid_state,is_combo) values('");
                                    sqlbuffer.append(tenancyId).append("','");
                                    sqlbuffer.append(result_obj.optString("id").trim()).append("','");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("id").trim())).append("','");
                                    sqlbuffer.append(result_obj.optString("item_code").trim()).append("','");
                                    sqlbuffer.append(result_obj.optString("item_name").trim()).append("','");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_1", result_obj.optString("item_class").trim())).append("','1','");
                                    sqlbuffer.append(result_obj.optString("is_combo").trim()).append("')");
                                    listStringAdd.add(sqlbuffer.toString());

                                    sqlbuffer = new StringBuffer();
                                    sqlbuffer.append("insert into hq_item_unit(tenancy_id,fake_id,id,item_id,unit_name,standard_price,valid_state,is_default) values('");
                                    sqlbuffer.append(tenancyId).append("','");
                                    sqlbuffer.append(result_obj.optString("id").trim()).append("','");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", result_obj.optString("id").trim())).append("','");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("id").trim())).append("','");
                                    sqlbuffer.append(result_obj.optString("dw_name").trim()).append("','");
                                    sqlbuffer.append(result_obj.optString("xsdj").trim()).append("','1','Y')");
                                    listStringAdd.add(sqlbuffer.toString());
                                }

                            }
                            // hq_item_menu
                        } else if (key.toString().equals("TZXERP.ERP_CPYSSZ")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_id_seq", addsize);
                                Map<String, Object> hq_item_menu_map = mapIds.get("hq_item_menu");
                                mapIds.put("hq_item_menu", hq_item_menu_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_menu_id = generateIds1.poll();
                                    hq_item_menu_map.put(result_obj.optString("id"), hq_item_menu_id);
                                }

                                StringBuffer sqlbuffer = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sqlbuffer = new StringBuffer();
                                    sqlbuffer.append("insert into hq_item_menu(tenancy_id,fake_id,id,item_menu_code,item_menu_name,startdate,starttime,remark,valid_state) values('");
                                    sqlbuffer.append(tenancyId).append("','");
                                    sqlbuffer.append(result_obj.optString("id").trim()).append("','");
                                    sqlbuffer.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu", result_obj.optString("id").trim())).append("','");
                                    sqlbuffer.append(result_obj.optString("item_menu_code").trim()).append("','");
                                    sqlbuffer.append(result_obj.optString("item_menu_name").trim()).append("','");
                                    sqlbuffer.append(result_obj.optString("startdate").trim()).append("','");
                                    sqlbuffer.append(result_obj.optString("starttime").trim()).append("','");
                                    sqlbuffer.append(result_obj.optString("remark").trim()).append("','1')");
                                    listStringAdd.add(sqlbuffer.toString());
                                }

                            }
                        //价格体系明细 按照菜品档案的价格将各个渠道都复制一份
                        } else if (key.toString().equals("TZXERP.ERP_ITEMPRICE_SYS")) {
                            if (addsize > 0) {
                                StringBuffer sbsql = new StringBuffer();
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sbsql = new StringBuffer();
                                    sbsql.append("insert into hq_item_pricesystem(tenancy_id,fake_id,item_unit_id,price_system,chanel,price) values('");
                                    sbsql.append(tenancyId).append("','");
                                    sbsql.append(result_obj.optString("id").trim()).append("','");
                                    sbsql.append(result_obj.optString("item_unit_id_temp")).append("','");
                                    sbsql.append(result_obj.optString("price_system_temp")).append("','");
                                    sbsql.append(result_obj.optString("chanel").trim()).append("',");
                                    sbsql.append(result_obj.optString("price").trim());
                                    sbsql.append(")");
                                    listStringAdd.add(sbsql.toString());
                                }
                            }
                        }
                        //餐谱明细
                        // hq_item_menu_details、hq_item_menu_class   餐谱信息明细
                        else if (key.toString().equals("TZXERP.ERP_CPYSMX")) {
                            //餐谱信息明细
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_details_id_seq", addsize);
                                StringBuffer sb = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sb = new StringBuffer();
                                    String hq_item_menu_details_id = generateIds1.poll();
                                    sb.append("insert into hq_item_menu_details(tenancy_id,fake_id,id,item_menu_id,item_id,starttime,endtime,valid_state,menu_item_rank) values('");
                                    sb.append(tenancyId).append("','");
                                    sb.append(result_obj.optString("id").trim()).append("','");
                                    sb.append(hq_item_menu_details_id).append("','");

                                    //item_menu_id,item_id在MealMenuCompareModified比较的时候已经取到值，不用再次浪费资源去取了   begin
//									sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu", result_obj.optString("item_menu_id").trim())).append("','");
//									sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("item_id").trim())).append("','");

                                    sb.append(result_obj.optString("item_menu_id_temp")).append("','");
                                    sb.append(result_obj.optString("item_id_temp")).append("','");
                                    //item_menu_id,item_id在MealMenuCompareModified比较的时候已经取到值，不用再次浪费资源去取了   end

                                    sb.append(result_obj.optString("starttime").trim()).append("','");
                                    sb.append(result_obj.optString("endtime").trim()).append("','1','");
                                    sb.append(result_obj.optString("cpsx").trim()).append("')");
                                    listStringAdd.add(sb.toString());

                                    sb = new StringBuffer();
                                    sb.append("insert into hq_item_menu_class(tenancy_id,fake_id,details_id,chanel,class,item_name,menu_class_rank) values('");
                                    sb.append(tenancyId).append("','");
                                    sb.append(result_obj.optString("id").trim()).append("','");
                                    sb.append(hq_item_menu_details_id).append("','");
                                    sb.append(result_obj.optString("chanel").trim()).append("','");

                                    //class_id在MealMenuCompareModified比较的时候已经取到值，不用再次浪费资源去取了  begin
//									sb.append((StringUtils.equals("MD01", result_obj.optString("chanel").trim())
//											? SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info_class_id", result_obj.optString("item_id").trim())
//											: SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_3", result_obj.optString("class_id").trim())));
                                    sb.append(result_obj.optString("class_id_temp"));
                                    //class_id在MealMenuCompareModified比较的时候已经取到值，不用再次浪费资源去取了  end

                                    sb.append("','");
                                    sb.append(result_obj.optString("item_name").trim()).append("','");
                                    sb.append(result_obj.optString("lbsx").trim()).append("')");
                                    listStringAdd.add(sb.toString());
                                }
                            }
                        }
                        // hq_item_menu_organ  餐谱部门
                        else if (key.toString().equals("TZXERP.ERP_CPYSSYJG")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_organ_id_seq", addsize);
                                Map<String, Object> hq_item_menu_organ_map = mapIds.get("hq_item_menu_organ");
                                mapIds.put("hq_item_menu_organ", hq_item_menu_organ_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_menu_organ_id = generateIds1.poll();
                                    hq_item_menu_organ_map.put(result_obj.optString("id"), hq_item_menu_organ_id);
                                }

                                StringBuffer sb = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sb = new StringBuffer();
                                    sb.append("insert into hq_item_menu_organ(tenancy_id,fake_id,id,item_menu_id,store_id) values('");
                                    sb.append(tenancyId).append("','");
                                    sb.append(result_obj.optString("id").trim()).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu_organ", result_obj.optString("id").trim())).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu", result_obj.optString("item_menu_id").trim())).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("store_id").trim())).append("')");
                                    listStringAdd.add(sb.toString());
                                }

                            }
                        }
                        // 付款方式同步 payment_way
                        else if (key.toString().equals("TZXERP.ERP_PAYMENTS")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.payment_way_id_seq", addsize);
                                Map<String, Object> payment_way_map = mapIds.get("payment_way");
                                mapIds.put("payment_way", payment_way_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String payment_way_id = generateIds1.poll();
                                    payment_way_map.put(result_obj.optString("id"), payment_way_id);
                                }

                                for (JSONObject result_obj : resultMap.get("add")) {
                                    if (result_obj.optString("fkfsbh").equalsIgnoreCase("0115")) {
                                        result_obj.put("payment_class", "ele_pay");
                                        result_obj.put("payment_name1", "饿了么付款");
                                        sql = "insert into payment_way(tenancy_id,fake_id,id,payment_class,payment_name1,rate,if_invoicing,if_jifen,if_prepay,if_income,status,payment_code) values" + "('" + tenancyId + "','"
                                                + result_obj.optString("id").trim() + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "payment_way", result_obj.optString("id").trim()) + "','"
                                                + result_obj.optString("payment_class").trim() + "'," + "'" + result_obj.optString("payment_name1").trim() + "','" + result_obj.optString("rate").trim() + "','" + result_obj.optString("if_invoicing").trim() + "','"
                                                + result_obj.optString("if_jifen").trim() + "','" + result_obj.optString("if_prepay").trim() + "','" + result_obj.optString("if_income").trim() + "','" + result_obj.optString("status").trim() + "','" + result_obj.optString("fkfsbh").trim() + "')";
                                        listStringAdd.add(sql);
                                    } else if (result_obj.optString("fkfsbh").equalsIgnoreCase("0110")) {
                                        result_obj.put("payment_class", "baidu_pay");
                                        result_obj.put("payment_name1", "百度外卖付款");
                                        sql = "insert into payment_way(tenancy_id,fake_id,id,payment_class,payment_name1,rate,if_invoicing,if_jifen,if_prepay,if_income,status,payment_code) values" + "('" + tenancyId + "','"
                                                + result_obj.optString("id").trim() + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "payment_way", result_obj.optString("id").trim()) + "','"
                                                + result_obj.optString("payment_class").trim() + "'," + "'" + result_obj.optString("payment_name1").trim() + "','" + result_obj.optString("rate").trim() + "','" + result_obj.optString("if_invoicing").trim() + "','"
                                                + result_obj.optString("if_jifen").trim() + "','" + result_obj.optString("if_prepay").trim() + "','" + result_obj.optString("if_income").trim() + "','" + result_obj.optString("status").trim() + "','" + result_obj.optString("fkfsbh").trim() + "')";
                                        listStringAdd.add(sql);
                                    } else if (result_obj.optString("fkfsbh").equalsIgnoreCase("0111")) {
                                        result_obj.put("payment_class", "meituan_pay");
                                        result_obj.put("payment_name1", "美团外卖付款");
                                        sql = "insert into payment_way(tenancy_id,fake_id,id,payment_class,payment_name1,rate,if_invoicing,if_jifen,if_prepay,if_income,status,payment_code) values" + "('" + tenancyId + "','"
                                                + result_obj.optString("id").trim() + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "payment_way", result_obj.optString("id").trim()) + "','"
                                                + result_obj.optString("payment_class").trim() + "'," + "'" + result_obj.optString("payment_name1").trim() + "','" + result_obj.optString("rate").trim() + "','" + result_obj.optString("if_invoicing").trim() + "','"
                                                + result_obj.optString("if_jifen").trim() + "','" + result_obj.optString("if_prepay").trim() + "','" + result_obj.optString("if_income").trim() + "','" + result_obj.optString("status").trim() + "','" + result_obj.optString("fkfsbh").trim() + "')";
                                        listStringAdd.add(sql);
                                    } else if (result_obj.optString("fkfsbh").equalsIgnoreCase("0112")) {
                                        result_obj.put("payment_class", "wechat_pay");
                                        result_obj.put("payment_name1", "微信支付");
                                        sql = "insert into payment_way(tenancy_id,fake_id,id,payment_class,payment_name1,rate,if_invoicing,if_jifen,if_prepay,if_income,status,payment_code) values" + "('" + tenancyId + "','"
                                                + result_obj.optString("id").trim() + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "payment_way", result_obj.optString("id").trim()) + "','"
                                                + result_obj.optString("payment_class").trim() + "'," + "'" + result_obj.optString("payment_name1").trim() + "','" + result_obj.optString("rate").trim() + "','" + result_obj.optString("if_invoicing").trim() + "','"
                                                + result_obj.optString("if_jifen").trim() + "','" + result_obj.optString("if_prepay").trim() + "','" + result_obj.optString("if_income").trim() + "','" + result_obj.optString("status").trim() + "','" + result_obj.optString("fkfsbh").trim() + "')";
                                        listStringAdd.add(sql);
                                    } else if (result_obj.optString("fkfsbh").equalsIgnoreCase("0113")) {
                                        result_obj.put("payment_class", "card");
                                        result_obj.put("payment_name1", "本系统卡");
                                        sql = "insert into payment_way(tenancy_id,fake_id,id,payment_class,payment_name1,rate,if_invoicing,if_jifen,if_prepay,if_income,status,payment_code) values" + "('" + tenancyId + "','"
                                                + result_obj.optString("id").trim() + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "payment_way", result_obj.optString("id").trim()) + "','"
                                                + result_obj.optString("payment_class").trim() + "'," + "'" + result_obj.optString("payment_name1").trim() + "','" + result_obj.optString("rate").trim() + "','" + result_obj.optString("if_invoicing").trim() + "','"
                                                + result_obj.optString("if_jifen").trim() + "','" + result_obj.optString("if_prepay").trim() + "','" + result_obj.optString("if_income").trim() + "','" + result_obj.optString("status").trim() + "','" + result_obj.optString("fkfsbh").trim() + "')";
                                        listStringAdd.add(sql);
                                    } else if (result_obj.optString("fkfsbh").equalsIgnoreCase("0114")) {
                                        result_obj.put("payment_class", "coupons");
                                        result_obj.put("payment_name1", "优惠券");
                                        sql = "insert into payment_way(tenancy_id,fake_id,id,payment_class,payment_name1,rate,if_invoicing,if_jifen,if_prepay,if_income,status,payment_code) values" + "('" + tenancyId + "','"
                                                + result_obj.optString("id").trim() + "','"
                                                + SynIdUtils.getIdBytableFakeId(mapIds, "payment_way", result_obj.optString("id").trim()) + "','"
                                                + result_obj.optString("payment_class").trim() + "'," + "'" + result_obj.optString("payment_name1").trim() + "','" + result_obj.optString("rate").trim() + "','" + result_obj.optString("if_invoicing").trim() + "','"
                                                + result_obj.optString("if_jifen").trim() + "','" + result_obj.optString("if_prepay").trim() + "','" + result_obj.optString("if_income").trim() + "','" + result_obj.optString("status").trim() + "','" + result_obj.optString("fkfsbh").trim() + "')";
                                        listStringAdd.add(sql);
                                    }

                                }

                            }
                            // 付款方式门店关系表 payment_way_of_ogran
                        } else if (key.toString().equals("TZXERP.ERP_PAYMENT_ORG_REF")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.payment_way_of_ogran_id_seq", addsize);
                                Map<String, Object> payment_way_of_ogran_map = mapIds.get("payment_way_of_ogran");
                                mapIds.put("payment_way_of_ogran", payment_way_of_ogran_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String payment_way_of_ogran_id = generateIds1.poll();
                                    payment_way_of_ogran_map.put(result_obj.optString("id"), payment_way_of_ogran_id);
                                }

                                StringBuffer sb = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sb = new StringBuffer();
                                    sb.append("insert into payment_way_of_ogran(tenancy_id,fake_id,id,payment_id,organ_id) values" + "('");
                                    sb.append(tenancyId).append("','");
                                    sb.append(result_obj.optString("id").trim()).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "payment_way_of_ogran", result_obj.optString("id").trim())).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "payment_way", result_obj.optString("payment_id").trim())).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organ_id").trim())).append("')");
                                    listStringAdd.add(sb.toString());
                                }

                            }

                        }
                        // 项目组hq_item_group
                        else if (key.toString().equals("TZXERP.ERP_ITEMGROUP")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_group_id_seq", addsize);
                                Map<String, Object> hq_item_group_map = mapIds.get("hq_item_group");
                                mapIds.put("hq_item_group", hq_item_group_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_group_id = generateIds1.poll();
                                    hq_item_group_map.put(result_obj.optString("id"), hq_item_group_id);
                                }
                                StringBuffer sb = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sb = new StringBuffer();
                                    sb.append("insert into hq_item_group(tenancy_id,fake_id,id,item_group_code,item_group_name,five_code,phonetic_code,item_group_price,valid_state) values('");
                                    sb.append(tenancyId).append("','");
                                    sb.append(result_obj.optString("id").trim()).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", result_obj.optString("id").trim())).append("','");
                                    sb.append(result_obj.optString("item_group_code").trim()).append("','");
                                    sb.append(result_obj.optString("item_group_name").trim()).append("','");
                                    sb.append(result_obj.optString("five_code").trim()).append("','");
                                    sb.append(result_obj.optString("phonetic_code").trim()).append("','");
                                    sb.append(result_obj.optString("item_group_price").trim()).append("','");
                                    sb.append(result_obj.optString("valid_state").trim()).append("')");
                                    listStringAdd.add(sb.toString());
                                }

                            }
                            // 项目组明细hq_item_group_details
                        } else if (key.toString().equals("TZXERP.ERP_ITEMGROUPLIST")) {
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_group_details_id_seq", addsize);
                                Map<String, Object> hq_item_group_details_map = mapIds.get("hq_item_group_details");
                                mapIds.put("hq_item_group_details", hq_item_group_details_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_group_details_id = generateIds1.poll();
                                    hq_item_group_details_map.put(result_obj.optString("id"), hq_item_group_details_id);
                                }
                                StringBuffer sb = null;
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    Double makeup_money = Double.isNaN(result_obj.optDouble("makeup_money")) ? 0.0 : result_obj.optDouble("makeup_money");
                                    Double quantity_limit = Double.isNaN(result_obj.optDouble("quantity_limit")) ? 0.0 : result_obj.optDouble("quantity_limit");
                                    sb = new StringBuffer();
                                    sb.append("insert into hq_item_group_details(tenancy_id,fake_id,id,item_group_id,item_id,item_unit_id,isdefault,makeup_money,quantity_limit) values('");
                                    sb.append(tenancyId).append("','");
                                    sb.append(result_obj.optString("id").trim()).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group_details", result_obj.optString("id").trim())).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", result_obj.optString("item_group_id").trim())).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("item_id").trim())).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", result_obj.optString("item_id").trim())).append("','");
                                    sb.append(result_obj.optString("isdefault").trim()).append("',");
                                    sb.append(makeup_money).append(",");
                                    sb.append(quantity_limit).append(")");
                                    listStringAdd.add(sb.toString());
                                }

                            }
                            // 菜品套餐明细hq_item_combo_details
                        } else if (key.toString().equals("TZXERP.ERP_ORGSILIST")) {
                            if (addsize > 0) {
                                StringBuffer sb = new StringBuffer();
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sb = new StringBuffer();
                                    sb.append("insert into hq_item_combo_details(tenancy_id,fake_id,iitem_id,is_itemgroup,details_id,item_unit_id,combo_num,standardprice,combo_order,valid_state) values('");
                                    sb.append(tenancyId).append("','");
                                    sb.append(result_obj.optString("id").trim()).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("iitem_id").trim())).append("','");
                                    sb.append(result_obj.optString("is_itemgroup")).append("','");
                                    sb.append((StringUtils.equals("Y", result_obj.optString("is_itemgroup")) ?
                                            SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", result_obj.optString("details_id").trim())
                                            : SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", result_obj.optString("details_id").trim())));
                                    sb.append("',");
                                    sb.append((StringUtils.equals("Y", result_obj.optString("is_itemgroup")) ?
                                            "0"
                                            : SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", result_obj.optString("details_id").trim())));
                                    sb.append(",").append(result_obj.optString("combo_num").trim()).append(",'");
                                    sb.append(result_obj.optString("standardprice").trim()).append("','");
                                    sb.append(result_obj.optString("combo_order").trim()).append("','1')");
                                    listStringAdd.add(sb.toString());
                                }

                            }
                        } else if (key.toString().equals("TZXERP.ERP_ORGCORP")) {
                            //法人信息
                            if (addsize > 0) {
                                Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_legal_per_id_seq", addsize);
                                Map<String, Object> hq_legal_per_map = mapIds.get("hq_legal_per");
                                mapIds.put("hq_legal_per", hq_legal_per_map);
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_item_group_details_id = generateIds1.poll();
                                    hq_legal_per_map.put(result_obj.optString("id"), hq_item_group_details_id);
                                }
                                StringBuffer sb = new StringBuffer();
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    sb = new StringBuffer();
                                    sb.append("insert into hq_legal_per(tenancy_id,fake_id,id,legal_per_code,legal_per_name,");
                                    sb.append("is_billing,docking_merchants,wechat_party_id,wechat_invoice_key,taxpayers_attr_id,tax_rate,");
                                    sb.append("invoice_name,cnpj,seller_name,seller_address,seller_number,bank,valid_state,last_operator");
                                    sb.append(",last_updatetime ) values('");
                                    sb.append(tenancyId).append("','");
                                    sb.append(result_obj.optString("fake_id").trim()).append("','");
                                    sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_legal_per", result_obj.optString("id").trim())).append("','");
                                    sb.append(result_obj.optString("legal_per_code").trim()).append("','");
                                    sb.append(result_obj.optString("legal_per_name").trim()).append("','");

                                    //是否开具电子发票  rif和saas的差异的处理
                                    String isBilling = result_obj.optString("is_billing").trim();
                                    if(StringUtils.equals("Y",result_obj.optString("is_billing").trim())) {
                                        isBilling = "1";
                                    } else {
                                        isBilling = "0";
                                    }
                                    sb.append(isBilling).append("','");

                                    //电子发票类型  rif和saas的差异的处理
                                    String dockingMechants = result_obj.optString("docking_merchants").trim();
                                    if(StringUtils.equals(dockingMechants,"YY_A")) {
                                        dockingMechants = "1";
                                    } else if(StringUtils.equals(dockingMechants,"BW_A")) {
                                        dockingMechants = "2";
                                    }
                                    sb.append(dockingMechants).append("','");
                                    sb.append(result_obj.optString("wechat_party_id").trim()).append("','");
                                    sb.append(result_obj.optString("wechat_invoice_key").trim()).append("','");
                                    sb.append(result_obj.optString("taxpayers_attr_id").trim()).append("','");
                                    sb.append(result_obj.optString("tax_rate").trim()).append("','");
                                    sb.append(result_obj.optString("invoice_name").trim()).append("','");
                                    sb.append(result_obj.optString("cnpj").trim()).append("','");
                                    sb.append(result_obj.optString("seller_name").trim()).append("','");
                                    sb.append(result_obj.optString("seller_address").trim()).append("','");
                                    sb.append(result_obj.optString("seller_number").trim()).append("','");
                                    sb.append(result_obj.optString("bank").trim()).append("','1','系统','");
                                    sb.append(currentDateStr).append("')");
                                    listStringAdd.add(sb.toString());
                                }
                                listStringAdd=parseList(listStringAdd);
                                saveAdds(tenancyId, listStringAdd, toTableName);
                                //法人机构关联关系
                                listStringAdd = hqLegalPerUtils.getHqLegalPerOrganRef4Add(tenancyId,mapIds,resultMap.get("add"));
                                saveAdds(tenancyId, listStringAdd, "hq_legal_per_organ_ref");
                                listStringAdd = hqLegalPerUtils.getSysParamSql(tenancyId,mapIds,resultMap.get("add"));
                                saveAdds(tenancyId, listStringAdd, "sys_parameter");
                                listStringAdd = new ArrayList<String>();
                            }
                        }else if(key.toString().equals("TZXERP.ERP_RETUENREASONG")){

                            if (resultMap.get("add") != null && resultMap.get("add").size() > 0) {
                                Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.hq_unusual_reason_id_seq", resultMap.get("add").size());
                                Map<String, Object>hq_unusual_reason_type_map = mapIds.get("hq_unusual_reason");

                                //机构对应关系
                                Map<String, Object> map = mapIds.get("organ");
                                for (JSONObject result_obj : resultMap.get("add")) {
                                    String hq_unusual_reason_id = generateIds.poll();
                                    hq_unusual_reason_type_map.put(result_obj.optString("id"), hq_unusual_reason_id);
                                    StringBuffer sqlbuffer = new StringBuffer();
                                    sqlbuffer.append("insert into hq_unusual_reason(tenancy_id,id,reason_code,unusual_type,father_id,reason_name,phonetic_code,five_code,valid_state,last_operator,last_updatetime,fake_id)");
                                    sqlbuffer.append(" values(");
                                    sqlbuffer.append("'").append(tenancyId).append("',");
                                    sqlbuffer.append(hq_unusual_reason_id).append(",");
                                    sqlbuffer.append(result_obj.optString("reason_code")).append(",");
                                    sqlbuffer.append("'").append("MD03").append("',");
                                    sqlbuffer.append(0).append(",");
                                    sqlbuffer.append("'").append(result_obj.optString("reason_name")).append("',");
                                    sqlbuffer.append("'").append(result_obj.optString("phonetic_code")).append("',");
                                    sqlbuffer.append("'").append(result_obj.optString("five_code")).append("',");
                                    sqlbuffer.append("'").append("1").append("',");
                                    sqlbuffer.append("'").append("system").append("',");
                                    sqlbuffer.append("now()").append(",");
                                    sqlbuffer.append(result_obj.optInt("id")).append("");
                                    sqlbuffer.append(")");
                                    sql = sqlbuffer.toString();
                                    listStringAdd.add(sql);

                                }
                            }
                        
                        }
                        
                        
                        
                        

                        if (listStringAdd.size() > 0) {
                            //保存所添加的数据
                            saveAdds(tenancyId, listStringAdd, toTableName);
                        }
                        if (resultMap.containsKey("update") && resultMap.get("update").size() > 0) {
                            try {
                                //转换数据所依赖的真实Id和伪Id的值   begin
                                List<JSONObject> list = resultMap.get("update");
                                String[] ignores = new String[]{"id"};
                                if (StringUtils.equals(key.toString(), "TZXUUS.UUS_ORGAN")) {
                                    for (JSONObject json : list) {
                                        json.put("top_org_id", SynIdUtils.getIdBytableFakeId(mapIds, "organ", json.optString("top_org_id")));
                                        String organPriceSystemId = SynIdUtils.getIdBytableFakeId(mapIds, "hq_price_system", json.optString("price_system"));
                                        json.put("price_system", StringUtils.equals(organPriceSystemId, "0") ? null : organPriceSystemId);
//										ignores = new String[]{"id","price_system"};
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ORGSORTS")) {
                                    for (JSONObject json : list) {
                                        json.put("father_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_1", json.optString("father_id")));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.VST_SAAS_ITEM_CLASS_LM")
                                        || StringUtils.equals(key.toString(), "TZXERP.VST_SAAS_ITEM_CLASS_SM")
                                        ) {
                                    for (JSONObject json : list) {
                                        json.put("father_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_2", json.optString("father_id")));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ITEMINFO")) {
                                    List<JSONObject> itemUnitList = new ArrayList<JSONObject>();
                                    JSONObject itemUnitJson = null;
                                    for (JSONObject json : list) {
                                        itemUnitJson = new JSONObject();
                                        //获取菜品规格的ID
                                        itemUnitJson.put("id",SynIdUtils.getIdBytableFakeId(mapIds,"hq_item_unit",json.optString("id")));
                                        //获取菜品规则的最新价格
                                        itemUnitJson.put("standard_price",json.optString("xsdj").trim());
                                        itemUnitJson.put("unit_name",json.optString("dw_name").trim());
                                        itemUnitList.add(itemUnitJson);

                                        //设置菜品信息的类别，fake_id向ID转换  hq_item_info
                                        json.put("item_class", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_1", json.optString("item_class").trim()));
                                    }
                                    //更新菜品规格的价格信息
                                    this.dao.updateBatchIgnorCase(tenancyId,"hq_item_unit",itemUnitList);
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ITEMPRICE_SYS")) {
                                    for (JSONObject json : list) {
                                        String hq_item_unit = SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", json.optString("item_id").trim());
                                        String hq_price_system = SynIdUtils.getIdBytableFakeId(mapIds, "hq_price_system", json.optString("price_system").trim());
                                        json.put("item_unit_id", hq_item_unit);
                                        json.put("price_system", hq_price_system);
                                        ignores = new String[]{"id", "chanel"};
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ITEMGROUPLIST")) {
                                    for (JSONObject json : list) {
                                        json.put("item_group_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", json.optString("item_group_id").trim()));
                                        json.put("item_unit_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", json.optString("item_id").trim()));
                                        json.put("item_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", json.optString("item_id").trim()));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_ORGSILIST")) {
                                    for (JSONObject json : list) {
                                        String is_itemgroup = json.optString("is_itemgroup");
                                        if (StringUtils.equals("Y", is_itemgroup)) {
                                            json.put("details_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_group", json.optString("details_id").trim()));
                                        } else {
                                            json.put("item_unit_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", json.optString("details_id").trim()));
                                            json.put("details_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", json.optString("details_id").trim()));
                                        }
                                        json.put("iitem_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", json.optString("iitem_id").trim()));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_CPYSSYJG")) {
                                    for (JSONObject json : list) {
                                        json.put("item_menu_id", SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu", json.optString("item_menu_id").trim()));
                                        json.put("store_id", SynIdUtils.getIdBytableFakeId(mapIds, "organ", json.optString("store_id").trim()));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_PAYMENT_ORG_REF")) {
                                    for (JSONObject json : list) {
                                        json.put("payment_id", SynIdUtils.getIdBytableFakeId(mapIds, "payment_way", json.optString("payment_id").trim()));
                                        json.put("organ_id", SynIdUtils.getIdBytableFakeId(mapIds, "organ", json.optString("organ_id").trim()));
                                    }
                                } else if (StringUtils.equals(key.toString(), "TZXERP.ERP_CPYSMX")) {
                                    for(JSONObject json : list){
                                        json.put("id",json.optString("menu_details_id"));
                                        json.put("menu_item_rank", json.optString("cpsx"));
                                    }
                                    this.dao.updateBatchIgnorCase(tenancyId, toTableName, list);
                                    for (JSONObject json : list) {
                                        json.remove("cancel");
                                        json.remove("class_id");
                                        json.put("menu_class_rank", json.optString("lbsx"));
                                        json.put("id", json.optString("menu_class_id"));
                                    }
                                } else if(StringUtils.equals("TZXERP.ERP_ORGCORP",key.toString())) {
                                    for(JSONObject json : list){
                                        //是否开具电子发票  rif和saas的差异的处理
                                        String isBilling = json.optString("is_billing").trim();
                                        if(StringUtils.equals("Y",json.optString("is_billing").trim())) {
                                            isBilling = "1";
                                        } else {
                                            isBilling = "0";
                                        }
                                        json.put("is_billing",isBilling);

                                        //电子发票类型  rif和saas的差异的处理
                                        String dockingMechants = json.optString("docking_merchants").trim();
                                        if(StringUtils.equals(dockingMechants,"YY_A")) {
                                            dockingMechants = "1";
                                        } else if(StringUtils.equals(dockingMechants,"BW_A")) {
                                            dockingMechants = "2";
                                        }
                                        json.put("docking_merchants",dockingMechants);
                                    }

                                    //处理电子发票和机构的关系在更新时
                                    List<String> addlist = hqLegalPerUtils.getHqLegalPerOrganRef4Add(tenancyId,mapIds,list);
                                    batchUpdate(tenancyId, addlist);
                                    logger.info("更新时添加电子发票机构关系成功");
                                    addlist = hqLegalPerUtils.getSysParamSql(tenancyId,mapIds,list);
                                    batchUpdate(tenancyId, addlist);
                                    logger.info("更新时添加电子系统参数关系成功");
                                    /*addlist = hqLegalPerUtils.getUpdateOrganSql(mapIds,list);
                                    batchUpdate(tenancyId, addlist);
                                    logger.info("更新电子发票机构关系成功");*/
                                    addlist = hqLegalPerUtils.getUpdateSysparamSql(mapIds,list);
                                    batchUpdate(tenancyId, addlist);
                                    logger.info("更新电子系统参数关系成功");
                                } 


                                //更新表
                                if (list != null && !list.isEmpty()) {
                                    if (key.toString().equals("TZXERP.ERP_ORGSORTS")
                                            ) {
                                        //根据fake_id和fake_type更新
                                        this.dataTransferDao.updateBatchIgnorCaseByfakeId(tenancyId, " AND fake_type=1 ", toTableName, list, ignores);
                                    } else if (key.toString().equals("TZXERP.VST_SAAS_ITEM_CLASS_LM")) {
                                        //根据fake_id和fake_type更新
                                        this.dataTransferDao.updateBatchIgnorCaseByfakeId(tenancyId, " AND fake_type=2 ", toTableName, list, ignores);
                                    } else if (key.toString().equals("TZXERP.VST_SAAS_ITEM_CLASS_SM")) {
                                        //根据fake_id和fake_type更新
                                        this.dataTransferDao.updateBatchIgnorCaseByfakeId(tenancyId, " AND fake_type=3 ", toTableName, list, ignores);
                                    } else if (key.toString().equals("TZXERP.ERP_CPYSMX")) {
                                        //对于餐谱明细只有餐谱才的名称可能会变化
                                        this.dao.updateBatchIgnorCase(tenancyId, "hq_item_menu_class", list);
                                    } else {
                                        //根据fake_id更新
                                        this.dataTransferDao.updateBatchIgnorCaseByfakeId(tenancyId, toTableName, list, ignores);
                                    }
                                }
                                logger.info("对saas表 " + toTableName + " 同步更新成功！");
                            } catch (Exception e2) {
                                logger.error(e2);
                                e2.printStackTrace();
                            }
                        }
                    } catch (Exception e) {
                        flag = false;
                        logger.error(e);
                        logger.info("对表：" + key.toString() + "初始化到saas的操作失败！" + e);
                        throw new Exception("对表：" + key.toString() + "初始化到saas的操作失败！" + e);
                    }
                }
                result_flag.put("flag", flag);
            }

            return result_flag;
        } finally {
            TempTableUtils.realizeConnection();
            MultiDatasourceContextHelper.clear();
        }
    }

    /**
     * 获取选中机构对应的餐谱主表Id
     * @param organstr1
     * @param mapIds
     * @return
     */
    private String getDelSql4Organ(String organstr1, String key, String column , Map<String, Map<String, Object>> mapIds) {
        String delsql;
        String[] split = organstr1.split(";");
        StringBuffer delsqlbuffer = new StringBuffer();
        for(int i=0;i<split.length;i++){
            String menuorganstr = (String) mapIds.get(key).get(split[i]);
            if(StringUtils.isNotBlank(menuorganstr)) {
                delsqlbuffer.append(menuorganstr).append("','");
            }
        }
        delsql = delsqlbuffer.toString();
        if(delsql.length()>3) {
            delsql = delsql.substring(0,delsql.length()-3);
            delsql = " and dest."+column+" in ('"+delsql+"')";
        }
        return delsql;
    }

    @Override
    public JSONObject dataSourceLoad(String tenancyID, JSONObject condition) throws Exception {
        StringBuilder sql = new StringBuilder();
        JSONObject result = new JSONObject();

        sql.append("SELECT *,db_driver as db_driver_value FROM cc_rif_database_setting ");

        @SuppressWarnings("unchecked")
        Set<String> keys = condition.keySet();
        for (String s : keys) {
            if ("tableName".equals(s) || "page".equals(s) || "rows".equals(s) || "sort".equals(s) || "order".equals(s) || "sortName".equals(s)) {
                continue;
            }
        }
        if (condition.optString("sort") != null && !"".equals(condition.optString("sort"))) {
            sql.append(" order by " + condition.optString("sort") + " " + condition.optString("order"));
        } else {
            sql.append(" order by tenancy_id");
        }
        int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
        long total = this.dao.countSql(tenancyID, sql.toString());
        List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));

        result.put("page", pagenum);
        result.put("total", total);
        result.put("rows", list);

        return result;
    }

    /**
     * 删除默认渠道没有，其他渠道确存在的信息  比如餐谱
     * @param tenancyId
     * @param toTableName
     */
    private void deleteOtherChanelInfo2MD01(String tenancyId, String toTableName) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append("delete from ").append(toTableName);
        sql.append(" where 1=1 and fake_id in (");
        sql.append("select a.fake_id from ");
        sql.append(" (select DISTINCT fake_id from ");
        sql.append(toTableName);
        sql.append(" a where a.chanel <> 'MD01') as a");
        sql.append(" left join (");
        sql.append(" select DISTINCT fake_id from ");
        sql.append(toTableName);
        sql.append(" where chanel = 'MD01') as b");
        sql.append(" on a.fake_id = b.fake_id");
        sql.append(" where b.fake_id is null");
        sql.append(")");
        this.dao.execute(tenancyId,sql.toString());
    }


    /**
     * 打包渠道缓存Map
     *
     * @param resultMap
     * @param generateIds
     * @param hq_item_pricesystem_map
     */
    private void pakQdMap(Map<String, List<JSONObject>> resultMap, Queue<String> generateIds, Map<String, Object> hq_item_pricesystem_map) {
        String chanel = null;
        for (JSONObject result_obj : resultMap.get("add")) {
            chanel = result_obj.optString("chanel");
            Map<String, String> channelMap = (Map<String, String>) hq_item_pricesystem_map.get(chanel);
            if (channelMap == null) {
                channelMap = new HashMap<String, String>();
                channelMap.put(result_obj.optString("id"), generateIds.poll());
                hq_item_pricesystem_map.put(chanel, channelMap);
            } else {
                channelMap.put(result_obj.optString("id"), generateIds.poll());
            }
        }
    }

    private void deleteBatchIgnorCaseByRelevance(String tenancyId, String toTableName, List<JSONObject> deleteList, String relevanceName) throws Exception {
        StringBuffer sql = new StringBuffer();
        if (deleteList == null || deleteList.isEmpty()) {
            logger.info("表" + toTableName + "没有要删除的数据");
            return;
        }
        sql.append("delete from ").append(toTableName);
        sql.append(" where 1=1 and ");
        sql.append(relevanceName);
        sql.append(" in (");
        for (int i = 0; i < deleteList.size(); i++) {
            if (i == 0) {
                sql.append(deleteList.get(i).optString("id"));
            } else {
                sql.append(",").append(deleteList.get(i).optString("id"));
            }
        }
        sql.append(")");

        String sqlstr = sql.toString();
        logger.info("对表" + toTableName + "进行删除删除语句为");
        logger.info(sqlstr);
        this.dataTransferDao.getJdbcTemplate(tenancyId).execute(sqlstr);
    }

    /**
     * 根据ID删除表的数据
     *
     * @param tenancyId
     * @param toTableName
     * @param deleteList
     */
    private void deleteBatchIgnorCaseById(String tenancyId, String toTableName, List<JSONObject> deleteList) throws Exception {
        deleteBatchIgnorCaseByRelevance(tenancyId, toTableName, deleteList, "id");
    }

    private void fillHqItemInfoClassId(String tenancyId, Map<String, Map<String, Object>> mapIds) throws Exception {
        Map<String, Object> hq_item_info_class_id = new HashMap<String, Object>();
        mapIds.put("hq_item_info_class_id", hq_item_info_class_id);

        StringBuffer sql = new StringBuffer();
        sql.append("select hq_item_info.fake_id,hq_item_class.id from hq_item_info ");
        sql.append(" inner join hq_item_class on (hq_item_info.item_class = hq_item_class.id)");
        sql.append(" where hq_item_class.fake_type = 1");

        List<Map<String, Object>> list = this.dataTransferDao.getJdbcTemplate(tenancyId).queryForList(sql.toString());
        for (Map<String, Object> item : list) {
            Integer fake_id = (Integer) item.get("fake_id");
            if(fake_id == null) {
                continue;
            }
            Integer id = (Integer) item.get("id");
            hq_item_info_class_id.put(fake_id.toString(), id.toString());
        }
    }

    /**
     * 获取渠道信息
     *
     * @param tenancyId
     * @return
     * @throws Exception
     */
    public List<JSONObject> getChanelInfo(String tenancyId) throws Exception {
       return getChanelInfo(tenancyId,null);
    }

    /**
     * 获取渠道信息
     *
     * @param tenancyId
     * @return
     * @throws Exception
     */
    private List<JSONObject> getChanelInfo(String tenancyId,JSONObject serviceParams) throws Exception {
        StringBuffer dictionnary_sql = new StringBuffer("SELECT * from sys_dictionary a where a.class_identifier_code='chanel' and a.valid_state = '1' and a.class_item_code in ('MD01','WX02','BD06','MT08','EL09','WM10')");
        if(serviceParams!=null) {
            String chanel = serviceParams.optString("chanel");
            chanel = chanel.replaceAll(";","','");
            dictionnary_sql.append(" and a.class_item_code in ('");
            dictionnary_sql.append(chanel);
            dictionnary_sql.append("')");
        }
        List<JSONObject> dic_result_list = this.dataTransferDao.query4Json(tenancyId, dictionnary_sql.toString());
        return dic_result_list;
    }

    /**
     * Db2专用，处理多出的数据
     *
     * @throws Exception
     */
    private void processAddChanel(String tenancyId,JSONObject param) throws Exception {
        List<JSONObject> chanelInfo = getChanelInfo(tenancyId,param);
        String sql = "select chanel from hq_item_class where fake_id is not null group by chanel";
        List<JSONObject> query4Json = this.dao.query4Json(tenancyId, sql);

        JSONObject chanelJsonExits = new JSONObject();
        for (JSONObject chanelJson : query4Json) {
            String chanel = chanelJson.optString("chanel");
            chanelJsonExits.put(chanel, Boolean.TRUE);
        }

        for (JSONObject chanelJson : chanelInfo) {
            String optString = chanelJson.optString("class_item_code");
            Boolean isExist = chanelJsonExits.optBoolean(optString);
            if (isExist != null && isExist.booleanValue()) {
                continue;
            }
            insertNewChanelInfo(tenancyId, optString);
        }
    }

    /**
     * 插入新渠道信息
     *
     * @param tenancyId
     * @throws Exception
     */
    private void insertNewChanelInfo(String tenancyId, String chanel) throws Exception {
        List<String> list = new ArrayList<String>();
        StringBuffer sql = new StringBuffer();
        //菜品类别信息
        sql.append(" select a.id,a.tenancy_id,a.itemclass_code,a.itemclass_name,a.father_id,a.valid_state,a.chanel,a.fake_id from hq_item_class a where fake_id is not null and chanel = 'MD01'");
        List<JSONObject> classOldJson = this.dao.query4Json(tenancyId, sql.toString());
        Queue<String> generateIds4Class = SynIdUtils.generateIds(tenancyId, "public.hq_item_class_id_seq", classOldJson.size());
        Map<String, String> map = new HashMap<String, String>();
        for (JSONObject json : classOldJson) {
            String id = json.optString("id");
            map.put(id, generateIds4Class.poll());
            map.put("0", "0");
        }

        for (JSONObject json : classOldJson) {
            sql = new StringBuffer();
            sql.append("insert into hq_item_class(tenancy_id,id,itemclass_code,itemclass_name,father_id,valid_state,chanel,fake_id)");
            sql.append(" values('");
            sql.append(json.optString("tenancy_id")).append("',");
            sql.append(map.get(json.optString("id"))).append(",'");
            sql.append(json.optString("itemclass_code")).append("','");
            sql.append(json.optString("itemclass_name")).append("',");
            sql.append(map.get(json.optString("father_id"))).append(",'");
            sql.append(json.optString("valid_state")).append("','");
            sql.append(chanel).append("',");
            sql.append(json.optString("fake_id"));
            sql.append(")");
            list.add(sql.toString());
        }
        saveAdds(tenancyId, list, "休整渠道信息");
        list.clear();

        //价格体系明细
        sql = new StringBuffer();
        sql.append("insert into hq_item_pricesystem (tenancy_id,item_unit_id,price_system,chanel,price,fake_id)");
        sql.append(" select tenancy_id,item_unit_id,price_system,'");
        sql.append(chanel);
        sql.append("',price,fake_id from hq_item_pricesystem ");
        sql.append(" where fake_id is not null and chanel = 'MD01' ");
        this.dao.execute(tenancyId, sql.toString());

        //餐谱明细
        sql = new StringBuffer();
        sql.append("select a.tenancy_id,a.item_menu_id,a.item_id,a.starttime,a.valid_state,a.fake_id,b.class as class_id,b.item_name");
        sql.append(" from hq_item_menu_details a,hq_item_menu_class b");
        sql.append(" where a.id = b.details_id and b.chanel = 'MD01' ");
        sql.append(" and a.fake_id is not null and b.fake_id is not null");
        List<JSONObject> tcmxs = this.dao.query4Json(tenancyId, sql.toString());

        Queue<String> generateIds = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_details_id_seq", tcmxs.size());
        for (JSONObject tcmx : tcmxs) {
            sql = new StringBuffer();
            String detailsid = generateIds.poll();
            sql.append("insert into hq_item_menu_details (tenancy_id,id,item_menu_id,item_id,starttime,valid_state,fake_id) ");
            sql.append(" values('");
            sql.append(tcmx.optString("tenancy_id")).append("',");
            sql.append(detailsid).append(",");
            sql.append(tcmx.optString("item_menu_id")).append(",");
            sql.append(tcmx.optString("item_id")).append(",'");
            sql.append(tcmx.optString("starttime")).append("','");
            sql.append(tcmx.optString("valid_state")).append("',");
            sql.append(tcmx.optString("fake_id"));
            sql.append(")");
            list.add(sql.toString());

            sql = new StringBuffer();
            sql.append("insert into hq_item_menu_class (tenancy_id,details_id,chanel,class,item_name,fake_id)");
            sql.append(" values('");
            sql.append(tcmx.optString("tenancy_id")).append("',");
            sql.append(detailsid).append(",'");
            sql.append(chanel).append("',");
            sql.append(map.get(tcmx.optString("class_id"))).append(",'");
            sql.append(tcmx.optString("item_name")).append("',");
            sql.append(tcmx.optString("fake_id"));
            sql.append(")");
            list.add(sql.toString());
        }
        saveAdds(tenancyId, list, "休整渠道信息");
    }

    /**
     * 保存添加的数据
     *
     * @param tenancyId
     * @param listStringAdd
     * @param toTableName
     */
    private void saveAdds(String tenancyId, List<String> listStringAdd,
                          String toTableName) {
        try {
            String[] sqlArray = null;
            int size = listStringAdd.size();
            if (size < 1) {
                return;
            }
            sqlArray = (String[]) listStringAdd.toArray(new String[size]);
            dataTransferDao.getJdbcTemplate(tenancyId).batchUpdate(sqlArray);
            logger.info("对saas表 " + toTableName + " 同步添加数据成功！");
        } catch (Exception e2) {
            logger.error(e2);
            e2.printStackTrace();
        }
    }

    /**
     * 保存添加的数据
     *
     * @param tenancyId
     * @param list
     */
    private void batchUpdate(String tenancyId, List<String> list) {
        try {
            String[] sqlArray = null;
            int size = list.size();
            if (size < 1) {
                return;
            }
            sqlArray = (String[]) list.toArray(new String[size]);
            dataTransferDao.getJdbcTemplate(tenancyId).batchUpdate(sqlArray);
        } catch (Exception e2) {
            logger.error(e2);
            e2.printStackTrace();
        }
    }

    /**
     * List<String> 去重
     *
     * @param tenancyId
     * @param list
     */
    private List<String> parseList(List<String> list) {
    	List<String> returnList=null;
    	Set<String> set = new HashSet<>();
    	if(list.size()>0){
            set.addAll(list);
            returnList = new ArrayList<String>(set);
    	}
    	return returnList;
    }

    @Override
    public String dataSourceSave(String tenantId, String tableKey, JSONObject data) throws Exception {
        String result = "{\"success\" : true , \"msg\" : \"保存成功\"}";
        StringBuilder sb = new StringBuilder();
        if (!"".equals(data.optString("db_driver"))) {
            sb.append("select id from cc_rif_database_setting where db_driver='" + data.optString("db_driver") + "' ");

            if (data.optInt("id") > 0) {
                sb.append(" and id<>" + data.optInt("id"));
            }

            List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());

            if (list != null && list.size() > 0) {
                result = "{\"success\" : false , \"msg\" : \"同一数据库类型只能有一条记录\"}";
                return result;
            }

        }
        if (!Tools.hv(tableKey) || data == null) {
            return null;
        }

        if (data.containsKey("id") && Tools.hv(data.get("id").toString())) {
            this.dao.updateIgnorCase(tenantId, tableKey, data);
        } else {
            data.remove("id");
            this.dao.insertIgnorCase(tenantId, tableKey, data);
        }
        return result;
    }


    @Override
    public String getRifOrgData(JSONObject p) throws Exception {
        List<JSONObject> dataList = dataTransferDao.findPreTransferData(p, "TZXUUS.UUS_ORGAN");
        if(dataList==null || dataList.isEmpty()) {
            return StringUtils.EMPTY;
        }

        List<Organ> list = new ArrayList<Organ>();
        Organ organ = new Organ();;
        for(JSONObject json :dataList){
            int id = json.optInt("id");
            String name = json.optString("org_full_name");
            int topOrgId = json.optInt("top_org_id");
            String orgType = json.optString("org_type");
            String organCode = json.optString("organ_code");
            int level = json.optInt("level");

            Organ organItem = new Organ();
            organItem.setId(id);
            organItem.setText(name);
            organItem.setFatherId(topOrgId);
            organItem.setType(orgType);
            organItem.setCode(organCode);
            organItem.setLevel(level);
            list.add(organItem);
        }
        List<Organ> list2 = new ArrayList<Organ>();
        Map<Integer, Organ> map = new HashMap<Integer, Organ>();
        for (Organ jo : list){
            map.put(jo.getId(), jo);
        }

        for (Organ jo1 : list){
            if (map.get(jo1.getFatherId()) == null){

                list2.add(jo1);
            }

            if (map.get(jo1.getFatherId()) != null){

                map.get(jo1.getFatherId()).getChildren().add(map.get(jo1.getId()));

            }
        }
        boolean flag = true;

        if (flag){
            organ.setChildren(list2);
            organ.setLevel(0);
            organ.setCode("");
            organ.setId(0);
            organ.setType("1");
            organ.setText(organ.getCode() + "总部");
            List<Organ> flist = new ArrayList<Organ>();
            flist.add(organ);
            return JsonUtils.list2json(flist);

        }
        return JsonUtils.list2json(list2);
    }


    @Override
    public JSONObject ping(String tenentid, JSONObject p) {
        JSONObject tablesMap = new JSONObject();
        String db_driver = p.optString("db_driver");
        JSONObject result_flag = new JSONObject();
        ;

        //设置数据源
        DriverUtils.setDriver(db_driver);
        boolean isConn = dataTransferDao.testConnect(p, result_flag);
        if (!isConn) {
            result_flag.put("success", "false");
            result_flag.put("msg", "失败");
        } else {
            result_flag.put("success", "true");
            result_flag.put("msg", "成功");
        }
        return result_flag;
    }
}
