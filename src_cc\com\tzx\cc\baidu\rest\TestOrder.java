package com.tzx.cc.baidu.rest;

import com.alibaba.fastjson.JSONObject;
import com.tzx.cc.baidu.entity.CmdType;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.framework.common.util.HttpUtil;

public class TestOrder {
	public static void main(String[] args) {
		try {
	        JSONObject A =new JSONObject();
			A.put("order_id", "15026799852275");
			String requestStr = CommonUtil.cmdFactory1("30258", "90a27d57f782a623", CmdType.ORDER_GET,A);
			String resultString = HttpUtil.sendPostRequest("http://api.waimai.baidu.com", requestStr);
			System.out.println(resultString);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	
	}

}
