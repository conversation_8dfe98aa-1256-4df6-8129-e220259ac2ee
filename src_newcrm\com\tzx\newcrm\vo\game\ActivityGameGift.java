package com.tzx.newcrm.vo.game;

import java.util.List;


/**
 * 游戏赠礼表
 * <AUTHOR>
 *
 */
public class ActivityGameGift {
    private Integer id;//主键id

    private String tenancyId;//商户id

    private Integer activityId;//活动id
    
    private String storerId;//机构id
    
    private Integer minNumber;//最少人数
    
    private Integer maxNumber;//最多人数

    private String couponId;//优惠券id
    
    private String couponName;//优惠券名称
    
    private Integer playerId;//玩家id
    
   private String playerName;// 玩家名称

    private Integer type; //类型1买单人2逃单人

    private String groupId;//规则组
    
    private Integer num;//玩家排名
    
    private String title;//规则名称
    
    private Integer name;//规则排序
    
    private String remark;//备注
    
    private Integer sheets;//优惠券数量
    //1220新增券面值
    /**
     * 券面值
     */
    private Double faceValue;
    
    private List<GamePlayer> playerlist;
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getTenancyId() {
		return tenancyId;
	}

	public void setTenancyId(String tenancyId) {
		this.tenancyId = tenancyId;
	}

	public Integer getActivityId() {
		return activityId;
	}

	public void setActivityId(Integer activityId) {
		this.activityId = activityId;
	}

	public String getStorerId() {
		return storerId;
	}

	public void setStorerId(String storerId) {
		this.storerId = storerId;
	}

	public Integer getMinNumber() {
		return minNumber;
	}

	public void setMinNumber(Integer minNumber) {
		this.minNumber = minNumber;
	}

	public Integer getMaxNumber() {
		return maxNumber;
	}

	public void setMaxNumber(Integer maxNumber) {
		this.maxNumber = maxNumber;
	}


	public String getCouponId() {
		return couponId;
	}

	public void setCouponId(String couponId) {
		this.couponId = couponId;
	}

	public String getCouponName() {
		return couponName;
	}

	public void setCouponName(String couponName) {
		this.couponName = couponName;
	}

	public Integer getPlayerId() {
		return playerId;
	}

	public void setPlayerId(Integer playerId) {
		this.playerId = playerId;
	}

	public String getPlayerName() {
		return playerName;
	}

	public void setPlayerName(String playerName) {
		this.playerName = playerName;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getName() {
		return name;
	}

	public void setName(Integer name) {
		this.name = name;
	}

	public Integer getSheets() {
		return sheets;
	}

	public void setSheets(Integer sheets) {
		this.sheets = sheets;
	}

	public List<GamePlayer> getPlayerlist() {
		return playerlist;
	}

	public void setPlayerlist(List<GamePlayer> playerlist) {
		this.playerlist = playerlist;
	}

	public Double getFaceValue() {
		return faceValue;
	}

	public void setFaceValue(Double faceValue) {
		this.faceValue = faceValue;
	}
 
}