package com.tzx.report.po.boh.impl;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.StaffCommissionInquiryDao;

@Repository(StaffCommissionInquiryDao.NAME)
public class StaffCommissionInquiryDaoImpl implements StaffCommissionInquiryDao{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource 
	ConditionUtils conditionUtils ;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Override
	public JSONObject find(String tenancyID, JSONObject condition) throws Exception
	{
		Integer type = condition.optInt("type");
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure =new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		String begindate = condition.optString("begin_date");
		String enddate = condition.optString("end_date");
		long total = 0L;
		if(begindate.length()>0 && enddate.length()>0 )
		{
			switch (type)
			{
				//按菜品汇总
				case 1:
					if(condition.optInt("hierarchytype") ==1){
						if(condition.containsKey("exportdataexpr")&&!condition.optString("exportdataexpr").equals("''")){
							condition.element("exportdataexpr", conditionUtils.spilt(condition.optString("exportdataexpr").substring(1, condition.optString("exportdataexpr").length()-1)));
						}
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_CHECK_THE_FIRST_FLOOR_ACCORDING_TO_THE_DISHES);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
								total = this.dao.countSql(tenancyID,completionSql);
								list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
								footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_TOTAL_ACCORDING_TO_DISHES_QUERY_SUMMARY));
					    }
					}else if(condition.optInt("hierarchytype") ==2){
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_CHECK_THE_SECOND_LAYERS_ACCORDING_TO_THE_DISHES);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level1")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					    }
					}else if(condition.optInt("hierarchytype") ==3){
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_CHECK_THE_THIRD_LAYERS_ACCORDING_TO_THE_DISHES);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level2")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					    }
					}else{
						  return null;
					}
				break;
				//按员工汇总	
				case 2:
					if(condition.optInt("hierarchytype") ==1){
						if(condition.containsKey("exportdataexpr")&&!condition.optString("exportdataexpr").equals("''")){
							condition.element("exportdataexpr", conditionUtils.spilt(condition.optString("exportdataexpr").substring(1, condition.optString("exportdataexpr").length()-1)));
						}
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THE_FIRST_FLOOR_ACCORDING_TO_THE_STAFF);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
							footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_TOTAL_COLLECT_BY_EMPLOYEE));
					    }
					}
					if(condition.optInt("hierarchytype") ==2){
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_SECOND_LAYERS_BY_STAFF);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level1")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					    }
					} 
					if(condition.optInt("hierarchytype") ==3){
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THIRD_LAYERS_BY_STAFF);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level2")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					    }
					}
					
				break;
				//按日期汇总	
				case 3:
					if(condition.optInt("hierarchytype") ==1){
						if(condition.containsKey("exportdataexpr")&&!condition.optString("exportdataexpr").equals("''")){
							condition.element("exportdataexpr", conditionUtils.spilt(condition.optString("exportdataexpr").substring(1, condition.optString("exportdataexpr").length()-1)));
						}
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THE_FIRST_FLOOR_BY_DATE);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
							footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_TOTAL_AGGREGATED_BY_DATE));
					    }
					}else if(condition.optInt("hierarchytype") ==2){
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_SECOND_LAYERS_BY_DATE);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level1")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					    }
					}else if(condition.optInt("hierarchytype") ==3){
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THIRD_LAYERS_BY_DATE);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level2")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					    }
					}else{
						  return null;
					}
				break;
				//按机构汇总
				case 4:
					if(condition.optInt("hierarchytype") ==1){
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THE_FIRST_FLOOR_ACCORDING_TO_THE_ORGANIZATION);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
							footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_TOTAL_COLLECT_BY_ORGANIZATION));
					    }
					}else if(condition.optInt("hierarchytype") ==2){
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_SECOND_LAYERS_ACCORDING_TO_THE_ORGANIZATION);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level1")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					    }
					}else if(condition.optInt("hierarchytype") ==3){
						String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THIRD_LAYERS_ACCORDING_TO_THE_ORGANIZATION);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level2")));
							structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
						}else{
							total = this.dao.countSql(tenancyID,completionSql);
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					    }
					}else{
						return null;
					}
				break;
				
		case 5:
			if(condition.optInt("hierarchytype") ==1){
				if(condition.containsKey("exportdataexpr")&&!condition.optString("exportdataexpr").equals("''")){
					condition.element("exportdataexpr", conditionUtils.spilt(condition.optString("exportdataexpr").substring(1, condition.optString("exportdataexpr").length()-1)));
				}
				String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THE_FIRST_FLOOR_AND_ITEMCLASS_BY_DATE1);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level")));
					structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
				}else{
					total = this.dao.countSql(tenancyID,completionSql);
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THE_FIRST_FLOOR_AND_ITEMCLASS_BY_DATE0));
			    }
			}else if(condition.optInt("hierarchytype") ==2){
				String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THE_FIRST_FLOOR_AND_ITEMCLASS_BY_DATE2);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level1")));
					structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
				}else{
					total = this.dao.countSql(tenancyID,completionSql);
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
			    }
			}else if(condition.optInt("hierarchytype") ==3){
				String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_COLLECT_THE_FIRST_FLOOR_AND_ITEMCLASS_BY_DATE3);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,completionSql.toString(),condition.optInt("level2")));
					structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
				}else{
					total = this.dao.countSql(tenancyID,completionSql);
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
			    }
			}else{
				  return null;
			}
				break;
				
				default:
				break;
			}
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}

	@Override
	public JSONObject getItemClass(String tenancyID, JSONObject condition)
			throws Exception {
		// TODO Auto-generated method stub
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.GET_FOODITEM_BY_ITEMCODE);
		long total = this.dao.countSql(tenancyID,completionSql);
		list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		return result;
	}
	 
}
