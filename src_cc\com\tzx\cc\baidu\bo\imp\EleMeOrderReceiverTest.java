package com.tzx.cc.baidu.bo.imp;

import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.thirdparty.bo.imp.EleManager;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2017-02-22
 */
public class EleMeOrderReceiverTest extends ThirdPartyOrderReceiver {
    private JSONObject requestOrder;
    private double discount_fee;                                                                // 优惠总金额
    private double commission_dish_total = 0.0;                                                        // 收取佣金菜品信息合计
    private double product_amount;                                                                // 订单商品总金额
    private double send_fee;                                                                    // 配送费
    private double discountRate;                                                                // 优惠比例=优惠总金额/订单商品总金额
    private double platform_side_discount_fee = 0.0;                                            // 平台方承担的优惠总金额
    private double takeawayBusinessIncome;                                                        // 外卖的营业收入=用户实付-配送费+平台方承担的金额
    private double food_sharing_date;                                                            // 菜品分摊的比例=菜品总计/用户实付-配送费+平台方承担的金额
    private double total_product_fee = 0.0;                                                            // 餐品价格
    protected double eleDiscountCommission;//饿了么佣金金额
    protected double ele_platform_charge_amount = 0.0;    // 平台收取金额
    protected double ele_shop_real_amount = 0.0;    // 商家收取金额
    private String productProfileName = ""; //外卖配送方式
    private String consumer_key = "5276817402";
    private String consumer_secret = "340885da38819eece75b3fb31bef07a98d5219b8";
    private static final Logger logger = Logger.getLogger(EleMeOrderReceiverTest.class);
    private EleManager thirdPartyManager = new EleManager(tenantId);

    public EleMeOrderReceiverTest() {
        super(Constant.ELE_CHANNEL);
    }

    public EleMeOrderReceiverTest(JSONObject order) {
        super(order, Constant.ELE_CHANNEL);
        JSONObject params = new JSONObject();
        try {
            String ids = order.optString("eleme_order_id");
            params.put("eleme_order_id", ids);
            params.put("tp_id", "1");
            consumer_key = order.optString("source");
            consumer_secret = order.optString("secret");
            params.put("source", consumer_key);
            params.put("secret", consumer_secret);
//            thirdPartyOrder = thirdPartyManager.getOrderDetail(params);
            thirdPartyOrder = order;
            System.out.println("================饿了么新订单:查询订单详情返回结果：[" + thirdPartyOrder + "]===========================");
            if (thirdPartyOrder.optString("code").equals("200") && thirdPartyOrder.optString("message").equals("ok")) {

                // 订单信息
                requestOrder = thirdPartyOrder.optJSONObject("data");
                // 存redis
                JSONObject thirdShopID = new JSONObject();
                thirdShopID.put("restaurant_id", thirdPartyOrder.optJSONObject("data").optString("restaurant_id"));
                logger.info("饿了么存入redis里的信息" + thirdShopID.toString());
                redis.saveBykv(ids, thirdShopID.toString(), 172800);

                JSONObject redisJson = taskRedisDao.read(thirdPartyOrder.optJSONObject("data").optString("restaurant_id").getBytes());
                taskRedisDao.save(thirdPartyOrder.optJSONObject("data").optString("restaurant_id").getBytes(), redisJson);
                String[] shopId = thirdPartyOrder.optJSONObject("data").optString("tp_restaurant_id").split("@");
                this.storeId = shopId[0];
                this.tenantId = shopId[1];
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @SuppressWarnings({"static-access", "deprecation"})
    @Override
    public JSONObject saveOrderList() throws Exception {

        JSONObject orderListParam = new JSONObject();
        //默认计算方式(平台结算)
        orderListParam.put("settlement_type", checkMode.getSettlement_type());
        // 商户ID
        orderListParam.put("tenancy_id", tenantId);
        // 商户id
        orderListParam.put("store_id", storeId);
        // 订单号
        orderListParam.put("order_code", orderCode);


        // 饿了么订单编号
        orderListParam.put("third_order_code", requestOrder.optString("order_id"));
        // 餐厅当日订单序号
        orderListParam.put("chanel_serial_number", requestOrder.optString("restaurant_number"));

        // 送餐时间
        if (requestOrder.containsKey("deliver_time")) {
            if (CommonUtil.checkStringIsNotEmpty(requestOrder.optString("deliver_time"))) {
                orderListParam.put("send_time", requestOrder.optString("deliver_time"));
            } else {
                orderListParam.put("send_time", "立即配送");
            }

        }
        //红包金额
        double hongbao = Double.isNaN(requestOrder.optDouble("hongbao")) ? 0.00 : requestOrder.optDouble("hongbao");

        //平台方承担的金额
        platform_side_discount_fee = Double.isNaN(requestOrder.getDouble("eleme_part")) ? 0.00 : requestOrder.getDouble("eleme_part");

        //配送费(2),打包费(102),优惠券(3),饿了么红包(13)
        JSONObject orderDetail = requestOrder.getJSONObject("detail");
        JSONArray extras = orderDetail.getJSONArray("extra");
        for (int i = 0; i < extras.size(); i++) {
            if (!extras.getJSONObject(i).optString("category_id").equals("2") && !extras.getJSONObject(i).optString("category_id").equals("102")) {
                discount_fee -= extras.getJSONObject(i).optDouble("price");
            }
            if (extras.getJSONObject(i).optString("category_id").equals("2")) {
                // 配送费
                send_fee = extras.getJSONObject(i).optDouble("price");
            }

        }


        orderListParam.put("discount_amount", Double.isNaN(discount_fee) ? 0.0 : discount_fee);


        // 订单总价
        double total_fee = Double.isNaN(requestOrder.optDouble("original_price")) ? 0.00 : requestOrder.optDouble("original_price");
        orderListParam.put("total_money", total_fee);

        //用户实付
        double actualPay = shopFee = Double.isNaN(requestOrder.optDouble("total_price")) ? 0.00 : requestOrder.optDouble("total_price");


        takeawayBusinessIncome = Scm.padd(Scm.psub(total_fee - hongbao - discount_fee, send_fee), platform_side_discount_fee);
        product_amount = total_fee - send_fee;//菜品明细加餐盒费和（订单总额-配送费=）

        // 原始价格（优惠前的价格，即菜价加上配送费和打包费，单位：元）
        //orderListParam.put("original_price", requestOrder.optString("original_price"));

        //配送类型（饿了么平台；自配送）
        JSONObject params = new JSONObject();
        params.put("restaurant_id", requestOrder.optString("restaurant_id"));
        params.put("source", consumer_key);
        params.put("secret", consumer_secret);
//        JSONObject response = thirdPartyManager.getProductProfile(params);
//        JSONObject productData = response.getJSONObject("data").getJSONObject("restaurant_product_profile");
        productProfileName ="饿了么-蜂鸟专送";
        double shopDeliveryFee = send_fee;
        if (CommonUtil.checkStringIsNotEmpty(productProfileName) && productProfileName.equals("饿了么-蜂鸟专送")) {//平台配送
            orderListParam.put("delivery_party", "1");
            shopFee = actualPay - send_fee;
            shopDeliveryFee = 0;
        } else {
            orderListParam.put("delivery_party", "2");
        }
        // 用户实付总价
        orderListParam.put("actual_pay", actualPay);
        // 商户实收总价
        orderListParam.put("shop_fee", shopFee);
        //配送费
        orderListParam.put("meal_costs", send_fee);
        //商家承担的配送费金额
        orderListParam.put("shop_delivery_fee", shopDeliveryFee);


        //----------------------------------------
        if (checkMode.getSettlement_type().equals("RIDER")) {
            double tempAmount = Double.valueOf(shopFee * (100 - Double.valueOf(checkMode.getDiscount_rate())) / 100);
            BigDecimal bg = new BigDecimal(tempAmount);
            discountR_amount = Double.isNaN(bg.setScale(4, RoundingMode.HALF_UP).doubleValue()) ? 0.0 : bg.setScale(4, RoundingMode.HALF_UP).doubleValue();
        }
        //----------------------------------------

        // 支付类型
        String payType = requestOrder.optString("is_online_paid");
        String isOnlinePayment = null;
        if (StringUtils.equals("1", payType)) {
            isOnlinePayment = "1";
        } else if (StringUtils.equals("0", payType)) {
            isOnlinePayment = "0";
        }
        String payment_state = null;
        if (StringUtils.equals("0", payType)) {
            payment_state = "01";
        } else if (StringUtils.equals("1", payType)) {
            payment_state = "03";
        }
        orderListParam.put("is_online_payment", isOnlinePayment);
        orderListParam.put("payment_state", payment_state);


        // 饿了么内部餐厅ID
        // orderListParam.put("inner_id", requestOrder.optString("inner_id"));


        //-----------------------------------------------

        // 用户id
        // orderListParam.put("user_id", requestOrder.optString("user_id"));
        // 用户名
        //orderListParam.put("user_name", requestOrder.optString("user_name"));


        // 是否预订单
        orderListParam.put("send_immediately", requestOrder.optString("is_book"));
        // 是否在线支付
        orderListParam.put("is_online_payment", requestOrder.optString("is_online_paid"));


        // 是否需要发票
        orderListParam.put("need_invoice", CommonUtil.checkStringIsNotEmpty(requestOrder.optString("invoiced")) ? requestOrder.optString("invoiced") : "0");
        // 发票抬头
        orderListParam.put("invoice_title", CommonUtil.checkStringIsNotEmpty(requestOrder.optString("invoice")) ? requestOrder.optString("invoice") : "");
        // 订单备注
        orderListParam.put("remark", requestOrder.optString("description"));
        // 物流//饿了么返回值无此字段
        //orderListParam.put("delivery_party", requestOrder.optInt("delivery_party"));
        // 订单创建时间
        orderListParam.put("single_time", requestOrder.optString("created_at"));
        // 订单生效时间(即支付时间)
        //	orderListParam.put("active_at", requestOrder.optString("active_at"));

        // 顾客姓名
        orderListParam.put("consigner", requestOrder.optString("consignee"));
        // 顾客电话
        JSONArray phone = requestOrder.optJSONArray("phone_list");
        orderListParam.put("consigner_phone", phone.toList(phone).get(0));

        // 订餐人
        orderListParam.put("order_name", requestOrder.optString("user_name"));
        // 订餐电话
        orderListParam.put("order_phone", phone.toList(phone).get(0));
        // 顾客性别
        //orderListParam.put("sex", requestUser.optInt("gender") == 1 ? "man" : "woman");
        // 渠道
        orderListParam.put("chanel", Constant.ELE_CHANNEL);
        // 送餐地址//送餐周围详情requestOrder.optString("delivery_poi_address")
        orderListParam.put("address", requestOrder.optString("address"));

        String[] delivery_geo = requestOrder.optString("delivery_geo").split(",");
        // 送餐地址百度经度
        orderListParam.put("longitude", delivery_geo[0]);
        // 送餐地址百度纬度
        orderListParam.put("latitude", delivery_geo[1]);

        // 餐盒费
        orderListParam.put("package_box_fee", requestOrder.optString("package_fee"));


        // 订单状态
        orderListParam.put("order_state", "01");
        // 订单类型//外卖订单
        orderListParam.put("order_type", "WM02");
        // 优惠类型
        orderListParam.put("discount_mode_id", "7");
        orderListParam.put("third_order_state", "1");
        orderListParam.put("order_state_desc", "待确认");
        // 订单状态
        String orderState = requestOrder.optString("status_code");
        if (StringUtils.equals("-1", orderState)) {
            orderListParam.put("third_order_state", orderState);
            orderListParam.put("order_state_desc", "订单已取消");
        } else if (StringUtils.equals("0", orderState)) {
            orderListParam.put("third_order_state", orderState);
            orderListParam.put("order_state_desc", "订单未处理");
        } else if (StringUtils.equals("1", orderState)) {
            orderListParam.put("third_order_state", orderState);
            orderListParam.put("order_state_desc", "订单等待餐厅确认");
        } else if (StringUtils.equals("2", orderState)) {
            orderListParam.put("third_order_state", orderState);
            orderListParam.put("order_state_desc", "订单已处理");
        } else if (StringUtils.equals("9", orderState)) {
            orderListParam.put("third_order_state", orderState);
            orderListParam.put("order_state_desc", "订单已完成");
        }

        orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_list", orderListParam));
        //this.dao.insertIgnorCase(tenantId, "cc_order_list", orderListParam);
        // 保存到redis
//		JSONObject redisJson = new JSONObject();
//		redisJson.put("shop_id", requestOrder.optString("tp_restaurant_id"));
//		logger.info("饿了么存入redis里的信息" + redisJson);
//		taskRedisDao.save(requestOrder.optString("order_id").getBytes(), redisJson);
        return orderListParam;
    }

    @Override
    public void saveOrderItem() throws Exception {

        discountRate = (discount_fee + discountR_amount) / product_amount;
        double discountR_rate = discountR_amount / product_amount;
        double sendFeeRate = Scm.pdiv(Double.valueOf(send_fee), Double.valueOf(product_amount));
        food_sharing_date = takeawayBusinessIncome / product_amount;


        JSONArray group = requestOrder.optJSONObject("detail").optJSONArray("group");
        JSONArray extras = requestOrder.optJSONObject("detail").getJSONArray("extra");
        int number = 0;// 餐盒费数量
        if (!extras.isEmpty()) {
            for (int i = 0; i < extras.size(); i++) {
                if (extras.getJSONObject(i).optString("category_id").equals("102")) {
                    number += extras.getJSONObject(i).optInt("quantity");
                }
            }
        }
        JSONObject orderItemParam = null;
        int group_index = 1;
        double package_fee = Double.isNaN(requestOrder.optDouble("package_fee")) ? 0.00 : requestOrder.optDouble("package_fee");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("shop_id", storeId);
        /*因为不推送商户，所以修改餐盒数量取值方式
		Double price=Double.isNaN(getStoreBoxPrice().optDouble("price"))?0.00:getStoreBoxPrice().optDouble("price");
		String num=Scm.pdiv(package_fee, price).toString();
		String resultNum=num.substring(0,num.lastIndexOf(".") );
		int number = Integer.parseInt(resultNum);// 餐盒数量*/
//		double product_send_fee_amount = 0.0;// 商品总配送费
        double share_product_price_total = 0.0; // 总共已经摊多少钱
        String item_ids = "";
        for (int m = 0; m < group.size(); m++) {
            JSONArray orderDetails = group.getJSONArray(m);
            for (int i = 0; i < orderDetails.size(); i++) {
                JSONObject groupDetail = orderDetails.getJSONObject(i);
                JSONArray garnishs = groupDetail.getJSONArray("garnish");

                if (garnishs.size() > 0) {
                    for (int j = 0; j < garnishs.size(); j++) {
                        product_org_total_fee += garnishs.getJSONObject(j).optDouble("price") * garnishs.getJSONObject(j).optInt("quantity");
                        item_ids += String.valueOf(garnishs.getJSONObject(j).optInt("tp_food_id")) + ",";
                    }
                    orderDetails.addAll(garnishs);
                } else {
                    item_ids += String.valueOf(groupDetail.optInt("tp_food_id")) + ",";
                }

            }
        }
        String query_sql = "SELECT item_code,is_charge_commission from cc_third_item_info a where cast(a.item_code as INT) in (" + item_ids.substring(0, item_ids.length() - 1) + ") and channel='EL09' and shop_id=" + storeId + " ";
        List<JSONObject> third_item_info_list = this.dao.query4Json(tenantId, query_sql);


        Map<String, String> third_item_info_list_map = new HashMap<String, String>();

        if (third_item_info_list.size() > 0) {
            for (JSONObject third_item_obj : third_item_info_list) {
                third_item_info_list_map.put(String.valueOf(third_item_obj.optInt("item_code")), third_item_obj.optString("is_charge_commission"));
            }
        }
        Map<Integer, Integer> unitMap = new HashMap<Integer, Integer>();
        String unit_query_sql = "select item_id,id,unit_name from hq_item_unit where item_id in (" + item_ids.substring(0, item_ids.length() - 1) + ")";
        List<JSONObject> item_unit_list = this.dao.query4Json(tenantId, unit_query_sql);
        if (item_unit_list.size() > 0) {
            for (JSONObject item_unit_obj : item_unit_list) {
                if (!unitMap.containsKey(item_unit_obj.optInt("item_id"))) {
                    unitMap.put(item_unit_obj.optInt("item_id"), item_unit_obj.optInt("id"));
                }

            }
        }
        //int productQuantity =0;
        for (int n = 0; n < group.size(); n++) {
            JSONArray orderDetails = group.getJSONArray(n);
            int productsListSize = orderDetails.size();
            for (int i = 0; i < productsListSize; i++) {
                int productQuantity = orderDetails.getJSONObject(i).optInt("quantity");

                orderItemParam = new JSONObject();
                // 商户ID
                orderItemParam.put("tenancy_id", tenantId);
                orderItemParam.put("store_id", storeId);
                // 明细索引
                orderItemParam.put("group_index", group_index++);
                // 订单号
                orderItemParam.put("order_code", orderCode);

                // 商品ID
                // String itemId =
                // requestProductsArray.getJSONObject(i).optString("product_id");
                JSONObject item_id_obj = new JSONObject();
                item_id_obj.put("itemId", orderDetails.getJSONObject(i).optString("tp_food_id"));
                String itemId = String.valueOf(item_id_obj.optInt("itemId"));
                orderItemParam.put("is_commission", CommonUtil.checkStringIsNotEmpty(third_item_info_list_map.get(itemId)) ? third_item_info_list_map.get(itemId) : "0");
                orderItemParam.put("item_id", itemId);

                // 商品名称
                String itemName = orderDetails.getJSONObject(i).optString("name");
                orderItemParam.put("item_name", itemName);
                //查询餐普ID
                String item_menu_id_sql = "SELECT c.item_menu_id from hq_item_menu_details a  left join hq_item_menu_class d on a.id=d.details_id LEFT JOIN  hq_item_menu  b on b.id=a.item_menu_id LEFT JOIN hq_item_menu_organ c on c.item_menu_id=b.id where c.store_id=" + storeId + " and a.item_id="
                        + itemId + " and d.chanel='EL09'";
                List<JSONObject> item_menu_id_list = this.dao.query4Json(tenantId, item_menu_id_sql);
                if (item_menu_id_list.size() > 0) {
                    orderItemParam.put("item_menu_id", item_menu_id_list.get(0).optInt("item_menu_id"));
                }


                Integer unit_id = unitMap.get(Integer.parseInt(itemId));
                orderItemParam.put("unit_id", unit_id);
                // 份数
                orderItemParam.put("number", productQuantity);
                // 商品价格
                orderItemParam.put("price", Double.isNaN(orderDetails.getJSONObject(i).optDouble("price")) ? 0.00 : orderDetails.getJSONObject(i).optDouble("price"));
                // 商品总价
                double product_fee = Scm.pmui(Double.isNaN(orderDetails.getJSONObject(i).optDouble("price")) ? 0.00 : orderDetails.getJSONObject(i).optDouble("price"), Double.isNaN(orderDetails.getJSONObject(i).optDouble("quantity")) ? 0.00 : orderDetails.getJSONObject(i).optDouble("quantity"));
                if (CommonUtil.checkStringIsNotEmpty(orderItemParam.optString("is_commission"))) {
                    if (orderItemParam.optString("is_commission").equals("1")) {
                        commission_dish_total += product_fee;
                    }
                }
                total_product_fee += product_fee;
                orderItemParam.put("product_fee", Double.isNaN(product_fee) ? 0.0 : product_fee);
                StringBuilder item_price_sql = new StringBuilder();
                item_price_sql.append("select c.id,c.unit_name,a.price from hq_item_pricesystem a left join organ b on cast(a.price_system as varchar) = b.price_system left JOIN hq_item_unit c on a.item_unit_id = c.id ");
                item_price_sql.append("where c.item_id='" + itemId + "' and b.id = '" + storeId + "' and a.chanel = 'EL09' and c.valid_state = '1'");
                List<JSONObject> item_price_list = this.dao.query4Json(tenantId, item_price_sql.toString());
                Double yuan_product_fee = 0.0d;
                if (item_price_list.size() > 0) {
                    yuan_product_fee = Scm.pmui(Double.isNaN(item_price_list.get(0).optDouble("price")) ? 0.00 : item_price_list.get(0).optDouble("price"), productQuantity * 1.0);
                } else {
                    yuan_product_fee = Double.valueOf(product_fee);
                }

                // 优惠金额
//			double discount_amount = Scm.pmui(product_fee, discountRate);
                double discount_amount = product_fee * discountRate;
                double discountk_amount = product_fee * discountR_rate;
//			comboDiscountRate = Scm.pdiv(discount_amount, Double.valueOf(product_fee));
                // 单品配送费
//			double send_fee_amount = Scm.pmui(Double.valueOf(product_fee), sendFeeRate);
//			product_send_fee_amount += send_fee_amount;
                share_product_price_total += Scm.pmui(product_fee, food_sharing_date);

                orderItemParam.put("discount_amount", Double.isNaN(discount_amount) ? 0.0 : discount_amount);
                orderItemParam.put("discountk_amount", Double.isNaN(discountk_amount) ? 0.0 : discountk_amount);
                //orderItemParam.put("costs", Double.isNaN(send_fee_amount)?0.0:send_fee_amount);
                // 实收金额
                double real_amount = Scm.psub(product_fee, discount_amount);
                //double costs_real_amount = Scm.padd(real_amount, send_fee_amount);
                orderItemParam.put("real_amount", Double.isNaN(real_amount) ? 0.0 : real_amount);
                // orderItemParam.put("share_amount", costs_real_amount);
                if (share_product_price_total > takeawayBusinessIncome) {
                    share_product_price_total = share_product_price_total - Scm.pmui(yuan_product_fee, food_sharing_date) + Scm.pmui(yuan_product_fee, food_sharing_date * Double.valueOf("0.6"));
                }
                orderItemParam.put("share_amount", Double.isNaN(Scm.pmui(product_fee, food_sharing_date)) ? 0.0 : Scm.pmui(product_fee, food_sharing_date));

                // 优惠价格
                // orderItemParam.put("discount_price", Scm.pdiv(costs_real_amount,
                // Double.valueOf(productAmount)));
                // 优惠方式
//			if (discount_fee != 0)
//			{
                // 优惠类型
                orderItemParam.put("discount_mode_id", "7");
                orderItemParam.put("store_id", storeId);
//			}
                orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_item", orderItemParam));
                //this.dao.insertIgnorCase(tenantId, "cc_order_item", orderItemParam);
            }
        }
        // 保存餐盒费信息
        if (number > 0) {
            JSONObject packageBoxFee = new JSONObject();
            packageBoxFee.put("tenancy_id", tenantId);
            packageBoxFee.put("store_id", storeId);
            packageBoxFee.put("group_index", group_index);
            packageBoxFee.put("order_code", orderCode);
            packageBoxFee.put("price", Scm.pdiv(package_fee, Double.valueOf(number)));
            packageBoxFee.put("number", number);

            String sql = "SELECT B.item_id,B.unit_id,C.item_name,d.item_menu_id FROM cc_meals_info A " + " LEFT JOIN cc_meals_info_default b ON A . ID = b.meals_id LEFT JOIN hq_item_info C ON C . ID = b.item_id LEFT JOIN hq_item_menu_details d on d.item_id=b.item_id"
                    + " LEFT JOIN hq_item_menu_class f on f.details_id=d.id  LEFT JOIN hq_item_menu_organ e on e.item_menu_id=d.item_menu_id" + " WHERE A .store_id = " + storeId + " AND A .channel = 'EL09' and f.chanel='EL09' AND A .meals_type = 'MR03' and e.store_id=" + storeId + " ";
            JSONObject pb =null;
            if(this.dao.query4Json(tenantId, sql).size()>0){
            	pb=this.dao.query4Json(tenantId, sql).get(0);
                packageBoxFee.putAll(pb);
            }
            	
            packageBoxFee.put("product_fee", Double.isNaN(package_fee) ? 0.0 : package_fee);
            // 优惠金额
//		double packageBox_discount_amount = Scm.psub(Double.valueOf(discount_fee), product_discount_amount);
//		packageBoxFee.put("discount_amount", packageBox_discount_amount);
            // 菜品摊的配送费
//		double packageBox_send_fee_amount = Scm.psub(Double.valueOf(send_fee), product_send_fee_amount);
//		packageBoxFee.put("costs", packageBox_send_fee_amount);
            packageBoxFee.put("discount_amount", Double.isNaN(package_fee * discountRate) ? 0.0 : package_fee * discountRate);
            packageBoxFee.put("discountk_amount", Double.isNaN(package_fee * discountR_rate) ? 0.0 : package_fee * discountR_rate);
            // 实收金额
            packageBoxFee.put("real_amount", Double.isNaN(Scm.qsub(package_fee, packageBoxFee.optDouble("discount_amount"))) ? 0.0 : Scm.qsub(package_fee, packageBoxFee.optDouble("discount_amount")));

            packageBoxFee.put("share_amount", Double.isNaN(package_fee * food_sharing_date) ? 0.0 : package_fee * food_sharing_date);


            packageBoxFee.put("is_commission", "1");

            // 优惠方式
//		if (discount_fee != 0)
//		{
            // 优惠类型
            packageBoxFee.put("discount_mode_id", "7");
            packageBoxFee.put("store_id", storeId);
//		}

            orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_item", packageBoxFee));
            //this.dao.insertIgnorCase(tenantId, "cc_order_item", packageBoxFee);
        }
    }

    @Override
    public void saveOrderRepayment() throws Exception {
        JSONObject payment = new JSONObject();
		payment.put("tenancy_id", tenantId);
		payment.put("remark", "ele_pay");//待修改
		payment.put("order_code", orderCode);
		payment.put("store_id", storeId);
		payment.put("pay_money", shopFee);
		payment.put("third_bill_code", requestOrder.optString("order_id"));
		payment.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
		
		String payType = requestOrder.optString("is_online_paid");
		
		String store_sql = "select remark from organ where id = " + storeId
				+ " ";
		JSONObject store_obj = this.dao.query4Json(tenantId, store_sql).get(0);
		if (store_obj.optString("remark").equals("read_from_rif")||payType.equals("1")) {
			String paySql = "SELECT a.id as payment_id FROM payment_way a LEFT JOIN payment_way_of_ogran b on a.id=b.payment_id where a.payment_class='baidu_pay' and b.organ_id="
					+ storeId;
			List list = this.dao.query4Json(tenantId, paySql);
			if (!list.isEmpty()) {
				payment.put("payment_id",
						((JSONObject) list.get(0)).optString("payment_id"));
			}
		}
        orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_repayment", payment));
    }

    @Override
    public void saveOrderDiscount() throws Exception {
        JSONArray requestDiscountArray = requestOrder.optJSONObject("detail").optJSONArray("extra");

        List<JSONObject> discountList = new ArrayList<>();

        StringBuilder sql = new StringBuilder();

        for (int i = 0; i < requestDiscountArray.size(); i++) {
            JSONObject dc = requestDiscountArray.getJSONObject(i);
            if (!dc.optString("category_id").equals("2") && !dc.optString("category_id").equals("102")) {
                sql.append("INSERT INTO cc_order_discount (tenancy_id,order_code,discount_type,activity_id,discount_desc,discount_fee,baidu_rate,shop_rate) VALUES (");

                JSONObject discountParam = new JSONObject();

                JSONObject discount = requestDiscountArray.getJSONObject(i);
                // 商户ID
                discountParam.put("tenancy_id", tenantId);
                sql.append("'" + tenantId + "',");
                // 门店ID
                //discountParam.put("store_id", storeId);
                //sql.append("'" + storeId + "',");
                // 订单号
                discountParam.put("order_code", orderCode);
                sql.append("'" + orderCode + "',");
                // 优惠类型
                String discountType = discount.optString("type");
                discountParam.put("discount_type", discountType);
                sql.append("'" + discountType + "',");
                // 活动ID
                String activityId = discount.optString("id");
                discountParam.put("activity_id", activityId);
                sql.append("'" + activityId + "',");
                // 优惠描述
                String desc = discount.optString("description");
                discountParam.put("discount_desc", desc);
                sql.append("'" + desc + "',");

                // 优惠金额
                double discountFee = Scm.pmui(discount.optDouble("price"), discount.optDouble("quantity"));
                if (discountFee < 0) {
                    discountFee = 0 - discountFee;
                }
                discountParam.put("discount_fee", discountFee);
                sql.append("'" + discountFee + "',");
                // 饿了么承担金额
                double eleMeRate = requestOrder.optDouble("eleme_part");
                double hongbao = Double.isNaN(requestOrder.optDouble("hongbao")) ? 0.0 : requestOrder.optDouble("hongbao");
                eleMeRate = eleMeRate - hongbao;//实际为饿了么平台承担+红包，因为红包是负数所以用减
                discountParam.put("baidu_rate", eleMeRate);
                sql.append("'" + eleMeRate + "',");
                //操作时间
                discountParam.put("operator_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
                sql.append("'" + DateUtil.format(new Timestamp(System.currentTimeMillis())) + "',");
                // 商户承担金额
                String shopRate = requestOrder.optString("restaurant_part");
                discountParam.put("shop_rate", shopRate);
                sql.append("'" + shopRate + "');");

                discountList.add(discountParam);
            }
        }
        if (discountList.isEmpty()) {
            return;
        }

        orderBatchSql.append(sql.toString());
        logger.info("订单号:[" + orderCode + "]===>优惠信息:" + discountList + "饿了么优惠原始参数:" + requestDiscountArray + " SQL:" + sql.toString());
//		this.dao.execute(tenantId, sql.toString());
//		this.dao.insertBatchIgnorCase(tenantId, "cc_order_discount", discountList);
    }

//    @SuppressWarnings({"static-access", "deprecation"})
//    @Override
//    public void saveCustomerInfoAndAddress() {
//        try {
//            JSONObject address = new JSONObject();
//            JSONArray phoneLists = requestOrder.optJSONArray("phone_list");
//            String[] deliverys = requestOrder.optString("delivery_geo").split(",");
//            address.put("order_phone", phoneLists.toList(phoneLists).get(0));
//            address.put("address", requestOrder.optString("address"));
//            address.put("consignee", requestOrder.optString("consignee"));
//            address.put("consignee_phone", phoneLists.toList(phoneLists).get(0));
//            address.put("longitude", deliverys[0]);
//            address.put("latitude", deliverys[1]);
//            //address.put("sex", requestUser.optInt("gender") == 1 ? "man" : "woman");
//            //顾客送餐详情地址，例如：近铁城市广场（普陀区金沙江路1518弄)
//            address.put("baidu_location", requestOrder.optString("delivery_poi_address"));
//            // 根据订餐电话判断是不是会员
//            String customer_id = placeOrderManagementService.loadCustomerByPhone(tenantId, address);
//            if (!"".equals(customer_id)) {
//                String sql = "update crm_customer_address set address='" + address.optString("address") + "',consignee='" + address.optString("consignee") + "',consignee_phone='" + address.optString("consignee_phone") + "',longitude='" + address.optString("longitude") + "',latitude='"
//                        + address.optString("latitude") + "',sex='" + address.optString("sex") + "',baidu_location='" + address.optString("address") + "' where customer_id='" + customer_id + "'";
//                this.dao.execute(tenantId, sql);
//            } else {
//                JSONObject info = new JSONObject();
//                info.put("tenancy_id", tenantId);
////				info.put("name", requestUser.optString("name"));
////				info.put("sex", requestUser.optInt("gender") == 1 ? "man" : "woman");
////				info.put("mobil", requestUser.optString("phone"));
//                info.put("add_chanel", channel);
//                info.put("add_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
//                info.put("store_id", storeId);
//                customer_id = this.dao.insertIgnorCase(tenantId, "crm_customer_info", info).toString();
//                address.put("customer_id", customer_id);
//                address.put("tenancy_id", tenantId);
//                this.dao.insertIgnorCase(tenantId, "crm_customer_address", address);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error(e);
//        }
//    }

    @Override
    public JSONObject thirdPartyResponse() {

        JSONObject body = new JSONObject();

        try {
            if (0 == err.optInt("errno")) {
                body.put("message", "ok");
            } else {
                body.putAll(err);
            }
        } catch (Exception e) {
            e.printStackTrace();

            body.put("errno", 200);
            body.put("error", "生成响应数据时失败！");
            body.put("errmsg", e);
        }
        return body;
    }

    @Override
    public JSONObject orderStatusPush(JSONObject params) {
        try {
            String status = "";
            String orderState = null;
            String new_status = params.optString("new_status");
            if (new_status.equals("-1") || new_status.equals("9")) {
                if (new_status.equals("-1")) {
                    status = ORDER_CANCEL;
                    orderState = "08";
                } else if (new_status.equals("9")) {
                    status = ORDER_COMPLETE;
                    orderState = "10";

                }
                // 读redis
              //at 20170822 改变redis的读取方式
    			//JSONObject orderIDdata = taskRedisDao.read(params.optString("eleme_order_id").getBytes());
    			String orderIDdataStr=redis.getByKey(params.optString("eleme_order_id"));
    			JSONObject orderIDdata=JSONObject.fromObject(orderIDdataStr);
    			logger.info("饿了么从redis里取到订单门店信息"+orderIDdata.toString());
    			//this.taskRedisDao.save(params.optString("eleme_order_id").getBytes(), orderIDdata);
    			logger.info("饿了么从redis里取到信息后执行");
    			//JSONObject restaurantData=taskRedisDao.read(orderIDdata.optString("restaurant_id").getBytes());
    			String restaurantDataStr=redis.getByKey(orderIDdata.optString("restaurant_id"));
    			JSONObject restaurantData=JSONObject.fromObject(restaurantDataStr);
    			//taskRedisDao.save(orderIDdata.optString("restaurant_id").getBytes(), restaurantData);
    			String[] shopId = restaurantData.optString("shop_id").split("@");
                this.storeId = shopId[0];
                this.tenantId = shopId[1];

                DBContextHolder.setTenancyid(tenantId);

                switch (status) {
                    case ORDER_CANCEL://第三方取消订单
                        JSONObject dataObj = new JSONObject();
                        dataObj.put("tenancy_id", tenantId);
                        dataObj.put("store_id", storeId);
                        dataObj.put("third_order_code", params.optString("eleme_order_id"));
                        orderManagementService.orderCancel(tenantId, dataObj);
                        break;
                    case ORDER_COMPLETE://第三方完成订单
                        JSONObject data_complete_Obj = new JSONObject();
                        data_complete_Obj.put("tenancy_id", tenantId);
                        data_complete_Obj.put("store_id", storeId);
                        data_complete_Obj.put("third_order_code", params.optString("eleme_order_id"));
                        orderManagementService.orderComplete(tenantId, data_complete_Obj);
                        break;
                    default:
                        break;
                }

                String sql = "update cc_order_list set order_state='" + orderState + "' where third_order_code='" + params.optString("eleme_order_id") + "'";
                dao.execute(tenantId, sql);

                err.put("errno", "0");
            }
        } catch (Exception e) {
            e.printStackTrace();
            err.put("errno", "1");
            err.put("error", e);
            err.put("errmsg", e.getMessage());
        }

        return thirdPartyResponse();

    }

    /**
     * 饿了么不用
     */
    @Override
    public JSONObject orderStatusGet(JSONObject params) {
        JSONObject result = new JSONObject();
        return result;
    }

    @Override
    protected void generateOrderCode() {
        String dateSerial = requestOrder.optString("created_at").replace("-", "").replace(":", "").replace(" ", "");
        int orderIndex = requestOrder.optInt("restaurant_number");
        orderCode = channel + storeId + dateSerial + CommonUtil.zeroFill(orderIndex);
    }

/*	修改餐盒数计算方式
 	private JSONObject getStoreBoxPrice(){
		JSONObject result=new JSONObject();
		String sql="SELECT B.item_id,B.price from cc_meals_info a LEFT JOIN cc_meals_info_default  b on b.meals_id=a.id where a.store_id='"+ storeId+ "' and a.channel='" + channel + "' AND A.meals_type='MR03'";
		try {
			List<JSONObject> list = this.dao.query4Json(tenantId, sql.toString());
			result=list.get(0);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}*/

    @Override
    public void calcCommission() {
        try {
            double commissionRate = 0.0;
            double discountCommission = 0.0;
            double hongbao = Double.isNaN(requestOrder.optDouble("hongbao")) ? 0.0 : requestOrder.optDouble("hongbao");
            JSONArray extras = requestOrder.getJSONObject("detail").getJSONArray("extra");
            for (int i = 0; i < extras.size(); i++) {
                if (extras.getJSONObject(i).optString("category_id").equals("13")) {
                    hongbao += Double.isNaN(extras.getJSONObject(i).optDouble("price")) ? 0.0 : extras.getJSONObject(i).optDouble("price");
                }

            }
            //面香新的计算公式
            //------------------------------------------------start
            //方法一：
            commissionRate = Double.isNaN(requestOrder.getDouble("service_rate")) ? 1 : requestOrder.getDouble("service_rate");
            discountCommission = Double.isNaN(requestOrder.getDouble("service_fee")) ? 0.0 : requestOrder.getDouble("service_fee");
            if (discountCommission > 17 && discountCommission < 27) {
                discountCommission = 4;
            }
            //方法二：
	            /*double shopRate=requestOrder.optDouble("restaurant_part");//商家承担优惠
	            double package_fee = Double.isNaN(requestOrder.optDouble("package_fee"))?0.00:requestOrder.optDouble("package_fee");//餐盒费
	            double total_fee=total_product_fee+package_fee;//全单金额
	            JSONObject commissionObj=commissionInfoSettingService.findCommissionRate(tenantId, Constant.ELE_CHANNEL, storeId);
	            commissionRate=Double.isNaN(commissionObj.optDouble("commission_rate"))?1:commissionObj.optDouble("commission_rate");
	            double caculateCommission=0.0;
	            if(discount_fee>0){//满减活动
	            	caculateCommission=(total_fee-shopRate)*commissionRate;
	            }else{//特价菜
	            	caculateCommission=(total_product_fee+package_fee)*commissionRate;
	            }
	            if(caculateCommission>17&&caculateCommission<27){
	            	discountCommission=4;
	            }else{
	            	discountCommission=caculateCommission;
	            }*/
            //------------------------------------------------end
            ele_shop_real_amount = Double.isNaN(requestOrder.optDouble("income")) ? 0.0 : requestOrder.optDouble("income");
            double eleme_part = Double.isNaN(requestOrder.optDouble("eleme_part")) ? 0.0 : requestOrder.optDouble("eleme_part");
            if (CommonUtil.checkStringIsNotEmpty(productProfileName) && productProfileName.equals("饿了么-蜂鸟专送")) {
                ele_platform_charge_amount = shopFee - ele_shop_real_amount;//shop_fee=shop_real_amount+platform_charge_amount;discountCommission-(eleme_part-hongbao)
            } else {
                ele_platform_charge_amount = discountCommission - (eleme_part - hongbao);
            }
            JSONObject paramsNew = new JSONObject();
            paramsNew.put("order_code", orderCode);
            paramsNew.put("tenentid", tenantId);
            paramsNew.put("discount_commission", discountCommission);
            paramsNew.put("commission_rate", commissionRate);
            paramsNew.put("shop_real_amount", ele_shop_real_amount);
            paramsNew.put("platform_charge_amount", ele_platform_charge_amount);
            paramsNew.put("product_org_total_fee", product_org_total_fee);
            orderDiscountCommissionReportService.updateYjxx4Order(paramsNew);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
    }
    
    
    @Override
	protected JSONObject createCustomerInfo() {
		String address=CommonUtil.replaceEvilChar(requestOrder.optString("address").trim());
		JSONArray phone=requestOrder.optJSONArray("phone_list");
		String phoneStr=(String)phone.toList(phone).get(0);
		String[] delivery_geo=requestOrder.optString("delivery_geo").split(",");
	    
		JSONObject customer=new JSONObject();
		customer.put("mobil", phone);
		customer.put("add_chanel", Constant.ELE_CHANNEL);
		customer.put("sex", "");
		
		JSONObject addresslist=new JSONObject();
		
		addresslist.put("sex", "");
		addresslist.put("province", "");
		addresslist.put("city", "");
		addresslist.put("area", "");
		addresslist.put("address", address);
		addresslist.put("consignee_phone", phoneStr);
		addresslist.put("consignee", CommonUtil.replaceEvilChar(requestOrder.optString("consignee").trim()));
		addresslist.put("longitude", delivery_geo[0] );
		addresslist.put("latitude", delivery_geo[1]);
		addresslist.put("baidu_location", "");
				
		
		customer.put("addresslist", addresslist);
		
		return customer;
	}
}
