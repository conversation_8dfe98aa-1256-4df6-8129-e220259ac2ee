package com.tzx.cc.datasync.po.springjdbc.dao.impl;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tzx.cc.datasync.bo.util.DriverUtils;
import net.sf.json.JSONNull;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.stereotype.Repository;

import com.tzx.cc.datasync.bo.dto.DataTransferDaoHelper;
import com.tzx.cc.datasync.bo.dto.PlanetVersionDataTransferDao;
import com.tzx.cc.datasync.po.springjdbc.dao.DataTransferDao;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.datasource.MultiDataSourceManager;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;

@Repository(DataTransferDao.NAME)
public class DataTransferDaoImpl extends GenericDaoImpl implements DataTransferDao
{
	private static final Logger	logger	= Logger.getLogger(DataTransferDaoImpl.class);

	@Override
	public boolean testConnect(JSONObject serviceParams,JSONObject result_flag) {
		Connection conn = null;
		PreparedStatement pstmt = null;
		try {
			conn = MultiDataSourceManager.buildOracleConnection(serviceParams.optString("db_driver"), serviceParams.optString("db_url"), serviceParams.optString("db_username"), serviceParams.optString("db_password"));
		} catch (Exception e) {
			result_flag.put("flag", false);
			result_flag.put("msg", "连接RIF数据库超时");
			logger.error(e);
			return false;
		}
		if(conn!=null) {

				try {
					String driver = DriverUtils.getDriver();
					if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
						pstmt = MultiDataSourceManager.getPstmt(conn, "select 1 from SYSIBM.SYSDUMMY1");
					} else {
						pstmt = MultiDataSourceManager.getPstmt(conn, "select 1 from dual");
					}
				ResultSet rs = pstmt.executeQuery();
			} catch (Exception e) {
				result_flag.put("flag", false);
				result_flag.put("msg", "用户名密码错误");
				logger.error(e);
				return false;
			} finally {
				MultiDataSourceManager.close(conn, pstmt, null);
			}
		}
		return true;
	}
	@Override
	public List<JSONObject> findPreTransferData(JSONObject serviceParams, String tableName) throws Exception
	{
		List<JSONObject> result =new ArrayList<JSONObject>();
		Connection conn = null;
		PreparedStatement pstmt = null;
		try
		{
            // 获取JDBC连接
//			conn = MultiDataSourceManager.buildOracleConnection("oracle.jdbc.driver.OracleDriver", "*************************************", "system", "Bohdb1516");
			conn = MultiDataSourceManager.buildOracleConnection(serviceParams.optString("db_driver"), serviceParams.optString("db_url"), serviceParams.optString("db_username"), serviceParams.optString("db_password"));

			JSONObject helperJson =new JSONObject();
			if (serviceParams.optString("db_driver").equals("com.ibm.db2.jcc.DB2Driver"))
			{
                // 获取要同步的表的操作条件
				helperJson =PlanetVersionDataTransferDao.INIT_TABLE.getJSONObject(tableName);
				
				if (null == helperJson || "".equals(helperJson))
				{
					Logger.getLogger(this.getClass()).warn("没有对表" + tableName + "从RIF到SAAS的同步操作！");
					throw new Exception("没有对表" + tableName + "从RIF到SAAS的同步操作！");
				}

				if(tableName.contains("@"))
				{
					tableName=tableName.substring(0,tableName.indexOf("@"));
				}
				
				// 注：完全可以拼完sql后替换?,就可以支持SELECTOR带参数的情况
				String fromFields = helperJson.getString(PlanetVersionDataTransferDao.FROMFIELDS);
				if (helperJson.containsKey(PlanetVersionDataTransferDao.PARAMSLIST))
				{
					String[] paramsArr = helperJson.getString(PlanetVersionDataTransferDao.PARAMSLIST).split(",");
					String[] val = new String[paramsArr.length];
					for (int i = 0; i < paramsArr.length; i++)
					{
						val[i] = serviceParams.getString(paramsArr[i]);
					}
					fromFields = replace(fromFields, val);
				}
				// 注：完全可以拼完sql后替换?,就可以支持SELECTOR带参数的情况
				
				if(helperJson.containsKey(PlanetVersionDataTransferDao.FROMTABLENAME))
				{
					tableName = "( "+helperJson.getString(PlanetVersionDataTransferDao.FROMTABLENAME)+" )";
				}
				
				StringBuffer sql = new StringBuffer(" SELECT " + fromFields + " FROM " + tableName); 

				if (helperJson.containsKey(PlanetVersionDataTransferDao.FROMTABLENICKNAME))
				{
					sql.append(" " + helperJson.getString(PlanetVersionDataTransferDao.FROMTABLENICKNAME));
				}
				
				if (helperJson.containsKey(PlanetVersionDataTransferDao.SELECTOR))
				{
					sql.append(" " + helperJson.getString(PlanetVersionDataTransferDao.SELECTOR));
				}

				List<JSONObject> dataList = new ArrayList<JSONObject>();
			    ResultSet rs = null;
			    try {
			      logger.info("同步数据  DB2 rif中的查询为" + sql.toString());
			      pstmt = MultiDataSourceManager.getPstmt(conn, sql.toString());
			      rs = pstmt.executeQuery();
			      dataList = MultiDataSourceManager.parseResultSet(rs, JSONObject.class);
			    } catch (Exception e) {
					logger.info(e);
			      e.printStackTrace();
			    } finally {
			    	MultiDataSourceManager.close(conn, pstmt, null);
			    }
			    return dataList;
			}
			else
			{
				helperJson =DataTransferDaoHelper.INIT_TABLE.getJSONObject(tableName);
				
				if (null == helperJson || "".equals(helperJson))
				{
					Logger.getLogger(this.getClass()).warn("没有对表" + tableName + "从RIF到SAAS的同步操作！");
					throw new Exception("没有对表" + tableName + "从RIF到SAAS的同步操作！");
				}

				if(tableName.contains("@"))
				{
					tableName=tableName.substring(0,tableName.indexOf("@"));
				}
				
				// 注：完全可以拼完sql后替换?,就可以支持SELECTOR带参数的情况
				String fromFields = helperJson.getString(DataTransferDaoHelper.FROMFIELDS);
				if (helperJson.containsKey(DataTransferDaoHelper.PARAMSLIST))
				{
					String[] paramsArr = helperJson.getString(DataTransferDaoHelper.PARAMSLIST).split(",");
					String[] val = new String[paramsArr.length];
					for (int i = 0; i < paramsArr.length; i++)
					{
						val[i] = serviceParams.getString(paramsArr[i]);
					}
					fromFields = replace(fromFields, val);
				}
				// 注：完全可以拼完sql后替换?,就可以支持SELECTOR带参数的情况
				
				if(helperJson.containsKey(DataTransferDaoHelper.FROMTABLENAME))
				{
					tableName = "( "+helperJson.getString(DataTransferDaoHelper.FROMTABLENAME)+" )";
				}
				
				StringBuffer sql = new StringBuffer(" SELECT " + fromFields + " FROM " + tableName); 

				if (helperJson.containsKey(DataTransferDaoHelper.FROMTABLENICKNAME))
				{
					sql.append(" " + helperJson.getString(DataTransferDaoHelper.FROMTABLENICKNAME));
				}
				
				if (helperJson.containsKey(DataTransferDaoHelper.SELECTOR))
				{
					sql.append(" " + helperJson.getString(DataTransferDaoHelper.SELECTOR));
				}

				List<JSONObject> dataList = new ArrayList<JSONObject>();
			    ResultSet rs = null;
			    try {
			      logger.info("同步数据  ORACLE rif中的查询为" + sql.toString());
			      pstmt = MultiDataSourceManager.getPstmt(conn, sql.toString());
			      rs = pstmt.executeQuery();
			      dataList = MultiDataSourceManager.parseResultSet(rs, JSONObject.class);
			    } catch (Exception e) {
			      e.printStackTrace();
					logger.error(e);
					throw new Exception(e);
			    } finally {
			    	MultiDataSourceManager.close(conn, pstmt, null);
			    }
			    return dataList;
			}

		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error(e);
			throw new Exception(e);
		}
		finally
		{
			MultiDataSourceManager.close(conn, pstmt, null);
		}
	}

    @Override
    public List<JSONObject> findRifOrgList(JSONObject serviceParams, String tableName) throws Exception {

        List<JSONObject> result =new ArrayList<JSONObject>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        try
        {
            // 获取JDBC连接
            conn = MultiDataSourceManager.buildOracleConnection(serviceParams.optString("db_driver"), serviceParams.optString("db_url"), serviceParams.optString("db_username"), serviceParams.optString("db_password"));

//            if (serviceParams.optString("db_driver").equals("com.ibm.db2.jcc.DB2Driver"))
            StringBuilder sql = new StringBuilder();

            sql.append(" select u.id,p.simplified as text from tzxuus.uus_organ u");
            sql.append(" left join tzxpl.platform_language p on p.id=u.org_name");
            sql.append(" where u.status='PLATFORM_STATUS_ENABLE' and u.id!=0");

            ResultSet rs = null;
            try {
                pstmt = MultiDataSourceManager.getPstmt(conn, sql.toString());
                rs = pstmt.executeQuery();
                result = MultiDataSourceManager.parseResultSet(rs, JSONObject.class);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                MultiDataSourceManager.close(conn, pstmt, null);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            logger.error(e);
        }
        finally
        {
            MultiDataSourceManager.close(conn, pstmt, null);
        }
        return result;
    }

    private String replace(String source, String... params) throws Exception
	{

		/*
		 * //参数个数与?号个数是否一致 int count = 0; String str = source; while
		 * (str.indexOf("?") != -1) { count++; str =
		 * str.substring(str.indexOf("?")+1); System.out.println(str); }
		 * 
		 * if(count!=params.length) { throw new Exception("参数个数与要替换的个数不相等！"); }
		 */

		StringBuilder sb = new StringBuilder();
		char[] ch = source.toCharArray();
		for (int i = 0, j = 0, len = ch.length; i < len; i++)
		{
			if (ch[i] == '?')
			{
				sb.append(params[j++]);
				continue;
			}
			sb.append(ch[i]);
		}
		return sb.toString();
	}

    
	@Override
	public List<JSONObject> findSAASData4Compare(JSONObject serviceParams, String fromTableName, String toTableName) throws Exception
	{
		JSONObject helperJson =new JSONObject();
		if (serviceParams.optString("db_driver").equals("com.ibm.db2.jcc.DB2Driver"))
		{
			helperJson =PlanetVersionDataTransferDao.INIT_TABLE.getJSONObject(fromTableName);
			if (null == helperJson || "".equals(helperJson))
			{
				Logger.getLogger(this.getClass()).warn("没有对表" + toTableName + "从RIF到SAAS的同步操作！");
				throw new Exception("没有对表" + toTableName + "从RIF到SAAS的同步操作！");
			}

			if(toTableName.contains("@"))
			{
				toTableName=toTableName.substring(0,toTableName.indexOf("@"));
			}
			String toFields = helperJson.getString(PlanetVersionDataTransferDao.TOFIELDS);
			
			StringBuffer sql = new StringBuffer(" SELECT " + toFields + " FROM " + toTableName); 
			
			if (helperJson.containsKey(PlanetVersionDataTransferDao.TOTABLENICKNAME))
			{
				sql.append(" " + helperJson.getString(PlanetVersionDataTransferDao.TOTABLENICKNAME));
			}
			
			if (helperJson.containsKey(PlanetVersionDataTransferDao.SELECTOR4TARGET))
			{
				sql.append(" " + helperJson.getString(PlanetVersionDataTransferDao.SELECTOR4TARGET));
			}
			
			//System.out.println("执行的SQL<<< "+sql.toString()+" >>>");
			//JSONObject dataObj = serviceParams.getJSONArray("data").getJSONObject(0);
			//long s=System.currentTimeMillis();
			//System.out.println("查询boh表 "+fromTableName+" 用时"+(System.currentTimeMillis()-s));
			logger.info("同步数据 saas这边的sql为");
			logger.info(sql.toString());
			return this.query4Json(serviceParams.containsKey("tenancyid") ? serviceParams.getString("tenancyid") : null, sql.toString());
		
		}
		else
		{
			helperJson = DataTransferDaoHelper.INIT_TABLE.getJSONObject(fromTableName);
			if (null == helperJson || "".equals(helperJson))
			{
				Logger.getLogger(this.getClass()).warn("没有对表" + toTableName + "从RIF到SAAS的同步操作！");
				throw new Exception("没有对表" + toTableName + "从RIF到SAAS的同步操作！");
			}

			if (toTableName.contains("@"))
			{
				toTableName = toTableName.substring(0, toTableName.indexOf("@"));
			}
			String toFields = helperJson.getString(DataTransferDaoHelper.TOFIELDS);

			StringBuffer sql = new StringBuffer(" SELECT " + toFields + " FROM " + toTableName);

			if (helperJson.containsKey(DataTransferDaoHelper.TOTABLENICKNAME))
			{
				sql.append(" " + helperJson.getString(DataTransferDaoHelper.TOTABLENICKNAME));
			}

			if (helperJson.containsKey(DataTransferDaoHelper.SELECTOR4TARGET))
			{
				sql.append(" " + helperJson.getString(DataTransferDaoHelper.SELECTOR4TARGET));
			}

			return this.query4Json(serviceParams.containsKey("tenancyid") ? serviceParams.getString("tenancyid") : null, sql.toString());

		}
		
		
	}
	
	private NumberFormat	numberFormat	= NumberFormat.getInstance();
	
	/* (non-Javadoc)
	 * @see com.tzx.cc.datasync.po.springjdbc.dao.DataTransferDao#updateBatchIgnorCaseByfakeId(java.lang.String, java.lang.String, java.util.List, java.lang.String[])
	 */
	public int[] updateBatchIgnorCaseByfakeId(String tenantId, String tableName, final List<JSONObject> jsonList,String ... ignoreColumns) throws Exception
	{
		return updateBatchIgnorCaseByfakeId(tenantId, null, tableName, jsonList, ignoreColumns);
	}

	/* (non-Javadoc)
	 * @see com.tzx.cc.datasync.po.springjdbc.dao.DataTransferDao#updateBatchIgnorCaseByfakeIdAndFakeType(java.lang.String, java.lang.String, java.lang.String, java.util.List, java.lang.String[])
	 */
	@Override
	public int[] updateBatchIgnorCaseByfakeId(String tenancyId,
			String whereAnd, String tableName, final List<JSONObject> jsonList,
			String... ignoreColumns) throws Exception {
		DatabaseMetaData dbmd = null;
		String idColumn = null;
		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
		dbmd = conn.getMetaData();
		idColumn = "fake_id";
		ResultSet rs = dbmd.getColumns(null, null, tableName, null);
		if (rs != null)
		{
			StringBuilder sql = new StringBuilder("UPDATE " + tableName + " SET ");
			StringBuilder column = new StringBuilder();
			StringBuilder where = new StringBuilder();
			final Map<String, String> order = new HashMap<String, String>();
			String idkey = "@";
			f:while (rs.next())
			{
				String colname = rs.getString("COLUMN_NAME");
				
				//如果是ignoreColumn直接跳过去，这个列的字段不需要更新
				if(ignoreColumns!=null) {
					for(String ignore:ignoreColumns){
						if(StringUtils.equals(ignore, colname)) {
							continue f;
						}
					}
				}
				
				
				if (!(jsonList.get(0).containsKey(colname)))
				{
					continue;
				}

				if (colname.equals(idColumn))
				{
					where.append(colname + "=?");
					idkey = idkey + rs.getInt("DATA_TYPE");
				}
				else
				{
					column.append(colname + "=?, ");
					order.put((order.size() + 1) + "@" + rs.getInt("DATA_TYPE") + "@" + rs.getString("TYPE_NAME"), colname);
				}
			}

			idkey = (order.size() + 1) + idkey;

			order.put(idkey, idColumn);

			MultiDataSourceManager.close(conn, null, rs);

			BatchPreparedStatementSetter batchPreparedStatementSetter = new BatchPreparedStatementSetter()
			{

				@Override
				public void setValues(PreparedStatement arg0, int arg1) throws SQLException
				{
					JSONObject json = jsonList.get(arg1);

					for (String ii : order.keySet())
					{
						String columnName = order.get(ii);

						Integer index = Integer.parseInt(ii.split("@")[0]);
						Integer dataType = Integer.parseInt(ii.split("@")[1]);

						Object obj = json.get(columnName);

						if (obj == null)
						{
							obj = json.get(columnName.replace("_", ""));
						}

						if (obj instanceof JSONObject)
						{
							if (((JSONObject) obj).isEmpty() || ((JSONObject) obj).isNullObject())
							{
								obj = null;
							}
						}
						else if (obj instanceof JSONNull)
						{
							obj = null;
						}

						if (obj != null)
						{
							switch (dataType)
							{
								case Types.TIMESTAMP:
									obj = DateUtil.parseTimestamp(obj.toString());
									break;
								case Types.DATE:
									Date d = DateUtil.parseDate(obj.toString());

									if (d != null)
									{
										obj = new java.sql.Date(d.getTime());
									}
									else
									{
										obj = null;
									}
									break;
								case Types.TIME:
									obj = DateUtil.parseTime(obj.toString());
									break;
								case Types.BIT:
									if ("true".equalsIgnoreCase(obj.toString()) || "1".equals(obj.toString()))
									{
										obj = true;
									}
									else
									{
										obj = false;
									}
									break;
								case Types.NUMERIC:
								case Types.INTEGER:
								case Types.DOUBLE:
								case Types.REAL:
								case Types.FLOAT:
								case Types.BIGINT:
								case Types.SMALLINT:
									if (obj.toString().isEmpty())
									{
										obj = null;
									}
									else
									{
										try
										{
											obj = numberFormat.parse(obj.toString());
										}
										catch (Exception e)
										{
											obj = null;
										}
									}
									break;
								case Types.DISTINCT:
									if ("datetime".equals(ii.split("@")[2]))
									{
										obj = DateUtil.parseTimestamp(obj.toString());
									}
									break;
								default:
									break;
							}
						}

						arg0.setObject(index, obj);
					}
				}
				@Override
				public int getBatchSize()
				{
					return jsonList.size();
				}
			};
			if(StringUtils.isNotBlank(whereAnd)) {
				where.append(whereAnd);
			}
			sql.append(column.toString().substring(0, column.toString().lastIndexOf(", ")) + " WHERE 1=1 AND " + where.toString());
			return this.jdbcTemplate.batchUpdate(sql.toString(), batchPreparedStatementSetter);
		}
		return null;
	}
}
