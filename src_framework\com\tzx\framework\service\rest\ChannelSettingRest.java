package com.tzx.framework.service.rest;

import com.tzx.framework.bo.ChannelSettingService;
import com.tzx.framework.common.exception.ExceptionMessage;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.PrintWriter;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Created by jj on 2018-11-01.
 */
@Controller("ChannelSettingRest")
@RequestMapping("/framework/ChannelSettingRest")
public class ChannelSettingRest {
    private static final Logger logger = Logger.getLogger(ChannelSettingRest.class);

    @Resource(name = ChannelSettingService.NAME)
    private ChannelSettingService channelSettingService;

    @RequestMapping(value = "/loadChannelList")
    public void loadChannelList(HttpServletRequest request, HttpServletResponse response){
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");

        PrintWriter out = null;
        HttpSession session = request.getSession();

        List<JSONObject> list = new LinkedList<>();
        try
        {
            JSONObject condition = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for(String key : map.keySet()){
                if(map.get(key)[0] != null && !"".equals(map.get(key)[0])){
                    condition.put(key, map.get(key)[0]);
                }
            }

            list = channelSettingService.loadChannelList((String)session.getAttribute("tenentid"), condition);

        }
        catch (Exception e)
        {
            logger.error(ExceptionMessage.getExceptionMessage(e));
        }
        finally
        {
            try
            {
                out = response.getWriter();
                out.println(list.toString());
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }

    @RequestMapping(value = "/updateChannel")
    public void updateChannel(HttpServletRequest request, HttpServletResponse response){
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");

        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject returnJson = new JSONObject();
        returnJson.put("success", false);
        try
        {
            JSONObject condition = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for(String key : map.keySet()){
                if(map.get(key)[0] != null && !"".equals(map.get(key)[0])){
                    condition.put(key, map.get(key)[0]);
                }
            }
            condition.put("last_operator", session.getAttribute("employeeName"));

            channelSettingService.updateChannel((String)session.getAttribute("tenentid"), condition, returnJson);

        }
        catch (Exception e)
        {
            returnJson.put("msg", "更新渠道启用状态异常！");
            logger.error(ExceptionMessage.getExceptionMessage(e));
        }
        finally
        {
            try
            {
                out = response.getWriter();
                out.println(returnJson.toString());
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }

}
