package com.tzx.cc.invoice.electronic.service;

import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import com.tzx.cc.bo.dto.Data;

public interface ElecInvoiceService {
	final static String NAME = "com.tzx.cc.invoice.electronic.service.impl.ElecInvoiceServiceImpl";

	/**
	 * 生成电子发票
	 * 
	 * @param data
	 * @param result
	 * @throws Exception
	 */
	void issueElectronicInvoice(Data data, Data result) throws Exception;
	
	/**
	 * 自动跳转页面的
	 * @param data
	 * @param result
	 * @param response
	 * @throws Exception
	 */
	void issueElectronicInvoice(Data data, Data result,HttpServletResponse response) throws Exception;

	/**
	 * 取消电子发票
	 * 
	 * @param data
	 * @param result
	 * @throws Exception
	 */
	void cancelElectronicInvoice(Data data, Data result) throws Exception;

	/**
	 * 查询电子发票
	 * 
	 * @param data
	 * @param result
	 * @throws Exception
	 */
	void queryElectronicInvoice(Data data, Data result) throws Exception;

	
	/**
	 * 申请开票
	 * @param json
	 * @param result
	 * @throws Exception
	 */
	void sqkp(JSONObject json, JSONObject result) throws Exception;

	/**
	 * 发票开具
	 * @param json
	 * @param result
	 * @throws Exception
	 */
	void fpkj(JSONObject json, JSONObject result) throws Exception;

	void orderCallback(String xmlParam, JSONObject result) throws Exception;
}
