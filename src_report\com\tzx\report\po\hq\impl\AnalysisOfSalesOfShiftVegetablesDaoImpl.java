package com.tzx.report.po.hq.impl;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.po.hq.dao.AnalysisOfSalesOfShiftVegetablesDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

@Repository(AnalysisOfSalesOfShiftVegetablesDao.NAME)
public class AnalysisOfSalesOfShiftVegetablesDaoImpl implements AnalysisOfSalesOfShiftVegetablesDao {
 
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
    @Resource
    ConditionUtils conditionUtils;



    @Override
	public JSONObject getAnalysisOfSalesOfShiftVegetables(String tenancyID, JSONObject condition) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
        List<JSONObject> structure = new ArrayList<JSONObject>();
        List<JSONObject> footerList = new ArrayList<JSONObject>();
		long total = 0L;

		StringBuilder sql = new StringBuilder();

        String shifts = condition.optString("p_shift_id");
        String query_type_val = condition.optString("p_type");
        String store_id = condition.optString("p_store_id");
        String store_level_val = condition.optString("p_miandian");
        String exportdataexpr = condition.optString("exportdataexpr");

        String [] arr = null;
        int pagenum = 0;
        if(shifts.length() > 0) {
            arr = shifts.split(",");

            // 查询明细
            sql.append("  with ttt as (\n");
            sql.append("  with temp as (\n");
            sql.append("    select \n");
            sql.append("        row_number() over() as id\n");
            sql.append("       ,pb.item_num as item_code  -- 菜品编码\n");
            sql.append("       ,pb.item_name as item_name  -- 菜品名称\n");

            for(int i = 0; i < arr.length ; i++){
                String value = String.valueOf(Math.abs(Integer.parseInt(arr[i])));
                sql.append("       ,sum(case when pb.item_shift_id=" + arr[i] + " and pb.item_property in ('SINGLE', 'SETMEAL') then coalesce(pb.item_count, 0) else 0 end) as sale_price_" + value + " --  数量 (总销量)\n");
                sql.append("       ,sum(case when pb.item_shift_id=" + arr[i] + " and pb.item_property in ('SINGLE', 'SETMEAL') then coalesce(pb.item_amount, 0) else 0 end) as sale_amount_" + value + "  --  金额(销售总额)\n");
            }

            sql.append("       ,sum(case when pb.item_property in ('SINGLE', 'SETMEAL') then coalesce(pb.item_count, 0) else 0 end) as total_sale_price --  数量 (总销量)\n");
            sql.append("       ,sum(case when pb.item_property in ('SINGLE', 'SETMEAL') then coalesce(pb.item_amount, 0) else 0 end) as total_sale_amount --  金额(销售总额)\n");
            sql.append("    from pos_bill_item2 pb\n");
            sql.append("    left join organ o on o.id=pb.store_id\n");
            sql.append("    where 1=1 and pb.item_property in ('SINGLE', 'SETMEAL')\n");

            if(store_id.length() > 0) {
                sql.append(" and pb.store_id in (" + store_id + ")\n");
            }

            if(shifts.length() > 0) {
                sql.append(" and pb.item_shift_id in (" + shifts + ")\n");
            }

            if(store_level_val.length() > 0) {
                sql.append(" and o.storelevel in (" + store_level_val + ")\n");

            }

            if(query_type_val.length() > 0) {
                if("day".equals(query_type_val)){
                    String start_time = condition.optString("p_report_date_begin");
                    String end_time = condition.optString("p_report_date_end");
                    if(start_time.length() > 0) {
                        sql.append(" and pb.report_date>='" + start_time + "'\n");
                    }

                    if(end_time.length() > 0){
                        sql.append(" and pb.report_date<='" + end_time + "'\n");
                    }
                } else if ("week".equals(query_type_val)){
                    String week = condition.optString("p_report_date_begin");
                    //sql.append(" and date_part('year', pb.report_date)||'-'||date_part('" + query_type_val + "', pb.report_date)='" + week + "'\n");
                    String[] dateArr = week.split("-");
                    sql.append(" and date_part('year', pb.report_date)=" + dateArr[0] + "\n");
                    sql.append(" and date_part('" + query_type_val + "', pb.report_date)=" + dateArr[1] + "\n");
                } else if("month".equals(query_type_val)){
                    String month = condition.optString("p_report_date_begin");
                    String[] dateArr = month.split("-");
                    sql.append(" and date_part('year', pb.report_date)=" + dateArr[0] + "\n");
                    sql.append(" and date_part('" + query_type_val + "', pb.report_date)=" + dateArr[1] + "\n");
                    //sql.append(" and date_part('year', pb.report_date)||'-'||date_part('" + query_type_val + "', pb.report_date)='" + month + "'\n");
                } else if("quarter".equals(query_type_val)) {
                    String quarter = condition.optString("p_report_date_begin");
                    String[] dateArr = quarter.split("-");
                    sql.append(" and date_part('year', pb.report_date)=" + dateArr[0] + "\n");
                    sql.append(" and date_part('" + query_type_val + "', pb.report_date)=" + dateArr[1] + "\n");
                    //sql.append(" and date_part('year', pb.report_date)||'-'||date_part('" + query_type_val + "', pb.report_date)='" + quarter + "'\n");
                } else if("year".equals(query_type_val)) {
                    String year = condition.optString("p_report_date_begin");
                    sql.append(" and date_part('" + query_type_val + "', pb.report_date)=" + year + "\n");
                }
            }

            

            sql.append("    group by pb.item_num,pb.item_name\n");
            sql.append("    order by pb.item_num\n");
            sql.append(") select\n");
            sql.append("    t.*");
            for(int i = 0; i < arr.length ; i++){
                String value = String.valueOf(Math.abs(Integer.parseInt(arr[i])));
                sql.append("    ,case when total_sale_amount > 0 then round((t.sale_amount_" + value+ "/total_sale_amount),4)*100 else 0 end as sale_amount_proportion_"  +value + "\n");
                sql.append("    ,case when total_sale_price > 0 then round((t.sale_price_" + value + "/total_sale_price),4)*100 else 0 end as sale_price_proportion_"  + value + "\n");
            }
            sql.append(" from temp t\n");
            sql.append(" )\n");
            sql.append("select * from ttt where 1 = 1");
            
            if(exportdataexpr.length() > 0 && !"''".equals(exportdataexpr)) {
            	if(exportdataexpr.substring(0,1).equals("'")){
            		 sql.append(" and id in (" + exportdataexpr.substring(1, exportdataexpr.length()-1) + ")\n");
            	}else{
            		sql.append(" and id in (" + exportdataexpr + ")\n");
            	}
            }
            

            if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
                list=this.dao.query4Json(tenancyID,sql.toString());
                structure = conditionUtils.getSqlStructure(tenancyID,sql.toString());
            }else{
                list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,sql.toString()));
            }

            total = this.dao.countSql(tenancyID, sql.toString());

            // 查询合计
            sql.setLength(0);
            sql.append("    select \n");
            sql.append("        1 as id  -- 菜品编码\n");

            for(int i = 0; i < arr.length ; i++){
                String value = String.valueOf(Math.abs(Integer.parseInt(arr[i])));
                sql.append("       ,sum(case when pb.item_shift_id=" + arr[i] + " and pb.item_property in ('SINGLE', 'SETMEAL') then coalesce(pb.item_count, 0) else 0 end) as sale_price_" + value + " --  数量 (总销量)\n");
                sql.append("       ,sum(case when pb.item_shift_id=" + arr[i] + " and pb.item_property in ('SINGLE', 'SETMEAL') then coalesce(pb.item_amount, 0) else 0 end) as sale_amount_" + value + "  --  金额(销售总额)\n");
            }

            sql.append("       ,sum(case when pb.item_property in ('SINGLE', 'SETMEAL') then coalesce(pb.item_count, 0) else 0 end) as total_sale_price --  数量 (总销量)\n");
            sql.append("       ,sum(case when pb.item_property in ('SINGLE', 'SETMEAL') then coalesce(pb.item_amount, 0) else 0 end) as total_sale_amount  --  金额(销售总额)\n");
            sql.append("    from pos_bill_item2 pb\n");
            sql.append("    left join organ o on o.id=pb.store_id\n");
            sql.append("    where 1=1\n");

            if(store_id.length() > 0) {
                sql.append(" and pb.store_id in (" + store_id + ")\n");
            }

            if(shifts.length() > 0) {
                sql.append(" and pb.item_shift_id in (" + shifts + ")\n");
            }

            if(store_level_val.length() > 0) {
                sql.append(" and o.storelevel in (" + store_level_val + ")\n");

            }

            if(query_type_val.length() > 0) {
                if("day".equals(query_type_val)){
                    String start_time = condition.optString("p_report_date_begin");
                    String end_time = condition.optString("p_report_date_end");
                    if(start_time.length() > 0) {
                        sql.append(" and pb.report_date>='" + start_time + "'\n");
                    }

                    if(end_time.length() > 0){
                        sql.append(" and pb.report_date<='" + end_time + "'\n");
                    }
                } else if ("week".equals(query_type_val)){
                    String week = condition.optString("p_report_date_begin");
                    //sql.append(" and date_part('year', pb.report_date)||'-'||date_part('" + query_type_val + "', pb.report_date)='" + week + "'\n");
                    String[] dateArr = week.split("-");
                    sql.append(" and date_part('year', pb.report_date)=" + dateArr[0] + "\n");
                    sql.append(" and date_part('" + query_type_val + "', pb.report_date)=" + dateArr[1] + "\n");
                } else if("month".equals(query_type_val)){
                    String month = condition.optString("p_report_date_begin");
                    String[] dateArr = month.split("-");
                    sql.append(" and date_part('year', pb.report_date)=" + dateArr[0] + "\n");
                    sql.append(" and date_part('" + query_type_val + "', pb.report_date)=" + dateArr[1] + "\n");
                    //sql.append(" and date_part('year', pb.report_date)||'-'||date_part('" + query_type_val + "', pb.report_date)='" + month + "'\n");
                } else if("quarter".equals(query_type_val)) {
                    String quarter = condition.optString("p_report_date_begin");
                    String[] dateArr = quarter.split("-");
                    sql.append(" and date_part('year', pb.report_date)=" + dateArr[0] + "\n");
                    sql.append(" and date_part('" + query_type_val + "', pb.report_date)=" + dateArr[1] + "\n");
                    //sql.append(" and date_part('year', pb.report_date)||'-'||date_part('" + query_type_val + "', pb.report_date)='" + quarter + "'\n");
                } else if("year".equals(query_type_val)) {
                    String year = condition.optString("p_report_date_begin");
                    sql.append(" and date_part('" + query_type_val + "', pb.report_date)=" + year + "\n");
                }
            }

            footerList = this.dao.query4Json(tenancyID, sql.toString());
        } else {
            total = 0;
            list = new JSONArray();
        }
        pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;

		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
        result.put("footer", footerList);
        result.put("structure", structure);
		return result;
	}
}
