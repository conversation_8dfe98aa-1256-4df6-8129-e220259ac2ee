package com.tzx.cc.baidu.util;

import java.util.Iterator;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 *
 */
public class SuweiJsonUtils {
	/**
	 * 两个json可选择的进行copy键值
	 * @param src 源
	 * @param dest 目标
	 * @param names 要copy的json的key, 若names为空，则是全部copy
	 */
	@SuppressWarnings("rawtypes")
	public static void copySrc2Dest4jsonOption(JSONObject src,JSONObject dest,String ... names){
		Set keySet = src.keySet();
		Iterator iterator = keySet.iterator();
		while(iterator.hasNext()){
			String key = (String) iterator.next();
			if(names==null || names.length == 0) {
				Object value = src.opt(key);
				dest.put(key, value);
			} else {
				for(String name:names) {
					if(StringUtils.equals(name, key)) {
						Object value = src.opt(key);
						dest.put(key, value);
						break;
					}
				}
			}
		}
	}
}
