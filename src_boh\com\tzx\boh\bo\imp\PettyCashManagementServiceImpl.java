package com.tzx.boh.bo.imp;

import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.boh.bo.PettyCashManagementService;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
@Service(PettyCashManagementService.NAME)
public class PettyCashManagementServiceImpl implements PettyCashManagementService
{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	@SuppressWarnings("unchecked")
	@Override
	public JSONObject loadPettyCashManagement(String tenancyID, JSONObject condition) throws Exception
	{
		
		StringBuilder sql = new StringBuilder();
		JSONObject result = new JSONObject();
		if(!condition.get("organ_code").equals("0")&&!"".equals(condition.get("organ_code")) &&!"0".equals(condition.get("is_zb"))){ 
			sql.append("select f.* ,k.org_full_name from ( ");
			sql.append("SELECT d.*,e.totalMoney from  boh_imprest_change d,(select a.id,sum(b.money) as totalMoney from boh_imprest_change a ,");
//			sql.append(" (select id, money from boh_imprest_change where type='change' and  store_id in (select * from get_oids_byid(" + condition.get("organ_id") + "))) b where a.type='change' and a.id>=b.id group by 1)e where d.id=e.id ");
			sql.append(" (select id, money from boh_imprest_change where type='change' and store_id='" + condition.get("organ_id") + "') b where a.type='change' and a.id>=b.id group by 1)e where d.id=e.id ");
			if (condition.containsKey("t1") && condition.containsKey("t2"))
			{
				sql.append(" and a.business_date between TO_DATE('" + condition.get("t1") + "','YYYY-MM-DD') and TO_DATE('" + condition.get("t2") + "','YYYY-MM-DD') ");
			}
			
			Set<String> keys = condition.keySet();
			for(String s:keys)
			{
				if("tableName".equals(s)||"page".equals(s)||"rows".equals(s)||"sort".equals(s)||"order".equals(s)||"sortName".equals(s))
				{
					continue;
				}
				if(!"t1".equals(s)&&!"t2".equals(s)&&!"organ_code".equals(s)&&!"organ_id".equals(s)&&!"is_zb".equals(s)){
					sql.append(" and d."+s+" like '"+condition.optString(s)+"%'");
				}
				
			}
			if(condition.optString("sort")!=null && !"".equals(condition.optString("sort")))
			{
				sql.append(" order by "+condition.optString("sort")+" "+condition.optString("order"));
			}
			else
			{
				sql.append(" ORDER BY d.id ASC,tenancy_id");
			}
			sql.append(" )f ");
			sql.append(" , organ k where k.id=f.store_id");
			sql.append(" and k.organ_code LIKE '"+condition.get("organ_code")+"%' ");
//			sql.append(" and k.id in (select * from get_oids_bycode('"+condition.get("organ_code").toString().trim()+"')) ");
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
//			if(list.size()>=1){
//				  for(JSONObject result : list){
//					  JSONObject obj = JSONObject.fromObject("{}");
//					  obj.put("id", result.getString("id"));
//					  String totalMoney=gainTotalMoney(tenancyID,obj);
//					  Map<String, Object> map =new HashMap<String, Object>();
//					  map.put("totalMoney", totalMoney);
//					  result.accumulateAll(map);
//	          }
//			}
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
			
		}
		
		
		return result;
	}
	@Override
	public String gainTotalMoney(String tenentId, JSONObject param) throws Exception
	{
		String totalMoney = "0";
		try
		{

			String id = param.optString("id");
			if (Tools.hv(id))
			{
				StringBuilder sql = new StringBuilder();
				sql.append("SELECT sum(a.money) as totalmoney from boh_imprest_change a where a.type='change' and a.id <='" + id + "'");
				List<JSONObject> list = this.dao.query4Json(tenentId, this.dao.buildPageSql(param, sql.toString()));
				if (list.size() >= 1)
				{
					for (JSONObject result : list)
					{
						totalMoney = result.getString("totalmoney");
					}
				}
			}

			return totalMoney;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			return totalMoney;
		}
	}

}
