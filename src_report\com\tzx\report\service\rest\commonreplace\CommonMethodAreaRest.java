package com.tzx.report.service.rest.commonreplace;

import com.alibaba.fastjson.JSONException;
import com.tzx.framework.bo.SystemUserService;
import com.tzx.framework.bo.dto.Roles;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpClientPostUtil;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.bo.imp.commonreplace.CommonMethodAreaServiceImpl;
import com.tzx.report.common.entity.Organ;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ReportExportUtils;
import com.tzx.report.common.utils.FileToZip;
import com.tzx.report.common.utils.ReportBigDataPoiUtils;
import io.swagger.annotations.*;
import jxl.write.WriteException;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

/**
 * 报表公共方法区
 * <AUTHOR>
 *
 */

@Controller
@RequestMapping(value= "/report/CommonMethodAreaRest")
public class CommonMethodAreaRest {
	
	@Resource
	private CommonMethodAreaService commonMethodAreaService;
	
	@Resource
	private ConditionUtils conditionUtils;
	
	@Resource
	private ReportBigDataPoiUtils reportBigDataPoiUtils ;
	 
	@Autowired
	private SystemUserService systemUserService;
	
//	private static final String ScmInterfaceUrl = "http://ecsc.meishijia.com/ecsc-extension-rpc/report";
	private static final String ScmInterfaceUrl = "http://ecsc.meishijia.com/ecsc-extension-rpc/report";//测试地址
//	private static final String ScmInterfaceUrl = "http://ecsc.e7e6.net/ecsc-extension-rpc/report";//测试地址
	
	private static final Logger	 log	= Logger.getLogger(CommonMethodAreaRest.class);
	
	/**
	 * 列付款方式
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getPaymentDetails")
	public void getPaymentDetails(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			result = JsonUtils.list2json(commonMethodAreaService.getPaymentDetails((String) session.getAttribute("tenentid"), p));

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	
	
	/**
	 * 付款方式查询方式
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getPaymentMethod")
	public void getPaymentMethod(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			result = conditionUtils.getPaymentMethodTree((String) session.getAttribute("tenentid"), p).toString();

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	/**
	 * 能源抄表字段
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getEnergyConsumption")
	public void getEnergyConsumption(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
				if(p.optString("store_ids").length()==0){
					p.element("store_ids", session.getAttribute("user_organ_codes_group"));
				}
			}else{
				if(p.optString("store_ids").length()==0){
					p.element("store_ids", session.getAttribute("user_organ"));
				}
			}
			
			result = JsonUtils.list2json(commonMethodAreaService.getEnergyConsumption((String) session.getAttribute("tenentid"), p));

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	/**
	 * 报表说明
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	
	@RequestMapping(value = "/getExplain")
	public void getExplain(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result = JsonUtils.list2json(commonMethodAreaService.getExplain((String) session.getAttribute("tenentid"), p));
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	
	
	
	
	/**
	 * 班次
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getloadDutyOrderNew")
	public void getloadDutyOrderNew(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();
			
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
				if(obj.optString("store_id").length()==0){
					obj.element("store_id", session.getAttribute("user_organ_codes_group"));
				}
			}else{
				if(obj.optString("store_id").length()==0){
					obj.element("store_id", session.getAttribute("user_organ"));
				}
			}
			
			result = conditionUtils.loadDutyOrderNew((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\":false,\"msg\":\"" + e.getMessage() + "\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}
			
			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	/**
	 * 时段
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getBusinessSummaryPeriod")
	public void getBusinessSummaryPeriod(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();
			
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("store_id",session.getAttribute("store_id"));
			result = conditionUtils.getBusinessSummaryPeriod((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\":false,\"msg\":\"" + e.getMessage() + "\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}
			
			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	
	
	/**
	  *营业区域 Tree
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
	@RequestMapping(value = "/getBusinessArea")
	public void getBusinessArea(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result = conditionUtils.getBusinessAreaTree((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	/**
	  *餐位类型
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
	@RequestMapping(value = "/getMealPositionType")
	public void getMealPositionType(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result = conditionUtils.getMealPositionTypeTree((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	/**
	  *活动主题
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
	@RequestMapping(value = "/getActivityTheme")
	public void getActivityTheme(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result = conditionUtils.getActivityTheme((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	/**
	  *明细选项
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
	@RequestMapping(value = "/getDetailOptions")
	public void getDetailOptions(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result = conditionUtils.getDetailOptions((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	  *营销主题
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
	@RequestMapping(value = "/getEventTopicDetail")
	public void getEventTopicDetail(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result = conditionUtils.getEventTopicDetail((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	/**
	  *POS菜品大类
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
	@RequestMapping(value = "/getDishesClass")
	public void getDishesClass(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result = conditionUtils.getDishesClass((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	/**
	  *菜品小类
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
	@RequestMapping(value = "/getDishesSmallClass")
	public void getDishesSmallClass(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result = conditionUtils.getDishesSmallClass((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	/**
	 * 注册门店
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getOrgansTreeActivationStore")
	public void getOrgansTreeActivationStore(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String returnJSONstring = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}

			returnJSONstring = conditionUtils.getOrganTreeAllA((String) session.getAttribute("tenentid"),p);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.println(returnJSONstring);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	/**
	 * 交易门店
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getOrgansTreeStore")
	public void getOrgansTreeStore(HttpServletRequest request,
			HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String returnJSONstring = "";
		String conditions = "";
		try {
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				p.put(key, map.get(key)[0]);
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
				conditions = (String) session.getAttribute("user_organ_codes_group");
			}else{
				conditions = (String) session.getAttribute("user_organ");
			}
			
			
			if (p.containsKey("clearing_store_id") && "".equals(p.optString("clearing_store_id"))) {
				p.put("clearing_store_id", (String) session.getAttribute("organ_id"));
			}
			String organ_id = "";
			if(p.containsKey("jg")){
				organ_id = "0";
			}else{
				organ_id = (String) session.getAttribute("organ_id");
			}
			returnJSONstring = conditionUtils.getOrgansTreeByConditios(
					(String) session.getAttribute("tenentid"),
					organ_id,
					(String) session.getAttribute("organ_code"), p,conditions);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				out = response.getWriter();
				out.println(returnJSONstring);
				out.flush();
				out.close();
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
	
	
	/**
	  *操作类型（‘充值’,'反充值'）
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
	@RequestMapping(value = "/getOperationType")
	public void getOperationType(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			result = conditionUtils.getOperationType((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	/**
	  *操作类型（积分）
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
	@RequestMapping(value = "/getIntegralOperationType")
	public void getIntegralOperationType(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			result = conditionUtils.getIntegralOperationType((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	/**
	  *收银人员
	  * @param request
	  * @param response
	  * @throws IOException
	  * @throws WriteException
	  */
		@RequestMapping(value = "/getLoadCashierNum")
		public void getLoadCashierNum(HttpServletRequest request, HttpServletResponse response)
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					if (!"".equals(map.get(key)[0]))
					{
						p.put(key, map.get(key)[0]);
					}
				}
				
					if(p.optString("organ_id").length()==0){
						if( session.getAttribute("organ_id").equals("0")) {
							p.element("organ_id", session.getAttribute("user_organ_codes_group"));
						}else {
							p.element("organ_id", session.getAttribute("organ_id"));
						}
					}
				 
				result = conditionUtils.loadCashierNum((String) session.getAttribute("tenentid"), p).toString();

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"查询会员等级时发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		
		
		
		/**
		 *  菜品类别 xlbh
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getDishesType")
		public void getDishesType(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result = conditionUtils.getDishesTypeCommon((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		
		
		
		
		/**
		 * 菜品名称   xlbh
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		
		@RequestMapping(value = "/getDishesName")
		public void getDishesName(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result = conditionUtils.getDishesNameCommon((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		
		
		
		
		/**
		  *营业区域 combox
		  * @param request
		  * @param response
		  * @throws IOException
		  * @throws WriteException
		  */
		
		@RequestMapping(value = "/getBusinessAreaCombox")
		public void getBusinessAreaCombox(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result = conditionUtils.getBusinessAreaCommon((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		
		
		
		/**
		 * 餐位类型combox 
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getMealPositionTypeCombox")
		public void getMealPositionTypeCombox(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result = conditionUtils.getMealPositionTypeCommon((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		
		
		//导出
		@RequestMapping(value = "/exportDate")
		public void exportDate(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			HttpSession session = request.getSession();
			HSSFWorkbook workBook = null;
			String exportName = null;
			try
			{

				workBook = new HSSFWorkbook();
			       
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
					if(p.optString("p_store_id").length()==0){
						p.element("p_store_id", "'"+session.getAttribute("user_organ_codes_group")+"'");
					}	
				}else{
					if(p.optString("p_store_id").length()==0){
						p.element("p_store_id", "'"+session.getAttribute("user_organ")+"'");
					}
				}
				
				exportName = p.optString("exportName");
				
				workBook = commonMethodAreaService.exportDate((String) session.getAttribute("tenentid"), p ,workBook);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			try
			{
				ReportExportUtils.download(workBook,response,exportName);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		
		
		
		/**
		  *订单状态
		  * @param request
		  * @param response
		  * @throws IOException
		  * @throws WriteException
		  */
		@RequestMapping(value = "/getOrderStatusTree")
		public void getOrderStatusTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result = conditionUtils.getOrderStatusTree((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		/**
		  *付款状态
		  * @param request
		  * @param response
		  * @throws IOException
		  * @throws WriteException
		  */
		@RequestMapping(value = "/getPaymentStatusTree")
		public void getPaymentStatusTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result = conditionUtils.getPaymentStatusTree((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		/**
		  *预定类型
		  * @param request
		  * @param response
		  * @throws IOException
		  * @throws WriteException
		  */
		@RequestMapping(value = "/getPredeterminedTypeTree")
		public void getPredeterminedTypeTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result = conditionUtils.getPredeterminedTypeTree((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		/**
		  *订单来源
		  * @param request
		  * @param response
		  * @throws IOException
		  * @throws WriteException
		  */
		@RequestMapping(value = "/getOrderSourceTree")
		public void getOrderSourceTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result = conditionUtils.getOrderSourceTree((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		/**
		  *订单来源
		  * @param request
		  * @param response
		  * @throws IOException
		  * @throws WriteException
		  */
		@RequestMapping(value = "/getOrderSourceTrees")
		public void getOrderSourceTrees(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result = conditionUtils.getOrderSourceTrees((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}

		/**
		 * 列付款方式按照付款类型排序
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getPaymentDetailsOrderByClass")
		public void getPaymentDetailsOrderByClass(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				result = JsonUtils.list2json(commonMethodAreaService.getPaymentDetailsOrderByClass((String) session.getAttribute("tenentid"), p));

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		
		
		
		/**
		 * 指标类型
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getIndexAllocation")
		public void getIndexAllocation(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				result = JsonUtils.list2json(conditionUtils.getIndexAllocation((String) session.getAttribute("tenentid"), p));

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		
		/**
		 * 营业桌位Tree
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getBusinessSeatTree")
		public void getBusinessSeatTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
//				if(p.optString("store_id").length()==0){
//					p.element("store_id", session.getAttribute("user_organ_codes_group"));
//				}
				
				result = conditionUtils.getBusinessSeatTree((String) session.getAttribute("tenentid"), p).toString();

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		/**
		 * pos区分正餐快餐
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getPosDinnerSnack")
		public void getPosDinnerSnack(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				if(p.containsKey("tenentid")){
					DBContextHolder.setTenancyid(p.getString("tenentid"));
				}
				
				result = commonMethodAreaService.getPosDinnerSnack((String) session.getAttribute("tenentid"), p).toString();

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		
		
		/**
		 * Tree 菜品名称
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getDishesNameTree")
		public void getDishesNameTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				result = conditionUtils.getDishesNameTree((String) session.getAttribute("tenentid"), p).toString();

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		
		
		//订单自动导出
				@RequestMapping(value = "/exportDateNewOrder")
				public void exportDateNewOrder(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{
					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					HttpSession session = request.getSession();
					HSSFWorkbook workBook = null;
					String exportName = null;
					Object[] o = null;
					try
					{
						workBook = new HSSFWorkbook();
					       
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						String session_user_organ_word = "user_organ";
						if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)||((Integer)session.getAttribute("valid_state")).equals(0))session_user_organ_word="user_organ_codes_group";
						
						if(p.optString("store_id").length()==0){
							p.element("store_id", session.getAttribute(session_user_organ_word));
						}

						if(p.optString("p_store_id").length()==0){
							p.element("p_store_id", session.getAttribute(session_user_organ_word));
						}
						if(p.optString("jy_store_id").length()==0){
							
							p.element("jy_store_id", session.getAttribute(session_user_organ_word));
						}
						if(p.optString("store_ids").length()==0){
							
							p.element("store_ids", session.getAttribute(session_user_organ_word));
						}
						// 填装当前登录人
						p.put("man", session.getAttribute("employeeName"));
						exportName = p.optString("exportName");
						
						o = commonMethodAreaService.exportDateNewOrder((String) session.getAttribute("tenentid"), p ,workBook);
					}
					catch (Exception e)
					{
						log.info("new111平台订单对账报表导出异常>>>>"+ExceptionMessage.getExceptionMessage(e));
					}
					try
					{
						if(o[1] == null){
							ReportExportUtils.download((HSSFWorkbook)(o[0]),response,exportName);
						}else{
							File f = (File)o[1];
							ReportExportUtils.downloadFile(f,response,exportName);
						}
						
					}
					catch (Exception e)
					{
						log.info("new222平台订单对账报表导出异常>>>>"+ExceptionMessage.getExceptionMessage(e));
					}
				}
				
				
				//自动导出
				@RequestMapping(value = "/exportDateNew")
				public void exportDateNew(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{
					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					HttpSession session = request.getSession();
					HSSFWorkbook workBook = null;
					String exportName = null;
					Object[] o = null;
					try
					{
						workBook = new HSSFWorkbook();
					       
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						String session_user_organ_word = "user_organ";
						if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)||((Integer)session.getAttribute("valid_state")).equals(0))session_user_organ_word="user_organ_codes_group";
						
						if(p.optString("store_id").length()==0){
							p.element("store_id", session.getAttribute(session_user_organ_word));
						}

						if(p.optString("p_store_id").length()==0){
							p.element("p_store_id", session.getAttribute(session_user_organ_word));
						}
						if(p.optString("jy_store_id").length()==0){
							
							p.element("jy_store_id", session.getAttribute(session_user_organ_word));
						}
						if(p.optString("store_ids").length()==0){
							
							p.element("store_ids", session.getAttribute(session_user_organ_word));
						}
						// 填装当前登录人
						p.put("man", session.getAttribute("employeeName"));
						exportName = p.optString("exportName");
						
						o = commonMethodAreaService.exportDateNew((String) session.getAttribute("tenentid"), p ,workBook);
					}
					catch (Exception e)
					{
						log.info("111平台订单对账报表>>>"+ ExceptionMessage.getExceptionMessage(e));
						e.printStackTrace();
					}
					try
					{
						if(o[1] == null){
							ReportExportUtils.download((HSSFWorkbook)(o[0]),response,exportName);
						}else{
							File f = (File)o[1];
							ReportExportUtils.downloadFile(f,response,exportName);
						}
						
					}
					catch (Exception e)
					{
						log.info("222平台订单对账报表>>>"+ ExceptionMessage.getExceptionMessage(e));
						e.printStackTrace();
					}
				}
				
				/**
				 * 列付款方式
				 * @param request
				 * @param response
				 * @throws IOException
				 * @throws WriteException
				 */
				@RequestMapping(value = "/getPaymentDetailsStatus2")
				public void getPaymentDetailsStStatus2(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{

					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					PrintWriter out = null;
					InputStream in = null;
					HttpSession session = request.getSession();
					String result = "";
					try
					{
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						result = JsonUtils.list2json(commonMethodAreaService.getPaymentDetailsStatus2((String) session.getAttribute("tenentid"), p));

					}
					catch (Exception e)
					{
						result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
						e.printStackTrace();
					}
					finally
					{
						try
						{
							if (in != null)
							{
								in.close();
							}
						}
						catch (Exception e)
						{
						}

						try
						{
							out = response.getWriter();

							out.print(result);
							out.flush();
							out.close();
						}
						catch (Exception e)
						{
						}
						finally
						{
							if (out != null) out.close();
						}
					}

				}
				
				
				
				/**
				 * 操作人员
				 * @param request
				 * @param response
				 * @throws IOException
				 * @throws WriteException
				 */
				@RequestMapping(value = "/getRegainmanagernum")
				public void getRegainmanagernum(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{

					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					PrintWriter out = null;
					InputStream in = null;
					HttpSession session = request.getSession();
					String result = "";
					try
					{
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
							if(p.optString("store_id").length()==0){
								p.element("store_id", session.getAttribute("user_organ_codes_group"));
							}	
						}else{
							if(p.optString("store_id").length()==0){
								p.element("store_id", session.getAttribute("user_organ"));
							}
						}
						
						result = JsonUtils.list2json(conditionUtils.getRegainmanagernum((String) session.getAttribute("tenentid"), p));

					}
					catch (Exception e)
					{
						result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
						e.printStackTrace();
					}
					finally
					{
						try
						{
							if (in != null)
							{
								in.close();
							}
						}
						catch (Exception e)
						{
						}

						try
						{
							out = response.getWriter();

							out.print(result);
							out.flush();
							out.close();
						}
						catch (Exception e)
						{
						}
						finally
						{
							if (out != null) out.close();
						}
					}

				}
				
				
				
				/**
				 * 操作机号
				 * @param request
				 * @param response
				 * @throws IOException
				 * @throws WriteException
				 */
				@RequestMapping(value = "/getPosnum")
				public void getPosnum(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{

					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					PrintWriter out = null;
					InputStream in = null;
					HttpSession session = request.getSession();
					String result = "";
					try
					{
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
							if(p.optString("store_id").length()==0){
								p.element("store_id", session.getAttribute("user_organ_codes_group"));
							}
						}else{
							if(p.optString("store_id").length()==0){
								p.element("store_id", session.getAttribute("user_organ"));
							}
						}
						
						result = JsonUtils.list2json(conditionUtils.getPosnum((String) session.getAttribute("tenentid"), p));

					}
					catch (Exception e)
					{
						result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
						e.printStackTrace();
					}
					finally
					{
						try
						{
							if (in != null)
							{
								in.close();
							}
						}
						catch (Exception e)
						{
						}

						try
						{
							out = response.getWriter();

							out.print(result);
							out.flush();
							out.close();
						}
						catch (Exception e)
						{
						}
						finally
						{
							if (out != null) out.close();
						}
					}

				}
				
				
				/**
				 * 多层级通用机构
				 * @param request
				 * @param response
				 * @throws IOException
				 * @throws WriteException
				 */
				@RequestMapping(value = "/getMultiLevelGeneralAgency")
				public void getMultiLevelGeneralAgency(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{

					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					PrintWriter out = null;
					InputStream in = null;
					HttpSession session = request.getSession();
					String result = "";
					try
					{
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						result = JsonUtils.list2json(conditionUtils.getMultiLevelGeneralAgency((String) session.getAttribute("tenentid"), p));

					}
					catch (Exception e)
					{
						result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
						e.printStackTrace();
					}
					finally
					{
						try
						{
							if (in != null)
							{
								in.close();
							}
						}
						catch (Exception e)
						{
						}

						try
						{
							out = response.getWriter();

							out.print(result);
							out.flush();
							out.close();
						}
						catch (Exception e)
						{
						}
						finally
						{
							if (out != null) out.close();
						}
					}

				}
				
				
				
				/**
				 * 店面级别
				 * @param request
				 * @param response
				 * @throws IOException
				 * @throws WriteException
				 */
				@RequestMapping(value = "/getStoreLevel")
				public void getStoreLevel(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{

					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					PrintWriter out = null;
					InputStream in = null;
					HttpSession session = request.getSession();
					String result = "";
					try
					{
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						result = JsonUtils.list2json(conditionUtils.getStoreLevel((String) session.getAttribute("tenentid"), p));
					}
					catch (Exception e)
					{
						result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
						e.printStackTrace();
					}
					finally
					{
						try
						{
							if (in != null)
							{
								in.close();
							}
						}
						catch (Exception e)
						{
						}

						try
						{
							out = response.getWriter();

							out.print(result);
							out.flush();
							out.close();
						}
						catch (Exception e)
						{
						}
						finally
						{
							if (out != null) out.close();
						}
					}

				}
				
				/**
				 * 地区级别
				 * @param request
				 * @param response
				 * @throws IOException
				 * @throws WriteException
				 */
				@RequestMapping(value = "/getAreaLevel")
				public void getAreaLevel(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{

					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					PrintWriter out = null;
					InputStream in = null;
					HttpSession session = request.getSession();
					String result = "";
					try
					{
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						result = JsonUtils.list2json(conditionUtils.getAreaLevel((String) session.getAttribute("tenentid"), p));
					}
					catch (Exception e)
					{
						result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
						e.printStackTrace();
					}
					finally
					{
						try
						{
							if (in != null)
							{
								in.close();
							}
						}
						catch (Exception e)
						{
						}

						try
						{
							out = response.getWriter();

							out.print(result);
							out.flush();
							out.close();
						}
						catch (Exception e)
						{
						}
						finally
						{
							if (out != null) out.close();
						}
					}

				}
				
				/**
				 * 经营方式
				 * @param request
				 * @param response
				 * @throws IOException
				 * @throws WriteException
				 */
				@RequestMapping(value = "/getBusinessMode")
				public void getBusinessMode(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{

					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					PrintWriter out = null;
					InputStream in = null;
					HttpSession session = request.getSession();
					String result = "";
					try
					{
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						result = JsonUtils.list2json(conditionUtils.getBusinessMode((String) session.getAttribute("tenentid"), p));
					}
					catch (Exception e)
					{
						result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
						e.printStackTrace();
					}
					finally
					{
						try
						{
							if (in != null)
							{
								in.close();
							}
						}
						catch (Exception e)
						{
						}

						try
						{
							out = response.getWriter();

							out.print(result);
							out.flush();
							out.close();
						}
						catch (Exception e)
						{
						}
						finally
						{
							if (out != null) out.close();
						}
					}

				}
				
				/**
				 * 税率查询
				 * @param request
				 * @param response
				 * @throws IOException
				 * @throws WriteException
				 */
				@RequestMapping(value = "/getTaxRate")
			    @ApiOperation(value = "开关控制接口",consumes= "multipart/form-data" ,httpMethod="POST",notes = "开关控制接口")
				@ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenentid",value = "商户号"),
											
				})
			    @ApiResponses(value = {  
				          @ApiResponse(code = 400, message = "No Name Provided")
				  })
				
				public void getTaxRate(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
				{
					response.setContentType("text/html; charset=UTF-8");
					response.setContentType("text/html");
					response.setCharacterEncoding("UTF-8");
					PrintWriter out = null;
					InputStream in = null;
					HttpSession session = request.getSession();
					String result = "";
					try
					{
						JSONObject p = JSONObject.fromObject("{}");

						Map<String, String[]> map = request.getParameterMap();

						for (String key : map.keySet())
						{
							p.put(key, map.get(key)[0]);
						}
						
						try{
							if(p.containsKey("tenentid")){
								DBContextHolder.setTenancyid(p.optString("tenentid"));
							}
						}catch(Exception e){
							e.printStackTrace();
						}
						
						
						p.put("store_id", (String) session.getAttribute("organ_id"));
						result = JsonUtils.list2json(commonMethodAreaService.getTaxRate((String) session.getAttribute("tenentid"), p));
					}
					catch (Exception e)
					{
						result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
						e.printStackTrace();
					}
					finally
					{
						try
						{
							if (in != null)
							{
								in.close();
							}
						}
						catch (Exception e)
						{
						}

						try
						{
							out = response.getWriter();

							out.print(result);
							out.flush();
							out.close();
						}
						catch (Exception e)
						{
						}
						finally
						{
							if (out != null) out.close();
						}
					}

				}
				
				
			//税率修改
			@RequestMapping(value="/save",method = RequestMethod.POST)
			@ResponseBody
			public JSONObject saveTaxRateModification(HttpServletRequest request, HttpServletResponse response,@RequestBody JSONObject array) throws Exception{
				JSONObject result = new JSONObject();
				HttpSession session = request.getSession();
				String tenancyId = (String) session.getAttribute("tenentid");
				DBContextHolder.setTenancyid(tenancyId); // 线程安全
				List<JSONObject> list = (List) array.get("array");
				String tableName = array.getString("tableName");
		        try {  
		            result = (JSONObject) commonMethodAreaService.saveTaxRateModification((String) session.getAttribute("tenentid"),tableName,list);
		        } catch (JSONException e) {  
		            e.printStackTrace();  
		        }  
				return result;
			}
			
			
			/**
			 * Tree 菜品类别 级联-菜品名称
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getDishesTypeTree")
			public void getDishesTypeTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					result = conditionUtils.getDishesTypeTree((String) session.getAttribute("tenentid"), p).toString();

				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}

			}
			
			
			
			/**
			 * Tree 菜品名称 级联-菜品类别
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getDishesNameTrees")
			public void getDishesNameTrees(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					result = conditionUtils.getDishesNameTrees((String) session.getAttribute("tenentid"), p).toString();

				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}

			}
			
			
			
			
			/**
			 * Tree 菜品类别 级联-菜品名称
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getDishesTypeDlTree")
			public void getDishesTypeDlTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					result = conditionUtils.getDishesTypeDlTree((String) session.getAttribute("tenentid"), p).toString();

				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}

			}
			
			
			
			/**
			 * Tree 菜品名称 级联-菜品类别
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getDishesNameDlTree")
			public void getDishesNameDlTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					result = conditionUtils.getDishesNameDlTree((String) session.getAttribute("tenentid"), p).toString();

				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}

			}
			
				
			
			/**
			 * Tree 报表唯一编码
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getReportEnginetNum")
			public void getReportEnginetNum(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					result = conditionUtils.getReportEnginetNum((String) session.getAttribute("tenentid")).toString();
				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}
			}
			
			
			
			/**
			 * Tree java版本
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getJavaVersion")
			public void getJavaVersion(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					result = conditionUtils.getJavaVersion((String) session.getAttribute("tenentid")).toString();
				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}
			}
			
			
			
			/**
			 * Tree Sql版本
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getSqlVersion")
			public void getSqlVersion(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					result = conditionUtils.getSqlVersion((String) session.getAttribute("tenentid")).toString();
				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}
			}
			/**
			 * 第一次获取名字 包含插入
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			
			@RequestMapping(value = "/getFilesNames")
			public void getFilesNames(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					 
					p.put("tenancy_id",(String) session.getAttribute("tenentid"));
					p.put("store_id", (String) session.getAttribute("organ_id"));
					// 操作人
					p.put("usernum", session.getAttribute("employeeName"));
					result=commonMethodAreaService.getFilesNames((String) session.getAttribute("tenentid"), p).toString();
				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{ 
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}
			}	
			
			
			
			/**
			 * 
			 * @param request 请求对象
			 * @param response // 返回对象
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/exportBigData")
			public void exportBigData(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				
				HttpSession session = request.getSession();
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					
					
					if(p.optString("store_id").length()==0){
						p.element("store_id", session.getAttribute("user_organ_codes_group"));
					}

					if(p.optString("p_store_id").length()==0){
						p.element("p_store_id", session.getAttribute("user_organ_codes_group"));
					}
					if(p.optString("jy_store_id").length()==0){
						
						p.element("jy_store_id", session.getAttribute("user_organ_codes_group"));
					}
					if(p.optString("store_ids").length()==0){
						
						p.element("store_ids", session.getAttribute("user_organ_codes_group"));
					}
					// 当前门店id
					p.put("store_id", (String) session.getAttribute("store_id")); 
					p.put("store_id", (String) session.getAttribute("organ_id"));
					// 操作人
					p.put("usernum", session.getAttribute("employeeName"));
					
					File[] roots = File.listRoots();
					// 该方法查询机器的根级 目录 并且在DownExcleFiles文件夹中建立文件夹
					String uuid=UUID.randomUUID().toString();
					log.info("roots[0]:"+roots[0]);
					log.info("文件地址:"+roots[0]+"DownExcleFilesZip/"+uuid+"/");
					p.put("pathFolder",roots[0]+"DownExcleFilesZip/"+uuid+"/");
					
					p.put("tenancy_id",(String) session.getAttribute("tenentid"));
					String pathFolder = exportBigDataTools((String) session.getAttribute("tenentid"), p,session);
					log.info("文件输出完毕，地址为："+pathFolder);
					// 调用打包工具打包
					// 文件下载好地址为pathFolder
					String sourceFilePath = pathFolder;  
					// zip 的名字
			        String zipFilePath = pathFolder;  
			        // 即将压缩的文件名字
			        String fileName = UUID.randomUUID().toString();  
			        // 调用压缩工具 需要提供 文件路径 压缩路径 压缩的文件名
			        boolean flag = FileToZip.fileToZip(sourceFilePath, zipFilePath, fileName);  
			        if(flag){  
			            log.info("文件打包成功!");  
			        }else{  
			            log.info("文件打包失败!");  
			        }
			        log.info("压缩包绝对路径地址："+pathFolder+fileName+".zip");  
			        File file = new File(pathFolder+fileName+".zip");
			        final String deletePath= p.getString("pathFolder");
			        //deletePath=deletePath.replace("\\", "\\\\");
			        
			        try
			        {	 
			        	// 包含删除方法
			        	ReportExportUtils.downloadFile(file,response,p.optJSONObject("fileStateList").getJSONArray("list").getJSONObject(0).optString("zipname"),deletePath);
			        	// 开启新线程删除文件(线程)
			       /* 	new Thread(new Runnable() {
			        		              @Override
			        		              public void run() { 
			        		            	  try {
												Thread.sleep(5000);
											} catch (InterruptedException e) {
												// TODO Auto-generated catch block
												e.printStackTrace();
											}*/
			        				        	//boolean deleteDir = PoiTest.deleteDir(new File(deletePath));
			        				        	 
			        		          /*    }
			        		          }) {
			        		          }.start();*/
			        	
			          
			        }
			        catch (Exception e)
			        {
			        	e.printStackTrace();
			        }
			        
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}
				
				 
			}	
			
			/**
			 * 调用大批量导出方法
			 * @param tenancyID
			 * @param json
			 * @param session
			 * @return
			 * @throws Exception
			 */
			public String exportBigDataTools(String tenancyID, JSONObject json,HttpSession session) throws Exception {
				JSONObject paramData=null;
			    List<JSONObject> objs = new ArrayList<JSONObject>();
			    List<JSONObject> data = null;
				List<String> fileNameLits = new ArrayList<>();
				JSONArray fileStateList =new JSONArray();
				String pathFolder ="";
				  if(json.containsKey("rowcolumns") && json.getJSONObject("rowcolumns")!=null){
					  JSONObject obj = json.getJSONObject("rowcolumns");
					  JSONArray list=obj.getJSONArray("key");
					  // 拼接导出表头
					  for(int i=0;i<list.size();i++){
						  JSONObject job = new JSONObject();
						  JSONObject info=list.getJSONObject(i);
						  job.put("field", info.getString("field"));
						  objs.add(job);
					  }
					  CommonMethodAreaServiceImpl commonMethodAreaServiceImpl = new CommonMethodAreaServiceImpl();
					  	data =  commonMethodAreaServiceImpl.getMethodOne(tenancyID,json);  //第一层返回集合
						// 查询数据库文件名字
						fileStateList=json.optJSONObject("fileStateList").optJSONArray("list");
						
						for(int i = 0 ; i<json.optJSONObject("fileStateList").optJSONArray("list").size() ; i++) {
							fileNameLits.add(json.optJSONObject("fileStateList").optJSONArray("list").getJSONObject(i).optString("filename"));
						}
						  // 把数据+文件名字+表头+session+前台参数 传给核心方法 把数据写到磁盘里面
						  pathFolder = poiGroupBigData(data, json,objs, paramData,fileNameLits,fileStateList,session);
				  }
				  return pathFolder;
			}		
			
			/**
			 * 查询文件
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/selectFilesStates")
			public void selectFilesStates(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					p.put("tenancy_id",(String) session.getAttribute("tenentid"));
					result=commonMethodAreaService.selectFilesStates((String) session.getAttribute("tenentid"), p).toString();
				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{ 
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}
			}
				public String poiGroupBigData(List<JSONObject> mapList, JSONObject json,List<JSONObject> list, JSONObject paramData, List<String> fileNameLits,List<JSONObject> fileStateList,HttpSession session) {
					
					
					HSSFWorkbook workBook = null;
					// list分批
					
					// 声明要输出的list
					List<JSONObject> mapListWrit =new ArrayList<>();
					
					// 按多少数据来分
					Integer pageNum=json.optInt("pageNum"); 
					
					// 总条数
					Integer totolNum = json.optInt("totolNum");
					
					// 文件夹名字 例子:C:/downTest/
					File[] roots = File.listRoots();
					// 该方法查询机器的根级 目录 并且在DownExcleFiles文件夹中建立文件夹
					
					//String pathFolder = roots[0]+uuid+"\\";// uuid 的文件名字
					String pathFolder = json.optString("pathFolder");
//					json.put("pathFolder",pathFolder);
					
					
					
					// 拼接path地址
					JSONObject  structure = new JSONObject();
					//判断是否有必要分批
					if(totolNum>pageNum){
						
						// 分批次数
						Integer forNum = (int) Math.ceil((double)totolNum/(double)pageNum);
						log.info("共有 ： "+totolNum+"条，！"+" 分为 ："+forNum+"批");
						if(mapList.size()>0) {
							 structure= mapList.get(mapList.size()-1);
							 mapList.remove(mapList.size()-1);
						}
						  
						for(Integer i = 1 ; i <=forNum ; i++) {
							// 检测文件的状态十分为0 是带下载 1是已下载 2 是取消
							List<JSONObject> selectFilesStates = commonMethodAreaService.selectFilesStatesById((String) session.getAttribute("tenentid"), fileStateList.get(i-1));
							if(pageNum>mapList.size()) {
								break;
							}
							if(selectFilesStates.size()>0) {
								if(selectFilesStates.get(0).optString("filestate").equals("2")){
									// 该文件已经取消 把该文件的数据在总集合中删除掉
									log.info("当前的第"+i+"批已经取消  》》删除 ");
									mapList.subList(0, pageNum).clear();
									// 终止本次循环
									continue;
								}
							}
							/*try {
								Thread.sleep(5000);
							} catch (InterruptedException e1) {
								// TODO Auto-generated catch block
								e1.printStackTrace();
							}*/
							// 文件基础名字
							String fileName = fileNameLits.get(i-1).toString();
							// 地址
							String path = pathFolder+fileName+".xlsx";
							
							json.put("path",path);
							
							
							// mapListWrit 为分好批的list
							mapListWrit = new ArrayList<>(mapList.subList(0, pageNum));
							mapListWrit.add(structure);
							// 每次创建新的额work才能分成多哥excle
							workBook = new HSSFWorkbook();
							HSSFSheet sheet1=workBook.createSheet(fileName);
							JSONObject returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
							
							// 把即将分批的list填装道workBook里面
							reportBigDataPoiUtils.outDataToWork(json,mapListWrit, workBook,sheet1,list,returnTitleJson);
							// 调用io输出流 写到硬盘里面 拼接好的名字该 例子：C:/downTest/账单流水1.xlsx
							log.info("写入磁盘的地址"+json.optString("path"));
							reportBigDataPoiUtils.fileOut(workBook,json);
							
							//在总的list里面把分好批的listWrit剔除
							mapList.subList(0, pageNum/*+1*/).clear();
							//mapListWrit.removeAll(mapListWrit);
							
							// 更新状态
							fileStateList.get(i-1).put("filestate", "1");
							 
							try {
								int c=commonMethodAreaService.updateFileListState(json.optString("tenancy_id"),fileStateList.get(i-1));
								System.err.println(c);
							} catch (Exception e) {
								// TODO Auto-generated catch block
								log.error("修改文件状态失败");
								e.printStackTrace();
							}
						}
						// 检测总集合中还有没有数据往外输出
						if(!mapList.isEmpty()){
							log.error("剩余："+mapList.size());//表示最后剩下的数据
							List<JSONObject> selectFilesStates = commonMethodAreaService.selectFilesStatesById((String) session.getAttribute("tenentid"), fileStateList.get(fileStateList.size()-1));
							if(selectFilesStates.size()>0) {
								if(!selectFilesStates.get(0).optString("filestate").equals("2")){
								// 拼接文件的绝对路径
								String path = pathFolder+fileNameLits.get(fileNameLits.size()-1).toString()+".xlsx";
								json.put("path",path);
								
								// 每次创建新的额work才能分成多哥excle
								workBook = new HSSFWorkbook();
								// 创建sheet
								HSSFSheet sheet1=workBook.createSheet(fileNameLits.get(fileNameLits.size()-1).toString());
								// 拼接表头
								JSONObject returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
								
								mapList.add(structure);
								// 把即将分批的list填装道workBook里面
								reportBigDataPoiUtils.outDataToWork(json,mapList, workBook,sheet1,list,returnTitleJson);
								log.info("写入磁盘的地址"+json.optString("path"));
								// 开始写入磁盘
								reportBigDataPoiUtils.fileOut(workBook,json);
								
								fileStateList.get(fileStateList.size()-1).put("filestate", "1");
								try {
									// 更新文件状态
									commonMethodAreaService.updateFileListState(json.optString("tenancy_id"),fileStateList.get(fileStateList.size()-1));
								} catch (Exception e) {
									// TODO Auto-generated catch block
									log.error("修改文件状态失败");
									e.printStackTrace();
								}
						}
				}
						}
					}else {

						// 分批次数
						Integer forNum = (int) Math.ceil((double)totolNum/(double)pageNum);
						System.out.println("共有 ： "+totolNum+"条，！"+" 分为 ："+forNum+"批");
						
						if(mapList.size()>0) {
							 structure= mapList.get(mapList.size()-1);
							 mapList.remove(mapList.size()-1);
						}
						//表示最后剩下的数据
						log.error("剩余："+mapList.size());
						// 查询当前文件的状态
						List<JSONObject> selectFilesStates = commonMethodAreaService.selectFilesStatesById((String) session.getAttribute("tenentid"), fileStateList.get(fileStateList.size()-1));
						 
						if(selectFilesStates.size()>0) {
							if(!selectFilesStates.get(0).optString("filestate").equals("2")){
							// 拼接文件的绝对路径	
							String path = pathFolder+fileNameLits.get(fileNameLits.size()-1).toString()+".xlsx";
							json.put("path",path);
							
							// 每次创建新的额work才能分成多个excle
							workBook = new HSSFWorkbook();
							HSSFSheet sheet1=workBook.createSheet(fileNameLits.get(fileNameLits.size()-1).toString());
							JSONObject returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
							
							mapList.add(structure);
							// 把即将分批的list填装道workBook里面
							reportBigDataPoiUtils.outDataToWork(json,mapList, workBook,sheet1,list,returnTitleJson);
							log.info("写入磁盘的地址"+json.optString("path"));
							// 写入磁盘
							reportBigDataPoiUtils.fileOut(workBook,json);
							
							fileStateList.get(fileStateList.size()-1).put("filestate", "1");
							try {
								// 更新文件类型
								commonMethodAreaService.updateFileListState(json.optString("tenancy_id"),fileStateList.get(fileStateList.size()-1));
							} catch (Exception e) {
								// TODO Auto-generated catch block
								log.error("修改文件状态失败");
								e.printStackTrace();
							}
					}
			}
					
					}
					return pathFolder;
				}
		
	
		/**
		 * 打开文件
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/openCommonFile")
		public void openCommonFile(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				try {
					//"C:/DownExcleFiles/平台订单对账报表2017-07-28 015556  1-4.xlsx"
					Desktop.getDesktop().open(new File(p.optString("openFileName")));
					result = "{\"success\" : true , \"msg\" : \"打开!\"}";
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				 
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"打开发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		/**
		 * 取消文件
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		
		@RequestMapping(value = "/cancelFilesStates")
		public void cancelFilesStates(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				p.put("tenancy_id",(String) session.getAttribute("tenentid"));
				result=commonMethodAreaService.cancelFilesStates((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}	
		 
		@RequestMapping(value = "/selectAllFilesStates")
		public void selectAllFilesStates(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				p.put("store_id", (String) session.getAttribute("organ_id"));
				p.put("tenancy_id",(String) session.getAttribute("tenentid"));
				result=commonMethodAreaService.selectAllFilesStates((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		} 	
		
	
		@RequestMapping(value = "/getFileAddress")
		public void getFileAddress(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject json = new JSONObject();
				File[] roots = File.listRoots();
				// 该方法查询机器的根级 目录 并且在DownExcleFiles文件夹中建立文件夹
				String uuid=UUID.randomUUID().toString();
				String pathFolder = roots[0]+"DownExcleFilesZip\\"+uuid+"\\";// uuid 的文件名字
				json.put("fileAddress",pathFolder);
				result=json.toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		} 
		
		/**
		 * 根据id删除
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/deleteFilesList")
		public void deleteFilesList(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				p.put("tenancy_id",(String) session.getAttribute("tenentid"));
				result=commonMethodAreaService.deleteFilesList((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		/**
		 * 清空列表方法
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/deleteAllFilesList")
		public void deleteAllFilesList(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				p.put("store_id", (String) session.getAttribute("organ_id"));
				p.put("tenancy_id",(String) session.getAttribute("tenentid"));
				result=commonMethodAreaService.deleteAllFilesList((String) session.getAttribute("tenentid"), p).toString();
			}
			
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		
		
		/**
		 * 当前商户下所有人员
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getUserPersonnel")
		public void getUserPersonnel(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
 
				result=conditionUtils.getUserPersonnel((String) session.getAttribute("tenentid"), p).toString();
			}
			
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		/**
		 * 自定义模板
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getCustomPermissions")
		public void getCustomPermissions(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
 
				result=conditionUtils.getCustomPermissions((String) session.getAttribute("tenentid"), p).toString();
			}
			
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		
		/**
		 * 通用权限
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getUserJurisdiction")
		public void getUserJurisdiction(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
 
				result=conditionUtils.getUserJurisdiction((String) session.getAttribute("tenentid"), p).toString();
			}
			
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		
		
		
		/**
		 * 类别选择
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getCategorySelect")
		public void getCategorySelect(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
 
				result=commonMethodAreaService.getCategorySelect((String) session.getAttribute("tenentid"), p).toString();
			}
			
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		
		
		/**
		 * 菜品分类
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/loadCategoryTree")
		public void loadCategoryTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
 
				result=commonMethodAreaService.loadCategoryTree((String) session.getAttribute("tenentid"), p).toString();
			}
			
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		
		
		
		
		
		
		
		/**
		 * 代销菜品查询
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getConsignmentStore")
		public void getConsignmentStore(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
					p.element("store_id", session.getAttribute("user_organ_codes_group"));
				}else{
					p.element("store_id", session.getAttribute("user_organ"));
				}
 
				result=conditionUtils.getConsignmentStore((String) session.getAttribute("tenentid"), p).toString();
			}
			
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{ 
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}

		/**
		 * 自定义查询
		 */
		@RequestMapping(value = "/customize")
		public void customize(HttpServletRequest request, HttpServletResponse response)
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				} 
				p.put("last_operator", session.getAttribute("employeeName"));
				p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
				p.put("tenancy_id", session.getAttribute("tenentid"));

				result = commonMethodAreaService.customize((String) session.getAttribute("tenentid"), p.getInt("type"), p);

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		@RequestMapping(value = "/getChanelType")
		public void getChanelType(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String,String[]> map = request.getParameterMap();
				
				for(String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				// 传递参数：{"code":"chanel","type":"0","y":"1"} 出参数：[{"id":"MD01","text":"默认（餐厅）"},{"id":"WX02","text":"微店"}}]
				// 
				result =commonMethodAreaService.getChanelType((String) session.getAttribute("tenentid"),Integer.parseInt(p.get("type").toString()),p);

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		@RequestMapping(value = "/getOrgansTreeByConditions")
		public void getOrgansTreeByConditions(HttpServletRequest request,
				HttpServletResponse response) {
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			HttpSession session = request.getSession();
			String returnJSONstring = "";
			try {
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet()) {
					p.put(key, map.get(key)[0]);
				}
				String conditions = (String) session.getAttribute("user_organ_codes_group");
				
				if (p.containsKey("clearing_store_id")
						&& "".equals(p.optString("clearing_store_id"))) {
					p.put("clearing_store_id",
							(String) session.getAttribute("organ_id"));
				}
				
				JSONObject json = new JSONObject();
				if(StringUtils.isBlank(conditions)){
					json.put("id", (Integer)session.getAttribute("user_id"));
					json.put("organ_code", (String) session.getAttribute("organ_code"));
					conditions = systemUserService.findOrganCodes((String) session.getAttribute("tenentid"), (String) session.getAttribute("sysuser"), json);
					session.setAttribute("user_organ_codes_group", conditions);
				}
				returnJSONstring = commonMethodAreaService.getOrgansTreeByConditios(
						(String) session.getAttribute("tenentid"),
						(String) session.getAttribute("organ_id"),
						(String) session.getAttribute("organ_code"), p,conditions);
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				try {
					out = response.getWriter();
					out.println(returnJSONstring);
					out.flush();
					out.close();
				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					if (out != null)
						out.close();
				}
			}
		}
		
		/**
		 * 根据机构id查询服务员
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getWaiterName")
		public void getWaiterName(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = conditionUtils.getWaiter((String) session.getAttribute("id"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		} 
		
		/**
		 * 查询账单的表头数据
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getBillTitleData")
		public void getBillTitleData(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = commonMethodAreaService.getBillTitleData((String) session.getAttribute("id"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 查询活动名称
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getActivity")
		public void getActivity(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = conditionUtils.getActivity((String) session.getAttribute("id"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		} 
		
		/**
		 * 查询所有品牌
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getBrand")
		public void getBrand(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = conditionUtils.getBrand((String) session.getAttribute("id"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 贵宾券下拉框
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getCoupon")
		public void getCoupon(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = commonMethodAreaService.getCoupon((String) session.getAttribute("id"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 根据门店筛选kvs名称
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getKVSName")
		public void getKVSName(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = conditionUtils.getKVSName((String) session.getAttribute("id"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		} 
		
		/**
		 * 营运指标类型
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getOperationIndexType")
		public void getOperationIndexType(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
					
				}
				result = conditionUtils.getOperationIndexType((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		} 
		
		/**
		 * 获取价格体系
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getPriceSystemUrl")
		public void getPriceSystemUrl(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
					
				}
				result = conditionUtils.getPriceSystemUrl((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		} 
		/**
		 * 味千——获取菜品大类
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getItemDl")
		public void getItemDl(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = commonMethodAreaService.getItemDl((String) session.getAttribute("id"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		/**
		 * 味千——获取菜品小类
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getItemXl")
		public void getItemXl(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = commonMethodAreaService.getItemXl((String) session.getAttribute("id"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		/**
		 * 味千——获取菜品名称
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getItemName")
		public void getItemName(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = commonMethodAreaService.getItemName((String) session.getAttribute("id"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 根据门店获取价格体系(味千)
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getPriceSystemWithStore")
		public void getPriceSystemWithStore(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
					
				}
				
				String storeString = "organ_ids";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"********".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'********'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
				
				result = conditionUtils.getPriceSystemWithStore((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		} 
		
		/**
		 * 获取操作人
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getOperator")
		public void getOperator(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
					
				}
				result = conditionUtils.getOperator((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		} 
/**
		 * 获取操作人——味千
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getOperatorWQ")
		public void getOperatorWQ(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
					
				}
				result = conditionUtils.getOperatorWQ((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		
				@RequestMapping("/getOperatorWQ2")
		public void getOperatorWQ2(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
					
				}
				result = conditionUtils.getOperatorWQ2((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		
				/**
		 * 获取免单原因
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getSingleReason")
		public void getSingleReason(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
					
				}
				result = conditionUtils.getSingleReason((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		
				/**
		 * 品牌名称
		 * @param request
		 * @param response
		 */
		@RequestMapping("/getBrandNameUrl")
		public void getBrandNameUrl(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
					
				}
				result = conditionUtils.getBrandNameUrl((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		} 
		
		
		
		//新vue机构树
		@RequestMapping(value = "/getOrgansTreeByConditionsV2")
		public void getOrgansTreeByConditionsV2(HttpServletRequest request,
				HttpServletResponse response) {
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			HttpSession session = request.getSession();
			String returnJSONstring = "";
			String conditions = "";
			try {
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet()) {
					p.put(key, map.get(key)[0]);
				}
				
				//针对POS报表
				if(p.containsKey("jg")){
					conditions = p.getString("jg");
				}else{
					conditions = (String) session.getAttribute("user_organ_codes_group");
				}
				
				if (p.containsKey("clearing_store_id")
						&& "".equals(p.optString("clearing_store_id"))) {
					p.put("clearing_store_id",
							(String) session.getAttribute("organ_id"));
				}
				
				JSONObject json = new JSONObject();
				if(StringUtils.isBlank(conditions)){
					json.put("id", (Integer)session.getAttribute("user_id"));
					json.put("organ_code", (String) session.getAttribute("organ_code"));
					conditions = systemUserService.findOrganCodes((String) session.getAttribute("tenentid"), (String) session.getAttribute("sysuser"), json);
					session.setAttribute("user_organ_codes_group", conditions);
				}
				//针对POS报表免登陆
				if(p.containsKey("jg")){
					returnJSONstring = commonMethodAreaService.getOrgansTreeByConditionsV2(
							 p.optString("tenentid"),
							 p.optString("jg"),
							 "", p,conditions);
				}else{
					returnJSONstring = commonMethodAreaService.getOrgansTreeByConditionsV2(
							(String) session.getAttribute("tenentid"),
							(String) session.getAttribute("organ_id"),
							(String) session.getAttribute("organ_code"), p,conditions);
				}
				
				if(!returnJSONstring.equals("[]")){
					returnJSONstring = returnJSONstring.substring(1, returnJSONstring.length()-1);
					returnJSONstring = "{\"success\" : true , \"msg\" : \"成功\" , \"data\" : ["+ returnJSONstring +"]}";
				}
				else{
					returnJSONstring = "{\"success\" : false , \"msg\" : \"发生错误!\", \"data\" : []}";
				}
				if(StringUtils.isBlank(conditions)){
					log.info("查询机构树失败："+json.toString());
				}
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				try {
					out = response.getWriter();
					out.println(returnJSONstring);
					out.flush();
					out.close();
				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					if (out != null)
						out.close();
				}
			}
		}
		
		/**
		 * 获取报表编号集合
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getReportNumList")
		@ApiOperation(value = "获取报表编号集合",consumes= "application/json" ,httpMethod="GET",notes = "获取报表编号集合")
		@ApiImplicitParams({
		})
		public void getReportNumList(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				result = JsonUtils.list2json(conditionUtils.getReportList((String) session.getAttribute("tenentid"), p));
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		/**
		 * 获取查询方式集合
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getSearchMethods")
		@ApiOperation(value = "获取查询方式集合",consumes= "application/json" ,httpMethod="GET",notes = "获取查询方式集合")
		@ApiImplicitParams({
		})
		public void getSearchMethods(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				result = JsonUtils.list2json(conditionUtils.getSearchMethodList((String) session.getAttribute("tenentid"), p));
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		/**
		 * 查询自定义表头
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getReportTH")
		@ApiImplicitParams({
			@ApiImplicitParam(dataType = "String",paramType = "form",name = "report_num",value = "报表编号"),
			@ApiImplicitParam(dataType = "String",paramType = "form",name = "inquiry_mode",value = "查询方式"),
		})
		public void getReportTH(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
			String result= "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				result = commonMethodAreaService.getReportTH((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
					
				}
				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		/**
		 * 新增和修改自定义表头
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value="/addOrUpdateTH",method = RequestMethod.POST)
		@ResponseBody
		@ApiImplicitParams({
//			@ApiImplicitParam(dataType = "String",paramType = "form",name = "report_num",value = "报表编号"),
//			@ApiImplicitParam(dataType = "String",paramType = "form",name = "inquiry_mode",value = "查询方式"),
		})
		public void addOrUpdateTH(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject array) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result= "";
			try
			{
				String tenancyId = (String) session.getAttribute("tenentid");
//				tenancyId="rlyhbi";///////////////////////////////////////测试使用//////////////////////////////
				DBContextHolder.setTenancyid(tenancyId); // 线程安全
				List<JSONObject> list = (List) array.get("array");
				String tableName = "rpt_free";
				String type = array.getString("type");
				
				result = commonMethodAreaService.addOrUpdTH(tenancyId,tableName,list,type).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
					
				}
				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		/**
		 * 获取门店类型(味千)
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getStoreTypeUrl")
		public void getStoreTypeUrl(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				String storeString = "organ_ids";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"********".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'********'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
				result = conditionUtils.getStoreTypeUrl((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		/**
		 * 获取经营方式(味千)
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getManageTypeUrl")
		public void getManageTypeUrl(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				String storeString = "organ_ids";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"********".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'********'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
				result = conditionUtils.getManageTypeUrl((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 获取店面级别(味千)
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getStoreLevelUrl")
		public void getStoreLevelUrl(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				String storeString = "organ_ids";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"********".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'********'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
				result = conditionUtils.getStoreLevelUrl((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		/**
		 * 获取地区级别(味千)
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getAreaLevelUrl")
		public void getAreaLevelUrl(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				String storeString = "organ_ids";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"********".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'********'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
				result = conditionUtils.getAreaLevelUrl((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		
		/**
		 * 获取门店下所有的菜品授权操作人(味千)
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getOperatorUrl")
		public void getOperatorUrl(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				String storeString = "store_id";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"********".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'********'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
				result = conditionUtils.getOperatorUrl((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 获取门店下所有的菜品授权操作人（很多人调用）
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getOrganCode")
		public void getOrganCode(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				String storeString = "organ_ids";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"********".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'********'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
				result = conditionUtils.getOrganCode((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		/**
		 * 查询HBASE使用权限
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		@RequestMapping(value = "/getHBASEPrivilege")
		@ApiOperation(value = "查询HBASE使用权限",consumes= "multipart/form-data" ,httpMethod="GET",notes = "查询HBASE使用权限")
		@ApiImplicitParams({
		})
		public void getHBASEPrivilege(HttpServletRequest request, HttpServletResponse response) throws Exception {
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				
				result = commonMethodAreaService.getHBASEPrivilege((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		/**
		 * 获取特殊商品
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getSpecialType")
		public void getSpecialType(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = conditionUtils.getSpecialType((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 获取登录账号(很多调用)
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getLoginAccount")
		public void getLoginAccount(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				String storeString = "organ_ids";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"********".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'********'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
				result = conditionUtils.getLoginAccount((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		
		/**
		 * 获取 员工姓名（味千）
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getEmployeeName")
		public void getEmployeeName(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				String storeString = "organ_ids";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"********".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'********'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
				result = conditionUtils.getEmployeeName((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		/**
		 * 获取角色
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getRole")
		public void getRole(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = conditionUtils.getRole((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		/**
		 * 获取员工状态
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getState")
		public void getState(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = conditionUtils.getState((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		
		/**
		 * 获取口味名称
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getTasteName")
		public void getTasteName(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = conditionUtils.getTasteName((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 获取供应链机构树
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getSCMOrganTree")
		@ApiOperation(value = "供应链机构树",consumes= "multipart/form-data" ,httpMethod="POST",notes = "供应链机构树")
	    @ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenancyId",value = "商户号"),
	    	                @ApiImplicitParam(dataType = "Int",paramType = "form",name = "userId",value = "用户id"),
	    	})
		public void getSCMOrganTree(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String url = ScmInterfaceUrl+"/basicData/findStoreInfo";
			try
			{
				Map<String,String> params = new HashMap<String,String>();
				
				//测试代码
				params.put("tenancyId", "dev");
				params.put("userId", "0");
				result = ConditionUtils.sendPostRequest(url, params, "dev");
				
//				params.put("tenancyId", (String)session.getAttribute("tenentid"));
//				params.put("userId", (String)session.getAttribute("user_id"));
//				result = ConditionUtils.sendPostRequest(url, params, (String)session.getAttribute("tenentid"));
				
				JSONObject result_json = JSONObject.fromObject(result);
				if(result_json.getBoolean("success")){
					JSONArray result_json_arr = JSONArray.fromObject(result_json.get("data"));
					List<Organ> org_list = new ArrayList<Organ>();
					for (int i = 0; i < result_json_arr.size(); i++) {
						JSONObject tmp = result_json_arr.getJSONObject(i);
						Organ org = new Organ();
						org.setId(tmp.getInt("id"));
						org.setLabel((tmp.getString("storeName")));
						org.setLabelc((tmp.getString("storeName")));
						org.setFatherId((tmp.get("parentId")==null||tmp.getString("parentId").equals("null"))?null:tmp.getInt("parentId"));
						org_list.add(org);
					}
					
					List<Organ> list2 = new ArrayList<Organ>();
					Map<Integer, Organ> map = new HashMap<Integer, Organ>();
					for (Organ jo : org_list){
						map.put(jo.getId(), jo);
					}
					for (Organ jo1 : org_list){
						if (map.get(jo1.getFatherId()) == null){
							list2.add(jo1);
						}
						if (map.get(jo1.getFatherId()) != null){
							map.get(jo1.getFatherId()).getChildren().add(map.get(jo1.getId()));
						}
					}
					for(Integer key : map.keySet()){
						int size =map.get(key).getChildren()==null?0:map.get(key).getChildren().size();
						map.get(key).setCount(size);
					}
					
					result = JsonUtils.list2json(list2);
				}else{
					result = "{\"success\" : false , \"msg\" : \"接口返回失败!\"}";
				}
				
				
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 获取供应链仓库集合
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getSCMWarehouse")
		@ApiOperation(value = "供应链仓库集合",consumes= "multipart/form-data" ,httpMethod="POST",notes = "供应链仓库集合")
	    @ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenancyId",value = "商户号"),
	    	                @ApiImplicitParam(dataType = "Int",paramType = "form",name = "storeId",value = "机构id"),
	    	})
		public void getSCMWarehouse(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String url = ScmInterfaceUrl+"/basicData/findWarehouse";
			try
			{
				Map<String,String> params = new HashMap<String,String>();
				
				//测试代码
				params.put("tenancyId", "dev");
				params.put("storeId", "310");
				result = ConditionUtils.sendPostRequest(url, params, "dev");
				
//				params.put("tenancyId", (String)session.getAttribute("tenentid"));
//				params.put("storeId", (String)session.getAttribute("store_id"));
//				result = ConditionUtils.sendPostRequest(url, params, (String)session.getAttribute("tenentid"));
				
				JSONObject result_json = JSONObject.fromObject(result);
				if(result_json.getBoolean("success")){
					JSONArray result_json_arr = JSONArray.fromObject(result_json.get("data"));
					List<JSONObject> result_list = new ArrayList<JSONObject>();
					for (int i = 0; i < result_json_arr.size(); i++) {
						JSONObject tmp = result_json_arr.getJSONObject(i);
						JSONObject obj = new JSONObject();
						obj.put("id", tmp.getString("id"));
						obj.put("text", tmp.getString("warehouseName"));
						result_list.add(obj);
					}
					result = result_list.toString();
				}else{
					result = "{\"success\" : false , \"msg\" : \"接口返回失败!\"}";
				}
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 获取供应链（物品）类别（仓库物品表）
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getSCMCategory")
		@ApiOperation(value = "供应链物品类别",consumes= "multipart/form-data" ,httpMethod="POST",notes = "供应链物品类别")
	    @ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenancyId",value = "商户号"),
	    	})
		public void getSCMCategory(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String url = ScmInterfaceUrl+"/basicData/findMaterialClass";
			try
			{
				Map<String,String> params = new HashMap<String,String>();
				
				//测试代码
				params.put("tenancyId", "dev");
				result = ConditionUtils.sendPostRequest(url, params, "dev");
				
//				params.put("tenancyId", (String)session.getAttribute("tenentid"));
//				result = ConditionUtils.sendPostRequest(url, params, (String)session.getAttribute("tenentid"));
				
				JSONObject result_json = JSONObject.fromObject(result);
				if(result_json.getBoolean("success")){
					JSONArray result_json_arr = JSONArray.fromObject(result_json.get("data"));
					List<JSONObject> result_list = new ArrayList<JSONObject>();
					for (int i = 0; i < result_json_arr.size(); i++) {
						JSONObject tmp = result_json_arr.getJSONObject(i);
						JSONObject obj = new JSONObject();
						obj.put("id", tmp.getString("id"));
						obj.put("text", tmp.getString("categoryName"));
						result_list.add(obj);
					}
					result = result_list.toString();
				}else{
					result = "{\"success\" : false , \"msg\" : \"接口返回失败!\"}";
				}
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * 获取供应链供应商集合
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getSCMSupply")
		@ApiOperation(value = "供应链供应商集合",consumes= "multipart/form-data" ,httpMethod="POST",notes = "供应链供应商集合")
	    @ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenancyId",value = "商户号"),
	    					@ApiImplicitParam(dataType = "Int",paramType = "form",name = "categoryId",value = "类别id"),
	    	})
		public void getSCMSupply(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String url = ScmInterfaceUrl+"/basicData/findSupplierInfo";
			try
			{
				Map<String,String> params = new HashMap<String,String>();
				
				//测试代码
				params.put("tenancyId", "dev");
//				params.put("categoryId", "0");//供应商类别id，不传默认查所有
				result = ConditionUtils.sendPostRequest(url, params, "dev");
				
//				JSONObject obj = JSONObject.fromObject("{}");
//				Map<String, String[]> map = request.getParameterMap();
//				for (String key : map.keySet())
//				{
//					obj.put(key, map.get(key)[0]);
//				}
//				params.put("tenancyId", (String)session.getAttribute("tenentid"));
//				params.put("categoryId", obj.optString("categoryId", ""));//供应商类别id，不传默认查所有
//				result = ConditionUtils.sendPostRequest(url, params, (String)session.getAttribute("tenentid"));
				
				JSONObject result_json = JSONObject.fromObject(result);
				if(result_json.getBoolean("success")){
					JSONArray result_json_arr = JSONArray.fromObject(result_json.get("data"));
					List<JSONObject> result_list = new ArrayList<JSONObject>();
					for (int i = 0; i < result_json_arr.size(); i++) {
						JSONObject tmp = result_json_arr.getJSONObject(i);
						JSONObject object = new JSONObject();
						object.put("id", tmp.getString("id"));
						object.put("text", tmp.getString("supplierName"));
						result_list.add(object);
					}
					result = result_list.toString();
				}else{
					result = "{\"success\" : false , \"msg\" : \"接口返回失败!\"}";
				}
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
			
		}
		
		/**
		 * Tree 菜品名称(味千专版，带点菜码)
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getDishesNameTreeWQ")
		public void getDishesNameTreeWQ(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				result = conditionUtils.getDishesNameTreeWQ((String) session.getAttribute("tenentid"), p).toString();

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		/**
		 * 获取变价原因
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getChangePriceReason")
		public void getChangePriceReason(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				result = conditionUtils.getChangePriceReason((String) session.getAttribute("tenentid"), p).toString();
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		
		
		/**
		 * 多品牌机构树
		 * 多重组合：商户号查询，商户号+品牌查询，商户号+用户名查询
		 * swagger模式下不能全部参数都输入数据，只能多重组合
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getMultiBrandOrganization")
		@ApiOperation(value = "多品牌机构树接口",consumes= "multipart/form-data" ,httpMethod="POST",notes = "多品牌机构树接口")
	    @ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenancyId",value = "商户号"),
	    	                @ApiImplicitParam(dataType = "Int",paramType = "form",name = "brandId",value = "品牌ID"),
	    	                @ApiImplicitParam(dataType = "int",paramType = "form",name = "employeeId",value = "用户ID")
	    	})
	    @ApiResponses(value = {  
	              @ApiResponse(code = 400, message = "No Name Provided")
	      })
		public void getMultiBrandOrganization(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String url = "";
			String sys_url = com.tzx.framework.common.constant.Constant.getSystemMap().get("sys_url");
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				JSONObject parameter = new JSONObject();
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				
				if(p.containsKey("tenancyId")&&p.containsKey("employeeId")&&p.containsKey("brandId")){
					parameter.put("parameter", p);
					url = sys_url+"finter/organization/organOut/getOrganTreeByEmployeeAndBrand";  //swagger模式通过商户，用户,品牌ID查询机构      (暂无)
				}else if(p.containsKey("tenancyId")&&p.containsKey("brandId")){
					parameter.put("parameter", p);
					url = sys_url+"organization/organOut/getOrganTreeByBrand";  //swagger模式根据商户+品牌ID查询机构
				}else if(p.containsKey("tenancyId")&&p.containsKey("employeeId")){
					parameter.put("parameter", p);
					url = sys_url+"finter/organization/organOut/getOrganTreeByEmployee";  //swagger模式根据商户+用户ID查询机构
				}else if(p.containsKey("tenancyId")){
					parameter.put("parameter", p);
					url = sys_url+"organization/organOut/getAllOrgan";  //swagger模式根据商户查询所有机构
				}else if(p.containsKey("employeeId")&&p.containsKey("brandId")){
					p.put("tenancyId", session.getAttribute("tenentid"));
					url = sys_url+"finter/organization/organOut/getOrganTreeByEmployeeAndBrand";  //默认模式根据商户\用户\品牌查询机构  (暂无)
					parameter.put("parameter", p);
				}else if(p.containsKey("employeeId")){
					p.put("tenancyId", session.getAttribute("tenentid"));
					url = sys_url+"finter/organization/organOut/getOrganTreeByEmployee";  //默认根据商户、用户ID查询机构
					parameter.put("parameter", p);
				}else if(p.containsKey("brandId")){
					p.put("tenancyId", session.getAttribute("tenentid"));
					url = sys_url+"organization/organOut/getOrganTreeByBrand";  //默认根据商户+品牌ID查询机构
				}else{
					p.put("tenancyId", session.getAttribute("tenentid"));
					parameter.put("parameter", p);
					url = sys_url+"organization/organOut/getAllOrgan";  //默认根据商户查询所有机构
				}
				
				// 数据格式转换
				result = HttpClientPostUtil.sendPostRequest(url, parameter.toString());
				result = getOrganListString(result,"data");
				
				Set<Integer> ids_set = new HashSet<Integer>();
				JSONArray result_json_arr = JSONArray.fromObject(result);
				if(result_json_arr.size()>0){
					for(Object obj:result_json_arr){
						JSONObject organ = JSONObject.fromObject(obj);
						getOrganIds(ids_set, organ);
					}
				}
				String ids = StringUtils.join(ids_set.toArray(), ",");
				session.setAttribute("user_organ", ids);//当前商户下品牌对应得门店(非品牌也取这个)
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		private void getOrganIds(Set<Integer> ids_set, JSONObject organ){
			ids_set.add(organ.getInt("id"));
			JSONArray childs = JSONArray.fromObject(organ.get("children"));
			for (Object obj : childs) {
				JSONObject child = JSONObject.fromObject(obj);
				getOrganIds(ids_set, child);
			}
		}
		
		/**
		 * 获取登入用户人员信息
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getLoginUserInformation")
		@ApiOperation(value = "获取登入用户人员信息接口",consumes= "multipart/form-data" ,httpMethod="POST",notes = "获取登入用户人员信息")
	    @ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenancyId",value = "商户号"),
	    	                @ApiImplicitParam(dataType = "String",paramType = "form",name = "userName",value = "用户名"),
	    	                @ApiImplicitParam(dataType = "String",paramType = "form",name = "storeId",value = "商户ID预留字段可以忽略") //预留字段可以忽略
	    	})
	    @ApiResponses(value = {  
	              @ApiResponse(code = 400, message = "No Name Provided")
	      })
		public void getLoginUserInformation(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String url = "";
			String sys_url = com.tzx.framework.common.constant.Constant.getSystemMap().get("sys_url");
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				JSONObject parameter = new JSONObject();
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				
				if(p.containsKey("tenancyId")){
					parameter.put("parameter", p);
					url = sys_url+"finter/inter/queryUserEmpInfo";  //swagger模式查询用户信息
				}else{
					p.put("tenancyId", session.getAttribute("tenentid"));
					url = sys_url+"finter/inter/queryUserEmpInfo";  //接口方式查询用户信息
				}
				result = HttpClientPostUtil.sendPostRequest(url, p.toString());
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		/**
		 * 获取登录用户角色模块信息
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/getloginuserrolemoduleinformation")
		@ApiOperation(value = "获取登录用户角色模块信息接口",consumes= "multipart/form-data" ,httpMethod="POST",notes = "获取登录用户角色模块信息")
	    @ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenancyId",value = "商户号"),
	    	                @ApiImplicitParam(dataType = "String",paramType = "form",name = "userName",value = "用户名"),
	    	                @ApiImplicitParam(dataType = "String",paramType = "form",name = "storeId",value = "商户ID预留字段可以忽略"), //预留字段可以忽略
	                        @ApiImplicitParam(dataType = "String",paramType = "form",name = "version",value = "版本号预留字段可以忽略"),//预留字段可以忽略
	                        @ApiImplicitParam(dataType = "String",paramType = "form",name = "modulType",value = "模块类型") 
	    	})
	    @ApiResponses(value = {  
	              @ApiResponse(code = 400, message = "No Name Provided")
	      })
		public void getloginuserrolemoduleinformation(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String url = "";
			String sys_url = com.tzx.framework.common.constant.Constant.getSystemMap().get("sys_url");
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				JSONObject parameter = new JSONObject();
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				
				if(p.containsKey("tenancyId")){
					parameter.put("parameter", p);
					url = sys_url+"finter/inter/queryUserAuthModulesInfo";  //swagger模式查询用户信息
				}else{
					p.put("tenancyId", session.getAttribute("tenentid"));
					url = sys_url+"finter/inter/queryUserAuthModulesInfo";  //接口方式查询用户信息
				}
				result = HttpClientPostUtil.sendPostRequest(url, p.toString());
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		/**
		 * 用户登录验证，并将对应人员信息存入session中
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/userloginauthentication")
		@ApiOperation(value = "用户登录验证，并将对应人员信息存入session中接口",consumes= "multipart/form-data" ,httpMethod="POST",notes = "用户登录验证，并将对应人员信息存入session中")
	    @ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenancyId",value = "商户号没有数据可以忽略"),
	    	                @ApiImplicitParam(dataType = "String",paramType = "form",name = "userName",value = "用户名没有数据可以忽略"),
	    	                @ApiImplicitParam(dataType = "String",paramType = "form",name = "storeId",value = "商户ID没有数据可以忽略"), 
	                        @ApiImplicitParam(dataType = "String",paramType = "form",name = "version",value = "版本号没有数据可以忽略")
	    	})
	    @ApiResponses(value = {  
	              @ApiResponse(code = 400, message = "No Name Provided")
	      })
		public void userloginauthentication(HttpServletRequest request, HttpServletResponse response){
			
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String url = "";
			JSONObject json = new JSONObject();
			JSONObject data = new JSONObject();
			String sys_url = com.tzx.framework.common.constant.Constant.getSystemMap().get("sys_url");
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				JSONObject parameter = new JSONObject();
				Map<String, String[]> map = request.getParameterMap();
				
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != ""){
						p.put(key, map.get(key)[0]);
					}
				}
				
				
				if(p.containsKey("tenancyId")){
					parameter.put("parameter", p);
					url = sys_url+"finter/inter/userLogin";  //swagger模式查询用户信息
				}else{
					p.put("tenancyId", session.getAttribute("tenentid"));
					if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
						p.put("userName", session.getAttribute("employeeName"));//单品牌
					}else{
						p.put("userName", session.getAttribute("userName"));//多品牌
					}
					
					url = sys_url+"finter/inter/userLogin";  //接口方式查询用户信息
				}
				
				result = HttpClientPostUtil.sendPostRequest(url, p.toString());
				//json.put("result", result);
				/*if(json.getJSONObject("result").get("code").equals("0")){
					data.put("data", json.getJSONObject("result").get("data"));
					data.put("smInfo",data.getJSONObject("data").get("smInfo"));
					session.setAttribute("sysModule", data.getJSONObject("smInfo").get("sysModule")); //模块信息
					data.put("sessnioInfo",data.getJSONObject("data").get("sessnioInfo"));
					session.setAttribute("isLogin", data.getJSONObject("sessnioInfo").get("isLogin"));//
					session.setAttribute("employeeId", data.getJSONObject("sessnioInfo").get("employeeId"));//
					session.setAttribute("organ_id", data.getJSONObject("sessnioInfo").get("organ_id"));//
					session.setAttribute("org_type", data.getJSONObject("sessnioInfo").get("org_type"));//
					session.setAttribute("moduleMap", data.getJSONObject("sessnioInfo").get("moduleMap"));//
					session.setAttribute("authMap", data.getJSONObject("sessnioInfo").get("authMap"));//
					session.setAttribute("organ_code", data.getJSONObject("sessnioInfo").get("organ_code"));//
					session.setAttribute("organ_brief_code", data.getJSONObject("sessnioInfo").get("organ_brief_code"));//
					session.setAttribute("store_id", data.getJSONObject("sessnioInfo").get("store_id"));//机构ID
					session.setAttribute("day_count", data.getJSONObject("sessnioInfo").get("day_count"));//
					session.setAttribute("e_id", data.getJSONObject("sessnioInfo").get("e_id"));//
					session.setAttribute("da_date", data.getJSONObject("sessnioInfo").get("da_date"));//
					session.setAttribute("employeeName", data.getJSONObject("sessnioInfo").get("employeeName"));//用户名
					session.setAttribute("user_organ_codes_group", data.getJSONObject("sessnioInfo").get("user_organ_codes_group"));//所有机构
					session.setAttribute("sysuser", data.getJSONObject("sessnioInfo").get("sysuser"));//
					session.setAttribute("name", data.getJSONObject("sessnioInfo").get("name"));//
					session.setAttribute("user_organ_codes_group_invalid", data.getJSONObject("sessnioInfo").get("user_organ_codes_group_invalid"));//
					session.setAttribute("user_id", data.getJSONObject("sessnioInfo").get("user_id"));//用户ID
					session.setAttribute("tenentid", data.getJSONObject("sessnioInfo").get("tenentid"));//商户号
					session.setAttribute("organName", data.getJSONObject("sessnioInfo").get("organName"));//机构名称
					session.setAttribute("valid_state", 1);//是否多品牌，1为多品牌
					session.setAttribute("_s", "1221705");
				}*/
				
				if(!StringUtils.isEmpty(result)){
					JSONObject dataJson = JSONObject.fromObject(result);
					if(dataJson != null){
						if("0".equals(dataJson.optString("code"))){
							JSONObject sessionJson = dataJson.optJSONObject("data").optJSONObject("sessnioInfo");
							if(sessionJson != null ){
								Set<String> keys = sessionJson.keySet();
								for(String key : keys){
									session.setAttribute(key, sessionJson.get(key));
								}
							}
						}
					}
				}
				if(session.getAttribute("themes") == null){
					session.setAttribute("themes","metro-gray");
				}
		
			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}
				
				try
				{
					out = response.getWriter();
					
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
	
		/**
		 * 转换成eazyui tree格式
		 * @param s
		 * @param key
		 * @return
		 */
		public String getOrganListString(String s,String key) {
			JSONObject fromObject = JSONObject.fromObject(s);
			String result ="";
			if(fromObject.optString("code").equals("0")) {
				result= fromObject.optJSONArray(key).toString();
			}
			return result;
		}
		
		
		
		@RequestMapping(value = "/getChanelTypeByBrand")
		public void getChanelTypeByBrand(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String,String[]> map = request.getParameterMap();
				
				for(String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result =commonMethodAreaService.getChanelTypeByBrand((String) session.getAttribute("tenentid"),p);

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		/**
		 * 通过品牌id查询菜品分类 包含 大类和小类 （树形结构）
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/loadCategoryTreeByBrand")
		public void loadCategoryTreeByBrand(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				} 
				p.put("last_operator", session.getAttribute("employeeName"));
				p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
				p.put("tenancy_id", session.getAttribute("tenentid"));

//				result = commonMethodAreaService.loadCategoryTreeByBrand((String) session.getAttribute("tenentid"), p);
				result = commonMethodAreaService.loadCategoryTreeByBrand(p.optString("tenancyId"), p);

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		//多品牌渠道
		@RequestMapping(value = "/findDicVal")
		public void findDicVal(HttpServletRequest request, HttpServletResponse response)
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				String field = request.getParameter("field");
				result = commonMethodAreaService.findDicVal((String) session.getAttribute("tenentid"), field).toString();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();
					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		//多品牌注册渠道
		@RequestMapping(value = "/registerChanel")
		public void registerChanel(HttpServletRequest request, HttpServletResponse response)
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				
				Map<String,String[]> map = request.getParameterMap();
				
				for(String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				result =commonMethodAreaService.registerChanel((String) session.getAttribute("tenentid"),Integer.parseInt(p.get("type").toString()),p);

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		/**
		 * 多品牌菜品类别树
		 */
		@RequestMapping(value = "/getComboTree")
		public void getComboTree(HttpServletRequest request, HttpServletResponse response)
		{

			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			HttpSession session = request.getSession();
			String result = "[]";
			try
			{
				JSONObject obj = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					obj.put(key, map.get(key)[0]);
				}
				String chanel = obj.containsKey("chanel") ? obj.getString("chanel") : "";
				String fatherId = obj.containsKey("fatherId") ? obj.getString("fatherId") : "";

				result = commonMethodAreaService.getComboTree((String) session.getAttribute("tenentid"),chanel, fatherId);

			}
			catch (Exception e)
			{
				result = "[]";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		
		/**
		 * POS报表权限访问
		 * @param request
		 * @param response
		 */
		@RequestMapping(value = "/loadFirstLevel")
		@ApiOperation(value = "lvl1",consumes= "multipart/form-data" ,httpMethod="POST",notes = "lvl1")
		@ApiImplicitParams({
			@ApiImplicitParam(dataType = "String",paramType = "form",name = "uname",value = "用户名"),
			@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenentid",value = "商户号"),
			@ApiImplicitParam(dataType = "String",paramType = "form",name = "store_id",value = "机构"),
		})
		public void loadFirstLevel(HttpServletRequest request, HttpServletResponse response)
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			HttpSession session = request.getSession();
			JSONObject json = JSONObject.fromObject("{}");
			PrintWriter out = null;
			InputStream in = null;
			String result = "";
			Integer uid = null;
			try
			{
				JSONObject p = JSONObject.fromObject("{}");
				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				
				DBContextHolder.setTenancyid(p.getString("tenentid"));
				
				if(p.optString("uname").equals(Constant.ADMIN)){
					p.element("sysuser", 1);
				}else{
					p.element("sysuser", 0);
					 uid= commonMethodAreaService.getUserId(p.getString("tenentid"), p.optString("uname"));
				}
				
				p.put("tenancy_id", p.getString("tenentid"));
				p.put("id", uid);
				p.put("store_id",  p.optString("store_id"));
				List<JSONObject> list = commonMethodAreaService.loadFirstLevel(p.getString("tenentid"),p.getString("sysuser"), p);
				json.put("data", list);
				json.put("success", true);
				json.put("msg", "");
				/*if (list != null && list.size() > 0)
				{
					//result = list.toString();
					//result = "{\"success\" : true , \"msg\" : \"\", data:"+list.toString()+"}";
				}else{
					//result = "{\"success\" : true , \"msg\" : \"\", data:[]}";
				}*/
			}
			catch (Exception e)
			{
				json.put("success", false);
				json.put("msg", "发生错误!");
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();
					out.print(json);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}
		}
		
		
		/**
		 * pos获取门店人员信息存入session
		 * @param request
		 * @param response
		 * @throws IOException
		 * @throws WriteException
		 */
		@RequestMapping(value = "/localSession")
		@ApiOperation(value = "pos获取门店人员信息存入session",consumes= "multipart/form-data" ,httpMethod="POST",notes = "pos获取门店人员信息存入session")
	    @ApiImplicitParams({@ApiImplicitParam(dataType = "String",paramType = "form",name = "tenentid",value = "商户号"),
	    	                @ApiImplicitParam(dataType = "String",paramType = "form",name = "userName",value = "用户名"),
	    	})
	    @ApiResponses(value = {  
	              @ApiResponse(code = 400, message = "No Name Provided")
	      })
		public void localSession(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			try
			{
				JSONObject p = JSONObject.fromObject("{}");

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					p.put(key, map.get(key)[0]);
				}
				
				result = commonMethodAreaService.localSession(session,p).toString();

			}
			catch (Exception e)
			{
				result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
				e.printStackTrace();
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
				}
				catch (Exception e)
				{
				}

				try
				{
					out = response.getWriter();

					out.print(result);
					out.flush();
					out.close();
				}
				catch (Exception e)
				{
				}
				finally
				{
					if (out != null) out.close();
				}
			}

		}
		
		
		/**
		  *收银人员
		  * @param request
		  * @param response
		  * @throws IOException
		  * @throws WriteException
		  */
			@RequestMapping(value = "/getLoadPOSNum")
			public void getLoadPOSNum(HttpServletRequest request, HttpServletResponse response)
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						if (!"".equals(map.get(key)[0]))
						{
							p.put(key, map.get(key)[0]);
						}
					}
					
					if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
						if(p.optString("organ_id").length()==0){
							p.element("organ_id", session.getAttribute("user_organ_codes_group"));
						}
					}else{
						if(p.optString("organ_id").length()==0){
							if(session.getAttribute("user_organ").equals("0")) {
								p.element("organ_id", session.getAttribute("user_organ_codes_group"));
							}else {
								p.element("organ_id", session.getAttribute("user_organ"));
							}
						}
					}
					
					result = conditionUtils.getLoadPOSNum((String) session.getAttribute("tenentid"), p).toString();

				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"查询会员等级时发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}
			}
			
			
			/**
			 * 查询不计收入付款方式和计入付款方式的方法
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getNoComeOrInComPaymentDetails")
			public void getNoComeOrInComPaymentDetails(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{

				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					result = JsonUtils.list2json(conditionUtils.getNoComeOrInComPaymentDetails((String) session.getAttribute("tenentid"), p));

				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}

			}		
			/**
			  *获取配送类型
			  * @param request
			  * @param response
			  * @throws IOException
			  * @throws WriteException
			  */
			@RequestMapping(value = "/getDeliveryPartyTree")
			public void getDeliveryPartyTree(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					result = conditionUtils.getDeliveryPartyTree((String) session.getAttribute("tenentid"), p).toString();
				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}
			}	
			
			@RequestMapping(value = "/getPaymentDetailsOrderByClassForHuaLaiShi")
			public void getPaymentDetailsOrderByClassForHuaLaiShi(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{

				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					result = JsonUtils.list2json(commonMethodAreaService.getPaymentDetailsOrderByClass((String) session.getAttribute("tenentid"), p));

				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}
			}
			
			/**
			 * pos是否限制门店总部报表查询范围
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getPosLimitReportQueryArea")
			public void getPosLimitReportQueryArea(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					result = commonMethodAreaService.getPosLimitReportQueryArea(p.optString("tenentid"), p).toString();

				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}

			}
			
			
			
			
			/**
			 * 当前范围付款方式条件
			 * @param request
			 * @param response
			 * @throws IOException
			 * @throws WriteException
			 */
			@RequestMapping(value = "/getPaymentRange")
			@ApiOperation(value = "付款方式范围查询",consumes= "multipart/form-data" ,httpMethod="POST",notes = "付款方式范围查询")
		    @ApiImplicitParams({@ApiImplicitParam(dataType = "Date",paramType = "form",name = "begin_date",value = "开始日期"),
		    	                @ApiImplicitParam(dataType = "Date",paramType = "form",name = "end_date",value = "结束日期"),
		    	                @ApiImplicitParam(dataType = "String",paramType = "form",name = "tenentid",value = "商户号"),
		    	})
			public void getPaymentRange(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				PrintWriter out = null;
				InputStream in = null;
				HttpSession session = request.getSession();
				String result = "";
				try
				{
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					try{
						if(p.containsKey("tenentid")){
							DBContextHolder.setTenancyid(p.optString("tenentid"));
						}
					}catch(Exception e){
						e.printStackTrace();
					}
					
					result = commonMethodAreaService.getPaymentRange(p.optString("tenentid"), p).toString();

				}
				catch (Exception e)
				{
					result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
					e.printStackTrace();
				}
				finally
				{
					try
					{
						if (in != null)
						{
							in.close();
						}
					}
					catch (Exception e)
					{
					}

					try
					{
						out = response.getWriter();

						out.print(result);
						out.flush();
						out.close();
					}
					catch (Exception e)
					{
					}
					finally
					{
						if (out != null) out.close();
					}
				}

			}

		@RequestMapping(value = "/getCurUserRoles")
		@ResponseBody
		public Roles getCurUserRoles(HttpServletRequest request){
				Roles roles = new Roles();
				HttpSession session = request.getSession();
				String tenancyId = (String) session.getAttribute("tenentid");
				if(session.getAttribute("employeeId")!=null){
					String employeeId = (String)session.getAttribute("employeeId");
					try {
						roles = this.commonMethodAreaService.getUserRoles(tenancyId,employeeId);
						String curDay = DateUtil.getNowDateYYDDMMHHMMSS();
						roles.setRemark(curDay);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				return roles;
		}
}
