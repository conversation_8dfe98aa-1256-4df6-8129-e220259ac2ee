package com.tzx.report.bo.imp.boh;

import com.tzx.report.bo.boh.BusinessTotalmoneyAnalysisService;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import com.tzx.report.po.boh.dao.BusinessTotalmoneyAnalysisDao;

import javax.annotation.Resource;

/**
 * Created by gj on 2019-05-30.
 */

@Service(BusinessTotalmoneyAnalysisService.NAME)
public class BusinessTotalmoneyAnalysisServiceImp implements BusinessTotalmoneyAnalysisService {


    @Resource(name = BusinessTotalmoneyAnalysisDao.NAME)
    private BusinessTotalmoneyAnalysisDao businessTotalmoneyAnalysisDao;

    @Override
    public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
        return businessTotalmoneyAnalysisDao.find(tenancyID, condition);
    }
}
