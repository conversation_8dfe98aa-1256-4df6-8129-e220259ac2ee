package com.tzx.boh.service.rest;

import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.boh.bo.OperationInformationManagementService;
import com.tzx.framework.bo.DataDictionaryService;
import com.tzx.framework.common.util.DateUtil;

/**
 * <AUTHOR>
 */
@Controller("OperationInformationManagementContraller")
@RequestMapping("/boh/operationInformationManagementContraller")
public class OperationInformationManagementRest
{
	@Resource(name = OperationInformationManagementService.NAME)
	private OperationInformationManagementService	operationInformationManagementService;

	@Autowired
	private DataDictionaryService	dataDictionaryService;

	/**
	 * 运营信息列表
	 */
	@RequestMapping(value = "/loadOperationInformation")
	public void loadOperationInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("organ_code")){
				obj.put("organ_code",(String) session.getAttribute("organ_code"));
			}
//			obj.put("is_zb",(String) session.getAttribute("organ_code"));
			result = operationInformationManagementService.loadOperationInformation((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 保存运营信息
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/saveOperationInformation")
	public void saveOperationInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String,String[]> map = request.getParameterMap();
			for(String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			obj.put("store_id", (String) session.getAttribute("organ_id"));
			Object dic = dataDictionaryService.save((String) session.getAttribute("tenentid"), "boh_operate_info", obj);
			if (dic != null) result = "{\"success\": true, \"id\" : \"" + dic.toString() + "\"}";
		}
		catch (Exception e)
		{
			result = "{\"success\": false, \"msg\" : \"" + e.getMessage() + "\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	@RequestMapping(value = "/checkUnique")
	public void checkUnique(HttpServletRequest request, HttpServletResponse response)
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("store_id", (String) session.getAttribute("organ_id"));
			boolean rs = operationInformationManagementService.checkUnique((String) session.getAttribute("tenentid"),obj);

			if(rs)
			{
				result = "{\"success\": true}";
			}else
			{
				result = "{\"success\": false}";
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
}
