package com.tzx.cc.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.exception.SystemException;

public interface DeliveryChargeSetService
{
	String	NAME = "com.tzx.cc.bo.imp.DeliveryChargeSetImpl";
	public JSONObject loadDeliveryChargeInfo(String tenancyID,JSONObject condition, String conditions) throws Exception;
	
	public String saveOrUpdate(String tenancyID, JSONObject obj) throws Exception,SystemException;
	
	public Boolean saveCopyDeliveryChargeInfo(String tenancyID, JSONObject obj) throws Exception,SystemException;
	
	public JSONObject loadCopyList(String tenancyID,JSONObject condition) throws Exception;

	public String customize(String tenancyId, Object param) throws Exception;

	public String getTargetTree(String tenentId, String chanel, String storeId,Integer classId,String mealsId)throws Exception;

	//查询初始化计价方式
	public String findValuationMethod(String tenentId, JSONObject obj)throws Exception;

}
