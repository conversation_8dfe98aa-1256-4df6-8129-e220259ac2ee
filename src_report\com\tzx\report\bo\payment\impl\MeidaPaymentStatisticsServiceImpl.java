package com.tzx.report.bo.payment.impl;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.HttpClientPostUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.payment.service.MeidaPaymentStatisticsService;
import com.tzx.report.common.util.ReportExportUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;


@Service(MeidaPaymentStatisticsService.NAME)
public class MeidaPaymentStatisticsServiceImpl implements MeidaPaymentStatisticsService
{
	
	private static final Logger	 log	= Logger.getLogger(MeidaPaymentStatisticsServiceImpl.class);
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Override
	public JSONObject getMeidaPaymentStatisticsQuery(String tenancyID,JSONObject json) throws Exception {
		  /*json = new JSONObject () ;
		  json.put("tenancyId", "mianxiang1");
		  json.put("storeId", "373,23,43,2423");
		  json.put("startDate", "");
		  json.put("endDate", "");
		  json.put("pageNum", "1");
		  json.put("pageSize", "10");
		  json.put("outTradeNo", "983099a1086d2747b23861d0aa6f37fd");*/

		  StringBuilder sb = new StringBuilder("select array_to_string(ARRAY(select DISTINCT merchant_id  from sys_payment_account_config where store_id in("+json.optString("storeId")+") and  payment_channel='6' and valid_state='1'),',') as poiid ");
		  try {
				List<JSONObject> list =  this.dao.query4Json(tenancyID,sb.toString());
				if(list.size()>0)
				{
					json.put("merchantIds",list.get(0).optString("poiid"));
				}

		  } catch (Exception e) {
			e.printStackTrace();
		  }

		JSONObject resultJson = new JSONObject () ;
		  String omUrl = Constant.getSystemMap().get("omUrl");

		  String IPString = "http://om.e7e6.net/api/xmdOrders/listOrderPage";
		  if(omUrl!=null)
		  {
			if(omUrl.lastIndexOf("/")!=(omUrl.length()-1))
			{
				omUrl+="/";
			}
			IPString = omUrl+"api/xmdOrders/listOrderPage";

		  }
		  if(json.containsKey("IPString")) {
			  IPString = json.optString("IPString");
		  }
		  /*List<JSONObject> list =g  this.dao.query4Json(tenancyID,"SELECT SQL as ip FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_177' AND SQL_TYPE='OMIP'");
		  if(list.size()>0){
			  IPString= list.get(0).optString("ip");*/
			  log.info("om接口地址====>>"+IPString+"<<");
		  /*}else {
			  resultJson.put("status", "fail");
			  resultJson.put("msc", "查询地址为空");
			  return  resultJson; 
		  }*/
		  String request = HttpClientPostUtil.sendPostRequest(IPString, json.toString(), 100000 , 100000);
		  resultJson = JSONObject.fromObject(request);
		  
		  log.info("om返回的数据"+resultJson);

		try {
			JSONArray jr  = resultJson.getJSONArray("rows");
			StringBuilder poiid = new StringBuilder();
			for(int i=0;i<jr.size();i++)
			{
				JSONObject jb2 = jr.getJSONObject(i);
				if(jb2.optString("storeName").equals("\"null\"")|| jb2.optString("storeName").equals("null")||jb2.optString("storeName").length()==0)
				{
					poiid.append(",'"+jb2.optString("merchantId").trim()+"'");
				}
			}
			if(poiid.length()>0)
			{
				poiid.delete(0,1);
				sb.setLength(0);
				sb.append("select (CASE aa.store_id when 0 then '总部' else oo.org_full_name end) as org_full_name,aa.partner from (select  spac.partner,spac.store_id from sys_payment_account_config spac  where spac.partner in("+poiid+") and spac.valid_state='1' ) aa LEFT JOIN organ oo on aa.store_id=oo.id");
				List<JSONObject> list2 =  this.dao.query4Json(tenancyID,sb.toString());
				Map<String,String> quc = new HashMap<String,String>();
				Map<String,String> mapnp = new HashMap<String,String>();
				for(JSONObject jo:list2)
				{
					String partner = jo.optString("partner");
					String org_full_name = jo.optString("org_full_name");
					String qun = partner+org_full_name;
					if(mapnp.containsKey(partner))
					{
						if(quc.containsKey(qun))
						{
							continue;
						}
						quc.put(qun,partner);
						String nk = mapnp.get(partner)+" / " +org_full_name;
						mapnp.put(partner,nk);
						continue;
					}
					quc.put(qun,partner);
					mapnp.put(partner,org_full_name);
				}
				for(int i=0;i<jr.size();i++)
				{
					JSONObject jb2 = jr.getJSONObject(i);
					if(jb2.optString("storeName").equals("\"null\"")|| jb2.optString("storeName").equals("null")||jb2.optString("storeName").length()==0)
					{
						String merchantId = jb2.optString("merchantId");
						if(mapnp.containsKey(merchantId))
						{
							jb2.put("storeName",mapnp.get(merchantId));
							continue;
						}
					}
				}
				resultJson.put("rows",jr);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}


		List<JSONObject> footerList =new ArrayList<JSONObject>();
		JSONObject jsonCount = new JSONObject () ;
		jsonCount.put("totalFee", resultJson.optString("totalMoney"));
		jsonCount.put("poundageFee",resultJson.optString("totalPoundagefee"));
		jsonCount.put("outTradeNo", "");
		jsonCount.put("storeName", "合计");
		jsonCount.put("orderTime", "");
		jsonCount.put("type", json.optString("selectType"));
		footerList.add(jsonCount);
		resultJson.put("footer", footerList);
		  
		return  resultJson;
	}

	@Override
	public JSONObject getMeidaPaymentAccountQuery(String tenancyID, JSONObject json) throws Exception {
		  /*String selectType = json.getString("selectType");
		  json = new JSONObject () ;
		  json.put("storeId", "0,57,45,58,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,55,56,4,46,47,44,1,32,33,34,35,36,37,38,59,5,39,6,49,50,51,52,48,2,40,41,42,43,54,53");
		  json.put("startDate", "2018-05-01 00:00:00");
		  json.put("endDate", "2018-05-22 23:59:59");
		  json.put("page", "1");
		  json.put("rows", "10");
		  json.put("merchantIds","*********");
		  json.put("selectType","-1");
		  json.put("billNum", null);*/

		StringBuilder sb = new StringBuilder("select array_to_string(ARRAY(select DISTINCT merchant_id  from sys_payment_account_config where store_id in("+json.optString("storeId")+") and  payment_channel='6' and valid_state='1'),',') as poiid ");
		try {
			List<JSONObject> list =  this.dao.query4Json(tenancyID,sb.toString());
			if(list.size()>0)
			{
				json.put("merchantIds",list.get(0).optString("poiid"));
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		JSONObject resultJson = new JSONObject () ;
		String omUrl = Constant.getSystemMap().get("omUrl");

		String IPString = "http://om.e7e6.net/api/xmdOrders/listOrderPage";
		if(omUrl!=null)
		{
			if(omUrl.lastIndexOf("/")!=(omUrl.length()-1))
			{
				omUrl+="/";
			}
			IPString = omUrl+"api/xmdOrders/listAccount";

		}
		if(json.containsKey("IPString")) {
			IPString = json.optString("IPString");
		}
		/*  List<JSONObject> list = this.dao.query4Json(tenancyID,"SELECT SQL as ip FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_177' AND SQL_TYPE='OMIP'");
		  if(list.size()>0){
			  IPString= list.get(0).optString("ip");*/
		log.info("om接口地址====>>"+IPString+"<<");
		 /* }else {
			  resultJson.put("status", "fail");
			  resultJson.put("msc", "查询地址为空");
			  return  resultJson;
		  }*/
		String request = HttpClientPostUtil.sendPostRequest(IPString, json.toString(), 100000 , 100000);
		resultJson = JSONObject.fromObject(request);
		//resultJson = this.getLocalMeidaPaymentAccountQuery(null,json);

		log.info("导出返回的数据"+resultJson);

		try {
			JSONArray jr  = resultJson.getJSONArray("rows");
			StringBuilder poiid = new StringBuilder();
			for(int i=0;i<jr.size();i++)
			{
				JSONObject jb2 = jr.getJSONObject(i);
				if(jb2.optString("storename").equals("\"null\"")|| jb2.optString("storename").equals("null")||jb2.optString("storename").length()==0)
				{
					poiid.append(",'"+jb2.optString("merchantid").trim()+"'");
				}
			}
			if(poiid.length()>0)
			{
				poiid.delete(0,1);
				sb.setLength(0);
				sb.append("select (CASE aa.store_id when 0 then '总部' else oo.org_full_name end) as org_full_name,aa.partner from (select  spac.partner,spac.store_id from sys_payment_account_config spac  where spac.partner in("+poiid+") and spac.valid_state='1' ) aa LEFT JOIN organ oo on aa.store_id=oo.id");
				List<JSONObject> list2 =  this.dao.query4Json(tenancyID,sb.toString());
				Map<String,String> quc = new HashMap<String,String>();
				Map<String,String> mapnp = new HashMap<String,String>();
				for(JSONObject jo:list2)
				{
					String partner = jo.optString("partner");
					String org_full_name = jo.optString("org_full_name");
					String qun = partner+org_full_name;
					if(mapnp.containsKey(partner))
					{
						if(quc.containsKey(qun))
						{
							continue;
						}
						quc.put(qun,partner);
						String nk = mapnp.get(partner)+" / " +org_full_name;
						mapnp.put(partner,nk);
						continue;
					}
					quc.put(qun,partner);
					mapnp.put(partner,org_full_name);
				}
				for(int i=0;i<jr.size();i++)
				{
					JSONObject jb2 = jr.getJSONObject(i);
					if(jb2.optString("storename").equals("\"null\"")|| jb2.optString("storename").equals("null")||jb2.optString("storename").length()==0)
					{
						String merchantId = jb2.optString("merchantid");
						if(mapnp.containsKey(merchantId))
						{
							jb2.put("storename",mapnp.get(merchantId));
							continue;
						}
					}
				}
				resultJson.put("rows",jr);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}


		List<JSONObject> footerList =new ArrayList<JSONObject>();
		JSONObject jsonCount = new JSONObject () ;

		jsonCount.put("chayi", resultJson.containsKey("totalchayi")?resultJson.optString("totalchayi"):null);
		jsonCount.put("orderfee",resultJson.optString("totalOrderFee"));
		jsonCount.put("settlefee",resultJson.optString("totalFee"));
		jsonCount.put("poundagefee",resultJson.optString("totalPoundagefee"));
		jsonCount.put("totalamount",resultJson.optString("totalAmount"));

		jsonCount.put("outTradeNo", "");
		jsonCount.put("storename", "合计");
		jsonCount.put("orderTime", "");
		jsonCount.put("type", json.optString("selectType"));
		footerList.add(jsonCount);
		resultJson.put("footer", footerList);

		return  resultJson;
	}

	@Override
	public JSONObject getLocalMeidaPaymentAccountQuery(String tenancyID, JSONObject json) throws Exception {
		StringBuilder sb = new StringBuilder("select DISTINCT merchant_id as poiid from sys_payment_account_config where store_id in("+json.optString("storeId")+") and  payment_channel='6' and valid_state='1'");
		String merchantIds = "";
		try {
			List<JSONObject> list =  this.dao.query4Json(tenancyID,sb.toString());
			if(list.size()>0)
			{
				for(JSONObject poiJson:list){
					String poiid = poiJson.getString("poiid");
					if(StringUtils.isNotBlank(poiid) && !"null".equals(poiid)){
						merchantIds +=",'"+poiid+"'";
					}
				}
				merchantIds = merchantIds.substring(1);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		JSONObject resultJson = new JSONObject () ;

		StringBuilder countSql = new StringBuilder();
		countSql.append("SELECT  SUM (s.settle_fee) as totalFee, sum(s.poundage_fee) as totalPoundageFee,sum(s.order_fee) as totalOrderFee,");
		countSql.append("sum((abs(s.order_fee) - o.total_amount)) as chayi,sum(o.total_amount) as totalamount ");
		countSql.append("FROM xmd_settlement_detail s JOIN pos_payment_order o ON s.out_trade_no=o.out_trade_no where 1=1 and o.final_state=1");

		StringBuilder sbsql = new StringBuilder();
		sbsql.append("SELECT DISTINCT s.tenancy_id AS tenancyId,s.tenancy_name AS tenancyName,a.org_full_name AS storeName,");
		sbsql.append("substr(o.bill_num,1,CASE WHEN POSITION ('_' IN o.bill_num) > 0 THEN (POSITION ('_' IN o.bill_num) - 1) WHEN POSITION ('@' IN o.bill_num) > 0 THEN (POSITION ('@' IN o.bill_num) - 1) ELSE LENGTH (o.bill_num) END) AS billNum,");
		sbsql.append("s.pay_type AS payType,s.merchant_id AS merchantId,(ABS (s.order_fee) - o.total_amount) AS chayi,");
		sbsql.append("o.create_time AS createTime,(CASE final_state::TEXT WHEN '4' THEN concat('-',total_amount) ELSE total_amount::TEXT END) AS totalAmount,o.status,s.order_fee AS orderFee,s.poundage_fee AS poundageFee,");
		sbsql.append("s.settle_fee AS settleFee,s.pay_status AS payStatus,s.out_trade_no AS outTradeNo,s.trade_time AS tradeTime ");
		sbsql.append("FROM xmd_settlement_detail s  JOIN pos_payment_order o ON s.out_trade_no=o.out_trade_no ");
		sbsql.append("left join organ a on a.id::TEXT=o.store_id ");
		sbsql.append("where 1=1");
		if(json.containsKey("startDate") && StringUtils.isNotBlank(json.optString("startDate"))){
			sbsql.append("AND s.trade_time >= '"+json.optString("startDate"));
			sbsql.append("'::TIMESTAMP ");

			countSql.append("AND s.trade_time >= '"+json.optString("startDate"));
			countSql.append("'::TIMESTAMP ");
		}
		if(json.containsKey("endDate") && StringUtils.isNotBlank(json.optString("endDate"))){
			sbsql.append("AND s.trade_time <= '"+json.optString("endDate"));
			sbsql.append("'::TIMESTAMP ");

			countSql.append("AND s.trade_time <= '"+json.optString("endDate"));
			countSql.append("'::TIMESTAMP ");
		}
		if(StringUtils.isNotBlank(merchantIds)){
			sbsql.append("and s.merchant_id in ("+merchantIds);
			sbsql.append(") ");

			countSql.append("and s.merchant_id in ("+merchantIds);
			countSql.append(") ");
		}
		if(json.containsKey("billNum") && StringUtils.isNotBlank(json.optString("billNum"))){
			sbsql.append("AND o.bill_num LIKE CONCAT('%','"+json.optString("billNum"));
			sbsql.append("','%') ");

			countSql.append("AND o.bill_num LIKE CONCAT('%','"+json.optString("billNum"));
			countSql.append("','%') ");
		}
		if(json.containsKey("outTradeNo") && StringUtils.isNotBlank(json.optString("outTradeNo"))){
			String outTradeNo = json.getString("outTradeNo");
			String tradeNo = "";
			if(StringUtils.isNotBlank(outTradeNo)){
				String[] no = outTradeNo.split(",");
				for(int j=0;j<no.length;j++){
					tradeNo +="'"+no[j]+"',";
				}
			}
			if(StringUtils.isNotBlank(tradeNo)){
				sbsql.append("AND s.out_trade_no in ("+tradeNo.substring(0,tradeNo.length()-1));
				sbsql.append(") ");
			}
		}
		if(json.containsKey("selectType") && StringUtils.isNotBlank(json.optString("selectType"))){
			if(!StringUtils.equals("-1",json.optString("selectType"))){
				String[] paytypes = json.optString("selectType").split(",");
				String paytype = "";
				for(int i=0;i<paytypes.length;i++){
					paytype += ",'"+paytypes[i]+"'";
				}

				sbsql.append("and s.pay_type IN ("+paytype.substring(1,paytype.length()));
				sbsql.append(") ");

				countSql.append("and s.pay_type IN ("+paytype.substring(1,paytype.length()));
				countSql.append(") ");
			}

		}
		if(json.containsKey("isDiff") && StringUtils.isNotBlank(json.optString("isDiff"))){
			String isDiff = json.optString("isDiff");
			if(StringUtils.equals("1",isDiff)){
				sbsql.append("AND abs(s.order_fee)!=abs(o.total_amount) ");

				countSql.append("AND abs(s.order_fee)!=abs(o.total_amount) ");
			}
		}

		json.put("sort","s.trade_time");
		json.put("order","desc");

		long total = this.dao.countSql(tenancyID,sbsql.toString());
		resultJson.put("total",total);

		List<JSONObject> jsonList = this.dao.query4Json(null,this.dao.buildPageSql(json,sbsql.toString()));
		resultJson.put("rows",jsonList);

		if(jsonList.size()>0 && json.get("outTradeNo")==null){
			List<JSONObject> countList = this.dao.query4Json(tenancyID,countSql.toString());
			if(countList.size()>0){
				JSONObject countJson = countList.get(0);

				double totalFee = 0.00;

				if(countJson.containsKey("totalfee") && countJson.get("totalfee")!=null && !"null".equals(countJson.optString("totalfee"))){
					totalFee = new BigDecimal(countJson.optString("totalfee")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
				}
				double totalPoundageFee = 0.00;
				if(countJson.containsKey("totalpoundagefee") && countJson.get("totalpoundagefee")!=null && !"null".equals(countJson.optString("totalpoundagefee"))){
					totalPoundageFee = new BigDecimal(countJson.optString("totalpoundagefee")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
				}
				double totalOrderFee = 0.00;
				if(countJson.containsKey("totalorderfee") && countJson.get("totalorderfee")!=null && !"null".equals(countJson.optString("totalorderfee"))){
					totalOrderFee = new BigDecimal(countJson.optString("totalorderfee")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
				}
				double chayi = 0.00;
				if(countJson.containsKey("chayi") && countJson.get("chayi")!=null && !"null".equals(countJson.optString("chayi"))){
					chayi = new BigDecimal(countJson.optString("chayi")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
				}
				double amount = 0.00;
				if(countJson.containsKey("totalamount") && countJson.get("totalamount")!=null && !"null".equals(countJson.optString("totalamount"))){
					amount = new BigDecimal(countJson.optString("totalamount")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
				}

				//结算金额
				resultJson.put("totalFee", totalFee);
				//手续费
				resultJson.put("totalPoundagefee",totalPoundageFee);
				//订单金额
				resultJson.put("totalOrderFee",totalOrderFee);
				//总差异
				resultJson.put("totalchayi",chayi);
				//总订单金额
				resultJson.put("totalAmount",amount);
			}
		}


		List<JSONObject> footerList =new ArrayList<JSONObject>();
		JSONObject jsonCount = new JSONObject () ;

		jsonCount.put("chayi", resultJson.containsKey("totalchayi")?resultJson.optString("totalchayi"):null);
		jsonCount.put("orderfee",resultJson.optString("totalOrderFee"));
		jsonCount.put("settlefee",resultJson.optString("totalFee"));
		jsonCount.put("poundagefee",resultJson.optString("totalPoundagefee"));
		jsonCount.put("totalamount",resultJson.optString("totalAmount"));

		jsonCount.put("outTradeNo", "");
		jsonCount.put("storename", "合计");
		jsonCount.put("orderTime", "");
		jsonCount.put("type", json.optString("selectType"));
		footerList.add(jsonCount);
		resultJson.put("footer", footerList);

		return  resultJson;
	}


	public HSSFWorkbook exportData(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception {
		// TODO Auto-generated method stub
		Integer rowNum=1;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",0);
		Integer stratIndex = 0 ;
		JSONObject findResult= getMeidaPaymentStatisticsQuery(attribute, p);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		  JSONObject out1Result =null;
			//创建sheet 表格   同时还可以设置名字!  
			  	HSSFSheet sheet1=workBook.createSheet("美大支付统计报表说明");
				String [] listTitleName = {"交易机构","新美大ID","交易日期","交易时间","账单编号","订单唯一编号","账单总额","手续费","结算金额","交易方式"};
				String [] dataName ={"storeName","merchantId","orderTime11","orderTime","billNum","outTradeNo","discount11","poundageFee","totalFee","type"};
				String [] dataType ={"String","String","String","String","String","String","0.00","0.00","0.00","String"};
				
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				if(list1.size()>0){
					for(JSONObject json1 : list1) {
						if(json1.optString("type").equals("0")) {
							json1.put("type", "支付宝");
						}else {
							json1.put("type", "微信");
							
						}
						// 调用到处方法；
						json1.put("orderTime11",  json1.getString("orderTime").substring(0,json1.getString("orderTime").indexOf(" ")));
						json1.put("discount11",  json1.getDouble("poundageFee")+json1.getDouble("totalFee"));
						out1Result =ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
						stratIndex =out1Result.optInt("rowNum");
						paramData.put("rowNum", out1Result.opt("rowNum"));
						paramData.put("jin", out1Result.optInt("jin"));
					}
				}
					
					sheet1.groupRow(1,out1Result.optInt("rowNum"));
					sheet1.setRowSumsBelow(false);
					sheet1.setRowSumsRight(false);
		return workBook;
	}

	@Override
	public HSSFWorkbook exportAccountData(String attribute, JSONObject p, HSSFWorkbook workBook) throws Exception {
		// TODO Auto-generated method stub
		Integer rowNum=1;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",0);
		Integer stratIndex = 0 ;
		JSONObject findResult= getLocalMeidaPaymentAccountQuery(attribute, p);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		JSONObject out1Result =null;
		//创建sheet 表格   同时还可以设置名字!
		HSSFSheet sheet1=workBook.createSheet("美大支付对账报表说明");
		String [] listTitleName = {"交易门店","POIID","账单号","支付类型","交易时间","差异结果","订单金额","交易状态","原订单金额","手续费","结算金额","交易状态","接入方订单号","交易日期"};
		String [] dataName ={"storename","merchantid","billnum","paytype","createtime","chayi","totalamount","status","orderfee","poundagefee","settlefee","paystatus","outtradeno","tradetime"};
		String [] dataType ={"String","String","String","String","String","0.00","0.00","String","0.00","0.00","0.00","String","String","String"};

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if(list1.size()>0){
			for(JSONObject json1 : list1) {
				if(json1.optString("type").equals("0")) {
					json1.put("type", "支付宝");
				}else {
					json1.put("type", "微信");

				}
				if(StringUtils.isNotBlank(json1.optString("paytype"))){
					String paytypeen = json1.optString("paytype");
					if("wx_barcode_pay".equals(paytypeen)){
						json1.put("paytype","微信条码支付");
					}else if("ali_barcode_pay".equals(paytypeen)){
						json1.put("paytype","支付宝条码支付");
					}else if("wx_scan_pay".equals(paytypeen)){
						json1.put("paytype","微信扫码支付");
					}else if("ali_scan_pay".equals(paytypeen)){
						json1.put("paytype","支付宝扫码支付");
					}
				}

				/*if(StringUtils.isNotBlank(json1.optString("createtime"))){
					String createtime = json1.optString("createtime");
					if(StringUtils.isNotBlank(createtime) && !StringUtils.equals("null",createtime)){
                        Date d = new Date(Long.parseLong(createtime));
                        String t = sdf.format(d);
                        json1.put("createtime",t);
                    }
				}*/
				/*if(StringUtils.isNotBlank(json1.optString("tradetime"))){
					String tradetime = json1.optString("tradetime");
                    if(StringUtils.isNotBlank(tradetime) && !StringUtils.equals("null",tradetime)){
                        Date d = new Date(Long.parseLong(tradetime));
                        json1.put("tradetime",sdf.format(d));
                    }
				}*/

				if(StringUtils.isNotBlank(json1.optString("paystatus"))){
					String paystatus = json1.optString("paystatus");
					if("0".equals(paystatus)){
						json1.put("paystatus","支付");
					}else if("1".equals(paystatus)){
						json1.put("paystatus","撤销");
					}else if("2".equals(paystatus)){
						json1.put("paystatus","退款");
					}else if("4".equals(paystatus)){
						json1.put("paystatus","押金扣款");
					}else if("6".equals(paystatus)){
						json1.put("paystatus","押金扣款");
					}else if("7".equals(paystatus)){
						json1.put("paystatus","银行退单");
					}else if("8".equals(paystatus)){
						json1.put("paystatus","隔日退款");
					}else if("9".equals(paystatus)){
						json1.put("paystatus","调单暂缓");
					}else if("10".equals(paystatus)){
						json1.put("paystatus","调单暂缓");
					}else if("16".equals(paystatus)){
						json1.put("paystatus","活动_支付");
					}else if("17".equals(paystatus)){
						json1.put("paystatus","活动_撤销");
					}else if("18".equals(paystatus)){
						json1.put("paystatus","活动_退款");
					}
				}

				if(StringUtils.isNotBlank(json1.optString("status"))){
					String status = json1.optString("status");
					if("1".equals(status)){
						json1.put("status","支付成功");
					}else if("2".equals(status)){
						json1.put("status","处理中");
					}else if("3".equals(status)){
						json1.put("status","未知");
					}else if("4".equals(status)){
						json1.put("status","已退款");
					}else if("5".equals(status)){
						json1.put("status","退款中");
					}else if("6".equals(status)){
						json1.put("status","已取消");
					}else if("7".equals(status)){
						json1.put("status","退款失败");
					}else if("7".equals(status)){
						json1.put("status","退款失败");
					}else if("8".equals(status)){
						json1.put("status","取消中");
					}else if("9".equals(status)){
						json1.put("status","取消失败");
					}
				}

				out1Result =ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
				stratIndex =out1Result.optInt("rowNum");
				paramData.put("rowNum", out1Result.opt("rowNum"));
				paramData.put("jin", out1Result.optInt("jin"));
			}
		}

		sheet1.groupRow(1,out1Result.optInt("rowNum"));
		sheet1.setRowSumsBelow(false);
		sheet1.setRowSumsRight(false);
		return workBook;
	}


//	public static void main(String[] args) {
//		JSONObject resultJson = JSONObject.fromObject("{\"rows\":[{\"a\":1},{\"a\":1},{\"a\":1}]}");
//
//		JSONArray jr  = resultJson.getJSONArray("rows");
//		System.out.println(jr.size());
//	}
}
