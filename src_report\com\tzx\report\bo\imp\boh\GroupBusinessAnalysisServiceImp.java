package com.tzx.report.bo.imp.boh;

import com.tzx.report.bo.boh.GroupBusinessAnalysisService;
import com.tzx.report.po.boh.dao.GroupBusinessAnalysisDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */

@Service(GroupBusinessAnalysisService.NAME)
public class GroupBusinessAnalysisServiceImp implements GroupBusinessAnalysisService {


    @Resource(name = GroupBusinessAnalysisDao.NAME)
    private GroupBusinessAnalysisDao groupBusinessAnalysisDao;

    @Override
    public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
        return groupBusinessAnalysisDao.find(tenancyID, condition);
    }

    @Override
    public List<JSONObject> getClassItems(String tenancyID, JSONObject condition) throws Exception {
        return groupBusinessAnalysisDao.getClassItems(tenancyID, condition);
    }
}
