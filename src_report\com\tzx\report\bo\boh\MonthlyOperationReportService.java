package com.tzx.report.bo.boh;

import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import net.sf.json.JSONObject;

public interface MonthlyOperationReportService
{
	String NAME = "com.tzx.report.bo.imp.boh.MonthlyOperationReportServiceImp";
	
	public JSONObject getMonthlyOperationData(String tenancyID,JSONObject condition) throws Exception;
	
	public JSONObject getMonthlyOperationTopData(String tenancyID,JSONObject condition) throws Exception;

	public JSONObject getClassAndBusinessTypeDetails(String attribute, JSONObject p ) throws Exception;

	public HSSFWorkbook exportData(String attribute, JSONObject p,H<PERSON>FWorkbook workBook) throws Exception;
}
