package com.tzx.task.po.redis.dao;


import java.util.List;

import net.sf.json.JSONObject;


public interface TaskRedisDao {
	
	String NAME = "com.tzx.task.po.redis.dao.TaskRedisDaoImpl";
	
	void save(byte[] type,JSONObject jo);

	JSONObject read(byte[] key);
	
	boolean exists(byte[] key);
	
	Long len(byte[] key);
	
	List<JSONObject> get(byte[] key);
	
	void clear(byte[] key);
	
	void getMainTaskAndPushTask(byte[] key,byte[] key1);
	
	void clearIdentificationTask(byte[] key);
	
	void clearOutOfDateInformation(byte[] key);
	
	void update(byte[] key,Long index,JSONObject jo1);
	
	List<JSONObject> getTaskList(byte[] key);

	void lpush(byte[] type, JSONObject jsonObject);
	
	String getKey(String key); 
	
	boolean haskeyS(String key);
	
	void setKey(String key,String value);
	
	boolean containskey(String key); 
	
	void dishmain(String tenancyID);
	
	void wakeupmain(String tenancyID);
	
	void plpush(String tenancyID,JSONObject jb);
}
