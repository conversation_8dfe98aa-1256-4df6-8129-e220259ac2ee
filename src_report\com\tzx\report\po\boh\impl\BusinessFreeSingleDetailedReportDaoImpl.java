package com.tzx.report.po.boh.impl;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.BusinessFreeSingleDetailedReportDao;

import net.sf.json.JSONObject;

@Repository(BusinessFreeSingleDetailedReportDao.NAME)
public class BusinessFreeSingleDetailedReportDaoImpl implements BusinessFreeSingleDetailedReportDao{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource 
	ConditionUtils conditionUtils ;
	
	@Resource
	ParameterUtils parameterUtils;
	
	private final String sqlTypeDateL1 ="select sql from saas_report_engine where report_num = 'SAAS_BI_2017_69' and sql_type='MXL1'";
	private final String sqlTypeDateL0 ="select sql from saas_report_engine where report_num = 'SAAS_BI_2017_69' and sql_type='MXL0'";
	
	@Override
	public JSONObject find(String tenancyID, JSONObject condition) throws Exception
	{
		Integer type = condition.optInt("type");
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure =new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		String begindate = condition.optString("begin_date");
		String enddate = condition.optString("end_date");
		long total = 0L;
		if(begindate.length()>0 && enddate.length()>0 )
		{
			if(condition.containsKey("exportdataexpr") && !condition.optString("exportdataexpr").equals("''")){
				String exp = condition.optString("exportdataexpr");
				condition.element("exportdataexpr", conditionUtils.spilt(exp.substring(1,exp.length()-1)));
			}
			String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, sqlTypeDateL1);
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
				list = this.dao.query4Json(tenancyID,completionSql);
				structure = conditionUtils.getSqlStructure(tenancyID,completionSql);
			}else{
					total = this.dao.countSql(tenancyID,completionSql);
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql));
					footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, sqlTypeDateL0));
		    }
		}		 
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}
	 
}
