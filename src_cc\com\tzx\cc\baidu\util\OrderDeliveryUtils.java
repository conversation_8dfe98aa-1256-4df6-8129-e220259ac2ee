package com.tzx.cc.baidu.util;

import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;

/**
 * Created by flycat on 2017/12/19.
 * 订单下发工具类，处理将订单信息写入redis 部分数据
 */
public class OrderDeliveryUtils {

    private static final Logger logger = LoggerFactory.getLogger(OrderDeliveryUtils.class);

    private static final String redis_ip = "";

    //加载 delivery 订单下发redis 连接方式
    private static RedisTemplate redisTemplate ;

    static {
        redisTemplate = (RedisTemplate) SpringConext.getApplicationContext().getBean("deliveryRedisTemplate");
    }

    /**
     * 下发订单信息到redis
     */
    public static void orderDelivery(final String sendMsg,String order_code,String third_id,String tanencyId, String shop_id,String org_uuid) {

        if(!validateDeliveryFlag(tanencyId,shop_id)){
            return;
        }
        try{
            final String delivery_key = com.tzx.framework.common.constant.Constant.getSystemMap()
                    .get("qtoboh") + "_" + org_uuid;
            logger.info("==ordermiss code:{},thirdId:{} 保障下发至redis start!,order_delivery key:[{}]",order_code,third_id,delivery_key);

            //数据压入redis
            redisTemplate.execute(new RedisCallback<String>() {
                @Override
                public String doInRedis(RedisConnection connection)
                        throws DataAccessException {
                    return connection.lPush(delivery_key.getBytes(), sendMsg.getBytes()) + "";
                }
            });

        }catch (Exception e){
            logger.info("==ordermiss code:{},thirdId:{} delivery redis -->failed,reasion:{}",order_code,third_id,e.getCause().toString());
            logger.error("delivery failed detail info:",e);
            e.printStackTrace();
        }
        return;
    }

    /**
     * 校验门店是否开启参数
     * @return
     */
    private static boolean validateDeliveryFlag(String tanencyId, String shop_id){
        boolean is_start = false;
        String sql = "select para_value from sys_parameter where para_code = 'order_delivery_type' and valid_state='"
                + com.tzx.cc.base.Constant.VALID_STATE_TRUE + "' and store_id='"+shop_id+"'";
        List<JSONObject> list;
        try {
            DBContextHolder.setTenancyid(tanencyId);
            GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
            list = dao.query4Json(tanencyId, sql.toString());
            if (list == null || list.size() == 0) {
                is_start = false;
            } else {
                for (net.sf.json.JSONObject json : list) {
                    if ("1".equals(json.optString("para_value"))) {
                        is_start = true;
                        logger.info("==商户:{} 门店:{}] deliveryType 订单下发redis保障开关:[已开启]  ------ ",tanencyId,shop_id);
                    } else {
                        logger.info("==商户:{} 门店:{}] deliveryType 订单下发redis保障开关:[未开启]  ------ ",tanencyId,shop_id);
                        is_start = false;
                    }
                }
            }
        } catch (Exception e) {
            is_start = false;
            logger.info("==deliveryType: 未在数据库中检索到[商户:{} 门店:{}]的库信息  ---- ",tanencyId,shop_id);
            e.printStackTrace();
        }
        return is_start;
    }
}
