package com.tzx.cc.invoice.electronic.util;

import java.util.Date;

import org.apache.commons.lang.time.DateFormatUtils;

/**
 * 生成唯一值工具类
 * <AUTHOR>
 *
 */
public class UUID4EWMUtils {
	private static Date date = new Date();
	private static int seq = 0;
	private static final int ROTATION = 99;

	public static synchronized long next() {
		if (seq > ROTATION)
			seq = 0;
		date.setTime(System.currentTimeMillis());
		String format = DateFormatUtils.format(date, "HHmmss");
		seq++;
		return Long.parseLong(format+seq);
	}
}
