package com.tzx.cc.baidu.rest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.reflect.TypeToken;
import com.tzx.cc.baidu.bo.ShopService;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.log4j.util.LoggerUtil;

/**
 * <AUTHOR>
 * 
 */
@Controller("LogRest")
@RequestMapping("/thirdparty/logrest")
public class LogRest {

	private Logger log = LoggerFactory.getLogger(this.getClass());

	@Resource(name = ShopService.NAME)
	private ShopService shopService;

	/**
	 * 获取商户信息（本地）
	 * 
	 * @param request
	 * @param response
	 * @throws IOException 
	 */
	@RequestMapping(value = "getLogrest")
	@ResponseBody
	public JSONObject getLogrest(HttpServletRequest request,
			HttpServletResponse response) throws IOException {

		String path = "http://"+LoggerUtil.getQueryLoggerServerAddress()+"/logger/query";
		
        JSONObject reqParam= new JSONObject();
        JSONObject reqPage= new JSONObject();
        
        int page = Integer.parseInt(request.getParameter("page"));
        int rows = Integer.parseInt(request.getParameter("rows"));
        
        reqPage.put("pageNo", page);
        reqPage.put("pageSize", rows);
        
        //2017-11-10 组装查询条件start
        String filterRules=request.getParameter("filterRules");
        
        JSONObject json=new JSONObject();
        
        JSONArray jsonarry=new JSONArray();
        
        if(!StringUtils.isEmpty(filterRules)){
        	json.put("data", filterRules);
        }
        if(json.containsKey("data")){
          jsonarry = json.getJSONArray("data");
        }
        
        String strFilterArray="";
        
        SimpleDateFormat df = new SimpleDateFormat ("yyyyMMdd"); 
        String strDate=df.format(new Date());
        
        //如果没有给出日期就给当前日期
        reqParam.put("date",strDate);
	    JSONObject resultObject=new JSONObject();
	    
        try{
        for(int i=0;i<jsonarry.size();i++){
        	 JSONObject jsonUnitParam=JSONObject.fromObject(jsonarry.get(i));
        	 if(jsonUnitParam.optString("op").equals("contains")){
        		 jsonUnitParam.put("op", "eq");
        	 }
        	 
             if(jsonUnitParam.optString("field").equals("createTime")){
            	 reqParam.put("date", jsonUnitParam.optString("value").replace("-", "").replace("-", ""));
            	 jsonUnitParam.put("value", DateUtil.date2TimeStamp(jsonUnitParam.optString("value")+" 00:00:00", "yyyy-MM-dd hh:mm:ss"));
            	 jsonUnitParam.put("op", "gte");
             }else{
            	 
            	 if(StringUtils.isEmpty(strFilterArray)){
            		 strFilterArray=jsonUnitParam.toString(); 
            	 }else{
            		 strFilterArray=strFilterArray+","+jsonUnitParam.toString(); 
            	 }
             }  
        	 
        }
        

        reqParam.put("type", "customize");
        reqParam.put("tenancyId", request.getSession().getAttribute("tenentid"));
         
        if(StringUtils.isEmpty(filterRules)||filterRules.equals("[]")){
            reqParam.put("date",strDate);	 	
        }else{
          if(!StringUtils.isEmpty(strFilterArray)){
        	  reqParam.put("conditions", "["+strFilterArray+"]"); 
          }
        }
        
        reqParam.put("pagination", reqPage.toString());//结果分页
      //2017-11-10 组装查询条件end
        
        JSONObject result=sendPost(path, reqParam.toString());//调用外围系统查询
        
        JSONArray arr=result.optJSONArray("data");
	    List<JSONObject> documentList = new ArrayList<JSONObject>();
	    if(arr.size()>0){
	    	for(int i=0;i<arr.size();i++){
	    	 	JSONObject returnObject=new JSONObject();
	    	 	JSONObject dishTrNullObj=checkAndTransForDishesNull(arr.getJSONObject(i));
	    		returnObject=transFormValueIsNull(dishTrNullObj);
	    		documentList.add(returnObject);
	    	}
	    }
	    
	    resultObject.put("total", result.optJSONObject("page").optInt("totalCount"));
	    resultObject.put("rows", documentList);
        }catch(Exception e){
    		e.printStackTrace();
    	}
	    return resultObject;
	  

	}

	/**
	 * 当请求参数requestBody中的值含有null时，需转成""
	 * @param omJsonObject
	 * @return
	 */
	public JSONObject checkAndTransForDishesNull(JSONObject omJsonObject) {
		JSONObject omTResult=JSONObject.fromObject(omJsonObject);
		if(omJsonObject.containsKey("requestBody")&&omJsonObject.optJSONObject("requestBody")!=null) {
			if(omJsonObject.optJSONObject("requestBody").containsKey("dishes")) {
				JSONArray dataArr=new JSONArray();
				omTResult.optJSONObject("requestBody").remove("dishes");
				dataArr=omJsonObject.optJSONObject("requestBody").optJSONArray("dishes");
				JSONArray dishArr=checkRequestBodyNullValue(dataArr);
				omTResult.optJSONObject("requestBody").put("dishes",dishArr);
			}
			if(omJsonObject.optJSONObject("requestBody").containsKey("dishes_class")){
				JSONArray dataArr=new JSONArray();
				omTResult.optJSONObject("requestBody").remove("dishes_class");
				dataArr=omJsonObject.optJSONObject("requestBody").optJSONArray("dishes_class");
				JSONArray dishClassArr=checkRequestBodyNullValue(dataArr);
				omTResult.optJSONObject("requestBody").put("dishes_class",dishClassArr);
			}
			if(omJsonObject.optJSONObject("requestBody").containsKey("data")){
				JSONArray dataArr=new JSONArray();
				omTResult.optJSONObject("requestBody").remove("data");
				dataArr=omJsonObject.optJSONObject("requestBody").optJSONArray("data");
				JSONArray reDataArr=checkRequestBodyNullValue(dataArr);
				omTResult.optJSONObject("requestBody").put("data",reDataArr);
			}
		}
		return omTResult;
	}

	//转返回值中的null字符串
	private JSONArray checkRequestBodyNullValue(JSONArray dataArr) {
		JSONArray dishes=new JSONArray();
		if(dataArr!=null){
			for(int i=0;i<dataArr.size();i++){
				JSONObject dishObjct=transFormValueIsNull(dataArr.getJSONObject(i));
				dishes.add(dishObjct);
			}
		}
		return dishes;
	}
	/**
	 * 2017-11-10 当JSONObject的key对应的value为null时，需转成""
	 * @param jsonObject
	 * @return
	 */
    private JSONObject transFormValueIsNull(JSONObject jsonObject) {
    	JSONObject result=new JSONObject();
    	Iterator<String> it=jsonObject.keys();
    	try {
    	while(it.hasNext()){
    		String keyName=it.next();
    		String keyValue=jsonObject.optString(keyName);
    		if(keyValue.equals(null)||keyValue.equals("null")){
    			result.put(keyName, "");
    		}else{
    			result.put(keyName, keyValue);
    		}	
    	}
    	}catch (Exception e) {
    		System.out.println("ERR："+it.next()+"\r\n"+jsonObject.optString(it.next()));
    		e.printStackTrace();
    	}
		return result;
	}


	/**
     *对于esayui中的数据为了满足mongoDB的格式，特做如下处理
     */
    public static String mongoFilterRules(String filterRules) {
    	String mongoStrs="";
		if (filterRules != null && !"[]".equals(filterRules)) {
			String filterRulesStr = filterRules.substring(1, filterRules.length()-1);
			String replaceFilterRulesStr = filterRulesStr.replace("},", ";");
			String trimFilterRulesStr = replaceFilterRulesStr.substring(1, replaceFilterRulesStr.length()-1);
            String[] filterArray = trimFilterRulesStr.split(";");
            for(int i=0;i<filterArray.length;i++){
            	String[] filters=filterArray[i].split(",");
            	for(int j=0;j<filters.length;j++){
            		if(filters[1].equals("contains")){
            			filters[1]="=";
            		}
            	}
            }
		}
        return mongoStrs;
    } 
    
	 /**
     * 向指定 URL 发送POST方法的请求
     * 
     * @param url
     *            发送请求的 URL
     * @param param
     *            请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static JSONObject sendPost(String url, String param) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        JSONObject js=null;
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", " application/json");
            conn.setRequestProperty("Accept-Charset", "UTF-8");
            conn.setRequestProperty("contentType", "UTF-8");
            
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            
            conn.setConnectTimeout(20000);  
            conn.setReadTimeout(300000); 
            
            
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            
            //JSONObject
            JSONObject jsonObject=JSONObject.fromObject(param);
            
//            if(jsonObject.containsKey("createTime")){
//            	jsonObject.put("date", jsonObject.optString("createTime"));
//            }
            
//            jsonObject.remove("createTime");
          
            // 发送请求参数
            out.print(jsonObject);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(),"UTF-8"));
            String line="";
            while ((line = in.readLine()) != null) {
            		result += line;
            }
            
            js=JSONObject.fromObject(result);
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！"+e);
            e.printStackTrace();
        }
        //使用finally块来关闭输出流、输入流
        finally{
            try{
                if(out!=null){
                    out.close();
                }
                if(in!=null){
                    in.close();
                }
            }
            catch(IOException ex){
                ex.printStackTrace();
            }
        }
        return js;
    }    
	
}
