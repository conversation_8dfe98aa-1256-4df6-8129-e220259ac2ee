package com.tzx.cc.bo.imp;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.google.gson.reflect.TypeToken;
import com.tzx.cc.bo.OrderingPeriodSetService;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;

@Service(OrderingPeriodSetService.NAME)
public class OrderingPeriodSetImpl implements OrderingPeriodSetService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Override
	public JSONObject loadOrderingPeriod(String tenancyID, JSONObject condition,String organIds) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
//		if (condition.containsKey("organ_code") && !condition.get("organ_code").equals("0") && !"".equals(condition.get("organ_code")))
//		{
			sql.append("select a.*,b.org_full_name,c.id as hq_organ_id,c.room_times from hq_lineup_time_org A left join organ b on b. ID = A .store_id left join hq_organ c on c.organ_id=a.store_id  where 1=1 ");
			if(null != condition && condition.containsKey("organ_code") && StringUtils.isNotBlank("organ_code")){
				sql.append(" and b.organ_code like '" + condition.get("organ_code") + "%'");
			}
			sql.append(" and a.store_id in ("+organIds+")");
			// int pagenum = condition.containsKey("page") ?
			// (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) :
			// 1;
			Set<String> keys = condition.keySet();

			for (String s : keys)
			{
				if ("type".equals(s) || "tableName".equals(s) || "page".equals(s) || "rows".equals(s) || "sort".equals(s) || "order".equals(s) || "sortName".equals(s))
				{
					continue;
				}
				if (!"text".equals(s) && !"t1".equals(s) && !"t2".equals(s) && !"organ_code".equals(s) && !"is_zb".equals(s) && !"organ_id".equals(s))
				{
					sql.append(" and a." + s + " like '" + condition.optString(s) + "%'");
				}

			}

			if (condition.optString("sort") != null && !"".equals(condition.optString("sort")))
			{
				sql.append(" order by " + condition.optString("sort") + " " + condition.optString("order"));
			}
			else
			{
				sql.append(" order by a.tenancy_id");
			}

			long total = this.dao.countSql(tenancyID, sql.toString());
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1
					: condition.getInt("page"))
					: 1;
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
			if (condition.containsKey("organ_code") && !condition.get("organ_code").equals("0") && !"".equals(condition.get("organ_code")))
			{
				sql.delete(0, sql.length());
				sql.append("select a.id, a.id as hq_organ_id,a.room_times from hq_organ a left join organ b on b.id=a.organ_id where ");
				sql.append("  b.organ_code = '" + condition.get("organ_code") + "'");
				List<JSONObject> hq_organ_list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
				String org_code = condition.optString("organ_code");
				String text = condition.optString("text");
				int maxList = list.size();
				if (maxList < 3 && condition.optInt("type") == 3)
				{
					for (int i = 0; i < 3 - maxList; i++)
					{
						JSONObject obj = JSONObject.fromObject("{}");
						obj.put("org_full_name", text.substring(org_code.length(), text.length()));
						list.add(obj);
						if (hq_organ_list.size() > 0)
						{
							obj.put("hq_organ_id", hq_organ_list.get(0).optString("hq_organ_id"));
							obj.put("room_times", hq_organ_list.get(0).optString("room_times"));
						}
	
					}
				}
			}

			 result.put("page", pagenum);
			 result.put("total", total);
			result.put("rows", list);
//		}
		return result;
	}

	@Override
	public Boolean saveOrderingPeriod(String tenancyID, JSONObject obj) throws Exception, SystemException
	{
		Boolean flag = true;
		Integer room_times = 0;
		Integer hq_organ_id = 0;
		if (obj.containsKey("periods") && obj.get("periods") != "")
		{
			@SuppressWarnings("unchecked")
			List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(obj.get("periods").toString(), new TypeToken<List<JSONObject>>()
			{
			}.getType());
			Iterator<JSONObject> it2 = list.iterator();
			while (it2.hasNext())
			{

				JSONObject period = it2.next();
				if (hq_organ_id == 0)
				{
					hq_organ_id = period.optInt("hq_organ_id");
				}
				if (period.containsKey("lineup_starttime"))
				{
					period.put("tenancy_id", obj.get("tenancy_id"));
					period.put("store_id", obj.optString("organ_id"));
					period.put("lineup_type", "02");
					period.put("valid_state", "1");
					period.put("last_operator", obj.get("last_operator"));
					period.put("last_updatetime", obj.get("last_updatetime"));

					if (period.containsKey("id") && Tools.hv(period.get("id").toString()))
					{
						this.dao.updateIgnorCase(tenancyID, "hq_lineup_time_org", period);
					}
					else
					{
						period.remove("id");
						this.dao.insertIgnorCase(tenancyID, "hq_lineup_time_org", period);
					}
				}
			}
		}
		if (obj.containsKey("room_times"))
		{
			room_times = obj.optInt("room_times");
			JSONObject hqOrganObj = new JSONObject();
			hqOrganObj.put("tenancy_id", obj.get("tenancy_id"));
			hqOrganObj.put("organ_id", obj.optString("organ_id"));
			if (hq_organ_id > 0)
			{
				hqOrganObj.put("id", hq_organ_id);
			}
			hqOrganObj.put("room_times", room_times);
			if (hqOrganObj.containsKey("id") && Tools.hv(hqOrganObj.get("id").toString()))
			{
				this.dao.updateIgnorCase(tenancyID, "hq_organ", hqOrganObj);
			}
			else
			{
				hqOrganObj.remove("id");
				hqOrganObj.put("cc_bill_state", "1");
				this.dao.insertIgnorCase(tenancyID, "hq_organ", hqOrganObj);
			}
		}
		return flag;
	}

	public JSONObject loadCopyOrderingPeriod(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		if (condition.containsKey("organ_id"))
		{
			sql.append("select a.*,b.org_full_name,c.id as hq_organ_id,c.room_times from hq_lineup_time_org A left join organ b on b. ID = A .store_id left join hq_organ c on c.organ_id=a.store_id  where 1=1 and a.lineup_starttime !='' and a.lineup_endtime !='' ");
			sql.append(" and a.store_id = " + condition.optInt("organ_id") + " ");
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		}
		return result;
	}

	@Override
	public Boolean saveCopyPeriods(String tenancyID, JSONObject obj) throws Exception, SystemException
	{
		Boolean flag = true;
		Integer room_times_copy = 0;
		String toIds = obj.getString("torgan_id");
		String[] ids = toIds.split(",");
		for (String id1 : ids)
		{
			StringBuilder sb = new StringBuilder();
			sb.delete(0, sb.length());
			sb.append(" DELETE  from hq_lineup_time_org  k where k.store_id= " + id1 + "");
			this.dao.execute(tenancyID, sb.toString());
			List<JSONObject> delListJson = new ArrayList<JSONObject>();
			JSONObject json = JSONObject.fromObject("{'id' : " + id1 + "}");
			delListJson.add(json);
			if (delListJson.size() > 0)
			{
				this.dao.deleteBatchIgnorCase(tenancyID, "hq_lineup_time_org", delListJson);
			}
			List<JSONObject> periodList = new ArrayList<JSONObject>();
			if (obj.containsKey("copy_periods") && obj.get("copy_periods") != "")
			{
				@SuppressWarnings("unchecked")
				List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(obj.get("copy_periods").toString(), new TypeToken<List<JSONObject>>()
				{
				}.getType());
				Iterator<JSONObject> it2 = list.iterator();
				while (it2.hasNext())
				{
					JSONObject period = it2.next();
					period.put("tenancy_id", obj.get("tenancy_id"));
					period.put("store_id", id1);
					period.put("lineup_type", "02");
					period.put("valid_state", "1");
					period.put("last_operator", obj.get("last_operator"));
					period.put("last_updatetime", obj.get("last_updatetime"));
					periodList.add(period);
				}
				if (periodList.size() > 0)
				{
					this.dao.insertBatchIgnorCase(tenancyID, "hq_lineup_time_org", periodList);
				}
			}

			if (obj.containsKey("room_times_copy") && !"".equals(obj.optString("room_times_copy")))
			{
				StringBuilder sbs = new StringBuilder();
				sbs.delete(0, sbs.length());
				sbs.append(" DELETE  from hq_organ  k where k.organ_id= " + id1 + "");
				this.dao.execute(tenancyID, sbs.toString());
				room_times_copy = obj.optInt("room_times_copy");
				JSONObject hqOrganObj = new JSONObject();
				hqOrganObj.put("tenancy_id", obj.get("tenancy_id"));
				hqOrganObj.put("organ_id", id1);
				hqOrganObj.put("room_times", room_times_copy);
				hqOrganObj.remove("id");
				this.dao.insertIgnorCase(tenancyID, "hq_organ", hqOrganObj);
			}
		}

		return flag;
	}

	@Override
	public String delete(String tenantId, String tableKey, List<JSONObject> keyList) throws Exception
	{
		String result = "";

		if (!Tools.hv(tableKey) || !Tools.hv(keyList))
		{
			result = "{\"success\" : false , \"msg\" : \"不能删除!\"}";
			return result;
		}
		if (tableKey != null)
		{
			result = "{\"success\" : true , \"msg\" : \"删除成功!\"}";

			this.dao.deleteBatchIgnorCase(tenantId, tableKey, keyList);
			return result;

		}
		return result;

	}

	@Override
	public String delete(String tenantId, String tableKey, JSONObject key) throws Exception
	{
		// TODO Auto-generated method stub
		return null;
	}
}
