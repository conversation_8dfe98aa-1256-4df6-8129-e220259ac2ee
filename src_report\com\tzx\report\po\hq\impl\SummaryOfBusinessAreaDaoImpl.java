package com.tzx.report.po.hq.impl;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.hq.dao.SummaryOfBusinessAreaDao;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

@Repository(SummaryOfBusinessAreaDao.NAME)
public class SummaryOfBusinessAreaDaoImpl implements SummaryOfBusinessAreaDao {
 
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	
	@Override
	public JSONObject getSummaryOfBusinessArea(String tenancyID, JSONObject condition) throws Exception {
        List<JSONObject> list = new ArrayList<JSONObject>();
        List<JSONObject> footerList = new ArrayList<JSONObject>();
        List<JSONObject> structure = new ArrayList<JSONObject>();
        JSONObject result = new JSONObject();
        long total = 0L;
        String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.BUSINESS_AREA_SUMMARY_QUERY_subtotal);
        total = this.dao.countSql(tenancyID, reportSql.toString());
        if (condition.containsKey("derivedtype") && condition.optInt("derivedtype") == 2) {
            list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReport(condition, reportSql.toString()));
            structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
        } else {
            list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, reportSql.toString()));
            footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.BUSINESS_AREA_SUMMARY_QUERY_amount_to).toString());
        }
        
        int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
        result.put("page", pagenum);
        result.put("total", total);
        result.put("rows", list);
        result.put("footer", footerList);
        result.put("structure", structure);
        return result;
    }
}
