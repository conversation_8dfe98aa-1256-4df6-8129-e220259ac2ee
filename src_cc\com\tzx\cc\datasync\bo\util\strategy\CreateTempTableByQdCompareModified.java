package com.tzx.cc.datasync.bo.util.strategy;

import com.tzx.cc.datasync.bo.dto.DataTransferDaoHelper;
import com.tzx.cc.datasync.bo.dto.PlanetVersionDataTransferDao;
import com.tzx.cc.datasync.bo.util.DriverUtils;
import com.tzx.cc.datasync.bo.util.SynUtils;
import com.tzx.cc.datasync.bo.util.TempTableUtils;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 以创建临时表的形式比较saas和rif的数据（只适用于saas多渠道，rif没有渠道概念，所以会把默认数据向各个渠道全复制一份）
 * 注意：只适用于rif无渠道，saas各个渠道数据都相等的情况
 * Created by XUGY on 2017-02-28.
 */
public class CreateTempTableByQdCompareModified implements CompareModified {
    /**
     * logger
     */
    private static final Logger logger = Logger.getLogger(CreateTempTableByQdCompareModified.class);

    private GenericDao dao;

    @Override
    public Object compare(Object... objs) throws Exception {
        String tenantId = (String) objs[0];
        List<JSONObject> rifData = (List<JSONObject>) objs[1];
        String fromTable = (String) objs[2];
        JSONObject compareFlag = (JSONObject) objs[3];
        List<JSONObject> dic_result_list = (List<JSONObject>) objs[4];
        Map<String, List<JSONObject>> resultMap = new HashMap<String, List<JSONObject>>();
        if (rifData == null || rifData.size() == 0) {
            return resultMap;
        }
        //创建临时表 存储同步过来的数据  格式同源库
        String createTempTable = TempTableUtils.createTempTable(tenantId, rifData, fromTable);
        List<JSONObject> rifDataTemp = new ArrayList<JSONObject>();
        try {
            SynUtils.addQd(rifData,dic_result_list);

            //将数据插入临时表中
            TempTableUtils.insertTempTable(tenantId, createTempTable, rifData);
            String chanelStr = SynUtils.getChanelStr(dic_result_list);

            List<JSONObject> updateList = getUpdateDataByQd(tenantId, fromTable, createTempTable,chanelStr);
            List<JSONObject> insertList = getInsertDataByQd(tenantId, fromTable, createTempTable,chanelStr);
            List<JSONObject> deleteList = getDeleteDataByQd(tenantId, fromTable, createTempTable,chanelStr);
            resultMap.put("add", insertList);
            resultMap.put("update", updateList);
            resultMap.put("delete", deleteList);
        } catch (Exception e) {
            logger.error(e);
            throw new Exception(e);
        } finally {
            TempTableUtils.dropTempTable(tenantId, createTempTable);
        }
        return resultMap;
    }

    /**
     * 获取需要更新的数据渠道更新专用
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getUpdateDataByQd(String tenantId, String fromTable,
                                               String tempTable,String chanelStr) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }

        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();
        sql.append("select distinct sou.* from ");
        sql.append(destName).append(" dest ,");
        sql.append(tempTable).append(" sou");
        sql.append(" where dest.fake_id = to_number(sou.fake_id,'9999999999') and sou.chanel = dest.chanel");
        sql.append(" and dest.fake_id is not NULL");
        sql.append(" and dest.chanel in ('");
        sql.append(chanelStr);
        sql.append("')");

        return TempTableUtils.executeQuery(sql.toString());
    }


    /**
     * 获取新增加的数据  根据渠道查询
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getInsertDataByQd(String tenantId, String fromTable,
                                               String tempTable,String chanelStr) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }
        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();

        sql.append("select a.* from ");
        sql.append(tempTable);
        sql.append(" a left join ").append(destName);
        sql.append(" b on to_number(a.fake_id,'9999999999') = b.fake_id and a.chanel = b.chanel");
        sql.append(" where b.fake_id is null ");
        sql.append(" and a.chanel in ('");
        sql.append(chanelStr);
        sql.append("')");

        return TempTableUtils.executeQuery(sql.toString());
    }

    /**
     * 获取需要删除的数据 根据渠道查询
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getDeleteDataByQd(String tenantId, String fromTable,
                                               String tempTable,String chanelStr) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }
        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();

        sql.append("select a.* from ");
        sql.append(destName);
        sql.append(" a left join ").append(tempTable);
        sql.append(" b on to_number(b.fake_id,'9999999999') = a.fake_id and a.chanel = b.chanel");
        sql.append(" where b.fake_id is null and a.fake_id is not null");
        sql.append(" and a.chanel in ('");
        sql.append(chanelStr);
        sql.append("')");

        return TempTableUtils.executeQuery(sql.toString());
    }
}
