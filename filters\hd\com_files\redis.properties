#\u6700\u5927\u5206\u914d\u7684\u5bf9\u8c61\u6570
redis.pool.maxTotal=70
#\u6700\u5927\u80fd\u591f\u4fdd\u6301idel\u72b6\u6001\u7684\u5bf9\u8c61\u6570
redis.pool.maxIdle=10
#\u5f53\u6c60\u5185\u6ca1\u6709\u8fd4\u56de\u5bf9\u8c61\u65f6\uff0c\u6700\u5927\u7b49\u5f85\u65f6\u95f4
redis.pool.maxWaitMillis=1000
#\u5f53\u8c03\u7528borrow Object\u65b9\u6cd5\u65f6\uff0c\u662f\u5426\u8fdb\u884c\u6709\u6548\u6027\u68c0\u67e5
redis.pool.testOnBorrow=false

#IP
redis.ip=*********
#redis.ip=127.0.0.1
#Port
redis.port=4379
#password
redis.pass=000

redis.useCache=true
redis.defaultExpireTime=1800

#OM-task-used  IP
omredis.ip=*********
#omredis.ip=127.0.0.1
#Port
omredis.port=4379

#takeway recevice order model used 
wroRedis.ip=*********
wroRedis.port=4379
wroRedis.pool.maxTotal=100
wroRedis.pool.maxIdle=10
wroRedis.pool.maxWaitMillis=4000
wroRedis.pool.testOnBorrow=false