package com.tzx.cc.datasync.bo.dto;

import net.sf.json.JSONObject;

/**
 * 
 * 行云表取数逻辑 oracle数据库
 */
public interface DataTransferDaoHelper
{
	String		SELECTOR			= "selector";			// 源where
															// 条件
	String		FROMTABLENAME		= "fromtablename";		// 源表名
	String		FROMTABLENICKNAME	= "fromtablenickname";	// 源表别名
	String		FROMFIELDS			= "fromfields";		// 源字段

	String		SELECTOR4TARGET		= "selector4target";	// 目的where
															// 条件
	String		TOTABLENAME			= "totablename";		// 目的表名
	String		TOTABLENICKNAME		= "totablenickname";	// 目的表别名
	String		TOFIELDS			= "tofields";			// 目的字段

	String		PRIMARYKEY			= "primarykey";		// 表主键
	String		VALUES				= "values";			// 值信息
	String		PARAMSLIST			= "paramslist";		// 参数列表

	String		FROMDB				= "1";					// MultiDatasourceContext.SQLSERVER_DATASOURCE;
	String		TODB				= "0";					// MultiDatasourceContext.POSTGRES_DATASOURCE;

	// 初始化时tofields,totablenickname没用到
	JSONObject	INIT_TABLE			= JSONObject
											.fromObject("{"
													
													// 价格体系设置
													+ "\"TZXERP.ERP_JGTXSZ\": {"
													+ "    \"totablename\": \"hq_price_system\","
													+ "    \"tofields\": \"id,price_system_name,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"u.id ,u.txmc as price_system_name,u.id as fake_id\","
													+ "    \"selector\": \" u \""
													+ "},"
//													 门店数据 同步机构树
													+ "\"TZXUUS.UUS_ORGAN\": {"
													+ "    \"totablename\": \"organ\","
													+ "    \"tofields\": \"id,organ_code,org_full_name,org_short_name,valid_state,top_org_id,org_type,price_system,complain_phone,phone,address,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"u.id AS id,u.org_code AS organ_code,(SELECT p.simplified FROM tzxpl.platform_language p  WHERE p.id=u.org_name) AS org_full_name,( SELECT  p.simplified FROM tzxpl.platform_language p  WHERE  p.id=u.org_name) AS org_short_name,case when u.status='PLATFORM_STATUS_ENABLE' THEN '1' else '0' END as valid_state,CASE WHEN Z.parent_id IS NULL THEN 0 ELSE Z.parent_id END AS TOp_org_id , CASE  WHEN u.org_type='PLATFORM_ORGAN_DEPARTMENT' OR  u.org_type='PLATFORM_ORGAN_FILIALE' THEN 1 WHEN u.org_type='PLATFORM_ORGAN_SHOP' THEN 3 END AS org_type ,k.jgtxid as price_system ,case when e.tsdh is null then ' ' else e.tsdh end as complain_phone,e.memo6 as phone,u.ADDRESS ,CASE WHEN LENGTH(u.ORG_CODE) =2  THEN 1  WHEN LENGTH(u.ORG_CODE) =4 THEN 2   WHEN LENGTH(u.ORG_CODE) =6 THEN 3 WHEN LENGTH(u.ORG_CODE) =8 THEN 4  WHEN LENGTH(u.ORG_CODE) =10 THEN 5 END AS organ_LEVEL,u.id as fake_id \","
													+ "    \"selector\": \"  u LEFT JOIN (SELECT ORGAN_PROPERTY_REF.* FROM tzxuus.UUS_ORGAN_PROPERTY_REF  ORGAN_PROPERTY_REF WHERE  ORGAN_PROPERTY_REF.ORGAN_PROPERTY_ID =( SELECT ag.ID AS ORGAN_PROPERTY_id FROM tzxuus.UUS_ORGAN_PROPERTY ag LEFT JOIN TZXPL.PLATFORM_LANGUAGE bg ON bg.ID=ag.PROPERTY_NAME WHERE  bg.SIMPLIFIED='行政'))z ON z.organ_id=u.id LEFT join tzxerp.erp_orginfo e on u.id=e.JGXH left join tzxerp.Erp_Jgtxszfpjg k on k.jgxh=u.ID WHERE u.STATUS='PLATFORM_STATUS_ENABLE'AND u.ID !=0 \""
													+ "},"
//													// 菜品类别信息--
													+ "\"TZXERP.ERP_ORGSORTS\": {"
													+ "    \"totablename\": \"hq_item_class\","
													+ "    \"tofields\": \"id,itemclass_code,itemclass_name,father_id,chanel,fake_id,fake_type\","
													+ "    \"selector4target\": \" where fake_id is not null and fake_type = 1 \","
													+ "    \"fromfields\": \"u.id ,u.lbbh as itemclass_code,u.zh_code as itemclass_name,case when u.sjid =1 then 0 else u.sjid end as father_id,'MD01' as chanel,u.id as fake_id \","
													+ "    \"selector\": \" u  where u.zt='Y' \""
													+ "},"
													// 外卖一级菜品类别
													+ "\"TZXERP.VST_SAAS_ITEM_CLASS_LM\": {"
													+ "    \"totablename\": \"hq_item_class\","
													+ "    \"tofields\": \"id,itemclass_code,itemclass_name,father_id,chanel,fake_id,fake_type\"," 
													+ "    \"selector4target\": \" where fake_id is not null and fake_type = 2 \","
													+ "    \"fromfields\": \"u.id ,u.cpysbh as itemclass_code,u.cpysmc1 as itemclass_name,'0' as father_id,case when trim(u.SJMC) ='百度外卖' then 'BD06' when trim(u.SJMC) ='美团外卖' then  'MT08' when u.SJMC ='微信点餐' then  'WX02' when u.SJMC ='饿了么外卖' then  'EL09' end as chanel,u.id as fake_id \","
													+ "    \"selector\": \" u \"" 
													+ "},"
													
													// 外卖二级菜品类别
													+ "\"TZXERP.VST_SAAS_ITEM_CLASS_SM\": {"
													+ "    \"totablename\": \"hq_item_class\","
													+ "    \"tofields\": \"id,itemclass_code,itemclass_name,father_id,chanel,fake_id,fake_type\"," 
													+ "    \"selector4target\": \" where fake_id is not null and fake_type = 3 \","
													+ "    \"fromfields\": \"u.id ,u.cpysbh as itemclass_code,u.cpysmc1 as itemclass_name,u.sjcpys as father_id,case when trim(u.sjsjmc) ='百度外卖' then 'BD06' when trim(u.sjsjmc) ='美团外卖' then  'MT08' when u.sjsjmc ='微信点餐' then  'WX02' when u.sjsjmc ='饿了么外卖' then  'EL09' end as chanel,u.id as fake_id \","
													+ "    \"selector\": \" u \"" 
													+ "},"
													
													// 菜品信息 rif列和saas列不一致
													+ "\"TZXERP.ERP_ITEMINFO\": {"
													+ "    \"totablename\": \"hq_item_info\","
													+ "    \"tofields\": \"id,item_code,item_name,item_class,is_combo,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"u.id ,u.bhdm as item_code,(select p.simplified from tzxpl.platform_language p where p.id=u.mcid) as item_name,u.lbid as item_class,case when u.xmsx ='ERP_ITEM_PROPERTY_SINGLE' then 'N' when u.xmsx ='ERP_ITEM_PROPERTY_WHOLE' then  'Y' end as is_combo,l.simplified as dw_name,u.xsdj,u.id as fake_id\","
													+ "    \"selector\": \" u left join tzxerp.erp_units k on k.id=u.dwid left join tzxpl.platform_language l on l.id=k.mcid where trim(u.SFYX) = 'Y'\""
													+ "},"
													// 餐谱表
													+ "\"TZXERP.ERP_CPYSSZ\": {"
													+ "    \"totablename\": \"hq_item_menu\","
													+ "    \"tofields\": \"id,item_menu_code,item_menu_name,startdate,starttime,remark,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"to_char(id) as id,cpysbh as item_menu_code,cpysmc1 as item_menu_name,ksrq as startdate,substr(kssj,1,5) as starttime,bz as remark,to_char(id) as fake_id\","
													+ "    \"selector\": \"WHERE YSDJ = '0'  AND SHZT='ERP_CHECKSTATE_YES'\""
													+ "},"
													// 餐谱门店关系表
													+ "\"TZXERP.ERP_CPYSSYJG\": {"
													+ "    \"totablename\": \"hq_item_menu_organ\","
													+ "    \"tofields\": \"id,item_menu_id,store_id,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"*\","
													+ "    \"fromtablename\": \"select c.id,c.cpysid as item_menu_id,o.id as store_id,c.id as fake_id from tzxerp.erp_cpyssyjg c join tzxerp.erp_orginfo o on c.jgid=o.id where o.zt='Y'\""
													+ "},"
													//价格体系明细  按照餐谱中的价格为准
//													+ "\"TZXERP.VST_SAAS_ITEM_MENU_INFO\": {"
//													+ "    \"totablename\": \"hq_item_pricesystem\","
//													+ "    \"tofields\": \"id,item_unit_id,price_system,chanel,price,fake_id\","
//													+ "    \"selector4target\": \" where fake_id is not null \","
//													+ "    \"fromfields\": \"u.id ,u.sjsjsjid as item_menu_id,u.xmid as item_id ,u.ksrq as starttime,u.jsrq as endtime,case when u.sjsjmc ='百度外卖' then 'BD06' when u.sjsjmc ='美团外卖' then  'MT08' when u.sjsjmc ='堂食' then 'MD01' when u.sjsjmc ='微信点餐' then  'WX02' when u.sjsjmc ='饿了么外卖' then  'EL09' end as chanel,u.cpysid as class_id, u.xmmc as item_name,u.cpsx,u.lbsx,u.xmid as xmid,(case when u.jgid is null then 0 else u.jgid end) as jgid,xmdj as price,u.id as fake_id\","
//													+ "    \"selector\": \" u \""
//													+ "},"
													//价格体系明细 按照菜品档案的价格将各个渠道都复制一份
													+ "\"TZXERP.ERP_ITEMPRICE_SYS\": {"
													+ "    \"totablename\": \"hq_item_pricesystem\","
													+ "    \"tofields\": \"id,item_unit_id,price_system,price,chanel,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
 													+ "    \"fromfields\": \"u.id,u.jgtxid as price_system ,u.xmid as item_id,u.cmje as price,'MD01' chanel,u.id as fake_id\","
													+ "    \"selector\": \" u\""
													+ "},"
													//同步餐谱明细
													+ "\"TZXERP.ERP_CPYSMX\": {"
													+ "    \"totablename\": \"hq_item_menu_details\","
													+ "    \"tofields\": \"id,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromtablename\": \"select distinct u.id ,u.sjsjsjid as item_menu_id,u.xmid as item_id ,u.ksrq as starttime,u.jsrq as endtime,case when u.sjsjmc ='百度外卖' then 'BD06' when u.sjsjmc ='美团外卖' then  'MT08' when u.sjsjmc ='堂食' then 'MD01' when u.sjsjmc ='微信点餐' then 'WX02' when u.sjsjmc ='饿了么外卖' then  'EL09' end as chanel,u.cpysid as class_id, u.xmmc as item_name,u.cpsx,u.lbsx,u.xmid as xmid,xmdj as price,u.id as fake_id from TZXERP.VST_SAAS_ITEM_MENU_INFO u\","
													+ "    \"fromfields\": \"*\""
													+ "},"
													// 付款方式表 rif列和saas列不一致
													+ "\"TZXERP.ERP_PAYMENTS\": {"
													+ "    \"totablename\": \"payment_way\","
													+ "    \"tofields\": \"id,payment_name1,status,if_income,if_prepay,if_invoicing,if_jifen,is_standard_money,rate,fake_id\","
													+ "    \"fromfields\": \" u.id,u.fkfsbh ,(select p.simplified from tzxpl.platform_language p where p.id=u.mcid) as payment_name1,case when u.sfyx ='Y' then '1' else '0' end as status ,case when u.fssr ='Y' then '1' else '0' end as if_income,u.jexz as ,case when u.sfysk ='Y' then '1' else '0' end as if_prepay ,case when u.sfkfp ='Y' then '1' else '0' end as if_invoicing,case when u.sfjf ='Y' then '1' else '0' end as if_jifen,case when u.yld ='Y' then '1' else '0' end as is_standard_money,u.whhl as rate,u.id as fake_id\","
													+ "    \"selector\": \" u where u.sfyx = 'Y' and fkfsbh  in (0110,0111,0112,0113,0114,0115)\""
													+ "},"

													// 付款方式机构表
													+ "\"TZXERP.ERP_PAYMENT_ORG_REF\": {"
													+ "\"totablename\": \"payment_way_of_ogran\","
													+ " \"tofields\": \"id,payment_id,organ_id,fake_id\","
													+ " \"fromfields\": \" u.id,u.fkfsid as payment_id,u.jgid as organ_id,u.id as fake_id \","
													+ "  \"selector\": \" u left join TZXERP.ERP_PAYMENTS a on a.id=u.fkfsid where a.fkfsbh in (0110,0111,0112,0113,0114,0115)\""
													+ "},"

													// 项目组表hq_item_group
													+ "\"TZXERP.ERP_ITEMGROUP\": {"
													+ "    \"totablename\": \"hq_item_group\","
													+ "    \"tofields\": \"id,item_group_code,item_group_name,five_code,phonetic_code,valid_state,item_group_price,fake_id\","
													+ "    \"fromfields\": \" u.id,u.igno as item_group_code ,u.igname as item_group_name,u.wbjm as five_code ,u.pyjm as phonetic_code,case when u.statu ='ERP_SYSTEM_DISABLE_Y' then '1' else '0' end as valid_state ,u.xmdj as item_group_price,u.id as fake_id\","
													+ "    \"selector\": \" u where u.statu ='ERP_SYSTEM_DISABLE_Y' \""
													+ "},"

													// 项目组明细表hq_item_group_details
													+ "\"TZXERP.ERP_ITEMGROUPLIST\": {"
													+ "    \"totablename\": \"hq_item_group_details\","
													+ "    \"tofields\": \"id,item_group_id,item_id,isdefault,makeup_money,quantity_limit,fake_id\","
													+ "    \"fromfields\": \" id,igid as item_group_id,xmid as item_id,sfmr as isdefault,jjje as makeup_money,xzsl as quantity_limit,id as fake_id   \","
													+ "    \"selector\": \" u \""
													+ "},"

													// 套餐信息
													+ "\"TZXERP.ERP_ORGSILIST\": {"
													+ "    \"totablename\": \"hq_item_combo_details\","
													+ "    \"tofields\": \"id,iitem_id,is_itemgroup,details_id,combo_num,standardprice,combo_order,fake_id\","
													+ "    \"fromfields\": \"u.id ,u.xmid as iitem_id,case when u.mxlx ='ERP_MXLX_GROUP' then 'Y' when u.mxlx ='ERP_MXLX_SINGLE' then  'N' end as is_itemgroup , u.mxid as details_id,u.xmsl as combo_num,u.xsdj as standardprice,u.xmph as combo_order,u.id as fake_id \","
													+ "    \"selector\": \" u \"" 
													+ "},"

													// 法人信息
													+ "\"TZXERP.ERP_ORGCORP\": {"
													+ "    \"totablename\": \"hq_legal_per\","
													+ "    \"tofields\": \"id,fake_id\","
													+ "    \"fromfields\": \"i.id,u.id as organid,e.TSSFKFP,e.WMSFKFP,e.DZFPYXQ,e.DZFPMY,i.FRCODE as legal_per_code,i.FRNAME as legal_per_name,i.frfpmc,i.SFKFP as is_billing,i.DJCS as docking_merchants ,i.WXKPID as wechat_party_id,i.WXKPMY as wechat_invoice_key,i.NSRSX as taxpayers_attr_id,i.SL as tax_rate,i.FPMX as invoice_name ,i.FRSWHM as cnpj ,i.XSFMC as seller_name,i.XSFDZ as seller_address,i.XSFDH as seller_number ,i.XSFYHZH as bank ,i.id as fake_id \","
													+ "    \"selector\": \" i inner join tzxerp.erp_orginfo e on e.FRLX = i.id inner join TZXUUS.UUS_ORGAN  u  on u.id=e.JGXH WHERE u.STATUS='PLATFORM_STATUS_ENABLE'AND u.ID !=0  \""
													+ "},"
													
													//退菜原因
													+ "\"TZXERP.ERP_RETUENREASONG\": {"
													+ "    \"totablename\": \"hq_unusual_reason\","
													+ "    \"tofields\": \"id,reason_code,reason_name,phonetic_code,five_code,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null and fake_type = 2\","
													+ "    \"fromfields\": \"     RT.ID as id,RT.BH as reason_code,RT.REASON as reason_name,RT.PYDM as phonetic_code,RT.WBDM as five_code,RT.ID as fake_id  \","
													+ "    \"selector\": \" RT WHERE RT.SHZT='ERP_CHECKSTATE_YES' \"" 
													+ "}"
													+ "}");
													
													

}
