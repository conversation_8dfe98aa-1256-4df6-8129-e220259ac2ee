<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/tx
       http://www.springframework.org/schema/tx/spring-tx-2.5.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context-2.5.xsd
       http://www.springframework.org/schema/task
       http://www.springframework.org/schema/task/spring-task-3.1.xsd"
	default-autowire="byName">

	<task:scheduler id="scheduler" pool-size="10" />
	<!-- 支持异步方法执行 -->
	<task:annotation-driven executor="executorWithCallerRunsPolicy"
		scheduler="scheduler" />
	<task:executor id="executorWithCallerRunsPolicy"
		pool-size="50-150" queue-capacity="50" rejection-policy="CALLER_RUNS" />

	<!-- 待加入Spring Schedual进行调度的task列表 -->
	<bean id="notifySpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="notifyScheduledMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<bean id="foodPriceSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="foodPriceScheduledMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="60000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="60000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<bean id="goodsPriceSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="goodsPriceScheduledMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="60000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="60000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<bean id="birthdaySpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="birthdayScheduledMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<bean id="couponRecyclingSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="couponRecyclingScheduledMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="60000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="60000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<bean id="integralSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="integralScheduledMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="0" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="60000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<!-- 唤醒会员 -->
	<bean id="wakeMarketingTaskExecutorScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="wakeMarketingTaskExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<bean id="wakeMarketingTaskExecutor" class="com.tzx.task.common.task.WakeMarketingTaskExecutor" />
	<!-- 唤醒会员结束 -->


	<bean id="notifySpringScheduledExecutorFactoryBean"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorFactoryBean">
		<property name="scheduledExecutorTasks">
			<list>
				<!--唤醒会员 -->
				<ref bean="wakeMarketingTaskExecutorScheduledExecutorTask" />
				<ref bean="notifySpringScheduledExecutorTask" />
				<ref bean="WX02PaymentNumberMainExecutorTask" />
				<ref bean="foodPriceSpringScheduledExecutorTask" />
				<ref bean="goodsPriceSpringScheduledExecutorTask" />
				<ref bean="birthdaySpringScheduledExecutorTask" />
				<ref bean="couponRecyclingSpringScheduledExecutorTask" />
				<ref bean="messageSendScheduledExecutorTask" />
				<ref bean="electronicInvoiceTaskSpringScheduledExecutorTask" />
				<ref bean="integralSpringScheduledExecutorTask" />
				<ref bean="wechatpayPaymentAutoQueryTaskSpringScheduledExecutorTask" />
				<ref bean="alipayPaymentAutoQueryTaskSpringScheduledExecutorTask" />
				<ref bean="xmdpayPaymentAutoQueryTaskSpringScheduledExecutorTask" />
				<ref bean="paymentStateSpringScheduledExecutorTask" />
				<ref bean="wechatAutoCancelOrderTaskSpringScheduledExecutorTask" />
			    <ref bean="wechatAutoEnsureOrderStateTaskSpringScheduledExecutorTask" />
			    <!-- *单例* 总部SAAS同步基本资料任务检出主线程  process-->
			    <ref bean="dataSyncByHqSaasSpringScheduledExecutorTask"/>

				<!-- 卡定时任务子线程 -->
				<ref bean="cardScheduledMainExecutorTask" />

				<!-- *单例* 生日优惠券任务检出主线程 -->
				<ref bean="detectionBirthdaySpringScheduledExecutorTask" />
				<!-- *单例* 菜品变价任务检出主线程 -->
				<ref bean="detectionFoodPriceSpringScheduledExecutorTask" />
				<!-- *单例* 物品变价任务检出主线程 -->
				<ref bean="detectionGoodsPriceSpringScheduledExecutorTask" />
				<!-- *单例* 过期优惠券回收任务检出主线程 -->
				<ref bean="detectionCouponRecyclingSpringScheduledExecutorTask" />
				<!-- *单例* 过期积分回收任务检出主线程 -->
				<ref bean="detectionIntegralSpringScheduledExecutorTask" />
				<!-- *单例* 卡定时任务主线程 -->
				<ref bean="detectionCardTaskExecutorTask" />
				<!-- *单例*  推送平台订单任务任务-->
				<ref bean="pushPlatFromModeTaskTaskSpringScheduledExecutorTask"/>
				<!-- *单例*  健康检测数据加载任务
				<ref bean="monitorInfoSpringScheduledExecutorTask" /> -->
				<!-- *单例* 总部SAAS同步基本资料任务检出主线程  main-->
				<ref bean="detectionDataSyncByHqSaasSpringScheduledExecutorTask" />
				<!-- 报表新闻点击率 -->
				<ref bean="clickRateSpringScheduledExecutorTask" />
			</list>
		</property>
	</bean>
	<!-- 微信定时任务调度主线程 -->
	<bean id="WX02PaymentNumberMainExecutor" class="com.tzx.task.common.task.WX02PaymentNumberMainExecutor">
	</bean>
	<!-- 优惠券发放任务调度主线程 -->
	<bean id="notifyScheduledMainExecutor" class="com.tzx.task.common.task.NotifyScheduledMainExecutor" />
	<!-- 生日相关任务调度主线程 -->
	<bean id="birthdayScheduledMainExecutor" class="com.tzx.task.common.task.BirthdayScheduledMainExecutor" />
	<!-- 菜品变价任务调度主线程 -->
	<bean id="foodPriceScheduledMainExecutor" class="com.tzx.task.common.task.FoodPriceScheduledMainExecutor" />
	<!-- 物品变价任务调度主线程 -->
	<bean id="goodsPriceScheduledMainExecutor"
		class="com.tzx.task.common.task.GoodsPriceScheduledMainExecutor" />
	<!-- 过期优惠券回收任务调度主线程 -->
	<bean id="couponRecyclingScheduledMainExecutor"
		class="com.tzx.task.common.task.CouponRecyclingScheduledMainExecutor" />
	<!-- 过期积分回收任务调度主线程 -->
	<bean id="integralScheduledMainExecutor" class="com.tzx.task.common.task.IntegralScheduledMainExecutor" />
	<bean id="WX02PaymentNumberMainExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="WX02PaymentNumberMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="5000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<bean id="detectionBirthdaySpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="detectionBirthdayTaskExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<bean id="detectionFoodPriceSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="detectionFoodPriceTaskExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<bean id="detectionGoodsPriceSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="detectionGoodsPriceTaskExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<bean id="detectionCouponRecyclingSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="detectionCouponRecyclingTaskExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<bean id="detectionIntegralSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="detectionIntegralTaskExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<bean id="paymentSccRepairTaskSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="paymentSccRepairTask" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="0" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="0" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<bean id="electronicInvoiceTaskSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="electronicInvoiceTask" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="3600000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<bean id="alipayPaymentAutoQueryTaskSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="alipayPaymentAutoQueryTask" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="3000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="true" />
	</bean>

	<bean id="wechatpayPaymentAutoQueryTaskSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="wechatpayPaymentAutoQueryTask" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="3000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="true" />
	</bean>
	
	<bean id="wechatAutoCancelOrderTaskSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="wechatAutoCancelOrderTask"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="60000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="3000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="true"/>
    </bean>
    
    <bean id="wechatAutoEnsureOrderStateTaskSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="wechatAutoEnsureOrderStateTask"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="60000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="3000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="true"/>
    </bean>
    <bean id="wechatAutoCancelSmallLiseOrderTaskSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="wechatAutoCancelSmallLiseOrderTask"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="60000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="3000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="true"/>
    </bean>

	<!-- 生日任务检出主线程，需要单例部署 -->
	<bean id="detectionBirthdayTaskExecutor" class="com.tzx.task.common.task.DetectionBirthdayTaskExecutor" />
	<!-- 菜品变任务价检出主线程，需要单例部署 -->
	<bean id="detectionFoodPriceTaskExecutor" class="com.tzx.task.common.task.DetectionFoodPriceTaskExecutor" />
	<!-- 物品变价任务调度主线程 -->
	<bean id="detectionGoodsPriceTaskExecutor"
		class="com.tzx.task.common.task.DetectionGoodsPriceTaskExecutor" />
	<!-- 过期优惠券任务检出主线程，需要单例部署 -->
	<bean id="detectionCouponRecyclingTaskExecutor"
		class="com.tzx.task.common.task.DetectionCouponRecyclingTaskExecutor" />
	<!-- 过期积分回收任务调度主线程 -->
	<bean id="detectionIntegralTaskExecutor" class="com.tzx.task.common.task.DetectionIntegralTaskExecutor" />
	<!-- 取消扫描二维码补救线程 -->
	<bean id="paymentSccRepairTask" class="com.tzx.task.common.task.PaymentSccRepairTask" />
	<!-- 电子发票自动轮询 -->
	<bean id="electronicInvoiceTask" class="com.tzx.task.common.task.ElectronicInvoiceTask" />
	<!-- 支付宝自动轮询 -->
	<bean id="alipayPaymentAutoQueryTask" class="com.tzx.task.common.task.AlipayPaymentAutoQueryTask" />
	<!-- 微信自动轮询 -->
	<bean id="wechatpayPaymentAutoQueryTask" class="com.tzx.task.common.task.WechatPaymentAutoQueryTask" />
	
	<!-- 微信取消订单自动轮询-->
    <bean id="wechatAutoCancelOrderTask" class="com.tzx.task.common.task.WechatAutoCancelOrderTask"/>
    
    <!-- 微信自动确认订单与支付状态的统一 -->
    <bean id="wechatAutoEnsureOrderStateTask" class="com.tzx.task.common.task.WechatAutoEnsureOrderStateTask"/>

    <!-- 微信自动取消微生活中提交交易而未支付的订单 -->
    <bean id="wechatAutoCancelSmallLiseOrderTask" class="com.tzx.task.common.task.WechatAutoCancelSmallLiseOrderTask"/>

	<!-- 微信群发消息定时任务调度主线程 -->
	<bean id="wxMessageSendExecutor" class="com.tzx.weixin.common.task.WXMessageSendExecutor"></bean>
	<!-- 待加入Spring Schedual进行调度的task列表 -->
	<bean id="messageSendScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="wxMessageSendExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="60000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="120000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<!-- 团体挂账支付状态自动轮询 -->
	<bean id="paymentStateScheduledMainExecutor"
		class="com.tzx.task.common.task.PaymentStateScheduledMainExecutor" />
	<bean id="paymentStateSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="paymentStateScheduledMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="3000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>

	<!-- hq健康检测数据加载至redis -->
	<bean id="monitorInfoTask" class="com.tzx.task.common.task.MonitorInfoTask"/>
	<bean id="monitorInfoSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="monitorInfoTask"/>
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000"/>
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="3600000"/>
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false"/>
	</bean>

	<!-- 卡定时任务调度主线程 start -->
	<bean id="detectionCardTaskExecutor" class="com.tzx.task.common.task.DetectionCardTaskExecutor" />
	<bean id="detectionCardTaskExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="detectionCardTaskExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="0" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如10min -->
		<property name="period" value="600000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<!-- 卡定时任务调度主线程 end -->

	<!-- 卡定时任务子线程 start -->
	<bean id="cardScheduledMainExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="cardScheduledMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<bean id="cardScheduledMainExecutor" class="com.tzx.task.common.task.CardScheduledMainExecutor" />
	<!-- 卡定时任务子线程 end -->

	<!-- 检测字典表数据（下发方式）状态定时任务 -->
	<bean id="sendWayAutoCheckTask" class="com.tzx.task.common.task.SendWayAutoCheckTask" />

	<task:scheduled-tasks>
		<!--签账卡重置，每天1:00:00 执行 -->
		<task:scheduled ref="com.tzx.task.common.task.DebitCardJob"
			method="resetDebitCard" cron="0 0 1 * * ?" />
		<task:scheduled ref="sendWayAutoCheckTask" method="check" cron=" 0 0 1 * * ?"/>
	</task:scheduled-tasks>
	
	  <!--  外卖新接单模式启动线程 start -->
    <!--  <task:scheduled-tasks>
     <task:scheduled ref="unifiedReceiveOrderTaskExecutor" method="run" fixed-delay="2000" />
    </task:scheduled-tasks>  
    <bean id="unifiedReceiveOrderTaskExecutor" class="com.tzx.cc.takeaway.unifiedreceiveorder.service.UnifiedReceiveOrderTaskServcieRunable"/>-->
    <!--    外卖新接单模式启动线程  end --> 	
	
		 <!-- 第三方接口日志线程启动 start -->
     <task:scheduled-tasks>
     <task:scheduled ref="kafkaConsumerLogTaskExecutor" method="run" fixed-delay="5000" />
    </task:scheduled-tasks> 
    <bean id="kafkaConsumerLogTaskExecutor" class="com.tzx.cc.thirdparty.log.KafkaConsumerLog"/>
    <!--   第三方接口日志线程启动  end -->
    
    <!-- 李先生评论抓取 调度函数-->
    <task:scheduled-tasks>
         <task:scheduled ref="thirdPlatCommentGrapExcutor"
                        method="run" cron=" ${comment.task.triggertime} * * * * ?"/>   
 		 <!-- <task:scheduled ref="thirdPlatCommentGrapExcutor"
                        method="run" cron=" * ${comment.task.triggertime} * * * ?"/> -->                    
    </task:scheduled-tasks>
    
     <!-- 第三方评论获取 调度函数-->
    <bean id="thirdPlatCommentGrapTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="thirdPlatCommentGrapExcutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="${comment.task.min}"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="10000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>
    <bean id="thirdPlatCommentGrapExcutor" class="com.tzx.cc.takeaway.task.ThirdPlatCommentGrapExecutor"/>

	<!-- 推送订单轮询-->
	<bean id="pushPlatFromModeTask" class="com.tzx.task.common.task.PushPlatFromModeTask"/>

	<bean id="pushPlatFromModeTaskTaskSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="pushPlatFromModeTask"/>
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000"/>
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="10000"/>
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false"/>
	 </bean>
	 
     <!-- 新美大自动轮询-->
	 <bean id="xmdpayPaymentAutoQueryTaskSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="xmdpayPaymentAutoQueryTask"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="3000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="true"/>
     </bean>
   
     <bean id="xmdpayPaymentAutoQueryTask" class="com.tzx.task.common.task.XmdpayPaymentAutoQueryTask"/>


	
	<bean id="detectionDataSyncByHqSaasSpringScheduledExecutorTask"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="detectionDataSyncByHqSaasScheduledMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="300000" />  <!-- 5分钟-->
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min   -->
		<property name="period" value="7200000" /><!-- 2小时-->
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	</bean>
	<!-- 总部SAAS同步基本资料任务检出主线程，需要单例部署 -->
	<bean id="detectionDataSyncByHqSaasScheduledMainExecutor" class="com.tzx.task.common.task.DetectionDataSyncByHqSaasScheduledMainExecutor" />
	
	<!-- 从总部SAAS同步基本资料-->
	 <bean id="dataSyncByHqSaasSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="dataSyncByHqSaasScheduledMainExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="310000"/><!-- 5分钟-->
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="7200000"/><!-- 2小时-->
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="true"/>
     </bean>
    <bean id="dataSyncByHqSaasScheduledMainExecutor" class="com.tzx.task.common.task.DataSyncByHqSaasScheduledMainExecutor"/>
    
    <!-- 报表新闻点击率 -->
	 <bean id="clickRateSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="newsClickRateSpringScheduledExecutorTask"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="310000"/><!-- 5分钟-->
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="7200000"/><!-- 2小时-->
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="true"/>
     </bean>
    <bean id="newsClickRateSpringScheduledExecutorTask" class="com.tzx.report.common.util.redisUtil.NewsClickRateTask"/>
</beans>