#Tue May 04 19:58:01 CST 2014
# hq or boh
sotreorhq=hq
#ifstart=true
ifstart=false
Url=tcp://activemq.db.tzx.com.cn:6666
User=
Password=
qtoboh=qtoboh
qcometoboh=qtoboh_hdl_a3x3m0znn6eeqerjdud68xp6s7ojuiqz

#for tt
#ttsvr=tcp://*************:1884
tttopic=app_topic

#datasourcetype
datasourcetype=postgres
print.type=80
#system_password=1db88c018878aef3187e7710aea95c40
system_password=71646bcb65b3c9870bdb09520622a347

#smstype\u662f\u7528\u4e8e\u914d\u7f6e\u4f7f\u7528\u5e73\u53f0\u8fd8\u662f\u77ed\u4fe1\u732b\uff0c\u7528\u732b\u503c\u4e3a\u77ed\u4fe1\u732b\u670d\u52a1\u5668IP \u5982\uff1a************,\u5982\u679c\u662f\u4f7f\u7528\u77ed\u4fe1\u5e73\u53f0\u76f4\u63a5\u51990\u4e3a\u4e0a\u6d77\u5e0c\u5965\u77ed\u4fe1\u5e73\u53f0\uff0c\u51991\u662f\u7528\u4ebf\u7f8e\u77ed\u4fe1\u5e73\u53f0,\u51992\u4e3a\u7ebf\u4e0a\u56e2\u961f\u7559\u4e0b\u7684\u65b0\u5e73\u53f0\uff08\u72d7\u5c4e\uff09
smstype=2
#fsdw=\u5929\u5b50\u661f

#\u4e0b\u9762\u662f\u4ebf\u7f8e\u5e73\u53f0\u76f8\u5173\u914d\u7f6e\u53c2\u6570
#\u4ebf\u7f8e\u8d2d\u4e70\u7684\u8f6f\u4ef6\u5e8f\u5217\u53f7
softwareSerialNo=3SDK-EMY-0130-MEWSR
#\u4ece\u4ebf\u7f8e\u8d2d\u4e70\u7684\u8f6f\u4ef6\u5e8f\u5217\u53f7\u6240\u5e26\u7684KEY \u503c
key=026404
#\u4ece\u4ebf\u7f8e\u8d2d\u4e70\u7684\u8f6f\u4ef6\u5e8f\u5217\u53f7\u6240\u5e26\u7684\u5bc6\u7801
password=055956

#\u4e0b\u9762\u7684\u53c2\u6570\u662f\u914d\u7f6e\u4e0a\u6d77\u5e0c\u5965\u77ed\u4fe1\u5e73\u53f0\u76f8\u5173\u53c2\u6570
#\u77ed\u4fe1\u5e73\u53f0\u5730\u5740
url=http://api.52ao.com
#\u7528\u6237\u540d
user=laitianhua06
#\u5bc6\u7801\u8981\u8fdb\u884cMD5\u52a0\u5bc6,32\u4f4d\uff0c\u5927\u5c0f\u5199\u90fd\u53ef\u4ee5\uff0c\u5de5\u5177\uff1a  http://tool.chinaz.com/Tools/MD5.aspx?q=123456&md5type=0
pass=E10ADC3949BA59ABBE56E057F20F883E

#\u4e0b\u9762\u662f20150327\u65b0\u77ed\u4fe1\u5e73\u53f0\u63a5\u53e3\u8d26\u53f7\u5bc6\u7801(\u7ebf\u4e0a\u5e73\u53f0)
newkey=SDK-BBX-010-20057
newpassword=tzx68698

#\u4e0b\u9762\u662f20150723pos\u8f6c\u67e5\u4f1a\u5458\u63a5\u53e3url
crmUrl=http://saastest.xmhxl.cn/crmRest/post

#Whether as a redis task service startup, 1 is yes
taskopen=1

#uploadImgIp
upload_img_ip=http://saastest.xmhxl.cn:8888/
upload_websiteimg_ip=http://saastest.xmhxl.cn:8888/

#
hqdata_minite_period=10

#
saas_url=http://saastest.xmhxl.cn/
post_url=http://saastest.xmhxl.cn/tzxsecondpay/
#
storestatus_minite_period=1

item_photo_path=itemImage
#pos alipay 
pos_payment_url=http://saastest.xmhxl.cn/payment/aliPaymentRest/aliPay
#pos wechat
pos_payment_wechat_url=http://saastest.xmhxl.cn/payment/wechat/post
#alipay notify url 
alipay_notify_url=http://saastest.xmhxl.cn/payment/aliPaymentRest/notify
#wechat notify url 
wechat_notify_url=http://saastest.xmhxl.cn/payment/wechat/notify
wechat_notify_url_new=http://saastest.xmhxl.cn/paymentCallBack/weixin/notify

#APP
app_name=app-release.apk
#
app_path=download/

#for wechat product
product_wechat_service_ip=http://saastest.xmhxl.cn
product_wechat_scmip=http://saastest.xmhxl.cn/crmRest/post

#wechat service mch message	start
wechat_service_mch_service_ip=http://www.e7e6.net
wechat_service_mch_appid=wxc2864bc7ba5baa5c
wechat_service_mch_secert=385ae5ab6d306a008cc6ca3f1f4a33d4
wechat_service_mch_mch_id=10010438
wechat_service_mch_api_secert=tzxsaasweixinpay2015101212150000
wechat_service_mch_cert=cert/apiclient_cert.p12
wechat_service_sub_mch_id = **********
#wechat service mch message end

#alipay service provider id start
alipay_service_provider_id=2088411391202430
#alipay service provider id end
#tenent_id==hdl
#store_id=48

#\u662f\u5426\u5916\u5356
is_delivery=true

# can be local IP
supplier_address=http://saastest.xmhxl.cn/sup/supContraller/post 
saas_supplier=SAAS_SUPPLY_QUEUE

#wechat thrid
wechat_component_appid=wxa86b7e18a2ba8dd5
wechat_component_appsecret=f2b82f3d4f19bbda288b4d1473e9e897
wechat_compoment_encodingaeskey=dOQtg5BkmuCfv4djlIHeBIqPC3ZsdlN1oGuEeayTX88
wechat_compoment_token=tzx
#wechat thrid
#wechat thrid redirect_uri
wechat_thrid_redirect_uri=http://saastest.xmhxl.cn
#wechat thrid redirect_uri

# 新美大团购&闪惠接口
xmd_url=http://api.open.cater.meituan.com
xmd_developerid=100113
#xmd_signkey=m1fwurrvo09o33c7
xmd_signkey=ewas1wb32icc8clw


# 外卖平台api调用地址 {

#百度外卖api
baidu_api_urL=http://api.waimai.baidu.com

#美团外卖api
meituan_api_url=http://waimaiopen.meituan.com/api/v1/
#美团外卖测试api
#meituan_api_url=http://test.waimaiopen.meituan.com/api/v1/

#饿了么api
ele_api_url=http://v2.openapi.ele.me/

#大众点评
dp_api_url=https://e.51ping.com/mpi/

#yichi
#yichi_address=http://localhost:8081/yichi/yichi/yichiContraller/post
#yichi_address=http://*************/yichi/yichi/yichiContraller/post


rif_tzxApply = /tzxApply
rif_finish = /tzxDistributionFinish
rif_return = /tzxReturn
rif_supplyin = /tzxSupplierIn
rif_supplyout = /tzxSupplierOut
# }

omUrl = http://saastest.xmhxl.cn
#maintaskopen = 1
#childtaskopen = 1

MQFASTDFSURL=http://saastest.xmhxl.cn/

#elm2.0 appid \u81EA\u52A8\u62C9\u5355\u63A5\u53E3\u65F6\u4F7F\u7528
ele_appid=15616757
#elm2.0 oauth callback used... note:must be https
ele_oauth_callbackUrl=https://saastest.xmhxl.cn/thirdpartydown/orderdownstreamrest/elm/authCallBack
#ele_oauth_callbackUrl=http://saastest.xmhxl.cn/thirdpartydown/orderdownstreamrest/elm/authCallBack
#elm2.0 oauth true|false \u997F\u4E86\u4E48\u8BA4\u8BC1\u65B9\u5F0F:\u6D4B\u8BD5\u8BA4\u8BC1|\u751F\u4EA7\u8BA4\u8BC1
ele_oauth_istest=false
#####2017-09-12 ------------------ config add  ----------------#############################
#waimai_queue值为1时为启用新接单模式
waimai_start=1
#\u5916\u5356\u961F\u5217\u540D\u79F0
waimai_queue=waimai
#\u5916\u5356\u7EBF\u7A0B\u6570
waimai_queue_thread_size=100

#外卖分区配置 1开，0关
waimai_partition_start=1
##KafKa Log 配置
#\u6B64\u503C\u4E3A1\u65F6\u6253\u5F00\u65E5\u5FD7\uFF0C0\u4F4D\u5173\u95ED
kafka_open=1
zookeeper.connect=zk.db.tzx.com.cn:2181,zk.db.tzx.com.cn:2182,zk.db.tzx.com.cn:2183
kafka_topic=CcTopic
metadata.broker.list=zk.db.tzx.com.cn:9091,zk.db.tzx.com.cn:9092,zk.db.tzx.com.cn:9093
auto.offset.reset=earliest
group.id=cc_log
enable.auto.commit=true
auto.commit.interval.ms=1000
#WebSocket \u63A8\u9001 url
websocket_push_url=http://pushtest.xmhxl.cn/push/bizapi/message
#sass taskmain 
sass_task_url=http://taskmain.tzx.com.cn:8081
xmdwm_url=https://open-erp.meituan.com
order_log=0
#新美大支付 - key 
payment_xmd_key=c5d4753ca66cd5442bd7c211e0dbf10f
#新美大支付 - url（测试环境 - 外网）
payment_xmd_url=https://openpay.meituan.com/
#xmd pay appid
app_id=31101

#\u674e\u5148\u751f\u8bc4\u8bba\u5b9a\u65f6\u5206\u949f\u6570
comment.task.min=1440
#\u674e\u5148\u751f\u8bc4\u8bba\u83b7\u53d6\u89e6\u53d1\u65f6\u95f4
comment.task.triggertime=5
#\u674e\u5148\u751f\u8bc4\u8bba\u5206\u5e03\u5f0f\u9501\u8fc7\u671f\u65f6\u95f4
comment.task.expiretime=1
#\u8bc4\u8bba\u6570\u636e\u83b7\u53d6 \u7ebf\u7a0b\u6c60\u914d\u7f6e
comment.threadpool.max=500
comment.threadpool.core=50
comment.threadpool.alive=3

sys_url=http://saastest.xmhxl.cn/

# SSO Redirect Url
server.homepage=http://bitest.xmhxl.cn/pages/framework/layout/main.jsp
sysconf.homepage=http://saastest.xmhxl.cn/homepage/hp/index


# Upload file configure
#imgServerType=0
#fileBaseUri=http://saastest.xmhxl.cn:7778/

sso.homepage=http://ssotest.xmhxl.cn
saas.domain=http://saastest.xmhxl.cn
sso.logout.url=http://ssotest.xmhxl.cn/logout
saas.homepage=http://saastest.xmhxl.cn/pages/framework/layout/main.jsp
server.homepage=http://bitest.xmhxl.cn/reportpages/reportvue/dist/index.html

payment_xmd_key=c5d4753ca66cd5442bd7c211e0dbf10f
payment_xmd_url=https://openpay.meituan.com/
app_id=31101
