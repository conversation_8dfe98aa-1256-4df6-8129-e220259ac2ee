<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/tx
       http://www.springframework.org/schema/tx/spring-tx-2.5.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context-2.5.xsd
       http://www.springframework.org/schema/task
       http://www.springframework.org/schema/task/spring-task-3.1.xsd"
       default-autowire="byName">

    <task:scheduler id="scheduler" pool-size="10"  />
    <!-- 支持异步方法执行 -->
    <task:annotation-driven executor="executorWithCallerRunsPolicy" scheduler="scheduler" />
    <task:executor id="executorWithCallerRunsPolicy" pool-size="50-150" queue-capacity="50" rejection-policy="CALLER_RUNS"/>

    <!-- 统一处理json问题
    <context:annotation-config/>
    <bean class="org.springframework.web.servlet.mvc.annotation.DefaultAnnotationHandlerMapping"/>
    <bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
        <property name="messageConverters">
            <ref bean="jacksonMessageConverter"/>
        </property>
    </bean>
    <bean id="jacksonMessageConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
        <property name="supportedMediaTypes">
            <value>application/json;charset=UTF-8</value>
        </property>
        <property name="objectMapper">
            <bean class="org.springframework.http.converter.json.Jackson2ObjectMapperFactoryBean">
                <property name="failOnEmptyBeans" value="false"/>
                <property name="serializers">
                    <array>
                        <bean class="com.tzx.weixin.waimai.util.JSONObjectSerializer"/>
                    </array>
                </property>
            </bean>
        </property>
    </bean>

    <bean id="viewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
        <property name="prefix" value="/"></property>
    </bean>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver"/>
    -->

    <!-- 待加入Spring Schedual进行调度的task列表 -->
    <bean id="notifySpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="notifyScheduledMainExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="10000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>
    <bean id="foodPriceSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="foodPriceScheduledMainExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="60000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="60000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>
    <bean id="goodsPriceSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="goodsPriceScheduledMainExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="60000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="60000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>
    <bean id="birthdaySpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="birthdayScheduledMainExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="10000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>
    <bean id="couponRecyclingSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="couponRecyclingScheduledMainExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="60000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="60000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>
    <bean id="integralSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="integralScheduledMainExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="60000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="60000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>

    <!-- 唤醒会员 -->
    <bean id="wakeMarketingTaskExecutorScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="wakeMarketingTaskExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="10000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>
    <bean id="wakeMarketingTaskExecutor" class="com.tzx.task.common.task.WakeMarketingTaskExecutor"/>
    <!-- 唤醒会员结束 -->

    <bean id="notifySpringScheduledExecutorFactoryBean" class="org.springframework.scheduling.concurrent.ScheduledExecutorFactoryBean">
    <property name="scheduledExecutorTasks">
        <list>
		<!-- *单例* 生日优惠券任务检出主线程-->
          	<ref bean="detectionBirthdaySpringScheduledExecutorTask" />  
          	<!-- *单例* 菜品变价任务检出主线程-->
          	<ref bean="detectionFoodPriceSpringScheduledExecutorTask" />  
          	<!-- *单例* 物品变价任务检出主线程-->
          	<ref bean="detectionGoodsPriceSpringScheduledExecutorTask" />  
          	<!-- *单例* 过期优惠券回收任务检出主线程-->
          	<ref bean="detectionCouponRecyclingSpringScheduledExecutorTask" />  
          	<!-- *单例* 过期积分回收任务检出主线程-->
          	<ref bean="detectionIntegralSpringScheduledExecutorTask" />  
        </list>
    </property>
    </bean>
    <!-- 微信定时任务调度主线程 -->
	<bean id="WX02PaymentNumberMainExecutor" class="com.tzx.task.common.task.WX02PaymentNumberMainExecutor">
	</bean>
    <!-- 优惠券发放任务调度主线程 -->
    <bean id="notifyScheduledMainExecutor" class="com.tzx.task.common.task.NotifyScheduledMainExecutor"/>
    <!-- 生日相关任务调度主线程 -->
    <bean id="birthdayScheduledMainExecutor" class="com.tzx.task.common.task.BirthdayScheduledMainExecutor"/>
    <!-- 菜品变价任务调度主线程 -->
    <bean id="foodPriceScheduledMainExecutor" class="com.tzx.task.common.task.FoodPriceScheduledMainExecutor"/>
    <!-- 物品变价任务调度主线程 -->
    <bean id="goodsPriceScheduledMainExecutor" class="com.tzx.task.common.task.GoodsPriceScheduledMainExecutor"/>
    <!-- 过期优惠券回收任务调度主线程 -->
    <bean id="couponRecyclingScheduledMainExecutor" class="com.tzx.task.common.task.CouponRecyclingScheduledMainExecutor"/>
    <!-- 过期积分回收任务调度主线程 -->
    <bean id="integralScheduledMainExecutor" class="com.tzx.task.common.task.IntegralScheduledMainExecutor"/>
    <bean id="WX02PaymentNumberMainExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
		<property name="runnable" ref="WX02PaymentNumberMainExecutor" />
		<!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
		<property name="delay" value="10000" />
		<!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
		<property name="period" value="5000" />
		<!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
		<!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
		<property name="fixedRate" value="false" />
	 </bean>
    <bean id="detectionBirthdaySpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="detectionBirthdayTaskExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="10000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>

    <bean id="detectionFoodPriceSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="detectionFoodPriceTaskExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="10000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>

    <bean id="detectionGoodsPriceSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="detectionGoodsPriceTaskExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="10000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>

    <bean id="detectionCouponRecyclingSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="detectionCouponRecyclingTaskExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="10000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>

    <bean id="detectionIntegralSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="detectionIntegralTaskExecutor"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="10000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>

    <bean id="paymentSccRepairTaskSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="paymentSccRepairTask"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="0"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="0"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>

    <bean id="electronicInvoiceTaskSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="electronicInvoiceTask"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <property name="delay" value="10000"/>
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="3600000"/>
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="false"/>
    </bean>

    <!-- 生日任务检出主线程，需要单例部署 -->
    <bean id="detectionBirthdayTaskExecutor" class="com.tzx.task.common.task.DetectionBirthdayTaskExecutor"/>
    <!-- 菜品变任务价检出主线程，需要单例部署 -->
    <bean id="detectionFoodPriceTaskExecutor" class="com.tzx.task.common.task.DetectionFoodPriceTaskExecutor"/>
    <!-- 物品变价任务调度主线程 -->
    <bean id="detectionGoodsPriceTaskExecutor" class="com.tzx.task.common.task.DetectionGoodsPriceTaskExecutor"/>
    <!-- 过期优惠券任务检出主线程，需要单例部署 -->
    <bean id="detectionCouponRecyclingTaskExecutor" class="com.tzx.task.common.task.DetectionCouponRecyclingTaskExecutor"/>
    <!-- 过期积分回收任务调度主线程 -->
    <bean id="detectionIntegralTaskExecutor" class="com.tzx.task.common.task.DetectionIntegralTaskExecutor"/>
    <!-- 取消扫描二维码补救线程 -->
    <bean id="paymentSccRepairTask" class="com.tzx.task.common.task.PaymentSccRepairTask"/>
    <!-- 电子发票自动轮询-->
    <bean id="electronicInvoiceTask" class="com.tzx.task.common.task.ElectronicInvoiceTask"/>

</beans>
