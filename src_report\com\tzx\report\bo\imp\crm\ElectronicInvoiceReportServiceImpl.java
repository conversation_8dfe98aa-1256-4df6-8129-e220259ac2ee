package com.tzx.report.bo.imp.crm;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.crm.ElectronicInvoiceReportService;
import com.tzx.report.common.util.ExportUtils;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service(ElectronicInvoiceReportService.NAME)
public class ElectronicInvoiceReportServiceImpl implements ElectronicInvoiceReportService
{
	private Logger logger = Logger.getLogger(ElectronicInvoiceReportServiceImpl.class);

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	/* (non-Javadoc)
	 * @see com.tzx.report.bo.crm.ElectronicInvoiceReportService#queryElectronicInvoiceReport(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public Object queryElectronicInvoiceReport(String tenancyId, JSONObject obj)
			throws Exception {
		StringBuffer sql = new StringBuffer();
		//sql.append("select case ele_info.organ_id when 0 then '总部' else organ.org_full_name END,pb.report_date,ele_info.* from hq_electronic_invoice_info ele_info");
		sql.append("select case ele_info.organ_id when 0 then '总部' else organ.org_full_name END,ele_info.order_date as report_date,ele_info.* from hq_electronic_invoice_info ele_info");

		sql.append(" left join hq_electronic_invoice_details ele_details on ele_info.id = ele_details.electronic_id");
		sql.append(" left join organ on ele_info.organ_id = organ.id");
//		sql.append(" LEFT JOIN pos_bill2 pb ON COALESCE(pb.order_num, pb.bill_num) = ele_info.order_code ");
		sql.append(" where 1=1");

		String authority = obj.optString("authority_organ");
		if(StringUtils.isNotBlank(authority)) {
			sql.append(" and ele_info.organ_id in (").append(authority).append(")");
		}

		String ids = obj.optString("ids");
		if(StringUtils.isNotBlank(ids)) {
			sql.append(" and ele_info.organ_id in (").append(ids).append(")");
		}

		String invoiceFlowNumber = obj.optString("invoice_flow_number");
		if(StringUtils.isNotBlank(invoiceFlowNumber)) {
			invoiceFlowNumber = StringEscapeUtils.escapeSql(invoiceFlowNumber);
			sql.append(" and ele_info.invoice_flow_number like '%").append(invoiceFlowNumber).append("%'");
		}

		String order_code = obj.optString("order_code");
		if(StringUtils.isNotBlank(order_code)) {
			order_code = StringEscapeUtils.escapeSql(order_code);
			sql.append(" and ele_info.order_code like '%").append(order_code).append("%'");
		}
		String tax = obj.optString("tax");
		if(StringUtils.isNotBlank(tax)) {
			tax = StringEscapeUtils.escapeSql(tax);
			sql.append(" and ele_info.tax like '%").append(tax).append("%'");
		}

		String invoice_state = obj.optString("invoice_state");
		if(StringUtils.isNotBlank(invoice_state)) {
			sql.append(" and ele_info.invoice_state = '").append(invoice_state).append("'");
		}
		String invoice_service_type = obj.optString("invoice_service_type");
		if(StringUtils.isNotBlank(invoice_service_type)) {
			sql.append(" and ele_info.invoice_service_type = '").append(invoice_service_type).append("'");
		}
		String invoice_type = obj.optString("invoice_type");
		if(StringUtils.isNotBlank(invoice_type)) {
			sql.append(" and ele_info.invoice_type = '").append(invoice_type).append("'");
		}

		String invoice_number = obj.optString("invoice_number");
		if(StringUtils.isNotBlank(invoice_number)) {
			sql.append(" and ele_info.invoice_number like '%").append(invoice_number).append("%'");
		}

		String total_tax_amount = obj.optString("total_tax_amount");
		if(StringUtils.isNotBlank(total_tax_amount)) {
			sql.append(" and ele_info.total_tax_amount = ").append(Double.valueOf(total_tax_amount)).append("");
		}

		String date1 = obj.optString("date1");
		if(StringUtils.isNotBlank(date1)) {
			sql.append(" and ele_info.invoice_time >= '").append(date1).append("'");
		}
		String date2 = obj.optString("date2");
		if(StringUtils.isNotBlank(date2)) {
			Date parseDate = DateUtils.parseDate(date2, new String[]{"yyyy-MM-dd"});
			Calendar cal = Calendar.getInstance();
			cal.setTime(parseDate);
			cal.add(Calendar.DAY_OF_MONTH, 1);
			date2 = DateFormatUtils.format(cal, "yyyy-MM-dd");
			sql.append(" and ele_info.invoice_time <= '").append(date2).append("'");
		}

		String date3 = obj.optString("date3");
		if(StringUtils.isNotBlank(date3)) {
//			sql.append(" and pb.report_date>='").append(date3).append("'");
			sql.append(" and ele_info.order_date>='").append(date3).append("'");
		}

		String date4 = obj.optString("date4");
		if(StringUtils.isNotBlank(date4)) {
//			sql.append(" and pb.report_date<='").append(date4).append("'");
			sql.append(" and ele_info.order_date<='").append(date4).append("'");
		}

		logger.info("电子发票查询sql:"+sql);
		int pagenum = obj.containsKey("page") ? (obj.getInt("page") == 0 ? 1
				: obj.getInt("page"))
				: 1;
		long total = this.dao.countSql(tenancyId, sql.toString());

		obj.put("sort", "ele_info.id");
		obj.put("order", "desc");
		List<JSONObject> list = this.dao.query4Json(tenancyId,
				this.dao.buildPageSql(obj, sql.toString()));

		List<JSONObject> list2 = this.dao.query4Json(tenancyId,sql.toString());

		JSONObject result = new JSONObject();
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		addFooter(list2, result);
		return result;
	}

	private void addFooter(List<JSONObject> list, JSONObject result) {
		List<JSONObject> footer = new ArrayList<JSONObject>();
		JSONObject foo = new JSONObject();
		BigDecimal taxAmountSum = new BigDecimal(0.00);
		BigDecimal totalAmountSum = new BigDecimal(0.00);
		BigDecimal totalTaxAmountSum = new BigDecimal(0.00);
		for (int i = 0; (list != null) && (i < list.size()); i++) {
			JSONObject jsonObject = (JSONObject)list.get(i);
			Double taxAmount = Double.valueOf(jsonObject.optDouble("tax_amount"));
			Double totalAmount = Double.valueOf(jsonObject.optDouble("total_amount"));
			Double totalTaxAmount = Double.valueOf(jsonObject.optDouble("total_tax_amount"));
			if (!taxAmount.isNaN()) {
				taxAmountSum= taxAmountSum.add(new BigDecimal(taxAmount));
			}
			if (!totalAmount.isNaN()) {
				totalAmountSum = totalAmountSum.add(new BigDecimal(totalAmount));
			}
			if (!totalTaxAmount.isNaN()) {
				totalTaxAmountSum = totalTaxAmountSum.add(new BigDecimal(totalTaxAmount));
			}
		}
		taxAmountSum = taxAmountSum.setScale(2,BigDecimal.ROUND_HALF_UP);
		totalAmountSum = totalAmountSum.setScale(2,BigDecimal.ROUND_HALF_UP);
		totalTaxAmountSum = totalTaxAmountSum.setScale(2,BigDecimal.ROUND_HALF_UP);

		foo.put("invoice_service_type", "合计");
		foo.put("tax_amount", taxAmountSum.toString());
		foo.put("total_amount", totalAmountSum.toString());
		foo.put("total_tax_amount", totalTaxAmountSum.toString());
		footer.add(foo);
		result.put("footer", footer);
	}


	@Override
	public HSSFWorkbook exportDate(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception {
		Integer rowNum=2;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",1);

		StringBuffer sql = new StringBuffer();
//		sql.append("select case ele_info.organ_id when 0 then '总部' else organ.org_full_name END,pb.report_date,ele_info.* from hq_electronic_invoice_info ele_info");
//		sql.append(" left join hq_electronic_invoice_details ele_details on ele_info.id = ele_details.electronic_id");
//		sql.append(" left join organ on ele_info.organ_id = organ.id");
////		sql.append(" left join pos_bill2 pb on pb.bill_num=ele_info.order_code or pb.order_num=ele_info.order_code ");
//		sql.append(" left join pos_bill2 pb on COALESCE(pb.order_num,pb.bill_num ) = ele_info.order_code ");
		sql.append("select case ele_info.organ_id when 0 then '总部' else organ.org_full_name END,ele_info.order_date as report_date,ele_info.* from hq_electronic_invoice_info ele_info");

		sql.append(" left join hq_electronic_invoice_details ele_details on ele_info.id = ele_details.electronic_id");
		sql.append(" left join organ on ele_info.organ_id = organ.id");
//		sql.append(" LEFT JOIN pos_bill2 pb ON COALESCE(pb.order_num, pb.bill_num) = ele_info.order_code ");
		sql.append(" where 1=1");

		String authority = json.optString("authority_organ");
		if(StringUtils.isNotBlank(authority)) {
			sql.append(" and ele_info.organ_id in (").append(authority).append(")");
		}

		String ids = json.optString("ids");
		if(StringUtils.isNotBlank(ids)) {
			sql.append(" and ele_info.organ_id in (").append(ids).append(")");
		}

		String invoiceFlowNumber = json.optString("invoice_flow_number");
		if(StringUtils.isNotBlank(invoiceFlowNumber)) {
			invoiceFlowNumber = StringEscapeUtils.escapeSql(invoiceFlowNumber);
			sql.append(" and ele_info.invoice_flow_number like '%").append(invoiceFlowNumber).append("%'");
		}

		String order_code = json.optString("order_code");
		if(StringUtils.isNotBlank(order_code)) {
			order_code = StringEscapeUtils.escapeSql(order_code);
			sql.append(" and ele_info.order_code like '%").append(order_code).append("%'");
		}
		String tax = json.optString("tax");
		if(StringUtils.isNotBlank(tax)) {
			tax = StringEscapeUtils.escapeSql(tax);
			sql.append(" and ele_info.tax like '%").append(tax).append("%'");
		}

		String invoice_state = json.optString("invoice_state");
		if(StringUtils.isNotBlank(invoice_state)) {
			sql.append(" and ele_info.invoice_state = '").append(invoice_state).append("'");
		}
		String invoice_service_type = json.optString("invoice_service_type");
		if(StringUtils.isNotBlank(invoice_service_type)) {
			sql.append(" and ele_info.invoice_service_type = '").append(invoice_service_type).append("'");
		}
		String invoice_type = json.optString("invoice_type");
		if(StringUtils.isNotBlank(invoice_type)) {
			sql.append(" and ele_info.invoice_type = '").append(invoice_type).append("'");
		}

		String date1 = json.optString("date1");
		if(StringUtils.isNotBlank(date1)) {
			sql.append(" and ele_info.invoice_time >= '").append(date1).append("'");
		}
		String date2 = json.optString("date2");
		if(StringUtils.isNotBlank(date2)) {
			Date parseDate = DateUtils.parseDate(date2, new String[]{"yyyy-MM-dd"});
			Calendar cal = Calendar.getInstance();
			cal.setTime(parseDate);
			cal.add(Calendar.DAY_OF_MONTH, 1);
			date2 = DateFormatUtils.format(cal, "yyyy-MM-dd");
			sql.append(" and ele_info.invoice_time <= '").append(date2).append("'");
		}

//		String date3 = json.optString("date3");
//		if(StringUtils.isNotBlank(date3)) {
//			sql.append(" and pb.report_date>='").append(date3).append("'");
//		}
//
//		String date4 = json.optString("date4");
//		if(StringUtils.isNotBlank(date4)) {
//			sql.append(" and pb.report_date<='").append(date4).append("'");
//		}
		String date3 = json.optString("date3");
		if(StringUtils.isNotBlank(date3)) {
//			sql.append(" and pb.report_date>='").append(date3).append("'");
			sql.append(" and ele_info.order_date>='").append(date3).append("'");
		}

		String date4 = json.optString("date4");
		if(StringUtils.isNotBlank(date4)) {
//			sql.append(" and pb.report_date<='").append(date4).append("'");
			sql.append(" and ele_info.order_date<='").append(date4).append("'");
		}

		logger.info("导出电子发票列表sql:"+sql);

		List<JSONObject> list = this.dao.query4Json(tenancyID,sql.toString());

		String key="";
		String val="";
		//获取开票业务类型map
		sql.setLength(0);
		sql.append("select class_item_code,class_item from sys_dictionary where class_identifier_code='electron_icinvoice_service_type'");
		List<JSONObject> typeList = this.dao.query4Json(tenancyID,sql.toString());
		Map<String,String> typeMap=new HashMap<String,String>();
		for(JSONObject typeJson:typeList){
			key=typeJson.optString("class_item_code");
			val=typeJson.optString("class_item");
			typeMap.put(key,val);
		}

		//获取开票状态map
		sql.setLength(0);
		sql.append("select class_item_code,class_item from sys_dictionary where class_identifier_code='electron_icinvoice_status'");
		List<JSONObject> stateList = this.dao.query4Json(tenancyID,sql.toString());
		Map<String,String> stateMap=new HashMap<String,String>();
		for(JSONObject stateJson:stateList){
			key=stateJson.optString("class_item_code");
			val=stateJson.optString("class_item");
			stateMap.put(key,val);
		}

		//发票类型map
		Map<String,String> invoiceTypeMap=new HashMap<String,String>();
		invoiceTypeMap.put("0","蓝字发票");
		invoiceTypeMap.put("1","红字发票");

		//////////////////////////////////////////////
		JSONObject out1Result =null;
		HSSFSheet sheet1;
		int ArrWidth[] = null;
		String ArrHeader[][];
		//创建sheet 表格   同时还可以设置名字!
		sheet1=workBook.createSheet(json.optString("exportName"));
		ArrHeader =new String[3][34];
		ArrHeader[0]=new String[] {"机构","订单号","发票流水号","业务类型","开票类型","发票代码","发票号码","税额","金额（不含税）","开票金额","税率","销售方纳税人识别号","发票抬头","开票人","开票时间","原发票代码","取消开票时间","开票状态"};
		ArrHeader[1]=new String[] {"org_full_name","order_code","invoice_flow_number","invoice_service_type","invoice_type","invoice_code","invoice_number","tax_amount","total_amount","total_tax_amount","tax_rate","tax","buyer_name","drawer","invoice_time","original_invoice_code","invoice_cancel_time","invoice_state"};
		ArrHeader[2]=new String[] {"String","String","String","String","String","String","String","String","String","String","String","String","String","String","String","String","String","String"};
		ArrWidth = new int[] {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17};
		String serviceType="";
		String invoiceState="";
		String invoiceType="";
		if(list.size()>0){
			for(JSONObject json1 : list) {
				serviceType=json1.optString("invoice_service_type");
				invoiceState=json1.optString("invoice_state");
				invoiceType=json1.optString("invoice_type");

				serviceType=typeMap.get(serviceType);
				invoiceState=stateMap.get(invoiceState);
				invoiceType=invoiceTypeMap.get(invoiceType);

				json1.put("invoice_service_type", serviceType);
				json1.put("invoice_state", invoiceState);
				json1.put("invoice_type", invoiceType);

				// 调用到处方法；
				out1Result =ExportUtils.out1(json1,workBook,sheet1,ArrHeader,paramData);
				paramData.put("rowNum", out1Result.opt("rowNum"));
				paramData.put("jin", out1Result.optInt("jin"));
			}
		}
		HSSFRow rowtitle =sheet1.createRow(0);
		HSSFRow rowtitle2 =sheet1.getRow(1);
		//合并上下的行 限制一列
		sheet1 =ExportUtils.upOrDownMergr(workBook, sheet1, ArrWidth, rowtitle2, rowtitle, 0, 1);
		sheet1.groupRow(1,out1Result.optInt("rowNum"));
		sheet1.setRowSumsBelow(false);
		sheet1.setRowSumsRight(false);

		return workBook;
	}

}
