package com.tzx.cc.baidu.bo.imp;

import org.apache.log4j.Logger;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

/**
 * <AUTHOR> on 2017-04-14
 */
public class OrderPublish {

    static Logger logger = Logger.getLogger(OrderPublish.class);

    static ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();//线程池所使用的缓冲队列
    static GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");

    static {
        taskExecutor.setQueueCapacity(200);
        //线程池维护线程的最少数量
        taskExecutor.setCorePoolSize(5);
        //线程池维护线程的最大数量
        taskExecutor.setMaxPoolSize(1000);
        //线程池维护线程所允许的空闲时间
        taskExecutor.setKeepAliveSeconds(30000);
        taskExecutor.initialize();
    }

    static RedisTemplate redisTemplate = (RedisTemplate) SpringConext.getBean("ordercenterRedisTemplate");

    /**
     * 发布到redis
     */
    public static void toRedis(final String tenancyId, final String orderCode, final String key, final String data, final long delaySecond, String orderDistributeType) {

        if ("REDIS".equalsIgnoreCase(orderDistributeType) || "ALL".equalsIgnoreCase(orderDistributeType)) {

            try {
                if (delaySecond > 0) {

                    taskExecutor.execute(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Thread.sleep(delaySecond * 1000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            DBContextHolder.setTenancyid(tenancyId);
                            String sql = "SELECT order_state FROM cc_order_list where order_code='" + orderCode + "';";
                            try {
                           /*     List<JSONObject> res = dao.query4Json(tenancyId, sql);
                                if (!res.isEmpty()) {
                                    String orderState = res.get(0).optString("order_state");
                                    logger.info("发布到redis订单状态:[" + orderCode + "]:"+orderState+"");
//                                    if ("01".equals(orderState)) {
                                        redisTemplate.opsForList().rightPush(key, data);
                                        logger.info("订单[" + orderCode + "]已成功异步发布到redis[key=" + key + ",value=" + data + ",delay=" + delaySecond + "s]");
//                                    }
                                    
                                }*/
                            	// 防止未退取消单   新增/取消订单同时往redis和mq里放
                                redisTemplate.opsForList().rightPush(key, data);
                                logger.info("订单[" + orderCode + "]已成功异步发布到redis[key=" + key + ",value=" + data + ",delay=" + delaySecond + "s]");
                            } catch (Exception e) {
                            	logger.info(orderCode+ e.getStackTrace());
                                e.printStackTrace();
                            }
                        }
                    });
                }
                if (delaySecond == 0) {
                    redisTemplate.opsForList().rightPush(key, data);
                }
            } catch (Exception e) {
                e.printStackTrace();
                logger.info("订单[" + orderCode + "]发布到redis失败!");
            }
        }
    }
}
