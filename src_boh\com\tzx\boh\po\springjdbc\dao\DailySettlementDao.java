package com.tzx.boh.po.springjdbc.dao;

import java.util.List;
import java.util.Map;

/**
 * 日结DAO
 * Created by <PERSON> on 2016-08-22.
 */
public interface DailySettlementDao {

    String Name = "com.tzx.boh.po.springjdbc.dao.impl.DailySettlementDaoImpl";

    public void getRepairPayment(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getInsertPosBill(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getInsertPosBillItem(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getInsertPosBillPayment(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getUnusualReason(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getPayment(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getItem(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getUpItem(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getItemTimes(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getUpItemTimes(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getOrganTimes(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getItemShift(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getUpdateItemShift(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getDaucount(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getdeletePosBill(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getDeletePosBillItem(StringBuilder sql, Map<String, String> map, List param) throws Exception;

    public void getDeletePosBillPayment(StringBuilder sql, Map<String, String> map, List param) throws Exception;

}
