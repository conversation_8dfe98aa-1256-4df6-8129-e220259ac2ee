package com.tzx.cc.datasync.service.rest;

import com.tzx.cc.datasync.bo.SyncSystemDataRepairService;
import com.tzx.cc.datasync.bo.SyncSystemDataService;
import com.tzx.framework.bo.OrganService;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.JsonUtils;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller("SyncSystemDataRest")
@RequestMapping("/cc/syncsystemdatarest")
public class SyncSystemDataRest {
	
	private static final Logger		logger	= Logger.getLogger(SyncSystemDataRest.class);
	
    @Resource(name = SyncSystemDataService.NAME)
    private SyncSystemDataService syncSystemDataService;

    @Resource(name = OrganService.NAME)
    private OrganService organService;

    @Resource(name = SyncSystemDataRepairService.NAME)
    private SyncSystemDataRepairService syncSystemDataRepairService;


    @RequestMapping(value = "/syncSystemDataCfg", method = RequestMethod.POST)
    public void syncSystemDataCfg(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        String operType = request.getParameter("operType");
        String result = "";

        JSONObject obj = JSONObject.fromObject("{}");
        Map<String, String[]> map = request.getParameterMap();
        for (String key : map.keySet()) {
            obj.put(key, map.get(key)[0]);
        }

        switch (operType) {
            case "list":
                try {
                    obj.put("tenancy_id", (String) session.getAttribute("tenentid"));
                    result = syncSystemDataService.dataSourceLoad((String) session.getAttribute("tenentid"), obj)
                            .toString();
                } catch (Exception e) {
                    logger.error(e);
                    e.printStackTrace();
                }
                break;
            case "save":
                try {
                    obj.put("last_operator", session.getAttribute("employeeName"));
                    obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
                    obj.put("tenancy_id", (String) session.getAttribute("tenentid"));
                    result = syncSystemDataService.dataSourceSave((String) session.getAttribute("tenentid"),
                            "cc_rif_database_setting", obj);
                } catch (Exception e) {
                    logger.error(e);
                    result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
                    e.printStackTrace();
                }
                break;
            default:
                break;
        }
        close4Finally(response, out, result.toString());

    }



    @RequestMapping(value = "/syncSystemData", method = RequestMethod.POST)
    public void syncSystemData(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        InputStream in = null;
        HttpSession session = request.getSession();
        String result = "{\"success\": true}";
        try {
            JSONObject p = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                p.put(key, map.get(key)[0]);
            }
            p.put("tenancyid", (String) session.getAttribute("tenentid"));

            logger.info("rif到saas同步数据开始");
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            JSONObject result_flag = syncSystemDataService.dataTransfer(p);
            stopWatch.stop();
            logger.info("处理同步数据时:"+stopWatch.prettyPrint());
            double timed = stopWatch.getTotalTimeSeconds();
            int timeint = (int)timed;

            int minite = timeint/(int) 60;
            int second = timeint%(int)60;
            String timestr = "所用时间"+minite+"分"+second+"秒!";
            logger.info(timestr);

            if (result_flag.optBoolean("flag")) {
                result = "{\"success\" : true , \"msg\" : \"数据同步成功!"+timestr+"\"}";
            } else {
                if(StringUtils.isBlank(result_flag.optString("msg"))) {
                    result = "{\"success\" : false , \"msg\" : \"数据同步失败!\"}";
                } else {
                    result = "{\"success\" : false , \"msg\" : \""+result_flag.optString("msg")+"\"}";
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e);
            result = "{\"success\" : false , \"msg\" : \"同步失败!\"}";
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e) {
                logger.error(e);
            }

            close4Finally(response, out, result.toString());
        }

    }

    /**
     * 获取机构映射数据列表
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/getRifOrgData", method = RequestMethod.POST)
    public void getRifOrgData(HttpServletRequest request, HttpServletResponse response) {

        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;

        HttpSession session = request.getSession();
        String result = "";

        try {
            JSONObject p = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet()) {
                p.put(key, map.get(key)[0]);
            }

            result = syncSystemDataService.getRifOrgData(p);

        } catch (Exception e) {
            logger.error(e);
            e.printStackTrace();
            result = "{\"success\" : false , \"msg\" : \"同步失败!\"}";

        } finally {
            close4Finally(response, out, result.toString());
        }

    }

    /**
     * 测试连接是否ping通
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/synPing", method = RequestMethod.POST)
    public void synPing(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject result = new JSONObject();
        try {
            JSONObject p = JSONObject.fromObject("{}");
            Map<String, String[]> map = request.getParameterMap();
            for (String key : map.keySet()) {
                p.put(key, map.get(key)[0]);
            }
            result = syncSystemDataService.ping((String) session.getAttribute("tenentid"), p);
        } catch (Exception e) {
            logger.error(e);
            e.printStackTrace();
            result.put("success","false");
            result.put("msg","失败");
        } finally {
            close4Finally(response, out, result.toString());
        }
    }


    /**
     * 测试连接是否ping通
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/repairdata", method = RequestMethod.POST)
    public void repairData(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject result = new JSONObject();
        try {
            JSONObject p = JSONObject.fromObject("{}");
            Map<String, String[]> map = request.getParameterMap();
            for (String key : map.keySet()) {
                p.put(key, map.get(key)[0]);
            }
            p.put("tenancyid",(String) session.getAttribute("tenentid"));
            result = syncSystemDataRepairService.repairSynData(p);
        } catch (Exception e) {
            logger.error(e);
            e.printStackTrace();
            result.put("success","false");
            result.put("msg","失败");
        } finally {
            close4Finally(response, out, result.toString());
        }
    }

    /**
     * 刷新到response
     * @param response
     * @param out
     * @param result
     */
    private void close4Finally(HttpServletResponse response, PrintWriter out, String result) {
        try {
            out = response.getWriter();
            out.print(result.toString());
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error(e);
        } finally {
            if (out != null)
                out.close();
        }
    }


    /**
     * 获取同步的渠道
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/getChanels", method = RequestMethod.POST)
    public void getChanels(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject result = new JSONObject();
        try {
            JSONObject p = JSONObject.fromObject("{}");
            Map<String, String[]> map = request.getParameterMap();
            for (String key : map.keySet()) {
                p.put(key, map.get(key)[0]);
            }
            List<JSONObject> chanellist = syncSystemDataService.getChanelInfo((String) session.getAttribute("tenentid"));
            result.put("chanellist", chanellist);
        } catch (Exception e) {
            logger.error(e);
            e.printStackTrace();
        } finally {
            close4Finally(response, out, result.toString());
        }
    }
}
