<?xml version="1.0" encoding="UTF-8"?>

<conditions>
	<condition id="001">
		<name>SAAS_DR_006</name>
		<description>日结后实时表没清除</description>
		<sql>
			select tenancy_id, store_id, bill_num, report_date from pos_bill as a where EXISTS (select 1 from pos_bill2 as b where a.store_id=b.store_id and a.report_date=b.report_date and a.bill_num=b.bill_num and a.payment_amount=b.payment_amount and a.payment_time=b.payment_time) and EXISTS (select 1 from hq_daycount_info as c where c.store_id=a.store_id and c.day_count=a.report_date and hq_sign='1') and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			union all
			select tenancy_id, store_id, bill_num, report_date from pos_bill_payment as a where EXISTS (select 1 from pos_bill_payment2 as b where a.store_id=b.store_id and a.report_date=b.report_date and a.bill_num=b.bill_num and a.jzid=b.jzid and a.amount=b.amount and a.currency_amount=b.currency_amount and a.last_updatetime=b.last_updatetime) and EXISTS (select 1 from hq_daycount_info as c where c.store_id=a.store_id and c.day_count=a.report_date and hq_sign='1') and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			union all
			select tenancy_id, store_id, bill_num, report_date from pos_bill_item as a where EXISTS (select 1 from pos_bill_item2 as b where a.store_id=b.store_id and a.report_date=b.report_date and a.bill_num=b.bill_num and a.item_id=b.item_id and a.item_amount=b.item_amount and a.real_amount=b.real_amount and a.item_time=b.item_time and a.rwid=b.rwid) and EXISTS (select 1 from hq_daycount_info as c where c.store_id=a.store_id and c.day_count=a.report_date and hq_sign='1') and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>3</paramNumber>
	</condition>
	<condition id="002">
		<name>SAAS_DR_008</name>
		<description>套餐实结金额小于明细实结金额之和</description>
		<sql>
			SELECT A.TENANCY_ID,A.STORE_ID,A.REPORT_DATE,A.BILL_NUM,CASE WHEN A.ITEM_PROPERTY = 'SETMEAL' THEN A.ITEM_ID
			WHEN A.ITEM_PROPERTY = 'MEALLIST' THEN A.SETMEAL_ID ELSE A.SETMEAL_ID END AS TCID,CASE WHEN A.ITEM_PROPERTY = 'SETMEAL' THEN A.ITEM_SERIAL
			WHEN A.ITEM_PROPERTY = 'MEALLIST' THEN A.SETMEAL_RWID ELSE A.SETMEAL_RWID END AS DCXH,A.ITEM_REMARK
			,MAX(CASE WHEN A.ITEM_PROPERTY = 'SETMEAL' THEN A.ITEM_COUNT END) AS TCSL
			,MAX(CASE WHEN A.ITEM_PROPERTY = 'MEALLIST'THEN A.ITEM_COUNT END) AS MXSL
			,SUM(CASE WHEN A.ITEM_PROPERTY = 'SETMEAL' THEN A.ITEM_AMOUNT ELSE 0 END) AS TCCPJE
			,SUM(CASE WHEN A.ITEM_PROPERTY = 'MEALLIST' THEN A.ITEM_AMOUNT ELSE 0 END) AS MXCPJE
			,SUM(CASE WHEN A.ITEM_PROPERTY = 'SETMEAL' THEN A.REAL_AMOUNT ELSE 0 END) AS TCSJJE
			,SUM(CASE WHEN A.ITEM_PROPERTY = 'MEALLIST' THEN A.REAL_AMOUNT ELSE 0 END) AS MXSJJE
			FROM PUBLIC.POS_BILL_ITEM2 A
			WHERE A.ITEM_PROPERTY IN('MEALLIST','SETMEAL') AND A.TENANCY_ID = ? AND A.STORE_ID = ? AND A.REPORT_DATE = ? 
			GROUP BY A.TENANCY_ID ,A.STORE_ID,A.REPORT_DATE,A.BILL_NUM ,CASE WHEN A.ITEM_PROPERTY = 'SETMEAL' THEN A.ITEM_ID
			WHEN A.ITEM_PROPERTY = 'MEALLIST' THEN A.SETMEAL_ID ELSE A.SETMEAL_ID END,CASE WHEN A.ITEM_PROPERTY = 'SETMEAL' THEN A.ITEM_SERIAL
			WHEN A.ITEM_PROPERTY = 'MEALLIST' THEN A.SETMEAL_RWID ELSE A.SETMEAL_RWID END ,A.ITEM_REMARK
			HAVING ABS(SUM(CASE WHEN A.ITEM_PROPERTY = 'SETMEAL' THEN A.ITEM_AMOUNT ELSE 0 END) - SUM(CASE WHEN A.ITEM_PROPERTY = 'MEALLIST' THEN A.ITEM_AMOUNT ELSE 0 END))
			+ ABS(SUM(CASE WHEN A.ITEM_PROPERTY = 'SETMEAL' THEN A.REAL_AMOUNT ELSE 0 END) - SUM(CASE WHEN A.ITEM_PROPERTY = 'MEALLIST' THEN A.REAL_AMOUNT ELSE 0 END)) > 0
			ORDER BY A.REPORT_DATE DESC,A.STORE_ID,A.BILL_NUM
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,tcid,dcxh,item_remark,tcsl,mxsl,tccpje,mxcpje,tcsjje,mxsjje</column>
		<comment>商户号,门店号,账单号,报表日期,退菜Id,点菜序号,卧单备注,套菜数量,明细数量,套菜菜品金额,明细菜品金额,套菜实结金额,明细实结金额</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="003">
		<name>SAAS_DR_009</name>
		<description>付款方式ID混乱</description>
		<sql>
			<![CDATA[
			with t1 as(
			select '人民币' as name, id as jzid from payment_way where payment_class = 'cash' and payment_name1 = 'RMB'
			UNION
			select '找零' as name, id as jzid from payment_way where payment_class = 'cash' and payment_name1 = 'RMB'
			) , t2 as(
			select a.jzid, COALESCE(a.name,'') as name from pos_bill_payment2 a where a.tenancy_id = ? and a.store_id = ? and a.report_date = ?  group by jzid, name
			) , t3 as(
			select * from t2 where not exists (select 1 from t1 where t1.jzid = t2.jzid and t1.name = t2.name)
			EXCEPT
			select id as jzid , payment_name1 as name from payment_way where payment_class <> 'cash' and payment_name1 <> 'RMB'
			) 
			select tenancy_id,report_date,store_id, bill_num from pos_bill_payment a
			inner join t3 b on a.jzid = b.jzid and a.name = b.name
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="004">
		<name>SAAS_DR_012</name>
		<description>存在付款没有账单</description>
		<sql>
			SELECT A.TENANCY_ID,A.STORE_ID,A.REPORT_DATE,A.BILL_NUM,B.STORE_ID PB2_STORE_ID,B.REPORT_DATE PB2_REPORT_DATE,B.BILL_NUM PB2_BILL_NUM
			FROM POS_BILL_PAYMENT2 A LEFT JOIN POS_BILL2 B ON A.BILL_NUM = B.BILL_NUM
			WHERE B.BILL_NUM IS NULL AND A.TENANCY_ID = ? AND A.STORE_ID = ? AND A.REPORT_DATE = ?
			ORDER BY A.REPORT_DATE DESC,A.STORE_ID,A.BILL_NUM
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,pb2_store_id,pb2_bill_num,pb2_report_date</column>
		<comment>商户号,门店号,账单号,报表日期,pos_bill_门店号,pos_bill_账单号,pos_bill_报表日期</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="005">
		<name>SAAS_DR_013</name>
		<description>挂账流水与账务付款中挂账部分不一致</description>
		<sql>
			<![CDATA[
			select a.tenancy_id,a.store_id,a.report_date,a.bill_num,a.jzid,a.name,a.amount,b.store_id as store_id_b,b.business_date,b.third_bill_code,b.gz_money
			from pos_bill_payment2 a left join crm_incorporation_gzlist b on a.bill_code = b.bill_code
			where a.type = 'incorporation' and a.amount >= 0 and a.amount <> coalesce(abs(b.gz_money),0) and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			order by a.report_date desc,a.store_id,a.bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,jzid,name,amount,store_id_b,business_date,third_bill_code,gz_money</column>
		<comment>商户号,门店号,账单号,报表日期,付款方式编号,付款方式名称,付款金额,crm_incorporation_gzlist_门店号,挂账日期,第三方账单号,挂账金额</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="006">
		<name>SAAS_DR_015</name>
		<description>退菜原流水未标记TC01</description>
		<sql>
			<![CDATA[
			select tenancy_id,store_id,report_date,bill_num,sum(sumTC01) sumTC01,sum(sumCJ05) sumCJ05 from
			(SELECT c.tenancy_id,c.store_id,c.report_date,c.bill_num,sum(case when item_remark='TC01' then 1 else 0 end) as sumTC01,sum(case when item_remark='CJ05' then 1 else 0 end) as sumCJ05
			FROM pos_bill_item2 c 
			where exists (select a.bill_num,b.bill_num from pos_bill2 a,pos_bill2 b where b.bill_state='CJ01' and b.copy_bill_num=a.bill_num and c.bill_num=a.bill_num)
			group by c.tenancy_id,c.store_id,c.report_date,c.bill_num
			union all
			SELECT c.tenancy_id,c.store_id,c.report_date,d.bill_num ,sum(case when item_remark='TC01' then 1 else 0 end) as sumTC01,sum(case when item_remark='CJ05' then 1 else 0 end) as sumCJ05
			FROM pos_bill_item2 c inner join 
			(select a.bill_num ,b.bill_num as cj_bill_num from pos_bill2 a,pos_bill2 b where b.bill_state='CJ01' and b.copy_bill_num=a.bill_num ) d
			on d.cj_bill_num=c.bill_num group by c.store_id,c.report_date,d.bill_num,c.tenancy_id) e
			where e.tenancy_id = ? and e.store_id = ? and e.report_date = ? and bill_num is not NULL group by tenancy_id,store_id,report_date,bill_num having sum(sumTC01)<sum(sumCJ05)
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,sumtc01,sumcj05</column>
		<comment>商户号,门店号,账单号,报表日期,退菜未标记,冲减未标记</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="007">
		<name>SAAS_DR_016</name>
		<description>菜品奉送后整单取消，原奉送菜品标记错误</description>
		<sql>
			select tenancy_id,report_date,store_id,bill_num from pos_bill_item2 as a where ITEM_REMARK ='FS02' and EXISTS (select 1 from pos_bill2 as b 
			where a.report_date=b.report_date and a.store_id=b.store_id and a.bill_num=b.bill_num and b.bill_state in ('ZDQX02')) and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			union all
			select tenancy_id,report_date,store_id,bill_num from pos_bill_item2 as c where c.ITEM_REMARK ='FS02' and EXISTS (select 1 from 
			(select a.store_id,a.bill_num,b.bill_num as cj_bill_num from pos_bill2 a,pos_bill2 b 
			where b.bill_state='CJ01' and b.copy_bill_num=a.bill_num and a.store_id=b.store_id and b.bill_state in ('ZDQX02')) as d 
			where c.store_id=d.store_id and c.bill_num=d.cj_bill_num) and c.tenancy_id = ? and c.store_id = ? and c.report_date = ?
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="008">
		<name>SAAS_DR_018</name>
		<description>挂账相关表日期与对应账单日期有差异</description>
		<sql>
			<![CDATA[
			SELECT A.TENANCY_ID,A.STORE_ID,A.REPORT_DATE,A.BILL_NUM,A.JZID,A.NAME,B.STORE_ID CRM_STORE_ID,B.BUSINESS_DATE,B.THIRD_BILL_CODE
			FROM POS_BILL_PAYMENT2 A
			LEFT JOIN CRM_INCORPORATION_GZLIST B ON A.BILL_NUM = B.THIRD_BILL_CODE
			WHERE A.TYPE = 'incorporation' AND A.REPORT_DATE <> COALESCE(B.BUSINESS_DATE,CURRENT_DATE) AND A.TENANCY_ID = ? AND A.STORE_ID = ? AND A.REPORT_DATE = ?
			ORDER BY A.REPORT_DATE DESC,A.STORE_ID,A.BILL_NUM
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,jzid,name,crm_store_id,business_date,third_bill_code</column>
		<comment>商户号,门店号,账单号,报表日期,付款方式编号,crm_门店号,挂账日期,第三方账单号</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="009">
		<name>SAAS_DR_020</name>
		<description>账单存在本系统卡付款，卡交易流水缺失</description>
		<sql>
			<![CDATA[
			select tenancy_id,store_id,report_date,bill_num,jzid from pos_bill_payment2 as b
			where EXISTS (select 1 from (
			(select bill_num from pos_bill_payment2 as a where a.jzid=1 union all select bill_num from pos_bill_payment as a where a.jzid=1)
			EXCEPT select third_bill_code from crm_card_trading_list as a where COALESCE(third_bill_code,'')<>'' and a.operat_type in('03','05')) as c where b.bill_num=c.bill_num)
			and b.tenancy_id = ? and b.store_id = ? and b.report_date = ?
			order by report_date desc,store_id,bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,jzid</column>
		<comment>商户号,门店号,账单号,报表日期,付款方式编号</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="010">
		<name>SAAS_DR_021</name>
		<description>卡交易流水存在，对应账单缺失</description>
		<sql>
			<![CDATA[
			select tenancy_id,store_id,business_date,third_bill_code as bill_num
			from crm_card_trading_list as b
			where EXISTS (select 1 from (
			select third_bill_code from crm_card_trading_list as a where COALESCE(third_bill_code,'')<>'' and a.operat_type in('03','05')
			EXCEPT
			(
			select bill_num from pos_bill_payment2 
			union all
			select bill_num from pos_bill_payment 
			)
			) as c where b.third_bill_code=c.third_bill_code
			)
			and b.tenancy_id = ? and b.store_id = ? and b.business_date = ?
			order by business_date desc,store_id,bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,business_date</column>
		<comment>商户号,门店号,账单号,挂账日期</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="011">
		<name>SAAS_DR_022</name>
		<description>卡交易流水存在，对应账单付款方式错误</description>
		<sql>
			<![CDATA[
			select tenancy_id,store_id,business_date,third_bill_code as bill_num
			from crm_card_trading_list as b
			where EXISTS (select 1 from (( 
			select third_bill_code from crm_card_trading_list as a where COALESCE(third_bill_code,'')<>'' and operat_type in('03','05')
			EXCEPT
			(
			select bill_num from pos_bill_payment2 as a where a.jzid=1
			union all
			select bill_num from pos_bill_payment as a where a.jzid=1
			)
			)
			intersect
			select bill_num from pos_bill_payment2 
			) as c where b.third_bill_code=c.third_bill_code
			)
			and operat_type in('03','05') and b.tenancy_id = ? and b.store_id = ? and b.business_date = ?
			order by business_date desc,store_id,bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num，business_date</column>
		<comment>商户号,门店号,账单号,挂账日期</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="012">
		<name>SAAS_DR_023</name>
		<description>营业相关表班次字段为空</description>
		<sql>
			<![CDATA[
			select tenancy_id,store_id,report_date,bill_num,shift_id,'payment2' as ttype
			from pos_bill_payment2
			where not EXISTS (select 1 from (select a.id from (select id,name from duty_order ) as a
			INNER JOIN sys_dictionary as b on a.name = b.class_item_code) as a where coalesce(shift_id,-1)=id)
			and tenancy_id = ? and store_id = ? and report_date = ?
			union all
			select tenancy_id,store_id,report_date,bill_num,shift_id,'bill2' as ttype
			from pos_bill2
			where not EXISTS (select 1 from (select a.id from (select id,name from duty_order ) as a
			INNER JOIN sys_dictionary as b on a.name = b.class_item_code) as a where coalesce(shift_id,-1)=id)
			and tenancy_id = ? and store_id = ? and report_date = ?
			union all
			select tenancy_id,store_id,report_date,bill_num,item_shift_id,'bill_item2' as ttype
			from pos_bill_item2
			where not EXISTS (select 1 from (select a.id from (select id,name from duty_order ) as a
			INNER JOIN sys_dictionary as b on a.name = b.class_item_code) as a where coalesce(item_shift_id,-1)=id)
			and tenancy_id = ? and store_id = ? and report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,shift_id,ttype</column>
		<comment>商户号,门店号,账单号,报表日期,结账班次ID,表名</comment>
		<paramNumber>3</paramNumber>
	</condition>
	<condition id="013">
		<name>SAAS_DR_024</name>
		<description>付款方式ID为空</description>
		<sql>
			<![CDATA[
			select a.tenancy_id,a.report_date,a.store_id,a.bill_num from pos_bill_payment2 as a
			where jzid is null and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			and EXISTS (select 1 from v_payment_name as b where (case when a.name in ('人民币','找零') then 'RMB' else a.name end)=b.payment_name)
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="014">
		<name>SAAS_DR_026</name>
		<description>营业表销售模式字段为空</description>
		<sql>
			<![CDATA[
			select tenancy_id, store_id, bill_num,report_date
			from pos_bill_item2 as a
			where coalesce(sale_mode,'')='' and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="015">
		<name>SAAS_DR_027</name>
		<description>订单转账单后丢失</description>
		<sql>
			<![CDATA[
			select p.tenancy_id,store_id,report_date,order_num,count(1) gs,bill_num
			from pos_bill2 p 
			where source <>'MD01' and not exists (select 1 from cc_order_list c where c.order_code=p.order_num) and p.tenancy_id = ? and p.store_id = ? and p.report_date = ?
			group by p.tenancy_id,store_id,report_date,order_num,bill_num
			order by 1,2 desc
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,order_num,gs</column>
		<comment>商户号,门店号,账单号,报表日期,订单号,数量</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="016">
		<name>SAAS_DR_028</name>
		<description>账单中积分抵现金额与积分流水不一致</description>
		<sql>
			<![CDATA[
			select a.*,b.currency_amount,b.report_date from 
			(
			select tenancy_id,store_id,business_date,third_bill_code,sum(total_credit) as total_credit,sum(credit) as credit,sum(change_money) as change_money,count(1) as gs
			from crm_customer_credit_list 
			where operat_type in ('14','15') and tenancy_id = ? and store_id = ? and business_date = ?
			group by tenancy_id,store_id,business_date,third_bill_code
			) as a
			inner JOIN
			(
			select report_date,bill_num,sum(currency_amount) as currency_amount from pos_bill_payment2 
			where type = 'card_credit' and tenancy_id = ? and store_id = ? and report_date = ?
			group by report_date,bill_num
			) as b on a.third_bill_code=b.bill_num
			where currency_amount<>change_money order by business_date desc,bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,report_date,business_date,third_bill_code,total_credit,credit,change_money,gs,currency_amount</column>
		<comment>商户号,门店号,报表日期,挂账日期,第三方账单号,交易总积分,本次交易积分,抵扣金额,数量,本币金额</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="017">
		<name>SAAS_DR_035</name>
		<description>统计流水表数据重复</description>
		<sql>
			<![CDATA[
			select item.tenancy_id,item.report_date,item.store_id,item.bill_num,'pos_bill_item2' tablename,1 as gs
			from (
			select tenancy_id,report_date,store_id,bill_num,sum(item_amount) as item_amount
			from pos_bill_item2 as a
			where a.tenancy_id = ? and a.store_id = ? and a.report_date = ? and EXISTS ( select 1 from (
			select store_id,report_date,bill_num,item_id,item_amount,real_amount,item_time,rwid from pos_bill_item2
			group by store_id,report_date,bill_num,item_id,item_amount,real_amount,item_time,rwid
			having count(1) > 1
			) as b where a.store_id=b.store_id and a.report_date=b.report_date and a.bill_num=b.bill_num and a.item_property<>'MEALLIST')
			group by tenancy_id,report_date,store_id,bill_num
			) as item
			inner join (select report_date,store_id,bill_num ,bill_amount from pos_bill2 LIMIT 1) as bill
			on item.report_date=bill.report_date and item.store_id=bill.store_id and item.bill_num=bill.bill_num and item.item_amount<>bill.bill_amount
			union all
			select item.tenancy_id,item.report_date,item.store_id,item.bill_num,'pos_bill_payment2' tablename,1 as gs
			from (
			select tenancy_id,report_date,store_id,bill_num,sum(currency_amount) as currency_amount
			from pos_bill_payment2 as a
			where a.tenancy_id = ? and a.store_id = ? and a.report_date = ? and EXISTS ( select 1 from (
			select store_id,report_date,bill_num,jzid,amount,currency_amount,last_updatetime from pos_bill_payment2
			group by store_id,report_date,bill_num,jzid,amount,currency_amount,last_updatetime
			having count(1) > 1
			) as b where a.store_id=b.store_id  and a.report_date=b.report_date and a.bill_num=b.bill_num)
			group by tenancy_id,report_date,store_id,bill_num
			) as item
			inner join (select report_date,store_id,bill_num ,payment_amount from pos_bill2 LIMIT 1) as bill
			on item.report_date=bill.report_date and item.store_id=bill.store_id and item.bill_num=bill.bill_num and item.currency_amount<>bill.payment_amount
			union all
			select tenancy_id,report_date,store_id,bill_num,'pos_bill2' tablename,count(1) as gs
			from pos_bill2 as a
			where a.tenancy_id = ? and a.store_id = ? and a.report_date = ? and EXISTS ( select 1 from (
			select store_id,report_date,bill_num,bill_amount,payment_amount,payment_time from pos_bill2
			group by store_id,report_date,bill_num,bill_amount,payment_amount,payment_time
			having count(1) > 1
			) as b where a.store_id=b.store_id 
			and a.report_date=b.report_date
			and a.bill_num=b.bill_num)
			group by tenancy_id,report_date,store_id ,bill_num,bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,tablename,gs</column>
		<comment>商户号,门店号,账单号,报表日期,表名,数量</comment>
		<paramNumber>3</paramNumber>
	</condition>
	<condition id="018">
		<name>SAAS_DR_036</name>
		<description>账单表与付款表付款金额不一致</description>
		<sql>
			<![CDATA[
			with t1 as 
			(select tenancy_id,store_id,report_date,bill_num,payment_amount from pos_bill2 where tenancy_id = ? and store_id = ? and report_date = ?)
			,
			t2 as (
			select report_date,bill_num,sum(currency_amount) as currency_amount from pos_bill_payment2 as a where a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by report_date,bill_num
			)
			select t1.*,COALESCE(t2.currency_amount,'0') as currency_amount
			from t1 
			left JOIN t2
			on t1.report_date=t2.report_date
			and t1.bill_num=t2.bill_num
			where COALESCE(t1.payment_amount,0)<>COALESCE(t2.currency_amount,'0')
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,payment_amount,currency_amount</column>
		<comment>商户号,门店号,账单号,报表日期,付款金额,本币金额</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="019">
		<name>SAAS_DR_037</name>
		<description>商户号出现数据混乱</description>
		<sql>
			<![CDATA[
			select tenancy_id,report_date,store_id,bill_num,'pos_bill' tablename,count(*) as gs
			from pos_bill2 as a
			where a.tenancy_id <> tenancy_id() and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by tenancy_id,report_date,store_id,bill_num
			union all
			select tenancy_id,report_date,store_id,bill_num,'pos_bill_item' tablename,count(*) as gs
			from pos_bill_item2 as a
			where a.tenancy_id <> tenancy_id() and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by tenancy_id,report_date,store_id,bill_num
			union all
			select tenancy_id,report_date,store_id,bill_num,'pos_bill_payment' tablename,count(*) as gs
			from pos_bill_payment2 as a 
			where a.tenancy_id <> tenancy_id() and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by tenancy_id,report_date,store_id,bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,tablename,gs</column>
		<comment>商户号,门店号,账单号,报表日期,表名,数量</comment>
		<paramNumber>3</paramNumber>
	</condition>
	<condition id="020">
		<name>SAAS_DR_039</name>
		<description>套餐明细数量单价算法存在偏差</description>
		<sql>
			<![CDATA[
			select tenancy_id,report_date,store_id,bill_num,item_property,item_price,item_count,assist_money,item_amount
			from pos_bill_item as a
			where item_property in('MEALLIST','SINGLE') and round(item_price * item_count + assist_money,2) <> item_amount and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,item_property,item_price,item_count,assist_money,item_amount</column>
		<comment>商户号,门店号,账单号,报表日期,菜目属性,菜目单价,菜目数量,辅助金额,菜目金额</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="021">
		<name>SAAS_DR_041</name>
		<description>账单明细公式不平</description>
		<sql>
			<![CDATA[
			select tenancy_id,report_date,store_id,bill_num,item_amount,real_amount,discount_amount,discountr_amount,single_discount_amount,item_remark_his
			from pos_bill_item2
			where COALESCE(item_remark,'') not in ('FS02','MD03')
			and COALESCE(item_remark_his,'') <> 'FS02'
			and item_amount <> real_amount + discount_amount + discountr_amount - single_discount_amount
			and tenancy_id = ? and store_id = ? and report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,item_amount,real_amount,discount_amount,discountr_amount,single_discount_amount,item_remark_his</column>
		<comment>商户号,门店号,账单号,报表日期,菜目金额,实结金额,折扣金额,折让金额,抹零均摊金额,历史菜品状态</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="022">
		<name>SAAS_DR_042</name>
		<description>冲减单付款方式存在重复</description>
		<sql>
			<![CDATA[
			select *
			from 
			(
			select tenancy_id,report_date,store_id,bill_num,abs(sum(currency_amount)) as currency_amount
			from pos_bill_payment2 as a
			where EXISTS (select 1 from (
			select store_id,report_date,bill_num,jzid,amount,currency_amount,last_updatetime from pos_bill_payment2
			group by store_id,report_date,bill_num,jzid,amount,currency_amount,last_updatetime
			having count(1) > 1
			) as b where a.store_id=b.store_id 
			and a.report_date=b.report_date
			and a.bill_num=b.bill_num
			and a.jzid=b.jzid
			) group by a.tenancy_id,a.report_date,a.store_id,a.bill_num
			) as item
			inner join (select tenancy_id,report_date,store_id,bill_num ,payment_amount,bill_state from pos_bill2 ) as bill
			on item.report_date=bill.report_date
			and item.store_id=bill.store_id
			and item.bill_num=bill.bill_num
			and item.currency_amount = bill.payment_amount
			and bill_state='CJ01'
			and item.tenancy_id = item.tenancy_id
			where item.tenancy_id = ? and item.store_id = ? and item.report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,currency_amount,payment_amount,bill_state</column>
		<comment>商户号,门店号,账单号,报表日期,总的本币金额,付款金额,账单状态</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="023">
		<name>SAAS_DR_043</name>
		<description>门店取消打烊导致总部日结功能不完整</description>
		<sql>
			<![CDATA[
			select tenancy_id,report_date,store_id,bill_num,'pos_bill2' tablename,count(1) as gs
			from pos_bill as a
			where not EXISTS ( select 1 from pos_bill2 as b where a.store_id=b.store_id 
			and a.report_date=b.report_date
			and a.bill_num=b.bill_num)
			and EXISTS (select 1 from hq_daycount_info as c where c.store_id=a.store_id and c.day_count=a.report_date and hq_sign='1')
			and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by tenancy_id,report_date,store_id ,bill_num
			union all
			select tenancy_id,report_date,store_id,bill_num,'pos_bill_item2' tablename,count(1) as gs
			from pos_bill_item as a
			where not EXISTS ( select 1 from pos_bill_item2 as b where a.store_id=b.store_id 
			and a.report_date=b.report_date
			and a.bill_num=b.bill_num
			)
			and EXISTS (select 1 from hq_daycount_info as c where c.store_id=a.store_id and c.day_count=a.report_date and hq_sign='1')
			and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by tenancy_id,report_date,store_id,bill_num
			union all
			select tenancy_id,report_date,store_id,bill_num,'pos_bill_payment2' tablename,count(1) as gs
			from pos_bill_payment as a
			where not EXISTS ( select 1 from pos_bill_payment2 as b where a.store_id=b.store_id 
			and a.report_date=b.report_date
			and a.bill_num=b.bill_num
			)
			and EXISTS (select 1 from hq_daycount_info as c where c.store_id=a.store_id and c.day_count=a.report_date and hq_sign='1')
			and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by tenancy_id,report_date,store_id,bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,tablename,gs</column>
		<comment>商户号,门店号,账单号,报表日期,表名,数量</comment>
		<paramNumber>3</paramNumber>
	</condition>
	<condition id="024">
		<name>SAAS_DR_054</name>
		<description>账单开台时间大于结账时间</description>
		<sql>
			<![CDATA[
			select tenancy_id,report_date,store_id, bill_num from pos_bill
			where opentable_time > payment_time and tenancy_id = ? and store_id = ? and report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="025">
		<name>SAAS_DR_055</name>
		<description>账单结账时间小于付款表最小结账时间</description>
		<sql>
			<![CDATA[
			with t1 as (
			select bill_num,count(*),min(last_updatetime) as min_time from pos_bill_payment
			where tenancy_id = ? and store_id = ? and report_date = ?
			GROUP BY bill_num
			HAVING count(*) > 1
			) 
			select a.tenancy_id,a.report_date,a.store_id, a.bill_num from pos_bill a
			inner join t1 on a.bill_num = t1.bill_num
			where a.payment_time < t1.min_time and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="026">
		<name>SAAS_DR_056</name>
		<description>账单明细里的报表日期错误(原标题：账单项目小计与明细合计不一致)</description>
		<sql>
			<![CDATA[
			select a.tenancy_id,a.report_date,a.store_id,a.bill_num,b.report_date pb_report_date from pos_bill_item2 a
			left join pos_bill2 b on a.bill_num = b.bill_num
			where a.report_date <> b.report_date and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,pb_report_date</column>
		<comment>商户号,门店号,账单号,报表日期,pos_bill2报表日期</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="027">
		<name>SAAS_DR_057</name>
		<description>账单应收不等于项目小计加服务费</description>
		<sql>
			<![CDATA[
			select tenancy_id,report_date,bill_num,store_id,count(1) as gs from pos_bill2 as a
			where bill_amount <> subtotal + service_amount and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by tenancy_id,report_date,store_id,report_date,bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,gs</column>
		<comment>商户号,门店号,账单号,报表日期,数量</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="028">
		<name>SAAS_DR_058</name>
		<description>操作折扣后未打折商品也被标记为打折</description>
		<sql>select tenancy_id, report_date, store_id, bill_num,discount_mode_id, discount_amount, discountr_amount from pos_bill_item2 as a where (discount_mode_id is not null and discount_amount = 0 and discountr_amount = 0) and discount_state = 'Y' and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?</sql>
		<column>tenancy_id,store_id,bill_num,report_date,discount_mode_id,discount_amount,discountr_amount</column>
		<comment>商户号,门店号,账单号,报表日期,优惠方式ID,折扣金额,折让金额</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="029">
		<name>SAAS_DR_059</name>
		<description>部分账单人均消费算法有错误</description>
		<sql>
			<![CDATA[
			select tenancy_id,bill_num,report_date,store_id,count(*) as gs from pos_bill2
			where round(average_amount,2) <> round(payment_amount / guest ,2)
			and payment_amount > 0 
			and guest > 0
			and tenancy_id = ? and store_id = ? and report_date = ?
			group by tenancy_id,report_date,store_id,bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,gs</column>
		<comment>商户号,门店号,账单号,报表日期,数量</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="030">
		<name>SAAS_DR_060</name>
		<description>账单折扣不等于明细折扣合计</description>
		<sql>
			<![CDATA[
			with t1 as (
			select bill_num,sum(discount_amount) as discount_amount from pos_bill_item
			where item_property in ('MEALLIST','SINGLE') and tenancy_id = ? and store_id = ? and report_date = ?
			group by bill_num
			)
			select a.tenancy_id,a.report_date,a.store_id, a.bill_num from pos_bill a
			inner join t1 on a.bill_num = t1.bill_num
			where a.discountk_amount <> t1.discount_amount and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="031">
		<name>SAAS_DR_061</name>
		<description>账单折让不等于明细折让合计</description>
		<sql>
			<![CDATA[
			with t1 as (
			select bill_num,sum(discountr_amount) as discountr_amount from pos_bill_item2
			where item_property in ('MEALLIST','SINGLE') and tenancy_id = ? and store_id = ? and report_date = ?
			group by bill_num
			)
			select a.tenancy_id,a.report_date,a.store_id, a.bill_num 
			from pos_bill2 a
			inner join t1 on a.bill_num = t1.bill_num
			where a.discountr_amount <> t1.discountr_amount and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="032">
		<name>SAAS_DR_062</name>
		<description>账单抹零不等于明细抹零</description>
		<sql>
			<![CDATA[
			with t1 as (
			select bill_num,sum(single_discount_amount) as single_discount_amount from pos_bill_item
			where item_property in ('MEALLIST','SINGLE') and tenancy_id = ? and store_id = ? and report_date = ?
			group by bill_num
			)
			select a.tenancy_id,a.report_date,a.store_id, a.bill_num from pos_bill a
			inner join t1 on a.bill_num = t1.bill_num
			where a.maling_amount <> t1.single_discount_amount and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="033">
		<name>SAAS_DR_063</name>
		<description>套餐中明细存在缺失</description>
		<sql>
			<![CDATA[
			with t as (
			select a.tenancy_id, a.report_date,a.store_id,a.bill_num,count(*) num from pos_bill_item2 a 
			where a.item_property in ('SETMEAL' ,'MEALLIST' ) and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by a.report_date,a.store_id,a.item_property,a.bill_num,a.tenancy_id
			) 
			select tenancy_id,report_date,store_id,bill_num,sum(num)
			from t 
			group by report_date,store_id,bill_num,tenancy_id
			having sum(num) = 1
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,sum</column>
		<comment>商户号,门店号,账单号,报表日期,数量</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="034">
		<name>SAAS_DR_064</name>
		<description>套餐冲减单明细数量存在错误</description>
		<sql>
			<![CDATA[
			with t as (
			select report_date,store_id,bill_num,item_id,item_remark, item_count tc01 from pos_bill_item2 a 
			where item_property in ('SETMEAL','MEALLIST') and item_remark in ('TC01') and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			),
			t1 as (
			select report_date,store_id,bill_num,item_id,item_remark,item_count cj05 from pos_bill_item2 a 
			where item_property in ('SETMEAL','MEALLIST') and item_remark in ('CJ05') and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			)
			select 
			t.report_date,
			t.store_id,
			t.bill_num,
			sum(t.tc01) as tc01,
			sum(t1.cj05) as cj05
			from t inner join t1
			on t.bill_num = t1.bill_num and t.report_date = t1.report_date and t.store_id = t1.store_id 
			and t.item_id = t1.item_id 
			group by t.report_date,t.store_id,t.bill_num
			having ( sum(t.tc01) + sum(t1.cj05) ) <> 0 
			order by t.report_date desc
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,tc01,cj05</column>
		<comment>商户号,门店号,账单号,报表日期,套餐数量,冲减数量</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="035">
		<name>SAAS_DR_065</name>
		<description>套餐明细的算法不统一导致同一菜品多单价</description>
		<sql>
			<![CDATA[
			with t1 as (
			select bill_num,item_serial,sum(abs(item_amount)) as item_amount from pos_bill_item2
			where item_property = 'SETMEAL' and tenancy_id = ? and store_id = ? and report_date = ?
			group by bill_num,item_serial
			) , t2 as (
			select bill_num,item_serial,sum(abs(item_amount)) as item_amount from pos_bill_item2
			where item_property = 'MEALLIST' and tenancy_id = ? and store_id = ? and report_date = ?
			group by bill_num,item_serial
			) , t3 as (
			select bill_num,item_serial,sum(abs(real_amount)) as real_amount from pos_bill_item2
			where item_property = 'SETMEAL' and tenancy_id = ? and store_id = ? and report_date = ?
			group by bill_num,item_serial
			) , t4 as (
			select bill_num,item_serial,sum(abs(real_amount)) as real_amount from pos_bill_item2
			where item_property = 'MEALLIST' and tenancy_id = ? and store_id = ? and report_date = ?
			group by bill_num,item_serial
			) , t5 as (
			select distinct t2.bill_num from t2
			inner join t1 on t2.bill_num = t1.bill_num and t2.item_serial = t1.item_serial
			where t2.item_amount <> t1.item_amount
			union
			select distinct t4.bill_num from t4
			inner join t3 on t4.bill_num = t3.bill_num and t4.item_serial = t3.item_serial
			where t4.real_amount <> t3.real_amount
			)
			select a.tenancy_id,a.report_date,a.store_id, a.bill_num 
			from pos_bill2 a
			where exists (select 1 from t5 where a.bill_num = t5.bill_num) and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>5</paramNumber>
	</condition>
	<condition id="036">
		<name>SAAS_DR_100</name>
		<description>账单表班次不等于结账班次</description>
		<sql>
			<![CDATA[
			with t1 as (
			select a.tenancy_id,b.store_id,b.report_date,b.bill_num ,a.shift_id,b.shift_id
			from (
			select tenancy_id, store_id,report_date,bill_num ,shift_id from pos_bill where tenancy_id = ? and store_id = ? and report_date = ?
			) as a
			inner join
			( 
			select store_id,report_date,bill_num ,max(shift_id) as shift_id from pos_bill_payment where tenancy_id = ? and store_id = ? and report_date = ?
			 group by store_id,report_date,bill_num
			) as b 
			on a.bill_num=b.bill_num
			and a.report_date=b.report_date
			and a.store_id=b.store_id
			and a.shift_id<>b.shift_id
			)
			select tenancy_id,report_date,store_id, bill_num from t1 as a
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date</column>
		<comment>商户号,门店号,账单号,报表日期</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="037">
		<name>SAAS_DR_161</name>
		<description>零值账单存在多个0付款记录</description>
		<sql>select tenancy_id, store_id, report_date, bill_num, count(*) count from pos_bill_payment2 where currency_amount = 0 and tenancy_id = ? and store_id = ? and report_date = ? group by tenancy_id, store_id, report_date, bill_num HAVING count(*) > 1</sql>
		<column>tenancy_id,store_id,bill_num,report_date,count</column>
		<comment>商户号,门店号,账单号,报表日期,数量</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="038">
		<name>SAAS_DR_162</name>
		<description>服务员为空导致销售和提成数据异常</description>
		<sql>
			<![CDATA[
			select tenancy_id, store_id, report_date, bill_num, waiter_num from pos_bill_item2 where waiter_num is null and tenancy_id = ? and store_id = ? and report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,waiter_num </column>
		<comment>商户号,门店号,账单号,报表日期,服务员号</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="039">
		<name>SAAS_DR_163</name>
		<description>单品退菜后冲减单税额信息有误</description>
		<sql>
			<![CDATA[
			with t1 as (
			select a.tenancy_id,a.store_id,a.report_date,a.bill_num,a.item_id,sum(a.tax_rate) tax_rate,sum(a.tax_money) tax_money,sum(a.item_notax) item_notax,sum(a.payment_tax_money) payment_tax_money,sum(a.payment_notax) payment_notax
			from pos_bill_item2 a
			where exists (select 1 from pos_bill2 b where a.bill_num = b.bill_num and b.bill_state = 'DPQX03')
			and a.item_remark = 'TC01' and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by a.tenancy_id,a.store_id,a.report_date,a.bill_num,a.item_id
			) , t2 as (
			select a.tenancy_id,a.store_id,a.report_date,a.bill_num,b.copy_bill_num,a.item_id,sum(a.tax_rate) tax_rate,sum(a.tax_money) tax_money,sum(a.item_notax) item_notax,sum(a.payment_tax_money) payment_tax_money,sum(a.payment_notax) payment_notax
			from pos_bill_item2 a join pos_bill2 b on a.bill_num = b.bill_num
			where b.copy_bill_num is not null
			and a.item_remark = 'CJ05' and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			group by a.tenancy_id,a.store_id,a.report_date,a.bill_num,b.copy_bill_num,a.item_id
			)
			select t1.tenancy_id,t1.store_id,t1.report_date,t1.bill_num,t1.item_id,t1.tax_rate,t2.tax_rate cj_tax_rate,t1.tax_money,t2.tax_money cj_tax_money,t1.item_notax,t2.item_notax cj_item_notax,t1.payment_tax_money,t2.payment_tax_money cj_payment_tax_money,t1.payment_notax,t2.payment_notax cj_payment_notax
			from t1 join t2 on t1.store_id = t2.store_id and t1.report_date = t2.report_date and t1.bill_num = t2.copy_bill_num and t1.item_id = t2.item_id
			where t1.tax_rate <> -t2.tax_rate
			or t1.tax_money <> -t2.tax_money
			or t1.item_notax <> -t2.item_notax
			or t1.payment_tax_money <> -t2.payment_tax_money
			or t1.payment_notax <> -t2.payment_notax
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,item_id,tax_rate,cj_tax_rate,tax_money,cj_tax_money,item_notax,cj_item_notax,payment_tax_money,cj_payment_tax_money,payment_notax,cj_payment_notax</column>
		<comment>商户号,门店号,账单号,报表日期,菜目ID,总税率值,pos_bill_item2_总税率值,菜品应收(不含税),pos_bill_item2_菜品应收(不含税),菜品应收(含税),pos_bill_item2_菜品应收(含税),菜品实收(不含税),pos_bill_item2_菜品实收(不含税),菜品实收(含税),pos_bill_item2_菜品实收(含税)</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="040">
		<name>SAAS_DR_165</name>
		<description>退菜及取消账单缺少批准人信息</description>
		<sql>
			<![CDATA[
			select tenancy_id, store_id, report_date, bill_num, manager_num from pos_bill_item2 
			where item_remark in ('TC01','CJ05') and COALESCE(manager_num,'')='' and tenancy_id = ? and store_id = ? and report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,manager_num</column>
		<comment>商户号,门店号,账单号,报表日期,批准人</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="041">
		<name>SAAS_DR_167</name>
		<description>套菜主项金额与明细金额合计不等</description>
		<sql>
			<![CDATA[
			with t1 as (
			select bill_num,COALESCE(item_serial,-1) as item_serial,setmeal_id,sum(item_amount) item_amount from pos_bill_item2 a 
			where exists (select 1 from pos_bill_item2 b left join hq_item_info hi on b.item_id=hi.id 
			where item_property='SETMEAL' and item_count > 1 and a.bill_num=b.bill_num and hi.is_assemble_combo<>'1') 
			and item_property='MEALLIST'
			and COALESCE(item_remark,'') not in('TC01','CJ05') 
			and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			GROUP BY bill_num,batch_num,item_serial,setmeal_id
			), t2 as (
			select a.tenancy_id,a.report_date,a.store_id,a.bill_num,COALESCE(item_serial,-1) as item_serial,item_id, item_amount from pos_bill_item2 a
			left join hq_item_info hi on a.item_id=hi.id 
			where item_property='SETMEAL' and item_count > 1 and hi.is_assemble_combo<>'1' and a.tenancy_id = ? and a.store_id = ? and a.report_date = ?
			and COALESCE(item_remark,'') not in('TC01','CJ05') 
			) 
			select tenancy_id,report_date,store_id,t1.bill_num,t2.item_amount as item_amount_m,t1.item_amount as item_amount_s from t1,t2 
			where t1.bill_num = t2.bill_num
			and t1.item_serial = t2.item_serial
			and t1.setmeal_id = t2.item_id
			and t1.item_amount <> t2.item_amount
			ORDER BY t1.bill_num
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,item_amount_m,item_amount_s</column>
		<comment>商户号,门店号,账单号,报表日期,明细金额,主项金额</comment>
		<paramNumber>2</paramNumber>
	</condition>
	<condition id="042">
		<name>SAAS_DR_168</name>
		<description>账单中折扣+折让不等于优惠</description>
		<sql>
			<![CDATA[
			select tenancy_id, store_id, report_date, bill_num, discount_amount, discountr_amount, discountk_amount 
			from pos_bill2 
			where coalesce(discount_amount,0) <> coalesce(discountr_amount+discountk_amount,0) and tenancy_id = ? and store_id = ? and report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,discount_amount,discountr_amount,discountk_amount</column>
		<comment>商户号,门店号,账单号,报表日期,优惠金额,折让金额,折扣金额</comment>
		<paramNumber>1</paramNumber>
	</condition>
	<condition id="043">
		<name>SAAS_DR_169</name>
		<description>账单公式不平</description>
		<sql>
			<![CDATA[
			select tenancy_id,store_id,report_date,bill_num,bill_amount,payment_amount,discount_amount,maling_amount,givi_amount,more_coupon
			from pos_bill2 
			where bill_amount <> payment_amount + discount_amount - maling_amount + givi_amount - more_coupon and tenancy_id = ? and store_id = ? and report_date = ?
			]]>
		</sql>
		<column>tenancy_id,store_id,bill_num,report_date,bill_amount,payment_amount,discount_amount,maling_amount,givi_amount,more_coupon</column>
		<comment>商户号,门店号,账单号,报表日期,账单金额,付款金额,优惠金额,抹零金额,奉送金额,多收礼券</comment>
		<paramNumber>1</paramNumber>
	</condition>
</conditions>
