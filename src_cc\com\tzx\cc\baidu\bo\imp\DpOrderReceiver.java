package com.tzx.cc.baidu.bo.imp;

import com.tzx.cc.baidu.util.Constant;

import net.sf.json.JSONObject;

public class DpOrderReceiver extends ThirdPartyOrderReceiver {

	public DpOrderReceiver(){
		super(Constant.DIANPING_CHANNEL);
	}
	
	public DpOrderReceiver(JSONObject order) {
		
		super(order, Constant.DIANPING_CHANNEL);
		
		String[] shopIds=thirdPartyOrder.optString("shopid").split("@");
		this.storeId=shopIds[0];
		this.tenantId=shopIds[1];
			
	}

	@Override
	public JSONObject saveOrderList() throws Exception {
		// TODO Auto-generated method stub
        return null;
	}

	@Override
	public void saveOrderItem() throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public void saveOrderRepayment() throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public void saveOrderDiscount() throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public JSONObject orderStatusPush(JSONObject params)
	{
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	JSONObject thirdPartyResponse()
	{
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public JSONObject orderStatusGet(JSONObject params)
	{
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected void generateOrderCode()
	{
		// TODO Auto-generated method stub
		
	}


}
