package com.tzx.report.service.rest.boh;
/**
 * Created by gj on 2019-05-30.
 */

import com.tzx.framework.bo.dto.Roles;
import com.tzx.report.bo.boh.ConcessionAnalysisDetailsService;
import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.common.util.ConditionUtils;
import jxl.write.WriteException;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

/**
 *
 * 让利分析明细表
 *
 */

@Controller("ConcessionAnalysisDetailsRest")
@RequestMapping("/report/ConcessionAnalysisDetailsRest")
public class ConcessionAnalysisDetailsRest
{

    @Resource(name = ConcessionAnalysisDetailsService.NAME)
    private ConcessionAnalysisDetailsService concessionAnalysisDetailsService;

    @Resource
    ConditionUtils conditionUtils;

    @Resource
    private CommonMethodAreaService commonMethodAreaService;


    @RequestMapping(value = "/getConcessionTitle")
    public void getConcessionTitle(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        InputStream in = null;
        HttpSession session = request.getSession();
        String result = "";
        try
        {
            JSONObject p = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                p.put(key, map.get(key)[0]);
            }

            if(p.optString("p_store_id").length()==0){
                p.element("p_store_id", session.getAttribute("user_organ_codes_group"));
            }

            p.element("p_day","");
            String employeeId = (String)session.getAttribute("employeeId");
            String tenancyId = (String) session.getAttribute("tenentid");
            Roles roles = this.commonMethodAreaService.getUserRoles(tenancyId,employeeId);
            if(roles!=null){
                int dayType = roles.getReportDayType();
                if(1 == dayType){
                    int day = roles.getReportDay();
                    if(day>0){
                        p.element("p_day",day);
                    }
                }
            }

            result = concessionAnalysisDetailsService.getConcessionTitle((String) session.getAttribute("tenentid"), p).toString();
        }
        catch (Exception e)
        {
            result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
            e.printStackTrace();
        }
        finally
        {
            try
            {
                if (in != null)
                {
                    in.close();
                }
            }
            catch (Exception e)
            {
            }

            try
            {
                out = response.getWriter();

                out.print(result);
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }

    }



    @RequestMapping(value = "/find")
    public void find(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        InputStream in = null;
        HttpSession session = request.getSession();
        String result = "";
        try
        {
            JSONObject p = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                p.put(key, map.get(key)[0]);
            }

            if(p.optString("store_id").length()==0){
                p.element("store_id", session.getAttribute("user_organ_codes_group"));
            }

            String employeeId = (String)session.getAttribute("employeeId");
            String tenancyId = (String)session.getAttribute("tenentid");
            p.element("ss_temp_employeeId", employeeId);
            p.element("ss_temp_tenentid", tenancyId);

            result = concessionAnalysisDetailsService.find((String) session.getAttribute("tenentid"), p).toString();
        }
        catch (Exception e)
        {
            result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
            e.printStackTrace();
        }
        finally
        {
            try
            {
                if (in != null)
                {
                    in.close();
                }
            }
            catch (Exception e)
            {
            }

            try
            {
                out = response.getWriter();

                out.print(result);
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }

    }
}
