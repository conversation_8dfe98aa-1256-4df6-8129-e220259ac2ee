package com.tzx.cc.datasync.bo.dto.newpos;

import java.util.List;

/**
 * BtYddSendDto
 */
public class BtYddSendDto
{


	private String						beforeOrderType;
	private String						beforeOrderSource;

	private String						storeid;
	private String						shopid;
	private String						merchant_id;
	private String						order_id;
	private String						totalprice;
	private String						contact;
	private String						mobile;
	private String						people;
	private String						member_address1;
	private String						memo;
	private String						kwxh;
	private String						invoice;
	private String						ddzt;
	private String						tableinfo_id;
	private String						warning;
	private String						cancel_type;
	private String						paychannel;
	private String						paystatus;
	private String						shippingmethod;
	private String						qdrbh;
	private String						shrbh;
	private String						qdrbh1;
	private String						syrbh;
	private String						pdrbh;
	private String						sbsqrbh;
	private String						xsqxrbh;
	private String						qdrmc;
	private String						shrmc;
	private String						qdrmc1;
	private String						syrmc;
	private String						pdrmc;
	private String						sbsqrmc;
	private String						xsqxrmc;
	private String						qdsj;
	private String						yshsj;
	private String						qdsj1;
	private String						sysj;
	private String						pdsj;
	private String						wcsj;
	private String						sbsqsj;
	private String						xsqxsj;
	private String						blrq;
	private String						ydrq;
	private String						yl5;
	private String						yl4;
	private String						yl3;
	private String						yl2;
	private String						yl1;
	private String						khtmtz;
	private String						khjlbh;
	private String						khjlmc;
	private String  	jk;
	private String  	recieptdept;
	private String  	ydrs;
	private String						commission_amount;
	private String						shop_real_amount;
	
	
	public String getYl1()
	{
		return yl1;
	}

	public void setYl1(String yl1)
	{
		this.yl1 = yl1;
	}

	private String						ddsj;
	private String						createUserName;

	private List<BtYddDetailSendDto>	items;
	private List<BtYddDetailTcmxDto>	items_tcmx;
	private List<?>	payments;
	
	private List<?>	discount;

	public String getStoreid()
	{
		return storeid;
	}

	public void setStoreid(String storeid)
	{
		this.storeid = storeid;
	}

	public String getShopid()
	{
		return shopid;
	}

	public void setShopid(String shopid)
	{
		this.shopid = shopid;
	}
	
	public String getYl4()
	{
		return yl4;
	}

	public void setYl4(String yl4)
	{
		this.yl4 = yl4;
	}

	public String getMerchant_id()
	{
		return merchant_id;
	}

	public void setMerchant_id(String merchant_id)
	{
		this.merchant_id = merchant_id;
	}

	public String getOrder_id()
	{
		return order_id;
	}

	public void setOrder_id(String order_id)
	{
		this.order_id = order_id;
	}

	public String getTotalprice()
	{
		return totalprice;
	}

	public void setTotalprice(String totalprice)
	{
		this.totalprice = totalprice;
	}

	public String getContact()
	{
		return contact;
	}

	public void setContact(String contact)
	{
		this.contact = contact;
	}

	public String getMobile()
	{
		return mobile;
	}

	public void setMobile(String mobile)
	{
		this.mobile = mobile;
	}

	public String getPeople()
	{
		return people;
	}

	public void setPeople(String people)
	{
		this.people = people;
	}

	public String getMember_address1()
	{
		return member_address1;
	}

	public void setMember_address1(String member_address1)
	{
		this.member_address1 = member_address1;
	}

	public String getMemo()
	{
		return memo;
	}

	public void setMemo(String memo)
	{
		this.memo = memo;
	}

	public String getInvoice()
	{
		return invoice;
	}

	public void setInvoice(String invoice)
	{
		this.invoice = invoice;
	}

	public String getDdzt()
	{
		return ddzt;
	}

	public void setDdzt(String ddzt)
	{
		this.ddzt = ddzt;
	}

	public String getTableinfo_id()
	{
		return tableinfo_id;
	}

	public void setTableinfo_id(String tableinfo_id)
	{
		this.tableinfo_id = tableinfo_id;
	}

	public String getWarning()
	{
		return warning;
	}

	public void setWarning(String warning)
	{
		this.warning = warning;
	}

	public String getCancel_type()
	{
		return cancel_type;
	}

	public void setCancel_type(String cancel_type)
	{
		this.cancel_type = cancel_type;
	}

	public String getPaystatus()
	{
		return paystatus;
	}

	public void setPaystatus(String paystatus)
	{
		this.paystatus = paystatus;
	}

	public String getShippingmethod()
	{
		return shippingmethod;
	}

	public void setShippingmethod(String shippingmethod)
	{
		this.shippingmethod = shippingmethod;
	}

	public String getQdrbh()
	{
		return qdrbh;
	}

	public void setQdrbh(String qdrbh)
	{
		this.qdrbh = qdrbh;
	}

	public String getShrbh()
	{
		return shrbh;
	}

	public void setShrbh(String shrbh)
	{
		this.shrbh = shrbh;
	}

	public String getQdrbh1()
	{
		return qdrbh1;
	}

	public void setQdrbh1(String qdrbh1)
	{
		this.qdrbh1 = qdrbh1;
	}

	public String getSyrbh()
	{
		return syrbh;
	}

	public void setSyrbh(String syrbh)
	{
		this.syrbh = syrbh;
	}

	public String getPdrbh()
	{
		return pdrbh;
	}

	public void setPdrbh(String pdrbh)
	{
		this.pdrbh = pdrbh;
	}

	public String getSbsqrbh()
	{
		return sbsqrbh;
	}

	public void setSbsqrbh(String sbsqrbh)
	{
		this.sbsqrbh = sbsqrbh;
	}

	public String getXsqxrbh()
	{
		return xsqxrbh;
	}

	public void setXsqxrbh(String xsqxrbh)
	{
		this.xsqxrbh = xsqxrbh;
	}

	public String getQdrmc()
	{
		return qdrmc;
	}

	public void setQdrmc(String qdrmc)
	{
		this.qdrmc = qdrmc;
	}

	public String getShrmc()
	{
		return shrmc;
	}

	public void setShrmc(String shrmc)
	{
		this.shrmc = shrmc;
	}

	public String getQdrmc1()
	{
		return qdrmc1;
	}

	public void setQdrmc1(String qdrmc1)
	{
		this.qdrmc1 = qdrmc1;
	}

	public String getSyrmc()
	{
		return syrmc;
	}

	public void setSyrmc(String syrmc)
	{
		this.syrmc = syrmc;
	}

	public String getPdrmc()
	{
		return pdrmc;
	}

	public void setPdrmc(String pdrmc)
	{
		this.pdrmc = pdrmc;
	}

	public String getSbsqrmc()
	{
		return sbsqrmc;
	}

	public void setSbsqrmc(String sbsqrmc)
	{
		this.sbsqrmc = sbsqrmc;
	}

	public String getXsqxrmc()
	{
		return xsqxrmc;
	}

	public void setXsqxrmc(String xsqxrmc)
	{
		this.xsqxrmc = xsqxrmc;
	}

	public String getQdsj()
	{
		return qdsj;
	}

	public void setQdsj(String qdsj)
	{
		this.qdsj = qdsj;
	}

	public String getYshsj()
	{
		return yshsj;
	}

	public void setYshsj(String yshsj)
	{
		this.yshsj = yshsj;
	}

	public String getQdsj1()
	{
		return qdsj1;
	}

	public void setQdsj1(String qdsj1)
	{
		this.qdsj1 = qdsj1;
	}

	public String getSysj()
	{
		return sysj;
	}

	public void setSysj(String sysj)
	{
		this.sysj = sysj;
	}

	public String getPdsj()
	{
		return pdsj;
	}

	public void setPdsj(String pdsj)
	{
		this.pdsj = pdsj;
	}

	public String getWcsj()
	{
		return wcsj;
	}

	public void setWcsj(String wcsj)
	{
		this.wcsj = wcsj;
	}

	public String getSbsqsj()
	{
		return sbsqsj;
	}

	public void setSbsqsj(String sbsqsj)
	{
		this.sbsqsj = sbsqsj;
	}

	public String getXsqxsj()
	{
		return xsqxsj;
	}

	public void setXsqxsj(String xsqxsj)
	{
		this.xsqxsj = xsqxsj;
	}

	public String getBlrq()
	{
		return blrq;
	}

	public void setBlrq(String blrq)
	{
		this.blrq = blrq;
	}

	public String getYdrq()
	{
		return ydrq;
	}

	public void setYdrq(String ydrq)
	{
		this.ydrq = ydrq;
	}

	public String getDdsj()
	{
		return ddsj;
	}

	public void setDdsj(String ddsj)
	{
		this.ddsj = ddsj;
	}

	public List<BtYddDetailSendDto> getItems()
	{
		return items;
	}

	public void setItems(List<BtYddDetailSendDto> items)
	{
		this.items = items;
	}

	public String getBeforeOrderType()
	{
		return beforeOrderType;
	}

	public void setBeforeOrderType(String beforeOrderType)
	{
		this.beforeOrderType = beforeOrderType;
	}

	public String getBeforeOrderSource()
	{
		return beforeOrderSource;
	}

	public void setBeforeOrderSource(String beforeOrderSource)
	{
		this.beforeOrderSource = beforeOrderSource;
	}


	public String getKwxh()
	{
		return kwxh;
	}

	public void setKwxh(String kwxh)
	{
		this.kwxh = kwxh;
	}

	public String getPaychannel()
	{
		return paychannel;
	}

	public void setPaychannel(String paychannel)
	{
		this.paychannel = paychannel;
	}

	public String getCreateUserName()
	{
		return createUserName;
	}

	public void setCreateUserName(String createUserName)
	{
		this.createUserName = createUserName;
	}

	public String getYl5()
	{
		return yl5;
	}

	public void setYl5(String yl5)
	{
		this.yl5 = yl5;
	}


	public String getYl3()
	{
		return yl3;
	}

	public void setYl3(String yl3)
	{
		this.yl3 = yl3;
	}

	public List<?> getPayments()
	{
		return payments;
	}

	public void setPayments(List<?> payments)
	{
		this.payments = payments;
	}

	public String getYl2()
	{
		return yl2;
	}

	public void setYl2(String yl2)
	{
		this.yl2 = yl2;
	}

	public String getKhtmtz()
	{
		return khtmtz;
	}

	public void setKhtmtz(String khtmtz)
	{
		this.khtmtz = khtmtz;
	}

	public String getKhjlbh()
	{
		return khjlbh;
	}

	public void setKhjlbh(String khjlbh)
	{
		this.khjlbh = khjlbh;
	}

	public String getKhjlmc()
	{
		return khjlmc;
	}

	public void setKhjlmc(String khjlmc)
	{
		this.khjlmc = khjlmc;
	}

	public String getJk()
	{
		return jk;
	}

	public void setJk(String jk)
	{
		this.jk = jk;
	}

	public String getRecieptdept()
	{
		return recieptdept;
	}

	public void setRecieptdept(String recieptdept)
	{
		this.recieptdept = recieptdept;
	}

	public List<?> getDiscount() {
		return discount;
	}

	public void setDiscount(List<?> discount) {
		this.discount = discount;
	}

	public String getYdrs() {
		return ydrs;
	}

	public void setYdrs(String ydrs) {
		this.ydrs = ydrs;
	}

	public List<BtYddDetailTcmxDto> getItems_tcmx() {
		return items_tcmx;
	}

	public void setItems_tcmx(List<BtYddDetailTcmxDto> items_tcmx) {
		this.items_tcmx = items_tcmx;
	}

	public String getCommission_amount() {
		return commission_amount;
	}

	public void setCommission_amount(String commission_amount) {
		this.commission_amount = commission_amount;
	}

	public String getShop_real_amount() {
		return shop_real_amount;
	}

	public void setShop_real_amount(String shop_real_amount) {
		this.shop_real_amount = shop_real_amount;
	}
   
	
} 
