package com.tzx.report.po.boh.dao;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */
public interface ConcessionAnalysisDetailsDao {

    String NAME = "com.tzx.report.po.boh.impl.ConcessionAnalysisDetailsDaoImpl";

    JSONObject find(String tenancyID, JSONObject condition) throws Exception;

    List<JSONObject> getConcessionTitle(String tenancyID, JSONObject condition) throws Exception;
}
