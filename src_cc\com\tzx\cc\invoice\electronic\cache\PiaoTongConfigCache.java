package com.tzx.cc.invoice.electronic.cache;

import java.io.File;

import org.apache.log4j.Logger;
import org.dom4j.DocumentException;
import org.springframework.core.io.Resource;

import com.tzx.framework.common.util.SpringConext;
import com.tzx.payment.news.util.DomUtils;

import net.sf.json.JSONObject;

/**
 * 票通电子发票接口配置
 * <AUTHOR>
 *
 */
public class PiaoTongConfigCache {
	private static final Logger	logger	= Logger.getLogger(PiaoTongConfigCache.class);

	private static JSONObject cache;
	
	static {
		try {
			Resource resource = SpringConext.getApplicationContext().getResource("classpath:/invoice/electronic/certificate/piaotong_config.xml");
			
			File file = resource.getFile();
//			String url = "E:\\ideaworkspace\\taxsaas20170520\\resource\\invoice\\electronic\\certificate\\piaotong_config.xml";
//			File file = new File(url);
			cache = DomUtils.load(file);
		} catch (DocumentException e) {
			logger.error(e);
			e.printStackTrace();
		} catch (Exception e) {
			logger.error(e);
			e.printStackTrace();
		}
	}

	/**
	 * 获取票通配置
	 * @param keys
	 * @return
	 */
	public static String getElementText(String ... keys){
		JSONObject json = cache;
		for(int i=0;i<keys.length;i++){
			if(i==keys.length-1) {
				return json.optString(keys[i]);
			} else {
				json = (JSONObject) json.optJSONObject(keys[i]);
			}
		}
		return null;
	}
	
}
