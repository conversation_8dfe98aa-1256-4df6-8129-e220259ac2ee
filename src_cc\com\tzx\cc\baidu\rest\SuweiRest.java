package com.tzx.cc.baidu.rest;

import java.io.PrintWriter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.tzx.cc.baidu.cont.SuweiConst;
import com.tzx.cc.baidu.service.SuweiService;
import com.tzx.cc.baidu.util.SuweiPropertyUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.controller.BaseController;

/**
 * <AUTHOR>
 *
 */
@Controller("SuweiRest")
@RequestMapping("/suweirest/suwei")
public class SuweiRest extends BaseController
{
	private static final Logger	logger	= Logger.getLogger(SuweiRest.class);

	@Resource(name = SuweiService.NAME)
	private SuweiService suweiService;
	
	/**
	 * 请求入口
	 * 
	 * @return JSONObject
	 * @throws Exception 
	 */
	@RequestMapping(value = "post", method = RequestMethod.POST)
	// @ResponseBody
	public void post(HttpServletRequest request, HttpServletResponse response, @RequestBody
	JSONObject jsobj) throws Exception
	{
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		JSONObject responseJson = new JSONObject();
		
		String type = jsobj.optString("type");
		try {
			if(StringUtils.equals(type, SuweiConst.THIRD_SUWEI_APPLAY_ORDER)) {
				String tenantId = jsobj.optString("tenant_id");
				String store_id = jsobj.optString("store_id");
				DBContextHolder.setTenancyid(tenantId);
				JSONObject suweiStoreConfig = suweiService.getSuweiStoreConfig(tenantId, store_id);
				if(suweiStoreConfig==null) {
					logger.info("机构id"+store_id+"速位取餐未开通");
					return;
				}
				if(StringUtils.isBlank(suweiStoreConfig.optString("suwei_organ_id"))) {
					logger.info("机构id"+store_id+"速位餐厅ID未配置");
					return;
				}
				if(!StringUtils.equals(suweiStoreConfig.optString("is_open_suwei"),"1")) {
					logger.info("机构id"+store_id+"速位取餐未开通");
					return;
				}
				JSONObject thirdSuweiApplayOrder = suweiService.thirdSuweiApplayOrder(jsobj,responseJson);
				String result = suweiService.thirdSuweiApplayOrderReq(jsobj,thirdSuweiApplayOrder);
				suweiService.thirdSuweiApplayOrderAfter(jsobj,responseJson,result);
			}
		} catch (Exception e1) {
			responseJson.put(SuweiConst.ERR_CODE, SuweiConst.ERROR_SAAS_INNER);
			responseJson.put(SuweiConst.ERR_DESC, SuweiPropertyUtil.getMsg(String.valueOf(SuweiConst.ERROR_SAAS_INNER)));
			logger.error(e1);
		}
		
		try
		{
			out = response.getWriter();
			out.print(responseJson.toString());
			out.flush();
			out.close();
		}
		catch (Exception e)
		{
			logger.error(e);
		}
		finally
		{
			if (out != null) out.close();
		}
	}
}
