package com.tzx.cc.bo;

import java.util.List;

import com.tzx.framework.common.exception.SystemException;

import net.sf.json.JSONObject;

public interface PlaceOrderManagementService
{
	String	NAME = "com.tzx.cc.bo.imp.PlaceOrderManagementImpl";
	
	public JSONObject loadCustomerAddress(String tenancyID,JSONObject condition) throws Exception;
	
	public JSONObject findFoodCategory(String tenancyID,JSONObject condition) throws Exception;
	
	public JSONObject foodDetailList(String tenancyID,JSONObject condition) throws Exception;
	//
	public JSONObject loadRegionInfo(String tenancyID,JSONObject condition) throws Exception;
	//
	public JSONObject loadDistrictInfo(String tenancyID,JSONObject condition) throws Exception;
	
	public JSONObject loadDistrictByCity(String tenancyID,JSONObject condition) throws Exception;
	
	public JSONObject loadMealsInfo(String tenancyId, JSONObject param) throws Exception;
	
	public Boolean saveOrderConfirm(String tenancyID, JSONObject obj) throws Exception,SystemException;
	
	public  boolean hqOrderIssued(String tenancyId, JSONObject param) throws Exception;
	//
	public JSONObject loadOorderItem(String tenancyId, JSONObject param) throws Exception;
	//
	public JSONObject loadOrderItemDetails(String tenancyId, JSONObject param) throws Exception;
	//
	public JSONObject loadOrderRepayment(String tenancyId, JSONObject param) throws Exception;
	//
	public JSONObject loadOrderListNew(String tenancyId, JSONObject param) throws Exception;
	//根据顾客电话查询是否是会员
	public String loadCustomerByPhone(String tenancyId, JSONObject condition) throws Exception;
	
	public JSONObject loadItemTaste(String tenancyId, JSONObject param) throws Exception;
	
	public JSONObject loadItemMethod(String tenancyId, JSONObject param) throws Exception;
	
	public JSONObject loadServiceFeeByStoreId(String tenancyID,JSONObject condition) throws Exception;
	
	public boolean checkIsStopOrder(String tenentId, JSONObject param)throws Exception;
	
	public boolean checkIsOrderTimeRange(String tenentId, JSONObject param)throws Exception;
	
	public boolean checkIsOpen(String tenentId, JSONObject param)throws Exception;
	
	
	public boolean checkIsStop(String tenentId, JSONObject param)throws Exception;
	
	public boolean checkTenantState(String tenentId, JSONObject param)throws Exception;
	
	public boolean checkStoreState(String tenentId, JSONObject param)throws Exception;
	
	public boolean checkDeviceAuditState(String tenentId, JSONObject param)throws Exception;
	
	public boolean checkIsHaveAddress(String tenentId, JSONObject param)throws Exception;
	
	public JSONObject getSendTimeData(String tenancyId, JSONObject param) throws Exception;
	
	public JSONObject loadMealsInfoDefaultById(String tenancyID,JSONObject condition) throws Exception;
	
	public boolean checkIsHaveDistrictAddress(String tenentId, JSONObject param)throws Exception;
	
	public JSONObject loadStoreByDistrict(String tenancyID,JSONObject condition) throws Exception;
	
	//订单信息保存到总部
    public void  hqOrderSave(String tenancyId, JSONObject param)throws Exception;

}
