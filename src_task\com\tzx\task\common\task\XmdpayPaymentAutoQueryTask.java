  package com.tzx.task.common.task;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.news.cont.Contant;
import com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl;
import com.tzx.payment.news.service.impl.XmdPaymentServiceImpl;
import com.tzx.payment.news.util.PaymentRedisCache;
import com.tzx.payment.news.util.PaymentUtils;
import com.tzx.payment.news.util.ThreadPool;
import com.tzx.payment.news.util.xmd.PaymentUtil;
import com.tzx.payment.news.util.xmd.XmdConstant;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 新美大定时查询任务
 */
public class XmdpayPaymentAutoQueryTask implements Runnable {
    private static final Logger log = Logger.getLogger(XmdpayPaymentAutoQueryTask.class);

    @Resource(name = "com.tzx.payment.news.service.impl.XmdPaymentServiceImpl")
    private XmdPaymentServiceImpl xmdPaymentService;

    @Resource(name = "com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl")
    private AlipayPaymentDaoImpl paymentDao;


    @Override
    public void run() {
        try {
        	log.info("================新美大支付定时任务开启：=========");
            autoQuery();
        } catch (Exception e) {
            log.error(e);
        }
    }

    public void autoQuery() {
        ThreadPoolExecutor threadPool = ThreadPool.getThreadPool();
        List<JSONObject> xmd_pay = PaymentRedisCache.getOrderInfoByType(Contant.PAY_TYPE_XMDPAY);
        if (xmd_pay == null) {
            return;
        }

        for (JSONObject json : xmd_pay) {
            if (isExecute(json)) {
                log.info("新美大支付 - 查询任务 - 开启新线程（out_trade_no为" + json.optString("out_trade_no") + "）");
                final JSONObject json2 = json;
                try {
                    Runnable thread = new Runnable() {
                        @Override
                        public void run() {
                            try {
                                JSONObject jsonObject = queryDataJson(json2);

                                JSONObject xmdResponse = sendRequest(XmdConstant.URL_PAY_QUERY, jsonObject);
                                if (xmdResponse != null) {
                                    process(xmdResponse, jsonObject);
                                    xmdPaymentService.send2Md(jsonObject);
                                }
                            } catch (Exception e) {
                                log.error(e);
                            }
                        }
                    };
                    threadPool.execute(thread);

                } catch (Exception e) {
                    String out_trade_info = PaymentRedisCache.getInfoByOutTradeNo(json.optString("out_trade_no"));
                    PaymentRedisCache.lPushOutTradeNo2Info(json.optString("pay_type"), out_trade_info);
                    log.error(e);
                    continue;
                }
            }else {
                String out_trade_info = PaymentRedisCache.getInfoByOutTradeNo(json.optString("out_trade_no"));
                PaymentRedisCache.lPushOutTradeNo2Info(json.optString("pay_type"), out_trade_info);
            }
        }
    }

    /**
     * 处理返回信息并保存
     *
     * @param xmdResponse
     * @param json
     * @throws Exception
     */
    private void process(JSONObject xmdResponse, JSONObject json) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", json.optInt("id"));
         if (XmdConstant.PUBLIC_RETURN_STATUS_SUCCESS.equals(xmdResponse.optString("status"))) {
        	
            jsonObject.put("last_updatetime", PaymentUtils.currentTime2Str());
            
            int status = PaymentUtil.getPaymentState(xmdResponse.optString("orderStatus"));
            jsonObject.put("status", status);
            jsonObject.put("final_state", status);


            jsonObject.put("trade_no", xmdResponse.optString("tradeNo"));
            jsonObject.put("out_trade_no", xmdResponse.optString("outTradeNo"));
            double totalAmount = PaymentUtil.fen2yuan(xmdResponse.optLong("totalFee"));
            jsonObject.put("total_amount", totalAmount);
//			jsonObject.put("buyer_user_id", xmdResponse.optString("buyerId"));
//			jsonObject.put("pay_time", xmdResponse.optString("payTime"));
//			jsonObject.put("referno", xmdResponse.optString("referno"));

            String tenancyId = json.optString("tenancy_id");
            paymentDao.updateIgnorCase(tenancyId, "pos_payment_order", jsonObject);

            xmdPaymentService.addAssist(json, jsonObject);
            String out_trade_no = json.optString("out_trade_no");
            if(XmdConstant.ORDER_STATUS_ORDER_NEW.equals(xmdResponse.optString("orderStatus"))){
            }else{
            PaymentRedisCache.deleteInfoByOutTradeNo(out_trade_no);
            }
        }
    }

    /**
     * redis中查询商户信息
     * pos_payment_order中查询订单信息
     *
     * @param json
     * @return
     * @throws Exception
     */
    private JSONObject queryDataJson(JSONObject json) throws Exception {
        String out_trade_no = json.optString("out_trade_no");
        String tenancy_id = json.optString("tenancy_id");
        if (StringUtils.isBlank(tenancy_id)) {
            log.info("新美大支付 - 查询任务 - redis中不存在out_trade_no为" + out_trade_no + "的信息");
            return null;
        }
        log.info("新美大查询数据库请求参数为tenancy_id："+tenancy_id+"==="+"out_trade_no："+out_trade_no);
        DBContextHolder.setTenancyid(tenancy_id);
        return paymentDao.queryOrderByOutTradeNo2Recently(tenancy_id, out_trade_no);
    }

    /**
     * 请求新美大接口
     *
     * @param json
     * @return
     * @throws Exception
     */
    private JSONObject sendRequest(String api, JSONObject json) throws Exception {
        String out_trade_no = json.optString("out_trade_no");
        String tenancy_id = json.optString("tenancy_id");
        int store_id = json.optInt("store_id");
        String type=null;
        if(json.optInt("type")==1){
        	type=Contant.PAY_TYPE_WECHATPAY;
        }else if(json.optInt("type")==0){
        	type=Contant.PAY_TYPE_ALIPAY;
        }
        JSONObject paymentAccountConfig = paymentDao.getPaymentAccountConfig(tenancy_id, store_id
                , type, json.optString("service_type"));
        if (paymentAccountConfig == null) {
            log.error("没有机构所对应的 秘钥、 商户号信息,机构ID为" + store_id + "商户id为：" + tenancy_id);
            return null;
        }

        JSONObject xmdParmas = new JSONObject();
        xmdParmas.put("outTradeNo", out_trade_no);
        xmdParmas.put("merchantId", paymentAccountConfig.optString("merchant_id"));
        xmdParmas.put("appId", Long.parseLong(Constant.getSystemMap().get("app_id")));
//        xmdParmas.put("wxSubAppId", paymentAccountConfig.optString("wx_sub_app_id"));
        xmdParmas.put("random", UUID.randomUUID().toString().replace("-", ""));
        // 验证签名
        String key = Constant.getSystemMap().get("payment_xmd_key");
        xmdParmas.put("sign", PaymentUtil.generateSign(key, xmdParmas));
        log.info("新美大定时器发起查询请求参数：==="+xmdParmas.toString());
        // 验证封装参数
        boolean validate = validate(xmdParmas, "merchantId", "appId", "random", "outTradeNo", "sign");
        if (!validate) {
            return null;
        }

        log.info("新美大支付 - 查询任务 - 调用接口: " + api + ", 参数: " + xmdParmas.toString());
        String url = Constant.getSystemMap().get("payment_xmd_url");
        String response = HttpUtil.sendPostRequest(url + api, xmdParmas.toString());
        log.info("新美大支付 - 查询任务 - 调用接口: " + api + ", 返回: " + response);
        return JSONObject.fromObject(response);
    }

    /**
     * 验证传进的参数格式是否正确
     *
     * @param param
     * @param columns
     * @return
     */
    public boolean validate(JSONObject param, String... columns) {
        for (String column : columns) {
            if (!param.containsKey(column)
                    || StringUtils.isBlank(param.getString(column))) {
                log.info("参数：" + column + "不允许为空");
                return false;
            }
        }
        return true;
    }

    /**
     * 根据轮训的次数存储redis中的次数，并判断这个订单号是否需要执行
     *
     * @param json
     * @return
     */
    public boolean isExecute(JSONObject json) {
        String out_trade_no = json.optString("out_trade_no");
        int auto_num = json.optInt("auto_num");
        long time = json.optLong("time");
        boolean execute = isExecute(auto_num, time);
        if (execute) {
            auto_num++;
            json.put("auto_num", auto_num);
            if (!PaymentRedisCache.hasKey(out_trade_no)) {
                return false;
            }
            PaymentRedisCache.saveOutTradeNo2Info(out_trade_no, json.toString());// 更新执行次数
        }
        return execute;
    }

    /**
     * 是否已经到了该执行的时间
     *
     * @param auto_num
     * @param time
     * @return
     */
    public boolean isExecute(int auto_num, long time) {
        //   --------------查询发动频率15/15/30/180/1800/1800/1800/1800/3600/7200
        long currenttime = System.currentTimeMillis();

        auto_num = auto_num - 50;

        if (auto_num < 0) {
            if (currenttime - time >= (long) (3 * 1000)) {
                return true;
            }
        }

        if (auto_num == 0) {
            if (currenttime - time >= (long) (15 * 1000)) {
                return true;
            }
        } else if (auto_num == 1) {
            if (currenttime - time >= (long) (30 * 1000)) {
                return true;
            }
        } else if (auto_num == 2) {
            if (currenttime - time >= (long) (60 * 1000)) {
                return true;
            }
        } else if (auto_num == 3) {
            if (currenttime - time >= (long) (240 * 1000)) {
                return true;
            }
        } else if (auto_num == 4) {
            if (currenttime - time >= (long) (2040 * 1000)) {
                return true;
            }
        } else if (auto_num == 5) {
            if (currenttime - time >= (long) (3840 * 1000)) {
                return true;
            }
        } else if (auto_num == 6) {
            if (currenttime - time >= (long) (5640 * 1000)) {
                return true;
            }
        } else if (auto_num == 7) {
            if (currenttime - time >= (long) (7440 * 1000)) {
                return true;
            }
        } else if (auto_num == 8) {
            if (currenttime - time >= (long) (11040 * 1000)) {
                return true;
            }
        } else if (auto_num == 9) {
            if (currenttime - time >= (long) (18240 * 1000)) {
                return true;
            }
        }
        return false;
    }
}
