package com.tzx.report.service.rest.boh;

import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.report.bo.boh.SpecialFilteringService;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller("SpecialFilteringRest")
@RequestMapping("/report/SpecialFilteringRest")
public class SpecialFilteringRest
{
	private static final Logger logger = Logger.getLogger(SpecialFilteringRest.class);

	@Resource(name = SpecialFilteringService.NAME)
	private SpecialFilteringService	specialFilteringService;

	/**
	 * 过滤信息查询
	 */
	@RequestMapping(value = "/loadOperationInformation")
	public void loadOperationInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id", (String) session.getAttribute("tenentid"));

			if(!obj.containsKey("store_ids")){
				String store_id = (String) session.getAttribute("store_id");
				if ("0".equals(store_id)){
					obj.put("store_ids","0");
				} else {
					obj.put("store_ids", session.getAttribute("user_organ_codes_group"));
				}
			}

			result = specialFilteringService.loadOperationInformation((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
		    logger.error("查询特殊账务异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 保存过滤信息
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/saveOperationInformation")
	public void saveOperationInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		result.put("success", false);

		try
		{
			List<JSONObject> addJson =new ArrayList<>();
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			String [] codeArr =obj.opt("idStr").toString().split(",");
			String [] nameArr =obj.opt("nameStr").toString().split(",");
			for(int i = 0 ; i<codeArr.length ;i++) {
				JSONObject josn =new JSONObject();
				josn.put("store_id", obj.optString("store_id"));
				josn.put("item_type", obj.optString("item_type"));
				josn.put("type_name", obj.optString("type_name"));
				josn.put("item_name", nameArr[i]);
				josn.put("item_code", codeArr[i]);
				if(obj.optString("item_type").equals("rate_payname_filter") && i==codeArr.length-1 ) {
					// 当前数值加上唯一标示 2
					josn.put("is_run", 2);
				}else {
					josn.put("is_run", 1);
				}
				josn.put("last_update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
				addJson.add(josn);
			}

			specialFilteringService.saveOperationInformation((String) session.getAttribute("tenentid"), obj, addJson, result);

		}
		catch (Exception e)
		{
			result.put("success", false);
			result.put("msg", "保存失败！");
			e.printStackTrace();
			logger.error("特殊账务配置异常>>>"+ ExceptionMessage.getExceptionMessage(e));
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	 * 查询系统现有的字段
	 */
	@RequestMapping(value = "/getCheakTree")
	public void getCheakTree(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String returnJSONstring = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			returnJSONstring = specialFilteringService.getCheakTree((String) session.getAttribute("tenentid"),  p);
		}
		catch (Exception e)
		{
		    logger.error("查询系统现有的字典异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.println(returnJSONstring);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	 * 停用
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/deleteNode")
	public void deleteNode(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			List<JSONObject> addJson =new ArrayList<JSONObject>();
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
				specialFilteringService.delete((String) session.getAttribute("tenentid"), "saas_special_filter", obj);
		}
		catch (Exception e)
		{
			result = "{\"success\": false, \"msg\" : \"" + e.getMessage() + "\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/saveRun")
	public void saveRun(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			Object dic =null;
			// 修改
			obj.put("tenancy_id", (String) session.getAttribute("tenentid"));
			dic = specialFilteringService.updateSaveRun((String) session.getAttribute("tenentid"), "saas_special_filter", obj);
			if (dic != null) result = "{\"success\": true, \"id\" : \"" + dic.toString() + "\"}";
		}
		catch (Exception e)
		{
			result = "{\"success\": false, \"msg\" : \"" + e.getMessage() + "\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/selectRun")
	public void selectRun(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("store_ids")){
				obj.put("store_ids","0");
			}
				result = specialFilteringService.selectRun((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}


	@RequestMapping(value = "/getBillInfo")
	public void getBillInfo(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		//String result = "";
		JSONObject result = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("store_ids")){
				obj.element("store_ids", session.getAttribute("user_organ_codes_group"));
			}
			result = specialFilteringService.getBillInfo((String) session.getAttribute("tenentid"), obj);
		}
		catch (Exception e)
		{
			logger.error("查询账单或特殊场景的信息异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
			result.put("success",false);
			result.put("msg","查询账单或特殊场景的信息错误");
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}


	@RequestMapping(value = "/getOrgantree")
	public void getOrgantree(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("store_ids")){
				obj.element("store_ids", session.getAttribute("user_organ_codes_group"));
			}

			result = specialFilteringService.getOrgantree((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			logger.info("查询B帐门店树异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}


	@RequestMapping(value = "/saveBaccountRule")
	public void saveBaccountRule(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		//String result = "";
		JSONObject result = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("store_ids")){
				obj.element("store_id", session.getAttribute("user_organ_codes_group"));
			}
			result = specialFilteringService.saveBaccountRule((String) session.getAttribute("tenentid"), obj);
		}
		catch (Exception e)
		{
			logger.error("保存B帐规则异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
			result.put("success",false);
			result.put("msg","保存规则异常");
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}


	@RequestMapping(value = "/findBaccountRule")
	public void findBaccountRule(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("store_ids")){
				obj.element("store_id", session.getAttribute("user_organ_codes_group"));
			}
			result = specialFilteringService.findBaccountRule((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			logger.error("查询B帐规则异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/getAccountingAmount")
	public void getAccountingAmount(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		//String result = "";
		JSONObject result = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("store_ids")){
				obj.element("store_id", session.getAttribute("user_organ_codes_group"));
			}
			result = specialFilteringService.getAccountingAmount((String) session.getAttribute("tenentid"), obj);
		}
		catch (Exception e)
		{
			logger.error("生成账务金额异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
			result.put("success",false);
			result.put("msg","生成账务金额异常");
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}


	@RequestMapping(value = "/findBaccountRuleDetails")
	public void findBaccountRuleDetails(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		//String result = "";
		JSONObject result = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("store_id")){
				obj.element("store_id", session.getAttribute("user_organ_codes_group"));
			}
			result = specialFilteringService.findBaccountRuleDetails((String) session.getAttribute("tenentid"), obj);
		}
		catch (Exception e)
		{
			logger.error("查询B帐规则明细异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
			result.put("success",false);
			result.put("msg","查询规则明细错误");
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/getSysParam")
	public void getSysParam(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("store_ids")){
				obj.element("store_id", session.getAttribute("user_organ_codes_group"));
			}
			result = specialFilteringService.getSysParam((String) session.getAttribute("tenentid"), obj);
		}
		catch (Exception e)
		{
			logger.error("查询商户级参数异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
			result.put("success",false);
			result.put("msg","查询系统参数错误");
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}


	@RequestMapping(value = "/deleteSpecialFilter")
	public void deleteSpecialFilter(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("store_ids")){
				obj.element("store_id", session.getAttribute("user_organ_codes_group"));
			}
			result = specialFilteringService.deleteSpecialFilter((String) session.getAttribute("tenentid"), obj);
		}
		catch (Exception e)
		{
			logger.error("删除规则异常>>>"+ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
			result.put("success",false);
			result.put("msg","删除规则错误");
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

}
