package com.tzx.boh.bo.imp;

import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.boh.bo.BusinessinComeManagementService;
import com.tzx.framework.bo.dto.BasicCombobox;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
@Service(BusinessinComeManagementService.NAME)
public class BusinessinComeManagementServiceImpl implements BusinessinComeManagementService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	@SuppressWarnings("unchecked")
	@Override
	public JSONObject loadBusinessinComeInformation(String tenancyID, JSONObject condition) throws Exception
	{
		
		StringBuilder sql = new StringBuilder();
		JSONObject result = new JSONObject();
		if(!condition.get("organ_code").equals("0")&&!"".equals(condition.get("organ_code")) &&!"0".equals(condition.get("is_zb"))){
			sql.append("select A .*, b.org_full_name,c.inout_name from boh_inout_info A left join organ b on 	b. ID = A .store_id left join hq_inout_type c on c.id=A.inout_type where ");
			sql.append("  b.organ_code LIKE '"+condition.get("organ_code")+"%' ");
//			sql.append("  b.id in (select * from get_oids_bycode('"+condition.get("organ_code").toString().trim()+"')) ");
			sql.append(" and a.inout_property='SR01' ");
			if (condition.containsKey("t1") )
			{
				sql.append(" and  a.business_date >= TO_DATE('" + condition.get("t1") + "','YYYY-MM-DD') ");
			}
			if (condition.containsKey("t2") )
			{
				sql.append(" and  a.business_date <= TO_DATE('" + condition.get("t2") + "','YYYY-MM-DD') ");
			}
			if (condition.containsKey("t3") && !condition.get("t3").equals(""))
			{
				sql.append(" and a.inout_type=" + condition.get("t3") + " ");
			}
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			Set<String> keys = condition.keySet();
			for(String s:keys)
			{
				if("tableName".equals(s)||"page".equals(s)||"rows".equals(s)||"sort".equals(s)||"order".equals(s)||"sortName".equals(s))
				{
					continue;
				}
				if(!"t1".equals(s)&&!"t2".equals(s)&&!"t3".equals(s)&&!"organ_code".equals(s)&&!"is_zb".equals(s)){
					sql.append(" and a."+s+" like '"+condition.optString(s)+"%'");
				}
				
			}
			if(condition.optString("sort")!=null && !"".equals(condition.optString("sort")))
			{
				sql.append(" order by "+condition.optString("sort")+" "+condition.optString("order"));
			}
			else
			{
				sql.append(" order by tenancy_id");
			}
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
			}
		
		return result;
	}

	@Override
	public boolean checkUnique(String tenentId, JSONObject param) throws Exception
	{
		try{

			String business_date = param.optString("business_date");
			String oldId = param.optString("id");
			if (Tools.hv(business_date))
			{
				StringBuilder sql = new StringBuilder();
				sql.append("select id from boh_inout_info info where info.business_date= '"+business_date+"' and info.inout_property='SR01'");
				long total = this.dao.countSql(tenentId, sql.toString());
				List<JSONObject> list = this.dao.query4Json(tenentId, this.dao.buildPageSql(param,sql.toString()));
				String id="";
				if(list.size()>=1){
					  for(JSONObject result : list){
		                	id=result.getString("id");
		                }
				}
              
                if(oldId.equals("")&& total <1){
                	return false;
                }
                if(!id.equals("")&&oldId.equals(id)&& total >=1){
                	return false;
                }
                if(!oldId.equals(id)&& total >=1){
                	return true;
                }
                if(!oldId.equals(id)&& total <1){
                	return false;
                }
			}

			return true;
		}catch(Exception e)
		{
			e.printStackTrace();
			return true;
		}
	}

	@Override
	public String findInOutTypeName(String tenancyId, String type, Object param) throws Exception
	{
		JSONObject jb = (JSONObject) param;
		StringBuilder sb = new StringBuilder();
		if(type.equals("ZC02")){
			sb.append("SELECT a.id,a.inout_name as text FROM hq_inout_type A, hq_inout_type_org b WHERE b.inout_type_id = A .id AND b.store_id = '" + jb.get("store_id").toString() + "' AND A .inout_property='ZC02' AND A.valid_state='1'");
		}else if(type.equals("SR01")){
			sb.append("SELECT a.id,a.inout_name as text FROM hq_inout_type A, hq_inout_type_org b WHERE b.inout_type_id = A .id AND b.store_id = '" + jb.get("store_id").toString() + "' AND A .inout_property='SR01' AND A.valid_state='1'");
		}
		
		List<BasicCombobox> list = (List<BasicCombobox>) this.dao.query(tenancyId, sb.toString(), BasicCombobox.class);
		return com.tzx.framework.common.util.JsonUtils.list2json(list);
	}

	@Override
	public String findPayStyle(String tenancyId, String type, Object param) throws Exception
	{
		JSONObject jb = (JSONObject) param;
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT a.id,a.payment_name1 as text FROM payment_way A,payment_way_of_ogran b WHERE b.payment_id = A .id and b.organ_id = '" + jb.get("store_id").toString() + "' AND A.status='1'");
		
		List<BasicCombobox> list = (List<BasicCombobox>) this.dao.query(tenancyId, sb.toString(), BasicCombobox.class);
		return com.tzx.framework.common.util.JsonUtils.list2json(list);
	}

}
