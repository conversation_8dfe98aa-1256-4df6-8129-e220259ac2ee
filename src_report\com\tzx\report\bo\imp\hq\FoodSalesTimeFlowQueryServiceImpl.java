package com.tzx.report.bo.imp.hq;

import com.tzx.report.bo.hq.FoodSalesTimeFlowQueryService;
import com.tzx.report.po.hq.dao.FoodSalesTimeFlowQueryDao;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service(FoodSalesTimeFlowQueryService.NAME)
public class FoodSalesTimeFlowQueryServiceImpl implements FoodSalesTimeFlowQueryService
{
	
	@Resource
	private FoodSalesTimeFlowQueryDao foodSalesTimeFlowQueryDao;

	@Override
	public JSONObject getFoodSalesTimeFlowQuery(String tenancyID, JSONObject condition) throws Exception {
		return foodSalesTimeFlowQueryDao.getFoodSalesTimeFlowQuery(tenancyID, condition);
	}
 
}
