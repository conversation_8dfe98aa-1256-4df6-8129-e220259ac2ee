package com.tzx.cc.datasync.bo.dto.newpos;


// Generated 2014-1-18 2:32:01 by Hibernate Tools 3.4.0.CR1

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * BtYdxm2 generated by hbm2java
 */
public class BtYdxm2 implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 3580842027945293731L;

	private String				id;

	private String				yddh;

	private String				xmbh;

	private String				xmmc;

	private String				xmsx;

	private String				lrrq;

	private String				xm1bh;

	private String				xm1mc;

	private String				dwbh;

	private String				kwbh;

	private String				tcbh;

	private Integer				tcdch;

	private Integer			zkl;

	private BigDecimal			cmje;

	private BigDecimal			fzje;

	private BigDecimal			fzsl;

	private BigDecimal			totalprice;

	private BigDecimal			xmdj;

	private BigDecimal			xmsl;

	private Integer				dcxh;

	private String				memo;

	private String				ly1;

	private String				ly2;

	private String				ly3;

	private String				ly4;

	private String				ly5;

	private String				specialOffersId;
	private String				specialOffersName;
	private String				orderType;
	private Integer				xmid;
	
	private String oldcccode;
	
	public List cpbhs;

	public List getCpbhs() {
		return cpbhs;
	}

	public void setCpbhs(List cpbhs) {
		this.cpbhs = cpbhs;
	}

	public BtYdxm2()
	{
	}

	public BtYdxm2(String id, String yddh, String xmbh)
	{
		this.id = id;
		this.yddh = yddh;
		this.xmbh = xmbh;
	}

	public BtYdxm2(String id, String yddh, String xmbh, String xmmc, String xmsx, String lrrq, String xm1bh, String xm1mc, String dwbh, String kwbh, String tcbh, Integer tcdch, Integer zkl, BigDecimal cmje, BigDecimal fzje, BigDecimal fzsl, BigDecimal totalprice, BigDecimal xmdj, BigDecimal xmsl,
			Integer dcxh, String memo, String ly1, String ly2, String ly3, String ly4, String ly5)
	{
		this.id = id;
		this.yddh = yddh;
		this.xmbh = xmbh;
		this.xmmc = xmmc;
		this.xmsx = xmsx;
		this.lrrq = lrrq;
		this.xm1bh = xm1bh;
		this.xm1mc = xm1mc;
		this.dwbh = dwbh;
		this.kwbh = kwbh;
		this.tcbh = tcbh;
		this.tcdch = tcdch;
		this.zkl = zkl;
		this.cmje = cmje;
		this.fzje = fzje;
		this.fzsl = fzsl;
		this.totalprice = totalprice;
		this.xmdj = xmdj;
		this.xmsl = xmsl;
		this.dcxh = dcxh;
		this.memo = memo;
		this.ly1 = ly1;
		this.ly2 = ly2;
		this.ly3 = ly3;
		this.ly4 = ly4;
		this.ly5 = ly5;
	}

	public String getId()
	{
		return id;
	}

	public void setId(String id)
	{
		this.id = id;
	}

	public String getYddh()
	{
		return yddh;
	}

	public void setYddh(String yddh)
	{
		this.yddh = yddh;
	}

	public String getXmbh()
	{
		return xmbh;
	}

	public void setXmbh(String xmbh)
	{
		this.xmbh = xmbh;
	}

	public String getXmmc()
	{
		return xmmc;
	}

	public void setXmmc(String xmmc)
	{
		this.xmmc = xmmc;
	}

	public String getXmsx()
	{
		return xmsx;
	}

	public void setXmsx(String xmsx)
	{
		this.xmsx = xmsx;
	}

	public String getLrrq()
	{
		return lrrq;
	}

	public void setLrrq(String lrrq)
	{
		this.lrrq = lrrq;
	}

	public String getXm1bh()
	{
		return xm1bh;
	}

	public void setXm1bh(String xm1bh)
	{
		this.xm1bh = xm1bh;
	}

	public String getXm1mc()
	{
		return xm1mc;
	}

	public void setXm1mc(String xm1mc)
	{
		this.xm1mc = xm1mc;
	}

	public String getDwbh()
	{
		return dwbh;
	}

	public void setDwbh(String dwbh)
	{
		this.dwbh = dwbh;
	}

	public String getKwbh()
	{
		return kwbh;
	}

	public void setKwbh(String kwbh)
	{
		this.kwbh = kwbh;
	}

	public String getTcbh()
	{
		return tcbh;
	}

	public void setTcbh(String tcbh)
	{
		this.tcbh = tcbh;
	}

	public Integer getTcdch()
	{
		return tcdch;
	}

	public void setTcdch(Integer tcdch)
	{
		this.tcdch = tcdch;
	}

	public Integer getZkl()
	{
		return zkl;
	}

	public void setZkl(Integer zkl)
	{
		this.zkl = zkl;
	}

	public BigDecimal getCmje()
	{
		return cmje;
	}

	public void setCmje(BigDecimal cmje)
	{
		this.cmje = cmje;
	}

	public BigDecimal getFzje()
	{
		return fzje;
	}

	public void setFzje(BigDecimal fzje)
	{
		this.fzje = fzje;
	}

	public BigDecimal getFzsl()
	{
		return fzsl;
	}

	public void setFzsl(BigDecimal fzsl)
	{
		this.fzsl = fzsl;
	}

	public BigDecimal getTotalprice()
	{
		return totalprice;
	}

	public void setTotalprice(BigDecimal totalprice)
	{
		this.totalprice = totalprice;
	}

	public BigDecimal getXmdj()
	{
		return xmdj;
	}

	public void setXmdj(BigDecimal xmdj)
	{
		this.xmdj = xmdj;
	}

	public BigDecimal getXmsl()
	{
		return xmsl;
	}

	public void setXmsl(BigDecimal xmsl)
	{
		this.xmsl = xmsl;
	}

	public Integer getDcxh()
	{
		return dcxh;
	}

	public void setDcxh(Integer dcxh)
	{
		this.dcxh = dcxh;
	}

	public String getMemo()
	{
		return memo;
	}

	public void setMemo(String memo)
	{
		this.memo = memo;
	}

	public String getLy1()
	{
		return ly1;
	}

	public void setLy1(String ly1)
	{
		this.ly1 = ly1;
	}

	public String getLy2()
	{
		return ly2;
	}

	public void setLy2(String ly2)
	{
		this.ly2 = ly2;
	}

	public String getLy3()
	{
		return ly3;
	}

	public void setLy3(String ly3)
	{
		this.ly3 = ly3;
	}

	public String getLy4()
	{
		return ly4;
	}

	public void setLy4(String ly4)
	{
		this.ly4 = ly4;
	}

	public String getLy5()
	{
		return ly5;
	}

	public void setLy5(String ly5)
	{
		this.ly5 = ly5;
	}

	public String getSpecialOffersId()
	{
		return specialOffersId;
	}

	public void setSpecialOffersId(String specialOffersId)
	{
		this.specialOffersId = specialOffersId;
	}

	public String getSpecialOffersName()
	{
		return specialOffersName;
	}

	public void setSpecialOffersName(String specialOffersName)
	{
		this.specialOffersName = specialOffersName;
	}

	public String getOrderType()
	{
		return orderType;
	}

	public void setOrderType(String orderType)
	{
		this.orderType = orderType;
	}

	public Integer getXmid()
	{
		return xmid;
	}

	public void setXmid(Integer xmid)
	{
		this.xmid = xmid;
	}

	public String getOldcccode() {
		return oldcccode;
	}

	public void setOldcccode(String oldcccode) {
		this.oldcccode = oldcccode;
	}

}
