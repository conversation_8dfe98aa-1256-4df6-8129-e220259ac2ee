package com.tzx.cc.invoice.electronic.service.impl;

import io.netty.util.internal.StringUtil;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.gson.GsonBuilder;
import com.sun.org.apache.xml.internal.security.utils.Base64;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.Type;
import com.tzx.cc.invoice.electronic.cache.PiaoTongConfigCache;
import com.tzx.cc.invoice.electronic.cache.SysparamCache;
import com.tzx.cc.invoice.electronic.cont.ElectronicInvoiceConst;
import com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao;
import com.tzx.cc.invoice.electronic.service.ElecInvoiceService;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoiceUtils;
import com.tzx.cc.invoice.electronic.util.PiaoTongElectronicInvoiceWebUtils;
import com.tzx.cc.invoice.electronic.util.UUIDUtils;
import com.tzx.cc.invoice.electronic.util.YonyouElectronicInvoiceWebUtils;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.news.cont.Contant;

/**
 * 
 * 票通电子发票相关
 * <AUTHOR>
 * 2018-02-24
 */
@Service("com.tzx.cc.invoice.electronic.service.impl.PiaotongElecInvoiceServiceImpl")
public class PiaotongElecInvoiceServiceImpl implements ElecInvoiceService {
	private static final Logger logger = Logger.getLogger(PiaotongElecInvoiceServiceImpl.class);
	
	@Autowired
	private ElecInvoiceDao elecInvoiceDao;
	
	@Resource(name = "genericDaoImpl")
	private GenericDao dao;
	
	@Resource(name="saasRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
	
	
	
	/**
	 * 验证data属性
	 * @param data
	 * @param result
	 * @return
	 */
	private boolean validaData(Data data,Data result){
		String tenancyId = data.getTenancy_id();
		if(StringUtils.isBlank(tenancyId)) {
			result.setMsg("参数：tenancy_id不允许为空");
			result.setCode(Contant.ILLEGAL_PARAM);
			logger.info("参数：tenancy_id不允许为空");
			return false;
		}
		return true;
	}
	
	/**
	 * 生成电子发票
	 * @param data
	 * @param result
	 * @throws Exception
	 * <AUTHOR>
	 */
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#issueElectronicInvoice(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void issueElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		String serviceType = param.optString("SERVICE_TYPE");
		if(StringUtils.isBlank(serviceType)) {
			result.setMsg("业务类型不能为空");
            return;
		}
		
		Type type = data.getType();
		
		JSONObject json = elecInvoiceDao.getRequestInfo(data.getTenancy_id(),data.getStore_id());
		if(json==null) {
			result.setMsg("总部未配置纳税人识别号，税率等信息");
			return;
		}

		if(Type.ISSUE_ELECTRONIC_INVOICE.equals(type)) {
			String isEleServiceType = SysparamCache.getSysparam(data.getTenancy_id(), data.getStore_id(), serviceType);
			if(!(StringUtils.equals(isEleServiceType, "1")||StringUtils.equals(isEleServiceType, "4"))) {
				result.setMsg("对应业务类型不支持开通电子发票");
				return;
			}
			ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param);
		} else {
			ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param,"XSF_NSRSBH","seller_name","XMMC");
		}
		
		
		//验证参数是否全
		boolean validate = validate(param, result,"SERVICE_TYPE","XSF_NSRSBH","data","SL","DDRQ");
		if(!validate) {
			return;
		}
		//查库判断是否已经取消
		validate = issueElectronicInvoiceValid(data,result,param);
		if(!validate) {
			return;
		}
		//组装h5 URL 
		String reqUrl = buildUrl(data,result,param);
		if(StringUtils.isBlank(reqUrl)) {
			return;
		}
		logger.info("票通返回开票地址：");
		logger.info(reqUrl);
		
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		JSONObject resultJson = new JSONObject();
		resultJson.put("url", reqUrl);
		returnList.add(resultJson);
		result.setData(returnList);
	}
	
	/**
	 * 
	 * 返回二维码扫码开票URL（获取开票二维码和提取码）
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 * <AUTHOR>
	 */
	private String buildUrl(Data data, Data result, JSONObject param) throws Exception {
		String respUrl = "";
		JSONObject reqParam = new JSONObject();
        reqParam.put("taxpayerNum", param.optString("XSF_NSRSBH"));//销售方纳税人识别号
        reqParam.put("enterpriseName",param.optString("seller_name"));//企业名称
        reqParam.put("tradeNo", param.optString("FPQQLSH"));//交易单号(发票请求流水号)
        reqParam.put("tradeTime", DateUtil.getNowDateYYDDMMHHMMSS());//交易时间  // param.optString("DDRQ");
        reqParam.put("invoiceAmount", param.optString("JSHJ"));//发票金额(含税)
        reqParam.put("allowInvoiceCount", "1");//允许开票张数  不传默认为1
        reqParam.put("drawerName", "系统");		//开票人名称 默认：系统
        
        List<Object> items = new ArrayList<>();
		JSONObject item = new JSONObject();
		item.put("itemName", ElectronicInvoiceConst.ELECTRON_ICINVOICE_XMMC);//开票项目名称
		item.put("taxRateValue", param.optString("SL"));//税率，0.170，小数点后3位
		item.put("taxClassificationCode",ElectronicInvoiceConst.ELECTRIC_MEAL_BH);//税收分类编码
		item.put("unitPrice", param.optString("JSHJ"));	//单价(含税)
		item.put("quantity", "1");	//数量
		item.put("invoiceItemAmount", param.optString("JSHJ"));		//开票项目金额	
//		item.put("invoiceItemDisAmount", param.optString("invoiceItemDisAmount"));	//开票项目折扣金额	
		items.add(item);
		reqParam.put("itemList", items);//发票明细
       
        
        GsonBuilder builder = new GsonBuilder();
		String reqstr = builder.create().toJson(reqParam);
        logger.info("调用票通获取开票二维码接口业务报文"+reqstr);
		JSONObject respResult = PiaoTongElectronicInvoiceWebUtils.invoice(data.getTenancy_id(),data.getStore_id(),reqstr);
		String code = respResult.optString("code");;//状态码
		String msg = respResult.optString("msg");
		if("0000".equals(code)){
			//操作成功
			JSONObject drawObj = respResult.optJSONObject("content");
			String invoicecode = drawObj.optString("tradeNo");//交易单号(发票请求流水号)
			String url=drawObj.optString("invoiceUrl");
			byte[] decode = Base64.decode(url);
			String invoiceUrl = new String(decode);
//			String qrcode = drawObj.optString("invoiceUrl");
			respUrl = invoiceUrl;

			logger.info("调用电子发票扫码开票接口返回：" + msg + "["+code+"] ,发票提取码："+invoicecode);
		}else {
            result.setMsg(msg);
        }
		return respUrl;
	}
	
	/**
	 * 验证发票是否符合规则
	 * @param data
	 * @param result
	 * @param param
	 * @return
	 * @throws Exception 
	 * <AUTHOR>
	 */
	private boolean issueElectronicInvoiceValid(Data data, Data result,
			JSONObject param) throws Exception {
		String dh = param.optString("DH");	//订单编号
		
		JSONObject json = elecInvoiceDao.queryInfoByOrderNo(data.getTenancy_id(),dh);
		String jshj = calsJSHJ(data,result,param);
		if(json==null) {
			String FPQQLSH = String.valueOf(UUIDUtils.next());
			param.put("FPQQLSH", FPQQLSH);
			//计算价税合计
			param.put("JSHJ", jshj);
			json = issueElectronicInvoiceSave(data,result,param);
		} else {
            String FPQQLSH = json.optString("invoice_flow_number");
            //如果是待开票，删除之前redis中的发票流水号，再产生一个新的
            if(StringUtils.equals(json.optString("invoice_state"),ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT)) {
                redisTemplate.opsForHash().delete(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE,FPQQLSH);
                FPQQLSH = String.valueOf(UUIDUtils.next());
            }
            param.put("FPQQLSH", FPQQLSH);
            json.put("invoice_flow_number",FPQQLSH);
            this.dao.updateIgnorCase(data.getTenancy_id(),"hq_electronic_invoice_info",json);
        }
        json.put("total_tax_amount", jshj);
        if(StringUtils.isBlank(jshj) || StringUtils.equals(jshj, "null")) {
            result.setMsg("计算出来的价税合计为空");
            return false;
        }
        //把计算的价税合计存入param中
		param.put("JSHJ", jshj);
		String state = json.optString("invoice_state");
		if(StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS, state) //取消开票
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL, state)//已取消 业务状态
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS, state)//取消冲正
				) {
			result.setMsg("此发票单号已经取消");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return false;
		}
		if(StringUtils.equals(state, ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS)) {
			result.setMsg("此单号已开具过");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return false;
		}
		
		//二维码打印期限
		String ewmdyxq = SysparamCache.getSysparam(data.getTenancy_id(), "dzfp_ewmdyxq");
		String dataorstr = param.optString("DDRQ");
		if(StringUtils.isBlank(ewmdyxq)) {
			ewmdyxq = "7";
		}
		boolean expire = ElectronicInvoiceUtils.isExpire(dataorstr, Integer.parseInt(ewmdyxq));
		if(expire) {
			result.setMsg("二维码有效期为"+ewmdyxq+"天，现在已经失效了");
			return false;
		}
		
		//向redis中存储流水号和商户号的对应关系
		JSONObject redisJson = new JSONObject();
		redisJson.put("ID", json.optString("id"));
		redisJson.put("ORGAN", data.getStore_id());
		redisJson.put("FPQQLSH", param.optString("FPQQLSH"));
		redisJson.put("XSF_NSRSBH", param.optString("XSF_NSRSBH"));
		redisJson.put("tenancy_id", data.getTenancy_id());
		//设置发票流水号与商户的对应关系到redis中
		redisTemplate.opsForHash().put(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE, redisJson.optString("FPQQLSH"), redisJson.toString());
		//设置期限为24小时
		redisTemplate.expire(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE, ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE_EXPIRE, TimeUnit.HOURS);
		
		return true;
	}
	
	/**
	 * 生成电子发票保存到数据库
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 * <AUTHOR>
	 */
	private JSONObject issueElectronicInvoiceSave(Data data, Data result,
			JSONObject param) throws Exception {
		JSONObject json = new JSONObject();
		json.put("tenancy_id", data.getTenancy_id());
		json.put("organ_id", data.getStore_id());
		json.put("invoice_service_type",param.optString("SERVICE_TYPE"));
		//流水号
		json.put("invoice_flow_number",param.optString("FPQQLSH"));
		//纳税识别号
		json.put("tax",param.optString("XSF_NSRSBH"));
		json.put("seller_name",param.optString("seller_name"));
		//价税合计
		json.put("total_tax_amount",param.optString("JSHJ"));
		//税率
		json.put("tax_rate", param.optString("SL"));
		//订单号
		json.put("order_code",param.optString("DH"));
		json.put("third_order_code",param.optString("EWMFPQQLSH"));
		json.put("order_date",param.optString("DDRQ"));
        json.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_LP);
		json.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT);
		json.put("electric_use_choose", ElectronicInvoiceConst.ELECTRIC_USE_CHOOSE_PT);//电子发票-票通
		
		String billNo="";
		if(param.optString("DH","").contains("@"))
			billNo=param.optString("DH").split("@")[0];
		json.put("bill_no", billNo);
		
		Integer id = (Integer) dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", json);
		json.put("id", id);
		JSONObject json2 = new JSONObject();
		json2.put("electronic_id", id);
		//税率
		json2.put("tax_rate", param.optString("SL"));
		//项目名称
		json2.put("name",ElectronicInvoiceConst.ELECTRON_ICINVOICE_XMMC);//param.optString("XMMC")
		dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_details", json2);
		return json;
	}
	
	/**
	 * 计算价税合计
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 * <AUTHOR>
	 */
	private String calsJSHJ(Data data, Data result, JSONObject param) throws Exception {
	    String paymentclassJeStr = param.optString("data");
	    if(StringUtils.isBlank(paymentclassJeStr)) {
	        return null;
	    }
	    com.alibaba.fastjson.JSONArray paymentclassJeArray = JSON.parseArray(paymentclassJeStr);
	    if(paymentclassJeArray==null || paymentclassJeArray.isEmpty()) {
	        return null;
	    }
	    StringBuffer paymentClassBuffer = new StringBuffer();
	    
	    //如果是门店传递过来的付款方式和金额，不用验证总部这边是否配置，直接把金额加起来即可
	    if(Type.ISSUE_ELECTRONIC_INVOICE_SCC.equals(data.getType())) {
	    	 BigDecimal bigDecimal = new BigDecimal(0);
	 	    //遍历传过来的paymentClass
	 	    for(Object jsonObject :paymentclassJeArray){
	 	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	 	        BigDecimal je = json.getBigDecimal("JE");
	 	        bigDecimal = bigDecimal.add(je);
	 	    }
	 	    if(bigDecimal.compareTo(BigDecimal.ZERO)==0) {
	 	    	return null;
	 	    }
	 	    bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
	    	return bigDecimal.toString();
		} 
	    
	    
	    //遍历paymentclassJeArray 拼接paymentClass的sql条件
	    for(Object jsonObject : paymentclassJeArray){
	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	        if(!json.containsKey("PAYMENT_CLASS") || !json.containsKey("JE")) {
	            return null;
	        }
	        String paymentClass = json.getString("PAYMENT_CLASS");
	        paymentClassBuffer.append(paymentClass).append(",");
	    }
	    String paymentClassStr = paymentClassBuffer.toString();
	    paymentClassStr = paymentClassStr.substring(0,paymentClassStr.length()-1);
	    paymentClassStr = paymentClassStr.replaceAll(",", "','");
	    List<JSONObject> queryPaymentWay = elecInvoiceDao.queryPaymentWay(data.getTenancy_id(),data.getStore_id(),paymentClassStr);
	    
	    BigDecimal bigDecimal = new BigDecimal(0);
	    //遍历传过来的paymentClass
	    for(Object jsonObject :paymentclassJeArray){
	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	        String paymentClass = json.getString("PAYMENT_CLASS");
	        //遍历查出来的paymentClass
	        for(JSONObject queryJson:queryPaymentWay){
	            String ifInvoicing = queryJson.optString("if_invoicing");
	            String queryPaymentClass = queryJson.optString("payment_class");
	            //如果查出来的和传进来的一样并且已开启，就把金额相加
	            if(StringUtils.equals(paymentClass, queryPaymentClass) && StringUtils.equals(ifInvoicing, "1")) {
	                BigDecimal je = json.getBigDecimal("JE");
	                bigDecimal = bigDecimal.add(je);
	                break;
	            }
	        }
	    }
	    if(bigDecimal.compareTo(BigDecimal.ZERO)==0) {
 	    	return null;
 	    }
	    bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
        return bigDecimal.toString();
    }
	
	/**
	 * 验证传进的参数格式是否正确
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public boolean validate(JSONObject param, Data result,
			String... columns) {
		for (String column : columns) {
			if (!param.containsKey(column)
					|| StringUtils.isBlank(param.getString(column))) {
				result.setMsg("参数：" + column + "不允许为空");
				result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_PARAM);
				logger.info("参数：" + column + "不允许为空");
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 
	 * 请求开票 返回待开票界面(输入购买方名称等信息 开票)
	 * 
	 */
	@Override
	public void issueElectronicInvoice(Data data, Data result, HttpServletResponse response) throws Exception {
		issueElectronicInvoice(data, result);
		JSONObject param = getParam(result);
		if(param==null) {
			return;
		}
		String url = param.optString("url");
		response.sendRedirect(url);
	}

	/* (non-Javadoc)
	 * 取消电子发票接口
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#cancelElectronicInvoice(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void cancelElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		JSONObject json = elecInvoiceDao.getRequestInfo(data.getTenancy_id(),data.getStore_id());
		if(json==null) {
		    result.setMsg("取消订单总部未配置纳税人识别号，税率等信息");
		    return;
		}
        ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param);
		boolean validate = validate(param, result, "DH","XSF_NSRSBH");
		if(!validate) {
			return;
		}
		JSONObject queryInfoByOrderNo = elecInvoiceDao.queryInfoByOrderNo(data.getTenancy_id(), param.optString("DH"));


		//开票信息不存在 直接在系统中取消
		if(queryInfoByOrderNo==null
				) {
			if(queryInfoByOrderNo==null) {
				result.setMsg("订单号无效，创建一个订单号");
				queryInfoByOrderNo = new JSONObject();
			}
	        queryInfoByOrderNo.put("tenancy_id", data.getTenancy_id());
	        queryInfoByOrderNo.put("organ_id", data.getStore_id());
			queryInfoByOrderNo.put("order_code", param.optString("DH"));
			queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL);
			queryInfoByOrderNo.put("invoice_cancel_time", ElectronicInvoiceUtils.currentTime2Str());
			if(StringUtils.isBlank(queryInfoByOrderNo.optString("id"))) {
				this.dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
			} else {
				this.dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
			}

			List<JSONObject> returnList = new ArrayList<JSONObject>();
	        JSONObject resultJson = new JSONObject();
	        resultJson.put("code", "0000");
			resultJson.put("msg", "取消成功");
			returnList.add(resultJson);
			result.setData(returnList);
			result.setSuccess(Boolean.TRUE);
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
			result.setMsg("调用成功");
		    return;
		}
		String state = queryInfoByOrderNo.optString("invoice_state");
		if(StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS, state)
				) {
			result.setMsg("此发票单号已经取消");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return;
		}
		
		//查询二维码开票信息
		JSONObject reqParam = new JSONObject();
		reqParam.put("taxpayerNum", queryInfoByOrderNo.optString("tax"));//销售方纳税人识别号
		reqParam.put("tradeNo", queryInfoByOrderNo.optString("invoice_flow_number"));	//发票请求流水号
		reqParam.put("invoiceAmount", queryInfoByOrderNo.optDouble("total_tax_amount",0.00));//发票金额(含税)
		GsonBuilder builder = new GsonBuilder();
		String reqstr = builder.create().toJson(reqParam);
		logger.info("查询二维码开票信息接口请求业务报文:"+reqstr);
        JSONObject queryJsonObject = PiaoTongElectronicInvoiceWebUtils.queryInvoiceQrStatus(data.getTenancy_id(), data.getStore_id(),reqstr);

        if(!StringUtils.equals(queryJsonObject.optString("code"),"0000")) {
            result.setMsg("第三方没有该条记录");
            return;
        }
        
        String content=queryJsonObject.optString("content");
//        JSONObject returnJson=JSONObject.fromObject(content);
        com.alibaba.fastjson.JSONObject alijson = com.alibaba.fastjson.JSONObject.parseObject(content);
        JSONObject returnJson = ElectronicInvoiceUtils.convertAli2netJson(alijson);
        JSONArray itemList = returnJson.getJSONArray("itemList");
        JSONObject obj=itemList.getJSONObject(0);
        String invoiceReqSerialNo=obj.optString("invoiceReqSerialNo");	//获取发票请求流水号  如果为空则状态为待开票，不为空状态为已开票
        
        if(StringUtil.isNullOrEmpty(invoiceReqSerialNo)){	//待开票
        	 JSONObject reqJson = new JSONObject();
             reqJson.put("taxpayerNum",queryInfoByOrderNo.optString("tax"));					//纳税人识别号
             reqJson.put("tradeNo",queryInfoByOrderNo.optString("invoice_flow_number"));		//交易单号(发票请求流水号)
             reqJson.put("enterpriseName",queryInfoByOrderNo.optString("seller_name"));			//企业名称(与获取二维码接口时传递的企业名称一致)
             reqJson.put("invoiceAmount",queryInfoByOrderNo.optDouble("total_tax_amount",0.00));		//发票金额
             
             JSONArray reqJsonArray = new JSONArray();
             reqJsonArray.add(reqJson);
             logger.info("取消发票调用批量作废二维码接口请求业务报文:"+reqJsonArray.toString());
             //待开票状态，取消发票调用批量作废二维码接口
             JSONObject cancel = PiaoTongElectronicInvoiceWebUtils.cancel(data.getTenancy_id(), data.getStore_id(), reqJsonArray.toString());
             if(cancel==null) {
                 result.setSuccess(Boolean.FALSE);
                 result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_CONNECT);
                 result.setMsg("连接超时");
                 return;
             }
             String code = cancel.optString("code");
             String msg = cancel.optString("msg");

             if(StringUtils.equals("0000",code)) {
                 queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS);
                 queryInfoByOrderNo.put("invoice_cancel_time",ElectronicInvoiceUtils.currentTime2Str());
             } else {
                 queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_FAIL);
             }
             dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);

             List<JSONObject> returnList = new ArrayList<JSONObject>();
             JSONObject resultJson = new JSONObject();
             resultJson.put("code", code);
             resultJson.put("msg", msg);
             returnList.add(resultJson);
             result.setData(returnList);
             result.setSuccess(Boolean.TRUE);
             result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
             result.setMsg("调用成功");
             return;
        }
        
        //状态为已开票时 保存票通返回的发票请求流水号与invoice_flow_number 关联
        queryInfoByOrderNo.put("pt_invoicereqserialno", invoiceReqSerialNo);
        dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
        param.put("tenancy_id",data.getTenancy_id());
        
        //根据票通返回的发票请求流水号调用查询发票接口判断开票状态
        JSONObject reqJson = new JSONObject();
        reqJson.put("taxpayerNum",queryInfoByOrderNo.optString("tax"));					//纳税人识别号
        reqJson.put("invoiceReqSerialNo",invoiceReqSerialNo);							//票通系统发票请求流水号
        String reqStr = builder.create().toJson(reqJson);
        logger.info("查询发票信息接口请求业务报文:"+reqstr);
        JSONObject returnObj = PiaoTongElectronicInvoiceWebUtils.queryInvoiceStatus(data.getTenancy_id(), data.getStore_id(),reqStr);
        if(returnObj==null) {
            result.setSuccess(Boolean.FALSE);
            result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_CONNECT);
            result.setMsg("连接超时");
            return;
        }
        String code = returnObj.optString("code");
        String msg = returnObj.optString("msg");

        if(StringUtils.equals("0000",code)) {
            JSONObject returnContent=JSONObject.fromObject(returnObj.optString("content"));
            if(StringUtils.equals(returnContent.optString("code"),"0000")) {	//开票成功
            		logger.info("查询发票状态接口返回==》开票成功");
            		//调用回调地址
                    orderCallback(returnObj.optString("content"),param);
            		//红冲发票
            		JSONObject cancelParam = new JSONObject();
            		 
            		cancelParam.put("taxpayerNum", returnContent.optString("taxpayerNum"));			//纳税人识别号
            		String hc_invoiceReqSerialNo=getHCinvoiceReqSerialNo();							//生成红冲发票请求流水号（4位平台简称pt**+16位随机数）
                    cancelParam.put("invoiceReqSerialNo",hc_invoiceReqSerialNo);					//红冲发票请求流水号
                    cancelParam.put("invoiceCode", returnContent.optString("invoiceCode"));			//发票代码
                    cancelParam.put("invoiceNo", returnContent.optString("invoiceNo"));				//发票号码
                    cancelParam.put("redReason", "取消开票");
                    BigDecimal noTaxAmount =new BigDecimal(returnContent.optString("noTaxAmount"));//合计金额
                    BigDecimal tax_amount =new BigDecimal(returnContent.optString("taxAmount"));//合计税额
                    BigDecimal total_tax=noTaxAmount.add(tax_amount);								//价税合计金额
                    double total_tax_amount =total_tax.multiply(new BigDecimal("-1")).doubleValue(); 
                    cancelParam.put("amount",total_tax_amount);							//价税合计金额(原发票的价税合计金额,的负数值)
                    
                    JSONObject resultJson = new JSONObject();
                    param.put("reqJson",cancelParam);
//                    logger.info("访问的内容为："+cancelParam.toString());
            		hcfpkj(param, resultJson);		//红冲发票开具
            		//
            		List<JSONObject> returnList = new ArrayList<JSONObject>();
            		String hccode = resultJson.optString("code");
            		String hcmsg =resultJson.optString("msg");
//            		ElectronicInvoiceUtils.uploadOm(queryInfoByOrderNo);
            		resultJson.put("code", hccode);
            		resultJson.put("msg", hcmsg);
            		resultJson.remove("RSCODE");
            		resultJson.remove("MSG");
            		
            		returnList.add(resultJson);
            		result.setData(returnList);
            		result.setSuccess(Boolean.TRUE);
            		result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
            		result.setMsg("调用成功");
            }else if(StringUtils.equals(returnContent.optString("code"),"7777")) {	//开票中 ，发票状态置为取消中，通过线程异步处理取消中的发票取消操作
            	queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_PROCESS);
                dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
            	List<JSONObject> returnList = new ArrayList<JSONObject>();
                JSONObject resultJson = new JSONObject();
                resultJson.put("code", returnContent.optString("code"));
                resultJson.put("msg", returnContent.optString("msg"));
                returnList.add(resultJson);
                result.setData(returnList);
                result.setSuccess(Boolean.TRUE);
                result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
                result.setMsg("调用成功");
                return;
            	
            }else if(StringUtils.equals(returnContent.optString("code"),"9999")){//开票失败，不做处理直接取消成功
            	queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS);
                queryInfoByOrderNo.put("invoice_cancel_time",ElectronicInvoiceUtils.currentTime2Str());
                dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);

                List<JSONObject> returnList = new ArrayList<JSONObject>();
                JSONObject resultJson = new JSONObject();
                resultJson.put("code", returnContent.optString("code"));
                resultJson.put("msg", returnContent.optString("msg"));
                returnList.add(resultJson);
                result.setData(returnList);
                result.setSuccess(Boolean.TRUE);
                result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
                result.setMsg("调用成功");
                return;
            	
            }else{		//特殊情况返回的code为空或不存在时，发票状态置为取消中，通过线程异步处理取消中的发票取消操作
            	queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_PROCESS);
                dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
            	List<JSONObject> returnList = new ArrayList<JSONObject>();
                JSONObject resultJson = new JSONObject();
                resultJson.put("code", returnContent.optString("code"));
                resultJson.put("msg", returnContent.optString("msg"));
                returnList.add(resultJson);
                result.setData(returnList);
                result.setSuccess(Boolean.TRUE);
                result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
                result.setMsg("调用成功");
                return;
            }
            
            
        	
        } else {
        	result.setMsg("第三方没有该条记录");
            return;
        }
	}

	/**
	 * 发票红冲请求服务
	 * @param param
	 * @param result
	 * @throws Exception
	 *  <AUTHOR>
	 */
	public void hcfpkj(JSONObject param, JSONObject result) throws Exception {
		
		
        JSONObject elecInfo = param.optJSONObject("elecInfo");
        JSONObject reqJson = param.optJSONObject("reqJson");
        JSONObject redisJson = new JSONObject();

        redisJson.put("ID", elecInfo.optString("id"));
        redisJson.put("ORGAN", elecInfo.optString("organ_id"));
        String invoiceReqSerialNo=reqJson.optString("invoiceReqSerialNo");	//红冲发票请求流水号
        
        String invoice_flow_number=elecInfo.optString("invoice_flow_number");
        
        redisJson.put("FPQQLSH", invoice_flow_number);
        redisJson.put("tenancy_id", elecInfo.optString("tenancy_id"));
        //设置发票流水号与商户的对应关系到redis中
        redisTemplate.opsForHash().put(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE, redisJson.optString("FPQQLSH"), redisJson.toString());
        //设置期限为24小时
        redisTemplate.expire(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE, ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE_EXPIRE, TimeUnit.HOURS);

        /*JSONArray reqJsonArray = new JSONArray();
        reqJsonArray.add(reqJson);*/
        GsonBuilder builder = new GsonBuilder();
		String reqstr = builder.create().toJson(reqJson);
        //调用票通发票红冲请求
        JSONObject redJo = PiaoTongElectronicInvoiceWebUtils.red(elecInfo.optString("tenancy_id"),elecInfo.optInt("organ_id"),reqstr);
        logger.info("访问票通电子发票，返回的内容为");
		logger.info(redJo.toString());
		String code = redJo.optString("code");
		String msg = redJo.optString("msg");
		//操作成功
		if(StringUtils.equals(code, "0000")) {
			//通过回调URL 更新开票情况
			hcfpkjReturnUpdate(param,reqJson,redJo);
		}
		result.put("code", code);
		result.put("msg", msg);
	}
	
	/**
	 * 
	 * @param param
	 * @param returnContent
	 * @throws Exception
	 * 红冲完成，更新状态
	 * <AUTHOR>
	 */
	private void hcfpkjReturnUpdate(JSONObject param,JSONObject reqJson,JSONObject returnContent) throws Exception {
		JSONObject elecInfo = param.optJSONObject("elecInfo");

        String tenancyId = elecInfo.optString("tenancy_id");

        //红冲后修改原订单的状态和取消开票时间
        elecInfo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS);
        elecInfo.put("invoice_cancel_time",ElectronicInvoiceUtils.currentTime2Str());
        this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", elecInfo);

//        String invoiceReqSerialNo=reqJson.optString("invoiceReqSerialNo");
        //TODO 根据invoiceReqSerialNo 获取 invoice_flow_number
        String fplsh=elecInfo.optString("invoice_flow_number");
       
        JSONObject elecInfoDetail = this.elecInvoiceDao.queryDetailsByFlowNumber(tenancyId,elecInfo.optInt("id"));
        JSONObject hcelecInfo = this.elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fplsh);
        hcelecInfo.put("pt_invoicereqserialno", reqJson.optString("invoiceReqSerialNo"));	//红冲成功，替换蓝票发票请求流水号为红票发票请求流水号
		if(hcelecInfo==null) {
			hcelecInfo = elecInfo;
			hcelecInfo.remove("id");
		}
		//增加红冲记录
		hcelecInfo.put("original_invoice_number", elecInfo.optString("invoice_number"));
		hcelecInfo.put("original_invoice_code", elecInfo.optString("invoice_code"));
		hcelecInfo.remove("invoice_cancel_time");
		hcelecInfo.remove("invoice_time");

		hcelecInfo.put("total_tax_amount", "-"+elecInfo.optString("total_tax_amount"));
		hcelecInfo.put("tax_amount", "-"+elecInfo.optString("tax_amount"));
		hcelecInfo.put("total_amount", "-"+elecInfo.optString("total_amount"));
		hcelecInfo.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_HP);
		hcelecInfo.put("invoice_flow_number", fplsh);
		
		if(StringUtils.isBlank(hcelecInfo.optString("id"))) {
			int insertIgnorCase = (int) this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_info", hcelecInfo);
			JSONObject details = new JSONObject();
			details.put("electronic_id", insertIgnorCase);
			details.put("invoice_type",  ElectronicInvoiceConst.ELECTRIC_KPLX_HP);
			details.put("name",  elecInfoDetail.optString("name"));
			details.put("tax_rate",  elecInfo.optString("tax_rate"));
			this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_details", details);
		} else {
			this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", hcelecInfo);
		}
	}
	
	/**
	 * 开票状态查询服务
	 * <AUTHOR>
	 */
	@Override
	public void queryElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		boolean validate = validate(param, result, "FPQQLSH");
		if(!validate) {
			return;
		}
		
		JSONObject json = elecInvoiceDao.getRequestInfo(data.getTenancy_id(),data.getStore_id());
		if(json==null) {
			result.setMsg("总部未配置纳税人识别号，税率等信息");
			return;
		}
		ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param);
		
		JSONObject fpInfo = this.elecInvoiceDao.queryInfoByFlowNumber(data.getTenancy_id(),param.optString("FPQQLSH"));
		if(fpInfo==null){
			 result.setMsg("没有该条记录");
	         return;
		}
		
		
		JSONObject reqParam = new JSONObject();
        reqParam.put("taxpayerNum", param.optString("XSF_NSRSBH"));//销售方纳税人识别号
        reqParam.put("tradeNo", param.optString("FPQQLSH"));//交易单号(发票请求流水号)
        reqParam.put("invoiceAmount", fpInfo.optDouble("total_tax_amount",0.00));//发票金额(含税)
        //调用查询二维码开票信息接口
        logger.info("调用票通查询二维码开票信息接口业务报文"+reqParam.toString());
		JSONObject respResult = PiaoTongElectronicInvoiceWebUtils.queryInvoiceQrStatus(data.getTenancy_id(),data.getStore_id(),reqParam.toString());
		
		if(!StringUtils.equals(respResult.optString("code"),"0000")) {
            result.setMsg("第三方没有该条记录");
            return;
        }
        
        String content=respResult.optString("content");
//        JSONObject returnJson=JSONObject.fromObject(content);
        com.alibaba.fastjson.JSONObject alijson = com.alibaba.fastjson.JSONObject.parseObject(content);
        JSONObject returnJson = ElectronicInvoiceUtils.convertAli2netJson(alijson);
        JSONArray itemList = returnJson.getJSONArray("itemList");
        JSONObject obj=itemList.getJSONObject(0);
        String invoiceReqSerialNo=obj.optString("invoiceReqSerialNo");	//获取发票请求流水号  如果为空则状态为待开票，不为空状态为已开票
        
        if(!StringUtil.isNullOrEmpty(invoiceReqSerialNo)){		//已开票
        	JSONObject queryInfoByFlowNumber = elecInvoiceDao.queryInfoByFlowNumber(data.getTenancy_id(),param.optString("FPQQLSH"));
        	queryInfoByFlowNumber.put("pt_invoicereqserialno", invoiceReqSerialNo);
            dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByFlowNumber);
            
            //根据票通返回的发票请求流水号调用查询发票接口判断开票状态
            JSONObject reqJson = new JSONObject();
            reqJson.put("taxpayerNum",queryInfoByFlowNumber.optString("tax"));					//纳税人识别号
            reqJson.put("invoiceReqSerialNo",invoiceReqSerialNo);							//票通系统发票请求流水号
            GsonBuilder builder = new GsonBuilder();
            String reqStr = builder.create().toJson(reqJson);
            logger.info("调用票通查询发票状态接口业务报文"+reqStr);
            JSONObject returnObj = PiaoTongElectronicInvoiceWebUtils.queryInvoiceStatus(data.getTenancy_id(), data.getStore_id(),reqStr);
            if(returnObj==null) {
                result.setSuccess(Boolean.FALSE);
                result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_CONNECT);
                result.setMsg("连接超时");
                return;
            }
            String code = returnObj.optString("code");
            String msg = returnObj.optString("msg");
            if(StringUtils.equals("0000",code)) {
                JSONObject returnContent=JSONObject.fromObject(returnObj.optString("content"));
               
                List<JSONObject> returnList = new ArrayList<JSONObject>();
        		JSONObject resultJson = new JSONObject();
        		resultJson.put("code", returnContent.optString("code"));
        		resultJson.put("msg", returnContent.optString("msg"));
        		returnList.add(respResult);
        		result.setData(returnList);
        		result.setSuccess(Boolean.TRUE);
        		result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
            }

        }else{													//待开票
    		List<JSONObject> returnList = new ArrayList<JSONObject>();
    		JSONObject resultJson = new JSONObject();
    		resultJson.put("code", respResult.optString("code"));
    		resultJson.put("msg", "二维码待开票");
    		returnList.add(respResult);
    		result.setData(returnList);
    		result.setSuccess(Boolean.TRUE);
    		result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
        }
		
	}

	@Override
	public void sqkp(JSONObject json, JSONObject result) throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public void fpkj(JSONObject json, JSONObject result) throws Exception {
		//..
	}
	
	/**
	 * 获取redis发票流水号所对应的数据
	 * @param fplsh
	 * @return
	 */
	private JSONObject getRedisJson(String fplsh) {
		if(!redisTemplate.hasKey(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE)) {
			logger.info("redis中没有电子发票"+ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE+"所对应的键");
		}
		if(!redisTemplate.opsForHash().hasKey(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE, fplsh)) {
			logger.info("redis中没有电子发票"+fplsh+"所对应的键");
		}
		String redisJsonStr = (String) redisTemplate.opsForHash().get(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE, fplsh);
		com.alibaba.fastjson.JSONObject alijson = com.alibaba.fastjson.JSONObject.parseObject(redisJsonStr);
		JSONObject redisJson = ElectronicInvoiceUtils.convertAli2netJson(alijson);
		return redisJson;
	}
	

	/**
	 * 验证传进的参数格式是否正确
	 * 
	 * @param param
	 * @param result
	 * @return
	 * <AUTHOR>
	 */
	public boolean validateParam(JSONObject param, JSONObject result,
			String... columns) {
		for (String column : columns) {
			if (!param.containsKey(column)
					|| StringUtils.isBlank(param.getString(column))) {
				result.put("msg","参数：" + column + "不允许为空");
				result.put("code",Contant.ILLEGAL_PARAM);
				logger.info("参数：" + column + "不允许为空");
				return false;
			}
		}
		return true;
	}
	
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#orderCallback(java.lang.String, net.sf.json.JSONObject)
	 * 开票成功回调方法
	 * <AUTHOR>
	 */
	@Override
	public void orderCallback(String jsonParam, JSONObject result) throws Exception {
        com.alibaba.fastjson.JSONObject alijson = com.alibaba.fastjson.JSONObject
                .parseObject(jsonParam);
        if(alijson==null) {
            return;
        }
        if(alijson.containsKey("invoicePdf")) {
            alijson.remove("invoicePdf");
        }
        JSONObject jsonObject = ElectronicInvoiceUtils.convertAli2netJson(alijson);
        String code = jsonObject.optString("code");
        String msg = jsonObject.optString("msg");
        String invoiceReqSerialNo = jsonObject.optString("invoiceReqSerialNo");	//票通的发票请求流水号
        String tradeNo=jsonObject.optString("tradeNo");							//交易单号fpqqlsh
        String fpqqlsh="";
        if(result!=null){
        	//TODO 根据票通发票流水号获取saas存储的发票请求流水号
            JSONObject json = elecInvoiceDao.queryInfoByReqserialno(result.optString("tenancy_id"),invoiceReqSerialNo);
            fpqqlsh=json.optString("invoice_flow_number");
        }else{
        	fpqqlsh=tradeNo;
        }
        logger.info("票通电子发票回调结果:fpqqlsh="+fpqqlsh+"@code="+code+"@msg="+msg);
        JSONObject redisJson = getRedisJson(fpqqlsh);
        String tenancyId = redisJson.optString("tenancy_id");
        DBContextHolder.setTenancyid(tenancyId);
        JSONObject saveJson = elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fpqqlsh);
        
        if(StringUtil.isNullOrEmpty(code)){			//如果code 为空，推送过来的为开票信息（开票状态未知，只包含开票的一些基本信息） 票通提供两个推送接口
        	logger.info("接收票通推送的开票信息,发票请求流水号为"+fpqqlsh);
        	fillElecInfo2(jsonObject,saveJson);
        	this.dao.updateIgnorCase(tenancyId,"hq_electronic_invoice_info",saveJson);
        }else{										//不为空 ，推送过来的为发票信息（开票状态已知）
            if(!StringUtils.equals("0000",code)) {
                logger.info("票通电子发票开票失败，发票请求流水号为"+fpqqlsh);
            }
            fillElecInfo(jsonObject,saveJson);
            if(StringUtils.equals(saveJson.optString("invoice_state"),ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT)) {//取消失败也要加上，因为有可能之前操作过取消并失败了
                saveJson.put("invoice_state",ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS);
                saveJson.put("invoice_type",ElectronicInvoiceConst.ELECTRIC_KPLX_LP);
            }
    		if(result!=null) {
    			result.put("elecInfo",saveJson);
    		}
    		ElectronicInvoiceUtils.uploadOm(saveJson);
            this.dao.updateIgnorCase(tenancyId,"hq_electronic_invoice_info",saveJson);
        }
    }




    /**
	 * 获取到Data对象中的data  josn
	 * @param data
	 * @return
	 * <AUTHOR>
	 */
     public void fillElecInfo(JSONObject data, JSONObject saveJson) throws ParseException {
        saveJson.put("invoice_number",data.optString("invoiceNo"));		//发票号码
        saveJson.put("invoice_code",data.optString("invoiceCode"));		//发票代码
        saveJson.put("jym",data.optString("securityCode"));				//TODO 校验码   待确认 对应是否为票通防伪码
        saveJson.put("invoice_time",data.optString("invoiceDate"));		//开票日期
        saveJson.put("buyer_tax",data.optString("taxpayerNum"));		//购买方纳税人识别号
        saveJson.put("total_amount",data.optString("noTaxAmount"));		//合计金额
        saveJson.put("tax_amount",data.optString("taxAmount"));			//合计税额
        BigDecimal noTaxAmount =new BigDecimal(data.optString("noTaxAmount"));
        BigDecimal tax_amount =new BigDecimal(data.optString("taxAmount"));
        double total_tax_amount =noTaxAmount.add(tax_amount).doubleValue();
        saveJson.put("total_tax_amount",total_tax_amount);				//价税合计     
    }
     
     /**
 	 * 获取到Data对象中的data  josn
 	 * @param data
 	 * @return
 	 * <AUTHOR>
 	 */
      public void fillElecInfo2(JSONObject data, JSONObject saveJson) throws ParseException {
       saveJson.put("buyer_address",data.optString("buyerAddress"));	//购买方地址
       saveJson.put("buyer_name",data.optString("buyerName"));			//购买方名称 发票抬头
       saveJson.put("bank_no",data.optString("buyerBankAccount"));		//购买方银行号
       saveJson.put("drawer","系统");									//开票人
     }



	public JSONObject getParam(Data data) {
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		if(list==null || list.isEmpty()) {
			return null;
		}
		// 获取到传入的参数
		JSONObject param = JSONObject.fromObject(list.get(0));
		return param;
	}
	
	private boolean validReturn(JSONObject returnjson, JSONObject result) {
		if(StringUtils.isBlank(returnjson.optString("fpqqlsh"))) {
			result.put("code", 0000);
			result.put("msg", "fpqqlsh 不能为空 ");
			return false;
		}
		
		return true;
	}
	
	//生成红冲发票请求流水号（4位平台简称pt**+16位随机数）
	private String getHCinvoiceReqSerialNo(){
		StringBuilder sb=new StringBuilder();
		String prefix =PiaoTongConfigCache.getElementText("prefix");						//平台前缀(简称)
        Random rand=new Random();
        for(int i=0;i<16;i++)
        {
            sb.append(rand.nextInt(10));
        }
        return prefix+sb.toString();
	}
}
