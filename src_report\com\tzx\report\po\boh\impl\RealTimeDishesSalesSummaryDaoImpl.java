package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.RealTimeDishesSalesSummaryDao;

@Repository(RealTimeDishesSalesSummaryDao.NAME)
public class RealTimeDishesSalesSummaryDaoImpl implements RealTimeDishesSalesSummaryDao{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
 
	@Override
	public JSONObject getRealTimeDishesSalesSummary(String tenancyID,JSONObject condition) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		if(condition.containsKey("exportdataexpr")&& !condition.optString("exportdataexpr").equals("''")){
			String exp = condition.optString("exportdataexpr");
			condition.element("exportdataexpr", conditionUtils.spilt(exp.substring(1, exp.length()-1)));
		}
		String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_FOOD_SALES_REAL_TIME_REPORT);
		
		if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
			list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
			structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
		}else{
			total = this.dao.countSql(tenancyID,reportSql.toString());
			list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
			footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TOTAL_FOOD_SALES_REAL_TIME_REPORT));
		}
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}
}
