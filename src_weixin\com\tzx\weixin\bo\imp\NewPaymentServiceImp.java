package com.tzx.weixin.bo.imp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.payment.bo.NewWechatService;
import com.tzx.payment.common.constant.TradeStatusConstants;
import com.tzx.payment.news.service.PaymentService;
import com.tzx.payment.po.springjdbc.dao.AlipayConfigDao;
import com.tzx.payment.po.springjdbc.dao.AlipayPaymentOrderDao;
import com.tzx.payment.wechat.common.util.PayUtil;
import com.tzx.payment.wechat.common.util.StringUtils;
import com.tzx.task.po.redis.dao.TaskRedisDao;
import com.tzx.weixin.bo.ElectronicInvoiceService;
import com.tzx.weixin.bo.NewPaymentService;
import com.tzx.weixin.common.model.Data;
import com.tzx.weixin.common.util.FromRedisUtil;
import com.tzx.weixin.common.util.MyUtil;
import com.tzx.weixin.common.util.WXConfig;
import com.tzx.weixin.po.springjdbc.dao.NewWxSystemParameterDao;

import org.apache.log4j.Logger;
@Service(NewPaymentService.NAME)
public class NewPaymentServiceImp implements NewPaymentService
{
	private static final Logger	logger	= Logger.getLogger(NewPaymentServiceImp.class);
	
	@Autowired
	private AlipayConfigDao alipayDao;
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	@Autowired
	private AlipayPaymentOrderDao orderDao;
	@Resource(name=NewWechatService.NAME)
	private NewWechatService newWechatService;
	@Resource(name = "com.tzx.payment.news.service.impl.WechatPaymentServiceImpl")
	private PaymentService wechatPaymentServiceImpl;
	@Resource(name = "com.tzx.payment.news.service.impl.DuoLaBaoPaymentServiceImpl")
	private PaymentService duoLaBaoPaymentServiceImpl;
	@Resource(name = TaskRedisDao.NAME)
	private TaskRedisDao		taskRedisDao;
	@Resource(name=NewWxSystemParameterDao.NAME)
	private NewWxSystemParameterDao newWxSystemParameterDao;
	@Resource(name=ElectronicInvoiceService.NAME)
	private ElectronicInvoiceService electronicInvoiceService;
	
	@Override
	public JSONObject getPayMchJs(Data param,String remoteIp,String order_num) throws Exception
	{
		JSONObject result = new JSONObject();
		result.put("success", false);
		//发送参数在data。json中获取
		JSONObject elementJson = param.getJson();
		
		//验证请求参数
		if(elementJson==null || elementJson.size()<=0 || !valiateParam(elementJson)){
			result.put("err_msg", "请求参数发生错误");
			return result;
		}
		//验证会员卡是否有效
		String sql = "select card_state from crm_customer_card where card_code='"+elementJson.getString("card_code")+"'";
		List<JSONObject> cardList = orderDao.queryString4Json(param.getTenantId(), sql);
		if(cardList==null || cardList.size()<=0 || !"1".equals(cardList.get(0).optString("card_state"))){
			result.put("err_msg", "该会员卡状态异常，无法进行充值操作");
			return result;
		}
		//获取公众号对应的门店id
		JSONObject wxconfig = WXConfig.getConfigBean(param.getTenantId());
		if(wxconfig==null){
			result.put("err_msg", "获取微信公众号配置有误");
			return result;
		}
		
		param.setOrganId("0");//会员卡充值暂且默认为总部
		//获取参数,如果存在store_id，按store_id的内容查询支付配置信息
		/*
		//如果没有设置参数配置，则取该机构下最早建立的值
		JSONObject config = alipayDao.getWechat4JsPay(param.getTenantId(),TradeStatusConstants.ORDER_PAYTYPE_WECHAT,elementJson.optString("store_id"));
		if(config==null|| config.size()<=0){
			result.put("err_msg", "获取微信公众号支付配置有误");
			return result;
		}
		param.setOrganId(config.getString("store_id"));
		//生成订单信息
		com.tzx.framework.common.entity.Data data = new com.tzx.framework.common.entity.Data();
		data.setTenancy_id(param.getTenantId());
		data.setStore_id(Integer.parseInt(param.getOrganId()));
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject bean = new JSONObject();
		bean.put("bill_num",order_num);
		bean.put("total_amount",elementJson.getString("total_amount"));
		bean.put("subject", elementJson.getString("subject"));
		bean.put("remote_ip", remoteIp);
		bean.put("trade_type", "JSAPI");
		bean.put("device_info", "wechat_jssdk");
		bean.put("mac_openid", elementJson.optString("mac_openid"));
		bean.put("operator_id", param.getOpenId());
		bean.put("terminal_id", elementJson.getString("card_code"));
		//支付来源
		bean.put("pay_source", TradeStatusConstants.PAY_SOURCE_WECHAT_CARD_RECHANGE);
		*/
		String paystate="0";
		String sql2="select para_value from sys_parameter where system_name = 'WECHAT' and para_code = 'wechat_payment_way' and store_id='0'";
		List<JSONObject> sysparameter = orderDao.queryString4Json(param.getTenantId(), sql2);
		if(sysparameter==null || sysparameter.size()<=0){
			paystate="0";
		}else if("1".equals(sysparameter.get(0).optString("para_value"))) {
			paystate="1";
		}
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject bean = new JSONObject();
		com.tzx.framework.common.entity.Data param1=new com.tzx.framework.common.entity.Data();
		com.tzx.framework.common.entity.Data result1=new com.tzx.framework.common.entity.Data();
		param1.setTenancy_id(param.getTenantId());
		param1.setStore_id(Integer.parseInt(param.getOrganId()));
		param1.setType(Type.GET_PREPAY_BARCODE);
		bean.put("order_no", order_num);//订单号
		bean.put("service_type", "hy01");//多渠道支付，会员充值
		bean.put("pos_num", "WEB");//线上下单没有该号，默认为WEB
		bean.put("opt_num", "线上点餐");//线上没有
		bean.put("type", "GET_PREPAY_BARCODE");
		if(paystate.equals("1")){
			bean.put("pay_type","dlb_pay");
		}else{
			bean.put("pay_type","wechat_pay");
		}
		bean.put("amount", elementJson.getString("total_amount"));//订单金额
		bean.put("client_ip", remoteIp);//客户端IP
		bean.put("currency", "CNY");//默认金额单位为人名币
		bean.put("body", elementJson.getString("subject"));//商品描述
		JSONObject extra = new JSONObject();
		String notifyUrl = Constant.systemMap.get("wechat_notify_url");
		notifyUrl += "/"+param.getTenantId();
		notifyUrl += "/"+TradeStatusConstants.PAY_SOURCE_WECHAT_CARD_RECHANGE;
		
		
		//调用统一下单接口，获取预支付交易会话标识prepay_id
		
		if(paystate.equals("1")){
			JSONObject wechatOb = new JSONObject();
			wechatOb.put("notify_url", notifyUrl);
			wechatOb.put("trade_type", "JSAPI");
			extra.put("all_pay", wechatOb);
			bean.put("extra", extra);
			list.add(bean);
			param1.setData(list);
			
			duoLaBaoPaymentServiceImpl.getPrepayBarcode(param1, result1);
		}else{
			JSONObject wechatOb = new JSONObject();
			wechatOb.put("notify_url", notifyUrl);
			wechatOb.put("openid",elementJson.get("mac_openid"));
			wechatOb.put("trade_type", "JSAPI");
			extra.put("all_pay", wechatOb);
			bean.put("extra", extra);
			list.add(bean);
			param1.setData(list);
		    wechatPaymentServiceImpl.getPrepayBarcode(param1, result1);
		}
		if(result1.isSuccess()==false){
			result.put("err_msg", "网络请求出错");
			return result;
		}
		JSONObject response=(JSONObject)result1.getData().get(0);
		//logger.info(JSONObject.fromObject(result1).toString());
		if(response.containsKey("prepay_id") && MyUtil.checkEmpty(response.getString("prepay_id"))){
			//生成json串 供js初始化使用
			String prepay_id = response.getString("prepay_id");
			String mac_appid = Constant.getSystemMap().get("wechat_service_mch_appid");
			String mac_api_secret = Constant.getSystemMap().get("wechat_service_mch_api_secert");
			String json = PayUtil.generateMchPayJsRequestJson(prepay_id, mac_appid, mac_api_secret);
			//total_amount=response.getString("total_amount");
			result = JSONObject.fromObject(json);
			result.put("success", true);
			result.put("bill_num", order_num);
			result.put("pay_type", paystate);
			//将生成好的订单放入redis中
//			List<JSONObject> queryWXSystemParameterByParaCode = newWxSystemParameterDao.queryWXSystemParameterByParaCode(param.getTenantId(), "whether_wechat_card_menue");
//			if(queryWXSystemParameterByParaCode.size()>0){
//				JSONObject jsonObject = queryWXSystemParameterByParaCode.get(0);
//				int para_value = jsonObject.optInt("para_value");
//				if(para_value==1){
					JSONObject bean1=new JSONObject();
					bean1.put("activation_store", elementJson.getString("activation_store"));
					bean1.put("card_code", elementJson.getString("card_code"));
					bean1.put("order_code", order_num);
					bean1.put("income", elementJson.getString("total_amount"));
					bean1.put("tenantId", param.getTenantId());
					bean1.put("type1", "card");//卡充值
					bean1.put("oper", 1);
					//FromRedisUtil.setBillNum(bean1);
					taskRedisDao.lpush("wxpaymentbillnum".getBytes(), bean1);
//				}
//			}
			//插入会员记录表
//			JSONObject order=new JSONObject();
//			order.put("order_num", order_num);
//			order.put("pay_state", "0");
//			order.put("chanel", "WX02");
//			order.put("tenancy_id", param.getTenantId());
//			order.put("request_time", DateUtil.getNowDateYYDDMMHHMMSS());
//			order.put("card_code", elementJson.getString("card_code"));
//			order.put("store_id", 0);
//			order.put("income", elementJson.getString("total_amount"));
//			
//			dao.insertIgnorCase(param.getTenantId(), "wx_member_card_rechargecard", order);
		}else{
			result.putAll(response);
		}
		
		StringBuffer sqlb = new StringBuffer();
		
		sqlb.append("INSERT INTO wx_member_card_rechargecard (");
		sqlb.append("	store_id, tenancy_id, order_num, pay_state, chanel, request_time, card_code, income,need_invoice ) VALUES (");
		sqlb.append("'");sqlb.append("0");sqlb.append("',");
		sqlb.append("'");sqlb.append(param.getTenantId());sqlb.append("',");
		sqlb.append("'");sqlb.append(order_num);sqlb.append("',");
		sqlb.append("'");sqlb.append( "0");sqlb.append("',");
		sqlb.append("'");sqlb.append("WX02");sqlb.append("',");
		sqlb.append("'");sqlb.append(DateUtil.getNowDateYYDDMMHHMMSS());sqlb.append("',");
		sqlb.append("'");sqlb.append(elementJson.getString("card_code"));sqlb.append("',");
		sqlb.append("'");sqlb.append(elementJson.getString("total_amount"));sqlb.append("',");
		sqlb.append("'");sqlb.append(elementJson.optInt("need_invoice"));sqlb.append("'");
		sqlb.append(");");
		dao.execute(param.getTenantId(), sqlb.toString());
		
		/*
		logger.info(response.toJSONString());
		
		if(response.containsKey("prepay_id") && MyUtil.checkEmpty(response.getString("prepay_id"))){
			//生成json串 供js初始化使用
			String prepay_id = response.getString("prepay_id");
			String mac_appid = Constant.getSystemMap().get("wechat_service_mch_appid");
			String mac_api_secret = Constant.getSystemMap().get("wechat_service_mch_api_secert");
			String json = PayUtil.generateMchPayJsRequestJson(prepay_id, mac_appid, mac_api_secret);
			result = JSONObject.fromObject(json);
			result.put("success", true);
			result.put("bill_num", order_num);
		}else{
			result.putAll(response);
		}*/
		
		return result;
	}
	
	public boolean valiateParam(JSONObject elementJson){
		boolean flag = false;
		if(StringUtils.isNotBlankOrEmpty(elementJson.getString("card_code"))
				&&StringUtils.isNotBlankOrEmpty(elementJson.getString("income"))
				){
			flag = true;
		}
		return flag;
	}

	@Override
	public JSONObject queryAndChange(Data param) throws Exception
	{
		
		JSONObject response = new JSONObject();
		response.put("success", false);
		//获取公众号对应的门店id
		JSONObject wxconfig = WXConfig.getConfigBean(param.getTenantId());
		if(wxconfig==null){
			response.put("err_msg", "获取微信公众号配置有误");
			return response;
		}
		//发送参数在data。json中获取
		JSONObject elementJson = param.getJson();
		JSONObject config = alipayDao.getWechat4JsPay(param.getTenantId(),TradeStatusConstants.ORDER_PAYTYPE_WECHAT,elementJson.optString("store_id"));
		if(config==null || config.size()<=0){
			response.put("err_msg", "获取微信公众号支付配置有误");
			return response;
		}
		//查询订单记录表，如果已经订单的状态已经修改为已支付状态
		String query_num = param.getTenantId()+"_0_"+param.getRequestStr();
		String sql = "select status from pos_payment_order where order_num='"+query_num+"' order by final_state asc,id desc";
		List<JSONObject> tempList = alipayDao.queryString4Json(param.getTenantId(), sql);
		if(tempList!=null && tempList.size()>0){
			if(tempList.get(0).optInt("status") == 1){
				response.put("success", true);
				return response;
			}
		}
		//查询退款的单子
		String sql1="select * from pos_payment_refund where order_num='"+query_num+"'";
		List<JSONObject> tempList1 = dao.query4Json(param.getTenantId(), sql);
		if(tempList1.size()>0){
			response.put("success", true);
			response.put("err_msg", "已经退款成功");
			return response;
		}
		//查询订单状态
		com.tzx.framework.common.entity.Data data = new com.tzx.framework.common.entity.Data();
		data.setTenancy_id(param.getTenantId());
		data.setStore_id(Integer.parseInt(param.getOrganId()));
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject bean = new JSONObject();
		bean.put("bill_num", param.getRequestStr());
		bean.put("polling_flag", false);
		list.add(bean);
		data.setData(list);
		param.setOrganId(config.getString("store_id"));
		com.alibaba.fastjson.JSONObject result =  newWechatService.orderquery(data);
		//logger.info("微信查询："+result);
		if(result == null){
			response.put("err_msg", "网络请求发生错误");
			return response;
		}
		//logger.info("wechat jssdk payment :"+result.toJSONString());
		response.putAll(result);
		if(response.optBoolean("success")&&"finished".equals(response.optString("status"))){
			response.put("success", false);
			//订单状态为成功，进行卡片充值
			JSONObject paramJson = param.getJson();
			Map<String, Object> paramChange = new HashMap<String, Object>();
			paramChange.put("secret", "0");
			paramChange.put("type", "CUSTOMER_CARD_RECHARGE");
			paramChange.put("oper", "add");
			paramChange.put("tenancy_id", param.getTenantId());
			paramChange.put("store_id", param.getOrganId());
			
			Map<String, String> dataParamsChange = new HashMap<String, String>();
			dataParamsChange.put("card_code", paramJson.getString("card_code"));
			dataParamsChange.put("income", paramJson.getString("income"));
			dataParamsChange.put("bill_code", paramJson.getString("bill_code"));
			dataParamsChange.put("chanel", "WX02");
			StringBuilder sb=new StringBuilder();
//			sb.append("select id from payment_way where payment_class='wechat_pay' and status='1'");
			sb.append("select cc.* from (select id,(select count(pwoo.*)");
			sb.append(" from payment_way_of_ogran pwoo where pwoo.organ_id="+config.getString("store_id")+")");
			sb.append(" as z from payment_way where payment_class='wechat_pay' and status='1') cc where cc.z>0 ");
			List<JSONObject> query4Json = orderDao.query4Json(param.getTenantId(), sb.toString());
			if(query4Json.size()>0){
				dataParamsChange.put("payment", query4Json.get(0).optString("id"));//TODO 付款方式id 需要修改
			}else{
				dataParamsChange.put("payment", "");//TODO 付款方式id 需要修改
			}
//			dataParamsChange.put("payment", "124");//TODO 付款方式id 需要修改
			dataParamsChange.put("pay_no", response.getString("trade_no"));
			dataParamsChange.put("operator", "admin");
			dataParamsChange.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
			
			JSONArray dataChange = new JSONArray();
			dataChange.add(dataParamsChange);
			paramChange.put("data", dataChange);
			String tenantIdOrderCode=param.getTenantId()+""+param.getRequestStr();
			Boolean hasOrderNum = FromRedisUtil.hasOrderNum(tenantIdOrderCode);
			if(hasOrderNum){
				response.put("success", true);
				return response;
			}
			FromRedisUtil.setOrderNum(tenantIdOrderCode);
			String resultString = HttpUtil.sendPostRequest(Constant.systemMap.get("product_wechat_scmip"), JSON.toJSONString(paramChange));
			//logger.info("wechat change the money of the vip card :"+resultString);
			if(resultString!=null){
				JSONObject returnJson = new JSONObject();
				try {
					returnJson = JSONObject.fromObject(resultString);
					String msg = returnJson.optString("msg");
					String code =  returnJson.optString("code");
					if("0".equals(code))
					{
						response.put("success", true);
					}
					else
					{
						JSONObject refundJson = this.refundOrder(param.getTenantId(), param.getOrganId(), param.getRequestStr(), response.optDouble("total_amount"));
						response.put("refund_success", refundJson.optBoolean("success"));
						if(msg.length()==0)
						{
							response.put("refund_err_msg", refundJson.optString("err_msg"));
							response.put("err_msg","调用充值接口失败");
						}
						else
						{
							response.put("refund_err_msg", refundJson.optString("err_msg"));
							response.put("err_msg", returnJson.optString("msg"));
						}
						
					}
					
				} catch (Exception e) {
					JSONObject refundJson = this.refundOrder(param.getTenantId(), param.getOrganId(), param.getRequestStr(), response.optDouble("total_amount"));
					response.put("refund_success", refundJson.optBoolean("success"));
					response.put("refund_err_msg", refundJson.optString("err_msg"));
				}
				
			}
		}
		return response;
	}

	@Override
	public JSONObject queryOrderStatus(String tenancyId,String billNum,String storeId) throws Exception
	{
		return this.queryOrderStatus(tenancyId, billNum, storeId, null);
	}
	
	@Override
	public JSONObject queryOrderStatus(String tenancyId,String billNum,String storeId,String subMchId) throws Exception
	{
		JSONObject response = new JSONObject();
		response.put("success", false);
		//获取公众号对应的门店id
		StringBuilder sb=new StringBuilder();
		JSONObject wxconfig = null;
		List<JSONObject> query4Json=null;
		Boolean wechatAuthorize = FromRedisUtil.getWechatAuthorize(tenancyId);
		if(!wechatAuthorize){
			//第三方授权
			sb.append("select * from wx_authorizer_account");
			query4Json = dao.query4Json(tenancyId, sb.toString());
		}else{
			//普通公众号授权
			sb.append("select * from wx_config ");
			query4Json = dao.query4Json(tenancyId, sb.toString());
		}
		wxconfig=query4Json.size()>0?query4Json.get(0):null;
		
		if(wxconfig==null){
			response.put("err_msg", "获取微信公众号配置有误");
			return response;
		}
		//发送参数在data。json中获取
		/*
		sb.delete(0, sb.length());
		sb.append("select * from sys_payment_account_config");
		sb.append(" where tenancy_id = '"+tenancyId+"' and type="+TradeStatusConstants.ORDER_PAYTYPE_WECHAT+" and valid_state='1'");
		
//		if(!storeId.equals("null")){
//			sb.append(" and store_id="+storeId);
//		}
		sb.append(" order by id");
		List<JSONObject> query4Json2 = dao.query4Json(tenancyId, sb.toString());
		JSONObject config=query4Json2.size()>0?query4Json2.get(0):null;
		if(config==null|| config.size()<=0){
			response.put("err_msg", "获取微信公众号支付配置有误");
			return response;
		}
//		if(!MyUtil.checkEmpty(storeId)){
//			
//		}*/
		//storeId = config.getString("store_id");
		//查询订单状态
		com.tzx.framework.common.entity.Data param = new com.tzx.framework.common.entity.Data();
		com.tzx.framework.common.entity.Data result1 = new com.tzx.framework.common.entity.Data();

		param.setTenancy_id(tenancyId);
		if(null==storeId){
			param.setStore_id(0);
		}else{
			param.setStore_id(Integer.parseInt(storeId));
		}
		
		param.setType(Type.QUERY_PAY_STATE);
		//查询支付账单业务类型
		String paymentordernumm=tenancyId+"_"+storeId+"_"+billNum;
		String service_type="wd03";
		sb.delete(0, sb.length());
		sb.append("select * from pos_payment_order where order_num like '%"+billNum+"' order by final_state asc,id desc");
		List<JSONObject> query4Json2 = dao.query4Json(tenancyId, sb.toString());
		JSONObject config=query4Json2.size()>0?query4Json2.get(0):null;
		if(config==null|| config.size()<=0){
			response.put("err_msg", "没有查到该订单");
			return response;
		}else{
			service_type=config.getString("service_type");
		}
		
		String paystate="0";
		String sql2="select para_value from sys_parameter where system_name = 'WECHAT' and para_code = 'wechat_payment_way' and store_id='0'";
		List<JSONObject> sysparameter = orderDao.queryString4Json(tenancyId, sql2);
		if(sysparameter==null || sysparameter.size()<=0){
			paystate="0";
		}else if("1".equals(sysparameter.get(0).optString("para_value"))) {
			paystate="1";
		}
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject bean = new JSONObject();
		
		bean.put("order_no", billNum);
		bean.put("service_type", service_type);
		bean.put("pos_num", "WEB");//线上下单没有该号，默认为WEB
		bean.put("opt_num", "线上点餐");//线上没有
		bean.put("polling_flag", false);
		if(paystate.equals("1")){
			bean.put("pay_type","dlb_pay");
		}else{
			bean.put("pay_type","wechat_pay");
		}
		list.add(bean);
		param.setData(list);
		
		if(paystate.equals("1")){
			duoLaBaoPaymentServiceImpl.queryPayState(param, result1);
		}else{
			wechatPaymentServiceImpl.queryPayState(param, result1);
		}
		
		//com.alibaba.fastjson.JSONObject result =  newWechatService.orderquery(param);
		//logger.info("wechat jssdk payment :"+JSONObject.fromObject(result1).toString());
//		if(result1.isSuccess()==false){
//			response.put("err_msg", "网络请求发生错误");
//			return response;
//		}
		JSONObject object=query4Json2.get(0);
		if(object!=null&&"1".equals(object.getString("final_state"))){
		    response.put("success", true);
		    response.put("status","finished");
		    response.put("trade_no", object.getString("trade_no"));
		    response.put("total_amount", object.getString("total_amount"));
		    response.put("store_id", storeId);
		}else{
			response.put("err_msg", "支付未成功");
		}
		return response;
	}

	@Override
	public JSONObject getPayMachJsBean(String tenancyId,String storeId,String remoteIp, String order_num,
			String subject,String total_amount,String openid) throws Exception
	{
		return this.getPayMachJsBean(tenancyId, storeId, remoteIp, order_num, subject, total_amount, openid,null,null);
	}		
	
	@Override
	public JSONObject getPayMachJsBean(String tenancyId,String storeId,String remoteIp, String order_num,
			String subject,String total_amount,String openid,String subMchId,String paySource) throws Exception
	{
		
		 
		JSONObject result = new JSONObject();
		result.put("success", false);
		String paystate="0";
		String sql2="select para_value from sys_parameter where system_name = 'WECHAT' and para_code = 'wechat_payment_way' and store_id='0'";
		List<JSONObject> sysparameter = orderDao.queryString4Json(tenancyId, sql2);
		if(sysparameter==null || sysparameter.size()<=0){
			paystate="0";
		}else if("1".equals(sysparameter.get(0).optString("para_value"))) {
			paystate="1";
		}
//		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject bean = new JSONObject();
		com.tzx.framework.common.entity.Data param=new com.tzx.framework.common.entity.Data();
		com.tzx.framework.common.entity.Data result1=new com.tzx.framework.common.entity.Data();
		param.setTenancy_id(tenancyId);
		if(null==storeId){
			param.setStore_id(0);
		}else{
			param.setStore_id(Integer.parseInt(storeId));
		}
//		param.setStore_id(Integer.parseInt(storeId));
		param.setType(Type.GET_PREPAY_BARCODE);
		String service_type="wd03";
		if(TradeStatusConstants.PAY_SOURCE_WECHAT_BUY_MEMBER.equals(paySource)){
			service_type="hy01";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_BUY_PERSON.equals(paySource)){
			service_type="hy01";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_CARD_RECHANGE.equals(paySource)){
			service_type="hy01";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_DNDC.equals(paySource)){
			service_type="wd03";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_BUY_WXWM.equals(paySource)){
			service_type="wd03";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_MARKET_COUPONS.equals(paySource)){
			service_type="sc02";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_DS.equals(paySource)){
			service_type="wxds05";
		}
//		bean.put("order_no", order_num);//订单号
//		bean.put("service_type", service_type);//多渠道支付，微点餐/外卖
//		bean.put("pos_num", "WEB");//线上下单没有该号，默认为WEB
//		bean.put("opt_num", "线上点餐");//线上没有
//		bean.put("type", "GET_PREPAY_BARCODE");
		String pay_type="wechat_pay";
		if(paystate.equals("1")){
//			bean.put("pay_type","dlb_pay");
			pay_type="dlb_pay";
		}else{
//			bean.put("pay_type","wechat_pay");
			pay_type="wechat_pay";
		}
		//bean.put("pay_type", "wechatpay"); 
//		bean.put("amount", total_amount);//订单金额
//		bean.put("client_ip", remoteIp);//客户端IP
//		bean.put("currency", "CNY");//默认金额单位为人名币
//		bean.put("body", subject);//商品描述
//		bean.put("channel", "WX02");//渠道
//		bean.put("shift", "0");//班次
//		bean.put("report_date", DateUtil.getNowDateYYDDMMHHMMSS());//报表时间
//		bean.put("bill_num", order_num);
//		if(StringUtils.isNotBlankOrEmpty(paySource)){
//			notifyUrl += "/"+paySource;
//		}else{
//			notifyUrl += "/"+TradeStatusConstants.PAY_SOURCE_WECHAT_DEFAULT;
//		}

//		if(paystate.equals("1")){
//			JSONObject wechatOb = new JSONObject();
//			wechatOb.put("notify_url", notifyUrl);
//			wechatOb.put("trade_type", "JSAPI");
//			extra.put("all_pay", wechatOb);
//			bean.put("extra", extra);
//			list.add(bean);
//			param.setData(list);
//			
//			duoLaBaoPaymentServiceImpl.getPrepayBarcode(param, result1);
//		}else{
//			JSONObject wechatOb = new JSONObject();
//			wechatOb.put("notify_url", notifyUrl);
//			wechatOb.put("openid",openid);
//			wechatOb.put("trade_type", "JSAPI");
//			extra.put("all_pay", wechatOb);
//			bean.put("extra", extra);
//			list.add(bean);
//			param.setData(list);
//		    wechatPaymentServiceImpl.getPrepayBarcode(param, result1);
//		}
		//获取回调路径
	   com.tzx.framework.common.entity.Data data = new com.tzx.framework.common.entity.Data();
       data.setType(Type.GET_PREPAY_BARCODE);
       data.setTenancy_id(tenancyId);
       int store_id=0;
       if(null!=storeId&&!storeId.equals("")){
    	   store_id = Integer.valueOf(storeId);
       }
       data.setStore_id(store_id);
       List<net.sf.json.JSONObject> list = new ArrayList<net.sf.json.JSONObject>();
       net.sf.json.JSONObject object = new net.sf.json.JSONObject();
       object.put("subject", subject);
       object.put("order_no", order_num);
       object.put("pay_type", pay_type);
       object.put("pos_num", "WEB");
       object.put("opt_num", "微信卡充值");
       object.put("amount",  total_amount);
       object.put("client_ip", remoteIp);
       object.put("currency", "CNY");
       object.put("body", subject);
       object.put("description", subject);
       object.put("service_type", service_type);
       object.put("report_date",  DateUtil.getNowDateYYDDMMHHMMSS());
       object.put("shift", "0");
       object.put("channel", "WX02");
       object.put("bill_num", order_num);
       JSONObject all_pay = new JSONObject();

       all_pay.put("detail", subject);//可选
       all_pay.put("trade_type", "JSAPI");
       all_pay.put("product_id", order_num);//NATIVE必传
      //JSAPI二选一，
		all_pay.put("openid", openid);//可选
		
       JSONObject extra = new JSONObject();
       extra.put("all_pay", all_pay);

		object.put("extra", extra);

       list.add(object);
       data.setData(list);
       String temp = JSON.toJSONString(data);
       String url=com.tzx.framework.common.constant.Constant.getSystemMap().get("saas_url")+"/payment/news/post";;
       String responseStr = HttpUtil.sendPostRequest(url, temp);
	    System.out.println("预下单返回结果："+responseStr);
	    JSONObject resp = JSONObject.fromObject(responseStr);
	    if(!resp.optBoolean("success")){
	    	//预下单失败
	    	result.put("msg", resp.optString("msg"));
			return result;
	    }
	    JSONArray data_list = resp.optJSONArray("data");
	    if(data_list.size()<1){
	    	//预下单失败
	    	result.put("msg", "预下单失败");
			return result;
	    }
	    JSONObject response = data_list.optJSONObject(0);
		
		//调用统一下单接口，获取预支付交易会话标识prepay_id
		//logger.info(JSONObject.fromObject(result1).toString());
		if(response.containsKey("prepay_id") && MyUtil.checkEmpty(response.getString("prepay_id"))){
			if(response.containsKey("is_meida_pay") &&response.optBoolean("is_meida_pay")){
				//新美大支付
				String bizId = com.tzx.framework.common.constant.Constant.getSystemMap().get("app_id");
				result=response;
				result.put("bizId", bizId);
				result.put("is_meida_pay", true);
			}else{
				//普通微信或者支付宝
				//生成json串 供js初始化使用
				String prepay_id = response.getString("prepay_id");
				String mac_appid = Constant.getSystemMap().get("wechat_service_mch_appid");
				String mac_api_secret = Constant.getSystemMap().get("wechat_service_mch_api_secert");
				String json = PayUtil.generateMchPayJsRequestJson(prepay_id, mac_appid, mac_api_secret);
				//total_amount=response.getString("total_amount");
				result = JSONObject.fromObject(json);
				result.put("is_meida_pay", false);
			}
			result.put("success", true);
			result.put("bill_num", order_num);
			result.put("total_amount", total_amount);
			result.put("pay_type", paystate);
		}else if(paystate.equals("1")){
			result.put("pay_type", paystate);
			result.putAll(response);
		}
		return result;
	}

	@Override
	public JSONObject getPaymentWay(String tenancyId, String paymentClass) throws Exception
	{
		String sql = "select * from payment_way where payment_class='"+paymentClass+"' and status='1'";
		List<JSONObject> list = orderDao.queryString4Json(tenancyId, sql);
		return list!=null&&list.size()>0?list.get(0):null;
	}

	@Override
	public JSONObject refundOrder(String tenancyId,String storeId,String order_num,double refund_money,String subMchId) throws Exception
	{
		
		//logger.info("开始退款接口：tenancyId："+tenancyId+"storeId:"+storeId+"order_num:"+order_num);
		JSONObject result = new JSONObject();
		result.put("success", false);
		result.put("store_id", storeId);
		result.put("order_code", order_num);
		com.tzx.framework.common.entity.Data param = new com.tzx.framework.common.entity.Data();
		com.tzx.framework.common.entity.Data result1 = new com.tzx.framework.common.entity.Data();
		param.setTenancy_id(tenancyId);
		if(null==storeId||"".equals(storeId)){
			param.setStore_id(0);
		}else{
			param.setStore_id(Integer.parseInt(storeId));
		}
		//logger.info("退款的机构序号为："+param.getStore_id());
//		param.setStore_id(Integer.parseInt(storeId));
		param.setType(Type.REFUND_PAY_ORDER);
		//查询支付账单业务类型
		String paymentordernumm=tenancyId+"_"+param.getStore_id()+"_"+order_num;
		String service_type="wd03";
		
		String sql="select * from pos_payment_order where order_num='"+paymentordernumm+"' order by final_state asc,id desc ";
		List<JSONObject> query4Json2 = dao.query4Json(tenancyId, sql.toString());
		JSONObject config=query4Json2.size()>0?query4Json2.get(0):null;
		if(config==null|| config.size()<=0){
			result.put("err_msg", "退款失败,没有查到该订单");
			//logger.info("退款失败,没有查到该订单"+paymentordernumm);
			return result;
		}else{
			service_type=config.getString("service_type");
			refund_money=config.getDouble("total_amount");
		}
		String paystate="0";
		String sql2="select para_value from sys_parameter where system_name = 'WECHAT' and para_code = 'wechat_payment_way' and store_id='0'";
		List<JSONObject> sysparameter = orderDao.queryString4Json(tenancyId, sql2);
		if(sysparameter==null || sysparameter.size()<=0){
			paystate="0";
		}else if("1".equals(sysparameter.get(0).optString("para_value"))) {
			paystate="1";
		}
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject bean = new JSONObject();
		bean.put("order_no",order_num);
		bean.put("pos_num", "WEB");//线上下单没有该号，默认为WEB
		bean.put("opt_num", "线上点餐");//线上没有
		bean.put("service_type",service_type);
		if(paystate.equals("1")){
			bean.put("pay_type","dlb_pay");
		}else{
			bean.put("pay_type","wechat_pay");
		}
		bean.put("amount",refund_money+"");
		list.add(bean);
		param.setData(list);
		
		//logger.info("微信支付退款的请求：");
		if(paystate.equals("1")){
			duoLaBaoPaymentServiceImpl.refundPayOrder(param, result1);
		}else{
			wechatPaymentServiceImpl.refundPayOrder(param, result1);
		}
	
		//com.alibaba.fastjson.JSONObject json = newWechatService.refund(param);
		//logger.info("微信支付退款的结果："+JSONObject.fromObject("result1").toString());
		if(result1.isSuccess()){
            JSONObject response=(JSONObject)result1.getData().get(0);
			
			
			if(response!=null&&"1".equals(response.getString("payment_state"))){
				result.put("success", true);
				//取消电子发票
				try {
					electronicInvoiceService.cansleElectronicInvoiceUrl(tenancyId, result);
				} catch (Exception e4) {
					e4.printStackTrace();
				}
			}else{
				result.put("err_msg", "退款失败");
			}
	
		}else{
			result.put("err_msg", "退款失败");
		}
		
		return result;
	}
	
	@Override
	public JSONObject refundOrder(String tenancyId, String storeId, String order_num, double refund_money) throws Exception
	{
		return this.refundOrder(tenancyId, storeId, order_num, refund_money, null);
	}

	@Override
	public JSONObject MemberAfterRefundOrder(String tenancyId, String storeId,
			String order_num) throws Exception {
		// TODO Auto-generated method stub
		JSONObject result=new JSONObject();
		result.put("store_id", storeId);
		result.put("order_code", order_num);
		List<net.sf.json.JSONObject> list = dao.query4Json(tenancyId, "select * from wx_member_card_rechargecard where order_num='"+order_num+"' order by id desc");
		String card_code="";
		String income="";
		String order_code="";
		String orderid="";
		if(list!=null && list.size()>0){
			card_code=list.get(0).getString("card_code");
			income=list.get(0).getString("income");
			order_code=list.get(0).getString("order_num");
			orderid=list.get(0).getString("id");
		}
		if(!orderid.equals("")){
			dao.execute(tenancyId, "update wx_member_card_rechargecard set pay_state='2' where id='"+orderid+"'");
		}
		result.put("success", true);
		return result;
	}

	@Override
	public JSONObject cancelPayOrder(String tenancyId, String storeId,
			String order_num, String paySource) throws Exception {
		JSONObject result=new JSONObject();
		result.put("success", false);
		StringBuffer sb=new StringBuffer();
		result.put("store_id", storeId);
		result.put("order_code", order_num);
		com.tzx.framework.common.entity.Data param = new com.tzx.framework.common.entity.Data();
		com.tzx.framework.common.entity.Data result1 = new com.tzx.framework.common.entity.Data();
		param.setTenancy_id(tenancyId);
		if(null==storeId||"".equals(storeId)){
			param.setStore_id(0);
		}else{
			param.setStore_id(Integer.parseInt(storeId));
		}
		//logger.info("退款的机构序号为："+param.getStore_id());
//		param.setStore_id(Integer.parseInt(storeId));
		param.setType(Type.REFUND_PAY_ORDER);
		//查询支付账单业务类型
		String paymentordernumm=tenancyId+"_"+param.getStore_id()+"_"+order_num;
		String service_type="wd03";
		if(TradeStatusConstants.PAY_SOURCE_WECHAT_BUY_MEMBER.equals(paySource)){
			service_type="hy01";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_BUY_PERSON.equals(paySource)){
			service_type="hy01";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_CARD_RECHANGE.equals(paySource)){
			service_type="hy01";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_DNDC.equals(paySource)){
			service_type="wd03";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_BUY_WXWM.equals(paySource)){
			service_type="wd03";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_MARKET_COUPONS.equals(paySource)){
			service_type="sc02";
		}else if(TradeStatusConstants.PAY_SOURCE_WECHAT_DS.equals(paySource)){
			service_type="wxds05";
		}
		
		String sql="select * from pos_payment_order where order_num='"+paymentordernumm+"' order by final_state asc,id desc";
		List<JSONObject> query4Json2 = dao.query4Json(tenancyId, sql.toString());
		JSONObject config=query4Json2.size()>0?query4Json2.get(0):null;
		if(config==null|| config.size()<=0){
			result.put("err_msg", "退款失败,没有查到该订单");
			//logger.info("退款失败,没有查到该订单"+paymentordernumm);
			return result;
		}else{
			service_type=config.getString("service_type");
		}
		String paystate="0";
		String sql2="select para_value from sys_parameter where system_name = 'WECHAT' and para_code = 'wechat_payment_way' and store_id='0'";
		List<JSONObject> sysparameter = orderDao.queryString4Json(tenancyId, sql2);
		if(sysparameter==null || sysparameter.size()<=0){
			paystate="0";
		}else if("1".equals(sysparameter.get(0).optString("para_value"))) {
			paystate="1";
		}
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject bean = new JSONObject();
		bean.put("order_no",order_num);
		bean.put("pos_num", "WEB");//线上下单没有该号，默认为WEB
		bean.put("opt_num", "线上点餐");//线上没有
		bean.put("service_type",service_type);
		if(paystate.equals("1")){
			bean.put("pay_type","dlb_pay");
		}else{
			bean.put("pay_type","wechat_pay");
		}
		list.add(bean);
		param.setData(list);
		JSONObject response=null;
		//logger.info("微信支付退款的请求：");
		if(paystate.equals("1")){
			duoLaBaoPaymentServiceImpl.refundPayOrder(param, result1);
			if(result1.isSuccess()){
	            response=(JSONObject)result1.getData().get(0);
			}else{
				result.put("err_msg", "退款失败");
				return result;
			}
		}else{
//			wechatPaymentServiceImpl.refundPayOrder(param, result1);
			String url=Constant.getSystemMap().get("saas_url")+"/payment/news/post";
			String temp = JSON.toJSONString(param);
	        String result_req = HttpUtil.sendPostRequest(url, temp);
	        if(null!=result_req){
	        	response=JSONObject.fromObject(result_req);
	        }else{
				result.put("err_msg", "退款失败");
				return result;
			}
		}
		if(response!=null&&"1".equals(response.getString("payment_state"))){
			result.put("success", true);
		}else{
			result.put("err_msg", "退款失败");
		}
		
		return result;
	}
}
