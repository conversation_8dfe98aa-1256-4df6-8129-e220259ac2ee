package com.tzx.framework.bo.imp;

import com.tzx.framework.bo.ChannelSettingService;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by jj on 2018-11-01.
 */
@Service(ChannelSettingService.NAME)
public class ChannelSettingServiceImpl implements ChannelSettingService {

    private static final Logger logger = Logger.getLogger(ChannelSettingServiceImpl.class);

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Override
    public List<JSONObject> loadChannelList(String tenentid, JSONObject condition) throws Exception {
        String startState = condition.optString("start_state");

        StringBuffer sql = new StringBuffer();
        sql.append(" select s.id, d.class_item, d.class_item_code, d.model_name, d.class_identifier_code, s.start_state from sys_channel_setting s inner join sys_dictionary d on s.chanel_id = d.id ");
        sql.append(" where d.class_identifier_code = 'chanel' and d.valid_state = '1' and d.model_name = 'hq' and d.is_sys = 'Y' and s.valid_state = '1' ");
        if (StringUtils.isNotEmpty(startState)){
            sql.append(" and s.start_state = '").append(startState).append("'");
        }
        sql.append(" order by d.id asc ");

        List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());

        return list;
    }

    @Override
    public void updateChannel(String tenentid, JSONObject condition, JSONObject returnJson) throws Exception {
        int id = condition.optInt("id");
        String startState = condition.optString("start_state");
        String lastOperator = condition.optString("last_operator");

        if (id <= 0 || StringUtils.isEmpty(startState) || !("0".equals(startState) || "1".equals(startState))){
            returnJson.put("msg", "参数不合法！");
            return;
        }
        String startMsg = "启用";
        if ("0".equals(startState)){
            startMsg = "关闭";
        }

        StringBuffer sql = new StringBuffer();
        sql.append(" select s.id, d.class_item, d.class_item_code, s.start_state from sys_channel_setting s inner join sys_dictionary d on s.chanel_id = d.id ");
        sql.append(" where s.id = ").append(id);
        sql.append(" and d.class_identifier_code = 'chanel' and d.valid_state = '1' and s.valid_state = '1' ");

        List<JSONObject> list = this.dao.query4Json(tenentid, sql.toString());
        if (list.size() == 0){
            returnJson.put("msg", startMsg+"渠道不存在！");
            return;
        }
        JSONObject channel = list.get(0);

        // 默认（餐厅）渠道不能关闭
        String classItemCode = channel.optString("class_item_code");
        if ("MD01".equals(classItemCode)){ // 默认（餐厅）不能关闭
            if ("0".equals(startState)){
                returnJson.put("msg", "默认（餐厅）渠道不能关闭！");
                return;
            }
        }

        // 已在餐谱中使用的渠道不能关闭
        if ("0".equals(startState)){
            sql.setLength(0);
            sql.append(" select m.id, m.item_menu_code, m.item_menu_name from hq_item_menu m inner join ");
            sql.append(" (select d.item_menu_id from hq_item_menu_class c ");
            sql.append(" inner join hq_item_menu_details d on c.details_id = d.id ");
            sql.append(" where c.chanel = '").append(classItemCode).append("' ");
            sql.append(" and d.valid_state = '1' ");
            sql.append(" group by d.item_menu_id) t on m.id = t.item_menu_id where m.valid_state = '1' ");

            List<JSONObject> menuList = this.dao.query4Json(tenentid, sql.toString());
            int size = menuList.size();
            if (size > 0){
                StringBuffer msg = new StringBuffer();
                for (int i = 0; i < size; i++){
                    String itemMenuName = menuList.get(i).optString("item_menu_name");
                    if (i == (size - 1)){
                        msg.append(itemMenuName);
                    } else {
                        msg.append(itemMenuName).append("，");
                    }
                }
                msg.append("正在使用该渠道，不可以关闭！");
                returnJson.put("msg", msg.toString());
                return;
            }
        }

        // 更新启用状态
        sql.setLength(0);
        sql.append(" update sys_channel_setting set start_state = '").append(startState).append("', ");
        sql.append(" last_operator = '").append(lastOperator).append("', ");
        sql.append(" last_updatetime = '").append(DateUtil.getNowDateYYDDMMHHMMSS()).append("' ");
        sql.append(" where id = ").append(id);
        this.dao.execute(tenentid, sql.toString());

        returnJson.put("success", true);
        returnJson.put("msg", startMsg+"成功！");
    }


}
