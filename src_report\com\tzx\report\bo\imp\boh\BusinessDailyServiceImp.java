package com.tzx.report.bo.imp.boh;

import com.tzx.report.bo.boh.BusinessDailyService;
import com.tzx.report.po.boh.dao.BusinessDailyDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */

@Service(BusinessDailyService.NAME)
public class BusinessDailyServiceImp implements BusinessDailyService {


    @Resource(name = BusinessDailyDao.NAME)
    private BusinessDailyDao businessDailyDao;

    @Override
    public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
        return businessDailyDao.find(tenancyID, condition);
    }

    @Override
    public List<JSONObject> getPayTypeItems(String tenancyID, JSONObject condition) throws Exception {
        return businessDailyDao.getPayTypeItems(tenancyID, condition);
    }
}
