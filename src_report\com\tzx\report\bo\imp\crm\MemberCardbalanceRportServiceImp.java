package com.tzx.report.bo.imp.crm;

/**
 * Created by gj on 2019-04-30.
 */

import com.tzx.report.bo.crm.MemberCardbalanceRportService;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import  com.tzx.report.po.crm.dao.MemberCardbalanceRportDao;
import javax.annotation.Resource;

@Service(MemberCardbalanceRportService.NAME)
public class MemberCardbalanceRportServiceImp implements MemberCardbalanceRportService {

    @Resource
    MemberCardbalanceRportDao memberCardbalanceRportDao;

    @Override
    public JSONObject getMemberCardbalanceQuery(String tenantId, JSONObject condition) throws Exception{
        return memberCardbalanceRportDao.getMemberCardbalanceQuery(tenantId, condition);
    }
}
