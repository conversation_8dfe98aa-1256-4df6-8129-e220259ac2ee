package com.tzx.cc.invoice.electronic.rest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.protocol.HTTP;
import org.apache.log4j.Logger;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.alibaba.fastjson.JSONArray;
import com.sun.org.apache.xml.internal.security.utils.Base64;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.Type;
import com.tzx.cc.invoice.electronic.cache.PiaoTongConfigCache;
import com.tzx.cc.invoice.electronic.cache.SysparamCache;
import com.tzx.cc.invoice.electronic.service.ElecInvoiceService;
import com.tzx.cc.invoice.electronic.service.impl.YonyouElecInvoiceServiceImpl;
import com.tzx.cc.invoice.electronic.util.BwElectronicInvoiceWebServiceUtils;
import com.tzx.cc.invoice.electronic.util.ChooseEleServiceUtils;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoicePropertyUtil;
import com.tzx.cc.invoice.electronic.util.KeyUtils;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.ObjectMapper;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.controller.BaseController;
import com.vpiaotong.openapi.util.SecurityUtil;

import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 *
 */
@Controller("ElecInvoiceRest")
@RequestMapping("/invoice/elec")
public class ElecInvoiceRest extends BaseController
{
	private static final Logger	logger	= Logger.getLogger(ElecInvoiceRest.class);

	
	private ElecInvoiceService elecInvoiceService;

	@Resource(name="com.tzx.cc.invoice.electronic.service.impl.YonyouElecInvoiceServiceImpl")
	private ElecInvoiceService yyElecInvoiceService;

	@Resource(name="com.tzx.cc.invoice.electronic.service.impl.BwElecInvoiceServiceImpl")
	private ElecInvoiceService bwElecInvoiceService;

	@Resource(name=ElecInvoiceService.NAME)
	private ElecInvoiceService rhElecInvoiceService;
	
	@Resource(name="com.tzx.cc.invoice.electronic.service.impl.PiaotongElecInvoiceServiceImpl")
	private ElecInvoiceService ptElecInvoiceService;
	
	@Resource(name="saasRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

	@SuppressWarnings("resource")
	@RequestMapping(value = "get", method = RequestMethod.GET)
	public void get(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		String result = "";
		try {
			//tenancy_id#store_id#DDRQ#DH#SERVICE_TYPE#SL#JE
			//hdl#369#20170101#999999#1#0.03#4567
			String reqStr = request.getParameter("para");
			if(StringUtils.isBlank(reqStr)) {
				out = buildStyleException(response, out, "参数格式不正确");
				return;
			}
			reqStr = reqStr.replaceAll(" ", "+");
			try {
				result = validNative(reqStr,response);
			} catch (Exception e) {
				out = buildStyleException(response, out, "参数格式不正确");
				logger.error(e);
			}
			if(result.contains("#")) {
				String[] split = result.split("#");
				String tenancy_id = split[0];
				String store_id = split[1];
				String DDRQ = split[2];
				String DH = split[3];
				String SERVICE_TYPE = split[4];
				String SL = split[5];
				String JE = split[6];
				if(StringUtils.isBlank(SERVICE_TYPE)) {
					out = buildStyleException(response, out, "业务类型不能为空");
					return;
				}
				String serviceType = ElectronicInvoicePropertyUtil.getMsg(SERVICE_TYPE);
				if(StringUtils.isBlank(serviceType)) {
					out = buildStyleException(response, out, SERVICE_TYPE + "对应业务类型未定义");
					return;
				}
				String[] split2 = serviceType.split("@");
				if(StringUtils.equals(SERVICE_TYPE, "21")) {
					DH = split2[0] + DH;
				} else if(StringUtils.equals(SERVICE_TYPE, "1")){
					DH = store_id + DDRQ + DH;
				} else {
					DH = split2[0] + store_id + DDRQ + DH;
				}
				SERVICE_TYPE = split2[1];
				
				JSONObject jsondata = new JSONObject();
				jsondata.put("tenancy_id", tenancy_id);
				jsondata.put("store_id", store_id);
				
				jsondata.put("type", Type.ISSUE_ELECTRONIC_INVOICE_SCC.toString());
				List<JSONObject> list = new ArrayList<JSONObject>();
				JSONObject json = new JSONObject();
				json.put("DH", DH);
				Date parseDate = DateUtils.parseDate(DDRQ, new String[]{"yyyyMMdd"});
				DDRQ = DateFormatUtils.format(parseDate, "yyyy-MM-dd");
				
				json.put("DDRQ", DDRQ);
				json.put("SERVICE_TYPE", SERVICE_TYPE);
				json.put("SL", SL);
				
				JSONArray array = new JSONArray();
			    com.alibaba.fastjson.JSONObject innerJson = new com.alibaba.fastjson.JSONObject();
			    innerJson.put("PAYMENT_CLASS","payment_class");
			    innerJson.put("JE", JE);
			    array.add(innerJson);
				json.put("data", array);
				
				list.add(json);
				jsondata.put("data", list);
				post(request, response, jsondata);
			} else {
				out = buildStyleException(response, out, result);
			}
		} catch (Exception e) {
			try {
				out = response.getWriter();
				out.print(addStyle("发生异常"));
			} catch (IOException e1) {
				logger.error(e1);
			} finally {
				if (out != null) out.close();
			}
			logger.error(e);
		}
	}

	@SuppressWarnings("resource")
	@RequestMapping(value = "newposget", method = RequestMethod.GET)
	public void newposget(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		String result = "";
		try {
			//tenancy_id#store_id#DDRQ#DH#SERVICE_TYPE#SL#JE
			//hdl#369#20170101#999999#1#0.03#4567
			String reqStr = request.getParameter("para");
			if(StringUtils.isBlank(reqStr)) {
				out = buildStyleException(response, out, "参数格式不正确");
				return;
			}
			reqStr = reqStr.replaceAll(" ", "+");
			try {
				result = newposvalidNative(reqStr,response);
			} catch (Exception e) {
				out = buildStyleException(response, out, "参数格式不正确");
				logger.error(e);
			}
			if(result.contains("#")) {
				String[] split = result.split("#");
				String tenancy_id = split[0];
				String store_id = split[1];
                GenericDao dao = (GenericDao) SpringConext.getApplicationContext().getBean("genericDaoImpl");
                String sql = "select id from organ where fake_id = ?";
                List<JSONObject> list1 = dao.query4Json(tenancy_id, sql, new Object[]{Integer.parseInt(store_id)});
                if(list1.isEmpty()) {
                    out = buildStyleException(response, out, "saas中没有此机构信息机构ID为"+store_id);
                    return;
                }
                store_id = list1.get(0).optString("id");
                String DDRQ = split[2];
				String DH = split[3];
				String SERVICE_TYPE = split[4];
				String SL = split[5];
				String JE = split[6];
				if(StringUtils.isBlank(SERVICE_TYPE)) {
					out = buildStyleException(response, out, "业务类型不能为空");
					return;
				}
				String serviceType = ElectronicInvoicePropertyUtil.getMsg(SERVICE_TYPE);
				if(StringUtils.isBlank(serviceType)) {
					out = buildStyleException(response, out, SERVICE_TYPE + "对应业务类型未定义");
					return;
				}
				String[] split2 = serviceType.split("@");
				SERVICE_TYPE = split2[1];

				JSONObject jsondata = new JSONObject();
				jsondata.put("tenancy_id", tenancy_id);
				jsondata.put("store_id", store_id);

				jsondata.put("type", Type.ISSUE_ELECTRONIC_INVOICE_SCC.toString());
				List<JSONObject> list = new ArrayList<JSONObject>();
				JSONObject json = new JSONObject();
				json.put("DH", DH);
				Date parseDate = DateUtils.parseDate(DDRQ, new String[]{"yyyyMMdd"});
				DDRQ = DateFormatUtils.format(parseDate, "yyyy-MM-dd");

				json.put("DDRQ", DDRQ);
				json.put("SERVICE_TYPE", SERVICE_TYPE);
				json.put("SL", SL);

				JSONArray array = new JSONArray();
			    com.alibaba.fastjson.JSONObject innerJson = new com.alibaba.fastjson.JSONObject();
			    innerJson.put("PAYMENT_CLASS","payment_class");
			    innerJson.put("JE", JE);
			    array.add(innerJson);
				json.put("data", array);

				list.add(json);
				jsondata.put("data", list);
				post(request, response, jsondata);
			} else {
				out = buildStyleException(response, out, result);
			}
		} catch (Exception e) {
			try {
				out = response.getWriter();
				out.print(addStyle("发生异常"));
			} catch (IOException e1) {
				logger.error(e1);
			} finally {
				if (out != null) out.close();
			}
			logger.error(e);
		}
	}

	/**
	 * @param response
	 * @param out
	 * @param result
	 * @return
	 */
	private PrintWriter buildStyleException(HttpServletResponse response,
			PrintWriter out, String result) {
		try {
			out = response.getWriter();
			out.print(addStyle(result));
		} catch (IOException e1) {
			logger.error(e1);
		} finally {
			if (out != null) out.close();
		}
		return out;
	}
	
	/**
	 * @param response
	 * @param result
	 * @return
	 */
	private PrintWriter buildException(HttpServletResponse response, String result) {
		PrintWriter out = null;
		try {
			out = response.getWriter();
			out.print(result);
		} catch (IOException e1) {
			logger.error(e1);
		} finally {
			if (out != null) out.close();
		}
		return out;
	}
	
	/**
	 * 验证boh传过的参数
	 * @param reqStr
	 * @param response
	 * @throws Exception
	 */
	private String newposvalidNative(String reqStr, HttpServletResponse response) throws Exception {
		byte[] decode = Base64.decode(reqStr);
		String info = new String(decode);
		String realinfo = info.substring(0,info.lastIndexOf("#"));
		String key = info.substring(info.lastIndexOf("#")+1, info.length());
		logger.info("电子发票扫描二维码请求参数para对应的base64解码后为");
		logger.info(info);
		
		String[] split = realinfo.split("#");
		String tenancy_id = split[0];
		String store_id = split[1];

		DBContextHolder.setTenancyid(tenancy_id);
        GenericDao dao = (GenericDao) SpringConext.getApplicationContext().getBean("genericDaoImpl");
        String sql = "select id from organ where fake_id = ?";
        List<JSONObject> list1 = dao.query4Json(tenancy_id, sql, new Object[]{Integer.parseInt(store_id)});
        if(list1.isEmpty()) {
            return "saas中没有此机构信息机构ID为"+store_id;
        }
        store_id = list1.get(0).optString("id");
        String nativekey = SysparamCache.getSysparam(tenancy_id,Integer.parseInt(store_id), "dzfp_ewmdyfpmy");
        if(StringUtils.isBlank(nativekey)) {
            return "解密失败";
        }
		String key2 = KeyUtils.encryptASE_MD5(realinfo, nativekey);
		key2 = key2.substring(0, 4);
		if(!key.equals(key2)) {
			return "解密失败";
		}
		return realinfo;
	}


	/**
	 * 验证boh传过的参数
	 * @param reqStr
	 * @param response
	 * @throws Exception
	 */
	private String validNative(String reqStr, HttpServletResponse response) throws Exception {
		byte[] decode = Base64.decode(reqStr);
		String info = new String(decode);
		String realinfo = info.substring(0,info.lastIndexOf("#"));
		String key = info.substring(info.lastIndexOf("#")+1, info.length());
		logger.info("电子发票扫描二维码请求参数para对应的base64解码后为");
		logger.info(info);

		String[] split = realinfo.split("#");
		String tenancy_id = split[0];

		DBContextHolder.setTenancyid(tenancy_id);
        String nativekey = SysparamCache.getSysparam(tenancy_id, "dzfp_ewmdyfpmy");
        if(StringUtils.isBlank(nativekey)) {
            return "解密失败";
        }
		String key2 = KeyUtils.encryptASE_MD5(realinfo, nativekey);
		key2 = key2.substring(0, 4);
		if(!key.equals(key2)) {
			return "解密失败";
		}
		return realinfo;
	}

	/**
	 * 发票开具
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "orderCallback", method = RequestMethod.POST)
	public void orderCallback(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		String xmlParam = request.getParameter("xmlParam");
		
		
		logger.info("ElecInvoiceRest  orderCallback方法接受到的参数为："+xmlParam.toString());
		/*Map<String, String> map = new HashMap<String, String>();
		map.put("xmlParam", xmlParam);
		String sendPostRequest = HttpUtil.sendPostRequest("http://cs.meishijia.com/tzxsaas/invoice/elec/orderCallback", map);
		logger.info("调用18返回 为："+sendPostRequest);
		buildException(response, sendPostRequest);*/
		JSONObject result = new JSONObject();
		result.put("RSCODE", "9999");
		result.put("MSG", "发生意外，请稍后再试");
		String json2Xml = BwElectronicInvoiceWebServiceUtils.json2Xml("<RESPONSE>", "</RESPONSE> ", result);
		try {
			bwElecInvoiceService.orderCallback(xmlParam,result);
		} catch (Exception e) {
			json2Xml = BwElectronicInvoiceWebServiceUtils.json2Xml("<RESPONSE>", "</RESPONSE> ", result);
			buildException(response, json2Xml);
			logger.error(e);
		}
		json2Xml = BwElectronicInvoiceWebServiceUtils.json2Xml("<RESPONSE>", "</RESPONSE> ", result);
		buildException(response, json2Xml);
	}
	
	/**
	 * 用友发票开具回调服务
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "yyOrderCallback", method = RequestMethod.POST)
	public void yyOrderCallback(HttpServletRequest request, HttpServletResponse response, @RequestBody
            JSONObject jsobj)
	{
		response.setContentType("text/json; charset=UTF-8");

        String jsonstr = jsobj.toString();
        logger.info("用友回调的json串儿为"+jsonstr);
        try {
            //处理用友开票成功回调的内容
            yyElecInvoiceService.orderCallback(jsonstr,null);
		} catch (Exception e) {
			logger.error(e);
		}
	}
	
	/**
	 * 票通发票开具回调服务
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "ptOrderCallback", method = RequestMethod.POST)
	public void ptOrderCallback(HttpServletRequest request, HttpServletResponse response, @RequestBody
            JSONObject jsobj)
	{
		response.setContentType("text/json; charset=UTF-8");

        String jsonstr = jsobj.optString("content");
        jsonstr=SecurityUtil.decrypt3DES(PiaoTongConfigCache.getElementText("password"),jsonstr);
        logger.info("票通回调的json串儿content内容为"+jsonstr);
        try {
            //处理票通开票成功回调的内容
            ptElecInvoiceService.orderCallback(jsonstr,null);
		} catch (Exception e) {
			logger.error(e);
		}
	}
	
	/**
	 * 请求入口
	 * 
	 * @return JSONObject
	 */
	@RequestMapping(value = "post", method = RequestMethod.POST)
	// @ResponseBody
	public void post(HttpServletRequest request, HttpServletResponse response, @RequestBody
	JSONObject jsobj)
	{
		response.setContentType("text/html; charset=UTF-8");
		logger.info("电子发票请求信息JSON格式为");
		logger.info(jsobj.toString());
		PrintWriter out = null;
		JSONObject responseJson = null;

		ObjectMapper objectMapper = new ObjectMapper();
		Data param = null;
		try
		{
			param = objectMapper.readValue(jsobj.toString(), Data.class);
		}
		catch (Exception se)
		{
			se.printStackTrace();
			logger.error("Fastjson 类型解析错误" + ExceptionMessage.getExceptionMessage(se));
			responseJson = buildErrorResult(Constant.CODE_PARAM_FAILURE, "json转data类型错误，请查看传入参数");
		}

		String reqJson = JSONObject.fromObject(param).toString();

		logger.info("接收的请求：" + reqJson);

		try
		{
			DBContextHolder.setTenancyid(param.getTenancy_id());
		}
		catch (Exception e)
		{
			logger.error("切换数据源错误：" + ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
			responseJson = buildErrorResult(Constant.CODE_CHANGE_DATASOURCE_FAILURE, Constant.POS_CHANGE_DATASOURCE_FAILURE);
		}
		Data result = null; // 以前用Data.get(param),现在换成clone
		if (param.getPagination() == null)
		{
			param.setPagination(new Pagination());
		}
		result = param.clone();
		result.setData(null);

		Data data = null;
        if(!StringUtils.equals(param.getType().toString(),Type.CANCLE_LECTRONIC_INVOICE_NEWPOS.toString())) {
            elecInvoiceService = ChooseEleServiceUtils.getElecInvoiceService(param.getTenancy_id(), param.getStore_id());
        }

		
		switch (param.getType())
		{
			case ISSUE_ELECTRONIC_INVOICE:
				try
				{
					elecInvoiceService.issueElectronicInvoice(param,result);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, "生成电子发票信息失败");
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, "生成电子发票信息失败");
				}
				break;
			case ISSUE_ELECTRONIC_INVOICE_SCC:
				try
				{
					elecInvoiceService.issueElectronicInvoice(param,result,response);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, "生成电子发票信息失败");
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, "生成电子发票信息失败");
				}
				break;
			case CANCLE_LECTRONIC_INVOICE:
				try
				{
					elecInvoiceService.cancelElectronicInvoice(param,result);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, "取消电子发票失败");
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, "取消电子发票信息失败");
				}
				break;
			case CANCLE_LECTRONIC_INVOICE_NEWPOS:
				try
				{
                    String tenancyId = param.getTenancy_id();
                    int storeId = param.getStore_id();
                    GenericDao dao = (GenericDao) SpringConext.getApplicationContext().getBean("genericDaoImpl");
                    String sql = "select id from organ where fake_id = ?";
                    List<JSONObject> list1 = dao.query4Json(tenancyId, sql, new Object[]{storeId});
                    if(list1.isEmpty()) {
                        out = buildStyleException(response, out, "saas中没有此机构信息机构ID为"+storeId);
                        return;
                    }
                    storeId = list1.get(0).optInt("id");
                    param.setStore_id(storeId);
                    elecInvoiceService = ChooseEleServiceUtils.getElecInvoiceService(param.getTenancy_id(), param.getStore_id());

					elecInvoiceService.cancelElectronicInvoice(param,result);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, "取消电子发票失败");
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, "取消电子发票信息失败");
				}
				break;
			case QUERY_INVOICE_INFO:
				try
				{
					elecInvoiceService.queryElectronicInvoice(param,result);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, "查询电子发票失败");
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, "查询电子发票信息失败");
				}
				break;
			default:
				result.setCode(Constant.CODE_PARAM_FAILURE);
				result.setMsg(Constant.NOT_EXISTS_TYPE);
				break;
		}

		if (data != null)
		{
			result.setCode(data.getCode());
			result.setMsg(data.getMsg());
			result.setData(data.getData());
		}

		if (data != null && data.getPagination() == null)
		{
			Pagination page = result.getPagination();
			page.setPageno(1);
			List<?> lists = result.getData();
			if (lists == null)
			{
				lists = new ArrayList<Object>();
			}
			page.setPagesize(lists.size());
			page.setTotalcount(lists.size());
		}
		else if (data != null)
		{
			result.setPagination(data.getPagination());
		}

		if (result.getCode() == 0)
		{
			result.setSuccess(true);
		}
		else
		{
			result.setSuccess(false);
		}

		responseJson = JSONObject.fromObject(result);
		
		logger.info(responseJson.toString().length() + "<==长度，POS查询结果==>" + responseJson.toString());
		try
		{
			out = response.getWriter();
			if(Type.ISSUE_ELECTRONIC_INVOICE_SCC==param.getType()) {
				String returnstrr = result.getMsg();
				String resultStr = addStyle(returnstrr);
				out.print(resultStr);
			} else {
				out.print(responseJson.toString());
			}
			out.flush();
			out.close();
		}
		catch (Exception e)
		{
		}
		finally
		{
			if (out != null) out.close();
		}
	}

	private String addStyle(String returnstrr) {
		StringBuffer resultStr = new StringBuffer();
		resultStr.append("<div align='center' style='font-size: 5em;padding-top: 6%;color: red;hight:50%;'>");
		resultStr.append(returnstrr);
		resultStr.append("</div>");
		return resultStr.toString();
	}

	public void buildSysExceptionData(SystemException se, Data result, String message)
	{
		ErrorCode error = se.getErrorCode();
		String msg = PropertiesLoader.getProperty(String.valueOf(error.getNumber()));
		Map<String, Object> map = se.getProperties();
		for (String key : map.keySet())
		{
			msg = msg.replace(key, String.valueOf(map.get(key)));
		}

		result.setCode(error.getNumber());
		result.setMsg(msg);
		logger.error(message + ",原因：" + msg + ",错误码：" + error.getNumber());
		logger.error(ExceptionMessage.getExceptionMessage(se));
	}

	public void buildExceptionData(Exception e, Data result, String message)
	{
		result.setCode(Constant.CODE_INNER_EXCEPTION);
		result.setMsg(message);
		logger.error("系统内部错误：" + ExceptionMessage.getExceptionMessage(e));
		e.printStackTrace();
	}

	public void buildDataResult(int code, String msg, List<?> list, Data result)
	{
		result.setCode(code);
		result.setMsg(msg);

		if (list != null && list.size() != 0)
		{
			result.setData(list);
		}
	}

	public JSONObject buildErrorResult(int code, String msg)
	{
		JSONObject obj = new JSONObject();
		obj.put("code", code);
		obj.put("msg", msg);
		return obj;
	}
}
