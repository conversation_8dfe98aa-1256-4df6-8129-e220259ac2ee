DELEGATES_PREFERENCE=delegateValidatorList
USER_BUILD_PREFERENCE=enabledBuildValidatorListorg.eclipse.jst.j2ee.internal.classpathdep.ClasspathDependencyValidator;
USER_MANUAL_PREFERENCE=enabledManualValidatorListorg.jboss.tools.seam.internal.core.validation.SeamProjectPropertyValidator;org.eclipse.wst.wsi.ui.internal.WSIMessageValidator;org.eclipse.jst.j2ee.internal.classpathdep.ClasspathDependencyValidator;org.jboss.tools.common.validation.ValidatorManager;
USER_PREFERENCE=overrideGlobalPreferencestruedisableAllValidationfalseversion1.2.600.v201501211647
disabled=06target
eclipse.preferences.version=1
override=true
suspend=false
vals/org.eclipse.jst.jsf.facelet.ui.FaceletHTMLValidator/global=TF01
vals/org.eclipse.jst.jsf.ui.JSFAppConfigValidator/global=TF01
vals/org.eclipse.jst.jsp.core.JSPBatchValidator/global=TF01
vals/org.eclipse.jst.jsp.core.JSPContentValidator/global=TF01
vals/org.eclipse.jst.jsp.core.TLDValidator/global=TF01
vals/org.eclipse.wst.dtd.core.dtdDTDValidator/global=TF01
vals/org.eclipse.wst.html.core.HTMLValidator/global=TF01
vals/org.eclipse.wst.jsdt.web.core.JsBatchValidator/global=TF01
vals/org.eclipse.wst.wsdl.validation.wsdl/global=TF02158org.eclipse.wst.wsdl.validation.internal.eclipse.Validator
vals/org.eclipse.wst.xml.core.xml/global=TF03
vals/org.eclipse.wst.xsd.core.xsd/global=TF02162org.eclipse.wst.xsd.core.internal.validation.eclipse.Validator
vals/org.eclipse.wst.xsl.core.xsl/global=TF02
vals/org.jboss.tools.jsf.xhtml/global=TF03
vf.version=3
