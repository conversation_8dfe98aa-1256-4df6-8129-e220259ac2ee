package com.tzx.report.bo.imp.crm;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.crm.CouponUsingSummaryQueryService;

@Service(CouponUsingSummaryQueryService.NAME)
public class CouponUsingSummaryQueryServiceImp implements CouponUsingSummaryQueryService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Override
	public JSONObject find(String tenancyId, JSONObject jb) throws SystemException, Exception
	{
		JSONObject result = new JSONObject();
		List<JSONObject> list = new ArrayList<JSONObject>();
		StringBuilder sb = new StringBuilder();
		String date1 = jb.optString("date1");
		String date2 = jb.optString("date2");
		int pagenum = jb.containsKey("page") ? (jb.getInt("page") == 0 ? 1 : jb.getInt("page")) : 1;
		long total = 0L;
		String ids1 = jb.optString("store_ids1");

		String ids2 = jb.optString("store_ids2");

		String activity_type = jb.optString("activity_type");
		String coupons_class_id = jb.optString("coupons_class_id");
		Integer type = jb.optInt("type");
		String state = jb.optString("state");
		String code = jb.optString("code");
		if(type==0)
		{
			type =1;
		}
		if("".equals(date1) || "".equals(date2))
		{
			JSONObject date = DateUtil.getMonthDate();
			date1 = date.optString("from");
			date2 = date.optString("to");
		}
		if(type>0 && date1.length()>0 && date2.length()>0)
		{
			sb.setLength(0);
			sb.append("select (CASE WHEN  cc.use_bill is not null THEN (select  COALESCE(max(id),0) from crm_card_trading_list cctl where cctl.third_bill_code=cc.use_bill ) ELSE 0 END) as zz,cc.code,cc.state,to_char(cc.send_time,'yyyy-mm-dd') as send_time,oo.org_full_name as use_organ_name,cc.use_bill,cc.use_date,cc.send_store,cc.id,ca.activity_type,cc.operator,(select class_item from sys_dictionary where class_identifier_code='activity_type' and class_item_code=ca.activity_type) as activity_type_name,ccc.coupons_pro,ccc.class_name,cct.face_value,(select max(class_item) FROM sys_dictionary where class_item_code=cc.send_chanel and class_identifier_code='chanel') as chanel_name,(select max(class_item) FROM sys_dictionary where class_item_code=cc.use_chanel and class_identifier_code='chanel') as use_chanel,(CASE cc.send_store WHEN 0 THEN '总部' ELSE o.org_full_name END) as org_full_name,ca.subject");
			// 添加新字段
			sb.append(" ,cci.mobil,cci.name,cct.sale_money,sd.class_item as coupons_pro_name");
			sb.append(" ,case when cc.state ='0' then '过期' when cc.state = '1' then '有效' when cc.state = '2' then '已使用' else '无效' end as state_name");
			
			sb.append(" FROM crm_coupons cc ");
			
			// 添加新关联关系
			sb.append(" LEFT JOIN crm_coupons_type cct ON cc.type_id=cct.id  LEFT JOIN crm_activity ca ON cct.activity_id=ca.id LEFT JOIN crm_coupons_class ccc ON cct.class_id=ccc.id LEFT JOIN organ o ON cc.send_store=o.id LEFT JOIN organ oo ON cc.use_store=oo.id");
			sb.append(" left join crm_customer_coupons ccu on ccu.coupons_code=cc.code left join crm_customer_info cci on ccu.customer_id=cci.id");
			sb.append(" left join sys_dictionary sd on sd.class_identifier_code = 'coupons_goods_type' and ccc.coupons_pro = sd.class_item_code");
			
			// 条件部分
			sb.append(" where (cc."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
			if(ids1.length()>0)
			{
				sb.append(" and cc.send_store in("+ids1+") ");
			}
			if(ids2.length()>0)
			{
				sb.append(" and cc.use_store in("+ids2+") ");
			}
			if(activity_type.length()>0)
			{
				sb.append(" and ca.activity_type in("+activity_type+") ");
			}
			if(coupons_class_id.length()>0)
			{
				sb.append(" and ccc.id in("+coupons_class_id+") ");
			}
			if(code.length()>0)
			{
				sb.append(" and cc.code like '%"+code+"%' ");
			}
			if(state.length()>0)
			{
				sb.append(" and cc.state = '"+state+"' ");
			}
			if(jb.optString("mobil").length() > 0)
			{
				sb.append(" and cci.mobil like '" + jb.optString("mobil") + "%'");
			}
			if(jb.optString("name").length() > 0)
			{
				sb.append(" and cci.name like '%" + jb.optString("name") + "%'");
			}
			
			total = this.dao.countSql(tenancyId, sb.toString());				
			list= this.dao.query4Json(tenancyId,this.dao.buildPageSql(jb,sb.toString()));				
			result.put("page", pagenum);
			result.put("total", total);
			
		}
		

		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject newFind(String tenancyId, JSONObject jb) throws SystemException, Exception
	{
		JSONObject result = new JSONObject();
		List<JSONObject> list = new ArrayList<JSONObject>();
		StringBuilder sb = new StringBuilder();
		String date1 = jb.optString("date1");
		String date2 = jb.optString("date2");
		int pagenum = jb.containsKey("page") ? (jb.getInt("page") == 0 ? 1 : jb.getInt("page")) : 1;
		long total = 0L;
		String ids1 = jb.optString("store_ids1");
		String ids2 = jb.optString("store_ids2");
		String activity_type = jb.optString("activity_type");
		String coupons_class_id = jb.optString("coupons_class_id");
		Integer type = jb.optInt("type");
		String state = jb.optString("state");
		String code = jb.optString("code");
		if(type==0)
		{
			type =1;
		}
		if("".equals(date1) || "".equals(date2))
		{
			JSONObject date = DateUtil.getMonthDate();
			date1 = date.optString("from");
			date2 = date.optString("to");
		}
		if(type>0 && date1.length()>0 && date2.length()>0)
		{
			sb.setLength(0);
			sb.append(" with sys_dictionary_t as(select class_item_code, max(class_item) class_item from sys_dictionary where class_identifier_code = 'chanel'group by class_item_code),");
			sb.append(" t1 as (select CASE WHEN cc.use_bill is not null THEN COALESCE(cctl.id,0) else 0 end as zz");
			sb.append(" ,cc.code,cc.state,to_char(cc.send_time, 'yyyy-mm-dd') as send_time,oo.org_full_name as use_organ_name,cc.use_bill,cc.use_date,cc.send_store,cc.id");
			sb.append(" ,ca.activity_type,hii.item_name as coupons_used_item,sd1.class_item as activity_type_name,ccc.coupons_pro,ccc.class_name,cct.face_value,sd2.class_item as chanel_name,sd3.class_item as use_chanel");
			sb.append(" ,(CASE cc.send_store WHEN 0 THEN '总部' ELSE o.org_full_name END) as org_full_name,ca.subject,cci.mobil,cci.name,cct.sale_money,sd.class_item as coupons_pro_name");
			sb.append(" ,case when cc.state = '0' then '过期' when cc.state = '1' then '有效' when cc.state = '2' then '已使用' else '无效' end as state_name FROM crm_coupons cc");
			sb.append(" LEFT JOIN crm_coupons_type cct ON cc.type_id = cct.id");
			sb.append(" LEFT JOIN (SELECT third_bill_code, COALESCE(max(id), 0) id FROM crm_card_trading_list group by third_bill_code) cctl on cctl.third_bill_code = cc.use_bill");
			sb.append(" LEFT JOIN crm_activity ca ON cct.activity_id = ca.id");
			sb.append(" LEFT JOIN crm_coupons_class ccc ON cct.class_id = ccc.id ");
			sb.append(" LEFT JOIN crm_coupons_use_item ccd on cc.id=ccd.coupon_id ");
			sb.append(" LEFT JOIN hq_item_info hii on ccd.item_id=hii.id ");
			sb.append(" LEFT JOIN organ o ON cc.send_store = o.id");
			sb.append(" LEFT JOIN organ oo ON cc.use_store = oo.id ");
			sb.append(" left join crm_customer_coupons ccu on ccu.coupons_code = cc.code");
			sb.append(" left join crm_customer_info cci on ccu.customer_id = cci.id");
			sb.append(" left join sys_dictionary sd on sd.class_identifier_code = 'coupons_goods_type' and ccc.coupons_pro = sd.class_item_code");
			//  and sd1.class_identifier_code='activity_type' 2017-09-28 赵静茹 增加
			sb.append(" left join sys_dictionary sd1 on sd1.class_item_code = ca.activity_type and sd1.class_identifier_code='activity_type'");
			sb.append(" left join sys_dictionary_t sd2  on sd2.class_item_code = cc.send_chanel");
			sb.append(" left join sys_dictionary_t sd3 on sd3.class_item_code = cc.use_chanel");
			sb.append(" where (cc."+(type==1?"send_time":type==2?"use_date":"business_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");// 2018-03-26 增加按照报表日期查询
			if(ids1.length()>0)
			{
				sb.append(" and cc.send_store in("+ids1+") ");
			}
			if(ids2.length()>0)
			{
				sb.append(" and cc.use_store in("+ids2+") ");
			}
			if(activity_type.length()>0)
			{
				sb.append(" and ca.activity_type in("+activity_type+") ");
			}
			if(coupons_class_id.length()>0)
			{
				sb.append(" and ccc.id in("+coupons_class_id+") ");
			}
			if(code.length()>0)
			{
				sb.append(" and cc.code like '%"+code+"%' ");
			}
			if(state.length()>0)
			{
				sb.append(" and cc.state = '"+state+"' ");
			}
			if(jb.optString("mobil").length() > 0)
			{
				sb.append(" and cci.mobil like '" + jb.optString("mobil") + "%'");
			}
			if(jb.optString("name").length() > 0)
			{
				sb.append(" and cci.name like '%" + jb.optString("name") + "%'");
			}
			// 20180425 康彩霞 修改！
			/*sb.append(" ) select t1.*,p.currency_amount as coupons_real_amount,pb.bill_amount as sales_total,pb.payment_amount as real_amount from t1 left join pos_bill_payment2 p  on t1.use_bill=p.bill_num  and p.type='coupons' and t1.code=p.number");
			sb.append(" left join pos_bill2 pb on t1.use_bill=pb.bill_num ");
			*/
			sb.append(" ) select "
					+ " t1.* "
					+ " ,case when COALESCE(p.currency_amount,0)<>0 then p.currency_amount else pbm.amount end as coupons_real_amount "
					+ " ,case when COALESCE(pb.bill_amount,0) <>0 then pb.bill_amount else pm.amount end as sales_total "
					+ " ,case when COALESCE(pb.payment_amount,0)<>0 then pb.payment_amount else pm.amount end as real_amount  "
					+ " from t1 left join pos_bill_payment2 p  on t1.use_bill=p.bill_num  and p.type='coupons' and t1.code=p.number");
			sb.append(" left join pos_bill2 pb on t1.use_bill=pb.bill_num ");
			sb.append(" LEFT JOIN (select bill_num,sum(amount) as amount  from pos_bill_member group by bill_num)pm on t1.use_bill=pm.bill_num ");
			sb.append(" LEFT JOIN (select bill_num,type,sum(amount) as amount from pos_bill_member where type='YHJ05' group by bill_num,type) pbm on  t1.use_bill=pbm.bill_num ");
			
			total = this.dao.countSql(tenancyId, sb.toString());				
			list= this.dao.query4Json(tenancyId,this.dao.buildPageSql(jb,sb.toString()));				
			result.put("page", pagenum);
			result.put("total", total);
	}
	result.put("rows", list);
		return result;
	}
	
	
	
}
