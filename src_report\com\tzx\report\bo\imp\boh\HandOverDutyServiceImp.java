package com.tzx.report.bo.imp.boh;

import com.tzx.report.bo.boh.HandOverDutyService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.po.boh.dao.HandOverDutyDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service(HandOverDutyService.NAME)
public class HandOverDutyServiceImp implements HandOverDutyService
{
	
	@Resource(name = HandOverDutyDao.NAME)
	private HandOverDutyDao handOverDutyDao;
	
	@Resource
	ConditionUtils conditionUtils;

	@Override
	public JSONObject getHandOverDutyQuery(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = JSONObject.fromObject("{}");
		result.element("rows", handOverDutyDao.getHandOverDutyQuery(tenancyID, condition));
		result.element("success", true);
		return result;
	}
}
