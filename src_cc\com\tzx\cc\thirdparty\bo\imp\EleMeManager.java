package com.tzx.cc.thirdparty.bo.imp;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.alibaba.druid.util.StringUtils;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.common.constant.DishOper;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.ElmUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.cc.thirdparty.util.XmdWMUtils;
import com.tzx.framework.common.util.JsonDateValueProcessor;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.task.po.redis.dao.TaskRedisDao;
import com.tzx.weixin.common.model.Gps;
import com.tzx.weixin.common.util.MapUtil;

import eleme.openapi.sdk.api.entity.message.OMessage;
import eleme.openapi.sdk.api.entity.order.OOrder;
import eleme.openapi.sdk.api.entity.order.OrderList;
import eleme.openapi.sdk.api.entity.product.OBatchModifiedFailure;
import eleme.openapi.sdk.api.entity.product.OBatchModifiedResult;
import eleme.openapi.sdk.api.entity.product.OCategory;
import eleme.openapi.sdk.api.entity.product.OItem;
import eleme.openapi.sdk.api.entity.product.OItemSellingTime;
import eleme.openapi.sdk.api.entity.product.OItemTime;
import eleme.openapi.sdk.api.entity.product.OLabel;
import eleme.openapi.sdk.api.entity.product.OSpec;
import eleme.openapi.sdk.api.entity.shop.OShop;
import eleme.openapi.sdk.api.entity.ugc.OpenapiOrderRate;
import eleme.openapi.sdk.api.entity.user.OUser;
import eleme.openapi.sdk.api.enumeration.order.OInvalidateType;
import eleme.openapi.sdk.api.enumeration.order.OOrderStatus;
import eleme.openapi.sdk.api.enumeration.product.OItemCreateProperty;
import eleme.openapi.sdk.api.enumeration.product.OItemUpdateProperty;
import eleme.openapi.sdk.api.enumeration.product.OItemWeekEnum;
import eleme.openapi.sdk.api.enumeration.shop.OShopProperty;
import eleme.openapi.sdk.api.enumeration.ugc.ReplyType;
import eleme.openapi.sdk.api.exception.BusinessException;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.MessageService;
import eleme.openapi.sdk.api.service.OrderService;
import eleme.openapi.sdk.api.service.ProductService;
import eleme.openapi.sdk.api.service.ShopService;
import eleme.openapi.sdk.api.service.UgcService;
import eleme.openapi.sdk.api.service.UserService;
import eleme.openapi.sdk.oauth.response.Token;

public class EleMeManager extends AbstractThirdPartyManager {

	private final Logger logger = LoggerFactory.getLogger(EleMeManager.class);

	private TaskRedisDao taskRedisDao = (TaskRedisDao) SpringConext
			.getBean(TaskRedisDao.NAME);
	protected static ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();

	public EleMeManager(String tenantId, String shopId) {
		super(tenantId, shopId, Constant.ELE_CHANNEL);
	}

	public EleMeManager(String tenantId) {
		super(tenantId, Constant.ELE_CHANNEL);
	}

	@Override
	protected void postShopInfo(JSONObject params) throws Exception {
		JSONObject imgObject = new JSONObject();
		imgObject.put("img_url", params.optString("shop_logo"));
		String imgHash = postImageUrl(imgObject);
		ShopService shopService = new ShopService(ElmUtils.getConfig(tenantId,
				shopId), ElmUtils.getToken(tenantId, shopId));
		Map<OShopProperty, Object> properties = new HashMap<OShopProperty, Object>();
		properties.put(OShopProperty.addressText, params.optString("address"));
		properties.put(OShopProperty.geo, params.optString("longitude") + ","
				+ params.optString("latitude"));
		properties
				.put(OShopProperty.agentFee, params.optString("delivery_fee"));
		properties.put(OShopProperty.closeDescription,
				params.optString("close_description"));
		properties.put(OShopProperty.deliverDescription,
				params.optString("deliver_description"));

		String coordinateStr = params.optString("coordinate");
		String coordinates = "";
		if (CommonUtil.checkStringIsNotEmpty(coordinateStr)) {
			String[] coordinateStrArray = coordinateStr.split(";");

			for (int i = 0; i < coordinateStrArray.length; i++) {
				String[] location = coordinateStrArray[i].split(",");
				Gps gps = MapUtil.bd09_To_Gcj02(Double.valueOf(location[1]),
						Double.valueOf(location[0]));
				String lon = new BigDecimal(gps.getWgLon()).toPlainString();
				String lat = new BigDecimal(gps.getWgLat()).toPlainString();
				coordinates += ",[" + lon + "," + lat + "]";
			}
			coordinates = coordinates.replaceFirst(",", "");
		}

		String geo_json = "{\"type\":\"FeatureCollection\",\"features\":[{\"geometry\": {\"type\": \"Polygon\", \"coordinates\": [["
				+ coordinates
				+ "]]}, \"type\": \"Feature\", \"properties\": {\"area_agent_fee\": "
				+ params.optDouble("delivery_fee")
				+ ",\"delivery_price\": "
				+ params.optDouble("min_order_price")
				+ ",\"manual_weight\":0,\"system_weight\":0,\"weight_type\":1}}]}";
		properties.put(OShopProperty.deliverGeoJson, geo_json);
		properties.put(OShopProperty.description,
				params.optString("description"));
		properties.put(OShopProperty.name, params.optString("name"));
		properties.put(
				OShopProperty.isBookable,
				params.optString("is_bookable").equals("") ? "0" : params
						.optString("is_bookable"));
		properties.put(OShopProperty.openTime,
				params.optString("business_time_format"));
		String orgPhone = params.optString("phone");
		String phone = "";
		if (orgPhone.contains("-")) {
			String[] phoneValue = orgPhone.split("-");
			phone = phoneValue[0] + phoneValue[1];
		} else {
			phone = orgPhone;
		}
		properties.put(OShopProperty.phone, phone);
		properties.put(OShopProperty.promotionInfo,
				params.optString("promotion_info"));
		properties.put(OShopProperty.logoImageHash, imgHash);
		properties.put(
				OShopProperty.invoice,
				params.optString("invoice_support").equals("2") ? "0" : params
						.optString("invoice_support"));
		properties.put(OShopProperty.invoiceMinAmount,
				params.optString("min_invoice_fee"));
		properties.put(OShopProperty.noAgentFeeTotal,
				params.optString("no_agent_fee_total"));
		properties.put(OShopProperty.isOpen, params.optString("is_valid"));
		properties.put(OShopProperty.packingFee,
				String.valueOf(params.optDouble("package_box_price", 0.0)));
		properties.put(OShopProperty.openId, params.optString("third_shop_id"));// 152300793
		try {
			OShop oShop = shopService.updateShop(
					params.optLong("third_shop_id"), properties);
			if (oShop != null && null != oShop) {
				response.put("errno", "0");
			}
		} catch (ServiceException e) {
			response.put("errno", 500);
			response.put("errno", e.getMessage());
			e.printStackTrace();
		}

		JSONObject redisJson = new JSONObject();
		redisJson.put("shop_id", shopId + "@" + tenantId);
		taskRedisDao.save(params.optString("third_shop_id").getBytes(),
				redisJson);
	}

	@Override
	public JSONObject getRestaurantOwn(JSONObject params)
			throws ServiceException {
		JSONObject shopIDs = new JSONObject();
		try {
			UserService userService = new UserService(ElmUtils.getConfig(
					tenantId, shopId), ElmUtils.getToken(tenantId, shopId));
			OUser user = userService.getUser();
			shopIDs.put("data", user.getAuthorizedShops());
		} catch (Exception e) {
			shopIDs.put("errno", 500);
			if (e.getMessage().contains("token结果没空")) {
				shopIDs.put("error", "token结果为空,请先授权!");
			} else {
				shopIDs.put("error", e.getMessage());
			}
			e.printStackTrace();
		}

		return shopIDs;
	}

	@Override
	protected void postDishCategory(JSONObject params) throws Exception {

		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		getCcBusinessLogBean(params, ccBusniessLogBean);
		ccBusniessLogBean
				.setCmd("com.tzx.cc.thirdparty.bo.imp.EleMeManager:postDishCategory");
		ProductService productService = new ProductService(ElmUtils.getConfig(
				tenantId, shopId), ElmUtils.getToken(tenantId, shopId));
		try {
			OCategory category = productService.createCategory(
					params.optLong("third_shop_id"),
					params.optString("cur_class_name"),
					params.optString("description"));
			JSONObject jsonObject = new JSONObject();
			if (category != null && null != category) {
				jsonObject.put("food_category_id", category.getId());
				response.put("errno", "0");
				response.put("data", jsonObject);
				ccBusniessLogBean.setResponseBody(JSONObject.fromObject(
						category).toString());
			}
		} catch (ServiceException e) {
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			response.put("errno", 500);
			if (e.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			e.printStackTrace();
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			response.put("errno", 500);
			if (e.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
	}

	@Override
	protected void updateDishCategory(JSONObject params) throws Exception {

		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		getCcBusinessLogBean(params, ccBusniessLogBean);
		ccBusniessLogBean
				.setCmd("com.tzx.cc.thirdparty.bo.imp.EleMeManager:updateDishCategory");

		ProductService productService = new ProductService(ElmUtils.getConfig(
				tenantId, shopId), ElmUtils.getToken(tenantId, shopId));
		try {
			OCategory category = productService.updateCategory(
					params.optLong("third_class_id"),
					params.optString("cur_class_name"),
					params.optString("description"));
			if (category != null && null != category) {
				response.put("errno", "0");
				ccBusniessLogBean.setResponseBody(JSONObject.fromObject(
						category).toString());
			}

		} catch (ServiceException e) {
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			response.put("errno", 500);
			if (e.getMessage().contains("token结果没空，不能进行存储")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			e.printStackTrace();
		} catch (Exception e) {
			response.put("errno", 500);
			if (e.getMessage().contains("token结果没空，不能进行存储")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}

	}

	public void getCcBusinessLogBean(JSONObject params,
			CcBusniessLogBean ccBusniessLogBean) {
		ccBusniessLogBean.setRequestId(params.optString("requestId"));
		ccBusniessLogBean.setTenancyId(tenantId);
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dishCategory");
		ccBusniessLogBean.setChannel(params.optString("channel"));
		ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
		ccBusniessLogBean.setRequestBody(params.toString());

		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("0");

		// 做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		if (params.containsKey("dishes_class")) {
			ccBusniessLogBean.setThirdId(params.optJSONArray("dishes_class")
					.getJSONObject(0).optString("third_class_id"));
			ccBusniessLogBean.setTzxId(params.optJSONArray("dishes_class")
					.getJSONObject(0).optString("class_id"));
			ccBusniessLogBean.setTzxName(params.optJSONArray("dishes_class")
					.getJSONObject(0).optString("cur_class_name"));
			ccBusniessLogBean.setShopId(shopId);
		} else {
			ccBusniessLogBean.setThirdId(params.optString("third_class_id"));
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
			ccBusniessLogBean.setTzxName(params.optString("cur_class_name"));
			ccBusniessLogBean.setShopId(shopId);
		}

		// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
		ccBusniessLogBean.setOperAction(DishOper.pushDishCategory.toString());
	}

	// token结果没空，不能进行存储

	@SuppressWarnings("unchecked")
	@Override
	public JSONObject setCategoryPositions(JSONObject params) throws Exception {
		ProductService productService = new ProductService(ElmUtils.getConfig(
				tenantId, shopId), ElmUtils.getToken(tenantId, shopId));
		List<Long> categoryIds = params.optJSONArray("categoryIds");
		try {
			// at 2017-11-08 张勇 此接口饿了么在2017.12.1禁用
			// productService.setCategoryPositions(
			// params.optLong("third_shop_id"), categoryIds);

			// 使用最新接口
			productService.setCategorySequence(params.optLong("third_shop_id"),
					categoryIds);

			// end 2017-11-08 张勇
			response.put("errno", "0");
		} catch (ServiceException e) {
			response.put("errno", 500);
			if (e.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			e.printStackTrace();
		}
		return response;
	}

	// 获取图片hash值
	protected String postImageUrl(JSONObject params) throws Exception {

		// 上传图片失败的时候，记录失败原因流水记录
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		ccBusniessLogBean.setRequestId(params.optString("requestId"));
		ccBusniessLogBean.setTenancyId(tenantId);
		ccBusniessLogBean.setShopId(params.optString("store_id"));
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dish");
		ccBusniessLogBean.setChannel(params.optString("channel"));
		ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
		ccBusniessLogBean
				.setCmd("com.tzx.cc.thirdparty.bo.imp:EleMeManager.postImageUrl");
		ccBusniessLogBean.setRequestBody(params.toString());

		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("1");

		// 做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		if (params.containsKey("dishes")) {
			ccBusniessLogBean.setThirdId(params.optJSONArray("dishes")
					.getJSONObject(0).optString("third_item_id"));
			ccBusniessLogBean.setTzxId(params.optJSONArray("dishes")
					.getJSONObject(0).optString("item_id"));
			ccBusniessLogBean.setTzxName(params.optJSONArray("dishes")
					.getJSONObject(0).optString("item_name"));
		} else {
			ccBusniessLogBean.setThirdId(params.optString("third_item_id"));
			ccBusniessLogBean
					.setTzxId(String.valueOf(params.optLong("item_id")));
			ccBusniessLogBean.setTzxName(params.optString("item_name"));
		}

		// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
		ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

		ProductService productService = new ProductService(ElmUtils.getConfig(
				tenantId, shopId), ElmUtils.getToken(tenantId, shopId));
		String imgHash = "";
		try {
			if (CommonUtil.checkStringIsNotEmpty(params.optString("img_url"))) {
				imgHash = productService.uploadImageWithRemoteUrl(params
						.optString("img_url"));
			}
			response.put("message", "ok");
			ccBusniessLogBean.setResponseBody(imgHash);
		} catch (ServiceException e) {
			response.put("errno", 1017);
			if (e.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} catch (Exception e) {
			response.put("errno", 1017);
			if (e.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		return imgHash;
	}

	// 确认订单
	@Override
	public JSONObject postOrderStatus(JSONObject params) throws Exception {

		String eleme_order_id = params.optString("eleme_order_id");
		String operType = params.optString("status");

		OrderService orderService = new OrderService(ElmUtils.getConfig(
				tenantId, shopId), ElmUtils.getToken(tenantId, shopId));
		try {

			switch (operType) {
			case "2":// 系统确认订单

				orderService.confirmOrderLite(eleme_order_id);

				response.put("errno", "0");
				response.put("message", "ok");
				break;
			case "-1":// 系统取消订单
				orderService.cancelOrderLite(eleme_order_id,
						OInvalidateType.restaurantTooBusy,
						params.optString("reason"));

				response.put("errno", "0");
				response.put("message", "ok");
				break;
			default:
				response.put("errno", -1);
				response.put("error", "不支持的订单操作:" + operType);
			}

		} catch (ServiceException s) {
			// at 20170821 解决2.0因订单确认过或取消的状态下，天子星能正常接单
			// response.put("errno", s.getCode());
			response.put("errno", "0");
			response.put("error", s.getMessage());
		}

		logger.info("[饿了么2.0订单状态变更]上传数据:操作类型：" + operType + ",饿了么订单号"
				+ eleme_order_id + "\n返回信息:" + response);
		return response;
	}

	@Override
	public JSONObject getOrderStatus(JSONObject params) throws Exception {
		JSONObject result = new JSONObject();
		try {
			String elmOrderId = params.optString("third_order_id");
			OrderService orderService = new OrderService(ElmUtils.getConfig(
					tenantId, shopId), ElmUtils.getToken(tenantId, shopId));

			OOrder order = orderService.getOrder(elmOrderId);
			result.put("status", order.getStatus());

		} catch (Exception ex) {
			ex.printStackTrace();
			logger.error("获取饿了么订单状态时发生异常:", ex);
		}
		return result;
	}

	@Override
	public JSONObject getShopCategory(JSONObject params) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public JSONObject postOrderModelStatus(JSONObject param, String string)
			throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected void delDishCategory(JSONObject params) throws Exception {
		// TODO Auto-generated method stub

	}

	// 删除饿了么2.0菜品信息
	@Override
	protected void delDish(JSONObject params) throws Exception {

		// CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();

		// ccBusniessLogBean.setTenancyId(tenantId);
		// ccBusniessLogBean.setShopId(params.optString("store_id"));
		// ccBusniessLogBean.setCategory("cc");
		// ccBusniessLogBean.setType("dish");
		// ccBusniessLogBean.setChannel(params.optString("channel"));
		// ccBusniessLogBean.setChannelName(params.optString("channel"));//
		// 暂时保持原来结构不变，暂时就不去处理该字段内容值
		// ccBusniessLogBean
		// .setCmd("com.tzx.cc.thirdparty.bo.imp.EleMeManager:delDish");
		// ccBusniessLogBean.setRequestBody(params.toString());
		//
		// ccBusniessLogBean.setCreateTime(new Date().getTime());
		// ccBusniessLogBean.setIsNormal("1");
		// ccBusniessLogBean.setIsThird("1");

		// 做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		// if (params.containsKey("dishes")) {
		// ccBusniessLogBean.setThirdId(params.optJSONArray("dishes")
		// .getJSONObject(0).optString("third_item_id"));
		// ccBusniessLogBean.setTzxId(params.optJSONArray("dishes")
		// .getJSONObject(0).optString("item_id"));
		// ccBusniessLogBean.setTzxName(params.optJSONArray("dishes")
		// .getJSONObject(0).optString("item_name"));
		// } else {
		// ccBusniessLogBean.setThirdId(params.optString("third_item_id"));
		// ccBusniessLogBean
		// .setTzxId(String.valueOf(params.optLong("item_id")));
		// ccBusniessLogBean.setTzxName(params.optString("item_name"));
		// }

		// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
		// ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

		Long footId = params.optLong("third_item_id");
		if (footId == 0) {
			throw new Exception("请输入饿了么的菜品id");
		}
		try {
			String shopId = params.optString("store_id");
			ProductService productService = new ProductService(
					ElmUtils.getConfig(tenantId, shopId), ElmUtils.getToken(
							tenantId, shopId));

			// at 2017-11-08 饿了么2017-12-01时禁用
			// productService.removeItem(footId);

			productService.invalidItem(footId);

			// at 2017-11-08 end
			response.put("errno", "0");
			response.put("message", "ok");

			// ccBusniessLogBean.setResponseBody(response.toString());

		} catch (ServiceException s) {
			response.put("errno", s.getCode());
			if (s.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", s.getMessage());
			}
			// ccBusniessLogBean.setErrorBody(s.getMessage());
		} catch (Exception e) {
			response.put("errno", 500);
			if (e.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
		}

		// CcBusinessLogUtils.log(ccBusniessLogBean);

		logger.info("[饿了么2.0商户批量删除菜品信息]上传数据:" + footId + "\n返回数据:" + response);
	}

	@Override
	protected void postDish(List<JSONObject> params) throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	protected void postDishSingle(JSONObject p) throws Exception {

		// 埋点接口方法日志
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		ccBusniessLogBean.setRequestId(p.optString("requestId"));
		ccBusniessLogBean.setTenancyId(tenantId);
		ccBusniessLogBean.setShopId(p.optString("store_id"));
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dish");
		ccBusniessLogBean.setChannel(p.optString("channel"));
		ccBusniessLogBean.setChannelName(p.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
		ccBusniessLogBean
				.setCmd("com.tzx.cc.thirdparty.bo.imp:EleMeManager.postDishSingle");
		ccBusniessLogBean.setRequestBody(p.toString());
		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("1");

		// 做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		if (p.containsKey("dishes")) {
			ccBusniessLogBean.setThirdId(p.optJSONArray("dishes")
					.getJSONObject(0).optString("third_item_id"));
			ccBusniessLogBean.setTzxId(p.optJSONArray("dishes")
					.getJSONObject(0).optString("item_id"));
			ccBusniessLogBean.setTzxName(p.optJSONArray("dishes")
					.getJSONObject(0).optString("item_name"));
		} else {
			ccBusniessLogBean.setThirdId(p.optString("third_item_id"));
			ccBusniessLogBean.setTzxId(String.valueOf(p.optLong("item_id")));
			ccBusniessLogBean.setTzxName(p.optString("item_name"));
		}

		// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
		ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

		// JSONObject imgObject = new JSONObject();

		// JSONObject response = new JSONObject();

		p.put("img_url", p.optString("photo1"));

		String img = "";
		try {
			// at 2017-06-02增加前端可选是进行上传图片，1代表上传，0或其它值代表不上传
			if ("1".equals(p.optString("isUploadImg"))) {
				img = postImageUrl(p);
			}

			ProductService productService = new ProductService(
					ElmUtils.getConfig(tenantId, shopId), ElmUtils.getToken(
							tenantId, shopId));
			Map<OItemCreateProperty, Object> properties = new HashMap<OItemCreateProperty, Object>();
			properties.put(OItemCreateProperty.name, p.optString("item_name"));
			properties.put(OItemCreateProperty.description,
					p.optString("description"));
			properties.put(OItemCreateProperty.imageHash, img);// 3077080f760e7bf0fc985e23dd3e36e2
			OLabel oLabel = new OLabel();
			oLabel.setIsFeatured(p.optInt("is_featured"));
			oLabel.setIsGum(p.optInt("is_gum"));
			oLabel.setIsNew(p.optInt("is_new"));
			oLabel.setIsSpicy(p.optInt("is_spicy"));
			properties.put(OItemCreateProperty.labels, oLabel);
			List<OSpec> oSpecs = new ArrayList<OSpec>();
			
			String unitName = "";
			if (p.containsKey("priceList")) {
				JSONArray priceObjects = p.getJSONArray("priceList");
				JSONObject unit = priceObjects.getJSONObject(0);
				unitName = unit.optString("unit_name");
			} else {
				unitName = p.optString("unit");
			}
			
			OSpec oSpec = new OSpec();
			/*20180305 新增菜品推送，单一规格传规格id为0即可，日后改造多规格再做调整  */
			oSpec.setSpecId(0);
			oSpec.setName(unitName);
			oSpec.setPrice(Double.isNaN(p.optDouble("default_price")) ? 0.00
					: p.optDouble("default_price"));
			oSpec.setStock(p.optInt("stock"));
			oSpec.setMaxStock(p.optInt("max_stock"));
			oSpec.setPackingFee(Double.isNaN(p.optDouble("box_price",0.0)) ? 0.00
					: p.optDouble("box_price",0.0));
			oSpec.setOnShelf(1);
			oSpec.setExtendCode(p.optString("item_code"));
			// oSpec.setBarCode("X148948686356666");
			oSpec.setWeight(p.optInt("rank"));
			oSpecs.add(oSpec);
			
			properties.put(OItemCreateProperty.specs, oSpecs);
			OItemSellingTime oItemSellingTime = new OItemSellingTime();
			List<OItemWeekEnum> weeks = new ArrayList<OItemWeekEnum>();
			weeks.add(OItemWeekEnum.MONDAY);
			weeks.add(OItemWeekEnum.TUESDAY);
			weeks.add(OItemWeekEnum.WEDNESDAY);
			weeks.add(OItemWeekEnum.THURSDAY);
			weeks.add(OItemWeekEnum.FRIDAY);
			weeks.add(OItemWeekEnum.SATURDAY);
			weeks.add(OItemWeekEnum.SUNDAY);
			oItemSellingTime.setWeeks(weeks);

			// 开始日期以当前日期减1天，结束日期加10年
			Calendar cBeginDate = Calendar.getInstance();
			Calendar cEndDate = Calendar.getInstance();
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			cBeginDate.add(Calendar.DAY_OF_WEEK, -1);
			cEndDate.add(Calendar.YEAR, 10);
			oItemSellingTime.setBeginDate(df.format(cBeginDate.getTime()));
			oItemSellingTime.setEndDate(df.format(cEndDate.getTime()));

			List<OItemTime> times = new ArrayList<OItemTime>();
			OItemTime oItemTime = new OItemTime();
			oItemTime.setBeginTime(p.optString("available_times_start"));
			oItemTime.setEndTime(p.optString("available_times_end"));
			times.add(oItemTime);
			oItemSellingTime.setTimes(times);
			properties.put(OItemCreateProperty.sellingTime, oItemSellingTime);
			
			// List<OItemTime> times = new ArrayList<OItemTime>();
			// 多时段售卖 2017-12-19 start 刘娟
			// JSONObject available_times_obj = new JSONObject();
			// if(CommonUtil.checkStringIsNotEmpty(p.optString("dish_available_time")))
			// {
			// Map<String, List<JSONObject>> timezone = new HashMap<String,
			// List<JSONObject>>();
			// String dish_available_time = p.optString("dish_available_time");
			// JSONObject obj = JSONObject.fromObject(dish_available_time);
			// Iterator<String> iterator = obj.keys();
			// int weekCount = 0;
			// while(iterator.hasNext()){
			// String weekDay = iterator.next();
			// weekCount++;
			// String timeStr = obj.optString(weekDay,"");
			// if(CommonUtil.checkStringIsNotEmpty(timeStr)){
			// String[] range = timeStr.split(",");
			// List<JSONObject> timeList = new ArrayList<JSONObject>();
			// for(String temp :range){
			//
			// OItemTime oItemTime = new OItemTime();
			// JSONObject rangeObj = JSONObject.fromObject("{}");
			// String start = temp.substring(0,temp.indexOf("-"));
			// String end = temp.substring(temp.indexOf("-")+1);
			// oItemTime.setBeginTime(start);
			// oItemTime.setEndTime(end);
			// times.add(oItemTime);
			// }
			// break;
			// }
			// }
			// }
			// end

			// changhui 2018-1-15 添加商品单位规格参数上传到饿了么 start
			properties.put(OItemCreateProperty.unit, unitName);
			// end

			OItem item = null;
			if (response.optString("message").equals("ok")
					|| !CommonUtil.checkStringIsNotEmpty(img)) {
				item = productService.createItem(p.optLong("third_class_id"),
						properties);
			}
			// 保持和1.0接口兼容
			if (item != null) {
				response.put("code", 200);
				response.put("message", "ok");
				response.put("request_id", item.getId());
				JSONObject dataObj = new JSONObject();
				dataObj.put("food_id", item.getId());
				/*20180307 推送完成,增加对菜品规格列表的回写  */
				if(item.getSpecs()!=null && !item.getSpecs().isEmpty()){
					List<JSONObject> eleSpecs = new ArrayList<JSONObject>();
					NumberFormat numF = NumberFormat.getInstance();
					numF.setGroupingUsed(false);
					for(OSpec ospec : item.getSpecs()){
						if(ospec != null){
							JSONObject espec = new JSONObject();
							espec.put("specId", numF.format(ospec.getSpecId()));//规格id
							espec.put("name", ospec.getName());
							espec.put("price", ospec.getPrice());
							eleSpecs.add(espec);
						}
					}
					dataObj.put("norms", JSONArray.fromObject(eleSpecs).toString());
				}
				response.put("data", dataObj);
				ccBusniessLogBean.setResponseBody(JSONObject.fromObject(item)
						.toString());
			}
		} catch (BusinessException e) {
			if (e.getCode().equals("BIZ_BIZ_INVALID_PARAMETER")
					|| e.getCode().equals("BIZ_BIZ_ITEM_NOT_EXIST")) {
				response.put("errno", 1016);
				updateDish(p);
			} else {
				response.put("errno", 500);
			}
			if (e.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} catch (Exception e) {
			response.put("errno", 500);
			if (e.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}

	}

	@Override
	protected void updateDish(JSONObject p) throws Exception {

		// 埋点接口方法日志
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		ccBusniessLogBean.setRequestId(p.optString("requestId"));
		ccBusniessLogBean.setTenancyId(tenantId);
		ccBusniessLogBean.setShopId(p.optString("store_id"));
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dish");
		ccBusniessLogBean.setChannel(p.optString("channel"));
		ccBusniessLogBean.setChannelName(p.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
		ccBusniessLogBean
				.setCmd("com.tzx.cc.thirdparty.bo.imp:EleMeManager.updateDish");
		ccBusniessLogBean.setRequestBody(p.toString());
		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("1");

		ccBusniessLogBean.setThirdId(p.optString("third_item_id"));
		ccBusniessLogBean.setTzxId(String.valueOf(p.optLong("item_id")));
		ccBusniessLogBean.setTzxName(p.optString("item_name"));

		// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
		ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

		// JSONObject imgObject = new JSONObject();
		p.put("img_url", p.optString("image"));
		try {
			String imgHash = "";
			String isUploadImg = p.optString("isUploadImg");
			// at 2017-06-02增加前端可选是进行上传图片，1代表上传，0或其它值代表不上传
			if ("1".equals(isUploadImg)) {
				imgHash = postImageUrl(p);
			}

			if (response.optString("message").equals("ok")
					|| !"1".equals(isUploadImg)) {
				ProductService productService = new ProductService(
						ElmUtils.getConfig(tenantId, shopId),
						ElmUtils.getToken(tenantId, shopId));
				Map<OItemUpdateProperty, Object> properties = new HashMap<OItemUpdateProperty, Object>();
				properties.put(OItemUpdateProperty.name,
						p.optString("item_name"));
				properties.put(OItemUpdateProperty.description,
						p.optString("description"));

				if ("1".equals(isUploadImg)) {
					properties.put(OItemUpdateProperty.imageHash, imgHash);// 3077080f760e7bf0fc985e23dd3e36e2
				}

				OLabel oLabel = new OLabel();
				oLabel.setIsFeatured(p.optInt("is_featured"));
				oLabel.setIsGum(p.optInt("is_gum"));
				oLabel.setIsNew(p.optInt("is_new"));
				oLabel.setIsSpicy(p.optInt("is_spicy"));
				properties.put(OItemUpdateProperty.labels, oLabel);
				List<OSpec> oSpecs = new ArrayList<OSpec>();
				String unitName = "";
				if (p.containsKey("priceList")) {
					JSONArray priceObjects = p.getJSONArray("priceList");
					JSONObject unit = priceObjects.getJSONObject(0);
					unitName = unit.optString("unit_name");
				} else {
					unitName = p.optString("unit");
				}

				/*20180305 当前多规格字段，单一规格保留三方规格id其他更新，多个规格以三方平台为准 */
				if(p.containsKey("norms") && !p.optJSONArray("norms").isEmpty()){
					JSONArray specs = p.optJSONArray("norms");
					if(null != specs && specs.size()>0){
						if(specs.size() == 1){
							JSONObject spec = JSONObject.fromObject(specs.get(0));
							OSpec oSpec = new OSpec();
							oSpec.setSpecId(spec.optLong("specId"));
							oSpec.setName(unitName);
							oSpec.setPrice(Double.isNaN(p.optDouble("default_price")) ? 0.00
									: p.optDouble("default_price"));
							oSpec.setStock(p.optInt("stock"));
							oSpec.setMaxStock(p.optInt("max_stock"));
							oSpec.setPackingFee(Double.isNaN(p.optDouble("box_price",0.0)) ? 0.00
									: p.optDouble("box_price",0.0));
							oSpec.setOnShelf(1);
							oSpec.setExtendCode(p.optString("item_code"));
							oSpec.setWeight(p.optInt("rank"));
							oSpecs.add(oSpec);
							properties.put(OItemUpdateProperty.specs, oSpecs);
						}else{
							for (int i = 0; i < specs.size(); i++) {
								JSONObject spec = JSONObject.fromObject(specs.get(i));
								OSpec oSpec = new OSpec();
								oSpec.setSpecId(spec.optLong("specId"));
								oSpec.setName(spec.optString("name"));
								oSpec.setPrice(spec.optDouble("price"));
								oSpec.setStock(p.optInt("stock"));
								oSpec.setMaxStock(p.optInt("max_stock"));
								oSpec.setPackingFee(Double.isNaN(p.optDouble("box_price",0.0)) ? 0.00
										: p.optDouble("box_price",0.0));
								oSpec.setOnShelf(1);
								oSpec.setExtendCode(p.optString("item_code"));
								oSpec.setWeight(p.optInt("rank"));
								oSpecs.add(oSpec);
								properties.put(OItemUpdateProperty.specs, oSpecs);
							}
							
						}
						
					}
				}else{
					
					OSpec oSpec = new OSpec();
					oSpec.setSpecId(0);
					oSpec.setName(unitName);
					oSpec.setPrice(Double.isNaN(p.optDouble("default_price")) ? 0.00
							: p.optDouble("default_price"));
					oSpec.setStock(p.optInt("stock"));
					oSpec.setMaxStock(p.optInt("max_stock"));
					oSpec.setPackingFee(Double.isNaN(p.optDouble("box_price",0.0)) ? 0.00
							: p.optDouble("box_price",0.0));
					oSpec.setOnShelf(1);
					oSpec.setExtendCode(p.optString("item_code"));
					oSpec.setWeight(p.optInt("rank"));
					oSpecs.add(oSpec);
					properties.put(OItemUpdateProperty.specs, oSpecs);
				}
				
				// JSONObject jsonObj = p.optString("");
				OItemSellingTime oItemSellingTime = new OItemSellingTime();
				List<OItemWeekEnum> weeks = new ArrayList<OItemWeekEnum>();
				weeks.add(OItemWeekEnum.MONDAY);
				weeks.add(OItemWeekEnum.TUESDAY);
				weeks.add(OItemWeekEnum.WEDNESDAY);
				weeks.add(OItemWeekEnum.THURSDAY);
				weeks.add(OItemWeekEnum.FRIDAY);
				weeks.add(OItemWeekEnum.SATURDAY);
				weeks.add(OItemWeekEnum.SUNDAY);
				oItemSellingTime.setWeeks(weeks);
				// 开始日期以当前日期减1天，结束日期加10年
				Calendar cBeginDate = Calendar.getInstance();
				Calendar cEndDate = Calendar.getInstance();
				DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
				cBeginDate.add(Calendar.DAY_OF_WEEK, -1);
				cEndDate.add(Calendar.YEAR, 10);
				oItemSellingTime.setBeginDate(df.format(cBeginDate.getTime()));
				oItemSellingTime.setEndDate(df.format(cEndDate.getTime()));

				List<OItemTime> times = new ArrayList<OItemTime>();
				// 多时段售卖 2017-12-19 start 刘娟
				JSONObject available_times_obj = new JSONObject();
				if (CommonUtil.checkStringIsNotEmpty(p
						.optString("dish_available_time"))) {
					Map<String, List<JSONObject>> timezone = new HashMap<String, List<JSONObject>>();
					String dish_available_time = p
							.optString("dish_available_time");
					JSONObject obj = JSONObject.fromObject(dish_available_time);
					Iterator<String> iterator = obj.keys();
					int weekCount = 0;
					while (iterator.hasNext()) {
						String weekDay = iterator.next();
						weekCount++;
						String timeStr = obj.optString(weekDay, "");
						if (CommonUtil.checkStringIsNotEmpty(timeStr)) {
							String[] range = timeStr.split(",");
							List<JSONObject> timeList = new ArrayList<JSONObject>();
							for (String temp : range) {

								OItemTime oItemTime = new OItemTime();
								JSONObject rangeObj = JSONObject
										.fromObject("{}");
								String start = temp.substring(0,
										temp.indexOf("-"));
								String end = temp
										.substring(temp.indexOf("-") + 1);
								oItemTime.setBeginTime(start);
								oItemTime.setEndTime(end);
								times.add(oItemTime);
							}
							break;
						}
					}
				}
				// end
				// if(org.apache.commons.lang.StringUtils.isNotEmpty(p.optString("dish_available_time")))
				// {
				// JSONObject jsonObj = p.optJSONObject("dish_available_time");
				// }else{
				// OItemTime oItemTime = new OItemTime();
				// oItemTime.setBeginTime(p.optString("available_times_start"));
				// oItemTime.setEndTime(p.optString("available_times_end"));
				// times.add(oItemTime);
				// }
				oItemSellingTime.setTimes(times);
				properties.put(OItemUpdateProperty.sellingTime,
						oItemSellingTime);

				// changhui 2018-1-15 添加商品单位规格参数上传到饿了么 start
				properties.put(OItemUpdateProperty.unit, unitName);
				// end

				OItem item = null;
				item = productService.updateItem(p.optLong("third_item_id"),
						p.optLong("third_class_id"), properties);

				// 保持和1.0接口兼容
				if (item != null) {
					response.put("code", 200);
					response.put("message", "ok");
					response.put("request_id", item.getId());
					JSONObject dataObj = new JSONObject();
					dataObj.put("food_id", item.getId());
					/*20180307 推送完成,增加对菜品规格列表的回写  */
					if(item.getSpecs()!=null && !item.getSpecs().isEmpty()){
						List<JSONObject> eleSpecs = new ArrayList<JSONObject>();
						NumberFormat numF = NumberFormat.getInstance();
						numF.setGroupingUsed(false);
						for(OSpec ospec : item.getSpecs()){
							if(ospec != null){
								JSONObject espec = new JSONObject();
								espec.put("specId", numF.format(ospec.getSpecId()));//规格id
								espec.put("name", ospec.getName());
								espec.put("price", ospec.getPrice());
								eleSpecs.add(espec);
							}
						}
						dataObj.put("norms", JSONArray.fromObject(eleSpecs).toString());
					}
					response.put("data", dataObj);
					ccBusniessLogBean.setResponseBody(JSONObject.fromObject(
							item).toString());
				}
			}
		} catch (BusinessException e) {
			if (e.getCode().equals("BIZ_BIZ_INVALID_PARAMETER")) {
				response.put("errno", 1020);
			} else {
				response.put("errno", 500);
			}
			response.put("error", e.getMessage());
			ccBusniessLogBean.setIsNormal("0");
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			e.printStackTrace();
		} catch (Exception e) {
			response.put("errno", 500);
			if (e.getMessage().contains("token结果没空")) {
				response.put("error", "token结果为空,请先授权!");
			} else {
				response.put("error", e.getMessage());
			}
			ccBusniessLogBean.setIsNormal("0");
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}

	}

	@Override
	protected void postShopStatus(JSONObject params) throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	protected void postDeliveryTime(JSONObject params) throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	protected void postDeliveryRegion(JSONObject params) throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public JSONObject bindRestaurantId(JSONObject params, String method)
			throws Exception {

		String restaurant_id = params.optString("third_shop_id");
		String tp_restaurant_id = params.optString("shop_id");
		String version = params.optString("version");
		try {
			ElmUtils.saveShopRelation(tenantId, restaurant_id,
					tp_restaurant_id, version);
			response.put("errno", "0");
		} catch (Exception e) {
			response.put("errno", "-1");
			response.put("error", e.getMessage());
			e.printStackTrace();
		}

		return response;
	}

	// at 20170607自动拉单接口
	public List<OMessage> queryEleNonReachedOrder() throws Exception {
		int appId = Integer
				.parseInt(com.tzx.framework.common.constant.Constant.systemMap
						.get("ele_appid"));
		try {
			logger.info("拉取饿了么2.0订单信息开始，商户号：" + tenantId + ",门店号:" + shopId);
			Token token = ElmUtils.getToken(tenantId, shopId);
			if (token != null && StringUtils.isEmpty(token.getAccessToken())) {
				MessageService messageService = new MessageService(
						ElmUtils.getConfig(tenantId, shopId), token);

				return messageService.getNonReachedOMessages(appId);
			}

		} catch (Exception ignore) {

		}

		return null;
	}

	/**
	 * 获取饿了么 指定时间内的评论信息
	 */
	public List<JSONObject> getEleShopComments(String tenantId, String shopId,
			String thirdShopId, String startTime, String endTime, int offset,
			int pageSize) throws Exception {
		List<JSONObject> list = new ArrayList<>();

		UgcService ugcService = new UgcService(ElmUtils.getConfig(tenantId,
				shopId), ElmUtils.getToken(tenantId, shopId));
		List<OpenapiOrderRate> result = ugcService.getOrderRatesByShopId(
				thirdShopId, startTime, endTime, offset, pageSize);
		for (OpenapiOrderRate openapiOrderRate : result) {
			JsonConfig jsonConfig = new JsonConfig();
			jsonConfig.registerJsonValueProcessor(Date.class,
					new JsonDateValueProcessor());
			JSONObject obj = JSONObject
					.fromObject(openapiOrderRate, jsonConfig);
			System.out.println(obj);
			list.add(obj);
		}
		return list;
	}

	/**
	 * 获取饿了么 指定时间内的评论信息
	 * 
	 * @param talentId
	 *            商铺ID
	 * @param shopId
	 *            本地店铺ID
	 * @param rateId
	 */
	public boolean replyEleShopComment(String tenantId, String shopId,
			String rateId, String comments) throws Exception {

		UgcService ugcService = new UgcService(ElmUtils.getConfig(tenantId,
				shopId), ElmUtils.getToken(tenantId, shopId));
		try {
			ugcService.replyRateByRateId(rateId, ReplyType.ORDER, comments);
		} catch (Exception e) {
			logger.info("ReplyExcep Ele reply: rateId:{} reason:{}", rateId,
					e.getMessage());
			return false;
		}
		return true;
	}

	/**
	 * 获取饿了么 指定时间内 多条评论
	 * 
	 * @param talentId
	 *            商铺ID
	 * @param shopId
	 *            本地店铺ID
	 * @param rateId
	 */
	public boolean replyEleShopComments(String tenantId, String shopId,
			List<String> rateIds, String comments) throws Exception {

		UgcService ugcService = new UgcService(ElmUtils.getConfig(tenantId,
				shopId), ElmUtils.getToken(tenantId, shopId));
		try {
			ugcService.replyRateByRateIds(rateIds, ReplyType.ORDER, comments);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	private Map<String, String> map = new HashMap<String, String>();

	public List<String> queryThirdOrderList(String tenantId, String tzxShopId,
			String date, int type, List<String> lists) throws Exception {
		List<String> messageList = new ArrayList<String>();
		JSONObject message = new JSONObject();

		OrderService orderService = new OrderService(ElmUtils.getConfig(
				tenantId, shopId), ElmUtils.getToken(tenantId, tzxShopId));

		for (String orderIdOrSeq : lists) {
			OOrder order = null;

			if (type == 1) {

				int seq = Integer.valueOf(orderIdOrSeq);

				orderIdOrSeq = "";

				DBContextHolder.setTenancyid(tenantId);

				List<JSONObject> resultJson = dao
						.query4Json(
								tenantId,
								"select third_shop_id from cc_third_organ_info where channel='EL09' and shop_id="
										+ tzxShopId + "");
				if (resultJson != null && resultJson.size() > 0) {
					String elm_shop_id = resultJson.get(0).optString(
							"third_shop_id", "");

					if (!StringUtils.isEmpty(elm_shop_id)) {

						OrderList orderList = orderService.getAllOrders(
								Long.valueOf(elm_shop_id), 1, 1, date);
						// 默认按序号倒序排序
						List<OOrder> list = orderList.getList();

						if (list.size() > 0) {
							order = list.get(0);
							int daySN = order.getDaySn();
							// 如果序号不相等，表示饿了么取出的号是最大序号，然后再次查询，页数变为：最大序号-当前序号+1
							if (daySN != seq) {
								order = null;
								if (daySN >= seq) {
									orderList = orderService.getAllOrders(
											Long.valueOf(elm_shop_id), (daySN
													- seq + 1), 1, date);
									list = orderList.getList();

									order = list.get(0);
								}
							}
						}
					}
				}

			}

			if (!StringUtils.isEmpty(orderIdOrSeq)) {
				order = orderService.getOrder(orderIdOrSeq);
			}

			if (order != null) {
				if (OOrderStatus.pending.equals(order.getStatus())
						|| OOrderStatus.valid.equals(order.getStatus())
						|| OOrderStatus.settled.equals(order.getStatus())
						|| OOrderStatus.unprocessed.equals(order.getStatus())) {
					message.put("tzxStatus", "10");
					message.put("type", 10);
				} else {
					message.put("tzxStatus", "08");
					message.put("type", 14);
				}
				String createAt = CommonUtil.getDateFromSeconds(String
						.valueOf(order.getCreatedAt().getTime() / 1000));
				JSONObject orderJson = JSONObject.fromObject(order);
				orderJson.put("createdAt", createAt);

				message.put("thirdOrderCode", order.getId());
				message.put("message", orderJson);
				message.put("tzxCreateAt", createAt);
				message.put("shopId", order.getShopId());

				messageList.add(message.toString());

				logger.info("商户：" + tenantId + ",门店：" + tzxShopId
						+ ",获取饿了么订单详情完成->" + message);

			} else {
				logger.info("商户：" + tenantId + ",门店：" + tzxShopId
						+ ",未获取饿了么订单信息...");

			}
		}
		return messageList;
	}

	@Override
	protected void updateDishStockSingle(JSONObject p) throws Exception {
		String shopId = String.valueOf(p.optInt("store_id"));
		ProductService productService = new ProductService(ElmUtils.getConfig(
				tenantId, shopId), ElmUtils.getToken(tenantId, shopId));
		List<JSONObject> list = new ArrayList<JSONObject>();
		if (p.containsKey("stockList")
				&& p.getJSONArray("stockList").size() > 0) {
			list = p.getJSONArray("stockList");
		} else {
			list.add(p);
		}
		Map<Long, OItem> map = batchGetItems(list, shopId);
		Map<Long, Integer> stockMap = new HashMap<Long, Integer>();
		if (p.containsKey("stockList")
				&& p.getJSONArray("stockList").size() > 0) {
			for (JSONObject jsonObject : list) {
				OItem oitem = map.get(jsonObject.optLong("third_item_id"));
				stockMap.put(oitem.getSpecs().get(0).getSpecId(),
						p.optInt("stock"));
			}
		} else {
			OItem oitem = map.get(p.optLong("third_item_id"));
			stockMap.put(oitem.getSpecs().get(0).getSpecId(), p.optInt("stock"));
		}
		OBatchModifiedResult result = productService.batchUpdateStock(stockMap);
		if (result.getFailures().size() > 0) {
			response.put("stockResult", ERROR);
			List<OBatchModifiedFailure> obatch = result.getFailures();
			List<Long> errorIds = new ArrayList<Long>();
			for (OBatchModifiedFailure o : obatch) {
				errorIds.add(o.getId());
			}
			response.put("errorThirdIds", errorIds);
		} else {
			response.put("stockResult", SUCCESS);
		}
		logger.info("[饿了么更新菜品库存]上传数据:" + stockMap + "\n返回数据:" + response);
	}

	private Map<Long, OItem> batchGetItems(List<JSONObject> list, String shopId)
			throws Exception {
		ProductService productService = new ProductService(ElmUtils.getConfig(
				tenantId, shopId), ElmUtils.getToken(tenantId, shopId));
		List<Long> itemIds = new ArrayList<Long>();
		for (JSONObject jsonObj : list) {
			itemIds.add(jsonObj.optLong("third_item_id"));
		}
		Map<Long, OItem> map = productService.batchGetItems(itemIds);
		return map;
	}
}
