package com.tzx.cc.baidu.service;

import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * 
 */
public interface SuweiService {
	final static String NAME = "com.tzx.cc.baidu.service.impl.SuweiServiceImpl";

	JSONObject thirdSuweiApplayOrder(JSONObject jsobj, JSONObject responseJson)
			throws Exception;
	
	String thirdSuweiApplayOrderReq(JSONObject jsobj,JSONObject thirdJosn) throws Exception;

	void thirdSuweiNotifySaas(JSONObject jsobj, JSONObject responseJson)throws Exception;

	void thirdSuweiApplayOrderAfter(JSONObject jsobj, JSONObject responseJson,
			String result) throws Exception;
	
	JSONObject getSuweiStoreConfig(String tenantId,String storeId) throws Exception;
}
