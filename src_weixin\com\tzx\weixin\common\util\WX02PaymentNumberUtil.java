package com.tzx.weixin.common.util;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.tzx.cc.bo.OrderManagementHqService;
import com.tzx.crm.base.constant.Oper;
import com.tzx.crm.bo.BonusPointManageService;
import com.tzx.crm.bo.CardTransactionService;
import com.tzx.crm.bo.dto.Data;
import com.tzx.ehk.bo.EhkPayService;
import com.tzx.framework.common.exception.CustomErrorCode;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.SystemCustomException;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.newcrm.bo.CardRechargeService;
import com.tzx.payment.news.dao.WechatDao;
import com.tzx.weixin.bo.ElectronicInvoiceService;
import com.tzx.weixin.bo.New2WxDishService;
import com.tzx.weixin.bo.NewMyOrderService;
import com.tzx.weixin.bo.NewPaymentService;
import com.tzx.weixin.bo.NewSendTemplateMasgService;
import com.tzx.weixin.bo.NewWxCouponsService;
import com.tzx.weixin.bo.NewWxCustomerCardService;
import com.tzx.weixin.bo.NewWxCustomerMemberService;
import com.tzx.weixin.bo.NewWxRewardService;

import net.sf.json.JSONException;
import net.sf.json.JSONObject;

@Component("WX02PaymentNumberUtil")
public class WX02PaymentNumberUtil {
    @Resource(name = NewWxCustomerCardService.NAME)
    private NewWxCustomerCardService newWxCustomerCardService;
    @Resource(name = CardTransactionService.NAME)
    private CardTransactionService cardTransactionService;
    @Resource(name = NewPaymentService.NAME)
    private NewPaymentService newPaymentService;
    @Resource(name = NewWxCouponsService.NAME)
    private NewWxCouponsService newWxCouponsTicketService;
    private static final Logger logger = Logger.getLogger(WX02PaymentNumberUtil.class);
    @Resource(name = New2WxDishService.NAME)
    private New2WxDishService newWxDishService;
    @Resource(name = NewMyOrderService.NAME)
    private NewMyOrderService newMyOrderService;

    @Resource(name = OrderManagementHqService.NAME)
    private OrderManagementHqService orderManagementHqService;
    @Resource(name = ElectronicInvoiceService.NAME)
    private ElectronicInvoiceService electronicInvoiceService;
    @Resource(name = NewSendTemplateMasgService.NAME)
    private NewSendTemplateMasgService newSendTemplateMasgService;
    @Resource(name = NewWxRewardService.NAME)
    private NewWxRewardService newWxRewardService;
    @Resource(name = CardRechargeService.NAME)
    private CardRechargeService cardRechargeService;
    @Resource(name = NewWxCustomerMemberService.NAME)
    private NewWxCustomerMemberService newWxCustomerMemberService;
    
    @Resource(name = EhkPayService.NAME)
    private EhkPayService ehkPayService;

    public JSONObject paymentOrderNumber(JSONObject jb) {
        JSONObject result = new JSONObject();
        result.put("success", false);
        String order_num = "";
        String tenantId = "";
        try {
            //设置动态数据源
            tenantId = jb.optString("tenantId");
            logger.info("调用方法入口");
            if (tenantId.length() > 0) {
                //设置数据源
                String type = jb.optString("type1");
                switch (type) {
                    case "card":
                        logger.info("卡充值的异步任务");
                        String store_id = jb.optString("store_id");
                        String order_code = jb.optString("order_code");
                        Data rechargeCard = newWxCustomerCardService.rechargeCard(tenantId, jb);
                        if (rechargeCard != null) {
                            try {
//								cardTransactionService.customerCardRecharge(rechargeCard);
                                cardRechargeService.requiresnewRecharge(rechargeCard);
                                result.put("success", true);
                            } catch (SystemException e) {
                                ErrorCode error = e.getErrorCode();
                                logger.info(ExceptionMessage.getExceptionMessage(e));
//								//退款接口
//								jb = newPaymentService.refundOrder(tenantId, jb.optString("store_id"), jb.optString("order_code"), jb.optDouble("income"));
//								if(jb.getBoolean("success")){
//									newPaymentService.MemberAfterRefundOrder(tenantId, store_id, order_code);
//								}
                                jb.put("type", "recharge");
                                newWxCustomerCardService.getAllWechatPayDate(tenantId, jb);
                                logger.info(e.getMessage());
                                logger.info("卡充值微信将错误信息放入redis中：" + jb);
                                logger.info(ExceptionMessage.getExceptionMessage(e));
                                result.put("success", false);
                                result.put("refund_success", jb.optBoolean("success"));
                                result.put("refund_err_msg", jb.optString("err_msg"));
                                result.put("err_msg", PropertiesLoader.getProperty(error.getNumber()));
                            } catch (SystemCustomException e) {
                                CustomErrorCode error = e.getCustomErrorCode();
                                String mes = PropertiesLoader.getProperty(error.getNumber());
                                List<JSONObject> list1 = error.getMes();
                                for (JSONObject jo1 : list1) {
                                    if (jo1.containsKey("key") && jo1.containsKey("value")) {
                                        mes = mes.replace(jo1.optString("key"), jo1.optString("value"));
                                    }
                                }
//								//退款接口
//								jb = newPaymentService.refundOrder(tenantId, jb.optString("store_id"), jb.optString("order_code"), jb.optDouble("income"));
//								if(jb.getBoolean("success")){
//									newPaymentService.MemberAfterRefundOrder(tenantId, store_id, order_code);
//								}
                                jb.put("type", "recharge");
                                newWxCustomerCardService.getAllWechatPayDate(tenantId, jb);
                                logger.info(e.getMessage());
                                logger.info("卡充值微信将错误信息放入redis中：" + jb);
                                logger.info(ExceptionMessage.getExceptionMessage(e));
                                result.put("err_msg", mes);
                                result.put("success", false);
                                result.put("refund_success", jb.optBoolean("success"));
                                result.put("refund_err_msg", jb.optString("err_msg"));
                            } catch (JSONException e) {
                                StringWriter sw = new StringWriter();
                                PrintWriter pw = new PrintWriter(sw);
                                e.printStackTrace(pw);
//								//退款接口
//								jb = newPaymentService.refundOrder(tenantId, jb.optString("store_id"), jb.optString("order_code"), jb.optDouble("income"));
//								if(jb.getBoolean("success")){
//									newPaymentService.MemberAfterRefundOrder(tenantId, store_id, order_code);
//								}
                                jb.put("type", "recharge");
                                newWxCustomerCardService.getAllWechatPayDate(tenantId, jb);
                                logger.info(e.getMessage());
                                logger.info("卡充值微信将错误信息放入redis中：" + jb);
                                logger.info(ExceptionMessage.getExceptionMessage(e));
                                result.put("err_msg", "》》e:" + sw.toString());
                                result.put("success", false);
                                result.put("refund_success", jb.optBoolean("success"));
                                result.put("refund_err_msg", jb.optString("err_msg"));
                            } catch (Exception e) {
                                StringWriter sw = new StringWriter();
                                PrintWriter pw = new PrintWriter(sw);
                                e.printStackTrace(pw);
//								//退款接口
//								jb = newPaymentService.refundOrder(tenantId, jb.optString("store_id"), jb.optString("order_code"), jb.optDouble("income"));
//								if(jb.getBoolean("success")){
//									newPaymentService.MemberAfterRefundOrder(tenantId, store_id, order_code);
//								}
                                jb.put("type", "recharge");
                                newWxCustomerCardService.getAllWechatPayDate(tenantId, jb);
                                logger.info(e.getMessage());
                                logger.info("卡充值微信将错误信息放入redis中：" + jb);
                                logger.info(ExceptionMessage.getExceptionMessage(e));
                                result.put("err_msg", "》》e:" + sw.toString());
                                result.put("success", false);
                                result.put("refund_success", jb.optBoolean("success"));
                                result.put("refund_err_msg", jb.optString("err_msg"));
                            }
                        } else {
                            if (jb.optBoolean("success") && !"notpay".equals(jb.optString("status"))) {
                                result = jb;
                            } else {
                                result = jb;
                                result.put("success", false);
                            }
                        }
                        break;
                    case "order":
                        logger.info("微信店内点餐查询订单状态的");
                        result = newWxDishService.orderSuccess(tenantId, jb);
                        if ("notpay".equals(result.optString("status"))) {
                            result.put("success", false);
                        }
                        break;
                    case "orderFailure":
                        logger.info("微信店内点餐延时取消点单（支付失败的）");
                        result = newMyOrderService.cancelOrderFor(tenantId, jb);
                        break;
                    /*case "orderCancel":
						logger.info("微信店内点餐延时退单（门店延时取单）");
						String payment = jb.optString("payment");
						JSONObject sureOrderState = newWxDishService.sureOrderState(tenantId, jb);
						if(null!=sureOrderState){
							if(sureOrderState.optString("order_state").equals("01")||
									sureOrderState.optString("order_state").equals("02")
									||sureOrderState.optString("order_state").equals("03")){
								if(payment.equals("wechat")){
									//微信支付的
									result = newWxDishService.refundOrder(tenantId, jb);
								}else if(payment.equals("card")){
									//卡支付
									logger.info("微信店内点餐卡支付退款");
									result = newWxDishService.notUseCardForPayOrder1(tenantId, jb);
									//取消电子发票
									try {
										electronicInvoiceService.cansleElectronicInvoiceUrl(tenantId, jb);
									} catch (Exception e) {
										e.printStackTrace();
									}
								}else if(payment.equals("coupons")){
									//优惠劵
									result = newWxDishService.notUseCoupon(tenantId, jb);
									//取消电子发票
									try {
										electronicInvoiceService.cansleElectronicInvoiceUrl(tenantId, jb);
									} catch (Exception e) {
										e.printStackTrace();
									}
								}else  if(payment.equals("card_credit")){
									result = newWxDishService.notUseCredit(tenantId, jb);
									//取消电子发票
									try {
										electronicInvoiceService.cansleElectronicInvoiceUrl(tenantId, jb);
									} catch (Exception e) {
										e.printStackTrace();
									}
								}
								
								//撤销微信增加积分
								backoutCreditDispose(tenantId, jb.optInt("store_id"), jb.optString("order_code"),jb.optString("type"));
								
								if(result.optBoolean("success")){
									//微信推送信息
									newSendTemplateMasgService.sendCanselOrder(tenantId, jb);
									//下发门店
									orderManagementHqService.orderCancel(tenantId, jb);
								}
								
							}
						}else{
							result.put("success", true);
						}
						break;*/
                    case "buy_coupon":
                        logger.info("新微信购买优惠劵查询");
                        order_num = jb.optString("order_num");
                        result = newWxCouponsTicketService.buyCouponSuccess(tenantId, jb);
                        break;
                    case "ds":
                        logger.info("打赏微信支付成功");
                        result = newWxRewardService.rewardPaymentSuccess(tenantId, jb);
                        break;
                    case "wechat_pay":
                        logger.info("微信支付报错的");
                        newWxCustomerCardService.isWeChatPayCanRefund(tenantId, jb);
                        break;
                    case "admission":
                        logger.info("微信入会费异步任务调用");
                        result = newWxCustomerMemberService.payShopAdmissionSuccess(tenantId, jb);
                        break;
                    case "buy_membership":
                        logger.info("微信购买会籍异步任务调用");
                        result = newWxCustomerMemberService.queryBuyCustomerLevelOrderState(tenantId, jb);
                        break;
                    case "card1":
                        logger.info("新微信会员卡充值异步任务调用");
                        result = newWxCustomerMemberService.memberCardOrderState(tenantId, jb);
                        break;
                    case "refund_order":
                        logger.info("微信支付退款异步任务调用");
                        result = newWxCustomerMemberService.refundOrder(tenantId, jb);
                        break;
                    case "ehk_pay_state":
                    	logger.info("e惠客支付宝或微信支付状态轮询查询");
                    	logger.info("e惠客支付宝或微信支付状态轮询查询参数["+jb.toString()+"]");
						try {
							//轮询处理订单状态
							result = ehkPayService.payOrderUpdate(tenantId,jb);
						} catch (Exception e) {
							e.printStackTrace();
							logger.error("e惠客支付宝或微信支付状态轮询失败:"+e);
						}
                    	logger.info("e惠客支付宝或微信支付状态轮询结果["+result.toString()+"]");
                    	break;
                    default:
                        break;
                }
            }
        } catch (SystemException e) {
            ErrorCode error = e.getErrorCode();
            logger.info("wx异常原因：" + PropertiesLoader.getProperty(error.getNumber()) + ",错误码：" + error.getNumber());
            logger.info(ExceptionMessage.getExceptionMessage(e));
        } catch (Exception e) {
            result.put("success", false);
            result.put("error_msg", e.getMessage());
            logger.info(e.getMessage());
            ;
        }
        return result;
    }

    private void backoutCreditDispose(String tenancy_id, int store_id, String order_no, String type) {
        logger.info("进入撤销微信会员积分：tenancy_id=" + tenancy_id +
                ",store_id=" + store_id + ",order_no=" + order_no);
        try {
            backoutCredit(tenancy_id, store_id, order_no, type);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("撤销微信会员积分时数据出现错误");
        }
    }

    private void backoutCredit(String tenancy_id, int store_id, String order_no, String type) {
        //查询是否可撤销积分
        String sql = "select * from cc_order_credit where "
                + "tenancy_id='" + tenancy_id + "' and store_id='" +
                store_id + "' and order_code='" + order_no + "'";

        JSONObject object = null;
        try {
            List<JSONObject> list = dao.query4Json(tenancy_id, sql);
            if (list == null || list.size() < 1) {
                return;    //没有查到增加积分信息
            }
            object = list.get(0);
        } catch (Exception e) {
            e.printStackTrace();
            //没有查到增加积分信息
            return;
        }

        //准备调用接口参数
        com.tzx.crm.bo.dto.Data data = new com.tzx.crm.bo.dto.Data();
        data.setOper(Oper.update);
        data.setStore_id(store_id);
        data.setTenancy_id(tenancy_id);
        JSONObject bean = new JSONObject();
        List<JSONObject> list1 = new ArrayList<JSONObject>();
        bean.put("card_code", object.optString("card_code"));// 卡号
        bean.put("old_bill_code", object.optString("bill_code"));//交易流水单号
        bean.put("bill_code", order_no);//订单号
        bean.put("chanel", type);// 渠道
        bean.put("operator", "admin");// 操作人员
        bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
        list1.add(bean);
        data.setData(list1);
        String status = "";
        try {
            cardTransactionService.customerCardConsume(data);
            status = "2";//撤销成功
        } catch (Exception e) {
            status = "3";//撤销成功
            e.printStackTrace();
        }

        //更新数据库状态
        try {
            String sql1 = "update wx_credit_log set status=" + status + " where bill_code='" + order_no + "'";
            dao.updateIgnorCase(tenancy_id, "wx_credit_log", object);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("撤销微信会员卡保存到数据库时出错");
        }
    }

    @Resource(name = WechatDao.NAME)
    private WechatDao dao;

    @Resource(name = BonusPointManageService.NAME)
    private BonusPointManageService bonusPointManageService;
}
