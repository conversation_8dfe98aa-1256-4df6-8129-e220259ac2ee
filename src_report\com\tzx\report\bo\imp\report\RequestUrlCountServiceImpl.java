package com.tzx.report.bo.imp.report;


import com.tzx.framework.common.util.DateUtil;
import com.tzx.report.bo.report.RequestUrlCountService;
import com.tzx.report.po.springjdbc.dao.report.RequestUrlCountDao;
import net.sf.json.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by zds on 2018-11-02.
 */
@Service(RequestUrlCountService.NAME)
public class RequestUrlCountServiceImpl implements RequestUrlCountService{

    private static Logger LOG = LoggerFactory.getLogger(RequestUrlCountServiceImpl.class);

    @Resource(name = RequestUrlCountDao.NAME)
    private RequestUrlCountDao requestUrlCountDao;

    @Override
    public void updateCount(JSONObject jsonObject) {
        String url = jsonObject.optString("reqUrl");
        String tenancyId = jsonObject.optString("tenentid");
        String hashUrl = DigestUtils.md5Hex(url);
        Integer menuId = jsonObject.optInt("menuId");

        try {
            int count = requestUrlCountDao.getCountByUrl(tenancyId, hashUrl);
            if(count >0 ){
                requestUrlCountDao.updateCount(tenancyId,hashUrl);
            }else{
                JSONObject obj = new JSONObject();
                obj.put("url", url);
                obj.put("hash_url", hashUrl);
                obj.put("menu_id", menuId);
                obj.put("tenancy_id", tenancyId);
                obj.put("count", 1);
                obj.put("update_time", DateUtil.getNowDateYYDDMMHHMMSS());
                requestUrlCountDao.saveCount(obj);
            }
        } catch (Exception e) {
            LOG.error("updateCount failed", e);
            LOG.error("updateCount failed... params={}", jsonObject);
        }

    }
}
