package com.tzx.cc.datasync.bo.imp;

import com.tzx.cc.datasync.bo.SyncSystemDataRepairService;
import com.tzx.cc.datasync.bo.util.SynIdUtils;
import com.tzx.cc.datasync.po.springjdbc.dao.DataTransferDao;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.MultiDataSourceManager;
import com.tzx.hq.bo.SysDictionaryService;
import net.sf.json.JSON;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

/**
 * Created by XUGY on 2017-03-14.
 */
@Service(SyncSystemDataRepairService.NAME)
public class SyncSystemDataRepairServiceImpl implements SyncSystemDataRepairService {
    private static final Logger logger = Logger.getLogger(SyncSystemDataRepairServiceImpl.class);


    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Resource(name = DataTransferDao.NAME)
    private DataTransferDao dataTransferDao;

    @Override
    public synchronized JSONObject repairSynData(JSONObject serviceParams) throws Exception {
        JSONObject result_flag = new JSONObject();
        repairPriceAndOrgan(serviceParams);
        Map<String, Map<String, Object>> mapIds = new HashMap<String, Map<String, Object>>();
        repairHqItemClass(mapIds,serviceParams);
        repairHqItemInfo(mapIds,serviceParams);
        repairHqItemPaymentWay(serviceParams);
        repairHqItemGroup(serviceParams);
        repairHqItemMenu(serviceParams);

        result_flag.put("success", "true");
        result_flag.put("msg", "成功");
        return result_flag;
    }

    /**
     * 删除餐谱相关
     * @param serviceParams
     * @throws Exception
     */
    private void repairHqItemMenu(JSONObject serviceParams) throws Exception {
        String tenancyId = serviceParams.optString("tenancyid");
        List<String> sqls = new ArrayList<String>();

        sqls.add("delete from hq_item_menu");
        sqls.add("delete from hq_item_menu_details");
        sqls.add("delete from hq_item_menu_class");
        sqls.add("delete from hq_item_menu_organ");

        String[] sqlArray = null;
        sqlArray = (String[]) sqls.toArray(new String[sqls.size()]);
        this.dao.getJdbcTemplate(tenancyId).batchUpdate(sqlArray);
    }

    /**
     * 修复菜品组
     * @param serviceParams
     * @throws Exception
     */
    private void repairHqItemGroup(JSONObject serviceParams) throws Exception {
        String tenancyId = serviceParams.optString("tenancyid");
        List<String> sqls = new ArrayList<String>();
        //为hq_item_group矫正fake_id
        sqls.add("update hq_item_group set fake_id = id");
        //为hq_item_group_details表矫正fake_id
        sqls.add("update hq_item_group_details set fake_id = id");
        //为hq_item_combo_details表矫正fake_id
        sqls.add("update hq_item_combo_details set fake_id = id");

        sqls.add("delete from hq_item_menu");
        sqls.add("delete from hq_item_menu_details");
        sqls.add("delete from hq_item_menu_class");
        sqls.add("delete from hq_item_menu_organ");


        String[] sqlArray = null;
        sqlArray = (String[]) sqls.toArray(new String[sqls.size()]);
        this.dao.getJdbcTemplate(tenancyId).batchUpdate(sqlArray);

        //矫正序列
        this.dao.execute(tenancyId,"select setval('public.hq_item_group_id_seq', max(id)) from hq_item_group");
        this.dao.execute(tenancyId,"select setval('public.hq_item_group_details_id_seq', max(id)) from hq_item_group_details");
        this.dao.execute(tenancyId,"select setval('public.hq_item_combo_details_id_seq', max(id)) from hq_item_combo_details");

    }


    //修复付款方式数据
    private void repairHqItemPaymentWay(JSONObject serviceParams) throws Exception {

        //修复payment_way数据
        String tenancyId = serviceParams.optString("tenancyid");
        List<JSONObject> rifPaymentClass = dataTransferDao.findPreTransferData(serviceParams, "TZXERP.ERP_PAYMENTS");
        String sql ="select * from payment_way";
        List<JSONObject> saasPayMentClass = this.dao.query4Json(tenancyId, sql);
        for(JSONObject saasJson:saasPayMentClass){
            for(JSONObject rifJson:rifPaymentClass){
                if(StringUtils.equals(saasJson.optString("id"),rifJson.optString("id"))) {
                    saasJson.put("fake_id",saasJson.optString("id"));
                    break;
                }
            }
        }
        this.dao.updateBatchIgnorCase(tenancyId,"payment_way",saasPayMentClass);

        //修复payment_way_of_ogran的数据

        StringBuffer sb = new StringBuffer();
        sb.append("update payment_way_of_ogran set fake_id = id where EXISTS(");
        sb.append(" select 1 from payment_way where fake_id is not null and payment_way_of_ogran.payment_id = id)");
        this.dao.execute(tenancyId,sb.toString());
    }

    /**修复价格体系和organ的数据
     *
     * @param serviceParams
     */
    private void repairPriceAndOrgan(JSONObject serviceParams) throws Exception {
        String tenancyId = serviceParams.optString("tenancyid");
        List<String> sqls = new ArrayList<String>();
        //为价格体系表矫正fake_id
        sqls.add("update hq_price_system set fake_id = id");
        //为organ表矫正fake_id
        sqls.add("update organ set fake_id = id");
        String[] sqlArray = null;
        sqlArray = (String[]) sqls.toArray(new String[sqls.size()]);
        this.dao.getJdbcTemplate(tenancyId).batchUpdate(sqlArray);

        //为价格体系更新序列
        this.dao.execute(tenancyId,"select setval('public.hq_price_system_id_seq', max(id)) from hq_price_system");
        //为organ更新序列
        this.dao.execute(tenancyId,"select setval('public.organ_id_seq', max(id)) from organ");
    }

    /**
     * 修复菜品类别
     * @param serviceParams
     */
    private void repairHqItemClass( Map<String, Map<String, Object>> mapIds,JSONObject serviceParams) throws Exception {
        String tenancyId = serviceParams.optString("tenancyid");
        //删除默认菜品类别的所有数据
        String sql = "delete from hq_item_class where chanel = 'MD01' or (fake_id is not null and fake_type is null)";
        this.dao.execute(tenancyId,sql);
        //修正菜品类别的序列 值
        sql = "select setval('public.hq_item_class_id_seq', max(id)) from hq_item_class";
        this.dao.execute(tenancyId,sql);

        //---------------修正默认渠道菜品类别的数据  begin   2017年3月15日16:24:18
        //获取默认渠道门店的数据
        List<JSONObject> hqItemClassMd01 = dataTransferDao.findPreTransferData(serviceParams, "TZXERP.ERP_ORGSORTS");

        Queue<String> generateIds1 = SynIdUtils.generateIds(tenancyId, "public.hq_item_menu_class_id_seq", hqItemClassMd01.size());

        Map<String, Object> hq_item_class_map = new HashMap<String,Object>();
        mapIds.put("hq_item_class_1",hq_item_class_map);
        for (JSONObject result_obj : hqItemClassMd01) {
            String hq_item_class_id = generateIds1.poll();
            hq_item_class_map.put(result_obj.optString("id"), hq_item_class_id);
        }
        List<String> listStringAdd = new ArrayList<String>();
        StringBuffer sqlbuffer = null;
        for (JSONObject result_obj : hqItemClassMd01) {
            sqlbuffer = new StringBuffer();
            sqlbuffer.append("insert into hq_item_class(tenancy_id,fake_id,fake_type,id,father_id,chanel,itemclass_code,itemclass_name,valid_state) values('");
            sqlbuffer.append(tenancyId).append("','").append(result_obj.optString("id"));
            sqlbuffer.append("','1','").append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_1", result_obj.optString("id")));
            sqlbuffer.append("','").append(SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_1", result_obj.optString("father_id")));
            sqlbuffer.append("','").append(result_obj.optString("chanel")).append("','");
            sqlbuffer.append(result_obj.optString("itemclass_code")).append("','");
            sqlbuffer.append(result_obj.optString("itemclass_name")).append("','1')");
            listStringAdd.add(sqlbuffer.toString());
        }
        String[] sqlArray = null;
        int size = listStringAdd.size();
        if (size < 1) {
            logger.info("rif修复默认渠道菜品类别的时候查不到数据");
            return;
        }
        sqlArray = (String[]) listStringAdd.toArray(new String[size]);
        this.dao.getJdbcTemplate(tenancyId).batchUpdate(sqlArray);
        //---------------修正默认渠道菜品类别的数据  end   2017年3月15日16:24:18


        //--------------修正菜品类别其他渠道的数据  begin    2017年3月15日16:29:27
        sql = "select * from hq_item_class where chanel <> 'MD01'";
        List<JSONObject> hqItemClassOtherChanel = this.dao.query4Json(tenancyId, sql);
        for(JSONObject json :hqItemClassOtherChanel){
            String id = json.optString("id");
            String fatherId = json.optString("father_id");
            json.put("fake_id",json.optString("id"));
            if(StringUtils.equals(fatherId,"0")) {
                json.put("fake_type","2");
            } else {
                json.put("fake_type","3");
            }
        }
        this.dao.updateBatchIgnorCase(tenancyId,"hq_item_class",hqItemClassOtherChanel);
        //--------------修正菜品类别其他渠道的数据  end    2017年3月15日16:29:27
    }

    /**
     * 修复菜品信息
     * @param serviceParams
     * @throws Exception
     */
    private void repairHqItemInfo( Map<String, Map<String, Object>> mapIds,JSONObject serviceParams) throws Exception {
        String tenancyId = serviceParams.optString("tenancyid");
        String sql = "select * from hq_item_info";
        List<JSONObject> hqItemInfoRifData = dataTransferDao.findPreTransferData(serviceParams, "TZXERP.ERP_ITEMINFO");
        List<JSONObject> hqItemInfoSaasData = this.dao.query4Json(tenancyId, sql);
        for(int i = 0;hqItemInfoSaasData!=null && i<hqItemInfoSaasData.size();i++){
            JSONObject saasData = hqItemInfoSaasData.get(i);
            String saasId = saasData.optString("id");
            for(JSONObject rifData:hqItemInfoRifData){
                String rifId = rifData.optString("id");
                if(StringUtils.equals(saasId.trim(),rifId.trim())) {
                    saasData.put("item_class",SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_1",rifData.optString("item_class")));
                }
            }
        }
        this.dao.updateBatchIgnorCase(tenancyId,"hq_item_info",hqItemInfoSaasData);
        sql = "update hq_item_info set fake_id = id";
        this.dao.execute(tenancyId,sql.toString());

        //修正hq_item_unit数据-------------------------------------------------------
        sql = "delete from hq_item_unit a where not EXISTS(select 1 from hq_item_info b where b.id = a.item_id)";
        this.dao.execute(tenancyId,sql.toString());

        sql = "select a.fake_id,b.id from hq_item_info a inner join hq_item_unit b on b.item_id = a.id";
        List<JSONObject> hqItemUnits = this.dao.query4Json(tenancyId, sql);
        this.dao.updateBatchIgnorCase(tenancyId,"hq_item_unit",hqItemUnits);
    }
}
