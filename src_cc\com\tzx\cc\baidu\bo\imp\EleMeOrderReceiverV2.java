package com.tzx.cc.baidu.bo.imp;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.common.constant.util.CcBusinessLogUtils;
import com.tzx.cc.common.constant.util.CcPartitionUtils;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.util.ElmUtils;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

import eleme.openapi.sdk.api.entity.packs.ShopContract;
import eleme.openapi.sdk.api.service.PacksService;

public class EleMeOrderReceiverV2 extends ThirdPartyOrderReceiver {

	/** 店铺食品配送类型：蜂鸟专送 */
	private final String SHOP_FOODSEND_TYPE_FENGNIAO = "蜂鸟专送";
	/** 店铺食品配送类型：众包服务 */
	private final String SHOP_FOODSEND_TYPE_ZHONGBAO = "众包服务";
	/** 店铺食品配送类型：商家自配送 */
	private final String SHOP_FOODSEND_TYPE_ZIPEI = "商家自配送";

	private JSONObject requestOrder;
	private double discount_fee; // 优惠总金额
	private double commission_dish_total = 0.0; // 收取佣金菜品信息合计
	private double product_amount; // 订单商品总金额
	private double send_fee; // 配送费
	private double discountRate; // 优惠比例=优惠总金额/订单商品总金额
	private double platform_side_discount_fee = 0.0; // 平台方承担的优惠总金额
	private double takeawayBusinessIncome; // 外卖的营业收入=用户实付-配送费+平台方承担的金额
	private double food_sharing_date; // 菜品分摊的比例=菜品总计/用户实付-配送费+平台方承担的金额
	private double total_product_fee = 0.0; // 餐品价格
	protected double eleDiscountCommission;// 饿了么佣金金额
	protected double ele_platform_charge_amount = 0.0; // 平台收取金额
	protected double ele_shop_real_amount = 0.0; // 商家收取金额
	private String productProfileName = ""; // 外卖配送方式
	private double comboDiscountRate; // 套餐明细优惠比例=套餐优惠总金额/套餐商品总金额

	private static final Logger logger = Logger
			.getLogger(EleMeOrderReceiver.class);

	public EleMeOrderReceiverV2() {
		super(Constant.ELE_CHANNEL);
	}

	public EleMeOrderReceiverV2(JSONObject order) {
		super(order, Constant.ELE_CHANNEL);

		try {
			String message = thirdPartyOrder.getString("message");
			// 订单信息
			requestOrder = JSONObject.fromObject(message);
			// 第三方饿了么门店id
			String th_shopId = thirdPartyOrder.optString("shopId");

			String[] shopId = ElmUtils.getTenancyInfoByElmShopId(th_shopId);
			this.storeId = shopId[0];
			this.tenantId = shopId[1];

			// 20171218日志中消息内容
			logger.info("饿了么2.0获取新订单缓存中的storeId=" + storeId + ",tenantId="
					+ tenantId + ",消息：" + thirdPartyOrder);

		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	@SuppressWarnings({ "static-access", "deprecation" })
	@Override
	public JSONObject saveOrderList() throws Exception {

		JSONObject orderListParam = new JSONObject();
		// 默认计算方式(平台结算)
		orderListParam.put("settlement_type", checkMode.getSettlement_type());
		// 商户ID
		orderListParam.put("tenancy_id", tenantId);
		// 店铺id
		orderListParam.put("store_id", storeId);
		// 订单号
		orderListParam.put("order_code", orderCode);

		orderListParam.put("report_date",
				DateUtil.format(new Timestamp(System.currentTimeMillis()))
						.toString().substring(0, 10));
		// 饿了么订单编号
		orderListParam.put("third_order_code", requestOrder.optString("id"));
		// 餐厅当日订单序号
		orderListParam.put("chanel_serial_number",
				requestOrder.optString("daySn"));
		// 每天总的流水号
		orderListParam.put("serial_number", flow_code);

		// 送餐时间
		if (requestOrder.containsKey("deliverTime")) {
			String deliverTime = requestOrder.optString("deliverTime");

			if (CommonUtil.checkStringIsNotEmpty(deliverTime)) {
				orderListParam.put("send_time", deliverTime);
			} else {
				orderListParam.put("send_time", "立即配送");
			}

		}
		// 红包金额
		double hongbao = Math.abs(Double.isNaN(requestOrder
				.optDouble("hongbao")) ? 0.00 : requestOrder
				.optDouble("hongbao"));

		// 平台方承担的金额
		platform_side_discount_fee = Math.abs(Double.isNaN(requestOrder
				.optDouble("elemePart", 0.0)) ? 0.00 : requestOrder.optDouble(
				"elemePart", 0.0));

		// // 获取优惠总额， 11 食物活动 12 餐厅活动 15 商家代金券抵扣 200 限时抢购
		// JSONArray orderActivities = requestOrder
		// .getJSONArray("orderActivities");
		// for (int i = 0; i < orderActivities.size(); i++) {
		// JSONObject orderActivitie = orderActivities.getJSONObject(i);
		// int categoryId = orderActivitie.optInt("categoryId");
		// if (categoryId == 11 || categoryId == 12 || categoryId == 15
		// || categoryId == 200) {
		// discount_fee += orderActivitie.optDouble("amount");
		// }
		//
		// }

		// 配送费(2),打包费(102),优惠券(3),饿了么红包(13)
		// JSONArray groups = requestOrder.getJSONArray("groups");
		// for (int j = 0; j < groups.size(); j++) {
		// JSONObject group = groups.getJSONObject(j);
		// String type = group.optString("type");
		// if ("extra".equals(type)) {// type=extra,表示其它费用,normal表示普通商品
		//
		// JSONArray items = group.getJSONArray("items");
		// for (int i = 0; i < items.size(); i++) {
		// JSONObject item = items.getJSONObject(i);
		//
		// if (!item.optString("categoryId").equals("2")
		// && !item.optString("categoryId").equals("102")) {
		// discount_fee += Math.abs(item.optDouble("price"));
		//
		// }
		// if (item.optString("categoryId").equals("2")) {
		// // 配送费
		// send_fee = item.optDouble("price");
		// }
		//
		// }
		// }
		// }

		// 订单活动费用
		// JSONArray orderActivities = requestOrder
		// .getJSONArray("orderActivities");
		// for (int j = 0; j < orderActivities.size(); j++) {
		// JSONObject orderActivitie = orderActivities.getJSONObject(j);
		// discount_fee += Math.abs(orderActivitie.optDouble("amount"));
		// }

		// 配送费
		send_fee = requestOrder.optDouble("deliverFee");

		// 订单总价
		double total_fee = Double
				.isNaN(requestOrder.optDouble("originalPrice")) ? 0.00
				: requestOrder.optDouble("originalPrice");
		orderListParam.put("total_money", total_fee);

		// 用户实付
		double actualPay = Double.isNaN(requestOrder.optDouble("totalPrice")) ? 0.00
				: requestOrder.optDouble("totalPrice");

		discount_fee = Math.abs(requestOrder.optDouble("elemePart"))
				+ Math.abs(requestOrder.optDouble("shopPart")) + hongbao;

		total_discount_fee = discount_fee;
		order_total_price = total_fee;
		user_actual_pay = actualPay;
		// 优惠虚付
		double discount_actual_pay = order_total_price - total_discount_fee;
		if (discount_actual_pay <= 0) {
			// 商户实际享受的优惠
			discount_fee = total_discount_fee - Math.abs(user_actual_pay)
					- Math.abs(discount_actual_pay);
		}

		product_amount = total_fee - send_fee;// 菜品明细加餐盒费和（订单总额-配送费=）

		// takeawayBusinessIncome = Scm.psub(product_amount,
		// Math.abs(requestOrder.optDouble("shopPart", 0.0)));
		// 应收=商家实收+服务费;
		takeawayBusinessIncome = Math.abs(requestOrder.optDouble("income"))
				+ Math.abs(requestOrder.optDouble("serviceFee"));

		// 配送类型
		productProfileName = getShopFoodSendType(tenantId,
				requestOrder.optLong("shopId"), storeId);
		double shopDeliveryFee = send_fee;
		double specialShopFee = total_fee - discount_fee;
		double excess_discount = 0;
		if (CommonUtil.checkStringIsNotEmpty(productProfileName)
				&& productProfileName.contains(SHOP_FOODSEND_TYPE_FENGNIAO)) {// 平台配送
			orderListParam.put("delivery_party", "1");
			double platSpecialShopFee = specialShopFee - send_fee;
			shopDeliveryFee = 0;
			if (platSpecialShopFee > 0) {
				shopFee = platSpecialShopFee;
			} else {// 计算shopFee为负数时
				shopFee = 0;
				discount_fee = discount_fee + platSpecialShopFee;
				excess_discount = 0 - platSpecialShopFee;
			}
		} else {
			orderListParam.put("delivery_party", "2");
			if (specialShopFee > 0) {
				shopFee = specialShopFee;
			} else {
				shopFee = 0;
				excess_discount = 0 - specialShopFee;
			}
		}
		// at 20170908 解决shopFee因小数位产生计算错误的情况
		shopFee = CommonUtil.keepTwoDecimal(shopFee);

		orderListParam.put("discount_amount", Double.isNaN(discount_fee) ? 0.0
				: CommonUtil.keepTwoDecimal(discount_fee));
		orderListParam.put("excess_discount",
				CommonUtil.keepTwoDecimal(excess_discount));
		// 用户实付总价
		orderListParam.put("actual_pay", CommonUtil.keepTwoDecimal(actualPay));

		// ------------start田老师对账报表佣金+实收不等于应收问题修复2017-06-06-------------------------
		double shopRate = Math.abs(requestOrder.optDouble("shopPart"));
		double eleMeRate = Math.abs(requestOrder.optDouble("elemePart"))
				+ hongbao;// 实际为饿了么平台承担+红包，因为红包是负数所以用减
		orderListParam.put("platform_rate",
				CommonUtil.keepTwoDecimal(eleMeRate));// 优惠信息平台承担
		orderListParam.put("shop_rate", CommonUtil.keepTwoDecimal(shopRate));// 优惠信息商户承担
		// ------------end田老师对账报表佣金+实收不等于应收问题修复2017-06-06-------------------------

		// 商户实收总价
		orderListParam.put("shop_fee", CommonUtil.keepTwoDecimal(shopFee));
		// 配送费
		orderListParam.put("meal_costs", CommonUtil.keepTwoDecimal(send_fee));
		// 商家承担的配送费金额
		orderListParam.put("shop_delivery_fee",
				CommonUtil.keepTwoDecimal(shopDeliveryFee));

		// ----------------------------------------
		if (checkMode.getSettlement_type().equals("RIDER")) {
			double tempAmount = Double.valueOf(shopFee
					* (100 - Double.valueOf(checkMode.getDiscount_rate()))
					/ 100);
			BigDecimal bg = new BigDecimal(tempAmount);
			discountR_amount = Double.isNaN(bg
					.setScale(4, RoundingMode.HALF_UP).doubleValue()) ? 0.0
					: bg.setScale(4, RoundingMode.HALF_UP).doubleValue();
		}
		// ----------------------------------------

		// 是否在线支付
		boolean payType = requestOrder.optBoolean("onlinePaid");
		String isOnlinePayment = null;
		String payment_state = null;
		if (payType) {
			isOnlinePayment = "1";

			payment_state = "03";
		} else {
			isOnlinePayment = "0";

			payment_state = "01";
		}

		orderListParam.put("is_online_payment", isOnlinePayment);
		orderListParam.put("payment_state", payment_state);

		// 饿了么内部餐厅ID
		// orderListParam.put("inner_id", requestOrder.optString("inner_id"));

		// -----------------------------------------------

		// 用户id
		// orderListParam.put("user_id", requestOrder.optString("user_id"));
		// 用户名
		// orderListParam.put("user_name", requestOrder.optString("user_name"));

		// 是否预订单
		orderListParam.put("send_immediately",
				requestOrder.optBoolean("book") ? "0" : "1");

		// 是否需要发票
		orderListParam.put("need_invoice",
				requestOrder.optBoolean("invoiced") ? "1" : "0");
		// 发票抬头
		orderListParam.put(
				"invoice_title",
				CommonUtil.checkStringIsNotEmpty(requestOrder
						.optString("invoice")) ? CommonUtil
						.replaceEvilChar(requestOrder.optString("invoice")
								.trim()) : "");
		// 纳税人号
		orderListParam.put(
				"taxpayerid",
				CommonUtil.checkStringIsNotEmpty(requestOrder
						.optString("taxpayerId")) ? CommonUtil
						.replaceEvilChar(requestOrder.optString("taxpayerId")
								.trim()) : "");
		// 订单备注
		orderListParam.put("remark", CommonUtil.replaceEvilChar(requestOrder
				.optString("description").trim()));
		// 物流//饿了么返回值无此字段
		// orderListParam.put("delivery_party",
		// requestOrder.optInt("delivery_party"));
		// 订单创建时间
		orderListParam.put("single_time", requestOrder.optString("createdAt"));
		// 订单生效时间(即支付时间)
		// orderListParam.put("active_at", requestOrder.optString("active_at"));

		// 顾客姓名
		orderListParam.put("consigner", CommonUtil.replaceEvilChar(requestOrder
				.optString("consignee").trim()));
		// 顾客电话
		JSONArray phone = requestOrder.optJSONArray("phoneList");
		List phoneList = phone.toList(phone);
		orderListParam.put("consigner_phone",
				phoneList.size() > 0 ? phoneList.get(0) : "");

		// 订餐人
		orderListParam.put("order_name", CommonUtil
				.replaceEvilChar(requestOrder.optString("userId").trim()));
		// 订餐电话
		orderListParam.put("order_phone",
				phoneList.size() > 0 ? phoneList.get(0) : "");
		// 顾客性别
		// orderListParam.put("sex", requestUser.optInt("gender") == 1 ? "man" :
		// "woman");
		// 渠道
		orderListParam.put("chanel", Constant.ELE_CHANNEL);
		// 送餐地址//送餐周围详情requestOrder.optString("delivery_poi_address")
		orderListParam.put(
				"address",
				CommonUtil.replaceEvilChar(requestOrder.optString(
						"deliveryPoiAddress").trim()));

		String[] delivery_geo = requestOrder.optString("deliveryGeo")
				.split(",");
		// 送餐地址百度经度
		orderListParam.put("longitude", delivery_geo[0]);
		// 送餐地址百度纬度
		orderListParam.put("latitude", delivery_geo[1]);

		// 餐盒费
		orderListParam.put("package_box_fee", CommonUtil.keepTwoDecimal(Double
				.valueOf(requestOrder.optString("packageFee"))));

		// 订单状态
		orderListParam.put("order_state", "01");
		// 订单类型//外卖订单
		orderListParam.put("order_type", "WM02");
		// 优惠类型
		orderListParam.put("discount_mode_id", "7");
		orderListParam.put("third_order_state", "1");
		orderListParam.put("order_state_desc", "待确认");

		// 消息类型 10新订单 14订单被取消
		int type = thirdPartyOrder.optInt("type");
		if (type == 10) {
			orderListParam.put("third_order_state", "1");
			orderListParam.put("order_state_desc", "订单等待餐厅确认");
		} else if (type == 14) {
			orderListParam.put("third_order_state", "-1");
			orderListParam.put("order_state_desc", "订单已取消");
		}

		CcPartitionUtils.lackInsertParam(tenantId, orderListParam);

		orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_list",
				orderListParam));

		log(CcBusniessLogBean.TypeProperty.ORDER_RECIVE, "saveOrderList",
				requestOrder.toString(), orderListParam.toString());

		return orderListParam;
	}

	@Override
	public void saveOrderItem() throws Exception {

		discountRate = product_amount == 0 ? 0
				: ((discount_fee + discountR_amount) / product_amount);
		double discountR_rate = product_amount == 0 ? 0
				: (discountR_amount / product_amount);
		double sendFeeRate = product_amount == 0 ? 0 : (Scm.pdiv(
				Double.valueOf(send_fee), Double.valueOf(product_amount)));
		food_sharing_date = product_amount == 0 ? 0
				: (takeawayBusinessIncome / product_amount);

		JSONObject packageBoxFee = new JSONObject();
		// 查询餐盒信息
		String sql = "SELECT  b.price,B.item_id,B.unit_id,C.item_name,d.item_menu_id FROM cc_meals_info A "
				+ " LEFT JOIN cc_meals_info_default b ON A . ID = b.meals_id LEFT JOIN hq_item_info C ON C . ID = b.item_id LEFT JOIN hq_item_menu_details d on d.item_id=b.item_id"
				+ " LEFT JOIN hq_item_menu_class f on f.details_id=d.id  LEFT JOIN hq_item_menu_organ e on e.item_menu_id=d.item_menu_id"
				+ " WHERE A .store_id = "
				+ storeId
				+ " AND A .channel = 'EL09' and f.chanel='EL09' AND A .meals_type = 'MR03' and e.store_id="
				+ storeId + " ";
		// 修正餐盒费异常时没有明确日志输出的问题
		List<JSONObject> pbList = this.dao.query4Json(tenantId, sql);

		double package_fee = Double.isNaN(requestOrder.optDouble("packageFee")) ? 0.00
				: requestOrder.optDouble("packageFee");

		JSONObject pb = new JSONObject();
		if (null == pbList || pbList.isEmpty()) {
			pb.put("price", package_fee);
			pb.put("item_name", "餐盒费");
			logger.info("[饿了么]收单失败:订单号[" + orderCode + "]->未正确获取餐盒费信息!");
			// throw new Exception("[饿了么]收单失败:订单号[" + orderCode +
			// "]->未正确获取餐盒费信息!");
		} else {
			pb = pbList.get(0);
		}

		double number = package_fee
				/ (pb.optDouble("price", 0) == 0 ? 1 : pb.optDouble("price"));// 餐盒费数量

		JSONObject orderItemParam = null;
		int group_index = 1;
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("shop_id", storeId);

		double share_product_price_total = 0.0; // 总共已经摊多少钱
		String item_ids = "";// 天子星菜品id
		// String th_item_ids = "";// 饿了么的菜品id

		JSONArray group = requestOrder.getJSONArray("groups");

		for (int m = 0; m < group.size(); m++) {
			JSONObject groupRow = group.getJSONObject(m);
			String type = groupRow.optString("type");
			if (!"normal".equals(type)) {// 表示其它费用，不是菜品
				continue;
			}
			JSONArray items = groupRow.getJSONArray("items");
			for (int i = 0; i < items.size(); i++) {
				JSONObject item = items.getJSONObject(i);
				int extendcode = item.optInt("extendCode", 0);
				if (!StringUtils.isEmpty(String.valueOf(extendcode))) {
					item_ids += extendcode + ",";
				}
				// String th_foodId = item.optString("id");
				// if (!StringUtils.isEmpty(th_foodId)) {
				// th_item_ids += th_foodId + ",";
				// }
			}
		}

		String query_sql = "SELECT third_item_id,item_code,is_charge_commission from cc_third_item_info a where cast(a.item_code as INT) in ("
				+ item_ids.substring(0, item_ids.length() - 1)
				+ ") and channel='EL09' and shop_id=" + storeId + " ";
		List<JSONObject> third_item_info_list = this.dao.query4Json(tenantId,
				query_sql);

		Map<String, String> third_item_info_list_map = new HashMap<String, String>();
		// 饿了么菜品id与天子星菜品id映射关系 Map<饿了么菜品id,天子星菜品id>
		// Map<String, String> thirdItemId2tzxItemId_map = new HashMap<String,
		// String>();

		if (third_item_info_list.size() > 0) {
			for (JSONObject third_item_obj : third_item_info_list) {
				String item_code = third_item_obj.optString("item_code");
				third_item_info_list_map.put(item_code,
						third_item_obj.optString("is_charge_commission"));

				// thirdItemId2tzxItemId_map.put(
				// third_item_obj.optString("third_item_id"), item_code);
				//
				// item_ids += item_code + ",";

			}
		}
		Map<Integer, String> unitMap = new HashMap<Integer, String>();
		// at 2017-11-09 张勇 SQL中增加默认规格条件
		String unit_query_sql = "select item_id,id,unit_name from hq_item_unit where is_default='Y' and item_id in ("
				+ item_ids.substring(0, item_ids.length() - 1) + ")";
		List<JSONObject> item_unit_list = this.dao.query4Json(tenantId,
				unit_query_sql);
		if (item_unit_list.size() > 0) {
			for (JSONObject item_unit_obj : item_unit_list) {
				if (!unitMap.containsKey(item_unit_obj.optInt("item_id"))) {
					unitMap.put(
							item_unit_obj.optInt("item_id"),
							item_unit_obj.optInt("id") + "@"
									+ item_unit_obj.optString("unit_name"));
				}

			}
		}
		// int productQuantity =0;
		for (int n = 0; n < group.size(); n++) {
			JSONObject groupRow = group.getJSONObject(n);

			String type = groupRow.optString("type");
			if (!"normal".equals(type)) {// normal 表示正常菜品
				continue;
			}

			JSONArray items = groupRow.getJSONArray("items");

			int productsListSize = items.size();
			for (int i = 0; i < productsListSize; i++) {
				orderItemParam = new JSONObject();
				
				JSONObject item = items.getJSONObject(i);
				if(item.optString("name").contains("红包")&&item.optDouble("price")==0.0){
					continue;
				}
					
				JSONArray units = item.getJSONArray("newSpecs");
				int productQuantity = item.optInt("quantity");
				product_org_total_fee += item.optDouble("price")
						* item.optInt("quantity");
				
				
				// 商户ID
				orderItemParam.put("tenancy_id", tenantId);
				orderItemParam.put("store_id", storeId);
				// 明细索引
				orderItemParam.put("group_index", group_index++);
				// 订单号
				orderItemParam.put("order_code", orderCode);

				orderItemParam.put(
						"report_date",
						DateUtil.format(
								new Timestamp(System.currentTimeMillis()))
								.toString().substring(0, 10));

				// 获取天子星菜品id
				int itemId = item.optInt("extendCode", 0);
				// String itemId =
				// thirdItemId2tzxItemId_map.get(item.optString("id"));

				/*
				 * 天子星菜品id用int型取值，如果值为null者，id为0 if
				 * (StringUtils.isEmpty(itemId)) { itemId = "-1"; }
				 */

				orderItemParam
						.put("is_commission",
								CommonUtil
										.checkStringIsNotEmpty(third_item_info_list_map
												.get(itemId)) ? third_item_info_list_map
										.get(itemId) : "0");
				orderItemParam.put("item_id", itemId);

				// 商品名称
				String itemName = item.optString("name");

				orderItemParam.put("item_name", itemName);
				// 查询餐普ID
				String item_menu_id_sql = "SELECT c.item_menu_id from hq_item_menu_details a  left join hq_item_menu_class d on a.id=d.details_id LEFT JOIN  hq_item_menu  b on b.id=a.item_menu_id LEFT JOIN hq_item_menu_organ c on c.item_menu_id=b.id where c.store_id="
						+ storeId
						+ " and a.item_id="
						+ itemId
						+ " and d.chanel='EL09'";
				List<JSONObject> item_menu_id_list = this.dao.query4Json(
						tenantId, item_menu_id_sql);
				if (item_menu_id_list.size() > 0) {
					orderItemParam.put("item_menu_id", item_menu_id_list.get(0)
							.optInt("item_menu_id"));
				}

				int unitId = 0;
				String unitName = "";
				if (CommonUtil.checkStringIsNotEmpty(unitMap.get(itemId))
						&& unitMap.get(itemId).contains("@")) {
					String[] unitArr = unitMap.get(itemId).split("@");
					unitId = Integer.parseInt(unitArr[0]);
					unitName = unitArr[1];
				} else {
					unitId = 0;
					if (units.size() > 0) {
						JSONObject unitObject = units.getJSONObject(0);
						unitName = unitObject.optString("value");
					} else {
						unitName = "0";
					}
				}
				orderItemParam.put("unit_id", unitId);
				orderItemParam.put("unit_name", unitName);
				// 份数
				orderItemParam.put("number", productQuantity);
				// 商品价格
				orderItemParam.put(
						"price",
						Double.isNaN(item.optDouble("price")) ? 0.00
								: CommonUtil.keepTwoDecimal(item
										.optDouble("price")));
				// 商品总价
				double product_fee = Scm.pmui(
						Double.isNaN(item.optDouble("price")) ? 0.00 : item
								.optDouble("price"),
						Double.isNaN(item.optDouble("quantity")) ? 0.00 : item
								.optDouble("quantity"));

				if (CommonUtil.checkStringIsNotEmpty(orderItemParam
						.optString("is_commission"))) {
					if (orderItemParam.optString("is_commission").equals("1")) {
						commission_dish_total += product_fee;
					}
				}
				total_product_fee += product_fee;
				orderItemParam.put(
						"product_fee",
						Double.isNaN(product_fee) ? 0.0 : CommonUtil
								.keepTwoDecimal(product_fee));
				StringBuilder item_price_sql = new StringBuilder();
				item_price_sql
						.append("select c.id,c.unit_name,a.price from hq_item_pricesystem a left join organ b on cast(a.price_system as varchar) = b.price_system left JOIN hq_item_unit c on a.item_unit_id = c.id ");
				item_price_sql.append("where c.item_id='" + itemId
						+ "' and b.id = '" + storeId
						+ "' and a.chanel = 'EL09' and c.valid_state = '1'");
				List<JSONObject> item_price_list = this.dao.query4Json(
						tenantId, item_price_sql.toString());
				Double yuan_product_fee = 0.0d;
				if (item_price_list.size() > 0) {
					yuan_product_fee = Scm.pmui(Double.isNaN(item_price_list
							.get(0).optDouble("price")) ? 0.00
							: item_price_list.get(0).optDouble("price"),
							productQuantity * 1.0);
				} else {
					yuan_product_fee = Double.valueOf(product_fee);
				}

				// 优惠金额
				// double discount_amount = Scm.pmui(product_fee, discountRate);
				double discount_amount = product_fee * discountRate;
				double discountk_amount = product_fee * discountR_rate;

				comboDiscountRate = Scm.pdiv(discount_amount,
						Double.valueOf(product_fee));

				// 单品配送费
				// double send_fee_amount =
				// Scm.pmui(Double.valueOf(product_fee), sendFeeRate);
				// product_send_fee_amount += send_fee_amount;

				discount_amount = Double.isNaN(discount_amount) ? 0.0
						: discount_amount;

				orderItemParam.put("discount_amount",
						CommonUtil.keepTwoDecimal(discount_amount));
				orderItemParam.put(
						"discountk_amount",
						Double.isNaN(discountk_amount) ? 0.0 : CommonUtil
								.keepTwoDecimal(discountk_amount));
				// orderItemParam.put("costs",
				// Double.isNaN(send_fee_amount)?0.0:send_fee_amount);
				// 实收金额
				double real_amount = Scm.psub(product_fee, discount_amount);
				// double costs_real_amount = Scm.padd(real_amount,
				// send_fee_amount);
				orderItemParam.put(
						"real_amount",
						Double.isNaN(real_amount) ? 0.0 : CommonUtil
								.keepTwoDecimal(real_amount));
				// orderItemParam.put("share_amount", costs_real_amount);
				if (number == 0
						&& share_product_price_total < takeawayBusinessIncome
						&& i == productsListSize - 1) {
					orderItemParam.put("share_amount", CommonUtil
							.keepTwoDecimal(Scm.psub(takeawayBusinessIncome,
									share_product_price_total)));
				} else if (share_product_price_total > takeawayBusinessIncome
						&& number != 0) {
					orderItemParam
							.put("share_amount", CommonUtil.keepTwoDecimal(Scm
									.pmui(Double.valueOf(yuan_product_fee),
											food_sharing_date
													* Double.valueOf("0.6"))));
					share_product_price_total = share_product_price_total
							- Scm.pmui(Double.valueOf(yuan_product_fee),
									food_sharing_date)
							+ Scm.pmui(Double.valueOf(yuan_product_fee),
									food_sharing_date * Double.valueOf("0.6"));
				} else {
					orderItemParam.put("share_amount", CommonUtil
							.keepTwoDecimal(Scm.pmui(
									Double.valueOf(product_fee),
									food_sharing_date)));
				}
				share_product_price_total += orderItemParam
						.optDouble("share_amount");

				// 优惠价格
				// orderItemParam.put("discount_price",
				// Scm.pdiv(costs_real_amount,
				// Double.valueOf(productAmount)));
				// 优惠方式
				// if (discount_fee != 0)
				// {
				// 优惠类型
				orderItemParam.put("discount_mode_id", "7");
				orderItemParam.put("store_id", storeId);
				// }

				// 套餐
				String item_infoSql = "select is_combo from hq_item_info a where a.id="
						+ itemId;
				List<JSONObject> list = this.dao.query4Json(tenantId,
						item_infoSql);
				if (list.size() > 0) {

					JSONObject item_info_obj = new JSONObject();
					item_info_obj = list.get(0);
					if (item_info_obj.optString("is_combo").equalsIgnoreCase(
							"y")) {

						List<JSONObject> order_item_details_list = new ArrayList<JSONObject>();
						String hq_item_combo_details_sql = "select * from hq_item_combo_details a where a.iitem_id="
								+ itemId;
						List<JSONObject> item_combo_details_list = this.dao
								.query4Json(tenantId, hq_item_combo_details_sql);
						if (item_combo_details_list.size() > 0) {
							int b = 1;
							for (int k = 0; k < item_combo_details_list.size(); k++) {
								b++;
								JSONObject item_combo_details_obj = item_combo_details_list
										.get(k);
								double combo_discount_amount = 0.0;
								int combo_num = item_combo_details_obj
										.optInt("combo_num");
								if (b == item_combo_details_list.size()) {

									if (item_combo_details_obj.optString(
											"is_itemgroup").equalsIgnoreCase(
											"y")) {
										hq_item_combo_details_sql = "select a.* ,b.standard_price from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id  where a.item_group_id="
												+ item_combo_details_obj
														.optInt("details_id");
										List<JSONObject> item_group_details_list = this.dao
												.query4Json(tenantId,
														hq_item_combo_details_sql
																.toString());
										if (item_group_details_list.size() > 0) {
											for (int l = 0; l < combo_num; l++) {
												JSONObject item_group_details_obj = item_group_details_list
														.get(l);
												item_group_details_obj.put(
														"group_index",
														group_index - 1);
												item_group_details_obj
														.put("order_code",
																orderCode);
												item_group_details_obj
														.put("item_id",
																item_group_details_obj
																		.optInt("item_id",
																				0));
												item_group_details_obj
														.put("unit_id",
																item_group_details_obj
																		.optInt("item_unit_id",
																				0));
												item_group_details_obj
														.put("price",
																CommonUtil
																		.keepTwoDecimal(Double
																				.valueOf(item_combo_details_obj
																						.optString("standardprice"))));
												item_group_details_obj.put(
														"number",
														productQuantity);
												item_group_details_obj
														.put("report_date",
																"'"
																		+ DateUtil
																				.format(new Timestamp(
																						System.currentTimeMillis()))
																				.toString()
																				.substring(
																						0,
																						10)
																		+ "'");
												item_group_details_obj
														.put("product_fee",
																CommonUtil
																		.keepTwoDecimal((item_combo_details_obj
																				.optDouble("standardprice"))
																				* productQuantity));
												item_group_details_obj
														.put("discount_amount",
																CommonUtil
																		.keepTwoDecimal(discount_amount
																				- combo_discount_amount));
												item_group_details_obj
														.put("real_amount",
																CommonUtil
																		.keepTwoDecimal(Scm
																				.psub(item_group_details_obj
																						.optDouble("product_fee"),
																						item_group_details_obj
																								.optDouble("discount_amount"))));
												item_group_details_obj
														.remove("id");
												order_item_details_list
														.add(item_group_details_obj);
											}

										}
									} else {
										item_combo_details_obj.remove("id");
										item_combo_details_obj.put(
												"order_code", orderCode);
										item_combo_details_obj.put("unit_id",
												item_combo_details_obj.optInt(
														"item_unit_id", 0));
										item_combo_details_obj
												.put("price",
														CommonUtil
																.keepTwoDecimal(Double
																		.valueOf(item_combo_details_obj
																				.optString("standardprice"))));
										item_combo_details_obj.put("number",
												productQuantity * combo_num);
										item_combo_details_obj
												.put("product_fee",
														CommonUtil
																.keepTwoDecimal((item_combo_details_obj
																		.optDouble("standardprice"))
																		* (item_combo_details_obj
																				.optDouble("number"))));
										item_combo_details_obj
												.put("discount_amount",
														CommonUtil
																.keepTwoDecimal(discount_amount
																		- combo_discount_amount));
										item_combo_details_obj
												.put("real_amount",
														CommonUtil
																.keepTwoDecimal(Scm
																		.psub(item_combo_details_obj
																				.optDouble("product_fee"),
																				item_combo_details_obj
																						.optDouble("discount_amount"))));
										item_combo_details_obj.put("item_id",
												item_combo_details_obj.optInt(
														"details_id", 0));
										item_combo_details_obj.put(
												"group_index", group_index - 1);
										item_combo_details_obj
												.put("report_date",
														"'"
																+ DateUtil
																		.format(new Timestamp(
																				System.currentTimeMillis()))
																		.toString()
																		.substring(
																				0,
																				10)
																+ "'");
										order_item_details_list
												.add(item_combo_details_obj);
									}
								} else {
									if (item_combo_details_obj.optString(
											"is_itemgroup").equalsIgnoreCase(
											"y")) {
										hq_item_combo_details_sql = "select a.* ,b.standard_price from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id  where a.item_group_id="
												+ item_combo_details_obj
														.optInt("details_id");
										List<JSONObject> item_group_details_list = this.dao
												.query4Json(tenantId,
														hq_item_combo_details_sql
																.toString());

										if (item_group_details_list.size() > 0) {
											for (int l = 0; l < combo_num; l++) {
												JSONObject item_group_details_obj = item_group_details_list
														.get(l);
												item_group_details_obj.put(
														"group_index",
														group_index - 1);
												item_group_details_obj
														.put("order_code",
																orderCode);
												item_group_details_obj
														.put("item_id",
																item_group_details_obj
																		.optInt("item_id",
																				0));
												item_group_details_obj
														.put("unit_id",
																item_group_details_obj
																		.optInt("item_unit_id",
																				0));
												item_group_details_obj
														.put("price",
																CommonUtil
																		.keepTwoDecimal(Double
																				.valueOf(item_combo_details_obj
																						.optString("standardprice"))));
												item_group_details_obj.put(
														"number",
														productQuantity);
												item_group_details_obj
														.put("product_fee",
																CommonUtil
																		.keepTwoDecimal((item_combo_details_obj
																				.optDouble("standardprice"))
																				* productQuantity));
												item_group_details_obj
														.put("discount_amount",
																CommonUtil
																		.keepTwoDecimal(Scm
																				.pmui(item_group_details_obj
																						.optDouble("product_fee"),
																						comboDiscountRate)));
												combo_discount_amount += Scm
														.pmui(item_group_details_obj
																.optDouble("product_fee"),
																comboDiscountRate);
												item_group_details_obj
														.put("real_amount",
																CommonUtil
																		.keepTwoDecimal(Scm
																				.psub(item_group_details_obj
																						.optDouble("product_fee"),
																						item_group_details_obj
																								.optDouble("discount_amount"))));
												item_group_details_obj
														.put("report_date",
																"'"
																		+ DateUtil
																				.format(new Timestamp(
																						System.currentTimeMillis()))
																				.toString()
																				.substring(
																						0,
																						10)
																		+ "'");
												item_group_details_obj
														.remove("id");
												order_item_details_list
														.add(item_group_details_obj);
											}

										}
									} else {
										item_combo_details_obj.remove("id");
										item_combo_details_obj.put(
												"order_code", orderCode);
										item_combo_details_obj.put("unit_id",
												item_combo_details_obj.optInt(
														"item_unit_id", 0));
										item_combo_details_obj
												.put("price",
														CommonUtil
																.keepTwoDecimal(Double
																		.valueOf(item_combo_details_obj
																				.optString("standardprice"))));
										item_combo_details_obj.put("number",
												productQuantity * combo_num);
										item_combo_details_obj
												.put("product_fee",
														CommonUtil
																.keepTwoDecimal((item_combo_details_obj
																		.optDouble("standardprice"))
																		* (item_combo_details_obj
																				.optDouble("number"))));
										item_combo_details_obj
												.put("discount_amount",
														CommonUtil
																.keepTwoDecimal(Scm
																		.pmui(item_combo_details_obj
																				.optDouble("product_fee"),
																				comboDiscountRate)));
										combo_discount_amount += Scm
												.pmui(item_combo_details_obj
														.optDouble("product_fee"),
														comboDiscountRate);
										item_combo_details_obj
												.put("real_amount",
														CommonUtil
																.keepTwoDecimal(Scm
																		.psub(item_combo_details_obj
																				.optDouble("product_fee"),
																				item_combo_details_obj
																						.optDouble("discount_amount"))));
										item_combo_details_obj.put("item_id",
												item_combo_details_obj.optInt(
														"details_id", 0));
										item_combo_details_obj.put(
												"group_index", group_index - 1);
										item_combo_details_obj
												.put("report_date",
														"'"
																+ DateUtil
																		.format(new Timestamp(
																				System.currentTimeMillis()))
																		.toString()
																		.substring(
																				0,
																				10)
																+ "'");
										order_item_details_list
												.add(item_combo_details_obj);
									}
								}

							}
						}

						CcPartitionUtils.lackInsertParam(tenantId,
								orderItemParam, order_item_details_list);

						this.dao.insertBatchIgnorCase(tenantId,
								"cc_order_item_details",
								order_item_details_list);
					}

				}

				CcPartitionUtils.lackInsertParam(tenantId, orderItemParam);

				orderBatchSql.append(CommonUtil.insertJSONParamsToSql(
						"cc_order_item", orderItemParam));
				// this.dao.insertIgnorCase(tenantId, "cc_order_item",
				// orderItemParam);
			   
			}
		}
		// 保存餐盒费信息
		if (number > 0) {
			if (null == pbList || pbList.isEmpty()) {
				packageBoxFee.put("item_id", 0);
				packageBoxFee.put("unit_id", 0);
				packageBoxFee.put("price", package_fee);
				packageBoxFee.put("item_name", "餐盒费");
				logger.info("[饿了么]收单失败:订单号[" + orderCode + "]->未正确获取餐盒费信息!");
				// throw new Exception("[饿了么]收单失败:订单号[" + orderCode +
				// "]->未正确获取餐盒费信息!");
			} else {
				packageBoxFee.putAll(pb);
			}
			packageBoxFee.put("tenancy_id", tenantId);
			packageBoxFee.put("store_id", storeId);
			packageBoxFee.put("group_index", group_index);
			packageBoxFee.put("order_code", orderCode);
			packageBoxFee.put("report_date",
					DateUtil.format(new Timestamp(System.currentTimeMillis()))
							.toString().substring(0, 10));
			// packageBoxFee.put("price",
			// Scm.pdiv(package_fee,Double.valueOf(number)));
			packageBoxFee.put("number", number);

			packageBoxFee.put("product_fee", Double.isNaN(package_fee) ? 0.0
					: CommonUtil.keepTwoDecimal(package_fee));
			// 优惠金额
			// double packageBox_discount_amount =
			// Scm.psub(Double.valueOf(discount_fee), product_discount_amount);
			// packageBoxFee.put("discount_amount", packageBox_discount_amount);
			// 菜品摊的配送费
			// double packageBox_send_fee_amount =
			// Scm.psub(Double.valueOf(send_fee), product_send_fee_amount);
			// packageBoxFee.put("costs", packageBox_send_fee_amount);
			packageBoxFee.put(
					"discount_amount",
					Double.isNaN(package_fee * discountRate) ? 0.0 : CommonUtil
							.keepTwoDecimal(package_fee * discountRate));
			packageBoxFee.put(
					"discountk_amount",
					Double.isNaN(package_fee * discountR_rate) ? 0.0
							: CommonUtil.keepTwoDecimal(package_fee
									* discountR_rate));
			// 实收金额
			packageBoxFee
					.put("real_amount",
							Double.isNaN(Scm.qsub(package_fee,
									packageBoxFee.optDouble("discount_amount"))) ? 0.0
									: CommonUtil.keepTwoDecimal(Scm.qsub(
											package_fee,
											packageBoxFee
													.optDouble("discount_amount"))));

			packageBoxFee.put("share_amount", CommonUtil.keepTwoDecimal(Scm
					.psub(takeawayBusinessIncome, share_product_price_total)));

			packageBoxFee.put("is_commission", "1");

			// 优惠方式
			// if (discount_fee != 0)
			// {
			// 优惠类型
			packageBoxFee.put("discount_mode_id", "7");
			packageBoxFee.put("store_id", storeId);
			// }

			CcPartitionUtils.lackInsertParam(tenantId, packageBoxFee);

			orderBatchSql.append(CommonUtil.insertJSONParamsToSql(
					"cc_order_item", packageBoxFee));
			// this.dao.insertIgnorCase(tenantId, "cc_order_item",
			// packageBoxFee);
		}

		log(CcBusniessLogBean.TypeProperty.ORDER_RECIVE, "saveOrderItem",
				orderItemParam.toString() + "##" + packageBoxFee.toString(), "");
	}

	@Override
	public void saveOrderRepayment() throws Exception {
		JSONObject payment = new JSONObject();
		payment.put("tenancy_id", tenantId);
		payment.put("remark", "ele_pay");// 待修改
		payment.put("order_code", orderCode);
		payment.put("store_id", storeId);
		payment.put("shop_real_amount", ele_shop_real_amount);
		payment.put("pay_money", CommonUtil.keepTwoDecimal(shopFee));
		payment.put("third_bill_code", requestOrder.optString("id"));
		payment.put("report_date",
				DateUtil.format(new Timestamp(System.currentTimeMillis()))
						.toString().substring(0, 10));
		// at 2017-08-18 此值与pay_money一致
		payment.put("local_currency", CommonUtil.keepTwoDecimal(shopFee));

		// 支付类型
		boolean payType = requestOrder.optBoolean("onlinePaid");
		String store_sql = "select remark from organ where id = " + storeId
				+ " ";
		JSONObject store_obj = this.dao.query4Json(tenantId, store_sql).get(0);
		if (store_obj.optString("remark").equals("read_from_rif") || payType) {
			String paySql = "SELECT a.id as payment_id FROM payment_way a LEFT JOIN payment_way_of_ogran b on a.id=b.payment_id where a.payment_class='ele_pay' and b.organ_id="
					+ storeId;
			List list = this.dao.query4Json(tenantId, paySql);
			if (!list.isEmpty()) {
				payment.put("payment_id",
						((JSONObject) list.get(0)).optString("payment_id"));
			}
		}

		CcPartitionUtils.lackInsertParam(tenantId, payment);

		orderBatchSql.append(CommonUtil.insertJSONParamsToSql(
				"cc_order_repayment", payment));

		log(CcBusniessLogBean.TypeProperty.ORDER_RECIVE, "saveOrderRepayment",
				payment.toString(), "");
	}

	@Override
	public void saveOrderDiscount() throws Exception {
		JSONArray groups = requestOrder.getJSONArray("groups");

		List<JSONObject> discountList = new ArrayList<>();

		StringBuilder sql = new StringBuilder();

		for (int j = 0; j < groups.size(); j++) {

			JSONObject group = groups.getJSONObject(j);
			String type = group.optString("type");

			// 如果不是其它费用，则跳过，该方法只处理费用信息
			if (!"extra".equals(type)) {
				continue;
			}

			JSONArray requestDiscountArray = groups.getJSONObject(j)
					.getJSONArray("items");

			for (int i = 0; i < requestDiscountArray.size(); i++) {

				JSONObject dc = requestDiscountArray.getJSONObject(i);

				if (!dc.optString("categoryId").equals("2")
						&& !dc.optString("categoryId").equals("102")) {

					JSONObject discount = requestDiscountArray.getJSONObject(i);
					// 优惠类型
					String discountType = discount.optString("type");
					// 优惠金额
					double discountFee = Scm.pmui(discount.optDouble("price"),
							discount.optDouble("quantity"));
					// 活动ID
					String activityId = discount.optString("id");
					JSONObject discountParam = createOrderDiscountParam(sql,
							activityId, discountType, discountFee, "");

					discountList.add(discountParam);
				}
			}
		}

		// 订单活动信息
		JSONArray orderActivities = requestOrder
				.getJSONArray("orderActivities");

		for (int j = 0; j < orderActivities.size(); j++) {
			JSONObject orderActivitie = orderActivities.getJSONObject(j);

			// 优惠类型
			String discountType = orderActivitie.optString("categoryId");
			// 优惠金额
			double discountFee = orderActivitie.optDouble("amount");
			String activityId = orderActivitie.optString("id");
			String desc = orderActivitie.optString("name");
			JSONObject discountParam = createOrderDiscountParam(sql,
					activityId, discountType, discountFee, desc);

			discountList.add(discountParam);
		}

		// 饿了么红包大于0时，产生一条虚拟的红包优惠记录
		double hongbao = Math.abs(Double.isNaN(requestOrder
				.optDouble("hongbao")) ? 0.0 : requestOrder
				.optDouble("hongbao"));

		if (hongbao > 0) {
			JSONObject discountParam = createOrderDiscountParam(sql, "C10010",
					"", hongbao, "饿了么红包");

			discountList.add(discountParam);
		}

		if (discountList.isEmpty()) {
			return;
		}

		// orderBatchSql.append(sql.toString());

		logger.info("订单号:[" + orderCode + "]===>优惠信息:" + discountList
				+ "饿了么优惠原始参数:" + groups + " SQL:" + sql.toString());
		// this.dao.execute(tenantId, sql.toString());
		// this.dao.insertBatchIgnorCase(tenantId, "cc_order_discount",
		// discountList);

		log(CcBusniessLogBean.TypeProperty.ORDER_RECIVE, "saveOrderDiscount",
				discountList.toString(), "");
	}

	// 获取优惠活动信息
	private JSONObject createOrderDiscountParam(StringBuilder sql,
			String activityId, String discountType, double discountFee,
			String desc) {
		sql.append("INSERT INTO cc_order_discount (tenancy_id,store_id,report_date,order_code,discount_type,activity_id,discount_desc,discount_fee,baidu_rate,operator_time) VALUES (");

		JSONObject discountParam = new JSONObject();

		// 商户ID
		discountParam.put("tenancy_id", tenantId);
		sql.append("'" + tenantId + "',");
		// 门店ID
		discountParam.put("store_id", storeId);
		sql.append("'" + storeId + "',");

		String report_date = DateUtil
				.format(new Timestamp(System.currentTimeMillis())).toString()
				.substring(0, 10);
		discountParam.put("report_date", report_date);
		sql.append("'" + report_date + "',");
		// 订单号
		discountParam.put("order_code", orderCode);
		sql.append("'" + orderCode + "',");
		// 优惠类型
		discountParam.put("discount_type", discountType);
		sql.append("'" + discountType + "',");
		// 活动ID
		discountParam.put("activity_id", activityId);
		sql.append("'" + activityId + "',");
		// 优惠描述
		discountParam.put("discount_desc", desc);
		sql.append("'" + desc + "',");

		// 优惠金额
		if (discountFee < 0) {
			discountFee = 0 - discountFee;
		}

		discountParam.put("discount_fee", discountFee);
		sql.append("'" + CommonUtil.keepTwoDecimal(discountFee) + "',");
		// 饿了么承担金额
		double eleMeRate = Math.abs(requestOrder.optDouble("elemePart", 0.0));
		double hongbao = Math.abs(Double.isNaN(requestOrder
				.optDouble("hongbao")) ? 0.0 : requestOrder
				.optDouble("hongbao"));
		eleMeRate = eleMeRate + hongbao;// 实际为饿了么平台承担+红包
		discountParam.put("baidu_rate", eleMeRate);
		sql.append("'" + CommonUtil.keepTwoDecimal(eleMeRate) + "',");
		// 操作时间
		discountParam.put("operator_time",
				DateUtil.format(new Timestamp(System.currentTimeMillis())));
		sql.append("'"
				+ DateUtil.format(new Timestamp(System.currentTimeMillis()))
				+ "');");

		CcPartitionUtils.lackInsertParam(tenantId, discountParam);

		orderBatchSql.append(CommonUtil.insertJSONParamsToSql(
				"cc_order_discount", discountParam));

		return discountParam;
	}

	// at 2017-08-15
	// @SuppressWarnings({ "static-access", "deprecation" })
	// @Override
	// public void saveCustomerInfoAndAddress() {
	// JSONObject address = new JSONObject();
	// try {
	//
	// JSONArray phoneLists = requestOrder.optJSONArray("phoneList");
	// String[] deliverys = requestOrder.optString("deliveryGeo").split(
	// ",");
	// address.put("order_phone", phoneLists.toList(phoneLists).get(0));
	// address.put("address", CommonUtil.replaceEvilChar(requestOrder
	// .optString("address").trim()));
	// address.put(
	// "consignee",
	// CommonUtil.replaceEvilChar(requestOrder.optString(
	// "consignee").trim()));
	// address.put("consignee_phone", phoneLists.toList(phoneLists).get(0));
	// address.put("longitude", deliverys[0]);
	// address.put("latitude", deliverys[1]);
	// // address.put("sex", requestUser.optInt("gender") == 1 ? "man" :
	// // "woman");
	// // 顾客送餐详情地址，例如：近铁城市广场（普陀区金沙江路1518弄)
	// address.put(
	// "baidu_location",
	// CommonUtil.replaceEvilChar(requestOrder.optString(
	// "deliveryPoiAddress").trim()));
	// // 根据订餐电话判断是不是会员
	// String customer_id = placeOrderManagementService
	// .loadCustomerByPhone(tenantId, address);
	// if (!"".equals(customer_id)) {
	// String sql = "update crm_customer_address set address='"
	// + address.optString("address") + "',consignee='"
	// + address.optString("consignee")
	// + "',consignee_phone='"
	// + address.optString("consignee_phone")
	// + "',longitude='" + address.optString("longitude")
	// + "',latitude='" + address.optString("latitude")
	// + "',sex='" + address.optString("sex")
	// + "',baidu_location='" + address.optString("address")
	// + "' where customer_id='" + customer_id + "'";
	// this.dao.execute(tenantId, sql);
	// } else {
	// JSONObject info = new JSONObject();
	// info.put("tenancy_id", tenantId);
	// // info.put("name", requestUser.optString("name"));
	// // info.put("sex", requestUser.optInt("gender") == 1 ? "man" :
	// // "woman");
	// // info.put("mobil", requestUser.optString("phone"));
	// info.put("add_chanel", channel);
	// info.put("add_time", DateUtil.format(new Timestamp(System
	// .currentTimeMillis())));
	// info.put("store_id", storeId);
	// customer_id = this.dao.insertIgnorCase(tenantId,
	// "crm_customer_info", info).toString();
	// address.put("customer_id", customer_id);
	// address.put("tenancy_id", tenantId);
	// this.dao.insertIgnorCase(tenantId, "crm_customer_address",
	// address);
	// }
	// } catch (Exception e) {
	// e.printStackTrace();
	// logger.error(e);
	// }
	//
	// log(CcBusniessLogBean.TypeProperty.ORDER_RECIVE,
	// "saveCustomerInfoAndAddress", address.toString(), "");
	// }

	@Override
	public JSONObject thirdPartyResponse() {

		JSONObject body = new JSONObject();

		try {
			if (0 == err.optInt("errno")) {
				body.put("message", "ok");
			} else {
				body.putAll(err);
			}
		} catch (Exception e) {
			e.printStackTrace();

			body.put("errno", 200);
			body.put("error", "生成响应数据时失败！");
			body.put("errmsg", e);
		}
		return body;
	}

	@Override
	public JSONObject orderStatusPush(JSONObject params) {
		String logType = "";
		try {
			String status = "";
			String orderState = null;
			String new_status = params.optString("type");
			logger.info("饿了么取消状态==========1521==============" + new_status);
			// 14 订单被取消,15 订单置为无效,23 商户同意取消单,18 订单完结，17 订单强制无效，33 商户同意退单
			// 这些是新加的状态判断 20 用户申请取消单 30 用户申请退单 57 配送取消，商户取消 58 配送取消，用户取消 59
			// 配送取消，物流系统取消
			// 60 配送失败，呼叫配送晚 61 配送失败，餐厅出餐问题 62 配送失败，商户中断配送 63 配送失败，用户不接电话 64
			// 配送失败，用户退单
			// 65 配送失败，用户地址错误 66 配送失败，超出服务范围 67 配送失败，骑手标记异常 68 配送失败，系统自动标记异常 69
			// 配送失败，其他异常 70 配送失败，超时标记异常
			if (new_status.equals("14") || new_status.equals("15")
					|| new_status.equals("17") || new_status.equals("23")
					|| new_status.equals("33") || new_status.equals("20")
					|| new_status.equals("30") || new_status.equals("57")
					|| new_status.equals("58") || new_status.equals("59")
					|| new_status.equals("60") || new_status.equals("61")
					|| new_status.equals("62") || new_status.equals("63")
					|| new_status.equals("64") || new_status.equals("65")
					|| new_status.equals("66") || new_status.equals("67")
					|| new_status.equals("68") || new_status.equals("64")
					|| new_status.equals("70")) {
				status = ORDER_CANCEL;
				orderState = "08";
			} else if (new_status.equals("18")) {
				status = ORDER_COMPLETE;
				orderState = "10";
			}

			if (!StringUtils.isEmpty(status)) {

				JSONObject messsage = JSONObject.fromObject(params
						.getString("message"));

				String elm_shopId = messsage.optString("shopId");
				String orderId = messsage.optString("orderId");
				String reason = messsage.optString("reason");

				String[] shopId = ElmUtils
						.getTenancyInfoByElmShopId(elm_shopId);

				this.storeId = shopId[0];
				this.tenantId = shopId[1];

				DBContextHolder.setTenancyid(tenantId);

				switch (status) {
				case ORDER_CANCEL:// 第三方取消订单
					// start
					/**
					 * 添加的饿了么第三方取消 方法 王俊辉 2017-11-3
					 */
					this.elmCancel(new_status, orderId);
					// end
					orderState = "08";
					logType = CcBusniessLogBean.TypeProperty.ORDER_CANCEL;
					JSONObject dataObj = new JSONObject();
					dataObj.put("tenancy_id", tenantId);
					dataObj.put("store_id", storeId);
					dataObj.put("third_order_code", orderId);
					dataObj.put("requestId", params.optString("requestId"));
					orderManagementService.orderCancel(tenantId, dataObj);
					break;
				case ORDER_COMPLETE:// 第三方完成订单
					orderState = "10";
					logType = CcBusniessLogBean.TypeProperty.ORDER_FINISH;
					JSONObject data_complete_Obj = new JSONObject();
					data_complete_Obj.put("tenancy_id", tenantId);
					data_complete_Obj.put("store_id", storeId);
					data_complete_Obj.put("third_order_code", orderId);
					data_complete_Obj.put("requestId",
							params.optString("requestId"));
					orderManagementService.orderComplete(tenantId,
							data_complete_Obj);
					break;
				default:
					break;
				}
				err.put("errno", "0");
			}
		} catch (Exception e) {
			e.printStackTrace();
			err.put("errno", "1");
			err.put("error", e);
			err.put("errmsg", e.getMessage());
		}

		JSONObject result = thirdPartyResponse();

		log(logType, "orderStatusPush", params.toString(), result.toString());

		return result;
	}

	/**
	 * 添加的饿了么第三方取消 方法 王俊辉 2017-11-3
	 */
	public void elmCancel(String new_status, String orderId) {

		// 饿了么取消状态的修改 判断
		try {
			/*
			 * //在cc_order_reason_detail表里面查询是否有单 String selectYc =
			 * "SELECT s.cancel_type,s.order_code FROM cc_order_list s WHERE s.third_order_code = '"
			 * +orderId+"'";
			 * selectYc=CcPartitionUtils.makeSQL(tenantId,selectYc, "",
			 * CcPartitionUtils.TYPE_ORDERCODE_NO); List<JSONObject>
			 * order_reason_detail= null; order_reason_detail =
			 * dao.query4Json(tenantId, selectYc); //新增取消状态表
			 * if(null!=order_reason_detail
			 * &&!"0".equals(order_reason_detail.get(
			 * 0).optString("cancel_type"))){ String insertSql =
			 * "INSERT INTO cc_order_reason_detail (order_code,TYPE,complaint_content,complaints_time,order_state)"
			 * +"VALUES ('"+order_reason_detail.get(0).optString("order_code")+
			 * "','QX01','"+reason+"','"+new Date()+"','08');"; insertSql =
			 * CcPartitionUtils.makeSQL(tenantId,insertSql, "",
			 * CcPartitionUtils.TYPE_ORDERCODE_NO); dao.execute(tenantId,
			 * insertSql); }
			 */
			String sql = null;
			// 用户取消
			if (new_status.equals("23") || new_status.equals("20")
					|| new_status.equals("30") || new_status.equals("58")
					|| new_status.equals("64")) {
				sql = "update cc_order_list set cancel_type='1',order_state='08',payment_state='04' where third_order_code='"
						+ orderId + "'";
				// 17平台取消
			} else if (new_status.equals("15") || new_status.equals("17")
					|| new_status.equals("33") || new_status.equals("65")
					|| new_status.equals("66") || new_status.equals("69")
					|| new_status.equals("70") || new_status.equals("73")
					|| new_status.equals("74") || new_status.equals("76")) {
				sql = "update cc_order_list set cancel_type='2',order_state='08',payment_state='04' where third_order_code='"
						+ orderId + "'";
				// 14服务员取消
			} else {
				sql = "update cc_order_list set cancel_type='0',order_state='08',payment_state='04' where third_order_code='"
						+ orderId + "'";
			}

			sql = CcPartitionUtils.makeSQL(tenantId, sql, "",
					CcPartitionUtils.TYPE_ORDERCODE_NO);
			dao.execute(tenantId, sql);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 饿了么不用
	 */
	@Override
	public JSONObject orderStatusGet(JSONObject params) {
		JSONObject result = new JSONObject();
		return result;
	}

	@Override
	protected void generateOrderCode() {
		String dateSerial = requestOrder.optString("createdAt")
				.replace("T", "").replace("-", "").replace(":", "")
				.replace(" ", "");
		int orderIndex = requestOrder.optInt("daySn");
		orderCode = channel + storeId + dateSerial
				+ CommonUtil.zeroFill(orderIndex);
	}

	/*
	 * 修改餐盒数计算方式 private JSONObject getStoreBoxPrice(){ JSONObject result=new
	 * JSONObject(); String sql=
	 * "SELECT B.item_id,B.price from cc_meals_info a LEFT JOIN cc_meals_info_default  b on b.meals_id=a.id where a.store_id='"
	 * + storeId+ "' and a.channel='" + channel + "' AND A.meals_type='MR03'";
	 * try { List<JSONObject> list = this.dao.query4Json(tenantId,
	 * sql.toString()); result=list.get(0); } catch (Exception e) {
	 * e.printStackTrace(); } return result; }
	 */

	@Override
	public void calcCommission() {
		try {
			double commissionRate = 0.0;
			double discountCommission = 0.0;
			double hongbao = Math.abs(Double.isNaN(requestOrder
					.optDouble("hongbao")) ? 0.0 : requestOrder
					.optDouble("hongbao"));

			// JSONArray groups = requestOrder.getJSONArray("groups");
			//
			// for (int j = 0; j < groups.size(); j++) {
			//
			// JSONArray items = groups.getJSONObject(j).getJSONArray("items");
			//
			// // 获取红包抵扣
			// for (int i = 0; i < items.size(); i++) {
			// JSONObject item = items.getJSONObject(i);
			// if (item.optString("categoryId").equals("13")) {
			// hongbao += Math.abs(Double.isNaN(item
			// .optDouble("price")) ? 0.0 : item
			// .optDouble("price"));
			// }
			//
			// }
			// }
			// 面香新的计算公式
			// ------------------------------------------------start
			double serviceRate = Math
					.abs(requestOrder.getDouble("serviceRate"));
			double serviceFee = Math.abs(requestOrder.getDouble("serviceFee"));
			// 方法一：
			commissionRate = Double.isNaN(serviceRate) ? 1 : serviceRate;
			discountCommission = Double.isNaN(serviceFee) ? 0.0 : serviceFee;
			// if (discountCommission > 17 && discountCommission < 27) {
			// discountCommission = 4;
			// }

			double elmCommissionAmount = Math.abs(Double.isNaN(requestOrder
					.getDouble("serviceFee")) ? 0.0 : requestOrder
					.getDouble("serviceFee"));
			double shopRate = Math.abs(requestOrder.optDouble("shopPart"));// 商家承担优惠
			double package_fee = Math.abs(Double.isNaN(requestOrder
					.optDouble("packageFee")) ? 0.00 : requestOrder
					.optDouble("packageFee"));// 餐盒费
			JSONObject commissionObj = commissionInfoSettingService
					.findCommissionRate(tenantId, Constant.ELE_CHANNEL, storeId);
			commissionRate = Double.isNaN(commissionObj
					.optDouble("commission_rate")) ? 1 : commissionObj
					.optDouble("commission_rate");
			discountCommission = Scm.pmui(
					(product_org_total_fee + package_fee - shopRate),
					commissionRate);

			// 店铺实收
			ele_shop_real_amount = Double.isNaN(requestOrder
					.optDouble("income")) ? 0.0 : requestOrder
					.optDouble("income");
			// 饿了么承担活动费用
			double eleme_part = Math.abs(Double.isNaN(requestOrder.optDouble(
					"elemePart", 0.0)) ? 0.0 : requestOrder.optDouble(
					"elemePart", 0.0));
			if (CommonUtil.checkStringIsNotEmpty(productProfileName)
					&& productProfileName.equals(SHOP_FOODSEND_TYPE_FENGNIAO)) {
				ele_platform_charge_amount = shopFee - ele_shop_real_amount;// shop_fee=shop_real_amount+platform_charge_amount;discountCommission-(eleme_part-hongbao)
			} else {
				ele_platform_charge_amount = elmCommissionAmount
						- (eleme_part + hongbao);
			}

			// at 20170904 优惠总额大于等订单总额于时，平台收取金额（platform_charge_amount)加上0.01
			if (total_discount_fee >= order_total_price
					&& user_actual_pay == 0.01) {
				ele_platform_charge_amount += 0.01;
			}

			// start at 2017-09-30

			// 本地门店实收=餐盒费+菜品明细-佣金（本地计算的值）-商家承担
			// double local_shop_real_amount = package_fee +
			// product_org_total_fee- shopRate - discountCommission;

			double local_shop_real_amount = 0;
			if (CommonUtil.checkStringIsNotEmpty(productProfileName)
					&& productProfileName.equals(SHOP_FOODSEND_TYPE_FENGNIAO)) {
				// 本地门店实收（平）= 菜品明细+餐盒费-商家承担-拥金
				local_shop_real_amount = package_fee + product_org_total_fee
						- shopRate - discountCommission;
			} else {
				// 本地门店实收（自）= 菜品明细+餐盒费-商家承担+配送费-拥金
				local_shop_real_amount = package_fee + product_org_total_fee
						- shopRate + requestOrder.optDouble("deliverFee", 0)
						- discountCommission;
			}

			// end at 2017-09-30

			JSONObject paramsNew = new JSONObject();
			paramsNew.put("order_code", orderCode);
			paramsNew.put("tenentid", tenantId);
			paramsNew.put("discount_commission",
					CommonUtil.keepTwoDecimal(elmCommissionAmount));
			paramsNew.put("calculate_commission_amount",
					CommonUtil.keepTwoDecimal(discountCommission));
			paramsNew.put("local_shop_real_amount",
					CommonUtil.keepTwoDecimal(local_shop_real_amount));
			paramsNew.put("commission_rate", commissionRate);
			paramsNew.put("shop_real_amount",
					CommonUtil.keepTwoDecimal(ele_shop_real_amount));
			paramsNew.put("platform_charge_amount",
					CommonUtil.keepTwoDecimal(ele_platform_charge_amount));
			paramsNew.put("product_org_total_fee",
					CommonUtil.keepTwoDecimal(product_org_total_fee));
			orderDiscountCommissionReportService.updateYjxx4Order(paramsNew);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage());
		}
	}

	/**
	 * 查询店铺的配置的配送方式
	 * 
	 * @param tantenId
	 * @param th_shopId
	 *            饿了么门店id
	 * @param tzx_shopId
	 *            天子星门店id
	 * @return
	 * @throws Exception
	 */
	private String getShopFoodSendType(String tantenId, long th_shopId,
			String tzx_shopId) throws Exception {
		PacksService packsService = new PacksService(ElmUtils.getConfig(
				tantenId, tzx_shopId), ElmUtils.getToken(tantenId, tzx_shopId));

		ShopContract shopContract = null;
		String pn = SHOP_FOODSEND_TYPE_FENGNIAO;
		try {
			shopContract = packsService.getEffectServicePackContract(th_shopId);

			// 2018-01-10 fengzw 防止获取饿了么配送方式不正常返回 导致总部存单失败
			// 2018-01-22 zhangy 平台获取配送方式失败，返回平台送，防止订单无法入库
			pn = shopContract.getContractTypeName();

			if (pn.contains("蜂鸟专送") || pn.contains("蜂鸟快送")
					|| pn.contains("新零售") || pn.contains("e配送")
					||pn.contains("蜂鸟混合送")||pn.contains("蜂鸟质选")) {

				logger.info("订单【" + orderCode + "】原始配送方式：" + pn + ",统一转换："
						+ SHOP_FOODSEND_TYPE_FENGNIAO);

				return SHOP_FOODSEND_TYPE_FENGNIAO;
			} else {

				logger.info("订单【" + orderCode + "】配送方式：" + pn);
			}

		} catch (Exception e) {
			e.printStackTrace();

			pn = SHOP_FOODSEND_TYPE_FENGNIAO;

			logger.info("订单【" + orderCode + "】获取配送方式发生异常，设定为平台送。配送方式：" + pn);
		}

		return pn;
	}

	private void log(String type, String methodName, String requestBody,
			String responseBody) {
		CcBusniessLogBean ccLog = new CcBusniessLogBean();
		ccLog.setCategory("cc");
		ccLog.setChannel("EL09");
		ccLog.setChannelName("饿了么2.0");
		ccLog.setCreateTime(new Date().getTime());
		ccLog.setRequestBody(requestBody);
		ccLog.setIsNormal("1");
		ccLog.setIsThird("");
		ccLog.setType(type);
		ccLog.setCmd(this.getClass().getPackage().getName() + "." + methodName);
		ccLog.setResponseBody(responseBody);
		ccLog.setErrorBody("");
		// ccLog.setShopId(requestOrder.optString("shopId"));
		ccLog.setTenancyId(tenantId);
		ccLog.setTzxId(orderCode);
		// ccLog.setThirdId(requestOrder.optString("id"));

		try {
			// CcBusinessLogUtils.log(ccLog);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	// at 2017-08-15
	@Override
	protected JSONObject createCustomerInfo() {
		String address = CommonUtil.replaceEvilChar(requestOrder.optString(
				"deliveryPoiAddress").trim());
		JSONArray phone = requestOrder.optJSONArray("phoneList");

		List phoneList = phone.toList(phone);

		String phoneStr = phoneList.size() > 0 ? phoneList.get(0).toString()
				: "";
		// 增加判断如果电话为空，则不进行外卖即会员的操作
		if (StringUtils.isEmpty(phoneStr)) {
			return null;
		}

		String name=CommonUtil.replaceEvilChar(requestOrder
				.optString("consignee").trim());
		String[] delivery_geo = requestOrder.optString("deliveryGeo")
				.split(",");

		JSONObject customer = new JSONObject();
		customer.put("mobil", phoneStr);
		customer.put("add_chanel", Constant.ELE_CHANNEL);
		customer.put("sex", "");
		customer.put("name", name);

		JSONObject addresslist = new JSONObject();

		addresslist.put("sex", "");
		addresslist.put("province", "");
		addresslist.put("city", "");
		addresslist.put("area", "");
		addresslist.put("address", address);
		addresslist.put("consignee_phone", phoneStr);
		addresslist.put("consignee", name);
		addresslist.put("longitude", delivery_geo[0]);
		addresslist.put("latitude", delivery_geo[1]);
		addresslist.put("baidu_location", "");

		customer.put("addresslist", addresslist);

		return customer;
	}
}
