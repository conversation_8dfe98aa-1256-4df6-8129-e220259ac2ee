package com.tzx.cc.bo;

import java.util.List;

import com.tzx.framework.bo.dto.Combotree;

import net.sf.json.JSONObject;

public interface OrderingAbnormalReasonsetService
{
	String	NAME = "com.tzx.cc.bo.imp.OrderingAbnormalReaOrderingAbnormalReasonsetImplsonsetImpl";
	
	public JSONObject getOrderingAbnormalReason(String tenancyID,JSONObject condition);
	
	public String getUnusualReasonTree(String tenancyID,JSONObject params) throws Exception;
	
	public List<Combotree> getCombotreesByTableNameAndFatherColumn(String tenancyId,String tableName,String fatherColumn,String nameColumn,String typeColumn,String type,String codeColumn,JSONObject jb);

	JSONObject getSysEncodeingScheme(String tenancyId, String tableName);
	
	public String saveOrUpdate(String tenancyID,JSONObject condition) throws Exception;

   public String delete(String tenantId, String tableKey, List<JSONObject> keyList) throws Exception;
	
	public String delete(String tenantId, String tableKey, JSONObject key) throws Exception;
}
