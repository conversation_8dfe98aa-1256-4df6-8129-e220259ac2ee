package com.tzx.report.bo.imp.boh;


import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.report.bo.boh.AbnormityOrderForTakeoutQueryService;
import com.tzx.report.bo.boh.BusinessSummaryStatisticsFastfoodService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.po.boh.dao.BusinessSummaryStatisticsFastfoodDao;

@Service(BusinessSummaryStatisticsFastfoodService.NAME)
public class BusinessSummaryStatisticsFastfoodServiceImpl implements BusinessSummaryStatisticsFastfoodService
{
	@Resource(name = BusinessSummaryStatisticsFastfoodDao.NAME)
	private BusinessSummaryStatisticsFastfoodDao businessSummaryStatisticsFastfoodDao;
	@Resource(name=AbnormityOrderForTakeoutQueryService.NAME)
	private AbnormityOrderForTakeoutQueryService abnormityOrderForTakeoutQueryService;
	
	@Resource
	ConditionUtils conditionUtils;

	@Override
	public JSONObject getBusinessSummaryStatisticsFastfood(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = JSONObject.fromObject("{}");
		result.element("rows", businessSummaryStatisticsFastfoodDao.getBusinessSummaryStatisticsFastfood(tenancyID, condition));
		result.element("paymentDetail", conditionUtils.getPaymentDetailVerticalVersion(tenancyID, condition));
		// 增加外卖付款方式
		result.element("takeOutaymentDetail", businessSummaryStatisticsFastfoodDao.getBusinessSummaryTakeOutaymentDetail(tenancyID, condition));
		//增加外卖异常订单查询at 20180914 zhangy
		condition.put("store_ids", condition.optString("store_id"));
		result.element("takeOutwayExceptionDetail", abnormityOrderForTakeoutQueryService.getAbnormityBill(tenancyID, condition).optString("rows"));
		result.element("success", true);
		 return result;
	}
}
