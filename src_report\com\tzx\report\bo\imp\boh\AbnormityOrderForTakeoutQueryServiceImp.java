package com.tzx.report.bo.imp.boh;


import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.boh.AbnormityOrderForTakeoutQueryService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;

@Service(AbnormityOrderForTakeoutQueryService.NAME)
public class AbnormityOrderForTakeoutQueryServiceImp implements AbnormityOrderForTakeoutQueryService
{
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	@Resource
	ConditionUtils conditionUtils;

	private static String sql =" SELECT * FROM saas_report_engine WHERE report_num = 'SAAS_BI_2018_12' ";
	@Override
	public JSONObject getAbnormityBill(String tenancyID, JSONObject condition) throws Exception
	{
		List<JSONObject> lists = new ArrayList<JSONObject>();
		
		String reportSql=  parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sql);
		lists = this.dao.query4Json(tenancyID,reportSql.toString());
		
		JSONObject result = JSONObject.fromObject("{}");
		/*JSONObject json = new JSONObject () ;
		json.put("projec_name", "projec_name1");
		json.put("projec_num", "2");
		lists.add(json);*/
		
		result.element("rows",lists );
		result.element("success", true);
		return result;
	}
}
