package com.tzx.cc.baidu.log;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;

import net.sf.json.JSONObject;

import com.tzx.cc.baidu.util.SuweiPropertyUtil;
import com.tzx.cc.bo.OrderUpdateManagementService;
import com.tzx.cc.bo.dto.Data;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

public class FailedRemedy implements Runnable {
	private ScheduledExecutorService executorService;
	private JSONObject jsonObject;
	private JSONObject result;
	private OrderUpdateManagementService orderUpdateManagementService;
	public FailedRemedy(ScheduledExecutorService executorService,OrderUpdateManagementService orderUpdateManagementService,JSONObject jsonObject,JSONObject result){
		this.jsonObject = jsonObject;
		this.executorService = executorService;
		this.orderUpdateManagementService = orderUpdateManagementService;
		this.result = result;
	}
	private int num = 1;
	@Override
	public void run() {
		try {
			Data data = new Data();
			data.setTenancy_id(jsonObject.optString("tenant_id"));
			JSONObject param = new JSONObject();
			List<JSONObject> list = new ArrayList<JSONObject>();
			param.put("order_code", jsonObject.optString("order_code"));
			list.add(param);
			data.setData(list);
			DBContextHolder.setTenancyid(data.getTenancy_id());
			orderUpdateManagementService.orderIssued(data);
//			String responseInfo = suweiService.thirdSuweiApplayOrderReq(jsonObject, result);
			String responseInfo = ResultThreadlocalUtil.get();
			com.alibaba.fastjson.JSONObject resultJson = com.alibaba.fastjson.JSONObject
					.parseObject(responseInfo);
			if(resultJson!=null && resultJson.containsKey("oid")) {
				executorService.shutdownNow();
			} 
		} catch (Exception e) {
			e.printStackTrace();
		}
		num++;
		System.out.println(num);
		if(num>=Integer.parseInt(SuweiPropertyUtil.getMsg("push_time_num"))) {
			System.out.println("结束");
			executorService.shutdownNow();
		}
	}
}
