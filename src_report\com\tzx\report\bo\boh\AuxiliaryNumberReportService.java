package com.tzx.report.bo.boh;

import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.util.List;

public interface AuxiliaryNumberReportService {

	String NAME = "com.tzx.report.bo.imp.boh.AuxiliaryNumberReportServiceImpl";

	JSONObject getAuxiliaryNumberReport(String tenancyId, JSONObject json) throws Exception;

	HSSFWorkbook exportData(String tenancyId, JSONObject json, HSSFWorkbook workBook)throws Exception;

	HSSFWorkbook exportDataNew(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception;
}
