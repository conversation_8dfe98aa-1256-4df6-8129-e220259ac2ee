package com.tzx.report.common.statistics.filter;

import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.report.common.statistics.task.RequestUrlTask;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Created by zds on 2018-10-31.
 */
public class RequestUrlCountFilter implements Filter {

    private static Logger LOG = LoggerFactory.getLogger(RequestUrlCountFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        //init
        LOG.info("init...");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest)request;
        String url = req.getRequestURI();
        String tenentid = DBContextHolder.getTenancyid();
        if(null ==tenentid){
            tenentid = (String) req.getSession().getAttribute("tenentid");
        }

        LOG.info(" report requesturl {}", url);
        if(url.contains("reportpages")){
            String menuId = req.getParameter("fourLevelId");
            JSONObject pars = new JSONObject();
            pars.put("tenentid", tenentid);
            pars.put("reqUrl", url);
            pars.put("menuId", menuId);
            LOG.info("pars{}",pars);
            RequestUrlTask task = new RequestUrlTask(pars);
            task.startTask();
        }

        chain.doFilter(request,response);
    }

    @Override
    public void destroy() {

    }
}
