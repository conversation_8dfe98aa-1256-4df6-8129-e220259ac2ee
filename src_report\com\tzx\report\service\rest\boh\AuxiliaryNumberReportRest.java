package com.tzx.report.service.rest.boh;

import java.io.*;
import java.net.URLEncoder;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.report.common.util.ReportExportUtils;
import jxl.Workbook;
import jxl.write.WritableWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.boh.AuxiliaryNumberReportService;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

@Controller("AuxiliaryNumberReportRest")
@RequestMapping("/report/AuxiliaryNumberReportRest")
public class AuxiliaryNumberReportRest {

	
	@Resource(name = AuxiliaryNumberReportService.NAME)
	private AuxiliaryNumberReportService auxiliaryNumberReportService;
	
	
	@RequestMapping(value = "/getAuxiliaryNumberReport")
	public void getAuxiliaryNumberReport(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			String storeString = "store_id";
			if(p.optString(storeString).length()==0 ||
					"0".equals(p.optString(storeString)) ||
					"'0'".equals(p.optString(storeString)) ||
					"99999999".equals(p.optString(storeString)) ||
					"''".equals(p.optString(storeString)) ||
					"'99999999'".equals(p.optString(storeString)) 
					){
				if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}else{
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}
			p.element("tenancy_id",(String) session.getAttribute("tenentid"));
			result = auxiliaryNumberReportService.getAuxiliaryNumberReport((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}


	/**
	 * 导出Excel全部的数据
	 *
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/exportData")
	public void exportUrl(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{

			workBook = new HSSFWorkbook();

			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}

			if(p.optString("store_id").length()==0){
				p.element("store_id", session.getAttribute("user_organ_codes_group"));
			}
			p.element("tenancy_id",(String) session.getAttribute("tenentid"));
			workBook = auxiliaryNumberReportService.exportData((String) session.getAttribute("tenentid"), p ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			ReportExportUtils.download(workBook,response,"菜品辅助数量销售报表");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}



	//导出Excel选中的数据
	@RequestMapping(value = "/exportDataNew")
	public void exportDataNew(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{

			workBook = new HSSFWorkbook();

			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}

			if(p.optString("store_id").length()==0){
				p.element("store_id", session.getAttribute("user_organ_codes_group"));
			}
			p.element("tenancy_id",(String) session.getAttribute("tenentid"));
			workBook = auxiliaryNumberReportService.exportDataNew((String) session.getAttribute("tenentid"), p ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			ReportExportUtils.download(workBook,response,"菜品辅助数量销售报表");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}

	}
}
