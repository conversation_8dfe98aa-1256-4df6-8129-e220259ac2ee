package com.tzx.cc.bo;

import java.util.List;

import com.tzx.framework.bo.dto.Combotree;
import com.tzx.framework.common.exception.SystemException;

import net.sf.json.JSONObject;

public interface OrderManagementHqService {
	String	NAME = "com.tzx.cc.bo.imp.OrderManagementServiceImpl";
	
	public JSONObject loadOrderingList(String tenancyID,JSONObject condition) throws Exception;
	
	public JSONObject loadOrderRepayment(String tenancyID,JSONObject condition) throws Exception;
	
	public List<JSONObject> loadOrderRepaymentList(String tenancyID,JSONObject condition) throws Exception;
	
	public JSONObject loadOrderingDishsDetail(String tenancyID, JSONObject obj) throws Exception,SystemException;
	
	public List<JSONObject> loadOrderElectronicList(String tenancyID,JSONObject condition) throws Exception;
	
	public JSONObject loadOrderingDishsDetail1(String tenancyID, JSONObject obj) throws Exception,SystemException;
	
	public Boolean orderReIssued(String tenancyID, JSONObject obj) throws Exception,SystemException;
	
	public JSONObject loadingOrderIssuedInformation(String tenancyID, JSONObject obj) throws Exception,SystemException;
	
	public JSONObject queryModifyOrderInformation(String tenancyID,JSONObject condition) throws Exception;
	
	public Boolean saveUpdateOrderConfirm(String tenancyID, JSONObject obj) throws Exception,SystemException;
	//第三方做订单取消
	public void  orderCancel(String tenancyId, JSONObject param)throws Exception;
	//第三方做订单完成
	public void  orderComplete(String tenancyId, JSONObject param)throws Exception;
	//根据第三方订单编号查询订单信息
	public JSONObject loadOrderingByThirdCode(String tenancyID,JSONObject condition) throws Exception;
	//获取该机构的系统级取消订单的原因
	
	public JSONObject loadUnusualReason(String tenancyID,String reason_code,int store_id) throws Exception;
	
	public List<Combotree> getCancleComplaintTree(String tenancyId,String tableName,String fatherColumn,String nameColumn,String typeColumn,String type,String codeColumn,String store_id,JSONObject jb);

	JSONObject getSysEncodeingScheme(String tenancyId, String tableName);
	
	public boolean checkIsHaveRepayment(String tenentId, String order_code)throws Exception;
	
	public List<JSONObject> loadOrderOrderCreditList(String tenancyID,JSONObject condition) throws Exception;
	//总部取消订单下发
	public void hqCancelOrderIssued(String tenancyId,JSONObject update_order_list_result,JSONObject order_reason_detail,JSONObject qd_obj,JSONObject query_result_obj) throws Exception;
	
}
