package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.tzx.crm.base.utils.SqlUtil;
import com.tzx.report.common.util.ConditionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.BillingQueryDao;

@Repository(BillingQueryDao.NAME)
public class BillingQueryDaoImpl implements BillingQueryDao{
 
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;

	@Resource
	ConditionUtils conditionUtils;
	 
	List<JSONObject> list =null;
	StringBuilder sb = new StringBuilder();
	 
	@SuppressWarnings("unchecked")
	@Override
	public JSONObject getBillingQuery(String tenancyID, JSONObject condition) throws Exception{	
		List<JSONObject> list = new ArrayList<JSONObject>();
		//List<JSONObject> column = new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		String f_pos_bill_detail_query = null;
		String total_f_pos_bill_detail_query = null;
		JSONObject result = new JSONObject();
		long total = 0L;
		
		
		/*if(condition.optInt("type")==1){*/
		String reportSql =null;
		String p_date_begin = condition.optString("p_date_begin");
		String p_date_end = condition.optString("p_date_end");
		String p_store_id = condition.optString("p_store_id");
		Integer customer_id = condition.optInt("customer_id");
		if(customer_id>0)
		{
			condition.remove("customer_id");
			StringBuilder sb = new StringBuilder();
			sb.append(" with t0 as (select id,mobil from crm_customer_info where id ="+customer_id);
			sb.append(" ),t1 as (select t0.id as cucustomer_id,t0.mobil,a.card_code from t0");
			sb.append(" LEFT JOIN crm_customer_card as a on t0.id=a.customer_id");
			sb.append(" ),t2 as (select bill_num,mobil,card_code");
			sb.append(" from pos_bill_member  as a");
			sb.append(" where EXISTS(select 1 from t1 where (a.mobil=t1.mobil and COALESCE(a.mobil,'')<>'') or (a.card_code=t1.card_code and COALESCE(a.card_code,'')<>''))");
			sb.append(" group by bill_num,mobil,card_code),t3 as (");
			sb.append(" select bill_num,store_id from pos_bill2 as a");
			sb.append(" where 1=1 ");
			if(p_date_begin.length()>0 && p_date_end.length()>0)
			{
				sb.append("and report_date BETWEEN "+p_date_begin+" and "+p_date_end+" ");
			}
			if(p_store_id.length()>0 && !"null".equals(p_store_id))
			{
                p_store_id = p_store_id.replaceAll("'", "");
				p_store_id = SqlUtil.getInnerStr(p_store_id.split(","));
				sb.append(" and store_id in("+p_store_id+")");
			}
			sb.append(" and EXISTS (select 1 from t2 where a.bill_num=t2.bill_num))");
			sb.append(" select string_agg(bill_num,',') as billnums");
			sb.append(" from t3 ");
			List<JSONObject> p_bill_num_list = this.dao.query4Json(tenancyID,sb.toString());
			if(p_bill_num_list.size()>0)
			{
				JSONObject jo = p_bill_num_list.get(0);
				String 	p_bill_num = jo.optString("billnums");
				if(p_bill_num.length()>4)
				{
					condition.put("p_bill_num",p_bill_num);
				}else {
					condition.put("p_bill_num", "-1");
				}
			}else {
				condition.put("p_bill_num", "-1");
			}
		}

		if(condition.containsKey("p_sale_mode")&&!condition.optString("p_sale_mode").equals(null)) {
			reportSql= parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.ENGINE_BILLING_QUERY_SALEMODE);
		}else {
			reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.ENGINE_BILLING_QUERY);
		}
			SqlRowSet column = dao.query(tenancyID, reportSql.toString());
			if(column.next()){
				if(condition.containsKey("p_sale_mode")&&!condition.optString("p_sale_mode").equals(null)) {
					f_pos_bill_detail_query = column.getString("f_pos_bill_detail_query_salemode");
				}else{
					f_pos_bill_detail_query = column.getString("f_pos_bill_detail_query");
				}
				
			}
			list = this.dao.query4Json(tenancyID,f_pos_bill_detail_query.toString());
		    structure = conditionUtils.getSqlStructure(tenancyID,f_pos_bill_detail_query.toString());

		//合计
			String TotalSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.ENGINE_TOTAL_BILLING_INQUIRIES);
			
			SqlRowSet columnTotal = dao.query(tenancyID, TotalSql.toString());
			if(columnTotal.next()){
				total_f_pos_bill_detail_query = columnTotal.getString("f_pos_bill_total_query");
			}
			footerList = this.dao.query4Json(tenancyID, total_f_pos_bill_detail_query);
			total = footerList.get(0).getLong("total_count");
			
			/*result.put("footer", footerList);
			result.put("total",total);*/
		/*}else{
			String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.ENGINE_BILLING_QUERY);
			SqlRowSet column = dao.query(tenancyID, reportSql.toString());
			if(column.next()){
				f_pos_bill_detail_query = column.getString("f_pos_bill_detail_query");
			}
			list = this.dao.query4Json(tenancyID,f_pos_bill_detail_query.toString());
			
			result.put("total",condition.optString("pageTotal"));
		}*/
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("rows", list);
		result.put("page", pagenum);
		result.put("footer", footerList);
		result.put("total",total);
		result.put("structure",structure);

		return result;
	}

	@Override
	public JSONObject getBillDetails(String tenancyID, JSONObject condition)throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		
		String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_BILL_DETAILS);
		total = this.dao.countSql(tenancyID,reportSql.toString());
		list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
			
		footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TOTAL_BILL_DETAILS).toString());
		 
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		return result;
	}

	@Override
	public JSONObject getBillPayment(String tenancyID, JSONObject condition)throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		
		String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_BILL_PAYMENT);
		total = this.dao.countSql(tenancyID,reportSql.toString());
		list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
			
		footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TOTAL_BILL_PAYMENT).toString());
		 
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		return result;
	}

}
