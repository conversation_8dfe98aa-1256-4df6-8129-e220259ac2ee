<?xml version="1.0" encoding="UTF-8"?>
<urls>
	<native_url name="native_url" value="http://localhost:9090/tzxsaas/invoice/elec/getElectricData?para="/>

	<!--访问百旺的url-->
	<!-- 测试 -->
	<!-- <total_url name="total_url" value="https://dev.fapiao.com:18943/fpt-dsqz/invoice"/> -->
	<!--正式  -->
	<total_url name="total_url" value="https://www.fapiao.com:63087/fpt-dsqz/invoice"/>
	
	<!-- 访问二维码的url -->
   <!-- <ewm_url name="ewm_url" value="http://testwx.fapiao.com/fpt-wechat/wxscan/wxkp.do"/>-->	
   <!--    正式 -->
   <ewm_url name="ewm_url" value="https://www.fapiao.com/fpt-wechat/wxscan/wxkp.do"/>

    <!--总客户端证书，只有一个-->
    <secret_type name="total_secret">
            <!--总客户端证书的位置，相对于classpath下的-->
			<url name="url" value="\invoice\electronic\certificate\fapiao2017client.truststore"/>
            <!--总客户端证书的密码-->
			<password name="password" value="123456"/>
	</secret_type>

    <!--企业APP_ID-->
    <app_id name="app_id" value="6d29f136114544bcc73edcce960c430231183cc192c433e2b9ebcad56e8ceb08"/>
    <!--AES加密密钥-->
    <app_id name="content_password" value="5EE6C2C11DD421F2"/>
</urls>