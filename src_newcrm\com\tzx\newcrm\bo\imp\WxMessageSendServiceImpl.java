package com.tzx.newcrm.bo.imp;

import com.tzx.crm.base.Constant;
import com.tzx.crm.base.dao.HrBaseDao;
import com.tzx.framework.common.exception.SystemErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.newcrm.bo.CustomerService;
import com.tzx.newcrm.bo.WxMessageSendService;
import com.tzx.newcrm.po.springjdbc.dao.CardDao;
import com.tzx.newcrm.po.springjdbc.dao.CustomerDao;
import com.tzx.newcrm.vo.card.CardVo;
import com.tzx.newcrm.vo.customer.ConsumeVo;
import com.tzx.newcrm.vo.customer.CustomerVo;
import com.tzx.newcrm.vo.customer.LevelRule;
import com.tzx.weixin.common.constant.TemplateRelationEnum;
import com.tzx.weixin.common.model.resp.TemplateMessage;
import com.tzx.weixin.common.model.resp.TemplateMessageItem;
import com.tzx.weixin.common.task.MessageAsyncService;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 
 * 发送微信消息
 * <AUTHOR>
 *
 */
@SuppressWarnings("unchecked")
@Service(value = WxMessageSendService.NAME)
public class WxMessageSendServiceImpl implements WxMessageSendService {
	
	@Resource(name = "hrBaseDaoImpl")
	private HrBaseDao dao;
	@Resource(name = CustomerService.NAME)
	private CustomerService customerService;
	@Resource(name = CardDao.NAME)
	private CardDao cardDao;
	@Autowired
	private MessageAsyncService	messageService;
	@Resource(name=CustomerDao.NAME)
	private CustomerDao customerDao;

	@Autowired
	private MessageAsyncService		service;
	
	private Logger logger = Logger.getLogger(this.getClass().getName());
	
	private String logerror = "[发送微信消息错误]";
	
	
	@Override
	public void sendConsumeMessage(String tenancyId, Integer storeId, Integer customerId,String mobil, String billCode,
			String cardCode, String businessDate, Double consumeMoney, Integer payType) {
		if( checkConsumeMessageParam(tenancyId, storeId, customerId,mobil, billCode, cardCode, businessDate, consumeMoney, payType) ){
			JSONObject customer = getCustomerInfo(tenancyId , customerId , mobil);
			if(customer == null){
				logger.error(logerror+"会员不存在!");
			} else{
				if("".equals(customer.optString("wechat","")) || "".equals(customer.optString("remark",""))){
//					logger.info("没有绑定微信，不需要发送消息"+customerId);
				} else {//发送微信消息
					String wxUserId =  customer.optString("remark");
					JSONObject templete = getWxTemplete(tenancyId, TemplateRelationEnum.TRADE_COST);
					
					if(templete != null){
						ConsumeVo consumeVo = customerService.getConsumeVoByEHK(tenancyId, billCode,false);
						CardVo card = null;
						if(!StringUtils.isEmpty(cardCode)){
							card = cardDao.getCard(tenancyId, cardCode);
						}
						
						JSONObject mess_wx_xf = new JSONObject();
						String first = "尊敬的会员 " + customer.optString("name") + " ,您本次消费账单如下：\n流水号:" + billCode ;
						if( payType == 1){//会员卡
							first += "\n卡号：" + cardCode;
						}
						String keyword2 = "";
						switch (payType) {
							case 0:
								keyword2 = "现金";
								break;
							case 1:
								keyword2 = "会员卡";
								break;
							case 2:
								keyword2 = "微信";
								break;
							case 3:
								keyword2 = "支付宝";
								break;
							case 4:
								keyword2 = "银行卡";
								break;
							default:
								break;
						}
						mess_wx_xf.put("first", first);
						// 交易时间
						mess_wx_xf.put("keyword1", (businessDate == null ? businessDate : DateUtil.getNowDateYYDDMMHHMMSS()) +"\n" );
						// 交易类型
						mess_wx_xf.put("keyword2", keyword2+"消费\n");
						// 交易金额
						mess_wx_xf.put("keyword3",  consumeMoney
								+ (consumeVo != null && consumeVo.getCredit() != null && consumeVo.getCredit() > 0 ?  ("\n本次消费产生积分："+consumeVo.getCredit()) :"" ) 
								+ "\n\n结账方式："+keyword2+"\n消费餐厅："+
								(StringUtils.isEmpty(customer.optString("org_full_name")) || "null".equals(customer.optString("org_full_name")) ? "总部" : customer.optString("org_full_name"))
								+"\n");
						// 备注
						String remark = "";
						if(card != null ){
							remark += "会员卡账户余额(主)："+card.getMainBalance() +"\n会员卡账户余额(赠)："+card.getRewardBalance()+"\n";
						}
						remark += "会员可用积分：" + customer.optInt("useful_credit",0);
						
						mess_wx_xf.put("remark", remark );

						TemplateMessage message = new TemplateMessage();
						message.setTemplate_id(templete.optString("template_id"));
						message.setTouser(wxUserId);
						message.setUrl(templete.optString("url"));
						message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);
						LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();
						Iterator<String> keys = mess_wx_xf.keySet().iterator();
						while (keys.hasNext())
						{
							String key = keys.next();
							TemplateMessageItem temp = new TemplateMessageItem(mess_wx_xf.optString(key), Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put(key, temp);
						}
						message.setData(data);
						messageService.messageTemplateSend(tenancyId, message);
					}
				}
			}
		}
	}

	@Override
	public void revokedConsumeMessage(String tenancyId, Integer storeId, Integer customerId,String mobil,String billCode,String cardCode
			,String chanelName ,Double consumeMoney) {
		if( checkRevokedConsumeMessageParam(tenancyId, storeId, customerId,mobil , billCode, cardCode, chanelName) ){
			JSONObject customer = getCustomerInfo(tenancyId , customerId ,mobil );
			if(customer == null){
				logger.error(logerror+"会员不存在!");
			} else{
				if("".equals(customer.optString("wechat","")) || "".equals(customer.optString("remark",""))){
//					logger.info("没有绑定微信，不需要发送消息"+customerId);
				} else {//发送微信消息
					String wxUserId =  customer.optString("remark");
					JSONObject templete = getWxTemplete(tenancyId, TemplateRelationEnum.TRADE_REFUNED);
					
					if(templete != null){
//						ConsumeVo consumeVo = customerService.getConsumeVo(tenancyId, billCode);
						CardVo card = null;
						if(!StringUtils.isEmpty(cardCode)){
							card = cardDao.getCard(tenancyId, cardCode);
						}
						
						JSONObject mess_wx_xf = new JSONObject();
						mess_wx_xf.put("first", "尊敬的 " + customer.optString("name") + " ,您本次撤销消费账单如下:\n");
						// 交易时间
						mess_wx_xf.put("keyword1", DateUtil.getNowDateYYDDMMHHMMSS() + "\n流水号:" + billCode 
								+ (StringUtils.isEmpty(cardCode) ? "" : "\n卡号:" + cardCode));
						// 交易类型
						mess_wx_xf.put("keyword2", "撤销卡消费\n撤销渠道:"+chanelName+"\n撤销餐厅:"
						+(StringUtils.isEmpty(customer.optString("org_full_name")) || "null".equals(customer.optString("org_full_name")) ? "总部" : customer.optString("org_full_name"))+"\n");
						// 交易金额
						mess_wx_xf.put("keyword3", "" +consumeMoney + "\n");
						// 备注
						String remark = "";
						if(card != null ){
							remark += "会员卡账户余额(主)："+card.getMainBalance() +"\n会员卡账户余额(赠)："+card.getRewardBalance()+"\n";
						}
						remark += "会员可用积分：" + customer.optInt("useful_credit",0);
						mess_wx_xf.put("remark", remark);

						TemplateMessage message = new TemplateMessage();
						message.setTemplate_id(templete.optString("template_id"));
						message.setTouser(wxUserId);
						message.setUrl(templete.optString("url"));
						message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);
						LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();
						Iterator<String> keys = mess_wx_xf.keySet().iterator();
						while (keys.hasNext())
						{
							String key = keys.next();
							TemplateMessageItem temp = new TemplateMessageItem(mess_wx_xf.optString(key), Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put(key, temp);
						}
						message.setData(data);
						messageService.messageTemplateSend(tenancyId, message);
					}
				}
			}
		}
	}
	
	
	private boolean checkConsumeMessageParam(String tenancyId, Integer storeId, Integer customerId,String mobil, String billCode,
			String cardCode, String businessDate, Double consumeMoney, Integer payType){
		boolean r = true ;
		if( storeId == null ){
			r = false;
			logger.error(logerror+"storeId is null");
		}
		if( customerId == null || StringUtils.isEmpty(mobil)){
			r = false;
			logger.error(logerror+"customerId or mobil is null");
		}
		
		return r;
	}
	
	private boolean checkRevokedConsumeMessageParam(String tenancyId, Integer storeId, Integer customerId,String mobil,  String billCode,
			String cardCode, String chanel){
		boolean r = true ;
		if( storeId == null ){
			r = false;
			logger.error(logerror+"storeId is null");
		}
		if( customerId == null || StringUtils.isEmpty(mobil)){
			r = false;
			logger.error(logerror+"customerId or mobil is null");
		}
		
		return r;
	}
	
	private JSONObject getCustomerInfo(String tenancyId, Integer customerId ,String mobil){
		List<JSONObject> list = null;
		String sql = "select cci.id,cci.wechat,cci.remark,o.org_full_name,cci.useful_credit,cci.name  from crm_customer_info cci " +
					 " left join organ o on cci.store_id = o.id where 1= 1";
		if(customerId != null){
		    sql += " and cci.id = " + customerId ;
		}
		if( !StringUtils.isEmpty(mobil) ){
			sql += " and cci.mobil = '" + mobil +"' ";
		}
		try {
			list = this.dao.query4Json(tenancyId, sql);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e);
		}
		return list == null || list.isEmpty() ? null : list.get(0);
	}
	
	/**
	 * 获取模版信息
	 * @param tenancyId
	 * @param template
	 * @return
	 */
	private JSONObject getWxTemplete(String tenancyId, TemplateRelationEnum template){
		StringBuilder sbwx = new StringBuilder("select * from wx_template_relation where type=" + template.getType());
		List<JSONObject> listwxtl = new ArrayList<JSONObject>();
		try {
			listwxtl = this.dao.query4Json(tenancyId, sbwx.toString());
		} catch (Exception e) {
			e.printStackTrace();
			throw new SystemException(SystemErrorCode.SYSTEM_ERROR);
		}
		return listwxtl == null || listwxtl.isEmpty() ? null : listwxtl.get(0);
	}

	@Override
	public void sendUseCreditMessage(String tenancyid, CustomerVo customer, String code, Double cashMoney,
			Integer credit, Integer storeId) {
		JSONObject mess_wx_xf = new JSONObject();
		TemplateMessage message_xf = null;
		if(customer == null){
			return ;
		}
		String wxids_wx_xf = customer.getRemark();
		// 发送微信消息
		if (wxids_wx_xf != null && wxids_wx_xf.length() > 0 && !"null".equals(wxids_wx_xf))
		{
			
			mess_wx_xf.put("first", "尊敬的会员 " + customer.getCustomerName() + " ,您本次会员积分消费账单如下：\n流水号:" + code );
			// 交易时间
			mess_wx_xf.put("keyword1", DateUtil.getNowDateYYDDMMHHMMSS()+"\n" );
			// 交易类型
			mess_wx_xf.put("keyword2", "会员积分消费\n");
			// 交易金额
			mess_wx_xf.put("keyword3",  cashMoney+ "\n本次消费积分："+credit+"\n\n消费餐厅："+this.customerDao.getStoreName(tenancyid, storeId)+"\n");
			// 备注
			mess_wx_xf.put("remark", "\n会员可用积分：" + customer.getUsefulCredit());


			if (wxids_wx_xf != null && wxids_wx_xf.length() > 0)
			{
				String sbwx = "select * from wx_template_relation where type=" + TemplateRelationEnum.TRADE_COST.getType();
				List<JSONObject> listwxtl = null;
				try {
					listwxtl = this.customerDao.query4Json(tenancyid, sbwx);
				} catch (Exception e) {
					e.printStackTrace();
					throw new SystemException("数据库错误!",SystemErrorCode.SYSTEM_ERROR);
				}

				if (listwxtl != null && listwxtl.size() > 0)
				{
					JSONObject wxt = listwxtl.get(0);
					TemplateMessage message = new TemplateMessage();
					message.setTemplate_id(wxt.optString("template_id"));
					message.setTouser(wxids_wx_xf);
					message.setUrl(wxt.optString("url"));
					message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);
					LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();
					Iterator<String> keys = mess_wx_xf.keySet().iterator();
					while (keys.hasNext())
					{
						String key = keys.next();
						TemplateMessageItem temp = new TemplateMessageItem(mess_wx_xf.optString(key), Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put(key, temp);
					}
					message.setData(data);
					message_xf = message;
				}
			}
		}
		if(message_xf != null){
			//发送消息模板
			messageService.messageTemplateSend(tenancyid, message_xf);
		}
	}

	@Override
	public void sendRevokedCreditMessage(String tenancyid, CustomerVo customer, String code, Double credit,
			Integer storeId) {
		JSONObject mess_wx_xf = new JSONObject();
		TemplateMessage message_xf = null;
		if(customer == null){
			return ;
		}
		String wxids_wx_xf = customer.getRemark();
		// 发送微信消息
		if (wxids_wx_xf.length() > 0 && !"null".equals(wxids_wx_xf))
		{
			
			mess_wx_xf.put("first", "尊敬的会员 " + customer.getCustomerName() + " ,您本次撤销积分消费账单如下：\n流水号:" + code );
			// 交易时间
			mess_wx_xf.put("keyword1", DateUtil.getNowDateYYDDMMHHMMSS()+"\n" );
			// 交易类型
			mess_wx_xf.put("keyword2", "撤销积分消费\n");
			// 交易金额
			mess_wx_xf.put("keyword3", "本次撤销积分："+credit+"\n\n撤销积分餐厅："+this.customerDao.getStoreName(tenancyid, storeId)+"\n");
			// 备注
			mess_wx_xf.put("remark", "\n会员可用积分：" + customer.getUsefulCredit());


			if (wxids_wx_xf != null && wxids_wx_xf.length() > 0)
			{
				String sbwx = "select * from wx_template_relation where type=" + TemplateRelationEnum.TRADE_COST.getType();
				List<JSONObject> listwxtl = null;
				try {
					listwxtl = this.customerDao.query4Json(tenancyid, sbwx);
				} catch (Exception e) {
					e.printStackTrace();
					throw new SystemException("数据库错误!",SystemErrorCode.SYSTEM_ERROR);
				}


				if (listwxtl.size() > 0)
				{
					JSONObject wxt = listwxtl.get(0);
					TemplateMessage message = new TemplateMessage();
					message.setTemplate_id(wxt.optString("template_id"));
					message.setTouser(wxids_wx_xf);
					message.setUrl(wxt.optString("url"));
					message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);
					LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();
					@SuppressWarnings("unchecked")
					Iterator<String> keys = mess_wx_xf.keySet().iterator();
					while (keys.hasNext())
					{
						String key = keys.next();
						TemplateMessageItem temp = new TemplateMessageItem(mess_wx_xf.optString(key), Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put(key, temp);
					}
					message.setData(data);
					message_xf = message;
				}
			}
		}
		if(message_xf != null){
			//发送消息模板
			messageService.messageTemplateSend(tenancyid, message_xf);
		}
	}

	@Override
	public void sendExpireMessage(String tenancyId, String openId, JSONObject messageContent) {
		StringBuilder sbwx = new StringBuilder("select template_id,url from wx_template_relation where type=" + TemplateRelationEnum.COUPONS_LOSE.getType());
		List<JSONObject> listwxtl = null;
		try {
			listwxtl = this.dao.query4Json(tenancyId, sbwx.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
		//查询有没有启用微信模板
		if (listwxtl.size() > 0) {
			JSONObject wxt = listwxtl.get(0);
			TemplateMessage message = new TemplateMessage();
			message.setTemplate_id(wxt.optString("template_id"));
			message.setTouser(openId);
			message.setUrl(wxt.optString("url"));
			message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);
			//替换券过期模板的参数
			LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();

//						您好，您有优惠券即将到期，请及时消费使用。
//						您的优惠券 10元代金券 券号 12345689有效期至
//						2018-10-10，请及时消费使用。

			TemplateMessageItem first = new TemplateMessageItem(messageContent.optString("first"), Constant.WX_TEMPLATE_TEXT_COLOR);
			data.put("first", first);
			TemplateMessageItem keyword1 = new TemplateMessageItem(messageContent.optString("yhq"), Constant.WX_TEMPLATE_TEXT_COLOR);
			data.put("name", keyword1);
			TemplateMessageItem keyword2 = new TemplateMessageItem(messageContent.optString("expDate"), Constant.WX_TEMPLATE_TEXT_COLOR);
			data.put("expDate", keyword2);
			TemplateMessageItem remark = new TemplateMessageItem(messageContent.optString("remark"), Constant.WX_TEMPLATE_TEXT_COLOR);
			data.put("remark", remark);
			message.setData(data);
			//调用微信发送消息接口
			service.messageTemplateSend(tenancyId, message);
		}

	}

	
	@Override
	public void usedCouponsMesg(String tenancyId,String openId, String title, String type, String couponscode, JSONObject msgjb)
	{
		try
		{
			if (openId != null && openId.length() > 0)
			{
				StringBuilder sbwx = new StringBuilder("select * from wx_template_relation where type=10");
				List<JSONObject> listwxtl = this.dao.query4Json(tenancyId, sbwx.toString());
				if (listwxtl.size() > 0)
				{
					JSONObject wxt = listwxtl.get(0);
					TemplateMessage message = new TemplateMessage();
					message.setTemplate_id(wxt.optString("template_id"));
					message.setTouser(openId);
					message.setUrl(wxt.optString("url"));
					message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);
					LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();
//					@SuppressWarnings("unchecked")
//					Iterator<String> keys = msgjb.keySet().iterator();
//					while (keys.hasNext())
//					{
//						String key = keys.next();
//						TemplateMessageItem temp = new TemplateMessageItem(msgjb.optString(key), Constant.WX_TEMPLATE_TEXT_COLOR);
//						data.put(key, temp);
//					}
				
					TemplateMessageItem temp0 = new TemplateMessageItem("使用券号", Constant.WX_TEMPLATE_TEXT_COLOR);
					data.put("productType",temp0);
//					TemplateMessageItem temp1 = new TemplateMessageItem(type, Constant.WX_TEMPLATE_TEXT_COLOR);
//					data.put("keyword1",temp1);
					TemplateMessageItem temp2 = new TemplateMessageItem(DateUtil.getNowDateYYDDMMHHMMSS(), Constant.WX_TEMPLATE_TEXT_COLOR);
					data.put("time",temp2);
					TemplateMessageItem temp3 = new TemplateMessageItem(couponscode, Constant.WX_TEMPLATE_TEXT_COLOR);
					data.put("name",temp3);
					TemplateMessageItem temp4 = new TemplateMessageItem("", Constant.WX_TEMPLATE_TEXT_COLOR);
					data.put("remark",temp4);
					message.setData(data);
					service.messageTemplateSend(tenancyId, message);
				}
			}
		}
		catch (Exception e)
		{
			
		}
		
	}
	
	@Override
	public void sendMesg(String tenancyId, int bh, String openId, JSONObject msgjb)
	{
		try
		{
			if (openId != null && openId.length() > 0)
			{
				StringBuilder sbwx = new StringBuilder("select * from wx_template_relation where type=" + bh);
				List<JSONObject> listwxtl = this.dao.query4Json(tenancyId, sbwx.toString());
				if (listwxtl.size() > 0)
				{
					JSONObject wxt = listwxtl.get(0);
					TemplateMessage message = new TemplateMessage();
					message.setTemplate_id(wxt.optString("template_id"));
					message.setTouser(openId);
					message.setUrl(wxt.optString("url"));
					message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);

					LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();
					@SuppressWarnings("unchecked")
					Iterator<String> keys = msgjb.keySet().iterator();
					while (keys.hasNext())
					{
						String key = keys.next();
						TemplateMessageItem temp = new TemplateMessageItem(msgjb.optString(key), Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put(key, temp);
					}
					message.setData(data);
					service.messageTemplateSend(tenancyId, message);
				}
			}
		}
		catch (Exception e)
		{
			
		}
		
	}

	@Override
	public void sendCouponsMesg(String tenancyId,String openId, String title, String type, String couponscode, JSONObject msgjb,String toName)
	{
		sendCouponsMesgV2(tenancyId, openId, title, type, couponscode, msgjb, toName);
	}
	
	/**
	 * 2017-5-31 之前的使用的发送消息模版，目前该模版已经被微信停用
	 * @param tenancyId
	 * @param openId
	 * @param title
	 * @param type
	 * @param couponscode
	 * @param msgjb
	 * @param toName
	 * @deprecated
	 */
	private void sendCouponsMesgV1(String tenancyId,String openId, String title, String type, String couponscode, JSONObject msgjb,String toName)
	{
		try
		{
			if (openId != null && openId.length() > 0)
			{
				StringBuilder sbwx = new StringBuilder("select * from wx_template_relation where type= 11");
				List<JSONObject> listwxtl = this.dao.query4Json(tenancyId, sbwx.toString());
				if (listwxtl.size() > 0)
				{
					JSONObject wxt = listwxtl.get(0);
					TemplateMessage message = new TemplateMessage();
					message.setTemplate_id(wxt.optString("template_id"));
					message.setTouser(openId);
					message.setUrl(wxt.optString("url"));
					message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);
					LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();
                    
					if(msgjb==null)
					{
						TemplateMessageItem temp0 = new TemplateMessageItem("您好，赠送优惠券已成功领取", Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("first",temp0);
						TemplateMessageItem temp01 = new TemplateMessageItem(toName, Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("toName",temp01);
						TemplateMessageItem temp1 = new TemplateMessageItem(type, Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("gift",temp1);
						TemplateMessageItem temp2 = new TemplateMessageItem(DateUtil.getNowDateYYDDMMHHMMSS(), Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("time",temp2);
						TemplateMessageItem temp3 = new TemplateMessageItem("券号：\n"+couponscode, Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("remark",temp3);
					}
					else
					{
						if("supplement".equals(msgjb.optString("type")))
						{
							TemplateMessageItem temp0 = new TemplateMessageItem("您好，赠送积分、优惠券已成功领取！", Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("first",temp0);
							TemplateMessageItem temp01 = new TemplateMessageItem(toName, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("toName",temp01);
							TemplateMessageItem temp1 = new TemplateMessageItem(type, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("gift",temp1);
							TemplateMessageItem temp2 = new TemplateMessageItem(DateUtil.getNowDateYYDDMMHHMMSS(), Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("time",temp2);
							TemplateMessageItem temp3 = new TemplateMessageItem("赠送优惠券："+couponscode, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("remark",temp3);
						}
						else
						{
							TemplateMessageItem temp0 = new TemplateMessageItem("您好，赠送优惠券已成功领取", Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("first",temp0);
							TemplateMessageItem temp01 = new TemplateMessageItem(toName, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("toName",temp01);
							TemplateMessageItem temp1 = new TemplateMessageItem(type, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("gift",temp1);
							TemplateMessageItem temp2 = new TemplateMessageItem(DateUtil.getNowDateYYDDMMHHMMSS(), Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("time",temp2);
							TemplateMessageItem temp3 = new TemplateMessageItem("券号："+couponscode, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("remark",temp3);
						}
						message.setData(data);
						service.messageTemplateSend(tenancyId, message);
					}
					
					
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}		
	}
	
	/**
	 * 微信发送优惠券模版，启用日期 2017-05-31
	 * @param tenancyId
	 * @param openId
	 * @param title
	 * @param type
	 * @param couponscode
	 * @param msgjb
	 * @param toName
	 */
	private void sendCouponsMesgV2(String tenancyId,String openId, String title, String type, String couponscode, JSONObject msgjb,String toName)
	{
		int templateType = TemplateRelationEnum.COUPONS_SEND_NEW.getType();
		//生日营销的优惠券发送，跳转url不同，跳转会员中心
		if ("BRITHDAY_ACTIVITY".equals(title))
			templateType = TemplateRelationEnum.BRITHDAY_ACTIVITY.getType();
		try
		{
			if (openId != null && openId.length() > 0)
			{
				StringBuilder sbwx = new StringBuilder("select * from wx_template_relation where type= "+ templateType);
				List<JSONObject> listwxtl = this.dao.query4Json(tenancyId, sbwx.toString());
				if (listwxtl.size() > 0)
				{
					JSONObject wxt = listwxtl.get(0);
					TemplateMessage message = new TemplateMessage();
					message.setTemplate_id(wxt.optString("template_id"));
					message.setTouser(openId);
					message.setUrl(wxt.optString("url"));
					message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);
					LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();

					if(msgjb==null)
					{
						TemplateMessageItem temp0 = new TemplateMessageItem("您好，赠送优惠券已成功领取", Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("productType",temp0);
						
						TemplateMessageItem temp01 = new TemplateMessageItem(toName, Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("keyword1",temp01);//领取人
						TemplateMessageItem temp1 = new TemplateMessageItem(type, Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("keyword2",temp1);//礼品
						TemplateMessageItem temp2 = new TemplateMessageItem(DateUtil.getNowDateYYDDMMHHMMSS(), Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("keyword3",temp2);//领取时间
						TemplateMessageItem temp3 = new TemplateMessageItem("券号：\n"+couponscode, Constant.WX_TEMPLATE_TEXT_COLOR);
						data.put("remark",temp3);
					}
					else
					{
						if("supplement".equals(msgjb.optString("type")))
						{
							TemplateMessageItem temp0 = new TemplateMessageItem("您好，赠送积分、优惠券已成功领取！", Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("first",temp0);
							TemplateMessageItem temp01 = new TemplateMessageItem(toName, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("keyword1",temp01);
							TemplateMessageItem temp1 = new TemplateMessageItem(type, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("keyword2",temp1);
							TemplateMessageItem temp2 = new TemplateMessageItem(DateUtil.getNowDateYYDDMMHHMMSS(), Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("keyword3",temp2);
							TemplateMessageItem temp3 = new TemplateMessageItem("赠送优惠券："+couponscode, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("remark",temp3);
						}else if ("birthday".equals(msgjb.optString("type"))){
							//生日营销
							TemplateMessageItem temp0 = new TemplateMessageItem("亲爱的会员，在您生日来临之际特送上我们最温暖的祝福，祝您生日快乐。", Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("first",temp0);
							//领取人
							TemplateMessageItem temp01 = new TemplateMessageItem(toName, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("keyword1",temp01);
							//礼品
							TemplateMessageItem temp1 = new TemplateMessageItem(type, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("keyword2",temp1);
							//领取时间
							TemplateMessageItem temp2 = new TemplateMessageItem(DateUtil.getNowDateYYDDMM(), Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("keyword3",temp2);
							TemplateMessageItem temp3 = new TemplateMessageItem("礼品已放入您的会员账户，欢迎您的惠顾！", Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("remark",temp3);
						}
						else
						{
							TemplateMessageItem temp0 = new TemplateMessageItem("您好，赠送优惠券已成功领取", Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("first",temp0);
							TemplateMessageItem temp01 = new TemplateMessageItem(toName, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("keyword1",temp01);
							TemplateMessageItem temp1 = new TemplateMessageItem(type, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("keyword2",temp1);
							TemplateMessageItem temp2 = new TemplateMessageItem(DateUtil.getNowDateYYDDMMHHMMSS(), Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("keyword3",temp2);
							TemplateMessageItem temp3 = new TemplateMessageItem("券号："+couponscode, Constant.WX_TEMPLATE_TEXT_COLOR);
							data.put("remark",temp3);
						}
					}
					if(!data.isEmpty()){
						message.setData(data);
						service.messageTemplateSend(tenancyId, message);
					}
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}		
	}

	@Override
	public void sendLevelChangeMesg(String tenancyId,LevelRule before,LevelRule after,String openId){
		try {
			if(openId == null){
				return;
			}
			StringBuilder memberRights = new StringBuilder();
			if(after.getIsDiscount() != null && "1".equals(after.getIsDiscount()) && after.getRate() != null ){
				memberRights.append("消费享受"+(after.getRate()/10.0)+"折优惠");
			}
			if(after.getIsCredit() != null && "1".equals(after.getIsCredit())
					&& after.getTradingMoney() != null && after.getRewardCredit() != null ){
				memberRights.append("消费"+after.getTradingMoney()+"元积"+after.getRewardCredit()+"分");
			}
			
			StringBuilder sql = new StringBuilder();
			sql.append("select * from wx_template_relation where type=9");
			List<JSONObject> listwxtl = this.dao.query4Json(tenancyId, sql.toString());
			if (listwxtl.size() > 0)
			{
				JSONObject wxt = listwxtl.get(0);
				TemplateMessage message = new TemplateMessage();
				message.setTemplate_id(wxt.optString("template_id"));
				message.setTouser(openId);
				message.setUrl(wxt.optString("url"));
				message.setTopcolor(Constant.WX_TEMPLATE_TEXT_COLOR);
				LinkedHashMap<String, TemplateMessageItem> data = new LinkedHashMap<String, TemplateMessageItem>();

				TemplateMessageItem temp0 = new TemplateMessageItem("您好，您已成功由"+(before == null ? "无等级" : before.getName())+"升级为"+after.getName(), Constant.WX_TEMPLATE_TEXT_COLOR);
				data.put("first",temp0);
				TemplateMessageItem temp01 = new TemplateMessageItem(DateUtil.getNowDateYYDDMMHHMMSS(), Constant.WX_TEMPLATE_TEXT_COLOR);
				data.put("keyword1",temp01);
				TemplateMessageItem temp1 = new TemplateMessageItem(memberRights.toString(), Constant.WX_TEMPLATE_TEXT_COLOR);
				data.put("keyword2",temp1);
				TemplateMessageItem temp3 = new TemplateMessageItem("", Constant.WX_TEMPLATE_TEXT_COLOR);
				data.put("remark",temp3);
				message.setData(data);
				service.messageTemplateSend(tenancyId, message);
			}
		} catch (Exception e) {
			logger.error("升级发送微信消息失败:"+e);
		}
	}
}
