package com.tzx.boh.service.rest;

import java.io.File;
import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.boh.bo.BohPropertyInterfaceService;
import com.tzx.crm.bo.PropertyParametersService;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.JsonUtils;

@Controller("BohPropertyInterfaceRest")
@RequestMapping("/boh/bohPropertyInterfaceRest")
public class BohPropertyInterfaceRest
{
	@Resource(name=BohPropertyInterfaceService.NAME)
	private BohPropertyInterfaceService service;
	@Resource(name = PropertyParametersService.NAME)
	private PropertyParametersService propertyParametersService;
	@RequestMapping(value = "/load")
	public void load(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("last_operator", session.getAttribute("employeeName"));
			p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			p.put("tenancy_id", session.getAttribute("tenentid"));
			result = service.load((String) session.getAttribute("tenentid"),p,(String) session.getAttribute("user_organ_codes_group")).toString();

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 * 查找门店接口ID
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/find")
	public void find(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("last_operator", session.getAttribute("employeeName"));
			p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			p.put("tenancy_id", session.getAttribute("tenentid"));
			result = service.find((String) session.getAttribute("tenentid"),p).toString();

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 检查门店物业上传接口是否为自动上传
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/checkAutoUpload")
	public void checkAutoUpload(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\" : false , \"msg\" : \"自动上传关闭!\"}";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("tenancy_id", session.getAttribute("tenentid"));
			List<JSONObject> list=service.getProperty((String) session.getAttribute("tenentid"),p);
			if(list.size()>0){
				JSONObject json=list.get(0);
				String upload_mode=json.optString("upload_mode","0");
				String type_id=json.optString("type_id","0");
				if(upload_mode.equals("1")){
					result = "{\"success\" : true ,\"type_id\" : "+type_id+" ,  \"msg\" : \"成功!\"}";
				}
			}
			

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"检查自动上传失败!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 * 保存接口配置
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/save")
	public void save(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("last_operator", session.getAttribute("employeeName"));
			p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			p.put("tenancy_id", session.getAttribute("tenentid"));
			result = service.save((String) session.getAttribute("tenentid"),p).toString();

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 * 获取所在月最后一个账单列表
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getList")
	public void getList(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("last_operator", session.getAttribute("employeeName"));
			p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			p.put("tenancy_id", session.getAttribute("tenentid"));
			result = JsonUtils.list2json(service.getList((String) session.getAttribute("tenentid"),p));

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	@RequestMapping(value = "/getMonth")
	public void getMonth(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}	
			p.put("tenancy_id", session.getAttribute("tenentid"));
			result = JsonUtils.list2json(service.getMonth((String) session.getAttribute("tenentid"),p));

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	
	@RequestMapping(value = "/getUploadDetails")
	public void getUploadDetails(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("last_operator", session.getAttribute("employeeName"));
			p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			p.put("tenancy_id", session.getAttribute("tenentid"));
			result = service.getUploadDetails((String) session.getAttribute("tenentid"),p).toString();

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 上传数据
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/upload")
	public void upload(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("last_operator", session.getAttribute("employeeName"));
			p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			p.put("tenancy_id", session.getAttribute("tenentid"));
			p.put("startIndex", 0);
			result = service.upload((String) session.getAttribute("tenentid"),p).toString();
			
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	
	//停用接口
	@RequestMapping(value = "/stop")
	public void stop(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("last_operator", session.getAttribute("employeeName"));
			p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			p.put("tenancy_id", session.getAttribute("tenentid"));
			result = service.stop((String) session.getAttribute("tenentid"),p).toString();

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	
	@RequestMapping(value = "/findOrgan")
	public void findOrgan(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("tenancy_id", session.getAttribute("tenentid"));
			result = service.findOrgan((String) session.getAttribute("tenentid"),p).toString();

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	@RequestMapping(value = "/findTypeId")
	public void findTypeId(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("tenancy_id", session.getAttribute("tenentid"));
			result = service.findTypeId((String) session.getAttribute("tenentid"),p).toString();

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"加载发生错误!\",\"msg1\":\""+e.getMessage()+"\"}";
			e.printStackTrace();
		}
		finally{
			try{
				if (in != null){
					in.close();
				}
			}catch (Exception e){
			}

			try{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}catch (Exception e){
				
			}finally{
				if (out != null) out.close();
			}
		}
	}
	
	@RequestMapping(value = "/clearReportDateData")
	public void clearReportDateData(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			result = service.clearReportDateData((String) session.getAttribute("tenentid"),obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	@RequestMapping(value = "/findErrDate")
	public void findErrDate(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			result = service.findErrDate((String) session.getAttribute("tenentid"),obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	@RequestMapping(value = "/uploadSFTP")
	public void uploadSFTP(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		
		String uploadPath = request.getSession().getServletContext().getRealPath("") ;// 上传文件的目录
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			result = service.uploadSFTP((String) session.getAttribute("tenentid"),uploadPath,obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	@RequestMapping(value = "/uploadMISFTP")
	public void uploadMISFTP(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		
		String uploadPath = request.getSession().getServletContext().getRealPath("") ;// 上传文件的目录
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			result = service.uploadMISFTP((String) session.getAttribute("tenentid"),uploadPath+File.separator+"mis",obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	@RequestMapping(value = "/uploadxcSFTP")
	public void uploadxcSFTP(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		
		String uploadPath = request.getSession().getServletContext().getRealPath("") ;// 上传文件的目录
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			
			JSONObject params = null;
			try
			{
				params = propertyParametersService.queryParameterByStoreId(obj.optString("tenancy_id"), obj);
			}
			catch (Exception e1)
			{
				e1.printStackTrace();
			}
			if(params!=null&&"ftp".equals(params.optString("protocoltype"))){
				System.out.println("使用ftp上传");
				result = service.uploadxcSFTP((String) session.getAttribute("tenentid"),uploadPath,obj).toString();
			}
			else{
				System.out.println("使用sftp上传");
				result = service.uploadSFTP((String) session.getAttribute("tenentid"),uploadPath,obj).toString();
			}
			
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	//岭南店get请求调用接口
	@RequestMapping(value = "/uploadln")
	public void uploadln(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			result = service.uploadln((String) session.getAttribute("tenentid"),obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
}
