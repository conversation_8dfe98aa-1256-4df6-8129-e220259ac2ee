package com.tzx.boh.bo;

import net.sf.json.JSONObject;

public interface SaveBankManagementService
{
	   String NAME = "com.tzx.boh.bo.imp.SaveBankManagementServiceImpl";
		
		public JSONObject loadSaveBank(String tenancyID,JSONObject condition) throws Exception;

		public boolean checkUnique(String tenentId, JSONObject param)throws Exception;
		
		public JSONObject loadMaxDayCountDate(String tenancyID,JSONObject condition) throws Exception;
}
