package com.tzx.report.bo.imp.crm;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.crm.CouponsSaleSummaryQueryService;

@Service(CouponsSaleSummaryQueryService.NAME)
public class CouponsSaleSummaryQueryServiceImp implements CouponsSaleSummaryQueryService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Override
	public JSONObject find(String tenancyId, JSONObject jb) throws SystemException, Exception
	{
		JSONObject result = new JSONObject();
		List<JSONObject> list = new ArrayList<JSONObject>();
		StringBuilder sb = new StringBuilder();
		String date1 = jb.optString("date1");
		String date2 = jb.optString("date2");
		int pagenum = jb.containsKey("page") ? (jb.getInt("page") == 0 ? 1 : jb.getInt("page")) : 1;
		long total = 0L;
		String ids1 = jb.optString("store_ids1");
//		String organ_code = jb.optString("organ_code");
//		if ("".equals(ids1) && !"".equals(organ_code) && !"0".equals(organ_code))
//		{
//			ids1 = "select * from get_oids_bycode('" + organ_code + "')";
//		}
		String ids2 = jb.optString("store_ids2");

//		if ("".equals(ids2) && !"".equals(organ_code) && !"0".equals(organ_code))
//		{
//			ids2 = "select * from get_oids_bycode('" + organ_code + "')";
//		}
		String activity_type = jb.optString("activity_type");
		String coupons_class_id = jb.optString("coupons_class_id");
		
		Integer type = jb.optInt("type");
		if(type==0)
		{
			type =1;
		}

		if("".equals(date1) || "".equals(date2))
		{
			JSONObject date = DateUtil.getMonthDate();
			date1 = date.optString("from");
			date2 = date.optString("to");
		}
		Integer xx = jb.optInt("xx");
		if(type>0 && date1.length()>0 && date2.length()>0)
		{
			switch (xx)
			{
				case 1:
					Integer send_store = jb.optInt("send_store");
					Integer tid = jb.optInt("id");
					sb.setLength(0);
					sb.append("select count(*) as sl,cc.use_date,(CASE cc.use_store WHEN 0 THEN '总部' ELSE (select o.org_full_name from organ o where o.id=cc.use_store) END) as org_full_name FROM crm_coupons cc where cc.state='2' "+(ids2.length()>0?" and cc.use_store in("+ids2+")":"")+" and cc.send_store="+send_store+" and (cc."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') and cc.type_id="+tid+" GROUP BY cc.use_store,cc.use_date ORDER BY count(*) desc");
					list = this.dao.query4Json(tenancyId,sb.toString());
					break;

				default:
					sb.setLength(0);
					sb.append("select '"+ids2+"' as store_ids2,'"+date1+"' as date1,'"+date2+"' as date2,1 as xx,"+type+" as type,cc.send_store,count(cc.id) as zs,sum(case cc.state when '2' then 1 else 0 end) as ysy,sum(case cc.state when '0' then 1 else 0 end) as gq,cc.type_id as id,max(ca.activity_type) as activity_type,(CASE max(activity_type) WHEN 'scqyx' THEN '商城券营销' ELSE (select class_item from sys_dictionary where class_identifier_code='activity_type' and class_item_code=max(ca.activity_type)) END) as activity_type_name,max(ccc.coupons_pro) as coupons_pro,max(ccc.class_name) as class_name,max(cct.face_value) as face_value,(select max(class_item) FROM sys_dictionary where class_item_code=cc.send_chanel and class_identifier_code='chanel') as chanel_name,(CASE cc.send_store WHEN 0 THEN '总部' ELSE max(o.org_full_name) END) as org_full_name,max(ca.subject) as subject");
					sb.append(" ,max(cct.sale_money) as sale_money,MAX(sd.class_item) as coupons_pro_name");
					
					sb.append(" FROM crm_coupons cc");
					sb.append(" LEFT JOIN crm_coupons_type cct ON cc.type_id=cct.id");
					sb.append(" LEFT JOIN crm_activity ca ON cct.activity_id=ca.id");
					sb.append(" LEFT JOIN crm_coupons_class ccc ON cct.class_id=ccc.id");
					sb.append(" LEFT JOIN organ o ON cc.send_store=o.id");
					sb.append(" left join sys_dictionary sd on sd.class_identifier_code = 'coupons_goods_type' and ccc.coupons_pro = sd.class_item_code");
					
					sb.append(" where (cc."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
					if(ids1.length()>0)
					{
						sb.append(" and cc.send_store in("+ids1+") ");
					}
					if(ids2.length()>0)
					{
						sb.append(" and cc.use_store in("+ids2+") ");
					}
					if(activity_type.length()>0)
					{
						sb.append(" and ca.activity_type in("+activity_type+") ");
					}
					if(coupons_class_id.length()>0)
					{
						sb.append(" and ccc.id in("+coupons_class_id+") ");
					}
					
					sb.append("GROUP BY cc.send_store,cc.send_chanel,cc.type_id HAVING max(ca.id)>0");
					
					
					total = this.dao.countSql(tenancyId, sb.toString());				
					list= this.dao.query4Json(tenancyId,this.dao.buildPageSql(jb,sb.toString()));				
					result.put("page", pagenum);
					result.put("total", total);
					break;
			}
		}
		

		result.put("rows", list);
		return result;
	}

	@Override
	public List<JSONObject> getActTree(String tenancyId, JSONObject jb) throws SystemException, Exception
	{
		return this.dao.query4Json(tenancyId,"select class_item as text,concat ('''',class_item_code,'''') as id  from sys_dictionary where class_identifier_code='activity_type' and valid_state='1'");
	}

	@Override
	public List<JSONObject> getCpTree(String tenancyId, JSONObject jb) throws SystemException, Exception
	{
		List<JSONObject> list = this.dao.query4Json(tenancyId,"select concat(class_name,' (',class_code,')') as text,id,coupons_pro from crm_coupons_class where valid_state='1'");
		List<JSONObject> result = new ArrayList<JSONObject>();
		JSONObject cp = new JSONObject();
		cp.put("id",0);
		cp.put("text","-菜品券-");
		List<JSONObject> cpchildren = new ArrayList<JSONObject>();
		JSONObject dj = new JSONObject();
		dj.put("id",0);
		dj.put("text","-代金券-");
		List<JSONObject> djchildren = new ArrayList<JSONObject>();
		for(JSONObject jo : list)
		{
			String coupons_pro = jo.optString("coupons_pro");
			if("coupons_deduct".equals(coupons_pro))
			{
				djchildren.add(jo);
			}
			else if("coupons_dish".equals(coupons_pro))
			{
				cpchildren.add(jo);
			}
		}
		
		cp.put("children", cpchildren);
		dj.put("children", djchildren);
		
		result.add(cp);
		result.add(dj);
		return result;
	}

	@Override
	public JSONObject newFind(String tenancyId, JSONObject jb) throws SystemException, Exception
	{
		JSONObject result = new JSONObject();
		List<JSONObject> list = new ArrayList<JSONObject>();
		StringBuilder sb = new StringBuilder();
		String date1 = jb.optString("date1");
		String date2 = jb.optString("date2");
		int pagenum = jb.containsKey("page") ? (jb.getInt("page") == 0 ? 1 : jb.getInt("page")) : 1;
		long total = 0L;
		String ids1 = jb.optString("store_ids1");
		String ids2 = jb.optString("store_ids2");
		String activity_type = jb.optString("activity_type");
		String coupons_class_id = jb.optString("coupons_class_id");

		Integer type = jb.optInt("type");
		if(type==0)
		{
			type =1;
		}

		if("".equals(date1) || "".equals(date2))
		{
			JSONObject date = DateUtil.getMonthDate();
			date1 = date.optString("from");
			date2 = date.optString("to");
		}
		Integer xx = jb.optInt("xx");
		if(type>0 && date1.length()>0 && date2.length()>0)
		{
			switch (xx)
			{
				case 1:
					Integer send_store = jb.optInt("send_store");
					Integer tid = jb.optInt("id");
					sb.setLength(0);
					sb.append(" select ccs.type_id, ccs.use_date, ccs.use_store, ");
					sb.append(" max(case when ccs.use_store = 0 then '总部' else o.org_full_name end) as org_full_name, ");
					sb.append(" (select count(1) from crm_coupons s where s.state = '2' and s.type_id = ccs.type_id and s.use_store = ccs.use_store and s.use_date = ccs.use_date ");
					sb.append(" and (s."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
					if(ids1.length()>0){
						sb.append(" and s.send_store in("+ids1+") ");
					}
					if(ids2.length()>0)
					{
						sb.append(" and s.use_store in("+ids2+") ");
					}
					sb.append(" )as sl "); // 已使用数量
					sb.append(" from crm_coupons ccs left join organ o on ccs.use_store = o.id ");
					sb.append(" where (ccs."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
					sb.append(" and ccs.type_id = ").append(tid);
					sb.append(" and ccs.state = '2' ");
					if(ids1.length()>0){
						sb.append(" and ccs.send_store in("+ids1+") ");
					}
					if(ids2.length()>0)
					{
						sb.append(" and ccs.use_store in("+ids2+") ");
					}
					sb.append(" group by ccs.type_id, ccs.use_store, ccs.use_date order by ccs.use_store desc, sl desc ");

					list = this.dao.query4Json(tenancyId,sb.toString());
					break;

				default:
					sb.setLength(0);
					sb.append(" with t as (");
					sb.append(" select ccs.send_store,ccs.type_id, max(ccs.use_date) as use_date, max(ccs.send_time) as send_time, ");
					sb.append(" (select count(1) from crm_coupons s where s.type_id = ccs.type_id ");
					if (type == 1){
						sb.append(" and (s.send_time between '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
						if(ids1.length()>0){
							sb.append(" and s.send_store in("+ids1+") ");
						}
//						if(ids2.length()>0)
//						{
//							sb.append(" and s.use_store in("+ids2+") ");
//						}
					}

					sb.append(" ) as zs, "); // 发售数量
					sb.append(" (select count(1) from crm_coupons s where s.state = '2' and s.type_id = ccs.type_id and (s."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
					if(ids1.length()>0){
						sb.append(" and s.send_store in("+ids1+") ");
					}
					if(ids2.length()>0)
					{
						sb.append(" and s.use_store in("+ids2+") ");
					}
					sb.append(" )as ysy, "); // 已使用数量
					sb.append(" (select count(1) from crm_coupons s where s.state = '0' and s.type_id = ccs.type_id) as gq "); // 过期数量
					sb.append(" from crm_coupons ccs where (ccs."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
					if(ids1.length()>0)
					{
						sb.append(" and ccs.send_store in("+ids1+") ");
					}
//					if(ids2.length()>0)
//					{
//						sb.append(" and ccs.use_store in("+ids2+") ");
//					}
					sb.append(" group by ccs.type_id,ccs.send_store ");
					sb.append(" )");
					sb.append(" select DISTINCT * from (");//去重
					sb.append(" select '"+ids2+"'::text as store_ids2, '"+date1+"'::text as date1, '"+date2+"'::text as date2, "+type+" as type, ct.id, cc.id as class_id, 1 as xx, sd.class_item as coupons_pro_name, cc.class_name, ct.face_value, ct.sale_money, ");
					sb.append(" (select array_to_string(array(select d.class_item from crm_coupons_chanel c left join sys_dictionary d on c.chanel = d.class_item_code and d.class_identifier_code = 'chanel' where c.type_id = ct.id), ',')) as chanel_name, ");
					sb.append(" (select array_to_string(array( ");
					sb.append(" select distinct o.org_full_name from t c left join organ o on c.send_store = o.id where c.type_id = ct.id and c.send_store is not null ");
					sb.append(" union all select distinct '总部' from crm_coupons c where c.type_id = ct.id and c.send_store = 0 ");
					sb.append(" ), ',')) as org_full_name, ");
					sb.append(" t.zs, t.ysy, t.gq, sd1.class_item as activity_type_name, ca.subject ");
					sb.append(" from t ");
					sb.append(" left join crm_coupons_type ct on ct.id = t.type_id ");
					sb.append(" left join crm_coupons_class cc on ct.class_id = cc.id ");
					sb.append(" left join crm_activity ca on ca.id = ct.activity_id ");
					sb.append(" left join sys_dictionary sd on sd.class_identifier_code = 'coupons_goods_type' and sd.class_item_code = cc.coupons_pro ");
					sb.append(" left join sys_dictionary sd1 on sd1.class_identifier_code='activity_type' and sd1.class_item_code = ca.activity_type ");
					sb.append(" where (ct.activity_id is not null or ct.type_id is not null) ");
					if(activity_type.length()>0)
					{
						sb.append(" and ca.activity_type in("+activity_type+") ");
					}
					if(coupons_class_id.length()>0)
					{
						sb.append(" and cc.id in("+coupons_class_id+") ");
					}
					sb.append(" order by t."+(type==1?"send_time":"use_date")).append(" desc, ");
					sb.append(" t.ysy desc ");
					sb.append(" ) as yhj");//去重
					total = this.dao.countSql(tenancyId, sb.toString());
					list= this.dao.query4Json(tenancyId,this.dao.buildPageSql(jb,sb.toString()));
					result.put("page", pagenum);
					result.put("total", total);
					break;
			}
		}


		result.put("rows", list);
		return result;

//		JSONObject result = new JSONObject();
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		StringBuilder sb = new StringBuilder();
//		String date1 = jb.optString("date1");
//		String date2 = jb.optString("date2");
//		int pagenum = jb.containsKey("page") ? (jb.getInt("page") == 0 ? 1 : jb.getInt("page")) : 1;
//		long total = 0L;
//		String ids1 = jb.optString("store_ids1");
//		String ids2 = jb.optString("store_ids2");
//		String activity_type = jb.optString("activity_type");
//		String coupons_class_id = jb.optString("coupons_class_id");
//
//		Integer type = jb.optInt("type");
//		if(type==0)
//		{
//			type =1;
//		}
//
//		if("".equals(date1) || "".equals(date2))
//		{
//			JSONObject date = DateUtil.getMonthDate();
//			date1 = date.optString("from");
//			date2 = date.optString("to");
//		}
//		Integer xx = jb.optInt("xx");
//		if(type>0 && date1.length()>0 && date2.length()>0)
//		{
//			switch (xx)
//			{
//				case 1:
//					Integer send_store = jb.optInt("send_store");
//					Integer tid = jb.optInt("id");
//					sb.setLength(0);
//					sb.append(" select ccs.type_id, ccs.use_date, ccs.use_store, ");
//					sb.append(" max(case when ccs.use_store = 0 then '总部' else o.org_full_name end) as org_full_name, ");
//					sb.append(" (select count(1) from crm_coupons s where s.state = '2' and s.type_id = ccs.type_id and s.use_store = ccs.use_store and s.use_date = ccs.use_date ");
//					sb.append(" and (s."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
//					if(ids2.length()>0)
//					{
//						sb.append(" and s.use_store in("+ids2+") ");
//					}
//					sb.append(" )as sl "); // 已使用数量
//					sb.append(" from crm_coupons ccs left join organ o on ccs.use_store = o.id ");
//					sb.append(" where (ccs."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
//					sb.append(" and ccs.type_id = ").append(tid);
//					sb.append(" and ccs.state = '2' ");
//					if(ids2.length()>0)
//					{
//						sb.append(" and ccs.use_store in("+ids2+") ");
//					}
//					sb.append(" group by ccs.type_id, ccs.use_store, ccs.use_date order by ccs.use_store desc, sl desc ");
//
//					list = this.dao.query4Json(tenancyId,sb.toString());
//					break;
//
//				default:
//					sb.setLength(0);
//					sb.append(" select '"+ids2+"' as store_ids2, '"+date1+"' as date1, '"+date2+"' as date2, "+type+" as type, ct.id, cc.id as class_id, 1 as xx, sd.class_item as coupons_pro_name, cc.class_name, ct.face_value, ct.sale_money, ");
//					sb.append(" (select array_to_string(array(select d.class_item from crm_coupons_chanel c left join sys_dictionary d on c.chanel = d.class_item_code and d.class_identifier_code = 'chanel' where c.type_id = ct.id), ',')) as chanel_name, ");
//					sb.append(" (select array_to_string(array( ");
//					sb.append(" select distinct o.org_full_name from crm_coupons c left join organ o on c.send_store = o.id where c.type_id = ct.id and c.send_store is not null ");
//					sb.append(" union all select distinct '总部' from crm_coupons c where c.type_id = ct.id and c.send_store = 0 ");
//					sb.append(" ), ',')) as org_full_name, ");
//					sb.append(" t.zs, t.ysy, t.gq, sd1.class_item as activity_type_name, ca.subject ");
//					sb.append(" from ( ");
//					sb.append(" select ccs.type_id, max(ccs.use_date) as use_date, max(ccs.send_time) as send_time, ");
//					sb.append(" (select count(1) from crm_coupons s where s.type_id = ccs.type_id ");
//					if (type == 1){
//						sb.append(" and (s.send_time between '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
//					}
//					sb.append(" ) as zs, "); // 发售数量
//					sb.append(" (select count(1) from crm_coupons s where s.state = '2' and s.type_id = ccs.type_id and (s."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
//					if(ids2.length()>0)
//					{
//						sb.append(" and s.use_store in("+ids2+") ");
//					}
//					sb.append(" )as ysy, "); // 已使用数量
//					sb.append(" (select count(1) from crm_coupons s where s.state = '0' and s.type_id = ccs.type_id) as gq "); // 过期数量
//					sb.append(" from crm_coupons ccs where (ccs."+(type==1?"send_time":"use_date")+" BETWEEN '"+date1+" 00:00:00' and '"+date2+" 23:59:59') ");
//					if(ids1.length()>0)
//					{
//						sb.append(" and ccs.send_store in("+ids1+") ");
//					}
//					if(ids2.length()>0)
//					{
//						sb.append(" and ccs.use_store in("+ids2+") ");
//					}
//					sb.append(" group by ccs.type_id ");
//					sb.append(" ) t ");
//					sb.append(" left join crm_coupons_type ct on ct.id = t.type_id ");
//					sb.append(" left join crm_coupons_class cc on ct.class_id = cc.id ");
//					sb.append(" left join crm_activity ca on ca.id = ct.activity_id ");
//					sb.append(" left join sys_dictionary sd on sd.class_identifier_code = 'coupons_goods_type' and sd.class_item_code = cc.coupons_pro ");
//					sb.append(" left join sys_dictionary sd1 on sd1.class_identifier_code='activity_type' and sd1.class_item_code = ca.activity_type ");
//					sb.append(" where (ct.activity_id is not null or ct.type_id is not null) ");
//					if(activity_type.length()>0)
//					{
//						sb.append(" and ca.activity_type in("+activity_type+") ");
//					}
//					if(coupons_class_id.length()>0)
//					{
//						sb.append(" and cc.id in("+coupons_class_id+") ");
//					}
//					sb.append(" order by t."+(type==1?"send_time":"use_date")).append(" desc, ");
//					sb.append(" t.ysy desc ");
//
//					total = this.dao.countSql(tenancyId, sb.toString());
//					list= this.dao.query4Json(tenancyId,this.dao.buildPageSql(jb,sb.toString()));
//					result.put("page", pagenum);
//					result.put("total", total);
//					break;
//			}
//		}
//
//
//		result.put("rows", list);
//		return result;
	}
	
	
}
