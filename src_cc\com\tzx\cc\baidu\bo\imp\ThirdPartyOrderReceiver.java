package com.tzx.cc.baidu.bo.imp;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.alibaba.druid.util.StringUtils;
import com.tzx.cc.baidu.bo.ThirdPartyOrderService;
import com.tzx.cc.baidu.entity.OrderCheckMode;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.bo.OrderManagementHqService;
import com.tzx.cc.bo.OrderUpdateManagementService;
import com.tzx.cc.bo.PlaceOrderManagementService;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.OrderOper;
import com.tzx.cc.common.constant.util.CcPartitionUtils;
import com.tzx.cc.common.redis.service.CcRedisService;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.report.bo.OrderDiscountCommissionReportService;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.framework.bo.CodeService;
import com.tzx.framework.bo.CommissionInfoSettingService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.task.po.redis.dao.TaskRedisDao;

/**
 * <AUTHOR>
 */
public abstract class ThirdPartyOrderReceiver implements ThirdPartyOrderService {

	private static final Logger logger = Logger
			.getLogger(ThirdPartyOrderReceiver.class);

	/**
	 * 第三方订单数据
	 */
	protected JSONObject thirdPartyOrder;

	protected OrderCheckMode checkMode;

	/**
	 * 下发订单时需要获取第三方订单编号
	 */
	private JSONObject logOrder;

	/**
	 * 保存订单sql
	 */
	protected StringBuilder orderBatchSql = new StringBuilder();
	protected String channel;
	protected String tenantId;
	protected String storeId;
	protected String orderCode;
	protected double discountR_amount;// 骑手结算门店设置折扣的优惠金额
	protected double product_org_total_fee = 0.0;// 菜品原价合计
	// 处理客户0支付订单问题
	protected double order_total_price;// 订单总额
	protected double total_discount_fee;// 优惠总额
	protected double user_actual_pay;// 用户实付
	
	/**
	 * 门店实际应收金额(=用户实付-第三方承担的配送费) 注意：此字段很关键，涉及到付款结账，修改时请确认正确性!!!
	 */
	protected double shopFee;
	/** 天子星产生的外卖流水号 */
	protected String flow_code;// at 2017-07-19
	/**
	 * 异常信息
	 */
	protected JSONObject err = new JSONObject();

	protected GenericDaoImpl dao = (GenericDaoImpl) SpringConext
			.getBean("genericDaoImpl");

	protected CodeService codeService = (CodeService) SpringConext
			.getBean("codeService");

	protected static ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();

	protected OrderUpdateManagementService orderManagerService = (OrderUpdateManagementService) SpringConext
			.getBean(OrderUpdateManagementService.NAME);

	protected PlaceOrderManagementService placeOrderManagementService = (PlaceOrderManagementService) SpringConext
			.getBean(PlaceOrderManagementService.NAME);

	protected TaskRedisDao taskRedisDao = (TaskRedisDao) SpringConext
			.getBean(TaskRedisDao.NAME);
	protected CcRedisService redis = (CcRedisService) SpringConext
			.getBean("ccRedisServiceImpl");

	protected OrderManagementHqService orderManagementService = (OrderManagementHqService) SpringConext
			.getBean(OrderManagementHqService.NAME);

	protected OrderDiscountCommissionReportService orderDiscountCommissionReportService = (OrderDiscountCommissionReportService) SpringConext
			.getBean(OrderDiscountCommissionReportService.NAME);

	protected CommissionInfoSettingService commissionInfoSettingService = (CommissionInfoSettingService) SpringConext
			.getBean(CommissionInfoSettingService.NAME);

	/*
	 * static { //线程池所使用的缓冲队列 taskExecutor.setQueueCapacity(200); //线程池维护线程的最少数量
	 * taskExecutor.setCorePoolSize(5); //线程池维护线程的最大数量
	 * taskExecutor.setMaxPoolSize(1000); //线程池维护线程所允许的空闲时间
	 * taskExecutor.setKeepAliveSeconds(30000); taskExecutor.initialize(); }
	 */

	public ThirdPartyOrderReceiver(String channel) {
		this.channel = channel;
	}

	public ThirdPartyOrderReceiver(JSONObject order, String channel) {
		this.thirdPartyOrder = order;
		this.channel = channel;
	}

	/**
	 * 结算方式
	 * 
	 * @throws Exception
	 */
	protected void setCheckMode() throws Exception {

		String sql = "select settlement_type,check_mode,discount_rate,rounding_mode,payment_id from cc_third_check_mode where tenancy_id=? and store_id=? and channel=?";
		logger.info("商户号：" + tenantId + ",读取外卖结算方式......");
		List<OrderCheckMode> ret = dao.jdbcTemplate.query(sql, new Object[] {
				tenantId, Integer.valueOf(storeId), channel },
				BeanPropertyRowMapper.newInstance(OrderCheckMode.class));
		if (ret != null && !ret.isEmpty()) {
			checkMode = ret.get(0);
		} else {
			checkMode = new OrderCheckMode() {
				@Override
				public String getSettlement_type() {
					return "PLATFORM";
				}
			};
		}

	}

	protected void riderRecalc(JSONObject order) throws Exception {
		/*
		 * UPDATE cc_order_list SET discountk_amount = round(20 *(100 - 85) /
		 * 100, 2), discount_amount = 5 + round(20 *(100 - 85) / 100, 2),
		 * actual_pay = 20 - round(20 *(100 - 85) / 100, 2), shop_real_amount =
		 * 20 - round(20 *(100 - 85) / 100, 2), settlement_type = 'RIDER',
		 * check_mode = 'MANUAL' WHERE order_code = '';
		 */
		double discountAmount = order.optDouble("discount_amount");
		StringBuilder updateSql = new StringBuilder("UPDATE cc_order_list\n"
				+ "SET discountk_amount = round("
				+ shopFee
				+ " *(100 - "
				+ checkMode.getDiscount_rate()
				+ ") / 100, 2),\n"
				+ " discount_rate = "
				+ checkMode.getDiscount_rate()
				+ ",\n"
				+ " discount_amount = "
				+ discountAmount
				+ " + round("
				+ shopFee
				+ " *(100 - "
				+ checkMode.getDiscount_rate()
				+ ") / 100, 2),\n"
				+ " shop_fee = "
				+ shopFee
				+ " - round("
				+ shopFee
				+ " *(100 - "
				+ checkMode.getDiscount_rate()
				+ ") / 100, 2),\n"
				+ " shop_real_amount = "
				+ shopFee
				+ " - round("
				+ shopFee
				+ " *(100 - "
				+ checkMode.getDiscount_rate()
				+ ") / 100, 2),\n"
				+ " settlement_type = '"
				+ checkMode.getSettlement_type()
				+ "',\n"
				+ " check_mode = '"
				+ checkMode.getCheck_mode()
				+ "'\n" + "WHERE\n" + "	order_code = '" + orderCode + "';\n");
		updateSql.append("update cc_order_repayment set payment_id="
				+ checkMode.getPayment_id() + ",pay_money=" + shopFee
				+ " - round(" + shopFee + " *(100 - "
				+ checkMode.getDiscount_rate() + ") / 100, 2)"
				+ " where order_code='" + orderCode + "';");
		updateSql
				.append("update cc_order_item set share_amount='0.0',discount_rate='"
						+ checkMode.getDiscount_rate()
						+ "' where order_code='"
						+ orderCode + "';");

		String tmpSQL = CcPartitionUtils.makeSQL(tenantId,
				updateSql.toString(), orderCode,
				CcPartitionUtils.TYPE_ORDERCODE_TZX);

		dao.execute(tenantId, tmpSQL);
	}

	/**
	 * 生成统一订单编号
	 * 
	 * @throws Exception
	 */
	protected abstract void generateOrderCode();/*
												 * throws Exception{
												 * 
												 * JSONObject code_object = new
												 * JSONObject();
												 * code_object.put("store_id",
												 * storeId); long
												 * creditCurrentTime = (long)
												 * (System.currentTimeMillis());
												 * Date creditDate = new
												 * Date(creditCurrentTime);
												 * SimpleDateFormat creditDf =
												 * new
												 * SimpleDateFormat("yyyyMMdd");
												 * code_object.put("busi_date",
												 * creditDf.format(creditDate));
												 * code_object
												 * .put("dynamic",channel);
												 * this.orderCode =
												 * codeService.getCode(storeId,
												 * Code.CC_ORDER_CODE,
												 * code_object);
												 * 
												 * JSONObject
												 * flow_channel_code_object =
												 * new JSONObject();
												 * flow_channel_code_object
												 * .put("busi_date",
												 * creditDf.format(creditDate));
												 * flow_channel_code_object
												 * .put("dynamic",channel);
												 * this.flow_channel_code =
												 * codeService.getCode(storeId,
												 * Code
												 * .CC_ORDER_FLOW_CHANNEL_CODE,
												 * flow_channel_code_object);
												 * 
												 * JSONObject flow__code_object
												 * = new JSONObject();
												 * flow__code_object
												 * .put("store_id", storeId);
												 * flow__code_object
												 * .put("busi_date",
												 * creditDf.format(creditDate));
												 * flow__code_object
												 * .put("dynamic","");
												 * this.flow_code =
												 * codeService.getCode(storeId,
												 * Code.CC_ORDER_FLOW_CODE,
												 * flow__code_object); }
												 */

	/**
	 * 调用门店下发接口 param{tenentid,order_code}
	 * 
	 * @param
	 * @return
	 * @throws Exception
	 * @throws SystemException
	 */
	public void orderIssued() {
		Data data = new Data();
		data.setTenancy_id(tenantId);
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject param = new JSONObject();
		param.put("order_code", orderCode);
		param.put("tenentid", tenantId);
		list.add(param);
		data.setData(list);

		// 2017-11-03 外卖从redis中获取订单信息日志记录
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		UUID requestId = UUID.randomUUID();
		try {

			// 2017-11-03 添加日志记录信息start
			try {
				// ccBusniessLogBean.setRequestId(thirdPartyOrder.optString("requestId"));
				ccBusniessLogBean.setRequestId(requestId.toString());
				ccBusniessLogBean.setTzxId(orderCode);
				ccBusniessLogBean.setChannel(thirdPartyOrder
						.optString("tzxChannel"));
				ccBusniessLogBean.setChannelName(thirdPartyOrder
						.optString("tzxChannel"));
				ccBusniessLogBean.setThirdId(logOrder
						.getString("third_order_code"));
				ccBusniessLogBean.setTenancyId(tenantId);
				ccBusniessLogBean.setShopId(storeId);
				ccBusniessLogBean.setCategory("cc");
				ccBusniessLogBean.setType("OrderIssued");

				ccBusniessLogBean
						.setCmd("com.tzx.cc.baidu.bo.imp.ThirdPartyOrderReceiver:orderIssued");
				ccBusniessLogBean.setRequestBody(thirdPartyOrder.toString());

				ccBusniessLogBean.setCreateTime(new Date().getTime());
				ccBusniessLogBean.setIsNormal("1");
				ccBusniessLogBean.setIsThird("0");
				ccBusniessLogBean.setOperAction(OrderOper.OrderIssued
						.toString());
			} catch (Exception e) {
				ccBusniessLogBean.setErrorBody(LogUtils
						.getExceptionAllinformation(e));
				ccBusniessLogBean.setIsNormal("0");
			}
			// 2017-11-03 添加日志记录信息end

			orderManagerService.orderIssued(data);

			ccBusniessLogBean.setResponseBody("OK");
		} catch (SystemException e) {
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			logger.error(e);
			e.printStackTrace();
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			logger.error(e);
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}

	}

	/**
	 * 0支付特殊订单优惠增加记录
	 * 
	 */
	public void calcDiscountFee() {
		double userActurePay = Scm.psub(order_total_price, total_discount_fee);
		StringBuilder sql = new StringBuilder();
		String report_date = DateUtil
				.format(new Timestamp(System.currentTimeMillis())).toString()
				.substring(0, 10);
		// sql.append("INSERT INTO cc_order_discount (tenancy_id,store_id,report_date,order_code,activity_id,discount_desc,discount_fee,operator_time) VALUES ");
		double userPay = 0.0;
		userPay = userPay - user_actual_pay;
		if (userActurePay == 0.0) {
			if (user_actual_pay > 0) {
				// sql.append("('"
				// + tenantId
				// + "','"
				// + storeId
				// + "','"
				// + report_date
				// + "','"
				// + orderCode
				// + "','C1001','用户虚付',"
				// + userPay
				// + ",'"
				// + DateUtil.format(new Timestamp(System
				// .currentTimeMillis())) + "');");

				JSONObject discountParam = new JSONObject();

				discountParam.put("tenancy_id", tenantId);
				discountParam.put("store_id", storeId);
				discountParam.put("report_date", report_date);
				discountParam.put("order_code", orderCode);
				discountParam.put("activity_id", "C1001");
				discountParam.put("discount_desc", "用户虚付");
				discountParam.put("discount_fee", userPay);
				discountParam.put("operator_time", DateUtil
						.format(new Timestamp(System.currentTimeMillis())));

				CcPartitionUtils.lackInsertParam(tenantId, discountParam);

				sql.append(CommonUtil.insertJSONParamsToSql(
						"cc_order_discount", discountParam));
			}

		}
		if (userActurePay < 0.0) {
			// sql.append("('"
			// + tenantId
			// + "','"
			// + storeId
			// + "','"
			// + report_date
			// + "','"
			// + orderCode
			// + "','C1001','用户虚付',"
			// + userPay
			// + ",'"
			// + DateUtil.format(new Timestamp(System.currentTimeMillis()))
			// + "'),");
			// sql.append("('"
			// + tenantId
			// + "','"
			// + storeId
			// + "','"
			// + report_date
			// + "','"
			// + orderCode
			// + "','C1002','优惠虚付',"
			// + userActurePay
			// + ",'"
			// + DateUtil.format(new Timestamp(System.currentTimeMillis()))
			// + "');");

			JSONObject discountParam = new JSONObject();

			discountParam.put("tenancy_id", tenantId);
			discountParam.put("store_id", storeId);
			discountParam.put("report_date", report_date);
			discountParam.put("order_code", orderCode);
			discountParam.put("activity_id", "C1001");
			discountParam.put("discount_desc", "用户虚付");
			discountParam.put("discount_fee", userPay);
			discountParam.put("operator_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));

			CcPartitionUtils.lackInsertParam(tenantId, discountParam);

			sql.append(CommonUtil.insertJSONParamsToSql("cc_order_discount",
					discountParam));

			JSONObject discountParam2 = new JSONObject();

			discountParam2.put("tenancy_id", tenantId);
			discountParam2.put("store_id", storeId);
			discountParam2.put("report_date", report_date);
			discountParam2.put("order_code", orderCode);
			discountParam2.put("activity_id", "C1002");
			discountParam2.put("discount_desc", "优惠虚付");
			discountParam2.put("discount_fee", userActurePay);
			discountParam2.put("operator_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));

			CcPartitionUtils.lackInsertParam(tenantId, discountParam2);

			sql.append(CommonUtil.insertJSONParamsToSql("cc_order_discount",
					discountParam2));
		}
		try {
			this.dao.execute(tenantId, sql.toString());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage());
		}
	}

	/**
	 * 计算佣金信息
	 * 
	 * @throws Exception
	 */
	public void calcCommission() {
	}

	/**
	 * 第三方订单状态接收
	 * 
	 * @param params
	 * @return
	 */
	public abstract JSONObject orderStatusPush(JSONObject params);

	/**
	 * 第三方查询订单状态
	 * 
	 * @param params
	 * @return
	 */
	public abstract JSONObject orderStatusGet(JSONObject params);

	/**
	 * 保存会员信息
	 * 
	 * @see 会员信息生成后存入redis队列中。
	 * 
	 *      at 2017-08-15 增加会员信息存储内容
	 */
	private void saveCustomerInfo() {
		try {
			JSONObject customer = createCustomerInfo();

			if (customer != null) {
				CcRedisService rd = (CcRedisService) SpringConext
						.getBean("ccRedisServiceImpl");
				customer.put("organ_id", storeId);
				customer.put("tenancy_id", tenantId);
				customer.put(
						"add_time",
						DateUtil.format(
								new Timestamp(System.currentTimeMillis()))
								.toString());

				rd.rightPush("cc_customer_register", customer.toString());
				logger.info("订单:"+this.orderCode+"外卖向redis发送会员信息完成，信息：" + customer.toString());
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			logger.error("订单:"+this.orderCode+"存储会员信息时发生异常");
		}
	}

	/**
	 * 根据外卖订单信息生成会员信息
	 * 
	 * @return
	 * 
	 *         //at 2017-08-15
	 */
	protected JSONObject createCustomerInfo() {
		return null;
	}

	/**
	 * 生成第三方返回数据
	 */
	abstract JSONObject thirdPartyResponse();

	/**
	 * 接收第三方订单
	 * 
	 * @throws Exception
	 */
	public JSONObject receive() throws Exception {

		err.put("errno", "0");

		DBContextHolder.setTenancyid(tenantId);
		logger.info("外卖处理开始,商户：" + tenantId);
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		try {
			// 生成订单统一编号
			generateOrderCode();
			// 设置结算方式
			setCheckMode();
			// 查询外卖流水号 at 2017-07-19
			queryTakewayFlowCode();

			// 保存订单相关信息
			JSONObject order = saveOrderList();
			saveOrderItem();
			// 保存付款信息
			if (("1".equals(order.optString("is_online_payment")) && "PLATFORM"
					.equals(checkMode.getSettlement_type()))// 先付平台结算
					|| ("RIDER".equals(checkMode.getSettlement_type()) && "AUTOMATIC"
							.equals(checkMode.getCheck_mode()))// 骑手自动结算
			) {
				saveOrderRepayment();
			}
			// 保存优惠信息
			saveOrderDiscount();
			dao.execute(tenantId, orderBatchSql.toString());
			logger.error("插入数据库中的订单sql为:" + orderBatchSql.toString());

			// 计算佣金信息(平台结算)
			if ("PLATFORM".equals(checkMode.getSettlement_type())) {
				calcCommission();
			}
			// 骑手结算
			if ("RIDER".equals(checkMode.getSettlement_type())) {
				riderRecalc(order);
			}
			// 当订单师傅金额为0或负数时cc_order_discount增加记录
			if (order_total_price - total_discount_fee <= 0.0) {
				calcDiscountFee();
			}

			// 2017-11-14订单保存添加日志信息start
			ccBusniessLogBean = saveOrderLog(order);
			logOrder = JSONObject.fromObject(order);
			// 2017-11-14订单保存添加日志信息end

			// cc_order_item表中real_amount如果值为负数，则将值修改为0,负数添加到明细中最大的值上
			// at 20170823 改为优惠大于（菜品明细+餐盒费）时，要用优惠-配送费 进行分摊
			detailDatavalidate();

			// 下发数据
			orderIssued();

			// 存储会员信息 at 2017-08-15
			saveCustomerInfo();
			saveTenantId();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("收取订单失败[" + channel + "]:" + e);
			ccBusniessLogBean.setErrorBody(LogUtils
					.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			err.put("errno", "1");
			err.put("error", e.getMessage());
		} finally {
			if (CommonUtil.checkStringIsNotEmpty(ccBusniessLogBean
					.getRequestId())) {
				KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			}
		}
		JSONObject result = thirdPartyResponse();
		return result;
	}

	/**
	 * 订单日志存储
	 * 
	 * @param order
	 */
	public CcBusniessLogBean saveOrderLog(JSONObject order) {
		// 2017-11-03 添加日志记录信息start
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		try {
			ccBusniessLogBean.setRequestId(thirdPartyOrder
					.optString("requestId"));
			ccBusniessLogBean.setTzxId(orderCode);
			ccBusniessLogBean.setChannel(channel);
			ccBusniessLogBean.setChannelName(channel);
			ccBusniessLogBean.setThirdId(order.getString("third_order_code"));
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setShopId(storeId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("saveOrder");

			ccBusniessLogBean
					.setCmd("com.tzx.cc.baidu.bo.imp.ThirdPartyOrderReceiver:receive");
			ccBusniessLogBean.setRequestBody(thirdPartyOrder.toString());

			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			ccBusniessLogBean.setOperAction(OrderOper.saveOrder.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
		// 2017-11-03 添加日志记录信息end
		return ccBusniessLogBean;
	}

	/**
	 * 缓存 tenantId 到redis
	 * 
	 * @throws Exception
	 */
	private void saveTenantId() throws Exception {
		try {
			String key = Constant.KEY_COMMENT_TENANTS;
			List<String> list = redis.range(key, 0, -1);
			if (list != null && list.size() > 0) {
				if (!list.contains(tenantId)) {
					redis.leftPush(key, tenantId);
				}
			} else {
				redis.leftPush(key, tenantId);
			}
		} catch (Exception e) {
			logger.error("====获取 redis 缓存的tenantId失败" + e);
		}
	}

	public void detailDatavalidate() {
		// at 20170823
		String mSql = "select discount_amount,product_org_total_fee,package_box_fee,meal_costs,shop_real_amount from cc_order_list where order_code='"
				+ orderCode + "'";

		mSql = CcPartitionUtils.makeSQL(tenantId, mSql, orderCode,
				CcPartitionUtils.TYPE_ORDERCODE_TZX);

		String sql = "select id,product_fee from cc_order_item where order_code='"
				+ orderCode + "' ORDER BY product_fee";

		sql = CcPartitionUtils.makeSQL(tenantId, sql, orderCode,
				CcPartitionUtils.TYPE_ORDERCODE_TZX);

		
		try {

			List<JSONObject> mList = this.dao.query4Json(tenantId, mSql);
			
			List<JSONObject> itemList = this.dao.query4Json(tenantId,
					sql);
			
			int len = itemList.size();
			
			for (JSONObject mObj : mList) {
				JSONObject shareColumInfo_obj= this.extendItemShare(mObj, itemList);
				
				
				double discountAmount = mObj.optDouble("discount_amount", 0);
				double productAmount = mObj.optDouble("product_org_total_fee",
						0) + mObj.optDouble("package_box_fee", 0);
				double sendFee = mObj.optDouble("meal_costs", 0);
				// 如果优惠为0，则不进行优惠分摊处理
				if (discountAmount > 0) {

					double discountFee = discountAmount;
					// 如果优惠大于（菜品明细+餐盒费），优惠-配送费,然后用此金额进行分摊，避免出现负值的情况
					if (discountFee > productAmount) {
						discountFee = discountFee - sendFee;
					}

					// 优惠比例
					double discountRate = productAmount == 0 ? 0
							: (discountFee / productAmount);

					double item_discountAmount_sum = 0;
					for (int i = 0; i < len; i++) {
						JSONObject itemObj = itemList.get(i);
						double item_productFee = itemObj.optDouble(
								"product_fee", 0);
						String id = itemObj.optString("id");

						double item_discountAmount = 0;

						if (i == (len - 1)) {
							// 如果是最后一条记录，则优惠总额-Item表中分摊的优惠值，用于结果存入最后一条明细中，保证明细的和值与list表中优惠相等
							item_discountAmount = discountFee
									- item_discountAmount_sum;
						} else {
							item_discountAmount = CommonUtil
									.keepTwoDecimal(item_productFee
											* discountRate);
						}
						
						JSONObject itemShareObj=shareColumInfo_obj.getJSONObject(id);
						if(itemShareObj==null){
							itemShareObj=new JSONObject();
						}
						
						itemShareObj.put("discount_amount", item_discountAmount);
						itemShareObj.put("real_amount", CommonUtil
										.keepTwoDecimal(item_productFee
												- item_discountAmount));
						
						shareColumInfo_obj.put(id, itemShareObj);
						
//						sb.append("update cc_order_item set discount_amount=")
//								.append(item_discountAmount)
//								.append(",real_amount=")
//								.append(CommonUtil
//										.keepTwoDecimal(item_productFee
//												- item_discountAmount))
//								.append(" where id=").append(id)
//								.append(" and order_code='").append(orderCode)
//								.append("';");

						item_discountAmount_sum += item_discountAmount;
					}

					
				}
				//------------组装sql-------------------------------
				StringBuffer sb = new StringBuffer();
				for (int i = 0; i < len; i++) {
					JSONObject itemObj = itemList.get(i);
					String id=itemObj.optString("id");
					JSONObject itemShareObj=shareColumInfo_obj.getJSONObject(id);
					
					sb.append("update cc_order_item set ");
					
					if(discountAmount>0){
						sb.append("discount_amount=").append(itemShareObj.optDouble("discount_amount",0)).append(",");
						sb.append("real_amount=").append(itemShareObj.optDouble("real_amount",0)).append(",");
					}
					//sb.append("platform_share=").append(itemShareObj.optDouble("platform_share",0)).append(",");
					//sb.append("shop_share=").append(itemShareObj.optDouble("shop_share",0)).append(",");
					//sb.append("comission_share=").append(itemShareObj.optDouble("comission_share",0)).append(",");
					sb.append("net_income_amount=").append(itemShareObj.optDouble("net_income_amount",0));
					
					sb.append(" where id=").append(id)
						.append(" and order_code='").append(orderCode)
						.append("';");
				}
				
				String sbSql = CcPartitionUtils.makeSQL(tenantId,
						sb.toString(), orderCode,
						CcPartitionUtils.TYPE_ORDERCODE_TZX);

				logger.info("订单："+orderCode+"统一处理cc_order_item的明细中优惠：" + sbSql);

				dao.execute(tenantId, sbSql);
				
				
				break;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			logger.error("统一处理cc_order_item的明细中优惠信息时发生异常:" + ex.getMessage());
		}
	}

	// at 2017-07-19
	private void queryTakewayFlowCode() throws Exception {
		JSONObject code_object = new JSONObject();
		long creditCurrentTime = (long) (System.currentTimeMillis());
		Date creditDate = new Date(creditCurrentTime);
		SimpleDateFormat creditDf = new SimpleDateFormat("yyyyMMdd");
		code_object.put("store_id", storeId);
		code_object.put("busi_date", creditDf.format(creditDate));
		flow_code = codeService.getCode(tenantId, Code.CC_ORDER_FLOW_CODE,
				code_object);// 调用统一接口来实现
	}

	/**
	 * 扩展订单菜品明细分摊内容
	 * */
	protected JSONObject extendItemShare(JSONObject order, List<JSONObject> itemList) {
		// 明细净值=净值/菜品合计*菜品明细金额
		
		double shopRealAmount=order.optDouble("shop_real_amount", 0);
		double packageFee = order.optDouble("package_box_fee", 0);
		double productAmount = order.optDouble("product_org_total_fee", 0)
				+ packageFee;
		
		double netIncomePercent=shopRealAmount/(productAmount+packageFee);		
		
		double net_income_amount_sum=0,net_income_amount=0;
		
		int len=itemList.size();
		JSONObject result=new JSONObject();
		
		for(int i=0;i<len;i++){
			JSONObject rowResult=new JSONObject();
			
			JSONObject item=itemList.get(i);
			
			double productFee=item.optDouble("product_fee",0);
			
			
			
			if(i==(len-1)){
				net_income_amount= CommonUtil
						.keepTwoDecimal(shopRealAmount-net_income_amount_sum);
				
			}else{
				net_income_amount= CommonUtil
						.keepTwoDecimal(netIncomePercent*productFee);
				
				net_income_amount_sum+=net_income_amount;
			}

			
			rowResult.put("net_income_amount",net_income_amount);
			
			result.put(item.optString("id"), rowResult);
			
		}
		
		return result;
	}
	
	
	
}
