package com.tzx.cc.thirdparty.bo.imp;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import org.apache.commons.lang.StringUtils;

import com.tzx.cc.baidu.entity.CmdType;
import com.tzx.cc.baidu.entity.DeliveryRegion;
import com.tzx.cc.baidu.entity.Shop;
import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.baidu.util.SignHolder;
import com.tzx.cc.thirdparty.bo.ThirdPartyManager;
import com.tzx.cc.thirdparty.util.BaiduUtils;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class BaiduManager extends AbstractThirdPartyManager {
	

	public BaiduManager(String tenantId, String shopId) {
		super(tenantId, shopId, Constant.BAIDU_CHANNEL);
	}
	public BaiduManager(String tenantId) {
		super(tenantId, Constant.BAIDU_CHANNEL);
	}
	
	@Override
	public JSONObject getBindShopList (String source,String secret) throws Exception 
	{
			//2017-11-03 wangjihao 原来的代码运报错，jianghongyan让我改成以下 start
//			String cmd = CommonUtil.cmdFactory1(source, secret, CmdType.SHOP_LIST, "[]");//原
			String cmd = CommonUtil.cmdFactory1(source, secret, CmdType.SHOP_LIST, null);//现
			//2017-11-03 wangjihao 原来的代码运报错，jianghongyan让我改成以下 end
			
			String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);
			return JSONObject.fromObject(res).optJSONObject("body");
			
		
	}
	
	@Override
	public JSONObject getLocalShopInfo() throws Exception {
		JSONObject data = super.getLocalShopInfo();
		List<JSONObject> lineup=super.getBussinessTime();
		
		String lineupTimeId="";
		String bussinessTimeFormat="";
		List bs=new ArrayList();
		
		for(JSONObject json:lineup){
			lineupTimeId+=json.optString("id")+",";
			
			String start=json.optString("start").equals("24:00")?"00:00":json.optString("start");
			String end=json.optString("end").equals("24:00")?"00:00":json.optString("end");
			if(!CommonUtil.checkStringIsNotEmpty(start)||!CommonUtil.checkStringIsNotEmpty(end)) continue;
			
			Map m=new HashMap();
			m.put("start", start);
			m.put("end", end);
			bs.add(m);
			bussinessTimeFormat+=start+"-"+end+";";
		}
		data.put("business_time", bs);
		if(!bussinessTimeFormat.equals("")){
			bussinessTimeFormat=bussinessTimeFormat.substring(0, bussinessTimeFormat.length()-1);
		}
		if(!lineupTimeId.equals("")){
			lineupTimeId=lineupTimeId.substring(0, lineupTimeId.length()-1);
		}
		data.put("business_time_format", bussinessTimeFormat);
		data.put("lineup_time_org_id",lineupTimeId);
		data.put("tenant_id", tenantId);
		logger.info("[获取本地商户信息]"+data);
		return data;
	}

	@Override
	public void postShopInfo(JSONObject params)
			throws Exception {
		params.put("channel", Constant.BAIDU_CHANNEL);
		params.put("shop_state", Constant.BAIDU_SHOP_OPEN);
		Shop shop=generateBaiduShop(params);
		Map category=shop.getCategorys().get(0);
		params.put("category1", category.get("category1"));
		params.put("category2", category.get("category2"));
		params.put("category3", category.get("category3"));
		
		if(CommonUtil.checkStringIsNotEmpty(params.optString("id"))&&(params.optString("push_state").equals("1") || params.optString("push_state").equals("3") || (params.optString("push_state").equals("2")&&params.optString("online_flag").equals("1")))){
			updateShop(shop,params);
			//店铺不存在 20180305 zhangy
			if(response.optString("errno").equals("20253")){
				createShop(shop,params);
			}
		}else{
			createShop(shop,params);
		}
	}
	
	@Override
	public void postShopStatus(JSONObject params)
			throws Exception {
		
		String cmdType = params.optString("cmd_type");
		String source = params.optString("source");
		String secret = params.optString("secret");
		String shopId = params.optString("shop_id");
		Map map = new HashMap();
		map.put("shop_id", shopId+"@"+tenantId);
		CmdType type = null;
		switch (cmdType) {
		case "open":
			type = CmdType.SHOP_OPEN;
			break;
		case "close":
			type = CmdType.SHOP_CLOSE;
			break;
		default:
			break;
		}

		String cmd = CommonUtil.cmdFactory1(source, secret, type, map);

		String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);

		JSONObject resJson = JSONObject.fromObject(res);

		response=resJson.optJSONObject("body");


		logger.info("[更新百度商户状态]上传数据:"+cmd+"\n返回数据:"+resJson);
	}

	@Override
	public JSONObject postOrderStatus(JSONObject params) throws Exception {
		
		
		String orderId=params.optString("orderId");
		String operType=params.optString("operType");
		
		Sign sign=SignHolder.getShopSign(tenantId, shopId,channel);
		String source=sign.getSource();
		String secret=sign.getSecret();
		CmdType type=null;
		
		Map body=new TreeMap();
		
//		Map<String,String> map=new HashMap<>();
//		map.put("order_id", orderId);
		body.put("order_id", orderId);
		switch (operType) {
		case ThirdPartyManager.ORDER_CONFIRM:
			type=CmdType.ORDER_CONFIRM;
			break;
		case ThirdPartyManager.ORDER_CANCEL:	
			type=CmdType.ORDER_CANCEL;
			body.put("type", params.optInt("type"));
			body.put("reason", params.optString("reason"));
			break;
		case ThirdPartyManager.ORDER_COMPLETE:
			type=CmdType.ORDER_COMPLETE;
			break;	
		default:
			response.put("errno", -1);
			response.put("error", "不支持的订单操作:"+operType);
			return response;
		}
		
		String cmd = CommonUtil.cmdFactory1(source, secret, type, body ,"2.0");

		String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);

		JSONObject resJson = JSONObject.fromObject(res);

		response = resJson.optJSONObject("body");

		logger.info("[更新百度订单状态]上传数据:"+cmd+"\n返回数据:"+resJson);
		return response;
	}

	@Override
	public JSONObject getOrderStatus(JSONObject params) throws Exception {
		String baiduOrderId=params.optString("third_order_id");
		Sign sign=SignHolder.getShopSign(tenantId, shopId,channel);
		String source=sign.getSource();
		String secret=sign.getSecret();
		JSONObject json=new JSONObject();
		json.put("order_id", baiduOrderId);
		String cmd = CommonUtil.cmdFactory1(source, secret, CmdType.ORDER_STATUS_GET, json);
		String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);
		logger.info("[获取百度订单状态]上传数据:"+cmd+"\n返回数据:"+res);
		return JSONObject.fromObject(res);
	}
	
	private void createShop(Shop shop,JSONObject params){
		String cmd=CommonUtil.cmdFactory1(params.optString("source"), params.optString("secret"), CmdType.SHOP_CREATE, shop.JSONFormatBean());
		
		String res=CommonUtil.httpPost(Constant.BAIDU_API_URL,cmd);
		
		JSONObject resJson=JSONObject.fromObject(res);
		
		response=resJson.optJSONObject("body");

		logger.info("[修改百度商户]上传数据:"+cmd+"\n返回数据:"+resJson);
		
	}
	
	private void updateShop(Shop shop,JSONObject params){
		List bs=new ArrayList();
		String[] bussinessTimes=params.optString("business_time_format").split(";");
		for(String t:bussinessTimes){
			String[] lt=t.split("-");
			String start=lt[0];
			String end=lt[1];
			
			Map m=new HashMap();
			m.put("start", start);
			m.put("end", end);
			bs.add(m);
		}
		
		shop.setBusiness_time(bs);
		
		String cmd=CommonUtil.cmdFactory1(params.optString("source").trim(), params.optString("secret").trim(), CmdType.SHOP_UPDATE, shop.JSONFormatBean());
		
		String res=CommonUtil.httpPost(Constant.BAIDU_API_URL,cmd);
		
		JSONObject resJson=JSONObject.fromObject(res);
		
		response=resJson.optJSONObject("body");
		
		logger.info("[更新百度商户]上传数据:"+cmd+"\n返回数据:"+resJson);
	}
	
	
	private Shop generateBaiduShop(JSONObject data) {
		Shop shop=new Shop();
		
		shop.setShop_id(data.optString("shop_id")+"@"+data.optString("tenant_id"));//
		
		shop.setName(data.optString("name"));//
		
		shop.setShop_logo(data.optString("shop_logo"));//
		
		shop.setProvince(data.optString("province"));//
		
		shop.setCity(data.optString("city"));//
		
		shop.setCounty(data.optString("county"));//
		
		shop.setAddress(data.optString("address"));//
		
		shop.setBrand(data.optString("brand"));//
		
		List categorys=new ArrayList();
		Map<String,String> c=new HashMap<String,String>();
		c.put("category1", Constant.CATEGORY1);
		String category23=data.optString("category23");
		String[] category=category23.split(">");
		c.put("category2", category[0]);
		c.put("category3", category[1]);
		categorys.add(c);
		
		shop.setCategorys(categorys);
		
		shop.setPhone(data.optString("phone"));
		
		shop.setService_phone(data.optString("service_phone"));
		
		shop.setLatitude(data.optString("latitude"));
		
		shop.setLongitude(data.optString("longitude"));
		
		//商圈信息
		List<DeliveryRegion> deliveryRegions=new ArrayList<DeliveryRegion>();
		DeliveryRegion deliveryRegion=new DeliveryRegion();
		deliveryRegion.setName(data.optString("region_name"));
		List regions=new ArrayList();
		String coordinateStr= data.optString("coordinate");
		List region=new ArrayList();
		if(CommonUtil.checkStringIsNotEmpty(coordinateStr)){
			String[] coordinateStrArray=coordinateStr.split(";");
			for(int i=0;i<coordinateStrArray.length;i++){
				String[] location=coordinateStrArray[i].split(",");
				String lon=location[0];
				String lat=location[1];
				Map loc=new TreeMap();
				loc.put("latitude", lat);
				loc.put("longitude", lon);
				region.add(loc);
			}
			regions.add(region);
		}
		deliveryRegion.setRegion(regions);
		deliveryRegion.setDelivery_fee(String.valueOf(CommonUtil.yuan2Fen(data.optDouble("delivery_fee"))));
		deliveryRegion.setDelivery_time(data.optString("delivery_time"));
		deliveryRegions.add(deliveryRegion);
		shop.setDelivery_region(deliveryRegions);		
		
		shop.setBusiness_time(data.optJSONArray("business_time"));
		
		shop.setInvoice_support(data.optString("invoice_support").equals("1")?"1":"2");
		
		shop.setMin_order_price(String.valueOf(CommonUtil.yuan2Fen(data.optDouble("min_order_price"))));
		
		shop.setPackage_box_price(String.valueOf(CommonUtil.yuan2Fen(data.optDouble("package_box_price",0.0))));
		
		return shop;
	}

	@Override
	public JSONObject getShopCategory(JSONObject params) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected void postDeliveryTime(JSONObject params) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void postDeliveryRegion(JSONObject params) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void postDishCategory(JSONObject params) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void postDish(List<JSONObject> params) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void delDish(JSONObject params) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void postDishSingle(JSONObject p) throws Exception
	{
		// TODO Auto-generated method stub
		
	}
	@Override
	protected void delDishCategory(JSONObject params) throws Exception
	{
		// TODO Auto-generated method stub
		
	}
	@Override
	public JSONObject postOrderModelStatus(JSONObject param, String string)
			throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public JSONObject setCategoryPositions(JSONObject params)
			throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
	
	/**
	 * 获取单一店铺信息
	 * @param source
	 * @param secret
	 * @param shop_id
	 * @return
	 * @throws Exception
	 */
	public JSONObject getShop (String source,String secret,String shop_id) throws Exception {
			Map map=new HashMap();
			map.put("shop_id", shop_id);
			String cmd = CommonUtil.cmdFactory1(source, secret, CmdType.SHOP_GET ,map);//现
			String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);
			return JSONObject.fromObject(res).optJSONObject("body");
	}
	/**
	 * 功能：查询百度订单信息
	 * @see
	 * 注意：按序号查询只能查询当前用的数据
	 * 
	 * @param tenantId 商户号
	 * @param tzxShopId 门店号
	 * @param date 日期：yyyy-mm-dd
	 * @param type 1 序号 2订单号
	 * @param lists
	 * @return
	 * @throws Exception
	 */
	public List<String> queryThirdOrderList(String tenantId,String tzxShopId,String date,int type,List<String> lists)throws Exception{
		List<String> messageList=new ArrayList<String>();
		Map map=new HashMap();

		//Sign sign = SignHolder.getShopSign(tenantId, tzxShopId, channel);
		BaiduUtils butils= BaiduUtils.getInstance();
		String status="";
		for(String orderIdOrSeq:lists){
			if(type==1){
				
				int rowCount=100;
				int seq=Integer.valueOf(orderIdOrSeq);
				int mod=seq%rowCount;				
				int page=seq/rowCount;
				
				if(mod>0||page==0){
					page+=1;
				}
				
				String[] dateArr=date.split("-");
				
				Calendar cal=Calendar.getInstance();
				
				cal.set(Integer.valueOf(dateArr[0]), Integer.valueOf(dateArr[1])-1, Integer.valueOf(dateArr[2]),0,0,0);
				
				map.put("start_time", cal.getTimeInMillis()/1000);
				
				cal.set(Integer.valueOf(dateArr[0]), Integer.valueOf(dateArr[1])-1, Integer.valueOf(dateArr[2]),23,59,59);
				map.put("end_time", cal.getTimeInMillis()/1000);
				map.put("page", page);
				map.put("shop_id", tzxShopId+"@"+tenantId);
				
				
				String result=butils.execCmd(tenantId, tzxShopId, BaiduUtils.CMD_ORDER_ORDERLIST, map,  BaiduUtils.VERSION_3);
				
				logger.info("按序号获取百度获取订单列表结果："+result);
				
				
				//百度订单列表是顺序
				JSONObject resultJson = JSONObject.fromObject(result);
				JSONArray list=resultJson.getJSONObject("body").getJSONObject("data").getJSONArray("list");
				
				orderIdOrSeq="";
				
				for(int i=0;i<list.size();i++){					
					if(seq-(page-1)*rowCount-1==i){
						JSONObject row=list.getJSONObject(i);
						orderIdOrSeq=row.optString("order_id");
						status=row.optString("order_status");
						break;
					}
				}
				if(StringUtils.isEmpty(orderIdOrSeq)){
					logger.info(date+",商户："+tenantId+",门店："+tzxShopId+",百度没有根据序号查到结果");
				}
			}
			if(!StringUtils.isEmpty(orderIdOrSeq)){
				map.clear();
				
				map.put("order_id", orderIdOrSeq);	
				
				if(StringUtils.isEmpty(status)){
					String statusResult=butils.execCmd(tenantId, tzxShopId, BaiduUtils.CMD_ORDER_ORDERSTATUS, map,  BaiduUtils.VERSION_2);
					
					JSONObject statusResultJson = JSONObject.fromObject(statusResult);
					status=statusResultJson.getJSONObject("body").getJSONObject("data").optString("status");
				}
						
				String result=butils.execCmd(tenantId, tzxShopId, BaiduUtils.CMD_ORDER_ORDERGET, map,  BaiduUtils.VERSION_2);
				
				JSONObject resultJson = JSONObject.fromObject(result);
				
				JSONObject reqJson = new JSONObject();
				
				JSONObject data=resultJson.getJSONObject("body")
				.getJSONObject("data");
				
				//已取消
				if("10".equals(status)){
					reqJson.put("tzxStatus", "08");
				}else{
					reqJson.put("tzxStatus", "10");
				}
				
				reqJson.put("cmd", "order.create");
				reqJson.put("status", status);
				reqJson.put("thirdOrderCode", data.getJSONObject("order").optString("order_id"));
				reqJson.put("body", data);
				reqJson.put("tzxCreateAt",  CommonUtil.getDateFromSeconds(String.valueOf( data.getJSONObject("order").optLong("create_time"))));
				
				messageList.add(reqJson.toString());
				
				logger.info("商户："+tenantId+",门店："+tzxShopId+",获取百度订单详情完成->"+reqJson);
			}
		}
		return messageList;
	}
	@Override
	protected void updateDishStockSingle(JSONObject p) throws Exception {
		String shop_id = String.valueOf(p.optInt("store_id"));
		
		BaiduUtils butils= BaiduUtils.getInstance();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("shop_id", shop_id+"@"+tenantId);
		
		List<JSONObject> list = new ArrayList<JSONObject>();
		if(p.containsKey("stockList") && p.getJSONArray("stockList").size() > 0){
			List<JSONObject> stockList = p.getJSONArray("stockList");
			for(JSONObject json : stockList){
				JSONObject jsonObj = new JSONObject();
				jsonObj.put("dish_id", json.optString("item_code"));
				jsonObj.put("current_stock", p.optInt("stock"));
				list.add(jsonObj);
			}
		}else{
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("dish_id", p.optString("item_code"));
			jsonObj.put("current_stock", p.optInt("stock"));
			list.add(jsonObj);
		}
		
		map.put("stock", list);
		
		String result = butils.execCmd(tenantId, shop_id, BaiduUtils.CMD_DISH_STOCK, map,  BaiduUtils.VERSION_3);
		response = JSONObject.fromObject(result);
		if("0".equals(response.optString("errno"))){
			response.put("stockResult", ERROR);
		}else{
			response.put("stockResult", SUCCESS);
		}
		logger.info("[百度更新菜品库存]上传数据:"+map+"\n返回数据:"+response);
	}
	
	public JSONObject getShopCode(String tenancyID,JSONObject condition){
		Map<String,Object> bodyMap = new HashMap<String,Object>();
		bodyMap.put("shop_id", condition.optString("shop_id")+"@"+tenancyID);
		String result="";
		try {
			result=BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("shop_id"), "shop.code", bodyMap, BaiduUtils.VERSION_3);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return JSONObject.fromObject(result);
	}
	
	
	public JSONObject getDishMenu(String tenancyID,JSONObject condition){
		Map<String,Object> bodyMap = new HashMap<String,Object>();
		bodyMap.put("shop_id", condition.optString("shop_id")+"@"+tenancyID);
		bodyMap.put("has_category", "1");
		String result="";
		try {
			result=BaiduUtils.getInstance().execCmd(tenancyID, condition.optString("shop_id"), " dish.menu.get", bodyMap, BaiduUtils.VERSION_3);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return JSONObject.fromObject(result);
	}
}
