package com.tzx.report.bo.imp.boh;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.report.bo.boh.BusinessSummaryStatisticshualaishiReportService;
import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.po.boh.dao.BusinessSummaryStatisticshualaishiReportDao;

@Service(BusinessSummaryStatisticshualaishiReportService.NAME)
public class BusinessSummaryStatisticsReporthualaishiServiceImp implements BusinessSummaryStatisticshualaishiReportService
{
	 @Resource(name = BusinessSummaryStatisticshualaishiReportDao.NAME)
	 BusinessSummaryStatisticshualaishiReportDao businessSummaryStatisticshualaishiReportDao;
	 
	 @Resource
	private CommonMethodAreaService commonMethodAreaService;
	@Override
	public JSONObject getBusinessSummaryStatistics(String tenancyID,JSONObject condition) throws Exception {
		return businessSummaryStatisticshualaishiReportDao.getBusinessSummaryStatistics(tenancyID, condition);
	}
 
	 
}