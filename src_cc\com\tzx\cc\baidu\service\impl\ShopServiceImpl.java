package com.tzx.cc.baidu.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import com.tzx.cc.baidu.entity.CmdType;
import com.tzx.cc.baidu.entity.DeliveryRegion;
import com.tzx.cc.baidu.entity.Shop;
import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.baidu.service.ShopService;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.baidu.util.DataPost;
import com.tzx.cc.baidu.util.SignHolder;
import com.tzx.cc.openapi.dao.OpenApiDao;
import com.tzx.cc.thirdparty.util.MeiTuanHelper;

@Service(ShopService.NAME)
@Scope("prototype")
@Deprecated
public class ShopServiceImpl implements ShopService{
	
	@Resource(name=OpenApiDao.NAME)
	private OpenApiDao dao;
	
	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.service.impl.ShopService#getShopInitInfo(java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public JSONObject getShopInitInfo(String tenantId, String shopId,String channel) throws Exception {
		
		JSONObject data=null;
		//查询基本信息
		data=this.dao.getShopInitInfo(tenantId, shopId, channel).get(0);
		//查询营业时间
		List<JSONObject> lineup=this.dao.getShopBussinessTime(tenantId, shopId);
		
		if(Constant.BAIDU_CHANNEL.equals(channel)){
			String lineupTimeId="";
			String bussinessTimeFormat="";
			List bs=new ArrayList();
			
			for(JSONObject json:lineup){
				lineupTimeId+=json.optString("id")+",";
				
				String start=json.optString("start").equals("24:00")?"00:00":json.optString("start");
				String end=json.optString("end").equals("24:00")?"00:00":json.optString("end");
				if(!CommonUtil.checkStringIsNotEmpty(start)||!CommonUtil.checkStringIsNotEmpty(end)) continue;
				
				Map m=new HashMap();
				m.put("start", start);
				m.put("end", end);
				bs.add(m);
				bussinessTimeFormat+=start+"-"+end+";";
			}
			data.put("business_time", bs);
			if(!bussinessTimeFormat.equals("")){
				bussinessTimeFormat=bussinessTimeFormat.substring(0, bussinessTimeFormat.length()-1);
			}
			if(!lineupTimeId.equals("")){
				lineupTimeId=lineupTimeId.substring(0, lineupTimeId.length()-1);
			}
			data.put("business_time_format", bussinessTimeFormat);
			data.put("lineup_time_org_id",lineupTimeId);
			data.put("tenant_id", tenantId);
			
		}
		//TODO
		if(Constant.DIANPING_CHANNEL.equals(channel)){

			String lineupTimeId="";
			String bussinessTimeFormat="";
			List bs=new ArrayList();
			bussinessTimeFormat +="[";
			for(JSONObject json:lineup){
				lineupTimeId+=json.optString("id")+",";
				
				String start=json.optString("start");
				String end=json.optString("end");
				if(!CommonUtil.checkStringIsNotEmpty(start)||!CommonUtil.checkStringIsNotEmpty(end)) continue;
				
				Map m=new HashMap();
				m.put("start", start);
				m.put("end", end);
				bs.add(m);
				bussinessTimeFormat+= "["+start+","+end+"]"+",";
			}
			data.put("business_time", bs);
			if(!bussinessTimeFormat.equals("")){
				bussinessTimeFormat=bussinessTimeFormat.substring(0, bussinessTimeFormat.length()-1);
			}
			if(!lineupTimeId.equals("")){
				lineupTimeId=lineupTimeId.substring(0, lineupTimeId.length()-1);
			}
			bussinessTimeFormat +="]";
			data.put("business_time_format", bussinessTimeFormat);
			data.put("lineup_time_org_id",lineupTimeId);
			data.put("tenant_id", tenantId);
		}
		
		return data;
	}

	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.service.impl.ShopService#shopList(java.lang.String, java.lang.String, java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject shopList(String tenantId, String shopId,String channel,JSONObject params) throws Exception {
		
		if(null!=params){
			return this.dao.shopList(tenantId, shopId, channel, params.optInt("page"), params.optInt("rows"));
		}else{
			return this.dao.shopList(tenantId, shopId, channel, 1, 999);
		}
		
	}

	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.service.impl.ShopService#shopCreateOrUpdate(java.lang.String, net.sf.json.JSONObject, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject shopCreateOrUpdate(String tenantId,JSONObject params,JSONObject result) throws Exception {
		if(params.containsKey("channel")&&"BD06".equalsIgnoreCase(params.optString("channel"))){
			params.put("channel", Constant.BAIDU_CHANNEL);
			params.put("shop_state", Constant.BAIDU_SHOP_OPEN);
			
			Shop shop=generateBaiduShop(params);
			
			Map category=shop.getCategorys().get(0);
			
			params.put("category1", category.get("category1"));
			params.put("category2", category.get("category2"));
			params.put("category3", category.get("category3"));
			
			//修改
			if(CommonUtil.checkStringIsNotEmpty(params.optString("id"))){
				
				List bs=new ArrayList();
				String[] bussinessTimes=params.optString("business_time_format").split(";");
				for(String t:bussinessTimes){
					String[] lt=t.split("-");
					String start=lt[0];
					String end=lt[1];
					
					Map m=new HashMap();
					m.put("start", start);
					m.put("end", end);
					bs.add(m);
				}
				
				shop.setBusiness_time(bs);
				
				String cmd=CommonUtil.cmdFactory1(params.optString("source"), params.optString("secret"), CmdType.SHOP_UPDATE, shop.JSONFormatBean());
				
				String res=CommonUtil.httpPost(Constant.BAIDU_API_URL,cmd);
				
				JSONObject resJson=JSONObject.fromObject(res);
				
				JSONObject body=(resJson.optJSONObject("body"));
				
				
				if(body.optString("errno").equals("0")){
					
					this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);
					
				}else{
					result.put("errno", body.optString("errno"));
					result.put("error", body.optString("error"));
				}
			}else{//新建
				
				String cmd=CommonUtil.cmdFactory1(params.optString("source"), params.optString("secret"), CmdType.SHOP_CREATE, shop.JSONFormatBean());
				
				String res=CommonUtil.httpPost(Constant.BAIDU_API_URL,cmd);
				
				JSONObject resJson=JSONObject.fromObject(res);
				
				JSONObject body=(resJson.optJSONObject("body"));
				
				
				if(body.optString("errno").equals("0")){
					
					params.put("third_shop_id", body.optJSONObject("data").optString("baidu_shop_id"));
					this.dao.insertIgnorCase(tenantId, "cc_third_organ_info", params);
					
				}else{
					result.put("errno", body.optString("errno"));
					result.put("error", body.optString("error"));
				}
			}
		}else if (params.containsKey("channel")&&"DP07".equalsIgnoreCase(params.optString("channel"))){

			JSONObject dp_obj = JSONObject.fromObject("{}");
			dp_obj.put("shopid", params.optString("shop_id")+"@"+tenantId);
			dp_obj.put("city", params.optString("city"));
			dp_obj.put("shopname", params.optString("name"));
			dp_obj.put("address", params.optString("address"));
			dp_obj.put("phonenumber", params.optString("phone"));
			dp_obj.put("lat", params.optString("latitude"));
			dp_obj.put("lng", params.optString("longitude"));
			dp_obj.put("coordtype", 3);
			dp_obj.put("interval", params.optString("delivery_time"));
			dp_obj.put("discount", params.optString("discount"));
			dp_obj.put("status", params.optString("shop_state"));
			dp_obj.put("picture", params.optString("shop_logo"));
			dp_obj.put("minfee", params.optString("min_order_price"));
			dp_obj.put("mindeliverfee", params.optString("delivery_fee"));
			dp_obj.put("distance", params.optString("distance"));
			dp_obj.put("servetimejson", params.optString("business_time_format"));
			JSONObject geojson_obj =new JSONObject();
			geojson_obj.put("type", "FeatureCollection");
			
			List<JSONObject> features_obj_list =new ArrayList<JSONObject>();
			JSONObject features_obj =new JSONObject();
			
			JSONObject geometry_obj =new JSONObject();
			geometry_obj.put("type", "Polygon");
			StringBuffer coordinates_buffer =new StringBuffer(); 
			String coordinates[]=params.optString("coordinate").split(";");
			coordinates_buffer.append("[[");
			for(int i=0;i<coordinates.length;i++){
				coordinates_buffer.append("[");
				coordinates_buffer.append(coordinates[i]);
				coordinates_buffer.append("]");
				coordinates_buffer.append(",");
			}
			coordinates_buffer.append("]]");
			geometry_obj.put("coordinates",coordinates_buffer.toString());
			
			features_obj.put("geometry", geometry_obj);
			
			JSONObject properties_obj =new JSONObject();
			properties_obj.put("delivery_price", params.optString("min_order_price"));
			properties_obj.put("coordtype", 3);
			
			features_obj.put("properties", properties_obj);
			
			features_obj_list.add(features_obj);
			geojson_obj.put("features", features_obj_list);
			
			dp_obj.put("geojson", geojson_obj);
			
			dp_obj.put("onlinepayment", params.optString("onlinepayment"));
			
			dp_obj.put("invoiceSupported", params.optString("invoice_support_save"));
			
			dp_obj.put("minInvoiceFee", params.optString("min_invoice_fee"));
			
			params.put("geojson", geojson_obj);
			params.put("tenant_id", tenantId);
			params.put("coord_type", 3);
			params.put("invoice_support", params.optString("invoice_support_save"));
			JSONObject signJSON =new JSONObject();
			signJSON.put("source", params.optString("source"));
			signJSON.put("secret", params.optString("secret"));
			JSONObject result_obj= DataPost.postData2DP(dp_obj,signJSON);
//			if(result_obj.optString("errno").equals("0")){
//				params.put("third_shop_id", result_obj.optJSONObject("data").optString("id"));
//			}else{
//				result.put("errno", result_obj.optString("errno"));
//				result.put("error", result_obj.optString("error"));
//			}
			//修改
			if(CommonUtil.checkStringIsNotEmpty(params.optString("id"))){
				this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);
			}else{
				this.dao.insertIgnorCase(tenantId, "cc_third_organ_info", params);
			}
		}
	
		return result;
	}

	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.service.impl.ShopService#shopSetStatus(java.lang.String, net.sf.json.JSONObject, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject shopSetStatus(String tenantId,JSONObject params,JSONObject result) throws Exception {
		
		String cmdType = params.optString("cmd_type");
		String source = params.optString("source");
		String secret = params.optString("secret");
		String shopId = params.optString("shop_id");
		Map map = new HashMap();
		map.put("shop_id", shopId+"@"+tenantId);
		CmdType type = null;
		switch (cmdType) {
		case "open":
			type = CmdType.SHOP_OPEN;
			params.put("shop_state", Constant.BAIDU_SHOP_OPEN);
			break;
		case "close":
			type = CmdType.SHOP_CLOSE;
			params.put("shop_state", Constant.BAIDU_SHOP_CLOSE);
			break;
		default:
			break;
		}

		String cmd = CommonUtil.cmdFactory1(source, secret, type, map);

		String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);

		JSONObject resJson = JSONObject.fromObject(res);

		JSONObject body = (resJson.optJSONObject("body"));

		if (body.optString("errno").equals("0")) {

			this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);

		} else {
			result.put("errno", body.optString("errno"));
			result.put("error", body.optString("error"));
		}

		return result;
	}
	
	

	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.service.impl.ShopService#orderOper(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject orderOper(String tenantId, JSONObject params) throws Exception {
		JSONObject response=new JSONObject();
		String shopId=params.optString("shopId");
		String orderId=params.optString("orderId");
		String operType=params.optString("operType");
		
		Sign sign=SignHolder.getShopSign(tenantId, shopId);
		String source=sign.getSource();
		String secret=sign.getSecret();
		CmdType type=null;
		
		Map body=new TreeMap();
		
//		Map<String,String> map=new HashMap<>();
//		map.put("order_id", orderId);
		body.put("order_id", orderId);
		
		
		switch (operType) {
		case "confirm":
			type=CmdType.ORDER_CONFIRM;
			break;
		case "cancel":	
			type=CmdType.ORDER_CANCEL;
			body.put("type", params.optInt("type"));
			body.put("reason", params.optString("reason"));
			break;
		case "complete":
			type=CmdType.ORDER_COMPLETE;
			break;	
		default:
			response.put("errno", -1);
			response.put("error", "未支持的命令类型");
			return response;
		}
		
		String cmd = CommonUtil.cmdFactory1(source, secret, type, body ,"2.0");

		String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);

		JSONObject resJson = JSONObject.fromObject(res);

		JSONObject resbody = (resJson.optJSONObject("body"));

		response.put("errno", resbody.optString("errno"));
		response.put("error", resbody.optString("error"));
		
		return response;
	}

	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.service.impl.ShopService#initMealsInfo(java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public List<JSONObject> initMealsInfo(String tenantId, String shopId,String channel) throws Exception {
		return this.dao.getPackageBoxPrice(tenantId, shopId, channel);
	}

	
	
	private Shop generateBaiduShop(JSONObject data) {
		Shop shop=new Shop();
		
		shop.setShop_id(data.optString("shop_id")+"@"+data.optString("tenant_id"));//
		
		shop.setName(data.optString("name"));//
		
		shop.setShop_logo(data.optString("shop_logo"));//
		
		shop.setProvince(data.optString("province"));//
		
		shop.setCity(data.optString("city"));//
		
		shop.setCounty(data.optString("county"));//
		
		shop.setAddress(data.optString("address"));//
		
		shop.setBrand(data.optString("brand"));//
		
		List categorys=new ArrayList();
		Map<String,String> c=new HashMap<String,String>();
		c.put("category1", Constant.CATEGORY1);
		String category23=data.optString("category23");
		String[] category=category23.split(">");
		c.put("category2", category[0]);
		c.put("category3", category[1]);
		categorys.add(c);
		
		shop.setCategorys(categorys);
		
		shop.setPhone(data.optString("phone"));
		
		shop.setService_phone(data.optString("service_phone"));
		
		shop.setLatitude(data.optString("latitude"));
		
		shop.setLongitude(data.optString("longitude"));
		
		//商圈信息
		List<DeliveryRegion> deliveryRegions=new ArrayList<DeliveryRegion>();
		DeliveryRegion deliveryRegion=new DeliveryRegion();
		deliveryRegion.setName(data.optString("region_name"));
		List regions=new ArrayList();
		String coordinateStr= data.optString("coordinate");
		List region=new ArrayList();
		if(CommonUtil.checkStringIsNotEmpty(coordinateStr)){
			String[] coordinateStrArray=coordinateStr.split(";");
			for(int i=0;i<coordinateStrArray.length;i++){
				String[] location=coordinateStrArray[i].split(",");
				String lon=location[0];
				String lat=location[1];
				Map loc=new TreeMap();
				loc.put("latitude", lat);
				loc.put("longitude", lon);
				region.add(loc);
			}
			regions.add(region);
		}
		deliveryRegion.setRegion(regions);
		deliveryRegion.setDelivery_fee(String.valueOf(CommonUtil.yuan2Fen(data.optInt("delivery_fee"))));
		deliveryRegion.setDelivery_time(data.optString("delivery_time"));
		deliveryRegions.add(deliveryRegion);
		shop.setDelivery_region(deliveryRegions);		
		
		shop.setBusiness_time(data.optJSONArray("business_time"));
		
		shop.setInvoice_support(data.optString("invoice_support").equals("1")?"1":"2");
		
		shop.setMin_order_price(String.valueOf(CommonUtil.yuan2Fen(data.optInt("min_order_price"))));
		
		shop.setPackage_box_price(String.valueOf(CommonUtil.yuan2Fen(data.optInt("package_box_price"))));
		
		return shop;
	}

	@Override
	public JSONObject orderStatusGet(String tenantId, String shopId, String baiduOrderId) throws Exception {
		Sign sign=SignHolder.getShopSign(tenantId, shopId);
		String source=sign.getSource();
		String secret=sign.getSecret();
		JSONObject json=new JSONObject();
		json.put("order_id", baiduOrderId);
		String cmd = CommonUtil.cmdFactory1(source, secret, CmdType.ORDER_STATUS_GET, json);
		String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);
		return JSONObject.fromObject(res);
	}

	
	/**
	 * 定时任务 获取美团评论数据
	 */
	public void grabMeituanComments(){
		String tenantId="mrtian"; 
		String tableName="cc_comments";
		
		System.out.println("grabMeituanComments...");
		String cmd="poi/comment/app_poi_code";
		String appId="321";
		String secret="01838de512c19ac1f9bcd4b4a08e911b";
		
		JSONObject parmas=new JSONObject();
		parmas.put("app_poi_code", "6@mianxiang1");
		parmas.put("start_time", "1505865600");
		parmas.put("end_time", "1506729600");
		
		parmas.put("pagesize", "20");
		
		for(int i=0;i<10000;i++){
			System.out.println("正在处理第"+(i+1)+"页");
			parmas.put("pageoffset", (i*20)+"");
			
			String result=null;
			try {
				result=MeiTuanHelper.sendRequest(cmd, appId, secret, parmas, "get");
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			if(!StringUtils.isEmpty(result)){
				JSONObject json=JSONObject.fromObject(result);
				
				JSONArray data=JSONArray.fromObject(json.optString("data"));
				
				
				if(data!=null){
					Iterator<JSONObject> it=data.iterator();
					List jsonList=new LinkedList();
					while(it.hasNext()){
						JSONObject obj=it.next();
						int commentId=obj.optInt("comment_id");
						//查询数据库是否已经存在了
						long len=0;
						try {
							len = this.dao.countSql(tenantId, "select id from cc_comments where comment_id="+commentId);
						} catch (Exception e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
						if(len>0){
	//						log
							continue;
						}
							
						
						
						SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
						JSONObject jsonParam=new JSONObject();
						jsonParam.put("type", 1);
						jsonParam.put("store_comment", obj.optString("comment_content"));
						jsonParam.put("store_score", obj.optInt("order_comment_score"));
						jsonParam.put("comment_time", sdf.format(new Date()));
						jsonParam.put("comment_id", commentId);
						
						jsonList.add(jsonParam);
					}
					
					try {
						Object[] oarr=this.dao.insertBatchIgnorCase(tenantId, tableName, jsonList);
						System.out.println(oarr.length);
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					
					
					
				}
				
				//最后一页
				if(data.size()<20){
					break;
				}
				
				
			}
			
			try {
				Thread.sleep(500);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		
//		HttpUtil.sendPostRequest(reqURL, params)
		
		
	}

}
