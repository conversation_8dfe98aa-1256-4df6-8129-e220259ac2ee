package com.tzx.framework.service.rest;

import com.tzx.framework.bo.DataDictionaryService;
import com.tzx.framework.bo.SystemUserService;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.entity.JsonResult;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.framework.common.util.redis.BaseRedisUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Controller("SystemUserRest")
@RequestMapping("/framework/systemUserRest")
public class SystemUserRest
{

	private static Logger logger = LoggerFactory.getLogger(SystemUserRest.class);

	@Resource(name = SystemUserService.NAME)
	private SystemUserService		systemUserService;

	@Autowired
	private DataDictionaryService	dataDictionaryService;

	public static String			result_check	= "{\"success\" : true}";

	public static String[]			agents			= new String[]{"Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"};

	@Resource
	private BaseRedisUtil redisUtil;


	

	private static String TOKEN_PREFIX = "appapi_";


	@RequestMapping("/getSystemUser")
	public void getSystemUser(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";

		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			/*if (!obj.containsKey("organ_code"))
			{
				obj.put("organ_code", (String) session.getAttribute("organ_code"));
			}*/
			String conditions = (String) session.getAttribute("user_organ_codes_group");
			result = systemUserService.getSystemUser((String) session.getAttribute("tenentid"), obj,conditions).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\" : false , \"msg\" : \"加载用户时发生错误!\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping("/initPassword")
	public void initPassword(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";

		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			if (Tools.hv(obj.optString("id")))
			{
				obj.put("password", "e10adc3949ba59abbe56e057f20f883e");
				this.dataDictionaryService.save((String) session.getAttribute("tenentid"), "user_authority", obj);
			}

		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\" : false , \"msg\" : \"加载用户时发生错误!\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	 * 停用
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/updateValidate")
	public void updateValidate(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";

		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			// 前台传人的条件
			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}

			p.put("tenancy_id", session.getAttribute("tenentid"));
			// 修改人和修改时间
			p.put("last_operator", session.getAttribute("employeeName"));
			p.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));

			dataDictionaryService.save((String) session.getAttribute("tenentid"), p.getString("tableName"), p);

		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\" : false , \"msg\" : \"停用时发生错误!\"}";
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.println(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping("/saveRole")
	public void saveRole(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			// 调研批量插入的方法
			List<JSONObject> objList = new ArrayList<JSONObject>();
			JSONObject p = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			String roles = request.getParameter("role_id");
			if (roles != null && !"".equals(roles))
			{
				String[] roleArray = roles.split(",");
				for (int i = 0; i < roleArray.length; i++)
				{
					JSONObject r = JSONObject.fromObject("{}");
					r.put("user_id", p.get("id"));
					r.put("store_id", p.get("store_id"));
					r.put("roles_id", roleArray[i]);
					r.put("tenancy_id", (String) session.getAttribute("tenentid"));
					objList.add(r);
				}
			}
			else
			{
				result = "{\"success\" : false , \"msg\" : \"角色信息空!\"}";
			}
			// 先删除，然后再插入
			JSONObject del = JSONObject.fromObject("{}");
			del.put("tenentid", (String) session.getAttribute("tenentid"));
			del.put("user_id", p.optInt("id"));
			List<JSONObject> list = new ArrayList<JSONObject>();
			list.add(del);
			dataDictionaryService.delete((String) session.getAttribute("tenentid"), p.getString("tableName"), list);

			Object[] obj = dataDictionaryService.save((String) session.getAttribute("tenentid"), p.getString("tableName"), objList);

			if (obj != null) result = "{\"success\": true, \"id\" : \"" + obj.toString() + "\"}";
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\" : false , \"msg\" : \"保存用户角色时发生错误!\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	//@ResponseBody
	@RequestMapping(value = "/userlogin")
	public void userlogin(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		String tenentid = request.getParameter("tenentid");
		String loginUserName = request.getParameter("loginUserName");
		String password = request.getParameter("password");
		String themes = request.getParameter("themes");	
		Boolean mf = isMobile(request);
		PrintWriter out = null;
		JSONObject result = JSONObject.fromObject("{}");

		String clientUuid = request.getParameter(Constant.SPRING_SESSION_PARAM);
		String urlSuffix = clientUuid==null?"":"?_s="+clientUuid;

		try
		{
			JSONObject longinJsonObject = new JSONObject();
			longinJsonObject.put("tenentid", tenentid);
			longinJsonObject.put("loginUserName", loginUserName);
			longinJsonObject.put("password", password);

			JSONObject userInfoJosonObje = systemUserService.chekUserLogin(tenentid, longinJsonObject);

			if (loginUserName.equals("system") && Constant.getSystemMap().get("system_password").equals(password))
			{
				session.setAttribute("isLogin", "true");
				session.setAttribute("tenentid", tenentid);
				session.setAttribute("employeeName", "system");
				session.setAttribute("organName", "总部");
				session.setAttribute("organ_code", "0");
				session.setAttribute("organ_id", "0");
				session.setAttribute("store_id", "0");// 机构ID
				session.setAttribute("employeeId", "0");
				session.setAttribute("e_id", "0");
				session.setAttribute("organ_brief_code", "CN0000");
				session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
				session.setAttribute("name", "系统管理员");
				session.setAttribute("org_type", "0");
				session.setAttribute("da_date", "");
				session.setAttribute("day_count", "");
				// 用于判断是否是管理员
				session.setAttribute("sysuser", "1");
				//查询用户是否具有特殊权限,如果没有则返回普通权限
				String sysuser = (String) session.getAttribute("sysuser");
				String organ_codes= systemUserService.findOrganCodes(tenentid,sysuser,userInfoJosonObje);
				String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenentid,sysuser,userInfoJosonObje);
				session.setAttribute("user_organ_codes_group_invalid", organ_codes_invalid);
				session.setAttribute("user_organ_codes_group", organ_codes);
				
				session.setAttribute("themes", (themes == null || themes.isEmpty()) ? "metro-gray" : themes);
				// session.setAttribute("roles_id", "admin");// 以 ” ，"分开的IDS
				Map<Integer, Integer> map = new HashMap<Integer, Integer>();
				map.put(0, 1);
				session.setAttribute("authMap", map);
				result.element("success", true);
				JSONObject p = JSONObject.fromObject("{}");
				p.put("tenancy_id", (String) session.getAttribute("tenentid"));
				p.put("id", (String) session.getAttribute("employeeId"));
				p.put("store_id", (String) session.getAttribute("store_id"));
				session.setAttribute("moduleMap", systemUserService.getRoleAuthorutyModule(tenentid, (String) session.getAttribute("sysuser"), p));

				if ("tzx-new".equals(session.getAttribute("themes")))
				{
					result.element("url", mf ? "/pages/mobile/main.jsp"+urlSuffix : "/pages/framework/layout/main2.jsp"+urlSuffix);
				}
				else
				{
					result.element("url", mf ? "/pages/mobile/main.jsp"+urlSuffix : "/pages/framework/layout/main.jsp"+urlSuffix);
				}
			}
			else
			{
				if (userInfoJosonObje != null)
				{
					session.setAttribute("isLogin", "true");
					session.setAttribute("tenentid", tenentid);
					// 用于判断是否是管理员
					if(loginUserName.equals(Constant.ADMIN)){
						session.setAttribute("sysuser", "1");
					}else{
						session.setAttribute("sysuser", "0");
					}
					
					String sysuser = (String) session.getAttribute("sysuser");
					// session.setAttribute("login_account",
					// userInfoJosonObje.getString("login_account"));
					session.setAttribute("employeeName", userInfoJosonObje.optString("user_name"));
					session.setAttribute("employeeId", userInfoJosonObje.optString("id"));// 用户id
					session.setAttribute("e_id", userInfoJosonObje.optString("employee_id"));
					session.setAttribute("organ_id", userInfoJosonObje.optString("store_id"));
					session.setAttribute("organName", userInfoJosonObje.optString("org_full_name"));
					session.setAttribute("organ_code", userInfoJosonObje.optString("organ_code"));
					session.setAttribute("organ_brief_code", userInfoJosonObje.optString("organ_brief_code"));
					session.setAttribute("name", userInfoJosonObje.optString("name"));
					session.setAttribute("org_type", userInfoJosonObje.optString("org_type"));
					session.setAttribute("da_date", "null".equals(userInfoJosonObje.optString("da_date"))?"":userInfoJosonObje.optString("da_date"));
					session.setAttribute("day_count", "null".equals(userInfoJosonObje.optString("day_count"))?"":userInfoJosonObje.optString("day_count"));

					if ("0".equals(userInfoJosonObje.optString("store_id")))
					{
						session.setAttribute("org_type", "0");
						session.setAttribute("da_date", "");
						session.setAttribute("day_count", "");
						session.setAttribute("organ_code", "0");
						session.setAttribute("organName", "总部");
						session.setAttribute("organ_brief_code", "CN0000");
					}

					String organ_codes= systemUserService.findOrganCodes(tenentid,sysuser,userInfoJosonObje);
					session.setAttribute("user_organ_codes_group", organ_codes);
					String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenentid,sysuser,userInfoJosonObje);
					session.setAttribute("user_organ_codes_group_invalid", organ_codes_invalid);
					session.setAttribute("user_id", userInfoJosonObje.optInt("id"));
					session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
					// session.setAttribute("roles_id",
					// userInfoJosonObje.getString("role_id"));//
					session.setAttribute("store_id", userInfoJosonObje.getString("store_id"));// 机构ID
					session.setAttribute("themes", (themes == null || themes.isEmpty()) ? "metro-gray" : themes);
					// 加载用户的权限信息
					JSONObject p = JSONObject.fromObject("{}");
					p.put("tenancy_id", (String) session.getAttribute("tenentid"));
					p.put("id", (String) session.getAttribute("employeeId"));
					p.put("store_id", (String) session.getAttribute("store_id"));
					session.setAttribute("authMap", systemUserService.getRoleAuthoruty(tenentid, (String) session.getAttribute("sysuser"), p));
					session.setAttribute("moduleMap", systemUserService.getRoleAuthorutyModule(tenentid, (String) session.getAttribute("sysuser"), p));
					session.setAttribute(" ", userInfoJosonObje); // 把整个用户信息的JSONObject对象放入JSON
					result.element("success", true);
					if ("tzx-new".equals(session.getAttribute("themes")))
					{
						result.element("url", mf ? "/pages/mobile/main.jsp"+urlSuffix : "/pages/framework/layout/main2.jsp"+urlSuffix);
					}
					else
					{
						result.element("url", mf ? "/pages/mobile/main.jsp"+urlSuffix : "/pages/framework/layout/main.jsp"+urlSuffix);
					}
				}
				else
				{
					result.element("success", false);
					result.element("msg", "用户名或密码错误，请重新输入！");
					result.element("code", "passwordError");
					result.element("info", longinJsonObject.toString());
				}

			}// end first else

		}
		catch (Exception e)
		{
			e.printStackTrace();
			result.element("success", false);
			result.element("code", "tenentidError");
			result.element("msg", "商户号错误，请重新输入！");

		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{

			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	@RequestMapping(value = "/userloginom")
	public void userloginom(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		String tenentid = request.getParameter("tenentid");
		String loginUserName = request.getParameter("loginUserName");
		String password = request.getParameter("password");
		String themes = request.getParameter("themes");
		Boolean mf = isMobile(request);
		PrintWriter out = null;
		JSONObject result = JSONObject.fromObject("{}");

		try {
			JSONObject longinJsonObject = new JSONObject();
			longinJsonObject.put("tenentid", tenentid);
			longinJsonObject.put("loginUserName", loginUserName);
			longinJsonObject.put("password", password);

			if (loginUserName.equals("system") && password.equals("15f28fbc8c6e099a1e98b8a555d72374")) {

				session.setAttribute("isLogin", "true");
				session.setAttribute("tenentid", tenentid);
				session.setAttribute("employeeName", "system");
				session.setAttribute("organName", "总部");
				session.setAttribute("organ_code", "0");
				session.setAttribute("organ_id", "0");
				session.setAttribute("store_id", "0");// 机构ID
				session.setAttribute("employeeId", "0");
				session.setAttribute("e_id", "0");
				session.setAttribute("organ_brief_code", "CN0000");
				session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip")
						? Constant.getSystemMap().get("upload_img_ip").toString() : "");
				session.setAttribute("name", "系统管理员");
				session.setAttribute("org_type", "0");
				session.setAttribute("da_date", "");
				session.setAttribute("day_count", "");
				// 用于判断是否是管理员
				session.setAttribute("sysuser", "1");
				String sysuser = (String) session.getAttribute("sysuser");
				session.setAttribute("themes", (themes == null || themes.isEmpty()) ? "metro-gray" : themes);
				// session.setAttribute("roles_id", "admin");// 以 ” ，"分开的IDS
				Map<Integer, Integer> map = new HashMap<Integer, Integer>();
				map.put(0, 1);
				session.setAttribute("authMap", map);
				result.element("success", true);

				result.element("url", "/pages/framework/layout/ommain.jsp");

			}

		} catch (Exception e) {
			e.printStackTrace();
			result.element("success", false);
			result.element("code", "tenentidError");
			result.element("msg", "商户号错误，请重新输入！");

		} finally {
			try {
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			} catch (Exception e) {

			} finally {
				if (out != null)
					out.close();
			}
		}
	}

	@RequestMapping(value = "/loadFirstLevel")
	public void loadFirstLevel(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		PrintWriter out = null;
		InputStream in = null;
		String result = "";
		try
		{
			/*String moduleType = request.getParameter("module_type");
			if(!StringUtils.isEmpty(moduleType)) {*/
				JSONObject p = JSONObject.fromObject("{}");
				p.put("tenancy_id", (String) session.getAttribute("tenentid"));
				p.put("id", (String) session.getAttribute("employeeId"));
				p.put("store_id", (String) session.getAttribute("store_id"));
				//p.put("module_type", moduleType);	//模块类型，类似SCM、HQ、CRM等
				List<JSONObject> list = systemUserService.loadFirstLevel((String) session.getAttribute("tenentid"), (String) session.getAttribute("sysuser"), p);
				if (list != null && list.size() > 0)
				{
					result = list.toString();
				}
			/*} else {
				JSONObject jsonResult = new JSONObject();
				jsonResult.put("success", "false");
				jsonResult.put("msg", "请传入module_type参数");
				result = jsonResult.toString();
			}*/
		}
		catch (Exception e)
		{
			logger.error("loadFirstLevel异常", e);
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/loadChild")
	public void loadChild(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		PrintWriter out = null;
		InputStream in = null;
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			p.put("tenancy_id", (String) session.getAttribute("tenentid"));
			p.put("id", (String) session.getAttribute("employeeId"));
			p.put("store_id", (String) session.getAttribute("store_id"));
			List<JSONObject> list = systemUserService.loadChild((String) session.getAttribute("tenentid"), (String) session.getAttribute("sysuser"), p);
			if (list != null && list.size() > 0)
			{
				result = list.toString();
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/changeThemes")
	public void changeThemes(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		PrintWriter out = null;
		InputStream in = null;
		String result = "{\"success\":\"true\"}";
		try
		{
			session.setAttribute("themes", request.getParameter("themes"));
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/logout")
	public void logout(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		PrintWriter out = null;
		InputStream in = null;
		String result = "{\"success\":\"true\"}";
		try
		{
			session.setAttribute("isLogin", "false");
			session.setAttribute("tenentid", null);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/userrelogin")
	public void userrelogin(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		String tenentid = request.getParameter("tenentid");
		String loginUserName = request.getParameter("loginUserName");
		String password = request.getParameter("password");
		String themes = request.getParameter("themes");

		String clientUuid = request.getParameter(Constant.SPRING_SESSION_PARAM);
		String urlSuffix = clientUuid==null?"":"?_s="+clientUuid;

		PrintWriter out = null;
		JSONObject result = JSONObject.fromObject("{}");

		try
		{
			JSONObject longinJsonObject = new JSONObject();
			longinJsonObject.put("tenentid", tenentid);
			longinJsonObject.put("loginUserName", loginUserName);
			longinJsonObject.put("password", password);

			JSONObject userInfoJosonObje = systemUserService.chekUserLogin(tenentid, longinJsonObject);

			if (loginUserName.equals("system") && Constant.getSystemMap().get("system_password").equals(password))
			{

				session.setAttribute("isLogin", "true");
				session.setAttribute("tenentid", tenentid);
				session.setAttribute("employeeName", "system");
				session.setAttribute("organName", "总部");
				session.setAttribute("organ_code", "0");
				session.setAttribute("organ_id", "0");
				session.setAttribute("store_id", "0");// 机构ID
				session.setAttribute("employeeId", "0");
				session.setAttribute("e_id", "0");
				session.setAttribute("organ_brief_code", "CN0000");
				session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
				session.setAttribute("name", "系统管理员");
				session.setAttribute("org_type", "0");
				session.setAttribute("da_date", "");
				session.setAttribute("day_count", "");
				// 用于判断是否是管理员
				session.setAttribute("sysuser", "1");
				String sysuser = (String) session.getAttribute("sysuser");
				String organ_codes= systemUserService.findOrganCodes(tenentid,sysuser,userInfoJosonObje);
				session.setAttribute("user_organ_codes_group", organ_codes);
				String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenentid,sysuser,userInfoJosonObje);
				session.setAttribute("user_organ_codes_group_invalid", organ_codes_invalid);
				
				session.setAttribute("themes", (themes == null || themes.isEmpty()) ? "metro-gray" : themes);
				// session.setAttribute("roles_id", "admin");// 以 ” ，"分开的IDS
				Map<Integer, Integer> map = new HashMap<Integer, Integer>();
				map.put(0, 1);
				session.setAttribute("authMap", map);
				result.element("success", true);
				if ("tzx-new".equals(session.getAttribute("themes")))
				{
					result.element("url", "/pages/framework/layout/main2.jsp" + urlSuffix);
				}
				else
				{
					result.element("url", "/pages/framework/layout/main.jsp" + urlSuffix);
				}

			}
			else
			{

				if (userInfoJosonObje != null)
				{

					session.setAttribute("isLogin", "true");
					session.setAttribute("tenentid", tenentid);
					// 用于判断是否是管理员
					session.setAttribute("sysuser", "0");
					String sysuser = (String) session.getAttribute("sysuser");
					// session.setAttribute("login_account",
					// userInfoJosonObje.getString("login_account"));
					session.setAttribute("employeeName", userInfoJosonObje.optString("user_name"));
					session.setAttribute("employeeId", userInfoJosonObje.optString("id"));// 用户id
					session.setAttribute("e_id", userInfoJosonObje.optString("employee_id"));
					session.setAttribute("organ_id", userInfoJosonObje.optString("store_id"));
					session.setAttribute("organName", userInfoJosonObje.optString("org_full_name"));
					session.setAttribute("organ_code", userInfoJosonObje.optString("organ_code"));
					session.setAttribute("organ_brief_code", userInfoJosonObje.optString("organ_brief_code"));
					session.setAttribute("name", userInfoJosonObje.optString("name"));
					session.setAttribute("org_type", userInfoJosonObje.optString("org_type"));
					session.setAttribute("da_date", "null".equals(userInfoJosonObje.optString("da_date"))?"":userInfoJosonObje.optString("da_date"));
					session.setAttribute("day_count", "null".equals(userInfoJosonObje.optString("day_count"))?"":userInfoJosonObje.optString("day_count"));

					if ("0".equals(userInfoJosonObje.optString("store_id")))
					{
						session.setAttribute("org_type", "0");
						session.setAttribute("da_date", "");
						session.setAttribute("day_count", "");
						session.setAttribute("organ_code", "0");
						session.setAttribute("organName", "总部");
						session.setAttribute("organ_brief_code", "CN0000");
					}
					String organ_codes= systemUserService.findOrganCodes(tenentid,sysuser,userInfoJosonObje);
					session.setAttribute("user_organ_codes_group", organ_codes);
					String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenentid,sysuser,userInfoJosonObje);
					session.setAttribute("user_organ_codes_group_invalid", organ_codes_invalid);
					session.setAttribute("user_id", userInfoJosonObje.optInt("id"));
					session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
					// session.setAttribute("roles_id",
					// userInfoJosonObje.getString("role_id"));//
					session.setAttribute("store_id", userInfoJosonObje.getString("store_id"));// 机构ID
					session.setAttribute("themes", (themes == null || themes.isEmpty()) ? "metro-gray" : themes);
					// 加载用户的权限信息
					JSONObject p = JSONObject.fromObject("{}");
					p.put("tenancy_id", (String) session.getAttribute("tenentid"));
					p.put("id", (String) session.getAttribute("employeeId"));
					p.put("store_id", (String) session.getAttribute("store_id"));
					session.setAttribute("authMap", systemUserService.getRoleAuthoruty(tenentid, (String) session.getAttribute("sysuser"), p));
					session.setAttribute("moduleMap", systemUserService.getRoleAuthorutyModule(tenentid, (String) session.getAttribute("sysuser"), p));
					session.setAttribute("userInfoJosonObje", userInfoJosonObje); // 把整个用户信息的JSONObject对象放入JSON
					result.element("success", true);
					result.element("store_id", (String) session.getAttribute("store_id"));
					if ("tzx-new".equals(session.getAttribute("themes")))
					{
						result.element("url", "/pages/framework/layout/main2.jsp" + urlSuffix);
					}
					else
					{
						result.element("url", "/pages/framework/layout/main.jsp" + urlSuffix);
					}
				}
				else
				{
					result.element("success", false);
					result.element("msg", "用户名或密码错误，请重新输入！");
					result.element("code", "passwordError");
				}

			}// end first else
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result.element("success", false);
			result.element("code", "tenentidError");
			result.element("msg", "商户号错误，请重新输入！");
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{

			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	// 导出前发送一个ajax请求检测session是否失效，避免出现导出的时候子页面跳转页面
	@RequestMapping("/checkUserSession")
	public void checkUserSession(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;

		try
		{
			out = response.getWriter();
			out.print(result_check);
			out.flush();
			out.close();
		}
		catch (Exception e)
		{
		}
		finally
		{
			if (out != null) out.close();
		}
	}
	
	public static Boolean isMobile(HttpServletRequest request)
	{
		if(request!=null)
		{
			String userAgent = request.getHeader("user-agent").trim();
			int headersLen = agents.length;
	        for (int i = 0;i < headersLen; i++) {
	            if(userAgent.contains(agents[i])){
	               return true;
	            }
	        }
		}
		return false;
	}

	@RequestMapping("/findAllStoreIds")
	public void findAllStoreIds(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String eid = (String) session.getAttribute("employeeId");
		String tenancy_id  = (String) session.getAttribute("tenentid");
		List<JSONObject>  list  = null;
		try {
			out = response.getWriter();
			list = systemUserService.findAllStoreIds(tenancy_id,eid);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(JSONArray.fromObject(list).toString());
				out.flush();
				out.close();
			}
		}
	}

	/**
	 * 获取当前登录角色用户所拥有的模块信息
	 * @param request  需传入模块类型和版本信息
	 * @param response
	 */
	@ResponseBody
	@RequestMapping(value = "/queryUserAuthModulesInfo", method=RequestMethod.POST)
	public JSONObject queryUserAuthModulesInfo(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();

		String tenentid = (String) session.getAttribute("tenentid");
		String userName = (String) session.getAttribute("employeeName");
		String moduleType = request.getParameter("module_type");
		try {
			if (moduleType != null && !"".equals(moduleType)) {
				JSONObject param = JSONObject.fromObject("{}");
				param.put("tenancy_id", tenentid);
				param.put("user_name", userName);
				param.put("module_type", moduleType);
				List<JSONObject> retList = systemUserService.queryUserAuthModulesInfo(param);
				if (retList.size() > 0) {
					result.put("code", "0");
					result.put("msg", "success");
					result.put("data", retList);
				} else {
					result.put("code", "1001");
					result.put("msg", "未获取登录用户角色模块信息！");
				}
			} else {
				result.put("code", "1002");
				result.put("msg", "未获取到查询模块类型，请传入对应参数信息！");
			}
		} catch (Exception e) {
			logger.error("获取当前登录角色用户的模块信息异常");
			result.put("code", "1003");
			result.put("msg", "获取当前登录角色用户的模块信息异常！");
		}

		return result;
	}

	/**
	 * 退出登录
	 * @param request
	 */
	@ResponseBody
	@RequestMapping(value = "/userLogout")
	public JSONObject userLogout(HttpServletRequest request) {
		JSONObject result = new JSONObject();
		try {
			HttpSession session = request.getSession();
			session.invalidate();
            /*String ssoUrl = Constant.getSystemMap().get("sso.homepage");
            String saasUrl = Constant.getSystemMap().get("saas.domain");
            String redirectUrl = saasUrl + "/logout?service=" + saasUrl;*/
			String saasUrl = Constant.getSystemMap().get("saas.domain");
			String redirectUrl = saasUrl + "/pages/framework/layout/redirectlogout.jsp";

			result.put("code", "0");
			result.put("url", redirectUrl);
		} catch (Exception e) {
			logger.error("退出登录异常", e);
			result.put("code", "1001");
			result.put("msg", "退出登录异常");
		}

		return result;
	}

	/**
	 * 返回引导页
	 */
	@ResponseBody
	@RequestMapping(value = "/returnHomepage")
	public JSONObject returnHomepage() {
		JSONObject result = new JSONObject();

		try {
			String saas_url = Constant.getSystemMap().get("sso.homepage");
			result.put("code", "0");
			result.put("url", saas_url);
		} catch (Exception e) {
			logger.error("返回引导页");
			result.put("code", "1001");
			result.put("msg", "返回引导页异常");
		}

		return result;
	}

	/**
	 * 用户登录验证，并将对应人员信息存入session中 (对外接口，从sysconf迁移过来)
	 * @param request
	 */
	@ResponseBody
	@RequestMapping(value = "/userAuthentication", method=RequestMethod.POST)
	public JSONObject userAuthentication(HttpServletRequest request)
	{
		JSONObject httpResult = null;
		HttpSession session = request.getSession();
		String tenantId = (String) session.getAttribute("tenentid");
		String userName = (String) session.getAttribute("employeeName");
		String storeIdStr = request.getParameter("storeId");
		logger.info("请求接口[userAuthentication], 参数tenancyId={}, userName={}" ,tenantId ,userName);

		JSONObject loginJsonObject = new JSONObject();
		if(!StringUtils.isEmpty(storeIdStr)) {
			int storeId = Integer.parseInt(storeIdStr);			// 切换门店时所传门店id
			loginJsonObject.put("storeId", storeId);
		}
		loginJsonObject.put("tenancyId", tenantId);
		loginJsonObject.put("userName", userName);

		JSONObject result = new JSONObject();
		DBContextHolder.setTenancyid(tenantId);

		try
		{
			JSONObject empInfo = systemUserService.getEmpInfo(tenantId, loginJsonObject);
			if (userName.equals("system"))
			{
				logger.info("system账户访问接口");
				// 用于判断是否是管理员
				String sysuser = "1";//(String) session.getAttribute("sysuser");

				String organ_codes= systemUserService.findOrganCodes(tenantId,sysuser,empInfo);
				String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenantId,sysuser,empInfo);
				result.put("user_organ_codes_group_invalid", organ_codes_invalid);
				result.put("user_organ_codes_group", organ_codes);

				Map<Integer, Integer> map1 = new HashMap<Integer, Integer>();
				map1.put(0, 1);
				result.put("authMap", map1);

				JSONObject param0 = new JSONObject();
				param0.put("tenantId", tenantId);
				param0.put("userId", 0);
				param0.put("storeId", 0);
				result.put("moduleMap", systemUserService.getRoleAuthorutyModule(tenantId, sysuser, param0));
			}
			else {
				JSONObject sessnioInfo = new JSONObject();    // session中数据通过接口返回
				if (empInfo != null) {
					logger.info("当前用户信息: {}", empInfo.toString());
					String sysuser = "";
					JSONObject defaultBrand = systemUserService.findDefaultBrand(tenantId, userName);    //默认品牌
					if (defaultBrand != null) {
						sessnioInfo.put("defaultBrand", defaultBrand.get("id").toString());
					} else {
						sessnioInfo.put("defaultBrand", "");
					}
					sessnioInfo.put("isLogin", "true");
					sessnioInfo.put("tenentid", tenantId);
					// 用于判断是否是管理员
					if (userName.equals(Constant.ADMIN) || Integer.parseInt(empInfo.get("u_sid").toString()) == 0) {
						sysuser = "1";
						sessnioInfo.put("sysuser", "1");
						sessnioInfo.put("employeeName", empInfo.get("user_name"));//用户名称
						sessnioInfo.put("e_name", empInfo.get("name"));//人员名称
						sessnioInfo.put("employeeId", empInfo.get("employee_id").toString());//人员id
						sessnioInfo.put("user_id", empInfo.get("id").toString());//用户id
						sessnioInfo.put("roleId", empInfo.get("roles_id").toString());// 用户角色id
						sessnioInfo.put("roleName", empInfo.get("rolename").toString());// 用户角色名称
						sessnioInfo.put("e_id", empInfo.get("employee_id").toString());// 人员id
						sessnioInfo.put("organName", "总部");//当前登录用户机构全称
						sessnioInfo.put("organ_code", "0");//当前登录用户机构编码
						sessnioInfo.put("organ_id", "0");//机构id
						sessnioInfo.put("store_id", "0");// 机构ID
						sessnioInfo.put("organ_brief_code", "CN0000");//当前登录用户机构第三方编码
						sessnioInfo.put("userName", empInfo.get("user_name"));//用户名称
						sessnioInfo.put("org_type", "0");//当前登录用户机构类型
						sessnioInfo.put("da_date", "");
						sessnioInfo.put("day_count", "");
					} else {
						sysuser = "0";
						sessnioInfo.put("sysuser", "0");
						sessnioInfo.put("employeeName", empInfo.get("user_name"));//人员名称
						sessnioInfo.put("e_name", empInfo.get("name"));//人员名称
						sessnioInfo.put("employeeId", empInfo.get("employee_id").toString());//人员id
						sessnioInfo.put("user_id", empInfo.get("id").toString());//用户id
						sessnioInfo.put("roleId", empInfo.get("roles_id").toString());// 用户角色id
						sessnioInfo.put("roleName", empInfo.get("rolename").toString());// 用户角色名称
						sessnioInfo.put("e_id", empInfo.get("employee_id").toString());// 人员id
						sessnioInfo.put("organName", empInfo.get("org_full_name"));//当前登录用户机构全称
						sessnioInfo.put("organ_code", empInfo.get("organ_code"));//当前登录用户机构编码
						sessnioInfo.put("organ_id", empInfo.get("store_id").toString());//机构id
						sessnioInfo.put("store_id", empInfo.get("store_id").toString());// 机构ID
						sessnioInfo.put("organ_brief_code", empInfo.get("organ_brief_code"));//当前登录用户机构第三方编码
						sessnioInfo.put("userName", empInfo.get("user_name"));//用户名称
						sessnioInfo.put("org_type", empInfo.get("org_type"));//当前登录用户机构类型
						sessnioInfo.put("da_date", "".equals(empInfo.get("da_date")) || empInfo.get("da_date") == null ? "" : empInfo.get("da_date"));
						sessnioInfo.put("day_count", "".equals(empInfo.get("day_count")) || empInfo.get("day_count") == null ? "" : empInfo.get("day_count"));
					}
					String organ_codes = systemUserService.findOrganCodes(tenantId, sysuser, empInfo);
					sessnioInfo.put("user_organ_codes_group", organ_codes);
					String organ_codes_invalid = systemUserService.findOrganCodesWithInvalid(tenantId, sysuser, empInfo);
					sessnioInfo.put("user_organ_codes_group_invalid", organ_codes_invalid);
					logger.info("user_organ_codes_group--->{}", organ_codes);
					logger.info("user_organ_codes_group_invalid--->{}", organ_codes_invalid);

					// 加载用户的权限信息
					JSONObject param2 = new JSONObject();
					param2.put("tenantId", tenantId);
					param2.put("userId", Integer.parseInt(empInfo.get("id").toString()));
					param2.put("storeId", Integer.parseInt(sessnioInfo.get("store_id").toString()));

					Map<Integer, Integer> roleInfo = (Map<Integer, Integer>) systemUserService.getRoleAuthoruty(tenantId, (String) session.getAttribute("sysuser"), param2);
					JSONObject roleJson = new JSONObject();
					for(Integer key:roleInfo.keySet()) {
						roleJson.put(String.valueOf(key), roleInfo.get(key));
					}
					sessnioInfo.put("authMap", roleJson);
					sessnioInfo.put("moduleMap", systemUserService.getRoleAuthorutyModule(tenantId, (String) session.getAttribute("sysuser"), param2));

					JSONObject param4 = JSONObject.fromObject("{}");
					param4.put("tenancyId", tenantId);
					param4.put("userName", userName);
					List<JSONObject> retList = systemUserService.findUserAuthModul(param4);
					if (retList.size() > 0) {//返回当前登录用户所拥有的的主模块权限信息
						JSONObject m = new JSONObject();
						for (JSONObject sm : retList) {
							m.put(sm.getString("id"), sm.getString("modulename"));
						}
						sessnioInfo.put("authInfo", m);
					} else {
						//
					}

					result.put("sessnioInfo", sessnioInfo);
					httpResult = JsonResult.successResult(result);
				} else {
					logger.error("未获取到当前用户信息");
					httpResult = JsonResult.failureResult("1001", "未获取到当前用户信息！");
				}
			}
		}
		catch (Exception e)
		{
			logger.error("获取登录用户session信息调用异常", e);
			httpResult = JsonResult.failureResult("1002", "验证用户信息异常");
		}

		return httpResult;
	}
	
	
	/**
	 * 获取用户的一级模块
	 * @param request  需传入模块类型和版本信息
	 * @param response
	 */
	@ResponseBody
	@RequestMapping(value = "/queryUserModule", method=RequestMethod.POST)
	public JSONObject queryUserModule(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();

		String tenentid = (String) session.getAttribute("tenentid");
		String userName = (String) session.getAttribute("employeeName");
		String moduleType = request.getParameter("module_type");
		try {
			if (moduleType != null && !"".equals(moduleType)) {
				JSONObject param = JSONObject.fromObject("{}");
				param.put("tenancy_id", tenentid);
				param.put("user_name", userName);
				param.put("module_type", moduleType);

				List<JSONObject> moduleList = systemUserService.queryUserModule(param);
				if (moduleList !=null && moduleList.size() > 0) {
					result.put("code", "0");
					result.put("msg", "success");
					result.put("data", moduleList);
				} else {
					result.put("code", "1001");
					result.put("msg", "未获取登录用户角色模块信息！");
				}
			} else {
				result.put("code", "1002");
				result.put("msg", "缺少参数[module_type]");
			}
		} catch (Exception e) {
			logger.error("获取用户的一级模块信息异常", e);
			result.put("code", "1003");
			result.put("msg", "获取用户的一级模块信息异常！");
		}

		return result;
	}


	/**
	 * 获取当前商户信息以及跳转url
	 * 前端拿到返回值和他自己的商户号对比 不一样就需要跳转到返回的url
	 * @param request
	 * @return  tenancyId  redirectUrl
	 */
	@RequestMapping(value = "queryCurrentTenancy",method = RequestMethod.POST)
	@ResponseBody
	public JSONObject queryCurrentTenancy(HttpServletRequest request){
		JSONObject result = new JSONObject();
		result.put("success",false);
		try {
			HttpSession session = request.getSession();
			if(session.getAttribute("tenentid")!=null){
				String tenancyId = (String)session.getAttribute("tenentid");
				result.put("success",true);
				result.put("tenancyId",tenancyId);
				String saasUrl = Constant.getSystemMap().get("saas.domain");
				result.put("redirectUrl",saasUrl);
			}else {
				logger.warn("queryCurrentTenancy session中没有商户号信息!");
			}
		}catch (Exception e){
			e.printStackTrace();
		}
		return result;
	}
}
