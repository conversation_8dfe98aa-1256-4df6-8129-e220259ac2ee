package com.tzx.report.po.operate.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.tools.Tool;

import org.python.antlr.PythonParser.break_stmt_return;
import org.python.antlr.ast.Break;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.google.gson.JsonObject;
import com.ibm.db2.jcc.t4.sb;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.operate.dao.BusinessTaleAnalysisDao;
import com.tzx.report.po.operate.dao.DoBusinessPaymentDao;

@Repository(BusinessTaleAnalysisDao.NAME)
public class BusinessTaleAnalysisDaoImpl implements BusinessTaleAnalysisDao{

	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	
	private final String sqlTypeDateL0 ="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_25' and sql_type='ZWL0'";
	private final String sqlTypeDateL1 ="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_25' and sql_type='ZWL1'";
	private final String sqlTypeDateL2 ="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_25' and sql_type='ZWL2'";
	private final String sqlTypeDateL3 ="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_25' and sql_type='ZWL3'";
	
	//公共函数调用
	private final String selectCommonSql ="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_23' and sql_type='TABLE_AREA_MEAL'";
		
	
	List<JSONObject> list =null;
	StringBuilder sb = new StringBuilder();
	@Override
	public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
		Integer type = condition.optInt("type");
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> array = new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		String begindate = condition.optString("report_date_begin");
		String enddate = condition.optString("report_date_end");
		JSONObject result = new JSONObject();
		StringBuilder footer = new StringBuilder();
		String sqlType = "";
		String reportCount = "";
		String reportSql="";
		long total = 0L;
		if(begindate.length()>0 && enddate.length()>0 )
		{
				if(!condition.containsKey("exportdataexpr")){
					condition.put("exportdataexpr", "''");
				}
				if(condition.optInt("hierarchytype") ==1){
						// 获取可以执行的sql
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							condition.put("exportdataexpr", "'"+condition.get("exportdataexpr")+"'");
//							String store_ids = condition.getString("store_ids");
//							condition.put("store_ids", store_ids.subSequence(1, store_ids.length()-1));
							reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,selectCommonSql);
							reportSql = this.dao.getString(tenancyID, reportSql);
							list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
							structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
						}else{
							reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,selectCommonSql);
							reportSql = this.dao.getString(tenancyID, reportSql);
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							if(condition.optString("exportdataexpr").equals("''")) {
							condition.put("hierarchytype", "0");
							reportCount = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,selectCommonSql);
							reportCount = this.dao.getString(tenancyID, reportCount);
							footerList = this.dao.query4Json(tenancyID, reportCount.toString());
							}
						}
						
						
					}else if(condition.optInt("hierarchytype") ==2 && condition.containsKey("store_ids")){
							
						
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							String store_ids = condition.getString("store_ids");
							condition.put("store_ids", store_ids.subSequence(1, store_ids.length()-1));
							reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,selectCommonSql);
							reportSql = this.dao.getString(tenancyID, reportSql);
							list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level1")));
							for(JSONObject js :list){
								js.put("store_name", "");
							}
							structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
						}else{
							reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,selectCommonSql);
							reportSql = this.dao.getString(tenancyID, reportSql);
							condition.element("sort", "table_code");
							condition.element("order","asc");
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						}
						
						
					}else if(condition.optInt("hierarchytype") ==3 && condition.containsKey("table_codes")){
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,selectCommonSql);
							reportSql = this.dao.getString(tenancyID, reportSql);
							list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level2")));
							for(JSONObject js :list){
								js.put("store_name", "");
								js.put("table_name", "");
							}
							structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
						}else{
							reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,selectCommonSql);
							reportSql = this.dao.getString(tenancyID, reportSql);
							condition.element("sort", "id");
							condition.element("order","asc");
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						}
					}
					int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
					result.put("page", pagenum);
					result.put("total",total);	
					result.put("rows", list);
					result.put("footer", footerList);
					result.put("structure", structure);
			}
		return result;
	}
	
	/** 
	 * 
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public String getRunSql (String tenancyID , JSONObject condition ) throws Exception {
		String returnSlq = "";
		returnSlq = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectCommonSql);
		List<JSONObject> list = this.dao.query4Json(tenancyID, returnSlq.toString());
		 if (list.size() > 0 && list.get(0).opt("f_hq_table_area_property_analysis") != null) {
			 returnSlq  = list.get(0).opt("f_hq_table_area_property_analysis").toString();
		 } 
		return returnSlq;
	}
	
	
}
