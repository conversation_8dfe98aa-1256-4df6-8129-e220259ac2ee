package com.tzx.cc.baidu.service.impl;

import java.util.ArrayList;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.google.gson.reflect.TypeToken;
import com.tzx.cc.baidu.entity.Attr;
import com.tzx.cc.baidu.entity.Category;
import com.tzx.cc.baidu.entity.CmdType;
import com.tzx.cc.baidu.entity.DishCategoryCreate;
import com.tzx.cc.baidu.entity.DishCategoryUpdate;
import com.tzx.cc.baidu.entity.DishCreateUpdate;
import com.tzx.cc.baidu.entity.DishOnOfflineDel;
import com.tzx.cc.baidu.entity.Norms;
import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.baidu.entity.TimeRange;
import com.tzx.cc.baidu.service.DishService;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.baidu.util.SignHolder;
import com.tzx.cc.openapi.dao.OpenApiDao;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.HttpUtil;
@Deprecated
public class DishServiceImpl implements DishService{
	@Resource(name=OpenApiDao.NAME)
	private OpenApiDao dao;
	
	@Override
	public JSONObject loadDishCategoryList(String tenancyID,JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		
		if (condition.containsKey("id")
				&& condition.optInt("id") > 0) {
			sql.append("select d.id,a.class as class_id,case when f.itemclass_name is null then e.itemclass_name else concat(e.itemclass_name,'-',f.itemclass_name) end as curclass_name,d.last_send_class_name,d.rank,d.whether_push_over from hq_item_menu_class a left join hq_item_class e on a.class = e.id left join cc_third_item_class_info d on a.class = d.item_class_id and d.shop_id='" + condition.optInt("id") + "' left join hq_item_class f ON e.father_id = f.id");
			sql.append(" where  a.details_id in ( select b.id from hq_item_menu_details b inner join hq_item_menu_organ c on b.item_menu_id = c.item_menu_id inner join hq_item_menu g on b.item_menu_id = g.id inner join cc_third_organ_info h on c.store_id = h.shop_id where c.store_id = '" + condition.optInt("id") + "'  and g.valid_state = '1') and a.chanel = '" + condition.optString("channel") + "'");
			sql.append(" group by a.class,curclass_name,d.last_send_class_name,d.rank,d.whether_push_over,d.id");
			int pagenum = condition.containsKey("page") ? (condition
					.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		} else {
			result.put("page", "1");
			result.put("total", "0");
			result.put("rows", "[]");
		}

		return result;
	}
	
	@Override
	public JSONObject loadDishList(String tenancyID,JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		
		int storeId=condition.optInt("id");
		
		if (condition.containsKey("id")
				&& condition.optInt("id") > 0) {
			sql.append("select   DISTINCT(A . ID),d.store_id,a.id as item_code,b.item_name,a.item_barcode,c.item_id,g.g_price as default_price,e.min_order_num,e.package_box_num,e.whether_push_over,e.description,e.item_pic as photo1,e.available_times_start,e.available_times_end,f.last_send_class_name,f.rank,a.is_combo,case when a.is_combo='Y' then '套餐' else '单品' end as attr_name,e.item_status from hq_item_menu_class b inner join hq_item_menu_details c on b.details_id = c.id inner join hq_item_menu_organ d on c.item_menu_id = d.item_menu_id inner join hq_item_menu h on c.item_menu_id = h.id inner join cc_third_organ_info i on i.shop_id = d.store_id left join hq_item_info a on a.id = c.item_id left join cc_third_item_info e on e.item_code = CAST(A.id as VARCHAR) and e.shop_id="+storeId);
			sql.append(" left join cc_third_item_class_info f on b.class = f.item_class_id and f.shop_id="+storeId);
			sql.append(" left join ( select c1.unit_name as g_unit_name,a1.price as g_price,	c1.item_id as g_item_id from hq_item_pricesystem a1 left join organ b1 on cast(a1.price_system as varchar) = b1.price_system left join hq_item_unit c1 on a1.item_unit_id = c1.id where b1.id='" + condition.optInt("id") + "' and a1.chanel = '" + Constant.BAIDU_CHANNEL + "' and c1.valid_state = '1' and c1.is_default = 'Y' ) g on g.g_item_id = c.item_id ");
			sql.append("where d.store_id = '" + condition.optInt("id") + "' and b.chanel = '" + condition.optString("channel")+ "' and h.valid_state = '1' and f.last_send_class_name is not null ");

			int pagenum = condition.containsKey("page") ? (condition
					.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		} else {
			result.put("page", "1");
			result.put("total", "0");
			result.put("rows", "[]");
		}

		return result;
	}
	
	@Override
	public JSONObject getPrice(String tenancyID,JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		
		if (condition.containsKey("shop_id")
				&& condition.optInt("shop_id") > 0) {
			sql.append("select c.id,c.unit_name,a.price from hq_item_pricesystem a left join organ b on cast(a.price_system as varchar) = b.price_system left JOIN hq_item_unit c on a.item_unit_id = c.id ");
			sql.append("where c.item_id='" + condition.optInt("item_id") + "' and b.id = '" + condition.optInt("shop_id") + "' and a.chanel = '" + Constant.BAIDU_CHANNEL + "' and c.valid_state = '1'");

			int pagenum = condition.containsKey("page") ? (condition
					.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		} else {
			result.put("page", "1");
			result.put("total", "0");
			result.put("rows", "[]");
		}

		return result;
	}

	@Override
	public JSONObject dishDategoryCreate(String tenancyID,JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		
		String sql = null;
		
		if(condition != null)
		{
			if(condition.optString("channel").equalsIgnoreCase("BD06")){
				DishCategoryCreate dishCategoryCreate = new DishCategoryCreate();
				dishCategoryCreate.setShop_id(condition.optString("shop_id").concat("@").concat(tenancyID));
				dishCategoryCreate.setName(condition.optString("cur_class_name"));
				dishCategoryCreate.setRank(condition.optInt("rank"));
				
				Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("shop_id"));
				
				String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CATEGORY_CREATE, dishCategoryCreate);

				String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);
				
				JSONObject resultData = JSONObject.fromObject(resultString);
				
				int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
				
				if (resultCode == 0) {
					sql = "insert into cc_third_item_class_info(shop_id,item_class_id,last_send_class_name,rank,whether_push_over,send_operator,send_time,channel,tenancy_id) values('" + condition.optString("shop_id") + "','" + condition.optString("class_id") + "','" + condition.optString("cur_class_name") + "','"
							+ condition.optInt("rank") + "','1','" + condition.optString("send_operator") + "','" + condition.optString("send_time").trim() + "','" + Constant.BAIDU_CHANNEL + "','" + tenancyID + "')";
					dao.execute(tenancyID, sql);
					result.put("success", true);
				} else {
					result.put("success", false);
					result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
				}
			}else if(condition.optString("channel").equalsIgnoreCase("DP07")){
				if(CommonUtil.checkStringIsNotEmpty(condition.optString("id"))){

					Object obj_update=this.dao.updateIgnorCase(tenancyID, "cc_third_item_class_info", condition);
					if(obj_update!=null){
						result.put("success", true);
					}else{
						result.put("success", false);
						result.put("msg", "保存失败");
					}
				}else{
					Object obj_add=this.dao.insertIgnorCase(tenancyID, "cc_third_item_class_info", condition);
					if(obj_add!=null){
						result.put("success", true);
					}else{
						result.put("success", false);
						result.put("msg", "保存失败");
					}
				
				}
			
			}
		
		}
		return result;
	}

	@Override
	public JSONObject dishCategoryUpdate(String tenancyID,JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		
		String sql = null;
		
		if(condition != null)
		{
			DishCategoryUpdate dishCategoryUpdate = new DishCategoryUpdate();
			dishCategoryUpdate.setShop_id(condition.optString("shop_id").concat("@").concat(tenancyID));
			dishCategoryUpdate.setOld_name(condition.optString("last_send_class_name"));
			dishCategoryUpdate.setName(condition.optString("cur_class_name"));
			dishCategoryUpdate.setRank(condition.optInt("rank"));
			
			Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("shop_id"));
			
			String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CATEGORY_UPDATE, dishCategoryUpdate);

			String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);
			
			JSONObject resultData = JSONObject.fromObject(resultString);
			
            int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
			
			if (resultCode == 0) {
				sql = "update cc_third_item_class_info set last_send_class_name='" + condition.optString("cur_class_name") + "',rank='" + condition.optString("rank") + "',update_operator='" + condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim()
						+ "' where shop_id='" + condition.optString("shop_id") + "' and item_class_id='" + condition.optString("class_id") + "'";
				dao.execute(tenancyID, sql);
				result.put("success", true);
			} else {
				result.put("success", false);
				result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
			}

		}
		return result;
	}

	@Override
	public JSONObject dishCreate(String tenancyID,JSONObject condition) throws Exception
	{JSONObject result = new JSONObject();
	
	String sql = null;
	
	if(condition != null)
	{
		DishCreateUpdate dishCreate = new DishCreateUpdate();
		// 合作方商户唯一 ID
		dishCreate.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
		// 菜品唯一编号
		dishCreate.setDish_id(condition.optString("item_code"));
		// 菜品名称
		dishCreate.setName(condition.optString("item_name"));
		// 条形码编号
		dishCreate.setUpc(condition.optString("item_barcode"));
		// 菜品价格
		dishCreate.setPrice(CommonUtil.yuan2Fen(condition.optDouble("default_price")));
		// 菜品图片
		dishCreate.setPic(condition.optString("photo1"));
		// 最小起订份数
		dishCreate.setMin_order_num(condition.optInt("min_order_num"));
		// 单份所需餐盒数
		dishCreate.setPackage_box_num(condition.optInt("package_box_num"));
		// 描述
		dishCreate.setDescription(condition.optString("description"));
		
		//可售时间
		if (condition.optString("available_times_start") != "" || condition.optString("available_times_end") != "") {
			Map<String, List<JSONObject>> timezone = new HashMap<String, List<JSONObject>>();
			List<JSONObject> timeList = new ArrayList<JSONObject>();
			JSONObject timeRange = new JSONObject();
			timeRange.put("start",condition.optString("available_times_start"));
			timeRange.put("end",condition.optString("available_times_end"));
			timeList.add(timeRange);
			
			timezone.put("*", timeList);

			dishCreate.setAvailable_times(timezone);
		}
		
		// 分类信息
		List<JSONObject> categoryList = new ArrayList<JSONObject>();
		JSONObject category = new JSONObject();
		category.put("name",condition.optString("last_send_class_name"));
		category.put("rank",condition.optInt("rank"));

		categoryList.add(category);
		
		dishCreate.setCategory(categoryList);
		
		// 菜品规格
		List<JSONObject> normsList = new ArrayList<JSONObject>();
		
		Norms norms = null;
		
		if (condition.containsKey("priceList") && condition.get("priceList") != "")
		{
			
			@SuppressWarnings("unchecked")
			List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(condition.get("priceList").toString(), new TypeToken<List<JSONObject>>(){}.getType());
			
			for (int i = 0; i < list.size(); i++) {
				norms = new Norms();
				norms.setName(list.get(i).optString("unit_name"));
				//norms.setValue(String.valueOf(list.get(i).optInt("id")));//百度外卖不支持规格ID
				norms.setValue(list.get(i).optString("unit_name"));
				norms.setPrice(CommonUtil.yuan2Fen(list.get(i).optDouble("price")));
				normsList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(JSONObject.fromObject(norms))));
			}
		}

		dishCreate.setNorms(normsList);
		
		// 菜品属性
//		List<Attr> attrList = new ArrayList<Attr>();
//		Attr attr = new Attr();
//		attr.setName(condition.optString("attr_name"));
//		attr.setValue("Y".equals(condition.optString("is_combo"))?"是":"否");
//		attrList.add(attr);
//		
//		dishCreate.setAttr(attrList);
		
		Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));
		
		String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_CREATE, dishCreate);

		String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);
		
		JSONObject resultData = JSONObject.fromObject(resultString);
		
        int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
		
		if (resultCode == 0) {
			sql = "insert into cc_third_item_info(shop_id,min_order_num,package_box_num,available_times_start,available_times_end,rank,whether_push_over,description,item_pic,item_status,item_code,send_operator,send_time,channel,tenancy_id) values('" + condition.optString("store_id") + "','"
					+ condition.optInt("min_order_num") + "',"+condition.optInt("package_box_num")+",'" + condition.optString("available_times_start") + "','" + condition.optString("available_times_end") + "','" + condition.optString("rank") + "','1','" + condition.optString("description")
					+ "','" + condition.optString("photo1") + "','1','" + condition.optString("item_code") + "','" + condition.optString("send_operator") + "','" + condition.optString("send_time").trim() + "','" + Constant.BAIDU_CHANNEL + "','" + tenancyID + "')";
			dao.execute(tenancyID, sql);
			result.put("success", true);
		} else {
			result.put("success", false);
			result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
		}

	}
	return result;
	}

	@Override
	public JSONObject dishUpdate(String tenancyID,JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		
		String sql = null;
		
		if(condition != null)
		{
			DishCreateUpdate dishUpdate = new DishCreateUpdate();
			// 合作方商户唯一 ID
			dishUpdate.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
			// 菜品唯一编号
			dishUpdate.setDish_id(condition.optString("item_code"));
			// 菜品名称
			dishUpdate.setName(condition.optString("item_name"));
			// 条形码编号
			dishUpdate.setUpc(condition.optString("item_barcode"));
			// 菜品价格
			dishUpdate.setPrice(CommonUtil.yuan2Fen(condition.optDouble("default_price")));
			// 菜品图片
			dishUpdate.setPic(condition.optString("photo1"));
			// 最小起订份数
			dishUpdate.setMin_order_num(condition.optInt("min_order_num"));
			// 单份所需餐盒数
			dishUpdate.setPackage_box_num(condition.optInt("package_box_num"));
			// 描述
			dishUpdate.setDescription(condition.optString("description"));
			
			//可售时间
			if (condition.optString("available_times_start") != "" || condition.optString("available_times_end") != "") {
				Map<String, List<JSONObject>> timezone = new HashMap<String, List<JSONObject>>();
				List<JSONObject> timeList = new ArrayList<JSONObject>();
				JSONObject timeRange = new JSONObject();
				timeRange.put("start",condition.optString("available_times_start"));
				timeRange.put("end",condition.optString("available_times_end"));
				timeList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(timeRange)));
				
				timezone.put("*", timeList);
				dishUpdate.setAvailable_times(timezone);
			}
			
			// 分类信息
			List<JSONObject> categoryList = new ArrayList<JSONObject>();
			JSONObject category = new JSONObject();
			category.put("name",condition.optString("last_send_class_name"));
			category.put("rank",condition.optInt("rank"));

			categoryList.add(category);
			
			dishUpdate.setCategory(categoryList);
			
			// 菜品规格
			List<JSONObject> normsList = new ArrayList<JSONObject>();
			
			Norms norms = null;
			
			if (condition.containsKey("priceList") && condition.get("priceList") != "")
			{
				
				@SuppressWarnings("unchecked")
				List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(condition.get("priceList").toString(), new TypeToken<List<JSONObject>>(){}.getType());
				
				for (int i = 0; i < list.size(); i++) {
					norms = new Norms();
					norms.setName(list.get(i).optString("unit_name"));
					//norms.setValue(String.valueOf(list.get(i).optInt("id")));//百度外卖不支持规格ID
					norms.setValue(list.get(i).optString("unit_name"));
					norms.setPrice(CommonUtil.yuan2Fen(list.get(i).optDouble("price")));
					normsList.add(JSONObject.fromObject(CommonUtil.sortMapByKey(JSONObject.fromObject(norms))));
				}
			}

			dishUpdate.setNorms(normsList);
			
			// 菜品属性
//			List<Attr> attrList = new ArrayList<Attr>();
//			Attr attr = new Attr();
//			attr.setName(condition.optString("attr_name"));
//			attr.setValue("Y".equals(condition.optString("is_combo"))?"是":"否");
//			attrList.add(attr);
//			
//			dishUpdate.setAttr(attrList);
			
			Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));
			
			String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_UPDATE, dishUpdate);

			String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);
			
			JSONObject resultData = JSONObject.fromObject(resultString);
			
            int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
			
			if (resultCode == 0) {
				sql = "update cc_third_item_info set min_order_num='" + condition.optInt("min_order_num") + "',package_box_num='"+condition.optInt("package_box_num")+"',available_times_start='" + condition.optString("available_times_start") + "',available_times_end='"
						+ condition.optString("available_times_end") + "',rank='" + condition.optString("rank") + "',description='" + condition.optString("description") + "',item_pic='" + condition.optString("photo1") + "',update_operator='" + condition.optString("update_operator")
						+ "',update_time='" + condition.optString("update_time").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_code='" + condition.optString("item_code") + "'";
				dao.execute(tenancyID, sql);
				result.put("success", true);
			} else {
				result.put("success", false);
				result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
			}

		}
		return result;
	}

	@Override
	public JSONObject dishOnline(String tenancyID,JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		
		String sql = null;
		
		if(condition != null)
		{
			DishOnOfflineDel dishOnOfflineDel = new DishOnOfflineDel();
			dishOnOfflineDel.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
			dishOnOfflineDel.setDish_id(condition.optString("item_code"));
			
			Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));
			
			String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_ONLINE, dishOnOfflineDel);

			String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);
			
			JSONObject resultData = JSONObject.fromObject(resultString);
			
            int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
			
			if (resultCode == 0) {
				sql = "update cc_third_item_info set item_status='1',update_operator='" + condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_code='"
						+ condition.optString("item_code") + "'";
				dao.execute(tenancyID, sql);
				result.put("success", true);
			} else {
				result.put("success", false);
				result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
			}

		}
		return result;
	}

	@Override
	public JSONObject dishOffline(String tenancyID,JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		
		String sql = null;
		
		if(condition != null)
		{
			DishOnOfflineDel dishOnOfflineDel = new DishOnOfflineDel();
			dishOnOfflineDel.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
			dishOnOfflineDel.setDish_id(condition.optString("item_code"));
			
			Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));
			
			String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_OFFLINE, dishOnOfflineDel);

			String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);
			
			JSONObject resultData = JSONObject.fromObject(resultString);
			
            int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
			
			if (resultCode == 0) {
				sql = "update cc_third_item_info set item_status='0',update_operator='" + condition.optString("update_operator") + "',update_time='" + condition.optString("update_time").trim() + "' where shop_id='" + condition.optString("store_id") + "' and item_code='"
						+ condition.optString("item_code") + "'";
				dao.execute(tenancyID, sql);
				result.put("success", true);
			} else {
				result.put("success", false);
				result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
			}

		}
		return result;
	}

	@Override
	public JSONObject dishDelete(String tenancyID,JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		
		String sql = null;
		
		if(condition != null)
		{
			DishOnOfflineDel dishOnOfflineDel = new DishOnOfflineDel();
			dishOnOfflineDel.setShop_id(condition.optString("store_id").concat("@").concat(tenancyID));
			dishOnOfflineDel.setDish_id(condition.optString("item_code"));
			
			Sign sign = SignHolder.getShopSign(tenancyID, condition.optString("store_id"));
			
			String requestStr = CommonUtil.cmdFactory1(sign.getSource(), sign.getSecret(), CmdType.DISH_DELETE, dishOnOfflineDel);

			String resultString = HttpUtil.sendPostRequest(Constant.BAIDU_API_URL, requestStr);
			
			JSONObject resultData = JSONObject.fromObject(resultString);
			
            int resultCode = JSONObject.fromObject(resultData.opt("body")).optInt("errno");
			
			if (resultCode == 0) {
				sql = "delete from cc_third_item_info where shop_id='" + condition.optString("store_id") + "' and item_code='" + condition.optString("item_code") + "'";
				dao.execute(tenancyID, sql);
				result.put("success", true);
			} else {
				result.put("success", false);
				result.put("msg", JSONObject.fromObject(resultData.opt("body")).optString("error"));
			}

		}
		return result;
	}

}
