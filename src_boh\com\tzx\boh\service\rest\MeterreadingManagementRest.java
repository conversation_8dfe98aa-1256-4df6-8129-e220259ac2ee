package com.tzx.boh.service.rest;

import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.boh.bo.MeterreadingManagementService;
import com.tzx.framework.bo.DataDictionaryService;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Tools;
/**
 * <AUTHOR>
 */
@Controller("MeterreadingManagementContraller")
@RequestMapping("/boh/meterreadingManagementContraller")
public class MeterreadingManagementRest
{
	@Resource(name = MeterreadingManagementService.NAME)
	private MeterreadingManagementService meterreadingManagementService;

	@Autowired
	private DataDictionaryService dataDictionaryService;

	/**
	 * 获取离选择的业务日期最近的抄表记录
	 */
	@RequestMapping(value = "/loadLastMeterreadingInformation")
	public void loadLastMeterreadingInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			String lastReading= meterreadingManagementService.loadMeterreadingInformation((String) session.getAttribute("tenentid"), obj).toString();
			result = "{\"lastReading\": "+lastReading+"}";
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 保存抄表信息
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/saveMeterreadingInformation")
	public void saveMeterreadingInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			JSONObject checkObj = JSONObject.fromObject("{}");
			Map<String,String[]> map = request.getParameterMap();
			for(String key : map.keySet())
			{
				 if("water_utilit_id".equals(key)||"business_date".equals(key)){
					 checkObj.put(key, map.get(key)[0]);
				}
			}
			boolean rsFlag = meterreadingManagementService.checkUnique((String) session.getAttribute("tenentid"),checkObj);
			JSONObject obj = JSONObject.fromObject("{}");
			for(String key : map.keySet())
			{
				 if("id".equals(key)&& rsFlag==false){
					obj.put("id", "");
				}
				else {
					obj.put(key, map.get(key)[0]);
				}
				
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("store_id",(String) session.getAttribute("organ_id"));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			obj.put("recprd_type","read");
			Object dic = dataDictionaryService.save((String) session.getAttribute("tenentid"), "boh_water_utility_record", obj);
			if (dic != null) result = "{\"success\": true, \"id\" : \"" + dic.toString() + "\"}";
		}
		catch (Exception e)
		{
			result = "{\"success\": false, \"msg\" : \"" + e.getMessage() + "\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 抄表信息增加列表
	 */
	@RequestMapping(value = "/loadAddMeterreadingInformation")
	public void loadAddMeterreadingInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			obj.put("organ_id",(String) session.getAttribute("organ_id"));
			if(!obj.containsKey("organ_code")){
				obj.put("organ_code",(String) session.getAttribute("organ_code"));
			}
			obj.put("is_zb",(String) session.getAttribute("organ_code"));
			result = meterreadingManagementService.loadAddMeterreadingInformation((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	//验证同样的业务日期 相同的水表/电表/气表 是否已抄表
	@RequestMapping(value = "/checkUnique")
	public void checkUnique(HttpServletRequest request, HttpServletResponse response)
	{


		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			boolean rs = meterreadingManagementService.checkUnique((String) session.getAttribute("tenentid"),obj);

			if(rs)
			{
				result = "{\"success\": true}";
			}else
			{
				result = "{\"success\": false}";
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	
	}
	@RequestMapping(value = "/removeMeterreadingInformation")
	public void removeMeterreadingInformation(HttpServletRequest request, HttpServletResponse response)
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			
			String tableName = request.getParameter("tableName");
			
			String ids = request.getParameter("id");
			
			if(Tools.hv(ids))
			{
				String[] idsArray = ids.split(",");
				
				List<JSONObject> list = new ArrayList<JSONObject>();
				
				for(String id : idsArray)
				{
					JSONObject json = JSONObject.fromObject("{'id' : " + id + "}");
					
					list.add(json);
				}
				
				this.dataDictionaryService.delete((String) session.getAttribute("tenentid"), tableName, list);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
}
