#Redis Cluster config
redis.cluster.maxIdle=5
redis.cluster.maxTotal=20

#Spring-redis-data config
redis.cluster.maxRedirects=3

#Redis Cluster config

redis.host1=BJ-QEMU-SAAS-REDIS-NODE-1
redis.port1=3379
redis.host2=BJ-QEMU-SAAS-REDIS-NODE-2
redis.port2=3379
redis.host3=BJ-QEMU-SAAS-REDIS-NODE-3
redis.port3=3379
redis.host4=BJ-QEMU-SAAS-REDIS-NODE-4
redis.port4=3379
redis.host5=BJ-QEMU-SAAS-REDIS-NODE-5
redis.port5=3379
redis.host6=BJ-QEMU-SAAS-REDIS-NODE-6
redis.port6=3379


