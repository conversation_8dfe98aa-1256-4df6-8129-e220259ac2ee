package com.tzx.report.po.springjdbc.dao.imp.report;

import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.report.po.springjdbc.dao.report.RequestUrlCountDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

/**
 * Created by zds on 2018-11-01.
 */
@Repository(RequestUrlCountDao.NAME)
public class RequestUrlCountDaoImpl extends GenericDaoImpl implements RequestUrlCountDao{


    @Override
    public void saveCount(JSONObject jsonObject) throws Exception {
        this.insertIgnorCase(jsonObject.optString("tenancy_id"), "report_requrl_count", jsonObject);
    }

    @Override
    public void updateCount(JSONObject jsonObject) throws Exception {
        this.updateIgnorCase(jsonObject.optString("tenancy_id"), "report_requrl_count", jsonObject);
    }

    @Override
    public void updateCount(String tenancyId, String hashUrl) throws Exception {
        String sql =" UPDATE report_requrl_count set count=count+1,update_time=(CURRENT_TIMESTAMP(0)::timestamp without time zone) WHERE tenancy_id='" + tenancyId + "'" + " AND hash_url='" + hashUrl + "'";
        this.execute(tenancyId, sql);
    }

    @Override
    public int getCountByUrl(String tenancyId, String hashUrl) throws Exception {
        String sql = " SELECT count(id) FROM report_requrl_count WHERE tenancy_id='" + tenancyId + "'" + " AND hash_url='" + hashUrl + "'";

        return this.getInt(tenancyId, sql);
    }
}
