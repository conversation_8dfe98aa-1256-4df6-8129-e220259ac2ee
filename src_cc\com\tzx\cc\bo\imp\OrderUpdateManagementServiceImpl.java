package com.tzx.cc.bo.imp;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.annotation.Resource;

import jodd.util.StringUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.tzx.cc.api.bo.CcOrderGrabService;
import com.tzx.cc.baidu.bo.ShopService;
import com.tzx.cc.baidu.cont.SuweiConst;
import com.tzx.cc.baidu.service.SuweiService;
import com.tzx.cc.baidu.util.OrderDeliveryUtils;
import com.tzx.cc.base.CcExceptionUtil;
import com.tzx.cc.base.Constant;
import com.tzx.cc.bo.OrderManagementHqService;
import com.tzx.cc.bo.OrderUpdateManagementService;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.Oper;
import com.tzx.cc.common.constant.Type;
import com.tzx.cc.common.constant.util.CcPartitionUtils;
import com.tzx.cc.datasync.common.util.NewPosOrderUtil;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.po.springjdbc.dao.OcDao;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.crm.bo.BonusPointManageService;
import com.tzx.framework.bo.CodeService;
import com.tzx.framework.bo.DataDictionaryService;
import com.tzx.framework.common.baidupush.AndroidPushMsgToSingleDevice;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.exception.CcErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.MessageUtils;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.task.po.redis.dao.TaskRedisDao;
import com.tzx.weixin.bo.MemberService;
import com.tzx.weixin.bo.NewWxDishService;
import com.tzx.weixin.po.springjdbc.dao.ConfigDao;
import com.tzx.weixin.po.springjdbc.dao.MemberDao;
import com.tzx.weixin.po.springjdbc.dao.NewWxMemberDao;


@Service(OrderUpdateManagementService.NAME)
public class OrderUpdateManagementServiceImpl implements OrderUpdateManagementService {
    @Resource(name = "genericDaoImpl")
    private GenericDao dao;
    @Resource(name = OcDao.NAME)
    private OcDao ocDao;

    @Autowired
    private DataDictionaryService dataDictionaryService;

    @Resource(name = "codeService")
    private CodeService codeService;

    @Resource(name = MemberService.NAME)
    private MemberService memberService;

    @Resource(name = ShopService.NAME)
    private ShopService shopService;

    @Resource(name = OrderManagementHqService.NAME)
    private OrderManagementHqService orderManagementService;

    @Resource(name = NewWxDishService.NAME)
    private NewWxDishService newWxDishService;

    @Resource(name = SuweiService.NAME)
    private SuweiService suweiService;

    @Autowired
    private ConfigDao configDao;

    @Autowired
    private MemberDao memberDao;

    @Resource(name = TaskRedisDao.NAME)
    private TaskRedisDao taskRedisDao;

    @Resource(name = NewWxMemberDao.NAME)
    private NewWxMemberDao newWxMemberDao;

    @Resource
    private CcOrderGrabService ccOrderGrabService;

    //private static final String PUSH_PLATFORM_MODE = "PUSH_PLATFORM_MODE";
    private static final Logger logger = Logger.getLogger(OrderUpdateManagementServiceImpl.class);

    /**
     * 
     * 
     * update操作时反回的业务码：
     * 
     * code：
     * 0 成功
     * 1 参数失效
     * 3 数据集为空
     * 1001 确认订单异常，等待接收平台取消消息
     * 2408 订单已取消，等待接收取消消息
     * 
     */
    @Override
    public Map<String, Object> orderManage(Data param) throws SystemException, Exception {
    	Map<String,String> status2descMap=new HashMap<String,String>();//订单状态与描述
    	status2descMap.put("04", "已确认");
    	status2descMap.put("11", "已确认(异常)");
    	status2descMap.put("10", "已完成");
    	status2descMap.put("12", "已完成(异常)");
    	
        Map<String, Object> result = null;
        String tenantId = param.getTenancy_id();
        Oper oper = param.getOper();
        Integer store_id = param.getStore_id();
        StringBuilder sb = new StringBuilder();
        JSONObject jb = null;
        boolean flag = true;
        JSONObject third_obj = new JSONObject();
        if (param.getData() != null) {
            jb = new JSONObject();
            @SuppressWarnings("unchecked")
            Map<String, Object> maps = (Map<String, Object>) param.getData().get(0);
            Set<String> set = maps.keySet();
            for (String key : set) {
                jb.put(key, maps.get(key));
            }
        } else {
            param.setCode(Constant.CODE_DATA_NULL);
            param.setMsg(Constant.CODE_DATA_NULL_MSG);
            param.setSuccess(false);
            return result;
        }
        String hq_service_time = DateUtil.format(new Timestamp(System.currentTimeMillis()));
        String report_date = DateUtil.getNowDateYYDDMM();
        String order_state = jb.optString("order_state");
        String order_channel = jb.optString("chanel");
        JSONObject order_infot_obj = new JSONObject();
        JSONObject query_obj = new JSONObject();
        query_obj.put("order_code", jb.optString("order_code"));
        JSONObject result_obj = orderManagementService.loadOrderingList(tenantId, query_obj);
        List<JSONObject> order_list = result_obj.optJSONArray("rows");
        if (order_list.size() > 0) {
            order_infot_obj = order_list.get(0);
        }

        jb.put("payment_state", order_infot_obj.optString("payment_state"));
        jb.put("tenantId", tenantId);
        jb.put("store_id", order_infot_obj.optString("store_id"));
        if (jb.optString("receive_time").equalsIgnoreCase("")) {
            jb.put("receive_time", hq_service_time);
        }
        if (jb.optString("dispatch_time").equalsIgnoreCase("")) {
            jb.put("dispatch_time", hq_service_time);
            jb.put("receive_time_dispatch", hq_service_time);
        }
        if (jb.optString("distribution_time").equalsIgnoreCase("")) {
            jb.put("distribution_time", hq_service_time);
        }
        if (jb.optString("receive_time_distribution").equalsIgnoreCase("")) {
            jb.put("receive_time_distribution", hq_service_time);
        }
        if (jb.optString("take_time").equalsIgnoreCase("")) {
            jb.put("take_time", hq_service_time);
        }
        if (jb.optString("receive_time_qd").equalsIgnoreCase("")) {
            jb.put("receive_time_qd", hq_service_time);

        }
        if ("".equals(jb.optString("finish_time"))) {
            jb.put("finish_time", hq_service_time);
        }
        if ("".equals(jb.optString("report_date"))) {
            jb.put("report_date", report_date);
        }
        if ("".equals(jb.optString("receive_time_finish"))) {
            jb.put("receive_time_finish", hq_service_time);
        }

        String tmpSQL = "";

        jb.put("store_id", param.getStore_id());
        switch (oper) {
            // 修改订单状态
            case update:
                long startTimestamp = System.currentTimeMillis();

//				if(!"10".equals(order_infot_obj.optString("order_state"))&&"WX02".equalsIgnoreCase(order_infot_obj.optString("channel"))){
                if (!"10".equals(order_infot_obj.optString("order_state")) && !"08".equals(order_infot_obj.optString("order_state"))&& !"12".equals(order_infot_obj.optString("order_state"))) {
                    switch (order_state) {
                        // 到店
                        case "02":
                            sb.setLength(0);
                            switch (order_channel) {
                                default:
                                    sb.setLength(0);
                                    sb.append("update cc_order_list set order_state = '02' ,bill_num = '" + jb.optString("bill_num") + "', receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");
                            }

                            tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                            this.dao.execute(tenantId, tmpSQL);
                            break;
                        //异常接收    
                        case "11":
                        // 接收
                        case "04":
                            sb.setLength(0);
                            //保存门店的账号和派单时间
                            sb.append("update cc_order_list set  dispatch_time = '" + jb.optString("receive_time_qd") + "',bill_num = '" + jb.optString("bill_num") + "' where order_code ='" + jb.optString("order_code") + "' ");

                            tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                            this.dao.execute(tenantId, tmpSQL);
                            switch (order_channel) {
                                case "BD06":
                                    JSONObject params = new JSONObject();
                                    params.put("shopId", store_id);
                                    params.put("channel", order_channel);
                                    params.put("third_order_id", jb.optString("third_order_code"));
                                    JSONObject query_order_status_obj = shopService.orderStatusGet(tenantId, params);
                                    JSONObject body_obj = query_order_status_obj.optJSONObject("body");
                                    if (body_obj.optString("errno").equalsIgnoreCase("0")) {
                                        JSONObject data_obj = body_obj.optJSONObject("data");
                                        String status = data_obj.optString("status");

                                        logger.info("百度订单："+jb.optString("order_code")+"进行确认前的订单状态："+status);
                                        
                                        if (!"10".equalsIgnoreCase(status)) {
                                            third_obj.put("shopId", store_id);
                                            third_obj.put("orderId", jb.optString("third_order_code"));
                                            third_obj.put("operType", "confirm");
                                            third_obj.put("channel", order_channel);
                                            try {
                                                boolean flag_temp = false;//为true
                                                //5  已确认	, 7	正在取餐 ,8	正在配送,9	已完成
                                                if ("9".equals(status) || "5".equals(status) || "7".equals(status) || "8".equals(status)) {
                                                    flag_temp = true;
                                                    //不处理百度订单是“已完成”状态的情况
//                                                    if ("9".equals(status)) {
//                                                        flag_temp = false;
//                                                    }
                                                } else {
                                                	for (int i = 1; i < 5; i++) {
			                                            if (i == 2) {
			                                                Thread.sleep(4000);
			                                                logger.info("百度->第二次调用确认订单" + ":" + jb.optString("third_order_code"));
			                                            } else if (i == 3) {
			                                                Thread.sleep(9000);
			                                                logger.info("百度->第三次次调用确认订单" + ":" + jb.optString("third_order_code"));
			                                            } else if (i == 4) {
			                                                Thread.sleep(14000);
			                                                logger.info("百度->第四次调用确认订单" + ":" + jb.optString("third_order_code"));
			                                            }
			                                            
	                                                    JSONObject third_return_obj = shopService.orderOper(tenantId, third_obj);
	
	                                                    if (third_return_obj.optString("errno").equalsIgnoreCase("0")) {
	                                                        flag_temp = true;
	                                                        break;
	                                                    }
                                                	}
                                                }

                                                if (flag_temp) {
                                                    sb.setLength(0);
                                                    // 现在没有派单环节 先改为dispatch_time= receive_time_qd
                                                    sb.append("update cc_order_list set third_order_state='5', dispatch_time = '" + jb.optString("receive_time_qd") + "', order_state_desc='"+status2descMap.get(order_state)+"' , order_state = '"+order_state+"' ,bill_num = '" + jb.optString("bill_num") + "', take_time = '" + jb.optString("take_time") + "',receive_time_qd = '"
                                                            + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' ,receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");

                                                    tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                                    this.dao.execute(tenantId, tmpSQL);

                                                    logger.info("百度更新订单[" + jb.optString("order_code") + "]状态:sql->" + sb.toString());
                                                }else{
    		                                    	param.setCode(1001);
    		                                        param.setSuccess(false);
    		                                        param.setMsg("百度订单确认时异常");
    		                                    }

                                            } catch (Exception e) {
                                                e.printStackTrace();

                                            }
                                        } else {
                                            // 修改本地订单状态为取消
                                            // 下发给门店
                                            jb.put("reason_code", "9999902");
                                            orderManagementService.orderCancel(tenantId, jb);
                                            Map<String, Object> obj1 = new HashMap<String, Object>();
                                            obj1.put("order_code", jb.optString("order_code"));
                                            param.setCode(2408);
                                            param.setSuccess(false);
                                            param.setMsg("百度已取消订单");
                                            List<Map<String, Object>> array1 = new ArrayList<Map<String, Object>>();
                                            array1.add(obj1);
                                            param.setData(array1);
                                        }

                                    }
                                    break;
                                case "MT08":
                                	 JSONObject params_mt = new JSONObject();
                                	 params_mt.put("shopId", store_id);
                                	 params_mt.put("channel", order_channel);
                                	 params_mt.put("third_order_id", order_infot_obj.optString("meituan_id"));
                                     JSONObject query_order_status_obj_mt = shopService.orderStatusGet(tenantId, params_mt);
                                     String mt_order_status=query_order_status_obj_mt.optJSONObject("data").optString("status");
                                     
                                     logger.info("美团大订单："+jb.optString("order_code")+"进行确认前的订单状态："+mt_order_status);
                                     
                                	//1 用户已提交订单 2 可推送到APP方平台也可推送到商家 3 商家已收到 4 商家已确认 8已完成 9已取消 
                                     if(!"9".equals(mt_order_status)){
		                                	boolean flag_temp=false;	
		                                	if("1".equals(mt_order_status)||"2".equals(mt_order_status)||"3".equals(mt_order_status)){
		                                		
				                                    //直接调用确认订单
				                                    third_obj.put("shopId", store_id);
				                                    third_obj.put("orderId", order_infot_obj.optString("meituan_id"));
				                                    third_obj.put("operType", "confirm");
				                                    third_obj.put("channel", order_channel);
				                                    try {
				                                        for (int i = 1; i < 5; i++) {
				                                            if (i == 2) {
				                                                Thread.sleep(4000);
				                                                logger.info("美团->第二次调用确认订单" + ":" + order_infot_obj.optString("meituan_id"));
				                                            } else if (i == 3) {
				                                                Thread.sleep(9000);
				                                                logger.info("美团->第三次次调用确认订单" + ":" + order_infot_obj.optString("meituan_id"));
				                                            } else if (i == 4) {
				                                                Thread.sleep(14000);
				                                                logger.info("美团->第四次调用确认订单" + ":" + order_infot_obj.optString("meituan_id"));
				                                            }
				                                            
				                                            boolean result_flag = this.orderOper(tenantId, third_obj, jb, i);
				                                            if (result_flag){
				                                            	flag_temp=true;
				                                                break;
				                                            }
				                                        }
				                                    } catch (Exception e) {
				                                        e.printStackTrace();
				                                    }
		
				                                 
		                                    }else{
		                                    	//表示已被确认过，可以正常处理平台订单状态
		                                    	flag_temp=true;
		                                    }
		                                	
		                                    if(flag_temp){
			                                    sb.setLength(0);
	                                            sb.append("update cc_order_list set third_order_state='5', dispatch_time = '" + jb.optString("receive_time_qd") + "', order_state_desc='"+status2descMap.get(order_state)+"' , order_state = '"+order_state+"' ,bill_num = '" + jb.optString("bill_num") + "', take_time = '" + jb.optString("take_time") + "',receive_time_qd = '"
	                                                    + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' ,receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");
	
	                                            tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
	
	                                            this.dao.execute(tenantId, tmpSQL);
	                                            
	                                            logger.info("美团更新订单[" + jb.optString("order_code") + "]状态:sql->" + sb.toString());
		                                    }else{
		                                    	param.setCode(1001);
		                                        param.setSuccess(false);
		                                        param.setMsg("美团订单确认时异常");
		                                    }
                                     }else{
                                    	 // 修改本地订单状态为取消
                                         // 下发给门店
                                         jb.put("reason_code", "9999902");
                                         orderManagementService.orderCancel(tenantId, jb);
                                         Map<String, Object> obj1 = new HashMap<String, Object>();
                                         obj1.put("order_code", jb.optString("order_code"));
                                         param.setCode(2408);
                                         param.setSuccess(false);
                                         param.setMsg("美团已取消订单");
                                         List<Map<String, Object>> array1 = new ArrayList<Map<String, Object>>();
                                         array1.add(obj1);
                                         param.setData(array1);
                                     }
                                    break;
                                    //changhui 2017-11-9 新美大外卖 start
                                case "MT11":
                                	
                                	JSONObject params_md = new JSONObject();
                                	params_md.put("shopId", store_id);
                                	params_md.put("channel", order_channel);
                                	params_md.put("third_order_id", order_infot_obj.optString("meituan_id"));
                                    JSONObject query_order_status_obj_md = shopService.orderStatusGet(tenantId, params_md);
                                    String md_order_status=query_order_status_obj_md.optJSONObject("data").optString("status");
                                    
                                    logger.info("新美大订单："+jb.optString("order_code")+"进行确认前的订单状态："+md_order_status);
                                    
                                   //1-用户已提交订单；2-可推送到App方平台也可推送到商家；4-商家已确认；6-已配送；8-已完成；9-已取消
                                    if(!"9".equals(md_order_status)){
	                                	boolean flag_temp=false;	
	                                	if("1".equals(md_order_status)||"2".equals(md_order_status)||"3".equals(md_order_status)){
	
			                                    //直接调用确认订单
			                                    third_obj.put("shopId", store_id);
			                                    third_obj.put("orderId", order_infot_obj.optString("meituan_id"));
			                                    third_obj.put("operType", "confirm");
			                                    third_obj.put("channel", order_channel);
			                                    try {
			                                        for (int i = 1; i < 5; i++) {
			                                            if (i == 2) {
			                                                Thread.sleep(4000);
			                                                logger.info("新美大->第二次调用确认订单" + ":" + order_infot_obj.optString("meituan_id"));
			                                            } else if (i == 3) {
			                                                Thread.sleep(9000);
			                                                logger.info("新美大->第三次次调用确认订单" + ":" + order_infot_obj.optString("meituan_id"));
			                                            } else if (i == 4) {
			                                                Thread.sleep(14000);
			                                                logger.info("新美大->第四次调用确认订单" + ":" + order_infot_obj.optString("meituan_id"));
			                                            }
			                                            ;
			                                            boolean result_flag = this.orderOper(tenantId, third_obj, jb, i);
			                                            if (result_flag) {
			                                            	flag_temp=true;
			                                                break;
			                                            }
			                                        }
			                                    } catch (Exception e) {
			                                        e.printStackTrace();
			                                    }
                                    
	                                	}else{
	                                		//表示已被确认过，可以正常处理平台订单状态
	                                    	flag_temp=true;
	                                	}
	                                	
	                                	
	                                	if(flag_temp){
		                                	sb.setLength(0);
	                                        sb.append("update cc_order_list set third_order_state='5', dispatch_time = '" + jb.optString("receive_time_qd") + "', order_state_desc='"+status2descMap.get(order_state)+"' , order_state = '"+order_state+"' ,bill_num = '" + jb.optString("bill_num") + "', take_time = '" + jb.optString("take_time") + "',receive_time_qd = '"
	                                                + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' ,receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");
	
	                                        tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
	
	                                        this.dao.execute(tenantId, tmpSQL);
	                                        
	                                        logger.info("新美大更新订单[" + jb.optString("order_code") + "]状态:sql->" + sb.toString());
	                                	}else{
	                                    	param.setCode(1001);
	                                        param.setSuccess(false);
	                                        param.setMsg("新美大订单确认时异常");
	                                    }
	                                	
	                                }else{
	                                	//订单已取消
	                                	// 修改本地订单状态为取消
                                        // 下发给门店
                                        jb.put("reason_code", "9999902");
                                        orderManagementService.orderCancel(tenantId, jb);
                                        Map<String, Object> obj1 = new HashMap<String, Object>();
                                        obj1.put("order_code", jb.optString("order_code"));
                                        param.setCode(2408);
                                        param.setSuccess(false);
                                        param.setMsg("新美大已取消订单");
                                        List<Map<String, Object>> array1 = new ArrayList<Map<String, Object>>();
                                        array1.add(obj1);
                                        param.setData(array1);
	                                }
                                    
                                    break;
                                    //end
                                case "EL09":
                                	boolean flag_temp=false;
                                	JSONObject params_el = new JSONObject();
                                	params_el.put("shopId", store_id);
                                	params_el.put("channel", order_channel);
                                	params_el.put("third_order_id", jb.optString("third_order_code"));
                                    JSONObject query_order_status_obj_el = shopService.orderStatusGet(tenantId, params_el);
                                    String el_order_status=query_order_status_obj_el.optString("status");
                                	                                    
                                    logger.info("饿了么订单："+jb.optString("order_code")+"进行确认前的订单状态："+el_order_status);
                                	
                                    //invalid 无效订单  valid 已处理的有效订单 refunding 退单处理中  unprocessed 未处理订单 pending 未生效订单 settled 已完成订单
                                	if(!"invalid".equals(el_order_status)&&!"refunding".equals(el_order_status)){
		                                		
		                                	if("pending".equals(el_order_status)||"unprocessed".equals(el_order_status)){
		                                    
				                                    third_obj.put("shopId", store_id);
				                                    third_obj.put("eleme_order_id", jb.optString("third_order_code"));
				                                    third_obj.put("operType", "confirm");
				                                    third_obj.put("status", "2");
				                                    third_obj.put("channel", order_channel);
				                                    try {
				                                        for (int i = 1; i < 5; i++) {
				                                            if (i == 2) {
				                                                Thread.sleep(4000);
				                                                logger.info("饿了么->第二次调用饿了么确认订单" + ":" + jb.optString("third_order_code"));
				                                            } else if (i == 3) {
				                                                Thread.sleep(9000);
				                                                logger.info("饿了么->第三次次调用饿了么确认订单" + ":" + jb.optString("third_order_code"));
				                                            } else if (i == 4) {
				                                                Thread.sleep(14000);
				                                                logger.info("饿了么->第四次调用饿了么确认订单" + ":" + jb.optString("third_order_code"));
				                                            }
				                                            
				                                            JSONObject third_return_obj = shopService.orderOper(tenantId, third_obj);
				                                            logger.info("调用饿了么确认订单返回的信息" + ":" + third_return_obj.toString());
				                                            if (third_return_obj.optString("errno").equalsIgnoreCase("0")) {
				                                            	flag_temp=true;
				                                                break;
				                                            }
				                                        }
				                                    } catch (Exception e) {
				                                        e.printStackTrace();
				                                    }
		                                    
		                                	}else{
		                                		flag_temp=true;
		                                	} 
		                                    
		                                	if(flag_temp){
			                                    sb.setLength(0);
			                                    sb.append("update cc_order_list set third_order_state='5', order_state_desc='"+status2descMap.get(order_state)+"' , dispatch_time = '" + jb.optString("receive_time_qd") + "', order_state = '"+order_state+"' ,bill_num = '" + jb.optString("bill_num") + "', take_time = '" + jb.optString("take_time") + "',receive_time_qd = '"
			                                            + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' ,receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");
			
			                                    tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
			
			                                    this.dao.execute(tenantId, tmpSQL);
			                                    
			                                    logger.info("饿了么更新订单[" + jb.optString("order_code") + "]状态:sql->" + sb.toString());
		                                	}else{
		                                    	param.setCode(1001);
		                                        param.setSuccess(false);
		                                        param.setMsg("饿了么订单确认时异常");
		                                    }
                                    
                                	}else{
	                                	//订单已取消
	                                	// 修改本地订单状态为取消
                                        // 下发给门店
                                        jb.put("reason_code", "9999902");
                                        orderManagementService.orderCancel(tenantId, jb);
                                        Map<String, Object> obj1 = new HashMap<String, Object>();
                                        obj1.put("order_code", jb.optString("order_code"));
                                        param.setCode(2408);
                                        param.setSuccess(false);
                                        param.setMsg("饿了么已取消订单");
                                        List<Map<String, Object>> array1 = new ArrayList<Map<String, Object>>();
                                        array1.add(obj1);
                                        param.setData(array1);
	                                }
                                    
                                    break;
                                default:
                                    sb.setLength(0);
                                    if (StringUtils.isNotEmpty(jb.optString("table_code"))) {
                                        //总部cc_order_list保存门店上传的账号
                                        sb.append("update cc_order_list set order_state = '"+order_state+"' , dispatch_time = '" + jb.optString("receive_time_qd") + "',receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' ,bill_num = '" + jb.optString("bill_num") + "', take_time = '" + jb.optString("take_time") + "',receive_time_qd = '"
                                                + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "',table_code='" + jb.optString("table_code") + "',table_name='" + jb.optString("table_code") + "' where order_code ='" + jb.optString("order_code") + "' ");
                                    } else {
                                        sb.append("update cc_order_list set order_state = '"+order_state+"', dispatch_time = '" + jb.optString("receive_time_qd") + "' ,receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' ,bill_num = '" + jb.optString("bill_num") + "', take_time = '" + jb.optString("take_time") + "',receive_time_qd = '"
                                                + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");
                                    }

                                    tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                    this.dao.execute(tenantId, tmpSQL);

                            }
                            break;
                        // 付款完成
                        case "05":
                            sb.setLength(0);
                            switch (order_channel) {
                                case "BD06":
                                    JSONObject params = new JSONObject();
                                    params.put("shopId", store_id);
                                    params.put("channel", order_channel);
                                    params.put("third_order_id", jb.optString("third_order_code"));
                                    // JSONObject query_order_status_obj =
                                    // shopService.orderStatusGet(tenantId,
                                    // store_id.toString(),
                                    // jb.optString("third_order_code"));
                                    JSONObject query_order_status_obj = shopService.orderStatusGet(tenantId, params);
                                    JSONObject body_obj = query_order_status_obj.optJSONObject("body");
                                    if (body_obj.optString("errno").equalsIgnoreCase("0")) {
                                        JSONObject data_obj = body_obj.optJSONObject("data");
                                        if (!"10".equalsIgnoreCase(data_obj.optString("status"))) {
                                            third_obj.put("shopId", store_id);
                                            third_obj.put("orderId", jb.optString("third_order_code"));
                                            third_obj.put("operType", "confirm");
                                            third_obj.put("channel", order_channel);
                                            try {
                                                JSONObject third_return_obj = shopService.orderOper(tenantId, third_obj);
                                                if (third_return_obj.optString("errno").equalsIgnoreCase("0")) {
                                                    sb.setLength(0);
                                                    sb.append("update cc_order_list set order_state = '05',report_date='" + jb.optString("report_date") + "',payment_state = '03' where order_code ='" + jb.optString("order_code") + "' ");

                                                    tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                                    this.dao.execute(tenantId, tmpSQL);
                                                    flag = orderManagementService.checkIsHaveRepayment(tenantId, jb.optString("order_code"));
                                                    if (!flag) {
                                                        if (jb.containsKey("order_repayment") && jb.optJSONArray("order_repayment") != null && jb.optJSONArray("order_repayment").size() > 0 && jb.optJSONArray("order_repayment").size() > 0) {
                                                            saveOrderRepayment(tenantId, jb.optJSONArray("order_repayment"));
                                                        }
                                                    }

                                                }
                                            } catch (Exception e) {
                                                try {
                                                    JSONObject third_return_obj = shopService.orderOper(tenantId, third_obj);
                                                    if (third_return_obj.optString("errno").equalsIgnoreCase("0")) {
                                                        sb.setLength(0);
                                                        sb.append("update cc_order_list set order_state = '05',payment_state = '03' where order_code ='" + jb.optString("order_code") + "' ");

                                                        tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                                        this.dao.execute(tenantId, tmpSQL);
                                                        flag = orderManagementService.checkIsHaveRepayment(tenantId, jb.optString("order_code"));
                                                        if (!flag) {
                                                            if (jb.containsKey("order_repayment") && jb.optJSONArray("order_repayment") != null && jb.optJSONArray("order_repayment").size() > 0) {
                                                                saveOrderRepayment(tenantId, jb.optJSONArray("order_repayment"));
                                                            }
                                                        }
                                                    }
                                                } catch (Exception e1) {
                                                    // 第二次确认订单请求失败则取消订单
                                                    JSONObject third_cancel_obj = new JSONObject();
                                                    third_cancel_obj.put("shopId", store_id);
                                                    third_cancel_obj.put("orderId", jb.optString("third_order_code"));
                                                    third_cancel_obj.put("operType", "cancel");
                                                    third_cancel_obj.put("type", "-1");
                                                    third_cancel_obj.put("reason", "网络请求失败异常");
                                                    jb.put("reason_code", "9999901");
                                                    try {
                                                        JSONObject third_return_obj = shopService.orderOper(tenantId, third_obj);
                                                        if (third_return_obj.optString("errno").equalsIgnoreCase("0")) {
                                                            orderManagementService.orderCancel(tenantId, jb);
                                                        }
                                                    } catch (Exception e2) {
                                                        orderManagementService.orderCancel(tenantId, jb);
                                                        e2.printStackTrace();

                                                    }

                                                    query_obj.put("order_code", jb.optString("order_code"));
                                                }

                                            }
                                        } else {
                                            // 修改本地订单状态为取消
                                            // 下发给门店
                                            jb.put("reason_code", "9999902");
                                            orderManagementService.orderCancel(tenantId, jb);
                                            Map<String, Object> obj1 = new HashMap<String, Object>();
                                            obj1.put("order_code", jb.optString("order_code"));
                                            param.setCode(2408);
                                            param.setMsg("百度取消订单");
                                            List<Map<String, Object>> array1 = new ArrayList<Map<String, Object>>();
                                            array1.add(obj1);
                                            param.setData(array1);
                                        }

                                    }
                                    break;
                                default:
                                    sb.setLength(0);
                                    sb.append("update cc_order_list set receive_time = '" + jb.optString("receive_time") + "',report_date='" + jb.optString("report_date") + "',response_time='" + hq_service_time + "' , take_time = '" + jb.optString("take_time") + "',receive_time_qd = '" + jb.optString("receive_time_qd") + "',response_time_qd='"
                                            + hq_service_time + "', order_state = '05',payment_state = '03' where order_code ='" + jb.optString("order_code") + "' ");

                                    tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                    this.dao.execute(tenantId, tmpSQL);

                            }
                            flag = orderManagementService.checkIsHaveRepayment(tenantId, jb.optString("order_code"));
                            if (!flag) {
                                if (jb.containsKey("order_repayment") && jb.optJSONArray("order_repayment") != null && jb.optJSONArray("order_repayment").size() > 0) {
                                    saveOrderRepayment(tenantId, jb.optJSONArray("order_repayment"));
                                }
                            }
                            break;
                        // 已派单
                        case "06":
                            sb.setLength(0);
                            switch (order_channel) {
                                case "BD06":
                                    sb.setLength(0);
                                    sb.append("update cc_order_list set third_order_state='7', order_state_desc='正在取餐' , order_state = '06' , dispatch_time = '" + jb.optString("dispatch_time") + "',receive_time_dispatch = '" + jb.optString("receive_time_dispatch") + "' ,response_time_dispatch='"
                                            + hq_service_time + "' , take_time = '" + jb.optString("take_time") + "',receive_time_qd = '" + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' ,receive_time = '" + jb.optString("receive_time") + "',response_time='"
                                            + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");
                                    break;
                                default:
                                    sb.setLength(0);
                                    sb.append("update cc_order_list set  receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' , take_time = '" + jb.optString("take_time") + "',receive_time_qd = '" + jb.optString("receive_time_qd") + "',response_time_qd='"
                                            + hq_service_time + "', order_state = '06' , dispatch_time = '" + jb.optString("dispatch_time") + "',receive_time_dispatch = '" + jb.optString("receive_time_dispatch") + "'  where order_code ='" + jb.optString("order_code") + "' ");
                            }
                            flag = orderManagementService.checkIsHaveRepayment(tenantId, jb.optString("order_code"));
                            if (!flag) {
                                if (jb.containsKey("order_repayment") && jb.optJSONArray("order_repayment") != null && jb.optJSONArray("order_repayment").size() > 0) {
                                    saveOrderRepayment(tenantId, jb.optJSONArray("order_repayment"));
                                }
                            }

                            tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                            this.dao.execute(tenantId, tmpSQL);
                            break;
                        // 配送
                        case "07":
                            sb.setLength(0);
                            switch (order_channel) {
                                case "BD06":
                                    sb.setLength(0);
                                    sb.append("update cc_order_list set third_order_state='8', order_state_desc='正在配送' , order_state = '07' , distribution_time = '" + jb.get("distribution_time") + "' , take_time = '" + jb.optString("take_time") + "',receive_time_qd = '" + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' ,receive_time = '"
                                            + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");
                                    break;
                                default:
                                    sb.setLength(0);
                                    sb.append("update cc_order_list set receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' , take_time = '" + jb.optString("take_time") + "',receive_time_qd = '" + jb.optString("receive_time_qd") + "',response_time_qd='"
                                            + hq_service_time + "' ,order_state = '07' , distribution_time = '" + jb.get("distribution_time") + "' where order_code ='"
                                            + jb.optString("order_code") + "' ");
                            }
                            flag = orderManagementService.checkIsHaveRepayment(tenantId, jb.optString("order_code"));
                            if (!flag) {
                                if (jb.containsKey("order_repayment") && jb.optJSONArray("order_repayment") != null && jb.optJSONArray("order_repayment").size() > 0) {
                                    saveOrderRepayment(tenantId, jb.optJSONArray("order_repayment"));
                                }
                            }

                            tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                            this.dao.execute(tenantId, tmpSQL);
                            break;
                            
                        case "12":
                        // 完成
                        case "10":
                            sb.setLength(0);
                            switch (order_channel) {
                                case "BD06":
                                    third_obj.put("shopId", store_id);
                                    third_obj.put("orderId", jb.optString("third_order_code"));
                                    third_obj.put("operType", "complete");
                                    third_obj.put("channel", order_channel);
                                  
                                    if (!"10".equalsIgnoreCase(order_infot_obj.optString("order_state"))&&!"12".equalsIgnoreCase(order_infot_obj.optString("order_state"))&&!"02".equalsIgnoreCase(order_infot_obj.optString("order_state"))) {
                                        
                                        sb.setLength(0);
                                        sb.append("update cc_order_list  set third_order_state='9', order_state_desc='"+status2descMap.get(order_state)+"' , order_state = '"+order_state+"' ,payment_state = '03' ,bill_num = '" + jb.optString("bill_num") + "',finish_time = '" + jb.optString("finish_time") + "' , distribution_time = '" + jb.optString("distribution_time") + "', take_time = '" + jb.optString("take_time")
                                                + "',receive_time_qd = '" + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' ,receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' where order_code ='" + jb.optString("order_code")
                                                + "' ");

                                        tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                        this.dao.execute(tenantId, tmpSQL);
                                        // }
                                    }
                                    break;
                                default:
                                    sb.setLength(0);
                                    //2017-08-29美团渠道后付：门店取单后--》总部取消订单---》总部订单状态变成已完成【由于门店收到取消订单信息后回传了一个已完成状态】
                                    if (!order_infot_obj.optString("order_state").equals("08")) {
                                    	if(!"10".equalsIgnoreCase(order_infot_obj.optString("order_state"))&&!"12".equalsIgnoreCase(order_infot_obj.optString("order_state"))&&!"02".equalsIgnoreCase(order_infot_obj.optString("order_state"))){
	                                        sb.append("update cc_order_list set receive_time = '" + jb.optString("receive_time") + "',bill_num = '" + jb.optString("bill_num") + "',response_time='" + hq_service_time + "' , take_time = '" + jb.optString("take_time") + "',receive_time_qd = '" + jb.optString("receive_time_qd") + "',response_time_qd='"
	                                                + hq_service_time + "' ,distribution_time = '" + jb.optString("distribution_time") + "',receive_time_distribution = '" + jb.optString("receive_time_distribution") + "' ,response_time_distribution='" + hq_service_time + "',order_state = '"+order_state+"',order_state_desc='"+status2descMap.get(order_state)+"' ,payment_state = '03', finish_time = '"
	                                                + jb.get("finish_time") + "',receive_time_finish = '" + jb.get("receive_time_finish") + "',response_time_finish='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' and order_state<>'08'");
	
	                                        tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
	
	                                        this.dao.execute(tenantId, tmpSQL);
                                    	}
                                    }
                            }
                            flag = orderManagementService.checkIsHaveRepayment(tenantId, jb.optString("order_code"));
                            if (!flag) {
                                if (jb.containsKey("order_repayment") && jb.optJSONArray("order_repayment") != null && jb.optJSONArray("order_repayment").size() > 0) {
                                    saveOrderRepayment(tenantId, jb.optJSONArray("order_repayment"));
                                }
                            }
                            break;
                        // 取消状态为服务员取消
                        case "08":
                     
                            sb.setLength(0);
                            sb.append("update cc_order_list set order_state = '08',cancel_type = '0',cancellation_time = '" + jb.optString("cancellation_time") + "',payment_state='04'  where order_code ='" + jb.optString("order_code") + "'");

                            
                            tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
                            this.dao.execute(tenantId, tmpSQL);
                           break;
                    }
                    long endTimestamp = System.currentTimeMillis();

                    logger.info("orderManager执行update执行时间差：" + (endTimestamp - startTimestamp));

                    this.pushMsgToAndroid(jb, query_obj);
                }else if("08".equals(order_infot_obj.optString("order_state"))){
                	// 修改本地订单状态为取消
                    // 下发给门店
                    jb.put("reason_code", "9999902");
                    jb.put("third_order_code","");
                    orderManagementService.orderCancel(tenantId, jb);
                    Map<String, Object> obj1 = new HashMap<String, Object>();
                    obj1.put("order_code", jb.optString("order_code"));
                    param.setCode(2408);
                    param.setMsg(order_channel+"已取消订单");
                    List<Map<String, Object>> array1 = new ArrayList<Map<String, Object>>();
                    array1.add(obj1);
                    param.setData(array1);
                }
                break;
            // 门店取消订单
            case cancle:
                sb.setLength(0);
                JSONObject third_return_obj = new JSONObject();
                List<JSONObject> baidu_order_reason_list = new ArrayList<JSONObject>();
                List<JSONObject> order_reason_list = new ArrayList<JSONObject>();
                JSONObject order_reason_baidu = null;
                if (jb.containsKey("order_reason")) {
                    baidu_order_reason_list = jb.optJSONArray("order_reason");
                    order_reason_baidu = baidu_order_reason_list.get(0);
                    order_reason_list = jb.optJSONArray("order_reason");
                }
                List<JSONObject> unusual_reason_result_list = new ArrayList<JSONObject>();
                if (order_reason_baidu != null) {
                    sb.append("SELECT a.reason_name from hq_unusual_reason a where a.id=" + order_reason_baidu.optInt("reason_type") + "");
                    unusual_reason_result_list = this.dao.query4Json(tenantId, sb.toString());
                }
                if (unusual_reason_result_list.size() > 0) {
                    third_obj.put("reason", unusual_reason_result_list.get(0).optString("reason_name"));
                } else {
                    third_obj.put("reason", "门店取消订单");
                }
                switch (order_channel) {
                    case "BD06":
                        sb.setLength(0);
                        third_obj.put("shopId", store_id);
                        third_obj.put("orderId", jb.optString("third_order_code"));
                        third_obj.put("operType", "cancel");
                        third_obj.put("type", "-1");
                        third_obj.put("channel", order_channel);
                        try {
                            third_return_obj = shopService.orderOper(tenantId, third_obj);
                            logger.info("总部请求百度取消订单返回的消息:" + third_return_obj.toString());
                            if (third_return_obj.optString("errno").equalsIgnoreCase("0")) {
                                this.storeCancelOrder(jb, order_reason_list);
                            } else {
                                param.setCode(third_return_obj.optInt("errno"));
                                param.setMsg(third_return_obj.optString("error"));
                                param.setSuccess(false);
                            }
                        } catch (Exception e) {
                            System.out.println(third_return_obj.toString());
                            e.printStackTrace();
                        }
                        break;
                    case "MT08":
                        third_obj.put("shopId", store_id);
                        third_obj.put("orderId", order_infot_obj.optString("meituan_id"));
                        third_obj.put("operType", "cancel");
                        third_obj.put("channel", order_channel);
                        third_obj.put("type", "2007");
                        try {
                            logger.info("总部请求美团取消订单传的消息:" + third_obj.toString());
                            third_return_obj = shopService.orderOper(tenantId, third_obj);
                            logger.info("总部请求美团取消订单返回的消息:" + third_return_obj.toString());
                            if (third_return_obj.optString("errno").equalsIgnoreCase("0")) {
                                this.storeCancelOrder(jb, order_reason_list);
                            } else {
                                param.setCode(third_return_obj.optInt("errno"));
                                param.setMsg(third_return_obj.optString("error"));
                                param.setSuccess(false);
                            }
                        } catch (Exception e) {
                            System.out.println(third_return_obj.toString());
                            e.printStackTrace();
                        }
                        break;
                        //changhui 2017-11-9 新美大外卖 start
                    case "MT11":
                        third_obj.put("shopId", store_id);
                        third_obj.put("orderId", order_infot_obj.optString("meituan_id"));
                        third_obj.put("operType", "cancel");
                        third_obj.put("channel", order_channel);
                        third_obj.put("type", "2007");
                        try {
                            logger.info("总部请求新美大外卖取消订单传的消息:" + third_obj.toString());
                            third_return_obj = shopService.orderOper(tenantId, third_obj);
                            logger.info("总部请求新美大外卖取消订单返回的消息:" + third_return_obj.toString());
                            if (third_return_obj.optString("errno").equalsIgnoreCase("0")) {
                                this.storeCancelOrder(jb, order_reason_list);
                            } else {
                                param.setCode(third_return_obj.optInt("errno"));
                                param.setMsg(third_return_obj.optString("error"));
                                param.setSuccess(false);
                            }
                        } catch (Exception e) {
                            System.out.println(third_return_obj.toString());
                            e.printStackTrace();
                        }
                        break;
                        //end
                    case "CC04":
                        if ("08".equalsIgnoreCase(order_state)) {
                            this.storeCancelOrder(jb, order_reason_list);
                        }
                        break;
                    case "WX02":
                        storeCancelWx02Wx10Order(jb, order_infot_obj, order_reason_list);
                        break;
                    case "WM10":
                        storeCancelWx02Wx10Order(jb, order_infot_obj, order_reason_list);
                        break;
                    case "EL09":
                        third_obj.put("shopId", store_id);
                        third_obj.put("eleme_order_id", jb.optString("third_order_code"));
                        third_obj.put("operType", "cancel");
                        third_obj.put("type", "-1");
                        third_obj.put("status", "-1");
                        third_obj.put("channel", order_channel);
                        try {
                            third_return_obj = shopService.orderOper(tenantId, third_obj);
                            logger.info("总部请求百度取消订单返回的消息:" + third_return_obj.toString());
                            if (third_return_obj.optString("errno").equalsIgnoreCase("0")) {
                                this.storeCancelOrder(jb, order_reason_list);
                            } else {
                                param.setCode(third_return_obj.optInt("errno"));
                                param.setMsg(third_return_obj.optString("error"));
                                param.setSuccess(false);
                            }
                        } catch (Exception e) {
                            System.out.println(third_return_obj.toString());
                            e.printStackTrace();
                        }
                        break;
                }
                this.pushMsgToAndroid(jb, query_obj);
                break;
            // 订单新增（点菜）
            case add:
                orderSave(orderDataAssembly(param, jb));
                break;
            // 订单加菜
            case updatedish:
                orderUpdateDish(orderDataAssembly(param, jb));
                break;
            // 订单查询
            case query:
                // 订单查询
                orderQuery(param);
                break;
            // 订单状态查询
            case order_state_query:
                orderStateQuery(param);
                break;
            // 订单支付成功
            case scan:
                if (jb.containsKey("order_repayment")) {
                    String order_code = "";
                    // 保存到订单交易付款方式流水
                    JSONArray order_repayment_array = jb.optJSONArray("order_repayment");
                    if (order_repayment_array.size() > 0) {
                        List<JSONObject> order_repayment_list = new ArrayList<JSONObject>();
                        for (Object order_repayment_obj : order_repayment_array) {
                            ((JSONObject) order_repayment_obj).put("tenancy_id", param.getTenancy_id());
                            order_code = ((JSONObject) order_repayment_obj).optString("order_code");

                            CcPartitionUtils.lackInsertParam(tenantId, (JSONObject) order_repayment_obj);

                            order_repayment_list.add(((JSONObject) order_repayment_obj));
                        }
                        if (order_repayment_list.size() > 0) {
                            this.dao.insertBatchIgnorCase(param.getTenancy_id(), "cc_order_repayment", order_repayment_list);
                        }
                    }
                    // 修改订单支付状态
                    String update_order_pay_status_sql = "update cc_order_list set payment_state='02' where order_code='" + order_code + "' ";

                    tmpSQL = CcPartitionUtils.makeSQL(tenantId, update_order_pay_status_sql, order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);

                    this.dao.execute(tenantId, tmpSQL);
                }
                break;
            // 订单支付超时
            case paytimeout:
                if (jb.containsKey("order_code")) {
                    // 修改订单支付状态
                    String update_order_pay_status_sql = "update cc_order_list set payment_state='05' where order_code='" + jb.optString("order_code") + "' ";

                    tmpSQL = CcPartitionUtils.makeSQL(tenantId, update_order_pay_status_sql, jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                    this.dao.execute(tenantId, tmpSQL);
                }
                break;
            // 未支付之前 订单修改
            // 支付成功后不能修改订单 只能取消 看门店对订单处理到那个环节
            default:
                param.setCode(Constant.CODE_PARAM_FAILURE);
                param.setMsg(Constant.CODE_PARAM_FAILURE_MSG);
                param.setSuccess(false);
                break;
        }

        return result;
    }

    //推送订单状态到安卓儿端
    public void pushMsgToAndroid(JSONObject store_cancel_obj, JSONObject query_obj) throws Exception {
        JSONObject push_msg_andro_jb = new JSONObject();
        //订单状态发生变化推送 安卓儿端
        JSONObject order_infot_obj_new = new JSONObject();
        JSONObject result_obj_new = orderManagementService.loadOrderingList(store_cancel_obj.optString("tenantId"), query_obj);
        List<JSONObject> order_list_new = result_obj_new.optJSONArray("rows");
        if (order_list_new.size() > 0) {
            order_infot_obj_new = order_list_new.get(0);
        }
        push_msg_andro_jb.put("order_code", store_cancel_obj.optString("order_code"));
        push_msg_andro_jb.put("order_state", store_cancel_obj.optString("order_state"));
        push_msg_andro_jb.put("payment_state", order_infot_obj_new.optString("payment_state"));
        push_msg_andro_jb.put("single_time", order_infot_obj_new.optString("single_time"));
        push_msg_andro_jb.put("order_state_name", order_infot_obj_new.optString("order_state_name"));
        JSONObject order_total_result_obj = this.ocDao.orderTotal(store_cancel_obj.optString("tenantId"), store_cancel_obj);
        if (order_total_result_obj != null) {
            push_msg_andro_jb.put("today_order_total", order_total_result_obj.optInt("today_order_total"));
            push_msg_andro_jb.put("today_order_total_cancle", order_total_result_obj.optInt("today_order_total_cancle"));
            push_msg_andro_jb.put("today_order_total_effective", order_total_result_obj.optInt("today_order_total_effective"));
            push_msg_andro_jb.put("today_order_total_wm02", order_total_result_obj.optInt("today_order_total_wm02"));
            push_msg_andro_jb.put("today_order_total_zt01", order_total_result_obj.optInt("today_order_total_zt01"));

        }
        logger.info("电话外卖推送的订单信息 订单状态变更" + push_msg_andro_jb.toString());
        AndroidPushMsgToSingleDevice.pushOc(store_cancel_obj.optString("tenantId"), 2, dao, push_msg_andro_jb, store_cancel_obj.optInt("store_id"));
    }

    ;

    @Override
    public void orderSave(Data param) throws SystemException, Exception {
        try {
            List<String> listStringAdd = new ArrayList<String>();
            StringBuilder sql = new StringBuilder();
            Object dic = null;
            String order_code = "";
            JSONObject jb = null;
            if (param.getData() != null) {
                jb = new JSONObject();
                @SuppressWarnings("unchecked")
                Map<String, Object> maps = (Map<String, Object>) param.getData().get(0);
                Set<String> set = maps.keySet();
                for (String key : set) {
                    jb.put(key, maps.get(key));
                }
            }
            logger.info("总部收到的下单信息:" + jb.toString());
            JSONObject order_list_object = jb.optJSONObject("order_list");
            JSONObject customer_addresslist_object = jb.optJSONObject("customer_addresslist");
            JSONObject code_object = new JSONObject();
            code_object.element("store_id", param.getStore_id());
            long creditCurrentTime = (System.currentTimeMillis());
            Date creditDate = new Date(creditCurrentTime);
            SimpleDateFormat creditDf = new SimpleDateFormat("yyyyMMdd");
            code_object.put("busi_date", creditDf.format(creditDate));
            code_object.put("dynamic", order_list_object.optString("chanel"));
            order_code = codeService.getCode(param.getTenancy_id(), Code.CC_ORDER_CODE, code_object);// 调用统一接口来实现
            if (!order_list_object.containsKey("send_time") && !order_list_object.optString("chanel").equals("WX02")) {
                order_list_object.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
            }
    /*		String hsw=order_code.substring(order_code.length()-4,order_code.length());
			String qb=order_code.substring(0,order_code.length()-4);
			String zh=qb+(CommonUtil.zeroFill(500+Integer.valueOf(hsw)));
			order_code=zh;*/
            //保存取餐号 W+订单号的后两位
            // 根据订餐人电话查询会员信息
            String hlw = order_code.substring(order_code.length() - 2, order_code.length());
            order_list_object.put("take_meal_number", "W" + hlw);
            //保存商家实收
            order_list_object.put("shop_fee", order_list_object.optString("actual_pay"));
            order_list_object.put("report_date", "'" + DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10) + "'");
            sql.setLength(0);
            sql.append("select * from crm_customer_info a where 1=1");
            if (order_list_object.containsKey("open_id")) {
                sql.append(" and mobil in (select b.mobile_num from wx_member b where b.openid='" + order_list_object.optString("open_id") + "' )");
            } else {
                sql.append(" and mobil ='" + order_list_object.optString("order_phone") + "' ");
            }

            List<JSONObject> customer_list = this.dao.query4Json(param.getTenancy_id(), sql.toString());
            JSONObject customer_obj = new JSONObject();
            if (customer_list != null && customer_list.size() > 0) {
                customer_obj = customer_list.get(0);
                order_list_object.put("customer_id", customer_obj.optString("id"));
                order_list_object.put("order_name", customer_obj.optString("name"));
                order_list_object.put("order_phone", customer_obj.optString("mobil"));
                if (!order_list_object.optString("chanel").equals("WM10")) {
                    order_list_object.put("consigner", customer_obj.optString("name"));
                    order_list_object.put("consigner_phone", customer_obj.optString("mobil"));
                }
            } else {
                String wx_member_sql = "select b.* from wx_member b where b.openid='" + order_list_object.optString("open_id") + "'";
                List<JSONObject> wx_member_list = this.dao.query4Json(param.getTenancy_id(), wx_member_sql.toString());
                if (wx_member_list != null && wx_member_list.size() > 0) {
                    customer_obj = wx_member_list.get(0);
                    order_list_object.put("order_name", customer_obj.optString("nickname"));
                    order_list_object.put("consigner", customer_obj.optString("nickname"));
                }
                if (customer_addresslist_object != null) {
                    JSONObject customer_info_obj = new JSONObject();
                    customer_info_obj.put("tenancy_id", param.getTenancy_id());
                    customer_info_obj.put("name", customer_addresslist_object.optString("consignee_phone"));
                    customer_info_obj.put("sex", customer_addresslist_object.optString("sex"));
                    customer_info_obj.put("mobil", customer_addresslist_object.optString("consignee_phone"));
                    customer_info_obj.put("add_chanel", customer_addresslist_object.optString("add_chanel"));
                    StringBuilder sb = new StringBuilder();
                    sb.setLength(0);
                    sb.append("select para_value from sys_parameter where para_code = 'xzchymrdj' and store_id=0 and valid_state='" + Constant.VALID_STATE_TRUE + "'");
                    List<JSONObject> listParaCodeList = this.dao.query4Json(param.getTenancy_id(), sb.toString());
                    if (listParaCodeList != null && listParaCodeList.size() > 0) {
                        customer_info_obj.put("level", listParaCodeList.get(0).optInt("para_value"));
                    }
                    String customer_id = this.dao.insertIgnorCase(param.getTenancy_id(), "crm_customer_info", customer_info_obj).toString();
                    customer_info_obj.put("customer_id", customer_id);
                    customer_addresslist_object.put("customer_id", customer_id);
                    customer_addresslist_object.put("tenancy_id", param.getTenancy_id());
                    String customer_address_id = this.dao.insertIgnorCase(param.getTenancy_id(), "crm_customer_address", customer_addresslist_object).toString();
                    customer_addresslist_object.put("customer_address_id", customer_address_id);
                    order_list_object.put("customer_address_id", customer_addresslist_object.optString("customer_address_id"));
                    order_list_object.put("customer_id", customer_addresslist_object.optString("customer_id"));
                }

            }
            // 根据订单类型决定怎么处理参数
            String orderType = order_list_object.optString("order_type");
            if (orderType.equals("WM02") && order_list_object.optString("chanel").equals("WX02")) {
                StringBuffer addressSb = new StringBuffer();
                addressSb.append("select * from crm_customer_address cca where cca.id = " + order_list_object.optString("address_id"));
                List<JSONObject> addressDetailsList = this.dao.query4Json(param.getTenancy_id(), addressSb.toString());
                if (!addressDetailsList.isEmpty()) {
                    JSONObject addressDetails = addressDetailsList.get(0);
                    // 获取送餐地址的省市区
                    order_list_object.put("province", addressDetails.optString("province"));
                    order_list_object.put("city", addressDetails.optString("city"));
                    order_list_object.put("area", addressDetails.optString("area"));
                    // 获取外卖收货人信息
                    order_list_object.put("consigner", addressDetails.optString("consignee"));
                    order_list_object.put("consigner_phone", addressDetails.optString("consignee_phone"));
                    order_list_object.put("address", addressDetails.optString("address"));
                }
                order_list_object.put("meal_costs", order_list_object.optString("meal_costs"));
            }
            order_list_object.put("store_id", param.getStore_id());
            order_list_object.put("tenancy_id", param.getTenancy_id());
            order_list_object.put("order_code", order_code);
            order_list_object.put("order_state", "01");
            order_list_object.put("shop_real_amount", order_list_object.optDouble("actual_pay", 0.0));
            //微信点餐/外卖、电话外卖保存菜品总计金额
            order_list_object.put("product_org_total_fee", order_list_object.optDouble("actual_pay", 0.0));
            order_list_object.put("single_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
            order_list_object.put("payment_state", "01");
          /*  if (order_list_object.optString("chanel").equals(TakeoutConstant.ORDER_CHANNEL)) {
                order_list_object.put("chanel", TakeoutConstant.ORDER_CHANNEL);
            } else {
                order_list_object.put("chanel", "WX02");
            }*/
            if (order_list_object.optString("chanel").equals("CC04")) {
                order_list_object.put("delivery_party", "2");
            }

            CcPartitionUtils.lackInsertParam(param.getTenancy_id(), order_list_object);

            // 保存到预订单表里
            dic = this.dao.insertIgnorCase(param.getTenancy_id(), "cc_order_list", order_list_object);
            // 保存到预点菜明细
            JSONArray order_item_array = jb.optJSONArray("order_item");
            List<JSONObject> order_item_list = new ArrayList<JSONObject>();
            // 获取菜谱id
            String menuSql = "select * from hq_item_menu where id in(select item_menu_id from hq_item_menu_organ  where store_id=" + param.getStore_id() + ") and valid_state='1'";
            List<JSONObject> menuList = dao.query4Json(param.getTenancy_id(), menuSql);
            Integer item_menu_id = null;
            if (menuList != null && menuList.size() > 0) {
                item_menu_id = menuList.get(0).optInt("id");
            }
            int index = 0;
            for (Object order_item_obj : order_item_array) {
                String sql1 = null;
                ((JSONObject) order_item_obj).put("order_code", order_code);
                ((JSONObject) order_item_obj).put("tenancy_id", param.getTenancy_id());
                ((JSONObject) order_item_obj).put("item_menu_id", item_menu_id);
                ((JSONObject) order_item_obj).put("store_id", param.getStore_id());
                // product_fee 菜品小计=菜品数量X菜品金额+菜品做法
                JSONObject item = order_item_array.getJSONObject(index);
                double price = item.optDouble("price");
                index++;
                if (jb.containsKey("order_item_taste")) {
                    // 保存到预点菜口味明细
                    JSONArray order_item_taste_array = jb.optJSONArray("order_item_taste");
                    if (order_item_taste_array.size() > 0) {
                        for (int i = 0; i < order_item_taste_array.size(); i++) {
                            JSONObject taste = order_item_taste_array.getJSONObject(i);
                            if (taste.optString("item_id").equals(item.optString("item_id")) && taste.optInt("group_index") == item.optInt("group_index")) {
                                price += taste.optDouble("proportion_money", 0.0);
                            }
                        }
                    }
                }
                double product_fee = price * item.optInt("number");
                ((JSONObject) order_item_obj).put("product_fee", product_fee);
                if (order_list_object.optString("chanel").equals("CC04")) {
                    ((JSONObject) order_item_obj).put("real_amount", product_fee);
                }
                ((JSONObject) order_item_obj).put("report_date", "'" + DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10) + "'");
                order_item_list.add(((JSONObject) order_item_obj));

                sql1 = "insert into cc_order_item(tenancy_id,group_index,order_code,item_id,unit_id,number,price,item_menu_id,item_name,product_fee,store_id,real_amount,method_money,discount_mode_id,report_date,discount_price,discount_amount,discountr_amount) values('" + param.getTenancy_id() + "','" + ((JSONObject) order_item_obj).optInt("group_index") + "','" + order_code + "','" + ((JSONObject) order_item_obj).optInt("item_id") + "','" + ((JSONObject) order_item_obj).optInt("unit_id") + "','" + ((JSONObject) order_item_obj).optInt("number") + "','" + ((JSONObject) order_item_obj).optDouble("price") + "'," + item_menu_id + ",'" + ((JSONObject) order_item_obj).optString("item_name") + "'," + ((JSONObject) order_item_obj).optDouble("price") * ((JSONObject) order_item_obj).optInt("number") + "," + param.getStore_id() + "," + ((JSONObject) order_item_obj).optDouble("real_amount", 0.0) + "," + ((JSONObject) order_item_obj).optDouble("method_money", 0.0) + "," + ((JSONObject) order_item_obj).optInt("discount_mode_id", 0) + "," + ((JSONObject) order_item_obj).optString("report_date") + "," + ((JSONObject) order_item_obj).optDouble("discount_price", 0.0) + "," + ((JSONObject) order_item_obj).optDouble("discount_amount", 0.0) + "," + ((JSONObject) order_item_obj).optDouble("discountr_amount", 0.0) + ")";

                String tmpSql = CcPartitionUtils.lackInsertSQL(param.getTenancy_id(), sql1, order_code);

                listStringAdd.add(tmpSql);
            }
            if (listStringAdd.size() > 0) {
                try {
                    String[] sqlArray = null;
                    int size = listStringAdd.size();
                    sqlArray = listStringAdd.toArray(new String[size]);
                    dao.getJdbcTemplate(param.getTenancy_id()).batchUpdate(sqlArray);
                } catch (Exception e2) {
                    e2.printStackTrace();
                }
            }
			/*if (order_item_list.size() > 0)
			{
				try
				{
					this.dao.insertBatchIgnorCase(param.getTenancy_id(), "cc_order_item", order_item_list);
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}

			}*/

            if (jb.containsKey("order_item_taste")) {
                // 保存到预点菜口味明细
                JSONArray order_item_taste_array = jb.optJSONArray("order_item_taste");
                if (order_item_taste_array.size() > 0) {
                    List<JSONObject> order_item_taste_list = new ArrayList<JSONObject>();
                    for (Object order_item_taste_obj : order_item_taste_array) {
                        ((JSONObject) order_item_taste_obj).put("tenancy_id", param.getTenancy_id());
                        ((JSONObject) order_item_taste_obj).put("order_code", order_code);

                        order_item_taste_list.add(((JSONObject) order_item_taste_obj));
                    }
                    if (order_item_taste_list.size() > 0) {
                        this.dao.insertBatchIgnorCase(param.getTenancy_id(), "cc_order_item_taste", order_item_taste_list);
                    }
                }
            }

            // 保存到预点菜套餐明细
            if (jb.containsKey("order_item_details")) {
                JSONArray order_item_details_array = jb.optJSONArray("order_item_details");
                if (order_item_details_array.size() > 0) {
                    List<JSONObject> order_item_details_list = new ArrayList<JSONObject>();
                    for (Object order_item_details_obj : order_item_details_array) {
                        ((JSONObject) order_item_details_obj).put("tenancy_id", param.getTenancy_id());
                        ((JSONObject) order_item_details_obj).put("order_code", order_code);
                        //保存报表日期
                        ((JSONObject) order_item_details_obj).put("report_date", "'" + DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10) + "'");

                        CcPartitionUtils.lackInsertParam(param.getTenancy_id(), ((JSONObject) order_item_details_obj));

                        order_item_details_list.add(((JSONObject) order_item_details_obj));
                    }
                    if (order_item_details_list.size() > 0) {
                        this.dao.insertBatchIgnorCase(param.getTenancy_id(), "cc_order_item_details", order_item_details_list);
                    }
                }
            }
            if (jb.containsKey("order_repayment")) {
                // 保存到订单交易付款方式流水
                JSONArray order_repayment_array = jb.optJSONArray("order_repayment");
                if (order_repayment_array.size() > 0) {
                    List<JSONObject> order_repayment_list = new ArrayList<JSONObject>();
                    for (Object order_repayment_obj : order_repayment_array) {
                        ((JSONObject) order_repayment_obj).put("tenancy_id", param.getTenancy_id());
                        ((JSONObject) order_repayment_obj).put("order_code", order_code);
                        ((JSONObject) order_repayment_obj).put("report_date", "'" + DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10) + "'");

                        CcPartitionUtils.lackInsertParam(param.getTenancy_id(), ((JSONObject) order_repayment_obj));

                        order_repayment_list.add(((JSONObject) order_repayment_obj));
                    }
                    if (order_repayment_list.size() > 0) {
                        this.dao.insertBatchIgnorCase(param.getTenancy_id(), "cc_order_repayment", order_repayment_list);
                    }
                }
            }
	/*		Map<String, Object> obj1 = new HashMap<String, Object>();
			obj1.put("order_code", order_code);
			List<Map<String, Object>> array1 = new ArrayList<Map<String, Object>>();
			array1.add(obj1);
			param.setData(array1);*/
            JSONObject order_code_obj = new JSONObject();
            order_code_obj.put("order_code", order_code);
            List<JSONObject> order_code_obj_list = new ArrayList<JSONObject>();
            order_code_obj_list.add(order_code_obj);
            param.setData(order_code_obj_list);
            if (order_list_object.optString("chanel").equals("CC04")) {
                orderIssued(param);
            }
            //getAddressBean(order_list_object);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }

    @Override
    public void orderUpdateDish(Data param) throws SystemException, Exception {
        try {
            JSONObject jb = null;
            if (param.getData() != null) {
                jb = new JSONObject();
                @SuppressWarnings("unchecked")
                Map<String, Object> maps = (Map<String, Object>) param.getData().get(0);
                Set<String> set = maps.keySet();
                for (String key : set) {
                    jb.put(key, maps.get(key));
                }

            }
            logger.info("总部收到的加载信息:" + jb.toString());
            JSONObject order_list_object = jb.optJSONObject("order_list");
            if (order_list_object.containsKey("order_code") && !("").equals(order_list_object.optString("order_code"))) {
                String order_code_update = order_list_object.optString("order_code");

                order_list_object.optDouble("meal_costs", 0);
                String update_order_list_sql = "update cc_order_list set shop_real_amount=" + order_list_object.optDouble("shop_real_amount", 0.0) + ",product_org_total_fee=" + order_list_object.optDouble("product_org_total_fee", 0.0) + ",discount_mode_id=" + order_list_object.optInt("discount_mode_id") + ",remark='" + order_list_object.optString("remark") + "', total_money='" + order_list_object.optString("total_money") + "',actual_pay='" + order_list_object.optString("actual_pay") + "',shop_fee='" + order_list_object.optString("actual_pay") + "',meal_costs=" + order_list_object.optDouble("meal_costs", 0) + " where order_code='" + order_code_update + "'";

                String tmpSQL = CcPartitionUtils.makeSQL(param.getTenancy_id(), update_order_list_sql, order_code_update, CcPartitionUtils.TYPE_ORDERCODE_TZX);

                this.dao.execute(param.getTenancy_id(), tmpSQL);
                JSONArray order_item_array = jb.optJSONArray("order_item");
                List<JSONObject> order_item_list = new ArrayList<JSONObject>();
                // 获取菜谱id
                String menuSql = "select * from hq_item_menu where id in(select item_menu_id from hq_item_menu_organ  where store_id=" + param.getStore_id() + ") and valid_state='1'";
                List<JSONObject> menuList = dao.query4Json(param.getTenancy_id(), menuSql);
                Integer item_menu_id = null;
                if (menuList != null && menuList.size() > 0) {
                    item_menu_id = menuList.get(0).optInt("id");
                }
                // 获取最大的点菜顺序
                String max_group_index_Sql = "SELECT max(group_index)  from cc_order_item where order_code='" + order_code_update + "'";

                tmpSQL = CcPartitionUtils.makeSQL(param.getTenancy_id(), max_group_index_Sql, order_code_update, CcPartitionUtils.TYPE_ORDERCODE_TZX);

                List<JSONObject> max_group_index_list = dao.query4Json(param.getTenancy_id(), tmpSQL);
                Integer max_group_index = max_group_index_list.get(0).optInt("group_index");
                int index = 0;
                for (Object order_item_obj : order_item_array) {
                    ((JSONObject) order_item_obj).put("order_code", order_code_update);
                    ((JSONObject) order_item_obj).put("tenancy_id", param.getTenancy_id());
                    ((JSONObject) order_item_obj).put("item_menu_id", item_menu_id);
                    ((JSONObject) order_item_obj).put("store_id", param.getStore_id());
                    ((JSONObject) order_item_obj).put("report_date", "'" + DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10) + "'");
                    // product_fee 菜品小计=菜品数量X菜品金额+菜品做法
                    JSONObject item = order_item_array.getJSONObject(index);
                    double price = item.optDouble("price");
                    index++;
                    if (jb.containsKey("order_item_taste")) {
                        // 保存到预点菜口味明细
                        JSONArray order_item_taste_array = jb.optJSONArray("order_item_taste");
                        if (order_item_taste_array.size() > 0) {
                            for (int i = 0; i < order_item_taste_array.size(); i++) {
                                JSONObject taste = order_item_taste_array.getJSONObject(i);
                                if (taste.optString("item_id").equals(item.optString("item_id")) && taste.optInt("group_index") == item.optInt("group_index")) {
                                    price += taste.optDouble("proportion_money", 0.0);
                                }
                            }
                        }
                    }
                    double product_fee = price * item.optInt("number");
                    ((JSONObject) order_item_obj).put("product_fee", product_fee);

                    CcPartitionUtils.lackInsertParam(param.getTenancy_id(), ((JSONObject) order_item_obj));

                    order_item_list.add(((JSONObject) order_item_obj));
                }
                if (order_item_list.size() > 0) {
                    try {
                        this.dao.insertBatchIgnorCase(param.getTenancy_id(), "cc_order_item", order_item_list);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }

                if (jb.containsKey("order_item_taste")) {
                    // 保存到预点菜口味明细
                    JSONArray order_item_taste_array = jb.optJSONArray("order_item_taste");
                    if (order_item_taste_array.size() > 0) {
                        List<JSONObject> order_item_taste_list = new ArrayList<JSONObject>();
                        for (Object order_item_taste_obj : order_item_taste_array) {
                            ((JSONObject) order_item_taste_obj).put("tenancy_id", param.getTenancy_id());
                            ((JSONObject) order_item_taste_obj).put("order_code", order_code_update);
                            order_item_taste_list.add(((JSONObject) order_item_taste_obj));
                        }
                        if (order_item_taste_list.size() > 0) {
                            this.dao.insertBatchIgnorCase(param.getTenancy_id(), "cc_order_item_taste", order_item_taste_list);
                        }
                    }
                }

                // 保存到预点菜套餐明细
                if (jb.containsKey("order_item_details")) {
                    JSONArray order_item_details_array = jb.optJSONArray("order_item_details");
                    if (order_item_details_array.size() > 0) {
                        List<JSONObject> order_item_details_list = new ArrayList<JSONObject>();
                        for (Object order_item_details_obj : order_item_details_array) {
                            ((JSONObject) order_item_details_obj).put("tenancy_id", param.getTenancy_id());
                            ((JSONObject) order_item_details_obj).put("order_code", order_code_update);
                            ((JSONObject) order_item_details_obj).put("report_date", "'" + DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10) + "'");

                            CcPartitionUtils.lackInsertParam(param.getTenancy_id(), ((JSONObject) order_item_details_obj));

                            order_item_details_list.add(((JSONObject) order_item_details_obj));
                        }
                        if (order_item_details_list.size() > 0) {
                            this.dao.insertBatchIgnorCase(param.getTenancy_id(), "cc_order_item_details", order_item_details_list);
                        }
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 微信订单数据组装
    @Override
    public Data orderDataAssembly(Data param, JSONObject jb) throws Exception {
        try {
            String tenantId = param.getTenancy_id();
            // 订单基本信息
            JSONObject order_list_obj = jb.optJSONObject("order_list");
            CcExceptionUtil.validContains(order_list_obj, "order_type", CcErrorCode.ORDER_TYPE_EMPTY_ERROE);
            // 根据订单类型决定验证哪些参数
            String orderType = order_list_obj.optString("order_type");
            if (orderType.equals("ZT01") || orderType.equals("WM02")) {
                // 自提外卖
                CcExceptionUtil.validContains(order_list_obj, "chanel", CcErrorCode.CHANEL_EMPTY_ERROE);
                CcExceptionUtil.validContains(order_list_obj, "district_id", CcErrorCode.DISTRICT_ID_EMPTY_ERROE);
                CcExceptionUtil.validContains(order_list_obj, "address", CcErrorCode.ADDRESS_EMPTY_ERROE);
                CcExceptionUtil.validContains(order_list_obj, "send_time", CcErrorCode.SEND_TIME_EMPTY_ERROE);
                CcExceptionUtil.validContains(order_list_obj, "consigner", CcErrorCode.CONSIGNER_EMPTY_ERROE);
                CcExceptionUtil.validContains(order_list_obj, "consigner_phone", CcErrorCode.CONSIGNER_PHONE_EMPTY_ERROE);
            } else if (orderType.equals("DN03")) {
                // 到店点餐
                CcExceptionUtil.validContains(order_list_obj, "table_code", CcErrorCode.TABLE_CODE);
                // 验证桌位号码是否正确
                String tableSql = "select table_code,table_name from tables_info where organ_id=" + param.getStore_id() + " and valid_state='1' and table_code='" + order_list_obj.optString("table_code") + "'";
                List<JSONObject> tableList = configDao.queryString4Json(tenantId, tableSql);
                if (tableList == null || tableList.size() <= 0) {
                    param.setSuccess(false);
                    param.setMsg("桌位号码输入有误，请确认");
                    return null;
                }
                // 放入桌号和桌位名称
                order_list_obj.put("table_name", tableList.get(0).optString("table_name"));
                jb.put("order_list", order_list_obj);
            }
            CcExceptionUtil.validContains(order_list_obj, "total_money", CcErrorCode.TOTAL_MONEY_EMPTY_ERROE);
            CcExceptionUtil.validContains(order_list_obj, "actual_pay", CcErrorCode.ACTUAL_PAY_EMPTY_ERROE);
            CcExceptionUtil.validContains(order_list_obj, "open_id", CcErrorCode.OPEN_ID_EMPTY_ERROE);
            // 外卖需要计算起送价格
            if (orderType.equals("WM02")) {
                // 计算起送价格
                String meals_sql = "select * from cc_meals_info where meals_type='QJ02' and valid_state='1' and channel='WX02' and store_id = " + order_list_obj.optInt("store_id");
                List<JSONObject> startPriceList = configDao.queryString4Json(tenantId, meals_sql);
                if (startPriceList != null && startPriceList.size() > 0) {
                    JSONObject bean = startPriceList.get(0);
                    // 如果菜品价格小于起送价格、返回提示信息
                    double total_money = order_list_obj.optDouble("total_money");
                    if (total_money < bean.optDouble("money")) {
                        param.setSuccess(false);
                        param.setMsg("订单价格小于起送价，无法下单");
                        return null;
                    }
                }
            }
            // 订单 点菜信息
            if (Tools.isNullOrEmpty(jb.optJSONArray("order_item")) == false) {
                JSONArray order_item_arr = jb.optJSONArray("order_item");
                for (int i = 0; i < order_item_arr.size(); i++) {
                    CcExceptionUtil.validContains(order_item_arr.getJSONObject(i), "group_index", CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_arr.getJSONObject(i), "item_id", CcErrorCode.ITEM_ID_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_arr.getJSONObject(i), "number", CcErrorCode.NUMBER_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_arr.getJSONObject(i), "price", CcErrorCode.PRICE_EMPTY_ERROE);
                }
            } else {
                CcExceptionUtil.validContains(jb, "order_item", CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
            }
            // 菜品口味做法信息
            if (Tools.isNullOrEmpty(jb.optJSONArray("order_item_taste")) == false) {
                JSONArray order_item_taste_arr = jb.optJSONArray("order_item_taste");
                for (int i = 0; i < order_item_taste_arr.size(); i++) {
                    CcExceptionUtil.validContains(order_item_taste_arr.getJSONObject(i), "group_index", CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_taste_arr.getJSONObject(i), "taste_method_id", CcErrorCode.TASTE_METHOD_ID_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_taste_arr.getJSONObject(i), "item_remark", CcErrorCode.ITEM_REMARK_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_taste_arr.getJSONObject(i), "item_id", CcErrorCode.ITEM_ID_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_taste_arr.getJSONObject(i), "type", CcErrorCode.TYPE_EMPTY_ERROE);
                }
            }
            // 套餐明细信息
            if (Tools.isNullOrEmpty(jb.optJSONArray("order_item_details")) == false) {
                JSONArray order_item_details_arr = jb.optJSONArray("order_item_details");
                for (int i = 0; i < order_item_details_arr.size(); i++) {
                    CcExceptionUtil.validContains(order_item_details_arr.getJSONObject(i), "group_index", CcErrorCode.ORDER_ITEM_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_details_arr.getJSONObject(i), "item_id", CcErrorCode.ITEM_ID_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_details_arr.getJSONObject(i), "unit_id", CcErrorCode.UNIT_ID_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_details_arr.getJSONObject(i), "number", CcErrorCode.NUMBER_EMPTY_ERROE);
                    CcExceptionUtil.validContains(order_item_details_arr.getJSONObject(i), "price", CcErrorCode.PRICE_EMPTY_ERROE);
                }
            }
            // 获取添加餐盒费用(先添加上餐盒费用，然后计算服务费用)
            if (orderType.equals("ZT01") || orderType.equals("WM02")) {
                JSONArray order_item_array = jb.optJSONArray("order_item");
                String idArray = "";
                String[] tempIdArray = new String[order_item_array.size()];
                for (int i = 0; i < order_item_array.size(); i++) {
                    JSONObject bean = order_item_array.getJSONObject(i);
                    if (i != 0) {
                        idArray += ",";
                    }
                    idArray += bean.optString("item_id");
                    tempIdArray[i] = bean.optString("item_id");
                }
                StringBuilder packSql = new StringBuilder("select item_info.package_box_num,item_info.item_code,organ.package_box_price,hq_info.item_name");
                packSql.append(",organ.item_id,organ.item_name as pack_name");
                packSql.append(" from cc_third_item_info item_info");
                packSql.append(" left join cc_third_organ_info organ on organ.shop_id=item_info.shop_id and organ.channel=item_info.channel");
                packSql.append(" left join hq_item_info hq_info on hq_info.id||''=item_info.item_code");
                packSql.append(" where item_info.channel='WX02' and item_info.item_code::int in (" + idArray + ")");
                // 查询出有餐盒费用的菜品，然后计算添加
                List<JSONObject> packList = configDao.queryString4Json(tenantId, packSql.toString());
                int count = 0;
                JSONObject packBean = new JSONObject();
                if (packList != null && packList.size() > 0) {
                    for (int i = 0; i < packList.size(); i++) {
                        JSONObject bean = packList.get(i);
                        packBean.put("item_id", bean.optString("item_id"));
                        packBean.put("price", bean.optString("package_box_price"));
                        // 循环计算数量
                        String item_code = bean.getString("item_code");
                        for (int k = 0; k < tempIdArray.length; k++) {
                            if (item_code.equals(tempIdArray[k])) {
                                count += bean.optInt("package_box_num") * order_item_array.getJSONObject(i).optInt("number");
                                break;
                            }
                        }
                    }
                }
                if (count > 0) {
                    packBean.put("number", count);
                    packBean.put("group_index", order_item_array.size());
                    packBean.put("price", packBean.optDouble("price") * count);
                    jb.optJSONArray("order_item").add(packBean);
                    // 修改总价格
                    order_list_obj.put("total_money", order_list_obj.optDouble("total_money") + packBean.optDouble("price"));
                    order_list_obj.put("actual_pay", order_list_obj.optDouble("total_money"));
                }
            }

            // 服务费用的查询处理
            StringBuilder addpriceSql = new StringBuilder();
            if (orderType.equals("WM02")) {
                // 起送价格符合，然后计算配送费
                addpriceSql = new StringBuilder("select info.*,b.taken_mode,b.guding_jj,b.fwfl from cc_meals_info info");
                addpriceSql.append(" left join hq_service_fee_type b on info.service_id=b.id");
                addpriceSql.append(" where info.meals_type='PS01' and info.valid_state='1' and info.channel='WX02'");
                addpriceSql.append(" and info.store_id = " + order_list_obj.optInt("store_id"));
            } else if (orderType.equals("DN03")) {
                // 到店点餐的服务费信息处理
                addpriceSql = new StringBuilder("select fee.* from hq_service_fee_type fee");
                addpriceSql.append(" left join tables_info info on fee.id=info.fwfz_id");
                addpriceSql.append(" where info.table_code='" + order_list_obj.optString("table_code") + "' and info.organ_id=" + param.getStore_id());
            }
            if (addpriceSql.length() > 0) {
                List<JSONObject> addPriceList = configDao.queryString4Json(tenantId, addpriceSql.toString());
                if (addPriceList != null && addPriceList.size() > 0) {
                    JSONObject bean = addPriceList.get(0);
                    // 根据类型获取配送费用为多少
                    double additional = 0;
                    if ("GD01".equals(bean.optString("taken_mode"))) {
                        // 固定金额
                        additional = bean.optDouble("guding_jj");
                    } else if ("FS03".equals(bean.optString("taken_mode"))) {
                        // 按份数
                        JSONArray order_item_arr = jb.optJSONArray("order_item");
                        int dishNumber = 0;
                        for (int i = 0; i < order_item_arr.size(); i++) {
                            dishNumber += order_item_arr.getJSONObject(i).optInt("number");
                        }
                        additional = bean.optDouble("guding_jj") * dishNumber;
                    } else {
                        // 账单比例
                        additional = order_list_obj.optDouble("total_money") * bean.optDouble("fwfl") * 0.01;
                    }
                    order_list_obj.put("meal_costs", additional);
                    order_list_obj.put("total_money", order_list_obj.optDouble("total_money") + additional);
                    order_list_obj.put("actual_pay", order_list_obj.optDouble("total_money"));
                    if (orderType.equals("WM02")) {
                        order_list_obj.put("service_id", bean.optString("service_id"));
                        order_list_obj.put("meals_id", bean.optInt("id"));
                    } else if (orderType.equals("DN03")) {
                        order_list_obj.put("service_id", bean.optInt("id"));
                    }
                    jb.put("order_list", order_list_obj);
                }
            }
            List<JSONObject> list = new ArrayList<JSONObject>();
            list.add(jb);
            param.getData().clear();
            param.setData(list);

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return param;
    }

    @Override
    public void orderQuery(Data param) throws SystemException, Exception {
        JSONObject jb = null;
        if (param.getData() != null) {
            jb = new JSONObject();
            @SuppressWarnings("unchecked")
            Map<String, Object> maps = (Map<String, Object>) param.getData().get(0);
            Set<String> set = maps.keySet();
            for (String key : set) {
                jb.put(key, maps.get(key));
            }

        }

        List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT f.openid as open_id, a.tenancy_id,a.id,a.store_id,a.customer_id,a.order_type,a.order_code,a.chanel,a.taste_like,a.province,a.city,a.area,a.district_id,a.address,a.total_money,a.actual_pay,a.meal_costs,a.remark,a.order_state,a.send_time,a.single_time,a.last_operator,a.last_updatetime,a.order_name,a.order_phone,a.table_code,a.table_name,a .consigner,a.consigner_phone,a.entry_name from cc_order_list a left join organ b on b.id = a.store_id LEFT JOIN crm_customer_info e on e.id= a.customer_id left join wx_member f on f.mobile_num= e.mobil where a.order_code in ('"
                + jb.optString("order_code") + "')");
        List<JSONObject> cc_order_list = this.dao.query4Json(jb.optString("tenentid"), sql.toString());
        JSONObject result_mq = new JSONObject();
        for (JSONObject order_list : cc_order_list) {
            result_mq.put("order_list", order_list);

            // 获取订单菜品
            sql.delete(0, sql.length());
            sql.append("SELECT b.* from  cc_order_item b   where  b.order_code= '" + order_list.optString("order_code") + "'");

            String tmpSQL = CcPartitionUtils.makeSQL(jb.optString("tenentid"), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

            List<JSONObject> order_item_list = this.dao.query4Json(jb.optString("tenentid"), tmpSQL);
            result_mq.put("order_item", order_item_list);
            // 获取订单里套餐明细
            sql.delete(0, sql.length());
            sql.append("SELECT b.* from cc_order_item_details b   where  b.order_code= '" + order_list.optString("order_code") + "'");

            tmpSQL = CcPartitionUtils.makeSQL(jb.optString("tenentid"), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

            List<JSONObject> order_item_details_list = this.dao.query4Json(jb.optString("tenentid"), tmpSQL);
            result_mq.put("order_item_details", order_item_details_list);
            // 获取订单付款方式
            sql.delete(0, sql.length());
            sql.append("SELECT b.* from cc_order_repayment b   where  b.order_code= '" + order_list.optString("order_code") + "'");

            tmpSQL = CcPartitionUtils.makeSQL(jb.optString("tenentid"), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

            List<JSONObject> cc_order_repayment_list = this.dao.query4Json(jb.optString("tenentid"), tmpSQL);
            result_mq.put("order_repayment", cc_order_repayment_list);
            // 获取菜品口味
            sql.delete(0, sql.length());
            sql.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '" + order_list.optString("order_code") + "'");
            List<JSONObject> cc_order_item_taste_list = this.dao.query4Json(jb.optString("tenentid"), sql.toString());
            result_mq.put("order_item_taste", cc_order_item_taste_list);

            // 根据会员id查询会员信息
            JSONObject customer_info_obj = new JSONObject();
            sql.delete(0, sql.length());
            sql.append("select SUM(a.main_balance)+sum(a.reward_balance) as account_balance from crm_customer_card a where  a.customer_id=" + order_list.optInt("customer_id") + "");
            List<JSONObject> account_balance_list = this.dao.query4Json(jb.optString("tenentid"), sql.toString());
            if (account_balance_list != null && account_balance_list.size() > 0) {
                customer_info_obj.put("account_balance", account_balance_list.get(0));
            }

            result_mq.put("customer_info", customer_info_obj);
            // 查询会员积分信息
            List<JSONObject> integralList = getMemberIntegral(order_list);
            result_mq.put("integral_list", integralList);

            // 查询门店送餐费信息
            sql.delete(0, sql.length());
            sql.append("select b.* from cc_meals_info b   where  b.store_id= '" + order_list.optString("store_id") + "' AND  b.valid_state='1' ");
            List<JSONObject> cc_meals_info_list = this.dao.query4Json(jb.optString("tenentid"), sql.toString());
            result_mq.put("order_item_taste", cc_order_item_taste_list);
        }
        List<JSONObject> list = new ArrayList<JSONObject>();
        list.add(result_mq);
        param.getData().clear();
        param.setData(list);
    }

    @Override
    public void orderStateQuery(Data param) throws SystemException, Exception {
        JSONObject jb = null;
        if (param.getData() != null) {
            jb = new JSONObject();
            @SuppressWarnings("unchecked")
            Map<String, Object> maps = (Map<String, Object>) param.getData().get(0);
            Set<String> set = maps.keySet();
            for (String key : set) {
                jb.put(key, maps.get(key));
            }

        }

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  order_state from cc_order_list where order_code='"
                + jb.optString("order_code") + "'");

        String tmpSQL = CcPartitionUtils.makeSQL(jb.optString("tenentid"), sql.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

        List<JSONObject> cc_order_list = this.dao.query4Json(jb.optString("tenentid"), tmpSQL);
        JSONObject result_mq = new JSONObject();
        for (JSONObject order_list : cc_order_list) {
            result_mq.put("order_state", order_list.optString("order_state"));
        }
        List<JSONObject> list = new ArrayList<JSONObject>();
        list.add(result_mq);
        param.getData().clear();
        param.setData(list);
    }



    /**
     * 发送速位下单
     *
     * @param data
     * @return 速位是否开通
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    private Boolean suweiProcess(Data data) throws Exception {
        List<JSONObject> list = (List<JSONObject>) data.getData();
        JSONObject obj = list.get(0);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT a.store_id,b.org_uuid,b.remark from cc_order_list a left join organ b on b.id = a.store_id where a.order_code in ('" + obj.optString("order_code") + "')");
        List<JSONObject> storeList = this.dao.query4Json(data.getTenancy_id(), sql.toString());
        if (storeList.isEmpty()) {
            logger.info("调用速位接口订单号" + obj.optString("order_code") + "在cc_order_list中没有记录");
            return Boolean.TRUE;
        }
        JSONObject jsonObject = storeList.get(0);
        String store_id = jsonObject.optString("store_id");
        jsonObject.put("type", SuweiConst.THIRD_SUWEI_APPLAY_ORDER);
        jsonObject.put("tenant_id", data.getTenancy_id());
        jsonObject.put("store_id", store_id);
        jsonObject.put("order_code", obj.optString("order_code"));
        JSONObject suweiStoreConfig = suweiService.getSuweiStoreConfig(data.getTenancy_id(), store_id);
        if (suweiStoreConfig == null) {
            logger.info("机构id" + store_id + "速位取餐未开通");
            return Boolean.TRUE;
        }
        if (StringUtils.isBlank(suweiStoreConfig.optString("suwei_organ_id"))) {
            logger.info("机构id" + store_id + "速位餐厅ID未配置");
            return Boolean.TRUE;
        }
        if (!StringUtils.equals(suweiStoreConfig.optString("is_open_suwei"), "1")) {
            logger.info("机构id" + store_id + "速位取餐未开通");
            return Boolean.TRUE;
        }

        JSONObject responseJson = new JSONObject();
        //发送
        JSONObject thirdSuweiApplayOrder = suweiService.thirdSuweiApplayOrder(jsonObject, responseJson);
        String result = suweiService.thirdSuweiApplayOrderReq(jsonObject, thirdSuweiApplayOrder);
        suweiService.thirdSuweiApplayOrderAfter(jsonObject, responseJson, result);

        com.alibaba.fastjson.JSONObject resultJson = JSON
                .parseObject(result);
        if (resultJson != null && resultJson.containsKey("oid")) {
            String orderStateSql = "update cc_order_list set order_state = '01' where order_code = '" + obj.optString("order_code") + "'";
            this.dao.execute(data.getTenancy_id(), orderStateSql);
            return Boolean.TRUE;
        }
        String orderStateSql = "update cc_order_list set order_state = '03' where order_code = '" + obj.optString("order_code") + "'";
        this.dao.execute(data.getTenancy_id(), orderStateSql);
        return Boolean.FALSE;
    }

    private static final String DELIVER_DATA_TYPE = "1";
    private static final int BUSINESS_TYPE = 3;
    private static final int BUSINESS_TYPE_FOUR = 4;


    @Override
    public JSONObject orderIssued(Data data) throws SystemException, Exception {
        JSONObject result = new JSONObject();
		/*if(!suweiProcess(data)) {
			return result;
		}*/

        try {
            List<JSONObject> list = (List<JSONObject>) data.getData();
            logger.info("接受到新增的下发数据:" + list.toString());
            for (JSONObject obj : list) {

                List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT a.store_id,b.org_uuid,b.remark,a.chanel from cc_order_list a left join organ b on b.id = a.store_id where a.order_code ='" + obj.optString("order_code") + "'");

                String tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "a", sql.toString(), obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                List<JSONObject> storeList = this.dao.query4Json(data.getTenancy_id(), tmpSQL);

                if (storeList.size() > 0
                        && ("WX02".equals(storeList.get(0).optString("chanel"))
                        || "WM10".equals(storeList.get(0).optString("chanel")))) {
                    //微信支付、优惠券增加积分处理
                    try {
                        creditDispose(data.getTenancy_id(), obj.optString("order_code"));
                    } catch (Exception e) {
                        System.out.println("增加积分失败！");
                        e.printStackTrace();
                    }
                }

                sql.delete(0, sql.length());
                // sql.append("SELECT a.service_id,a.tenancy_id,a.id,a.store_id,a.customer_id,a.order_type,a.order_code,a.chanel,a.taste_like,a.province,a.city,a.area,a.district_id,a.address,a.total_money,a.actual_pay,a.meals_id,a.meal_costs,a.remark,a.order_state,a.send_time,a.single_time,a.last_operator,a.last_updatetime,a.order_name,a.order_phone,a.table_code,a.table_name,a .consigner,a.consigner_phone,a.entry_name,a.payment_state,a.is_online_payment,a.maling_amount from cc_order_list a left join organ b on b.id = a.store_id where a.order_code in ('"
                sql.append("SELECT a.*,c.class_item as channel_name from cc_order_list a left join organ b on b.id = a.store_id left join sys_dictionary  c on c.class_item_code = a.chanel and c.class_identifier_code ='chanel' where a.order_code ='" + obj.optString("order_code") + "'");

                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "a", sql.toString(), obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                List<JSONObject> cc_order_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                for (JSONObject storeObj : storeList) {
                    if (list.size() >= 1) {
                        for (JSONObject order_list : cc_order_list) {

                            double platform_side_discount_fee = 0.0; // 平台方承担的优惠总金额
                            int message_result = 1;
                            if (storeObj.optInt("store_id") == order_list.optInt("store_id")) {
                                JSONObject result_mq = new JSONObject();
                                // 获取订单菜品
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.*,c.item_code,c.fake_id,c.is_combo from  cc_order_item b left join hq_item_info c on c.id=b.item_id  where  b.order_code= '" + order_list.optString("order_code") + "'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                List<JSONObject> order_item_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject order_item_obj : order_item_list) {
                                    order_item_obj.remove("upload_tag");
                                    order_item_obj.remove("report_date");
                                }
                                result_mq.put("order_item", order_item_list);
                                // 获取订单里套餐明细
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_item_details b   where  b.order_code= '" + order_list.optString("order_code") + "'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                List<JSONObject> order_item_details_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject order_item_details_obj : order_item_details_list) {
                                    order_item_details_obj.remove("upload_tag");
                                    order_item_details_obj.remove("report_date");
                                }
                                result_mq.put("order_item_details", order_item_details_list);
                                // 获取订单付款方式
                                sql.delete(0, sql.length());
                                
                                //at 2017-11-15 张勇-解决repayment记录为0时，不进行repayment记录下发，从而引起门店无法取单的问题。
                                if ((!order_list.optString("chanel").equalsIgnoreCase("WX02") && !order_list.optString("chanel").equalsIgnoreCase("WM10")) && "PLATFORM".equals(order_list.optString("settlement_type"))) {
                                    sql.append("SELECT a.id as payment_id,b.tenancy_id,b.store_id,b.order_code,b.pay_money,b.pay_no,b.third_bill_code,b.remark,a.payment_name1 as pay_name ,a.payment_code,a.payment_class from cc_order_repayment b left join payment_way a on a.payment_class=b.remark  where  a.status='1' and b.order_code= '" + order_list.optString("order_code") + "' ");//and b.pay_money>0
                                } else {
                                    sql.append("SELECT b.*,a.payment_name1 as pay_name ,a.payment_code,a.payment_class from cc_order_repayment b left join payment_way a on a.id=b.payment_id  where  a.status='1' and  b.order_code= '" + order_list.optString("order_code") + "'");
                                }
                                //2017-11-20 张勇 start兼容微信的下发情况
                                if(order_list.optString("chanel").equalsIgnoreCase("WX02")||order_list.optString("chanel").equalsIgnoreCase("WM10")){
                                	//2018-02-08 张勇  微信渠道需求->付款记录数量>1时，下发>0的付款记录 2 付款记录数量=1时，必下发付款记录
                                    tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), "select count(id) as pcount from cc_order_repayment where order_code='"+order_list.optString("order_code")+"'", order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
                                    int count = dao.getInt(data.getTenancy_id(), tmpSQL);                                   
                                	if(count>1){
                                		sql.append(" and b.pay_money>0");
                                	}
                                    
                                }
                                //2017-11-20 end
                                
                                
                                //end 2017-11-15 
                                
                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                //付款信息 增加平台收取的金额
                                List<JSONObject> cc_order_repayment_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject cc_order_repayment_obj : cc_order_repayment_list) {
                                    cc_order_repayment_obj.remove("upload_tag");
                                    cc_order_repayment_obj.remove("report_date");
                                }
                                // 获取菜品口味
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '" + order_list.optString("order_code") + "'");
                                List<JSONObject> cc_order_item_taste_list = this.dao.query4Json(data.getTenancy_id(), sql.toString());
                                for (JSONObject cc_order_item_taste_obj : cc_order_item_taste_list) {
                                    cc_order_item_taste_obj.remove("upload_tag");
                                    cc_order_item_taste_obj.remove("report_date");
                                }
                                result_mq.put("order_item_taste", cc_order_item_taste_list);
                                // 优惠信息表
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_discount b   where  b.order_code= '" + order_list.optString("order_code") + "'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                List<JSONObject> cc_order_discount_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject cc_order_discount_obj : cc_order_discount_list) {
                                    cc_order_discount_obj.remove("upload_tag");
                                    cc_order_discount_obj.remove("report_date");
                                }
                                result_mq.put("cc_order_discount", cc_order_discount_list);
                                //changhui 2017-11-9 增加新美大外卖的判断 order_list.optString("chanel").equalsIgnoreCase("MT11") start
                                //old 
                                /* if (order_list.optString("chanel").equalsIgnoreCase("BD06") || order_list.optString("chanel").equalsIgnoreCase("MT08")) {*/
                                //new
                                if (order_list.optString("chanel").equalsIgnoreCase("BD06") || order_list.optString("chanel").equalsIgnoreCase("MT08") || order_list.optString("chanel").equalsIgnoreCase("MT11")) {
                                    for (int i = 0; i < cc_order_discount_list.size(); i++) {
                                        JSONObject dc = cc_order_discount_list.get(i);
                                        platform_side_discount_fee += Double.valueOf(dc.optDouble("baidu_rate", 0.0));
                                    }
                                }

							/*	//订单主表保存商家实付并下发门店
								Double shop_real_amount =Scm.psub(Scm.psub(Scm.padd(order_list.optDouble("actual_pay"),platform_side_discount_fee), order_list.optDouble("meal_costs", 0.0)), order_list.optDouble("commission_amount", 0.0)) ;
								JSONObject update_order_list_obj=new JSONObject();
								//百度美团结算方式是平台结算的时候计算商家实收
								if((!order_list.optString("chanel").equalsIgnoreCase("WX02")&&!order_list.optString("chanel").equalsIgnoreCase("WM10"))&&"PLATFORM".equals(order_list.optString("settlement_type"))){
									update_order_list_obj.put("shop_real_amount", shop_real_amount);
									update_order_list_obj.put("id", order_list.optInt("id"));
									update_order_list_obj.put("platform_charge_amount", Scm.psub(Scm.padd(order_list.optDouble("meal_costs", 0.0),order_list.optDouble("commission_amount", 0.0)),platform_side_discount_fee));
									this.dao.updateIgnorCase(data.getTenancy_id(), "cc_order_list", update_order_list_obj);
									order_list.put("shop_real_amount", shop_real_amount);
									order_list.put("platform_charge_amount", Scm.psub(Scm.padd(order_list.optDouble("meal_costs", 0.0),order_list.optDouble("commission_amount", 0.0)),platform_side_discount_fee));
								}*/
                                order_list.remove("report_date");
                                order_list.remove("response_time_cancellation");
                                order_list.remove("receive_time_cancellation");
                                order_list.remove("cancellation_time");
                                order_list.remove("response_time_finish");
                                order_list.remove("receive_time_finish");
                                order_list.remove("finish_time");
                                order_list.remove("response_time_distribution");

                                order_list.remove("receive_time_distribution");
                                order_list.remove("distribution_time");
                                order_list.remove("response_time_dispatch");

                                order_list.remove("receive_time_dispatch");
                                order_list.remove("dispatch_time");
                                order_list.remove("response_time_qd");

                                order_list.remove("receive_time_qd");
                                order_list.remove("take_time");
                                order_list.remove("response_time");
                                order_list.remove("receive_time");

                                if (order_list.optString("meal_costs").equals("null") && StringUtils.isEmpty(order_list.optString("meal_costs"))) {
                                    order_list.remove("meal_costs");
                                }
                                order_list.remove("last_updatetime");
                                order_list.remove("upload_tag");

                                result_mq.put("order_list", order_list);
							/*	for(int i = 0; i < cc_order_repayment_list.size(); i++){
									JSONObject query_order_repayment_obj=cc_order_repayment_list.get(i);
									JSONObject update_order_repayment_obj=new JSONObject();
									update_order_repayment_obj.put("platform_side_charge", Scm.psub(order_list.optDouble("actual_pay"), shop_real_amount));
									update_order_repayment_obj.put("id", query_order_repayment_obj.optInt("id"));
									query_order_repayment_obj.put("platform_side_charge", Scm.psub(order_list.optDouble("actual_pay"), shop_real_amount));
									this.dao.updateIgnorCase(data.getTenancy_id(), "cc_order_repayment", update_order_repayment_obj);
								}*/
                                result_mq.put("order_repayment", cc_order_repayment_list);
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_credit b   where  b.order_code= '" + order_list.optString("order_code") + "'");
                                List<JSONObject> cc_order_credit_list = this.dao.query4Json(data.getTenancy_id(), sql.toString());
                                result_mq.put("order_credit", cc_order_credit_list);

                                pushOrderToMQ(data, obj, jsonObjectList, storeObj, order_list, result_mq);
                            }
                            ;

                        }
                    }

                }

            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    private void addFailList(Data data, JSONObject order_list) {
        addFailList(data, order_list,"0");
    }

    /**
     * 添加fail列表，增加订单类型参数，区分此次操作是否是取消操作
     * @param data
     * @param order_list
     */
    private void addFailList(Data data, JSONObject order_list,String type) {
        JSONObject order = JSONObject.fromObject("{}");
        order.element("tenancy_id", data.getTenancy_id());
        order.element("order_code", order_list.optString("order_code"));
        order.element("order_type", type);
        order.element("push_count", 0);
        logger.info("========= 总部下发saas 数据失败["+order_list.optString("order_code")+"]," +
                "类型["+type+"],进入redis错误队列");
        taskRedisDao.lpush(com.tzx.cc.baidu.util.Constant.PUSH_PLATFORM_MODE.getBytes(), order);
    }


    // 订单加菜下发
    @Override
    public JSONObject orderUpdateDishIssued(Data data) throws SystemException, Exception {
        JSONObject result = new JSONObject();
        try {
            List<JSONObject> list = (List<JSONObject>) data.getData();
            logger.info("接受到更新的下发数据:" + list.toString());
            for (JSONObject obj : list) {
                JSONObject order_list_obj = new JSONObject();
                order_list_obj = obj.optJSONObject("order_list");
                List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT a.store_id,b.org_uuid,b.remark from cc_order_list a left join organ b on b.id = a.store_id where a.order_code='" + order_list_obj.optString("order_code") + "'");

                String tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "a", sql.toString(), order_list_obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                List<JSONObject> storeList = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                sql.delete(0, sql.length());
                // sql.append("SELECT a.service_id,a.tenancy_id,a.id,a.store_id,a.customer_id,a.order_type,a.order_code,a.chanel,a.taste_like,a.province,a.city,a.area,a.district_id,a.address,a.total_money,a.actual_pay,a.meals_id,a.meal_costs,a.remark,a.order_state,a.send_time,a.single_time,a.last_operator,a.last_updatetime,a.order_name,a.order_phone,a.table_code,a.table_name,a .consigner,a.consigner_phone,a.entry_name,a.payment_state,a.is_online_payment,a.maling_amount from cc_order_list a left join organ b on b.id = a.store_id where a.order_code in ('"
                sql.append("SELECT a.*,c.class_item as channel_name from cc_order_list a left join organ b on b.id = a.store_id left join sys_dictionary  c on c.class_item_code = a.chanel and c.class_identifier_code ='chanel' where a.order_code='" + order_list_obj.optString("order_code") + "'");

                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "a", sql.toString(), order_list_obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                List<JSONObject> cc_order_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                for (JSONObject storeObj : storeList) {
                    if (list.size() >= 1) {
                        for (JSONObject order_list : cc_order_list) {
                            if (storeObj.optInt("store_id") == order_list.optInt("store_id")) {
                                JSONObject result_mq = new JSONObject();
                                order_list.remove("report_date");
                                order_list.remove("response_time_cancellation");
                                order_list.remove("receive_time_cancellation");
                                order_list.remove("cancellation_time");
                                order_list.remove("response_time_finish");
                                order_list.remove("receive_time_finish");
                                order_list.remove("finish_time");
                                order_list.remove("response_time_distribution");

                                order_list.remove("receive_time_distribution");
                                order_list.remove("distribution_time");
                                order_list.remove("response_time_dispatch");

                                order_list.remove("receive_time_dispatch");
                                order_list.remove("dispatch_time");
                                order_list.remove("response_time_qd");

                                order_list.remove("receive_time_qd");
                                order_list.remove("take_time");
                                order_list.remove("response_time");
                                order_list.remove("receive_time");

                                if (order_list.optString("meal_costs").equals("null") && StringUtils.isEmpty(order_list.optString("meal_costs"))) {
                                    order_list.remove("meal_costs");
                                }
                                order_list.remove("last_updatetime");
                                order_list.remove("upload_tag");
                                result_mq.put("order_list", order_list);
                                // 获取订单菜品
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.*,c.item_code,c.fake_id,c.is_combo from  cc_order_item b left join hq_item_info c on c.id=b.item_id  where  b.order_code= '" + order_list.optString("order_code") + "' and b.is_add_dish='1'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                List<JSONObject> order_item_list = this.dao.query4Json(data.getTenancy_id(), sql.toString());
                                for (JSONObject order_item_details_obj : order_item_list) {
                                    order_item_details_obj.remove("upload_tag");
                                    order_item_details_obj.remove("report_date");
                                }
                                result_mq.put("order_item", order_item_list);
                                //修改菜品状态 qinhulin add on 2016.6.13
                                sql.setLength(0);
                                sql.append("update cc_order_item set is_add_dish='0' where order_code='" + order_list.optString("order_code") + "' and is_add_dish = '1'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                dao.execute(data.getTenancy_id(), tmpSQL);
                                // 获取订单里套餐明细
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_item_details b   where  b.order_code= '" + order_list.optString("order_code") + "' and   b.is_add_dish='1' ");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                List<JSONObject> order_item_details_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject order_item_details_obj : order_item_details_list) {
                                    order_item_details_obj.remove("upload_tag");
                                    order_item_details_obj.remove("report_date");
                                }
                                result_mq.put("order_item_details", order_item_details_list);
                                sql.setLength(0);
                                sql.append("update cc_order_item_details set is_add_dish='0' where order_code='" + order_list.optString("order_code") + "' and is_add_dish = '1'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                dao.execute(data.getTenancy_id(), tmpSQL);
                                // 获取订单付款方式
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.*,a.payment_name1 as pay_name ,a.payment_code,a.payment_class from cc_order_repayment b left join payment_way a on a.id=b.payment_id  where  b.order_code= '" + order_list.optString("order_code") + "'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                List<JSONObject> cc_order_repayment_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject order_item_details_obj : cc_order_repayment_list) {
                                    order_item_details_obj.remove("upload_tag");
                                    order_item_details_obj.remove("report_date");
                                }
                                result_mq.put("order_repayment", cc_order_repayment_list);
                                // 获取菜品口味
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '" + order_list.optString("order_code") + "'  and  b.is_add_dish='1' ");
                                List<JSONObject> cc_order_item_taste_list = this.dao.query4Json(data.getTenancy_id(), sql.toString());
                                for (JSONObject order_item_details_obj : cc_order_item_taste_list) {
                                    order_item_details_obj.remove("upload_tag");
                                    order_item_details_obj.remove("report_date");
                                }
                                result_mq.put("order_item_taste", cc_order_item_taste_list);
                                sql.setLength(0);
                                sql.append("update cc_order_item_taste set is_add_dish='0' where order_code='" + order_list.optString("order_code") + "' and is_add_dish = '1'");
                                dao.execute(data.getTenancy_id(), sql.toString());
                                if (storeObj.optString("remark").equalsIgnoreCase("read_from_rif")) {
                                    try {
                                        MessageUtils mu = new MessageUtils();
                                        logger.info("总部下发给门店的订单信息4:" + JSONObject.fromObject(result_mq).toString());
                                        mu.sendMessage(NewPosOrderUtil.ccOrder2NewPos(result_mq).toString(), storeObj.optString("org_uuid") + "_rif", 1, data.getTenancy_id(), storeObj.optString("store_id"));
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                } else {
                                    jsonObjectList.add(result_mq);
                                    MessageUtils mu = new MessageUtils();
                                    if (jsonObjectList.size() > 0) {
                                        Data d = Data.get();
                                        d.setType(Type.ORDER);
                                        d.setOper(Oper.updatedish);
                                        d.setTenancy_id(data.getTenancy_id());
                                        d.setData(jsonObjectList);
                                        d.setStore_id(storeObj.optInt("store_id"));
                                        logger.info("总部下发给门店的订单信息5:" + JSONObject.fromObject(d).toString());
                                        mu.sendMessage(JSONObject.fromObject(d).toString(), storeObj.optString("org_uuid"), 1, data.getTenancy_id(), storeObj.optString("store_id"));
                                    }
                                }

                            }
                            ;

                        }
                    }

                }

            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;

    }

    @Override
    public List<JSONObject> getMemberIntegral(JSONObject order_list) throws Exception {
        List<JSONObject> returnList = new ArrayList<JSONObject>();
        returnList = this.getMemberCards(order_list);
        List<JSONObject> memberList = memberDao.getMemberInfo(order_list.optString("tenancy_id"), order_list.optString("open_id"));
        if (memberList.get(0).getBoolean("isbind")) {
            // 获取积分明细
            JSONObject jsonParam = new JSONObject();
            jsonParam.put("tenancy_id", order_list.optString("tenancy_id"));
            jsonParam.put("type", "CUSTOMER_CREDIT");
            jsonParam.put("oper", "find");
            jsonParam.put("secret", "0");

            JSONObject obj = new JSONObject();
            obj.put("mobil", memberList.get(0).getString("mobile_num"));
            // 查询获取用户的绑定时间
            if (memberList != null && memberList.size() > 0) {
                JSONObject member = memberList.get(0);
                obj.put("startdate", DateUtil.format(DateUtil.parseDate(member.getString("bind_time"))));
                obj.put("enddate", DateUtil.format(new Date()));
            }

            JSONArray array = new JSONArray();
            array.add(obj);

            jsonParam.put("data", array);
            System.out.println(com.tzx.framework.common.constant.Constant.systemMap.get("product_wechat_scmip"));
            String resultString = HttpUtil.sendPostRequest(com.tzx.framework.common.constant.Constant.systemMap.get("product_wechat_scmip"), jsonParam.toString());
            JSONObject returnJson = JSONObject.fromObject(resultString);
            System.out.println("返回结果：" + resultString);
            // 放入积分消费详情
            JSONObject json = new JSONObject();
            json.put("integral_list", returnJson.get("data"));
            returnList.add(json);
        }
        return returnList;
    }

    @Override
    public List<JSONObject> getMemberCards(JSONObject order_list) throws Exception {
        List<JSONObject> returnList = new ArrayList<JSONObject>();
        JSONObject json = new JSONObject();
        List<JSONObject> memberList = this.memberDao.getMemberInfo(order_list.optString("tenancy_id"), order_list.optString("open_id"));
        // 获取前台会员信息 调用查询接口。
        if (memberList.get(0).getBoolean("isbind")) {
            JSONObject jsonParam = new JSONObject();
            jsonParam.put("secret", "0");
            jsonParam.put("tenancy_id", order_list.optString("tenancy_id"));
            // jsonParam.put("store_id", 0);
            jsonParam.put("type", "CUSTOMERINFO");
            jsonParam.put("oper", "find");

            JSONObject obj = new JSONObject();
            obj.put("mobil", memberList.get(0).getString("mobile_num"));

            JSONArray array = new JSONArray();
            array.add(obj);

            jsonParam.put("data", array);
            System.out.println(com.tzx.framework.common.constant.Constant.systemMap.get("product_wechat_scmip"));
            String resultString = HttpUtil.sendPostRequest(com.tzx.framework.common.constant.Constant.systemMap.get("product_wechat_scmip"), jsonParam.toString());
            JSONObject returnJson = JSONObject.fromObject(resultString);
            System.out.println("返回结果：" + resultString);
            @SuppressWarnings("unchecked")
            List<JSONObject> vipList = (List<JSONObject>) returnJson.get("data");
            JSONObject vipJson = new JSONObject();
            if (vipList != null) {
                vipJson = vipList.get(0);
            }
            json.put("vip", vipJson);
        }
        json.put("member", memberList);
        // 获取后台设置的会员卡显示设置
        String sql = "select * from wx_format_info where type_info = '" + com.tzx.weixin.common.constant.Constant.CUSTOMIZE_CARD + "'";
        List<JSONObject> cardShowList = configDao.queryString4Json(order_list.optString("tenancy_id"), sql);
        json.put("card_show_list", cardShowList);
        returnList.add(json);

        return returnList;
    }

    /**
     * 封装用户送餐地址信息
     *
     * @param order
     */
    public void getAddressBean(JSONObject order) throws Exception {
        try {
            JSONObject bean = new JSONObject();
            if (!"WM02".equals(order.optString("order_type"))) {
                return;
            }
            bean.put("tenancy_id", order.optString("tenancy_id"));
            bean.put("customer_id", order.optString("customer_id"));
            bean.put("province", order.optString("province"));
            bean.put("city", order.optString("city"));
            bean.put("area", order.optString("area"));
            bean.put("address", order.optString("address"));
            bean.put("consignee", order.optString("consigner"));
            bean.put("consignee_phone", order.optString("consigner_phone"));
            bean.put("entry_time", DateUtil.getNowDateYYDDMMHHMMSS());
            bean.put("district_id", order.optString("district_id"));
            bean.put("longitude", order.optString("longitude"));
            bean.put("latitude", order.optString("latitude"));
            if (!order.containsKey("address_id")) {
                // 先确定地址是否重复，然后再进行入库
                StringBuilder sql = new StringBuilder("select * from crm_customer_address where customer_id=" + bean.optString("customer_id"));
                sql.append(" and province='" + bean.optString("province") + "' and city='" + bean.optString("city") + "'");
                sql.append(" and area='" + bean.optString("area") + "' and address='" + bean.optString("address") + "'");
                sql.append(" and consignee='" + bean.optString("consignee") + "' and consignee_phone='" + bean.optString("consignee_phone") + "'");
                sql.append(" and district_id=" + bean.optString("district_id"));
                List<JSONObject> list = dao.query4Json(order.optString("tenancy_id"), sql.toString());
                if (list == null || list.size() <= 0) {
                    dao.insertIgnorCase(order.optString("tenancy_id"), "crm_customer_address", bean);
                }
            } else {
                bean.put("id", order.optString("address_id"));
                dao.updateIgnorCase(order.optString("tenancy_id"), "crm_customer_address", bean);
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    // 保存门店返回的付款信息
    public void saveOrderRepayment(String tenantId, JSONArray arr) throws Exception {
        dao.insertBatchIgnorCase(tenantId, "cc_order_repayment", JSONArray.toList(arr, JSONObject.class));

    }

    // 下发数据封装
    private JSONObject issueData(String order_code, JSONObject creditInfo) {
        JSONObject ret = new JSONObject();

        ret.put("tenancy_id", creditInfo.get("tenancy_id"));
        ret.put("store_id", creditInfo.get("store_id"));

        ret.put("bill_num", order_code);//账单编号
        ret.put("order_code", order_code);//账单编号
        ret.put("bill_code", creditInfo.get("credit_code"));//增加积分账单号

        ret.put("credit", creditInfo.get("credit"));//本次增加积分
        ret.put("customer_name", creditInfo.get("name"));//会员名称

        ret.put("consume_before_credit", 0);//开始积分暂存为0
        ret.put("consume_after_credit", 0);//结束积分暂存为0

        ret.put("report_date", DateUtil.getNowDateYYDDMM());
        ret.put("type", " JF01");//账单会员的操作类型: JF01 消费赠积分,ZK02 会员折扣与会员价 ,JZ03 会员卡结账

        ret.put("amount", creditInfo.get("total_amount"));
        ret.put("customer_code", creditInfo.get("customer_code"));
        ret.put("mobil", creditInfo.get("mobile"));

        ret.put("last_updatetime", DateUtil.getNowDateYYDDMMHHMMSS());

        return ret;
    }

    // 微信点餐增加积分新增处理
    private void creditDispose(String tenantId, String order_code)
            throws SystemException, Exception {
        boolean isMember = false; // 是否会员
        JSONObject creditInfo = new JSONObject();
        try { // 增加积分处理
            isMember = addCredit(tenantId, order_code, creditInfo);
            if (!creditInfo.containsKey("status")) {
                creditInfo.put("status", 0); // 增加积分成功
            }
        } catch (Exception e) {
            e.printStackTrace();
            creditInfo.put("status", 1); // 增加积分失败
            creditInfo.put("msg", "增加积分处理时出现异常");
        }
        if (!isMember) { // 不是会员不保存数据
            return;
        }

        try { // 保存数据处理
            dao.insertIgnorCase(tenantId, "wx_credit_log", creditInfo);
            // 增加积分成功，才保存下发数据
            if (0 == creditInfo.getInt("status")) {
                JSONObject data = issueData(order_code, creditInfo);
                dao.insertIgnorCase(tenantId, "cc_order_credit", data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("微信增加积分保存数据时失败：" + creditInfo.toString());
        }
        return;
    }

    // 调用会员卡消费接口
    private boolean addCredit(String tenantId, String order_code,
                              JSONObject creditInfo) throws Exception {
        // 查询会员信息
        String customersql = "select a.mobil mobile_num,a.code code,c.chanel chanel,"
                + "c.openid,c.store_id,a.name ,c.customer_id,c.total_money from cc_order_list c left join "
                + "crm_customer_info a on a.id=c.customer_id where "
                + "c.order_code='" + order_code + "'";

        String tmpSQL = CcPartitionUtils.makeSQL(tenantId, false, "c", customersql, order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);

        List<JSONObject> list3 = this.dao.query4Json(tenantId, tmpSQL);

        if (list3.size() <= 0) {
            return false;
        }
        JSONObject customer = list3.get(0);
        // 判断是否非会员
        if ("".equals(customer.optString("openid"))
                || "null".equals(customer.optString("openid"))) {
            return false;
        }
        int customer_id = customer.optInt("customer_id");

        String checkcardsql = "select cor.* from cc_order_repayment cor "
                + " LEFT JOIN payment_way pw on cor.payment_id=pw.id "
                + " where cor.order_code ='" + order_code + "'"
                + " and pw.payment_class ='card'" + " and pw.status='1'";
        List<JSONObject> checkcardlist = this.dao.query4Json(tenantId,
                checkcardsql);
        //如果订单使用了会员卡支付，则不需要再调用会员卡消费接口进行crm活动营销
        if (checkcardlist.size() > 0) {
            return false;
        }
        // 随机查询一张会员卡
//			String cardsql = "select  card_code from  crm_customer_card where customer_id="
//					+ customer_id;
//			List<JSONObject> cardlist = this.dao.query4Json(tenantId, cardsql);
//
//			if (cardlist.size() <= 0) {
//				return false;
//			}
//			JSONObject cardjson = cardlist.get(0);
//			String card_code = cardjson.optString("card_code");
        //保存到日志中的值
        creditInfo.put("mobile", customer.optString("mobile_num"));
        creditInfo.put("customer_code", customer.optString("code"));

        creditInfo.put("tenancy_id", tenantId);
        creditInfo.put("store_id", customer.optString("store_id"));

        creditInfo.put("bill_code", order_code);
        creditInfo.put("openid", customer.optString("openid"));
        creditInfo.put("chanel", customer.optString("chanel"));
        creditInfo.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());

        creditInfo.put("name", customer.optString("name"));
        StringBuffer sb = new StringBuffer();
        JSONObject couponswechatWay = null;
        String payment_classstr = "";
        sb.setLength(0);
        sb.append("select  pw.* from payment_way_of_ogran po  left join payment_way pw on po.payment_id=pw.id ");
        sb.append(" where organ_id='" + customer.optString("store_id") + "' and payment_class='coupons' and pw.status='1' ");
        List<JSONObject> query41Json = dao.query4Json(tenantId, sb.toString());
        couponswechatWay = query41Json.size() > 0 ? query41Json.get(0) : null;
        if (couponswechatWay != null) {
            String if_jifen = couponswechatWay.optString("if_jifen");
            if ("".equals(if_jifen) || if_jifen == null || "0".equals(if_jifen)) {
                //如果优惠券付款方式是否积分字段为空或null或不允许积分，则不处理
            } else {
                payment_classstr += "'coupons',";
            }
        }
        double consume_creditmoney = 0.0;
        sb.setLength(0);
        sb.append("select  pw.* from payment_way_of_ogran po  left join payment_way pw on po.payment_id=pw.id ");
        sb.append(" where organ_id='" + customer.optString("store_id") + "' and payment_class='wechat_pay' and pw.status='1' ");
        List<JSONObject> query42Json = dao.query4Json(tenantId, sb.toString());
        couponswechatWay = query42Json.size() > 0 ? query42Json.get(0) : null;
        if (couponswechatWay != null) {
            String if_jifen = couponswechatWay.optString("if_jifen");
            if ("".equals(if_jifen) || if_jifen == null || "0".equals(if_jifen)) {
                //如果优惠券付款方式是否积分字段为空或null或不允许积分，则不处理
            } else {
                payment_classstr += "'wechat_pay',";
            }
        }
        double money = 0.0;
        if (payment_classstr.length() > 0) {
            payment_classstr = payment_classstr.substring(0, payment_classstr.length() - 1);
            // 计算需要积分的金额
            String countsql = "select coalesce(sum(cor.pay_money),0) sum from cc_order_repayment cor "
                    + " LEFT JOIN payment_way pw on cor.payment_id=pw.id "
                    + " where cor.order_code ='"
                    + order_code
                    + "'"
                    + " and pw.payment_class in (" + payment_classstr + ")"
                    + " and pw.status='1'";

            List<JSONObject> list2 = dao.query4Json(tenantId, countsql);
            money = list2.get(0).optDouble("sum");
            if (money <= 0.0) {
                return false;
            }
        }
        double bill_amount = 0.0;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT total_money from cc_order_list  where order_code ='" + order_code + "'");

        tmpSQL = CcPartitionUtils.makeSQL(tenantId, sql.toString(), order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);

        List<JSONObject> storeList = this.dao.query4Json(tenantId, tmpSQL);
        if (storeList.size() > 0) {
            bill_amount = storeList.get(0).optDouble("total_money", 0.0);
        }


        //准备调用接口参数
        com.tzx.crm.bo.dto.Data param = new com.tzx.crm.bo.dto.Data();
        param.setTenancy_id(tenantId);
        param.setStore_id(customer.optInt("store_id"));
        param.setOper(com.tzx.crm.base.constant.Oper.add);

        List<Map<Object, Object>> data = new ArrayList<Map<Object, Object>>(1);
        Map<Object, Object> map = new HashMap<Object, Object>();

        map.put("mobil", creditInfo.getString("mobile"));
        map.put("consume_creditmoney", money);
        map.put("reason", "微信会员增加积分");
        map.put("chanel", creditInfo.getString("chanel"));
        map.put("bill_code", order_code);
        map.put("operator", "wx");
        map.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
        map.put("bill_amount", bill_amount);
        data.add(map);
        param.setData(data);

        // 调用增加积分接口
        System.out.println("调用会员消费接口传参：" + data.toString());
        try {
//				cardTransactionService.customerCardConsume(data);
            Map<String, Object> customerCredit = bonusPointManageService
                    .customerCredit(param);
            // 会员卡消费账单号，用于撤销消费。
            creditInfo.put("credit_code", customerCredit.get("bill_code"));
            creditInfo.put("credit", customerCredit.get("credit"));//本次积分
//				creditInfo.put("credit_code", bill_code);
//				creditInfo.put("credit",consume_creditmoney);// 本次积分金额
        } catch (Exception e) {
            e.printStackTrace();
            creditInfo.put("status", 1); // 增加积分失败
            creditInfo.put("msg", "调用增加积分接口失败");
            return true;
        }

        return true;
    }

    //订单确认方法
    public boolean orderOper(String tenantId, JSONObject third_obj, JSONObject jb, int i) throws Exception {
        String hq_service_time = DateUtil.format(new Timestamp(System.currentTimeMillis()));
        StringBuilder sb = new StringBuilder();
        boolean result_flag = true;
        JSONObject third_return_obj = shopService.orderOper(tenantId, third_obj);
        logger.info("20170216确认订单美团最终返回的信息是" + i + ":" + JSONObject.fromObject(third_return_obj));
        if (third_return_obj.optString("data").equalsIgnoreCase("ok")) {
            sb.setLength(0);
            sb.append("update cc_order_list set third_order_state='5', dispatch_time = '" + jb.optString("receive_time_qd") + "', order_state_desc='已确认' , order_state = '04' ,bill_num = '" + jb.optString("bill_num") + "', take_time = '" + jb.optString("take_time") + "',receive_time_qd = '"
                    + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' ,receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");

            String tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

            this.dao.execute(tenantId, tmpSQL);
            result_flag = true;
        } else {
            if (third_return_obj.optString("errno").equalsIgnoreCase("808")) {
                sb.setLength(0);
                sb.append("update cc_order_list set third_order_state='5', order_state_desc='已确认' , dispatch_time = '" + jb.optString("receive_time_qd") + "' , order_state = '04' ,bill_num = '" + jb.optString("bill_num") + "', take_time = '" + jb.optString("take_time") + "',receive_time_qd = '"
                        + jb.optString("receive_time_qd") + "',response_time_qd='" + hq_service_time + "' ,receive_time = '" + jb.optString("receive_time") + "',response_time='" + hq_service_time + "' where order_code ='" + jb.optString("order_code") + "' ");

                String tmpSQL = CcPartitionUtils.makeSQL(tenantId, sb.toString(), jb.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                this.dao.execute(tenantId, tmpSQL);
                result_flag = true;
            } else {
                result_flag = false;
            }
        }
        return result_flag;

    }


    //微信点餐、微信外卖先付门店拒单总部做相应的对款操作
    public void storeCancelWx02Wx10Order(JSONObject store_cancel_obj, JSONObject order_infot_obj, List<JSONObject> order_reason_list) throws Exception {
        //查询是否开具了电子发票
        List<JSONObject> order_electronic_list = orderManagementService.loadOrderElectronicList(order_infot_obj.optString("tenancy_id"), order_infot_obj);
        if (order_electronic_list.size() > 0) {
            for (JSONObject order_electronic_obj : order_electronic_list) {
                com.tzx.cc.bo.dto.Data data1 = new com.tzx.cc.bo.dto.Data();
                data1.setOper(com.tzx.cc.common.constant.Oper.cancle);
                data1.setType(com.tzx.cc.common.constant.Type.CANCLE_LECTRONIC_INVOICE);
                data1.setStore_id(order_infot_obj
                        .optInt("store_id"));
                data1.setTenancy_id(order_infot_obj.optString("tenancy_id"));
                JSONObject bean = new JSONObject();
                List<JSONObject> list2 = new ArrayList<JSONObject>();
                bean.put("DH", order_electronic_obj.optString("order_code"));// 电话
                list2.add(bean);
                data1.setData(list2);
                JSONObject a = new JSONObject();
                a = JSONObject.fromObject(data1);
                String resultString = HttpUtil.sendPostRequest(com.tzx.framework.common.constant.Constant.systemMap.get("saas_url") + "/invoice/elec/post", a.toString());
                logger.info(order_electronic_obj.optString("order_code") + "门店取消电子发票返回的信息:" + resultString);
            }
        }
        //查询是否有产生了积分
        List<JSONObject> order_credit_list = orderManagementService.loadOrderOrderCreditList(store_cancel_obj.optString("tenantId"), order_infot_obj);
        if (order_credit_list.size() > 0) {
            for (JSONObject order_credit_obj : order_credit_list) {
                // 撤销积分增加消费
                JSONObject queryMemberBaseInfo = newWxMemberDao
                        .queryMemberBaseInfo(store_cancel_obj.optString("tenantId"),
                                order_infot_obj.optString("openid"));
                String mobile_num = queryMemberBaseInfo
                        .optString("mobile_num");
                String third_bill_code = order_credit_obj
                        .optString("bill_code");
                com.tzx.crm.bo.dto.Data data1 = new com.tzx.crm.bo.dto.Data();
                data1.setOper(com.tzx.crm.base.constant.Oper.update);
                data1.setType(com.tzx.crm.base.constant.Type.CUSTOMER_CREDIT);
                data1.setSecret("123456");
                data1.setStore_id(order_infot_obj
                        .optInt("store_id"));
                data1.setTenancy_id(store_cancel_obj.optString("tenantId"));
                JSONObject bean = new JSONObject();
                List<JSONObject> list2 = new ArrayList<JSONObject>();
                bean.put("mobil", mobile_num);// 电话
                bean.put("chanel", order_infot_obj.optString("chanel"));// 渠道
                bean.put("operator", "admin");// 操作人员
                bean.put("updatetime",
                        DateUtil.getNowDateYYDDMMHHMMSS());//
                bean.put("old_bill_code", third_bill_code);// 交易单号
                bean.put("business_date",
                        DateUtil.getNowDateYYDDMMHHMMSS());
                bean.put("shift_id", "WX");
                bean.put("batch_no", "WX");
                bean.put("bill_code",
                        order_infot_obj.optString("order_code"));
                list2.add(bean);
                data1.setData(list2);
                JSONObject a = new JSONObject();
                a = JSONObject.fromObject(data1);
                a.remove("success");
                try {
                    String resultString = HttpUtil
                            .sendPostRequest(
                                    com.tzx.framework.common.constant.Constant.systemMap
                                            .get("crmUrl"), a.toString());
                    JSONObject returnJson = JSONObject
                            .fromObject(resultString);
                    System.out.println("微信点餐积分增加撤销传入信息：" + a.toString() + "返回信息为：" + returnJson.toString());
                    if (returnJson.optBoolean("success")) {
                        logger.info("总部取消订单积分成功:"
                                + order_infot_obj.optString("order_code"));
                    } else {
                        logger.info("总部取消订单积分失败:"
                                + order_infot_obj.optString("order_code") + " " + returnJson.toString());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }


            }
        }
        // 查询订单支付信息
        List<JSONObject> order_repayment_obj_wm_list = orderManagementService.loadOrderRepaymentList(store_cancel_obj.optString("tenantId"), store_cancel_obj);
        if (order_repayment_obj_wm_list.size() > 0) {
            for (JSONObject order_repayment_obj_wm : order_repayment_obj_wm_list) {
                System.out.println("查询到的订单支付信息为：" + store_cancel_obj.optString("order_code") + " " + order_repayment_obj_wm.toString());
                if (order_repayment_obj_wm != null && !"{}".equalsIgnoreCase(order_repayment_obj_wm.toString())) {
                    if (order_repayment_obj_wm.optString("payment_class").equalsIgnoreCase("wechat_pay")) {
                        JSONObject jsonParam = new JSONObject();
                        jsonParam.put("tenancy_id", store_cancel_obj.optString("tenantId"));
                        jsonParam.put("oper", com.tzx.weixin.common.constant.WxRestOper.refundOrder);
                        jsonParam.put("openid", order_infot_obj.optString("openid"));
                        jsonParam.put("order_code", store_cancel_obj.optString("order_code"));
                        jsonParam.put("bill_num", store_cancel_obj.optString("order_code"));
//							String resultString = HttpUtil.sendPostRequest(com.tzx.framework.common.constant.Constant.systemMap.get("saas_url")+"/newwx/wx", jsonParam.toString());
                        String resultString = newWxDishService.refundOrder(store_cancel_obj.optString("tenantId"), jsonParam).toString();
                        System.out.println("微信支付传入的参数为：" + jsonParam.toString() + " 返回为" + resultString.toString());
                        JSONObject returnJson = JSONObject.fromObject(resultString);
                        if (returnJson.optBoolean("success")) {
                            this.storeCancelOrder(store_cancel_obj, order_reason_list);
                        } else {
                            logger.info("微信退款异常:" + resultString);
                        }
                    } else if (order_repayment_obj_wm.optString("payment_class").equalsIgnoreCase("card")) {
                        JSONObject jsonParam = new JSONObject();
                        jsonParam.put("tenancy_id", store_cancel_obj.optString("tenantId"));
                        jsonParam.put("store_id", order_infot_obj.optString("store_id"));
                        jsonParam.put("type", "CUSTOMER_CARD_CONSUME");
                        jsonParam.put("secret", "123456");
                        jsonParam.put("oper", "update");
                        JSONObject obj_data = new JSONObject();
                        obj_data.put("card_code", order_repayment_obj_wm.optString("pay_no"));
                        obj_data.put("old_bill_code", order_repayment_obj_wm.optString("third_bill_code"));
                        obj_data.put("chanel", "WX02");

                        obj_data.put("operator", store_cancel_obj.optString("cancle_name"));
                        obj_data.put("updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));

                        JSONArray array = new JSONArray();
                        array.add(obj_data);
                        jsonParam.put("data", array);
                        String resultString = HttpUtil.sendPostRequest(com.tzx.framework.common.constant.Constant.systemMap.get("crmUrl"), jsonParam.toString());

                        JSONObject returnJson = JSONObject.fromObject(resultString);
                        System.out.println("卡支付传入的参数为：" + jsonParam.toString() + " 返回为" + returnJson.toString());
                        if (returnJson.optBoolean("success")) {
                            this.storeCancelOrder(store_cancel_obj, order_reason_list);
                        }
                    } else if (order_repayment_obj_wm.optString("payment_class").equalsIgnoreCase("coupons")) {

                        //有使用优惠劵
                        com.tzx.crm.bo.dto.Data param1 = new com.tzx.crm.bo.dto.Data();
                        param1.setOper(com.tzx.crm.base.constant.Oper.init);
                        param1.setTenancy_id(store_cancel_obj.optString("tenantId"));
                        param1.setStore_id(order_infot_obj.optInt("store_id"));
                        param1.setSecret("123456");
                        param1.setType(com.tzx.crm.base.constant.Type.COUPONS);
                        List<JSONObject> list = new ArrayList<JSONObject>();
                        JSONObject bean = new JSONObject();
                        bean.put("chanel", order_infot_obj.optString("chanel"));
                        List<JSONObject> couponList = new ArrayList<JSONObject>();
                        JSONObject bean1 = new JSONObject();
                        bean1.put("coupons_code", order_repayment_obj_wm.optString("pay_no"));
                        couponList.add(bean1);
                        bean.put("couponslist", couponList);
                        list.add(bean);
                        param1.setData(list);
                        JSONObject a = new JSONObject();
                        a = JSONObject.fromObject(param1);
                        a.remove("success");
                        //撤销接口
                        try {
                            String resultString = HttpUtil.sendPostRequest(com.tzx.framework.common.constant.Constant.systemMap.get("crmUrl"), a.toString());

                            JSONObject returnJson = JSONObject.fromObject(resultString);
                            System.out.println("券支付传入的参数为：" + a.toString() + " 返回为" + returnJson.toString());
                            if (returnJson.optBoolean("success")) {

                                this.storeCancelOrder(store_cancel_obj, order_reason_list);

                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }


                    } else if (order_repayment_obj_wm.optString("payment_class").equalsIgnoreCase("card_credit")) {
                        //撤销积分消费
                        JSONObject queryMemberBaseInfo = newWxMemberDao.queryMemberBaseInfo(store_cancel_obj.optString("tenantId"), order_infot_obj.optString("openid"));
                        String mobile_num = queryMemberBaseInfo.optString("mobile_num");
                        String third_bill_code = order_repayment_obj_wm.optString("third_bill_code");
                        com.tzx.crm.bo.dto.Data data1 = new com.tzx.crm.bo.dto.Data();
                        data1.setOper(com.tzx.crm.base.constant.Oper.update);
                        data1.setType(com.tzx.crm.base.constant.Type.CUSTOMER_CREDIT_COUNSUME);
                        data1.setSecret("123456");
                        data1.setStore_id(order_infot_obj.optInt("store_id"));
                        data1.setTenancy_id(store_cancel_obj.optString("tenantId"));
                        JSONObject bean = new JSONObject();
                        List<JSONObject> list = new ArrayList<JSONObject>();
                        bean.put("mobil", mobile_num);// 电话
                        bean.put("chanel", order_infot_obj.optString("chanel"));// 渠道
                        bean.put("operator", "admin");// 操作人员
                        bean.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());//
                        bean.put("old_bill_code", third_bill_code);//交易单号
                        bean.put("business_date", DateUtil.getNowDateYYDDMMHHMMSS());
                        bean.put("shift_id", "WX");
                        bean.put("batch_no", "WX");
                        bean.put("bill_code", store_cancel_obj.optString("order_code"));
                        list.add(bean);
                        data1.setData(list);
                        JSONObject a = new JSONObject();
                        a = JSONObject.fromObject(data1);
                        a.remove("success");
                        try {
                            String resultString = HttpUtil.sendPostRequest(com.tzx.framework.common.constant.Constant.systemMap.get("crmUrl"), a.toString());
                            JSONObject returnJson = JSONObject.fromObject(resultString);
                            System.out.println("积分支付传入的参数为：" + a.toString() + " 返回为" + returnJson.toString());
                            List<JSONObject> obj_list = (List<JSONObject>) returnJson.get("data");
                            if (obj_list.size() > 0) {
                                this.storeCancelOrder(store_cancel_obj, order_reason_list);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }

                }
            }
        } else {
            this.storeCancelOrder(store_cancel_obj, order_reason_list);
        }
    }

    //门店发起取消订单总部操作cc_order_list、cc_order_reason_detail
    public void storeCancelOrder(JSONObject store_cancel_obj, List<JSONObject> order_reason_list) throws Exception {
        try {
            String hq_service_time = DateUtil.format(new Timestamp(System.currentTimeMillis()));
            StringBuilder sb = new StringBuilder();
            System.out.println("门店发起取消订单总部操作");
            if ("08".equalsIgnoreCase(store_cancel_obj.optString("order_state"))) {
                if (!store_cancel_obj.containsKey("cancle_name")) {
                    store_cancel_obj.put("cancle_name", "");
                }
                if (!store_cancel_obj.containsKey("receive_time_cancellation")) {
                    store_cancel_obj.put("receive_time_cancellation", hq_service_time);
                }
                sb.setLength(0);
                
                ///增加操作用户名 2017-11-06 王纪好 start
                String userName="系统";
                
                String user_num=store_cancel_obj.optString("cancle_name");
                if(!StringUtil.isEmpty(user_num)){
                	//at 2017-11-06 张勇  *)增加try..catch 防止该脚本影响已有业务  start
                	try{
	                	SqlRowSet rs=this.dao.query(store_cancel_obj.optString("tenantId"), "SELECT user_name from user_authority WHERE employee_id="+user_num);
	                    if(rs.next()){ 
	                    	userName=rs.getString("user_name");
	                    }
                	}catch(Exception ex){
                		logger.error("商户："+store_cancel_obj.optString("tenantId")+"->执行查询取消用户名称时发生异常："+ex.getMessage());
                	}
                	//end 2017-11-06 张勇 end
                }
                
                ///增加操作用户名 2017-11-06 王纪好 end
                
                ///修改用户ID改为用户名 2017-11-06 王纪好 start
                //取消状态为服务员取消
                if (store_cancel_obj.optString("payment_state").equalsIgnoreCase("03")) {
                    sb.append("update cc_order_list set cancel_type='0',order_state = '08' ,payment_state='04',cancle_name ='" + userName+ "', cancellation_time='" + store_cancel_obj.optString("cancellation_time") + "' where order_code ='" + store_cancel_obj.optString("order_code") + "' ");
                } else {
                    sb.append("update cc_order_list set cancel_type='0',order_state = '08' ,cancle_name ='" + userName + "', cancellation_time='" + store_cancel_obj.optString("cancellation_time") + "' where order_code ='" + store_cancel_obj.optString("order_code") + "'");
                }
              ///修改用户ID改为用户名 2017-11-06 王纪好 end

                String tmpSQL = CcPartitionUtils.makeSQL(store_cancel_obj.optString("tenantId"), sb.toString(), store_cancel_obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                this.dao.execute(store_cancel_obj.optString("tenantId"), tmpSQL);
            }
            if (order_reason_list != null && order_reason_list.size() > 0) {
                for (JSONObject order_reason : order_reason_list) {
                    order_reason.put("tenancy_id", store_cancel_obj.optString("tenantId"));
                    order_reason.put("order_code", store_cancel_obj.optString("order_code"));
                    dataDictionaryService.save(store_cancel_obj.optString("tenantId"), "cc_order_reason_detail", order_reason);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取MQ下发方式
     *
     * @param tenancy_id
     * @param store_id
     * @return
     * @throws Exception
     */
    private JSONObject getSysparamer(String tenancy_id, String store_id) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append(" select * from sys_parameter\n");
        sb.append(" where 1=1\n");
        sb.append(" and para_code='MQ_SEND_WAY'\n");

        sb.append(" and store_id in (0");
        if (StringUtils.isNotEmpty(store_id)) {
            sb.append("," + store_id);
        }
        sb.append(")\n");

        sb.append(" and tenancy_id='" + tenancy_id + "'\n");
        sb.append(" and valid_state='1'\n");
        sb.append(" order by store_id desc limit 1\n");

        List<JSONObject> sysParaList = this.dao.query4Json(tenancy_id, sb.toString());

        if (sysParaList != null && sysParaList.size() > 0) {
            return sysParaList.get(0);
        }
        return null;
    }

    @Resource(name = BonusPointManageService.NAME)
    private BonusPointManageService bonusPointManageService;


    /**
     * 推送订单摘要信息到 MQ队列
     * @param data
     * @param obj
     * @param storeObj
     * @param order_list
     * @param mu 发送消息体
     * @param pushData
     * @throws Exception
     */
    private void pushOrderToMQ(Data data, JSONObject obj, List<JSONObject> jsonObjectList, JSONObject storeObj, JSONObject order_list, JSONObject result_mq) throws Exception {
        int message_result=1;
        String tmpSQL;
        String channel = order_list.optString("chanel");
        // -----标识此次订单处理类型（0 其他 1 取消订单）------
        String orderType = "0";
        if (storeObj.optString("remark").equalsIgnoreCase("read_from_rif")) {
            try {
                String select_state = "select * from cc_order_list where order_code='" + obj.optString("order_code") + "'";
                List<JSONObject> result = this.dao.query4Json(data.getTenancy_id(), select_state);
                if(result != null && result.size()>0){
                    String type = result.get(0).optString("order_state");
                    logger.info("======总部下发订单["+obj.optString("order_code")+"] 状态order_state:["+type+"]");
                    if(type.equals("08")){
                        orderType="1";
                    }
                }
                JSONObject message = NewPosOrderUtil.ccOrder2NewPos(result_mq);
                logger.info("总部下发rif数据:" + message);
                MessageUtils mu = new MessageUtils();
                if(validOrderPushTypeFlag(data.getTenancy_id(),channel)) {
                    try{
                        message_result = mu.sendMessage(message.toString(), storeObj.optString("org_uuid") + "_rif", 1, data.getTenancy_id(),
                                storeObj.optString("store_id"), com.tzx.cc.baidu.util.Constant.DELIVER_DATA_TYPE,  com.tzx.cc.baidu.util.Constant.BUSINESS_TYPE_FOUR);
                        if(message_result != 1){
                            // 失败进入task队列
                            addFailList(data, order_list,orderType);
                        }
                    } catch (Exception e) {
                        // 失败进入task队列
                        message_result = 0;
                        addFailList(data, order_list,orderType);
                    }
                }else{
                    message_result = mu.sendMessage(message.toString(), storeObj.optString("org_uuid") + "_rif", 1, data.getTenancy_id(), storeObj.optString("store_id"));
                }
                if (message_result != 1) {
                    refreshOrderPushLog(data, obj, storeObj, order_list, message);
//                    String update_order_state_sql = "update cc_order_list set order_state='03' where order_code='" + obj.optString("order_code") + "'";
//
//                    tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), update_order_state_sql, obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
//                    this.dao.execute(data.getTenancy_id(), tmpSQL);
                }
                //--2017-12-19  添加订单 数据到redis
                OrderDeliveryUtils.orderDelivery(message.toString(),obj.optString("order_code"),"",data.getTenancy_id(),storeObj.optString("store_id"),storeObj.optString("org_uuid") + "_rif");
                message = null;
                //--end
            } catch (Exception e) {
                String update_order_state_sql = "update cc_order_list set order_state='03' where order_code='" + obj.optString("order_code") + "'";
                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), update_order_state_sql, obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
                this.dao.execute(data.getTenancy_id(), tmpSQL);
                e.printStackTrace();
            }
        } else {
            jsonObjectList.add(result_mq);
            MessageUtils mu = new MessageUtils();
            if (jsonObjectList.size() > 0) {
                try {
                    String select_state = "select * from cc_order_list where order_code='" + obj.optString("order_code") + "'";
                    List<JSONObject> result = this.dao.query4Json(data.getTenancy_id(), select_state);
                    if(result != null && result.size()>0){
                        String type = result.get(0).optString("order_state");
                        logger.info("======总部下发订单["+obj.optString("order_code")+"] 状态order_state:["+type+"]");
                        if(type.equals("08")){
                            orderType="1";
                        }
                    }

                    Data d = Data.get();
                    d.setType(Type.ORDER);
                    d.setOper(Oper.add);
                    d.setTenancy_id(data.getTenancy_id());
                    d.setData(jsonObjectList);
                    d.setStore_id(storeObj.optInt("store_id"));

                    JSONObject pushData = JSONObject.fromObject(d);
                    if (order_list.optString("chanel").equalsIgnoreCase("WX02")) { // 11 为推送平台方式
                        logger.info("总部下发saas数据(新推送平台方式):" + pushData.toString());
                        try{
                            message_result = mu.sendMessage(pushData.toString(), storeObj.optString("org_uuid"), 1, data.getTenancy_id(),
                                    storeObj.optString("store_id"), com.tzx.cc.baidu.util.Constant.DELIVER_DATA_TYPE, com.tzx.cc.baidu.util.Constant.BUSINESS_TYPE);
                            if(message_result != 1){
                                // 失败进入task队列
                                addFailList(data, order_list,orderType);
                            }


                        } catch (Exception e) {
                            // 失败进入task队列
                            message_result = 0;
                            addFailList(data, order_list,orderType);
                        }
                    } else {
                        logger.info("=============总部下发saas数据(新推送平台方式)渠道["+order_list.optString("chanel")+"],订单号["+order_list.optString("order_code")+"] \n Data is:" + pushData.toString());
                        if(validOrderPushTypeFlag(data.getTenancy_id(),channel)){
                            try{
                                message_result = mu.sendMessage(pushData.toString(), storeObj.optString("org_uuid"), 1, data.getTenancy_id(),
                                        storeObj.optString("store_id"),com.tzx.cc.baidu.util.Constant.DELIVER_DATA_TYPE,com.tzx.cc.baidu.util.Constant.BUSINESS_TYPE_FOUR);
                                if(message_result != 1){
                                    addFailList(data, order_list,orderType);
                                }
                            } catch (Exception e) {
                                // 失败进入task队列
                                logger.info("平台推送失败:渠道["+order_list.optString("chanel")+"],订单号["+order_list.optString("order_code")+"]",e);
                                message_result = 0;
                                addFailList(data, order_list,orderType);
                            }
                        }else{
                            message_result = mu.sendMessage(pushData.toString(), storeObj.optString("org_uuid"), 1, data.getTenancy_id(), storeObj.optString("store_id"));
                        }
                    }
                    if (message_result != 1) {
                        refreshOrderPushLog(data, obj, storeObj, order_list, pushData);
                    }

                    //--2017-12-19  添加订单 数据到redis
                    OrderDeliveryUtils.orderDelivery(pushData.toString(),obj.optString("order_code"),"",data.getTenancy_id(),storeObj.optString("store_id"),storeObj.optString("org_uuid"));
                    //--end

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 更新订单推送失败日志
     * @param data
     * @param obj
     * @param storeObj
     * @param order_list
     * @param pushData
     * @throws Exception
     */
    private void refreshOrderPushLog(Data data, JSONObject obj, JSONObject storeObj, JSONObject order_list, JSONObject pushData) throws Exception {
        String tmpSQL;
        //--------------更新订单状态-----------------
        String update_order_state_sql = "update cc_order_list set order_state='03' where order_code='" + obj.optString("order_code") + "' and order_state='01'";
        tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), update_order_state_sql, obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);
        this.dao.execute(data.getTenancy_id(), tmpSQL);

        // 记录日志
        JSONObject cc_order_push_log = JSONObject.fromObject("{}");

        cc_order_push_log.element("tenancy_id", data.getTenancy_id());
        cc_order_push_log.element("order_code", order_list.optString("order_code"));
        cc_order_push_log.element("store_id", storeObj.optString("store_id"));
        cc_order_push_log.element("org_uuid", storeObj.optString("org_uuid"));
        cc_order_push_log.element("chanel", order_list.optString("chanel"));
        cc_order_push_log.element("msg_body", pushData.toString());
        cc_order_push_log.element("status", 0);
        cc_order_push_log.element("create_time", DateUtil.getNowDateYYDDMMHHMMSS());
        cc_order_push_log.element("last_update_time", DateUtil.getNowDateYYDDMMHHMMSS());

        this.dao.insertIgnorCase(data.getTenancy_id(), "cc_order_push_log", cc_order_push_log);
    }

    /**
     * 校验对应渠道是否使用新下发模式
     */
    public boolean validOrderPushTypeFlag(String tanencyId,String channel) {
//        boolean is_start = false;
//        String sql = "select para_value from sys_parameter where para_code = 'order_issued_type' and store_id=0 and valid_state='"
//                + com.tzx.cc.base.Constant.VALID_STATE_TRUE + "'";
//        List<net.sf.json.JSONObject> list;
//        try {
//            list = this.dao.query4Json(tanencyId, sql.toString());
//            if (list == null || list.size() == 0) {
//                is_start = false;
//            } else {
//                for (net.sf.json.JSONObject json : list) {
//                    String pushType =json.optString("para_value");
//                    String matchType = "0";
//                    switch (channel)
//                    {
//                        case com.tzx.cc.baidu.util.Constant.BAIDU_CHANNEL:
//                            matchType = pushType.substring(0,1);
//                               break;
//                        case com.tzx.cc.baidu.util.Constant.ELE_CHANNEL:
//                            matchType = pushType.substring(1,2);
//                            break;
//                        case com.tzx.cc.baidu.util.Constant.MEITUAN_CHANNEL:
//                            matchType = pushType.substring(2,3);
//                            break;
//                        case com.tzx.cc.baidu.util.Constant.XMDWM_CHANNEL:
//                            matchType = pushType.substring(3);
//                            break;
//                        default:matchType = "0";
//                    }
//                    if ("1".equals(matchType)) {
//                        is_start = true;
//                        logger.info("----------- OrderPushType [" + tanencyId + "]推送类型：新模式  ------ ");
//                    } else {
//                        logger.info("----------- OrderPushType [" + tanencyId + "]推送类型：老模式  ------ ");
//                        is_start = false;
//                    }
//                }
//            }
//        } catch (Exception e) {
//            is_start = false;
//            logger.info("-----------CommentGrap: 未在数据库中检索到[" + tanencyId + "]商户的库信息  ---- ");
//            e.printStackTrace();
//        }
        //return is_start;
        return true;
    }

    /**
     * 获取断号订单信息
     * @param requestParams {tenancy_id,channel,shop_id,sort_num,date_start,date_end }
     * @return
     */
    @Override
    public JSONArray getMissOrderInfo(JSONObject requestParams) throws Exception {
        JSONArray result=JSONArray.fromObject("[]");
        String tenancy_id = requestParams.optString("tenancy_id");
        String shop_id = requestParams.optString("shop_id");
        JSONArray detail = requestParams.optJSONArray("detail");
        String serverName = requestParams.optString("serverName");
        //String channel = requestParams.optString("channel");
        String date_start = requestParams.optString("date_start");
        String date_end = requestParams.optString("date_end");

        String ymd=DateUtil.format(new Date(), "yyyy-MM-dd");
        String start_=ymd+" 00:00:00";
        String end_=ymd+" 23:59:59";

        List<JSONObject> order_codes = new ArrayList<>();
        //设置库中未存在的订单暂时存储容器
        Map<String,List<String>> notExistsChannelSerialNums = new HashMap<>();

        //获取渠道生成sql
        for(int index=0;index<detail.size();index++){
            StringBuilder sql_getorder = new StringBuilder("SELECT c.* FROM cc_order_list c where");
            sql_getorder.append(" c.store_id=").append(shop_id);
            if(StringUtils.isNotEmpty(date_start) && StringUtils.isNotEmpty(date_end)){
                sql_getorder.append(" and c.single_time between '"+date_start+"' and '"+date_end+"' ");
            }else{
                sql_getorder.append(" and c.single_time between '"+start_+"' and '"+end_+"' ");
            }
            String tempSQL=CcPartitionUtils.makeSQL(tenancy_id,false, "c", sql_getorder.toString(), ymd, CcPartitionUtils.TYPE_SINGLETIME_LESS_EQ);
            sql_getorder.setLength(0);
            sql_getorder.append(tempSQL);
            JSONObject info = detail.getJSONObject(index);
            String channel = info.optString("channel");

            JSONArray sort_num_arr = info.getJSONArray("sort_num");
            if(sort_num_arr == null ||  sort_num_arr.size()<1){
                logger.info("==ordermiss== 渠道["+channel+"]没有传入sort_num;");
                continue;
            }
            StringBuilder sb = new StringBuilder("");
            //记录库中已经存在断号信息
            String inputDate = date_start.substring(0,10);
            List<String> inputSerialNums = new ArrayList<>();
            for(int i=0;i<sort_num_arr.size();i++){
                int job = (Integer)sort_num_arr.get(i);
                sb.append("'").append(job).append("',");
                //若不是查询当天数据则不做拉单处理
                if(inputDate.equalsIgnoreCase(ymd)){
                    inputSerialNums.add(job+"");
                }
            }
            sb.append("'-1'");
            sql_getorder.append(" and c.chanel_serial_number in(").append(sb.toString()).append(")");
            if(StringUtils.isNotEmpty(channel)){
                sql_getorder.append(" and c.chanel='").append(channel).append("'");
            }
            logger.info("sql_getorder::=="+sql_getorder);
            DBContextHolder.setTenancyid(tenancy_id);
            List<JSONObject> list = this.dao.query4Json(tenancy_id, sql_getorder.toString());

            List<String> existSerialNums = new ArrayList<>();
            for(JSONObject temp : list){
                JSONObject obj = JSONObject.fromObject("{}");
                obj.put("order_code",temp.optString("order_code"));
                obj.put("order_state",temp.optString("order_state",""));
                order_codes.add(obj);
                existSerialNums.add(temp.optString("chanel_serial_number"));
            }
            logger.info("==chanel_serial_numbers before:"+inputSerialNums);
            logger.info("==exitsSerialNums:"+existSerialNums);
            inputSerialNums.removeAll(existSerialNums);
            if(inputSerialNums.size()>0){
                notExistsChannelSerialNums.put(channel,inputSerialNums);
            }
            logger.info("==chanel_serial_numbers after:"+inputSerialNums);
        }

        //获取下发订单数据体
        Data data = new Data();
        data.setTenancy_id(tenancy_id);
        data.setData(order_codes);
        CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
        UUID requestId=UUID.randomUUID();
        try{

            DBContextHolder.setTenancyid(tenancy_id);

            // 获取订单数据
            result= getDeliveryDataInfo(data);
            //如果存在平台订单未推送到saas则去拉取平台三方订单
            if(!notExistsChannelSerialNums.isEmpty()){
                getThirdPartyOrderBySerialNums(tenancy_id,shop_id,notExistsChannelSerialNums,date_end,serverName);
            }
            //日志数据准备
            ccBusniessLogBean.setRequestId(requestId.toString());
            ccBusniessLogBean.setTenancyId(tenancy_id);
            ccBusniessLogBean.setCategory("cc");
            ccBusniessLogBean.setType("grabOrder");
            ccBusniessLogBean.setOperAction("Order");
            //ccBusniessLogBean.setChannel(channel);
            //ccBusniessLogBean.setChannelName(channel);// 暂时保持原来结构不变，暂时就不去处理该字段内容值
            ccBusniessLogBean.setCmd("com.tzx.cc.bo.imp.OrderUpdateManagementServiceImpl.getMissOrderInfo");
            ccBusniessLogBean.setRequestBody(requestParams.toString());
            ccBusniessLogBean.setCreateTime(new Date().getTime());
            ccBusniessLogBean.setResponseBody(result.toString());
            ccBusniessLogBean.setIsNormal("1");
            ccBusniessLogBean.setIsThird("0");
            ccBusniessLogBean.setThirdId("");
            ccBusniessLogBean.setTzxId("");
            ccBusniessLogBean.setTzxName("");
            ccBusniessLogBean.setShopId(shop_id);
        }catch (Exception e){
            throw e;
        }finally {
              KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
        }
        return result;
    }

    /**
     * 拉取 saas未记录在库的平台订单信息
     * @param tenancy_id
     * @param shop_id saas 商铺ID
     * @param notExistsChannelSerialNums 本地未存储的订单序列号列表
     * @param date_end  订单产生时间，接口调用者需要传入日期信息
     */
    private void getThirdPartyOrderBySerialNums(String tenancy_id, String shop_id, Map<String, List<String>> notExistsChannelSerialNums,
                                                String date_end,String serverName) {
        for(Map.Entry<String,List<String>> entry : notExistsChannelSerialNums.entrySet()){
            JSONObject params = JSONObject.fromObject("{}");
            String channel = entry.getKey();
            List<String> list = entry.getValue();
            params.put("tenantId",tenancy_id);
            params.put("channel",channel);
            params.put("storeId",shop_id);
            params.put("type",1);
            if(StringUtils.isNotEmpty(date_end)){
                params.put("date",date_end.substring(0,10));
            }else{
                String ymd=DateUtil.format(new Date(), "yyyy-MM-dd");
                params.put("date",ymd);
            }
            params.put("value",list);
            // 从三方平台获取订单信息
            JSONObject result = ccOrderGrabService.grabOrderList(params);
            logger.info("==ordermiss grap_third_Data result:"+result);
            if("0".equals(result.optString("error"))){
                JSONArray arr = result.optJSONArray("orderMessage");
                Iterator<JSONObject> iteraor = arr.iterator();
                while(iteraor.hasNext()){
                    //String message = iteraor.next();
                    JSONObject orderResult = iteraor.next();
                   String tzxStatus= orderResult.optString("tzxStatus");
                   //warning: 如果是取消订单则 不做推送处理
                   if(!"08".equals(tzxStatus)){
                       String third_code = orderResult.optString("thirdOrderCode");
                       logger.info("------- grab_third_order_code  {"+third_code+"}");
                       JSONObject params2 = JSONObject.fromObject("{}");
                       params2.put("channel",channel);
                       params2.put("msg",orderResult);
                       params2.put("url_prefix","http://"+serverName);
                       logger.info("push params ["+params2+"]");
                       JSONObject pushResult = ccOrderGrabService.orderPush(params2);
                       logger.info("push result ["+pushResult+"]");
                   }
                }
            }
        }
    }

    /**
     * 获取下发订单信息体
     */
    private JSONArray getDeliveryDataInfo(Data data) {
        JSONArray result = JSONArray.fromObject("[]");

        try {
            List<JSONObject> list = (List<JSONObject>) data.getData();
            logger.info("miss 订单到主动拉取订单code:" + list.toString());
            for (JSONObject obj : list) {
                List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
                StringBuilder sql = new StringBuilder();
                sql.append("SELECT a.store_id,b.org_uuid,b.remark,a.chanel from cc_order_list a left join organ b on b.id = a.store_id where a.order_code ='" + obj.optString("order_code") + "'");

                String tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "a", sql.toString(), obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                List<JSONObject> storeList = this.dao.query4Json(data.getTenancy_id(), tmpSQL);

                if (storeList.size() > 0
                        && ("WX02".equals(storeList.get(0).optString("chanel"))
                        || "WM10".equals(storeList.get(0).optString("chanel")))) {
                    //微信支付、优惠券增加积分处理
                    try {
                        creditDispose(data.getTenancy_id(), obj.optString("order_code"));
                    } catch (Exception e) {
                        System.out.println("增加积分失败！");
                        e.printStackTrace();
                    }
                }

                sql.delete(0, sql.length());
                // sql.append("SELECT a.service_id,a.tenancy_id,a.id,a.store_id,a.customer_id,a.order_type,a.order_code,a.chanel,a.taste_like,a.province,a.city,a.area,a.district_id,a.address,a.total_money,a.actual_pay,a.meals_id,a.meal_costs,a.remark,a.order_state,a.send_time,a.single_time,a.last_operator,a.last_updatetime,a.order_name,a.order_phone,a.table_code,a.table_name,a .consigner,a.consigner_phone,a.entry_name,a.payment_state,a.is_online_payment,a.maling_amount from cc_order_list a left join organ b on b.id = a.store_id where a.order_code in ('"
                sql.append("SELECT a.*,c.class_item as channel_name from cc_order_list a left join organ b on b.id = a.store_id left join sys_dictionary  c on c.class_item_code = a.chanel and c.class_identifier_code ='chanel' where a.order_code ='" + obj.optString("order_code") + "'");

                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "a", sql.toString(), obj.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                List<JSONObject> cc_order_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                for (JSONObject storeObj : storeList) {
                    if (list.size() >= 1) {
                        for (JSONObject order_list : cc_order_list) {
                        	//除了取消其他状态全部改为新订单下发
                        	if(!order_list.optString("order_state","").equals("08")){
                        		order_list.put("order_state","01");
                           }
                            double platform_side_discount_fee = 0.0; // 平台方承担的优惠总金额
                            int message_result = 1;
                            if (storeObj.optInt("store_id") == order_list.optInt("store_id")) {
                                JSONObject result_mq = new JSONObject();
                                // 获取订单菜品
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.*,c.item_code,c.fake_id,c.is_combo from  cc_order_item b left join hq_item_info c on c.id=b.item_id  where  b.order_code= '" + order_list.optString("order_code") + "'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                List<JSONObject> order_item_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject order_item_obj : order_item_list) {
                                    order_item_obj.remove("upload_tag");
                                    order_item_obj.remove("report_date");
                                }
                                result_mq.put("order_item", order_item_list);
                                // 获取订单里套餐明细
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_item_details b   where  b.order_code= '" + order_list.optString("order_code") + "'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                List<JSONObject> order_item_details_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject order_item_details_obj : order_item_details_list) {
                                    order_item_details_obj.remove("upload_tag");
                                    order_item_details_obj.remove("report_date");
                                }
                                result_mq.put("order_item_details", order_item_details_list);
                                // 获取订单付款方式
                                sql.delete(0, sql.length());

                                //at 2017-11-15 张勇-解决repayment记录为0时，不进行repayment记录下发，从而引起门店无法取单的问题。
                                if ((!order_list.optString("chanel").equalsIgnoreCase("WX02") && !order_list.optString("chanel").equalsIgnoreCase("WM10")) && "PLATFORM".equals(order_list.optString("settlement_type"))) {
                                    sql.append("SELECT a.id as payment_id,b.tenancy_id,b.store_id,b.order_code,b.pay_money,b.pay_no,b.third_bill_code,b.remark,a.payment_name1 as pay_name ,a.payment_code,a.payment_class from cc_order_repayment b left join payment_way a on a.payment_class=b.remark  where  a.status='1' and b.order_code= '" + order_list.optString("order_code") + "' ");//and b.pay_money>0
                                } else {
                                    sql.append("SELECT b.*,a.payment_name1 as pay_name ,a.payment_code,a.payment_class from cc_order_repayment b left join payment_way a on a.id=b.payment_id  where  a.status='1' and  b.order_code= '" + order_list.optString("order_code") + "'");
                                }
                                //2017-11-20 张勇 start兼容微信的下发情况
                                if(order_list.optString("chanel").equalsIgnoreCase("WX02")||order_list.optString("chanel").equalsIgnoreCase("WM10")){
                                    sql.append(" and b.pay_money>0");
                                }
                                //2017-11-20 end


                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                //付款信息 增加平台收取的金额
                                List<JSONObject> cc_order_repayment_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject cc_order_repayment_obj : cc_order_repayment_list) {
                                    cc_order_repayment_obj.remove("upload_tag");
                                    cc_order_repayment_obj.remove("report_date");
                                }
                                // 获取菜品口味
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '" + order_list.optString("order_code") + "'");
                                List<JSONObject> cc_order_item_taste_list = this.dao.query4Json(data.getTenancy_id(), sql.toString());
                                for (JSONObject cc_order_item_taste_obj : cc_order_item_taste_list) {
                                    cc_order_item_taste_obj.remove("upload_tag");
                                    cc_order_item_taste_obj.remove("report_date");
                                }
                                result_mq.put("order_item_taste", cc_order_item_taste_list);
                                // 优惠信息表
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_discount b   where  b.order_code= '" + order_list.optString("order_code") + "'");

                                tmpSQL = CcPartitionUtils.makeSQL(data.getTenancy_id(), false, "b", sql.toString(), order_list.optString("order_code"), CcPartitionUtils.TYPE_ORDERCODE_TZX);

                                List<JSONObject> cc_order_discount_list = this.dao.query4Json(data.getTenancy_id(), tmpSQL);
                                for (JSONObject cc_order_discount_obj : cc_order_discount_list) {
                                    cc_order_discount_obj.remove("upload_tag");
                                    cc_order_discount_obj.remove("report_date");
                                }
                                result_mq.put("cc_order_discount", cc_order_discount_list);
                                //changhui 2017-11-9 增加新美大外卖的判断 order_list.optString("chanel").equalsIgnoreCase("MT11") start
                                //old
                                /* if (order_list.optString("chanel").equalsIgnoreCase("BD06") || order_list.optString("chanel").equalsIgnoreCase("MT08")) {*/
                                //new
                                if (order_list.optString("chanel").equalsIgnoreCase("BD06") || order_list.optString("chanel").equalsIgnoreCase("MT08") || order_list.optString("chanel").equalsIgnoreCase("MT11")) {
                                    for (int i = 0; i < cc_order_discount_list.size(); i++) {
                                        JSONObject dc = cc_order_discount_list.get(i);
                                        platform_side_discount_fee += Double.valueOf(dc.optDouble("baidu_rate", 0.0));
                                    }
                                }

							/*	//订单主表保存商家实付并下发门店
								Double shop_real_amount =Scm.psub(Scm.psub(Scm.padd(order_list.optDouble("actual_pay"),platform_side_discount_fee), order_list.optDouble("meal_costs", 0.0)), order_list.optDouble("commission_amount", 0.0)) ;
								JSONObject update_order_list_obj=new JSONObject();
								//百度美团结算方式是平台结算的时候计算商家实收
								if((!order_list.optString("chanel").equalsIgnoreCase("WX02")&&!order_list.optString("chanel").equalsIgnoreCase("WM10"))&&"PLATFORM".equals(order_list.optString("settlement_type"))){
									update_order_list_obj.put("shop_real_amount", shop_real_amount);
									update_order_list_obj.put("id", order_list.optInt("id"));
									update_order_list_obj.put("platform_charge_amount", Scm.psub(Scm.padd(order_list.optDouble("meal_costs", 0.0),order_list.optDouble("commission_amount", 0.0)),platform_side_discount_fee));
									this.dao.updateIgnorCase(data.getTenancy_id(), "cc_order_list", update_order_list_obj);
									order_list.put("shop_real_amount", shop_real_amount);
									order_list.put("platform_charge_amount", Scm.psub(Scm.padd(order_list.optDouble("meal_costs", 0.0),order_list.optDouble("commission_amount", 0.0)),platform_side_discount_fee));
								}*/
                                order_list.remove("report_date");
                                order_list.remove("response_time_cancellation");
                                order_list.remove("receive_time_cancellation");
                                order_list.remove("cancellation_time");
                                order_list.remove("response_time_finish");
                                order_list.remove("receive_time_finish");
                                order_list.remove("finish_time");
                                order_list.remove("response_time_distribution");

                                order_list.remove("receive_time_distribution");
                                order_list.remove("distribution_time");
                                order_list.remove("response_time_dispatch");

                                order_list.remove("receive_time_dispatch");
                                order_list.remove("dispatch_time");
                                order_list.remove("response_time_qd");

                                order_list.remove("receive_time_qd");
                                order_list.remove("take_time");
                                order_list.remove("response_time");
                                order_list.remove("receive_time");

                                if (order_list.optString("meal_costs").equals("null") && StringUtils.isEmpty(order_list.optString("meal_costs"))) {
                                    order_list.remove("meal_costs");
                                }
                                order_list.remove("last_updatetime");
                                order_list.remove("upload_tag");

                                result_mq.put("order_list", order_list);
							/*	for(int i = 0; i < cc_order_repayment_list.size(); i++){
									JSONObject query_order_repayment_obj=cc_order_repayment_list.get(i);
									JSONObject update_order_repayment_obj=new JSONObject();
									update_order_repayment_obj.put("platform_side_charge", Scm.psub(order_list.optDouble("actual_pay"), shop_real_amount));
									update_order_repayment_obj.put("id", query_order_repayment_obj.optInt("id"));
									query_order_repayment_obj.put("platform_side_charge", Scm.psub(order_list.optDouble("actual_pay"), shop_real_amount));
									this.dao.updateIgnorCase(data.getTenancy_id(), "cc_order_repayment", update_order_repayment_obj);
								}*/
                                result_mq.put("order_repayment", cc_order_repayment_list);
                                sql.delete(0, sql.length());
                                sql.append("SELECT b.* from cc_order_credit b   where  b.order_code= '" + order_list.optString("order_code") + "'");
                                List<JSONObject> cc_order_credit_list = this.dao.query4Json(data.getTenancy_id(), sql.toString());
                                result_mq.put("order_credit", cc_order_credit_list);

                                if (storeObj.optString("remark").equalsIgnoreCase("read_from_rif")) {
                                        JSONObject message = NewPosOrderUtil.ccOrder2NewPos(result_mq);
                                    result.add(message);

                                } else {
                                    jsonObjectList.add(result_mq);
                                    if (jsonObjectList.size() > 0) {
                                            Data d = Data.get();
                                            d.setType(Type.ORDER);
                                            if("08".equals(obj.optString("order_state"))){
                                                d.setOper(Oper.cancle);
                                            }else{
                                                d.setOper(Oper.add);
                                            }
                                            d.setTenancy_id(data.getTenancy_id());
                                            d.setData(jsonObjectList);
                                            d.setStore_id(storeObj.optInt("store_id"));
                                            JSONObject pushData = JSONObject.fromObject(d);
                                            result.add(pushData);
                                    }
                                }
                            } ;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
