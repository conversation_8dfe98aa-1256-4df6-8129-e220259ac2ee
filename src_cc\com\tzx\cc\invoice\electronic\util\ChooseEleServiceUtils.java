package com.tzx.cc.invoice.electronic.util;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.cc.invoice.electronic.cont.ElectronicInvoiceConst;
import com.tzx.cc.invoice.electronic.service.ElecInvoiceService;
import com.tzx.cc.invoice.electronic.service.impl.BwElecInvoiceServiceImpl;
import com.tzx.cc.invoice.electronic.service.impl.ElecInvoiceServiceImpl;
import com.tzx.cc.invoice.electronic.service.impl.PiaotongElecInvoiceServiceImpl;
import com.tzx.cc.invoice.electronic.service.impl.YonyouElecInvoiceServiceImpl;
import com.tzx.framework.common.util.SpringConext;

public class ChooseEleServiceUtils {
	private static final Logger	logger	= Logger.getLogger(ChooseEleServiceUtils.class);
	public static ElecInvoiceService getElecInvoiceService(String tenancyId,int storeId){
		try {
			JSONObject getfrxx = BwElectronicInvoiceWebServiceUtils.getfrxx(tenancyId, storeId);
            if(getfrxx==null) {
                return SpringConext.getApplicationContext().getBean(ElecInvoiceServiceImpl.class);
            }
			String choose = getfrxx.optString("docking_merchants");
			if(StringUtils.equals(ElectronicInvoiceConst.ELECTRIC_USE_CHOOSE_BW,choose)) {
				return SpringConext.getApplicationContext().getBean(BwElecInvoiceServiceImpl.class);
			} else if(StringUtils.equals(ElectronicInvoiceConst.ELECTRIC_USE_CHOOSE_YY, choose)){
				return SpringConext.getApplicationContext().getBean(YonyouElecInvoiceServiceImpl.class);
			} else if(StringUtils.equals(ElectronicInvoiceConst.ELECTRIC_USE_CHOOSE_PT, choose)){
				return SpringConext.getApplicationContext().getBean(PiaotongElecInvoiceServiceImpl.class);
			} else {
				return SpringConext.getApplicationContext().getBean(ElecInvoiceServiceImpl.class);
			}
		} catch (Exception e) {
			logger.error(e);
		}
		return null;
	}
}

