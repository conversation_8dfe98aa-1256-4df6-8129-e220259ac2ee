package com.tzx.cc.invoice.electronic.util;

import java.io.File;
import java.io.IOException;
import java.util.List;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.core.io.Resource;

import com.tzx.framework.common.util.SpringConext;

/**
 * <AUTHOR>
 * 
 */
public class XmlUtils {
	/**
	 * log
	 */
	private static final Logger logger = Logger
			.getLogger(XmlUtils.class);
	
	
	/**
	 * 把没有根节点的xml换成json， 只适合单节点
	 * @param xmlParam
	 * @return
	 * @throws DocumentException
	 */
	public static JSONObject xml2json(String xmlParam) throws DocumentException {
		Element parse = XmlUtils.parse(xmlParam);
		List<Object> content = parse.content();

		JSONObject returnjson = new JSONObject();
		for(Object obj:content){
			Element el = (Element) obj;
			String textTrim2 = el.getTextTrim();
			String name = el.getName();
			returnjson.put(name, textTrim2);
		}
		return returnjson;
	}
	
	/**
	 * 获取发票开具xml
	 * @return
	 * @throws IOException
	 * @throws DocumentException
	 */
	public static Element getFPKJXML() throws IOException, DocumentException{
		Resource resource = SpringConext.getApplicationContext().getResource("classpath:/invoice/electronic/fpkj_templete.xml");
		File file = resource.getFile();
		Element root = XmlUtils.load(file);
		return root;
	}
	
	/**
	 * 获取访问webservice返回的xml
	 * @param xmlstr
	 * @return
	 * @throws DocumentException
	 */
	public static String getReturnData(String xmlstr) throws DocumentException{
		Element parse = parse(xmlstr);
		Element element = getElement(parse, "Body","xmlStringResponse","return");
		return element.getText();
	}

	/**
	 * 将字符串读入dom
	 * @param xmlstr
	 * @return
	 * @throws DocumentException
	 */
	public static Element parse(String xmlstr) throws DocumentException{
		Document document = DocumentHelper.parseText(xmlstr); 
		Element root = document.getRootElement();
		return root;
	}
	
	/**
	 * 将xml文件中的内容读取到内存中，并返回第一个Element
	 * 
	 * @param file
	 * @return
	 * @throws DocumentException
	 */
	public static Element load(File file) throws DocumentException {
		SAXReader reader = new SAXReader();
		Document document = reader.read(file);
		Element root = document.getRootElement();
		String asXML = root.asXML();
		logger.info(asXML);
		return root;
	}

	/**
	 * 获取element下指定名称的element
	 * @param element
	 * @param names  一层一层向下传 
	 * 				例如：<a><b></b></a>
	 * 				获取b元素传递的参数为    "a","b"
	 * 				注：   <a><b></b><b></b></a> 获取的b为第一个b元素
	 * @return
	 */
	public static Element getElement(Element element, String... names) {
		for(String name:names){
			element = element.element(name);
		}
		return element;
	}
	
	public static void main(String[] args) throws DocumentException {
		String url = "E:\\workspace\\tzxsaas20161118\\tzxsaas\\resource\\invoice\\electronic\\fpkj_templete.xml";
		File file = new File(url);
		load(file);
	}
}
