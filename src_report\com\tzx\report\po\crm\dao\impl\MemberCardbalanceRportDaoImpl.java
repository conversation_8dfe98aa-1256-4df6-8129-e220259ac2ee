package com.tzx.report.po.crm.dao.impl;

/**
 * Created by gj on 2019-04-30.
 */
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.crm.dao.MemberCardbalanceRportDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository(MemberCardbalanceRportDao.NAME)
public class MemberCardbalanceRportDaoImpl implements MemberCardbalanceRportDao {

    @Resource(name = "genericDaoImpl")
    private GenericDao	dao;

    @Resource(name = "parameterUtils")
    ParameterUtils parameterUtils;

    @Resource
    ConditionUtils conditionUtils;

    @Override
    public JSONObject getMemberCardbalanceQuery(String tenantId, JSONObject condition) throws Exception{
        JSONObject result = new JSONObject();
        String reportSql="";
        long total = 0L;
        String reportCount = "";

        List<JSONObject> list = new ArrayList<JSONObject>();
        List<JSONObject> footerList = new ArrayList<JSONObject>();
        List<JSONObject> structure = new ArrayList<JSONObject>();

        reportSql = parameterUtils.parameterAutomaticCompletion(tenantId, condition, EngineConstantArea.MEMBER_CARD_BALANCE_QUERY_R1);
        total = this.dao.countSql(tenantId, reportSql);
        list = this.dao.query4Json(tenantId,this.dao.buildPageSql(condition, reportSql.toString()));
        structure = conditionUtils.getSqlStructure(tenantId, reportSql.toString());

        reportCount= parameterUtils.parameterAutomaticCompletion(tenantId, condition, EngineConstantArea.MEMBER_CARD_BALANCE_QUERY_R2);
        footerList = this.dao.query4Json(tenantId, reportCount.toString());

        int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;

        result.put("page", pagenum);
        result.put("total",total);
        result.put("rows", list);
        result.put("footer", footerList);
        result.put("structure", structure);
       return result;
    }
}
