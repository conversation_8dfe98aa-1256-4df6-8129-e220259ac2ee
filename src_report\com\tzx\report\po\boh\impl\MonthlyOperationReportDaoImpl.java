package com.tzx.report.po.boh.impl;

import java.io.IOException;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.tzx.report.common.util.ReportExportUtils;
import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.MonthlyOperationReportDao;
import org.springframework.web.bind.annotation.RequestMapping;

@Repository(MonthlyOperationReportDao.NAME)
public class MonthlyOperationReportDaoImpl implements MonthlyOperationReportDao{

	private final String sqlFunction = "select sql from saas_report_engine where report_num = 'SAAS_BI_2017_168' and sql_type='FUNL0'";
	

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	
	@Override
	public JSONObject getMonthlyOperationData(String tenancyID, JSONObject condition) throws Exception {
		Integer type = condition.optInt("type");
		
		StringBuilder sb =new StringBuilder();
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		String begindate = condition.optString("begin_date");
		String enddate = condition.optString("end_date");
		//正序展示
		condition.element("sort", "id");
		condition.element("order", "asc");
		JSONObject result = new JSONObject();
		String reportSql = "";
		long total = 0L;
		List<JSONObject> listOld = new ArrayList<JSONObject>();
		if(begindate.length()>0 && enddate.length()>0 )
		{
			// 导出
			condition.put("selectType", "L1");
			reportSql =getQuerySQL(tenancyID,condition);
			//reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlFunction);
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
				listOld = this.dao.query4Json(tenancyID, reportSql);
				// 获取到自定义的字段字段返回类型
				structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
			}else {
				total = this.dao.countSql(tenancyID,reportSql.toString());
				listOld = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				
				condition.put("selectType", "L0");
				reportSql =getQuerySQL(tenancyID,condition);
				footerList = this.dao.query4Json(tenancyID, reportSql.toString());
			}
		}
		
		// 将￥ 换成_
		for (JSONObject o :listOld) {
			String oString = o.toString().replace("$", "_").replace("\"", "\'");
			JSONObject newJson = JSONObject.fromObject(oString);
			list.add(newJson);
		}

		//动态列的处理
		JSONObject columnObject = getDynamicList(tenancyID, list);
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		//返回的动态列的表头
		result.put("columnObject", columnObject);
		return result;
 	}
	
	
	@Override
	public JSONObject getMonthlyOperationTopData(String tenancyID, JSONObject condition) throws Exception {

		StringBuilder sb =new StringBuilder();
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		String begindate = condition.optString("begin_date");
		String enddate = condition.optString("end_date");
		JSONObject result = new JSONObject();
		String reportSql = "";
		long total = 0L;
		if(begindate.length()>0 && enddate.length()>0 )
		{
			// 导出
			condition.put("selectType", "YS");
			reportSql =getQuerySQL(tenancyID,condition);
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
				list = this.dao.query4Json(tenancyID, reportSql);
				// 获取到自定义的字段字段返回类型
				structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
			}else {
				total = this.dao.countSql(tenancyID,reportSql.toString());
				list = this.dao.query4Json(tenancyID,reportSql.toString());
			}
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}
	
	public String getQuerySQL(String tenancyID ,  JSONObject condition) {
		String reportSql = "";
		List<JSONObject> list = new ArrayList<JSONObject>();
		try {
			reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlFunction);
			System.out.println(reportSql);
			list = this.dao.query4Json(tenancyID, reportSql);
			if(list.size() > 0) {
				reportSql = list.get(0).getString("f_rpt_service_report_month");
			}
			System.out.println(reportSql);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return reportSql;
	}

	/**
	 * 设置动态列
	 * @param list
	 */
	private JSONObject getDynamicList(String tenancyID, List<JSONObject> list) throws Exception{
		if (null == list || list.size() == 0)
			return null;
		JSONObject result = new JSONObject();
		StringBuffer cqColumn = new StringBuffer();
		StringBuffer cqColumnName = new StringBuffer();
		StringBuffer mdColumn = new StringBuffer();
		StringBuffer mdColumnName = new StringBuffer();
//		StringBuffer bcColumn = new StringBuffer();
//		StringBuffer bcColumnName = new StringBuffer();
		StringBuffer sb = new StringBuffer();
		sb.append(" select payment_class||id as payment_class,payment_name1 from payment_way WHERE  payment_name1 like '%券%'");
		List<JSONObject> couponList = this.dao.query4Json(tenancyID, sb.toString());

		//餐券动态列
		String couponColumn = "";
		for (JSONObject coupon : couponList){
			couponColumn = coupon.optString("payment_class");
			String str = list.get(0).optString(couponColumn);
			Double tmp = list.get(0).optDouble(couponColumn);
			if (null != str && !"".equals(str) && null != tmp && tmp != 0){
				cqColumn.append(couponColumn).append(",");
				cqColumnName.append(coupon.optString("payment_name1")).append(",");
			}
		}

		//免单动态列
		sb.setLength(0);
		sb.append(" select reason_code,reason_name from hq_unusual_reason where unusual_type='MD01';");
		List<JSONObject> mdList = this.dao.query4Json(tenancyID, sb.toString());

		for (JSONObject md : mdList){
			couponColumn = md.optString("reason_code");
			String str = list.get(0).optString(couponColumn);
			Double tmp = list.get(0).optDouble(couponColumn);
			if (null != str && !"".equals(str) && null != tmp && tmp != 0){
				mdColumn.append(couponColumn).append(",");
				mdColumnName.append(md.optString("reason_name")).append(",");
			}
		}

		//班次动态列
		/*sb.setLength(0);
		sb.append(" select b.name,c.class_item||'('||b.start_time||'-'||b.end_time||')' as class_item from duty_order b" +
				" LEFT JOIN sys_dictionary c ON b.name= c.class_item_code AND c.class_identifier_code ='duty'" +
				" group by b.name,c.class_item,b.start_time,b.end_time");
		List<JSONObject> bcList = this.dao.query4Json(tenancyID, sb.toString());

		for (JSONObject bc : bcList){
			couponColumn = bc.optString("name");
			Double tmp = list.get(0).optDouble(couponColumn);
			if (null != tmp && tmp != 0){
				bcColumn.append(couponColumn).append(",");
				bcColumnName.append(bc.optString("class_item")).append(",");
			}
		}*/

		if (null != cqColumn && cqColumn.length() > 0)
			result.put("cqColumn", cqColumn.substring(0, cqColumn.length() - 1));
		if (null != cqColumnName && cqColumnName.length() > 0)
			result.put("cqColumnName", cqColumnName.substring(0, cqColumnName.length() - 1));
		if (null != mdColumn && mdColumn.length() > 0)
			result.put("mdColumn", mdColumn.substring(0, mdColumn.length() - 1));
		if (null != mdColumnName && mdColumnName.length() > 0)
			result.put("mdColumnName", mdColumnName.substring(0, mdColumnName.length() - 1));

		return result;
	}

}
