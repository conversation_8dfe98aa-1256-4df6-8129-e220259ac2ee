package com.tzx.cc.baidu.rest;

import java.io.PrintWriter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.tzx.cc.baidu.cont.SuweiConst;
import com.tzx.cc.baidu.service.SuweiService;
import com.tzx.cc.baidu.util.SuweiPropertyUtil;
import com.tzx.pos.base.controller.BaseController;

/**
 * <AUTHOR>
 *
 */
@Controller("SuweiNotifyRest")
@RequestMapping("/suweinotify/suwei")
public class SuweiNotifySaasRest extends BaseController
{
	private static final Logger	logger	= Logger.getLogger(SuweiNotifySaasRest.class);

	@Resource(name = SuweiService.NAME)
	private SuweiService suweiService;
	
	/**
	 * 请求入口
	 * 
	 * @return JSONObject
	 * @throws Exception 
	 */
	@RequestMapping(value = "post", method = RequestMethod.POST)
	// @ResponseBody
	public void post(HttpServletRequest request, HttpServletResponse response, @RequestBody
	JSONObject jsobj) throws Exception
	{
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		JSONObject responseJson = new JSONObject();
		
		try {
				suweiService.thirdSuweiNotifySaas(jsobj,responseJson);
		} catch (Exception e1) {
			responseJson.put(SuweiConst.ERR_CODE, SuweiConst.ERROR_SAAS_INNER);
			responseJson.put(SuweiConst.ERR_DESC, SuweiPropertyUtil.getMsg(String.valueOf(SuweiConst.ERROR_SAAS_INNER)));
			logger.error(e1);
		}
		
		try
		{
			out = response.getWriter();
			out.print(responseJson.toString());
			out.flush();
			out.close();
		}
		catch (Exception e)
		{
			logger.error(e);
		}
		finally
		{
			if (out != null) out.close();
		}
	}
}
