package com.tzx.cc.datasync.bo.util.strategy;

import com.tzx.cc.datasync.bo.dto.DataTransferDaoHelper;
import com.tzx.cc.datasync.bo.dto.PlanetVersionDataTransferDao;
import com.tzx.cc.datasync.bo.util.DriverUtils;
import com.tzx.cc.datasync.bo.util.TempTableUtils;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 以创建临时表的方式比较saas和rif的数据，通过fake_id进行比较
 * Created by XUGY on 2017-02-28.
 */
public class CreateTempTableCompareModified implements CompareModified {
    /**
     * logger
     */
    private static final Logger logger = Logger.getLogger(CreateTempTableCompareModified.class);

    private GenericDao dao;

    @Override
    public Object compare(Object... objs) throws Exception {
        String tenantId = (String) objs[0];
        List<JSONObject> rifData = (List<JSONObject>) objs[1];
        String fromTable = (String) objs[2];
        JSONObject compareFlag = (JSONObject) objs[3];
        String sql = StringUtils.EMPTY;
        if(objs.length>4) {
            sql = (String) objs[4];
        }
        String delSql = StringUtils.EMPTY;
        if(objs.length>5) {
            delSql = (String) objs[5];
        }

        Map<String, List<JSONObject>> resultMap = new HashMap<String, List<JSONObject>>();
        if (rifData == null || rifData.size() == 0) {
            return resultMap;
        }
        //创建临时表 存储同步过来的数据  格式同源库
        String createTempTable = TempTableUtils.createTempTable(tenantId, rifData, fromTable);
        try {
            //将数据插入临时表中
            dao = (GenericDao) SpringConext.getApplicationContext().getBean("genericDaoImpl");
            TempTableUtils.insertTempTable(tenantId, createTempTable, rifData);

            List<JSONObject> updateList = getUpdateData(tenantId, fromTable, createTempTable,sql);
            List<JSONObject> insertList = getInsertData(tenantId, fromTable, createTempTable,sql);
            List<JSONObject> deleteList = getDeleteData(tenantId, fromTable, createTempTable,delSql);
            resultMap.put("add", insertList);
            resultMap.put("update", updateList);
            resultMap.put("delete", deleteList);
        } catch (Exception e) {
            logger.error(e);
            throw new Exception(e);
        } finally {
            TempTableUtils.dropTempTable(tenantId, createTempTable);
        }
        return resultMap;
    }

    /**
     * 获取需要更新的数据
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getUpdateData(String tenantId, String fromTable,
                                           String tempTable,String assistSql) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }

        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();
        sql.append("select distinct sou.* from ");
        sql.append(destName).append(" dest ,");
        sql.append(tempTable).append(" sou");
        sql.append(" where dest.fake_id = to_number(sou.fake_id,'9999999999')");
        sql.append(" and dest.fake_id is not NULL ");
        sql.append(assistSql);
        return TempTableUtils.executeQuery(sql.toString());
    }


    /**
     * 获取新增加的数据
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getInsertData(String tenantId, String fromTable,
                                           String tempTable,String assistSql) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }
        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();

        sql.append("select sou.* from ");
        sql.append(tempTable);
        sql.append(" sou left join ").append(destName);
        sql.append(" dest on to_number(sou.fake_id,'9999999999') = dest.fake_id ");
        sql.append(" where dest.fake_id is null ");
        sql.append(assistSql);

        return TempTableUtils.executeQuery(sql.toString());
    }


    /**
     * 获取需要删除的数据
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getDeleteData(String tenantId, String fromTable,
                                           String tempTable,String assistSql) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }
        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();

        sql.append("select dest.* from ");
        sql.append(destName);
        sql.append(" dest left join ").append(tempTable);
        sql.append(" sou on to_number(sou.fake_id,'9999999999') = dest.fake_id ");
        sql.append(" where sou.fake_id is null and dest.fake_id is not null ");
        sql.append(assistSql);

        return TempTableUtils.executeQuery(sql.toString());
    }

}
