package com.tzx.report.service.rest.boh;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.boh.AbnormityOrderForTakeoutQueryService;



/**
 * 外卖异常订单
 * <AUTHOR>
 *
 */


@Controller("AbnormityOrderForTakeoutQueryRest")
@RequestMapping("/report/abnormityOrderForTakeoutQueryRest")
public class AbnormityOrderForTakeoutQueryRest
{
	
	@Resource(name = AbnormityOrderForTakeoutQueryService.NAME)
	private AbnormityOrderForTakeoutQueryService abnormityOrderForTakeoutQueryService;
	
	@RequestMapping(value = "/getAbnormityBill")
	public void getTheCashierOverDinner(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		JSONObject result = JSONObject.fromObject("{}");
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			result = abnormityOrderForTakeoutQueryService.getAbnormityBill((String) session.getAttribute("tenentid"), p);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result.element("success", false);
			result.element("msg", e.getMessage());
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
}
