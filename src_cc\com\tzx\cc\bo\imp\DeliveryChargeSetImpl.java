package com.tzx.cc.bo.imp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.google.gson.reflect.TypeToken;
import com.tzx.cc.bo.DeliveryChargeSetService;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.hq.bo.dto.HqItemMenuClass;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service(DeliveryChargeSetService.NAME)
public class DeliveryChargeSetImpl implements DeliveryChargeSetService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Override
	public JSONObject loadDeliveryChargeInfo(String tenancyID, JSONObject condition,String organIds) throws Exception
	{

		StringBuilder sql = new StringBuilder();
		if (!condition.get("organ_code").equals("0") && !"".equals(condition.get("organ_code")))
		{
			sql.append("select a.*,b.org_full_name,c.class_item as channel_name from cc_meals_info A LEFT JOIN organ b on b. ID = A .store_id left join sys_dictionary c on c.class_item_code=a.channel and c.class_identifier_code='chanel' where ");
			sql.append("  b.organ_code LIKE '" + condition.get("organ_code") + "%' ");
			// sql.append("  b.id in (select * from get_oids_bycode('"+condition.get("organ_code").toString().trim()+"')) ");
		}
		else
		{
			sql.append("select a.*,b.org_full_name,c.class_item as channel_name from cc_meals_info A LEFT JOIN organ b on b.id=a.store_id left join sys_dictionary c on c.class_item_code=a.channel and c.class_identifier_code='chanel' where 1=1");
			sql.append(" and b.id in ("+organIds+")");
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		Set<String> keys = condition.keySet();
		for (String s : keys)
		{
			if ("tableName".equals(s) || "page".equals(s) || "rows".equals(s) || "sort".equals(s) || "order".equals(s) || "sortName".equals(s))
			{
				continue;
			}
			if (!"t1".equals(s) && !"store_id".equals(s) && !"organ_code".equals(s) && !"_s".equals(s))
			{
				sql.append(" and a." + s + " like '" + condition.optString(s) + "%'");
			}
		}
		long total = this.dao.countSql(tenancyID, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
		JSONObject result = new JSONObject();
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}

	@Override
	public String saveOrUpdate(String tenancyID, JSONObject obj) throws Exception, SystemException
	{
		Integer mealsId = 0;
		String result = "";
		StringBuilder sb = new StringBuilder();
		if(obj.containsKey("dishes")&&obj.optString("meals_type").equals("MR03")){
			JSONArray dishes=obj.optJSONArray("dishes");
			if(dishes.size()>0&&dishes!=null){
			JSONObject dish=dishes.getJSONObject(0);
				obj.put("money", dish.optDouble("price",0.0));	
			}	
		}
		if (obj == null)
		{
			result = "{\"success\" : false , \"msg\" : \"数据为空!\"}";
			return result;
		}
		if (obj.optInt("id") > 0)
		{
			result = "{\"success\" : true , \"msg\" : \"保存成功!\"}";

			sb.append("select *  from cc_meals_info where channel='" + obj.optString("channel") + "' and  store_id='" + obj.optInt("store_id") + "'  and meals_type='" + obj.optString("meals_type") + "' and id<>" + obj.get("id"));
			List<JSONObject> lisas = this.dao.query4Json(tenancyID, sb.toString());

			if (lisas.size() > 0)
			{
				result = "{\"success\" : false , \"msg\" : \"该送餐类型在该渠道已存在\"}";
				return result;
			}
			this.dao.updateIgnorCase(tenancyID, "cc_meals_info", obj);

			if (obj.containsKey("dishes") && obj.get("dishes") != "")
			{
				mealsId = obj.optInt("id");
				StringBuilder delete_sql = new StringBuilder();
				delete_sql.delete(0, delete_sql.length());
				delete_sql.append("delete from cc_meals_info_default a where a.meals_id= '" + mealsId + "' ");
				this.dao.execute(tenancyID, delete_sql.toString());
				@SuppressWarnings("unchecked")
				List<JSONObject> list1 = (List<JSONObject>) GsonUtil.toT(obj.get("dishes").toString(), new TypeToken<List<JSONObject>>()
				{
				}.getType());
				if (list1.size() > 0)
				{
					Iterator<JSONObject> it2 = list1.iterator();
					List<JSONObject> dishesList = new ArrayList<JSONObject>();

					while (it2.hasNext())
					{

						JSONObject dishe = it2.next();
						dishe.put("price", dishe.get("price"));
						dishe.put("item_id", dishe.optInt("itemId"));
						dishe.put("unit_id", dishe.optInt("unitId"));
						dishe.put("tenancy_id", tenancyID);
						dishe.put("meals_id", mealsId);
						String update_sql = "update cc_third_organ_info set package_box_price=" + dishe.optDouble("price",0.0) + ",item_id =" + dishe.optInt("itemId") + ",unit_id =" + dishe.optInt("unitId") + " where shop_id='" + obj.optInt("store_id") + "' and channel='" + obj.optString("channel")
								+ "' ";
						this.dao.execute(tenancyID, update_sql);
						dishesList.add(dishe);

					}
					if (dishesList.size() > 0)
					{
						this.dao.insertBatchIgnorCase(tenancyID, "cc_meals_info_default", dishesList);
					}
				}

			}

		}
		else
		{
			result = "{\"success\" : true , \"msg\" : \"添加成功!\"}";

			sb.append("select *  from cc_meals_info where channel='" + obj.optString("channel") + "' and  store_id='" + obj.optInt("store_id") + "' and meals_type='" + obj.optString("meals_type") + "'");
			List<JSONObject> lisas = this.dao.query4Json(tenancyID, sb.toString());
			if (lisas.size() > 0)
			{
				result = "{\"success\" : false , \"msg\" : \"该送餐类型在该渠道已存在\"}";
				return result;
			}

			mealsId = Integer.valueOf(this.dao.insertIgnorCase(tenancyID, "cc_meals_info", obj).toString());
			if (obj.containsKey("dishes") && obj.get("dishes") != "")
			{
				@SuppressWarnings("unchecked")
				List<JSONObject> list1 = (List<JSONObject>) GsonUtil.toT(obj.get("dishes").toString(), new TypeToken<List<JSONObject>>()
				{
				}.getType());
				if (list1.size() > 0)
				{
					Iterator<JSONObject> it2 = list1.iterator();
					List<JSONObject> dishesList = new ArrayList<JSONObject>();

					while (it2.hasNext())
					{

						JSONObject dishe = it2.next();
						dishe.put("price", dishe.get("price"));
						dishe.put("item_id", dishe.optInt("itemId"));
						dishe.put("unit_id", dishe.optInt("unitId"));
						dishe.put("tenancy_id", tenancyID);
						dishe.put("meals_id", mealsId);
						dishesList.add(dishe);

					}
					if (dishesList.size() > 0)
					{
						this.dao.insertBatchIgnorCase(tenancyID, "cc_meals_info_default", dishesList);
					}
				}

			}
		}

		return result;
	}

	// @Override
	// public String saveOrUpdate(String tenancyID, JSONObject obj) throws
	// Exception, SystemException {
	// Integer mealsId = 0;
	// String result = "";
	//
	// StringBuilder sb = new StringBuilder();
	// if (obj == null) {
	// result = "{\"success\" : false , \"msg\" : \"数据为空!\"}";
	// return result;
	// }
	//
	// if (obj.optInt("id") > 0) {
	// result = "{\"success\" : true , \"msg\" : \"保存成功!\"}";
	//
	// sb.append("select *  from cc_meals_info where store_id='" +
	// obj.optInt("store_id")
	// + "' and valid_state='1' and meals_type='" + obj.optString("meals_type")
	// + "' and id<>"
	// + obj.get("id"));
	// List<JSONObject> lisas = this.dao.query4Json(tenancyID, sb.toString());
	//
	// if (lisas.size() > 0 && obj.optInt("valid_state") == 1 &&
	// !obj.containsKey("channel")) {
	// result = "{\"success\" : false , \"msg\" : \"一个机构下同一送餐类型只能有一个是有效的!\"}";
	// return result;
	// }
	//
	// if (lisas.size() > 0 && obj.optInt("valid_state") == 1 &&
	// obj.containsKey("channel")) {
	// result = "{\"success\" : false , \"msg\" : \"一个机构下同一送餐类型只能有一个是有效的!\"}";
	// return result;
	// }
	//
	// sb.delete(0, sb.length());
	// sb.append("select id from cc_meals_info where meals_type='" +
	// obj.optString("meals_type")
	// + "' and  service_name='" + obj.optString("service_name") +
	// "'  and store_id='"
	// + obj.optString("store_id") + "' and id<>" + obj.get("id"));
	// List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
	// if (list.size() > 0 && !"".equals(obj.optString("service_name"))) {
	// result = "{\"success\" : false , \"msg\" : \"名称重复，请重新录入!\"}";
	// return result;
	// }
	// sb.delete(0, sb.length());
	// sb.append("select id from cc_meals_info where meals_type='" +
	// obj.optString("store_id")
	// + "'  and  is_defaul='" + obj.optString("is_defaul") +
	// "' and valid_state='1' and id<>"
	// + obj.get("id"));
	// List<JSONObject> listMr = this.dao.query4Json(tenancyID, sb.toString());
	// if (listMr.size() > 0) {
	// result = "{\"success\" : false , \"msg\" : \"一个机构下同一送餐类型只能有一个默认送餐费!\"}";
	// return result;
	// }
	//
	// this.dao.updateIgnorCase(tenancyID, "cc_meals_info", obj);
	//
	// if (obj.containsKey("dishes") && obj.get("dishes") != "") {
	// mealsId = obj.optInt("id");
	// StringBuilder delete_sql = new StringBuilder();
	// delete_sql.delete(0, delete_sql.length());
	// delete_sql.append("delete from cc_meals_info_default a where a.meals_id= '"
	// + mealsId + "' ");
	// this.dao.execute(tenancyID, delete_sql.toString());
	// @SuppressWarnings("unchecked")
	// List<JSONObject> list1 = (List<JSONObject>)
	// GsonUtil.toT(obj.get("dishes").toString(),
	// new TypeToken<List<JSONObject>>() {
	// }.getType());
	// if (list1.size() > 0) {
	// Iterator<JSONObject> it2 = list1.iterator();
	// List<JSONObject> dishesList = new ArrayList<JSONObject>();
	//
	// while (it2.hasNext()) {
	//
	// JSONObject dishe = it2.next();
	// dishe.put("price", dishe.get("price"));
	// dishe.put("item_id", dishe.optInt("itemId"));
	// dishe.put("unit_id", dishe.optInt("unitId"));
	// dishe.put("tenancy_id", tenancyID);
	// dishe.put("meals_id", mealsId);
	// dishesList.add(dishe);
	//
	// }
	// if (dishesList.size() > 0) {
	// this.dao.insertBatchIgnorCase(tenancyID, "cc_meals_info_default",
	// dishesList);
	// }
	// }
	//
	// }
	//
	// } else {
	// result = "{\"success\" : true , \"msg\" : \"添加成功!\"}";
	//
	// sb.append("select *  from cc_meals_info where store_id='" +
	// obj.optInt("store_id")
	// + "' and valid_state='1' and meals_type='" + obj.optString("meals_type")
	// + "'");
	// List<JSONObject> lisas = this.dao.query4Json(tenancyID, sb.toString());
	// if (lisas.size() > 0) {
	// result = "{\"success\" : false , \"msg\" : \"一个机构下同一送餐类型只能有一个是有效的!\"}";
	// return result;
	// }
	// sb.delete(0, sb.length());
	// sb.append("select id from cc_meals_info where  meals_type='" +
	// obj.optString("meals_type")
	// + "' and service_name='" + obj.optString("service_name") +
	// "' and  store_id='"
	// + obj.optString("store_id") + "' ");
	// List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
	// if (list.size() > 0 && !"".equals(obj.optString("service_name"))) {
	// result = "{\"success\" : false , \"msg\" : \"名称重复，请重新录入!\"}";
	// return result;
	// }
	// sb.delete(0, sb.length());
	// sb.append("select id from cc_meals_info where meals_type='" +
	// obj.optString("meals_type")
	// + "' and  store_id='" + obj.optString("store_id") + "'  and  is_defaul='"
	// + obj.optString("is_defaul") + "' ");
	// List<JSONObject> listMr = this.dao.query4Json(tenancyID, sb.toString());
	// if (listMr.size() > 0) {
	// result = "{\"success\" : false , \"msg\" : \"一个机构下同一送餐类型只能有一个默认的送餐费!\"}";
	// return result;
	// }
	//
	// mealsId = Integer.valueOf(this.dao.insertIgnorCase(tenancyID,
	// "cc_meals_info", obj).toString());
	// if (obj.containsKey("dishes") && obj.get("dishes") != "") {
	// @SuppressWarnings("unchecked")
	// List<JSONObject> list1 = (List<JSONObject>)
	// GsonUtil.toT(obj.get("dishes").toString(),
	// new TypeToken<List<JSONObject>>() {
	// }.getType());
	// if (list1.size() > 0) {
	// Iterator<JSONObject> it2 = list1.iterator();
	// List<JSONObject> dishesList = new ArrayList<JSONObject>();
	//
	// while (it2.hasNext()) {
	//
	// JSONObject dishe = it2.next();
	// dishe.put("price", dishe.get("price"));
	// dishe.put("item_id", dishe.optInt("itemId"));
	// dishe.put("unit_id", dishe.optInt("unitId"));
	// dishe.put("tenancy_id", tenancyID);
	// dishe.put("meals_id", mealsId);
	// dishesList.add(dishe);
	//
	// }
	// if (dishesList.size() > 0) {
	// this.dao.insertBatchIgnorCase(tenancyID, "cc_meals_info_default",
	// dishesList);
	// }
	// }
	//
	// }
	// }
	//
	// return result;
	//
	// }

	@Override
	public Boolean saveCopyDeliveryChargeInfo(String tenancyID, JSONObject obj) throws Exception, SystemException
	{
		Boolean flag = true;
		String toIds = obj.getString("torgan_id");
		String[] ids = toIds.split(",");
		for (String id1 : ids)
		{
			if (obj.containsKey("copy_send_meals") && obj.get("copy_send_meals") != "")
			{
				@SuppressWarnings("unchecked")
				List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(obj.get("copy_send_meals").toString(), new TypeToken<List<JSONObject>>()
				{
				}.getType());
				Iterator<JSONObject> it2 = list.iterator();
				while (it2.hasNext())
				{
					JSONObject meals_info = it2.next();
					meals_info.put("tenancy_id", obj.get("tenancy_id"));
					meals_info.put("yuan_store_id", meals_info.optInt("store_id"));
					meals_info.put("store_id", id1);
					// meals_info.put("valid_state", "1");
					meals_info.put("last_operator", obj.get("last_operator"));
					meals_info.put("last_updatetime", obj.get("last_updatetime"));
					if (meals_info.optString("meals_type").equals("PS01"))
					{
						String query_service_fee_of_organ_sql = "select id from  hq_service_fee_of_organ a where a.service_fee_id=" + meals_info.optString("service_id");//" and store_id=" + meals_info.optString("store_id") + "";固定服务费只要有一条数据即可，不用针对门店单独设置
						List<JSONObject> service_fee_of_organ_list = this.dao.query4Json(tenancyID, query_service_fee_of_organ_sql);
						if (service_fee_of_organ_list.size() > 0)
						{
							String delete_meals_info_ps01_sql = "DELETE  from cc_meals_info  k where k.store_id= " + id1 + " and k.meals_type='PS01' and k.channel='"+meals_info.optString("channel")+"'";
							this.dao.execute(tenancyID, delete_meals_info_ps01_sql);
							this.dao.insertIgnorCase(tenancyID, "cc_meals_info", meals_info);
						}
						
					}
					else if (meals_info.optString("meals_type").equals("MR03"))
					{
						String meals_info_default_sql = "SELECT item_id,unit_id,price from cc_meals_info_default  a left join cc_meals_info b on b.id=a.meals_id where a.meals_id=" + meals_info.optInt("id") + " and b.channel='"+meals_info.optString("channel")+"' ";
						List<JSONObject> meals_info_default_list = this.dao.query4Json(tenancyID, meals_info_default_sql);
						if (meals_info_default_list.size() > 0)
						{
							String item_menu_details_sql = "SELECT item_id from hq_item_menu_details a  left join hq_item_menu_class d on a.id=d.details_id LEFT JOIN  hq_item_menu  b on b.id=a.item_menu_id LEFT JOIN hq_item_menu_organ c on c.item_menu_id=b.id where c.store_id=" + id1 + " and a.item_id=" + meals_info_default_list.get(0).optInt("item_id")+ " and d.chanel='"+meals_info.optString("channel")+"'";
							List<JSONObject> item_menu_details_list = this.dao.query4Json(tenancyID, item_menu_details_sql);
							if (item_menu_details_list.size() > 0)
							{
								String cc_meals_info_default_mr03_sql = "DELETE  from cc_meals_info_default  k where k.meals_id IN (select id from cc_meals_info  k where k.store_id= " + id1 + " and k.meals_type='MR03' and k.channel='"+meals_info.optString("channel")+"') ";
								this.dao.execute(tenancyID, cc_meals_info_default_mr03_sql);
								String delete_meals_info_mr03_sql = "DELETE  from cc_meals_info  k where k.store_id= " + id1 + " and k.meals_type='MR03' and k.channel='"+meals_info.optString("channel")+"'";
								this.dao.execute(tenancyID, delete_meals_info_mr03_sql);
								Object dic = this.dao.insertIgnorCase(tenancyID, "cc_meals_info", meals_info);
								StringBuilder sql2 = new StringBuilder();
								sql2.append("SELECT " + dic.toString() + " as meals_id, b.tenancy_id,b.item_id,b.unit_id,b.price,b.remark FROM cc_meals_info A LEFT JOIN cc_meals_info_default b ON b.meals_id = A . ID where a.store_id='" + meals_info.optInt("yuan_store_id") + "'");
								sql2.append(" and a.meals_type='MR03' and b.meals_id='" + meals_info.getInt("id") + "'");
								List<JSONObject> listMr2 = this.dao.query4Json(tenancyID, sql2.toString());

								if (listMr2.size() > 0)
								{
									this.dao.insertBatchIgnorCase(tenancyID, "cc_meals_info_default", listMr2);
								}
							}
						}
					}else if (meals_info.optString("meals_type").equals("QJ02"))
					{
							String delete_meals_info_ps01_sql = "DELETE  from cc_meals_info  k where k.store_id= " + id1 + " and k.meals_type='QJ02' and k.channel='"+meals_info.optString("channel")+"'";
							this.dao.execute(tenancyID, delete_meals_info_ps01_sql);
							this.dao.insertIgnorCase(tenancyID, "cc_meals_info", meals_info);
					}

				}
			}
		}

		return flag;
	}

	@Override
	public JSONObject loadCopyList(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		if (condition.containsKey("store_id"))
		{
			sql.append("select a.*,b.org_full_name ,c.class_item as channel_name from cc_meals_info A,organ b,sys_dictionary c  where b.id=a.store_id and a.channel=c.class_item_code and c.class_identifier_code='chanel'");
			sql.append(" and a.store_id = " + condition.optInt("store_id") + " ");
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		}
		return result;
	}

	@Override
	public String customize(String tenancyId, Object param) throws Exception
	{
		String result = "";
		StringBuilder sql = new StringBuilder();
		JSONObject condition = (JSONObject) param;
		sql.append("SELECT a.* from hq_service_fee_type a LEFT JOIN hq_service_fee_of_organ b on b.service_fee_id=a.id where b.store_id='" + condition.optInt("store_id") + "' and a.fee_type='FJ02'");
		List<JSONObject> list3 = this.dao.query4Json(tenancyId, sql.toString());
		result = com.tzx.framework.common.util.JsonUtils.list2json(list3);
		return result;
	}
	
	@Override
	public String findValuationMethod(String tenancyId, JSONObject obj)
			throws Exception {
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT a.* from hq_service_fee_type a LEFT JOIN hq_service_fee_of_organ b on b.service_fee_id=a.id where 1=1 and a.fee_type='FJ02'");
		List<JSONObject> list3 = this.dao.query4Json(tenancyId, sql.toString());
		result=list3.get(0);
		return result.toString();
	}

	public String getTargetTree(String tenentId, String chanel, String storeId, Integer classId, String mealsId) throws Exception
	{

		if (!Tools.hv(chanel) || !Tools.hv(storeId))
		{
			return "";
		}

		if (classId == 0)
		{
			StringBuilder sql = new StringBuilder();
			// 生成根节点
			sql.append("SELECT org. ID,org.org_full_name FROM hq_item_menu_organ menuorg JOIN organ org ON org. ID = menuorg.store_id ");
			sql.append(" JOIN hq_item_menu itemmenu ON itemmenu. ID = menuorg.item_menu_id");
			sql.append(" AND itemmenu.valid_state = '1' AND menuorg.store_id = '" + storeId + "'");
			List<JSONObject> listJson = this.dao.query4Json(tenentId, sql.toString());
			sql.delete(0, sql.length());
			if (listJson.size() == 0)
			{
				return "该机构还未分配餐谱";
			}
			else
			{

				sql.append("select 0 as id,0 as father_id,class_item_code as chanel,class_item_code as code ,");
				sql.append("class_item as text,class_item as item_name from sys_dictionary where 1=1");
				sql.append(" and class_identifier_code = 'chanel'");
				sql.append(" and class_item_code='" + chanel + "'");
				sql.append(" order by chanel");
				@SuppressWarnings("unchecked")
				List<HqItemMenuClass> listDict = (List<HqItemMenuClass>) dao.query(tenentId, sql.toString(), HqItemMenuClass.class);

				sql.delete(0, sql.length());

				// 检索菜品类别表获得相应的渠道的菜品类别
			/*	sql.append("select * from (select a.id,a.id as item_class,a.chanel,a.father_id,a.itemclass_name as item_name,a.itemclass_code as item_code, a.itemclass_code as code");
				sql.append(",(select case when count(z1.class)>0 then 'closed' else '' end  from  hq_item_menu_class  z1 where z1.details_id in(select z2.id from hq_item_menu_details z2 where z2.item_menu_id=(SELECT item_menu_id FROM hq_item_menu_organ LEFT JOIN hq_item_menu aa on aa.id = item_menu_id WHERE aa.valid_state='1' and store_id ="+ storeId + ")) and z1.class= a.id and z1.chanel='" + chanel + "') as state  from hq_item_class a ");
				sql.append(" where 1=1 and a.valid_state='1'");
				sql.append(" and chanel='" + chanel + "'");
				sql.append(" order by itemclass_code ) k where k.STATE ='closed' ");*/
				// System.out.println("sql1:"+sql);
				sql.append("select 	A . ID,A . ID AS item_class,A .chanel,A .father_id,A .itemclass_name AS item_name,A .itemclass_code AS item_code,A .itemclass_code AS code,'' as state from hq_item_class a where a.id in (SELECT DISTINCT K .father_id FROM");
				sql.append(" (SELECT A . ID,A . ID AS item_class,A .chanel,A .father_id,A .itemclass_name AS item_name,A .itemclass_code AS item_code,A .itemclass_code AS code,");
				sql.append(" (SELECT CASE WHEN COUNT (z1. CLASS) > 0 THEN 'closed' ELSE '' END FROM hq_item_menu_class z1 WHERE z1.details_id IN ");
				sql.append(" (SELECT z2. ID FROM hq_item_menu_details z2 WHERE z2.item_menu_id =");
				sql.append(" (SELECT item_menu_id FROM hq_item_menu_organ LEFT JOIN hq_item_menu aa ON aa. ID = item_menu_id WHERE aa.valid_state = '1' AND store_id = "+ storeId + "))");
				sql.append(" AND z1. CLASS = A . ID AND z1.chanel = '" + chanel + "') AS STATE");
				sql.append(" FROM 	hq_item_class A");
				sql.append(" WHERE 1 = 1 AND A .valid_state = '1' AND chanel = '" + chanel + "' ORDER BY itemclass_code");
				sql.append(" ) K");
				sql.append(" LEFT JOIN hq_item_menu_class b ON b. CLASS = K.item_class AND b.chanel = '" + chanel + "' LEFT JOIN hq_item_menu_details C ON C.ID = b.details_id");
				sql.append(" LEFT JOIN hq_item_menu_organ d ON d.item_menu_id = C .item_menu_id AND d.store_id = "+ storeId + "");
				sql.append("  WHERE b.chanel = '" + chanel + "' AND d.store_id = "+ storeId + " )");
				sql.append(" union all");
				sql.append(" select DISTINCT(k.id),k.item_class,k.chanel,k.father_id,k.item_name,k.item_code,k.code,k.state from");
				sql.append(" (SELECT A . ID,A.ID AS item_class,A.chanel,A.father_id,A.itemclass_name AS item_name,A.itemclass_code AS item_code,A.itemclass_code AS code,");
				sql.append(" (SELECT CASE WHEN  COUNT(z1.CLASS) > 0 THEN 'closed' ELSE '' END FROM hq_item_menu_class z1 WHERE z1.details_id IN (SELECT z2. ID FROM hq_item_menu_details z2 WHERE 	z2.item_menu_id = (");
				sql.append(" SELECT item_menu_id FROM hq_item_menu_organ LEFT JOIN hq_item_menu aa ON aa. ID = item_menu_id 	WHERE aa.valid_state = '1' AND store_id = "+ storeId + "))AND z1. CLASS = A . ID AND z1.chanel = '" + chanel + "' ) AS STATE");
				sql.append(" FROM hq_item_class A");
				sql.append(" WHERE 1 = 1 AND A .valid_state = '1' AND chanel = '" + chanel + "'");
				sql.append(" ORDER BY itemclass_code) k LEFT JOIN hq_item_menu_class b ON b.CLASS = k.item_class and b.chanel='" + chanel + "'");
				sql.append(" LEFT JOIN hq_item_menu_details C ON C.ID = b.details_id");
				sql.append(" LEFT JOIN  hq_item_menu_organ d ON d.item_menu_id = C .item_menu_id   and d.store_id="+ storeId + " where b.chanel='" + chanel + "' and d.store_id="+ storeId + " and k.state='closed'");
				@SuppressWarnings("unchecked")
				List<HqItemMenuClass> listRes = (List<HqItemMenuClass>) dao.query(tenentId, sql.toString(), HqItemMenuClass.class);

				// StringBuilder sqlForItemInfo = new StringBuilder();
				// sqlForItemInfo.append(" select menuclass.item_name as
				// text,menuclass.id as class_id,menuclass.chanel as
				// chanel,menuclass.id as CHECKED,");
				// sqlForItemInfo.append(" menuclass.class as
				// item_class,menuclass.item_name as
				// item_name,menuclass.item_english as item_english,");
				// sqlForItemInfo.append(" details.id as details_id
				// ,details.item_id as item_id,details.item_id as
				// id,details.item_menu_id,");
				// sqlForItemInfo.append(" info.item_code as item_code");
				// sqlForItemInfo.append(" from hq_item_menu_details details");
				// sqlForItemInfo.append(" LEFT JOIN hq_item_info info on
				// info.id = details.item_id");
				// sqlForItemInfo.append(" join hq_item_menu_class menuclass on
				// menuclass.details_id = details.id and menuclass.chanel = '"
				// + chanel + "'");
				// sqlForItemInfo.append(" where details.item_menu_id = '" +
				// itemMenuId + "'");
				// sqlForItemInfo.append(" and details.valid_state='1'");
				// sqlForItemInfo.append(" order by item_code");
				//
				// @SuppressWarnings("unchecked")
				// List<HqItemMenuClass> listOfMenu = (List<HqItemMenuClass>)
				// this.dao.query(tenentId, sqlForItemInfo.toString(),
				// HqItemMenuClass.class);

				Map<Object, HqItemMenuClass> map = new HashMap<Object, HqItemMenuClass>();

				// 初始化每条记录的children变量
				for (HqItemMenuClass json : listRes)
				{
					json.setChildren(new ArrayList<HqItemMenuClass>());
					map.put(json.getItemClass(), json);
				}

				// 将菜品放到相应的小类下面
				// for (HqItemMenuClass itemInfo : listOfMenu)
				// {
				// if (map.containsKey(itemInfo.getItemClass()))
				// {
				// List<HqItemMenuClass> l1 =
				// map.get(itemInfo.getItemClass()).getChildren();
				// l1.add(itemInfo);
				// }
				// }

				// 设置上下级关系
				for (HqItemMenuClass json : listRes)
				{
					String itemclassCode = json.getCode();
					if ("".equals(itemclassCode))
					{
						json.setText(json.getItemName());
					}
					else
					{
						json.setText("(" + itemclassCode + ")" + json.getItemName());
					}

					if (map.containsKey(json.getFatherId()))
					{
						List<HqItemMenuClass> l1 = map.get(json.getFatherId()).getChildren();
						l1.add(json);
					}
				}

				for (HqItemMenuClass gO : listDict)
				{
					gO.setChildren(new ArrayList<HqItemMenuClass>());
					for (HqItemMenuClass j1 : listRes)
					{
						if (j1.getFatherId().equals(0) && j1.getChanel().equals(gO.getChanel()))
						{

							List<HqItemMenuClass> l2 = gO.getChildren();
							l2.add(j1);
						}

					}
				}

				return com.tzx.framework.common.util.JsonUtils.list2json(listDict);
			}
		}

		else
		{
			StringBuilder sqlForItemInfo = new StringBuilder();
			sqlForItemInfo.append(" select 1 as v,menuclass.item_name||'('||unit.unit_name||')' as text,menuclass.id as class_id,menuclass.chanel as chanel,");
			sqlForItemInfo.append(" menuclass.class as item_class,menuclass.item_name as item_name,menuclass.item_english as item_english,");
			sqlForItemInfo.append(" details.id as details_id ,details.item_id as item_id,details.item_id as id,details.item_menu_id,");
			// sqlForItemInfo.append(" info.item_code as item_code,info.is_combo ,unit.standard_price as price,unit.id as unit_id");
			sqlForItemInfo.append(" info.item_code as item_code,info.is_combo ,DD.price AS price,unit.id as unit_id");
			sqlForItemInfo.append(" from hq_item_menu_details details");
			sqlForItemInfo.append(" LEFT JOIN hq_item_info info on info.id = details.item_id");
			sqlForItemInfo.append(" join hq_item_menu_class menuclass on menuclass.details_id = details.id  LEFT JOIN hq_item_unit unit on unit.item_id=details.item_id and unit.valid_state='1' and menuclass.chanel = '" + chanel
					+ "' LEFT JOIN hq_item_pricesystem dd on dd.item_unit_id=unit.id and CAST(dd.price_system  AS VARCHAR )= (select price_system from organ where ID=" + storeId + ") and dd.chanel='" + chanel + "'");
			sqlForItemInfo.append(" where details.item_menu_id =(SELECT item_menu_id FROM hq_item_menu_organ LEFT JOIN hq_item_menu aa ON aa. ID = item_menu_id WHERE	aa.valid_state = '1' AND  store_id =" + storeId + ") ");
			sqlForItemInfo.append(" and details.valid_state='1' and menuclass.class=" + classId);
			sqlForItemInfo.append(" order by item_code");
			@SuppressWarnings("unchecked")
			List<HqItemMenuClass> listOfMenu = (List<HqItemMenuClass>) this.dao.query(tenentId, sqlForItemInfo.toString(), HqItemMenuClass.class);

			if (null != mealsId && !"".equals(mealsId))
			{
				List<JSONObject> listCheckedUnitId = this.dao.query4Json(tenentId, "SELECT UNIT_ID FROM CC_MEALS_INFO_DEFAULT WHERE MEALS_ID =" + mealsId);

				for (int i = 0; i < listCheckedUnitId.size(); i++)
				{
					int unitId = listCheckedUnitId.get(i).optInt("unit_id");
					for (HqItemMenuClass itemMenu : listOfMenu)
					{
						if (unitId == itemMenu.getUnitId())
						{
							itemMenu.setChecked("true");
						}
					}
				}
			}

			// StringBuilder sb = new StringBuilder();
			// sb.append(
			// "select id,item_name,item_code from hq_item_info where id in(select item_id from hq_item_menu_details where item_menu_id =(SELECT item_menu_id FROM hq_item_menu_organ WHERE store_id ="
			// + storeId + ") and valid_state='1') and is_combo='Y'");
			// List<JSONObject> listcomb = this.dao.query4Json(tenentId,
			// sb.toString());
			// System.out.println("222:" + sb);
			if (listOfMenu != null)
			{
				// HashMap<Integer, HqItemMenuClass> temp = new HashMap<Integer,
				// HqItemMenuClass>();
				// for (HqItemMenuClass hqi : listOfMenu) {
				// temp.put(hqi.getItemId(), hqi);
				//
				// }
				// // Set<Integer> set1 = temp.keySet();
				// for (JSONObject combo : listcomb) {
				// String codeString = combo.optString("item_code");
				// String nameString = combo.optString("item_name");
				// Integer comboId = combo.optInt("id");
				//
				// sqlForItemInfo.setLength(0);
				// sqlForItemInfo.append("select details_id from hq_item_combo_details where iitem_id="
				// + comboId
				// + " and is_itemgroup='N'");
				// System.out.println("333:" + sqlForItemInfo);
				// List<JSONObject> list33 = this.dao.query4Json(tenentId,
				// sqlForItemInfo.toString());
				// for (JSONObject jo33 : list33) {
				// Integer key33 = jo33.optInt("details_id");
				// if (temp.containsKey(key33)) {
				// HqItemMenuClass it33 = temp.get(key33);
				//
				// }
				//
				// }
				//
				// sqlForItemInfo.setLength(0);
				// sqlForItemInfo
				// .append("select item_id from hq_item_group_details where item_group_id in(select details_id from hq_item_combo_details where iitem_id="
				// + comboId + " and is_itemgroup='Y')");
				//
				// System.out.println("444" + sqlForItemInfo);
				// List<JSONObject> list34 = this.dao.query4Json(tenentId,
				// sqlForItemInfo.toString());
				// for (JSONObject jo33 : list34) {
				// Integer key33 = jo33.optInt("item_id");
				// if (temp.containsKey(key33)) {
				// HqItemMenuClass it33 = temp.get(key33);
				//
				// }
				//
				// }
				//
				// }

				// List<HqItemMenuClass> listOfMenu2 = new
				// ArrayList<HqItemMenuClass>();
				// for(Integer key:set1)
				// {
				// listOfMenu2.add(temp.get(key));
				// }
				//
				// Collections.sort(listOfMenu2);

				return com.tzx.framework.common.util.JsonUtils.list2json(listOfMenu);

			}

			return "[]";
		}
	}

	public String getTargetTree(String tenentId, String chanel, String storeId, Integer classId) throws Exception
	{
		// TODO Auto-generated method stub
		return null;
	}


}
