package com.tzx.report.service.rest.boh;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.boh.BusinessSummaryStatisticshualaishiReportService;
import com.tzx.report.common.util.ConditionUtils;

/**
 * 
 * 营业汇总统计报表
 *
 */

@Controller("BusinessSummaryStatisticshualaishiReportRest")
@RequestMapping("/report/businessSummaryStatisticshualaishiReportRest")
public class BusinessSummaryStatisticshualaishiReportRest
{
	@Resource(name = BusinessSummaryStatisticshualaishiReportService.NAME)
	private BusinessSummaryStatisticshualaishiReportService		businessSummaryStatisticshualaishiReportService;
	
	@Resource
	ConditionUtils conditionUtils;
	
	@RequestMapping(value = "/getBusinessSummaryStatistics")
	public void getBusinessSummaryStatistics(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)||((Integer)session.getAttribute("valid_state")).equals(0)){
				String storeString = "store_id";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"99999999".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'99999999'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
			}else{
				String storeString = "store_id";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"99999999".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'99999999'".equals(p.optString(storeString)) 
						){
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}
			
			result = businessSummaryStatisticshualaishiReportService.getBusinessSummaryStatistics((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
		 
}
