package com.tzx.report.service.rest.boh;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.boh.OrderFlowQueryService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ReportExportUtils;


/**
 * 新外卖订单流水查询
 * <AUTHOR>
 *
 */

@Controller("OrderFlowQueryRest")
@RequestMapping("/report/orderFlowQueryRest")
public class OrderFlowQueryRest
{
	
	@Resource(name = OrderFlowQueryService.NAME)
	private OrderFlowQueryService orderFlowQueryService;
	
	
	@Resource(name = "conditionUtils")
	ConditionUtils conditionUtils;
	
	
	/**
	 * 外卖订单流水查询
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getOrderFlowQuery")
	public void getOrderFlowQuery(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			if(p.optString("p_store_id").length()==0){
				p.element("p_store_id", "'"+session.getAttribute("user_organ_codes_group")+"'");
			}
			
			result = orderFlowQueryService.getOrderFlowQuery((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	/**
	 * 订单明细查询
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getOrderDetailInquiry")
	public void getOrderDetailInquiry(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			result = orderFlowQueryService.getOrderDetailInquiry((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	/**
	 * 订单优惠查询
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	
	@RequestMapping(value = "/getOrderDiscountInquiry")
	public void getOrderDiscountInquiry(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			result = orderFlowQueryService.getOrderDiscountInquiry((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	
	/**
	 * 订单付款查询
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	
	@RequestMapping(value = "/getOrderPaymentEnquiry")
	public void getOrderPaymentEnquiry(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			result = orderFlowQueryService.getOrderPaymentEnquiry((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
			//导出
			@RequestMapping(value = "/exportDate")
			public void exportDate(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
			{
				response.setContentType("text/html; charset=UTF-8");
				response.setContentType("text/html");
				response.setCharacterEncoding("UTF-8");
				HttpSession session = request.getSession();
				HSSFWorkbook workBook = null;
				String exportName = null;
				try
				{

					workBook = new HSSFWorkbook();
				       
					JSONObject p = JSONObject.fromObject("{}");

					Map<String, String[]> map = request.getParameterMap();

					for (String key : map.keySet())
					{
						p.put(key, map.get(key)[0]);
					}
					
					if(p.optString("p_store_id").length()==0){
						p.element("p_store_id", "'"+session.getAttribute("user_organ_codes_group")+"'");
					}	
					
					exportName = p.optString("exportName");

					// 报表导出不需要分页，默认设置成1
					p.put("page", 1);

					workBook = orderFlowQueryService.exportDate((String) session.getAttribute("tenentid"), p ,workBook);
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}
				try
				{
					ReportExportUtils.download(workBook,response,exportName);
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}
			}
}
