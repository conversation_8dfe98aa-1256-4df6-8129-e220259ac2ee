package com.tzx.cc.bo.imp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.cc.bo.OrderingAbnormalReasonsetService;
import com.tzx.cc.bo.dto.OrderingAbnormalReason;
import com.tzx.framework.bo.CodeService;
import com.tzx.framework.bo.dto.Combotree;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.hq.bo.dto.HqItemClass;

@Service(OrderingAbnormalReasonsetService.NAME)
public class OrderingAbnormalReasonsetImpl implements OrderingAbnormalReasonsetService
{

	@Resource(name = "genericDaoImpl")
	private GenericDao		dao;

	@Resource(name = "codeService")
	private CodeService		codeService;

	public static Pattern	pattern	= Pattern.compile("[0-9]*");

	@Override
	public JSONObject getOrderingAbnormalReason(String tenancyID, JSONObject condition)
	{
		StringBuilder sb = new StringBuilder();

		sb.append("select a.tenancy_id,a.id,a.reason_code,a.unusual_type,a.father_id,a.reason_name,a.phonetic_code,a.five_code,a.remark,a.valid_state,a.last_operator,a.last_updatetime,b.reason_name as father_name,b.reason_code as parent_code,f.class_item from hq_unusual_reason a LEFT JOIN hq_unusual_reason b on a.father_id = b.id LEFT JOIN sys_dictionary f on a.unusual_type  = f.class_item_code and f.class_identifier_code ='unusual_type'   where 1=1");

		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;

		@SuppressWarnings("unchecked")
		Set<String> keys1 = condition.keySet();

		for (String s : keys1)
		{
			if ("page".equals(s) || "rows".equals(s) || "tableName".equals(s) || "id".equals(s) || "reason_code".equals(s) || "reason_name".equals(s) || "sort".equals(s) || "order".equals(s))
			{
				if ("id".equals(s))
				{
					StringBuilder sb1 = new StringBuilder();
					sb1.append("WITH RECURSIVE cc as(select aa.id from hq_unusual_reason aa where id = " + condition.get(s) + " UNION ALL select s.id from hq_unusual_reason s,cc WHERE s.father_id=cc.id) select * from cc");
					try
					{
						List<JSONObject> list = this.dao.query4Json(tenancyID, sb1.toString());
						sb1.delete(0, sb1.length());
						for (JSONObject jo : list)
						{
							sb1.append(jo.getInt("id") + ",");
						}
						sb1.delete(sb1.length() - 1, sb1.length());
						sb.append(" AND a.id in(" + sb1 + ") ");
					}
					catch (Exception e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}

				}
				else if ("reason_code".equals(s) || "reason_name".equals(s))
				{

					sb.append(" AND a." + s);

					Object o = condition.get(s);

					sb.append(" LIKE '%" + o + "%'");

				}

				continue;
			}

			sb.append(" AND a." + s);

			Object o = condition.get(s);
			if (isNum(o))
			{
				sb.append("='" + o + "'");
			}
			else
			{
				sb.append(" LIKE '%" + o + "%'");
			}

		}
		try
		{
			long total = this.dao.countSql(tenancyID, sb.toString());

			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sb.toString()));

			JSONObject result = new JSONObject();

			result.put("page", pagenum);
			// result.put("rows", limit);
			result.put("total", total);
			// result.put("totalpage", total / limit);
			result.put("rows", list);

			return result;

		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}

	}

	private static boolean isNum(Object obj)
	{
		return pattern.matcher(obj.toString()).matches();
	}

	@Override
	public String getUnusualReasonTree(String tenancyID, JSONObject params) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		if (params.optInt("id") == 0)
		{
			sql.append("select 0 as id,class_item_code as code,class_item as reason_name from sys_dictionary where class_identifier_code = 'unusual_type' ");

			@SuppressWarnings("unchecked")
			List<OrderingAbnormalReason> listDict = (List<OrderingAbnormalReason>) dao.query(tenancyID, sql.toString(), OrderingAbnormalReason.class);

			sql.setLength(0);

			sql.append("select a.*,(select case when count(b.id)>0 then 'closed' else '' end from hq_unusual_reason b where b.father_id=a.id ) as state  from hq_unusual_reason a  order by id asc");

			@SuppressWarnings("unchecked")
			List<OrderingAbnormalReason> listRes = (List<OrderingAbnormalReason>) dao.query(tenancyID, sql.toString(), OrderingAbnormalReason.class);

			Map<Object, OrderingAbnormalReason> map = new HashMap<Object, OrderingAbnormalReason>();

			for (OrderingAbnormalReason json : listRes)
			{
				json.setChildren(new ArrayList<OrderingAbnormalReason>());
				map.put(json.getId(), json);
			}

			for (OrderingAbnormalReason json : listRes)
			{
				if (map.containsKey(json.getFatherId()))
				{
					List<OrderingAbnormalReason> l1 = map.get(json.getFatherId()).getChildren();
					l1.add(json);
				}

			}

			for (OrderingAbnormalReason gO : listDict)
			{
				gO.setChildren(new ArrayList<OrderingAbnormalReason>());
				for (OrderingAbnormalReason j1 : listRes)
				{

					if (j1.getFatherId().equals(0) && j1.getUnusualType().equals(gO.getCode()))
					{

						List<OrderingAbnormalReason> l2 = gO.getChildren();
						l2.add(j1);
					}

				}

			}

			return com.tzx.framework.common.util.JsonUtils.list2json(listDict);
		}
		else
		{

			sql.setLength(0);
			;

			sql.append("a.*,(select case when count(b.id)>0 then 'closed' else '' end from hq_unusual_reason b where b.father_id=a.id ) as state  from hq_unusual_reason a where a.father_id=" + params.get("id") + " order by id asc");

			@SuppressWarnings("unchecked")
			List<OrderingAbnormalReason> listRes = (List<OrderingAbnormalReason>) dao.query(tenancyID, sql.toString(), OrderingAbnormalReason.class);

			return com.tzx.framework.common.util.JsonUtils.list2json(listRes);
		}

	}

	@Override
	public List<Combotree> getCombotreesByTableNameAndFatherColumn(String tenancyId, String tableName, String fatherColumn, String nameColumn, String typeColumn, String type, String codeColumn, JSONObject jb)
	{
		StringBuilder sb = new StringBuilder("select id," + fatherColumn + "," + nameColumn + "," + typeColumn + "," + codeColumn + " from " + tableName + " where " + typeColumn + " ='" + type + "' and valid_state='1' and id<>" + jb.optInt("uid"));
		JSONObject ses = this.getSysEncodeingScheme(tenancyId, tableName);
		int m = (ses == null) ? 5 : ses.getInt("max_level"); // 找不到默认5级

		try
		{
			if (tableName.equals("hq_unusual_reason"))
			{
				StringBuilder sql = new StringBuilder("select 0 as id,class_item as reason_name,class_item_code as code from sys_dictionary where class_identifier_code = 'unusual_type' ");

				@SuppressWarnings("unchecked")
				List<OrderingAbnormalReason> listDict = (List<OrderingAbnormalReason>) dao.query(tenancyId, sql.toString(), OrderingAbnormalReason.class);

				List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
				List<Combotree> list2 = new ArrayList<Combotree>();
				Map<Integer, Combotree> map = new HashMap<Integer, Combotree>();
				for (OrderingAbnormalReason ur : listDict)
				{
					if (ur.getCode().equals(type))
					{
						Combotree ct = new Combotree();
						ct.setId(ur.getId());
						ct.setText(ur.getReasonName());
						ct.setLevel(0);
						ct.setCode(ur.getCode());
						ct.setType(ur.getCode());
						ct.setFatherId(0);
						list2.add(ct);
					}
				}
				for (JSONObject jo : list)
				{
					Combotree ct = new Combotree();
					ct.setId(jo.getInt("id"));
					ct.setText(jo.getString(nameColumn));
					ct.setLevel(1);
					ct.setFatherId(jo.getInt(fatherColumn));
					ct.setType(jo.getString(typeColumn));
					ct.setCode(jo.getString(codeColumn));
					map.put(jo.getInt("id"), ct);
					for (Combotree cob : list2)
					{
						if (cob.getType().equals(jo.getString(typeColumn)) && jo.getInt(fatherColumn) == 0)
						{
							cob.getChildren().add(ct);
						}
					}
				}

				for (JSONObject jo : list)
				{
					if (jo.getInt(fatherColumn) != 0)
					{
						Combotree combotree = map.get(jo.getInt(fatherColumn));

						if (combotree != null)
						{
							if (combotree.getLevel() < (m - 1))
							{
								Combotree combotree2 = map.get(jo.get("id"));
								combotree2.setLevel(combotree.getLevel() + 1);
								combotree.getChildren().add(combotree2);
							}
						}

					}
				}

				return list2;

			}
			else if (tableName.equals("hq_item_class"))
			{

				StringBuilder sql = new StringBuilder("select 0 as id,class_item_code as chanel,class_item from sys_dictionary where class_identifier_code = 'chanel' ");

				@SuppressWarnings("unchecked")
				List<HqItemClass> listDict = (List<HqItemClass>) dao.query(tenancyId, sql.toString(), HqItemClass.class);
				sb.append(" order by itemclass_code");
				List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
				List<Combotree> listsForFatherId = new ArrayList<Combotree>();
				Map<Integer, Combotree> map = new HashMap<Integer, Combotree>();
				for (HqItemClass ur : listDict)
				{
					if (ur.getChanel().equals(type))
					{
						Combotree ct = new Combotree();
						ct.setId(ur.getId());
						ct.setText(" " + ur.getClassItem() + "类别");
						ct.setLevel(0);
						ct.setCode(ur.getItemclassCode());
						ct.setType(ur.getChanel());
						ct.setFatherId(0);
						listsForFatherId.add(ct);
					}
				}
				for (JSONObject jo : list)
				{
					Combotree ct = new Combotree();
					ct.setId(jo.getInt("id"));
					ct.setText(jo.getString(codeColumn) + jo.getString(nameColumn));
					ct.setLevel(1);
					ct.setFatherId(jo.getInt(fatherColumn));
					ct.setType(jo.getString(typeColumn));
					ct.setCode(jo.getString(codeColumn));
					map.put(jo.getInt("id"), ct);
					for (Combotree cob : listsForFatherId)
					{
						if (cob.getType().equals(jo.getString(typeColumn)) && jo.getInt(fatherColumn) == 0)
						{
							cob.getChildren().add(ct);
						}
					}
				}

				for (JSONObject jo : list)
				{
					if (jo.getInt(fatherColumn) != 0)
					{
						Combotree combotree = map.get(jo.getInt(fatherColumn));

						if (combotree != null)
						{
							if (combotree.getLevel() < (m - 1))
							{
								Combotree combotree2 = map.get(jo.get("id"));
								combotree2.setLevel(combotree.getLevel() + 1);
								combotree.getChildren().add(combotree2);
							}
						}

					}
				}

				return listsForFatherId;

			}
			sb.delete(0, sb.length());
			sb.append("select id," + nameColumn + " as text," + fatherColumn + " as fatherId from " + tableName);
			@SuppressWarnings("unchecked")
			List<Combotree> list = (List<Combotree>) this.dao.query(tenancyId, sb.toString(), Combotree.class);
			List<Combotree> list2 = new ArrayList<Combotree>();
			Map<Integer, Combotree> map = new HashMap<Integer, Combotree>();
			for (Combotree jo : list)
			{
				map.put(jo.getId(), jo);

			}

			for (Combotree jo1 : list)
			{

				if (jo1.getFatherId() != 0)
				{
					if (map.get(jo1.getFatherId()) == null)
					{
						jo1.setLevel(1);
						list2.add(jo1);
					}

					if (map.get(jo1.getFatherId()) != null)
					{
						if (map.get(jo1.getFatherId()).getLevel() < m - 1)
						{
							map.get(jo1.getId()).setFatherId(map.get(jo1.getFatherId()).getLevel() + 1);
							map.get(jo1.getFatherId()).getChildren().add(map.get(jo1.getId()));
						}

					}
				}
			}

			return list2;

		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			// TODO Auto-generated method stub
			return null;
		}

	}

	@Override
	public JSONObject getSysEncodeingScheme(String tenancyId, String tableName)
	{
		StringBuilder sb = new StringBuilder();
		sb.append("select * from sys_encoding_scheme where table_name ='" + tableName + "' ");
		try
		{
			List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
			if (list.size() > 0)
			{
				return list.get(0);
			}
			return null;
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}

	}

	@Override
	public String saveOrUpdate(String tenancyID, JSONObject data) throws Exception
	{
		String result = "";

		StringBuilder sb = new StringBuilder();
		if (data == null)
		{
			result = "{\"success\" : false , \"msg\" : \"数据为空!\"}";
			return result;
		}

		if (data.optInt("id") > 0)
		{
			result = "{\"success\" : true , \"msg\" : \"修改成功!\"}";
			sb.append("select id from hq_unusual_reason where unusual_type='" + data.optString("unusual_type") + "' and  reason_name='" + data.optString("reason_name") + "' and father_id=" + data.optInt("father_id") + " and id<>" + data.get("id"));

			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());

			if (list.size() > 0)
			{
				result = "{\"success\" : false , \"msg\" : \"名称重复，请重新录入!\"}";
				return result;
			}

			if (data.containsKey("ofid") && data.optInt("ofid") != data.optInt("father_id"))
			{
				JSONObject pram = JSONObject.fromObject("{}");
				pram.put("current_level", data.optInt("father_id") == 0 ? 1 : 2);
				pram.put("parent_code", data.optString("father_code"));
				String code = codeService.getCode(tenancyID, Code.CC_UNUSUAL_REASON_CODE, pram);
				data.put("code", code);
				// p.put("valid_state",Constant.VALID_STATE_TRUE);
				// System.out.println("rcode:"+code);
			}

			this.dao.updateIgnorCase(tenancyID, "hq_unusual_reason", data);

		}
		else
		{
			result = "{\"success\" : true , \"msg\" : \"添加成功!\"}";
			sb.append("select id from hq_unusual_reason where  unusual_type='" + data.optString("unusual_type") + "' and reason_name='" + data.optString("reason_name") + "' and father_id=" + data.optInt("father_id") + " ");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			if (list.size() > 0)
			{
				result = "{\"success\" : false , \"msg\" : \"名称重复，请重新录入!\"}";
				return result;
			}
			JSONObject pram = JSONObject.fromObject("{}");
			if ("null".equals(data.optString("father_id")) || "".equals(data.optString("father_id")) || data.optString("father_id") == "null")
			{
				pram.put("current_level", 1);
				data.put("father_id", 0);
			}
			else
			{
				pram.put("current_level", data.optInt("father_id") == 0 ? 1 : 2);
			}
			pram.put("parent_code", data.optString("father_code"));
			String code = codeService.getCode(tenancyID, Code.HQ_UNUSUAL_REASON_CODE, pram);
			data.put("code", code);

			this.dao.insertIgnorCase(tenancyID, "hq_unusual_reason", data);
		}
		return result;

	}

	@Override
	public String delete(String tenantId, String tableKey, List<JSONObject> keyList) throws Exception
	{
		String result = "";
		StringBuilder sb = new StringBuilder();
		if (!Tools.hv(tableKey) || !Tools.hv(keyList))
		{
			result = "{\"success\" : false , \"msg\" : \"已被引用不能删除!\"}";
			return result;
		}
		if (tableKey != null)
		{
			result = "{\"success\" : true , \"msg\" : \"删除成功!\"}";
			for (JSONObject obj : keyList)
			{
				Integer indetity = obj.optInt("id");
				sb.append("SELECT a.* FROM cc_order_reason_detail AS A where reason_type=" + indetity);
			}

			List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
			if (list.size() > 0)
			{
				result = "{\"success\" : false , \"msg\" : \"已被引用不能删除!\"}";
				return result;
			}
			this.dao.deleteBatchIgnorCase(tenantId, tableKey, keyList);
			return result;

		}
		return result;

	}

	@Override
	public String delete(String tenantId, String tableKey, JSONObject key) throws Exception
	{
		String result = "";
		StringBuilder sb = new StringBuilder();
		if (!Tools.hv(tableKey) || !Tools.hv(key))
		{
			result = "{\"success\" : false , \"msg\" : \"已被引用不能删除!\"}";

		}
		if (tableKey != null)
		{
			result = "{\"success\" : true , \"msg\" : \"删除成功!\"}";
			Integer identity = key.optInt("id");

			sb.append("SELECT a.* FROM cc_order_reason_detail AS A where reason_type=" + identity);
			List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
			if (list.size() > 0)
			{
				result = "{\"success\" : false , \"msg\" : \"已被引用不能删除!\"}";
				return result;
			}
			this.dao.deleteBatchIgnorCase(tenantId, tableKey, list);
			return result;
		}
		return result;

	}

}
