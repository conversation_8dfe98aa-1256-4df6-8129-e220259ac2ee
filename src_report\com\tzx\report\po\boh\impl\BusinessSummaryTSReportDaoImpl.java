package com.tzx.report.po.boh.impl;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.BusinessSummaryTSReportDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository(BusinessSummaryTSReportDao.NAME)
public class BusinessSummaryTSReportDaoImpl implements BusinessSummaryTSReportDao{

	private final String sqlCount = "select sql from saas_report_engine where report_num = 'SAAS_BI_2018_25' and sql_type='RQL0'";
	private final String sqlType = "select sql from saas_report_engine where report_num = 'SAAS_BI_2018_25' and sql_type='RQL1'";
	private final String sqlType2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2018_25' and sql_type='RQL2'";
	
	
	private final String sqlCount1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2018_25' and sql_type='JGL0'";
	private final String sqlType3 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2018_25' and sql_type='JGL1'";
	private final String sqlType4 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2018_25' and sql_type='JGL2'";

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	
	@Override
	public JSONObject getBusinessSummaryTS(String tenancyID, JSONObject condition) throws Exception {
		Integer type = condition.optInt("type");
		
		StringBuilder sb =new StringBuilder();
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		String begindate = condition.optString("begin_date");
		String enddate = condition.optString("end_date");
		String separate =condition.optString("separate");
		JSONObject result = new JSONObject();
		String reportSql = "";
		String reportSqlCount = "";
		long total = 0L;
		if(begindate.length()>0 && enddate.length()>0 )
		{
			switch (type)
			{
				//按日期汇总
				case 1:
					if(condition.optInt("hierarchytype") ==1){
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType);
						// 导出
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
							list = this.dao.query4Json(tenancyID, reportSql);
							// 获取到自定义的字段字段返回类型
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
						}else {
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						}
						
						StringBuilder sbdate = new StringBuilder();
						for (JSONObject jo : list)
						{
							String bd = jo.optString("report_date");
							if(bd.length()>0)
							{
								sbdate.append(",'"+bd+"'");
							}
						}
						List<JSONObject> listp = new ArrayList<JSONObject>();
						// 是否查询支付数据
						if(sbdate.length()>0 && condition.optString("customtype").equals("1"))
						{
							sbdate.delete(0,1);
							sb.setLength(0);
							sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,day_count from hq_payment_count_new where store_id in(" + condition.optString("store_id") + ") and day_count in  (" + sbdate + ")  GROUP BY payment_id,day_count");
							
							listp = this.dao.query4Json(tenancyID,sb.toString());
							
							for(JSONObject json:list)
							{
								String str  = json.optString("report_date");
								for(JSONObject jop:listp)
								{
									String b_d = jop.optString("day_count");
									if(b_d.equals(str))
									{
										json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
									}
								}
							}
							
							// 获取支付方式的返回类型
							if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
								List<JSONObject> paymentDetailsTreeOrderByClaas = getPaymentDetailsTreeOrderByClaas( tenancyID, condition);
								JSONObject jj =  null;
								for(JSONObject jop:paymentDetailsTreeOrderByClaas){
									jj =  new JSONObject();
									jj.put("fieldname",jop.get("pname"));
									jj.put("fieldtype", "double");
									structure.add(jj);
								}
							}
							
						}
						
						
					
						
						reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlCount);
						footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString());
						
						List<JSONObject> payfoot = this.dao.query4Json(tenancyID, "SELECT concat('t',payment_id) AS pid,SUM(pay_money) AS local_currency FROM hq_payment_count_new WHERE store_id IN(" + condition.optString("store_id") + ") AND day_count<='" + enddate + "' and day_count >='" + begindate + "' GROUP BY payment_id");
						for (JSONObject jso : payfoot)
						{
							footerList.get(0).put(jso.optString("pid"), jso.optDouble("local_currency", 0.00));
						}
						
					}else if(condition.containsKey("report_date")){
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType2);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
							list = this.dao.query4Json(tenancyID,reportSql+" ORDER BY report_date ".toString());
							// 获取到自定义的字段字段返回类型
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
						}else {
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						}
						StringBuilder sbstore_id = new StringBuilder();
						for (JSONObject jo2 : list)
						{
							String str = jo2.optString("store_id");
							if(str.length()>0)
							{
								sbstore_id.append(","+str);
							}
						}
						List<JSONObject> listp = new ArrayList<JSONObject>();
						if(sbstore_id.length()>0 && condition.optString("customtype").equals("1"))
						{
							sbstore_id.delete(0,1);
							sb.setLength(0);
							sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,store_id,day_count as report_date from hq_payment_count_new where store_id in(" + sbstore_id + ") and day_count in  (" + condition.optString("report_date") + ")    GROUP BY payment_id,store_id,day_count");
							
							listp = this.dao.query4Json(tenancyID,sb.toString());
							
							for(JSONObject json:list)
							{
								String storeIdStr  = json.optString("store_id");
								String reportDateStr  = json.optString("report_date");
								for(JSONObject jop:listp)
								{
									String storeIdStrPay = jop.optString("store_id");
									String reportDateStrPay = jop.optString("report_date");
									if(storeIdStrPay.equals(storeIdStr) && reportDateStrPay.equals(reportDateStr))
									{
										json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
									}
								}
							}
							
							// 获取支付方式的返回类型
							if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
								List<JSONObject> paymentDetailsTreeOrderByClaas = getPaymentDetailsTreeOrderByClaas( tenancyID, condition);
								JSONObject jj =  null;
								for(JSONObject jop:paymentDetailsTreeOrderByClaas){
									jj =  new JSONObject();
									jj.put("fieldname",jop.get("pname"));
									jj.put("fieldtype", "double");
									structure.add(jj);
								}
							}
						}
					}
					 
				break;
				//按机构汇总
				case 2:
					if(condition.optInt("hierarchytype") ==1){
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType3);
						// 导出
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
							list = this.dao.query4Json(tenancyID,reportSql.toString());
							// 获取到自定义的字段字段返回类型
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
						}else {
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						}
						
						/*for (JSONObject jo : list)
						{
							 
							sb.setLength(0);
							sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency from hq_payment_count_new where store_id in(" + jo.optString("store_id") + ") and (day_count between '" + begindate + "' and '" + enddate + "' )  GROUP BY payment_id");
							List<JSONObject> list2 = this.dao.query4Json(tenancyID, sb.toString());
							for (JSONObject jo2 : list2)
							{
								jo.put(jo2.optString("pid"), jo2.optDouble("local_currency", 0.00));
							}
						}*/
						
						StringBuilder sbstore_id = new StringBuilder();
						for (JSONObject jo2 : list)
						{
							String str = jo2.optString("store_id");
							if(str.length()>0)
							{
								sbstore_id.append(","+str);
							}
						}
						List<JSONObject> listp = new ArrayList<JSONObject>();
						if(sbstore_id.length()>0)
						{
							sbstore_id.delete(0,1);
							sb.setLength(0);
							sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,store_id from hq_payment_count_new where store_id in(" + sbstore_id + ") and (day_count between '" + begindate + "' and '" + enddate + "' )  GROUP BY payment_id,store_id");
							
							listp = this.dao.query4Json(tenancyID,sb.toString());
							
							for(JSONObject json:list)
							{
								String str  = json.optString("store_id");
								for(JSONObject jop:listp)
								{
									String b_d = jop.optString("store_id");
									if(b_d.equals(str))
									{
										json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
									}
								}
							}
							
							// 获取支付方式的返回类型
							if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
								List<JSONObject> paymentDetailsTreeOrderByClaas = getPaymentDetailsTreeOrderByClaas( tenancyID, condition);
								JSONObject jj =  null;
								for(JSONObject jop:paymentDetailsTreeOrderByClaas){
									jj =  new JSONObject();
									jj.put("fieldname",jop.get("pname"));
									jj.put("fieldtype", "double");
									structure.add(jj);
								}
							}
							
						}
						
						
						//footerList = ConditionUtils.cutIn(list,separate);
						
						reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlCount1);
						footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString());
						
						List<JSONObject> payfoot = this.dao.query4Json(tenancyID, "SELECT concat('t',payment_id) AS pid,SUM(pay_money) AS local_currency FROM hq_payment_count_new WHERE store_id IN(" + condition.optString("store_id") + ") AND day_count<='" + enddate + "' and day_count >='" + begindate + "' GROUP BY payment_id");
						for (JSONObject jso : payfoot)
						{
							footerList.get(0).put(jso.optString("pid"), jso.optDouble("local_currency", 0.00));
						}
					}else if(condition.containsKey("store_id")){
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType4);
						// 导出
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
							list = this.dao.query4Json(tenancyID,reportSql.toString());
							// 获取到自定义的字段字段返回类型
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
						}else {
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						}
						/*for (JSONObject jo : list)
						{
							sb.setLength(0);
							sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency from hq_payment_count_new where store_id in(" + condition.optString("store_id1") + ") and day_count =  '" + jo.optString("report_date") + "'  GROUP BY payment_id");
							List<JSONObject> list2 = this.dao.query4Json(tenancyID, sb.toString());
							for (JSONObject jo2 : list2)
							{
								jo.put(jo2.optString("pid"), jo2.optDouble("local_currency", 0.00));
							}
						}*/
						
						StringBuilder sbdate = new StringBuilder();
						for (JSONObject jo : list)
						{
							String bd = jo.optString("report_date");
							if(bd.length()>0)
							{
								sbdate.append(",'"+bd+"'");
							}
						}
						List<JSONObject> listp = new ArrayList<JSONObject>();
						if(sbdate.length()>0)
						{
							sbdate.delete(0,1);
							sb.setLength(0);
							sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,day_count as report_date,store_id from hq_payment_count_new where store_id in(" + condition.optString("store_id") + ") and day_count in( " + sbdate + ")  GROUP BY payment_id,day_count,store_id");
							
							listp = this.dao.query4Json(tenancyID,sb.toString());
							
							for(JSONObject json:list)
							{
								String storeIdStr  = json.optString("store_id");
								String reportDateStr  = json.optString("report_date");
								for(JSONObject jop:listp)
								{
									String storeIdStrPay = jop.optString("store_id");
									String reportDateStrPay = jop.optString("report_date");
									if(storeIdStrPay.equals(storeIdStr) && reportDateStrPay.equals(reportDateStr))
									{
										json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
									}
								}
							}
							
							// 获取支付方式的返回类型
							if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
								List<JSONObject> paymentDetailsTreeOrderByClaas = getPaymentDetailsTreeOrderByClaas( tenancyID, condition);
								JSONObject jj =  null;
								for(JSONObject jop:paymentDetailsTreeOrderByClaas){
									jj =  new JSONObject();
									jj.put("fieldname",jop.get("pname"));
									jj.put("fieldtype", "double");
									structure.add(jj);
								}
							}
						}
					}
					 
				break;
				default:
					break;
			}
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
 	}
	
	private  List<JSONObject> getPaymentDetailsTreeOrderByClaas(String tenancyID, JSONObject condition) throws Exception {
		StringBuilder sb = new StringBuilder();
		sb.setLength(0);
		sb.append("  with a as ( ");
		sb.append(" 	select 1 as id , 'cash' as payment_class ");
		sb.append(" union ALL select 2 as id , 'bankcard' as payment_class");
		sb.append(" union ALL select 3 as id , 'card' as payment_class ");
		sb.append(" union ALL select 4 as id , 'thirdparty' as payment_class");
		sb.append(" union ALL select 5 as id , 'card_credit' as payment_class");
		sb.append(" union ALL select 6 as id , 'incorporation' as payment_class");
		sb.append(" union ALL select 7 as id , 'coupons' as payment_class");
		sb.append(" union ALL select 8 as id , 'other' as payment_class");
		sb.append(" ) select  ");
		sb.append(" concat  ('t', payment_id) AS pname , payment_name as zname , payment_type as payment_class  from v_payment_name as t1");
		sb.append(" left JOIN a on a.payment_class=t1.payment_type_code order by   a.id,payment_class,payment_id ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
}
