package com.tzx.report.po.commonreplace;

import java.util.List;

import net.sf.json.JSONArray;

import net.sf.json.JSONObject;

public interface CommonMethodAreaDao {
	
	/**
	 * 付款方式
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getExplain(String tenancyID,JSONObject condition) throws Exception;
	 
	/**
	 * 报表说明
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getPaymentDetails(String tenancyID,JSONObject condition)throws Exception;

	/**
	 * 列付款方式按照付款类型排序
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getPaymentDetailsOrderByClass(String tenancyID,JSONObject condition) throws Exception;
	
	
	/**
	 * pos区分正餐快餐
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	JSONObject getPosDinnerSnack(String tenancyID,JSONObject condition) throws Exception;
	
	/**
	 * 列付款方式
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getPaymentDetailsStatus2(String tenancyID,JSONObject condition)throws Exception;
	
	/**
	 * 税率查询
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getTaxRate(String tenancyID,JSONObject condition)throws Exception;
	
	/**
	 * 指标配置新增/修改
	 * @param tenancyID
	 * @param tableName
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	Object saveTaxRateModification(String tenancyID,String tableName,List<JSONObject> condition) throws Exception;
	
	
	/**
	 * 菜品选择
	 * @param tenancyId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getCategorySelect(String tenancyId, JSONObject obj) throws Exception;
	
	/**
	 * 菜品分类
	 * @param tenancyId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	 String loadCategoryTree(String tenancyId, JSONObject obj) throws Exception;
	
	 /**
		 * 菜品类别查询 含大类小类
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public String customize(String tenancyId, Integer type, Object param) throws Exception;


		public String getChanelType(String tenancyId, Integer type, Object param);


		public String getOrgansTreeByConditios(String attribute, String attribute2,
				String attribute3, JSONObject p, String conditions) throws Exception;
		
		/**
		 *  优惠券条件下拉框
		 * @param tenancyId
		 * @param obj
		 * @return
		 * @throws Exception
		 */
		 public List<JSONObject> getCoupon(String tenancyID, JSONObject condition)throws Exception;
		
		 //获取自定义表头
		 public List<JSONObject> getReportTH(String tenancyID, JSONObject condition)throws Exception;
		 //新增或修改表头
		 Object addOrUpdTH(String tenancyID,String tableName,List<JSONObject> condition,String type) throws Exception;

		/**
		 *  味千——获取菜品大类
		 * @param tenancyId
		 * @param obj
		 * @return
		 * @throws Exception
		 */
		 public List<JSONObject> getItemDl(String tenancyID, JSONObject condition)throws Exception;
		 
		 /**
		  *  味千——获取菜品小类
		  * @param tenancyId
		  * @param obj
		  * @return
		  * @throws Exception
		  */
		 public List<JSONObject> getItemXl(String tenancyID, JSONObject condition)throws Exception;
		 /**
		  *  味千——获取菜品名称
		  * @param tenancyId
		  * @param obj
		  * @return
		  * @throws Exception
		  */
		 public List<JSONObject> getItemName(String tenancyID, JSONObject condition)throws Exception;
		 
		 
		 /**
		  * Hbase访问权限
		  * @param tenancyID
		  * @param condition
		  * @return
		  */
		 public Boolean getHBASEPrivilege(String tenancyID, JSONObject condition) throws Exception;
		 
		 public Boolean getHBASEPrivileges() throws Exception;
		 
		 /**
		  * 
		  * @param tenancyId
		  * @param type
		  * @param param
		  * @return
		 * @throws Exception 
		  */
		 public String getChanelTypeByBrand(String tenancyId, JSONObject param) throws Exception;
		 
		 
		 public String loadCategoryTreeByBrand(String tenancyId,  Object param) throws Exception;

		 //点击率同步至数据库
		 int[] updNewsClickRate(String tenancyID,String tableName,List<JSONObject> array) throws Exception;
			
	 /**
		 * 批量更新记录，返回数组为批量修改后修改的行数，0代表修改失败
		 * 
		 * @param tenantId
		 * @param tableName
		 * @param jsonList
		 * @return
		 * @throws Exception
		 */
		int[] updateBatchIgnorCase(String tenantId, String tableName, final List<JSONObject> jsonList) throws Exception;
		
		
		/**
		 * pos是否限制门店总部报表查询范围
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		JSONObject getPosLimitReportQueryArea(String tenancyID,JSONObject condition) throws Exception;
		
		/**
		 * 查询条件付款方式范围
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		JSONArray getPaymentRange(String tenancyID,JSONObject condition) throws Exception;
				
}
