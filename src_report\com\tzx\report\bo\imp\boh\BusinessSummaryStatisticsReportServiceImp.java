package com.tzx.report.bo.imp.boh;

import com.tzx.report.bo.boh.BusinessSummaryStatisticsReportService;
import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.common.util.ReportExportUtils;
import com.tzx.report.po.boh.dao.BusinessSummaryStatisticsReportDao;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service(BusinessSummaryStatisticsReportService.NAME)
public class BusinessSummaryStatisticsReportServiceImp implements BusinessSummaryStatisticsReportService
{
	private static final Logger LOGGER = Logger.getLogger(BusinessSummaryStatisticsReportServiceImp.class);

	 @Resource(name = BusinessSummaryStatisticsReportDao.NAME)
	 BusinessSummaryStatisticsReportDao businessSummaryStatisticsReportDao;
	 
	 @Resource
	private CommonMethodAreaService commonMethodAreaService;
	@Override
	public JSONObject getBusinessSummaryStatistics(String tenancyID,JSONObject condition) throws Exception {
		return businessSummaryStatisticsReportDao.getBusinessSummaryStatistics(tenancyID, condition);
	}

	@Override
	public HSSFWorkbook exportData(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception {
		// TODO Auto-generated method stub
		Integer rowNum=2;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",1);
		Integer stratIndex = 0 ;
		Integer stratIndex2 = 0 ;
		JSONObject findResult= getBusinessSummaryStatistics(attribute, p);

		LOGGER.info("查询第一层开始......");
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		LOGGER.info("查询第一层结束======");

		  JSONObject out1Result =null;
		  JSONObject out1Result2 =null;
		  String serviceOrBao ="服务费";
		  String [] listTitleName1 = null;
		  String [] dataName1 = null;
		  String [] dataType1 = null;
		  Integer titleIndexStr = null;
			//创建sheet 表格   同时还可以设置名字!  
			  HSSFSheet sheet1=workBook.createSheet("营业汇总统计报表");
			  String tenentIdVar =attribute;
			  if(attribute.length()>4) {
				 tenentIdVar = attribute.substring(0, 4);
			  }
			  if(tenentIdVar.equals("judh")||tenentIdVar.equals("jdht")||attribute.equals("emeijiujia")||attribute.equals("dianmenemeijiujia")||attribute.equals("deneiemeijiujia")) {
				  serviceOrBao="包间费";
			  }
			  List<JSONObject> payListObject =commonMethodAreaService.getPaymentDetailsOrderByClass((String) attribute, p);
			  if(p.optInt("typeStatus")==0){
				  listTitleName1 = new String[] {"交易日期","交易门店","营业应收","饿了么外卖应收 ","新美大（美团）外卖应收","开放外卖应收","消费流水","菜品消费",serviceOrBao,"退菜","折扣","折让","奉送","抹零","多收礼券"     ,"支付商家优惠","支付平台优惠","订单商家优惠","订单平台优惠","订单商家配送","订单平台配送","订单佣金",  "账单实收","营业实收(付款合计)","营业净收","消费客数","人均消费","消费账单","账单均值","桌台数","翻台率","座位数","上座率","锅底数（火锅类）","小料数（火锅类）",/*/*"营业外收支",*/"售卡收入+工本费","卡主账户","卡副账户","卡主账户","卡副账户",};
				  dataName1 = new String[] {"report_date","store_name","sale_total","el_bill_amount","mt_bill_amount","th_bill_amount","item_sale","item_amount","service_fee_income","back_money_item","discount_money","reduction_money","free_money","moling_money","coupons_ds",   "shop_discount_pay","platform_discount_pay","shop_discount_amount","platform_rate","shop_dispatching","platform_dispatching","commission_amount",    "payment_total","real_amount","net_amount","sale_person_num","sale_person_average","sale_billnum","sale_billaverage","tables_num","num_ft","seat_num","num_sz","hotpot_table_num","hotpot_guest_num",/*/*"yyw_income",*/"card_sales","qt_main_trading","qt_reward_trading","ht_main_trading","ht_reward_trading",};
				  dataType1 = new String[] {"String","String","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00%","0.00","0.00%","0.00","0.00","0.00","0.00","0.00","0.00","0.00",};
					if(p.opt("type").equals("2")) {
						listTitleName1 = new String []{"交易门店","交易日期","营业应收","饿了么外卖应收 ","新美大（美团）外卖应收","开放外卖应收","消费流水","菜品消费",serviceOrBao,"退菜","折扣","折让","奉送","抹零","多收礼券" ,"支付商家优惠","支付平台优惠","订单商家优惠","订单平台优惠","订单商家配送","订单平台配送","订单佣金","账单实收","营业实收(付款合计)","营业净收","消费客数","人均消费","消费账单","账单均值","桌台数","翻台率","座位数","上座率","锅底数（火锅类）","小料数（火锅类）",/*"营业外收支",*/"售卡收入+工本费","卡主账户","卡副账户","卡主账户","卡副账户",};
						 dataName1 =new String []{"store_name","report_date","sale_total","el_bill_amount","mt_bill_amount","th_bill_amount","item_sale","item_amount","service_fee_income","back_money_item","discount_money","reduction_money","free_money","moling_money","coupons_ds", "shop_discount_pay","platform_discount_pay","shop_discount_amount","platform_rate","shop_dispatching","platform_dispatching","commission_amount",  "payment_total","real_amount","net_amount","sale_person_num","sale_person_average","sale_billnum","sale_billaverage","tables_num","num_ft","seat_num","num_sz","hotpot_table_num","hotpot_guest_num",/*"yyw_income",*/"card_sales","qt_main_trading","qt_reward_trading","ht_main_trading","ht_reward_trading",};
						dataType1 =new String []{"String","String","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00%","0.00","0.00%","0.00","0.00","0.00","0.00","0.00","0.00","0.00",};
					}
					titleIndexStr =40;
			  }else{
				  listTitleName1 = new String[] {"交易日期","交易门店","含税金额","税额","未税金额","消费流水","菜品消费","含税金额","税额","未税金额","退菜","折扣","折让","奉送","抹零","多收礼券" ,"支付商家优惠","支付平台优惠","订单商家优惠","订单平台优惠","订单商家配送","订单平台配送","订单佣金","账单实收","含税金额","税额","未税金额","含税金额","税额","未税金额","营业净收","消费客数","人均消费","消费账单","账单均值","桌台数","翻台率","座位数","上座率","锅底数（火锅类）","小料数（火锅类）",/*"营业外收支",*/"售卡收入+工本费","卡主账户","卡副账户","卡主账户","卡副账户",};
				  		dataName1 = new String[] {"report_date","store_name","sale_total","bill_tax_money","bill_notax","item_sale","item_amount","service_fee_income","service_tax_money","service_notax","back_money_item","discount_money","reduction_money","free_money","moling_money","coupons_ds", "shop_discount_pay","platform_discount_pay","shop_discount_amount","platform_rate","shop_dispatching","platform_dispatching","commission_amount", "payment_total","real_amount","payment_tax_money","payment_notax","tax_amount","tax_money","no_tax_amount","net_amount","sale_person_num","sale_person_average","sale_billnum","sale_billaverage","tables_num","num_ft","seat_num","num_sz","hotpot_table_num","hotpot_guest_num",/*"yyw_income",*/"card_sales","qt_main_trading","qt_reward_trading","ht_main_trading","ht_reward_trading",};
				  		dataType1 = new String[] {"String","String","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00%","0.00","0.00%","0.00","0.00","0.00","0.00","0.00","0.00","0.00",};
					if(p.opt("type").equals("2")) {
						listTitleName1 = new String[] {"交易门店","交易日期","含税金额","税额","未税金额","消费流水","菜品消费","含税金额","税额","未税金额","退菜","折扣","折让","奉送","抹零","多收礼券" ,"支付商家优惠","支付平台优惠","订单商家优惠","订单平台优惠","订单商家配送","订单平台配送","订单佣金","账单实收","含税金额","税额","未税金额","含税金额","税额","未税金额","营业净收","消费客数","人均消费","消费账单","账单均值","桌台数","翻台率","座位数","上座率","锅底数（火锅类）","小料数（火锅类）",/*"营业外收支",*/"售卡收入+工本费","卡主账户","卡副账户","卡主账户","卡副账户",};
						dataName1 = new String[] {"store_name","report_date","sale_total","bill_tax_money","bill_notax","item_sale","item_amount","service_fee_income","service_tax_money","service_notax","back_money_item","discount_money","reduction_money","free_money","moling_money","coupons_ds", "shop_discount_pay","platform_discount_pay","shop_discount_amount","platform_rate","shop_dispatching","platform_dispatching","commission_amount", "payment_total","real_amount","payment_tax_money","payment_notax","tax_amount","tax_money","no_tax_amount","net_amount","sale_person_num","sale_person_average","sale_billnum","sale_billaverage","tables_num","num_ft","seat_num","num_sz","hotpot_table_num","hotpot_guest_num",/*"yyw_income",*/"card_sales","qt_main_trading","qt_reward_trading","ht_main_trading","ht_reward_trading",};
						dataType1 = new String[] {"String","String","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00%","0.00","0.00%","0.00","0.00","0.00","0.00","0.00","0.00","0.00",};
					}
					titleIndexStr =46;
			  }
			  Integer bb =payListObject.size()+listTitleName1.length;
				String [] listTitleName =new String[bb];
				String [] dataName =new String[bb];
				String [] dataType =new String[bb]; 
				 for(int i = 0 ;i<listTitleName1.length;i++) {
					 listTitleName[i]=listTitleName1[i];
					 dataName[i]=dataName1[i];
					 dataType[i]=dataType1[i];
				 } 
				  
				  for(JSONObject payJosn :payListObject) {
					  dataName[titleIndexStr]=payJosn.optString("pname");
					  dataType[titleIndexStr]="0.00";
					  listTitleName[titleIndexStr++]=payJosn.optString("title");
				  }

				LOGGER.info("循环写excel开始...........");
				if(list1.size()>0){
					// 批量查询明细，然后按门店或报表日期存放到map中，避免循环查询明细
					// 查询明细设置条件
					p.put("hierarchytype",2);
					if(p.opt("type").equals("1")) { // 按日期进行导出
						p.put("sort", "store_name");
						p.put("report_date", p.optString("exportdataexpr"));
					}else if (p.opt("type").equals("2")){ // 按门店进行导出
						p.put("sort", "report_date");
					}

					Map<String, List<JSONObject>> listMap = new HashMap<>();

					LOGGER.info("查询第二层开始......");
					p.put("rows", 9999); // 重新设置前端传入的rows条数，否则查询数据不全
					JSONObject findResult2 = getBusinessSummaryStatistics(attribute, p);
					LOGGER.info("查询第二层结束......");

					LOGGER.info("循环存放listMap开始......");
					List<JSONObject> list2 = (List<JSONObject>) findResult2.opt("rows");
					LOGGER.info("查询明细的总条数====="+ list2.size());
					if(list2.size()>0) {
						for(JSONObject json2 : list2) {
							if(p.opt("type").equals("1")) { // 按日期进行导出
								String report_date = json2.optString("report_date");
								if (listMap.containsKey(report_date)){
									List<JSONObject> detailList = listMap.get(report_date);
									detailList.add(json2);
								} else {
									List<JSONObject> detailList = new ArrayList<>();
									detailList.add(json2);
									listMap.put(report_date, detailList);
								}
								json2.put("report_date", "");
							}else if (p.opt("type").equals("2")) { // 按门店进行导出
								json2.put("store_name", "");
								String store_id = json2.optString("store_id");
								if (listMap.containsKey(store_id)){
									List<JSONObject> detailList = listMap.get(store_id);
									detailList.add(json2);
								} else {
									List<JSONObject> detailList = new ArrayList<>();
									detailList.add(json2);
									listMap.put(store_id, detailList);
								}
							}
						}
					}
					LOGGER.info("循环存放listMap结束......");

					for(JSONObject json1 : list1) {
						List<JSONObject> detailList = null;
						// 调用到处方法；
						if(p.opt("type").equals("1")) {
							json1.put("store_name", "");

							detailList = listMap.get(json1.optString("report_date"));
						}else if (p.opt("type").equals("2")) {
							json1.put("report_date", "");

							detailList = listMap.get(json1.optString("store_id"));
						}
						
						out1Result =ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
						stratIndex =out1Result.optInt("rowNum");
						paramData.put("rowNum", out1Result.opt("rowNum"));
						paramData.put("jin", out1Result.optInt("jin"));
						// 查询明细设置条件
//						p.put("hierarchytype",2);
//						if(p.opt("type").equals("1")) {
//							p.put("report_date", "'"+json1.opt("report_date")+"'");
//							p.put("sort", "store_name");
//						}else if (p.opt("type").equals("2")){
//							p.put("store_id", json1.opt("store_id"));
//							p.put("sort", "report_date");
//						}

//						LOGGER.info("查询第二层开始......");
//						JSONObject findResult2=getBusinessSummaryStatistics(attribute, p);
//						LOGGER.info("查询第二层结束......");
//
//						List<JSONObject> list2=(List<JSONObject>) findResult2.opt("rows");
						if(detailList != null && detailList.size()>0) {
							for(JSONObject json2 : detailList) {
//								if(p.opt("type").equals("1")) {
//									json2.put("report_date", "");
//								}else if (p.opt("type").equals("2")) {
//									json2.put("store_name", "");
//								}
								out1Result2 = ReportExportUtils.out1(json2,workBook,sheet1,null,dataName,dataType,paramData);
								stratIndex2=out1Result2.optInt("rowNum");
								paramData.put("rowNum", out1Result2.opt("rowNum"));
								paramData.put("jin", out1Result2.optInt("jin"));
							}
							sheet1.groupRow(stratIndex,out1Result2.optInt("rowNum"));
						}
					}
				}
				LOGGER.info("循环写excel结束...........");
				 
				 if(p.optInt("typeStatus")==0){
					 HSSFCellStyle style =ReportExportUtils.getTitleStyle(workBook);    
					 HSSFRow rowtitle =sheet1.createRow(0);
					// 普通合并列
						List<JSONObject> titleArr = new ArrayList() ;
						JSONObject sonJson =new JSONObject();
						sonJson.put("titleName", "优惠");
						sonJson.put("index", 7);
						sonJson.put("end", 10);
						titleArr.add(sonJson);
						
						JSONObject sonJson0 =new JSONObject();
						sonJson0.put("titleName", "第三方优惠承担信息");
						sonJson0.put("index", 12);
						sonJson0.put("end", 18);
						titleArr.add(sonJson0);
						
						JSONObject sonJson1 =new JSONObject();
						sonJson1.put("titleName", "营业指标");
						sonJson1.put("index", 22);
//						sonJson1.put("end", 29);
					 	sonJson1.put("end", 33);
						titleArr.add(sonJson1);
					    JSONObject sonJson11 =new JSONObject();
						 sonJson11.put("titleName", "售卡收入+工本费");
						 sonJson11.put("index", 35);
						 sonJson11.put("end", 35);
						 titleArr.add(sonJson11);

						JSONObject sonJson2 =new JSONObject();
						sonJson2.put("titleName", "会员卡前台消费");
						sonJson2.put("index", 36);
						sonJson2.put("end", 37);
						titleArr.add(sonJson2);
						JSONObject sonJson3 =new JSONObject();
						sonJson3.put("titleName", "会员卡后台调账");
						sonJson3.put("index", 38);
						sonJson3.put("end", 39);
						titleArr.add(sonJson3);
						
						sheet1 =ReportExportUtils.mergrColumn(workBook,sheet1,rowtitle,titleArr);
					
						// 动态 合并 列 根据：payment_class
						//sheet1 = ReportExportUtils.activeGoData(workBook,sheet1,payListObject,rowtitle,35,"payment_class");
					 	sheet1 = ReportExportUtils.activeGoData(workBook,sheet1,payListObject,rowtitle,40,"payment_class");

					 HSSFRow rowtitle2 =sheet1.getRow(1);
						//Integer [] valueNum ={0,1,2,3,4,5,6,11,19,20,21,30};
					    Integer [] valueNum ={0,1,2,3,4,5,6,11,19,20,21,35};
						//合并上下的行 限制一列
						sheet1 =ReportExportUtils.upOrDownMergr(workBook, sheet1, valueNum, rowtitle2, rowtitle, 0, 1);
				 }else{
					 HSSFCellStyle style =ReportExportUtils.getTitleStyle(workBook);    
					 HSSFRow rowtitle =sheet1.createRow(0);
					// 普通合并列
						List<JSONObject> titleArr = new ArrayList() ;
						JSONObject sonJson =new JSONObject();
						sonJson.put("titleName", "营业应收");
						sonJson.put("index", 2);
						sonJson.put("end", 4);
						titleArr.add(sonJson);
						JSONObject sonJson4 =new JSONObject();
						sonJson4.put("titleName", serviceOrBao);
						sonJson4.put("index", 7);
						sonJson4.put("end", 9);
						titleArr.add(sonJson4);
						JSONObject sonJson5 =new JSONObject();
						sonJson5.put("titleName", "优惠");
						sonJson5.put("index", 11);
						sonJson5.put("end", 14);
						titleArr.add(sonJson5);
						
						JSONObject sonJson0 =new JSONObject();
						sonJson0.put("titleName", "第三方优惠承担信息");
						sonJson0.put("index", 16);
						sonJson0.put("end", 22);
						titleArr.add(sonJson0);
						
						JSONObject sonJson6 =new JSONObject();
						sonJson6.put("titleName", "营业实收(付款合计)");
						sonJson6.put("index", 24);
						sonJson6.put("end", 26);
						titleArr.add(sonJson6);
						JSONObject sonJson7 =new JSONObject();
						sonJson7.put("titleName", "计税实收");
						sonJson7.put("index", 27);
						sonJson7.put("end", 29);
						titleArr.add(sonJson7);
						JSONObject sonJson1 =new JSONObject();
						sonJson1.put("titleName", "营业指标");
						sonJson1.put("index", 31);
						sonJson1.put("end", 40);
						titleArr.add(sonJson1);
						JSONObject sonJson2 =new JSONObject();
						sonJson2.put("titleName", "会员卡前台消费");
						sonJson2.put("index", 42);
						sonJson2.put("end", 43);
						titleArr.add(sonJson2);
						JSONObject sonJson3 =new JSONObject();
						sonJson3.put("titleName", "会员卡后台调账");
						sonJson3.put("index", 44);
						sonJson3.put("end", 45);
						titleArr.add(sonJson3);
						
						sheet1 =ReportExportUtils.mergrColumn(workBook,sheet1,rowtitle,titleArr);
					
						// 动态 合并 列 根据：payment_class
						sheet1 = ReportExportUtils.activeGoData(workBook,sheet1,payListObject,rowtitle,46,"payment_class");
						
						HSSFRow rowtitle2 =sheet1.getRow(1);
						Integer [] valueNum ={0,1,5,6,10,15,23,30,41};
						//合并上下的行 限制一列
						sheet1 =ReportExportUtils.upOrDownMergr(workBook, sheet1, valueNum, rowtitle2, rowtitle, 0, 1);
				 }
				 
					sheet1.groupRow(1,out1Result2.optInt("rowNum"));
					sheet1.setRowSumsBelow(false);
					sheet1.setRowSumsRight(false);
		return workBook;
	}
	
	 
}