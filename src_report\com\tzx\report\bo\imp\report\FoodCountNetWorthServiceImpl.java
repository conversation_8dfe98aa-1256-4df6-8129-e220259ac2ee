package com.tzx.report.bo.imp.report;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.report.FoodCountNetWorthService;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;

@Service(FoodCountNetWorthService.NAME)
public class FoodCountNetWorthServiceImpl implements FoodCountNetWorthService{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;

	@Override
	public  JSONObject getFoodCountNetWorth(String tenancyID, JSONObject condition) throws Exception { 
		
		JSONObject  result = new JSONObject();
		String startDate = condition.optString("begin_date");
		String endDate =condition.optString("end_date");
		List<JSONObject> list = new ArrayList<>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure =new ArrayList<JSONObject>();
		Long total = 0L;
		if(endDate != null && !endDate.equals(null) && startDate != null && !startDate.equals(null) ) {
			JSONObject json =new JSONObject();
			
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
				condition.put("rows", "100000");
				condition.put("page", "1");
				json =getQuerySQL( tenancyID , condition);
				list=this.dao.query4Json(tenancyID,json.optString("listSql").toString());
				structure = conditionUtils.getSqlStructure(tenancyID,json.optString("listSql"));
				footerList = this.dao.query4Json(tenancyID, json.optString("countSql").toString());
			}else{
			json =getQuerySQL( tenancyID , condition);
			list = this.dao.query4Json(tenancyID,json.optString("listSql").toString());
			footerList = this.dao.query4Json(tenancyID, json.optString("countSql").toString());
			
			condition.put("rows", "99999999");
			condition.put("page", "1");
			json =getQuerySQL( tenancyID , condition);
			total = this.dao.countSql(tenancyID,json.optString("listSql").toString());
			}
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}
	
	public JSONObject getQuerySQL(String tenancyID ,  JSONObject condition) {
		JSONObject json = new JSONObject();
		String listSql = "";
		String countSql = "";
		String reportSql = "";
		List<JSONObject> list = new ArrayList<JSONObject>();
		try {
			reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.BUSINESS_FOODCOUNTNET_FUNCTION_F1.toString() );
			list = this.dao.query4Json(tenancyID, reportSql);
			System.out.println(list);
			if(list.size() > 0) {
				 countSql= list.get(0).getString("sqltext");
				 listSql= list.get(1).getString("sqltext");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		json.put("listSql", listSql);
		json.put("countSql", countSql);
		return json;
	}

}
