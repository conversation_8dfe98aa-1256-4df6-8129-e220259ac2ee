package com.tzx.task.common.task;

import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.MessageUtils;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.task.common.util.ThreadPoolForPushPlat;
import com.tzx.task.po.redis.dao.TaskRedisDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDateTime;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

public class PushPlatFromModeTask implements Runnable{

    private static final Logger		logger	= Logger.getLogger(PushPlatFromModeTask.class);
    //private static final String PUSH_PLATFORM_MODE = "PUSH_PLATFORM_MODE";


    @Resource(name = TaskRedisDao.NAME)
    private TaskRedisDao	taskRedisDao;

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Override
    public void run() {
        try {
            Long len = taskRedisDao.len(com.tzx.cc.baidu.util.Constant.PUSH_PLATFORM_MODE.getBytes());
            logger.info("[推送下发队列数据，redis提取数据量]: --> " + len);
            if(len > 0){

                ThreadPoolExecutor threadPool = ThreadPoolForPushPlat.getThreadPool();
                Long loopFlag = 50L;

                if(len < 50){
                    loopFlag = len;
                }
                for(int i = 0; i < loopFlag; i++){
                    JSONObject cacheQueueObj = taskRedisDao.read(com.tzx.cc.baidu.util.Constant.PUSH_PLATFORM_MODE.getBytes());
                    logger.info("【推送任务数据为】  ====>  " + cacheQueueObj.toString());

                    if(cacheQueueObj == null && cacheQueueObj.isEmpty()){
                        continue;
                    }

                    if(cacheQueueObj.optInt("push_count") > 35){
                        changeStatus(cacheQueueObj, 2); // 任务失败
                        continue;
                    }

                    // 开始执行任务
                    autoQuery(cacheQueueObj, threadPool);
                }

            }

        } catch (Exception e) {
            logger.error(e);
        }

    }

    /**
     * 处理业务 -- [  ]
     * @param cacheQueueObj
     * @param threadPool
     */
    public void autoQuery(final JSONObject cacheQueueObj, ThreadPoolExecutor threadPool){

        try {
            DBContextHolder.setTenancyid(cacheQueueObj.optString("tenancy_id"));

            StringBuffer sql = new StringBuffer();

            sql.append("select * from cc_order_push_log cc where cc.order_code = '");
            sql.append(cacheQueueObj.optString("order_code"));
            sql.append("'");
            sql.append(" AND status <> '1'");
            List<JSONObject> cc_order_push_log_list = this.dao.query4Json(cacheQueueObj.optString("tenancy_id"), sql.toString());
            //清除2天前过期数据
            clearOutDateData(cacheQueueObj.optString("tenancy_id"));
            if(cc_order_push_log_list != null && cc_order_push_log_list.size() > 0){

                //2017-11-22 当有重复数据切且处理数据为取消订单动作 删除重复数据
                if(cc_order_push_log_list.size()>1){
                    String order_type = cacheQueueObj.optString("order_type");
                    if(StringUtils.isNotBlank(order_type) && "1".equals(order_type)){
                        String delsql = "delete from cc_order_push_log where order_code='"+cacheQueueObj.optString("order_code")+"'";
                        logger.info("推送失败日志中已经存在该订单["+ cacheQueueObj.optString("order_code")+"]数据,此次队列中操作为取消订单\n故删除数据" );
                        this.dao.execute(cacheQueueObj.optString("tenancy_id"),delsql);
                        return;
                    }
                }

                //end
                final JSONObject cc_order_push_log = cc_order_push_log_list.get(0);
                String dateStr = cc_order_push_log.optString("create_time");
                if(dateStr.length() > 0){
                    Date date = DateUtil.parseTimestamp(dateStr);
                    long second = (System.currentTimeMillis() - date.getTime()) / 1000;

                    // 如果时间没到,放回到缓存中
                    if(second < (cacheQueueObj.optInt("push_count")*3)) {
                        logger.info("【时间不到, 重新放回队列】 --> " + cacheQueueObj.toString());
                        taskRedisDao.lpush(com.tzx.cc.baidu.util.Constant.PUSH_PLATFORM_MODE.getBytes(), cacheQueueObj);
                        return ;
                    }
                }

                Runnable a = new Runnable(){
                    @Override
                    public void run() {
                        try{
                            MessageUtils mu = new MessageUtils();
                            DBContextHolder.setTenancyid(cc_order_push_log.optString("tenancy_id"));
                            int message_result = mu.sendMessage(cc_order_push_log.optString("msg_body"), cc_order_push_log.optString("org_uuid"), 1, cc_order_push_log.optString("tenancy_id"), cc_order_push_log.optString("store_id"), "1", 3);

                            // 失败进入task队列
                            if(message_result == 1){
                                changeStatus(cacheQueueObj, 1); // 任务成功
                                logger.info("【推送成功】 --> " + cacheQueueObj.toString());
                            } else {
                                addFailList(cc_order_push_log, cacheQueueObj);
                            }

                        }catch (Exception e) {
                            logger.info("【推送异常】 --> " + cc_order_push_log.optString("order_code"));
                            addFailList(cc_order_push_log, cacheQueueObj);
                            logger.error(e);
                        }
                    }
                };
                threadPool.execute(a);
            }else{
                logger.info("【推送任务结束】 --> 没有对应的数据信息!");
            }

        } catch (Exception e) {
            logger.error(e);
        }
    }

    private void addFailList(JSONObject cc_order_push_log, JSONObject cacheQueueObj) {
        JSONObject order = JSONObject.fromObject("{}");
        order.element("tenancy_id", cc_order_push_log.optString("tenancy_id"));
        order.element("order_code", cc_order_push_log.optString("order_code"));
        order.element("order_type", cacheQueueObj.optString("order_type"));
        order.element("push_count", cacheQueueObj.optInt("push_count") + 1);


        taskRedisDao.lpush(com.tzx.cc.baidu.util.Constant.PUSH_PLATFORM_MODE.getBytes(), order);
        logger.info("【推送失败,重新放回队列】 --> " + order.toString());
    }

    /**
     * 修改日志状态
     * @param cacheQueueObj
     * @return
     * @throws Exception
     */
    private boolean changeStatus(JSONObject cacheQueueObj, int status) throws Exception {

        DBContextHolder.setTenancyid(cacheQueueObj.optString("tenancy_id"));

        StringBuilder sb = new StringBuilder();
        sb.append("update cc_order_push_log set status = ");
        sb.append(status);
        sb.append(" ,last_update_time = '");
        sb.append(DateUtil.getNowDateYYDDMMHHMMSS());
        sb.append("'");
        sb.append(" where order_code = '");
        sb.append(cacheQueueObj.optString("order_code"));
        sb.append("';");

        return this.dao.execute(cacheQueueObj.optString("tenancy_id"), sb.toString());

    }

    /**
     * 清除两天前过期数据
     */
    private void clearOutDateData(String tenantId) throws  Exception{
        SimpleDateFormat simpleDateFormat =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dateTime = new LocalDateTime(now.getYear(),now.getMonthOfYear(),now.getDayOfMonth(),0,0,0);
        LocalDateTime after = dateTime.minusDays(2);
        Date date = after.toDate();
        String timeStamp =simpleDateFormat.format(date);
        String delsql = "delete from cc_order_push_log where create_time <'"+timeStamp+"'";
        logger.info("删除 ["+timeStamp+"]之前日志数据" );
        this.dao.execute(tenantId,delsql);
    }
}
