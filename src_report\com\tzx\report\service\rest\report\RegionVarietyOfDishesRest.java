
package com.tzx.report.service.rest.report;

import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.report.RegionVarietyOfDishesService;

/*
 * 区域菜品分类查询
 */
@Controller("RegionVarietyOfDishesRest")
@RequestMapping("/report/regionVarietyOfDishesRest")
public class RegionVarietyOfDishesRest {


	@Resource(name = RegionVarietyOfDishesService.NAME)
	private RegionVarietyOfDishesService	regionVarietyOfDishesService;

	// 查询微信增长用户
	@RequestMapping(value = "/getAddManReportData")
	public void getAddManReportData(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject  result = null ;
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			result = regionVarietyOfDishesService.find((String) session.getAttribute("tenentid"), obj);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result.put("msg", e.getMessage().toString());
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	 
}
