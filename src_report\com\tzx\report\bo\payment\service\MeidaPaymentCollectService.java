package com.tzx.report.bo.payment.service;

import com.alibaba.fastjson.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

/**
 *
 * <AUTHOR>
 * @date 2018/6/14
 */
public interface MeidaPaymentCollectService {

    public net.sf.json.JSONObject selectList(String tenancyId,net.sf.json.JSONObject paramJson) throws Exception;


    HSSFWorkbook exportAccountData(String attribute, net.sf.json.JSONObject p,
                                   HSSFWorkbook workBook) throws Exception;
    /**
     * 二級列表
     * @param paramJson
     * @return
     * @throws Exception
     */
    public net.sf.json.JSONObject selectSecondList(JSONObject paramJson) throws Exception;
}
