package com.tzx.cc.baidu.bo;

import net.sf.json.JSONObject;

/**
 * 菜品接口
 * <AUTHOR>
 *
 */
public interface DishService {
	public final String NAME="com.tzx.cc.baidu.bo.DishService";
  
	/**菜品分类列表取得
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject loadDishCategoryList(String tenancyID,JSONObject condition) throws Exception;
	
	/**菜品列表取得
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject loadDishList(String tenancyID,JSONObject condition) throws Exception;
	
	/**价格列表取得
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPrice(String tenancyID,JSONObject condition) throws Exception;
  
	/**新增菜品分类
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishDategoryCreate(String tenancyID,JSONObject condition) throws Exception;
	/**修改菜品分类
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishCategoryUpdate(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品上传	
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishCreate(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品修改
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishUpdate(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品上线
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishOnline(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品下线
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishOffline(String tenancyID,JSONObject condition) throws Exception;	
	/**菜品批量删除
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishDeleteBatch(String tenancyID,JSONObject condition) throws Exception;
	
	/**菜品删除
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject dishDelete(String tenancyID,JSONObject condition) throws Exception;

	/**菜品批量上线
	 * @param tenancyId
	 * @param dishList
	 * @return
	 */
	JSONObject dishOnlineBatch(String tenancyId, JSONObject dishList) ;
	
	/**
	 * 菜品批量推送
	 */
	
	JSONObject dishPushBatch(String tenancyId, JSONObject dishList) ;

	/**菜品批量下线
	 * @param tenantId
	 * @param obj
	 * @return
	 */
	JSONObject dishOfflineBatch(String tenantId, JSONObject obj);
	/**
	 * /加载机构百度外卖渠道下菜品类别
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public String findStoreChannelClass(String tenancyID,JSONObject condition) throws Exception;

	JSONObject  loadDishListNoPage(String tenancyID, JSONObject condition) throws Exception;

	
	public JSONObject  batchDishDategoryPush(String tenancyID,JSONObject condition) throws Exception;
	
	public JSONObject  batchIsCollectCommission(String tenancyID,JSONObject condition) throws Exception;

	JSONObject loadDishCategoryListNoPage(String tenancyID, JSONObject condition) throws Exception;
	
	public JSONObject batchPushProjectTeam(String tenancyID, JSONObject obj) throws Exception;
	
	public JSONObject delProjectTeam(String tenancyID, JSONObject obj) throws Exception;
	
} 