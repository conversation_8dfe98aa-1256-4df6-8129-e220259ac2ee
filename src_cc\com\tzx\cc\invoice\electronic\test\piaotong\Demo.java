package com.tzx.cc.invoice.electronic.test.piaotong;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import com.sun.org.apache.xml.internal.security.exceptions.Base64DecodingException;
import com.sun.org.apache.xml.internal.security.utils.Base64;
import com.tzx.framework.common.util.DateUtil;
import com.vpiaotong.openapi.OpenApi;
import com.vpiaotong.openapi.util.Base64Util;
import com.vpiaotong.openapi.util.HttpUtils;
import com.vpiaotong.openapi.util.JsonUtil;
import com.vpiaotong.openapi.util.RSAUtil;
import com.vpiaotong.openapi.util.SecurityUtil;

/**
 * <AUTHOR>
 * @date 2017/1/19 10:19
 */
public class Demo {

    private String privateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIVLAoolDaE7m5oMB1ZrILHkMXMF6qmC8I/FCejz4hwBcj59H3rbtcycBEmExOJTGwexFkNgRakhqM+3uP3VybWu1GBYNmqVzggWKKzThul9VPE3+OTMlxeG4H63RsCO1//J0MoUavXMMkL3txkZBO5EtTqek182eePOV8fC3ZxpAgMBAAECgYBp4Gg3BTGrZaa2mWFmspd41lK1E/kPBrRA7vltMfPj3P47RrYvp7/js/Xv0+d0AyFQXcjaYelTbCokPMJT1nJumb2A/Cqy3yGKX3Z6QibvByBlCKK29lZkw8WVRGFIzCIXhGKdqukXf8RyqfhInqHpZ9AoY2W60bbSP6EXj/rhNQJBAL76SmpQOrnCI8Xu75di0eXBN/bE9tKsf7AgMkpFRhaU8VLbvd27U9vRWqtu67RY3sOeRMh38JZBwAIS8tp5hgcCQQCyrOS6vfXIUxKoWyvGyMyhqoLsiAdnxBKHh8tMINo0ioCbU+jc2dgPDipL0ym5nhvg5fCXZC2rvkKUltLEqq4PAkAqBf9b932EpKCkjFgyUq9nRCYhaeP6JbUPN3Z5e1bZ3zpfBjV4ViE0zJOMB6NcEvYpy2jNR/8rwRoUGsFPq8//AkAklw18RJyJuqFugsUzPznQvad0IuNJV7jnsmJqo6ur6NUvef6NA7ugUalNv9+imINjChO8HRLRQfRGk6B0D/P3AkBt54UBMtFefOLXgUdilwLdCUSw4KpbuBPw+cyWlMjcXCkj4rHoeksekyBH1GrBJkLqDMRqtVQUubuFwSzBAtlc";

    private String ptPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJkx3HelhEm/U7jOCor29oHsIjCMSTyKbX5rpoAY8KDIs9mmr5Y9r+jvNJH8pK3u5gNnvleT6rQgJQW1mk0zHuPO00vy62tSA53fkSjtM+n0oC1Fkm4DRFd5qJgoP7uFQHR5OEffMjy2qIuxChY4Au0kq+6RruEgIttb7wUxy8TwIDAQAB";

    private String password = "lsBnINDxtct8HZB7KCMyhWSJ";

    // TODO 请更换请求流水号前缀
    private String prefix = "DEMO";

    /**
     * 
     * @title: testRSAGenerate
     * @description: RAS公钥私钥的生成
     * @throws Exception
     */
    @org.junit.Test
    public void testRSAGenerate() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        keyPairGen.initialize(1024);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();
        String publicKeyStr = RSAUtil.getKeyString(publicKey);
        System.out.println("publicKeyString:" + publicKeyStr);
        String privateKeyStr = RSAUtil.getKeyString(privateKey);
        System.out.println("privateKeyString:" + privateKeyStr);
    }


    /**
     * 
     * @title: testRegister
     * @description: 注册接口调用
     */
    @org.junit.Test
    public void testRegister() {

        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/register.pt";
        Map<String, String> map = new HashMap<String, String>();
        map.put("taxpayerNum", prefix + "000000000000");
        map.put("enterpriseName", "票通信息");
        map.put("legalPersonName", "AA");
        map.put("contactsName", "AA");
        map.put("contactsEmail", "<EMAIL>");
        map.put("contactsPhone", "15111111111");
        map.put("regionCode", "11");
        map.put("cityName", "海淀区");
        map.put("enterpriseAddress", "地址");
        // TODO 请修改为正确的图片Base64传
        map.put("taxRegistrationCertificate", "sdddddddddddddddddddd");
        map.put("isPermitPaperInvoice", "false");
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }

    /**
     * 
     * @title: testInvoiceBlueSimpleness
     * @description: 蓝票接口调用：单商品行蓝票
     */
    @org.junit.Test
    public void testInvoiceBlueOneItem() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoiceBlue.pt";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("taxpayerNum", "110101201702071");
        // TODO 请更换请求流水号前缀
        map.put("invoiceReqSerialNo", prefix + "0000000000000024");
        map.put("buyerName", "购买方名称1");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> listMap = new HashMap<String, String>();
        listMap.put("taxClassificationCode", "1010101020000000000");
        listMap.put("quantity", "1.00");
        listMap.put("unitPrice", "56.64");
        listMap.put("invoiceAmount", "56.64");
        listMap.put("taxRateValue", "0.13");
        list.add(listMap);
        map.put("itemList", list);
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }

    /**
     * 
     * @title: testInvoiceBlueSimpleness
     * @description: 蓝票接口调用：单商品行蓝票(含税)
     */
    @org.junit.Test
    public void testInvoiceBlueOneItemIncludeTax() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoiceBlue.pt";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("taxpayerNum", "110101201702071");
        // TODO 请更换请求流水号前缀
        map.put("invoiceReqSerialNo", prefix + "0000000000000027");
        map.put("buyerName", "购买方名称1");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> listMap = new HashMap<String, String>();
        listMap.put("taxClassificationCode", "1010101020000000000");
        listMap.put("quantity", "1.00");
        listMap.put("unitPrice", "56.64");
        listMap.put("invoiceAmount", "56.64");
        listMap.put("taxRateValue", "0.13");
        listMap.put("includeTaxFlag", "1");
        list.add(listMap);
        map.put("itemList", list);
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }

    /**
     * 
     * @title: testInvoiceBlueMultiItem
     * @description: 蓝票接口调用：多商品行蓝票
     */
    @org.junit.Test
    public void testInvoiceBlueMultiItem() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoiceBlue.pt";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("taxpayerNum", "110101201702071");
        // TODO 请更换请求流水号前缀
        map.put("invoiceReqSerialNo", prefix + "6578902234567952");
        map.put("buyerName", "购买方名称");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> listMapOne = new HashMap<String, String>();
        listMapOne.put("taxClassificationCode", "1010101020000000000");
        listMapOne.put("quantity", "1.00");
        listMapOne.put("unitPrice", "56.64");
        listMapOne.put("invoiceAmount", "56.64");
        listMapOne.put("taxRateValue", "0.13");
        Map<String, String> listMapTwo = new HashMap<String, String>();
        listMapTwo.put("taxClassificationCode", "5030000000000000000");
        listMapTwo.put("quantity", "1.00");
        listMapTwo.put("unitPrice", "15.21");
        listMapTwo.put("invoiceAmount", "15.21");
        listMapTwo.put("taxRateValue", "0.17");
        list.add(listMapOne);
        list.add(listMapTwo);
        map.put("itemList", list);
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }

    /**
     * 
     * @title: testInvoiceBlueMultiItem
     * @description: 蓝票接口调用：多商品行蓝票(含税)
     */
    @org.junit.Test
    public void testInvoiceBlueMultiItemIncludeTax() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoiceBlue.pt";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("taxpayerNum", "110101201702071");
        // TODO 请更换请求流水号前缀
        map.put("invoiceReqSerialNo", prefix + "6578902234567952");
        map.put("buyerName", "购买方名称");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> listMapOne = new HashMap<String, String>();
        listMapOne.put("taxClassificationCode", "1010101020000000000");
        listMapOne.put("quantity", "1.00");
        listMapOne.put("unitPrice", "56.64");
        listMapOne.put("invoiceAmount", "56.64");
        listMapOne.put("taxRateValue", "0.13");
        Map<String, String> listMapTwo = new HashMap<String, String>();
        listMapTwo.put("taxClassificationCode", "5030000000000000000");
        listMapTwo.put("quantity", "1.00");
        listMapTwo.put("unitPrice", "15.21");
        listMapTwo.put("invoiceAmount", "15.21");
        listMapTwo.put("taxRateValue", "0.17");
        listMapTwo.put("includeTaxFlag", "1");
        list.add(listMapOne);
        list.add(listMapTwo);
        map.put("itemList", list);
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }

    /**
     * 
     * @title: testInvoiceBlueMultiItem
     * @description: 蓝票接口调用：单商品行加带折扣行蓝票,在商品行里加入折扣率即可
     */
    @org.junit.Test
    public void testInvoiceBlueOneItemAndDiscount() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoiceBlue.pt";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("taxpayerNum", "110101201702071");
        // TODO 请更换请求流水号前缀
        map.put("invoiceReqSerialNo", prefix + "345678902234567955");
        map.put("buyerName", "购买方名称");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> listMapOne = new HashMap<String, String>();
        listMapOne.put("taxClassificationCode", "1010101020000000000");
        listMapOne.put("quantity", "1.00");
        listMapOne.put("unitPrice", "56.64");
        listMapOne.put("invoiceAmount", "56.64");
        listMapOne.put("taxRateValue", "0.13");
        // 折扣率
        listMapOne.put("discountRateValue", "0.1");
        list.add(listMapOne);
        map.put("itemList", list);
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }

    /**
     * 
     * @title: testInvoiceBlueMultiItem
     * @description: 蓝票接口调用：单商品行加带折扣行蓝票,在商品行里加入折扣率即可(含税)
     */
    @org.junit.Test
    public void testInvoiceBlueOneItemAndDiscountIncludeTax() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoiceBlue.pt";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("taxpayerNum", "110101201702071");
        // TODO 请更换请求流水号前缀
        map.put("invoiceReqSerialNo", prefix + "0000000000000032");
        map.put("buyerName", "购买方名称");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> listMapOne = new HashMap<String, String>();
        listMapOne.put("taxClassificationCode", "1010101020000000000");
        listMapOne.put("quantity", "1.00");
        listMapOne.put("unitPrice", "56.64");
        listMapOne.put("invoiceAmount", "56.64");
        listMapOne.put("taxRateValue", "0.13");
        // 折扣率
        listMapOne.put("discountRateValue", "0.1");
        listMapOne.put("discountRateValue", "0.1");
        listMapOne.put("includeTaxFlag", "1");
        list.add(listMapOne);
        map.put("itemList", list);
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }

    /**
     * 
     * @title: testInvoiceBlueMultiItem
     * @description: 蓝票接口调用：单商品行使用优惠政策
     */
    @org.junit.Test
    public void testInvoiceBlueOneItemAndPreferentialPolicy() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoiceBlue.pt";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("taxpayerNum", "110101201702071");
        // TODO 请更换请求流水号前缀
        map.put("invoiceReqSerialNo", prefix + "6678902234567963");
        map.put("buyerName", "购买方名称");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> listMapOne = new HashMap<String, String>();
        listMapOne.put("taxClassificationCode", "1010101020000000000");
        listMapOne.put("quantity", "1.00");
        listMapOne.put("unitPrice", "56.64");
        listMapOne.put("invoiceAmount", "56.64");
        listMapOne.put("taxRateValue", "0.13");
        // 优惠政策标示
        listMapOne.put("preferentialPolicyFlag", "1");
        listMapOne.put("vatSpecialManage", "22222222222222222");
        list.add(listMapOne);
        map.put("itemList", list);
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }

    /**
     * 
     * @title: testInvoiceBlueMultiItem
     * @description: 蓝票接口调用：零税率商品(使用零税率标示)
     */
    @org.junit.Test
    public void testInvoiceBlueZeroTax() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoiceBlue.pt";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("taxpayerNum", "110101201702071");
        // TODO 请更换请求流水号前缀
        map.put("invoiceReqSerialNo", prefix + "5678902234567941");
        map.put("buyerName", "购买方名称");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> listMapOne = new HashMap<String, String>();
        listMapOne.put("taxClassificationCode", "1010101020000000000");
        listMapOne.put("quantity", "1.00");
        listMapOne.put("unitPrice", "56.64");
        listMapOne.put("invoiceAmount", "56.64");
        listMapOne.put("taxRateValue", "0");
        // 零税率标示
        listMapOne.put("zeroTaxFlag", "1");
        list.add(listMapOne);
        map.put("itemList", list);
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }

    /**
     * 
     * @title: testInvoiceRed
     * @description: 红票接口调用
     */
    @org.junit.Test
    public void testInvoiceRed() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoiceRed.pt";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("taxpayerNum", "110101201702071");
        // TODO 请更换请求流水号前缀
        map.put("invoiceReqSerialNo", prefix + "5678902234568903");
        map.put("invoiceCode", "050003522444");
        map.put("invoiceNo", "11302054");
        map.put("redReason", "冲红");
        map.put("amount", "-56.64");
        String content = JsonUtil.toJson(map);
        System.out.println(content);
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        System.out.println(buildRequest);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
    }
    
    /**
     * 
     * @throws Base64DecodingException 
     * @throws IOException 
     * @title: testGetInvoiceQrAndExtractCode
     * @description: 获取开票二维码和提取码接口
     * <AUTHOR>
     */
    @org.junit.Test
    public void testGetInvoiceQrAndExtractCode() throws Base64DecodingException, IOException {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/getQrCodeByItems.pt";
        String content = "{\"taxpayerNum\":\"110101201702071\",\"enterpriseName\":\"电子票测试新1\",\"tradeNo\":\"2018030110431100003\",\"tradeTime\":\"2018-03-01 08:08:08\",\"invoiceAmount\":\"100.00\",\"allowInvoiceCount\":\"1\",\"itemList\":[{\"itemName\":\"餐饮\",\"taxRateValue\":\"0.03\",\"taxClassificationCode\":\"3070401000000000000\",\"unitPrice\":\"100.00\",\"quantity\":\"1\",\"invoiceItemAmount\":\"100.00\"}]}";
        String buildRequest = new OpenApi(password, "11111111", "tzx", privateKey).buildRequest(content);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
        System.out.println(new OpenApi(password, "11111111", "tzx", privateKey).disposeResponse(response, ptPublicKey));

        String returnStr=new OpenApi(password, "11111111", "tzx", privateKey).disposeResponse(response, ptPublicKey);
        JSONObject returnJson=JSONObject.fromObject(returnStr);
        String contentStr=returnJson.optString("content");
        JSONObject contentJson=JSONObject.fromObject(contentStr);
        String invoiceUrl=contentJson.optString("invoiceUrl");
        String invoiceQrCode=contentJson.optString("invoiceQrCode");
        byte[] decode = Base64.decode(invoiceUrl);;
		String info = new String(decode);
		System.out.println("invoiceUrl=="+info);
		byte[] decode2 = Base64.decode(invoiceQrCode);
		String imgFilePath = "D:\\票通二维码.jpg";//新生成的图片  
		OutputStream out = new FileOutputStream(imgFilePath);      
        out.write(decode2);  
        out.flush();  
        out.close();  
        
    }

    /**
     * 
     * @title: testGetPTBoxStatus
     * @description: 获取票通宝状态接口
     */
    @org.junit.Test
    public void testGetPTBoxStatus() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/getPTBoxStatus.pt";
        String content = "{\"taxpayerNum\":\"110101201702071\",\"enterpriseName\":\"电子票测试新1\"}";
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
        System.out
                .println(new OpenApi(password, "11111111", "DEMO", privateKey).disposeResponse(response, ptPublicKey));

    }

    /**
     * 
     * @title: testGetInvoiceRepertoryInfo
     * @description: 获取库存接口
     */
    @org.junit.Test
    public void testGetInvoiceRepertoryInfo() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/getInvoiceRepertoryInfo.pt";
        String content = "{\"taxpayerNum\":\"110101201702073\",\"enterpriseName\":\"电子票测试新1\"}";
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
        System.out
                .println(new OpenApi(password, "11111111", "DEMO", privateKey).disposeResponse(response, ptPublicKey));

    }

    /**
     * 
     * @title: testAuthWeChatCards
     * @description:微信卡包授
     */
    @org.junit.Test
    public void testAuthWeChatCards() {
        String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/authWeChatCards.pt";
        String content = "{\"taxpayerNum\":\"110101201702071\",\"invoiceReqSerialNo\":\"GAGA0000000000000009\"}";
        String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
        String response = HttpUtils.postJson(url, buildRequest);
        System.out.println(response);
        System.out
                .println(new OpenApi(password, "11111111", "DEMO", privateKey).disposeResponse(response, ptPublicKey));

    }
	
	   /**
    *
    * @title: testInvoicePaperDestroy
    * @description: 作废接口调用
    */
   @org.junit.Test
   public void testInvoicePaperDestroy() {
       String url = "http://fpkj.testnw.vpiaotong.cn/tp/openapi/invoicePaperDestroy.pt";
       Map<String, Object> map = new HashMap<String, Object>();
       map.put("taxpayerNum", "110101201702071");
       // TODO 请更换请求流水号前缀
       map.put("invoiceReqSerialNo", prefix + "0000000000000688");
       map.put("invoiceCode", "5000153650");
       map.put("invoiceNo", "06594215");
       map.put("destroyReason", "作废的原因作废的原因原因作废的原因作废的原因");
       map.put("amount", "-113.9");
       String content = JsonUtil.toJson(map);
       System.out.println(content);
       String buildRequest = new OpenApi(password, "11111111", "DEMO", privateKey).buildRequest(content);
       System.out.println(buildRequest);
       String response = HttpUtils.postJson(url, buildRequest);
       System.out.println(response);
       System.out
               .println(new OpenApi(password, "11111111", "DEMO", privateKey).disposeResponse(response, ptPublicKey));

   }

    /**
     * 
     * @title: test3DES
     * @description: 3DES加密
     */
    @org.junit.Test
    public void test3DES() {
        String content = "{\"taxpayerNum\": \"9120931023801231\",\"enterpriseName\": \"西单大悦城有限公司\",\"paymentTransID\": \"12109238102831023102983\",\"paymentType\": \"2\",\"paymentTransTime\": \"2017-01-19 18:20:09\",\"paymentTransMoney\": \"20\",\"orderID\": \"12109238102831023102981\",\"orderMoney\": \"30\"}";
        System.out.println(SecurityUtil.encrypt3DES("8f1$OeJ@eSR0z5Jh%!LmiBzi", content));
    }

    /**
     * 
     * @title: test3DESDecry
     * @description: 3DES解密
     */
   /* @org.junit.Test
    public void test3DESDecry() {
        String str = "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********************************+qgT3C7jLY8C/iSwvWbPH8o/4LR/9l5l4oVkRgiLo25eGKA5Uf9io5N/g8yGfW1K9n0EC88H+acafSKP78b5BZ2icnh9rhD9Q0OCzEHCVk0ibVc6MPYMbG9RDRKW0Y5hhAGFGWOX5XEf+b4dnk5WcPob4lXdpovNoXMUU6OnTsVmlbAK7ANbAO6DCrPfuiGuN74TgFFK/Ec85JcBDEGRHs4lnjYEGRjIRLnw0BeKJL9talCRAP8UpRS2ux6fis4XUMplvn2bk58cJIpIuHnhTEkDsYIevqYAgA73Lfopj3mQ2AQ3LYRGCukoOCXlPUKqOVYKbdjN4RtGLYBhj4kfipxxCQkg5Jl8bkeWPbtvjoJq/vfMSe/Jq25zVNBavq5e/IsFq9K4Tb6l4xxsPscBpCPyDb+7KfY3qPecOpqBjPyc6ebHT9Fn7RVa2AROoSMPcKmf4rZkBBQtG0ofgFlKWdiQYVKkOcvEETHUX/ECgF+fICX8msahgnqBlWFYi9owQlD6NHiEn8ViBqLWOcLYYNT/Fa5DITgqnmhyVuDb19zHcJdFmFSqCNKnRZDkUZXfAaxS5LAoPAl/PIXGYXLuE4RkIC1/w8cc+qETwHSxKt7BxBmK85Q+sjOfoYcorxiDe/83AGeOTNK+23+2RHXOW8OsGNbRbBAXujuDp0mHtcKV9mrYrfZgLPRsrkZAd54wSJS5DliJ74OU0863fsRb/bB8F3PX7WtuOUFnu1+j3VwoU9BEFdIGSID/obmgV9PH2YEntb/CMMS9v/Pr7joA9LhfGMTGf4/yn6rckS9tak1kblwB10AFo2EkKP6fKFBd6XPIcdnUPvsJQS6Vu/I0jvQKcW20C68ENql6dqPEviH+KT4KZVl2htq0+uoUph51BRXmS/NGpzZ9LPk+fgtwe+HKNOYMT7tALgNVFH0xt4su1sa8rm10pGiyeFixoi7SjW4vQk1vVNxp7GIcOGRX7SqG+rudjq42rkmcz9acseYPF2zvlV3XfrqZ6X/+lT79Q9+UODBQs6+rw2ScdZCCIuwrnblz6hPGJwoEbfY71kIsAWgoVB0HdTiWrmsdCr8xZwaJLgSSn9d6LmFAJo+tHxwtJReCFTs2c0YYSZSDK2+i5DrDCfogW2gxCq6lGySf6565NYrwydGhoiusbu//W6fgcijNZXupT8SWgT6P23QYl/52MX8omJe/6cyW7liCzh3jh5haPpyse78Slb7zzVXz8xk0zuIqIdiqV3JJDtbpwqNEW9q07V44yc6NQTMsPGhZAmTyIuXgKp562A5s6qaiar2oeGZV5SsqDiJyh+5kh3otRJbV/ycd/drTPiPiDegUME9ZLcSIQso3DXQuv7S99m1LuOe9K1gKoW69R8hPOU9rZ/HtOX7EIZEstxTclHRfRO9B6p1JJP4f7+gvQcr1xFFp1eZDI/7JFxqSGIA+pYGlSy36N9kSRWBrjnUivx1aVtMyDbJm1UKfUK7FeYDukM3wU2MsTlmmIlCbSyjZjJVe+KPDMLRghzL8Bw6JnABIM1s0srPfcFaN38daYVqdtEqZiYERVGyvsxHaQ5cbbn7p6E4EiFv9tX3fvaMGkEh1pU04JKKrziQw+NPqhu7EKvS8seEozebnkk0VSX3hoB3DAvE0hrox85n0nclVAM2lX2iwBg1mMOhB0YzHyBR4qn4Oy6U4MHYX07Zobw+NZBBuvJ+vg6GijZiP0FRV+A/KXvQgfql0Vl9CHhd8VrdZhcNl4nLL4GLS4e/MFGTvy9TIriNTSW1wfc+2CDcOYxK7QUqURiyFQmxdgODf1J8Dy+POVD6zOYEsbHktPiXReWj+2oH7VV9TMCSilCAbeJBXyEzioBGQewvWBx+ptwLva2hF22uN9VUviWx3iz78Yv2SKAPDxxvsREfj2DcAsdMn3up/WNN1xYNn/+pbhPWUvy3qJzc0X6SvMKI5+mv20ivgtY7g7Iw6/3nhH+dvD1drSK30uP1TxmKfAfey2EtUUi72VoMcmNrnMRBN1OJwzz4cDHvxQXTicoQK8aImTuvQ9cD/k8J+JCxfybNTcTyD1VgBI9yKchiNTOTM06Z3CKpxMxwmz3Qg8gYYj9m5QLQ37NGkJEbSUeu9t7YKltuW8TdgmD+rjUdc2JiU+sZiphjUhQN1uQf++qD8VUjJOFrBAIJ49/kKN2s54eCCZxIiewRNbBPm7T0bnVVtX8jv558emjVz3yEoKmdlMxovjAsfGeiInQVTfx7Kcdc9o0Cud/NnlYSYJ8LG0bMLgHUhusOyxREsobhnQ60Agtpy36t+Ao1q4DIipHczSeSGvMx2L4BKtc9at1hUADLzDiigFgG54BiJ4FJriJ2x4aIVK98hsBUSLtncU1n9atZr9esYBJguai01jtXX6VkwNg1/6D47xaR9TFO9yZT91Hs8nKw/btxyUxaEuzW5Wo8EmuAoemVFd69SH3p8SvZe64376AEF+JFh9g6mJa2LAsnccddoNPhMiAmhSKaeLXIrKWYy5phbZqIAm4aSnP6Y4PpkQ3tVgBLcOFrrUaRRgs0Fasz7oDSEFUeYLmL1ezBkLOIc14Jvigxpp+/A8goCJ9qOq2zI/Njn45xCde4pWKpaMlZbBiRH4oex4/aryOs720PAY6uzPEQARDAZJrFWPi2TCvCTzwQ4TrFmoPoXIsK6Cb8gAcNbKrPSf1LpB3sAzAdsAl94CSNPMSAzdBAJunZRIZFgyW2bCPtoXDw1SJaCD4wjvmlSKye7Yd5dZoUvAe40impE6pvgKIlXzNSxY2tR3sdpfnwGYdWpAiDeKvtOa/05sKDN5L/HKInl+PYuny1X6ho/iGafOfmq57EGonkoBlnuCGCDsyCWEf8/bVgMf3yNCXmW3XOthQxUWmNL6DCx1GnrWX4FYFPsqYPas0T46VdCZCMu0y5Uk6sATmgeWSMGit5rMkKxRRzZAO4X47RBFNijh/8Os4QgV8kNf8ePHsaSKc+txi00Jhid7uxPk9/95RNBB2knby/Id74p12C4NVb7+KjeHo7WmSClbL1nBD5/k89BLqpwaWYw0LbXddA1RjGd4b13Qp91NmICyo6k+f2KHdMzlf+PZ4g8Px6GPGNjFrLeQ8b+DyQLHZeQAQA7HpaFExYxbwY8+hOtf57ufj88ZKvS2grMRn4llRY0sMGkM2xXHDUYsfVv6HyxaTpVWPveJU+zmH4nd8EKXiv18Apw6PxWxnrjS1WUX5OysPJfLylHhil3TSk86ouHa2BmFe4a3nAKsZ3aVM+q/WyiA6gLhzg2YE7a8Hz3kID6fW2Chw3Vm4yNi2UMBMXMOovVbkNwdc0CoeyDbjuozwVsrlB5rWWVBYQCpQdZgubwlFcOk6YLIdQ+7fhTrA1R9kQkvxSk+nC0C1QVMQyi/bKLCCfxQevjzXIuAtK20SsKWEoRQj+20dwQzbCxj07wScWPbea2DWJZla+h6OxdP8WUExDrgQZQa2GmDPSz3cDZTqDVpd61memsGeydzPNxp7POqSMGTjmVlzEhRddJINE7c92A/HFiyXGXkoGw+2JuuvRS/DoE2PSAqEipW1KIhNGQf9YSzQMHX1tjO657G+qJtTMw8Fs/j5YqpM0q0iOcKeazuW6XuDTgU+J2p5WTG2mdVHy+Qn1BQLXIqW7cNlnIfpIs0CerTUAQKlVvCWj7K095EWlRWCbDobVhBUAGCCtCSAyZ+E2/qeqET04aveFMiWQEjwwWclMtxB9W8qfpsR5+G6DcCNRBJT1P55818tBHFy7c9atWHTozw7rXCPEei87YWMt4FVZmgXwzI2PmYfTFie3jz30kH98Rsdy1mW0IU4Tdrr9Q+27A41Sr/wP38aTl8NpwSJ5VCbO4d9VPj/XhkEH4zJbnxIkpTHDQZyomPPkbdRtG9UWcuglqcPj8jr1OhCZyw8shP1ko1tavIktXmQgmcsPLIT9ZKOkMKKcc3u6mpNo/9ds5PLnzVPHJG3Wp2m33ejDPKjjJ4Ldv9GAhNuTcCNRBJT1P55rKd3iNVZYGIPLS0ljE61Yzwh0Y8MGuKan4AZs2rNjdPkvlZhfEY3+9Xjw9eRdnp/f6Ny2V/SYH75GUv2lByqJruDXNj541+GPku3pYchBPy31lgV4KBzE+4QvDU+qVubqH2w6QnQ1/SGQPvNsBCF5JC9xyxKw3khYzUrZpzcZYakL6M5aPym5w1QVfdHygRwUIXmY+Cuh6peAWhrZQn50LbOXqMynOuWecmpxI4uVLlwjxHovO2FjFCF5mPgroeqXgFoa2UJ+dC2zl6jMpzrlsaTWN+Bf7sfb3XCELNGxel6yIbuokR/60jBJo8cFlM2sjqUrbHK5tMNBnKiY8+Rte2I87KSJQ91XQRx1JCZw+3BK1EyFajtrGHJSaJ0Vwr8R8HDcfgxJPmPZPkP/3/LhUAyc1uebs3eGGx027A7DPv2Qi/lN1fbYnTm4QBZ8B2msHvlTIP9/3hM1r3Vmp/moR1WYvSVFeyZwI1EElPU/nvo00O+SG+IRfO3CeXT1F3b9hJNu7DDK48jK3wDYn7OblsJi9aWIybBUEPx2CbmkbDzjI4rWJLGCO9Umw8KNi8fM3VtfKlPKGOZF/fugazbpasSQ6xMQkbGmKgf8hc8mRWDVzQdbNRjzoacrjhkfHxGuFke4mnhVoyyHUPu34U6wNUfZEJL8UpPpwtAtUFTEMguRxAPg1+K/cltDHjUPeUkJdeUJXW9nxaWStm5Yo3Tbkp1HxXEchXtxAbe2CEa60JM2+jhTcrquis7nmoPSYKckqRzZfI2V8WjRATNz+9cOZdXyulPusEiwF4xrscwHr/zkJm7PNloqA8wE6bBhMtQTbIDLb86GQcccOOdrtlv4zFPR/mk9Wf5mYpiKHC9Lw7NYvle725FXjipufnG2RuYJ8mu7fN3R8q0RKjrsd138qZTO977MwkwHYLcvr9u2vABDTc84MZai5jeuPmaSSZF1dLT0LYNzxB2v8pP7Xbp6cTwPT5Zb70arxVid7zPdo7mZfDBpJgnk45Jx32aZsyPqNIudnrsUfznvGmNhRi48j8iaI4bO8jrq2acxIwKRrtaW0WyfbSmZovSxa5aIWfvgEh58VG3e8pO22fAYsd2yLwDXW58X3ZTgOg6LGytvjBdfYiWkBA2u0aYTypDnLX+8ePXv9UIBY6ItJkhc5rH8K7ZuWgrK0q4z5+A2jSBkmb5PxpEN4k8lGNUllNz2llyEs3BY1COygZ85nvl8U9HgcCNRBJT1P55I1JAMo4419mSDE9Z6X2iLLuPflXl6WdWDhdddoSZtj7NRpWjpLbKhSZapHSSpBPRZuXZlZo+8GHAjUQSU9T+e3Tsi8/UaCLdCTxqlY5/MndnVop0K3eyS9K4sPH1Mm2Pm0WwMMdEenTBgU4xC52MBWWSFcGf3HgUFMDHlOph/vTH99fSWAVdL2GQFhJKnVlHL9uil2HLSUZe3A8h4Kny+Fv38RTlTzsqHvpdikSvmmQU30T07R7mtkLLagcnS3wP5im9oAQCXmEk2eKlzWVkC8uEXAcYCmEpLo+IHsRv4MELkAUFX5bW140+zAL7eWYs1Cs6TnHfOrlYJkweaJLjkhgSPLYNqTUPh9i33GX5848ZzFkEgdL76UR6JL1NyHEu9CWk/o3KR5a4dlcwR9JU0F8qSdvrarmFcclzT9yHnB7+FWzmbTkldJaPsrT3kRaVFYJsOhtWEFQAYIK0JIDJn4Tb+p6oRPThq94UyJZASPLPH8ZLxkVIeVNrmhI5GY9/68zrKfUYguloSRZhDgFYlY5hnNjSrK9qZfq10hZPmmDiyih1VMAWDLzfCqAijEh9zSmyf4UgmWihykeom4uFV1TwNVYuyeFmwSh0BeWCn1huaXFezf3TP4VuDYU+8e46vJI3xudB6fPp0FVO5hekd43FoVppKeWrH6hszyhuEmO1feEQwGumCSjYDQxSmxQckm2rvWoshaeaKmMh9dhxuX/fW0tsiN2rQRNd1Bllu8ZbxhhdEUMNrLvlzXTKc4n6kbqJmS9eJ/ZpgrWZbNm3vpeOp+tCf4udu9+BhK470Ci+X4PmTnU7oAEsHCC3x9TiYtNcRF8k3Qu9a4QgnBq9k3J7hmPEs8CPPmIRqvzmlcdRZbLj63o85/sEzUkHnwtVDVSXGGExvd9prY5HvBrYVIaDuyAqpBQSG9QYNT6yiTtzPapo8u+3hp5anwVgPN+DezREePSRrZW/dKZVUBkxwXO2SARGt502iQnCLfb9z6Tk8X05qddtGU6wpoS+vMJy8H3+PVqc0Jjney7dHgVoDKpCmsZjyPr1NbJhds9iXmbPC2XkI/oZLK7Rp9uYPf0zvmiksYBvNo2rlurtNBWtOSb/zk9j3A+BmMtXxap8+zMjMxA27lQSqJH87UTaGMpY0W/meZy4HvpcIL1L7gCyZGaG5VLZX2iOxt+Mx68zijPyi5aB0EoM76CPmLN1TNAM99cOHePqS/oOE/ibR1ZHDesZXwmBYk47OSFfSouW6qtdUtw/iqZZAgMZwHbWkrVY69LeOMyS/dKd4uS5XaeTS6co0VJI1jkEKeGBx2YxLEOOVvpA3461S2GipzcqAD8gn2sGLpAcgrXkc4s4yXIjHFK+nEMnu1mW6J38WR1DsfDN+QaVrwQzPBL8tvpARIVmPG0yEkEO1whI87yRL/rfJF/WEtU/jZ16QgfIGbhFOnjG9FECJLK7WXuK21n/FOnV1B/glo3O2iDWJZla+h6OxvkZS/aUHKon4A3T/EORXWZfArAWTs7wUrj86gCcoGkxwI1EElPU/nvo00O+SG+IR+SISF0Q9dh79hJNu7DDK4/zlm9Fiakm467zCJrq0pF4jPiN7wVCRS1QQ/HYJuaRsms0UgHDLrK/PCHRjwwa4piiwE6LGfzj/WqI3og079cxt8PmXpDh2ObRkqWho+5HubfD5l6Q4djlqxJDrExCRsaYqB/yFzyZFYNXNB1s1GPPUZ4/5VaTOYKo/sliev8k9vkZS/aUHKomCyh+CDSp1s4+S7elhyEE/LfWWBXgoHMT7hC8NT6pW5ujRs/uFcESIFec2VnywC7m3LGZmgrINCNIwSaPHBZTNQcbLXdJgfNvDQZyomPPkbdRtG9UWcuglDeGPMqn+I0SR/jEcf4TvDbAPUoG2Zm2U5MD/kM48rk5UrpwaX4to3K6TuTDiUerhu4nzvb3i/X5wZTYx+SSgo0FzfqAwQKB8g3Uzt4matqi3EVwCj7DRMvGo6xW3RrtKqTFdCODB8MTMmARz9K8VDOg//hLNzM+zqULXNjuuTdHgvpLzXw35f0Vgmw6G1YQVABggrQkgMmfAVZdhjL1zmvbpQB2feEZSLIdQ+7fhTrA1R9kQkvxSk+nC0C1QVMQyhBn026EFbIrkDvgcbP+h283Gns86pIwZOOZWXMSFF104Oxkjric9zkYljaCZWs018AQgzU+pvY1It60s/b/hcq4/OoAnKBpMc0HzTXP9z23ofmOx5f+/7ekrO0fxC8V3zcaezzqkjBk45lZcxIUXXZgqEdtkRgLU79En0n74GMEhqc34LzS25u6jQo3X13K60O5uV+uc+T83q8fX4I7lqayjNaS98eQVIq2+MY3lSxVHykIIbxwXNiWj7K095EWlRWCbDobVhBUAGCCtCSAyZ+E2/qeqET04gLhUT1csJRXrqQO3J6z7Cmvwzqwu4Paz6ZO88GwCIKRP32umngykBl09nMf87F+dTEav6gTcgGv8Se4EisaahoEcpVruezIWxnsbHgXTfZjLeuxGhcHRbOuGiEGdR5wCQjid+rAlC6JHfbmziFrwPKxzO9YTxLSPTsBNLAS340BDFkP1I9f0UMBCM32BfDCdQyl+uEcD4UXPL/0lZbf+abLlxB4GKuD5NRj3mKwjZeHQOOYf8UP1JElAdOKw9STR5NhDxR4E1b+ydwEuuM6sU+z50L7+1fO+5PIbMwfKn2lPTwfXmnJItulrAMICI8OuRHXiNIPumiDCCZhsro60+mgsE0L7h09Pm3Tf3m0HHibAv4CTlD5vdH4Zj+h7/TuXztdITxBogwo0YtM1RWC75mjOlBzqyuwX+0p2x9IPdf879jl7ZN5tOL8NuzNZr79c78Dg6BbDe3hHooGZp6iyKEiah0pKgt0W7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0exLHueLHBLR7Ese54scEtHsSx7nixwS0dclPAAUcUsbKK3sQWrZhNBCFHjSZTfEgXmmwxnfxrDs1G+371u+MpHY3N4mqWS8X9dWci9xrJi2QizgtqyJ0rTZBsMMk6zYB++pYZOwZTOhMOmakfpuTcfwXPVgUUGXheMW9aKK1ftMFVj/F4RhxmKhRIfUume4PZMPK7tpspqXBTCLrWZM9F4lvGwkpoZywnztuVDyB+ur8fk7wAzCQ3KBCFOdhN53wvQDHbhOCZXTv+pK4ATl0dE/efwtf8AtNTaU/LrRSJdzQGvgw2Avcb1Aa+DDYC9xvUBr4MNgL3G9QGvgw2Avcb1Aa+DDYC9xvUBr4MNgL3G9QGvgw2Avcb1Aa+DDYC9xvUBr4MNgL3G9jG2hJwTmIOr8BljqDlfGXv9TsY53SlM18FRbF+AjLBwoTK0D2/t+5+c6QUiIWpE/Qt+2iVnMNdD8+rmw23JEQkpugKhT91pIAub224Bh2gt1E6dQIZNdXcVCY7otcYnyw6nro3PozIXM3/Ng7Q8/k0F9k8M1qOiX8ABIW6VtQzGP8sA3ql04AvrFYLI0g85zJbwm6up159dUBfwWcqxwqgISFBmYzOc+La8Ti3ZEUJQxiRFwrFabt2Oji+bUwxvDPJQTDYqGlmxWbjI2LZQwE1ZcWjaT+nNf1zQKh7INuO4wHlpldenFSZ1VIUu3Vq+AWG/hOR2XXkbScm6GRY9e+N066CEcjrpKrJkWOnYojnof5wefVSAItlDNcl/TfcSRJnHlvklzclbq0MawO/NNiFZuMjYtlDATypxLkARSfwnXNAqHsg247k0Y+g68VICrxBZd4oRFVanUV6B578Z99IGZmAJd1jT8YhqX0s8h8anu4Jku59BB7/npkNl+hh296Ix5IRb5bW/9zmZU/6n+gjuyMUHbc8dQj/LAN6pdOAIcfVwzubZ+xohnDBpF8Okp9NMXwfD3TCceDO3/SLXpc3Lc4bEMFcTzG7jzZcLz1jGimLk3+o0PvruhIODQn2K96MvnR57+hZLoiw14rqWbtWL9m52Q4A7ckKdqWbpX5+Fa9ALceh/9VcQeTvqahiw5L1+Ijj82k6K5vftmTVc7DNm43I1BVNJD/sBGytagMbuMi+iTAeZfaP/2b81kDOEgtZvhzEH08IQypcn6wedeM5Mm+Ue2QAESOpDKQmfxrU9KboCoU/daSCcNj075Eb4bdROnUCGTXV2SP9WvrUpElGIal9LPIfGpPqYHb0eSu4niJ2CHKzSIIRL4Y6YZ+W6WoHmhDiDmedJGLJNv/KPJ0bh+Hf4ZKP4vE2p5lsdol2+oM8AuIK0oC3dWLYyRIMau6t0tDRwH/8FV1PNXkdiDV0Zcp6B3pyTsQG+kumwOPOU/nN5yAvKikyGx0ruIiLVlhXAy0D54HEW7BzWMlseKRHljBR2c+n8ynsW5quw6i0O2W+kAr2NXSrRRwFwF7bi9v5PgTeQ4I06ZUeqsHfHigpPcqWhGRcnkMko1iig7uwX553tI0SqH6IPYWD02Uwy9TxSMeVskHA04Gg2HnAca5skKxRRzZAO4X47RBFNijh/8Os4QgV8kNe9fR2Pd7QzJtxi00Jhid7sdpJ28vyHe+F5ABADseloU08rFtdqn0/9KJNmKqETvBExjhzRDQqWZQ+9WgdxCUoB8Ne3n2y7sEOeUbAVMDb5OJSLaRSwr6mDeI94OD2Y7lMtCfKrWcndDo3h6O1pkgpUg0ixuJR0KaRoEI798PWIOnlAOA+InCSTH1yUnhsFihG9iF4v+zqVaU9yFFHBSPmQytYpHB5O0Qb6GY77hztRplZjbD+oKhyLSahycgeDc/R3QtNCXlom6qWAkMIagxtl7FxTvw7oHRFTm74AdBkwsuyCMWnDnltNtodvK5f+Etj/uzSVB/hprU1LcPV8VQcATB0nS4j3H7XX4GERhih6zWBKkahdYYYVx4Du50y8fSzGk/OMixPFOJEdSr8XtKzdawqRWWOD++pbVCRD1xnTmvWnQq56c03v1rglSnjhTYDAI3KVgUIwePzhroYxCDKIu9NIwugKaelWRX0s4MchhLPecNbJrdnR1jFg6QQOkUHFHE8zPptDvf7reR4+g4BFUMsztOQbmVQwRe9ZJx4IR7+VvojtgMTv3pgRKzOzyROHqbnlEsfzKVgFMVRu+GSEOWBWBnIMzRK83Domir+EunakNnHDZY+09MvV5sENM2njoA18iuix4NSGpGxTLbEEblHl/0QHd3b4GP+fTslZ/vtnKJckzRC6XSQQi2wBFzQfL08RElgByOpDKQmfxrU9KeQbJ3h2vvZzYvfD8Jp3EFUi+4h2hpCPJCsUUc2QDuF+O0QRTYo4f/DrOEIFfJDVVkV9LODHIYfGVsiR9vLb579Py3anGnNuOY64hXgD4HeWam1f+FHICWF0roM8/cuM627LX8+wKlLck3pUpVtX1q64wf7xgf8MFUO6T4cjMZBCK7Ccg/Z97UAa3PR2sWKnPr7LfyBsbRGlw7fr3OvfJLcHYUi/AzlnPnnYweNiJyD1cC8ISid0CVKp3RbvhobCyGwPONe23lUnz5a3tvXWtzXhTPl+UlngfRq+S0PIbNrSKrx7BNFVjMHPCKBNPp7GEq7kfPUhlIzcGACSBtndIBGAQ+Q4kGFPM+zT1zHHStphaoJaS6nX6vzcnrs5HCXHSvs+yLsNttmCFPyWB/H54Q5F8UnlJcrJxNIJEKbgivkZYRP0JjbiWbJEwdryU647EDXN9iYOdQBJ+aZeG3GAAJbGUlr1tMBCYnj/9jtvmivgsnFK6HFxa4Uo2QYw5ZZJIyyojXTHNZItApVKRf4aRibIfB1ppuoE5lVKzxOFve+IYQxdxUHFH2JiGhsyLwT0HhAl7JHLZ+nYQo1enFyhLQ8HE5FSoG16T3KloRkXJ5JieP/2O2+aKwYSc1f0E0eLhSjZBjDllkkjLKiNdMc1kfQTPoicC9GuJsh8HWmm6gTmVUrPE4W974hhDF3FQcUfYmIaGzIvBPX1aOlPKP6L0E8Vn7T0D4OF87OvkV5wO0Pj0smQ4nzyoOpDKQmfxrU9KboCoU/daSI8L9sq6JoxEtxi00Jhid7uIcYP2vZLhI5ZDCpeJx3fhW62Eiuk4h3OOmqCESuGZvyNhaoqKuUpOpD/PlIa4fS30sEGM1UjJAB788XAVsnc3HdRrcYEy/CDNJayBpPRRQivSW9492jVZsgWn/tgnyccRXLmKsvKFOzRxdAwjIIcFbTyp0W0m51xfsP3pXGvIDdRXoHnvxn30gZmYAl3WNPxhFQZZcBZheXyc50v8heRkVk3Hm2/FKOBahpNLEig+sM8aXpieU6JfRnalFMw96rcMrJ4V5nSX6ZHvH8Y2H1MJiigTa13DaWlYllkLoY7XgoU0ePOje+rEYhqX0s8h8amRZL4PhL26zD+LpC0ApnnFUS9q9iGgCqfIPdJJIDbNNaBBCOchse4Ms3vxMHAmBA+N3yovILpW8UYyJbOvulwbk0t4b9DpRGFW2iBrvz7TZLzKxaO+x+fbHhil3TSk86qH4tw2xy+uAiLLcGfVONk/O4ERHcV2MRVMPxhwbPOeqVh59RB2akxneiWG/5aiVEMGORVs+J+EsSLn7rL7kjwx+LeyYt7+8jCKxGgxbzIJq5clujjkPJ86l3Y78/C3pF3yHZRjrI9/k1EvavYhoAqn6fvG6CAiCcr0sEGM1UjJAMQtMvjMA5COjQGhzsHqCrRpGK4Hydkd2MN4//08R/p2I8gtJeyU/mAy0t9tMgYeA+jEKWKgS6bMMCePD4Nldewi5+6y+5I8MVvIOH9DyLZwisRoMW8yCavAaFmUrANBJIrEaDFvMgmrxAPkq/FkXoWXdjvz8LekXTQ9Xp+lENhtl3Y78/C3pF2pO6Vbdo1pHFEvavYhoAqn5r+2NTZ47VzREnMUSaJW3M3bSbc6n1kgfEVbc9M8W2G7iXn9VwH8aVxndwQi25jQCxCxC1FLT7WYIOETIm/w2Mql/I50tNM5lGHN29GUQva4iRaXJBIp47MC0IN7+C2zmKrvOb9fASCn9ZHNtiL66ijDlJsjmOaz5StaywtKxEiSe8bVrDtiNB8uLwMW3lFkRL4TZShqlVYdESBIEoVLs218LIqhTk4OrFJR9jOLT8R5TUffj1/S0QCB2MPTVGacHj+SviXPvPhEvhNlKGqVVleO4bZFiHo/d9zlqyfNflFj4BnxozLDbdm6GGqEXPhZefHBU1OzTHDtOc06t8t6ET3+NAJg2520ftyb+gKst751E6dQIZNdXZI/1a+tSkSUJ6e0zlGUnrQVkqq8LHrcPP4yro/FoZgZnQHxkqdhK5FdBTjVPl9DMr3UsvCMnxkmlEbG49CRfSDEFl3ihEVVqUPHl3J0TmZm9dsO5viXHEKN3yovILpW8UYyJbOvulwbLdbI1dvFpEtu5fX+kA6U+4fMciXtbN6aZeotEnmdZ1/LQnyq1nJ3Q4D0QYI2kO7jiUydd4WBepNt9wKvja1OivCecKfQ0E2vhe83plRSYU4iTdiCVwGJFDCzlZQ9BNRWu2XeZmemNedKboCoU/daSEFqDR/rufuRs9a/EV6PoyHsSx7nixwS0U7MvXFV53czS/G2KghrFdf8f9Qx6xGZxJhEqdvq/Rd2ieZGpbCp700PGKSkhxBe6IvEs1pNjOAcHYZ88iFX+6MzjXjgmM7MNQ8YpKSHEF7oYDIhYekWtnVDwKvmsSlpJyPCaQoHXUopI4WGwBBp8rdo1x4NupT46/bYThQH1O2cFv00yGOTpOvRDpPgPgwMkfhppqGfeJug3ZnfbvKqJ+T2PTzbWf3cNw8YpKSHEF7oe2ChpTs7aFbbHkJszzYDxL3HTxs6BiFXPP4bU4Sz4Ex3CVkiCNgE9NEOk+A+DAyR+GmmoZ94m6Ay6bqMLO4YiAmV+8woX7ziDxikpIcQXuh7YKGlOztoViFXzf1BqT49vcdPGzoGIVc8/htThLPgTGJAH+C49YY+0Q6T4D4MDJH4aaahn3iboKxZ3zNqpNbnRm4KXowLXKkPGKSkhxBe6HtgoaU7O2hWWJ5GeXZEu6m9x08bOgYhVzz+G1OEs+BMtiU700/w69zRDpPgPgwMkfhppqGfeJugUlXkf744qIDZ2G0YA+0yDQ8YpKSHEF7oe2ChpTs7aFbnR7fPVW+raL3HTxs6BiFXWLBvnofcg4BP0GJPAS8N3iRIZe+BVOQj5y5aCyVkYiN4i1X8dE/6kgHo1Lqk59DTmIlYPGNeAZQhnV4gskTYKIDEKQx5FH86GNW2swJkA9fXVEupyOUWqNIxXUrOKpwJPuN6MdOpQMhb1lk02+XMVbNeGZhwBpu2wTUyXjxERbUr5SdlJEPJKe9+CkK7CsxyqJTT1ryn6z1W2iBrvz7TZMZRelq0lETyGzDn1B+skthFMpjbzOgaMVmKXkk5JfcioXB+FWEK/7D146RHU8YURDG0+c/CGnX9JpnnP1cARNhNvjXqOusL7D9Vwv9h3A0jVHhn6coYdN+L0m5DFrIL3uXwWPyNGyvd4/D/l/vpvu6cinBopHKsRkFpTD3kCCfwuo9BCSrgtc27zUdsugQpDwWEfytXRJEqpthVSMsz9Nm5KC41IprZI5X8WYhM+OYvWHXDGTaWRBcG7xag2O/7sHxrPDKWeaWUZdX2q2co9txVlf6mscDp9A==";
        System.out.println(SecurityUtil.decrypt3DES("kj2R8833G404PZ1Y88795Q0h", str));
    }*/

    /**
     * 
     * @title: testBase64
     * @description: Base64编码
     */
    @org.junit.Test
    public void testBase64() {
        String str = "JJON0d93C9nQN013N+cCwwIYbRVYlWChGQkSgAWG8g4mD1xFU6oGPauqih5gW7ZTcpejSPS8TqRbdBFdBATSXdwZqPM0q8sVYf3xwlp8OEw6INcUCvRW7myiFkzSJLV4Ost42d5Xp+sicgMj0bn99BsRSqe06BMvYTA46L/vGGPqN4tuuy2B/enpkGLcOQdPdtC+wG8ub6+zykisJT5I7EMls73cjaSlj1iRw/PT9huULu97iPHIiqnKhK05AXkvgWMcfg42+bLeG/kPgbaAtwAkXN/yDkKACcDML2WE8TZ+BFsaQPbH+BfY/XQ4VXSYF5NGeulhDJr1DLIHgH+KNQ==";
        System.out.println(Base64Util.encode2String(str));
    }

    /**
     * 
     * @title: testSign
     * @description: RSA签名
     */
    @org.junit.Test
    public void testSign() {
        String content = "{\"taxpayerNum\":\"GAGA000000000001\",\"enterpriseName\":\"测试注册A\",\"platformCode\":\"13242753\",\"registrationCode\":\"87774618\",\"authorizationCode\":\"4209110715\"}";
        String buildRequest = new OpenApi("D5ImkGeVdsCaZyna4G73jv6j", "vZpDTUSV", "GAGA",
                "")
                        .buildRequest(content);
        System.out.println(buildRequest);
    }

    /**
     * 
     * @title: testVerify
     * @description:RSA验签
     */
    @org.junit.Test
    public void testVerify() {
        String requestJson = "{\"format\":\"JSON\",\"sign\":\"i8VGz3Qs4tnw89aeJON21nn2h8oQAynM4BALX8iKb+BlfXvIc2PS1hHen0cnJm53bvgRfyVCEgXQkpFK1Rdiw6ht3KD1IxtYMSkISUuwRphuhwPQiVVsODZiGSRQpHCwcv+I7szWhOErY0sYBqPHfHSsvZ4bD/rOp7K+0Lp/sw0\\u003d\",\"signType\":\"RSA\",\"version\":\"1.0\",\"platformCode\":\"TohDKeNU\",\"content\":\"EYY+FTkOvZs70g6eplhvD8P3oOZBVwtO1mN3dwDgNo0wCi9URbwtGJBqiaKUpjvn43HuE04aelW7J+ZgewZG5AmgdbY0aFhDejb5czd6UPt8DWLK9NyrgKMtKBc4LULfPKFhQwchtfgWWJtEHMrIyyHQPe5N5vgmAGGBE4nYUyazp6p42JnMLJCUHHNKsI2zApQWp20I1BCMhT/XZc+jXZ3q50UmHJP9l4vKCfESLxwahdADgB2HLqRWLr1OgxYenUUNkokwtV5TxQBYr/iKdQ03HFEVjNV+tuTTrcdakA/tF0UOc71roxxmHFgJU8bl64U+KRMEM1cQoEDwRzjqcbfGRrj/JFEqQel2k7mDsNZi24EvIExNDFautR9XLsuryZ8vrDwK33Ln/oYz+K5wooyf13kv8KDQgVhWCkW3XmmWwGMCttzOP9pVbiD3WP6MJ5qmhtK7R4JBibMoLAjFUcA2IZgk9tnphTUlQZcFEIPCT0cv0fyvuw\\u003d\\u003d\",\"timestamp\":\"2017-05-12 13:55:06\",\"serialNo\":\"CHSE20170512135506OW6jE08r\"}";

        Map<String, String> paramMap = JsonUtil.json2Map(requestJson);
        String sign = paramMap.remove("sign");
        String a = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCdL0fHPUtT9bTc8tTx5cSNnLO09P/4pTnYyPpp9Jt+NU+nTYiVjDrX97a7M0NuuoxpX4KSFz3WKTgGir+uYcQUm50+oEaRI1gQZrjnIfXEyafdS1Gcr34OAvObxvShQst/p3swVatqK4b7/SfYrQN6S4tQ4L+06QBKgvvaxpGPTwIDAQAB";
        boolean verify = RSAUtil.verify(RSAUtil.getSignatureContent(paramMap), sign, a);
        System.out.println(verify);
        System.out.println(ptPublicKey.length());
        System.out.println(privateKey.length());
    }

    /**
     * 
     * @title: testPush
     * @description:testPush
     */
    @org.junit.Test
    public void testPush() {
        String a = "{\"sign\":\"hmoBFbyTi1QmnMtPcE8/XUSR6HcNVQ4hR2ZBHbgzs7m6dKeNjBuNoE5rqcEmAaR/PPTwlO0NMlvY+z21mr3LNOe/NI6mXzaithXruCqCJeBdbzj1YZmqmcr1evxfNAaP+ThzII6rMVhqHnOqt38sDyh1PFb6Lcg8S93smnYwp+8\\u003d\",\"timestamp\":\"2017-05-19 14:12:55\",\"content\":\"c3PX8j3oA9qlajRp8VsSxVAeABJFVFrndbivQXMoaFv5nnMQ3P2vziTi4d6XY8t0u4abbhMUElprxSMhy5880hTeUkVPVKu2oinrVJW227hdToKathg+KhE86RccUS4v/t7+MI7KL7c3lonzpC2hmFAFGoZL8ncK1P1dh6Smf53IDTtJ6Wxi3dVKJt6XHw4IK+kEYcrO/dG2E7GEfoUT/w\\u003d\\u003d\",\"serialNo\":\"GAGA201705191412550mACGnRI\",\"platformCode\":\"vZpDTUSV\",\"signType\":\"RSA\",\"format\":\"JSON\",\"version\":\"1.0\"}";
        String requestJson = "{\"format\":\"JSON\",\"sign\":\"eGhssKmnCMh1UckqqIqhv8XBiTPOU3JqGBHlflc8lZWHJtMBnN4r5Q2ovgNqkfZv78wE8jpiaucpQGKmHKXOc9Tda0oWcW5o0jF6mPtN5+xgRjeZIVQN1ZGGLay5/OQb/9SlwRdFN1a1zy0uAwjn+57i4UivS2NEeAn2Xd6yjvI\\u003d\",\"signType\":\"RSA\",\"version\":\"1.0\",\"platformCode\":\"1Ie3pU3A\",\"content\":\"QfjtruvJgIDYjKMCC92dt2QDqulOgslkDpVfwE3aOgULxrY52WlS4jLoADo/MIdqa/tqETq46Po0u7KGk9k5t1NYA0J/LYGuLmqfGYLjOXT0M8HLFK3UTwN4sgCK03bBzaRfD8xjR0X21E+TzgMuPCtQ8/Wgt5TK2i9l0CzeclbBlVRAJT7mGyzQI2rlvd9ffM7Ok+kk/IvdxMfZ+ZfDAg\\u003d\\u003d\",\"timestamp\":\"2017-05-16 15:53:56\",\"serialNo\":\"WYTC20170516155356pS5CzZMs\"}";
        String postJson = HttpUtils.postJson("http://imis.dev.goago.cn/ptInvoiceResult/register.do", a);

        System.out.println(postJson);
    }

    @org.junit.Test
    public void testpush() {
        String content = "{\"taxpayerNum\":\"GAGA000000000001\",\"enterpriseName\":\"测试注册A\",\"platformCode\":\"13242753\",\"registrationCode\":\"87774618\",\"authorizationCode\":\"4209110715\"}";
        String encrypt3des = SecurityUtil.encrypt3DES("vJxgkIaQiwPu2nWSLTSaVSwl", content);
        System.out.println(encrypt3des);
    }
    
    @org.junit.Test
    public void test() throws Base64DecodingException {
    	String invoiceUrl="aHR0cDovL2Zwa2oudGVzdG53LnZwaWFvdG9uZy5jbi9kL09UY3dPVE01TkRRMU1UWXhNREF3T1RZdy5wdA==";
    	byte[] decode = Base64.decode(invoiceUrl);;
    	String info = new String(decode);
    	System.out.println("invoiceUrl=="+info);
    }
    

}
