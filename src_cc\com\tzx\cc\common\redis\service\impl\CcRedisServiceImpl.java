package com.tzx.cc.common.redis.service.impl;

import javax.annotation.Resource;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class CcRedisServiceImpl extends AbstractCcRedisCommonServiceImpl {	
	@Resource(name = "saasRedisTemplate")
	private RedisTemplate<String, String> redisTemplate;

	@Override
	protected RedisTemplate<String, String> getRedisTemplate() {
		return redisTemplate;
	}
	
	
}
