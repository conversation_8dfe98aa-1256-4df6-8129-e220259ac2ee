package com.tzx.report.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.params.CoreProtocolPNames;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Repository;

import com.tzx.framework.bo.dto.Organ;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.dao.GenericDao;

import net.sf.json.JSONObject;

/**
 * 
 * <AUTHOR> 报表条件公共类 （ Tree后缀为combotree专用， Common后缀为 combobox 专用） 
 *
 */

@Repository
public class ConditionUtils {
	@Resource(name = "genericDaoImpl")
	private  GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	
	static StringBuilder sb = new StringBuilder();
	
	/**
	 * 付款类型
	 * @param tenancyID
	 * @param condition
	 * @return  
	 * @throws Exception
	 */
	public  List<JSONObject> getPaymenttypeCommon(String tenancyID,JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select payment_type_code,payment_type from v_payment_type");
		List<JSONObject> list  = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 付款方式（联动）
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getModeOfPaymentCommon(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select * from v_payment_name");
		if(condition.containsKey("payment_type_code")){
			sb.append(" where payment_type_code in("+condition.optString("payment_type_code")+")");
		}
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 付款方式（非联动）
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getModeOfPayment(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select  id, payment_name1 from payment_way where status='1' order by id");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 菜品类别 xlbh
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesTypeCommon(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("SELECT distinct xlbh,XLMC FROM BB_V_CPLB order by xlbh ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 菜品名称   xlbh
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesNameCommon(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		String code = spilt(condition.optString("xlbh"));
		sb.append("SELECT distinct xlbh,XLMC,XMBH,XMMC FROM BB_V_CPLB where xlbh in ("+code+") order by xmbh ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 菜品类别  dlbh
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesTypeCommons(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("SELECT distinct dlbh,DLMC FROM BB_V_CPLB order by dlbh ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 菜品名称  dlbh
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesNameCommons(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		String code = spilt(condition.optString("dlbh"));
		sb.append("SELECT distinct dlbh,DLMC,XMBH,XMMC FROM BB_V_CPLB where dlbh in ("+code+") order by xmbh ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	
	/**
	 * POS菜品类别  dlbh
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesClass(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select distinct id,itemclass_code,itemclass_name,father_id from hq_item_class where father_id = 0 order by itemclass_code");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * POS菜品类别  xlbh
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesSmallClass(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		String code = spilt(condition.optString("id"));
		sb.append("select distinct itemclass_code,itemclass_name,father_id from hq_item_class where father_id in("+code+") order by itemclass_code");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	
	/**
	 * 营业区域 根据 organ_id 查询相对应的 餐位
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getBusinessAreaCommon(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		/*sb.append("select  id,class_item from sys_dictionary WHERE CLASS_IDENTIFIER_CODE='business_area'");*/
		sb.append("select  s.id, s.class_item FROM tables_info t left join sys_dictionary s on t.business_area_id = s.id and s.class_identifier_code = 'business_area' and s.valid_state = '1'");
		if(condition.containsKey("organ_ids")) {
			sb.append(" where t.organ_id in ("+condition.optString("organ_ids")+") ");    
		}
		sb.append(" GROUP BY s.id ");   
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 餐位类型 根据 organ_id 查询相对应的 餐位
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getMealPositionTypeCommon(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		//sb.append("select id,class_item from sys_dictionary where class_identifier_code = 'table_property'");
		sb.append("select  s.id, s.class_item FROM tables_info t left join sys_dictionary s on t.table_property_id = s.id and s.class_identifier_code = 'table_property' and s.valid_state = '1'");
		if(condition.containsKey("organ_ids")) {
			sb.append(" where t.organ_id in ("+condition.optString("organ_ids")+") ");                
		}
		sb.append(" GROUP BY s.id ");    
		List<JSONObject> list  = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 团体会员
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getGroupMemberCommon(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append(" select distinct id,name from crm_incorporation_info order by id");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 挂账人(联动)
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getLossesArePeopleCommon(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append(" select distinct p.incorporation_id as incorporation_id, p.customer_id as customer_id , c.name as name from crm_incorporation_person p join crm_customer_info c on p.customer_id = c.id and p.incorporation_id in("+condition.getString("id")+")");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 *   所有班次 
	 * @param tenantid
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> loadDutyOrderNew(String tenantid, JSONObject condition) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.setLength(0);
		/*sql.append("SELECT a.id,(k.class_item || '('||a.start_time || '-' || a.end_time ||')') AS text");
		sql.append(" FROM duty_order A LEFT JOIN sys_dictionary k ON a.name=k.class_item_code AND k.class_identifier_code='duty'");
		sql.append(" LEFT JOIN duty_order_of_ogran b ON b.duty_order_id = a.id WHERE 1=1 AND A.valid_state='1'");
		if(condition.optInt("store_id")!=0 && !condition.containsKey("organ_id"))
		{
			sql.append(" AND b.organ_id = " + condition.optString("store_id"));
		}
		else if(condition.containsKey("organ_id"))
		{
			sql.append(" AND b.organ_id in (" + condition.optString("organ_id") + ")");
		}
		sql.append(" GROUP BY a.id,k.class_item");
		sql.append(" ORDER BY CASE WHEN a.name='ZB01' THEN 1 WHEN a.name='ZB02' THEN 2 WHEN a.name='WB03' THEN 3 ELSE 4 END, a.start_time");*/

		if (condition.containsKey("is_time_text") && !condition.optBoolean("is_time_text", true)) {
			//是否加载时间文字显示
			sql.append(" SELECT distinct a.id, k.class_item AS text");
		} else {
			sql.append(" SELECT distinct a.id,(k.class_item || '(' || a.start_time || '-' || a.end_time || ')') AS text");
		}


		if(condition.containsKey("getName") && condition.optString("getName").equals("yes") ) {
			sql.append(", a.name as name ");
		}
		sql.append(" FROM duty_order a LEFT JOIN sys_dictionary k ON a.name = k.class_item_code AND k.class_identifier_code = 'duty'");
		sql.append(" LEFT JOIN duty_order_of_ogran b ON b.duty_order_id = a.id WHERE 1 = 1 ");
		if(!condition.containsKey("class_state")) {
			sql.append(" AND A.valid_state = '1' ");
		} 
		
		if(condition.containsKey("store_id"))
		{
			sql.append(" AND b.organ_id in (" + condition.optString("store_id") + ")");
		}
		else if(condition.containsKey("organ_id"))
		{
			sql.append(" AND b.organ_id in (" + condition.optString("organ_id") + ")");
		}
		sql.append(" ORDER BY id");
		return this.dao.query4Json(tenantid, sql.toString());
	}
	
	
	/**
	 *   所有班次   直接返回字段，不然通过返回数据返回处理太复杂
	 * @param tenantid
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> loadDutyOrder(String tenantid, JSONObject condition) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.setLength(0);
		
		sql.append(" SELECT distinct a.id,k.class_item,a.start_time,a.end_time ");
		sql.append(" FROM duty_order a LEFT JOIN sys_dictionary k ON a.name = k.class_item_code AND k.class_identifier_code = 'duty'");
		sql.append(" LEFT JOIN duty_order_of_ogran b ON b.duty_order_id = a.id WHERE 1 = 1 AND A.valid_state = '1'");
		if(condition.containsKey("store_id"))
		{
			sql.append(" AND b.organ_id in (" + condition.optString("store_id") + ")");
		}
		else if(condition.containsKey("organ_id"))
		{
			sql.append(" AND b.organ_id in (" + condition.optString("organ_id") + ")");
		}
		sql.append(" ORDER BY id");
		return this.dao.query4Json(tenantid, sql.toString());
	}
	
	
	/**
	 * 交易机构-交易流水，充值明细，金额沉淀专用
	 * @param tenancyId
	 * @param organId
	 * @param organCode
	 * @param params
	 * @param conditions
	 * @return
	 * @throws Exception
	 */
	public String getOrgansTreeByConditios(String tenancyId, String organId, String organCode, JSONObject params, String conditions) throws Exception {
		StringBuilder sb = new StringBuilder();
		Organ organ = new Organ();
		Organ organs = new Organ();
		sb.append("select id,CONCAT(organ_code,org_full_name) as text,top_org_id as fatherId,level,organ_code as code,org_type as type,tenant_state as tenantstate,org_uuid as orguuid from organ where 1=1 and valid_state='1'"); 
		if(null != conditions && StringUtils.isNotBlank(conditions)){
			sb.append("and id in("+conditions+")    ");
		}
		sb.append(" order by organ_code  ");
		
		@SuppressWarnings("unchecked")
		List<Organ> list = (List<Organ>) this.dao.query(tenancyId, sb.toString(), Organ.class);
		List<Organ> list2 = new ArrayList<Organ>();
		Map<Integer, Organ> map = new HashMap<Integer, Organ>();
		for (Organ jo : list){
			map.put(jo.getId(), jo);
		}

		for (Organ jo1 : list){
			if (map.get(jo1.getFatherId()) == null){

				list2.add(jo1);
			}

			if (map.get(jo1.getFatherId()) != null){

				map.get(jo1.getFatherId()).getChildren().add(map.get(jo1.getId()));

			}
		}
		boolean flag = false;
		
		if("0".equals(organId))
		{
			flag = true;
		}
		else
		{
			String[] oids = conditions.split(",");
			for(String oid:oids)
			{
				if("0".equals(oid))
				{
					flag = true;
					break;
				}
			}
		}
		if (flag){
			List<Organ> flist = new ArrayList<Organ>();
			organ.setChildren(list2);
			organ.setLevel(0);
			organ.setCode("");
			organ.setId(0);
			organ.setType("1");
			organ.setText(organ.getCode() + "总部");
			 
			organs.setLevel(2);
			organs.setCode("999999999");
			organs.setId(999999999);
			organs.setType("3");
			organs.setText("总部线上");
			
			flist.add(organ);
			flist.add(organs);
			
			return JsonUtils.list2json(flist);
		}
		return JsonUtils.list2json(list2);
	}
	
	/**
	 * 收银人员
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> loadCashierNum(String tenancyID, JSONObject condition) throws Exception
	{
		sb.setLength(0);
		if (condition.containsKey("is_shift") && condition.optBoolean("is_shift", false)) {
			//是否只显示交班人员
			sb.append(" SELECT DISTINCT b.id as user_name,b.name from employee b");
			sb.append(" INNER JOIN pos_opter_changshift_main as pocm on pocm.store_id = b.store_id and  pocm.opt_num::int4 = b.id");
			sb.append(" left join organ o on o.id=b.store_id where 1=1 ");
			sb.append(" and o.id in ( "+condition.optString("organ_id")+" )");
		} else {
			sb.append(" SELECT b.id as user_name,b.name from employee b");
			sb.append(" left join organ o on o.id=b.store_id where 1=1 ");
			if(!condition.containsKey("organ_id")){
				sb.append(" and o.id in (select id from organ k where k.organ_code like '"+condition.optString("organ_code")+"%' ) ");
			}else{
				sb.append(" and o.id in ( "+condition.optString("organ_id")+" )");
			}
		}


		
		// 增加特殊权限的交班人员  -- 顾凯豪问题
		
		if(condition.containsKey("specialOpen")) {
			sb.setLength(0);
			sb.append("  SELECT b. ID AS user_name, b. NAME FROM employee b LEFT JOIN organ o ON o. ID = b.store_id WHERE 1 = 1  ");
			if(!condition.containsKey("organ_id")){
				sb.append("  AND o. ID  in (select id from organ k where k.organ_code like '"+condition.optString("organ_code")+"%' ) OR b. ID in ( SELECT employee_id as id  FROM user_authority_roles WHERE store_id in (select id from organ k where k.organ_code like '"+condition.optString("organ_code")+"%' ) GROUP BY employee_id )  GROUP BY b. ID  ");
			}else{
				sb.append("AND o. ID IN ("+condition.optString("organ_id")+") OR b. ID in ( SELECT employee_id as id  FROM user_authority_roles WHERE store_id IN ("+condition.optString("organ_id")+") GROUP BY employee_id )  GROUP BY b. ID ");
			}
		}
		
		System.out.println(sb);
		List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 当前登入商户下所有人员
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getUserPersonnel(String tenancyID, JSONObject condition) throws Exception
	{
		sb.setLength(0);
		sb.append("  SELECT id,user_name as TEXT FROM user_authority u WHERE u.tenancy_id = '"+tenancyID+"'");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 自定义模板
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCustomPermissions(String tenancyID, JSONObject condition) throws Exception
	{
		
		sb.setLength(0);
		sb.append(" select 0 as id,'默认模板' as text,'0' as is_default \n");
		sb.append(" UNION all \n");
		sb.append(" SELECT t.id as id,t.template_name as text,auth.is_default as is_default FROM rpt_free_template t \n");
		sb.append(" LEFT JOIN rpt_free_authority auth ON auth.report_num = t.report_num  and auth.frpt_id = t.id \n");
		sb.append(" LEFT JOIN rpt_free_use use on auth.report_num = use.report_num and t.ID = use.frpt_id \n");
		sb.append(" WHERE 1=1 and user_name = "+condition.optString("user_name")+" \n");
		sb.append(" AND auth.report_num = "+condition.optString("report_num")+" \n");
		sb.append(" AND use.inquiry_mode = "+condition.optString("inquiry_mode")+" \n");
		sb.append("  GROUP BY T.ID,T.template_name,auth.is_default");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 用户权限
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getUserJurisdiction(String tenancyID, JSONObject condition) throws Exception
	{
		sb.setLength(0);
		sb.append(" select id,user_name as text,employee_id from user_authority where valid_state = '1' ");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 获取机构下所有employee
	 * @param tenantId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getWaiter(String tenantId, JSONObject obj) throws Exception{
		sb.setLength(0);
		sb.append(" select b.id,b.name from organ a left join employee b on a.id = b.store_id where a.id in(");
		sb.append(obj.optString("store_id"));
		sb.append(")");
		System.out.println(sb.toString());
		List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
		return list;
	}
	
	
	/*****************************************combotree******************************************************************* 
	
	
	
	/**
	 * 菜品类别  dlbh   级联 - 菜品名称
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesTypeDlTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("SELECT distinct dlbh as id,DLMC as text FROM BB_V_CPLB order by dlbh ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 菜品名称  dlbh  级联 - 菜品类别
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesNameDlTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		String code = spilt(condition.optString("dlbh"));
		sb.append("SELECT distinct dlbh,DLMC,XMBH as id,XMMC as text FROM BB_V_CPLB where dlbh in ("+code+") order by xmbh ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 菜品类别 xlbh  - 级联  - 菜品名称
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesTypeTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("SELECT distinct xlbh as id ,XLMC as text FROM BB_V_CPLB order by xlbh ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 菜品名称   xlbh - 级联  - 菜品类别
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesNameTrees(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		String code = spilt(condition.optString("xlbh"));
		sb.append("SELECT distinct xlbh,XLMC,XMBH as id,XMMC as text FROM BB_V_CPLB where xlbh in ("+code+") order by xmbh ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 营业区域
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getBusinessAreaTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select  id,class_item as text from sys_dictionary WHERE CLASS_IDENTIFIER_CODE='business_area'");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 餐位类型
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getMealPositionTypeTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select id,class_item as text from sys_dictionary where class_identifier_code = 'table_property'");
		List<JSONObject> list  = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 付款明细
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getPaymentDetailsTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		
		sb.append("  with a as ( ");
		sb.append(" 	select 1 as id , 'cash' as payment_class ");
		sb.append(" union ALL select 2 as id , 'bankcard' as payment_class");
		sb.append(" union ALL select 3 as id , 'card' as payment_class ");
		sb.append(" union ALL select 4 as id , 'thirdparty' as payment_class");
		sb.append(" union ALL select 5 as id , 'card_credit' as payment_class");
		sb.append(" union ALL select 6 as id , 'incorporation' as payment_class");
		sb.append(" union ALL select 7 as id , 'coupons' as payment_class");
		sb.append(" union ALL select 8 as id , 'other' as payment_class");
		sb.append(" ) select  ");
		sb.append(" concat  ('t', payment_id) AS pname , payment_name as zname , payment_type as payment_class  from v_payment_name as t1");
		sb.append(" left JOIN a on a.payment_class=t1.payment_type_code order by   a.id,payment_class,payment_id ");
		
		
		/*sb.append(" select concat('t',w.id) as pname, case when s.class_item is null then '其他' else class_item end as payment_class,(case payment_class when 'cash'  then (  \n");
		sb.append(" select max(class_item) from sys_dictionary where class_identifier_code='currency' and class_item_code=payment_name1) else payment_name1 end  ) as zname from payment_way w   \n");
		sb.append(" LEFT JOIN sys_dictionary s ON w.payment_class = s.class_item_code where status='1' ORDER BY payment_class  ");
		*/
		
		// 没有按照支付方式查询的sql
		//sb.append(" select DISTINCT concat ('t', payment_id) AS pname , payment_name as zname ,payment_type as payment_class from v_payment_name order by payment_class ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 付款明细按照付款方式排序
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getPaymentDetailsTreeOrderByClaas(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("  with a as ( ");
		sb.append(" 	select 1 as id , 'cash' as payment_class ");
		sb.append(" union ALL select 2 as id , 'bankcard' as payment_class");
		sb.append(" union ALL select 3 as id , 'card' as payment_class ");
		sb.append(" union ALL select 4 as id , 'thirdparty' as payment_class");
		sb.append(" union ALL select 5 as id , 'card_credit' as payment_class");
		sb.append(" union ALL select 6 as id , 'incorporation' as payment_class");
		sb.append(" union ALL select 7 as id , 'coupons' as payment_class");
		sb.append(" union ALL select 8 as id , 'other' as payment_class");
		sb.append(" ) select  ");
		sb.append(" concat  ('t', payment_id) AS pname , payment_name as zname , payment_type as payment_class  from v_payment_name as t1");
//		sb.append(" left JOIN a on a.payment_class=t1.payment_type_code order by   a.id,payment_class,payment_id ");
		//sb.append(" left JOIN a on a.payment_class=t1.payment_type_code order by   a.id,payment_class,payment_type_code "); // 2018-3-26 修改支付方式排序
		sb.append(" left JOIN a on a.payment_class=t1.payment_type_code order by payment_type_code ");//分类排序
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	

	//付款方式
	public  List<JSONObject> getPaymentMethodTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("SELECT DISTINCT payment_id AS id, payment_name AS text, payment_type AS payment_class FROM v_payment_name ORDER BY payment_class ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 菜品名称  dlbh Tree
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDishesNameTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("SELECT xmid as id,XMMC as text FROM BB_V_CPLB where 1=1 ");
		if(condition.containsKey("id")){
			sb.append(" and xlid in ("+condition.optString("id")+")");
		}
		if(condition.containsKey("category_id")){
			StringBuilder catsb = new StringBuilder();
			//自定义类别
			List<JSONObject> cat_list =  dao.query4Json(tenancyID, "select item_id from hq_item_category_class_relation where category_id in ("+condition.optString("category_id")+")");
			if(cat_list.size()>0){
				for(JSONObject cat : cat_list) {
					  catsb.append(cat.optString("item_id")+",");
					}
				sb.append(" and xmid in ("+catsb.substring(0, catsb.length()-1)+")");
			}else{
				//如果没有菜品信息默认查询0
				sb.append(" and xmid in (0)");
			}
		}
		sb.append(" order by xmbh ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	
	/**
	 * 活动类型
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getDetailOptions(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append(" select  class_item_code activity_code, class_item activity_name from sys_dictionary where class_identifier_code='activity_type' and valid_state='1'");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 活动主题
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getActivityTheme(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append(" select id,subject as activity_name  from  crm_activity order by 1");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 营销主题
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getEventTopicDetail(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append(" select id,subject as activity_name  from  crm_activity where activity_type = "+condition.optString("activity_type")+"  order by 1");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	    //营业桌位 
		public Object getBusinessSeatTree(String tenancyID, JSONObject json) throws Exception {
			sb.setLength(0);
			sb.append("select table_code as id,table_name as text from tables_info where 1=1  ");
			if(json.containsKey("meal_position_type")&&!json.optString("meal_position_type").equals("")) {
				sb.append(" and table_property_id in("+json.opt("meal_position_type")+")");
			}if(json.containsKey("store_id")&&!json.optString("store_id").equals("")){
				sb.append(" and organ_id in("+json.opt("store_id")+")");
			}
			List<JSONObject> list  = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
	
	
	/**
	 * 竖版报表通用付款方式
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getPaymentDetailVerticalVersion(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append(" select v.payment_name payment_type,sum(h.pay_money) local_currency  ");
		if(condition.containsKey("shift_id")){
			sb.append(" from hq_payment_count_shift_new h");
		}else if(condition.containsKey("start_time") && condition.containsKey("end_time")){
			sb.append(" from hq_payment_count_times_new h");
			sb.append(" LEFT JOIN sys_times s ON h.times_id = s.time_id");
		}else{
			sb.append(" from hq_payment_count_new h");
		}
		sb.append(" left join v_payment_name v on h.payment_id = v.payment_id  ");
		sb.append(" where 1=1 ");
		if(condition.containsKey("day_count") && !condition.optString("day_count").equals("")) {
			sb.append(" and h.day_count = '" + condition.optString("day_count")+"' ");
		}else {
			sb.append(" and h.day_count BETWEEN '" + condition.optString("begin_date")+"' and '" + condition.optString("end_date")+"'");
		}
		sb.append(" and h.store_id in (" + condition.optString("store_id") + ") ");
		if(condition.containsKey("shift_id")){
			sb.append(" and h.shift_id in (" + condition.optString("shift_id")+") ");
		}
		if(condition.containsKey("start_time") && condition.containsKey("end_time")){
			sb.append(" and s.end_time BETWEEN '"+condition.optString("start_time")+"' and '"+condition.optString("end_time")+"' ");
		}
		sb.append(" group by payment_name ");
		sb.append(" order by local_currency");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 时段
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getBusinessSummaryPeriod(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append(" select 1 as time_id,'00:00' as end_time UNION select time_id,end_time from sys_times where time_type  = 'half' order by time_id ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 注册门店 - 交易流水专用
	 * @param tenentid
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public String getOrganTreeAllA(String tenentid, JSONObject params) throws Exception
	{
		StringBuilder sb = new StringBuilder();
		Organ organ = new Organ();
		Organ organs = new Organ();
		sb.append("select id,CONCAT(organ_code,org_full_name) as text,top_org_id as fatherId,level,organ_code as code,org_type as type,tenant_state as tenantstate,org_uuid as orguuid  from organ");
		sb.append(" where 1=1 and valid_state='1' ");
		sb.append(" order by organ_code ");

		@SuppressWarnings("unchecked")
		List<Organ> list = (List<Organ>) this.dao.query(tenentid, sb.toString(), Organ.class);
		List<Organ> list2 = new ArrayList<Organ>();
		Map<Integer, Organ> map = new HashMap<Integer, Organ>();
		for (Organ jo : list)
		{
			map.put(jo.getId(), jo);

		}

		for (Organ jo1 : list)
		{

			if (0 == jo1.getFatherId())
			{
				list2.add(jo1);
			}

			if (map.get(jo1.getFatherId()) != null)
			{
				map.get(jo1.getFatherId()).getChildren().add(map.get(jo1.getId()));
			}
		}

		organ.setChildren(list2);
		organ.setLevel(0);
		organ.setCode("");
		organ.setId(0);
		// 默认总部属于分公司，可以建立下级机构
		organ.setType("1");
		organ.setText(organ.getCode() + "总部");
		
		organs.setLevel(2);
		organs.setCode("999999999");
		organs.setId(999999999);
		organs.setType("2");
		organs.setText("总部线上");
		List<Organ> flist = new ArrayList<Organ>();
		flist.add(organ);
		flist.add(organs);
		return com.tzx.framework.common.util.JsonUtils.list2json(flist);
	}
	
	
	/**
	 * 操作类型(交易流水)
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getOperationType(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select class_item,class_item_code from sys_dictionary where class_item_code in ('01','03','05','02','04','06','09','08','07','10','12','11','00','13') and class_identifier_code = 'operat_type'");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 操作类型(积分)
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getIntegralOperationType(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select class_item,class_item_code from sys_dictionary where class_item_code in ('16','17','14','15','10','20') and class_identifier_code = 'operat_type'");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	
			
	/**
	 * 订单状态
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getOrderStatusTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select class_item_code as id,class_item as text from sys_dictionary where class_identifier_code = 'order_state' ");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 付款状态
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getPaymentStatusTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select class_item_code as id,class_item as text from sys_dictionary where class_identifier_code = 'payment_state'");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}


	/**
	 * 预定类型
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getPredeterminedTypeTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select class_item_code as id,class_item as text from sys_dictionary where class_identifier_code = 'order_type'");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 订单来源
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getOrderSourceTree(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select class_item_code as id,class_item as text from sys_dictionary where class_identifier_code = 'chanel'");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 订单来源（根据表中数据来显示）
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getOrderSourceTrees(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("select a.chanel as id,x.class_item as text from (select chanel from  cc_order_list group by chanel) as a inner join sys_dictionary x on x.class_item_code = a.chanel and x.class_identifier_code = 'chanel'");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	
	/**
	 * 指标类型
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public  List<JSONObject> getIndexAllocation(String tenancyID, JSONObject condition) throws Exception {
		sb.setLength(0);
		sb.append("SELECT id AS ID,index_name AS TEXT FROM sys_index where division = "+condition.getInt("division")+" order by id");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		return list;
	}
	
	/**
	 * 操作人员
	 * @param tenantId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getRegainmanagernum(String tenantId, JSONObject obj) throws Exception{
		sb.setLength(0);
		sb.append(" select e.id as id, e.name as text from user_authority u join employee e on u.employee_id = e.id where u.store_id in (");
		sb.append(obj.optString("store_id"));
		sb.append(")");
		List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
		return list;
	}

	
	/**
	 * 操作机号
	 * @param tenantId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosnum(String tenantId, JSONObject obj) throws Exception{
		sb.setLength(0);
		sb.append(" select devices_code as id,devices_name as text from hq_devices d where d.store_id in (");
		sb.append(obj.optString("store_id"));
		sb.append(")");
		List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
		return list;
	}
	
	
	
	/**
	 * 多层级通用机构
	 * @param tenantId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getMultiLevelGeneralAgency(String tenantId, JSONObject obj) throws Exception
	{
		return dao.query4Json(tenantId, parameterUtils.parameterAutomaticCompletionUpgrade(tenantId, obj,EngineConstantArea.ENGINE_MULTI_LEVEL_GENERAL_AGENCY).toString());
	}
	
	
	/**
	 * 店面级别
	 * @param tenantId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getStoreLevel(String tenantId, JSONObject obj) throws Exception
	{
		sb.setLength(0);
		sb.append("select id as id,class_item as text from sys_dictionary where class_identifier_code = 'storelevel' order by class_item_code");
		List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
		return list;
	}
	
	/**
	 * 地区级别
	 * @param tenantId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getAreaLevel(String tenantId, JSONObject obj) throws Exception
	{
		sb.setLength(0);
		sb.append("select id as id,class_item as text from sys_dictionary where class_identifier_code = 'arealevel' order by class_item_code");
		List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
		return list;
	}
	
	/**
	 * 经营方式
	 * @param tenantId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getBusinessMode(String tenantId, JSONObject obj) throws Exception
	{
		sb.setLength(0);
		sb.append("select id as id,class_item as text from sys_dictionary where class_identifier_code = 'bussinessmode' order by class_item_code");
		List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
		return list;
	}
	
	/**
	 * 针对交易流水查询总部线上设计
	 * @param ids
	 * @return
	 */
	 public static String spilts(String ids) {
		 String result = "y";
		  String[] info=null;
		  info=ids.split(",");
	      for(int i=0;i<info.length;i++){
	    	  if(info[i].equals("999999999")){
	    		  return result;
	    	  }
		}
		return "n";
	}
	 
	 /**
	  * 针对交易流水总部线上查询设计
	  * @param ids
	  * @return
	  */
	 public static String replace(String ids) {
		 StringBuffer sb = new StringBuffer();
		  String[] info=null;
		  info=ids.split(",");
	      for(int i=0;i<info.length;i++){
	    	  if(info[i].equals("999999999")){
	    		  sb.append(info[i].replace("999999999","0")+",");
	    		  //sb.append(info[i] = "0");
	    	  }else{
	    		  sb.append(info[i]+",");
	    	  }
		}
	      sb.deleteCharAt(sb.length()-1);
		return sb.toString();
	}
	 
 
	
	/**
	 * 分隔+‘’
	 * @param str
	 * @return
	 */
	public static String spilt(String str) {
		StringBuffer sb = new StringBuffer();
		String[] temp = str.split(",");
		for (int i = 0; i < temp.length; i++) {
		   if (!"".equals(temp[i]) && temp[i] != null)
		    sb.append("'" + temp[i] + "',");
		  }
		String result = sb.toString();
		String tp = result.substring(result.length() - 1, result.length());
		  if (",".equals(tp))
			  return result.substring(0, result.length() - 1);
		  else
			  return result;
	}
	
	
	
	public static List<JSONObject> cutIn(List<JSONObject> list,String parameter){
		List<JSONObject> json = new ArrayList<JSONObject>();
		if(list.size()>0){
			for(int i=list.size()-1;i>=0;i--){
				if(list.get(i).get(parameter).equals("合计")){
					json.add(list.get(i));
					list.remove(i);
					return json;
				}
			}	
		}
		return json;
	}
	
	
	
	/** 
	* 是否是中文 
	* @param c 
	* @return 
	*/ 
	public static boolean isChinese(char c) { 
		Character.UnicodeBlock ub = Character.UnicodeBlock.of(c); 
		if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS 
				|| ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS 
				|| ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A 
				|| ub == Character.UnicodeBlock.GENERAL_PUNCTUATION 
				|| ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION 
				|| ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS) {
			return true; 
		} 
		return false; 
	} 
	/** 
	* 是否是英文 
	* @param c 
	* @return 
	*/ 
	public static boolean isEnglish(String charaString){ 
		return charaString.matches("^[a-zA-Z]*"); 
	} 
	public static boolean isChinese(String str){ 
		String regEx = "[\\u4e00-\\u9fa5]+"; 
		Pattern p = Pattern.compile(regEx); 
		Matcher m = p.matcher(str); 
		if(m.find())
			return true; 
		else 
		return false; 
	} 
	
	
	
	//营业桌位 
	public Object getBusinessSeat(String attribute, JSONObject p) throws Exception {
		// TODO Auto-generated method stub
		sb.setLength(0);
		//sb.append("select payment_type_code,payment_type from v_payment_type");
		sb.append("select table_name,table_code from tables_info where 1=1  ");
		if(p.containsKey("mealType")&&!p.optString("mealType").equals("")) {
			sb.append(" and table_property_id in("+p.opt("mealType")+")");
		}if(p.containsKey("organ_ids")&&!p.optString("organ_ids").equals("")){
			sb.append(" and organ_id in("+p.opt("organ_ids")+")");
		}
		List<JSONObject> list  = dao.query4Json(attribute, sb.toString());
		return list;
	}
	// 销售员
	public String getEmployeeList(String attribute, JSONObject p) throws Exception {
		// TODO Auto-generated method stub
		sb.setLength(0);
		//sb.append("select payment_type_code,payment_type from v_payment_type");
		sb.append("select id,name from employee  where 1=1  ");
		if(p.containsKey("store_ids")&&!p.optString("store_ids").equals("")) {
			sb.append(" and store_id in ("+p.opt("store_ids")+") ");
		}
		List<JSONObject> list  = dao.query4Json(attribute, sb.toString());
		return com.tzx.framework.common.util.JsonUtils.list2json(list);
	}
	
	// 会员等级 (全部) 根据门店查询门店先会员的等级
	public String getMemberShipLevel(String tenancyID, JSONObject condition) throws Exception
	{
		String result="";
		StringBuilder sql = new StringBuilder();
		sql.append("select id,name from crm_level a ");
		sql.append(" where a.valid_state='1' ");
		List<JSONObject> list3 = this.dao.query4Json(tenancyID, sql.toString());
		result = com.tzx.framework.common.util.JsonUtils.list2json(list3);
		return result;
	}
	
	// 查询操作类型	
		public Object getOperatType(String attribute, JSONObject p) throws Exception {
			// TODO Auto-generated method stub
			sb.setLength(0);
			//sb.append("select payment_type_code,payment_type from v_payment_type");
			sb.append("select operat_type from crm_card_trading_list  where 1=1 ");
			if(p.containsKey("store_ids")&&!p.optString("store_ids").equals("")) {
				sb.append(" and store_id in "+p.opt("store_ids")+" ");
			}
			 sb.append(" GROUP BY operat_type");
			List<JSONObject> list  = dao.query4Json(attribute, sb.toString());
			return list;
		}
	// 查询交易渠道
		public Object getChanelType(String attribute, JSONObject p) throws Exception {
			// TODO Auto-generated method stub
			sb.setLength(0);
			//sb.append("select payment_type_code,payment_type from v_payment_type");
			sb.append("select chanel from crm_card_trading_list  where 1=1 ");
			if(p.containsKey("store_ids")&&!p.optString("store_ids").equals("")) {
				sb.append(" and store_id in "+p.opt("store_ids")+" ");
			}
			 sb.append(" GROUP BY chanel");
			List<JSONObject> list  = dao.query4Json(attribute, sb.toString());
			return list;
		}

		public List<JSONObject> getEnergyConsumption(String tenancyID,
				JSONObject condition) throws Exception {
			// TODO Auto-generated method stub
			sb.setLength(0);
			sb.append(" select concat ('t', h.id) AS pname ,'表底' as zname,concat (h.water_utility_name,' (',(round(default_coefficient, 0)),'倍)') as  payment_class  from  hq_water_utility h LEFT JOIN boh_water_utility_record boh ");
			sb.append(" on boh.water_utilit_id = h.id where h.store_id in ("+condition.opt("store_ids")+") and  h.valid_state = '1' GROUP BY  h.id,h.water_utility_name ORDER BY water_utility_type,h.id");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
	
		public List<JSONObject> getReturneReason(String attribute, JSONObject p) throws Exception {
			// TODO Auto-generated method stub
			sb.setLength(0);
			sb.append("SELECT id,reason_name FROM hq_unusual_reason WHERE  unusual_type ='TC03'");
			List<JSONObject> list = dao.query4Json(attribute, sb.toString());
			return list;
		}
		
		public String getApprovalMan(String tenancyID, JSONObject p) throws Exception {
			List<JSONObject> list = new ArrayList<JSONObject>();
			StringBuffer sql =new StringBuffer();
			sql.append(" select d.manager_num as id ,e.name as text from pos_bill_item2 d left join employee e on d.manager_num = e.id ::text where  e.store_id in (");
			sql.append(p.opt("store_ids"));
			sql.append(")  GROUP BY d.manager_num,e.name");
			 list = this.dao.query4Json(tenancyID,sql.toString());
			return JsonUtils.list2json(list);
		}
		
		public List<JSONObject> getFreeReason(String attribute, JSONObject p) throws Exception {
			// TODO Auto-generated method stub
			sb.setLength(0);
			sb.append("SELECT id,reason_name FROM hq_unusual_reason WHERE  unusual_type ='FS02'");
			List<JSONObject> list = dao.query4Json(attribute, sb.toString());
			return list;
		}
		
		
		/**
		 * 列类型
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getSqlStructure(String tenancyID,String sql) throws Exception {
			sb.setLength(0);
			String sqlReplace = sql.replaceAll("'", "''");
			sb.append("select * from f_sql_structure('"+sqlReplace+"')");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 付款明细
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getPaymentDetailsTreeStatus2(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			
			sb.append(" WITH T AS (                                                                          ");
			sb.append(" 		SELECT                                                                       ");
			sb.append(" s1. ID,                                                                              ");
			sb.append(" s1.class_identifier_code,                                                            ");
			sb.append(" s1.class_item_code AS payment_type_code,                                             ");
			sb.append(" s1.class_item AS payment_type,                                                       ");
			sb.append(" w. ID AS payment_id,                                                                 ");
			 sb.append(" CASE                                                                                ");
			 sb.append(" WHEN (                                                                              ");
			 sb.append(" (s2.class_identifier_code) :: TEXT = 'currency' :: TEXT                             ");
			 sb.append(" ) THEN                                                                              ");
			 sb.append(" s2.class_item                                                                       ");
			 sb.append(" ELSE                                                                                ");
			 sb.append(" w.payment_name1                                                                     ");
			 sb.append(" END AS payment_name                                                                 ");
			 sb.append(" FROM                                                                                ");
			 sb.append(" (                                                                                   ");
			 sb.append(" (                                                                                   ");
			sb.append("					payment_way w                                                        ");
			sb.append("					LEFT JOIN sys_dictionary s1 ON (                                     ");
			sb.append("						(                                                                ");
			sb.append("							(                                                            ");
			sb.append("								(w.payment_class) :: TEXT = (s1.class_item_code) :: TEXT ");
			sb.append("							)                                                            ");
			sb.append("							AND (                                                        ");
			sb.append("								(s1.model_name) :: TEXT = 'hq' :: TEXT                   ");
			sb.append("							)                                                            ");
			sb.append("						)                                                                ");
			sb.append("					)                                                                    ");
			sb.append("				)                                                                        ");
			sb.append("				LEFT JOIN sys_dictionary s2 ON (                                         ");
			sb.append("					(                                                                    ");
			sb.append("						(                                                                ");
			sb.append("							(w.payment_name1) :: TEXT = (s2.class_item_code) :: TEXT     ");
			sb.append("						)                                                                ");
			sb.append("						AND (                                                            ");
			sb.append("							(s2.model_name) :: TEXT = 'hq' :: TEXT                       ");
			sb.append("						)                                                                ");
			sb.append("					)                                                                    ");
			sb.append("				)                                                                        ");
			sb.append("			)                                                                            ");
			/*sb.append("		WHERE                                                                            ");
			sb.append("			((w.status) :: TEXT = '1' :: TEXT)                                           ");*/
			sb.append("	),                                                                                   ");
			sb.append("	 t1 AS (                                                                             ");
			sb.append("		SELECT DISTINCT                                                                  ");
			sb.append("			T . ID,                                                                      ");
			sb.append("			T .payment_id,                                                               ");
			sb.append("			CASE                                                                         ");
			sb.append("		WHEN (                                                                           ");
			sb.append("			(T .class_identifier_code) :: TEXT = 'third_pay' :: TEXT                     ");
			sb.append("		) THEN                                                                           ");
			sb.append("			'第三方' :: CHARACTER VARYING                                                ");
			sb.append("		ELSE                                                                             ");
			sb.append("			COALESCE (                                                                   ");
			sb.append("				T .payment_type,                                                         ");
			sb.append("				'其他' :: CHARACTER VARYING                                              ");
			sb.append("			)                                                                            ");
			sb.append("		END AS payment_type,                                                             ");
			sb.append("		CASE                                                                             ");
			sb.append("	WHEN (                                                                               ");
			sb.append("		(T .class_identifier_code) :: TEXT = 'third_pay' :: TEXT                         ");
			sb.append("	) THEN                                                                               ");
			sb.append("		'thirdparty' :: CHARACTER VARYING                                                ");
			sb.append("	ELSE                                                                                 ");
			sb.append("		COALESCE (                                                                       ");
			sb.append("			T .payment_type_code,                                                        ");
			sb.append("			'other' :: CHARACTER VARYING                                                 ");
			sb.append("		)                                                                                ");
			sb.append("	END AS payment_type_code,                                                            ");
			sb.append("	 T .payment_name                                                                     ");
			sb.append("	FROM                                                                                 ");
			sb.append("		T                                                                                ");
			sb.append("	),                                                                                   ");
			sb.append("	 v_payment_name1 AS (                                                                ");
			sb.append("		SELECT                                                                           ");
			sb.append("			t1.payment_id,                                                               ");
			sb.append("			t1.payment_type_code,                                                        ");
			sb.append("			t1.payment_type,                                                             ");
			sb.append("			t1.payment_name                                                              ");
			sb.append("		FROM                                                                             ");
			sb.append("			t1                                                                           ");
			sb.append("		ORDER BY                                                                         ");
			sb.append("			t1. ID                                                                       ");
			sb.append("	),                                                                                   ");
			sb.append("	 A AS (                                                                              ");
			sb.append("		SELECT                                                                           ");
			sb.append("			1 AS ID,                                                                     ");
			sb.append("			'cash' AS payment_class                                                      ");
			sb.append("		UNION ALL                                                                        ");
			sb.append("			SELECT                                                                       ");
			sb.append("				2 AS ID,                                                                 ");
			sb.append("				'bankcard' AS payment_class                                              ");
			sb.append("			UNION ALL                                                                    ");
			sb.append("				SELECT                                                                   ");
			sb.append("					3 AS ID,                                                             ");
			sb.append("					'card' AS payment_class                                              ");
			sb.append("				UNION ALL                                                                ");
			sb.append("					SELECT                                                               ");
			sb.append("						4 AS ID,                                                         ");
			sb.append("						'thirdparty' AS payment_class                                    ");
			sb.append("					UNION ALL                                                            ");
			sb.append("						SELECT                                                           ");
			sb.append("							5 AS ID,                                                     ");
			sb.append("							'card_credit' AS payment_class                               ");
			sb.append("						UNION ALL                                                        ");
			sb.append("							SELECT                                                       ");
			sb.append("								6 AS ID,                                                 ");
			sb.append("								'incorporation' AS payment_class                         ");
			sb.append("							UNION ALL                                                    ");
			sb.append("								SELECT                                                   ");
			sb.append("									7 AS ID,                                             ");
			sb.append("									'coupons' AS payment_class                           ");
			sb.append("								UNION ALL                                                ");
			sb.append("									SELECT                                               ");
			sb.append("										8 AS ID,                                         ");
			sb.append("										'other' AS payment_class                         ");
			sb.append("	) SELECT                                                                             ");
			sb.append("		concat ('t', payment_id) AS pname,                                               ");
			sb.append("		payment_name AS zname,                                                           ");
			sb.append("		payment_type AS payment_class                                                    ");
			sb.append("	FROM                                                                                 ");
			sb.append("		v_payment_name1 AS t1                                                            ");
			sb.append("	LEFT JOIN A ON A .payment_class = t1.payment_type_code                               ");
			sb.append("	ORDER BY");
			sb.append("		A . ID,");
			sb.append("		payment_class,");
			sb.append("		payment_id");
			
			/*sb.append(" select concat('t',w.id) as pname, case when s.class_item is null then '其他' else class_item end as payment_class,(case payment_class when 'cash'  then (  \n");
			sb.append(" select max(class_item) from sys_dictionary where class_identifier_code='currency' and class_item_code=payment_name1) else payment_name1 end  ) as zname from payment_way w   \n");
			sb.append(" LEFT JOIN sys_dictionary s ON w.payment_class = s.class_item_code where status='1' ORDER BY payment_class  ");
			*/
			
			// 没有按照支付方式查询的sql
			//sb.append(" select DISTINCT concat ('t', payment_id) AS pname , payment_name as zname ,payment_type as payment_class from v_payment_name order by payment_class ");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		
		
		
		/**
		 * 引擎报表唯一编码
		 * @param tenantId
		 * @param obj
		 * @return
		 * @throws Exception
		 */
		public List<JSONObject> getReportEnginetNum(String tenancyID) throws Exception
		{
			sb.setLength(0);
			sb.append("select report_num as id,report_num as text from saas_report_engine GROUP BY report_num order by 1 desc");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * java版本
		 * @param tenantId
		 * @param obj
		 * @return
		 * @throws Exception
		 */
		public List<JSONObject> getJavaVersion(String tenancyID) throws Exception
		{
			sb.setLength(0);
			sb.append("select java_version as id,java_version as text from saas_report_version  order by java_version desc");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * sql版本
		 * @param tenantId
		 * @param obj
		 * @return
		 * @throws Exception
		 */
		public List<JSONObject> getSqlVersion(String tenancyID) throws Exception
		{
			sb.setLength(0);
			sb.append("select sql_version as id,sql_version as text from saas_report_version  order by java_version desc");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		
		
		/**
		 * 代销门店
		 * @param tenantId
		 * @param obj
		 * @return
		 * @throws Exception
		 */
		public List<JSONObject> getConsignmentStore(String tenancyID, JSONObject condition) throws Exception
		{
			sb.setLength(0);
			sb.append("select target_store_id as id,target_store_name as text from hq_sales_agency_relation where valid_state = '1' and delete_state = '1' and source_store_id in ("+condition.optString("store_id")+")");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 根据所选门店获取门店下所有的活动
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public List<JSONObject> getActivity(String tenancyID, JSONObject condition)throws Exception
		{
			sb.setLength(0);
//			sb.append("select b.id,b.subject from crm_activity_org as a INNER JOIN crm_activity as b on b.id=a.activity_id where store_id in ("+condition.optString("store_id")+") group by b.id,b.subject order by b.id,b.subject");
			sb.append("SELECT DISTINCT b.ID, b.subject FROM crm_activity_org AS A INNER JOIN crm_activity AS b ON b. ID = A .activity_id where b.subject like '%外送%' and b.subject like '%折%' ");
			if(condition.containsKey("organ_id")){
				sb.append(" and store_id IN ("+condition.optString("organ_id")+")");
			}
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		/**
		 * 根据门店获取门店下所有的活动类别——默认全部
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public List<JSONObject> getActivityType(String tenancyID, JSONObject condition)throws Exception
		{
			sb.setLength(0);
			sb.append("select class_item_code AS activity_code, class_item AS activity_name "
					+ "from sys_dictionary where class_item_code in (SELECT DISTINCT b.activity_type "
					+ "FROM crm_activity_org AS A INNER JOIN crm_activity AS b ON b. ID = A .activity_id");
			if(condition.containsKey("organ_id")){
				sb.append(" WHERE a.store_id IN ("+condition.optString("organ_id")+")");
			}
			sb.append(")");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取品牌
		 * @param attribute
		 * @param p
		 * @return
		 */
		public Object getBrand(String tenancyID, JSONObject condition)throws Exception {
			sb.setLength(0);
			sb.append("select a.id as brand_id,a.org_full_name as brand_name from organ as a where a.level = 2");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取KVS名称
		 * @param attribute
		 * @param p
		 * @return
		 */
		public Object getKVSName(String tenancyID, JSONObject condition)throws Exception {
			sb.setLength(0);
			sb.append("select devices_code as id,devices_name as name from hq_devices ");
			String storeId = condition.optString("store_id");
			if(null != storeId && StringUtils.isNotBlank(storeId)){
				sb.append(" where store_id in ("+storeId+")");
			}
			System.out.println(sb.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 营运指标类型
		 * @param attribute
		 * @param p
		 * @return
		 */
		public Object getOperationIndexType(String tenancyID, JSONObject condition)throws Exception {
			sb.setLength(0);
			sb.append("select id from sys_index where division = '"+condition.getString("vision")+"' order by id");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}

		/**
		 * 获取价格体系
		 * @param attribute
		 * @param p
		 * @return
		 */
		public Object getPriceSystemUrl(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select id as id,price_system_name as name from hq_price_system");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取价格体系 与门店联动
		 * @param attribute
		 * @param p
		 * @return
		 */
		public  List<JSONObject> getPriceSystemWithStore(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select o.price_system as id,h.price_system_name as name from organ o left join hq_price_system h on o.price_system::int = h.id where o.level = 5 ");
			if(condition.containsKey("organ_ids")&&condition.optString("organ_ids").length()!=0) {
				sb.append(" and o.id in ("+condition.optString("organ_ids")+") ");    
			}
			sb.append(" group by o.price_system,h.price_system_name ");   
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取菜品转台操作人
		 * @param attribute
		 * @param p
		 * @return
		 */
		public Object getOperator(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select opt_num,opt_name from pos_table_log group by opt_num,opt_name");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
/**
		 * 获取操作人——味千
		 * @param attribute
		 * @param p
		 * @return
		 */
		public Object getOperatorWQ(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select e.id,e.name from employee as e INNER JOIN user_authority_roles as u on e.id=u.employee_id where u.roles_id in(6,3)");
			String storeId = condition.optString("id");
			if(null != storeId && StringUtils.isNotBlank(storeId)){
				sb.append("and e.store_id in ("+storeId+")");
			}
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取免单原因
		 * @param attribute
		 * @param p
		 * @return
		 */
		public Object getSingleReason(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select id,reason_name as text from hq_unusual_reason where unusual_type in ('MD01','MD03') order by id");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取审批人员——味千
		 * @param attribute
		 * @param p
		 * @return
		 */
		public Object getOperatorWQ2(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select e.id,e.name from employee as e INNER JOIN user_authority_roles as u on e.id=u.employee_id where u.roles_id = 2");
			String storeId = condition.optString("id");
			if(null != storeId && StringUtils.isNotBlank(storeId)){
				sb.append("and e.store_id in ("+storeId+")");
			}
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}

		
		/**
		 * 获取所有品牌名称
		 * @param attribute
		 * @param p
		 * @return
		 */
		public Object getBrandNameUrl(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select a.brand_code as id,a.brand_name1 as text from organ_brand as a union all select b.id::text as id,b.strname as text from hq_manybrands_foroneshop b");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取报表编号集合
		 */
		public List<JSONObject> getReportList(String tenancyID, JSONObject conditions) throws Exception{
			StringBuffer sb = new StringBuffer();
			sb.append("select report_num from rpt_free group by report_num order by report_num");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取查询方式集合
		 */
		public List<JSONObject> getSearchMethodList(String tenancyID, JSONObject conditions) throws Exception{
			StringBuffer sb = new StringBuffer();
			sb.append("select * from rpt_dictionary where valid_state = '1' and class_identifier_code='search_method' order by class_item_code");
			List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 门店类型   根据 organ_id 查询相对应的门店类型store_type
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getStoreTypeUrl(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select b.class_item_code as id, b.class_item as name from organ as a left join sys_dictionary as b on a.store_type = b.class_item_code where b.class_identifier_code = 'store_type' ");
			if(condition.containsKey("organ_ids")&&condition.optString("organ_ids").length()!=0) {
				sb.append(" and a.id in("+condition.optString("organ_ids")+") ");    
			}
			sb.append(" group by b.class_item_code,b.class_item");   
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 经营方式  根据 organ_id 查询相对应的经营方式manage_type
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getManageTypeUrl(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select b.id as id, b.class_item as name from organ as a left join sys_dictionary as b on a.manage_type = b.id where b.class_identifier_code = 'bussinessmode' ");
			if(condition.containsKey("organ_ids")&&condition.optString("organ_ids").length()!=0) {
				sb.append(" and a.id in("+condition.optString("organ_ids")+") ");    
			}
			sb.append(" group by b.id,b.class_item");   
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 店面级别 根据 organ_id 查询相对应的店面级别storelevel
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getStoreLevelUrl(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select b.id as id, b.class_item as name from organ as a left join sys_dictionary as b on a.storelevel = b.id where b.class_identifier_code = 'storelevel' ");
			if(condition.containsKey("organ_ids")&&condition.optString("organ_ids").length()!=0) {
				sb.append(" and a.id in("+condition.optString("organ_ids")+") ");    
			}
			sb.append(" group by b.id,b.class_item");   
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 地区级别 根据 organ_id 查询相对应的店面级别arealevel
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getAreaLevelUrl(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select b.id as id, b.class_item as name from organ as a left join sys_dictionary as b on a.arealevel = b.id where b.class_identifier_code = 'arealevel' ");
			if(condition.containsKey("organ_ids")&&condition.optString("organ_ids").length()!=0) {
				sb.append(" and a.id in("+condition.optString("organ_ids")+") ");    
			}
			sb.append(" group by b.id,b.class_item");   
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 门店下所有的菜品授权操作人 根据 organ_id 查询相对应的店面级别storelevel
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getOperatorUrl(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select distinct operator as id,operator as name from hq_item_authorization_his where 1=1 ");
			if(condition.containsKey("store_id")&&condition.optString("store_id").length()!=0) {
				sb.append(" and store_id in("+condition.optString("store_id")+") ");    
			}
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取机构简码
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getOrganCode(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select organ_brief_code as id,organ_brief_code as text from organ where level = 5 and operating_status = 'normal_business' ");
			if(condition.containsKey("organ_ids")&&condition.optString("organ_ids").length()!=0) {
				sb.append(" and id in("+condition.optString("organ_ids")+") ");    
			}
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 查询HBASE使用权限
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public Boolean getHBASEPrivilege(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select status from sys_config_center where config_code='10104'");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			if(list.size()>0){
				String status = list.get(0).optString("status");
				if(status.equals("1")){
					return true;
				}else{
					return false;
				}
			}
			return false;
		}
		
		/**
		 * 获取特殊类别
		*/
		public  List<JSONObject> getSpecialType(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" SELECT id as id,class_item as text from sys_dictionary where class_identifier_code = 'report_show_class' ");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取登录账号
		*/
		public  List<JSONObject> getLoginAccount(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select DISTINCT user_name as id,user_name as text from user_authority where 1=1 ");
			if(condition.containsKey("organ_ids")&&condition.optString("organ_ids").length()!=0) {
				sb.append(" and store_id in("+condition.optString("organ_ids")+") ");    
			}
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取员工姓名
		 */
		public  List<JSONObject> getEmployeeName(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select distinct id,NAME as text from employee where 1 = 1 ");
			if(condition.containsKey("organ_ids")&&condition.optString("organ_ids").length()!=0) {
				sb.append(" and store_id in("+condition.optString("organ_ids")+") ");    
			}
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取角色
		 */
		public  List<JSONObject> getRole(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select DISTINCT b.id as id,b.role_name as text from roles b order by id ");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取员工状态
		 */
		public  List<JSONObject> getState(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select distinct a.states as id,b.class_item as text from employee a LEFT JOIN sys_dictionary b on a.states=b.class_item_code and b.class_identifier_code='states' ");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		/**
		 * 获取口味名称
		 */
		public  List<JSONObject> getTasteName(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select id as id,name as text from item_taste ");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 获取供应链机构树
		 */
		public  List<JSONObject> getSCMOrganTree(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select ID,STORE_NAME,PARENT_ID,PARENT_NAME as text from ESCM_STORE_INFO");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		/**
		 * 获取供应链仓库集合
		 */
		public  List<JSONObject> getSCMWarehouse(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select ID as id,WAREHOUSE_NAME as text from ESCM_WAREHOUSE where busi_state='0'");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			JSONObject tmp = new JSONObject();
			tmp.put("id", "1");tmp.put("text", "val1");
			JSONObject tmp2 = new JSONObject();
			tmp2.put("id", "2");tmp2.put("text", "val2");
			list.add(tmp);list.add(tmp2);
			return list;
		}
		/**
		 * 获取供应链供应商集合
		 */
		public  List<JSONObject> getSCMSupply(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select ID as id,SUPPLIER_NAME as text from ESCM_SUPPLY_INFO where valid_state='1' and busi_state='0'");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			JSONObject tmp = new JSONObject();
			tmp.put("id", "1");tmp.put("text", "val1");
			JSONObject tmp2 = new JSONObject();
			tmp2.put("id", "2");tmp2.put("text", "val2");
			list.add(tmp);list.add(tmp2);
			return list;
		}
		
		/**
		 * 获取变价原因
		 */
		public  List<JSONObject> getChangePriceReason(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select reason as id, reason as text from hq_item_pricechange group by reason ");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		
		/**
		 * 菜品名称  dlbh Tree(味千专版带点菜码)
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getDishesNameTreeWQ(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("SELECT xmid as id,h.third_code||' '||XMMC as text FROM BB_V_CPLB left join hq_item_info h on xmid = h.id where 1=1 ");
			if(condition.containsKey("id")){
				sb.append(" and xlid in ("+condition.optString("id")+")");
			}
			if(condition.containsKey("category_id")){
				StringBuilder catsb = new StringBuilder();
				//自定义类别
				List<JSONObject> cat_list =  dao.query4Json(tenancyID, "select item_id from hq_item_category_class_relation where category_id in ("+condition.optString("category_id")+")");
				if(cat_list.size()>0){
					for(JSONObject cat : cat_list) {
						  catsb.append(cat.optString("item_id")+",");
						}
					sb.append(" and xmid in ("+catsb.substring(0, catsb.length()-1)+")");
				}else{
					//如果没有菜品信息默认查询0
					sb.append(" and xmid in (0)");
				}
			}
			sb.append(" order by xmbh ");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 
		 * 发送HTTP_POST请求
		 * 
		 * @see 该方法会自动关闭连接,释放资源
		 * 
		 * @see 该方法会自动对<code>params</code>中的[中文][|][ ]等特殊字符进行
		 *      <code>URLEncoder.encode(string,encodeCharset)</code>
		 * 
		 * @param reqURL
		 *            请求地址
		 * 
		 * @param params
		 *            请求参数
		 * 
		 * @return 远程主机响应正文
		 */
		public static String sendPostRequest(String reqURL, Map<String, String> params, String tenancyId)
		{
			String responseContent = null;

			HttpClient httpClient = new DefaultHttpClient();
			httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 3000);
			httpClient.getParams().setParameter(CoreProtocolPNames.HTTP_CONTENT_CHARSET, "UTF-8");
			httpClient.getParams().setParameter(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded");
			HttpPost httpPost = new HttpPost(reqURL);
			httpPost.addHeader("tenancyId", tenancyId);
			List<NameValuePair> formParams = new ArrayList<NameValuePair>(); // 创建参数队列
			for (Map.Entry<String, String> entry : params.entrySet())
			{
				formParams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
			}

			try
			{
				httpPost.setEntity(new UrlEncodedFormEntity(formParams, "UTF-8"));
				HttpResponse response = httpClient.execute(httpPost);
				
				int status = response.getStatusLine().getStatusCode();
				if (status == 200)
				{

					HttpEntity entity = response.getEntity();

					if (null != entity)
					{
						responseContent = EntityUtils.toString(entity, "UTF-8");
						EntityUtils.consume(entity);
					}
				}else if (status == 404)
				{
					JSONObject obj = new JSONObject();
					obj.put("code", 1);
					obj.put("msg", "找不到服务器，请联系管理员~");
					responseContent = obj.toString();
				}else
				{
					JSONObject obj = new JSONObject();
					obj.put("code", 1);
					obj.put("msg", "连接服务器异常，请联系管理员~");
					responseContent = obj.toString();
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				httpClient.getConnectionManager().shutdown();
			}
			return responseContent;
		}

		/**
		 * POS编号
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public List<JSONObject> getLoadPOSNum(String tenancyID, JSONObject condition) throws Exception
		{
			 String sql =" select devices_code as pos_number , devices_name as pos_name  from hq_devices where 1=1 "; // show_type = 'CP' 
			 if(condition.containsKey("organ_id")) {
				 sql +=" and store_id in ("+condition.optString("organ_id")+") ";
			 }
			List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
			return list;
		}
		
		/**
		 * 付款明细按照付款方式排序
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws wangb
		 */
		public  List<JSONObject> getNoComeOrInComPaymentDetails(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select concat  ('t', payment_id) AS pname ,payment_name as zname ,'不计收入付款方式' as payment_class from v_payment_way  where if_income  = '0'  ");
			sb.append(" UNION all ");
			sb.append(" select concat  ('t', payment_id) AS pname , payment_name as zname ,  '计收入付款方式' as payment_class from v_payment_way  where if_income  = '1'");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		/**
		 * 配送类型
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public  List<JSONObject> getDeliveryPartyTree(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append("select class_item_code as id,class_item as text from sys_dictionary where class_identifier_code = 'delivery_party'");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
		/**
		 * 付款明细按照付款方式排序
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws wangb
		 */
		public  List<JSONObject> getPaymentDetailsOrderByClassForHuaLaiShi(String tenancyID, JSONObject condition) throws Exception {
			sb.setLength(0);
			sb.append(" select concat  ('t', payment_id) AS pname ,payment_name as zname ,'堂食收款' as payment_class from v_payment_way   ");
			sb.append(" where status = '1' and payment_name not in ('百度外卖付款','美团外卖付款','饿了么外卖付款') ORDER BY payment_name ");
			List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
			return list;
		}
		
}
