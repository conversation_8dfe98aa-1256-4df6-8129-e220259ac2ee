package com.tzx.cc.invoice.electronic.service.impl;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import sun.misc.BASE64Encoder;

import com.alibaba.fastjson.JSON;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.Type;
import com.tzx.cc.invoice.electronic.bworiginal.DesEencryptUtil;
import com.tzx.cc.invoice.electronic.bworiginal.MyAES;
import com.tzx.cc.invoice.electronic.bworiginal.Utils;
import com.tzx.cc.invoice.electronic.cache.BWConfigCache;
import com.tzx.cc.invoice.electronic.cache.SysparamCache;
import com.tzx.cc.invoice.electronic.cont.ElectronicInvoiceConst;
import com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao;
import com.tzx.cc.invoice.electronic.service.ElecInvoiceService;
import com.tzx.cc.invoice.electronic.util.BwElectronicInvoiceWebServiceUtils;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoiceUtils;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoiceWebServiceUtils;
import com.tzx.cc.invoice.electronic.util.UUID4EWMUtils;
import com.tzx.cc.invoice.electronic.util.UUIDUtils;
import com.tzx.cc.invoice.electronic.util.XmlUtils;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.news.cont.Contant;

/**
 * <AUTHOR>
 *
 */
@Service("com.tzx.cc.invoice.electronic.service.impl.BwElecInvoiceServiceImpl")
public class BwElecInvoiceServiceImpl implements ElecInvoiceService {
	private static final Logger logger = Logger
			.getLogger(BwElecInvoiceServiceImpl.class);
	
	@Autowired
	private ElecInvoiceDao elecInvoiceDao;
	
	@Resource(name = "genericDaoImpl")
	private GenericDao		dao;
	
	@Resource(name="saasRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

	/**
	 * 验证data属性
	 * @param data
	 * @param result
	 * @return
	 */
	private boolean validaData(Data data,Data result){
		String tenancyId = data.getTenancy_id();
		if(StringUtils.isBlank(tenancyId)) {
			result.setMsg("参数：tenancy_id不允许为空");
			result.setCode(Contant.ILLEGAL_PARAM);
			logger.info("参数：tenancy_id不允许为空");
			return false;
		}
		return true;
	}
	/**
	 * 生成电子发票
	 * @param data
	 * @param result
	 * @throws Exception
	 */
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#issueElectronicInvoice(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void issueElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		String serviceType = param.optString("SERVICE_TYPE");
		if(StringUtils.isBlank(serviceType)) {
			result.setMsg("业务类型不能为空");
            return;
		}
		
		Type type = data.getType();
		
		JSONObject json = elecInvoiceDao.getRequestInfo(data.getTenancy_id(),data.getStore_id());
		if(json==null) {
			result.setMsg("总部未配置纳税人识别号，税率等信息");
			return;
		}
		
		if(Type.ISSUE_ELECTRONIC_INVOICE.equals(type)) {
			String isEleServiceType = SysparamCache.getSysparam(data.getTenancy_id(), data.getStore_id(), serviceType);
            if(!(StringUtils.equals(isEleServiceType, "1")||StringUtils.equals(isEleServiceType, "4"))) {
				result.setMsg("对应业务类型不支持开通电子发票");
				return;
			}
			ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param);
		} else {
			ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param,"XSF_NSRSBH");
		}
		
		
		//验证参数是否全
		boolean validate = validate(param, result,"SERVICE_TYPE","XSF_NSRSBH","data","SL","DDRQ");
		if(!validate) {
			return;
		}
		validate = issueElectronicInvoiceValid(data,result,param);
		if(!validate) {
			return;
		}
		String reqUrl = buildUrl(data,result,param);
		if(StringUtils.isBlank(reqUrl)) {
			return;
		}
		System.out.println(reqUrl);
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		JSONObject resultJson = new JSONObject();
		resultJson.put("url", reqUrl);
		returnList.add(resultJson);
		result.setData(returnList);
	}
	
	/**
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private String buildUrl(Data data, Data result, JSONObject param) throws Exception {
		StringBuffer sb = new StringBuffer();
		//r=门店号,订单号,金额,小票日期,纳税人识别号
		String ewmFpqqlsh = param.optString("EWMFPQQLSH");
		String mdh = data.getTenancy_id()+data.getStore_id();
		String je = param.optString("JSHJ");
		String nsrsbh = param.optString("XSF_NSRSBH");
		
		//处理订单日期格式
		String dataorstr = param.optString("DDRQ");
		Date parseDate = DateUtils.parseDate(dataorstr, new String[]{"yyyy-MM-dd"});
		String datastr = DateFormatUtils.format(parseDate, "yyyyMMdd");
		
		
		String ewmpassword=null;
		String thirdpageid=null;
		try {
			JSONObject getfrxx = BwElectronicInvoiceWebServiceUtils.getfrxx(data.getTenancy_id(), data.getStore_id());
			ewmpassword = getfrxx.optString("wechat_invoice_key");
			thirdpageid = getfrxx.optString("wechat_party_id");
		} catch (Exception e) {
			logger.error(e);
		}
		
		if(ewmpassword==null || thirdpageid==null) {
			result.setCode(Contant.ILLEGAL_PARAM);
			result.setMsg("未配置纳税人识别号"+nsrsbh+"所对应的扫描开票秘钥或id");
			return null;
		}
		String reqUrl = BWConfigCache.getElementText("ewm_url");
		sb.append(mdh).append(",");
		sb.append(ewmFpqqlsh).append(",");
		sb.append(je).append(",");
		sb.append(datastr).append(",");
		sb.append(nsrsbh);
		String r = URLEncoder.encode(DesEencryptUtil.encryptDES(sb.toString(), ewmpassword), "utf-8");
		
		sb = new StringBuffer();
		sb.append(reqUrl).append("?r=").append(r).append("&id=").append(thirdpageid);
		return sb.toString();
	}
	
	/**
	 * 验证发票是否符合规则
	 * @param data
	 * @param result
	 * @param param
	 * @return
	 * @throws Exception 
	 */
	private boolean issueElectronicInvoiceValid(Data data, Data result,
			JSONObject param) throws Exception {
		String dh = param.optString("DH");
		JSONObject json = elecInvoiceDao.queryInfoByOrderNo(data.getTenancy_id(),dh);
		String jshj = calsJSHJ(data,result,param);
		String bwflownum = null;
		if(json==null) {
			String FPQQLSH = String.valueOf(UUIDUtils.next());
			param.put("FPQQLSH", FPQQLSH);
			//计算价税合计
			param.put("JSHJ", jshj);
			bwflownum = String.valueOf(UUID4EWMUtils.next());
			param.put("EWMFPQQLSH", bwflownum);
			json = issueElectronicInvoiceSave(data,result,param);
		} else {
		    param.put("FPQQLSH", json.optString("invoice_flow_number"));
		    bwflownum = json.optString("third_order_code");
		    param.put("EWMFPQQLSH", bwflownum);
		}
		json.put("total_tax_amount", jshj);
		if(StringUtils.isBlank(jshj) || StringUtils.equals(jshj, "null")) {
		    result.setMsg("计算出来的价税合计为空");
            return false;
		}
		//把计算的价税合计存入param中
		param.put("JSHJ", jshj);
		String state = json.optString("invoice_state");
		if(StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS, state)
				) {
			result.setMsg("此发票单号已经取消");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return false;
		}
		if(StringUtils.equals(state, ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS)) {
			result.setMsg("此单号已开具过");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return false;
		}
		
		//获取系统参数设置里面，二维码打印期限
		String ewmdyxq = SysparamCache.getSysparam(data.getTenancy_id(), "dzfp_ewmdyxq");
		String dataorstr = param.optString("DDRQ");
		if(StringUtils.isBlank(ewmdyxq)) {
			ewmdyxq = "7";
		}
		boolean expire = ElectronicInvoiceUtils.isExpire(dataorstr, Integer.parseInt(ewmdyxq));
		if(expire) {
			result.setMsg("二维码有效期为"+ewmdyxq+"天，现在已经失效了");
			return false;
		}
		
		//向redis中存储流水号和商户号的对应关系
		JSONObject redisJson = new JSONObject();
		redisJson.put("ID", json.optString("id"));
		redisJson.put("FPQQLSH", param.optString("FPQQLSH"));
		redisJson.put("XSF_NSRSBH", param.optString("XSF_NSRSBH"));
		redisJson.put("tenancy_id", data.getTenancy_id());
		//设置发票流水号与商户的对应关系到redis中
		redisTemplate.opsForHash().put(ElectronicInvoiceConst.BW_ELECTRON_ICINVOICE_REDIS_CODE, redisJson.optString("FPQQLSH"), redisJson.toString());
		//设置期限为24小时
		redisTemplate.expire(ElectronicInvoiceConst.BW_ELECTRON_ICINVOICE_REDIS_CODE, ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE_EXPIRE, TimeUnit.HOURS);
		
		//设置二维码8位订单号与流水号对应关系
		redisTemplate.opsForHash().put(ElectronicInvoiceConst.BW_EWM_ELECTRON_ICINVOICE_REDIS_CODE, bwflownum, redisJson.optString("FPQQLSH"));
		redisTemplate.expire(ElectronicInvoiceConst.BW_EWM_ELECTRON_ICINVOICE_REDIS_CODE, ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE_EXPIRE, TimeUnit.HOURS);
		return true;
	}
	/**
	 * 生成电子发票保存到数据库
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private JSONObject issueElectronicInvoiceSave(Data data, Data result,
			JSONObject param) throws Exception {
		JSONObject json = new JSONObject();
		json.put("tenancy_id", data.getTenancy_id());
		json.put("organ_id", data.getStore_id());
		json.put("invoice_service_type",param.optString("SERVICE_TYPE"));
		//流水号
		json.put("invoice_flow_number",param.optString("FPQQLSH"));
		//纳税识别号
		json.put("tax",param.optString("XSF_NSRSBH"));
		//价税合计
		json.put("total_tax_amount",param.optString("JSHJ"));
		//税率
		json.put("tax_rate", param.optString("SL"));
		//订单号
		json.put("order_code",param.optString("DH"));
		json.put("third_order_code",param.optString("EWMFPQQLSH"));
		json.put("order_date",param.optString("DDRQ"));
		json.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT);
		json.put("electric_use_choose", ElectronicInvoiceConst.ELECTRIC_USE_CHOOSE_BW);
		Integer id = (Integer) dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", json);
		json.put("id", id);
		JSONObject json2 = new JSONObject();
		json2.put("electronic_id", id);
		//税率
		json2.put("tax_rate", param.optString("SL"));
		//项目名称
		json2.put("name", ElectronicInvoiceConst.ELECTRON_ICINVOICE_XMMC);
		dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_details", json2);
		return json;
	}

	/**
	 * 计算价税合计
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private String calsJSHJ(Data data, Data result, JSONObject param) throws Exception {
	    String paymentclassJeStr = param.optString("data");
	    if(StringUtils.isBlank(paymentclassJeStr)) {
	        return null;
	    }
	    com.alibaba.fastjson.JSONArray paymentclassJeArray = JSON.parseArray(paymentclassJeStr);
	    if(paymentclassJeArray==null || paymentclassJeArray.isEmpty()) {
	        return null;
	    }
	    StringBuffer paymentClassBuffer = new StringBuffer();
	    
	    //如果是门店传递过来的付款方式和金额，不用验证总部这边是否配置，直接把金额加起来即可
	    if(Type.ISSUE_ELECTRONIC_INVOICE_SCC.equals(data.getType())) {
	    	 BigDecimal bigDecimal = new BigDecimal(0);
	 	    //遍历传过来的paymentClass
	 	    for(Object jsonObject :paymentclassJeArray){
	 	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	 	        BigDecimal je = json.getBigDecimal("JE");
	 	        bigDecimal = bigDecimal.add(je);
	 	    }
	 	    if(bigDecimal.compareTo(BigDecimal.ZERO)==0) {
	 	    	return null;
	 	    }
	 	    bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
	    	return bigDecimal.toString();
		} 
	    
	    
	    //遍历paymentclassJeArray 拼接paymentClass的sql条件 拼接付款方式，判断是否开具电子发票
	    for(Object jsonObject : paymentclassJeArray){
	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	        if(!json.containsKey("PAYMENT_CLASS") || !json.containsKey("JE")) {
	            return null;
	        }
	        //拼接付款类型条件，
	        String paymentClass = json.getString("PAYMENT_CLASS");
	        paymentClassBuffer.append(paymentClass).append(",");
	    }
	    String paymentClassStr = paymentClassBuffer.toString();
	    paymentClassStr = paymentClassStr.substring(0,paymentClassStr.length()-1);
	    paymentClassStr = paymentClassStr.replaceAll(",", "','");
	    //根据条件（法人机构id，还有付款类型）获取付款方式是否支持开具电子发票 
	    List<JSONObject> queryPaymentWay = elecInvoiceDao.queryPaymentWay(data.getTenancy_id(),data.getStore_id(),paymentClassStr);
	    
	    BigDecimal bigDecimal = new BigDecimal(0);
	    //遍历传过来的paymentClass
	    for(Object jsonObject :paymentclassJeArray){
	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	        String paymentClass = json.getString("PAYMENT_CLASS");
	        //遍历查出来的paymentClass
	        for(JSONObject queryJson:queryPaymentWay){
	            String ifInvoicing = queryJson.optString("if_invoicing");
	            String queryPaymentClass = queryJson.optString("payment_class");
	            //如果查出来的和传进来的一样并且已开启，就把金额相加
	            if(StringUtils.equals(paymentClass, queryPaymentClass) && StringUtils.equals(ifInvoicing, "1")) {
	                BigDecimal je = json.getBigDecimal("JE");
	                bigDecimal = bigDecimal.add(je);
	                break;
	            }
	        }
	    }
	    if(bigDecimal.compareTo(BigDecimal.ZERO)==0) {
 	    	return null;
 	    }
	    bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
        return bigDecimal.toString();
    }
    /* (non-Javadoc)
	 * 取消电子发票接口
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#cancelElectronicInvoice(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void cancelElectronicInvoice(Data data, Data result)
			throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		JSONObject json = elecInvoiceDao.getRequestInfo(data.getTenancy_id(),data.getStore_id());
		if(json==null) {
		    result.setMsg("取消订单总部未配置纳税人识别号，税率等信息");
		    return;
		}
        ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param);
		boolean validate = validate(param, result, "DH","XSF_NSRSBH");
		if(!validate) {
			return;
		}
		JSONObject queryInfoByOrderNo = elecInvoiceDao.queryInfoByOrderNo(data.getTenancy_id(), param.optString("DH"));
		if(queryInfoByOrderNo==null
				|| StringUtils.equals(queryInfoByOrderNo.optString("invoice_state"),ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT)
				) {
			if(queryInfoByOrderNo==null) {
				result.setMsg("订单号无效，创建一个订单号");
				queryInfoByOrderNo = new JSONObject();
			}
	        queryInfoByOrderNo.put("tenancy_id", data.getTenancy_id());
	        queryInfoByOrderNo.put("organ_id", data.getStore_id());
			queryInfoByOrderNo.put("order_code", param.optString("DH"));
			queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL);
			queryInfoByOrderNo.put("invoice_cancel_time", ElectronicInvoiceUtils.currentTime2Str());
			if(StringUtils.isBlank(queryInfoByOrderNo.optString("id"))) {
				this.dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
			} else {
				this.dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
			}

			List<JSONObject> returnList = new ArrayList<JSONObject>();
	        JSONObject resultJson = new JSONObject();
	        resultJson.put("code", "0000");
			resultJson.put("msg", "取消成功");
			returnList.add(resultJson);
			result.setData(returnList);
			result.setSuccess(Boolean.TRUE);
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
			result.setMsg("调用成功");
		    return;
		}
		String state = queryInfoByOrderNo.optString("invoice_state");
		if(StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS, state)
				) {
			result.setMsg("此发票单号已经取消");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return;
		}

		String FPQQLSH = queryInfoByOrderNo.optString("invoice_flow_number");
		JSONObject cancelParam = new JSONObject();
		cancelParam.put("ddbh", FPQQLSH);
		cancelParam.put("sfcz", 1);
		JSONObject resultJson = new JSONObject();
		hcfpkj(cancelParam, resultJson);
		
		
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		String code = resultJson.optString("RSCODE");
		String msg =resultJson.optString("MSG");
		ElectronicInvoiceUtils.uploadOm(queryInfoByOrderNo);
		resultJson.put("code", code);
		resultJson.put("msg", msg);
		resultJson.remove("RSCODE");
		resultJson.remove("MSG");
		
		returnList.add(resultJson);
		result.setData(returnList);
		result.setSuccess(Boolean.TRUE);
		result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
		result.setMsg("调用成功");
	}

	
	
	@Override
	public void queryElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		boolean validate = validate(param, result, "FPQQLSH","XSF_NSRSBH");
		if(!validate) {
			return;
		}
		String FPQQLSH = param.optString("FPQQLSH");
		String XSF_NSRSBH = param.optString("XSF_NSRSBH");
		String xml = ElectronicInvoiceWebServiceUtils.sendWebService(ElectronicInvoiceWebServiceUtils.FPCX(XSF_NSRSBH, FPQQLSH));
		String xmlData = XmlUtils.getReturnData(xml);
		
		Element rootdata = XmlUtils.parse(xmlData);
		Element returncode = XmlUtils.getElement(rootdata, "RESPONSE_COMMON_FPCX","RETURNCODE");
		Element returnmsg = XmlUtils.getElement(rootdata,"RESPONSE_COMMON_FPCX", "RETURNMSG");
		
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		JSONObject resultJson = new JSONObject();
		resultJson.put("code", returncode.getText());
		resultJson.put("msg", returnmsg.getText());
		returnList.add(resultJson);
		result.setData(returnList);
		result.setSuccess(Boolean.TRUE);
		result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
	}
	
	/**
	 * 获取到Data对象中的data  josn
	 * @param data
	 * @return
	 */
	public JSONObject getParam(Data data) {
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		if(list==null || list.isEmpty()) {
			return null;
		}
		// 获取到传入的参数
		JSONObject param = JSONObject.fromObject(list.get(0));
		return param;
	}
	
	
	/**
	 * 处理参数
	 * @param param
	 * @param columns
	 * @return
	 */
	public JSONObject pakJSONObject(JSONObject param,
			String... columns) {
		JSONObject json = new JSONObject();
		for (String column : columns) {
			if (param.containsKey(column)) {
				json.put(column, param.getString(column));
			}
		}
		return json;
	}
	
	/**
	 * 处理参数
	 * @param param
	 * @param columns
	 * @return
	 */
	public Map<String, String> pakMap(JSONObject param,
			String... columns) {
		Map<String, String> map = new TreeMap<String, String>();
		for (String column : columns) {
			if (param.containsKey(column)) {
				map.put(column, param.getString(column));
			}
		}
		return map;
	}
	
	/**
	 * 验证传进的参数格式是否正确
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public boolean validate(JSONObject param, Data result,
			String... columns) {
		for (String column : columns) {
			if (!param.containsKey(column)
					|| StringUtils.isBlank(param.getString(column))) {
				result.setMsg("参数：" + column + "不允许为空");
				result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_PARAM);
				logger.info("参数：" + column + "不允许为空");
				return false;
			}
		}
		return true;
	}
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#issueElectronicInvoice(com.tzx.cc.bo.dto.Data, com.tzx.cc.bo.dto.Data, javax.servlet.http.HttpServletResponse)
	 */
	@Override
	public void issueElectronicInvoice(Data data, Data result,
			HttpServletResponse response) throws Exception {
		issueElectronicInvoice(data, result);
		JSONObject param = getParam(result);
		if(param==null) {
			return;
		}
		String url = param.optString("url");
		response.sendRedirect(url);
	}
	
	@Override
	public void fpkj(JSONObject param, JSONObject result) throws Exception {
		//验证表单必填信息
		if(!validateParam(param, result, "ddbh")) {
			return;
		}
		JSONObject thirdParam = new JSONObject();
		boolean b = fillFpkjInfo(param,thirdParam,result);
		if(!b) {
			return;
		}
		String requestXml = BwElectronicInvoiceWebServiceUtils.json2Xml("<REQUEST_COMMON_FPKJ class='REQUEST_COMMON_FPKJ'>",
                "</REQUEST_COMMON_FPKJ>", thirdParam);
		logger.info("请求百旺开票xml为:"+requestXml);
		String content = new BASE64Encoder().encodeBuffer(requestXml.toString().getBytes("UTF-8"));
//		String appId = BWConfigCache.getElementText("app_id");
//		String contentPassword = BWConfigCache.getElementText("content_password");
//		String xml = BwElectronicInvoiceWebServiceUtils.getSendXml(Utils.dfxj1001, 
//				appId, contentPassword, content);
		String requestUrl = BWConfigCache.getElementText("total_url");
		JSONObject elecInfo = param.optJSONObject("elecInfo");
	 //   String s = BwElectronicInvoiceWebServiceUtils.sendWebService(requestUrl, xml,elecInfo.optString("tenancy_id"),elecInfo.optInt("organ_id"));
	   String s = BwElectronicInvoiceWebServiceUtils.sendWebService(requestUrl, content,elecInfo.optString("tenancy_id"),elecInfo.optInt("organ_id"));
		logger.info("访问百旺电子发票，返回的内容为");
		logger.info(s);
		if(StringUtils.isBlank(s)) {
			result.put("RSCODE", 2001);
			result.put("MSG", "网络连接异常");
			return;
		}
		if(StringUtils.equals("该机构未配置法人信息", s)) {
			result.put("RSCODE", "9999");
			result.put("MSG", s);
			return;
		}
		Element parse = XmlUtils.parse(s);
		Element returncode = XmlUtils.getElement(parse, "returnStateInfo","returnCode");
		Element returnsmg = XmlUtils.getElement(parse, "returnStateInfo","returnMessage");
		logger.info("开具发票RSCODE= "+returncode.getTextTrim());
		logger.info("开具发票MSG= "+returnsmg.getTextTrim());
		if(!returncode.getTextTrim().equals("0000")) {
			result.put("RSCODE", returncode.getTextTrim());
			result.put("MSG", returnsmg.getTextTrim());
			return;
		}
		Element returnContent = XmlUtils.getElement(parse, "Data","content");
		fpkjReturnUpdate(returncode.getTextTrim(),returnContent);
		result.put("RSCODE", returncode.getTextTrim());
		result.put("MSG", returnsmg.getTextTrim());
		
	}
	
	
	public void hcfpkj(JSONObject param, JSONObject result) throws Exception {
		//验证表单必填信息
		if(!validateParam(param, result, "ddbh")) {
			return;
		}
		JSONObject thirdParam = new JSONObject();
		boolean b = fillFpkjInfo(param,thirdParam,result);
		if(!b) {
			return;
		}
		String requestXml = BwElectronicInvoiceWebServiceUtils.json2Xml("<REQUEST_COMMON_FPKJ class='REQUEST_COMMON_FPKJ'>",
				"</REQUEST_COMMON_FPKJ>", thirdParam);
		logger.info(requestXml);
		String content = new BASE64Encoder().encodeBuffer(requestXml.toString().getBytes("UTF-8"));
//		String appId = BWConfigCache.getElementText("app_id");
//		String contentPassword = BWConfigCache.getElementText("content_password");
//		String xml = BwElectronicInvoiceWebServiceUtils.getSendXml(Utils.dfxj1001, 
//				appId, contentPassword, content);
		String requestUrl = BWConfigCache.getElementText("total_url");
		
		JSONObject elecInfo = param.optJSONObject("elecInfo");
		String s = BwElectronicInvoiceWebServiceUtils.sendWebService(requestUrl, content,elecInfo.optString("tenancy_id"),elecInfo.optInt("organ_id"));
		logger.info("访问百旺电子发票，返回的内容为");
		logger.info(s);
		if(StringUtils.isBlank(s)) {
			result.put("RSCODE", 1);
			result.put("MSG", "网络连接异常");
			return;
		}
		if(StringUtils.equals("该机构未配置法人信息", s)) {
			result.put("RSCODE", "9999");
			result.put("MSG", s);
			return;
		}
		Element parse = XmlUtils.parse(s);
		Element returncode = XmlUtils.getElement(parse, "returnStateInfo","returnCode");
		Element returnsmg = XmlUtils.getElement(parse, "returnStateInfo","returnMessage");
		Element returnContent = XmlUtils.getElement(parse, "Data","content");
		if(StringUtils.equals(returncode.getTextTrim(), "0000")) {
			hcfpkjReturnUpdate(param,returnContent);
		}
		result.put("RSCODE", returncode.getTextTrim());
		result.put("MSG", returnsmg.getTextTrim());
		
	}
	
	private void hcfpkjReturnUpdate(JSONObject param,Element returnContent) throws Exception {
		JSONObject elecInfo = param.optJSONObject("elecInfo");
		JSONObject elecInfoDetail = param.optJSONObject("elecInfoDetail");
		String textTrim = returnContent.getTextTrim();
		byte []tmp = MyAES.decryptBASE64(textTrim);
		textTrim = new String(tmp,"UTF-8");
		JSONObject returnjson = XmlUtils.xml2json(textTrim);
		String tenancyId = elecInfo.optString("tenancy_id");
		
		//红冲后修改原订单的状态和取消开票时间
		String kprq = returnjson.optString("KPRQ");
		if(StringUtils.isNotBlank(kprq)) {
			Date parseDate = DateUtils.parseDate(kprq, new String[]{"yyyyMMddHHmmss"});
			String format = DateFormatUtils.format(parseDate, "yyyy-MM-dd HH:mm:ss");
			elecInfo.put("invoice_cancel_time", format);
			elecInfo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS);
		}
		this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", elecInfo);
		
		String fplsh = returnjson.optString("FPQQLSH");
		JSONObject hcelecInfo = this.elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fplsh);
		if(hcelecInfo==null) {
			hcelecInfo = elecInfo;
			hcelecInfo.remove("id");
		}
		//增加红冲记录
		hcelecInfo.put("original_invoice_number", elecInfo.optString("invoice_number"));
		hcelecInfo.put("original_invoice_code", elecInfo.optString("invoice_code"));
		hcelecInfo.remove("invoice_cancel_time");
		
		String fpdm = returnjson.optString("FP_DM");
		String fphm = returnjson.optString("FP_HM");
		String jym = returnjson.optString("JYM");
		String pdfUrl = returnjson.optString("PDF_URL");
		String spUrl = returnjson.optString("SP_URL");
		hcelecInfo.put("invoice_code", fpdm);
		hcelecInfo.put("invoice_number", fphm);
		if(StringUtils.isNotBlank(kprq)) {
			Date parseDate = DateUtils.parseDate(kprq, new String[]{"yyyyMMddHHmmss"});
			String format = DateFormatUtils.format(parseDate, "yyyy-MM-dd HH:mm:ss");
			hcelecInfo.put("invoice_time", format);
		}
		hcelecInfo.put("jym", jym);
		hcelecInfo.put("pdf_url", pdfUrl);
		hcelecInfo.put("sp_url", spUrl);
		
		hcelecInfo.put("total_tax_amount", "-"+elecInfo.optString("total_tax_amount"));
		hcelecInfo.put("tax_amount", "-"+elecInfo.optString("tax_amount"));
		hcelecInfo.put("total_amount", "-"+elecInfo.optString("total_amount"));
		hcelecInfo.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_HP);
		hcelecInfo.put("invoice_flow_number", fplsh);
		
		if(StringUtils.isBlank(hcelecInfo.optString("id"))) {
			int insertIgnorCase = (int) this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_info", hcelecInfo);
			JSONObject details = new JSONObject();
			details.put("electronic_id", insertIgnorCase);
			details.put("invoice_type",  ElectronicInvoiceConst.ELECTRIC_KPLX_HP);
			details.put("name",  ElectronicInvoiceConst.BW_ELECTRON_ICINVOICE_XMMC);
			details.put("number",  -1);
			details.put("price",  elecInfoDetail.optString("XMDJ"));
			details.put("amount",  elecInfoDetail.optString("XMJE"));
			details.put("tax_amount",  elecInfoDetail.optString("SE"));
			details.put("tax_rate",  elecInfo.optString("tax_rate"));
			this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_details", details);
		} else {
			this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", hcelecInfo);
		}
	}
	/**
	 * 发票开具后改变数据库状态
	 * @param returnContent
	 * @throws Exception
	 * @throws UnsupportedEncodingException
	 */
	private void fpkjReturnUpdate(String code,Element returnContent) throws Exception,
			UnsupportedEncodingException {
		String textTrim = returnContent.getTextTrim();
		byte []tmp = MyAES.decryptBASE64(textTrim);
		textTrim = new String(tmp,"UTF-8");
		JSONObject returnjson = XmlUtils.xml2json(textTrim);
		
		String fplsh = returnjson.optString("FPQQLSH");
		String fpdm = returnjson.optString("FP_DM");
		String fphm = returnjson.optString("FP_HM");
		String jym = returnjson.optString("JYM");
		String kprq = returnjson.optString("KPRQ");
		String pdfUrl = returnjson.optString("PDF_URL");
		String spUrl = returnjson.optString("SP_URL");
		JSONObject redisJson = getRedisJson(fplsh);
		String tenancyId = redisJson.optString("tenancy_id");
		
		JSONObject queryInfo = this.elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fplsh);
		queryInfo.put("invoice_code", fpdm);
		queryInfo.put("invoice_number", fphm);
		if(StringUtils.isNotBlank(kprq)) {
			Date parseDate = DateUtils.parseDate(kprq, new String[]{"yyyyMMddHHmmss"});
			String format = DateFormatUtils.format(parseDate, "yyyy-MM-dd HH:mm:ss");
			queryInfo.put("invoice_time", format);
		}
		queryInfo.put("jym", jym);
		queryInfo.put("pdf_url", pdfUrl);
		queryInfo.put("sp_url", spUrl);
		if(StringUtils.equals(code, "0000")) {
			queryInfo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS);
		} else {
			queryInfo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_FAIL);
		}
		dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", queryInfo);
		//上传开具发票信息到OM
		ElectronicInvoiceUtils.uploadOm(queryInfo);
	}
	
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#fpkj(net.sf.json.JSONObject, net.sf.json.JSONObject)
	 */
	@Override
	public void sqkp(JSONObject param, JSONObject result) throws Exception {
		//验证表单必填信息
		if(!validateParam(param, result, "ddbh","fpxm","fpje","fptt","grdh","nsrsbh","ddrq","sl","storeid")) {
			return;
		}
		boolean b = updateParam(param,result);
		if(!b) {
			return;
		}
		result.put("code", 0);
		result.put("msg", "成功");
	}
	
	private boolean updateParam(JSONObject param, JSONObject result) throws Exception {
		String fplsh = param.optString("ddbh");
		int storeId = param.optInt("storeid");
		JSONObject redisJson = getRedisJson(fplsh);
		String tenancyId = redisJson.optString("tenancy_id");
		DBContextHolder.setTenancyid(tenancyId);
		JSONObject saveJson = this.elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fplsh);
		if(saveJson==null) {
			result.put("code", 1);
			result.put("msg", "所传递的参数有误");
			return false;
		}
		
		saveJson.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_LP);
		saveJson.put("electric_use_choose", ElectronicInvoiceConst.ELECTRIC_USE_CHOOSE_BW);
		//法人信息
		JSONObject hqInfo = elecInvoiceDao.getRequestInfo(tenancyId,storeId);
		String sellerName = hqInfo.optString("seller_name");
		String sellerAddress = hqInfo.optString("seller_address");
		String sellerNumber = hqInfo.optString("seller_number");
		
		
		if(StringUtils.isBlank(sellerName)) {
			return valid2Result(result, sellerName,"销售方名称");
		}
		
		if(StringUtils.isBlank(sellerAddress)) {
			return valid2Result(result, sellerName,"销售方地址");
		}
		if(StringUtils.isBlank(sellerNumber)) {
			return valid2Result(result, sellerName,"销售方电话");
		}
		saveJson.put("seller_name", sellerName);
		saveJson.put("seller_address", sellerAddress);
		saveJson.put("seller_phone", sellerNumber);
		
		String gmfmc = param.optString("fptt");
		if(StringUtils.isBlank(gmfmc)) {
			return valid2Result(result, sellerName,"购买方名称");
		}
		saveJson.put("buyer_name", gmfmc);
		String gfdz = param.optString("gfdz");
		String gfdh = param.optString("gfdh");
		String gfyx = param.optString("fpyx");
		String gfzh = param.optString("yhzh");
		String grdh = param.optString("grdh");
		String gmfnsrsbh = param.optString("gmfnsrsbh");
		
		//购买方地址、 购买方电话、购买方 电子邮箱  、购买方银行号、购买方手机号
		saveJson.put("buyer_address", gfdz);
		saveJson.put("buyer_phone", gfdh);
		saveJson.put("buyer_mail", gfyx);
		saveJson.put("bank_no", gfzh);
		saveJson.put("buyer_mobile_phone", grdh);
		saveJson.put("buyer_tax", gmfnsrsbh);
		
		saveJson.put("drawer", "系统");
		
		Double fpje = param.optDouble("fpje");
		Double sl = hqInfo.optDouble("SL");
		
		if(fpje==null || fpje.isNaN()) {
			return valid2Result(result, sellerName,"发票金额");
		}
		if(sl==null || sl.isNaN()) {
			return valid2Result(result, sellerName,"税率");
		}
		//合计税额+金额
		BigDecimal fpjeBig = new BigDecimal(fpje);
		BigDecimal slBig = new BigDecimal(sl.toString());
		//合计金额
		BigDecimal add = slBig.add(new BigDecimal(1));
		BigDecimal hjjeBig = fpjeBig.divide(add,2,BigDecimal.ROUND_HALF_UP);
		//合计税额
		BigDecimal hjseBig = fpjeBig.subtract(hjjeBig);
		hjseBig = hjseBig.setScale(2,BigDecimal.ROUND_HALF_UP);
		
		saveJson.put("total_amount", fpjeBig.toString());
		saveJson.put("tax_amount", hjseBig.toString());
		saveJson.put("total_tax_amount", hjjeBig.toString());
		saveJson.put("invoice_amount", hjjeBig.toString());
		
		this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", saveJson);
		
		JSONObject details = this.elecInvoiceDao.queryDetailsByFlowNumber(tenancyId, saveJson.optInt("id"));
		if(details==null) {
			details = new JSONObject();
		}
		details.put("electronic_id", saveJson.optString("id"));
		details.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_LP);
		details.put("name", param.optString("fpxm"));
		details.put("number", 1);
		
		details.put("price", fpjeBig.toString());
		details.put("amount", fpjeBig.toString());
		details.put("tax_rate", sl);
		details.put("tax_amount", hjseBig.toString());
		if(StringUtils.isBlank(details.optString("id"))) {
			this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_details", details);
		} else {
			this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_details", details);
		}
		
		return true;
	}
	/**
	 * 补全发票开具信息
	 * @param param
	 * @return
	 * @throws Exception 
	 */
	private boolean fillFpkjInfo(JSONObject param,JSONObject json,JSONObject result) throws Exception {
		String fplsh = param.optString("ddbh");
		
		JSONObject redisJson = getRedisJson(fplsh);
		String tenancyId = redisJson.optString("tenancy_id");
		DBContextHolder.setTenancyid(tenancyId);
		JSONObject elecInfo = elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fplsh);
		
		json.put("FPQQLSH", fplsh);
		json.put("KPLX", ElectronicInvoiceConst.ELECTRIC_KPLX_LP);
		json.put("ZSFS", "0");
		json.put("XSF_NSRSBH", elecInfo.optString("tax"));
		
		String sellerName = elecInfo.optString("seller_name");
		String sellerAddress = elecInfo.optString("seller_address");
		String sellerNumber = elecInfo.optString("seller_phone");
		String sellerBankNo = elecInfo.optString("seller_bank_no");

		if(StringUtils.isBlank(sellerName)) {
			return valid2ThirdResult(result, sellerName,"销售方名称");
		}
		
		if(StringUtils.isBlank(sellerAddress)) {
			return valid2ThirdResult(result, sellerAddress,"销售方地址");
		}
		if(StringUtils.isBlank(sellerNumber)) {
			return valid2ThirdResult(result, sellerNumber,"销售方电话");
		}
		json.put("XSF_MC", sellerName);
		json.put("XSF_DZDH", sellerAddress+","+sellerNumber);
		json.put("XSF_YHZH", sellerBankNo);
		json.put("GMF_NSRSBH", "");
		
		String gmfmc = elecInfo.optString("buyer_name");
		if(StringUtils.isBlank(gmfmc)) {
			return valid2ThirdResult(result, gmfmc,"购买方名称");
		}
		json.put("GMF_MC", gmfmc);
		String gfdz = elecInfo.optString("buyer_address");
		String gfdh = elecInfo.optString("buyer_phone");
		if(!StringUtils.isBlank(gfdz) && !StringUtils.isBlank(gfdh)) {
//			json.put("GMF_DZDH", gfdz+gfdh);
			//由于扫码开票回调的时候无法拆分购方地址和购方电话，所以都存到购方地址里面了，故开票的时候只传递购方地址
			json.put("GMF_DZDH", gfdz);
		}
		json.put("GMF_YHZH", elecInfo.optString("bank_no"));
		json.put("GMF_SJH", elecInfo.optString("buyer_mobile_phone"));
		json.put("GMF_DZYX", elecInfo.optString("buyer_mail"));
		json.put("GMF_NSRSBH", elecInfo.optString("buyer_tax"));
		
		json.put("KPR", "系统");
		json.put("WX_ORDER_ID",elecInfo.optString("wx_order_id"));
		json.put("WX_APP_ID", elecInfo.optString("wx_app_id"));
		json.put("ZFB_UID", elecInfo.optString("zfb_uid"));
		
		Double fpje = elecInfo.optDouble("total_tax_amount");
		Double sl = elecInfo.optDouble("tax_rate");
		
		if(fpje==null || fpje.isNaN()) {
			return valid2ThirdResult(result, StringUtils.EMPTY,"发票金额");
		}
		if(sl==null || sl.isNaN()) {
			return valid2ThirdResult(result, StringUtils.EMPTY,"税率");
		}
		BigDecimal fpjeBig = new BigDecimal(fpje);
		fpjeBig = fpjeBig.setScale(2,BigDecimal.ROUND_HALF_UP);
		BigDecimal slBig = new BigDecimal(sl.toString());
		
		BigDecimal add = slBig.add(new BigDecimal(1));
		//合计金额
		BigDecimal hjjeBig = fpjeBig.divide(add,2,BigDecimal.ROUND_HALF_UP);
		//合计税额
		BigDecimal hjseBig = fpjeBig.subtract(hjjeBig);
		hjseBig = hjseBig.setScale(2,BigDecimal.ROUND_HALF_UP);
		
		json.put("JSHJ", fpjeBig.toString());
		json.put("HJSE", hjseBig.toString());
		json.put("HJJE", hjjeBig.toString());
		
		json.put("HYLX", 0);
		
		//项目明细
		JSONArray arr = new JSONArray();
        JSONObject j = new JSONObject();
        j.put("FPHXZ","0");
        j.put("XMMC",ElectronicInvoiceConst.BW_ELECTRON_ICINVOICE_XMMC);
        j.put("SPBM",ElectronicInvoiceConst.ELECTRIC_MEAL_BH);
        j.put("XMSL",1);
        j.put("XMDJ",hjjeBig.toString());
        j.put("XMJE",hjjeBig.toString());
        j.put("SL",sl);
        j.put("SE",hjseBig.toString());
        //红冲接口，关于金额 税额  数量 都为负数
        if(StringUtils.equals(param.optString("sfcz"),"1")) {
        	json.put("JSHJ", "-"+fpjeBig.toString());
        	json.put("HJSE", "-"+hjseBig.toString());
        	json.put("HJJE", "-"+hjjeBig.toString());
        	json.put("KPLX", ElectronicInvoiceConst.ELECTRIC_KPLX_HP);
        	json.put("YFP_DM", elecInfo.optString("invoice_code"));
        	json.put("YFP_HM", elecInfo.optString("invoice_number"));
        	json.put("FPQQLSH", UUIDUtils.next());
        	j.put("XMSL",-1);
        	j.put("XMJE","-"+hjjeBig.toString());
        	j.put("SE","-"+hjseBig.toString());
        }
        param.put("elecInfo",elecInfo);
        param.put("elecInfoDetail",j);
        
        BwElectronicInvoiceWebServiceUtils.fillDetailsObject(j);
        arr.add(j);
        json.put("COMMON_FPKJ_XMXXS", arr);
        
        BwElectronicInvoiceWebServiceUtils.fillJsonObject(json);
		return true;
	}
	
	
	private boolean valid2Result(JSONObject result, String sellerName,String msg) {
		result.put("code", 1);
		result.put("msg", msg+sellerName+"不可为空");
		return false;
	}
	
	private boolean valid2ThirdResult(JSONObject result, String sellerName,String msg) {
		result.put("RSCODE", 1);
		result.put("MSG", msg+sellerName+"不可为空");
		return false;
	}
	
	/**
	 * 获取redis发票流水号所对应的数据
	 * @param fplsh
	 * @return
	 */
	private JSONObject getRedisJson(String fplsh) {
		if(!redisTemplate.hasKey(ElectronicInvoiceConst.BW_ELECTRON_ICINVOICE_REDIS_CODE)) {
			logger.info("redis中没有电子发票"+ElectronicInvoiceConst.BW_ELECTRON_ICINVOICE_REDIS_CODE+"所对应的键");
		}
		if(!redisTemplate.opsForHash().hasKey(ElectronicInvoiceConst.BW_ELECTRON_ICINVOICE_REDIS_CODE, fplsh)) {
			logger.info("redis中没有电子发票"+fplsh+"所对应的键");
		}
		String redisJsonStr = (String) redisTemplate.opsForHash().get(ElectronicInvoiceConst.BW_ELECTRON_ICINVOICE_REDIS_CODE, fplsh);
		com.alibaba.fastjson.JSONObject alijson = com.alibaba.fastjson.JSONObject.parseObject(redisJsonStr);
		JSONObject redisJson = ElectronicInvoiceUtils.convertAli2netJson(alijson);
		return redisJson;
	}
	/**
	 * 获取redis发票流水号所对应的数据
	 * @param fplsh
	 * @return
	 */
	private String getFplsh4Redis(String ewmlsh) {
		if(!redisTemplate.hasKey(ElectronicInvoiceConst.BW_EWM_ELECTRON_ICINVOICE_REDIS_CODE)) {
			logger.info("redis中没有电子发票"+ElectronicInvoiceConst.BW_EWM_ELECTRON_ICINVOICE_REDIS_CODE+"所对应的键");
		}
		if(!redisTemplate.opsForHash().hasKey(ElectronicInvoiceConst.BW_EWM_ELECTRON_ICINVOICE_REDIS_CODE, ewmlsh)) {
			logger.info("redis中没有电子发票"+ewmlsh+"所对应的键");
		}
		String redisJsonStr = (String) redisTemplate.opsForHash().get(ElectronicInvoiceConst.BW_EWM_ELECTRON_ICINVOICE_REDIS_CODE, ewmlsh);
		return redisJsonStr;
	}
	/**
	 * 验证传进的参数格式是否正确
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public boolean validateParam(JSONObject param, JSONObject result,
			String... columns) {
		for (String column : columns) {
			if (!param.containsKey(column)
					|| StringUtils.isBlank(param.getString(column))) {
				result.put("msg","参数：" + column + "不允许为空");
				result.put("code",Contant.ILLEGAL_PARAM);
				logger.info("参数：" + column + "不允许为空");
				return false;
			}
		}
		return true;
	}
	
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#orderCallback(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public void orderCallback(String xmlParam, JSONObject result)
			throws Exception {
		JSONObject returnjson = XmlUtils.xml2json(xmlParam);
		boolean b = validReturn(returnjson,result);
		if(!b) {
			return;
		}
		b = updateParamThirdReturn(returnjson, result);
		if(!b) {
			return;
		}
		String fplsh = getFplsh4Redis(returnjson.optString("orderNo"));
		JSONObject thirdparam = new JSONObject();
		thirdparam.put("ddbh", fplsh);
		fpkj(thirdparam, result);
		System.out.println(returnjson.toString());
	}
	
	private boolean validReturn(JSONObject returnjson, JSONObject result) {
		if(StringUtils.isBlank(returnjson.optString("shopNo"))) {
			result.put("RSCODE", 1001);
			result.put("MSG", "shopNo 不能为空 ");
			return false;
		}
		if(StringUtils.isBlank(returnjson.optString("orderNo"))) {
			result.put("RSCODE", 1002);
			result.put("MSG", "orderNo 不能为空 ");
			return false;
		}
		if(StringUtils.isBlank(returnjson.optString("amt"))) {
			result.put("RSCODE", 1007 );
			result.put("MSG", "amt 不能为空 ");
			return false;
		}
		if(StringUtils.isBlank(returnjson.optString("head"))) {
			result.put("RSCODE", 1005 );
			result.put("MSG", "head 不能为空 ");
			return false;
		}
		if(StringUtils.isBlank(returnjson.optString("orderDate"))) {
			result.put("RSCODE", 1008 );
			result.put("MSG", "orderDate 不能为空 ");
			return false;
		}
		if(StringUtils.isBlank(returnjson.optString("nsrsbh"))) {
			result.put("RSCODE", 1009 );
			result.put("MSG", "nsrsbh 不能为空 ");
			return false;
		}
		return true;
	}
	
	
	/**
	 * 根据第三方返回的页面信息保存信息到数据库 准备开票
	 * @param param
	 * @param result
	 * @return
	 * @throws Exception
	 */
	private boolean updateParamThirdReturn(JSONObject param, JSONObject result) throws Exception {
		String fplsh = getFplsh4Redis(param.optString("orderNo"));
		JSONObject redisJson = getRedisJson(fplsh);
		String tenancyId = redisJson.optString("tenancy_id");
		DBContextHolder.setTenancyid(tenancyId);
		JSONObject saveJson = this.elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fplsh);
		if(saveJson==null) {
			result.put("RSCODE", 9999 );
			result.put("MSG", "所传递的参数有误");
			return false;
		}
		String invoice_state = saveJson.optString("invoice_state");
		if(StringUtils.equals(invoice_state,ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL)
				||StringUtils.equals(invoice_state,ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS)
				||StringUtils.equals(invoice_state,ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS)
				) {
			result.put("RSCODE", 40000 );
			result.put("MSG", "该发票已经被取消");
			return false;
		}

		saveJson.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_LP);
		saveJson.put("electric_use_choose", ElectronicInvoiceConst.ELECTRIC_USE_CHOOSE_BW);
		//法人信息
		JSONObject hqInfo = elecInvoiceDao.getRequestInfo(tenancyId,saveJson.optInt("organ_id"));
		String sellerName = hqInfo.optString("seller_name");
		String sellerAddress = hqInfo.optString("seller_address");
		String sellerNumber = hqInfo.optString("seller_number");
		String sellerBank = hqInfo.optString("bank");

		
		if(StringUtils.isBlank(sellerName)) {
			result.put("RSCODE", 4000 );
			result.put("MSG", "销售方名称为空 ");
			return false;
		}
		
		if(StringUtils.isBlank(sellerAddress)) {
			result.put("RSCODE", 4000 );
			result.put("MSG", "销售方地址为空 ");
			return false;
		}
		if(StringUtils.isBlank(sellerNumber)) {
			result.put("RSCODE", 4000 );
			result.put("MSG", "销售方电话为空 ");
			return false;
		}
		saveJson.put("seller_name", sellerName);
		saveJson.put("seller_address", sellerAddress);
		saveJson.put("seller_phone", sellerNumber);
		saveJson.put("seller_bank_no", sellerBank);

		String gmfmc = param.optString("head");
		if(StringUtils.isBlank(gmfmc)) {
			result.put("RSCODE", 4000 );
			result.put("MSG", "购买方名称为空 ");
			return false;
		}
		saveJson.put("buyer_name", gmfmc);
		String gfdz = param.optString("gmf_dzdh");
//		String gfdh = param.optString("gfdh");
		String gfyx = param.optString("email");
		String gfzh = param.optString("gmf_yhzh");
		String grdh = param.optString("phone");
		String gmfnsrsbh = param.optString("gmf_nsrsbh");
		
		//购买方地址、 购买方电话、购买方 电子邮箱  、购买方银行号、购买方手机号
		saveJson.put("buyer_address", gfdz);
//		saveJson.put("buyer_phone", gfdh);
		saveJson.put("buyer_mail", gfyx);
		saveJson.put("bank_no", gfzh);
		saveJson.put("buyer_mobile_phone", grdh);
		saveJson.put("buyer_tax", gmfnsrsbh);
		
		saveJson.put("drawer", "系统");
		
		Double fpje = param.optDouble("amt");
		Double sl = saveJson.optDouble("tax_rate");
		
		if(fpje==null || fpje.isNaN()) {
			result.put("RSCODE", 4000 );
			result.put("MSG", "发票金额非法 ");
			return false;
		}
		if(sl==null || sl.isNaN()) {
			result.put("RSCODE", 4000 );
			result.put("MSG", "税率为空 ");
			return false;
		}
		//合计税额+金额
		BigDecimal fpjeBig = new BigDecimal(fpje);
		fpjeBig = fpjeBig.setScale(2,BigDecimal.ROUND_HALF_UP);
		BigDecimal slBig = new BigDecimal(sl.toString());
		BigDecimal add = slBig.add(new BigDecimal(1));
		//合计金额
		BigDecimal hjjeBig = fpjeBig.divide(add,2,BigDecimal.ROUND_HALF_UP);
		//合计税额
		BigDecimal hjseBig = fpjeBig.subtract(hjjeBig);
		hjseBig = hjseBig.setScale(2,BigDecimal.ROUND_HALF_UP);
		
		saveJson.put("total_tax_amount", fpjeBig.toString());
		saveJson.put("total_amount", hjjeBig.toString());
		saveJson.put("tax_amount", hjseBig.toString());
		saveJson.put("invoice_amount", fpjeBig.toString());
		
		String WX_ORDER_ID  =  param.optString("WX_ORDER_ID");
		String WX_APP_ID  =  param.optString("WX_APP_ID");
		String ZFB_UID  =  param.optString("ZFB_UID");
		saveJson.put("wx_order_id", WX_ORDER_ID);
		saveJson.put("wx_app_id", WX_APP_ID);
		saveJson.put("zfb_uid", ZFB_UID);
		
		this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", saveJson);
		
		JSONObject details = this.elecInvoiceDao.queryDetailsByFlowNumber(tenancyId, saveJson.optInt("id"));
		if(details==null) {
			details = new JSONObject();
		}
		details.put("electronic_id", saveJson.optString("id"));
		details.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_LP);
		details.put("name", ElectronicInvoiceConst.BW_ELECTRON_ICINVOICE_XMMC);
		details.put("number", 1);
		
		details.put("price", fpjeBig.toString());
		details.put("amount", fpjeBig.toString());
		details.put("tax_rate", sl);
		details.put("tax_amount", hjseBig.toString());
		if(StringUtils.isBlank(details.optString("id"))) {
			this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_details", details);
		} else {
			this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_details", details);
		}
		
		return true;
	}
}
