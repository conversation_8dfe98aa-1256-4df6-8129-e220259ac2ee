package com.tzx.report.po.boh.impl;

import javax.annotation.Resource;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.po.boh.dao.AuxiliaryNumberReportDao;

import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

@Repository(AuxiliaryNumberReportDao.NAME)
public class AuxiliaryNumberReportDaoImpl implements AuxiliaryNumberReportDao{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;

	@Resource
	ConditionUtils conditionUtils;

	@Override
	public JSONObject getAuxiliaryNumberReport(String tenancyId, JSONObject json) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		String reportSql = "";

		//菜品辅助数量查询
		reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId, json, EngineConstantArea.AUXILLIARY_NUMBER_FIND1);
		if(json.containsKey("derivedtype") && json.optInt("derivedtype")==1){
			total = this.dao.countSql(tenancyId,reportSql.toString());
			list = this.dao.query4Json(tenancyId,this.dao.buildPageSql(json,reportSql.toString()));
		}

		int pagenum = json.containsKey("page") ? (json.getInt("page") == 0 ? 1 : json.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}



}
