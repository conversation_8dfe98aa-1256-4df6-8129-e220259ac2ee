<?xml version="1.0" encoding="UTF-8"?>
<urls>
	<native_url name="native_url" value="http://localhost:9090/tzxsaas/invoice/elec/getElectricData?para="/>

	<!--访问百旺的url-->
	<total_url name="total_url" value="https://www.fapiao.com:63087/fpt-dsqz/invoice"/>
	
	<!-- 访问二维码的url -->
	<ewm_url name="ewm_url" value="https://www.fapiao.com/fpt-wechat/wxscan/wxkp.do"/>

    <!--总客户端证书，只有一个-->
    <secret_type name="total_secret">
            <!--总客户端证书的位置，相对于classpath下的-->
			<url name="url" value="\invoice\electronic\certificate\fapiao2017client.truststore"/>
            <!--总客户端证书的密码-->
			<password name="password" value="123456"/>
	</secret_type>

    <!--百旺纳税人识别号证书，一个纳税人识别号对应的是一个-->
    <!-- <secret_type name="nsrsbh_secret">
        name是纳税人识别号
        <secret_nsrsbh name="110109500321655">
            百旺纳税人识别号证书位置，相对于classpath下的
            <url name="url" value="\invoice\electronic\certificate\testISSUE.pfx"/>
            证书密码
            <password name="password" value="123456"/>
            微信扫描开票接口秘钥
            <ewmpassword name="ewmpassword" value="feNAMWpm"/>
            微信扫描开票接口第三方id
            <thirdpageid name="thirdpageid" value="1gFd"/>
        </secret_nsrsbh>
	</secret_type> -->

    <!--企业APP_ID-->
    <app_id name="app_id" value="6a196e70d50478d531d0d9434a406a263462789e5745dca45c5c24fb3bed6bc2"/>
    <!--AES加密密钥-->
    <app_id name="content_password" value="408BE1C49A8ABE02"/>
</urls>