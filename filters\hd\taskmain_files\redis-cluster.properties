#Redis pool config
redis.pool.maxTotal=70
redis.pool.maxIdle=10
redis.pool.maxWaitMillis=1000
redis.pool.testOnBorrow=false

#Redis Cluster config
redis.cluster.maxIdle=5
redis.cluster.maxTotal=20

#Redis util config
redis.useCache=true
redis.defaultExpireTime=1800

#Spring-redis-data config
redis.cluster.maxRedirects=3

#Redis Cluster config
redis.host1=redis1.db.tzx.com.cn
redis.port1=7000
redis.host2=redis1.db.tzx.com.cn
redis.port2=7001
redis.host3=redis1.db.tzx.com.cn
redis.port3=7002
redis.host4=redis2.db.tzx.com.cn
redis.port4=7003
redis.host5=redis2.db.tzx.com.cn
redis.port5=7004
redis.host6=redis2.db.tzx.com.cn
redis.port6=7005


