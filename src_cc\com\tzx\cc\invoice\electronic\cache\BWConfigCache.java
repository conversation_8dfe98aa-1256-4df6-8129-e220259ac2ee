package com.tzx.cc.invoice.electronic.cache;

import com.tzx.framework.common.util.SpringConext;
import com.tzx.payment.news.util.DomUtils;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.dom4j.DocumentException;
import org.springframework.core.io.Resource;

import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * 百旺配置缓存
 * <AUTHOR>
 *
 */
public class BWConfigCache {
	private static final Logger	logger	= Logger.getLogger(BWConfigCache.class);
	
	private static JSONObject cache;
	
	static {
		try {
			Resource resource = SpringConext.getApplicationContext().getResource("classpath:/invoice/electronic/certificate/baiwang_config.xml");
			File file = resource.getFile();
//			String url = "E:\\ideaworkspace\\tzxsaas20170406\\resource\\invoice\\electronic\\certificate\\baiwang_config.xml";
//			File file = new File(url);
			cache = DomUtils.load(file);
		} catch (DocumentException e) {
			logger.error(e);
			e.printStackTrace();
		} catch (Exception e) {
			logger.error(e);
			e.printStackTrace();
		}
	}

	/**
	 * 获取百旺配置
	 * @param keys
	 * @return
	 */
	public static String getElementText(String ... keys){
		JSONObject json = cache;
		for(int i=0;i<keys.length;i++){
			if(i==keys.length-1) {
				return json.optString(keys[i]);
			} else {
				json = (JSONObject) json.optJSONObject(keys[i]);
			}
		}
		return null;
	}
}
