package com.tzx.cc.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.google.gson.reflect.TypeToken;
import com.tzx.cc.bo.PlaceOrderManagementService;
import com.tzx.cc.common.constant.util.CcPartitionUtils;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.MessageUtils;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;

@Service(PlaceOrderManagementService.NAME)
public class PlaceOrderManagementImpl implements PlaceOrderManagementService
{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	private static final Logger				logger	= Logger.getLogger(PlaceOrderManagementImpl.class);
	@Override
	public JSONObject loadCustomerAddress(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		if (condition.containsKey("order_phone") && !"".equals(condition.optString("order_phone")))
		{
			sql.append("select c.id,e.reg_code as area_reg_code,d.reg_code,c.id as district_id,c.coordinate,c.store_id,a.consignee as consigner, a.consignee_phone as consigner_phone, a.*,k.mobil from (SELECT a.customer_id,a.province,a.city,a.area,a.consignee,a.sex,a.address,a.consignee_phone,a.district_id,a.longitude,a.latitude,a.baidu_location from crm_customer_address a inner join crm_customer_info f on a.customer_id=f.id where f.mobil like '"
					+ condition.optString("order_phone")
					+ "%'  group by a.customer_id,a.province,a.city,a.area,a.consignee,a.sex,a.address,a.consignee_phone,a.district_id,a.longitude,a.latitude,a.baidu_location)a  left join crm_district c on c.id = a.district_id and c.valid_state='1' left join sys_regionalism d on d.reg_code=a.city left join sys_regionalism e on e.reg_code=a.area LEFT JOIN crm_customer_info k ON k.id = A.customer_id");
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			// List<JSONObject> list = this.dao.query4Json(tenancyID,
			// this.dao.buildPageSql(condition, sql.toString()));
			List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
			// result.put("page", pagenum);
			// result.put("total", total);
			result.put("rows", list);
		}
		else
		{
			result.put("page", "1");
			result.put("total", "0");
			result.put("rows", "[]");
		}

		return result;
	}

	@Override
	public JSONObject findFoodCategory(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT e.id,e.itemclass_code,e.itemclass_name from hq_item_class e where e.id in(select DISTINCT(d.class) from hq_item_menu_class d where d.chanel='CC04' and d.details_id in(  SELECT c.id from hq_item_menu_details c where c.item_menu_id in( SELECT b.id from hq_item_menu  b where b.valid_state='1' and b.id in (SELECT a.item_menu_id from hq_item_menu_organ a where a.store_id="
				+ condition.optInt("store_id") + ") and b.valid_state='1')))");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		sql.delete(0, sql.length());

		sql.append("select s.id,s.system_name,s.model_name,s.para_type,s.para_name,(case when ss.para_value is null then s.para_value else ss.para_value end) as para_value,s.para_defaut,s.values_name,s.valid_state,s.para_code from sys_parameter s");
		sql.append(" left join sys_parameter_detail ss on s.para_code=ss.para_code and s.para_value=ss.para_value_code");
		sql.append(" where s.para_code='CMJEWS'");
		List<JSONObject> list1 = this.dao.query4Json(tenancyID, sql.toString());
		Iterator<JSONObject> it2 = list.iterator();
		sql.delete(0, sql.length());

		sql.append("select s.id,s.system_name,s.model_name,s.para_type,s.para_name,(case when ss.para_value is null then s.para_value else ss.para_value end) as para_value,s.para_defaut,s.values_name,s.valid_state,s.para_code from sys_parameter s");
		sql.append(" left join sys_parameter_detail ss on s.para_code=ss.para_code and s.para_value=ss.para_value_code");
		sql.append(" where s.para_code='ZDJEWS'");
		List<JSONObject> list2 = this.dao.query4Json(tenancyID, sql.toString());

		while (it2.hasNext())
		{
			JSONObject lish = it2.next();
			for (JSONObject jsonObject : list1)
			{
				lish.put("para_cm", jsonObject.optInt("para_value"));
			}
			for (JSONObject jsonObject : list2)
			{
				lish.put("para_zd", jsonObject.optInt("para_value"));
			}

		}

		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject foodDetailList(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT DISTINCT(bb.id),bb.*,aa.price as standard_price,aa.chanel,zz.item_menu_id from ( SELECT K .*, l.item_name,l.is_combo,x.makeup_way,x.proportion_money,x.is_default as cpzf_is_default,x.method_name_id,y.class_item as method_name FROM hq_item_unit K LEFT JOIN  hq_item_info l on l. ID = K.item_id LEFT JOIN hq_item_method x on x.item_id=k.item_id and x.valid_state='1' and x.is_default='Y' LEFT JOIN sys_dictionary y on y.id=x.method_name_id WHERE 1=1 and k.valid_state='1' and  k.item_id in (select g.item_id from hq_item_menu_details g where g.id in (SELECT f.details_id from (select d.* from hq_item_menu_class d where d.chanel='CC04' and d.details_id in(  SELECT c.id from hq_item_menu_details c where c.item_menu_id in( SELECT b.id from hq_item_menu  b where b.valid_state='1' and b.id in (SELECT a.item_menu_id from hq_item_menu_organ a where a.store_id="
				+ condition.optString("store_id")
				+ "))))as f where f.class="
				+ condition.optString("class_id")
				+ ")))bb   LEFT JOIN hq_item_pricesystem aa ON aa.item_unit_id = bb.id and aa.chanel='CC04' and CAST(aa.price_system as VARCHAR)   in ( SELECT dd.price_system from organ dd where dd.id='"
				+ condition.optString("store_id")
				+ "') LEFT JOIN hq_item_menu_details zz on zz.item_id=bb.item_id and zz.item_menu_id in (	SELECT A .item_menu_id FROM hq_item_menu_organ A LEFT JOIN hq_item_menu yy on yy.id= a.item_menu_id WHERE A .store_id = '"
				+ condition.optString("store_id")
				+ "' and yy.valid_state = '1') order by bb.item_name");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		for (JSONObject food_detail_obj : list)
		{
			sql.delete(0, sql.length());
			sql.append("SELECT k.*,a.item_name,b.unit_name from  hq_item_combo_details k LEFT JOIN hq_item_info a on k.details_id=a.id left join hq_item_unit b on b.id=k.item_unit_id where k.iitem_id=" + food_detail_obj.optInt("item_id") + "");
			List<JSONObject> package_detail_list = this.dao.query4Json(tenancyID, sql.toString());
			if (package_detail_list.size() > 0)
			{
				for (JSONObject package_detail_obj : package_detail_list)
				{
					if (package_detail_obj.optString("is_itemgroup").equalsIgnoreCase("y"))
					{
						sql.delete(0, sql.length());
						sql.append("select d.item_name,a.*,b.standard_price,c.item_group_name from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id left join hq_item_group c on c.id=a.item_group_id left join hq_item_info d on d.id=a.item_id  where a.item_group_id="
								+ package_detail_obj.optInt("details_id") + "");
						List<JSONObject> item_group_detail_list = this.dao.query4Json(tenancyID, sql.toString());
						if (item_group_detail_list.size() > 0)
						{
							package_detail_obj.put("item_group_detail_list", item_group_detail_list);
						}
					}
				}
				food_detail_obj.put("package_detail_list", package_detail_list);
			}

		}
		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject loadServiceFeeByStoreId(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * from cc_meals_info a where a.valid_state='1' and a.channel='" + condition.optString("channel") + "'  and a.store_id='" + condition.optString("store_id") + "' ");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject loadRegionInfo(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * FROM sys_regionalism a where a.valid_state ='Y'");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject loadDistrictInfo(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * FROM crm_district a where a.valid_state ='1'");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject loadDistrictByCity(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		if (condition.containsKey("reg_code"))
		{
			sql.append("SELECT  DISTINCT(a.id),d.room_times,a.*,b.org_full_name,c.reg_code as city_area_id, c.father_id as city_id from crm_district a left join organ b on b.id=a.store_id LEFT JOIN sys_regionalism c on c.reg_code = b.regionalism left join hq_organ d on d.organ_id=a.store_id where a.valid_state='1' and c.reg_code like '%"
					+ condition.optString("reg_code").trim() + "%'");
		}
		else if (condition.containsKey("district_id") && !"".equals(condition.optString("district_id")))
		{
			sql.append("SELECT  DISTINCT(a.id),d.room_times,a.*,b.org_full_name,c.reg_code as city_area_id, c.father_id as city_id from crm_district a left join organ b on b.id=a.store_id LEFT JOIN sys_regionalism c on c.reg_code = b.regionalism left join hq_organ d on d.organ_id=a.store_id where a.valid_state='1' and A .id = "
					+ condition.optString("district_id").trim() + "");
		}
		if (sql.length() > 0)
		{
			List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
			result.put("rows", list);
		}
		else
		{
			result.put("rows", "[]");
		}

		return result;
	}

	@Override
	public JSONObject loadStoreByDistrict(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT b.store_id,a.org_full_name from organ a LEFT JOIN  crm_district b  on b.store_id = a .id  where b.id=" + condition.optInt("district_id") + "");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject loadMealsInfo(String tenancyId, JSONObject param) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * from cc_meals_info b where  b.store_id= " + param.optInt("store_id") + " AND b.valid_state='1'");
		List<JSONObject> list = this.dao.query4Json(tenancyId, sql.toString());
		result.put("rows", list);
		return result;
	}

	@Override
	public Boolean saveOrderConfirm(String tenancyID, JSONObject obj) throws Exception, SystemException
	{
		Boolean flag = true;
		hqOrderSave(tenancyID, obj);
		flag = hqOrderIssued(tenancyID, obj);
		return flag;
	}

	@Override
	public JSONObject loadOrderListNew(String tenancyId, JSONObject param) throws Exception
	{
		JSONObject result = new JSONObject();
		String organ_uuid = "";
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT b.org_uuid,a.tenancy_id,a.id,a.store_id,a.customer_id,a.order_type,a.order_code,a.chanel,a.taste_like,a.province,a.city,a.area,a.district_id,a.address,a.total_money,a.actual_pay,a.meal_costs,a.remark,a.order_state,a.send_time,a.single_time,a.last_operator,a.last_updatetime,a.order_name,a.order_phone,a.table_code,a.table_name,a.entry_name from cc_order_list a left join organ b on b.id = a.store_id where a.order_code= '"
				+ param.optString("order_code") + "'");
		
		String tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"a",sql.toString(),param.optString("order_code") , CcPartitionUtils.TYPE_ORDERCODE_TZX);
		
		List<JSONObject> list = this.dao.query4Json(tenancyId, tmpSQL);
		if (list.size() >= 1)
		{
			for (JSONObject result1 : list)
			{
				organ_uuid = result1.getString("org_uuid");
			}
		}
		result.put("order_rows", list);
		result.put("organ_uuid", organ_uuid);
		return result;
	}

	@Override
	public JSONObject loadOorderItem(String tenancyId, JSONObject param) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT b.*from  cc_order_item b   where  b.order_code= '" + param.optString("order_code") + "'");
		
		String tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(),param.optString("order_code") , CcPartitionUtils.TYPE_ORDERCODE_TZX);
		
		List<JSONObject> list = this.dao.query4Json(tenancyId, tmpSQL);
		result.put("order_item_rows", list);
		result.put("tablename", "cc_order_item");
		return result;
	}

	@Override
	public JSONObject loadOrderItemDetails(String tenancyId, JSONObject param) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT b.* from cc_order_item_details b   where  b.order_code= '" + param.optString("order_code") + "'");
		
		String tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(),param.optString("order_code") , CcPartitionUtils.TYPE_ORDERCODE_TZX);
		
		List<JSONObject> list = this.dao.query4Json(tenancyId, tmpSQL);
		result.put("order_item_details_rows", list);
		result.put("tablename", "cc_order_item_details");
		return result;
	}

	@Override
	public JSONObject loadOrderRepayment(String tenancyId, JSONObject param) throws Exception
	{
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean hqOrderIssued(String tenancyId, JSONObject param) throws Exception
	{
		boolean flag = true;
		String order_code = "";
		List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT a.store_id,b.org_uuid from cc_order_list a left join organ b on b.id = a.store_id where a.order_code='" + param.optString("order_code") + "'");
		
		String tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"a",sql.toString(),param.optString("order_code") , CcPartitionUtils.TYPE_ORDERCODE_TZX);
		
		List<JSONObject> storeList = this.dao.query4Json(tenancyId, tmpSQL);
		sql.delete(0, sql.length());
		sql.append("SELECT a.*  from cc_order_list a left join organ b on b.id = a.store_id where a.order_code ='" + param.optString("order_code") + "'");
		
		tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"a",sql.toString(),param.optString("order_code") , CcPartitionUtils.TYPE_ORDERCODE_TZX);
		
		List<JSONObject> list = this.dao.query4Json(tenancyId, tmpSQL);
		for (JSONObject storeObj : storeList)
		{
			if (list.size() >= 1)
			{
				for (JSONObject order_list : list)
				{
					if (storeObj.optInt("store_id") == order_list.optInt("store_id"))
					{
						order_code = order_list.optString("order_code");
						JSONObject result = new JSONObject();
						result.put("order_list", order_list);
						// 获取订单菜品
						sql.delete(0, sql.length());
						sql.append("SELECT b.* from  cc_order_item b   where  b.order_code= '" + order_list.optString("order_code") + "'");
						
						tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(),order_list.optString("order_code") , CcPartitionUtils.TYPE_ORDERCODE_TZX);
						
						List<JSONObject> order_item_list = this.dao.query4Json(tenancyId, tmpSQL);
						result.put("order_item", order_item_list);
						// 获取订单里套餐明细
						sql.delete(0, sql.length());
						sql.append("SELECT b.* from cc_order_item_details b   where  b.order_code= '" + order_list.optString("order_code") + "'");
						
						tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(),order_list.optString("order_code") , CcPartitionUtils.TYPE_ORDERCODE_TZX);
						
						List<JSONObject> order_item_details_list = this.dao.query4Json(tenancyId, tmpSQL);
						result.put("order_item_details", order_item_details_list);
						// 获取订单付款方式
						sql.delete(0, sql.length());
						sql.append("SELECT b.* from cc_order_repayment b   where  b.order_code= '" + order_list.optString("order_code") + "'");
						
						tmpSQL=CcPartitionUtils.makeSQL(tenancyId,false,"b",sql.toString(),order_list.optString("order_code") , CcPartitionUtils.TYPE_ORDERCODE_TZX);
						
						List<JSONObject> cc_order_repayment_list = this.dao.query4Json(tenancyId, tmpSQL);
						result.put("order_repayment", cc_order_repayment_list);
						// 获取菜品口味
						sql.delete(0, sql.length());
						sql.append("SELECT b.* from cc_order_item_taste b   where  b.order_code= '" + order_list.optString("order_code") + "'");
						List<JSONObject> cc_order_item_taste_list = this.dao.query4Json(tenancyId, sql.toString());
						result.put("order_item_taste", cc_order_item_taste_list);

						jsonObjectList.add(result);
					}
					;

				}
			}
			MessageUtils mu = new MessageUtils();
			if (jsonObjectList.size() > 0)
			{
				try
				{
					Data d = Data.get();
					d.setType(Type.ORDER);
					d.setOper(Oper.add);
					d.setTenancy_id(tenancyId);
					d.setData(jsonObjectList);
					d.setStore_id(storeObj.optInt("store_id"));
					logger.info("总部下发给门店的订单信息6:"+JSONObject.fromObject(d).toString());
					mu.sendMessage(JSONObject.fromObject(d).toString(), storeObj.optString("org_uuid"));
				}
				catch (Exception e)
				{
					e.printStackTrace();
					String update_sql = "update cc_order_list set order_state='03' where order_code='" + order_code + "'";
					
					tmpSQL=CcPartitionUtils.makeSQL(tenancyId,update_sql,order_code, CcPartitionUtils.TYPE_ORDERCODE_TZX);
					
					this.dao.execute(tenancyId, tmpSQL);
					flag = false;
				}

			}
		}

		return flag;
	}

	@Override
	public String loadCustomerByPhone(String tenancyID, JSONObject condition) throws Exception
	{
		List<JSONObject> list = new ArrayList<>();
		StringBuilder sql = new StringBuilder();
		if (condition.containsKey("order_phone") && !"".equals(condition.optString("order_phone")))
		{
			sql.append("select a.* from crm_customer_info a  where a.mobil = '" + condition.optString("order_phone") + "'");
			
			list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
			
		}

		return list.isEmpty()?null:list.get(0).optString("id");
	}
	

	@Override
	public JSONObject loadItemTaste(String tenancyId, JSONObject param) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT a.* from item_taste a left join item_taste_org b on b.teste_id=a.id where a.valid_state ='1' and b.store_id= " + param.optInt("store_id") + " ");
		List<JSONObject> list = this.dao.query4Json(tenancyId, sql.toString());
		result.put("rows", list);
		return result;
	}

	@Override
	public boolean checkIsStopOrder(String tenentId, JSONObject param) throws Exception
	{
		try
		{

			String store_id = param.optString("store_id");
			if (Tools.hv(store_id) && Tools.hv(store_id))
			{
				StringBuilder sql = new StringBuilder();
				sql.append("select  a.* from hq_organ a where a.cc_bill_state !='0' and a.organ_id = " + param.optInt("store_id") + "");
				long total = this.dao.countSql(tenentId, sql.toString());
				List<JSONObject> list = this.dao.query4Json(tenentId, sql.toString());
				if (list.size() >= 1)
				{
					return false;
				}
				else
				{
					return true;
				}
			}

			return false;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			return false;
		}
	}

	@Override
	public boolean checkIsOrderTimeRange(String tenentId, JSONObject param) throws Exception
	{
		boolean flag = false;
		String store_id = param.optString("store_id");
		if (Tools.hv(store_id) && Tools.hv(store_id))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select  a.* from hq_lineup_time_org a where  a.store_id = " + param.optInt("store_id") + "");
			List<JSONObject> list = this.dao.query4Json(tenentId, sql.toString());
			Date currDate = DateUtil.parseDateHHMM(DateUtil.getNowDateHHMM());
			if (list.size() > 0)
			{
				for (JSONObject obj : list)
				{
					if (!"".equals(obj.optString("lineup_starttime")) && !"".equals(obj.optString("lineup_endtime")))
					{
						Date lineup_starttime = DateUtil.parseDateHHMM(obj.optString("lineup_starttime"));
						Date lineup_endtime = DateUtil.parseDateHHMM(obj.optString("lineup_endtime"));
						if (currDate.compareTo(lineup_starttime) == 1 && currDate.compareTo(lineup_endtime) == -1)
						{
							flag = true;
						}
					}
				}
			}
			else
			{
				flag = true;
			}

		}

		return flag;
	}

	@Override
	public JSONObject loadItemMethod(String tenancyId, JSONObject param) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT a.*,b.class_item from hq_item_method  a  left join sys_dictionary b on b.id= a.method_name_id where a.valid_state='1' and a.item_id= " + param.optInt("item_id") + " ");
		List<JSONObject> list = this.dao.query4Json(tenancyId, sql.toString());
		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject getSendTimeData(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		if (condition.containsKey("store_id") && !"".equals(condition.optString("store_id")))
		{
			sql.append("SELECT a.id,a.room_times from hq_organ a where a.organ_id = " + condition.optString("store_id") + "");
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
			List<JSONObject> send_time_list = new ArrayList<JSONObject>();
			int send_time = 0;
			int send_time_millisecond = 0;
			if (list.size() > 0)
			{
				for (JSONObject obj : list)
				{
					send_time = obj.optInt("room_times");
				}
			}
			JSONObject send_time_obj = new JSONObject();
			send_time_obj.put("id", "立即配送");
			send_time_obj.put("text", "立即配送");
			send_time_list.add(send_time_obj);
			int j = 15;
			Date date = new Date();
			send_time_millisecond = send_time * 60 * 1000;
			String pre_date_hh_mm = "00:00";
			for (int i = 0; i < j; i++)
			{
				JSONObject send_time_obj_two = new JSONObject();
				long current_date_millisecond = date.getTime() + send_time_millisecond;
				long pre_date_millisecond = date.getTime() - send_time_millisecond;
				Date next_date = new Date(current_date_millisecond);
				Date pre_date = new Date(pre_date_millisecond);
				pre_date_hh_mm = DateUtil.getDateHHMM(pre_date);
				date = next_date;
				String next_date_hh_mm = DateUtil.getDateHHMM(next_date);
				Date end_time = DateUtil.parseDateHHMM("23:59");
				Date next_date_parse_datehh_mm = DateUtil.parseDateHHMM(next_date_hh_mm);
				Date pre_date_parse_datehh_mm = DateUtil.parseDateHHMM(pre_date_hh_mm);
				if ((next_date_parse_datehh_mm.compareTo(end_time) == -1) && (pre_date_parse_datehh_mm.compareTo(next_date_parse_datehh_mm) == -1))
				{
					send_time_obj_two.put("id", next_date_hh_mm);
					send_time_obj_two.put("text", next_date_hh_mm);
					send_time_list.add(send_time_obj_two);
				}
				else
				{
					i = 15;
				}
			}
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", send_time_list);
		}
		else
		{
			result.put("page", "1");
			result.put("total", "0");
			result.put("rows", "[]");
		}

		return result;
	}

	@Override
	public JSONObject loadMealsInfoDefaultById(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT A .*, a.price as standard_price,b.item_name,d.unit_name, b.item_name || '(' || d.unit_name || ')' AS service_name FROM cc_meals_info_default A LEFT JOIN hq_item_info b ON b. ID = A .item_id LEFT JOIN hq_item_unit d on d.id= a.unit_id where  a.meals_id="
				+ condition.optString("meals_id") + " ");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		result.put("rows", list);
		return result;
	}

	@Override
	public boolean checkIsOpen(String tenentId, JSONObject param) throws Exception
	{
		boolean flag = false;
		String store_id = param.optString("store_id");
		if (Tools.hv(store_id) && Tools.hv(store_id))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select  a.* from sys_store_online_state a where  a.store_id = " + param.optInt("store_id") + "");
			List<JSONObject> list = this.dao.query4Json(tenentId, sql.toString());
			if (list.size() > 0)
			{
				flag = true;
			}
			else
			{
				flag = false;
			}

		}

		return flag;
	}

	@Override
	public boolean checkIsStop(String tenentId, JSONObject param) throws Exception
	{
		boolean flag = false;
		String store_id = param.optString("store_id");
		if (Tools.hv(store_id) && Tools.hv(store_id))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT  * from organ a where a.valid_state='1' and  a.id = " + param.optInt("store_id") + "");
			List<JSONObject> list = this.dao.query4Json(tenentId, sql.toString());
			if (list.size() > 0)
			{
				flag = true;
			}
			else
			{
				flag = false;
			}

		}

		return flag;
	}

	@Override
	public boolean checkIsHaveAddress(String tenentId, JSONObject param) throws Exception
	{
		boolean flag = false;
		String store_id = param.optString("store_id");
		if (Tools.hv(store_id) && Tools.hv(store_id))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select  a.* from crm_customer_address a where  a.consignee = '" + param.optString("consignee") + "' and a.sex = '" + param.optString("sex") + "' and a.consignee_phone = '" + param.optString("consignee_phone") + "' and a.address = '" + param.optString("address")
					+ "' and a.baidu_location = '" + param.optString("baidu_location") + "'");
			List<JSONObject> list = this.dao.query4Json(tenentId, sql.toString());
			if (list.size() > 0)
			{
				flag = true;
			}
			else
			{
				flag = false;
			}

		}

		return flag;
	}

	@Override
	public boolean checkTenantState(String tenentId, JSONObject param) throws Exception
	{
		boolean flag = false;
		String store_id = param.optString("store_id");
		if (Tools.hv(store_id) && Tools.hv(store_id))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select * from organ a where a.tenant_state ='1' and  a.id = " + param.optInt("store_id") + "");
			List<JSONObject> list = this.dao.query4Json(tenentId, sql.toString());
			if (list.size() > 0)
			{
				flag = true;
			}
			else
			{
				flag = false;
			}

		}

		return flag;
	}

	@Override
	public boolean checkDeviceAuditState(String tenentId, JSONObject param) throws Exception
	{
		boolean flag = false;
		String store_id = param.optString("store_id");
		if (Tools.hv(store_id) && Tools.hv(store_id))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT * from hq_devices  a where  a.valid_state='1' and a.audit_state='1' and a.store_id = " + param.optInt("store_id") + "");
			List<JSONObject> list = this.dao.query4Json(tenentId, sql.toString());
			if (list.size() > 0)
			{
				flag = true;
			}
			else
			{
				flag = false;
			}

		}

		return flag;
	}

	@Override
	public boolean checkStoreState(String tenentId, JSONObject param) throws Exception
	{
		long current = System.currentTimeMillis();
		boolean flag = false;
		long storeStatusPeriod = Scm.ti(Constant.getSystemMap().get("storestatus_minite_period")) * 60 * 1000;
		String store_id = param.optString("store_id");
		if (Tools.hv(store_id) && Tools.hv(store_id))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT A .*, b.org_full_name,b.organ_code,CASE WHEN a . ID IS NULL THEN '' WHEN to_timestamp(a .refresh_time,'yyyy-MM-dd HH24:MI:ss') < '" + DateUtil.format(new Timestamp(current - 5 * storeStatusPeriod))
					+ "' THEN '01' WHEN to_timestamp(a .refresh_time,'yyyy-MM-dd HH24:MI:ss') < '" + DateUtil.format(new Timestamp(current - 3 * storeStatusPeriod))
					+ "' THEN '02' ELSE '03'	END AS store_state FROM sys_store_online_state a LEFT JOIN organ b ON a .store_id = b. ID where a.store_id = " + param.optInt("store_id") + "");
			List<JSONObject> list = this.dao.query4Json(tenentId, sql.toString());
			if (list.size() > 0)
			{
				JSONObject obj = list.get(0);
				if (obj.optString("store_state").equals("03"))
				{
					flag = true;
				}
			}
			else
			{
				flag = false;
			}

		}

		return flag;
	}

	@Override
	public boolean checkIsHaveDistrictAddress(String tenentId, JSONObject param) throws Exception
	{
		boolean flag = false;
		String store_id = param.optString("store_id");
		if (Tools.hv(store_id) && Tools.hv(store_id))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select  a.* from cc_super_district a where  a.store_id = '" + param.optString("store_id") + "' and a.address = '" + param.optString("address") + "' ");
			List<JSONObject> list = this.dao.query4Json(tenentId, sql.toString());
			if (list.size() > 0)
			{
				flag = true;
			}
			else
			{
				flag = false;
			}

		}

		return flag;
	}

	@Override
	public void hqOrderSave(String tenancyID, JSONObject obj) throws Exception
	{
		CcPartitionUtils.lackInsertParam(tenancyID,obj);
		
		Double total_money = 0.0;
		if (obj.containsKey("dishes") && obj.get("dishes") != "")
		{
			@SuppressWarnings("unchecked")
			List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(obj.get("dishes").toString(), new TypeToken<List<JSONObject>>()
			{
			}.getType());
			Iterator<JSONObject> it2 = list.iterator();
			List<JSONObject> dishesList = new ArrayList<JSONObject>();
			int i = 0;
			int k = 0;
			while (it2.hasNext())
			{
				i++;
				JSONObject dishe = it2.next();
				double product_fee = 0.0;
				dishe.put("tenancy_id", obj.get("tenancy_id"));
				dishe.put("order_code", obj.optString("order_code"));
				Double jia_price = dishe.optDouble("price");
				dishe.put("price", dishe.optDouble("standard_price"));
				// dishe.put("product_fee", dishe.optDouble("standard_price") *
				// dishe.optDouble("number"));
				// 改为菜品数量*（菜品单价+加价金额）菜品加价后的小计
				dishe.put("product_fee", jia_price * dishe.optDouble("number"));
				dishe.put("real_amount", obj.optString("product_fee"));
				if (dishe.optDouble("product_fee") > product_fee)
				{
					product_fee = dishe.optDouble("product_fee");
					k = i;
				}
				// 总的加价金额
				dishe.put("method_money", (jia_price - dishe.optDouble("standard_price")) * dishe.optInt("number"));
				dishe.put("group_index", i);
				List<JSONObject> order_item_taste_list = new ArrayList<JSONObject>();
				if (!dishe.optString("taste_id").isEmpty() && !"null".equals(dishe.optString("taste_id")) && dishe.optString("is_combo").equals("N"))
				{
					String taste_id_arr[] = dishe.optString("taste_id").split(",");
					String item_taste_arr[] = dishe.optString("item_taste").split(",");
					String zf_kw_type_arr[] = dishe.optString("type").split(",");
					if (taste_id_arr.length > 0 && item_taste_arr.length > 0 && zf_kw_type_arr.length > 0 && !"".equals(item_taste_arr[0].toString()))
					{
						for (int j = 0; j < taste_id_arr.length; j++)
						{
							if (!"undefined".equalsIgnoreCase(taste_id_arr[j]))
							{
								JSONObject order_item_taste_obj = new JSONObject();
								order_item_taste_obj.put("tenancy_id", tenancyID);
								order_item_taste_obj.put("order_code", obj.optString("order_code"));
								order_item_taste_obj.put("taste_method_id", taste_id_arr[j]);
								order_item_taste_obj.put("item_remark", item_taste_arr[j]);
								order_item_taste_obj.put("type", zf_kw_type_arr[j]);
								if (zf_kw_type_arr[j].equalsIgnoreCase("zf01"))
								{
									double price = jia_price;
									double standard_price = dishe.optDouble("standard_price");
									int number = dishe.optInt("number");
									// order_item_taste_obj.put("proportion_money",
									// (price - standard_price) * number);
									// 改为单个做法
									order_item_taste_obj.put("proportion_money", (price - standard_price));
								}
								order_item_taste_obj.put("group_index", i);
								order_item_taste_obj.put("item_id", dishe.optString("item_id"));
								order_item_taste_list.add(order_item_taste_obj);

							}
						}
					}

				}
				if (order_item_taste_list.size() > 0)
				{
					this.dao.insertBatchIgnorCase(tenancyID, "cc_order_item_taste", order_item_taste_list);
				}
				if (dishe.optString("is_combo").equalsIgnoreCase("y"))
				{
					List<JSONObject> order_item_details_list = new ArrayList<JSONObject>();
					StringBuilder sql = new StringBuilder();
					sql.append("SELECT * from  hq_item_combo_details k where k.iitem_id=" + dishe.optInt("item_id") + "");
					List<JSONObject> item_combo_details_list = this.dao.query4Json(tenancyID, sql.toString());
					// 套餐明细表
					if (item_combo_details_list.size() > 0)
					{
						for (JSONObject item_combo_details_obj : item_combo_details_list)
						{   // 套餐明细是项目组
							if (item_combo_details_obj.optString("is_itemgroup").equalsIgnoreCase("y"))
							{

								sql.delete(0, sql.length());
								sql.append("select a.* ,b.standard_price from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id  where a.item_group_id=" + item_combo_details_obj.optInt("details_id") + ";");
								List<JSONObject> item_group_details_list = this.dao.query4Json(tenancyID, sql.toString());
								
								if (item_group_details_list.size() > 0)
								{
									for (JSONObject item_group_details_obj : item_group_details_list)
									{
										item_group_details_obj.put("group_index", i);
										item_group_details_obj.put("order_code", obj.optString("order_code"));
										item_group_details_obj.put("unit_id", item_group_details_obj.optString("item_unit_id"));
										item_group_details_obj.put("price", item_group_details_obj.optString("standard_price"));
										item_group_details_obj.put("number", dishe.optInt("number"));
										item_group_details_obj.remove("id");
										order_item_details_list.add(item_group_details_obj);
									}

								}
							}// 套餐明细不是项目组
							else
							{
								item_combo_details_obj.remove("id");
								item_combo_details_obj.put("order_code", obj.optString("order_code"));
								item_combo_details_obj.put("unit_id", item_combo_details_obj.optString("item_unit_id"));
								item_combo_details_obj.put("price", item_combo_details_obj.optString("standardprice"));
								item_combo_details_obj.put("number", dishe.optInt("number"));
								item_combo_details_obj.put("item_id", item_combo_details_obj.optString("details_id"));
								item_combo_details_obj.put("group_index", i);
								order_item_details_list.add(item_combo_details_obj);
							}
						}
					}
					
					CcPartitionUtils.lackInsertParam(tenancyID,obj, order_item_details_list);
					
					this.dao.insertBatchIgnorCase(tenancyID, "cc_order_item_details", order_item_details_list);
				}

				dishesList.add(dishe);
				total_money += dishe.optDouble("small_total");
			}
			JSONObject product_fee_order_item = dishesList.get(k - 1);
			product_fee_order_item.put("single_discount_amount", obj.optString("maling_amount"));
			product_fee_order_item.put("real_amount", product_fee_order_item.optDouble("product_fee") - obj.optDouble("maling_amount"));
			
			CcPartitionUtils.lackInsertParam(tenancyID,obj, dishesList);
			
			this.dao.insertBatchIgnorCase(tenancyID, "cc_order_item", dishesList);
		}
		obj.put("total_money", obj.optDouble("total_money"));
		// obj.put("meal_costs", obj.optDouble("total_money")-total_money);
		// obj.put("address", obj.optString("baidu_location") +
		// obj.optString("address"));
		obj.put("address", obj.optString("city_name") + obj.optString("area_name") + obj.optString("address"));
		this.dao.insertIgnorCase(tenancyID, "cc_order_list", obj);

	}

}
