package com.tzx.report.po.commonreplace.impl;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.po.commonreplace.ExportReportDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository(ExportReportDao.NAME)
public class ExportReportDaoImpl implements ExportReportDao {

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

}
