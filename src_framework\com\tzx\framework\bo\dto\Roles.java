package com.tzx.framework.bo.dto;

import java.io.Serializable;
import java.util.List;
/**
 * 角色
 */
public class Roles implements Serializable
{
	
	private static final long	serialVersionUID	= 1L;

	private Integer				id;
	private String				roleName;
	private String				roleState;
	private String				state;
	private String				createPerson;
	private String				createDate;
	private Integer				roleLevel;
	private Integer				uplevelRoleId;
	private String  			tenancyId;
	private Integer           	target = 0;
	private String              uplevelRoleName;
	private List<Roles>			children;
	private String 				checked;
	private String				remark;

	private Integer reportDayType;
	private Integer reportDay;

	public Integer getReportDayType() {
		return reportDayType;
	}

	public void setReportDayType(Integer reportDayType) {
		this.reportDayType = reportDayType;
	}

	public Integer getReportDay() {
		return reportDay;
	}

	public void setReportDay(Integer reportDay) {
		this.reportDay = reportDay;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public String getRoleName()
	{
		return roleName;
	}

	public void setRoleName(String roleName)
	{
		this.roleName = roleName;
	}

	public String getRoleState()
	{
		return roleState;
	}

	public void setRoleState(String roleState)
	{
		this.roleState = roleState;
	}

	public String getCreatePerson()
	{
		return createPerson;
	}

	public void setCreatePerson(String createPerson)
	{
		this.createPerson = createPerson;
	}

	public String getCreateDate()
	{
		return createDate;
	}

	public void setCreateDate(String createDate)
	{
		this.createDate = createDate;
	}

	public Integer getRoleLevel()
	{
		return roleLevel;
	}

	public void setRoleLevel(Integer roleLevel)
	{
		this.roleLevel = roleLevel;
	}

	public Integer getUplevelRoleId()
	{
		return uplevelRoleId;
	}

	public void setUplevelRoleId(Integer uplevelRoleId)
	{
		this.uplevelRoleId = uplevelRoleId;
	}

	public List<Roles> getChildren()
	{
		return children;
	}

	public void setChildren(List<Roles> children)
	{
		this.children = children;
	}

	public Integer getTarget() {
		return target;
	}

	public void setTarget(Integer target) {
		this.target = target;
	}

	public String getUplevelRoleName()
	{
		return uplevelRoleName;
	}

	public void setUplevelRoleName(String uplevelRoleName)
	{
		this.uplevelRoleName = uplevelRoleName;
	}

	public String getState()
	{
		return state;
	}

	public void setState(String state)
	{
		this.state = state;
	}

	public String getTenancyId()
	{
		return tenancyId;
	}

	public void setTenancyId(String tenancyId)
	{
		this.tenancyId = tenancyId;
	}

	public String getChecked()
	{
		return checked;
	}

	public void setChecked(String checked)
	{
		this.checked = checked;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}
	
}
