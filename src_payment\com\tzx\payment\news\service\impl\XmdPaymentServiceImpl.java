package com.tzx.payment.news.service.impl;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.MessageUtils;
import com.tzx.framework.common.util.UUIDUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.news.cache.PaymentUrlCache;
import com.tzx.payment.news.cont.Contant;
import com.tzx.payment.news.cont.StatusConstant;
import com.tzx.payment.news.dao.XmdPaymentDao;
import com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl;
import com.tzx.payment.news.util.PaymentJsonUtils;
import com.tzx.payment.news.util.PaymentRedisCache;
import com.tzx.payment.news.util.PaymentUtils;
import com.tzx.payment.news.util.wechat.WechatUtils;
import com.tzx.payment.news.util.xmd.PaymentUtil;
import com.tzx.payment.news.util.xmd.XmdConstant;

import common.Logger;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 新美大支付接口实现
 */
@Service("com.tzx.payment.news.service.impl.XmdPaymentServiceImpl")
public class XmdPaymentServiceImpl extends AbstractPaymentService {
	private static Logger log = Logger.getLogger(XmdPaymentServiceImpl.class);

	@Resource(name = "com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl")
	private AlipayPaymentDaoImpl paymentDao;

	@Resource(name = XmdPaymentDao.NAME)
	private XmdPaymentDao dao;

	@Override
	public void getPrepayBarcode(Data data, Data result) throws Exception {
		JSONObject requestJson = new JSONObject();
		JSONObject param = getParam(data);
		boolean validate = validate(param, result, "pay_type", "order_no", "body", "subject", "amount", "channel");
		JSONObject all_pay = null;
		if (validate) {
			JSONObject extra = param.optJSONObject("extra");
			if(extra == null){
				extra = new JSONObject();
			}
			
			all_pay = extra.optJSONObject("all_pay");

			if(all_pay == null){
				all_pay = new JSONObject();
				all_pay.put("trade_type", "NATIVE");
				extra.put("all_pay", all_pay);
				param.put("extra", extra);
			}
			
			// 验证特殊参数,trade_type=JSAPI时，必须二选一openid或sub_openid，推荐openid
			if (("JSAPI".equals(all_pay.optString("trade_type"))
							&& !WechatUtils.keyAndValueNotBlankOrEmpty(
									all_pay, "openid"))) {
				validate = false;
			}
		}
		if (!validate) {
			fail(result, Contant.ILLEGAL_PARAM, "请求参数有误");
			return;
		}
		
		//取消同一单号的未成功的记录
		String orderNum = getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no"));
		JSONObject json= dao.getOrderByOrderNumDesc(data.getTenancy_id(), orderNum);
		if(json!=null){
			if(json.getInt("final_state")==1){//已支付成功
				result.setMsg("账单已支付成功，重复支付");
				result.setCode(Contant.ILLEGAL_PARAM);
				log.error("账单已支付成功，重复支付：" + orderNum);
				return;
			}else{ //支付未成功,需要取消
				String outTradeNo= json.getString("out_trade_no");
				JSONObject cancelJson=new JSONObject();
				cancelJson.put("outTradeNo", outTradeNo);
				cancelJson.put("merchantId", Long.parseLong(param.optString("merchant_id") ) );
				cancelJson.put("appId", Long.parseLong(Constant.getSystemMap().get("app_id")));
				cancelJson.put("random", UUIDUtil.generateGUID());
				try {
					JSONObject responseJson = orderClose(cancelJson);//取消请求
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
		}
		
		
		
		// 请求参数对象封装开始 createOutTradeNo
		String outTradeNo = PaymentUtil.createOutTradeNo(data.getTenancy_id(), data.getStore_id(),
				param.optString("order_no"));
		requestJson.put("outTradeNo", outTradeNo);
		double totalFee = PaymentUtil.yuan2fen(param.optDouble("amount", 0));
		requestJson.put("totalFee", (new Double(totalFee)).intValue());
		requestJson.put("subject", param.optString("subject"));
		requestJson.put("body", param.optString("body"));
		// 支付类型参数
		String pay_type = param.optString("pay_type");
		String channel = null;
		if (pay_type.equals("ali_pay")) {
			channel = "ali_scan_pay";
		} else if (pay_type.equals("wechat_pay")) {
			channel = "wx_scan_pay";
		} else {
			result.setMsg("非法的支付类型：" + pay_type);
			result.setCode(Contant.ILLEGAL_PARAM);
			log.error("非法的支付类型：" + pay_type);
			return;
		}
		requestJson.put("channel", channel);
		// 目前业务仅要求返回二维码
		String tradeType = all_pay.optString("trade_type");
		if(null==tradeType||tradeType.equals("")){
			tradeType="NATIVE";
		}
		param.put("trade_type", tradeType);
		requestJson.put("tradeType", tradeType);
		requestJson.put("merchantId", Long.parseLong(param.optString("merchant_id")));
		// 获取参数
		String appId = Constant.getSystemMap().get("app_id");
		requestJson.put("appId", Long.parseLong(appId));
		String openId=all_pay.optString("openid");
		requestJson.put("openId", openId);
		requestJson.put("random", UUIDUtil.generateGUID());
		// 回调地址notifyUrl
		requestJson.put("notifyUrl", PaymentUrlCache.getUrl("xmd_pay", "PAY_NOTIFY_URL"));
		log.info("sing参数为：" + Constant.getSystemMap().get("payment_xmd_key"));
		String sign = PaymentUtil.generateSign(Constant.getSystemMap().get("payment_xmd_key"), requestJson);
		requestJson.put("sign", sign);
		// 请求参数对象封装结束
		log.info("商户Id["+data.getTenancy_id()+"]机构Id["+data.getStore_id()+"]新美大支付接口调用" + XmdConstant.URL_PRECREATE + "参数:" + requestJson.toString());

		// 调用第三方接口开始
		String responseStr = null;
		try {
			String reqURL = Constant.getSystemMap().get("payment_xmd_url") + XmdConstant.URL_PRECREATE;
			log.info("商户Id["+data.getTenancy_id()+"]机构Id["+data.getStore_id()+"]新美大请求参数:" + requestJson.toString());
			responseStr = HttpUtil.sendPostRequest(reqURL, requestJson.toString());
			log.info("商户Id["+data.getTenancy_id()+"]机构Id["+data.getStore_id()+"]新美大请求返回:" + responseStr);
			//处理返回错误
			if (null == responseStr) {
				fail(result, Contant.CALL_THIRD_FAILD, "调用新美大支付接口调用" + XmdConstant.URL_PRECREATE + "失败，返回值为空.");
				return;
			}else{
				JSONObject ret = JSONObject.fromObject(responseStr);
				if(ret.optString("status","").equals("FAIL")){
					fail(result, Contant.CALL_THIRD_FAILD, "调用新美大支付接口调用" + XmdConstant.URL_PRECREATE + "失败，返回错误");
					return;
				}
			}
			//
			
			PaymentRedisCache.saveOutTradeNo2TenancyidAndTime(Contant.PAY_TYPE_XMDPAY, outTradeNo,
					result.getTenancy_id());
		} catch (Exception e) {
			log.info("请求新美大支付接口异常");
		}

		if (null == responseStr) {
			fail(result, Contant.CALL_THIRD_FAILD, "调用新美大支付接口调用" + XmdConstant.URL_PRECREATE + "失败，返回值为空");
			return;
		}
		JSONObject responseJson = JSONObject.fromObject(responseStr);
		getPrepayBarcodeReturnParam(result, responseJson);
		// 调用第三方接口结束
		persistPosPaymentOrder(data, responseJson, result);

	}

	/**
	 * 持久化pos_payment_order
	 *
	 * @throws Exception
	 */
	private void persistPosPaymentOrder(Data data, JSONObject xMDJson, Data result) throws Exception {
		JSONObject param = JSONObject.fromObject(data.getData().get(0));
		String orderNum = getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no"));
		param.put("order_num", orderNum);
		switch (data.getType()) {
		case GET_PREPAY_BARCODE:
			persistData4PrepayBarcode(param, xMDJson, result);
			break;
		default:
			break;
		}
	}

	/**
	 * @param param
	 *            入参
	 * @param xMDJson
	 *            第三方接口返回参数
	 * @param result
	 *            返回结果
	 * @throws Exception
	 */
	private void persistData4PrepayBarcode(JSONObject param, JSONObject xMDJson, Data result) throws Exception {
		JSONObject posPaymentOrder = new JSONObject();
		posPaymentOrder.put("bill_num", param.optString("order_no"));
		posPaymentOrder.put("order_num", param.optString("order_num"));
		posPaymentOrder.put("store_id", result.getStore_id());
		posPaymentOrder.put("tenancy_id", result.getTenancy_id());
		posPaymentOrder.put("total_amount", param.optDouble("amount", 0));
		posPaymentOrder.put("operator_id", param.optString("opt_num"));
		posPaymentOrder.put("terminal_id", param.optString("pos_num"));
		posPaymentOrder.put("prepay_id", xMDJson.optString("prepayId"));
		posPaymentOrder.put("type", param.optJSONObject("paymentAccountConfig").optString("type"));
		posPaymentOrder.put("shift", param.optString("shift"));
		if (result.getCode() == 0) {// 成功
			posPaymentOrder.put("status", StatusConstant.STATUS_PROGRESS);
			posPaymentOrder.put("final_state", StatusConstant.STATUS_PROGRESS);
		} else {// 失败
			posPaymentOrder.put("status", StatusConstant.STATUS_FAIL);
			posPaymentOrder.put("final_state", StatusConstant.STATUS_FAIL);
		}
		posPaymentOrder.put("create_time", DateUtil.getNowDateYYDDMMHHMMSS());
		posPaymentOrder.put("sub_code", result.getCode());
		posPaymentOrder.put("sub_msg", result.getMsg());
		posPaymentOrder.put("qr_code", xMDJson.optString("qrCode"));
		posPaymentOrder.put("out_trade_no", xMDJson.optString("outTradeNo"));
		posPaymentOrder.put("version", "1.0");
		posPaymentOrder.put("channel", param.optString("channel"));
		posPaymentOrder.put("report_date", param.optString("report_date"));
		posPaymentOrder.put("service_type", param.optString("service_type"));
		JSONObject extra = param.optJSONObject("extra");
		if(extra != null){
			JSONObject all_pay = extra.optJSONObject("all_pay");
			if(all_pay != null){
				posPaymentOrder.put("trade_type", all_pay.optString("trade_type"));
			}
		}
		
		posPaymentOrder.put("last_updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
		posPaymentOrder.put("platform", XmdConstant.XMD_PLATFORM);

		dao.persistPosPaymentOrder(result.getTenancy_id(), posPaymentOrder);
	}

	/**
	 * 封装二维码接口返回参数
	 */
	private void getPrepayBarcodeReturnParam(Data result, JSONObject xmdJson) {
		log.info("调用新美大支付返回结果："+xmdJson);
		result.setCode(0);
		result.setMsg("调用成功");
		List<JSONObject> data = new ArrayList<JSONObject>();
		JSONObject returnJson = new JSONObject();
		boolean paramComplete = false;
		returnJson.put("payment_state", StatusConstant.STATUS_PROGRESS);
		if (xmdJson.containsKey("outTradeNo") && StringUtils.isNotBlank(xmdJson.optString("outTradeNo"))) {
			returnJson.put("transaction_no", xmdJson.optString("outTradeNo"));
		} else {
			log.info("第三方接口返回值中outTradeNo为空");
			paramComplete = true;
		}
		if (xmdJson.containsKey("qrCode") && StringUtils.isNotBlank(xmdJson.optString("qrCode"))) {
			returnJson.put("qrcode", xmdJson.optString("qrCode"));
		} else {
			log.info("第三方接口返回值中qrCode为空");
			paramComplete = true;
		}
		if (xmdJson.containsKey("prepayId") && StringUtils.isNotBlank(xmdJson.optString("prepayId"))) {
			//h5支付
			returnJson.put("prepay_id", xmdJson.optString("prepayId"));
		}
		if (xmdJson.containsKey("appId") && StringUtils.isNotBlank(xmdJson.optString("appId"))) {
			//h5支付
			returnJson.put("appId", xmdJson.optString("appId"));
		}
		if (xmdJson.containsKey("timeStamp") && StringUtils.isNotBlank(xmdJson.optString("timeStamp"))) {
			//h5支付
			returnJson.put("timeStamp", xmdJson.optString("timeStamp"));
		}
		if (xmdJson.containsKey("nonceStr") && StringUtils.isNotBlank(xmdJson.optString("nonceStr"))) {
			//h5支付
			returnJson.put("nonceStr", xmdJson.optString("nonceStr"));
		}
		if (xmdJson.containsKey("signType") && StringUtils.isNotBlank(xmdJson.optString("signType"))) {
			//h5支付
			returnJson.put("signType", xmdJson.optString("signType"));
		}
		if (xmdJson.containsKey("paySign") && StringUtils.isNotBlank(xmdJson.optString("paySign"))) {
			//h5支付
			returnJson.put("paySign", xmdJson.optString("paySign"));
		}
		returnJson.put("is_meida_pay", true);
		if (paramComplete) {
			result.setCode(1);
			result.setMsg("调用失败");
			returnJson.put("payment_state", StatusConstant.STATUS_FAIL);
		}
		data.add(returnJson);
		result.setData(data);
	}

	@Override
	public void payOrderByCustomer(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
		log.info("验证参数是否为空");

		// 验证接收参数
		boolean validate = validate(param, result,
				// "merchantId", "appId", "random", "total_amount",

				"pay_type", "amount", "channel", "subject", "authcode");// goods_name
		if (!validate) {
			return;
		}
		// 刷卡预支付
		payOrderByCustomerSave4pervious(data.getTenancy_id(), data.getStore_id(), param);

		// 组装新美大接口参数
		JSONObject xmdParmas = PaymentUtil.generateParams(data, "merchantId", "appId", "random", "channel", "authCode",
				"totalFee", "subject", "body");

		String subject = param.getString("subject");
		if (subject.endsWith("")) {
			xmdParmas.put("subject", "餐费");
		}

		// 第三方订单号
		String outTradeNo = PaymentUtil.createOutTradeNo(data.getTenancy_id(), data.getStore_id(),
				param.getString("order_no"));
		xmdParmas.put("outTradeNo", outTradeNo);// 接入方订单号 不超过64位
		xmdParmas.put("expireMinutes", (short) 5);// 订单关闭时间

		// 验证签名
		String key = Constant.getSystemMap().get("payment_xmd_key");
		String appId = Constant.getSystemMap().get("app_id");
		xmdParmas.put("appId", Long.parseLong(appId));
		String merchant_no = param.getString("merchant_id");
		xmdParmas.put("merchantId", Long.parseLong(merchant_no));
		xmdParmas.put("random", UUIDUtil.generateGUID());

		String pay_type = param.optString("pay_type");
		String channel = null;
		if (pay_type.equals("ali_pay")) {
			channel = "ali_barcode_pay";
		} else if (pay_type.equals("wechat_pay")) {
			channel = "wx_barcode_pay";
		} else {
			result.setMsg("非法的支付类型：" + pay_type);
			result.setCode(Contant.ILLEGAL_PARAM);
			log.error("非法的支付类型：" + pay_type);
			return;
		}
		xmdParmas.put("channel", channel);
		xmdParmas.put("sign", PaymentUtil.generateSign(key, xmdParmas));

		// 验证封装参数
		validate = validate(xmdParmas, result, "merchantId", "appId", "random", "authCode", "totalFee", "subject",
				"body", "outTradeNo", "expireMinutes", "sign", "channel");
		if (!validate) {
			return;
		}

		// 发起新美大接口请求
		JSONObject xmdResponse = sendRequest(XmdConstant.URL_PAY_MICROPAY, xmdParmas);

		xmdResponse.put("outTradeNo2", xmdParmas.optString("outTradeNo"));
		fillReturnParams(xmdResponse, result, param, "1");
		// 保存请求第三方后的信息
		payOrderByCustomerSave(data, result, param);
	}

	@Override
	public void queryPayState(Data data, Data result) throws Exception {
		JSONObject param = getParam(data);
		String payType = param.optString("pay_type");
		boolean validate = validate(param, result, "order_no");
		if (!validate) {
			fail(result, Contant.ILLEGAL_PARAM, "请求参数有误");
			return;
		}
		String orderNum = getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no"));
		log.info("orderNum: " + orderNum);
		JSONObject posPaymentOrder = dao.getOrderByOrderNum(data.getTenancy_id(), orderNum);
		log.info("查询返回内容：" + posPaymentOrder);
		JSONObject requestJson = null;
		if (null == posPaymentOrder) {
			fail(result, Contant.NO_ORDER, "交易单号不存在");
			return;
		} else if (posPaymentOrder.optInt("final_state") == 1) {
			log.info("++++++++++新美大支付成功++++++++++++");
			List<JSONObject> resultData = new ArrayList<JSONObject>();
			JSONObject json = new JSONObject();
			result.setCode(0);
			result.setMsg("调用成功");
			json.put("payment_state", StatusConstant.STATUS_SUCCESS);
			json.put("out_trade_no", posPaymentOrder.optString("out_trade_no"));
			json.put("pay_type", payType);
			resultData.add(json);
			result.setData(resultData);
			return;
		} else if (posPaymentOrder.optInt("final_state") == 6) {
			log.info("++++++++++++++++新美大取消成功+++++++++++++++");

			List<JSONObject> resultData = new ArrayList<JSONObject>();
			JSONObject json = new JSONObject();
			result.setCode(0);
			result.setMsg("调用成功");
			json.put("payment_state", StatusConstant.STATUS_CANCELED);
			json.put("out_trade_no", posPaymentOrder.optString("out_trade_no"));
			json.put("pay_type", payType);
			// 外层
			resultData.add(json);
			result.setData(resultData);
			return;
		} else {

			JSONObject xmdResponse = null;
			try {
				requestJson = new JSONObject();
				String key = Constant.getSystemMap().get("payment_xmd_key");
				String appId = Constant.getSystemMap().get("app_id");
				requestJson.put("merchantId", Long.parseLong(param.getString("merchant_id")));
				requestJson.put("appId", appId);
				requestJson.put("random", UUIDUtil.generateGUID());
				// 第三方订单号
				requestJson.put("outTradeNo", posPaymentOrder.get("out_trade_no").toString());
				String sign = PaymentUtil.generateSign(key, requestJson);
				requestJson.put("sign", sign);
				// 验证封装参数
				validate = validate(requestJson, result, "merchantId", "appId", "random", "outTradeNo", "sign");
				if (!validate) {
					return;
				}
				log.info("商户Id["+data.getTenancy_id()+"]机构Id["+data.getStore_id()+"]订单号["+param.optString("order_no")+"]{}新美大支付查询订单状态请求参数：" + requestJson);
				// 发起新美大接口请求
				xmdResponse = sendRequest(XmdConstant.URL_PAY_QUERY, requestJson);
				log.info("商户Id["+data.getTenancy_id()+"]机构Id["+data.getStore_id()+"]订单号["+param.optString("order_no")+"]{}新美大支付查询订单状态返回：" + xmdResponse);

			} catch (Exception e) {
				e.printStackTrace();
			}
			fillReturnParams(xmdResponse, result, param, orderNum);

			// if(result.getCode()==1)//失败
			// return;
			// 保存请求第三方后的信息
			// payOrderByCustomerSave(data, result, param);

			// //交易成功返回的状态
			// if("SUCCESS".equals(xmdResponse.get("status"))&&"ORDER_SUCCESS".equals(xmdResponse.get("orderStatus"))){
			// result.setCode(Contant.SUCCESS);
			// result.setSuccess(Boolean.TRUE);
			// result.setMsg("调用成功");
			//
			// queryOrderByCustomerUpdate(data,xmdResponse);
			//
			// JSONObject resultJson = new JSONObject();
			//
			// result.setMsg("调用成功");
			// resultJson.put("payment_state",
			// PaymentUtil.getPaymentState(xmdResponse.optString("orderStatus")));
			// resultJson.put("trade_no", xmdResponse.optString("tradeNo"));
			// resultJson.put("out_trade_no",
			// xmdResponse.optString("outTradeNo"));
			// double totalAmount =
			// PaymentUtil.fen2yuan(xmdResponse.optLong("totalFee"));
			// resultJson.put("total_amount", totalAmount);
			// resultJson.put("transaction_no",
			// xmdResponse.optString("tradeNo"));
			// }else{
			// fail(result, Contant.NO_ORDER, "交易失败");
			// }
		}

		// List<JSONObject> datalist = new ArrayList<JSONObject>();
		// JSONObject dataObj = new JSONObject();
		// result.setCode(0);
		// result.setMsg("查询成功");
		// dataObj.put("payment_state",
		// posPaymentOrder.optString("final_state"));
		// dataObj.put("pay_type", param.optString("pay_type"));
		// dataObj.put("transaction_no",
		// posPaymentOrder.optString("out_trade_no"));
		// dataObj.put("total_amount",
		// posPaymentOrder.optString("total_amount"));
		// datalist.add(dataObj);
		// result.setData(datalist);
	}

	@Override
	public void refundPayOrder(Data data, Data result) throws Exception {

	}

	/**
	 * 订单关闭
	 */
	@Override
	public void cancelPayOrder(Data data, Data result) throws Exception {
		JSONObject requestJson = new JSONObject();
		JSONObject param = getParam(data);
		boolean validate = validate(param, result, "order_no", "service_type", "pos_num", "opt_num", "amount",
				"pay_type", "report_date", "shift", "channel");
		if (!validate) {
			fail(result, Contant.ILLEGAL_PARAM, "请求参数有误");
			return;
		}
		param.put("reason", "取消订单");

		String appId = Constant.getSystemMap().get("app_id");
		String merchant_no = param.getString("merchant_id");
		requestJson.put("merchantId", Long.parseLong(merchant_no));
		requestJson.put("appId", Long.parseLong(appId));
		requestJson.put("random", UUIDUtil.generateGUID());

		// requestJson.put("merchantId", param.optString("merchantId"));
		// requestJson.put("appId", param.optString("appId"));
		// requestJson.put("random", param.optString("random"));
		String orderNum = getOrderNum(data.getTenancy_id(), data.getStore_id(), param.optString("order_no"));
		JSONObject posPaymentOrder = dao.getOrderByOrderNum(data.getTenancy_id(), orderNum);
		if (null == posPaymentOrder) {
			fail(result, Contant.NO_ORDER, "交易单号不存在");
			return;
		}
		String outTradeNo = posPaymentOrder.optString("out_trade_no");
		requestJson.put("outTradeNo", outTradeNo);

		String qrCode = posPaymentOrder.optString("qr_code");
		log.info(posPaymentOrder.toString());
		log.info("+++++++++++++++++qrCode=" + qrCode + "+++++++");
		
		int status = posPaymentOrder.optInt("status");
		log.info("+++++++++++++++++status=" + status + "++++++++++++++++++++++");
		String[] orderArr = null;
		if (status == 1) {
			log.info("+++++订单退款接口++++++++++++");
			orderArr = new String[] { XmdConstant.INTERFACE_INVOKE_ORDER_REFUND};
		} else if (!"null".equals(qrCode) && status == 2) {
			log.info("+++++订单关闭接口++++++++++++");
			orderArr = new String[] { XmdConstant.INTERFACE_INVOKE_ORDER_CLOSE};
		} else {
			log.info("+++++订单撤销接口++++++++++++");
			orderArr = new String[] { XmdConstant.INTERFACE_INVOKE_ORDER_REVOKE};
		}
		// 数组用于定义接口调用顺序
		// String[] orderArr = new String[]{
		// XmdConstant.INTERFACE_INVOKE_ORDER_REVOKE
		// ,
		// XmdConstant.INTERFACE_INVOKE_ORDER_CLOSE,
		// XmdConstant.INTERFACE_INVOKE_ORDER_REFUND
		// };
		JSONObject responseJson = new JSONObject();
		log.info("{}+++++++++定义类型是那种取消状态，调用接口类型：" + orderArr.length + "======" + orderArr.toString());
		int finalState = StatusConstant.STATUS_CANCEL_FAIL;
		for (int i = 0; i < orderArr.length; i++) {
			// 移除多余参数
			clearParams(requestJson, "reason", "refundFee", "refundNo", "refundReason");

			String interfaceOrder = orderArr[i];
			switch (interfaceOrder) {
			case XmdConstant.INTERFACE_INVOKE_ORDER_REVOKE: // 取消支付
				responseJson = orderRevoke(requestJson, param);
				if (XmdConstant.PUBLIC_RETURN_STATUS_SUCCESS
						.equals(responseJson.optString("status").trim().toUpperCase())) {
					finalState = StatusConstant.STATUS_CANCELED;
					result.setCode(0);
					result.setMsg("取消订单成功");
				} else {
					finalState = StatusConstant.STATUS_CANCEL_FAIL;
					result.setCode(finalState);
					result.setMsg("取消失败");
				}
				break;
			case XmdConstant.INTERFACE_INVOKE_ORDER_CLOSE: // 关闭
				responseJson = orderClose(requestJson);
				if (XmdConstant.PUBLIC_RETURN_STATUS_SUCCESS
						.equals(responseJson.optString("status").trim().toUpperCase())) {
					finalState = StatusConstant.STATUS_ORDER_CLOSE;
					result.setCode(0);
					result.setMsg("订单关闭成功");
				} else if("ACQ.TRADE_NOT_EXIST".equals(responseJson.optString("subCode").trim().toUpperCase())){
					finalState = StatusConstant.STATUS_ORDER_CLOSE;
					result.setCode(0);
					result.setMsg("订单关闭成功");
				}else {
					finalState = StatusConstant.STATUS_CANCEL_FAIL;
					result.setCode(finalState);
					result.setMsg("取消失败");
				}
				break;
			case XmdConstant.INTERFACE_INVOKE_ORDER_REFUND: // 退款
				responseJson = orderRefund(requestJson, param, posPaymentOrder);
				if (XmdConstant.PUBLIC_RETURN_STATUS_SUCCESS
						.equals(responseJson.optString("status").trim().toUpperCase())) {
					finalState = StatusConstant.STATUS_REFUNDED;
					result.setCode(0);
					result.setMsg("订单退款成功");
				} else {
					finalState = StatusConstant.STATUS_REFUND_FAIL;
					result.setCode(finalState);
					result.setMsg("订单退款失败");
				}
				break;
			default:
				break;
			}

			if (XmdConstant.PUBLIC_RETURN_STATUS_SUCCESS
					.equals(responseJson.optString("status").trim().toUpperCase())) {
				break;
			}
		}

		// result.setCode(0);
		// result.setMsg("取消订单成功");
		// if (!closeFlag) {
		//
		// if("TRADE_ORDER_REFUNDED_ERROR".equals(responseJson.optString("errCode").trim().toUpperCase())){
		// result.setCode(0);
		// result.setMsg("订单已全部退款");
		// } else{
		// finalState = StatusConstant.STATUS_CANCEL_FAIL;
		// result.setCode(finalState);
		// result.setMsg("退款失败");
		// }
		//
		// }

		JSONObject json = new JSONObject();
		json.put("id", posPaymentOrder.optInt("id"));
		json.put("final_state", finalState);
		json.put("status", finalState);
		json.put("last_updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
		dao.updateIgnorCase(data.getTenancy_id().toString(), "pos_payment_order", json);

		JSONObject posPaymentCancel = JSONObject.fromObject(posPaymentOrder);
		posPaymentCancel.remove("id");
		posPaymentCancel.put("last_updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
		if (responseJson.containsKey("tradeNo") && StringUtils.isNotBlank(responseJson.optString("tradeNo"))) {
			posPaymentCancel.put("trade_no", responseJson.optString("tradeNo"));
		}
		posPaymentCancel.put("status",
				finalState == StatusConstant.STATUS_CANCEL_FAIL ? finalState : StatusConstant.STATUS_REFUNDED);
		dao.insertIgnorCase(data.getTenancy_id(), "pos_payment_cancel", posPaymentCancel);
		List<JSONObject> datalist = new ArrayList<JSONObject>();
		JSONObject dataObj = new JSONObject();
		dataObj.put("payment_state",
				finalState == StatusConstant.STATUS_CANCEL_FAIL ? finalState : StatusConstant.STATUS_REFUNDED);
		dataObj.put("pay_type", param.optString("pay_type"));
		dataObj.put("transaction_no", outTradeNo);
		dataObj.put("failure_code", responseJson.optString("subCode"));
		dataObj.put("failure_msg", responseJson.optString("subMsg"));
		datalist.add(dataObj);
		result.setData(datalist);
	}

	/**
	 * 移除多余参数
	 */
	private void clearParams(JSONObject requestJson, String... params) {
		if (params != null && params.length > 0) {
			for (String param : params) {
				if (requestJson.containsKey(param)) {
					requestJson.remove(param);
				}
			}
		}
	}

	/**
	 * 订单退款
	 */
	private JSONObject orderRefund(JSONObject requestJson, JSONObject param, JSONObject paymentOrderObj)
			throws IOException {
		// amount为0表示全额退款
		String amount = param.optString("amount");
		if (org.apache.commons.lang.StringUtils.isBlank(amount)
				|| org.apache.commons.lang.StringUtils.equals("0", amount)) {
			amount = paymentOrderObj.optString("total_amount");
		}
		requestJson.put("refundFee", PaymentUtil.yuan2fen(Double.valueOf(amount)));
		String refundNo = PaymentUtil.createRefundNo(paymentOrderObj.optString("tenancy_id"),
				paymentOrderObj.optInt("store_id"), param.optString("order_no"));
		requestJson.put("refundNo", refundNo);
		paymentOrderObj.put("refund_no", refundNo);
		requestJson.put("refundReason", param.optString("reason", "订单退款"));

		String sign = PaymentUtil.generateSign(Constant.getSystemMap().get("payment_xmd_key"), requestJson);
		requestJson.put("sign", sign);
		return sendRequest(XmdConstant.URL_REFUND, requestJson);
	}

	/**
	 * 订单撤销
	 */
	private JSONObject orderRevoke(JSONObject requestJson, JSONObject param) throws IOException {
		requestJson.put("reason", param.optString("reason", "订单撤销"));
		String sign = PaymentUtil.generateSign(Constant.getSystemMap().get("payment_xmd_key"), requestJson);
		requestJson.put("sign", sign);
		return sendRequest(XmdConstant.URL_CANCEL, requestJson);
	}

	/**
	 * 订单关闭
	 */
	private JSONObject orderClose(JSONObject requestJson) throws IOException {
		String sign = PaymentUtil.generateSign(Constant.getSystemMap().get("payment_xmd_key"), requestJson);
		requestJson.put("sign", sign);
		return sendRequest(XmdConstant.URL_CLOSE, requestJson);
	}

	@Override
	public void queryPayRefund(Data data, Data result) throws Exception {

	}

	// 返回失败信息处理
	@SuppressWarnings({ "unchecked", "rawtypes" })
	private JSONObject fail(Data result, int failCode, String failReason) {
		List resultList = new ArrayList();
		JSONObject json = new JSONObject();

		json.put("failure_code", failCode);
		json.put("failure_msg", failReason);
		resultList.add(json);

		result.setData(resultList);

		result.setSuccess(Boolean.FALSE);
		result.setCode(failCode);
		result.setMsg(failReason);

		json.put("payment_state", StatusConstant.STATUS_FAIL);
		// 返回data对象以便后期追加字段信息
		return json;
	}

	/**
	 * 发起请求
	 *
	 * @param api
	 * @param params
	 * @return
	 */
	private JSONObject sendRequest(String api, JSONObject params) throws IOException {
		log.info("新美大支付 - 调用接口: " + api + ", 参数: " + params.toString());
		String url = Constant.getSystemMap().get("payment_xmd_url");
		String response = "";
		JSONObject responseJson = null;
		for (int i = 0; i < 3; i++) {
			if (response == null) {
				try {
					Thread.currentThread().sleep(200);
					log.info("重试" + i + api);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			try {
				response = HttpUtil.sendPostRequest(url + api, params.toString());
				responseJson = JSONObject.fromObject(response);
				break;
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				response = null;
				responseJson = null;
			}
		}
		if (response == null) {
			log.error("调用3次都失败了---" + api);
		}

		log.info("新美大支付 - 调用接口: " + api + ", 返回: " + response);
		return responseJson;
	}

	/**
	 * 被扫预支付
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @throws Exception
	 */
	private void payOrderByCustomerSave4pervious(String tenancyId, int storeId, JSONObject param) throws Exception {
		JSONObject json = new JSONObject();
		json.put("tenancy_id", tenancyId);
		json.put("store_id", storeId);
		PaymentJsonUtils.copySrc2Dest4jsonOption(param, json, "terminal_id", "operator_id", "out_trade_no", "trade_no",
				"total_amount", "service_type", "channel", "report_date", "shift");
		String type = param.optJSONObject("paymentAccountConfig").optString("type");
		json.put("type", type);
		json.put("order_num", getOrderNum(tenancyId, storeId, param.optString("order_no")));
		json.put("bill_num", param.optString("order_no"));
		String billNum = param.optString("bill_num");
		if (StringUtils.isNotBlank(billNum)) {
			json.put("bill_num", billNum);
		}
		json.put("create_time", PaymentUtils.currentTime2Str());
		json.put("last_updatetime", PaymentUtils.currentTime2Str());

		// 返回信息
		json.put("status", StatusConstant.STATUS_PROGRESS);
		json.put("final_state", StatusConstant.STATUS_PROGRESS);
		json.put("version", StatusConstant.PAYMENT_VERSION_ONE_ZERO);
		json.put("sub_msg", "预订单");
		json.put("platform", XmdConstant.XMD_PLATFORM);
		log.info("生成预订单SQL : " + json.toString());
		int id = (int) this.paymentDao.insertIgnorCase(tenancyId, "pos_payment_order", json);
		log.info("生成预订单（pos_payment_order） : " + id);
		json.put("id", id);
		param.put("pos_payment_order_insert", json);
	}

	/**
	 * 处理新美大接口返回信息
	 *
	 * @param xmdResponse
	 * @param result
	 * @param param
	 */
	private void fillReturnParams(JSONObject xmdResponse, Data result, JSONObject param, String orderNum) {
		List<JSONObject> data = new ArrayList<JSONObject>();
		JSONObject json = new JSONObject();
		if (xmdResponse.optString("status").equals(XmdConstant.PUBLIC_RETURN_STATUS_FAIL)) {
			log.info(
					"新美大支付 - 支付失败（" + xmdResponse.optString("errCode") + " - " + xmdResponse.optString("errMsg") + "）");

			if (xmdResponse.optString("errCode").equals("TRADE_PAY_UNKOWN_ERROR")) {// 支付中
				result.setCode(0);
				result.setMsg("调用成功");
				json.put("payment_state", StatusConstant.STATUS_PROGRESS);
				json.put("out_trade_no", xmdResponse.optString("outTradeNo2", ""));
				json.put("pay_type", param.optString("pay_type"));
				// 外层
				// result.setOper(Oper.check);
				// result.setType(Type.QUERY_PAY_STATE);
			} else {
				log.info("++++++++++新美大支付失败++++++++++++++++++++");
				result.setCode(1);
				result.setMsg("调用失败");
				json.put("payment_state", StatusConstant.STATUS_FAIL);
				json.put("out_trade_no", xmdResponse.optString("outTradeNo2", ""));
				json.put("failure_code", xmdResponse.optString("errCode"));
				json.put("failure_msg", xmdResponse.optString("errMsg"));
			}

		} else if (xmdResponse.optString("status").equals(XmdConstant.PUBLIC_RETURN_STATUS_SUCCESS)) {
			// tradeNo outTradeNo totalFee payTime referno orderStatus

			if (xmdResponse.optString("tradeState", "").equals("USER_PAYING")) {// 支付中
				result.setCode(1);
				result.setMsg("支付中");
				json.put("payment_state", StatusConstant.STATUS_PROGRESS);
				json.put("out_trade_no", xmdResponse.optString("outTradeNo2", ""));
				json.put("pay_type", param.optString("pay_type"));
			} else if (xmdResponse.optString("tradeState", "").equals("SUCCESS")) { // 查询支付成功

				result.setCode(0);
				result.setMsg("调用成功");
				if (xmdResponse.optString("orderStatus", "").equals("ORDER_SUCCESS")) {
					log.info("++++++++++++++++新美大支付成功++++++++++++++");
					json.put("payment_state", StatusConstant.STATUS_SUCCESS);
				} else if (xmdResponse.optString("orderStatus", "").equals("TRADE_CANCELED")) {
					log.info("++++++++++++++++新美大取消成功++++++++++++++");
					json.put("payment_state", StatusConstant.STATUS_CANCELED);
				}

				json.put("last_updatetime", PaymentUtils.currentTime2Str());
				json.put("trade_no", xmdResponse.optString("tradeNo"));
				json.put("out_trade_no", xmdResponse.optString("outTradeNo"));
				double totalAmount = PaymentUtil.fen2yuan(xmdResponse.optLong("totalFee"));
				json.put("total_amount", totalAmount);
				if (orderNum == null || orderNum.length() == 0) {
					log.error("orderNum为空");
					return;
				}
				String updateSql = "update pos_payment_order set trade_no='%s',out_trade_no='%s',total_amount=%s,status=%s,final_state=%s,last_updatetime='%s' "
						+ " where  order_num='%s'";
				String updateSql2 = String.format(updateSql, xmdResponse.optString("tradeNo"),
						xmdResponse.optString("outTradeNo"), totalAmount, StatusConstant.STATUS_SUCCESS,
						StatusConstant.STATUS_SUCCESS, PaymentUtils.currentTime2Str(), orderNum);
				try {
					boolean i = paymentDao.execute(DBContextHolder.getTenancyid(), updateSql2);
				} catch (Exception e) {
					log.error("保存支付记录失败");
					e.printStackTrace();
				}

			} else {// 其它
				result.setCode(0);
				result.setMsg("调用成功");
				json.put("payment_state", PaymentUtil.getPaymentState(xmdResponse.optString("orderStatus")));
				json.put("trade_no", xmdResponse.optString("tradeNo"));
				json.put("out_trade_no", xmdResponse.optString("outTradeNo"));
				double totalAmount = PaymentUtil.fen2yuan(xmdResponse.optLong("totalFee"));
				json.put("total_amount", totalAmount);
				json.put("transaction_no", xmdResponse.optString("tradeNo"));
			}

		}
		data.add(json);
		result.setData(data);
	}

	/**
	 * 被扫保存数据
	 *
	 * @param data
	 * @param param
	 * @throws Exception
	 */
	private void payOrderByCustomerSave(Data data, Data result, JSONObject param) throws Exception {
		JSONObject json = param.optJSONObject("pos_payment_order_insert");
		// 返回信息
		JSONObject resultJson = getParam(result);
		String paymentState = resultJson.optString("payment_state");
		PaymentJsonUtils.copySrc2Dest4jsonOption(resultJson, json, "out_trade_no", "trade_no", "total_amount");
		json.put("status", paymentState);
		json.put("final_state", paymentState);
		json.put("platform", "1");
		if (StringUtils.equals(paymentState, String.valueOf(StatusConstant.STATUS_SUCCESS))) {
			log.info("+++++++++++新美大支付b扫c支付成功++++++++++++++++");
			json.put("finish_time", PaymentUtils.currentTime2Str());
			json.put("sub_code", result.getCode());
			json.put("sub_msg", result.getMsg());
		} else {
			log.info("+++++++++++新美大支付b扫c支付失败++++++++++++++++");
			json.put("sub_code", resultJson.optString("failure_code"));
			json.put("sub_msg", resultJson.optString("failure_msg"));
		}
		json.put("last_updatetime", PaymentUtils.currentTime2Str());
		log.info("插入数据库json: " + json.toString());
		paymentDao.update4Json(data.getTenancy_id(), "pos_payment_order", json);
	}

	@Override
	public String callback(JSONObject json) throws Exception {
		String outTradeNo = json.optString("outTradeNo");
		String tradeStatus = json.optString("tradeStatus");
		String tenancyId = PaymentRedisCache.getOutTradeNo2Tenancyid(outTradeNo);

		JSONObject result = new JSONObject();
		result.put("status", "SUCCESS");

		if ("".equals(tenancyId) || tenancyId == null) {
			log.info("{}从redis中，根据outTradeNo，取出tenancyId，判断是否为空如果为空redis中已删除，返回SUCCESS");
			return result.toString();
		}

		DBContextHolder.setTenancyid(tenancyId);
		JSONObject jsonObject = paymentDao.queryOrderByOutTradeNo2Recently(tenancyId, outTradeNo);
		if (!StringUtils.equals(tradeStatus, "TRADE_SUCCESS")) {
			return null;
		}

		if (jsonObject.optInt("status") == StatusConstant.STATUS_SUCCESS) {
			PaymentRedisCache.deleteInfoByOutTradeNo(outTradeNo);
			log.info("新美大支付 - 回调 - 查询数据库的状态status为SUCCESS状态");
			return result.toString();
		}

		if (StringUtils.isBlank(tenancyId)) {
			log.info("新美大支付 - 回调 - redis中不存在out_trade_no为" + outTradeNo + "的信息");
			return null;
		}
		addAssist(jsonObject, json);
		jsonObject.put("status", StatusConstant.STATUS_SUCCESS);

		if (jsonObject.optInt("final_state") == StatusConstant.STATUS_PROGRESS) {
			jsonObject.put("final_state", StatusConstant.STATUS_SUCCESS);
			jsonObject.put("finish_time", PaymentUtils.currentTime2Str());
		}
		this.paymentDao.updateIgnorCase(tenancyId, "pos_payment_order", jsonObject);
		log.info("新美大支付成功回掉保存数据成功：==参数" + jsonObject);
		PaymentRedisCache.deleteInfoByOutTradeNo(outTradeNo);
		send2Md(jsonObject);

		return result.toString();
	}

	public void addAssist(JSONObject json, JSONObject queryJsonObject) {
		json.put("last_updatetime", PaymentUtils.currentTime2Str());

		// 订单号
		String outTradeNo = queryJsonObject.optString("outTradeNo");
		if (StringUtils.isNotBlank(outTradeNo)) {
			json.put("out_trade_no", outTradeNo);
		}
		// 交易号
		String tradeNo = queryJsonObject.optString("providerTradeNo");
		if (StringUtils.isNotBlank(tradeNo)) {
			json.put("trade_no", tradeNo);
		}
		// 总金额
		Long totalFee = queryJsonObject.optLong("totalFee");
		if (totalFee != null) {
			double totalAmount = PaymentUtil.fen2yuan(totalFee);
			json.put("total_amount", totalAmount);
		}

		// try {
		// // 交易支付时间
		// long payTime = queryJsonObject.optLong("payTime");
		// String trade_date =
		// DateFormatUtils.format(PaymentUtil.long2date(payTime), "yyyy-MM-dd");
		// String trade_time =
		// DateFormatUtils.format(PaymentUtil.long2date(payTime), "HH:mm:ss");
		// json.put("trade_date", trade_date);
		// json.put("trade_time", trade_time);
		// } catch (Exception e) {
		// log.error(e);
		// }
	}

	/**
	 * 向门店下发支付结果
	 *
	 * @param json
	 * @throws Exception
	 */
	public void send2Md(JSONObject json) throws Exception {
		int finalState = json.optInt("final_state");
		if (!(finalState == StatusConstant.STATUS_SUCCESS || finalState == StatusConstant.STATUS_CANCELED
				|| finalState == StatusConstant.STATUS_REFUNDED)) {
			log.info("新美大支付 - 回调 - 待下发数据:" + json);
			log.info("新美大支付 - 回调 - 支付状态不明确，终止下发（单号为：" + json.optString("order_num") + "）");
			// 轮询没有得到明确结果，不下发到门店
			String out_trade_info = PaymentRedisCache.getInfoByOutTradeNo(json.optString("out_trade_no"));
			JSONObject task_json = JSONObject.fromObject(out_trade_info);
			PaymentRedisCache.lPushOutTradeNo2Info(task_json.optString("pay_type"), out_trade_info);
			return;
		}

		// 封装下发数据
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		JSONObject resultJson = new JSONObject();
		String tenancyId = json.optString("tenancy_id");
		int storeId = json.optInt("store_id");

		resultJson.put("order_no", json.optString("bill_num"));

		// C扫B的时候推送的订单号是门店传递过来的单号
		String qrCode = json.optString("qr_code");
		if (StringUtils.isNotBlank(qrCode) && !StringUtils.equals("null", qrCode)) {
			String orderNum = json.optString("order_num");
			String qrorderBefore = tenancyId + "_" + storeId + "_";
			String qrorderBehind = orderNum.replaceAll(qrorderBefore, StringUtils.EMPTY);
			resultJson.put("order_no", qrorderBehind);

			if (json.optInt("final_state") == StatusConstant.STATUS_CANCELED) {
				PaymentRedisCache.deleteInfoByOutTradeNo(json.optString("out_trade_no"));
			}
		}

		// // 验证同订单重复支付
		// if (setSynReturn(tenancy_id, store_id, json)) {
		// return;
		// }

		resultJson.put("pay_type", Contant.PAY_TYPE_XMDPAY);
		resultJson.put("payment_state", json.optString("final_state"));
		resultJson.put("transaction_no", json.optString("trade_no"));
		resultJson.put("total_amount", json.optString("total_amount"));
		resultList.add(resultJson);

		String sql = "select org_uuid,id from organ where id = ?";
		List<JSONObject> organInfos = paymentDao.query4Json(tenancyId, sql, new Object[] { storeId });
		if (organInfos.isEmpty()) {
			log.info("新美大支付 - 回调 - 未找到对应门店，终止下发（门店id为" + storeId + "）");
			return;
		}
		String uuid = organInfos.get(0).optString("org_uuid");

		MessageUtils message = new MessageUtils();
		Data d = Data.get();
		d.setType(Type.QUERY_PAY_STATE);
		d.setTenancy_id(tenancyId);
		d.setData(resultList);
		d.setStore_id(storeId);
		log.info("新美大支付 - 回调 - 下发数据:" + JSONObject.fromObject(d).toString());
		int returnId = message.sendMessage(JSONObject.fromObject(d).toString(), uuid, 1, tenancyId,
				String.valueOf("store_id"));
		PaymentRedisCache.deleteInfoByOutTradeNo(json.optString("out_trade_no"));
		if (returnId == 1) {
			log.info("=======================>> 轮询推送结果到门店成功!");
		} else {
			log.info("=======================>> 轮询推送结果到门店失败!");
		}
	}

}
