package com.tzx.report.service.rest.boh;

import com.tzx.report.bo.boh.BusinessSummaryTSReportService;
import com.tzx.report.common.util.ConditionUtils;
import jxl.write.WriteException;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

/**
 * 
 * 营业汇总统计报表
 *
 */

@Controller("businessSummaryTSReportRest")
@RequestMapping("/report/businessSummaryTSReportRest")
public class BusinessSummaryTSReportRest
{
	@Resource(name =BusinessSummaryTSReportService.NAME )
	private BusinessSummaryTSReportService businessSummaryTSReportService;

	
	@Resource
	ConditionUtils conditionUtils;
	
	@RequestMapping(value = "/getBusinessSummaryTS")
	public void getBusinessSummaryStatistics(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)||((Integer)session.getAttribute("valid_state")).equals(0)){
				String storeString = "store_id";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"99999999".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'99999999'".equals(p.optString(storeString)) 
						){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}
			}else{
				String storeString = "store_id";
				if(p.optString(storeString).length()==0 ||
						"0".equals(p.optString(storeString)) ||
						"'0'".equals(p.optString(storeString)) ||
						"99999999".equals(p.optString(storeString)) ||
						"''".equals(p.optString(storeString)) ||
						"'99999999'".equals(p.optString(storeString)) 
						){
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}
			
			result = businessSummaryTSReportService.getBusinessSummaryTS((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	/*@RequestMapping(value = "/exportDate")
	public void exportDate(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{

			workBook = new HSSFWorkbook();
		       
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			String storeString = "store_id";
			if(p.optString(storeString).length()==0 ||
					"0".equals(p.optString(storeString)) ||
					"'0'".equals(p.optString(storeString)) ||
					"99999999".equals(p.optString(storeString)) ||
					"''".equals(p.optString(storeString)) ||
					"'99999999'".equals(p.optString(storeString)) 
					){
				// 判断当前是门店还是总部
				if(session.getAttribute("organ_id").equals("0")) {
					//取所有门店
					p.element(storeString, session.getAttribute("user_organ_codes_group"));
				}else {
					// 取门店
					p.element(storeString, session.getAttribute("organ_id"));
				}
			}
	
			workBook = businessSummaryStatisticsReportService.exportData((String) session.getAttribute("tenentid"), p ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			download(workBook,response);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
public void download(HSSFWorkbook workBook, HttpServletResponse response) throws IOException {

		String fileName = "员工销售提成报表";
	      ByteArrayOutputStream os = new ByteArrayOutputStream();
	      workBook.write(os);
	      byte[] content = os.toByteArray();
	      InputStream is = new ByteArrayInputStream(content);
	      // 设置response参数，可以打开下载页面
	      response.reset();
	      response.setContentType("application/vnd.ms-excel;charset=utf-8");
	      response.setHeader("Content-Disposition", "attachment;filename="
	          + new String((fileName + ".xlsx").getBytes(), "iso-8859-1"));
	      ServletOutputStream out = response.getOutputStream();
	      BufferedInputStream bis = null;
	      BufferedOutputStream bos = null;
	 
	      try {
	        bis = new BufferedInputStream(is);
	        bos = new BufferedOutputStream(out);
	        byte[] buff = new byte[2048];
	        int bytesRead;
	        // Simple read/write loop.
	        while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
	          bos.write(buff, 0, bytesRead);
	        }
	      } catch (Exception e) {
	        // TODO: handle exception
	        e.printStackTrace();
	      } finally {
	        if (bis != null)
	          bis.close();
	        if (bos != null)
	          bos.close();
	      }
		
	}
		@RequestMapping(value = "/exportData")
		public void exportData(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
		{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			HttpSession session = request.getSession();
			HSSFWorkbook workBook = null;
			try
			{
		
				workBook = new HSSFWorkbook();
			       
				JSONObject p = JSONObject.fromObject("{}");
		
				Map<String, String[]> map = request.getParameterMap();
		
				for (String key : map.keySet())
				{
					if (map.get(key)[0] != "")
					{
						p.put(key, map.get(key)[0]);
					}
				}
				
				if(p.optString("store_id").length()==0){
					p.element("store_id", session.getAttribute("user_organ_codes_group"));
				}	
				
				workBook = businessSummaryStatisticsReportService.exportData((String) session.getAttribute("tenentid"), p ,workBook);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			try
			{
				ReportExportUtils.download(workBook,response,"营业汇总统计报表");
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}*/
}
