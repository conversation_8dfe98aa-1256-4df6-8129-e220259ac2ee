package com.tzx.cc.thirdparty.bo.imp;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import net.sf.json.JSONException;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.baidu.util.SignHolder;
import com.tzx.cc.common.constant.DishOper;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.cc.thirdparty.util.MeiTuanHelper;
import com.tzx.cc.thirdparty.util.XmdWMUtils;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.weixin.common.model.Gps;
import com.tzx.weixin.common.util.MapUtil;

public class XinMeiDaManager extends AbstractThirdPartyManager
{

	private static final Logger	logger					= Logger.getLogger(XinMeiDaManager.class);

	public XinMeiDaManager(String tenantId, String shopId)
	{
		super(tenantId, shopId, Constant.XMDWM_CHANNEL);
	}

	public XinMeiDaManager(String tenantId)
	{
		super(tenantId, Constant.XMDWM_CHANNEL);
	}

	@Override
	public JSONObject postOrderStatus(JSONObject params) throws Exception {
		String orderId = params.optString("orderId");
		String operType = params.optString("operType");

		Map<String, String> postParams = new HashMap<>();
		postParams.put("orderId", orderId);

		switch (operType)
		{
			case ORDER_CONFIRM:
				cmdPost(tenantId, shopId,XmdWMUtils.CMD_ORDER_CONFIRM, postParams);
				break;
			case ORDER_CANCEL:
				postParams.put("reasonCode", params.optString("type"));
				postParams.put("reason", params.optString("reason"));
				cmdPost(tenantId, shopId,XmdWMUtils.CMD_ORDER_CANCEL, postParams);
				break;
			default:
				response.put("errno", -1);
				response.put("error", "不支持的订单操作:" + operType);
		}
		logger.info("[新美大外卖订单状态变更]上传数据:"+postParams+"\n返回信息:"+response);
		return response;
	}

	@Override
	public JSONObject getOrderStatus(JSONObject params) throws Exception {
		String orderId = params.optString("third_order_id");
		Sign sign = SignHolder.getShopSign(tenantId, shopId, channel);
		String source = sign.getSource();
		String secret = sign.getSecret();
		Map<String, String> m = new HashMap<>();
		m.put("orderId", orderId);
		//20180202 zhangy修正参数传输错误bug
		cmdPost( tenantId, shopId,XmdWMUtils.CMD_ORDER_QUERYBYID, m);
		logger.info("[获取新美大外卖订单状态]上传数据:"+m+"\n返回数据:"+response);
		return response;
	}

	@Override
	public JSONObject getShopCategory(JSONObject params) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public JSONObject postOrderModelStatus(JSONObject param, String string) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public JSONObject setCategoryPositions(JSONObject params) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected void delDishCategory(JSONObject params) throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void delDish(JSONObject params) throws Exception {
		String shop_id = String.valueOf(params.optInt("store_id"));
//		Sign sign = SignHolder.getShopSign(tenantId, shop_id, channel);
//		String app_id = sign.getSource();
		Map<String, String> postParams = new HashMap<>();
		postParams.put("ePoiId", shop_id + "@" + tenantId);
		postParams.put("eDishCode", params.optString("item_code"));
		cmdPost(tenantId, shop_id ,XmdWMUtils.CMD_FOOD_DELETE, postParams);
		postParams.put("operator", params.optString("operator"));
		logger.info("[新美大商户批量删除菜品信息]上传数据:"+postParams+"\n返回数据:"+response);
	}

	@Override
	protected void postDish(List<JSONObject> params) throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void postDishSingle(JSONObject p) throws Exception {
		String shop_id = String.valueOf(p.optInt("store_id"));
//		Sign sign = SignHolder.getShopSign(tenantId, shop_id, channel);
//		String app_id = tenantId;
//		String secret = sign.getSecret();
		String ePoiId = shop_id + "@" + tenantId;
		
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		ccBusniessLogBean.setRequestId(p.optString("requestId"));
		ccBusniessLogBean.setTenancyId(tenantId);
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dish");
		ccBusniessLogBean.setChannel(channel);
		ccBusniessLogBean.setChannelName(channel);//暂时保持原来结构不变，暂时就不去处理该字段内容值
		ccBusniessLogBean.setCmd("com.tzx.cc.thirdparty.bo.imp.XinMeiDaManager:postDishSingle");
		
		
		
		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("1");
		//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		
		ccBusniessLogBean.setThirdId(p.optString("third_item_id"));
		ccBusniessLogBean.setTzxId(String.valueOf(p.optLong("item_id")));
	    ccBusniessLogBean.setTzxName(p.optString("item_name"));
	    ccBusniessLogBean.setShopId(shop_id);
		ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

		Map<String, String> foods = new HashMap<String, String>();
		foods.put("ePoiId", ePoiId);

		List<JSONObject> ff = new ArrayList<JSONObject>();

		JSONObject food = new JSONObject();
		food.put("epoiId", ePoiId);
		food.put("EDishCode", p.optInt("item_code"));
		food.put("dishName", p.optString("item_name"));
		food.put("description", p.optString("description"));
     
		String skus = "";
		String price = "";
		String unit_name="";
		JSONObject available_times_obj=new JSONObject();
		//多时段售卖 2017-12-19 start 刘娟 
		if(CommonUtil.checkStringIsNotEmpty(p.optString("dish_available_time"))) {
			Map<String, List<JSONObject>> timezone = new HashMap<String, List<JSONObject>>();
			String dish_available_time = p.optString("dish_available_time");
			available_times_obj = JSONObject.fromObject(dish_available_time);
			Iterator<String> iterator = available_times_obj.keys();
			while(iterator.hasNext()){
				String weekDay = iterator.next();
				if(!CommonUtil.checkStringIsNotEmpty(available_times_obj.optString(weekDay))){
					available_times_obj.put(weekDay, "00:00-23:59");
				}
			}
		}
		//end
//			SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
//			Date d1 = sdf.parse(p.optString("available_times_start"));
//			Date d2 = sdf.parse(p.optString("available_times_end"));
//			long result = d1.getTime()-d2.getTime();
//			if(result>=0&&!p.optString("available_times_end").equals("24:00")){
//				available_times_obj.put("monday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//				available_times_obj.put("tuesday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//				available_times_obj.put("wednesday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//				available_times_obj.put("thursday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//				available_times_obj.put("friday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//				available_times_obj.put("saturday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//				available_times_obj.put("sunday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");	
//			}else{
//				if(p.optString("available_times_end").equals("24:00")){
//		        	p.put("available_times_end", "23:59");
//		        }
//				available_times_obj.put("monday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//				available_times_obj.put("tuesday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//				available_times_obj.put("wednesday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//				available_times_obj.put("thursday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//				available_times_obj.put("friday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//				available_times_obj.put("saturday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//				available_times_obj.put("sunday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));	
//			
//			}

		if (p.containsKey("priceList") && !"".equals(p.get("priceList")))
		{
			List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(p.get("priceList").toString(), new TypeToken<List<JSONObject>>()
			{
			}.getType());
			for (JSONObject sku : list)
			{
				unit_name=sku.optString("unit_name");
				price=String.valueOf(sku.optDouble("price"));
				if(available_times_obj.toString().length() > 2){
					skus += ",{" + "\"skuId\":" + sku.optInt("id") +",\"availableTimes\":"+available_times_obj.toString()+ ",\"spec\":\"" + sku.optString("unit_name") +  "\",\"stock\":\"*\",\"price\":" + sku.optString("price") + "}";
				}else{
					skus += ",{" + "\"skuId\":" + sku.optInt("id") + ",\"spec\":\"" + sku.optString("unit_name") +  "\",\"stock\":\"*\",\"price\":" + sku.optString("price") + "}";
				}
				
			}
		}

		skus = "[" + skus.replaceFirst(",", "") + "]";

		food.put("skus", skus);
		food.put("price", price);
		food.put("minOrderCount", p.optInt("min_order_num"));
		food.put("unit", unit_name);//2017-03-03：unit取值改为规格名称
		food.put("boxNum", p.optInt("package_box_num"));
		food.put("boxPrice", p.optDouble("box_price",0.0));
		food.put("categoryName", p.optString("last_send_class_name"));
		food.put("isSoldOut", p.optString("is_sold_out"));
		food.put("picture", p.optString("item_pic"));
		
		//at 2017-05-08增加前端可选是进行上传图片，1代表上传，0或其它值代表不上传
		if(!"1".equals(p.optString("isUploadImg"))){
			food.discard("picture");
		}
		
		food.put("sequence", p.optString("rank"));
		ff.add(food);
		foods.put("dishes", ff.toString());
		ccBusniessLogBean.setRequestBody(foods.toString());
		try {
			cmdPost(tenantId, shop_id ,XmdWMUtils.CMD_FOOD_BATCHUPLOAD, foods);
			ccBusniessLogBean.setResponseBody(response.toString());
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.getStackTrace();
		}finally{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		food.put("operator", p.optString("send_operator"));
		logger.info("[新美大推送菜品信息]上传数据:"+food+"\n返回数据:"+response);

		
	}

	@Override
	protected void postDishCategory(JSONObject params) throws Exception {
		String shop_id = String.valueOf(params.optInt("store_id"));
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		ccBusniessLogBean.setRequestId(params.optString("requestId"));
		ccBusniessLogBean.setTenancyId(tenantId);
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dishCategory");
		ccBusniessLogBean.setChannel(params.optString("channel"));
		ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
		
		
		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("0");

		//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		if(params.containsKey("dishes_class")){
		  ccBusniessLogBean.setThirdId(params.optJSONArray("dishes_class").getJSONObject(0).optString("third_class_id"));
		  ccBusniessLogBean.setTzxId(String.valueOf(params.optJSONArray("dishes_class").getJSONObject(0).optLong("class_id")));
	      ccBusniessLogBean.setTzxName(params.optJSONArray("dishes_class").getJSONObject(0).optString("cur_class_name"));	
	      ccBusniessLogBean.setShopId(shop_id);
		}else{
		  ccBusniessLogBean.setThirdId(params.optString("third_class_id"));
		  ccBusniessLogBean.setTzxId(String.valueOf(params.optLong("class_id")));
	      ccBusniessLogBean.setTzxName(params.optString("cur_class_name"));	
	      ccBusniessLogBean.setShopId(shop_id);
		}

		// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
		ccBusniessLogBean.setOperAction(DishOper.pushDishCategory.toString());
		ccBusniessLogBean.setCmd("com.tzx.cc.thirdparty.bo.imp.XinMeiDaManager:postDishCategory");
//		Sign sign = SignHolder.getShopSign(tenantId, String.valueOf(params.optInt("store_id")), channel);
//		String app_id = sign.getSource();
//		String secret = sign.getSecret();

		Map<String, String> foodCat = new HashMap<>();
		//foodCat.put("app_poi_code", String.valueOf(params.optInt("store_id")) + "@" + tenantId);
		if(StringUtils.isNotEmpty(params.optString("id"))){
			if(StringUtils.isNotEmpty(params.optString("xmd_last_send_class_name"))){
				foodCat.put("oldCatName", params.optString("xmd_last_send_class_name"));
			}
		}
		foodCat.put("catName", params.optString("cur_class_name"));
		foodCat.put("sequence",  params.optString("rank").equals("") ? "1"
				: params.optString("rank"));
		
		ccBusniessLogBean.setRequestBody(foodCat.toString());
		try{
			cmdPost(tenantId, shop_id ,XmdWMUtils.CMD_FOOD_UPDATECAT, foodCat);
			ccBusniessLogBean.setResponseBody(response.toString());
			if(response.containsKey("errno") && response.optString("errno").equals("1021")){
				foodCat.remove("oldCatName");
				response.clear();
				cmdPost(tenantId, shop_id ,XmdWMUtils.CMD_FOOD_UPDATECAT, foodCat);
				ccBusniessLogBean.setResponseBody(response.toString());
			}else if(response.containsKey("errno") && response.optString("errno").equals("706")){
				foodCat.put("oldCatName", params.optString("cur_class_name"));
				response.clear();
				cmdPost(tenantId, shop_id ,XmdWMUtils.CMD_FOOD_UPDATECAT, foodCat);
				ccBusniessLogBean.setResponseBody(response.toString());
			}
		}catch(Exception ex){
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(ex));
			ccBusniessLogBean.setIsNormal("0");
			ex.printStackTrace();
		}finally{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		foodCat.put("operator", params.optString("update_operator"));
		logger.info("[推送新美大菜品分类]上传数据:"+foodCat+"\n返回数据:"+response);
	}

	@Override
	protected void postShopInfo(JSONObject params) throws Exception {
		String status = params.optString("open_level");
		String openTime = params.optString("business_time_format");

		JSONObject result_status = null;
		JSONObject result_time = null;
		boolean open_flag = true;
		boolean time_flag = true;
		try {

			DBContextHolder.setTenancyid(tenantId);
			GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
			List<JSONObject> resultJson = dao
					.query4Json(tenantId,
							"select * from cc_third_organ_info where channel='"+Constant.XMDWM_CHANNEL+"' and shop_id="
									+ shopId + " and tenant_id='" + tenantId + "'");
			if(resultJson != null && resultJson.size() > 0){
				String open_level = resultJson.get(0).optString("open_level");
				String business_time_format = resultJson.get(0).optString("business_time_format");
				if(StringUtils.isNotBlank(status) && status.equalsIgnoreCase(open_level)){
					open_flag = false;
				}
				if(StringUtils.isNotBlank(openTime) && openTime.equalsIgnoreCase(business_time_format)){
					time_flag =false;
				}

			}else {
				return ;
			}

			// 修改新美大休业，开业 及时间状态
			if (open_flag) {
				result_status = modifyXMDShopOpenStatus(tenantId, shopId, status);
			}
			if (time_flag) {
				result_time = modifyXMDShopOpenTime(tenantId, shopId, openTime);
			}
			logger.info("[新美大]修改店铺营业状态信息:" + (result_status != null ? result_status : "[]") + "\n[新美大]修改店铺营业时间结果："
					+ (result_time != null ? result_time : "[]"));
			//校验是否需要
			if ((result_status !=null && !result_status.optString("data").equalsIgnoreCase("ok")) ||
					(result_time != null && !result_time.optString("data").equalsIgnoreCase("ok"))) {
				StringBuilder sb = new StringBuilder();
				sb.append(result_status != null ? result_status : "");
				sb.append(result_time != null ? result_time : "");

				response.put("errno", ERROR);
				response.put("error", sb.toString());
			} else {
				response.put("errno", SUCCESS);
			}
		} catch (Exception e) {
			logger.info("新美大修改店铺信息出错：", e);
			response.put("errno", ERROR);
			response.put("error", e.getMessage());
			throw e;
		}
	}

	@Override
	protected void postShopStatus(JSONObject params) throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void postDeliveryTime(JSONObject params) throws Exception {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void postDeliveryRegion(JSONObject params) throws Exception {
		// TODO Auto-generated method stub
		
	}
	
	private void cmdPost(String appId, String shopId, String cmd, Map<String, String> params) throws Exception
	{
		String thirdRespose = XmdWMUtils.execCmd(appId, shopId,cmd,  params);
		logger.info("新美大返回的信息是:"+JSONObject.fromObject(thirdRespose));
		JSONObject result=null;
		try
		{
			result = JSONObject.fromObject(thirdRespose); 
			if (result.containsKey("error"))
			{
				JSONObject error = result.optJSONObject("error");
				response.put("errno", error.optString("code"));
				response.put("error", error.optString("message"));
			}
			else
			{
				response.putAll(result);
			}		
		}
		catch (Exception e1)
		{
			e1.printStackTrace();
			if(e1 instanceof JSONException){
				response.put("errno", ERROR);
				response.put("error","新美大接口未正确响应,请稍后重试!");
			}
		}
	}


	/**
	 * 修改新美大营业时间
	 * 
	 * @param tenantId
	 * @param shopId
	 * @param status
	 * @throws Exception
	 */
	private JSONObject modifyXMDShopOpenStatus(String tenantId, String shopId, String status) throws Exception {
		Map<String, String> paramMap = new HashMap<>();
		String result = "";
		if ("1".equals(status)) {
			result = XmdWMUtils.execCmd(tenantId, shopId, XmdWMUtils.CMD_SHOP_OPEN, paramMap);
		} else {
			result = XmdWMUtils.execCmd(tenantId, shopId, XmdWMUtils.CMD_SHOP_CLOSE, paramMap);
		}
		JSONObject obj = JSONObject.fromObject(result);
		if (obj != null && !obj.optString("data").equalsIgnoreCase("ok")) {
			throw new Exception("修改新美大门店营业状态["+status+"]失败!reason:" + result);
			
		}
		return JSONObject.fromObject(result);
	}

	/**
	 * 修改新没大
	 * 
	 * @param tenantId
	 * @param shopId
	 * @param status
	 * @throws Exception
	 */
	private JSONObject modifyXMDShopOpenTime(String tenantId, String shopId, String openTime) throws Exception {
		Map<String, String> paramMap = new HashMap<>();
		String result = "SUCCESS";
		StringBuilder sb = new StringBuilder();
		for (int i = 1; i <= 7; i++) {
			sb.append(openTime).append(";");
		}
		String time = sb.substring(0, sb.length() - 1);
		paramMap.put("openTime", time);
		result = XmdWMUtils.execCmd(tenantId, shopId, XmdWMUtils.CMD_SHOP_UPDATE_TIME, paramMap);
		JSONObject obj = JSONObject.fromObject(result);
		if (obj != null && !obj.optString("data").equalsIgnoreCase("ok")) {
			throw new Exception("修改新美大门店营业时间失败!reason:" + result);
		}
		return JSONObject.fromObject(result);
	}
	
	/**
	 * 新美大本地商户信息获取
	 */
	@Override
	public JSONObject getLocalShopInfo() throws Exception
	{
		JSONObject data = super.getLocalShopInfo();
		List<JSONObject> lineup = getBussinessTime();
		String lineupTimeId = "";
		String bussinessTimeFormat = "";
		for (JSONObject json : lineup)
		{
			lineupTimeId += "," + json.optString("id");
			String start = json.optString("start").equals("000:00") ? "000:00" : json.optString("start");
			String end = json.optString("end").equals("000:00") ? "000:00" : json.optString("end");
			if (!CommonUtil.checkStringIsNotEmpty(start) || !CommonUtil.checkStringIsNotEmpty(end)) continue;
			bussinessTimeFormat += start + "-" + end + ",";
		}
		if (bussinessTimeFormat.length() > 0)
		{
			bussinessTimeFormat = bussinessTimeFormat.substring(0, bussinessTimeFormat.length() - 1);
		}
		if (lineupTimeId.length() > 0)
		{
			lineupTimeId = lineupTimeId.substring(0, lineupTimeId.length() - 1);
		}
		data.put("business_time_format", bussinessTimeFormat);
		data.put("lineup_time_org_id", lineupTimeId);
		data.put("tenant_id", tenantId);
		// 百度坐标转换为火星坐标
		double latitude = data.optDouble("latitude");
		double longitude = data.optDouble("longitude");
		if (!Double.isNaN(latitude) && !Double.isNaN(longitude))
		{
			Gps gps = MapUtil.bd09_To_Gcj02(latitude, longitude);
			double lon = new BigDecimal(gps.getWgLon()).setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
			double lat = new BigDecimal(gps.getWgLat()).setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
			data.put("latitude", lat);
			data.put("longitude", lon);
		}
		return data;
	}
	
	public List<String> queryThirdOrderList(String tenantId,String tzxShopId,String date,int type,List<String> lists)throws Exception{
		List<String> messageList=new ArrayList<String>();
		JSONObject message=new JSONObject();
		
		String app_poi_code=tzxShopId+"@"+tenantId;
		

		Map<String,String> map=new TreeMap<String,String>();
		
		
		for(String orderIdOrSeq:lists){
			
			JSONObject result=null;
			if(type==1){
				map.put("ePoiId", app_poi_code);
				map.put("date", date.replace("-", ""));
				map.put("daySeq", orderIdOrSeq);
				String thirdRespose = XmdWMUtils.execCmd(tenantId, tzxShopId, XmdWMUtils.CMD_ORDER_QUERYBYDAYSEQ, map);
				if(!StringUtils.isEmpty(thirdRespose)){
					result=JSONObject.fromObject(thirdRespose);
				}
				
				orderIdOrSeq="";
			}
			
			if(!StringUtils.isEmpty(orderIdOrSeq)){
				map.clear();
				
				map.put("orderId", orderIdOrSeq);
				String thirdRespose = XmdWMUtils.execCmd(tenantId, tzxShopId, XmdWMUtils.CMD_ORDER_QUERYBYID, map);
				
				result=JSONObject.fromObject(thirdRespose);

			}
			
			
			JSONObject data=result.getJSONObject("data");
			data.put("ctime",data.optLong("cTime"));
			//已取消
			if("9".equals(data.optString("status"))){
				message.put("tzxStatus", "08");
			}else{
				message.put("tzxStatus", "10");
			}
			
			message.put("tzxCreateAt", CommonUtil.getDateFromSeconds(String.valueOf(data.optLong("cTime"))));
			message.put("status", data.optString("status"));
			message.put("thirdOrderCode", data.optString("orderId"));
			message.put("order", data);
			message.put("ePoiId", data.optString("ePoiId",""));
			
			messageList.add(message.toString());
			logger.info("商户："+tenantId+",门店："+tzxShopId+",获取新美大订单详情完成->"+result);
		}
		return messageList;
	}

	@Override
	protected void updateDishStockSingle(JSONObject p) throws Exception {
		String shop_id = String.valueOf(p.optInt("store_id"));
		String ePoiId = shop_id + "@" + tenantId;
		Map<String, String> foods = new HashMap<String, String>();
		foods.put("ePoiId", ePoiId);

		List<JSONObject> ff = new ArrayList<JSONObject>();

		if(p.containsKey("stockList") && p.getJSONArray("stockList").size() > 0){
			List<JSONObject> stockList = p.getJSONArray("stockList");
			for(JSONObject json : stockList){
				JSONObject food = new JSONObject();
				food.put("eDishCode", json.optInt("item_code"));
		     
				String skus = "";
				skus += ",{" + "\"stock\":" + p.optInt("stock") + ",\"skuId\":" + json.optInt("unit_id") + "}";
				skus = "[" + skus.replaceFirst(",", "") + "]";
				food.put("skus", skus);
				ff.add(food);
			}
		}else{
			JSONObject food = new JSONObject();
			food.put("eDishCode", p.optInt("item_code"));
	     
			String skus = "";
			skus += ",{" + "\"stock\":" + p.optInt("stock") + ",\"skuId\":" + p.optInt("unit_id") + "}";
			skus = "[" + skus.replaceFirst(",", "") + "]";
			food.put("skus", skus);
			ff.add(food);
		}
		
		foods.put("dishSkuStocks", ff.toString());
		try {
			cmdPost(tenantId, shop_id ,XmdWMUtils.CMD_FOOD_UPDATESTOCK, foods);
		} catch (Exception e) {
			e.getStackTrace();
		}finally{
			if(response.containsKey("data") && "ok".equals(response.optString("data"))){
				response.put("stockResult", SUCCESS);
			}else{
				response.put("stockResult", ERROR);
				
				if("805".equals(response.optString("errno"))){
					List<String> listError = new ArrayList<String>();
					String errorStr = response.optString("error").split(":")[1];
					String errorInfo = errorStr.substring(1, errorStr.length()-1);
					String [] errorIds = errorInfo.split(",");
					for(String s : errorIds){
						listError.add(s);
					}
					response.put("errorItemCodes", listError);
				}
			}
		}
		foods.put("operator", p.optString("send_operator"));
		logger.info("[新美大更新菜品库存]上传数据:"+foods+"\n返回数据:"+response);

	}

}
