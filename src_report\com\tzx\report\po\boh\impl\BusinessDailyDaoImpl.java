package com.tzx.report.po.boh.impl;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.BusinessDailyDao;
import net.sf.json.JSONObject;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */

@Repository(BusinessDailyDao.NAME)
public class BusinessDailyDaoImpl implements BusinessDailyDao{

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Resource(name = "parameterUtils")
    ParameterUtils parameterUtils;

    @Resource
    ConditionUtils conditionUtils;
    @Override
    public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
        List<JSONObject> list = new ArrayList<JSONObject>();
        List<JSONObject> footerList =new ArrayList<JSONObject>();
        List<JSONObject> structure = new ArrayList<JSONObject>();
        JSONObject result = new JSONObject();
        long total = 0L;
        String year = condition.optString("p_year");
        if(year.length()>0 && year.length()>0 ) {

            String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.BUSINESS_DAY_BAO01);
            String f_rpt_hxlyyrbg01 = null;
            if(!("").equals(reportSql)){
                SqlRowSet column = dao.query(tenancyID, reportSql.toString());
                if(column.next()){
                    f_rpt_hxlyyrbg01 = column.getString("f_rpt_hxlyyrbg01");
                    if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) { //导出
                        String sql = "SELECT * FROM (" +  f_rpt_hxlyyrbg01.toString() + ") as temp1 WHERE  id::TEXT IN ( SELECT REGEXP_SPLIT_TO_TABLE('"+condition.optString("exportdataexpr")+"', ',' ))";
                        list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition, sql , condition.optInt("level")));
                        structure = conditionUtils.getSqlStructure(tenancyID,   f_rpt_hxlyyrbg01.toString());
                    }else{
                        list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, f_rpt_hxlyyrbg01.toString()));
                        total = this.dao.countSql(tenancyID, f_rpt_hxlyyrbg01.toString());
                    }
                }

                String f_rpt_hxlyyrbg02 = null;
                String reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition, EngineConstantArea.BUSINESS_DAY_BAO02);
                if(!("").equals(reportSqlCount)){
                    SqlRowSet col1 = dao.query(tenancyID, reportSqlCount.toString());
                    if(col1.next()){
                        f_rpt_hxlyyrbg02 = col1.getString("f_rpt_hxlyyrbg02");
                        footerList = this.dao.query4Json(tenancyID, f_rpt_hxlyyrbg02.toString());
                    }
                }
            }

        }
        int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
        result.put("page", pagenum);
        result.put("total",total);
        result.put("rows", list);
        result.put("footer", footerList);
        result.put("structure", structure);
        return result;
    }

    @Override
    public List<JSONObject> getPayTypeItems(String tenancyID, JSONObject condition) throws Exception {
        List<JSONObject> list = new ArrayList<JSONObject>();

        String f_rpt_hxlyyrbg00 = null;
        String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.BUSINESS_DAY_BAO00);
        if(!reportSql.equals("")){
            SqlRowSet column = dao.query(tenancyID, reportSql.toString());
            if(column.next()){
                f_rpt_hxlyyrbg00 = column.getString("f_rpt_hxlyyrbg00");
                list = this.dao.query4Json(tenancyID, f_rpt_hxlyyrbg00);
            }
        }
        return list;
    }
}
