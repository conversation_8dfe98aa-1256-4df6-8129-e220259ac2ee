package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.SinglerateDao;

@Repository(SinglerateDao.NAME)
public class SinglerateDaoImpl implements SinglerateDao{

	private final String sqlType = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_15' and sql_type='JGL1'";
	private final String sqlType2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_15' and sql_type='JGL2'";
	private final String sqlType3 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_15' and sql_type='JGL3'";
	private final String sqlTypeCount ="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_15' and sql_type='JGL0'";
	
	private final String sqlType4 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_15' and sql_type='RQL1'";
	private final String sqlType5 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_15' and sql_type='RQL2'";
	private final String sqlType6 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_15' and sql_type='RQL3'";
	private final String sqlTypeCount1 ="select sql from saas_report_engine where report_num = 'SAAS_BI_2016_15' and sql_type='RQL0'";
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	
	List<JSONObject> list =null;
	StringBuilder sb = new StringBuilder();
	@Override
	public JSONObject getSinglerate(String tenancyID, JSONObject condition) throws Exception {
		Integer type = condition.optInt("type");
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> structure =new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		String begindate = condition.optString("report_date_begin");
		String enddate = condition.optString("report_date_end");
		JSONObject result = new JSONObject();
		long total = 0L;
		if(begindate.length()>0 && enddate.length()>0 )
		{
			switch (type)
			{
				//按机构汇总
				case 1:
					if(condition.optInt("hierarchytype") ==1){
						if(condition.containsKey("exportdataexpr") && !condition.optString("exportdataexpr").equals("''")){
							String exp = condition.optString("exportdataexpr");
							condition.element("exportdataexpr", conditionUtils.spilt(exp.substring(1, exp.length()-1)));
						}
						String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql);
						}else{
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCount));
					    }
					}else if(condition.optInt("hierarchytype") ==2){
						String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType2);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level1")));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql);
						}else{
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					    }
					}else if(condition.optInt("hierarchytype") ==3){
						String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType3);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level2")));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql);
						}else{
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					    }
					}
				break;
				//按日期汇总
				case 2:
					if(condition.optInt("hierarchytype") ==1){
						if(condition.containsKey("exportdataexpr") && !condition.optString("exportdataexpr").equals("''")){
							String exp = condition.optString("exportdataexpr");
							condition.element("exportdataexpr", conditionUtils.spilt(exp.substring(1, exp.length()-1)));
						}
						String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType4);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level1")));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql);
						}else{
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlTypeCount1));
					    }
					}else if(condition.optInt("hierarchytype") ==2){
						String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType5);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level1")));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql);
						}else{
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					    }
					}else if(condition.optInt("hierarchytype") ==3){
						String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlType6);
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level2")));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql);
						}else{
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					    }
					}
				break;
				default:
					break;
			}
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
 	}
}
