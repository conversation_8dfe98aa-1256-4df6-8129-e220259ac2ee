package com.tzx.report.bo.imp.hq;

import com.tzx.report.bo.hq.BusinessDiscountSummaryQueryService;
import com.tzx.report.po.hq.dao.BusinessDiscountSummaryQueryDao;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service(BusinessDiscountSummaryQueryService.NAME)
public class BusinessDiscountSummaryQueryServiceImpl implements BusinessDiscountSummaryQueryService
{
	
	@Resource
	private BusinessDiscountSummaryQueryDao businessDiscountSummaryQueryDao;

	@Override
	public JSONObject getBusinessDiscountSummaryQuery(String tenancyID, JSONObject condition) throws Exception {
		return businessDiscountSummaryQueryDao.getBusinessDiscountSummaryQuery(tenancyID, condition);
	}
 
}
