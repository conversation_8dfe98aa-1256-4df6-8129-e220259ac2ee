package com.tzx.boh.bo;

import java.util.List;

import net.sf.json.JSONObject;

public interface PaymentManagementService 
{

	   String NAME = "com.tzx.boh.bo.imp.PaymentManagementServiceImpl";
		
		public String loadLastPaymentRecord(String tenancyID,JSONObject condition) throws Exception;
		
		public JSONObject loadPaymentRecord(String tenancyID,JSONObject condition) throws Exception;

		public boolean checkUnique(String tenentId, JSONObject param)throws Exception;
		
		public void delete(String tenantId, String tableKey, List<JSONObject> keyList) throws Exception;

}
