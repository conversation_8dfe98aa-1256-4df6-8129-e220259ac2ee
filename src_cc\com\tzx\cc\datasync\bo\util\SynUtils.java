package com.tzx.cc.datasync.bo.util;

import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public class SynUtils {

	/**
	 * 添加空的值
	 * @param rifData
	 * @param keys
     */
	public static void addNullKey(List<JSONObject> rifData,String ... keys){
		if(keys==null || keys.length==0) {
			return;
		}
		if(rifData==null || rifData.size()==0) {
			return;
		}
		for(JSONObject json :rifData){
			for(String key:keys){
				if(!json.containsKey(key)) {
					json.put(key,StringUtils.EMPTY);
				}
			}
		}
	}
	/**
	 * 为rifData添加渠道
	 * @param rifData
	 * @param dic_result_list
     */
	public static void addQd(List<JSONObject> rifData
			, List<JSONObject> dic_result_list){
		List<JSONObject> rifDataTemp = new ArrayList<JSONObject>();
		for (JSONObject chanelJson : dic_result_list) {
			String chanel = chanelJson.optString("class_item_code").trim();
			for (JSONObject rifitem : rifData) {
				JSONObject json = new JSONObject();
				SynUtils.copySrc2Dest4jsonOption(rifitem, json);
				json.put("chanel", chanel);
				rifDataTemp.add(json);
			}
		}
		rifData.clear();
		rifData.addAll(rifDataTemp);
	}

	/**
	 * 获取chanel的str
	 * @param dic_result_list
     */
	public static String getChanelStr(List<JSONObject> dic_result_list){
		StringBuffer chanelstr = new StringBuffer();
		for (int i = 0;i< dic_result_list.size();i++) {
			String chanel = dic_result_list.get(i).optString("class_item_code");
			if(i==0) {
				chanelstr.append(chanel);
			} else {
				chanelstr.append("','").append(chanel);
			}
		}
		return chanelstr.toString();
	}

	/**
	 * 两个json可选择的进行copy键值
	 * @param src 源
	 * @param dest 目标
	 * @param names 要copy的json的key, 若names为空，则是全部copy
	 */
	@SuppressWarnings("rawtypes")
	public static void copySrc2Dest4jsonOption(JSONObject src,JSONObject dest,String ... names){
		Set keySet = src.keySet();
		Iterator iterator = keySet.iterator();
		while(iterator.hasNext()){
			String key = (String) iterator.next();
			if(names==null || names.length == 0) {
				dest.put(key, src.opt(key));
			} else {
				for(String name:names) {
					if(StringUtils.equals(name, key)) {
						Object value = src.opt(key);
						dest.put(key, value);
						break;
					}
				}
			}
		}
	}
}
