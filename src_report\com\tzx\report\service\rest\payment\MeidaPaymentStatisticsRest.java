package com.tzx.report.service.rest.payment;

import com.tzx.report.bo.payment.service.MeidaPaymentStatisticsService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ReportExportUtils;
import jxl.write.WriteException;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;


/**
 * 查询新美支付统计报表
 * <AUTHOR>
 *
 */

@Controller("meidaPaymentStatisticsRest")
@RequestMapping("/report/MeidaPaymentStatisticsRest")
public class MeidaPaymentStatisticsRest
{
	
	@Resource(name = MeidaPaymentStatisticsService.NAME)
	private MeidaPaymentStatisticsService meidaPaymentStatisticsService;

	@Resource(name = "conditionUtils")
	ConditionUtils conditionUtils;

	/**
	 * 查询新美支付统计报表
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getMeidaPaymentStatisticsQuery")
	public void getMeidaPaymentStatisticsQuery(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}

			String storeString = "storeId";
			if(p.optString(storeString).length()==0 ||
					"0".equals(p.optString(storeString)) ||
					"'0'".equals(p.optString(storeString)) ||
					"********".equals(p.optString(storeString)) ||
					"''".equals(p.optString(storeString)) ||
					"'********'".equals(p.optString(storeString)) 
					){
				if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}else{
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}
			//添加

			result = meidaPaymentStatisticsService.getMeidaPaymentStatisticsQuery((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
				
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	@RequestMapping(value = "/exportDate")
	public void exportDate(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{

			workBook = new HSSFWorkbook();
		       
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			String storeString = "storeId";
			if(p.optString(storeString).length()==0 ||
					"0".equals(p.optString(storeString)) ||
					"'0'".equals(p.optString(storeString)) ||
					"********".equals(p.optString(storeString)) ||
					"''".equals(p.optString(storeString)) ||
					"'********'".equals(p.optString(storeString)) 
					){
				// 判断当前是门店还是总部
				if(session.getAttribute("organ_id").equals("0")) {
					//取所有门店
					p.element(storeString, session.getAttribute("user_organ_codes_group"));
					//p.element(storeString, "373");
				}else {
					// 取门店
					p.element(storeString, session.getAttribute("organ_id"));
				}
			}
	
			workBook = meidaPaymentStatisticsService.exportData((String) session.getAttribute("tenentid"), p ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			ReportExportUtils.download(workBook,response,"美大支付统计报表");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}




	/**
	 * 查询新美支付对账报表
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getMeidaPaymentAccountQuery")
	public void getMeidaPaymentAccountQuery(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}

			String storeString = "storeId";
			if(p.optString(storeString).length()==0 ||
					"0".equals(p.optString(storeString)) ||
					"'0'".equals(p.optString(storeString)) ||
					"********".equals(p.optString(storeString)) ||
					"''".equals(p.optString(storeString)) ||
					"'********'".equals(p.optString(storeString))
					){
				if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}else{
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}
			//添加

			result = meidaPaymentStatisticsService.getMeidaPaymentAccountQuery((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{

			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}


	/**
	 * 查询新美支付对账报表
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getLocalMeidaPaymentAccountQuery")
	public void getLocalMeidaPaymentAccountQuery(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}

			String storeString = "storeId";
			if(p.optString(storeString).length()==0 ||
					"0".equals(p.optString(storeString)) ||
					"'0'".equals(p.optString(storeString)) ||
					"********".equals(p.optString(storeString)) ||
					"''".equals(p.optString(storeString)) ||
					"'********'".equals(p.optString(storeString))
					){
				if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}else{
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}
			//添加

			result = meidaPaymentStatisticsService.getLocalMeidaPaymentAccountQuery((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{

			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}


	/**
	 * 导出对账报表
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/exportAccountDate")
	public void exportAccountDate(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{

			workBook = new HSSFWorkbook();

			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}

			String storeString = "storeId";
			if(p.optString(storeString).length()==0 ||
					"0".equals(p.optString(storeString)) ||
					"'0'".equals(p.optString(storeString)) ||
					"********".equals(p.optString(storeString)) ||
					"''".equals(p.optString(storeString)) ||
					"'********'".equals(p.optString(storeString))
					){
				// 判断当前是门店还是总部
				if(session.getAttribute("organ_id").equals("0")) {
					//取所有门店
					p.element(storeString, session.getAttribute("user_organ_codes_group"));
					//p.element(storeString, "373");
				}else {
					// 取门店
					p.element(storeString, session.getAttribute("organ_id"));
				}
			}

			workBook = meidaPaymentStatisticsService.exportAccountData((String) session.getAttribute("tenentid"), p ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			ReportExportUtils.download(workBook,response,"美大支付对账报表");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
}
