package com.tzx.report.po.boh.dao;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */
public interface BusinessDailyDao {

    String NAME = "com.tzx.report.po.boh.impl.BusinessDailyDaoImpl";

    JSONObject find(String tenancyID, JSONObject condition) throws Exception;

    List<JSONObject> getPayTypeItems(String tenancyID, JSONObject condition) throws Exception;

}
