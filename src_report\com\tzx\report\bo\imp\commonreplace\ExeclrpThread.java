package com.tzx.report.bo.imp.commonreplace;

import com.tzx.report.common.util.ExportUtils;
import com.tzx.report.common.util.ReportExportUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Callable;

public class ExeclrpThread implements Callable<File>{

    private final Logger logger = LoggerFactory.getLogger(getClass());
    static final int sheetNum = 20000;

    private String exportName;
    private JSONObject json;
    private List<JSONObject> data1;
    private List<JSONObject> data2;
    private List<JSONObject> data3;
    private HSSFSheet sheet1;
    private HSSFWorkbook workBook;
    private Integer stratIndex;
    private Integer stratIndex2;
    private Integer stratIndex3;
    private JSONObject out1Result;
    private JSONObject out1Result2;
    private JSONObject out1Result3;
    private List<JSONObject> list;
    private JSONObject paramData;
    private File gue;

    public ExeclrpThread(String exportName,JSONObject json,List<JSONObject> data1,List<JSONObject> data2,List<JSONObject> data3,HSSFSheet sheet1, HSSFWorkbook workBook,Integer stratIndex,Integer stratIndex2,Integer stratIndex3,JSONObject out1Result,JSONObject out1Result2,JSONObject out1Result3,List<JSONObject> list,JSONObject paramData,File gue){
        this.exportName = exportName;
        this.json = json;
        this.data1 = data1;
        this.data2 = data2;
        this.data3 = data3;
        this.sheet1 = sheet1;
        this.workBook = workBook;
        this.stratIndex = stratIndex;
        this.stratIndex2 = stratIndex2;
        this.stratIndex3 = stratIndex3;
        this.out1Result = out1Result;
        this.out1Result2 = out1Result2;
        this.out1Result3 = out1Result3;
        this.list = list;
        this.paramData = paramData;
        this.gue = gue;
    }


    @Override
    public File call() throws Exception {
        boolean judge = true;
        int rowNo = 0;      //总行号
        int rowline = 1; //sheet页最大得记录条数
        List<JSONObject> structure = null;
        List<JSONObject>  structure1= null;
        List<JSONObject>  structure2= null;
        List<File> files = new ArrayList();
        File f = null;
        boolean b = false;
        //合计
        JSONArray footerArr = null;
        JSONObject footer = null;

        if(data1!=null&&data1.size()>0){
            structure = structureData(data1);
        }
        if(data2!=null&&data2.size()>0){
            structure1 = structureData(data2);
        }
        if(data3!=null&&data3.size()>0){
            structure2 = structureData(data3);
        }

        try{
            if(data1!=null&&data2!=null&&data3!=null){
                if(data1.size()>0 && data2.size()>0 && data3.size()>0){
                    if(structure.size()>0 && structure1.size()>0 && structure2.size()>0){
                        for(int i=0;i<data1.size();i++){
                            for(int j=0;j<data2.size();j++){
                                if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
                                    JSONObject obj = data1.get(i).getJSONObject("json1");
                                    JSONObject objs = data2.get(j).getJSONObject("json2");
                                    if(obj.size()>0 && objs.size()>0){
                                        if(judge){
                                            out1Result = ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
                                            paramData.put("rowNum", out1Result.opt("rowNum"));
                                            paramData.put("jin", out1Result.optInt("jin"));
                                            stratIndex=out1Result.optInt("rowNum");
                                            judge = false;
                                        }
                                        if(!objs.optString(json.optString("key")).equals("")){
                                            objs.remove(json.optString("key"));
                                        }
                                        out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
                                        stratIndex2=out1Result2.optInt("rowNum");
                                        paramData.put("rowNum", out1Result2.opt("rowNum"));
                                        paramData.put("jin", out1Result2.optInt("jin"));

                                        for(int k=0;k<data3.size();k++){
                                            if(data2.get(j).optString(json.optString("key1")).equals(data3.get(k).optString(json.optString("key1")))){   //第二层对比
                                                if(data2.get(j).optString("exportdataexpr1").equals(data3.get(k).optString("exportdataexpr2"))){  //第一层对比
                                                    JSONObject obj3 = data3.get(k).getJSONObject("json3");
                                                    if(obj3.size()>0){
                                                        if(!obj3.optString(json.optString("key")).equals("")){
                                                            obj3.remove(json.optString("key"));
                                                        }
                                                        if(!obj3.optString(json.optString("key1")).equals("")){
                                                            obj3.remove(json.optString("key1"));
                                                        }
                                                        out1Result3 =ExportUtils.out2(exportName,obj3,workBook,sheet1,list,structure2,paramData);
                                                        stratIndex3=out1Result3.optInt("rowNum");
                                                        paramData.put("rowNum", out1Result3.opt("rowNum"));
                                                        paramData.put("jin", out1Result3.optInt("jin"));
                                                    }
                                                }
                                            }
                                        }
                                        if(json.optInt("groupRow")==2){
                                            sheet1.groupRow(stratIndex2,stratIndex3);
                                            sheet1.setRowGroupCollapsed(stratIndex2, true);
                                        }else{
                                            sheet1.groupRow(stratIndex2,stratIndex3);
                                        }
                                    }
                                }
                            }
                            if(json.optInt("groupRow")==2){
                                sheet1.groupRow(stratIndex,stratIndex3);
                                sheet1.setRowGroupCollapsed(stratIndex, true);
                            }else{
                                sheet1.groupRow(stratIndex,stratIndex3);
                            }
                            judge = true;
                        }
                    }
                }
            }else if(data1!=null && data2!=null && data1.size()>0 && data2.size()>0){
                if(structure.size()>0 && structure1.size()>0){
                    for(int i=0;i<data1.size();i++){
                        for(int j=0;j<data2.size();j++){
                            if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
                                JSONObject obj = data1.get(i).getJSONObject("json1");
                                JSONObject objs = data2.get(j).getJSONObject("json2");
                                if(obj.size()>0 & objs.size()>0){
                                    if(judge){
                                        out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
                                        paramData.put("rowNum", out1Result.opt("rowNum"));
                                        paramData.put("jin", out1Result.optInt("jin"));
                                        stratIndex=out1Result.optInt("rowNum");
                                        judge = false;
                                    }

                                    if(!objs.optString(json.optString("key")).equals("")){
                                        objs.remove(json.optString("key"));
                                    }

                                    out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
                                    stratIndex2=out1Result2.optInt("rowNum");
                                    paramData.put("rowNum", out1Result2.opt("rowNum"));
                                    paramData.put("jin", out1Result2.optInt("jin"));
                                }
                            }
                        }
                        if(json.optInt("groupRow")==2){
                            sheet1.groupRow(stratIndex,stratIndex2);
                            sheet1.setRowGroupCollapsed(stratIndex, true);
                        }else{
                            sheet1.groupRow(stratIndex,stratIndex2);
                        }
                        judge = true;
                    }
                }
            }else{
                if(data1!=null && data1.size()>0){
                    if(data1.get(data1.size()-1).containsKey("footerList")){
                        footerArr = data1.get(data1.size()-1).getJSONArray("footerList");
                        footer = footerArr.getJSONObject(0);
                    }
                    if(structure.size()>0){
                        Integer rowNum = null;
                        Integer jin = null;
                        JSONObject returnTitleJson = null;
                        JSONObject obj = null;
                        int num = data1.size();
                        for(int i=0;i<data1.size();i++) {
                            obj = data1.get(i).getJSONObject("json1");
                            if(obj.size()>0){
                                if(rowNo%sheetNum==0){
                                    // System.out.println("Current Sheet:" + rowNo/100);
                                    sheet1 = workBook.createSheet(exportName+"-"+System.currentTimeMillis());//建立新的sheet对象
                                    returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
                                    rowNum= returnTitleJson.optInt("rowNum");
                                    jin=json.optInt("jin");
                                    paramData=exportContrastline(rowNum,jin);
                                    //sheet1 = workBook.getSheetAt(rowNo/100);        //动态指定当前的工作表
                                }
                                rowNo++;
                                // 调用导出方法；
                                out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
                                paramData.put("rowNum", out1Result.opt("rowNum"));
                                paramData.put("jin", out1Result.optInt("jin"));

                                if(rowNo%sheetNum==0){
                                    b=true;
                                    //System.out.println("row no: " + rowNo);
                                    int a1 = out1Result==null?0:out1Result.optInt("rowNum");
                                    int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
                                    int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
                                    // 导出印记
                                    JSONObject cc = new JSONObject();
                                    cc.put("man", json.opt("man"));
                                    cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
                                    ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);
                                    if(rowNo >= sheetNum){
                                        f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
                                        OutputStream fout =  new FileOutputStream(f);
                                        workBook.write(fout);
                                        //不分sheet页就必须重新new该对象
                                        workBook  = new HSSFWorkbook();
                                        fout.close();
                                        //files.add(f);
                                    }

                                }

//                                if((num-1)>=sheetNum){
//                                    if(rowNo%sheetNum==0){
//                                        b=true;
//                                        //System.out.println("row no: " + rowNo);
//                                        int a1 = out1Result==null?0:out1Result.optInt("rowNum");
//                                        int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
//                                        int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
//                                        // 导出印记
//                                        JSONObject cc = new JSONObject();
//                                        cc.put("man", json.opt("man"));
//                                        cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
//                                        ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);
//                                        if(rowNo >= sheetNum){
//                                            f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
//                                            OutputStream fout =  new FileOutputStream(f);
//                                            workBook.write(fout);
//                                            //不分sheet页就必须重新new该对象
//                                            workBook  = new HSSFWorkbook();
//                                            fout.close();
//                                            //files.add(f);
//                                        }
//
//                                    }
//                                }else if((num-1)<sheetNum){
//                                    if(rowNo%(num-2)==0){
//                                        b=true;
//                                        //System.out.println("row no: " + rowNo);
//                                        int a1 = out1Result==null?0:out1Result.optInt("rowNum");
//                                        int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
//                                        int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
//                                        // 导出印记
//                                        JSONObject cc = new JSONObject();
//                                        cc.put("man", json.opt("man"));
//                                        cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
//                                        ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);
//                                        if(rowNo >= (num-2)){
//                                            f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
//                                            OutputStream fout =  new FileOutputStream(f);
//                                            workBook.write(fout);
//                                            //不分sheet页就必须重新new该对象
//                                            workBook  = new HSSFWorkbook();
//                                            fout.close();
//                                            //files.add(f);
//                                        }
//
//                                    }
//                                }

                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        finally {
//            int a1 = out1Result==null?0:out1Result.optInt("rowNum");
//            int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
//            int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
//            // 导出印记
//            JSONObject cc = new JSONObject();
//            cc.put("man", json.opt("man"));
//            cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
//            ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);

            //if(b){
                f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
                OutputStream fout =  new FileOutputStream(f);
                workBook.write(fout);
                //不分sheet页就必须重新new该对象
                fout.close();
                files.add(f);

                return f;
            //}
        }
        //return null;
    }

    public List<JSONObject> structureData(List<JSONObject> list1){
        List<JSONObject> obj = new ArrayList<JSONObject>();
        if(list1.get(list1.size()-1)!=null){
            JSONArray arr = list1.get(list1.size()-1).getJSONArray("structure");
            for(int i=0;i<arr.size();i++){
                JSONObject job = new JSONObject();
                JSONObject info=arr.getJSONObject(i);
                job.put("fieldname", info.getString("fieldname"));
                job.put("fieldtype", info.getString("fieldtype"));
                obj.add(job);
            }
        }
        return obj;
    }

    public JSONObject exportContrastline(int rowNum,int jin){
        JSONObject paramData =new JSONObject();
        paramData.put("rowNum", rowNum);
        paramData.put("jin",jin);
        paramData.put("strtIndex",rowNum);
        return paramData;
    }


}
