package com.tzx.framework.service.rest;

import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.framework.bo.ProvincesAndCitiesService;

@Controller("provincesAndCitiesController")
@RequestMapping("/framework/provincesAndCitiesController")
public class ProvincesAndCitiesRest {
	
	@Resource(name = ProvincesAndCitiesService.NAME)
	ProvincesAndCitiesService provincesAndCitiesService;

	@RequestMapping(value = "/getProvincesAndCitiesTree")
	public void getProvincesAndCitiesTree(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String returnJSONstring = "";
		try
		{
			JSONObject condition = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();
			
			for(String key : map.keySet()){
				if(map.get(key)[0] != null && !"".equals(map.get(key)[0])){
					condition.put(key, map.get(key)[0]);
				}
			}
			//returnJSONstring = organService.getOrganTree((String) session.getAttribute("tenentid"), (String) session.getAttribute("organ_id"));
			returnJSONstring = provincesAndCitiesService.getProvincesAndCitiesTree((String)session.getAttribute("tenentid"), condition);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.println(returnJSONstring);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	@RequestMapping(value = "getProvincesAndCitiesList")
	public void getProvincesAndCitiesList(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String returnJSONstring = "";
		try
		{
			JSONObject condition = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();
			
			for(String key : map.keySet()){
				if(map.get(key)[0] != null && !"".equals(map.get(key)[0])){
					condition.put(key, map.get(key)[0]);
				}
			}
			
			returnJSONstring = provincesAndCitiesService.getProvincesAndCitiesList((String)session.getAttribute("tenentid"), condition);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.println(returnJSONstring);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 * 省、市、区 级联查询
	 * @param request
	 * @param response
	 */
//	@RequestMapping(value = "/getCity")
//	public void getCity(HttpServletRequest request, HttpServletResponse response){
//		
//		response.setContentType("text/html; charset=UTF-8");
//		response.setContentType("text/html");
//		response.setCharacterEncoding("UTF-8");
//		PrintWriter out = null;
//		HttpSession session = request.getSession();
//		String returnJSONstring = "";
//		try
//		{
//			JSONObject condition = JSONObject.fromObject("{}");
//			Map<String, String[]> map = request.getParameterMap();
//			
//			for(String key : map.keySet()){
//				if(map.get(key)[0] != null && !"".equals(map.get(key)[0])){
//					condition.put(key, map.get(key)[0]);
//				}
//			}
//			
//			returnJSONstring = provincesAndCitiesService.getCity((String)session.getAttribute("tenentid"), condition);
//		}
//		catch (Exception e)
//		{
//			e.printStackTrace();
//		}
//		finally
//		{
//			try
//			{
//				out = response.getWriter();
//				out.println(returnJSONstring);
//				out.flush();
//				out.close();
//			}
//			catch (Exception e)
//			{
//				e.printStackTrace();
//			}
//			finally
//			{
//				if (out != null) out.close();
//			}
//		}
//	}
}
