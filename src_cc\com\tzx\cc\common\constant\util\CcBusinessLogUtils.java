package com.tzx.cc.common.constant.util;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.util.Date;

import net.sf.json.JSONObject;
import junit.framework.Assert;

import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.framework.log4j.util.LoggerUtil;

public class CcBusinessLogUtils {

	public static void log(CcBusniessLogBean ccBusniessLogBean){
        try{
        	System.out.println("===============================日志写入=====================================");
        	boolean bo = false;
            for(int i =0;i< 1;i++){
                long start = System.currentTimeMillis(); //获取当前系统毫秒
                String path = "http://"+LoggerUtil.getLoggerServerAddress()+"/customize/logger/info";
                JSONObject obj = JSONObject.fromObject(ccBusniessLogBean);
                String jsonStr = obj.toString();
                byte[] data = jsonStr.getBytes("UTF-8");
                java.net.URL url = new java.net.URL(path);
                java.net.HttpURLConnection conn = (java.net.HttpURLConnection) url.openConnection();
                conn.setRequestMethod("POST");
                conn.setDoOutput(true);
                conn.setRequestProperty("Connection", "keep-alive");
                conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
                conn.setRequestProperty("Content-Length", String.valueOf(data.length));
                OutputStream outStream = conn.getOutputStream();
                outStream.write(data);
                outStream.flush();
                outStream.close();
                if(conn.getResponseCode() == 200){
                    BufferedReader in = new BufferedReader(new InputStreamReader((InputStream) conn.getInputStream(), "UTF-8"));
                    String msg = in.readLine();
                    System.out.println("msg: " + msg);
                    in.close();
                    bo = true;
                }
                conn.disconnect();
                long end1 = System.currentTimeMillis();         // 获取当前系统毫秒
                String runTime1 = String.valueOf((end1-start));  // 运行时间，单位毫
                System.out.println("===============================日志结束=====================================");
            }        	
        }catch(Exception e){
        	e.printStackTrace();
        	System.out.println("===============================日志异常=====================================");
        }            
	}
}
