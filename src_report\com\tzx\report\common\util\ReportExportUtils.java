package com.tzx.report.common.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.tzx.report.service.rest.commonreplace.CommonMethodAreaRest;

public class ReportExportUtils {

	
	private static final Logger	 log	= Logger.getLogger(ReportExportUtils.class);
	
	//下载HSSFWorkBook 
	public static void download(HSSFWorkbook workBook, HttpServletResponse response,String fileName) throws IOException {

      ByteArrayOutputStream os = new ByteArrayOutputStream();
      workBook.write(os);
      byte[] content = os.toByteArray();
      InputStream is = new ByteArrayInputStream(content);
      // 设置response参数，可以打开下载页面
      response.reset();
      response.setContentType("application/vnd.ms-excel;charset=utf-8");
      response.setHeader("Content-Disposition", "attachment;filename="
          + new String((fileName + ".xls").getBytes(), "iso-8859-1"));
      ServletOutputStream out = response.getOutputStream();
      BufferedInputStream bis = null;
      BufferedOutputStream bos = null;
 
      try {
        bis = new BufferedInputStream(is);
        bos = new BufferedOutputStream(out);
        byte[] buff = new byte[2048];
        int bytesRead;
        // Simple read/write loop.
        while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
          bos.write(buff, 0, bytesRead);
        }
      } catch (Exception e) {
        // TODO: handle exception
        e.printStackTrace();
      } finally {
        if (bis != null)
          bos.flush();
          bis.close();
      }
	
}
	
	
	/**
	 * 
	 * @param file 需要下载的文件
	 * @param response 输出对象
	 * @param fileName 下载的文件名字
	 * @param deletePath 需要删除的文件地址
	 * @throws IOException
	 */
	public static void downloadFile(File file, HttpServletResponse response,String fileName,String deletePath) throws IOException {

        try {  
            response.setCharacterEncoding("UTF-8");  
            response.setHeader("Content-Disposition",  
                    "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8")+".zip" /*new String(fileName.getBytes("ISO8859-1"), "UTF-8")+".zip"*/);  
            response.setContentLength((int) file.length());  
            response.setContentType("application/zip");// 定义输出类型  
            FileInputStream fis = new FileInputStream(file);  
            BufferedInputStream buff = new BufferedInputStream(fis);  
            byte[] b = new byte[1024];// 相当于我们的缓存  
            long k = 0;// 该值用于计算当前实际下载了多少字节  
            OutputStream myout = response.getOutputStream();// 从response对象中得到输出流,准备下载  
            // 开始循环下载  
            while (k < file.length()) {  
                int j = buff.read(b, 0, 1024);  
                k += j;  
                myout.write(b, 0, j);  
            }  
            myout.flush();  
            buff.close();  
            //file.delete();  
            // 延迟
            Thread.sleep(30000);
            log.info("文件删除成功："+deleteDir(new File(deletePath)));
        } catch (Exception e) {  
            System.out.println(e);  
        }  
	
	}
	
	
	/**
	 * 
	 * @param file 需要下载的文件
	 * @param response 输出对象
	 * @param fileName 下载的文件名字
	 * @param deletePath 需要删除的文件地址
	 * @throws IOException
	 */
	public static void downloadFile(File file, HttpServletResponse response,String fileName) throws IOException {

        try {  
            response.setCharacterEncoding("UTF-8");  
            response.setHeader("Content-Disposition",  
                    "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8")+".zip" /*new String(fileName.getBytes("ISO8859-1"), "UTF-8")+".zip"*/);  
            response.setContentLength((int) file.length());  
            response.setContentType("application/zip");// 定义输出类型  
            FileInputStream fis = new FileInputStream(file);  
            BufferedInputStream buff = new BufferedInputStream(fis);  
            byte[] b = new byte[1024];// 相当于我们的缓存  
            long k = 0;// 该值用于计算当前实际下载了多少字节  
            OutputStream myout = response.getOutputStream();// 从response对象中得到输出流,准备下载  
            // 开始循环下载  
            while (k < file.length()) {  
                int j = buff.read(b, 0, 1024);  
                k += j;  
                myout.write(b, 0, j);  
            }  
            myout.flush();  
            buff.close();  
        } catch (Exception e) {  
            System.out.println(e);  
        }  
	
	}
	
public static boolean deleteDir(File dir) {
		
		// 给文件是不是目录
        if (dir.isDirectory()) {
        	// 是目录 获取目录里面的文件名字
            String[] children = dir.list();
            //递归删除目录中的子目录下
            for (int i=0; i<children.length; i++) {
            	// 循环目录里面的文件
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        // 目录此时为空，可以删除
        return forceDelete(dir);
    }
	
	public static boolean forceDelete(File file) {
        boolean result = file.delete();
        int tryCount = 0;
        while (!result && tryCount++ < 10) {
            System.gc();    //回收资源
            result = file.delete();
        }
        return result;
    }
	
	
	public static void download03(XSSFWorkbook workBook, HttpServletResponse response,String fileName) throws IOException {

	      ByteArrayOutputStream os = new ByteArrayOutputStream();
	      workBook.write(os);
	      byte[] content = os.toByteArray();
	      InputStream is = new ByteArrayInputStream(content);
	      // 设置response参数，可以打开下载页面
	      response.reset();
	      response.setContentType("application/vnd.ms-excel;charset=utf-8");
	      response.setHeader("Content-Disposition", "attachment;filename="
	          + new String((fileName + ".xls").getBytes(), "iso-8859-1"));
	      ServletOutputStream out = response.getOutputStream();
	      BufferedInputStream bis = null;
	      BufferedOutputStream bos = null;
	 
	      try {
	        bis = new BufferedInputStream(is);
	        bos = new BufferedOutputStream(out);
	        byte[] buff = new byte[2048];
	        int bytesRead;
	        // Simple read/write loop.
	        while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
	          bos.write(buff, 0, bytesRead);
	        }
	      } catch (Exception e) {
	        // TODO: handle exception
	        e.printStackTrace();
	      } finally {
	        if (bis != null)
	          bis.close();
	        if (bos != null)
	          bos.close();
	      }
		
	}
	
	/**
	 * 创建普通的单元格样式  仅四面带有边框的
	 * @return
	 */
	private static HSSFCellStyle styleString =null;
	private static HSSFCellStyle style000 =null;
	private static HSSFCellStyle style00 =null;
	private static HSSFCellStyle style0 =null;
	private static HSSFCellStyle stylebai =null;

	private static HSSFWorkbook workBookCan =null;
	public static HSSFCellStyle createCellStyle(HSSFWorkbook workBook,String styleType) {
		
		HSSFCellStyle styleReturn= null;
		if(workBookCan==null) {
			workBookCan=workBook;
		}
		if(!workBookCan.equals(workBook)) {
			workBookCan=workBook;
			styleString=null;
			style000 =null;
			style00 =null;
			style0 =null;
			stylebai =null;
		}
		if(styleString==null&&style000==null&&style00==null&&style0==null&&stylebai==null) {
			styleString =workBook.createCellStyle();
	    	//设置前景的背景颜色
		    //style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		    //设置颜色 GREEN 可变
		    //style.setFillForegroundColor(HSSFColor.GREEN.index);
		    
			styleString.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			styleString.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			styleString.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			styleString.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			
		    /*HSSFFont font =workBook.createFont();
		    //设置字体格式
		    font.setFontName("Aharoni");
		    font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		    style.setFont(font);*/
			style000 =workBook.createCellStyle();
			style000.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			style000.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			style000.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			style000.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			style000.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00")); 
			 
			style00 =workBook.createCellStyle();
			style00.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			style00.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			style00.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			style00.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			style00.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.0"));
			 
			style0 =workBook.createCellStyle();
			style0.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			style0.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			style0.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			style0.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			style0.setDataFormat(HSSFDataFormat.getBuiltinFormat("0"));
			 
			stylebai =workBook.createCellStyle();
			stylebai.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			stylebai.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			stylebai.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			stylebai.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			stylebai.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00%"));
			 
		}
		if(styleType.endsWith("String")) {
			styleReturn =	styleString;
		}else if (styleType.endsWith("0.00")) {
			styleReturn =	style000;
		}else if (styleType.endsWith("0.0")) {
			styleReturn =	style00;
		}else if (styleType.endsWith("0")) {
			styleReturn =	style0;
		}else if (styleType.endsWith("0.00%")) {
			styleReturn =	stylebai;
		}
	    return styleReturn;
	}
	
	/**
	 * 
	 * @param outJson report的json
	 * @param workBook HSSFWorkbook对象
	 * @param sheet1 唯一的sheet1
	 * @param listTitleName 头名
	 * @param dataName 数据字段
	 * @param dataType 数据类型
	 * @return
	 */
	public static Integer startNum = 0;
	public static JSONObject out1(JSONObject outJson,HSSFWorkbook workBook,HSSFSheet sheet1,String[] listTitleName,String[] dataName,String[] dataType,JSONObject paramData) throws Exception {
		  
		Integer rowNum =paramData.optInt("rowNum");
		Integer jin =paramData.optInt("jin");
		JSONObject result =new JSONObject();
		    if(paramData.optInt("jin")==0){
		    	// 单例模式
		    	HSSFCellStyle style =getTitleStyle( workBook); ;
		    	startNum = rowNum;
		    int  strtIndex = 0;
		    //创建下标为0的一行 就是第一行  循环赋值表头名字
		    if(paramData.containsKey("strtIndex") ) {
		    	strtIndex=paramData.optInt("strtIndex");
		    }
		    HSSFRow row1 =sheet1.createRow(strtIndex);
		    if(listTitleName.length>0) {
		    	for(int y = 0;y<listTitleName.length;y++) {
			    	HSSFCell cell =row1.createCell(y);
				    cell.setCellStyle(style);
				    cell.setCellValue(listTitleName[y]);
				    
			    }
		    }
		    
	       // HSSFCellStyle style1 =createCellStyle(workBook);
		   	/*style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
*/		    
		    HSSFRow row2 =sheet1.createRow(rowNum++);
		    
		  	if(dataName.length>0) {
		  		for(int c =0;c<dataName.length;c++) {
		    		HSSFCell cell1 =row2.createCell(c);
			    	if(dataType[c].equals("String")) {
				    	
				    	cell1.setCellValue((outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals(null)||outJson.opt(dataName[c]).equals("")? "":outJson.optString(dataName[c])));				    	
				    	
				    	cell1.setCellStyle(createCellStyle(workBook,"String"));
				    }else if(dataType[c].equals("0.00")){
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("0.00");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(dataName[c]));
				    		
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0.00"));
				    }else if (dataType[c].equals("0.0")) {
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue( outJson.optDouble(dataName[c]));
				    		 
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0.0"));
				    }else if (dataType[c].equals("0")) {
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optInt(dataName[c]));
				    		 
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0"));
				    }else if(dataType[c].equals("0.00%")) {
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(dataName[c]));
				    		 
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0.00%"));
				    }
			  }
		  	}
		   
	         
	        jin++;
		    }else {
		    	
			   //	HSSFCellStyle style1 = createCellStyle(workBook);
			  /* 	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
*/			    
		    	if(paramData.containsKey("TenThousandExcle") && paramData.optString("TenThousandExcle").equals("Yes") &&  paramData.optInt("maxExcleNum") == (rowNum - startNum) ) {
					throw new Exception("导出已达到上线  : " +paramData.optInt("maxExcleNum") ); 
				}
		    	
		    	
			    HSSFRow row2 =sheet1.createRow(rowNum++);
			  	
			    if(dataName.length>0) {
			    	for(int c =0;c<dataName.length;c++) {
			    		HSSFCell cell1 =row2.createCell(c);
				    	if(dataType[c].equals("String")) {
					    	cell1.setCellValue((outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals(null)||outJson.opt(dataName[c]).equals("")? "":outJson.optString(dataName[c])));				    	
					    	
					    	cell1.setCellStyle(createCellStyle(workBook,"String"));
					    }else if(dataType[c].equals("0.00")){
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("0.00");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(dataName[c]));
					    		
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0.00"));
					    }else if (dataType[c].equals("0.0")) {
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue( outJson.optDouble(dataName[c]));
					    		 
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0.0"));
					    }else if (dataType[c].equals("0")) {
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optInt(dataName[c]));
					    		 
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0"));
					    }else if(dataType[c].equals("0.00%")) {
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(dataName[c]));
					    		
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0.00%"));
					    }
				  }
			    }
		 }
		    result.put("rowNum", rowNum);
		    result.put("jin", jin);
		    return result;
	}
	
	public static JSONObject out1Object(JSONObject outJson,HSSFWorkbook workBook,HSSFSheet sheet1,Object[] listTitleName,Object[] dataName,Object[] dataType,JSONObject paramData) {
		  
		Integer rowNum =paramData.optInt("rowNum");
		Integer jin =paramData.optInt("jin");
		JSONObject result =new JSONObject();
		    if(paramData.optInt("jin")==0){
		    	// 单例模式
		    	HSSFCellStyle style =getTitleStyle( workBook); ;
		   
		    int  strtIndex = rowNum;
		    //创建下标为0的一行 就是第一行  循环赋值表头名字
		    if(paramData.containsKey("strtIndex") ) {
		    	strtIndex=paramData.optInt("strtIndex");
		    }
		    HSSFRow row1 =sheet1.createRow(strtIndex);
		    if(listTitleName.length>0) {
		    	for(int y = 0;y<listTitleName.length;y++) {
			    	HSSFCell cell =row1.createCell(y);
				    cell.setCellStyle(style);
				    cell.setCellValue(listTitleName[y].toString());
				    
			    }
		    }
		    
	       // HSSFCellStyle style1 =createCellStyle(workBook);
		   	/*style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
*/		    
		    HSSFRow row2 =sheet1.createRow(++rowNum);
		    
		  	if(dataName.length>0) {
		  		for(int c =0;c<dataName.length;c++) {
		    		HSSFCell cell1 =row2.createCell(c);
		    		sheet1.setColumnWidth(c, 23 * 256);
			    	if(dataType[c].equals("String")) {
				    	
				    	cell1.setCellValue((outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals(null)||outJson.opt(dataName[c].toString()).equals("")? "":outJson.optString(dataName[c].toString())));				    	
				    	
				    	cell1.setCellStyle(createCellStyle(workBook,"String"));
				    }else if(dataType[c].equals("0.00")){
				    	if(outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals("")||outJson.opt(dataName[c].toString()).equals(null)){
				    		cell1.setCellValue("0.00");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(dataName[c].toString()));
				    		
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0.00"));
				    }else if (dataType[c].equals("0.0")) {
				    	if(outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals("")||outJson.opt(dataName[c].toString()).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue( outJson.optDouble(dataName[c].toString()));
				    		 
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0.0"));
				    }else if (dataType[c].equals("0")) {
				    	if(outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals("")||outJson.opt(dataName[c].toString()).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optInt(dataName[c].toString()));
				    		 
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0"));
				    }else if(dataType[c].equals("0.00%")) {
				    	if(outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals("")||outJson.opt(dataName[c].toString()).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(dataName[c].toString()));
				    		 
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0.00%"));
				    }
			  }
		  	}
		   
	         
	        jin++;
		    }else {
		    	
			   //	HSSFCellStyle style1 = createCellStyle(workBook);
			  /* 	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
*/			    
			    HSSFRow row2 =sheet1.createRow(++rowNum);
			  	
			    if(dataName.length>0) {
			    	for(int c =0;c<dataName.length;c++) {
			    		HSSFCell cell1 =row2.createCell(c);
				    	if(dataType[c].equals("String")) {
					    	cell1.setCellValue((outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals(null)||outJson.opt(dataName[c].toString()).equals("")? "":outJson.optString(dataName[c].toString())));				    	
					    	
					    	cell1.setCellStyle(createCellStyle(workBook,"String"));
					    }else if(dataType[c].equals("0.00")){
					    	if(outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals("")||outJson.opt(dataName[c].toString()).equals(null)){
					    		cell1.setCellValue("0.00");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(dataName[c].toString()));
					    		
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0.00"));
					    }else if (dataType[c].equals("0.0")) {
					    	if(outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals("")||outJson.opt(dataName[c].toString()).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue( outJson.optDouble(dataName[c].toString()));
					    		 
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0.0"));
					    }else if (dataType[c].equals("0")) {
					    	if(outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals("")||outJson.opt(dataName[c].toString()).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optInt(dataName[c].toString()));
					    		 
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0"));
					    }else if(dataType[c].equals("0.00%")) {
					    	if(outJson.opt(dataName[c].toString())==null||outJson.opt(dataName[c].toString()).equals("")||outJson.opt(dataName[c].toString()).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(dataName[c].toString()));
					    		
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0.00%"));
					    }
				  }
			    }
		 }
		    result.put("rowNum", rowNum);
		    result.put("jin", jin);
		    return result;
	}
	
	public static JSONObject out1ObjectOne(HSSFWorkbook workBook,HSSFSheet sheet1,List<JSONObject> list1,JSONObject paramData) {
		  
		Integer rowNum =paramData.optInt("rowNum");
		JSONObject result =new JSONObject();
		JSONObject json = list1.get(0);
		
			HSSFRow row1 =sheet1.createRow(rowNum);
			HSSFCell cell1 =row1.createCell(0);
	    	cell1.setCellValue("账单数量");
	    	
			cell1 =row1.createCell(1);
	    	cell1.setCellValue(json.opt("bill_num") == null ? 0.00 : json.optDouble("bill_num") );
	    	
			cell1 =row1.createCell(2);
	    	cell1.setCellValue("消费客数:");
	    	
			cell1 =row1.createCell(3);
	    	cell1.setCellValue(json.opt("customer_num") == null ? 0.00 :json.optDouble("customer_num"));
	    	
	    	
	    	HSSFRow row2 =sheet1.createRow(rowNum+=1);
			cell1 =row2.createCell(0);
	    	cell1.setCellValue("营业应收:");
	    	
			cell1 =row2.createCell(1);
	    	cell1.setCellValue(json.opt("bill_amount") == null ? 0.00 :json.optDouble("bill_amount"));
	    	
			cell1 =row2.createCell(2);
	    	cell1.setCellValue("折扣金额:");
	    	
			cell1 =row2.createCell(3);
	    	cell1.setCellValue(json.opt("discountk_amount") == null ? 0.00 :json.optDouble("discountk_amount"));
	    	
	    	HSSFRow row3 =sheet1.createRow(rowNum+=1);
			cell1 =row3.createCell(0);
	    	cell1.setCellValue("营业实收:");
	    	
			cell1 =row3.createCell(1);
	    	cell1.setCellValue(json.opt("bill_real_amount") == null ? 0.00 :json.optDouble("bill_real_amount"));
	    	
	    	HSSFRow row4 =sheet1.createRow(rowNum+=1);
			cell1 =row4.createCell(0);
	    	cell1.setCellValue("开机找零金:	");
	    	
			cell1 =row4.createCell(1);
	    	cell1.setCellValue(json.opt("pos_change") == null ? 0.00 :json.optDouble("pos_change"));
	    	
			cell1 =row4.createCell(2);
	    	cell1.setCellValue("取大钞金额:");
	    	
			cell1 =row4.createCell(3);
	    	cell1.setCellValue(json.opt("pos_money") == null ? 0.00 :json.optDouble("pos_money"));
	    	
	    	HSSFRow row5 =sheet1.createRow(rowNum+=1);
			cell1 =row5.createCell(0);
	    	cell1.setCellValue("取消次数:");
	    	
			cell1 =row5.createCell(1);
	    	cell1.setCellValue(json.opt("cancel_num") == null ? 0.00 :json.optDouble("cancel_num"));
	    	
			cell1 =row5.createCell(2);
	    	cell1.setCellValue("取消金额:");
	    	
			cell1 =row5.createCell(3);
	    	cell1.setCellValue(json.opt("cancel_amount") == null ? 0.00 :json.optDouble("cancel_amount"));
	    	
	    	HSSFRow row6 =sheet1.createRow(rowNum+=1);
			cell1 =row6.createCell(0);
	    	cell1.setCellValue("退款次数:");
	    	
			cell1 =row6.createCell(1);
	    	cell1.setCellValue(json.opt("refund_num") == null ? 0.00 :json.optDouble("refund_num"));
	    	
			cell1 =row6.createCell(2);
	    	cell1.setCellValue("退款金额:");
	    	
			cell1 =row6.createCell(3);
	    	cell1.setCellValue(json.opt("refund_amount") == null ? 0.00 :json.optDouble("refund_amount"));
	    	
	    	HSSFRow row7 =sheet1.createRow(rowNum+=1);
			cell1 =row7.createCell(0);
	    	cell1.setCellValue("单均金额（总）:");
	    	
			cell1 =row7.createCell(1);
	    	cell1.setCellValue(json.opt("bill_avg_amount") == null ? 0.00 :json.optDouble("bill_avg_amount"));
	    	
			cell1 =row7.createCell(2);
	    	cell1.setCellValue("人均消费(总):");
	    	
			cell1 =row7.createCell(3);
	    	cell1.setCellValue(json.opt("customer_avg_amount") == null ? 0.00 :json.optDouble("customer_avg_amount"));
	    	
	    	HSSFRow row8 =sheet1.createRow(rowNum+=1);
			cell1 =row8.createCell(0);
	    	cell1.setCellValue("单均金额（净）:");
	    	
			cell1 =row8.createCell(1);
	    	cell1.setCellValue(json.opt("bill_avg_real_amount") == null ? 0.00 :json.optDouble("bill_avg_real_amount"));
	    	
			cell1 =row8.createCell(2);
	    	cell1.setCellValue("人均消费(净):");
	    	
			cell1 =row8.createCell(3);
	    	cell1.setCellValue(json.opt("customer_avg_real_amount") == null ? 0.00 :json.optDouble("customer_avg_real_amount"));
	    	
	    	result.put("rowNum", rowNum);
		   
	    	return result;
	}
	
	// 写大标题
	
	public static JSONObject outBigTitle(JSONObject json,HSSFSheet sheet1,HSSFWorkbook workBook) {
		JSONObject returnJson = new JSONObject() ;
		// 写大标题
	 	HSSFCell cell1 =sheet1.createRow(0).createCell(0);
	 	HSSFCellStyle setBorder = workBook.createCellStyle();
	 	setBorder.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中  
	 	setBorder.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
	 	
	 	HSSFFont font2 = workBook.createFont();
	 	font2.setFontName("仿宋_GB2312");
	 	font2.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
	 	font2.setFontHeightInPoints((short) 20);
	 	setBorder.setFont(font2);
	 	
	 	cell1.setCellStyle(setBorder);
	    cell1.setCellValue(json.optString("exportName"));
	    sheet1.addMergedRegion(new CellRangeAddress(0,3,0,3));
	    return returnJson ;
	}
	
	public static HSSFCellStyle getStyle1(HSSFWorkbook workBook) {
		HSSFCellStyle style1 =workBook.createCellStyle();
		style1 =workBook.createCellStyle();
	   	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
	    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
	    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
	    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
	    
	    return style1 ;
	}
	
	public static XSSFCellStyle getStyle03(XSSFWorkbook workBook) {
		XSSFCellStyle style1 =workBook.createCellStyle();
		style1 =workBook.createCellStyle();
	   	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
	    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
	    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
	    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
	    
	    return style1 ;
	}
	public static JSONObject out03(JSONObject outJson, XSSFWorkbook workBook,
			XSSFSheet sheet1, String[] listTitleName, String[] dataName,
			String[] dataType, JSONObject paramData) {
		// TODO Auto-generated method stub

		  
		Integer rowNum =paramData.optInt("rowNum");
		Integer jin =paramData.optInt("jin");
		JSONObject result =new JSONObject();
		    if(paramData.optInt("jin")==0){
	    	XSSFCellStyle style =workBook.createCellStyle();
	    	//设置前景的背景颜色
		    //style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		    //设置颜色 GREEN 可变
		    //style.setFillForegroundColor(HSSFColor.GREEN.index);
		    
		    style.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		    style.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		    style.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		    style.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
		    	
		    XSSFFont font =workBook.createFont();
		    //设置字体格式
		    font.setFontName("Aharoni");
		    font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		    style.setFont(font);
		   
		    int  strtIndex = 0;
		    //创建下标为0的一行 就是第一行  循环赋值表头名字
		    if(paramData.containsKey("strtIndex") ) {
		    	strtIndex=paramData.optInt("strtIndex");
		    }
		    XSSFRow row1 =sheet1.createRow(strtIndex);
		    if(listTitleName.length>0) {
		    	for(int y = 0;y<listTitleName.length;y++) {
			    	XSSFCell cell =row1.createCell(y);
				    cell.setCellStyle(style);
				    cell.setCellValue(listTitleName[y]);
				    
			    }
		    }
		    
	        XSSFCellStyle style1 =workBook.createCellStyle();
		   	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
		    
		    XSSFRow row2 =sheet1.createRow(rowNum++);
		    
		  	if(dataName.length>0) {
		  		for(int c =0;c<dataName.length;c++) {
		    		XSSFCell cell1 =row2.createCell(c);
			    	if(dataType[c].equals("String")) {
				    	cell1.setCellValue((outJson.opt(dataName[c]).equals(null)||outJson.optString(dataName[c]).equals("")? "":outJson.optString(dataName[c])));				    	
				    	style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("General")); 
				    	cell1.setCellStyle(style1);
				    }else if(dataType[c].equals("0.00")){
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("0.00");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(dataName[c]));
				    		style1 = getStyle03(workBook);
				    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00")); 
				    	}
					    cell1.setCellStyle(style1);
				    }else if (dataType[c].equals("0.0")) {
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue( outJson.optDouble(dataName[c]));
				    		style1 = getStyle03(workBook);
				    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.0")); 
				    	}
					    cell1.setCellStyle(style1);
				    }else if (dataType[c].equals("0")) {
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optInt(dataName[c]));
				    		style1 = getStyle03(workBook);
				    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0")); 
				    	}
					    cell1.setCellStyle(style1);
				    }else if(dataType[c].equals("0.00%")) {
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(dataName[c]));
				    		style1 = getStyle03(workBook);
				    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00%")); 
				    	}
					    cell1.setCellStyle(style1);
				    }
			  }
		  	}
		   
	         
	        jin++;
		    }else {
		    	
			   	XSSFCellStyle style1 =workBook.createCellStyle();
			   	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
			    
			    XSSFRow row2 =sheet1.createRow(rowNum++);
			  	
			    if(dataName.length>0) {
			    	for(int c =0;c<dataName.length;c++) {
			    		XSSFCell cell1 =row2.createCell(c);
				    	if(dataType[c].equals("String")) {
					    	cell1.setCellValue((outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals(null)||outJson.optString(dataName[c]).equals("")? "":outJson.optString(dataName[c])));				    	
					    	style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("General")); 
					    	cell1.setCellStyle(style1);
					    }else if(dataType[c].equals("0.00")){
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("0.00");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(dataName[c]));
					    		style1 = getStyle03(workBook);
					    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00")); 
					    	}
						    cell1.setCellStyle(style1);
					    }else if (dataType[c].equals("0.0")) {
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue( outJson.optDouble(dataName[c]));
					    		style1 = getStyle03(workBook);
					    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.0")); 
					    	}
						    cell1.setCellStyle(style1);
					    }else if (dataType[c].equals("0")) {
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optInt(dataName[c]));
					    		style1 = getStyle03(workBook);
					    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0")); 
					    	}
						    cell1.setCellStyle(style1);
					    }else if(dataType[c].equals("0.00%")) {
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(dataName[c]));
					    		style1 = getStyle03(workBook);
					    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00%")); 
					    	}
						    cell1.setCellStyle(style1);
					    }
				  }
			    }
		 }
		    result.put("rowNum", rowNum);
		    result.put("jin", jin);
		    return result;
	
	}
	
	/**
	 * 
	 * @param workBook work类
	 * @param sheet1  sheet 表
	 * @param payListObject  动态拼接的List<JSOBObject>
	 * @param rowtitle        要写入的行
	 * @param titleEnd1       动态拼接的开始下标
	 * @param dataName 根据那个字段划分区间   列：dataName =payment_class
	 * [{"pname":"t1","zname":"本系统卡","payment_class":"本系统卡","title":"本系统卡"}, 29  29 30 30 
  		{"pname":"t1014","zname":"美团外卖付款","payment_class":"第三方","title":"美团外卖付款"},  30  31 
   		{"pname":"t1015","zname":"美团验券支付","payment_class":"第三方","title":"美团验券支付"},
	 * @return
	 */
	 public static HSSFSheet activeGoData(HSSFWorkbook workBook,HSSFSheet sheet1,List<JSONObject> payListObject,HSSFRow rowtitle,Integer titleEnd1,String dataName) {
		String titleCan =null;
		Integer titleIndex =titleEnd1;
		Integer titleEnd =titleEnd1;
		if(payListObject.size()>0) {
		for(int cc = 0 ;cc<payListObject.size();cc++) {
			if(titleCan==null) {
				titleCan =payListObject.get(cc).optString(dataName);
				continue;
			}
			if(!titleCan.equals(payListObject.get(cc).optString(dataName))) {
				HSSFCell cellTitle5 = rowtitle.createCell(titleIndex);
				cellTitle5.setCellValue(payListObject.get(cc-1).optString(dataName));
				cellTitle5.setCellStyle(getTitleStyle(workBook));
				if(titleIndex!=titleEnd) {
					sheet1.addMergedRegion(new CellRangeAddress(0,0,titleIndex,titleEnd));
				}
				titleEnd++;
				titleIndex=titleEnd;
				titleCan=payListObject.get(cc).optString(dataName);
			}else {
				titleEnd++;
			}
			if((cc+1)==payListObject.size()) {
				HSSFCell cellTitle5 = rowtitle.createCell(titleIndex);
				cellTitle5.setCellValue(payListObject.get(cc).optString(dataName));
				cellTitle5.setCellStyle(getTitleStyle(workBook));
				 sheet1.addMergedRegion(new CellRangeAddress(0,0,titleIndex,titleEnd));
			}
		}
		}
		return sheet1;
	} 
	/**
	 *   该方法只是单纯的上下合并  不支持跨列 只支持 一列
	 * @param workBook work类
	 * @param sheet1   sheet表
	 * @param valueNum 需要第几列上下合  
	 * @param rowtitle2 要合并的数据行数  就好比取数据 
	 * @param rowtitle 要合到的数据行数  放数据   因为 合并单元格的时候  只识别左上角的数值
	 * @param heightStrat 要合并的开始行  例如  heightStrat = 0  heightEnd =1 结果合并一行
	 * @param heightEnd   结束行数
	 * @return
	 */
	public static HSSFSheet upOrDownMergr(HSSFWorkbook workBook,HSSFSheet sheet1, Integer[] valueNum,HSSFRow rowtitle2 , HSSFRow rowtitle,Integer heightStrat ,Integer heightEnd) {
		
			for(Integer value :valueNum) {
				String cellValue = rowtitle2.getCell(value).getStringCellValue();
				HSSFCell createCell = rowtitle.createCell(value);
				createCell.setCellValue(cellValue);
				createCell.setCellStyle(getTitleStyle(workBook));
				sheet1.addMergedRegion(new CellRangeAddress(heightStrat,heightEnd,value,value));
				 
			}
			return sheet1;
	}
	
	/**
	 * 获取合并的表头的titleStyle的 样式
	 * @param workBook
	 * @return
	 */
	public static HSSFCellStyle getTitleStyle(HSSFWorkbook workBook) {
		HSSFCellStyle style1 = workBook.createCellStyle(); 
		//	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			style1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直      
            style1.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平    
			    HSSFFont font1 =workBook.createFont();
			    //设置字体格式
			    font1.setFontName("Aharoni");
			    font1.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
			style1.setFont(font1);
		return style1;
	}
	
	/**
	 * 
	 * @param workBook
	 * @param sheet1
	 * @param rowtitle 需要（到）写入的行类
	 * @param titleArr Json集合 json 里面 包含 titleName 头名字 index 起始列树 end 结束列数
	 * @return
	 */
	public static HSSFSheet mergrColumn (HSSFWorkbook workBook,HSSFSheet sheet1,HSSFRow rowtitle,List<JSONObject> titleArr) {
		for(JSONObject titleJson:titleArr) {
			HSSFCell cellTitle = rowtitle.createCell(titleJson.optInt("index"));
			cellTitle.setCellValue(titleJson.optString("titleName"));
			cellTitle.setCellStyle(getTitleStyle(workBook));
			sheet1.addMergedRegion(new CellRangeAddress(0,0,titleJson.optInt("index"),titleJson.optInt("end")));
		}
	return sheet1;
	}
	
	/**
	 * 新版的 拼接表头 上下左后合并相同内容的单元格
	 *  
		返回参数:rowNum 当前表头写到了第几行 ； 在血拼接数据的时候  要沿用当前的 rowNum 写
	*/
	public static JSONObject titleActivity(JSONObject json,HSSFSheet sheet1,HSSFWorkbook workBook) {
		JSONArray ArrHeader = json.optJSONObject("rowcolumns").optJSONArray("val");
		 // 1： 先把数值写到sheet里面
		 JSONObject out2 = out2(workBook,sheet1,ArrHeader);
		 // 写大标题
		 	HSSFCell cell1 =sheet1.createRow(0).createCell(0);
		 	HSSFCellStyle setBorder = workBook.createCellStyle();
		 	/*setBorder.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		 	setBorder.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		 	setBorder.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		 	setBorder.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
*/		 	
		 	setBorder.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中  
		 	setBorder.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
		 	
		 	HSSFFont font2 = workBook.createFont();
		 	font2.setFontName("仿宋_GB2312");
		 	font2.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
		 	font2.setFontHeightInPoints((short) 20);
		 	setBorder.setFont(font2);
		 	
		 	cell1.setCellStyle(setBorder);
		    cell1.setCellValue(json.optString("exportName"));
		    
		    sheet1.addMergedRegion(new CellRangeAddress(0,3,0,ArrHeader.getJSONArray(0).size()-1));
		     // 根据3层的特殊性 当
		    if (ArrHeader.size()==3) {
		    	out2.put("rowNum", out2.getInt("rowNum")-1);
		    }
		 // 2: 循环三个row 分别获取第一行数据 开始合并列各行的列
		 for(int i =0;i<ArrHeader.size();i++) {
			 //HSSFRow row = sheet1.getRow(i);
			 String titleText =null;
			 Integer titleInteger =null;
			 for(int y = 0 ; y<ArrHeader.getJSONArray(i).size() ; y++){
				 if(y==0) {
					 titleText = ArrHeader.getJSONArray(i).getString(y);
					 titleInteger = y ;
				 }else {
					 if(!titleText.equals(ArrHeader.getJSONArray(i).getString(y))) {
						 if(titleInteger!=y-1) {
							 sheet1.addMergedRegion(new CellRangeAddress(i+out2.optInt("rowNum")-2,i+out2.optInt("rowNum")-2,titleInteger,y-1));
						 }
						 titleText = ArrHeader.getJSONArray(i).getString(y);
						 titleInteger = y ; 
					 } 
					 if(y==ArrHeader.getJSONArray(i).size()-1) {
						 if(titleInteger != y) {
							 sheet1.addMergedRegion(new CellRangeAddress(i+out2.optInt("rowNum")-2,i+out2.optInt("rowNum")-2,titleInteger,y));
						 }
					 }
				 }
			 }
		 }
		 // 3：循环三个row 开始合并行
		 HSSFRow row1 =null;
		 HSSFRow row2 =null;
		 HSSFRow row3 =null;
		 if(ArrHeader.size()==1) {
			 row1 =sheet1.getRow(out2.optInt("rowNum")-1);
		 }else if (ArrHeader.size()==2) {
			 row1 =sheet1.getRow(out2.optInt("rowNum")-2);
			 row2 =sheet1.getRow(out2.optInt("rowNum")-1);
		 }else if (ArrHeader.size()==3) {
	 		 row1 =sheet1.getRow(out2.optInt("rowNum")-2);
			 row2 =sheet1.getRow(out2.optInt("rowNum")-1);
			 row3 =sheet1.getRow(out2.optInt("rowNum"));
		 }
		 String row1Text =null;
		 if(ArrHeader.getJSONArray(0)!=null){
			 for(int i = 0 ; i<ArrHeader.getJSONArray(0).size() ; i++) {
				 row1Text = row1.getCell(i).getStringCellValue();
				 if(row2!=null) {
					 if(row1Text.equals(row2.getCell(i).getStringCellValue())) {
						 if(row3!=null) {
							 if(row3.getCell(i).getStringCellValue().equals(row2.getCell(i).getStringCellValue())) {
								 sheet1.addMergedRegion(new CellRangeAddress(out2.optInt("rowNum")-2,out2.optInt("rowNum"),i,i));
							 }else{
								 sheet1.addMergedRegion(new CellRangeAddress(0+out2.optInt("rowNum")-2,out2.optInt("rowNum")-1,i,i));
							 } 
							 continue;
						 }else{
							 sheet1.addMergedRegion(new CellRangeAddress(0+out2.optInt("rowNum")-2,out2.optInt("rowNum")-1,i,i));
						 }  
					 }
					 if(row3!=null) {
						 if(row3.getCell(i).getStringCellValue().equals(row2.getCell(i).getStringCellValue())) {
							 sheet1.addMergedRegion(new CellRangeAddress(0+out2.optInt("rowNum")-1,out2.optInt("rowNum"),i,i));
						 } 
					 }
				 }else {
					 break;
				 }
				 
				 
			 }
		 }
		 if (ArrHeader.size()==3) {
		    	out2.put("rowNum", out2.getInt("rowNum")+1);
		    }
		 
		 // 表头里面是否包含 门店信息
		 JSONObject customExprtParameter = customExprtParameter( json, sheet1, workBook,out2);
		 out2.put("rowNum", customExprtParameter.getInt("rowNum"));
		 return out2;
	}
	
	/**\
	 * 增加自定义标识
	 * @param data
	 * @param sheet1
	 * @param workBook
	 * @param json
	 * @return
	 */
	public  static JSONObject customExprtParameter (JSONObject data ,HSSFSheet sheet1,HSSFWorkbook workBook,JSONObject json) {
		Integer maxRowNum = json.optInt("rowNum") ;
		if(data.containsKey("customExprtParameter")) {
				maxRowNum = json.optInt("rowNum")+1;
			HSSFCellStyle setBorder2 = workBook.createCellStyle(); 
	        setBorder2.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中  
		 	setBorder2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
	        
	        HSSFFont font3 = workBook.createFont();
		 	font3.setFontName("仿宋_GB2312");
		 	font3.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
		 	font3.setFontHeightInPoints((short) 10);
		 	setBorder2.setFont(font3);
			
			JSONObject jsonparameter = JSONObject.fromObject(data.getString("customExprtParameter"));
			  Iterator keys = jsonparameter.keys();
			  
			  
			  sheet1.setColumnWidth(0, 23 * 256); // 设置宽度
			  sheet1.setColumnWidth(1, 23 * 256); // 设置宽度
			  
			  while(keys.hasNext()){
				  String k = keys.next().toString();
				  HSSFRow row =sheet1.createRow(maxRowNum);
				  HSSFCell createCell = row.createCell(0);
				  createCell.setCellValue(k);
				  createCell.setCellStyle(setBorder2);
				  row.createCell(1).setCellValue(jsonparameter.optString(k));
				  maxRowNum++;
			  }
		}
		json.put("rowNum", maxRowNum);
		return json;
	}
	
	public static JSONObject out2( HSSFWorkbook workBook,HSSFSheet sheet1,JSONArray ArrHeader) {
		  
			Integer rowNum = 4 ;
			JSONObject result =new JSONObject();
			
	    	// 单例模式
	    	HSSFCellStyle style =getTitleStyle( workBook); ;
		   if(ArrHeader!=null) {
			    for(int i = 0 ; i<ArrHeader.size() ; i++) {
			    	JSONArray title =ArrHeader.getJSONArray(i);
			    		i=i+4;
			    	HSSFRow row =sheet1.createRow(i);
			    		i=i-4;
			    	for(int y = 0;y<title.size();y++) {
				    	HSSFCell cell =row.createCell(y);
					    cell.setCellStyle(style);
					    cell.setCellValue(title.getString(y));
				    }
			    	rowNum++;
			    }
		   }
		    result.put("rowNum", rowNum);
		    return result;
	}
	
	/**
	 * 
	 * @param json
	 * man 制作人
	 * rowNum 当前第几行
	 */
	public static void setManAndTimeOrder(HSSFWorkbook workBook,HSSFSheet sheet1,JSONObject json,JSONObject data,JSONObject footer_jsonobject) {
		JSONArray ArrHeader = data.optJSONObject("rowcolumns").optJSONArray("val");
		Integer sizeData = null;
		if(ArrHeader.getJSONArray(0)!=null) {
			 sizeData = ArrHeader.getJSONArray(0).size();
		}
		HSSFRow row =sheet1.createRow(json.optInt("rowNum")+2);
		HSSFRow total_row =sheet1.createRow(json.optInt("rowNum"));
		Date d = new Date();  
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
        String dateNowStr = sdf.format(d);  
        
        HSSFCellStyle total_setBorder = workBook.createCellStyle(); 
        total_setBorder.setAlignment(HSSFCellStyle.ALIGN_RIGHT);//水平居中  
        total_setBorder.setVerticalAlignment(HSSFCellStyle.ALIGN_RIGHT);//垂直居中
        total_setBorder.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
	 	
	 	
        HSSFCellStyle setBorder = workBook.createCellStyle(); 
        setBorder.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中  
	 	setBorder.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
	 	setBorder.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
        
        HSSFFont font2 = workBook.createFont();
	 	font2.setFontName("仿宋_GB2312");
	 	//font2.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
	 	font2.setColor(HSSFColor.RED.index);
	 	//font2.setFontHeightInPoints((short) 13);
	 	setBorder.setFont(font2);
	 	
	 	 HSSFCellStyle setBorder2 = workBook.createCellStyle(); 
	        setBorder2.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中  
		 	setBorder2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
	        
	        HSSFFont font3 = workBook.createFont();
		 	font3.setFontName("仿宋_GB2312");
		 	font3.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
		 	font3.setFontHeightInPoints((short) 13);
		 	setBorder2.setFont(font3);
	 	
		if(sizeData>8) {
			
			HSSFCell totalCell = total_row.createCell(sizeData-22);
			totalCell.setCellValue("合计:");
			
			HSSFCell total_money = total_row.createCell(sizeData-17);
			total_money.setCellValue(footer_jsonobject.optString("total_money"));
			total_money.setCellStyle(total_setBorder);
			
			HSSFCell total_money_nomeal = total_row.createCell(sizeData-16);
			total_money_nomeal.setCellValue(footer_jsonobject.optString("total_money_nomeal"));
			total_money_nomeal.setCellStyle(total_setBorder);
			
			HSSFCell product_org_total_fee = total_row.createCell(sizeData-15);
			product_org_total_fee.setCellValue(footer_jsonobject.optString("product_org_total_fee"));
			product_org_total_fee.setCellStyle(total_setBorder);
			
			HSSFCell package_box_fee = total_row.createCell(sizeData-14);
			package_box_fee.setCellValue(footer_jsonobject.optString("package_box_fee"));
			package_box_fee.setCellStyle(total_setBorder);
			
			HSSFCell meal_costs = total_row.createCell(sizeData-13);
			meal_costs.setCellValue(footer_jsonobject.optString("meal_costs"));
			meal_costs.setCellStyle(total_setBorder);
			
			HSSFCell discountk_amount = total_row.createCell(sizeData-12);
			discountk_amount.setCellValue(footer_jsonobject.optString("discountk_amount"));
			discountk_amount.setCellStyle(total_setBorder);
			
			HSSFCell discountr_amount = total_row.createCell(sizeData-11);
			discountr_amount.setCellValue(footer_jsonobject.optString("discountr_amount"));
			discountr_amount.setCellStyle(total_setBorder);
			
			HSSFCell discount_amount = total_row.createCell(sizeData-10);
			discount_amount.setCellValue(footer_jsonobject.optString("discount_amount"));
			discount_amount.setCellStyle(total_setBorder);
			
			HSSFCell platform_rate = total_row.createCell(sizeData-9);
			platform_rate.setCellValue(footer_jsonobject.optString("platform_rate"));
			platform_rate.setCellStyle(total_setBorder);
			
			HSSFCell shop_rate = total_row.createCell(sizeData-8);
			shop_rate.setCellValue(footer_jsonobject.optString("shop_rate"));
			shop_rate.setCellStyle(total_setBorder);
			
			HSSFCell maling_amount = total_row.createCell(sizeData-7);
			maling_amount.setCellValue(footer_jsonobject.optString("maling_amount"));
			maling_amount.setCellStyle(total_setBorder);
			
			HSSFCell more_coupon = total_row.createCell(sizeData-6);
			more_coupon.setCellValue(footer_jsonobject.optString("more_coupon"));
			more_coupon.setCellStyle(total_setBorder);

			HSSFCell actual_money = total_row.createCell(sizeData-5);
			actual_money.setCellValue(footer_jsonobject.optString("actual_money"));
			actual_money.setCellStyle(total_setBorder);
			
			HSSFCell shop_fee = total_row.createCell(sizeData-4);
			shop_fee.setCellValue(footer_jsonobject.optString("shop_fee"));
			shop_fee.setCellStyle(total_setBorder);
			
			HSSFCell commission_amount = total_row.createCell(sizeData-3);
			commission_amount.setCellValue(footer_jsonobject.optString("commission_amount"));
			commission_amount.setCellStyle(total_setBorder);
			
			HSSFCell shop_real_amount = total_row.createCell(sizeData-2);
			shop_real_amount.setCellValue(footer_jsonobject.optString("shop_real_amount"));
			shop_real_amount.setCellStyle(total_setBorder);
			
			HSSFCell createCell = row.createCell(sizeData-8);
			createCell.setCellValue("制表人:");
			
			HSSFCell createCell2 = row.createCell(sizeData-7);
			createCell2.setCellValue(json.optString("man"));
			createCell2.setCellStyle(setBorder);
			
			HSSFCell createCell3 = row.createCell(sizeData-5);
			createCell3.setCellValue("制作时间:");
			
			HSSFCell createCell4 = row.createCell(sizeData-4);
			createCell4.setCellValue(dateNowStr);
			createCell4.setCellStyle(setBorder);
		}else {
			row.createCell(0).setCellValue("制作人:");
			row.createCell(1).setCellValue(json.optString("man"));
			row.createCell(2).setCellValue("制作时间:");
			row.createCell(3).setCellValue(dateNowStr);
		}
		// 自定义导出数据 
		/*if(data.containsKey("customExprtParameter")) {
			JSONObject jsonparameter = data.getJSONObject("customExprtParameter");
			  Iterator keys = jsonparameter.keys();
			  Integer maxRowNum = json.optInt("rowNum")+3 ;
			  while(keys.hasNext()){
				  String k = keys.next().toString();
				  row =sheet1.createRow(maxRowNum);
				  row.createCell(0).setCellValue(k);
				  row.createCell(1).setCellValue(jsonparameter.optString(k));
				  maxRowNum++;
			  }
		}*/
		
	}
	/**
	 * 
	 * @param json
	 * man 制作人
	 * rowNum 当前第几行
	 */
	public static void setManAndTime(HSSFWorkbook workBook,HSSFSheet sheet1,JSONObject json,JSONObject data,List<JSONObject> list,List<JSONObject> structure,JSONObject footer) {
		JSONArray ArrHeader = data.optJSONObject("rowcolumns").optJSONArray("val");
		Integer sizeData = null;
		if(ArrHeader.getJSONArray(0)!=null) {
			 sizeData = ArrHeader.getJSONArray(0).size();
		}
		HSSFRow row = null;
		Date d = new Date();  
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
        String dateNowStr = sdf.format(d);  
        
        HSSFCellStyle setBorder = workBook.createCellStyle(); 
        setBorder.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中  
	 	setBorder.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        
        HSSFFont font2 = workBook.createFont();
	 	font2.setFontName("仿宋_GB2312");
	 	//font2.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
	 	font2.setColor(HSSFColor.RED.index);
	 	//font2.setFontHeightInPoints((short) 13);
	 	setBorder.setFont(font2);
	 	
	 	 HSSFCellStyle setBorder2 = workBook.createCellStyle(); 
	        setBorder2.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中  
		 	setBorder2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
	        
	        HSSFFont font3 = workBook.createFont();
		 	font3.setFontName("仿宋_GB2312");
		 	font3.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
		 	font3.setFontHeightInPoints((short) 13);
		 	setBorder2.setFont(font3);
		 	
		//导出合计行	
		if(data.containsKey("total_line") && data.optInt("total_line")==1){
			if(footer.size()>0){
				HSSFCell createCell = null;
				//求出导出最大得条数+1
				row = sheet1.createRow(json.optInt("rowNum")+1);
				ExportUtils.HSSFCell(createCell, list, structure, footer, workBook, sheet1, row);
				
				//此处为了后面加上制表人
				row = sheet1.createRow(json.optInt("rowNum")+3);
			}
			//ExportUtils.HSSFCell(cell1, objs, structure, outJson, workBook, sheet1, row2);
		}else{
			row = sheet1.createRow(json.optInt("rowNum")+2);
		}
	 	
		if(sizeData>8) {
			
			HSSFCell createCell = row.createCell(sizeData-8);
			createCell.setCellValue("制表人:");
			
			
			HSSFCell createCell2 = row.createCell(sizeData-7);
			createCell2.setCellValue(json.optString("man"));
			createCell2.setCellStyle(setBorder);
			
			HSSFCell createCell3 = row.createCell(sizeData-5);
			createCell3.setCellValue("制作时间:");
			
			HSSFCell createCell4 = row.createCell(sizeData-4);
			createCell4.setCellValue(dateNowStr);
			createCell4.setCellStyle(setBorder);
		}else {
			row.createCell(0).setCellValue("制作人:");
			row.createCell(1).setCellValue(json.optString("man"));
			row.createCell(2).setCellValue("制作时间:");
			row.createCell(3).setCellValue(dateNowStr);
		}
		// 自定义导出数据 
		/*if(data.containsKey("customExprtParameter")) {
			JSONObject jsonparameter = data.getJSONObject("customExprtParameter");
			  Iterator keys = jsonparameter.keys();
			  Integer maxRowNum = json.optInt("rowNum")+3 ;
			  while(keys.hasNext()){
				  String k = keys.next().toString();
				  row =sheet1.createRow(maxRowNum);
				  row.createCell(0).setCellValue(k);
				  row.createCell(1).setCellValue(jsonparameter.optString(k));
				  maxRowNum++;
			  }
		}*/
		
	}
	
	
	/**
	 * 竖版导出 （收银员交班报表导出）
	 * 
	 * 表头 : outJson = {feildnamecha:"+菜品优惠金额",payment_amount:52,
	 * 				  feildnamechawidth:1,payment_amountwidth:2,
	 * 				  feildnamechastyle:title,payment_amountstyle:content}
	 */
	
	public static JSONObject outJson(JSONObject outJson,HSSFWorkbook workBook,HSSFSheet sheet1
			,JSONObject paramData,
			String[] fieldArr
			 ) {
		
		Integer rowNum =paramData.optInt("rowNum");
		HSSFRow createRow = sheet1.createRow(rowNum);
		if(rowNum.equals(0)) {
			createRow.setHeight((short) 1000);
		}
		//
		sheet1.setColumnWidth((short) 0, (short) 5000);
		
		// 获取表头样式
    	HSSFCellStyle titleStyle = getTitleStyle(workBook);
    	HSSFCell cell = null;
    	
    	for (int i = 0; i < fieldArr.length; i++) {
    		
    		
    		cell = createRow.createCell(i);
    		
    		// 居右
    		cell.setCellType(HSSFCellStyle.ALIGN_RIGHT);
    		
    		if(outJson.optString("feildnamecha").equals("上座率") || outJson.optString("feildnamecha").equals("翻台率")){
    			if(outJson.optString(fieldArr[i]).equals("null")|| outJson.optString(fieldArr[i]).equals("")||outJson.optString(fieldArr[i]).equals("0")){
	    			cell.setCellValue("0.00%");
	    		}else if(fieldArr[i].equals("feildnamecha")){
	    			cell.setCellValue(outJson.optString(fieldArr[i]));
	    		}else if(fieldArr[i].equals("feildval")){
	    			DecimalFormat decimalFormat=new DecimalFormat("0.00");//构造方法的字符格式这里如果小数不足2位,会以0补足.
	    			//format 返回的是字符串
	    			cell.setCellValue(decimalFormat.format(outJson.optDouble(fieldArr[i])*100)+"%");
	    			//cell.setCellValue(outJson.optDouble(fieldArr[i])*100+"%");
	    		}else{
	    			cell.setCellValue("");
	    		}
			}else if(outJson.optString("feildnamecha").equals("开始日期") || outJson.optString("feildnamecha").equals("结束日期") || outJson.optString("feildnamecha").equals("交易日期") || outJson.optString("feildnamecha").equals("交易门店") || outJson.optString("feildnamecha").equals("交易班次") || outJson.optString("feildnamecha").equals("收银人员")){
				if(outJson.optString(fieldArr[i]).equals("null")|| outJson.optString(fieldArr[i]).equals("")||outJson.optString(fieldArr[i]).equals("0")){
	    			cell.setCellValue("0.00");
	    		}else if(fieldArr[i].equals("feildnamecha")){
	    			cell.setCellValue(outJson.optString(fieldArr[i]));
	    		}else if(fieldArr[i].equals("feildval")){
	    			cell.setCellValue(outJson.optString(fieldArr[i]));
	    		}else{
	    			cell.setCellValue("");
	    		}
			}else{
				if(outJson.optString(fieldArr[i]).equals("null")|| outJson.optString(fieldArr[i]).equals("")||outJson.optString(fieldArr[i]).equals("0")){
	    			cell.setCellValue("0.00");
	    		}else if(fieldArr[i].equals("feildnamecha")){
	    			cell.setCellValue(outJson.optString(fieldArr[i]));
	    		}else if(fieldArr[i].equals("feildval")){
	    			DecimalFormat decimalFormat=new DecimalFormat("0.00");//构造方法的字符格式这里如果小数不足2位,会以0补足.
	    			//format 返回的是字符串
	    			cell.setCellValue(decimalFormat.format(outJson.optDouble(fieldArr[i])));
	    		}else{
	    			cell.setCellValue("");
	    		}
			}
    		
    		if(outJson.containsKey(fieldArr[i]+"style") && outJson.optString(fieldArr[i]+"style").equals("title")) {
    			cell.setCellStyle(titleStyle);
    		}else{
    			cell.setCellStyle(createCellStyle(workBook,"String"));
    		}
    		
    		if(outJson.containsKey(fieldArr[i]+"width")) {
    			if(i<outJson.getInt(fieldArr[i]+"width")) {
	    			for( Integer strat = i+1; strat<=outJson.getInt(fieldArr[i]+"width") ; strat++) {
	    				createRow.createCell(strat).setCellStyle(createCellStyle(workBook,"String"));
	    			}
    			}
    			sheet1.addMergedRegion(new CellRangeAddress(rowNum,rowNum,i,outJson.getInt(fieldArr[i]+"width")));
    		}
    		
		}
    	paramData.put("rowNum", rowNum+1);
    	return paramData;
	}
		
}
