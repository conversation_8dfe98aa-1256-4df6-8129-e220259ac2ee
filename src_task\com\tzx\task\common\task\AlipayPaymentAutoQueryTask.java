package com.tzx.task.common.task;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.news.cache.PaymentUrlCache;
import com.tzx.payment.news.cont.Contant;
import com.tzx.payment.news.cont.StatusConstant;
import com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl;
import com.tzx.payment.news.service.impl.AlipayPaymentServiceImpl;
import com.tzx.payment.news.util.PaymentJsonUtils;
import com.tzx.payment.news.util.PaymentRedisCache;
import com.tzx.payment.news.util.PaymentUtils;
import com.tzx.payment.news.util.ThreadPool;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

public class AlipayPaymentAutoQueryTask implements Runnable{

	private static final Logger		logger	= Logger.getLogger(AlipayPaymentAutoQueryTask.class);

    @Resource(name="com.tzx.payment.news.service.impl.AlipayPaymentServiceImpl")
    private AlipayPaymentServiceImpl aliPayMentService;

    @Resource(name="com.tzx.payment.news.dao.impl.AlipayPaymentDaoImpl")
    private AlipayPaymentDaoImpl paymentDao;


	@Override
	public void run() {
//        logger.info("Alipay 自动查询开始：");
        try {
            autoQuery();
        } catch (Exception e) {
            logger.error(e);
//            logger.error("自动轮训出现异常");
        }
//        logger.info("Alipay 自动查询结束：");

	}

	public void autoQuery(){
        ThreadPoolExecutor threadPool = ThreadPool.getThreadPool();
        List<JSONObject> ali_pay = PaymentRedisCache.getOrderInfoByType("ali_pay");
        if(ali_pay==null) {
            return;
        }

        for(JSONObject json :ali_pay) {
            if(isExecute(json)) {
                logger.info("支付宝轮询开启一个新的线程，out_trade_no为"+json.optString("out_trade_no"));
                final JSONObject json2 = json;
                try {
                    Runnable a = new Runnable(){
                        @Override
                        public void run() {
                            try{
                                JSONObject saveJson = queryDataJson(json2);
                                AlipayTradeQueryResponse aliResponse = reqAli(saveJson);
                                process(saveJson,aliResponse);
                                aliPayMentService.send2Md(saveJson, "【支付宝】");
                            }catch (Exception e) {
                                logger.error(e);
                            }
                        }
                    };
                    threadPool.execute(a);

                } catch (Exception e) {
                    String out_trade_info = PaymentRedisCache.getInfoByOutTradeNo(json.optString("out_trade_no"));
                    PaymentRedisCache.lPushOutTradeNo2Info(json.optString("pay_type"), out_trade_info);
                    logger.error(e);
                    continue;
                }
            } else {
                String out_trade_info = PaymentRedisCache.getInfoByOutTradeNo(json.optString("out_trade_no"));
                PaymentRedisCache.lPushOutTradeNo2Info(json.optString("pay_type"), out_trade_info);
            }
        }
    }

    private void process(JSONObject saveJson,AlipayTradeQueryResponse aliResponse) throws Exception {
        String tradeStatus = aliResponse.getTradeStatus();
        logger.info("支付宝查询结果："+aliResponse.toString());
        if(StringUtils.equals("TRADE_SUCCESS", tradeStatus) ||  StringUtils.equals("TRADE_FINISHED", tradeStatus) ) {
            saveJson.put("status", StatusConstant.STATUS_SUCCESS);
            if(saveJson.optInt("final_state")==StatusConstant.STATUS_PROGRESS) {
                saveJson.put("final_state", StatusConstant.STATUS_SUCCESS);
                saveJson.put("finish_time", PaymentUtils.currentTime2Str());
            }
        } else{
            return;
        }

        String body = aliResponse.getBody();
        com.alibaba.fastjson.JSONObject responseAliJson = com.alibaba.fastjson.JSONObject
                .parseObject(body);
        JSONObject responseJson = PaymentJsonUtils.convertAli2netJson(responseAliJson);
        
        JSONObject jsonObject = responseJson.optJSONObject("alipay_trade_query_response");
        aliPayMentService.addAssist(saveJson,jsonObject);
        String out_trade_no = saveJson.optString("out_trade_no");
        String tenancyId = saveJson.optString("tenancy_id");
        paymentDao.updateIgnorCase(tenancyId,"pos_payment_order",saveJson);

        List<JSONObject> list = paymentDao.queryOrderDetailsByOutTradeNo(tenancyId, out_trade_no);
        if(!list.isEmpty()) {
            PaymentRedisCache.deleteInfoByOutTradeNo(out_trade_no);
            return;
        }
        aliPayMentService.saveDetails(jsonObject,jsonObject.optJSONArray("fund_bill_list"));
        PaymentRedisCache.deleteInfoByOutTradeNo(out_trade_no);
    }

    /**
     * 查询本地的信息
     * @param json
     * @return
     * @throws Exception
     */
    private JSONObject queryDataJson(JSONObject json) throws Exception {
        String out_trade_no = json.optString("out_trade_no");
        String tenancy_id = json.optString("tenancy_id");
        if(StringUtils.isBlank(tenancy_id)) {
            logger.info("支付宝轮训查询时redis中没找到out_trade_no="+out_trade_no+"所对应的信息");
            return null;
        }
        DBContextHolder.setTenancyid(tenancy_id);
        JSONObject jsonObject = paymentDao.queryOrderByOutTradeNo2Recently(tenancy_id, out_trade_no);
        return jsonObject;
    }

    /**
     * 请求阿里服务器
     * @param saveJson
     * @return
     * @throws Exception
     */
    private AlipayTradeQueryResponse reqAli(JSONObject saveJson) throws Exception {
        String out_trade_no = saveJson.optString("out_trade_no");
        String tenancy_id = saveJson.optString("tenancy_id");
        int store_id = saveJson.optInt("store_id");
        JSONObject reqJson = new JSONObject();
        reqJson.put("out_trade_no",out_trade_no);
        JSONObject paymentAccountConfig = paymentDao.getPaymentAccountConfig(tenancy_id, store_id
                , "ali_pay",saveJson.optString("service_type"));
        if(paymentAccountConfig==null) {
            logger.error("没有机构所对应的 秘钥、 商户号信息,机构ID为"+store_id+"商户id为："+tenancy_id);
        }

        String reqURL = PaymentUrlCache.getUrl("ali_pay","QUERY_PAY_STATE");
        AlipayClient alipayClient = null;
        String private_key = paymentAccountConfig.optString("private_key");
	    if ((StringUtils.isNotBlank(private_key)) && (private_key.length() > 1024)) {
	      alipayClient = new DefaultAlipayClient(reqURL, paymentAccountConfig.optString("app_id"), private_key, "json", "UTF-8", paymentAccountConfig.optString("alipay_public_pay"), "RSA2");
	    }
	    else
	    {
	      alipayClient = new DefaultAlipayClient(reqURL, paymentAccountConfig.optString("app_id"), private_key, "json", "UTF-8", paymentAccountConfig.optString("alipay_public_pay"));
	    }


        
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        request.setBizContent(reqJson.toString());
        System.out.println("查询请求参数："+reqJson.toString());
        AlipayTradeQueryResponse response = null;
        try {
            response = alipayClient.execute(request);
        } catch (Exception e) {
            logger.error(e);
        }
        return response;
    }

    /**
     *  根据轮训的次数存储redis中的次数，并判断这个订单号是否需要执行
     * @param json
     * @return
     */
    public boolean isExecute(JSONObject json){
        String out_trade_no = json.optString("out_trade_no");
        int auto_num = json.optInt("auto_num");
        long time = json.optLong("time");
        boolean execute = isExecute(auto_num, time);
        if(execute) {
            auto_num++;
            json.put("auto_num",auto_num);
            if(!PaymentRedisCache.hasKey(out_trade_no)) {
                return false;
            }
            PaymentRedisCache.saveOutTradeNo2Info(out_trade_no,json.toString()); // 更新执行次数
        }
        return execute;
    }

    /**
     * 是否已经到了该执行的时间
     * @param auto_num
     * @param time
     * @return
     */
    public boolean isExecute(int auto_num,long time){
        //   --------------查询发动频率15/15/30/180/1800/1800/1800/1800/3600/7200
        long currenttime = System.currentTimeMillis();

        auto_num  = auto_num - 50;

        if(auto_num < 0) {
            if (currenttime - time >= (long) (3 * 1000)) {
                return true;
            }
        }

        if(auto_num==0) {
            if(currenttime-time >= (long)(15*1000)) {
                return true;
            }
        } else if(auto_num==1) {
            if(currenttime-time >= (long)(30*1000)) {
                return true;
            }
        } else if(auto_num==2) {
            if(currenttime-time >= (long)(60*1000)) {
                return true;
            }
        } else if(auto_num==3) {
            if(currenttime-time >= (long)(240*1000)) {
                return true;
            }
        } else if(auto_num==4) {
            if(currenttime-time >= (long)(2040*1000)) {
                return true;
            }
        } else if(auto_num==5) {
            if(currenttime-time >= (long)(3840*1000)) {
                return true;
            }
        } else if(auto_num==6) {
            if(currenttime-time >= (long)(5640*1000)) {
                return true;
            }
        } else if(auto_num==7) {
            if(currenttime-time >= (long)(7440*1000)) {
                return true;
            }
        } else if(auto_num==8) {
            if(currenttime-time >= (long)(11040*1000)) {
                return true;
            }
        } else if(auto_num==9) {
            if(currenttime-time >= (long)(18240*1000)) {
                return true;
            }
        }
        return false;
    }
}
