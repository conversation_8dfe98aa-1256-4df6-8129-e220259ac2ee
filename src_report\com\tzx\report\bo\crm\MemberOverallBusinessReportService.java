package com.tzx.report.bo.crm;

import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import net.sf.json.JSONObject;

public interface MemberOverallBusinessReportService
{
	String NAME = "com.tzx.report.bo.imp.crm.MemberOverallBusinessReportServiceImp";

	public JSONObject getRestoreBill(String attribute, JSONObject p) throws Exception;


	public HSSFWorkbook exportDate(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception;

	public HSSFWorkbook exportDate2(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception;


}
