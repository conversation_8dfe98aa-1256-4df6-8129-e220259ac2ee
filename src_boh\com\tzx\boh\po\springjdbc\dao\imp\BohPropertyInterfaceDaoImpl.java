package com.tzx.boh.po.springjdbc.dao.imp;

import com.tzx.boh.bo.imp.BohPropertyInterfaceServiceImpl;
import com.tzx.boh.po.springjdbc.dao.BohPropertyInterfaceDao;
import com.tzx.cc.test.logTest;
import com.tzx.framework.common.TxzSop.TechTransSoapClient;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.weixin.po.redis.dao.RedisTemplateDao;

import common.Logger;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import jline.internal.Log;

@Repository(BohPropertyInterfaceDao.Name)
public class BohPropertyInterfaceDaoImpl implements BohPropertyInterfaceDao {
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = RedisTemplateDao.NAME)
    private RedisTemplateDao rdao;
	private static final Logger		log	= Logger.getLogger(BohPropertyInterfaceDaoImpl.class);
	
	@Override
	public JSONObject load(String tenancyID, JSONObject condition, String oids) throws Exception
	{
		int store_id = condition.optInt("store_id");
		int pagenum = condition.containsKey("page")&&condition.getInt("page") > 0 ? condition.getInt("page"):1;
		StringBuilder sb = new StringBuilder();
		sb.append("select hpip.*,(select oo.org_full_name from organ oo where oo.id=hpip.store_id) from boh_property_interface_parameter hpip where 1=1 ");		
		if(store_id==0)
		{
			sb.append(" and hpip.store_id in("+oids+")");
		}
		else
		{
			sb.append(" and hpip.store_id="+store_id);
		}
		if(condition.containsKey("type_id")&&condition.optInt("type_id")==3||condition.optInt("type_id")==4||condition.optInt("type_id")==5)
		{
			sb.append(" and type_id='"+condition.optString("type_id")+"'");
		}
	
		else
		{
			sb.append(" and type_id in(0,1,8,9)");
		}
		
		long total = this.dao.countSql(tenancyID,sb.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sb.toString()));
		JSONObject result = new JSONObject();
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);

		return result;
	}

	@Override
	public JSONObject find(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		int type_id = condition.optInt("type_id");
		int store_id = condition.optInt("store_id");
		result.put("success",false);
		List<JSONObject> list = this.dao.query4Json(tenancyID,"select id from boh_property_interface_parameter where store_id="+store_id+"  and type_id="+type_id+" and  valid_state='1'");
		 
		if(list.size()>0)
		{
			result.put("success",true);
		}
		return result;
	}

	@Override
	public JSONObject save(String tenancyID, JSONObject condition) throws Exception
	{
		int id = condition.optInt("id");
		
		int type_id = condition.optInt("type_id");
		int store_id = condition.optInt("store_id");
		JSONObject result = new JSONObject();
		
		if(type_id==0||type_id==1||type_id==3){ //保留历史类型
			if(type_id==1) {
				///修改属性名
				condition.put("url", condition.optString("url_1"));
				condition.put("property_name", condition.optString("property_name_1"));
				condition.put("storecode", condition.optString("storecode_1"));
			}else if(type_id==0) {
				///修改属性名
				condition.put("url", condition.optString("url_0"));
				condition.put("property_name", condition.optString("property_name_0"));
				condition.put("storecode", condition.optString("storecode_0"));
			}
			//删除KEY
			removeJSONKey(condition,new String[]{"url_1", "property_name_1","storecode_1","url_0", "property_name_0","storecode_0"});
			
			
			String property_name = condition.optString("property_name");
			if(property_name.length()>0)//判重
			{
				List<JSONObject> listname = this.dao.query4Json(tenancyID,"select id from boh_property_interface_parameter where store_id="+store_id+"  and type_id="+type_id+" and property_name='"+property_name+"' and id<>"+id);
				//
				if(listname.size()>0)
				{
					result.put("success", false);
					result.put("msg","该名称已存在,同一机构下不能重复");
					return result;
				}
			}
			
			if(id==0)//判断门店是否存在多条
			{// 
				List<JSONObject> listsize = this.dao.query4Json(tenancyID,"select id from boh_property_interface_parameter where  type_id="+type_id+" and store_id="+store_id+" and valid_state='1' ");
				if(listsize.size()>0)
				{
					result.put("success", false);
					result.put("msg","一个门店一个业务接口只能存在一条有效数据");
					return result;
				}
			}
			
			if(id>0)//存在 ID ，是更新配置
			{	
				this.dao.updateIgnorCase(tenancyID, "boh_property_interface_parameter", condition);//更新boh_property_interface_parameter
				if(type_id==1) {// esPOS
					JSONObject updateItem = new JSONObject ();
					updateItem.put("http_url", condition.optString("url"));
					updateItem.put("last_operator", condition.optString("last_operator"));
					updateItem.put("last_updatetime", condition.optString("last_updatetime"));
					updateItem.put("id", condition.optString("hq_organ_id"));
					this.dao.updateIgnorCase(tenancyID, "hq_organ_property_info", updateItem);//更新hq_organ_property_info
				}
			}
			else//不存在ID，是插入配置
			{
				if(condition.has("id"))
				{
					condition.remove("id");
				}
				condition.put("tenancy_id",tenancyID);
				condition.put("type_id",type_id);
				condition.put("valid_state","1");
				this.dao.insertIgnorCase(tenancyID, "boh_property_interface_parameter", condition);//插入boh_property_interface_parameter
				if(type_id==1) {// esPOS
					JSONObject updateItem = new JSONObject ();
					updateItem.put("http_url", condition.optString("url"));
					updateItem.put("tenancy_id",tenancyID);
					updateItem.put("orange_perproty_info_id","1");
					updateItem.put("store_id", condition.optString("store_id"));
					updateItem.put("property_id", "1");
					updateItem.put("transfer_mode", "1");
					updateItem.put("last_operator", condition.optString("last_operator"));
					updateItem.put("last_updatetime", condition.optString("last_updatetime"));
					this.dao.insertIgnorCase(tenancyID, "hq_organ_property_info", updateItem);//插入hq_organ_property_info
				}
			}
	}else if(type_id==8||type_id==9){//新增的2种类型
		
		if(id==0)//插入操作 先判断门店是否已经存在
		{// 
			List<JSONObject> listsize = this.dao.query4Json(tenancyID,"select id from boh_property_interface_parameter where  type_id="+type_id+" and store_id="+store_id+" and valid_state='1' ");
			if(listsize.size()>0)
			{
				result.put("success", false);
				result.put("msg","一个门店一个业务接口只能存在一条有效数据");
				return result;
			}
		}
		
		if(type_id==8) {
			///修改属性名
			condition.put("url", condition.optString("url_2"));
			condition.put("storecode", condition.optString("storecode_2"));
			condition.put("vipcode", condition.optString("vipcode_2"));
			condition.put("port", condition.optString("port_2"));
			condition.put("user_name", condition.optString("user_name_2"));
			condition.put("password", condition.optString("password_2"));
			condition.put("plu", condition.optString("plu_2"));
			condition.put("tillid", condition.optString("tillid_2"));
		}else if(type_id==9) {
			///修改属性名
			condition.put("url", condition.optString("url_3"));
			condition.put("mallid", condition.optString("mallid_3"));
			condition.put("storecode", condition.optString("storecode_3"));
			condition.put("tillid", condition.optString("tillid_3"));
			condition.put("user_name", condition.optString("user_name_3"));
			condition.put("password", condition.optString("password_3"));
			condition.put("plu", condition.optString("plu_3"));
		}
		//删除KEY
		removeJSONKey(condition,new String[]{"url_2", "vipcode_2","storecode_2","port_2","tillid_2","user_name_2","password_2","url_3", "mallid_3","storecode_3","tillid_3","plu_3","user_name_3","password_3"});
		
		if(type_id==8){// 类型POS-MIS
			if(id>0)//存在 ID ，是更新配置
			{	
				this.dao.updateIgnorCase(tenancyID, "boh_property_interface_parameter", condition);//更新boh_property_interface_parameter
			}else{ //ID不存在，是插入配置
				if(condition.has("id"))
				{
					condition.remove("id");
				}
				condition.put("tenancy_id",tenancyID);
				condition.put("type_id",type_id);
				condition.put("valid_state","1");
				this.dao.insertIgnorCase(tenancyID, "boh_property_interface_parameter", condition);//插入boh_property_interface_parameter
			}
		}
		
		if(type_id==9){// 类型v332m
			if(id>0)//存在 ID ，是更新配置
			{	
				this.dao.updateIgnorCase(tenancyID, "boh_property_interface_parameter", condition);//更新boh_property_interface_parameter
			}else{ //ID不存在，是插入配置
				if(condition.has("id"))
				{
					condition.remove("id");
				}
				condition.put("tenancy_id",tenancyID);
				condition.put("type_id",type_id);
				condition.put("valid_state","1");
				this.dao.insertIgnorCase(tenancyID, "boh_property_interface_parameter", condition);//插入boh_property_interface_parameter
			}
		}
		
		
		
	}
		
		result.put("success", true);
		return result;
	}
	//清除JSON中的KEY
	private void removeJSONKey(JSONObject json,String[] keys){
		for(String key:keys){
			if(json.containsKey(key))
				json.remove(key);
		}
	}
	

	@Override
	public List<JSONObject> getProperty(String tenancyID, JSONObject condition) throws Exception
	{
		return this.dao.query4Json(tenancyID,"select * from boh_property_interface_parameter where store_id="+condition.optInt("store_id")+" and type_id="+condition.optInt("type_id")+" and valid_state='1'");
	}

	@Override
	public JSONObject upload(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		int type_id = condition.optInt("type");
		String report_date  = condition.optString("report_date");
		if(report_date.length()!=10)
		{
			result.put("success",false);
			result.put("msg","业务日期错误");
			return result;
		}
		int store_id = condition.optInt("store_id");
		StringBuilder sb = new StringBuilder();
		sb.append("select zz.*,oo.org_full_name,oo.organ_code from (select boh.url,boh.store_id,boh.licensekey,boh.mallid,boh.storecode,boh.user_name,boh.password,boh.remark from boh_property_interface_parameter boh where boh.type_id="+type_id+" and boh.store_id="+store_id+" and boh.valid_state='1' ORDER BY boh.id desc limit 1) zz LEFT JOIN organ oo on zz.store_id=oo.id");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
		if(list.size()==0)
		{
			result.put("success",false);
			result.put("msg","指定机构不存在物业接口信息");
			return result;
		}
		//radius
		String key = "bohproperty_"+tenancyID+"_"+type_id+"_"+store_id+"_"+report_date;
		
		try
		{
			if(rdao.hasKey(key))
			{
				result.put("success",false);
				result.put("msg","正在上传日结数据,请稍后再试");
				return result;
			}
			rdao.set(key,1);
			rdao.expire(key, 2*60l);
		}
		catch (Exception e)
		{
			// TODO: handle exception
		}
		
		
		JSONObject jo2 = list.get(0);
		String organ_code = jo2.optString("organ_code");
		String url = jo2.optString("url");
		String licensekey = jo2.optString("licensekey");
		String mallid = jo2.optString("mallid");
		String storecode = jo2.optString("storecode");
		String user_name = jo2.optString("user_name");
		String password = jo2.optString("password");
		String last_operator = condition.optString("last_operator"); 
		String last_updatetime = DateUtil.getNowDateYYDDMMHHMMSS(); 
		String remark =  jo2.optString("remark");
		if(remark.length()<5)
		{
			remark = "A000011";
		}
		StringBuilder bill_num_sb = new StringBuilder();
		sb.setLength(0);

		sb.append("select pb.id,pb.bill_num,pb.batch_num,pb.bill_num,pb.serial_num,pb.report_date,0 as service_amount,0 as subtotal,pb.payment_amount as bill_amount,to_char(pb.payment_time,'yyyymmdd') as yyyymmdd,to_char(pb.payment_time,'HH24mmss') as hhmmss,(CASE when pb.payment_amount<0 then 'SR' else 'SA' end) as salestype from pos_bill2 pb where pb.store_id="+store_id+" and pb.report_date='"+report_date+"' and pb.id not in(select bpius.send_id from boh_property_interface_upload bpius where bpius.store_id="+store_id+" and bpius.type_id="+type_id+" and bpius.user_name='"+user_name+"') ORDER BY id asc LIMIT 50");

		List<JSONObject> list2 = this.dao.query4Json(tenancyID, sb.toString());
		if(list2.size()==0)
		{
			result.put("success",true);
			result.put("now_size",-1);
			result.put("msg","上传完毕");
			return result;
		}
		Map<String,JSONObject> mapBill = new HashMap<String, JSONObject>();
		Map<String,List<JSONObject>> mapItem = new HashMap<String, List<JSONObject>>();
		Map<String,List<JSONObject>> mapPayment = new HashMap<String, List<JSONObject>>();
		Map<String,String> bill_id = new HashMap<String,String>();
		StringBuilder bill_ids = new StringBuilder("0");
		for(JSONObject jo:list2)
		{
			int id = jo.optInt("id");
			String bill_num = jo.optString("bill_num");
			Double service_amount = jo.optDouble("service_amount",0.0);
			Double subtotal  = jo.optDouble("subtotal",0.0);
			Double bill_amount  = jo.optDouble("bill_amount",0.0);
			bill_ids.append(","+id);
			if(mapBill.containsKey(bill_num))
			{
				JSONObject jo22 = mapBill.get(bill_num);
				Double service_amount22 = jo22.optDouble("service_amount",0.0);
				Double subtotal22  = jo22.optDouble("subtotal",0.0);
				Double bill_amount22  = jo22.optDouble("bill_amount",0.0);
				jo.put("service_amount", Scm.padd(service_amount, service_amount22));
				jo.put("subtotal", Scm.padd(subtotal, subtotal22));
				Double ba =  Scm.padd(bill_amount, bill_amount22);
				jo.put("bill_amount", ba);
				if(ba<0)
				{
					jo.put("salestype","SR");
				}
				else
				{
					jo.put("salestype","SA");
				}
				bill_id.put(bill_num, bill_id.get(bill_num)+","+id);
				
			}
			else
			{
				bill_num_sb.append(",'"+bill_num+"'");//编号拼接
				mapBill.put(bill_num, jo);//编号为键  编号对应实体为值
				bill_id.put(bill_num, id+"");//编号id
			}
		}
		if(bill_num_sb.length()>0)
		{   
			bill_num_sb.delete(0,1);
			sb.setLength(0);
			sb.append("select pb.id,pb.service_amount,pb.bill_num,pb.batch_num,pb.bill_num,pb.serial_num,pb.report_date,pb.service_amount,pb.subtotal,pb.bill_amount,to_char(pb.payment_time,'yyyymmdd') as yyyymmdd,to_char(pb.payment_time,'HH24mmss') as hhmmss,(CASE when pb.bill_amount<0 then 'SR' else 'SA' end) as salestype from pos_bill2 pb where pb.store_id="+store_id+"  and  pb.report_date='"+report_date+"' and pb.id not in(select bpius.send_id from boh_property_interface_upload bpius where bpius.store_id="+store_id+" and bpius.type_id="+type_id+" and bpius.user_name='"+user_name+"') and bill_num in("+bill_num_sb+") and id not in("+bill_ids+")  ORDER BY id ");
			List<JSONObject> list3 = this.dao.query4Json(tenancyID, sb.toString());
			for(JSONObject jo:list3)
			{
				int id = jo.optInt("id");
				String bill_num = jo.optString("bill_num");
				Double service_amount = jo.optDouble("service_amount",0.0);
				Double subtotal  = jo.optDouble("subtotal",0.0);
				Double bill_amount  = jo.optDouble("bill_amount",0.0);
				bill_ids.append(","+id);
				if(mapBill.containsKey(bill_num))
				{
					JSONObject jo22 = mapBill.get(bill_num);
					Double service_amount22 = jo22.optDouble("service_amount",0.0);
					Double subtotal22  = jo22.optDouble("subtotal",0.0);
					Double bill_amount22  = jo22.optDouble("bill_amount",0.0);
					jo.put("service_amount", Scm.padd(service_amount, service_amount22));
					jo.put("subtotal", Scm.padd(subtotal, subtotal22));
					Double ba =  Scm.padd(bill_amount, bill_amount22);
					jo.put("bill_amount", ba);
					if(ba<0)
					{
						jo.put("salestype","SR");
					}
					else
					{
						jo.put("salestype","SA");
					}
					bill_id.put(bill_num, bill_id.get(bill_num)+","+id);
					
				}
				else
				{
					bill_num_sb.append(",'"+bill_num+"'");
					mapBill.put(bill_num, jo);
					bill_id.put(bill_num, id+"");
				}
			}
			sb.setLength(0);
			sb.append("select bill_num,item_num as code,item_count,item_amount from pos_bill_item2 where store_id="+store_id+" and bill_num in("+bill_num_sb.toString()+")");
			List<JSONObject> list3item = this.dao.query4Json(tenancyID,sb.toString());
			sb.setLength(0);
			sb.append("select bill_num,amount,(CASE WHEN name_english='RMB' THEN 'CH' WHEN name_english like '中国银行%' THEN 'CI' ELSE 'OT' END) as pay_code  from pos_bill_payment2 where bill_num in("+bill_num_sb.toString()+")");
			List<JSONObject> list4payment = this.dao.query4Json(tenancyID,sb.toString());
			
			for(JSONObject item: list3item)
			{
				String bill_num = item.optString("bill_num");
				if(mapItem.containsKey(bill_num))
				{
					mapItem.get(bill_num).add(item);
				}
				else
				{
					List<JSONObject> listitems = new ArrayList<JSONObject>();
					listitems.add(item);
					mapItem.put(bill_num, listitems);
				}
			}
			
			for(JSONObject payment: list4payment)
			{
				String bill_num = payment.optString("bill_num");
				if(mapPayment.containsKey(bill_num))
				{
					mapPayment.get(bill_num).add(payment);
				}
				else
				{
					List<JSONObject> listpayment = new ArrayList<JSONObject>();
					listpayment.add(payment);
					mapPayment.put(bill_num, listpayment);
				}
			}
			
		}
		int send_total = 0;
		Iterator<String> ist = mapBill.keySet().iterator();
		List<JSONObject> listaddsend = new ArrayList<JSONObject>();
		boolean flag = false;
		int recode = 0;
		while(ist.hasNext())
		{
			String bill_num = ist.next();
			JSONObject bill_info = mapBill.get(bill_num);
			bill_info.put("remark",remark);
			List<JSONObject> item_list = mapItem.containsKey(bill_num)?mapItem.get(bill_num):new ArrayList<JSONObject>();
			List<JSONObject> payment_list = mapPayment.containsKey(bill_num)?mapPayment.get(bill_num):new ArrayList<JSONObject>();
			int returncode = TechTransSoapClient.postdailysalesestimatecreate(url, licensekey, user_name, password, storecode, mallid, sb, organ_code, bill_info, item_list, payment_list);
			if(returncode==200)
			{
				send_total ++;
				String[] ids = bill_id.get(bill_num).split(",");
				for(String sendid :ids)
				{
					JSONObject joadd = new JSONObject();
					joadd.put("tenancy_id",tenancyID);
					joadd.put("type_id",type_id);
					joadd.put("user_name",user_name);
					joadd.put("store_id",store_id);
					joadd.put("send_id",sendid);
					joadd.put("last_update_person",last_operator);
					joadd.put("last_update_time",last_updatetime);
					listaddsend.add(joadd);
				}
			}
			else if(returncode == 402)
			{
				recode = 402;
				flag = true;
				break;
			}
			else if(returncode == 403)
			{
				recode = 403;
				flag = true;
				break;
			}
			else if(returncode==404)
			{
				recode = 404;
				flag = true;
				break;
			}	
		}
		//mapBill
		
		int send_total2 = 0;
		sb.setLength(0);
		sb.append("select id,send_total from boh_property_interface_upload_summary where type_id="+type_id+" and store_id="+store_id+" and business_date='"+report_date+"' and user_name='"+user_name+"'");
		List<JSONObject> listtt = this.dao.query4Json(tenancyID, sb.toString());
		if(listtt.size()==0)
		{
			send_total2 = send_total;
			JSONObject totjo = new JSONObject();
			totjo.put("tenancy_id",tenancyID);
			totjo.put("type_id",type_id);
			totjo.put("user_name",user_name);
			totjo.put("store_id",store_id);
			totjo.put("business_date",report_date);
			totjo.put("send_total",send_total);
			this.dao.insertIgnorCase(tenancyID, "boh_property_interface_upload_summary", totjo);
		}
		else
		{
			sb.setLength(0);
			sb.append("update boh_property_interface_upload_summary set send_total=send_total+"+send_total+" where type_id="+type_id+" and store_id="+store_id+" and business_date='"+report_date+"' and user_name='"+user_name+"'");
			this.dao.execute(tenancyID, sb.toString());
			send_total2 = listtt.get(0).optInt("send_total")+send_total;
		}
		if(listaddsend.size()>0)
		{
			this.dao.insertBatchIgnorCase(tenancyID, "boh_property_interface_upload", listaddsend);
		}
		result.put("success",true);
		result.put("now_size",send_total2);
		result.put("msg","上传完毕");
		if(flag && send_total==0)
		{
			result.put("success",false);
			result.put("msg","请检查接口地址是否正确或网络状况是否良好");
			if(recode==403)
			{
				result.put("msg","业务日期超出接口接收范围（约45天内）");
			}
			if(recode==402)
			{
				result.put("msg","请求失败,请联系系统管理员");
			}

		}
		try
		{
			if(rdao.hasKey(key))
			{
				rdao.delete(key);
			}
		}
		catch (Exception e)
		{
			// TODO: handle exception
		}
		return result;
	}
	
	
	
	@Override
	public JSONObject uploadV332m(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		int type_id = condition.optInt("type_id");
		String report_date  = condition.optString("report_date");
		if(report_date.length()!=10)
		{
			result.put("success",false);
			result.put("msg","业务日期错误");
			return result;
		}
		int store_id = condition.optInt("store_id");
		StringBuilder sb = new StringBuilder();
		sb.append("select zz.*,oo.org_full_name,oo.organ_code from (select boh.url,boh.store_id,boh.licensekey,boh.mallid,boh.storecode,boh.user_name,boh.password,boh.remark,boh.plu from boh_property_interface_parameter boh where boh.type_id="+type_id+" and boh.store_id="+store_id+" and boh.valid_state='1' ORDER BY boh.id desc limit 1) zz LEFT JOIN organ oo on zz.store_id=oo.id");
		log.info("查询接口信息:");
		log.info("tenancyID:"+tenancyID);
		log.info("sql:"+sb.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, sb.toString());
		if(list.size()==0)
		{
			result.put("success",false);
			result.put("msg","指定机构不存在物业接口信息");
			return result;
		}
		//radius
		String key = "bohproperty_"+tenancyID+"_"+type_id+"_"+store_id+"_"+report_date;
		
		try
		{
			if(rdao.hasKey(key))
			{
				result.put("success",false);
				result.put("msg","正在上传日结数据,请稍后再试");
				return result;
			}
			rdao.set(key,1);
			rdao.expire(key, 2*60l);
		}
		catch (Exception e)
		{
			// TODO: handle exception
		}
		
		
		JSONObject jo2 = list.get(0);
		String organ_code = jo2.optString("organ_code");
		String url = jo2.optString("url");
		String licensekey = jo2.optString("licensekey");
		String mallid = jo2.optString("mallid");
		String storecode = jo2.optString("storecode");
		String user_name = jo2.optString("user_name");
		String password = jo2.optString("password");
		String last_operator = condition.optString("last_operator"); 
		String last_updatetime = DateUtil.getNowDateYYDDMMHHMMSS(); 
		String remark =  jo2.optString("remark");
		String plu =  jo2.optString("plu");
//		if(remark.length()<5)
//		{
//			remark = "B0133N02";
//		}
		StringBuilder bill_num_sb = new StringBuilder();
		sb.setLength(0);

		sb.append("select pb.id,pb.bill_num,pb.batch_num,pb.bill_num,pb.serial_num,pb.report_date,0 as service_amount,0 as subtotal,pb.payment_amount as bill_amount,to_char(pb.payment_time,'yyyymmdd') as yyyymmdd,to_char(pb.payment_time,'HH24mmss') as hhmmss,(CASE when pb.payment_amount<0 then 'SR' else 'SA' end) as salestype from pos_bill2 pb where pb.store_id="+store_id+" and pb.report_date='"+report_date+"' and pb.id not in(select bpius.send_id from boh_property_interface_upload bpius where bpius.store_id="+store_id+" and bpius.type_id="+type_id+" and bpius.user_name='"+user_name+"') ORDER BY id asc LIMIT 50");

		List<JSONObject> list2 = this.dao.query4Json(tenancyID, sb.toString());
		if(list2.size()==0)
		{
			result.put("success",true);
			result.put("now_size",-1);
			result.put("msg","上传完毕");
			return result;
		}
		Map<String,JSONObject> mapBill = new HashMap<String, JSONObject>();
		Map<String,List<JSONObject>> mapItem = new HashMap<String, List<JSONObject>>();
		Map<String,List<JSONObject>> mapPayment = new HashMap<String, List<JSONObject>>();
		Map<String,String> bill_id = new HashMap<String,String>();
		StringBuilder bill_ids = new StringBuilder("0");
		for(JSONObject jo:list2)
		{
			int id = jo.optInt("id");
			String bill_num = jo.optString("bill_num");
			Double service_amount = jo.optDouble("service_amount",0.0);
			Double subtotal  = jo.optDouble("subtotal",0.0);
			Double bill_amount  = jo.optDouble("bill_amount",0.0);
			bill_ids.append(","+id);
			if(mapBill.containsKey(bill_num))
			{
				JSONObject jo22 = mapBill.get(bill_num);
				Double service_amount22 = jo22.optDouble("service_amount",0.0);
				Double subtotal22  = jo22.optDouble("subtotal",0.0);
				Double bill_amount22  = jo22.optDouble("bill_amount",0.0);
				jo.put("service_amount", Scm.padd(service_amount, service_amount22));
				jo.put("subtotal", Scm.padd(subtotal, subtotal22));
				Double ba =  Scm.padd(bill_amount, bill_amount22);
				jo.put("bill_amount", ba);
				if(ba<0)
				{
					jo.put("salestype","SR");
				}
				else
				{
					jo.put("salestype","SA");
				}
				bill_id.put(bill_num, bill_id.get(bill_num)+","+id);
				
			}
			else
			{
				bill_num_sb.append(",'"+bill_num+"'");//编号拼接
				mapBill.put(bill_num, jo);//编号为键  编号对应实体为值
				bill_id.put(bill_num, id+"");//编号id
			}
		}
		if(bill_num_sb.length()>0)
		{   
			bill_num_sb.delete(0,1);
			sb.setLength(0);
			sb.append("select pb.id,pb.service_amount,pb.bill_num,pb.batch_num,pb.bill_num,pb.serial_num,pb.report_date,pb.service_amount,pb.subtotal,pb.bill_amount,to_char(pb.payment_time,'yyyymmdd') as yyyymmdd,to_char(pb.payment_time,'HH24mmss') as hhmmss,(CASE when pb.bill_amount<0 then 'SR' else 'SA' end) as salestype from pos_bill2 pb where pb.store_id="+store_id+"  and  pb.report_date='"+report_date+"' and pb.id not in(select bpius.send_id from boh_property_interface_upload bpius where bpius.store_id="+store_id+" and bpius.type_id="+type_id+" and bpius.user_name='"+user_name+"') and bill_num in("+bill_num_sb+") and id not in("+bill_ids+")  ORDER BY id ");
			List<JSONObject> list3 = this.dao.query4Json(tenancyID, sb.toString());
			for(JSONObject jo:list3)
			{
				int id = jo.optInt("id");
				String bill_num = jo.optString("bill_num");
				Double service_amount = jo.optDouble("service_amount",0.0);
				Double subtotal  = jo.optDouble("subtotal",0.0);
				Double bill_amount  = jo.optDouble("bill_amount",0.0);
				bill_ids.append(","+id);
				if(mapBill.containsKey(bill_num))
				{
					JSONObject jo22 = mapBill.get(bill_num);
					Double service_amount22 = jo22.optDouble("service_amount",0.0);
					Double subtotal22  = jo22.optDouble("subtotal",0.0);
					Double bill_amount22  = jo22.optDouble("bill_amount",0.0);
					jo.put("service_amount", Scm.padd(service_amount, service_amount22));
					jo.put("subtotal", Scm.padd(subtotal, subtotal22));
					Double ba =  Scm.padd(bill_amount, bill_amount22);
					jo.put("bill_amount", ba);
					if(ba<0)
					{
						jo.put("salestype","SR");
					}
					else
					{
						jo.put("salestype","SA");
					}
					bill_id.put(bill_num, bill_id.get(bill_num)+","+id);
					
				}
				else
				{
					bill_num_sb.append(",'"+bill_num+"'");
					mapBill.put(bill_num, jo);
					bill_id.put(bill_num, id+"");
				}
			}
			sb.setLength(0);
			sb.append("select bill_num,item_num as code,item_count,item_amount from pos_bill_item2 where store_id="+store_id+" and bill_num in("+bill_num_sb.toString()+")");
			List<JSONObject> list3item = this.dao.query4Json(tenancyID,sb.toString());
			sb.setLength(0);
			sb.append("select bill_num,amount,(CASE WHEN name_english='RMB' THEN 'CH' WHEN name_english like '中国银行%' THEN 'CI' ELSE 'OT' END) as pay_code  from pos_bill_payment2 where bill_num in("+bill_num_sb.toString()+")");
			List<JSONObject> list4payment = this.dao.query4Json(tenancyID,sb.toString());
			
			for(JSONObject item: list3item)
			{
				String bill_num = item.optString("bill_num");
				if(mapItem.containsKey(bill_num))
				{
					mapItem.get(bill_num).add(item);
				}
				else
				{
					List<JSONObject> listitems = new ArrayList<JSONObject>();
					listitems.add(item);
					mapItem.put(bill_num, listitems);
				}
			}
			
			for(JSONObject payment: list4payment)
			{
				String bill_num = payment.optString("bill_num");
				if(mapPayment.containsKey(bill_num))
				{
					mapPayment.get(bill_num).add(payment);
				}
				else
				{
					List<JSONObject> listpayment = new ArrayList<JSONObject>();
					listpayment.add(payment);
					mapPayment.put(bill_num, listpayment);
				}
			}
			
		}
		int send_total = 0;
		Iterator<String> ist = mapBill.keySet().iterator();
		List<JSONObject> listaddsend = new ArrayList<JSONObject>();
		boolean flag = false;
		int recode = 0;
		while(ist.hasNext())
		{
			String bill_num = ist.next();
			JSONObject bill_info = mapBill.get(bill_num);
			bill_info.put("plu",plu);
			List<JSONObject> item_list = mapItem.containsKey(bill_num)?mapItem.get(bill_num):new ArrayList<JSONObject>();
			List<JSONObject> payment_list = mapPayment.containsKey(bill_num)?mapPayment.get(bill_num):new ArrayList<JSONObject>();
			int returncode = TechTransSoapClient.postdailysalesestimatecreate_v332m(url, licensekey, user_name, password, storecode, mallid, sb, organ_code, bill_info, item_list, payment_list);
			if(returncode==200)
			{
				send_total ++;
				String[] ids = bill_id.get(bill_num).split(",");
				for(String sendid :ids)
				{
					JSONObject joadd = new JSONObject();
					joadd.put("tenancy_id",tenancyID);
					joadd.put("type_id",type_id);
					joadd.put("user_name",user_name);
					joadd.put("store_id",store_id);
					joadd.put("send_id",sendid);
					joadd.put("last_update_person",last_operator);
					joadd.put("last_update_time",last_updatetime);
					listaddsend.add(joadd);
				}
			}
			else if(returncode == 402)
			{
				recode = 402;
				flag = true;
				break;
			}
			else if(returncode == 403)
			{
				recode = 403;
				flag = true;
				break;
			}
			else if(returncode==404)
			{
				recode = 404;
				flag = true;
				break;
			}	
		}
		//mapBill
		
		int send_total2 = 0;
		sb.setLength(0);
		sb.append("select id,send_total from boh_property_interface_upload_summary where type_id="+type_id+" and store_id="+store_id+" and business_date='"+report_date+"' and user_name='"+user_name+"'");
		List<JSONObject> listtt = this.dao.query4Json(tenancyID, sb.toString());
		if(listtt.size()==0)
		{
			send_total2 = send_total;
			JSONObject totjo = new JSONObject();
			totjo.put("tenancy_id",tenancyID);
			totjo.put("type_id",type_id);
			totjo.put("user_name",user_name);
			totjo.put("store_id",store_id);
			totjo.put("business_date",report_date);
			totjo.put("send_total",send_total);
			this.dao.insertIgnorCase(tenancyID, "boh_property_interface_upload_summary", totjo);
		}
		else
		{
			sb.setLength(0);
			sb.append("update boh_property_interface_upload_summary set send_total=send_total+"+send_total+" where type_id="+type_id+" and store_id="+store_id+" and business_date='"+report_date+"' and user_name='"+user_name+"'");
			this.dao.execute(tenancyID, sb.toString());
			send_total2 = listtt.get(0).optInt("send_total")+send_total;
		}
		if(listaddsend.size()>0)
		{
			this.dao.insertBatchIgnorCase(tenancyID, "boh_property_interface_upload", listaddsend);
		}
		result.put("success",true);
		result.put("now_size",send_total2);
		result.put("msg","上传完毕");
		if(flag && send_total==0)
		{
			result.put("success",false);
			result.put("msg","请检查接口地址是否正确或网络状况是否良好");
			if(recode==403)
			{
				result.put("msg","业务日期超出接口接收范围（约45天内）");
			}
			if(recode==402)
			{
				result.put("msg","请求失败,请联系系统管理员");
			}

		}
		try
		{
			if(rdao.hasKey(key))
			{
				rdao.delete(key);
			}
		}
		catch (Exception e)
		{
			// TODO: handle exception
		}
		return result;
	}


	@Override
	public JSONObject getUploadDetails(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		int type_id = condition.optInt("type_id");
		int store_id = condition.optInt("store_id");
		long total = 0;
		int now_size = 0;
		String report_date  = condition.optString("report_date");
		if(report_date.length()!=10)
		{
			result.put("success",false);
			result.put("msg","业务日期错误");
			return result;
		}
		StringBuilder sb = new StringBuilder();
		sb.append("select count(*) as z from pos_bill2 where store_id="+store_id+" and report_date='"+report_date+"' ");
		List<JSONObject> totalList = this.dao.query4Json(tenancyID,sb.toString());
		if(totalList.size()>0)
		{
			total = totalList.get(0).optInt("z");
		}
		sb.setLength(0);
		sb.append("select bpius.send_total from (select boh.id,boh.user_name from boh_property_interface_parameter boh where boh.type_id="+type_id+" and boh.store_id="+store_id+" and boh.valid_state='1' ORDER BY boh.id desc limit 1 ) zz LEFT JOIN boh_property_interface_upload_summary bpius on bpius.store_id="+store_id+"  and bpius.business_date='"+report_date+"' and bpius.type_id="+type_id+"");
		if(type_id==0) {
			sb.append("and bpius.user_name= zz.user_name LIMIT 1");
		}
		List<JSONObject> listSendtoatl = this.dao.query4Json(tenancyID, sb.toString());
		if(listSendtoatl.size()>0)
		{
			now_size = listSendtoatl.get(0).optInt("send_total");
		}
		result.put("success",true);
		result.put("total",total);
		result.put("now_size",now_size);
		return result;
	}

	@SuppressWarnings("deprecation")
	@Override
	public List<JSONObject> getList(String tenancyID, JSONObject condition) throws Exception
	{
		Date date = new Date(System.currentTimeMillis());
		
		String year = condition.optString("year");
		String month = condition.optString("month");
		int store_id = condition.optInt("store_id");
		if(year.length()==0)
		{
			year = date.getYear()+"";
		}
		if(month.length()==0)
		{
			int mm = date.getMonth()+1;
			if(mm>=10)
			{
				month = mm+"";
			}
			else
			{
				month = "0"+mm;
			}
		}
		if(month.length()==1)
		{
			
			month = "0"+month;
		}
		return this.dao.query4Json(tenancyID,"select max(id) as id,report_date from pos_bill2 where store_id="+store_id+" and to_char(report_date, 'yyyy-mm-dd') like '"+year+"-"+month+"%'  GROUP BY report_date ORDER BY report_date desc");
	}

	/* (non-Javadoc)
	 * @see com.tzx.boh.po.springjdbc.dao.BohPropertyInterfaceDao#getMonth(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public List<JSONObject> getMonth(String tenancyID, JSONObject condition) throws Exception
	{
		List<JSONObject> obj=new ArrayList<JSONObject>();
		int year=condition.optInt("id");
		Calendar a=Calendar.getInstance();
		int i = a.get(Calendar.YEAR);		
		int month = a.get(Calendar.MONTH) + 1;
		if(i!=year){
			month=12;
		}
		JSONObject jb;
		for(int j=1;j<month+1;j++)
		{
			jb=new JSONObject();
			String monthStr=j+"";
			jb.put("id",j );
			if(j<10){
				monthStr="0"+monthStr;
			}
			
			jb.put("text", monthStr);
			obj.add(jb);
		}
		return obj;
	}

	/* (non-Javadoc)
	 * @see com.tzx.boh.po.springjdbc.dao.BohPropertyInterfaceDao#stop(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject stop(String tenancyID, JSONObject condition) throws Exception
	{   
		JSONObject result=new JSONObject();
		//StringBuilder sb=new StringBuilder();
		
		condition.element("valid_state", "0");
		int updateIgnorCase = this.dao.updateIgnorCase(tenancyID, "boh_property_interface_parameter", condition);
		if(updateIgnorCase==0)
		{
			result.put("msg","修改失败！");
			
		}
		else
		{	
			result.put("success", true);
			result.put("msg", "修改成功！");	
		}
		
		return result;
	}

	@Override
	public JSONObject findTypeId(String attribute, JSONObject p) throws Exception {
		// TODO Auto-generated method stub
		JSONObject result=new JSONObject();
		StringBuilder sb=new StringBuilder();
		sb.append(" select * from  boh_property_interface_parameter where store_id ='"+p.getString("store_id")+"' and  valid_state ='1'");
		List<JSONObject> query4Json = this.dao.query4Json(attribute,sb.toString());
		if(query4Json.size()!=0) {
			result=query4Json.get(0);
		}
		return result;
	}

	@Override
	public JSONObject findOrgan(String attribute, JSONObject p) throws Exception {
		// TODO Auto-generated method stub
		JSONObject result=new JSONObject();
		StringBuilder sb=new StringBuilder();
		sb.append(" select * from hq_organ_property_info where store_id ='"+p.getString("store_id")+"'");
		List<JSONObject> query4Json = this.dao.query4Json(attribute,sb.toString());
		if(query4Json.size()!=0) {
			result=query4Json.get(0);
		}
		result.put("success", true);
		return result;
	}

	@Override
	public JSONObject getLoginSession(JSONObject params) throws Exception {
	     String username  = params.optString("user_name");
	     String password =params.optString("password");
	     String storecode =params.optString("storecode");
	     String url = params.optString("url");
	     String getmessage = url+"/LSLogin?STORECODE="+storecode+"&LOGINNAME="+username+"&PASSWORD="+password;
	     URL u =null;
		try {
			u = new URL(getmessage);
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("请检查连接地址url 配置是否正确！");
			
		}
	        InputStream in = u.openStream();
	        ByteArrayOutputStream out = new ByteArrayOutputStream();
	        try {
	            byte buf[] = new byte[1024];
	            int read = 0;
	            while ((read = in.read(buf)) > 0) {
	                out.write(buf, 0, read);
	            }
	        } finally {
	            if (in != null) {
	                in.close();
	            }
	        }
	        byte b[] = out.toByteArray();	    
	        String result=new String(b,"GBK");  
	        
	        String[] split = result.split("=");
	        JSONObject obj = new JSONObject();

	        for (int i=0;i<split.length;i++){      
	        	System.out.println(split[i]);
	        	if(split[i].contains("FRESULT")){
	        	}
	        	else	if(split[i].contains("FMSG")){
	        		String[] split2 = split[i].split("\r\n");
	        		obj.put("FRESULT", split2[0]);
	        	}
	        	else if(split[i].contains("FDATA")){
	        		String[] split2 = split[i].split("\r\n");
	        		obj.put("FMSG", split2[0]);
	        	}
	        	else{
	        		String[] split2 = split[i].split("\r\n");
	        		if(split2.length==0){
	        			obj.put("FDATA", "");
	        		}
	        		else{
	        			obj.put("FDATA", split2[0]);
	        		}
	        		
	        	}
	        }
	    
	 System.out.println(obj.toString());
	        
	        //可以使用fastjson解析
		return obj;
	}

	@Override
	public JSONObject sendGetMessage(String tenantId, JSONObject bill,
			JSONObject params,JSONObject obj ) throws Exception {
		// TODO Auto-generated method stub
	      String Session = params.optString("FDATA");//session
		   String url = obj.optString("url");//连接地址
		   String username  = obj.optString("user_name");//设备号
		   String FILTIME = bill.optString("payment_time");
		   String FLOWNO = bill.optString("report_date");
		   Double payment_amount = bill.optDouble("payment_amount",0.0); 
		   String getmessage = url+"/ILSUPLOADTRANS?DEVNAME="+username+"&FLOWNO="+FLOWNO+"&FILTIME="+FILTIME+"&REALAMT="+payment_amount+"&SESSION="+Session;
		     URL u =null;
			try {
				u = new URL(getmessage);
			} catch (Exception e) {
				e.printStackTrace();
				throw new Exception("请检查连接地址url 配置是否正确！");
				
			}
		        InputStream in = u.openStream();
		        ByteArrayOutputStream out = new ByteArrayOutputStream();
		        try {
		            byte buf[] = new byte[1024];
		            int read = 0;
		            while ((read = in.read(buf)) > 0) {
		                out.write(buf, 0, read);
		            }
		        } finally {
		            if (in != null) {
		                in.close();
		            }
		        }
		        byte b[] = out.toByteArray();	    
		        String result=new String(b,"GBK");  
		        
		        String[] split = result.split("=");
		        JSONObject obj1 = new JSONObject();

		        for (int i=0;i<split.length;i++){      
		        	System.out.println(split[i]);
		        	if(split[i].contains("FRESULT")){
		        	}
		        	else	if(split[i].contains("FMSG")){
		        		String[] split2 = split[i].split("\r\n");
		        		obj1.put("FRESULT", split2[0]);
		        	}
		        	else{
		        		String[] split2 = split[i].split("\r\n");
		        		if(split2.length==0){
		        			obj1.put("FMSG", "");
		        		}
		        		else{
		        			obj1.put("FMSG", split2[0]);
		        		}
		        		
		        	}
		        }
		        System.out.println(obj1.toString());
		return obj1;
	}
	
}
