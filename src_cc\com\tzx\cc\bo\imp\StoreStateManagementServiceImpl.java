package com.tzx.cc.bo.imp;

import java.sql.Timestamp;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.tzx.cc.bo.StoreStateManagementService;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.dao.GenericDao;

@Service(StoreStateManagementService.NAME)
public class StoreStateManagementServiceImpl implements StoreStateManagementService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Override
	public JSONObject loadStoreStateList(String tenancyID, JSONObject condition) throws Exception
	{
		long current = System.currentTimeMillis();
		long storeStatusPeriod = Scm.ti(Constant.getSystemMap().get("storestatus_minite_period")) * 60 * 1000;
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append("select d.* from (");
		sql.append("select c.cc_version,a.*, b.org_full_name,b.organ_code,b.operating_status,b.operating_status,");
		sql.append("case when c.id is null then '' when to_timestamp(c.refresh_time,'yyyy-MM-dd HH24:MI:ss') < '").append(DateUtil.format(new Timestamp(current - 5 * storeStatusPeriod))).append("' then '01' ");
		sql.append("when to_timestamp(c.refresh_time,'yyyy-MM-dd HH24:MI:ss') < '").append(DateUtil.format(new Timestamp(current - 3 * storeStatusPeriod))).append("' then '02' ");
		sql.append("else '03' end as store_state");
		sql.append(" from hq_organ a left join organ b on a.organ_id=b.id left join sys_store_online_state c on c.store_id=a.organ_id where 1=1");
		
		//添加门店 权限   2016年8月12日15:00:09   xgy  begin
		if(condition.containsKey("authority_organ")) {
			String authority = condition.optString("authority_organ");
			if(StringUtils.isNotBlank(authority)) {
				sql.append(" and b.id in (").append(authority).append(")");
			}
		}
		//添加门店 权限   2016年8月12日15:00:09   xgy  end
		
		if (condition.containsKey("store_id") && !"".equals(condition.optString("store_id")) && !"0".equals(condition.optString("store_id")))
		{
			sql.append(" and  b.id = " + condition.optString("store_id") + "");
		}
		if (condition.containsKey("organ_code") && !"".equals(condition.optString("organ_code")))
		{
			sql.append(" and  b.organ_code like '%" + condition.optString("organ_code") + "%'");
//			sql.append(" and b.id in (select * from get_oids_bycode('"+condition.get("organ_code").toString().trim()+"')) ");
		}

		if (condition.containsKey("store_name") && !"".equals(condition.optString("store_name")))
		{
			sql.append(" and  b.org_full_name like '%" + condition.optString("store_name") + "%'");
		}
		sql.append(") as d");
		if (condition.containsKey("store_state") && !"".equals(condition.optString("store_state")))
		{
			sql.append(" where d.store_state like '%" + condition.optString("store_state") + "%'");

		}

		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenancyID, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));

		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}

}
