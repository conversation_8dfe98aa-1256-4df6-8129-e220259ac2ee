package com.tzx.report.bo.commonreplace;

import com.tzx.framework.bo.dto.Roles;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import javax.servlet.http.HttpSession;
import java.util.List;


public interface CommonMethodAreaService {
	
	/**
	 * 付款方式
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getPaymentDetails(String tenancyID,JSONObject condition) throws Exception;
	
	/**
	 * 付款方式按照付款方式
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getPaymentDetailsOrderByClass(String tenancyID,JSONObject condition) throws Exception;
	
	/**
	 * 报表说明
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getExplain(String tenancyID,JSONObject condition) throws Exception;
	
	/**
	 * 能源报表字段
	 * @param attribute
	 * @param p
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getEnergyConsumption(String attribute, JSONObject p) throws Exception;
	
	
	/**
	 * 导出
	 * @param tenancyID
	 * @param json
	 * @param workBook
	 * @return
	 * @throws Exception
	 */
	public HSSFWorkbook exportDate(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception;

	/**
	 * pos区分正餐快餐
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	JSONObject getPosDinnerSnack(String tenancyID,JSONObject condition) throws Exception;
	
	/**
	 * 写入execl中列的行
	 * @param rowNum
	 * @param jin
	 * @return
	 *//*
	public JSONObject exportContrastline(int rowNum,int jin);
	
	
	*//**
	 * execl导出反射机制
	 * @param tenancyID
	 * @param json
	 * @return
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 * @throws IllegalAccessException
	 * @throws NoSuchMethodException
	 * @throws SecurityException
	 * @throws IllegalArgumentException
	 * @throws InvocationTargetException
	 *//*
	public JSONObject getInvokeMethod(String tenancyID,JSONObject json) throws ClassNotFoundException, InstantiationException, 
		IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException;
	
	
	*//**
	 * execl导出第一层
	 * @param tenancyID
	 * @param json
	 * @return
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 * @throws IllegalAccessException
	 * @throws NoSuchMethodException
	 * @throws SecurityException
	 * @throws IllegalArgumentException
	 * @throws InvocationTargetException
	 *//*
	public List<JSONObject>  getMethodOne(String tenancyID,JSONObject json) throws ClassNotFoundException, InstantiationException, 
	IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException;
	
	*//**
	 * execl导出第二层
	 * @param tenancyID
	 * @param json
	 * @param stratIndex2
	 * @return
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 * @throws IllegalAccessException
	 * @throws NoSuchMethodException
	 * @throws SecurityException
	 * @throws IllegalArgumentException
	 * @throws InvocationTargetException
	 *//*
	public List<JSONObject>  getMethodTwo(String tenancyID,JSONObject json,int stratIndex2) throws ClassNotFoundException, InstantiationException, 
	IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException;
	
	*//**
	 * execl导出第三层
	 * @param tenancyID
	 * @param json
	 * @param stratIndex3
	 * @return
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 * @throws IllegalAccessException
	 * @throws NoSuchMethodException
	 * @throws SecurityException
	 * @throws IllegalArgumentException
	 * @throws InvocationTargetException
	 *//*
	public List<JSONObject>  getMethodThree(String tenancyID,JSONObject json,int stratIndex3) throws ClassNotFoundException, InstantiationException,
	IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException;

	
	*//**
	 * 1,2,3层写入execl
	 *//*
	public void execlrp(List<JSONObject> data1,List<JSONObject> data2,List<JSONObject> data3,HSSFSheet sheet1,HSSFWorkbook workBook,Integer stratIndex,Integer stratIndex2,
			 Integer stratIndex3,JSONObject out1Result,JSONObject out1Result2,JSONObject out1Result3,String ArrHeader[][],JSONObject paramData);*/

	
	
	public Object[] exportDateNew(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception;
	
	public Object[] exportDateNewOrder(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception;
	/**
	 * 付款方式 无Status过滤
	 * @param attribute
	 * @param p
	 * @return
	 * @throws Exception
	 */
	List<JSONObject>  getPaymentDetailsStatus2(String attribute, JSONObject p)throws Exception;
	
	
	/**
	 * 税率查询
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getTaxRate(String tenancyID, JSONObject condition)throws Exception;
	
	//税率配置修改
	Object saveTaxRateModification(String tenancyID,String tableName,List<JSONObject> condition) throws Exception;

	 
	/**
	 * 更新文件状态
	 * @return
	 */
	Integer updateFileListState (String attribute,JSONObject json) ;

	/**
	 * 第一个获取文件状态含（插入）
	 * @param attribute
	 * @param p
	 * @return
	 */
	JSONObject  getFilesNames(String attribute, JSONObject p);

	/**
	 * 查询状态
	 * @param attribute 
	 * @param p
	 * @return
	 */
	JSONObject selectFilesStates(String attribute, JSONObject p);

	/**
	 * 取消文件
	 * @param attribute
	 * @param p
	 * @return
	 */
	JSONObject cancelFilesStates(String attribute, JSONObject p);

	/**
	 * 查询全部的文件
	 * @param attribute
	 * @param p
	 * @return
	 */
	JSONObject selectAllFilesStates(String attribute, JSONObject p);

	/**
	 * 根据id删除
	 * @param attribute
	 * @param p
	 * @return
	 */
	Object deleteFilesList(String attribute, JSONObject p);

	/**
	 * 根据id查询文件的属性
	 * @param attribute
	 * @param jsonObject
	 * @return
	 */
	List<JSONObject> selectFilesStatesById(String attribute, JSONObject jsonObject);
	
	/**
	 * 根据id删除
	 * @param attribute
	 * @param p
	 * @return
	 */
	Object deleteAllFilesList(String attribute, JSONObject p);
	
	/**
	 * 菜品选择
	 * @param tenancyId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getCategorySelect(String tenancyId, JSONObject obj) throws Exception;
	
	/**
	 * 菜品分类
	 * @param tenancyId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	 String loadCategoryTree(String tenancyId, JSONObject obj) throws Exception;
	
	 
	 /**
		 * 菜品类别查询 含大类小类
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public String customize(String tenancyId, Integer type, Object param) throws Exception;

	/**
	 * 查询渠道	
	 * @param attribute
	 * @param parseInt
	 * @param p
	 * @return
	 */
		public String getChanelType(String attribute, int parseInt, JSONObject p);

	/**
	 * 查询门店
	 * @param attribute
	 * @param attribute2
	 * @param attribute3
	 * @param p
	 * @param conditions
	 * @return
	 * @throws Exception
	 */
		public String getOrgansTreeByConditios(String attribute, String attribute2,
				String attribute3, JSONObject p, String conditions) throws Exception;

	/**
	 *  查询账单的表头数据	
	 * @param attribute
	 * @param p
	 * @return
	 * @throws Exception
	 */
	public JSONObject getBillTitleData(String attribute, JSONObject p) throws Exception;
	
	/**
	 *  优惠券条件下拉框
	 * @param tenancyId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	 public List<JSONObject> getCoupon(String tenancyID, JSONObject condition)throws Exception;
	 /**
	  *  味千——获取菜品大类
	  * @param tenancyId
	  * @param obj
	  * @return
	  * @throws Exception
	  */
	 public List<JSONObject> getItemDl(String tenancyID, JSONObject condition)throws Exception;
	 /**
	  *  味千——获取菜品小类
	  * @param tenancyId
	  * @param obj
	  * @return
	  * @throws Exception
	  */
	 public List<JSONObject> getItemXl(String tenancyID, JSONObject condition)throws Exception;
	 /**
	  *  味千——获取菜品名称
	  * @param tenancyId
	  * @param obj
	  * @return
	  * @throws Exception
	  */
	 public List<JSONObject> getItemName(String tenancyID, JSONObject condition)throws Exception;
		 
	 public String getOrgansTreeByConditionsV2(String attribute, String attribute2,
				String attribute3, JSONObject p, String conditions)throws Exception;
	 
	/**
	 * 获取自定义表头
	 * @throws Exception
	 */
	List<JSONObject> getReportTH(String attribute, JSONObject p)throws Exception;
	//新增或修改表头
	JSONObject addOrUpdTH(String tenancyID,String tableName,List<JSONObject> array,String type) throws Exception;
	
	/**
	  * Hbase访问权限
	  * @param tenancyID
	  * @param condition
	  * @return
	  */
	 public Boolean getHBASEPrivilege(String tenancyID, JSONObject condition) throws Exception;	
	 
	 /**
	  * Hbase访问权限
	  * @param tenancyID
	  * @param condition
	  * @return
	  */
	 public Boolean getHBASEPrivileges() throws Exception;	
	 
	 
		/**
		 * 查询渠道通过品牌	
		 * @param attribute
		 * @param parseInt
		 * @param p
		 * @return
		 * @throws Exception 
		 */
	  public String getChanelTypeByBrand(String attribute,JSONObject p) throws Exception;
	  
	  /**
		 * 菜品类别查询通过品牌id
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		public String loadCategoryTreeByBrand(String tenancyId, Object param) throws Exception;
	  
		//多品牌渠道
		public List<JSONObject> findDicVal(String tenancyID,String field) throws Exception;
		//多品牌注册渠道
		public String registerChanel(String tenancyId, Integer type, Object param) throws Exception;
		//多品牌菜品类别
		public String getComboTree(String tenancyId, String chanel, String fatherId) throws Exception;

		public List<JSONObject> loadFirstLevel(String attribute, String sysuser, JSONObject p) throws Exception;
		
		public Integer getUserId(String tenancyId, String username) throws Exception;
		
		/**
		  *  POS本地存储门店信息到session
		 * @param session 
		  * @param tenancyId
		  * @param obj
		  * @return
		  * @throws Exception
		  */
		 public JSONObject localSession(HttpSession session, JSONObject condition) throws Exception;
		 
		//点击率同步至数据库
	    int[] updNewsClickRate(String tenancyID,String tableName,List<JSONObject> array) throws Exception;
	    
	    /**
		 * pos是否限制门店总部报表查询范围
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		JSONObject getPosLimitReportQueryArea(String tenancyID,JSONObject condition) throws Exception;
		
		/**
		 * 付款方式条件查询范围
		 * @param tenancyID
		 * @param condition
		 * @return
		 * @throws Exception
		 */
		JSONArray getPaymentRange(String tenancyID,JSONObject condition) throws Exception;

		Roles getUserRoles(String tenancyId,String employeeId) throws Exception;
}
