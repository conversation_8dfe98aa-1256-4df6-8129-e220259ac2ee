package com.tzx.report.bo.imp.crm;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.crm.MemberOverallBusinessReportService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.common.util.ReportExportUtils;
import com.tzx.report.po.boh.dao.BusinessDiscountReportDao;

@Service(MemberOverallBusinessReportService.NAME)
public class MemberOverallBusinessReportServiceImp implements MemberOverallBusinessReportService
{
	 @Resource(name = BusinessDiscountReportDao.NAME)
	 BusinessDiscountReportDao businessDiscountReportDao;
	 
		@Resource
		private CommonMethodAreaService commonMethodAreaService;
		// 按照激活日期汇总
		private final String selectAddDate0 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JHRQL0'";
		private final String selectAddDate1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JHRQL1'";
		private final String selectAddDate2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JHRQL2'";
		private final String selectAddDate3 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JHRQL3'";
		
		//按照交易日期汇总
		private final String selectStore0 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JYRQL0'";
		private final String selectStore1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JYRQL1'";
		private final String selectStore2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JYRQL2'";
		private final String selectStore3 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JYRQL3'";
		private final String selectStore4 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JYRQL4'";
		
		// 按照注册门店汇总
		private final String selectAddStore0 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='ZCMDL0'";
		private final String selectAddStore1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='ZCMDL1'";
		private final String selectAddStore2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='ZCMDL2'";
		private final String selectAddStore3 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='ZCMDL3'";
		
		// 按照交易门店汇总
		private final String selectDiscountStore0 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JYMDL0'";
		private final String selectDiscountStore1 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JYMDL1'";
		private final String selectDiscountStore2 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JYMDL2'";
		private final String selectDiscountStore3 = "select sql from saas_report_engine where report_num = 'SAAS_BI_2016_35' and sql_type='JYMDL3'";
		@Resource(name = "genericDaoImpl")
		private GenericDao	dao;
		
		@Resource
		ParameterUtils parameterUtils;
		
		@Resource
		ConditionUtils conditionUtils;

		@Override
		public JSONObject getRestoreBill(String tenancyID, JSONObject condition) throws Exception {
			// TODO Auto-generated method stub
			Integer type = condition.optInt("selectType");
			StringBuilder sb =new StringBuilder();
			List<JSONObject> list = new ArrayList<JSONObject>();
			List<JSONObject> footerList =new ArrayList<JSONObject>();
			String begindate = condition.optString("begin_date");
			String enddate = condition.optString("end_date");
			JSONObject result = new JSONObject();
			String reportSql = "";
			String reportSqlCount = "";
			long total = 0L;
			if(begindate.length()>0 && enddate.length()>0 )
			{
				switch (type)
				{
				//按照激活日期汇总
				case 1: 
					if(condition.optInt("hierarchytype") ==1){
					reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectAddDate1);
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					
					 reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectAddDate0);
					footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString()); 
					
					}else if (condition.optInt("hierarchytype") ==2 && !condition.optString("activation_date").equals(null)) {
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectAddDate2);
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
					}else if (condition.optInt("hierarchytype") ==3 && !condition.optString("report_date").equals(null)){
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectAddDate3);
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}
				break;	
				// 按照交易日期汇总
				case 2: 
					if(condition.optInt("hierarchytype") ==1){
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore1);
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
						
						 reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore0);
						footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString()); 
						
					}else if (condition.optInt("hierarchytype") ==2 && !condition.optString("report_date").equals(null)) {
							reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore2);
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}else if (condition.optInt("hierarchytype") ==3 && !condition.optString("activation_date").equals(null)) {
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore3);
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}else if (condition.optInt("hierarchytype") ==4) {
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectStore4);
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
//						System.out.println("获取数据结果------"+list.toString());
					}
					break;
					// 按照注册门店汇总
				case 3: 
					if(condition.optInt("hierarchytype") ==1){
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectAddStore1);
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
						
					 reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectAddStore0);
						footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString()); 
						
					}else if (condition.optInt("hierarchytype") ==2 && !condition.optString("store_ids").equals(null)) {
							reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectAddStore2);
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}else if (condition.optInt("hierarchytype") ==3 && !condition.optString("report_date").equals(null)) {
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectAddStore3);
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}
					break;
					// 按照交易门店汇总	
				case 4: 
					if(condition.optInt("hierarchytype") ==1){
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDiscountStore1);
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						
						
						 reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDiscountStore0);
						footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString()); 
						
					}else if (condition.optInt("hierarchytype") ==2 && !condition.optString("discount_mode_id").equals(null)) {
							reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDiscountStore2);
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}else if (condition.optInt("hierarchytype") ==3 && !condition.optString("store_ids").equals(null)) {
						reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,selectDiscountStore3);
						total = this.dao.countSql(tenancyID,reportSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					}
					break;	
				default:
					break;
				}
			}
			
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			result.put("page", pagenum);
			result.put("total",total);	
			result.put("rows", list);
			result.put("footer", footerList);
			return result;
		}
		
	 


	@Override
	public HSSFWorkbook exportDate(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception {
		
		// TODO Auto-generated method stub
		// 备份一个json
		String type =p.optString("selectType");
		Integer rowNum=2;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",1);
		Integer stratIndex = 0 ;
		Integer stratIndex2 = 0 ;
		JSONObject findResult= getRestoreBill(attribute, p);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		  JSONObject out1Result =null;
		  JSONObject out1Result2 =null;
		  JSONObject out1Result3 =null;
	 	  Object discountMode =p.opt("old_store_id");
		  Object storeIds =p.opt("store_ids"); 
		  Object beginDate =p.opt("begin_date");
		  Object endDate =p.opt("end_date"); 
		  Object oldBeginDate=p.opt("old_begin_date");
		  Object oldEndDate=p.opt("old_end_date"); 
				//创建sheet 表格   同时还可以设置名字!  
				  HSSFSheet sheet1=workBook.createSheet("会员交易整体分析");
				  
				String [] listTitleName = {"激活日期","交易日期","交易门店","会员单数","营业单数","单数占比","储值消费","积分消费","其它消费","会员总额","营业总额","金额占比","会员交易单均"};
				String [] dataName ={"activation_date","report_date","store_name","member_num","sale_billnum","num_zb","trading_amount","credit_amount","other_amount","member_amount","sale_total","amount_zb","member_average",};
				String [] dataType ={"String","String","String","0","0","0.00%","0.00","0.00","0.00","0.00","0.00","0.00%","0.00"};
				 if(type.equals("2")) {
					 listTitleName = new String [] {"交易日期","激活日期","交易门店","会员单数","营业单数","单数占比","储值消费","积分消费","其它消费","会员总额","营业总额","金额占比","会员交易单均"};
					 dataName =new String []{"report_date","activation_date","store_name","member_num","sale_billnum","num_zb","trading_amount","credit_amount","other_amount","member_amount","sale_total","amount_zb","member_average",};
					 dataType =new String []{"String","String","String","0","0","0.00%","0.00","0.00","0.00","0.00","0.00","0.00%","0.00"};
				 }else if (type.equals("3") ) {
					 listTitleName = new String [] {"注册门店","交易日期","交易门店","会员单数","营业单数","单数占比","储值消费","积分消费","其它消费","会员总额","营业总额","金额占比","会员交易单均"};
					 dataName =new String []{"activation_name","report_date","store_name","member_num","sale_billnum","num_zb","trading_amount","credit_amount","other_amount","member_amount","sale_total","amount_zb","member_average",};
					 dataType =new String []{"String","String","String","0","0","0.00%","0.00","0.00","0.00","0.00","0.00","0.00%","0.00"};
				 } else if ( type.equals("4")) {
					 listTitleName = new String [] {"交易门店","交易日期","注册门店","会员单数","营业单数","单数占比","储值消费","积分消费","其它消费","会员总额","营业总额","金额占比","会员交易单均"};
					 dataName =new String []{"store_name","report_date","activation_name","member_num","sale_billnum","num_zb","trading_amount","credit_amount","other_amount","member_amount","sale_total","amount_zb","member_average",};
					 dataType =new String []{"String","String","String","0","0","0.00%","0.00","0.00","0.00","0.00","0.00","0.00%","0.00"};
				 }
				
				if(list1.size()>0){
					for(JSONObject json1 : list1) {
						// 调用到处方法；
						p.put("store_ids", storeIds);
						p.put("old_store_id", discountMode);
						p.put("old_begin_date", oldBeginDate);
						p.put("old_end_date", oldEndDate);
						p.put("end_date", endDate);
						p.put("begin_date", beginDate);
						out1Result =ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
						stratIndex =out1Result.optInt("rowNum");
						paramData.put("rowNum", out1Result.opt("rowNum"));
						paramData.put("jin", out1Result.optInt("jin"));
						p.put("hierarchytype",2);
						if(type.equals("1")) {
						p.put("activation_date", "'"+json1.opt("activation_date")+"'");
						}
						else if(type.equals("2")) {
						p.put("report_date", "'"+json1.opt("report_date")+"'");
						}else if (type.equals("3") ) {
							p.put("old_store_id",  json1.opt("id") );
						} else if (type.equals("4")) {
							p.put("store_ids",  json1.opt("id") );
						}
						JSONObject findResult2=getRestoreBill(attribute, p);
						List<JSONObject> list2=(List<JSONObject>) findResult2.opt("rows");
						if(list2.size()>0) {
							
							for(JSONObject json2 : list2) {
								out1Result2 = ReportExportUtils.out1(json2,workBook,sheet1,null,dataName,dataType,paramData);
								stratIndex2 =out1Result2.optInt("rowNum");
								paramData.put("rowNum", out1Result2.opt("rowNum"));
								paramData.put("jin", out1Result2.optInt("jin"));
								p.put("hierarchytype",3);
									if(type.equals("1")) {
						   	             p.put("report_date", "'"+json2.opt("report_date")+"'");
						   	          }else if(type.equals("2")) {
						   	             p.put("activation_date", "'"+json2.opt("activation_date")+"'");
						   	          }else if (type.equals("3") ) {
						   	            p.put("report_date", "'"+json2.opt("report_date")+"'");
						   	          }else if ( type.equals("4")) {
						   	            p.put("report_date", "'"+json2.opt("report_date")+"'");
						   	          }
								JSONObject findResult3=getRestoreBill(attribute, p);
								List<JSONObject> list3=(List<JSONObject>) findResult3.opt("rows");
								if(list3.size()>0) {
									for(JSONObject json3 : list3) {
										out1Result3 = ReportExportUtils.out1(json3,workBook,sheet1,null,dataName,dataType,paramData);
										paramData.put("rowNum", out1Result3.opt("rowNum"));
										paramData.put("jin", out1Result3.optInt("jin"));
									}
									sheet1.groupRow(stratIndex2,out1Result3.optInt("rowNum"));
							}
						}
						sheet1.groupRow(stratIndex,out1Result3.optInt("rowNum"));
					}
					
				}
					sheet1.groupRow(1,out1Result3.optInt("rowNum"));
					sheet1.setRowSumsBelow(false);
					sheet1.setRowSumsRight(false);
				}
				
				HSSFRow rowtitle =sheet1.createRow(0);
				
				List<JSONObject> titleArr = new ArrayList() ;
				JSONObject sonJson =new JSONObject();
				sonJson.put("titleName", "会员交易笔数分析");
				sonJson.put("index", 3);
				sonJson.put("end", 5);
				titleArr.add(sonJson);
				
				JSONObject sonJson2 =new JSONObject();
				sonJson2.put("titleName", "会员交易金额分析");
				sonJson2.put("index", 6);
				sonJson2.put("end", 11);
				titleArr.add(sonJson2);
				
				sheet1 =ReportExportUtils.mergrColumn(workBook,sheet1,rowtitle,titleArr);
				
				HSSFRow rowtitle2 =sheet1.getRow(1);
				Integer [] valueNum ={0,1,2,12};
				//合并上下的行 限制一列
				sheet1 =ReportExportUtils.upOrDownMergr(workBook, sheet1, valueNum, rowtitle2, rowtitle, 0, 1);
				
				 
				
				return workBook;
	    }

	
	
	@Override
	public HSSFWorkbook exportDate2(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception {
		
		// TODO Auto-generated method stub
		// 备份一个json
		String type =p.optString("selectType");
		Integer rowNum=2;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",1);
		Integer stratIndex = 0 ;
		Integer stratIndex2 = 0 ;
		JSONObject findResult= getRestoreBill(attribute, p);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		  JSONObject out1Result =null;
	 	  Object discountMode =p.opt("old_store_id");
		  Object storeIds =p.opt("store_ids"); 
		  Object beginDate =p.opt("begin_date");
		  Object endDate =p.opt("end_date"); 
				//创建sheet 表格   同时还可以设置名字!  
				  HSSFSheet sheet1=workBook.createSheet("会员交易整体分析");
				  
				String [] listTitleName = {"交易门店","会员单数","营业单数","单数占比","储值消费","积分消费","其它消费","会员总额","营业总额","金额占比","会员交易单均"};
				String [] dataName ={"store_name","member_num","sale_billnum","num_zb","trading_amount","credit_amount","other_amount","member_amount","sale_total","amount_zb","member_average",};
				String [] dataType ={"String","0","0","0.00%","0.00","0.00","0.00","0.00","0.00","0.00%","0.00"};
				 if(type.equals("2")) {
					 listTitleName = new String [] {"交易门店","会员单数","营业单数","单数占比","储值消费","积分消费","其它消费","会员总额","营业总额","金额占比","会员交易单均"};
					 dataName =new String []{"store_name","member_num","sale_billnum","num_zb","trading_amount","credit_amount","other_amount","member_amount","sale_total","amount_zb","member_average",};
					 dataType =new String []{"String","0","0","0.00%","0.00","0.00","0.00","0.00","0.00","0.00%","0.00"};
				 }
				
				if(list1.size()>0){
					for(JSONObject json1 : list1) {
						// 调用到处方法；
						p.put("store_ids", storeIds);
						p.put("old_store_id", discountMode);
						p.put("end_date", endDate);
						p.put("begin_date", beginDate);
						out1Result =ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
						stratIndex =out1Result.optInt("rowNum");
						paramData.put("rowNum", out1Result.opt("rowNum"));
						paramData.put("jin", out1Result.optInt("jin"));
						p.put("hierarchytype",4);
						if(type.equals("2")) {
						p.put("report_date", "'"+json1.opt("report_date")+"'");
						}
				}
					sheet1.setRowSumsBelow(false);
					sheet1.setRowSumsRight(false);
				}
				
				HSSFRow rowtitle =sheet1.createRow(0);
				
				List<JSONObject> titleArr = new ArrayList() ;
				JSONObject sonJson =new JSONObject();
				sonJson.put("titleName", "会员交易笔数分析");
				sonJson.put("index", 1);
				sonJson.put("end", 3);
				titleArr.add(sonJson);
				
				JSONObject sonJson2 =new JSONObject();
				sonJson2.put("titleName", "会员交易金额分析");
				sonJson2.put("index", 4);
				sonJson2.put("end", 9);
				titleArr.add(sonJson2);
				
				sheet1 =ReportExportUtils.mergrColumn(workBook,sheet1,rowtitle,titleArr);
				
				HSSFRow rowtitle2 =sheet1.getRow(1);
				Integer [] valueNum ={0,10};
				//合并上下的行 限制一列
				sheet1 =ReportExportUtils.upOrDownMergr(workBook, sheet1, valueNum, rowtitle2, rowtitle, 0, 1);
				
				 
				
				return workBook;
	    }

}