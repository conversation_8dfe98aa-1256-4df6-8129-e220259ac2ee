package com.tzx.boh.service.rest;

import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.boh.bo.MeterreadingManagementNewService;
import com.tzx.boh.bo.MeterreadingManagementService;
import com.tzx.framework.bo.DataDictionaryService;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Tools;
/**
 * <AUTHOR>
 */
@Controller("MeterreadingManagementNewContraller")
@RequestMapping("/boh/meterreadingManagementNewContraller")
public class MeterreadingManagementNewRest
{
	@Resource(name = MeterreadingManagementNewService.NAME)
	private MeterreadingManagementNewService mns;

	@Autowired
	private DataDictionaryService dataDictionaryService;

	/**
	 * 获取离选择的业务日期最近的抄表记录
	 */
	@RequestMapping(value = "/loadLastMeterreadingInformation")
	public void loadLastMeterreadingInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			String lastReading= mns.loadMeterreadingInformation((String) session.getAttribute("tenentid"), obj).toString();
			result = "{\"lastReading\": "+lastReading+"}";
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 保存抄表信息
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/saveMeterreadingInformation")
	public void saveMeterreadingInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			JSONObject checkObj = JSONObject.fromObject("{}");
			Map<String,String[]> map = request.getParameterMap();
			for(String key : map.keySet())
			{
				 if("water_utilit_id".equals(key)||"business_date".equals(key)){
					 checkObj.put(key, map.get(key)[0]);
				}
			}
			boolean rsFlag = mns.checkUnique((String) session.getAttribute("tenentid"),checkObj);
			JSONObject obj = JSONObject.fromObject("{}");
			for(String key : map.keySet())
			{
				 if("id".equals(key)&& rsFlag==false){
					obj.put("id", "");
				}
				else {
					obj.put(key, map.get(key)[0]);
				}
				
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			obj.put("recprd_type","read");
			Object dic = dataDictionaryService.save((String) session.getAttribute("tenentid"), "boh_water_utility_record", obj);
			if (dic != null) result = "{\"success\": true, \"id\" : \"" + dic.toString() + "\"}";
		}
		catch (Exception e)
		{
			result = "{\"success\": false, \"msg\" : \"" + e.getMessage() + "\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 抄表信息增加列表
	 */
	@RequestMapping(value = "/loadAddMeterreadingInformation")
	public void loadAddMeterreadingInformation(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			obj.put("organ_id",(String) session.getAttribute("organ_id"));
			if(!obj.containsKey("organ_code")){
				obj.put("organ_code",(String) session.getAttribute("organ_code"));
			}
			obj.put("is_zb",(String) session.getAttribute("organ_code"));
			obj.put("user_organ_codes_group", (String) session.getAttribute("user_organ_codes_group"));
			result = mns.loadAddMeterreadingInformation((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	//验证同样的业务日期 相同的水表/电表/气表 是否已抄表
	@RequestMapping(value = "/checkUnique")
	public void checkUnique(HttpServletRequest request, HttpServletResponse response)
	{


		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			boolean rs = mns.checkUnique((String) session.getAttribute("tenentid"),obj);

			if(rs)
			{
				result = "{\"success\": true}";
			}else
			{
				result = "{\"success\": false}";
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	
	}
	
	
	
	/**
	 *
	 */
	@RequestMapping(value = "/findMeterTypeByStoreId")
	public void findMeterTypeByStoreId(HttpServletRequest request, HttpServletResponse response){
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String tenancyId = (String) session.getAttribute("tenentid");
			JSONObject returnJson = new JSONObject();
			List<JSONObject> list = null;
			try {
				JSONObject obj = new JSONObject();

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet()){
					obj.put(key, map.get(key)[0]);
				}
				out = response.getWriter();
				returnJson = mns.findMeterTypeByStoreId(tenancyId,obj);
				returnJson.put("oid",(String) session.getAttribute("organ_code"));
			} catch (Exception e) {
				e.printStackTrace();
			}finally{
				if(null != out){
					out.print(returnJson.toString());
					out.flush();
					out.close();
				}
			}
		}
	
	
	/**
	 * 根据store_id查询最新的数据
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/findLastDataByStoreId")
	public void findLastDataByStoreId(HttpServletRequest request, HttpServletResponse response){
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String tenancyId = (String) session.getAttribute("tenentid");
			JSONObject returnJson = new JSONObject();
			List<JSONObject> list = null;
			try {
				JSONObject obj = new JSONObject();

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet()){
					obj.put(key, map.get(key)[0]);
				}
				out = response.getWriter();
				list = mns.findLastDataByStoreId(tenancyId,obj);
			} catch (Exception e) {
				e.printStackTrace();
			}finally{
				if(null != out){
					out.print(JSONArray.fromObject(list).toString());
					out.flush();
					out.close();
				}
			}
		}
	
	
	@RequestMapping(value = "/saveNewInfo")
	public void saveNewInfo(HttpServletRequest request, HttpServletResponse response){
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String tenancyId = (String) session.getAttribute("tenentid");
			JSONObject returnJson = new JSONObject();
			JSONObject checkReturnJson = null;
			List<JSONObject> list = null;
			try {
				JSONObject obj = new JSONObject();

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet()){
					obj.put(key, map.get(key)[0]);
				}
				JSONArray jsonArray = JSONArray.fromObject(obj.get("rows"));
				JSONObject object = new JSONObject();
				object.put("rows", jsonArray);
				object.put("last_operator", session.getAttribute("employeeName"));
				object.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
				out = response.getWriter();
				checkReturnJson = mns.checkBsDate((String) session.getAttribute("tenentid"),object);
				if(checkReturnJson.optBoolean("success")){
					//保存方法
					mns.saveNewInfo(tenancyId, object);
					result = "{\"success\": true, \"msg\" : \"保存成功\"}";
				}else{
					result = "{\"success\": false, \"msg\" : \""+checkReturnJson.optString("msg")+"\"}";
				}
				
			} catch (Exception e) {
				result = "{\"success\": false, \"msg\" : \"保存数据时发生异常\",\"excep\" : \""+e.getMessage()+"\"}";
				e.printStackTrace();
			}finally{
				if(null != out){
					out.print(result);
					out.flush();
					out.close();
				}
			}
		}
	
	
	
	@RequestMapping(value = "/updateUrl")
	public void updateUrl(HttpServletRequest request, HttpServletResponse response){
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String tenancyId = (String) session.getAttribute("tenentid");
			JSONObject returnJson = new JSONObject();
			JSONObject checkReturnJson = null;
			List<JSONObject> list = null;
			try {
				JSONObject obj = new JSONObject();

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet()){
					obj.put(key, map.get(key)[0]);
				}
				obj.put("last_operator", session.getAttribute("employeeName"));
				obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
				out = response.getWriter();
				JSONObject check = new JSONObject();
				JSONArray arr = new JSONArray();
				arr.add(obj);
				check.put("rows", arr);
				checkReturnJson = mns.checkBsDate((String) session.getAttribute("tenentid"),check);
				if(checkReturnJson.optBoolean("success")){
					//保存方法
					mns.updateUrl(tenancyId, obj);
					result = "{\"success\": true, \"msg\" : \"保存成功\"}";
				}else{
					result = "{\"success\": false, \"msg\" : \""+checkReturnJson.optString("msg")+"\"}";
				}
				
			} catch (Exception e) {
				result = "{\"success\": false, \"msg\" : \"保存数据时发生异常\",\"excep\" : \""+e.getMessage()+"\"}";
				e.printStackTrace();
			}finally{
				if(null != out){
					out.print(result);
					out.flush();
					out.close();
				}
			}
		}
	
	
	
	
	@RequestMapping(value = "/deleteUrl")
	public void deleteUrl(HttpServletRequest request, HttpServletResponse response){
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String tenancyId = (String) session.getAttribute("tenentid");
			JSONObject returnJson = new JSONObject();
			JSONObject checkReturnJson = null;
			List<JSONObject> list = null;
			try {
				JSONObject obj = new JSONObject();

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet()){
					obj.put(key, map.get(key)[0]);
				}
				out = response.getWriter();
				mns.deleteNew((String) session.getAttribute("tenentid"),obj);
				result = "{\"success\": true, \"msg\" : \"删除数据成功!\"}";
			} catch (Exception e) {
				result = "{\"success\": false, \"msg\" : \"删除数据时发生异常\",\"excep\" : \""+e.getMessage()+"\"}";
				e.printStackTrace();
			}finally{
				if(null != out){
					out.print(result);
					out.flush();
					out.close();
				}
			}
		}
	
	
	
	@RequestMapping(value = "/updateBeginningUrl")
	public void updateBeginningUrl(HttpServletRequest request, HttpServletResponse response){
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			String result = "";
			String tenancyId = (String) session.getAttribute("tenentid");
			JSONObject returnJson = new JSONObject();
			JSONObject checkReturnJson = null;
			List<JSONObject> list = null;
			try {
				JSONObject obj = new JSONObject();

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet()){
					obj.put(key, map.get(key)[0]);
				}
				out = response.getWriter();
				obj.put("last_operator", session.getAttribute("employeeName"));
				obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
				mns.updateBeginningUrl((String) session.getAttribute("tenentid"),obj);
				result = "{\"success\": true, \"msg\" : \"更新期初值成功!\"}";
			} catch (Exception e) {
				result = "{\"success\": false, \"msg\" : \"更新期初值时发生异常\",\"excep\" : \""+e.getMessage()+"\"}";
				e.printStackTrace();
			}finally{
				if(null != out){
					out.print(result);
					out.flush();
					out.close();
				}
			}
		}
}
