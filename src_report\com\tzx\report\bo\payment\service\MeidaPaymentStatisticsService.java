package com.tzx.report.bo.payment.service;

import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

public interface MeidaPaymentStatisticsService
{
	String NAME = "com.tzx.report.bo.payment.impl.MeidaPaymentStatisticsServiceImpl";
 
	/**
	 * om接口调用()
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	JSONObject getMeidaPaymentStatisticsQuery(String tenancyID,JSONObject condition) throws Exception;


	/**
	 * 对账统计报表
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	JSONObject getMeidaPaymentAccountQuery(String tenancyID,JSONObject condition) throws Exception;


	JSONObject getLocalMeidaPaymentAccountQuery(String tenancyID,JSONObject condition) throws Exception;
	/**
	 * 导出
	 * @param attribute
	 * @param p
	 * @param workBook
	 * @return
	 * @throws Exception 
	 */
	
	HSSFWorkbook exportData(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception;


	/**
	 * 导出
	 * @param attribute
	 * @param p
	 * @param workBook
	 * @return
	 * @throws Exception
	 */

	HSSFWorkbook exportAccountData(String attribute, JSONObject p,
							HSSFWorkbook workBook) throws Exception;
}
