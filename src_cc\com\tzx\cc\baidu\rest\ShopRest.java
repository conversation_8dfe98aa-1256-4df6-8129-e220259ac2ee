package com.tzx.cc.baidu.rest;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import kafka.utils.Json;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.jsoup.helper.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.cc.baidu.bo.ShopService;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.common.constant.DishOper;
import com.tzx.cc.common.constant.util.CcBusinessLogUtils;
import com.tzx.cc.common.redis.service.CcRedisService;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.EleHelper;
import com.tzx.cc.thirdparty.util.ElmUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.cc.thirdparty.util.MeiTuanHelper;
import com.tzx.cc.thirdparty.util.XmdWMUtils;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.framework.log4j.util.LoggerUtil;
import com.tzx.pos.base.controller.BaseController;

import eleme.openapi.sdk.api.entity.product.OCategory;
import eleme.openapi.sdk.api.entity.product.OItem;
import eleme.openapi.sdk.api.entity.product.OSpec;
import eleme.openapi.sdk.api.service.ProductService;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;

/**
 * <AUTHOR>
 *
 */
@Controller("ShopRest")
@RequestMapping("/thirdparty/shoprest")
public class ShopRest extends BaseController {
	private Logger log = LoggerFactory.getLogger(this.getClass());

	@Resource(name = ShopService.NAME)
	private ShopService shopService;

	/**
	 * 获取商户信息（本地）
	 *
	 * @param request
	 * @param responsee
	 */
	@RequestMapping(value = "getShopInitInfo")
	public void getShopInitInfo(HttpServletRequest request,
								HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");

		PrintWriter writer = null;

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");

		String shopId = request.getParameter("shopId");

		String channel = request.getParameter("channel");

		DBContextHolder.setTenancyid(tenantId);

		JSONObject json = new JSONObject();

		json.put("errno", 0);

		try {
			JSONObject jo = shopService.getShopInitInfo(tenantId, shopId,
					channel);
			json.put("data", jo);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", Constant.MSG_1);
		}

		try {
			writer = response.getWriter();
			writer.print(json.toString());
			writer.flush();
			writer.close();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}

	}

	/**
	 * 查看商户列表（本地）
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "shopList")
	public void shopList(HttpServletRequest request,HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");

		PrintWriter writer = null;

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");
		DBContextHolder.setTenancyid(tenantId);

		JSONObject obj = new JSONObject();
		Map<String, String[]> map = request.getParameterMap();
		for (String key : map.keySet()) {
			obj.put(key, map.get(key)[0]);
		}


		// 添加门店 权限 2016年8月12日14:39:44 xgy begin
		String conditions = (String) request.getSession().getAttribute(
				"user_organ_codes_group");
		obj.put("authority_organ", conditions);
		// 添加门店 权限 2016年8月12日14:39:44 xgy end

		String result = "[]";

		try {
			result = shopService.shopList(tenantId, obj).toString();
			writer = response.getWriter();
			writer.print(result.toString());
			writer.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}
	}

	/**
	 * 创建/修改商户
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "shopCreate")
	public @ResponseBody
	JSONObject shopCreate(HttpServletRequest request,
						  HttpServletResponse response, @RequestBody JSONObject param) {

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");

		DBContextHolder.setTenancyid(tenantId);

		JSONObject json = new JSONObject();

		try {
			param.put("operator",
					request.getSession().getAttribute("employeeName"));
			param.put("operate_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			param.put("min_order_price", param.optString("min_order_price"));
			json = shopService.shopCreateOrUpdate(tenantId, param);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", Constant.MSG_1+":【"+json.optString("error")+"】");
			// logger.info("==========新增商户失败：" + e);
		}

		return json;
	}

	/**
	 * 保存并推送商户
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/thirdShopCreateOrUpdate")
	public @ResponseBody
	JSONObject thirdShopCreateOrUpdate(HttpServletRequest request,
									   HttpServletResponse response) {
		HttpSession session = request.getSession();
		JSONObject obj = JSONObject.fromObject("{}");
		Map<String, String[]> map = request.getParameterMap();
		for (String key : map.keySet()) {
			obj.put(key, map.get(key)[0]);
		}

		String tenantId = (String) session.getAttribute("tenentid");
		obj.put("business_license_url", obj.optString("photo1"));
		obj.put("food_distribution_license_url", obj.optString("photo2"));
		obj.put("catering_service_license_url", obj.optString("photo3"));
		obj.put("invoice_support", obj.optString("invoice_support_save"));

		JSONObject json = new JSONObject();

		try {

			json = shopService.shopCreateOrUpdate(tenantId, obj);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", Constant.MSG_1);
		}

		return json;
	}

	/**
	 * 更新商户状态
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "shopUpdate")
	public @ResponseBody
	JSONObject shopUpdate(HttpServletRequest request,
						  HttpServletResponse response, @RequestBody JSONObject param) {

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");

		DBContextHolder.setTenancyid(tenantId);

		JSONObject json = new JSONObject();

		try {
			param.put("operator",
					request.getSession().getAttribute("employeeName"));
			param.put("operate_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			json = shopService.shopSetStatus(tenantId, param);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", e.getMessage());
			// logger.info("=========更新商户状态失败：" + e);
		}

		return json;

	}

	/**
	 * 保存预计送达时间
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "saveDeliveryTime")
	public @ResponseBody
	JSONObject saveDeliveryTime(HttpServletRequest request,
								HttpServletResponse response, @RequestBody JSONObject param) {

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");

		DBContextHolder.setTenancyid(tenantId);

		JSONObject json = new JSONObject();

		try {
			param.put("operator",
					request.getSession().getAttribute("employeeName"));
			param.put("operate_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			json = shopService.saveDeliveryTime(tenantId, param);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", Constant.MSG_1);
		}

		return json;

	}

	/**
	 * 保存配送范围
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "saveDeliveryRegion")
	public @ResponseBody
	JSONObject saveDeliveryRegion(HttpServletRequest request,
								  HttpServletResponse response, @RequestBody JSONObject param) {

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");

		DBContextHolder.setTenancyid(tenantId);

		JSONObject json = new JSONObject();

		try {
			param.put("operator",
					request.getSession().getAttribute("employeeName"));
			param.put("operate_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			json = shopService.saveDeliveryRegion(tenantId, param);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", Constant.MSG_1);
		}

		return json;

	}

	/**
	 * 获取菜品分类列表
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getDishCategoryList")
	public void getDishCategoryList(HttpServletRequest request,
									HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			// 添加门店 权限 2016年8月12日13:40:05 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);
			String classID = params.optString("class_id");
			if (classID.equals("==全部==")) {
				params.put("class_id", "");
			}
			// 添加门店 权限 2016年8月12日13:40:05 xgy end
			try {
				result = shopService.getLocalDishCategoryList(tenantId, params)
						.toString();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 获取菜品列表
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getLocalDishList")
	public void getLocalDishList(HttpServletRequest request,
								 HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);
			// 添加门店 权限 2016年8月12日13:53:54 xgy end
			String classID = params.optString("class_id");
			if (classID.equals("==全部==")) {
				params.put("class_id", "");
			}
			result = shopService.getLocalDishList(tenantId, params).toString();

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 获取待操作菜品信息
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getLocalDishNoPageList")
	public void getLocalDishNoPageList(HttpServletRequest request,
									   HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			String classID = params.optString("class_id");
			if (classID.equals("==全部==")) {
				params.put("class_id", "");
			}
			result = shopService.getLocalDishNoPageList(tenantId, params)
					.toString();

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 获取菜品价格体系
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getLocalPriceSystem")
	public void getLocalPriceSystem(HttpServletRequest request,
									HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			result = shopService.getLocalPriceSystem(tenantId, params)
					.toString();

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 保存菜品分类(创建/更新)
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "saveDishCategory")
	public void saveDishCategory(HttpServletRequest request,
								 HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			params.put("requestId", requestId.toString());
			params.put("update_operator", session.getAttribute("employeeName"));
			params.put("update_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			params.put("send_operator", session.getAttribute("employeeName"));
			params.put("send_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("dishCategory");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:saveDishCategory");
			ccBusniessLogBean.setRequestBody(params.toString());


			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
			if(params.containsKey("dishes_class")){
				String thirdId = params.optJSONArray("dishes_class").getJSONObject(0).optString("third_class_id");
				ccBusniessLogBean.setThirdId(thirdId.equals("null")?"0":thirdId);
				ccBusniessLogBean.setTzxId(params.optJSONArray("dishes_class").getJSONObject(0).optString("class_id"));
				ccBusniessLogBean.setTzxName(params.optJSONArray("dishes_class").getJSONObject(0).optString("cur_class_name"));
				ccBusniessLogBean.setShopId(params.optJSONArray("dishes_class").getJSONObject(0).optString("store_id"));
			}else{
				ccBusniessLogBean.setThirdId(params.optString("third_class_id").equals("null")?"0":params.optString("third_class_id"));
				ccBusniessLogBean.setTzxId(params.optString("class_id"));
				ccBusniessLogBean.setTzxName(params.optString("cur_class_name"));
				ccBusniessLogBean.setShopId(params.optString("store_id"));
			}

			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.pushDishCategory.toString());

			result = shopService.saveDishCategory(tenantId, params).toString();
			ccBusniessLogBean.setResponseBody(result.toString());
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 饿了么2.0接口保存菜品分类顺序
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "setCategoryPosition")
	public void setDishCategoryPosition(HttpServletRequest request,
										HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}
			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			result = shopService.setDishCategoryPosition(tenantId, params)
					.toString();
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 保存菜品信息(创建/更新)
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "saveDish")
	public void saveDish(HttpServletRequest request,
						 HttpServletResponse response) throws Exception {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject params = new JSONObject();
			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}
			params.put("send_operator", session.getAttribute("employeeName"));
			params.put("send_time",DateUtil.format(new Timestamp(System.currentTimeMillis())));
			params.put("requestId",requestId.toString());
			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("dish");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:saveDish");
			ccBusniessLogBean.setRequestBody(params.toString());

			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			// 做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
			if (params.containsKey("dishes")) {
				ccBusniessLogBean.setThirdId(params.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
				ccBusniessLogBean.setTzxId(params.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
				ccBusniessLogBean.setTzxName(params.optJSONArray("dishes").getJSONObject(0).optString("item_name"));
				ccBusniessLogBean.setShopId(params.optJSONArray("dishes").getJSONObject(0).optString("store_id"));
			} else {
				ccBusniessLogBean.setThirdId(params.optString("third_item_id"));
				ccBusniessLogBean.setTzxId(params.optString("item_id"));
				ccBusniessLogBean.setTzxName(params.optString("item_name"));
				ccBusniessLogBean.setShopId(params.optString("store_id"));
			}

			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

			result = shopService.saveDish(tenantId, params).toString();

			ccBusniessLogBean.setResponseBody(result.toString());

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} finally {
			// 记录接口访问日志，便于定位问题分析
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 删除菜品类别
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "deleteDishCategory")
	public void deleteDishCategory(HttpServletRequest request,
								   HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			params.put("send_operator", session.getAttribute("employeeName"));
			params.put("send_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			result = shopService.deleteDishCategory(tenantId, params)
					.toString();

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 菜品信息更新(上线/下线）
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "updateDishState")
	public void updateDishState(HttpServletRequest request,
								HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			params.put("send_operator", session.getAttribute("employeeName"));
			params.put("send_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			result = shopService.updateDishState(tenantId, params).toString();

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 删除菜品
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "/deleteDish")
	public void deleteDish(HttpServletRequest request,
						   HttpServletResponse response) {

		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			params.put("operator",request.getSession().getAttribute("employeeName"));
			params.put("operate_time",DateUtil.format(new Timestamp(System.currentTimeMillis())));
			params.put("requestId",requestId.toString());

			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("dish");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:deleteDish");
			ccBusniessLogBean.setRequestBody(params.toString());

			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("1");

			// 做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
			if (params.containsKey("dishes")) {
				ccBusniessLogBean.setThirdId(params.optJSONArray("dishes")
						.getJSONObject(0).optString("third_item_id"));
				ccBusniessLogBean.setTzxId(params.optJSONArray("dishes")
						.getJSONObject(0).optString("item_id"));
				ccBusniessLogBean.setTzxName(params.optJSONArray("dishes")
						.getJSONObject(0).optString("item_name"));
				ccBusniessLogBean.setShopId(params.optJSONArray("dishes")
						.getJSONObject(0).optString("store_id"));
			} else {
				ccBusniessLogBean.setThirdId(params.optString("third_item_id"));
				ccBusniessLogBean.setTzxId(params.optString("item_id"));
				ccBusniessLogBean.setTzxName(params.optString("item_name"));
				ccBusniessLogBean.setShopId(params.optString("store_id"));
			}

			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

			result = shopService.deleteDish(tenantId, params).toString();

			ccBusniessLogBean.setResponseBody(result.toString());

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		} finally {
			// 记录接口访问日志，便于定位问题分析
			System.out.println("美团、饿了么Controller批量删除ID:"+ccBusniessLogBean.getRequestId()+"..............");
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}

	}

	@RequestMapping(value = "initCategoryMt")
	public void initCategoryMt(HttpServletRequest request,
							   HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");

		PrintWriter writer = null;

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");
		String shopId = request.getParameter("shopId");
		String channel = request.getParameter("channel");

		DBContextHolder.setTenancyid(tenantId);

		try {
			JSONObject params = new JSONObject();
			params.put("channel", "MT08");
			JSONObject obj = shopService.getShopCategory(tenantId, params);
			List list = obj.optJSONArray("data");
			writer = response.getWriter();
			writer.print(JSONArray.fromObject(list));
			writer.flush();
			writer.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}

	}

	@RequestMapping(value = "initMealsInfo")
	public void initMealsInfo(HttpServletRequest request,
							  HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");

		PrintWriter writer = null;

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");
		String shopId = request.getParameter("shopId");
		String channel = request.getParameter("channel");

		DBContextHolder.setTenancyid(tenantId);

		try {
			@SuppressWarnings("rawtypes")
			List list = shopService.initMealsInfo(tenantId, shopId, channel);
			writer = response.getWriter();
			writer.print(JSONArray.fromObject(list));
			writer.flush();
			writer.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}

	}

	/**
	 * 上传菜品图片（美团）
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "/uploadImage")
	public void uploadImage(HttpServletRequest request,
							HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			result = shopService.imageUpload(tenantId, params).toString();

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 根据机构id查询要推送的菜品类别
	 */
	@RequestMapping(value = "/loadDishCategoryListByStoreId")
	public void loadDishCategoryListByStoreId(HttpServletRequest request,
											  HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));

			String tenantId = (String) session.getAttribute("tenentid");
			String classID = obj.optString("class_id");
			if (classID.equals("==全部==")) {
				obj.put("class_id", "");
			}
			DBContextHolder.setTenancyid(tenantId);
			result = shopService.getLocalDishCategoryListNoPage(tenantId, obj)
					.toString();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e) {
			}

			try {
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			} catch (Exception e) {
			} finally {
				if (out != null)
					out.close();
			}
		}
	}

	// 保存复制菜品信息
	@RequestMapping(value = "/saveCopyDishInfo", method = RequestMethod.POST)
	public void saveCopyDishInfo(HttpServletRequest request,
								 HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		String code = "";
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);

			}
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id", session.getAttribute("tenentid"));
			Boolean dic = shopService.saveCopyDishInfo(
					(String) session.getAttribute("tenentid"), obj);

			if (dic) {
				result = "{\"success\": true, \"id\" : \"" + dic.toString()
						+ "\",\"code\" : \"" + code + "\"}";
				// logger.info("[第三方菜品信息复制返回信息：]" + result);
			} else {
				result = "{\"success\": false , \"msg\" : \"保存失败！\"}";
			}
		} catch (SystemException e) {
			ErrorCode error = e.getErrorCode();
			JSONObject jo = JSONObject.fromObject("{}");
			jo.put("msg", PropertiesLoader.getProperty(error.getNumber() + ""));
			jo.put("success", false);
			result = jo.toString();
			// logger.info("[第三方菜品信息复制返回信息：]" + result);
		} catch (Exception e) {
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		} finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e) {
			}

			try {
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			} catch (Exception e) {
			} finally {
				if (out != null)
					out.close();
			}
		}

	}

	// 保存复制菜品类别信息
	@RequestMapping(value = "/saveCopyDishCategoryInfo", method = RequestMethod.POST)
	public void saveCopyDishCategoryInfo(HttpServletRequest request,
										 HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		String code = "";
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);

			}
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id", session.getAttribute("tenentid"));
			Boolean dic = shopService.saveCopyDishCategoryInfo(
					(String) session.getAttribute("tenentid"), obj);

			if (dic) {
				result = "{\"success\": true, \"id\" : \"" + dic.toString()
						+ "\",\"code\" : \"" + code + "\"}";
				// logger.info("[第三方菜品分类复制返回信息:]" + result);
			} else {
				result = "{\"success\": false , \"msg\" : \"保存失败！\"}";
			}
		} catch (SystemException e) {
			ErrorCode error = e.getErrorCode();
			JSONObject jo = JSONObject.fromObject("{}");
			jo.put("msg", PropertiesLoader.getProperty(error.getNumber() + ""));
			jo.put("success", false);
			result = jo.toString();
			// logger.info("[第三方菜品分类复制返回信息:]" + result);
		} catch (Exception e) {
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		} finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e) {
			}

			try {
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			} catch (Exception e) {
			} finally {
				if (out != null)
					out.close();
			}
		}

	}

	/**
	 * 获取美团团购账号秘钥
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "loadXmdAccountInfo")
	public void loadXmdAccountInfo(HttpServletRequest request,
								   HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter writer = null;
		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");
		DBContextHolder.setTenancyid(tenantId);
		JSONObject json = new JSONObject();
		json.put("errno", 0);
		try {
			JSONObject jo = shopService.loadXmdAccountInfo(tenantId);
			json.put("data", jo);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", Constant.MSG_1);
		}

		try {
			writer = response.getWriter();
			writer.print(json.toString());
			writer.flush();
			writer.close();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}

	}

	/**
	 * 获取账号秘钥
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "loadAccountInfo")
	public void loadAccountInfo(HttpServletRequest request,
								HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter writer = null;
		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");
		DBContextHolder.setTenancyid(tenantId);
		JSONObject json = new JSONObject();
		json.put("errno", 0);
		try {
			JSONObject jo = shopService.loadAccountInfo(tenantId);
			json.put("data", jo);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", Constant.MSG_1);
		}

		try {
			writer = response.getWriter();
			writer.print(json.toString());
			writer.flush();
			writer.close();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}

	}

	/**
	 * 保存账号秘钥
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/saveXmdAccountInfo")
	public void saveXmdAccountInfo(HttpServletRequest request,
								   HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		String code = "";
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);

			}
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id", session.getAttribute("tenentid"));
			Boolean dic = shopService.saveXmdAccountInfo(
					(String) session.getAttribute("tenentid"), obj);

			if (dic) {
				result = "{\"success\": true, \"id\" : \"" + dic.toString()
						+ "\",\"code\" : \"" + code + "\"}";
			} else {
				result = "{\"success\": false , \"msg\" : \"保存失败！\"}";
			}
		} catch (SystemException e) {
			ErrorCode error = e.getErrorCode();
			JSONObject jo = JSONObject.fromObject("{}");
			jo.put("msg", PropertiesLoader.getProperty(error.getNumber() + ""));
			jo.put("success", false);
			result = jo.toString();
		} catch (Exception e) {
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		} finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e) {
			}

			try {
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			} catch (Exception e) {
			} finally {
				if (out != null)
					out.close();
			}
		}

	}

	/**
	 * 保存账号秘钥
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/saveAccountInfo")
	public void saveAccountInfo(HttpServletRequest request,
								HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		String code = "";
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}
			String tenanId = (String) session.getAttribute("tenentid");

			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id", session.getAttribute("tenentid"));
			Boolean dic = shopService.saveAccountInfo(tenanId, obj);

			if (dic) {
				//at ********  zhangyong start
				//变更饿了么2.0密钥时进行缓存更新
				String key = obj.optString("source_ele_add2").trim();
				String secret =obj.optString("secret_ele_add2").trim();
				if (!StringUtils.isEmpty(key) && !StringUtils.isEmpty(secret)) {
					ElmUtils.saveKeySecretKv(tenanId, "",
							ElmUtils.ELM_AUTH_LEVEL_TENANCY, key, secret);
				}
				//at ********  zhangyong end

				result = "{\"success\": true, \"id\" : \"" + dic.toString()
						+ "\",\"code\" : \"" + code + "\"}";
			} else {
				result = "{\"success\": false , \"msg\" : \"保存失败！\"}";
			}
		} catch (SystemException e) {
			ErrorCode error = e.getErrorCode();
			JSONObject jo = JSONObject.fromObject("{}");
			jo.put("msg", PropertiesLoader.getProperty(error.getNumber() + ""));
			jo.put("success", false);
			result = jo.toString();
		} catch (Exception e) {
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
			// logger.info("[第三方同步帐号异常信息：]" + response);
		} finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e) {
			}

			try {
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			} catch (Exception e) {
			} finally {
				if (out != null)
					out.close();
			}
		}

	}

	/**
	 * 获取外卖授权请求url
	 *
	 * @param channel
	 *            渠道，饿了么E09
	 * @param key
	 *            应用id
	 * @param secret
	 *            密钥
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/getOAuthUrl")
	public String getOAuthUrl(HttpServletRequest request, String shopId,
							  String channel, String key, String secret) {

		log.info("进入授权请求开始...参数：channel:{}, key:{},secret:{}", channel, key,
				secret);
		JSONObject result = new JSONObject();
		try {

			if (StringUtils.isEmpty(channel) || StringUtils.isEmpty(key)
					|| StringUtils.isEmpty(secret)) {
				result.put("success", false);
				result.put("msg", "参数输入格式不正确");
				return result.toString();
			}

			if (Constant.ELE_CHANNEL.equals(channel)) {
				String tenentid = (String) request.getSession().getAttribute(
						"tenentid");
				String subfix = "";
				if (!StringUtils.isEmpty(shopId)) {
					// 门店级
					subfix = "_" + shopId + "_" + ElmUtils.ELM_AUTH_LEVEL_SHOP;
				} else {
					subfix = "_" + ElmUtils.ELM_AOUTH_TENANCY_DEFALUT_SHOPID
							+ "_" + ElmUtils.ELM_AUTH_LEVEL_TENANCY;
				}

				String ele_oauth_callbackUrl = com.tzx.framework.common.constant.Constant.systemMap
						.get("ele_oauth_callbackUrl");
				//at 20170908 如果获取的地址不为空，则去空格处理，防止配置文件中无意加入空格的问题
				ele_oauth_callbackUrl=StringUtils.trim(ele_oauth_callbackUrl);

				// 使用config对象，实例化一个授权类
				OAuthClient client = new OAuthClient(
						ElmUtils.getConfigBykeyAndappSecret(key, secret));
				// 根据OAuth2.0中的对应state，scope和callback_url，获取授权URL
				String authUrl = client.getAuthUrl(ele_oauth_callbackUrl,
						ElmUtils.SCOPE, tenentid + subfix);

				result.put("success", true);
				result.put("msg", "成功");
				result.put("oauthUrl", authUrl);

				log.info("渠道{}授权url生成完成->{}", channel, result);

				return result.toString();
			}

			result.put("success", false);
			result.put("msg", "获取授权信息失败");
		} catch (Exception ex) {
			result.put("success", false);
			result.put("msg", "程序发生异常，请重试或联系管理员!");
		}
		return result.toString();
	}

	/**
	 * 饿了么2.0个人级应用授权
	 *
	 * @param channel
	 *            渠道，饿了么E09
	 * @param key
	 *            应用id
	 * @param secret
	 *            密钥
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/getPersonalAuth")
	public String getPersonalAuth(HttpServletRequest request, String channel,
								  String key, String secret) {

		log.info("进入个人应用授权请求开始...参数：channel:{}, key:{},secret:{}", channel,
				key, secret);
		JSONObject result = new JSONObject();
		try {
			if (StringUtils.isEmpty(channel) || StringUtils.isEmpty(key)
					|| StringUtils.isEmpty(secret)) {
				result.put("success", false);
				result.put("msg", "参数输入格式不正确");
				return result.toString();
			}
			Config cf = new Config(Boolean.parseBoolean(com.tzx.framework.common.constant.Constant.systemMap
					.get("ele_oauth_istest")), key, secret);
			OAuthClient client = new OAuthClient(cf);
			String tenanId = (String) request.getSession().getAttribute("tenentid");
			Token token = client.getTokenInClientCredentials();
			if(CommonUtil.checkStringIsNotEmpty(token.getAccessToken())){
				ElmUtils.saveToken(true, tenanId, null,
						ElmUtils.ELM_AUTH_LEVEL_TENANCY,
						ElmUtils.ELM_OAUTH_TYPE_PERSONAL, token);
				result.put("success", true);
				result.put("msg", "授权成功！");
			}else{
				result.put("success", false);
				result.put("msg", "授权失败，请确认账号密钥是个人级账号！");
			}
			log.info("渠道{}个人应用授权完成->{}", channel, result);
		} catch (Exception ex) {
			result.put("success", false);
			result.put("msg", "授权失败，请检查帐号密钥是否正确，再次尝试!");
		}
		return result.toString();
	}

	/**
	 * 获取饿了么餐厅ID
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "/getRestaurantIDs")
	public void getEleRestaurantsList(HttpServletRequest request,
									  HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			result = shopService.getEleRestaurantIDs(tenantId, params)
					.toString();

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 验证开店设置并推送是否已经存在数据
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "/validAdd")
	public void validAdd(HttpServletRequest request,
						 HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			List<JSONObject> query4Json = shopService.getThrirdShopInfo(
					tenantId, params);

			if (query4Json.isEmpty()) {
				result.put("isEmpty", Boolean.TRUE);
			} else {
				result = query4Json.get(0);
				result.put("isEmpty", Boolean.FALSE);
			}
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 批量获取没有设置餐厅id的机构
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "/suweiPlQuery")
	public void suweiPlQuery(HttpServletRequest request,
							 HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject param = new JSONObject();
		try {
			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			String organs = request.getParameter("organs");
			String channel = request.getParameter("channel");
			param.put("organs", organs);
			param.put("channel", channel);

			List<JSONObject> query4suweiNoCanT = shopService.query4suweiNoCanT(
					tenantId, param);
			out = response.getWriter();
			out.print(query4suweiNoCanT);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 饿了么商户绑定
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "shopBind")
	public @ResponseBody
	JSONObject shopBind(HttpServletRequest request,
						HttpServletResponse response, @RequestBody JSONObject param) {

		String tenantId = (String) request.getSession().getAttribute("tenentid");

		DBContextHolder.setTenancyid(tenantId);

		JSONObject json = new JSONObject();

		try {
			param.put("operator",request.getSession().getAttribute("employeeName"));
			param.put("operate_time",DateUtil.format(new Timestamp(System.currentTimeMillis())));
			json = shopService.shopBind(tenantId, param);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", json.optString("error"));
			// logger.info("==========绑定商户失败：" + e);
		}

		return json;
	}

	/**
	 * 饿了么设置接单模式
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "orderModelSet")
	public @ResponseBody
	JSONObject postOrderModelSet(HttpServletRequest request,
								 HttpServletResponse response, @RequestBody JSONObject param) {

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");

		DBContextHolder.setTenancyid(tenantId);

		JSONObject json = new JSONObject();

		try {
			param.put("operator",
					request.getSession().getAttribute("employeeName"));
			param.put("operate_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			json = shopService.postOrderModel(tenantId, param);

		} catch (Exception e) {
			e.printStackTrace();
			json.put("errno", "1");
			json.put("error", json.optString("error"));
			// logger.info("==========设置接单模式失败：" + e);
		}

		return json;
	}

	/**
	 * 饿了么2.0商户解除绑定
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "shopUnBind")
	public @ResponseBody
	JSONObject shopUnBind(HttpServletRequest request,
						  HttpServletResponse response, @RequestBody JSONObject param) {

		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");

		DBContextHolder.setTenancyid(tenantId);

		JSONObject json = new JSONObject();
		try {
			param.put("operator",
					request.getSession().getAttribute("employeeName"));
			param.put("operate_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
			json = shopService.shopUnBind(tenantId, param);

		} catch (Exception e) {
			e.printStackTrace();
		}

		return json;
	}

	/**
	 * 验证所选门店或选择的菜品是否与第三方平台菜品信息已都做映射
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "/platformSaaSDisheIsMapped")
	public void platformSaaSDisheIsMapped(HttpServletRequest request,
										  HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			params.put("send_operator", session.getAttribute("employeeName"));
			params.put("send_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			result = shopService.platformSaaSDisheIsMapped(tenantId, params)
					.toString();

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 自动映射菜品分类列表
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "dishCategoryAutomaticMap")
	public void doDishCategoryAutomaticMap(HttpServletRequest request,
										   HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			// 添加门店 权限 2016年8月12日13:40:05 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);
			// 添加门店 权限 2016年8月12日13:40:05 xgy end
			try {
				result = shopService.saveDishCategoryAutomaticMap(tenantId,
						params).toString();
			} catch (Exception e) {
				e.printStackTrace();
			}

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 建立菜品分类平台数据与本地数据映射关系
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "saveDishCategoryThirdClassID")
	public void saveDishCategoryThirdClassID(HttpServletRequest request,
											 HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			try {
				result = shopService.saveDishCategoryThirdClassID(tenantId,
						params).toString();
			} catch (Exception e) {
				e.printStackTrace();
			}

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 自动映射菜品列表
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "dishAutomaticMap")
	public void doDishAutomaticMap(HttpServletRequest request,
								   HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			// 添加门店 权限 2016年8月12日13:40:05 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);
			String classID = params.optString("class_id");
			if (classID.equals("==全部==")) {
				params.put("class_id", "");
			}
			// 添加门店 权限 2016年8月12日13:40:05 xgy end
			try {
				result = shopService.saveDishAutomaticMap(tenantId, params)
						.toString();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	@RequestMapping(value = "getPlatNoMapDishList")
	public void getPlatNoMapDishList(HttpServletRequest request,
									 HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			params.put("tenancy_id", tenantId);
			DBContextHolder.setTenancyid(tenantId);
			try {
				result = shopService.getPlatNoMapDishList(params).toString();
			} catch (Exception e) {
				e.printStackTrace();
			}

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 建立菜品平台数据与本地数据映射关系
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "saveDishThirdItemId")
	public void saveDishThirdItemId(HttpServletRequest request,
									HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			try {
				result = shopService.saveDishThirdItemId(tenantId, params)
						.toString();
			} catch (Exception e) {
				e.printStackTrace();
			}

			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	/**
	 * 批量修改基础信息
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "batchSaveOrUpdateDishInfo")
	public void batchSaveOrUpdateDishInfo(HttpServletRequest request,
										  HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet()) {
				if(key.equalsIgnoreCase("dishes")){
					System.out.println("dishes:"+map.get(key)[0]);
				}
				params.put(key, map.get(key)[0]);

			}

			params.put("send_operator", session.getAttribute("employeeName"));
			params.put("send_time",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			JSONArray arr=new JSONArray();
			arr=params.optJSONArray("dishes");
			result = shopService.saveOrUpdateDishInfo(tenantId, params).toString();



			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}

	}

	@RequestMapping(value = "elmShopAuthLevelInfo")
	public void elmShopAuthLevelInfo(HttpServletRequest request,
									 HttpServletResponse response) {
		JSONObject result=new JSONObject();

		JSONObject data=new JSONObject();
		data.put("isexist_shop_level", "0");
		result.put("error", "0");
		result.put("data", data);

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject shopListJson;
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			HttpSession session = request.getSession();
			JSONObject obj=new JSONObject();
			obj.put("version", "2.0");
			obj.put("channel", Constant.ELE_CHANNEL);
			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			shopListJson = shopService.shopList(tenantId, obj);
			if(shopListJson!=null){
				JSONArray list=shopListJson.getJSONArray("rows");
				System.out.println("==============>>"+list.size());
				for(int i=0;i<list.size();i++){
					JSONObject row=list.getJSONObject(i);
					String auth_level=row.optString("auth_level");
					if(auth_level.equals(ElmUtils.ELM_AUTH_LEVEL_SHOP)){

						data.put("isexist_shop_level", "1");
						result.put("data", data);

						break;
					}
				}
			}
			out = response.getWriter();
			out.print(result);
			out.flush();
		}catch(Exception ex){
			ex.printStackTrace();
		}finally {
			if (null != out) {
				out.close();
			}
		}
	}
	/**
	 * 回复评论(多条评论)
	 * @param request
	 * @param commentIds
	 * @param content
	 * @param response
	 */
	@RequestMapping(value = "replyComments")
	public @ResponseBody  JSONObject replyComments(HttpServletRequest request,@RequestParam String commentIds, @RequestParam String content,
												   HttpServletResponse response) {
		HttpSession session = request.getSession();
		String tenantId = (String) session.getAttribute("tenentid");
		String employeeName = (String)session.getAttribute("employeeName");
		DBContextHolder.setTenancyid(tenantId);
		JSONObject param = new JSONObject();
		if(StringUtils.isEmpty(employeeName)){
			employeeName="";
		}
		param.put("user", employeeName);
		String[] idArr=commentIds.split(",");
		return shopService.addReply(tenantId,idArr, content,param);

	}

	public void replyAllComments(HttpServletRequest request,@RequestParam String commentIds, @RequestParam String content,
								 HttpServletResponse response) {
		HttpSession session = request.getSession();
		String tenantId = (String) session.getAttribute("tenentid");
		DBContextHolder.setTenancyid(tenantId);

		String[] idArr=commentIds.split(",");
		shopService.addReply(tenantId,idArr, content,null);
	}

	/**
	 * 获取评论统计
	 * @param request
	 * @param commentIds
	 * @param timeBegin
	 * @param timeEnd
	 * @param channel
	 * @param response
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "getCommentStat")
	public JSONObject getCommentStat(HttpServletRequest request, @RequestParam(name="comment_time_begin") String timeBegin,
									 @RequestParam(name="comment_time_end") String timeEnd,
									 @RequestParam int channel,
									 @RequestParam String organCode,
									 HttpServletResponse response) {
		HttpSession session = request.getSession();
		String tenantId = (String) session.getAttribute("tenentid");
		DBContextHolder.setTenancyid(tenantId);

		JSONObject json= shopService.getCommentStat(tenantId, timeBegin, timeEnd, channel, organCode);
		JSONObject result=new JSONObject();
		result.put("data", json);
		result.put("code", 0);
		return result;
	}

	/**
	 * 获取第三方店铺状态
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getThirdShopList")
	public void getThirdShopList(HttpServletRequest request,HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");

		JSONObject obj = JSONObject.fromObject("{}");
		String returnStr="{}";
		HttpSession session = request.getSession();
		//获取tenentid
		String tenentid=(String)session.getAttribute("tenentid");
		try {
			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("store_id", (String) session.getAttribute("store_id"));
			JSONObject res=shopService.getThirdShop(tenentid,obj);
			List<JSONObject> list=(List<JSONObject>)res.opt("rows");
			for(JSONObject json:list){//添加外卖平台列表
				int shop_id=json.optInt("id");
				JSONArray arr=shopService.getThirdShopByShopId(tenentid,shop_id);
				json.put("waimai", arr);
			}
			returnStr=res.toString();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		PrintWriter writer = null;
		try {
			writer = response.getWriter();
			writer.print(returnStr);
			writer.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}
	}


	/**
	 * 获取店铺状态
	 * @param request
	 * @param tenentid
	 * @param shopIdArr  12-MT09,13-BD06-ELE08
	 * @return [ [0,1,2] ]
	 */
	@ResponseBody
	@RequestMapping(value = "getThirdShopStatus")
	public Object  getThirdShopStatus(HttpServletRequest request,@RequestParam String shopIdArr){

		String tenentid = (String) request.getSession()
				.getAttribute("tenentid");

		//返回list结果
		List result=new ArrayList();
		//检查参数
		if(StringUtil.isBlank(tenentid)||StringUtil.isBlank(shopIdArr))
			return result;

		String[] shopArr=shopIdArr.split(",");
		for(String shop:shopArr){///循环shopid
			String[] channelArr=shop.split("-");
			String shopIdStr=channelArr[0];
			Integer shopId=Integer.parseInt(channelArr[0]);

			//存储第三方店铺
			Map map=new HashMap();
			List channelList=new ArrayList();
			for(int i=1;i<channelArr.length;i++){//循环channel
				String channel=channelArr[i];
				Integer[] status=null;
				if(channel.equals(Constant.MEITUAN_CHANNEL)){
					status=shopService.grabMeituanStatus(tenentid, shopIdStr);
				}
				if(channel.equals(Constant.BAIDU_CHANNEL)){
					status=shopService.grabBaiduStatus(tenentid, shopIdStr);
				}
				if(channel.equals(Constant.ELE_CHANNEL)){
					status=shopService.grabEleStatus(tenentid, shopIdStr);
				}

				if(status!=null){
					channelList.add(status);
//					if(status>-1){
					//更新数据库的三方状态
//						shopService.storeShopStatus(tenentid,shopIdStr,channel,status+"");
//					}
				}


			}
//			map.put(shopIdStr, channelList);

			result.add(channelList);
		}
		return result;

	}


	/**
	 * 加载评论列表
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "getCommentsList")
	public void getCommentsList(HttpServletRequest request,
								HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");

		PrintWriter writer = null;

		JSONObject obj = new JSONObject();
		Map<String, String[]> map = request.getParameterMap();
		for (String key : map.keySet()) {
			obj.put(key, map.get(key).length>1?map.get(key):map.get(key)[0]);
		}
		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");
		obj.put("tenantId", tenantId);

		String result = "[]";

		try {
			result = shopService.getCommentsList(obj).toString();
			writer = response.getWriter();
			writer.print(result.toString());
			writer.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}
	}


	/**
	 * 初始化获取第三方店铺信息
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "initShopInfoFromXMD")
	public void initShopInfoFromXMD(HttpServletRequest request,
									HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");

		PrintWriter writer = null;

		JSONObject obj = new JSONObject();
		Map<String, String[]> map = request.getParameterMap();
		for (String key : map.keySet()) {
			obj.put(key, map.get(key).length>1?map.get(key):map.get(key)[0]);
		}
		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");
		obj.put("tenantId", tenantId);

		String result = "[]";

		try {
			result = shopService.initShopInfoFromXMD(obj).toString();
			writer = response.getWriter();
			writer.print(result.toString());
			writer.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}
	}

	/**
	 * 获取菜品列表和新美大外卖的autoToken，做菜品映射 changhui add 2017-11-15
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getLocalDishListAndAutoToken")
	public void getLocalDishListAndAutoToken(HttpServletRequest request,
											 HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result;
		JSONObject resultList = new JSONObject();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			params.put("tenantId", tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);
			// 添加门店 权限 2016年8月12日13:53:54 xgy end
			String classID = params.optString("class_id");
			if (classID.equals("==全部==")) {
				params.put("class_id", "");
			}
			//result = shopService.getLocalDishList(tenantId, params);
			result = shopService.getLocalDishNoPageList(tenantId, params);

			JSONArray dishList = JSONArray.fromObject(result.optString("rows"));

			//String resIsAchieveMappingCondition = shopService.isAchieveMappingCondition(params, dishList);

			JSONObject resDishes = new JSONObject();
			JSONArray resDishInfoList = new JSONArray();
			for (int i = 0; i < dishList.size(); i++)
			{
				JSONObject dl = dishList.getJSONObject(i);
				if(!dl.isNullObject() && !dl.isEmpty()){
					JSONObject resDishInfo = new JSONObject();
					resDishInfo.put("categoryName", dl.optString("last_send_class_name"));
					resDishInfo.put("eDishCode", dl.optString("item_id"));
					resDishInfo.put("eDishSkuCode", dl.optString("unit_id"));
					resDishInfo.put("dishNameWithSpec", dl.optString("item_name") + "(" +  dl.optString("unit") + ")");
					resDishInfoList.add(resDishInfo);
				}
			}

			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:getLocalDishListAndAutoToken");
			ccBusniessLogBean.setRequestBody(params.toString());


			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
			ccBusniessLogBean.setTzxName("");
			ccBusniessLogBean.setShopId(params.optString("shop_id"));

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());

			resDishes.put("dishes", resDishInfoList);
			String appAuthToken = XmdWMUtils.getStoreToken(tenantId, params.optString("shop_id"));
			Map<String,String> mapToken = XmdWMUtils.getXmdDeveloperAndKey(tenantId);
			//resultList.put("resIsAchieveMappingCondition", resIsAchieveMappingCondition);
			resultList.put("signKey", mapToken.get("signkey").toString());
			resultList.put("ePoiId", params.optString("shop_id") + "@" + tenantId);
			resultList.put("appAuthToken", appAuthToken);
			String mappingUrl = XmdWMUtils.BASE_WM_URL + XmdWMUtils.CMD_DISH_MAPPING;
			resultList.put("url", mappingUrl);
			//String resDisheList = "{\"dishes\": [{\"categoryName\": \"海军测试菜品\",\"eDishCode\": \"16\",\"eDishSkuCode\":\"16\",\"dishNameWithSpec\": \"\"}]}";
			resultList.put("resDishes", resDishes.toString());
			ccBusniessLogBean.setResponseBody(resultList.toString());
			out = response.getWriter();
			out.print(resultList);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 新美大外卖映射后改变saas系统的菜品状态为已推送 changhui 2017-11-15
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "updateWhetherPushOver")
	public void updateWhetherPushOver(HttpServletRequest request,
									  HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");

		PrintWriter writer = null;

		JSONObject params = new JSONObject();

		Map<String, String[]> map = request.getParameterMap();

		for (String key : map.keySet()) {
			params.put(key, map.get(key)[0]);
		}

		JSONObject result = new JSONObject();
		String tenantId = (String) request.getSession()
				.getAttribute("tenentid");
		params.put("tenantId", tenantId);
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject resultDish = shopService.getLocalDishNoPageList(tenantId, params);
			JSONArray dishList = JSONArray.fromObject(resultDish.optString("rows"));
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:updateWhetherPushOver");
			ccBusniessLogBean.setRequestBody(params.toString());


			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
			ccBusniessLogBean.setTzxName("");
			ccBusniessLogBean.setShopId(params.optString("shop_id"));

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());
			result = shopService.updateXmdWhetherPushOver(params, dishList);
			ccBusniessLogBean.setResponseBody(result.toString());
			writer = response.getWriter();
			writer.print(result.toString());
			writer.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}
	}

	/**
	 *  饿了么菜品映射获取菜品的映射信息列表 changhui add 2017-12-13
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getEleDishListMapping")
	public void getEleDishListMapping(HttpServletRequest request,
									  HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result;
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			params.put("tenantId", tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);
			// 添加门店 权限 2016年8月12日13:53:54 xgy end
			String classID = params.optString("class_id");
			params.put("class_id", "");

			//result = shopService.getLocalDishList(tenantId, params);
			result = shopService.getLocalDishNoPageList(tenantId, params);

			JSONArray dishList = JSONArray.fromObject(result.optString("rows"));

			//String resIsAchieveMappingCondition = shopService.isAchieveMappingCondition(params, dishList);
			List<JSONObject> resDishInfoAllList = new ArrayList<JSONObject>();
			List<JSONObject> resEleDishInfoList = new ArrayList<JSONObject>();
			List<JSONObject> resDishInfoList = getLocalDishInfoList(dishList);

			if(resDishInfoList != null && resDishInfoList.size() > 0){
				ProductService productService = new ProductService(ElmUtils.getConfig(tenantId, params.optString("shop_id")), ElmUtils.getToken(tenantId, params.optString("shop_id")));
				Map<String, String> mapPara = ElmUtils.getELeDeveloperAndKey(tenantId, params.optString("shop_id"));
				List<OCategory> strResul = productService.getShopCategories(Long.valueOf(mapPara.get("thirdShopId")));
				if(strResul != null && strResul.size() > 0){
					for(OCategory o : strResul){//循环分类
						Map<Long, OItem> reslutMap = productService.getItemsByCategoryId(Long.valueOf(o.getId()));
						if(!reslutMap.isEmpty() && reslutMap.size() > 0){
							for(OItem item : reslutMap.values()){//循环菜品
								boolean f = true;
								for (int i = 0; i < resDishInfoList.size(); i++)
								{//循环本地菜品
									JSONObject dl = resDishInfoList.get(i);
									if(!dl.isNullObject() && !dl.isEmpty()){//

										boolean ff = true;
										if(dl.optString("thirdItemId").equals(String.valueOf(item.getId()))){
											f = false;
											ff = false;
											String error = "0";
											dl.put("eleLastSendClassName", o.getName());
											dl.put("eleItemName", item.getName());
											dl.put("eleId", item.getId());
											dl.put("eleUit", item.getUnit());//商品单位
											Double elePrice = 0d;
											
											//商品规格
											List<OSpec> specs = item.getSpecs();
											List<JSONObject> eleSpecs = new ArrayList<JSONObject>();
											if(specs != null && specs.size() > 0){
												for(OSpec ospec : specs){
													if(ospec != null){
														elePrice = ospec.getPrice();
														eleSpecs.add(JSONObject.fromObject(ospec));
													}
												}
											}
											
											dl.put("eleSpecs", eleSpecs);//饿了么规格
											dl.put("elePrice", elePrice);
											if(dl.optString("defaultPrice") != null && !dl.optString("defaultPrice").equals("")){
												if(dl.optDouble("defaultPrice") != elePrice){
													error = "6";
												}
											}
											if(!dl.optString("unit").equals(item.getUnit())){
												error = "4";
											}
											if(!dl.optString("itemName").equals(item.getName())){
												error = "6";
											}
											if(!dl.optString("lastSendClassName").equals(o.getName())){
												if(dl.optString("thirdClassId").equals(o.getId())){
													error = "9";
												}else{
													error = "8";
												}
											}
											dl.put("error", error);
										}
										if((dl.optString("lastSendClassName").equals(o.getName()) || dl.optString("thirdClassId").equals(o.getId())) && dl.optString("itemName").equals(item.getName()) && ff){
											f = false;
											dl.put("eleLastSendClassName", o.getName());
											dl.put("eleItemName", item.getName());
											dl.put("eleId", item.getId());
											dl.put("eleUit", item.getUnit());
											Double elePrice = 0d;
											List<OSpec> specs = item.getSpecs();
											List<JSONObject> eleSpecs = new ArrayList<JSONObject>();
											if(specs != null && specs.size() > 0){
												for(OSpec ospec : specs){
													if(ospec != null){
														elePrice = ospec.getPrice();
														eleSpecs.add(JSONObject.fromObject(ospec));
													}
												}
											}
											
											dl.put("eleSpecs", eleSpecs);//饿了么规格
											dl.put("elePrice", elePrice);
											String error = "0";
											if(dl.optString("thirdItemId") != null && !dl.optString("thirdItemId").equals("") && !dl.optString("thirdItemId").equals("null")){
												if(!dl.optString("thirdItemId").equals(String.valueOf(item.getId()))){
													error = "1";
												}
											}
											if(dl.optString("thirdClassId") != null && !dl.optString("thirdClassId").equals("") && !dl.optString("thirdClassId").equals("null")){
												if(!dl.optString("thirdClassId").equals(String.valueOf(o.getId()))){
													error = "2";
												}
											}
											if(dl.optString("defaultPrice") != null && !dl.optString("defaultPrice").equals("")){
												if(dl.optDouble("defaultPrice") != elePrice){
													error = "3";
												}
											}
											if(!dl.optString("unit").equals(item.getUnit())){
												error = "4";
											}
											if(!dl.optString("lastSendClassName").equals(o.getName())){
												if(dl.optString("thirdClassId").equals(o.getId())){
													error = "9";
												}else{
													error = "8";
												}
											}
											dl.put("error", error);
										}
									}
								}
								if(f){
									JSONObject itemJson = new JSONObject();
									itemJson.put("thirdClassId", "");
									itemJson.put("thirdItemId", "");
									itemJson.put("itemName", "");
									itemJson.put("lastSendClassName", "");
									itemJson.put("itemId", "");
								/*itemJson.put("unitId", "");*/
									itemJson.put("unit", "");
									itemJson.put("unit_id", "");
								/*itemJson.put("boxPrice", "");*/
									itemJson.put("defaultPrice", "");
									itemJson.put("eleLastSendClassName", o.getName());
									itemJson.put("eleItemName", item.getName());
									itemJson.put("eleId", item.getId());
									itemJson.put("eleUit", item.getUnit());
									itemJson.put("error", "5");
									Double elePrice = 0d;
									List<OSpec> specs = item.getSpecs();
									List<JSONObject> eleSpecs = new ArrayList<JSONObject>();
									if(specs != null && specs.size() > 0){
										for(OSpec ospec : specs){
											if(ospec != null){
												elePrice = ospec.getPrice();
												eleSpecs.add(JSONObject.fromObject(ospec));
											}
										}
									}
									
									itemJson.put("eleSpecs", eleSpecs);
									itemJson.put("elePrice", elePrice);
									resEleDishInfoList.add(itemJson);
								}
							}
						}else{//本地无菜品信息
							boolean f = true;
							for (int i = 0; i < resDishInfoList.size(); i++) {
								JSONObject dl = resDishInfoList.get(i);
								if (!dl.isNullObject() && !dl.isEmpty()) {
									if(dl.optString("lastSendClassName").equals(o.getName())){
										f = false;
									}
									if(dl.optString("thirdClassId").equals(o.getId())){
										f = false;
										if(!dl.optString("lastSendClassName").equals(o.getName())){
											dl.put("error", "9");
										}
									}
								}
							}
							if(f){
								JSONObject itemJson = new JSONObject();
								itemJson.put("thirdClassId", "");
								itemJson.put("thirdItemId", "");
								itemJson.put("itemName", "");
								itemJson.put("lastSendClassName", "");
								itemJson.put("itemId", "");
								itemJson.put("unit", "");
								itemJson.put("unit_id", "");
								itemJson.put("defaultPrice", "");
								itemJson.put("eleLastSendClassName", o.getName());
								itemJson.put("eleItemName", "");
								itemJson.put("eleId", "");
								itemJson.put("eleUit", "");
								itemJson.put("error", "7");
								itemJson.put("elePrice", "");
								itemJson.put("eleSpecs", "[]");
								resEleDishInfoList.add(itemJson);
							}
						}
					}
				}
			}

			resDishInfoAllList.addAll(resDishInfoList);
			resDishInfoAllList.addAll(resEleDishInfoList);


			String strError = "";
			if(resDishInfoAllList.size() > 0){
				for(JSONObject json : resDishInfoAllList){
					if(!json.optString("error").equals("0")){
						strError += json.optString("error") + ",";
					}
				}
			}

			CcRedisService rd = (CcRedisService) SpringConext
					.getBean("ccRedisServiceImpl");
			String developerKey = rd.getByKey("eleMappingDishInfoList");

			if (!StringUtils.isEmpty(developerKey)) {
				rd.del("eleMappingDishInfoList");
				rd.saveBykv("eleMappingDishInfoList", strError, 3600 * 24 * 30);
			}else{
				rd.saveBykv("eleMappingDishInfoList", strError, 3600 * 24 * 30);
			}

			List<JSONObject> resDishInfoAllSearchPageList = new ArrayList<JSONObject>();
			if(resDishInfoAllList.size() > 0){
				if (classID.equals("==全部==")) {
					params.put("class_id", "");
				}else{
					params.put("class_id", classID);
				}
				String item_name = params.optString("item_name");
				String item_class = params.optString("class_id");
				if((item_name != null && !item_name.equals("")) || (item_class != null && !item_class.equals("")))
				{
					for(JSONObject json : resDishInfoAllList){
						if(item_name != null && !item_name.equals("")){
							if(item_class != null && !item_class.equals("")){
								if((json.optString("itemName").indexOf(item_name) >= 0 && json.optString("lastSendClassName").indexOf(item_class) >= 0) || (json.optString("eleItemName").indexOf(item_name) >= 0 && json.optString("eleLastSendClassName").indexOf(item_class) >= 0)){
									resDishInfoAllSearchPageList.add(json);
								}
							}else{
								if(json.optString("itemName").indexOf(item_name) >= 0 || json.optString("eleItemName").indexOf(item_name) >= 0){
									resDishInfoAllSearchPageList.add(json);
								}
							}
						}else{
							if(item_class != null && !item_class.equals("")){
								if(json.optString("lastSendClassName").indexOf(item_class) >= 0 || json.optString("eleLastSendClassName").indexOf(item_class) >= 0){
									resDishInfoAllSearchPageList.add(json);
								}
							}
						}
					}
				}else{
					resDishInfoAllSearchPageList = resDishInfoAllList;
				}
			}
			List<JSONObject> resDishInfoAllPageList = new ArrayList<JSONObject>();
			Integer page = params.optInt("page", 0);
			Integer rows = params.optInt("rows", 0);
			Integer count = resDishInfoAllSearchPageList.size();
			Integer sNum = (page - 1) * rows;
			Integer eNum = page * rows;

			if(count > 0){
				if(eNum <= count){
					resDishInfoAllPageList = resDishInfoAllSearchPageList.subList(sNum, eNum);
				}else{
					resDishInfoAllPageList = resDishInfoAllSearchPageList.subList(sNum, count);
				}
			}


			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:getEleDishListMapping");
			ccBusniessLogBean.setRequestBody("params:" + params.toString() + "resDishInfoAllList:" + resDishInfoAllList.toString());


			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
			ccBusniessLogBean.setTzxName("");
			ccBusniessLogBean.setShopId(params.optString("shop_id"));

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());


			int pagenum = params.containsKey("page") ? (params.getInt("page") == 0 ? 1 : params.getInt("page")) : 1;
			JSONObject jsonRes = new JSONObject();
			jsonRes.put("page", pagenum);
			jsonRes.put("total", resDishInfoAllSearchPageList.size());
			jsonRes.put("rows", resDishInfoAllPageList);
			ccBusniessLogBean.setResponseBody(jsonRes.toString());

			out = response.getWriter();
			out.print(jsonRes.toString());
			out.flush();
		} catch (Exception e) {
			log.error("饿了么菜品映射获取菜品的映射信息列表异常：", e.getCause());
			e.printStackTrace();
			ccBusniessLogBean.setResponseBody(e.getMessage());
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}

	//将本地分类名，菜品名，菜品单价，规格信息存放到一个集合中
	private List<JSONObject> getLocalDishInfoList(JSONArray dishList) {
	List<JSONObject> resDishInfoList=new ArrayList<JSONObject>();
		for (int i = 0; i < dishList.size(); i++)
		{
			JSONObject dl = dishList.getJSONObject(i);
			if(!dl.isNullObject() && !dl.isEmpty()){
				JSONObject resDishInfo = new JSONObject();
				resDishInfo.put("thirdClassId", dl.optString("third_class_id"));
				resDishInfo.put("thirdItemId", dl.optString("third_item_id"));
				resDishInfo.put("itemName", dl.optString("item_name"));
				resDishInfo.put("lastSendClassName", dl.optString("last_send_class_name"));
				resDishInfo.put("itemId", dl.optString("item_id"));
				/*resDishInfo.put("unitId", dl.optString("unit_id"));*/
				resDishInfo.put("unit", dl.optString("unit"));
				resDishInfo.put("unit_id", dl.optString("unit_id"));
				/*resDishInfo.put("boxPrice", dl.optDouble("box_price",0));*/
				resDishInfo.put("defaultPrice", dl.optDouble("default_price",0));
				resDishInfo.put("error", "0");
				resDishInfo.put("eleLastSendClassName", "");
				resDishInfo.put("eleItemName","");
				resDishInfo.put("eleId", "");
				resDishInfo.put("eleUit", "");
				resDishInfo.put("elePrice", "");
				resDishInfoList.add(resDishInfo);
			}
		}
		return resDishInfoList;
	}
	
	/**
	 *  验证保存饿了么菜品映射的映射关系是否正确 changhui add 2017-12-13
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "saveEleDishListMappingValidate")
	public void saveEleDishListMappingValidate(HttpServletRequest request,
											   HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			params.put("tenantId", tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);

			CcRedisService rd = (CcRedisService) SpringConext
					.getBean("ccRedisServiceImpl");
			String developerKey = rd.getByKey("eleMappingDishInfoList");

			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:saveEleDishListMappingValidate");
			ccBusniessLogBean.setRequestBody("params:" + params.toString() + "developerKey:" + developerKey);


			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
			ccBusniessLogBean.setTzxName("");
			ccBusniessLogBean.setShopId(params.optString("shop_id"));

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());


			if (developerKey == null) {
				result.put("result", "ok");
				result.put("msg", "errorRedis");
			}else{
				if(developerKey.equals("")){
					result.put("result", "ok");
					result.put("msg", "");
				}else{
					boolean f = true;
					if(developerKey.indexOf("3") >= 0){
						f = false;
					}else if(developerKey.indexOf("4") >= 0){
						f = false;
					}else if(developerKey.indexOf("5") >= 0){
						f = false;
					}else if(developerKey.indexOf("6") >= 0){
						f = false;
					}else if(developerKey.indexOf("7") >= 0){
						f = false;
					}else if(developerKey.indexOf("8") >= 0){
						f = false;
					}else if(developerKey.indexOf("9") >= 0){
						f = false;
					}
					if(f){
						result.put("result", "ok");
						result.put("msg", "");
					}else{
						result.put("result", "ok");
						result.put("msg", "error");
					}
				}
			}
			ccBusniessLogBean.setResponseBody(result.toString());
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			log.error("验证保存饿了么菜品映射的映射关系是否正确异常：", e.getCause());
			e.printStackTrace();
			result.put("result", "no");
			result.put("msg", e.getMessage());
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 *  保存饿了么菜品映射的映射关系 changhui add 2017-12-13
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "saveEleDishListMapping")
	public void saveEleDishListMapping(HttpServletRequest request,
									   HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result;
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			params.put("tenantId", tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);

			//result = shopService.getLocalDishList(tenantId, params);
			result = shopService.getLocalDishNoPageList(tenantId, params);

			JSONArray dishList = JSONArray.fromObject(result.optString("rows"));

			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:saveEleDishListMapping");
			ccBusniessLogBean.setRequestBody("params:" + params.toString() + "dishList:" + dishList.toString());


			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
			ccBusniessLogBean.setTzxName("");
			ccBusniessLogBean.setShopId(params.optString("shop_id"));

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());

			JSONArray resDishInfoList = new JSONArray();
			for (int i = 0; i < dishList.size(); i++)
			{
				JSONObject dl = dishList.getJSONObject(i);
				if(!dl.isNullObject() && !dl.isEmpty()){
					JSONObject resDishInfo = new JSONObject();
					resDishInfo.put("categoryName", dl.optString("last_send_class_name"));
					resDishInfo.put("eDishCode", dl.optString("item_id"));
					resDishInfo.put("eDishSkuCode", dl.optString("unit_id"));
					resDishInfo.put("dishNameWithSpec", dl.optString("item_name") + "(" +  dl.optString("unit") + ")");
					resDishInfoList.add(resDishInfo);
				}
			}
			result = shopService.updateWhetherPushOver(params, resDishInfoList);
			String data = result.optString("data");
			if(data.equals("ok")){
				JSONArray  dishInfoList = shopService.getEleDishInfoList(tenantId, params);
				result = shopService.saveEleDishAndDishCategoryMapping(params, dishInfoList);
				ccBusniessLogBean.setResponseBody("result:" + result.toString() + "" + dishInfoList.toString());
			}else{
				ccBusniessLogBean.setResponseBody("result:" + result.toString());
			}
			
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			log.error("保存饿了么菜品映射的映射关系异常：", e.getCause());
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 判断饿了么商家是否已经授权 changhui add 2017-12-26
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getEleTokenInfo")
	public void getEleTokenInfo(HttpServletRequest request,
								HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		try {

			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			params.put("tenantId", tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);
			// 添加门店 权限 2016年8月12日13:53:54 xgy end
			String classID = params.optString("class_id");
			params.put("class_id", "");
			log.info("饿了么菜品映射授权参数:" + params.toString());
			try {
				Token token = ElmUtils.getToken(tenantId, params.optString("shop_id"));
				if (token != null && token.getAccessToken() != null && !token.getAccessToken().equals("")) {
					result.put("result", "ok");
				} else {
					result.put("result", "no");
				}
			}catch (Exception e){
				log.info("饿了么菜品映射授权错误信息:" + e.getMessage());
				result.put("result", "no");
			}

			out = response.getWriter();
			out.print(result.toString());
			out.flush();
		} catch (Exception e) {
			log.info("饿了么菜品映射授权错误信息:" + e.getMessage());
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 * 判断组织机构是多品牌还是单品牌
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getOrgansTree")
	public void getOrgansTree(HttpServletRequest request, HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		String tenantId = (String) session.getAttribute("tenentid");
		PrintWriter out = null;
//		String resultString = "";
		try {
			//获取系统参数 是否是多品牌
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("system_name", Constant.SYS_PARAM);
			jsonObj.put("para_code", Constant.SYS_PARAM_BRAND);
			JSONObject isMultibrand = shopService.getSysParameterBySystemName(tenantId,jsonObj);
			out = response.getWriter();
			out.print(isMultibrand.toString());
			out.flush();
		} catch (Exception e) {
			log.info("获取组织机构数错误信息:" + e.getMessage());
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
	}
	
	
	@RequestMapping(value = "getShopCode")
	public void getShopCode(HttpServletRequest request,HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");

		PrintWriter writer = null;

		String tenantId = (String) request.getSession().getAttribute("tenentid");
		DBContextHolder.setTenancyid(tenantId);

		JSONObject obj = new JSONObject();
		Map<String, String[]> map = request.getParameterMap();
		for (String key : map.keySet()) {
			obj.put(key, map.get(key)[0]);
		}
		
		String result = "[]";

		try {
			result = shopService.getShopCode(tenantId, obj).toString();
			writer = response.getWriter();
			writer.print(result.toString());
			writer.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != writer) {
				writer.close();
				writer = null;
			}
		}
	}


	/**
	 * 判断新美大外卖商家是否已经授权 changhui add 2018-1-18
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getXmdwmTokenInfo")
	public void getXmdwmTokenInfo(HttpServletRequest request,
								  HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		try {

			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			params.put("tenantId", tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);
			// 添加门店 权限 2016年8月12日13:53:54 xgy end
			String classID = params.optString("class_id");
			params.put("class_id", "");
			log.info("新美大外卖菜品映射授权参数:" + params.toString());
			try {
				String token = XmdWMUtils.getStoreToken(tenantId, params.optString("shop_id"));
				if (token != null && !"".equals(token)) {
					result.put("result", "ok");
				} else {
					result.put("result", "no");
				}
			}catch (Exception e){
				log.info("新美大外卖菜品映射授权错误信息:" + e.getMessage());
				result.put("result", "no");
			}

			out = response.getWriter();
			out.print(result.toString());
			out.flush();
		} catch (Exception e) {
			log.info("新美大外卖菜品映射授权错误信息:" + e.getMessage());
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 *  新美大外卖菜品映射获取菜品的映射信息列表 changhui add 2018-1-18
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "getXmdwmDishListMapping")
	public void getXmdwmDishListMapping(HttpServletRequest request,
										HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result;
		JSONObject resultList = new JSONObject();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			params.put("tenantId", tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);
			// 添加门店 权限 2016年8月12日13:53:54 xgy end
			String classID = params.optString("class_id");
			params.put("class_id", "");

			//result = shopService.getLocalDishList(tenantId, params);
			result = shopService.getLocalDishNoPageList(tenantId, params);

			JSONArray dishList = JSONArray.fromObject(result.optString("rows"));

			//String resIsAchieveMappingCondition = shopService.isAchieveMappingCondition(params, dishList);
			List<JSONObject> resDishInfoAllList = new ArrayList<JSONObject>();
			List<JSONObject> resXmdwmDishInfoList = new ArrayList<JSONObject>();
			List<JSONObject> resDishInfoList = new ArrayList<JSONObject>();
			for (int i = 0; i < dishList.size(); i++)
			{
				JSONObject dl = dishList.getJSONObject(i);
				if(!dl.isNullObject() && !dl.isEmpty()){
					JSONObject resDishInfo = new JSONObject();
					resDishInfo.put("thirdItemId", dl.optString("third_item_id"));
					resDishInfo.put("itemName", dl.optString("item_name"));
					resDishInfo.put("lastSendClassName", dl.optString("last_send_class_name"));
					resDishInfo.put("itemId", dl.optString("item_id"));
					resDishInfo.put("unit", dl.optString("unit"));
					resDishInfo.put("unit_id", dl.optString("unit_id"));
					resDishInfo.put("defaultPrice", dl.optDouble("default_price",0));
					resDishInfo.put("error", "0");
					resDishInfo.put("xmdwmLastSendClassName", "");
					resDishInfo.put("xmdwmItemName","");
					resDishInfo.put("xmdwmId", "");
					resDishInfo.put("xmdwmUnit", "");
					resDishInfo.put("xmdwmPrice", "");
					resDishInfoList.add(resDishInfo);
				}
			}

			if(resDishInfoList != null && resDishInfoList.size() > 0){
				/*Map<String, String> paramMap = new HashMap<>();
				String shopId = params.optString("shop_id");
				String ePoiId = shopId+"@"+tenantId;
				paramMap.put("ePoiId",  ePoiId);*/

				/*String xmdResult = XmdWMUtils.execCmd(tenantId, shopId, XmdWMUtils.CMD_FOOD_QUERYLISTBYEPOIID,paramMap,XmdWMUtils.HTTP_REQUEST_GET);
				if(xmdResult != null && !"".equals(xmdResult)){*/
					/*JSONArray arrayResult = JSONObject.fromObject(xmdResult).optJSONArray("data");*/
				Map<String, String> paramMap = new HashMap<>();
				String shopId = params.optString("shop_id");
				String ePoiId = shopId+"@"+tenantId;
				String xmdCatResult = XmdWMUtils.execCmd(tenantId, shopId, XmdWMUtils.CMD_FOOD_QUERYCATLIST,paramMap,XmdWMUtils.HTTP_REQUEST_GET);
				JSONArray catList = JSONObject.fromObject(xmdCatResult).optJSONArray("data");
				ArrayList<String> strCatList = new ArrayList<String>();
				if(!catList.isEmpty() && catList.size() > 0) {
					for (int y = 0; y < catList.size(); y++) {
						JSONObject jsonInfo = catList.getJSONObject(y);
						if(ePoiId.equals(jsonInfo.optString("ePoiId"))){
							strCatList.add(jsonInfo.optString("name"));
						}
					}
				}

				JSONArray arrayResult = shopService.getXmdwmDishInfos(tenantId, params);
				if(!arrayResult.isEmpty() && arrayResult.size() > 0){
					for(int y = 0; y < arrayResult.size(); y++){
						boolean f = true;
						JSONObject jsonInfo = arrayResult.getJSONObject(y);
						//JSONArray waimaiSkuBases = jsonInfo.getJSONArray("waiMaiDishSkuBases");
						for (int i = 0; i < resDishInfoList.size(); i++)
						{
							JSONObject dl = resDishInfoList.get(i);
							if(!dl.isNullObject() && !dl.isEmpty()){

								boolean ff = true;
								if(dl.optString("itemId").equals(String.valueOf(jsonInfo.optInt("eDishCode")))){
									f = false;
									ff = false;
									String error = "0";
									dl.put("xmdwmLastSendClassName", jsonInfo.optString("categoryName"));
									dl.put("xmdwmItemName", jsonInfo.optString("dishName"));
									dl.put("xmdwmId", jsonInfo.optInt("dishId", 0));

									String xmdwmUnit = jsonInfo.optString("unit");
									/*String xmdwmUnit = "";
									if(waimaiSkuBases != null && !waimaiSkuBases.isEmpty() && waimaiSkuBases.size() > 0){
										xmdwmUnit = waimaiSkuBases.getJSONObject(0).optString("spec");
									}*/
									dl.put("xmdwmUnit", xmdwmUnit);

									Double xmdwmPrice = jsonInfo.optDouble("price", 0);
									/*Double xmdwmPrice = 0d;
									if(waimaiSkuBases != null && !waimaiSkuBases.isEmpty() && waimaiSkuBases.size() > 0){
										xmdwmPrice = waimaiSkuBases.getJSONObject(0).optDouble("price", 0);
									}*/
									dl.put("xmdwmPrice", xmdwmPrice);
									if(dl.optString("defaultPrice") != null && !dl.optString("defaultPrice").equals("")){
										if(dl.optDouble("defaultPrice") != xmdwmPrice){
											error = "6";
										}
									}
									if(!dl.optString("unit").equals(xmdwmUnit)){
										error = "4";
									}
									if(!dl.optString("itemName").equals(jsonInfo.optString("dishName"))){
										error = "6";
									}
									if(!dl.optString("lastSendClassName").equals(jsonInfo.optString("categoryName"))){
										error = "8";
									}
									dl.put("error", error);
								}
								if(dl.optString("lastSendClassName").equals(jsonInfo.optString("categoryName")) && dl.optString("itemName").equals(jsonInfo.optString("dishName")) && ff){
									f = false;
									dl.put("xmdwmLastSendClassName", jsonInfo.optString("categoryName"));
									dl.put("xmdwmItemName", jsonInfo.optString("dishName"));
									dl.put("xmdwmId", jsonInfo.optString("dishId"));

									String xmdwmUnit = jsonInfo.optString("unit");
									/*String xmdwmUnit = "";
									if(waimaiSkuBases != null && !waimaiSkuBases.isEmpty() && waimaiSkuBases.size() > 0){
										xmdwmUnit = waimaiSkuBases.getJSONObject(0).optString("spec");
									}*/
									dl.put("xmdwmUnit", xmdwmUnit);

									Double xmdwmPrice = jsonInfo.optDouble("price", 0);
									/*Double xmdwmPrice = 0d;
									if(waimaiSkuBases != null && !waimaiSkuBases.isEmpty() && waimaiSkuBases.size() > 0){
										xmdwmPrice = waimaiSkuBases.getJSONObject(0).optDouble("price", 0);
									}*/
									dl.put("xmdwmPrice", xmdwmPrice);
									String error = "0";
									/*if(dl.optString("thirdItemId") != null && !dl.optString("thirdItemId").equals("") && !dl.optString("thirdItemId").equals("null")){
												if(!dl.optString("thirdItemId").equals(String.valueOf(item.getId()))){
													error = "1";
												}
											}*/
									if(dl.optString("itemId") != null && !dl.optString("itemId").equals("") && !dl.optString("itemId").equals("null")){
										if(!dl.optString("itemId").equals(jsonInfo.optString("eDishCode"))){
											error = "1";
										}
									}
									if(dl.optString("defaultPrice") != null && !dl.optString("defaultPrice").equals("")){
										if(dl.optDouble("defaultPrice") != xmdwmPrice){
											error = "3";
										}
									}
									if(!dl.optString("unit").equals(xmdwmUnit)){
										error = "4";
									}
									if(!dl.optString("lastSendClassName").equals(jsonInfo.optString("categoryName"))){
										error = "8";
									}
									dl.put("error", error);
								}
							}
						}
						if(f){
							JSONObject itemJson = new JSONObject();
							itemJson.put("thirdClassId", "");
							itemJson.put("thirdItemId", "");
							itemJson.put("itemName", "");
							itemJson.put("lastSendClassName", "");
							itemJson.put("itemId", "");
							itemJson.put("unit", "");
							itemJson.put("unit_id", "");
							itemJson.put("defaultPrice", "");
							itemJson.put("xmdwmLastSendClassName", jsonInfo.optString("categoryName"));
							itemJson.put("xmdwmItemName", jsonInfo.optString("dishName"));
							itemJson.put("xmdwmId", jsonInfo.optString("dishId"));

							String xmdwmUnit = jsonInfo.optString("unit");
							/*String xmdwmUnit = "";
							if(waimaiSkuBases != null && !waimaiSkuBases.isEmpty() && waimaiSkuBases.size() > 0){
								xmdwmUnit = waimaiSkuBases.getJSONObject(0).optString("spec");
							}*/

							itemJson.put("xmdwmUnit", xmdwmUnit);
							itemJson.put("error", "5");
							Double xmdwmPrice = jsonInfo.optDouble("price", 0);
							/*Double xmdwmPrice = 0d;
							if(waimaiSkuBases != null && !waimaiSkuBases.isEmpty() && waimaiSkuBases.size() > 0){
								xmdwmPrice = waimaiSkuBases.getJSONObject(0).optDouble("price", 0);
							}*/
							itemJson.put("xmdwmPrice", xmdwmPrice);
							resXmdwmDishInfoList.add(itemJson);
						}
					}
				}
				/*}*/
				for(int z = 0; z < strCatList.size(); z++){
					boolean f = true;
					String xmdName = strCatList.get(z);
					for (int w = 0; w < resDishInfoList.size(); w++) {
						JSONObject dl = resDishInfoList.get(w);
						if (!dl.isNullObject() && !dl.isEmpty()) {
							if(xmdName.equals(dl.optString("lastSendClassName"))){
								f = false;
							}
						}
					}
					for (int x = 0; x < resXmdwmDishInfoList.size(); x++) {
						JSONObject dl = resXmdwmDishInfoList.get(x);
						if (!dl.isNullObject() && !dl.isEmpty()) {
							if(xmdName.equals(dl.optString("xmdwmLastSendClassName"))){
								f = false;
							}
						}
					}
					if(f){
						JSONObject itemJson = new JSONObject();
						itemJson.put("thirdClassId", "");
						itemJson.put("thirdItemId", "");
						itemJson.put("itemName", "");
						itemJson.put("lastSendClassName", "");
						itemJson.put("itemId", "");
						itemJson.put("unit", "");
						itemJson.put("unit_id", "");
						itemJson.put("defaultPrice", "");
						itemJson.put("xmdwmLastSendClassName", xmdName);
						itemJson.put("xmdwmItemName", "");
						itemJson.put("xmdwmId", "");
						itemJson.put("xmdwmUnit", "");
						itemJson.put("error", "7");
						itemJson.put("xmdwmPrice", "");
						resXmdwmDishInfoList.add(itemJson);
					}
				}
			}



			resDishInfoAllList.addAll(resDishInfoList);
			resDishInfoAllList.addAll(resXmdwmDishInfoList);


			String strError = "";
			if(resDishInfoAllList.size() > 0){
				for(JSONObject json : resDishInfoAllList){
					if(!json.optString("error").equals("0")){
						strError += json.optString("error") + ",";
					}
				}
			}

			CcRedisService rd = (CcRedisService) SpringConext
					.getBean("ccRedisServiceImpl");
			String developerKey = rd.getByKey("xmdwmMappingDishInfoList");

			if (!StringUtils.isEmpty(developerKey)) {
				rd.del("xmdwmMappingDishInfoList");
				rd.saveBykv("xmdwmMappingDishInfoList", strError, 3600 * 24 * 30);
			}else{
				rd.saveBykv("xmdwmMappingDishInfoList", strError, 3600 * 24 * 30);
			}

			List<JSONObject> resDishInfoAllSearchPageList = new ArrayList<JSONObject>();
			if(resDishInfoAllList.size() > 0){
				if (classID.equals("==全部==")) {
					params.put("class_id", "");
				}else{
					params.put("class_id", classID);
				}
				String item_name = params.optString("item_name");
				String item_class = params.optString("class_id");
				if((item_name != null && !item_name.equals("")) || (item_class != null && !item_class.equals("")))
				{
					for(JSONObject json : resDishInfoAllList){
						if(item_name != null && !item_name.equals("")){
							if(item_class != null && !item_class.equals("")){
								if((json.optString("itemName").indexOf(item_name) >= 0 && json.optString("lastSendClassName").indexOf(item_class) >= 0) || (json.optString("xmdwmItemName").indexOf(item_name) >= 0 && json.optString("xmdwmLastSendClassName").indexOf(item_class) >= 0)){
									resDishInfoAllSearchPageList.add(json);
								}
							}else{
								if(json.optString("itemName").indexOf(item_name) >= 0 || json.optString("xmdwmItemName").indexOf(item_name) >= 0){
									resDishInfoAllSearchPageList.add(json);
								}
							}
						}else{
							if(item_class != null && !item_class.equals("")){
								if(json.optString("lastSendClassName").indexOf(item_class) >= 0 || json.optString("xmdwmLastSendClassName").indexOf(item_class) >= 0){
									resDishInfoAllSearchPageList.add(json);
								}
							}
						}
					}
				}else{
					resDishInfoAllSearchPageList = resDishInfoAllList;
				}
			}
			List<JSONObject> resDishInfoAllPageList = new ArrayList<JSONObject>();
			Integer page = params.optInt("page", 0);
			Integer rows = params.optInt("rows", 0);
			Integer count = resDishInfoAllSearchPageList.size();
			Integer sNum = (page - 1) * rows;
			Integer eNum = page * rows;

			if(count > 0){
				if(eNum <= count){
					resDishInfoAllPageList = resDishInfoAllSearchPageList.subList(sNum, eNum);
				}else{
					resDishInfoAllPageList = resDishInfoAllSearchPageList.subList(sNum, count);
				}
			}


			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:getEleDishListMapping");
			ccBusniessLogBean.setRequestBody("params:" + params.toString() + "resDishInfoAllList:" + resDishInfoAllList.toString());


			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
			ccBusniessLogBean.setTzxName("");
			ccBusniessLogBean.setShopId(params.optString("shop_id"));

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());


			int pagenum = params.containsKey("page") ? (params.getInt("page") == 0 ? 1 : params.getInt("page")) : 1;
			JSONObject jsonRes = new JSONObject();
			jsonRes.put("page", pagenum);
			jsonRes.put("total", resDishInfoAllSearchPageList.size());
			jsonRes.put("rows", resDishInfoAllPageList);
			ccBusniessLogBean.setResponseBody(jsonRes.toString());

			out = response.getWriter();
			out.print(jsonRes.toString());
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
			ccBusniessLogBean.setResponseBody(e.getMessage());
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}

	/**
	 *  验证保存饿了么菜品映射的映射关系是否正确 changhui add 2017-12-13
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "saveXmdwmDishListMappingValidate")
	public void saveXmdwmDishListMappingValidate(HttpServletRequest request,
												 HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			params.put("tenantId", tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);

			CcRedisService rd = (CcRedisService) SpringConext
					.getBean("ccRedisServiceImpl");
			String developerKey = rd.getByKey("xmdwmMappingDishInfoList");

			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:saveXmdwmDishListMappingValidate");
			ccBusniessLogBean.setRequestBody("params:" + params.toString() + "developerKey:" + developerKey);


			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
			ccBusniessLogBean.setTzxName("");
			ccBusniessLogBean.setShopId(params.optString("shop_id"));

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());


			if (developerKey == null) {
				result.put("result", "ok");
				result.put("msg", "errorRedis");
			}else{
				if(developerKey.equals("")){
					result.put("result", "ok");
					result.put("msg", "");
				}else{
					boolean f = true;
					if(developerKey.indexOf("3") >= 0){
						f = false;
					}else if(developerKey.indexOf("4") >= 0){
						f = false;
					}else if(developerKey.indexOf("5") >= 0){
						f = false;
					}else if(developerKey.indexOf("6") >= 0){
						f = false;
					}else if(developerKey.indexOf("7") >= 0){
						f = false;
					}else if(developerKey.indexOf("8") >= 0){
						f = false;
					}else if(developerKey.indexOf("9") >= 0){
						f = false;
					}
					if(f){
						result.put("result", "ok");
						result.put("msg", "");
					}else{
						result.put("result", "ok");
						result.put("msg", "error");
					}
				}
			}
			ccBusniessLogBean.setResponseBody(result.toString());
			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
			result.put("result", "no");
			result.put("msg", e.getMessage());
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}


	/**
	 *  保存新美大外卖菜品映射的映射关系 changhui add 2017-12-13
	 *
	 * @param request
	 * @param response
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "saveXmdwmDishListMapping")
	public void saveXmdwmDishListMapping(HttpServletRequest request,
										 HttpServletResponse response) {

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		JSONObject result;
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				params.put(key, map.get(key)[0]);
			}

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);
			params.put("tenantId", tenantId);

			// 添加门店 权限 2016年8月12日13:53:54 xgy begin
			String conditions = (String) session
					.getAttribute("user_organ_codes_group");
			params.put("authority_organ", conditions);

			//result = shopService.getLocalDishList(tenantId, params);
			result = shopService.getLocalDishNoPageList(tenantId, params);

			JSONArray dishList = JSONArray.fromObject(result.optString("rows"));

			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.ShopRest:saveXmdwmDishListMapping");
			ccBusniessLogBean.setRequestBody("params:" + params.toString() + "dishList:" + dishList.toString());


			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
			ccBusniessLogBean.setTzxName("");
			ccBusniessLogBean.setShopId(params.optString("shop_id"));

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());

			JSONArray resDishInfoList = new JSONArray();
			for (int i = 0; i < dishList.size(); i++)
			{
				JSONObject dl = dishList.getJSONObject(i);
				if(!dl.isNullObject() && !dl.isEmpty()){
					JSONObject resDishInfo = new JSONObject();
					resDishInfo.put("categoryName", dl.optString("last_send_class_name"));
					resDishInfo.put("eDishCode", dl.optString("item_id"));
					resDishInfo.put("eDishSkuCode", dl.optString("unit_id"));
					resDishInfo.put("dishNameWithSpec", dl.optString("item_name") + "(" +  dl.optString("unit") + ")");
					resDishInfoList.add(resDishInfo);
				}
			}
			result = shopService.updateWhetherPushOver(params, resDishInfoList);
			String data = result.optString("data");
			if(data.equals("ok")){
				JSONArray  dishInfoList = shopService.getXmdwmDishInfoList(tenantId, params.optString("shop_id"), params);
				result = shopService.saveXmdwmDishAndDishCategoryMapping(params, dishInfoList);
				ccBusniessLogBean.setResponseBody("result:" + result.toString() + "" + dishInfoList.toString());
			}else{
				ccBusniessLogBean.setResponseBody("result:" + result.toString());
			}




			out = response.getWriter();
			out.print(result);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			if (null != out) {
				out.close();
			}
		}
	}

}