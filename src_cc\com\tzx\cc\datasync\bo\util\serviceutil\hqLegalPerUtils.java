package com.tzx.cc.datasync.bo.util.serviceutil;

import com.tzx.cc.datasync.bo.util.SynIdUtils;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by XUGY on 2017-07-05.
 */
public class hqLegalPerUtils {

    public static List<String> getUpdateSysparamSql(Map<String, Map<String, Object>> mapIds,List<JSONObject> list){
        List<String> listStringAdd = new ArrayList<String>();
        StringBuffer sb = null;
        for(JSONObject json : list){
            String organid = SynIdUtils.getIdBytableFakeId(mapIds, "organ", json.optString("organid").trim());
            if(StringUtils.isBlank(organid)) {
                continue;
            }
            String wmsfkfp = json.optString("wmsfkfp");
            String tssfkfp = json.optString("tssfkfp");
            String dzfpyxq = json.optString("dzfpyxq");
            String dzfpmy = json.optString("dzfpmy");
            sb = new StringBuffer();
            sb.append("update sys_parameter set para_value = ");
            if(StringUtils.equals(wmsfkfp,"Y")) {
                sb.append(1);
            } else {
                sb.append(0);
            }
            sb.append(" where para_code = 'dzfp_dsfwmsfkjdzfp' and store_id = ").append(organid);
            listStringAdd.add(sb.toString());

            sb = new StringBuffer();
            sb.append("update sys_parameter set para_value = ");
            if(StringUtils.equals(tssfkfp,"Y")) {
                sb.append(1);
            } else {
                sb.append(0);
            }
            sb.append(" where para_code = 'dzfp_posdcsfkjdzfp' and store_id = ").append(organid);
            listStringAdd.add(sb.toString());
            sb = new StringBuffer();
            sb.append("update sys_parameter set para_value = ");
            if(StringUtils.isBlank(dzfpyxq)) {
                sb.append(0);
            } else {
                sb.append(dzfpyxq);
            }
            sb.append(" where para_code = 'dzfp_ewmdyxq' and store_id = ").append(organid);
            listStringAdd.add(sb.toString());
            sb = new StringBuffer();
            sb.append("update sys_parameter set para_value = '");
            if(StringUtils.isNotBlank(dzfpmy)) {
                sb.append(dzfpmy);
            }
            sb.append("' where para_code = 'dzfp_ewmdyfpmy' and store_id = ").append(organid);
            listStringAdd.add(sb.toString());
        }
        return listStringAdd;
    }

    /**
     * 获取更新法人机构的sql
     * @param mapIds
     * @param list
     * @return
     */
    public static List<String> getUpdateOrganSql(Map<String, Map<String, Object>> mapIds,List<JSONObject> list){
        List<String> listStringAdd = new ArrayList<String>();
        StringBuffer sb = null;
        for(JSONObject json : list){
            String organid = SynIdUtils.getIdBytableFakeId(mapIds, "organ", json.optString("organid").trim());
            String hqLegalPerId = SynIdUtils.getIdBytableFakeId(mapIds, "hq_legal_per", json.optString("id").trim());
            if(StringUtils.isBlank(organid) || StringUtils.isBlank(hqLegalPerId)) {
                continue;
            }

            sb = new StringBuffer();
            sb.append("update hq_legal_per_organ_ref set organ_id = ");
            sb.append(organid);
            sb.append(" where legal_per_id = ").append(hqLegalPerId);
            listStringAdd.add(sb.toString());
        }
        return listStringAdd;
    }


    /**
     * 获取添加机构的sql
     * @param tenancyId
     * @param mapIds
     * @param list
     * @return
     */
    public static List<String> getHqLegalPerOrganRef4Add(String tenancyId, Map<String, Map<String, Object>> mapIds
            , List<JSONObject> list){
        List<String> listStringAdd = new ArrayList<String>();
        
        for(JSONObject result_obj : list){
        	String hqLegalPerId = SynIdUtils.getIdBytableFakeId(mapIds, "hq_legal_per", result_obj.optString("id").trim());
        	 StringBuffer sb = new StringBuffer();
             sb.append("delete from hq_legal_per_organ_ref where legal_per_id = ").append(hqLegalPerId);
             listStringAdd.add(sb.toString());
        }
        for(JSONObject result_obj : list){
            String organid = SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organid").trim());
            String hqLegalPerId = SynIdUtils.getIdBytableFakeId(mapIds, "hq_legal_per", result_obj.optString("id").trim());

            StringBuffer sb = new StringBuffer();
           /* sb.append("delete from hq_legal_per_organ_ref where legal_per_id = ").append(hqLegalPerId);
            listStringAdd.add(sb.toString());*/

            sb = new StringBuffer();
            sb.append("insert into hq_legal_per_organ_ref(tenancy_id,legal_per_id,organ_id)");
            sb.append("values('").append(tenancyId).append("',");
            sb.append("'").append(hqLegalPerId).append("',");
            sb.append("'").append(organid).append("')");
            /*
            sb.append("select '").append(tenancyId).append("',");
            sb.append(hqLegalPerId);
            sb.append(",").append(organid);
            sb.append(" where not EXISTS (SELECT id FROM hq_legal_per_organ_ref");
            sb.append(" WHERE legal_per_id = ");
            sb.append(hqLegalPerId);
            sb.append(" ) LIMIT 1");*/
            listStringAdd.add(sb.toString());
        }
        return listStringAdd;
    }


    /**
     * 获取法人涉及系统参数的添加sql
     * @param tenancyId
     * @param mapIds
     * @param list
     * @return
     */
    public static List<String> getSysParamSql(String tenancyId, Map<String, Map<String, Object>> mapIds
            , List<JSONObject> list){
        List<String> listStringAdd = new ArrayList<String>();
        for(JSONObject result_obj : list){
            StringBuffer sb = new StringBuffer();
            //第三方外卖的参数设置
            String wmsfkfp = result_obj.optString("wmsfkfp");
            sb.append("INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, ");
            sb.append(" para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark) ");
            sb.append(" select '").append(tenancyId).append("' as tenancy_id,");
            sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organid").trim()));
            sb.append(" as store_id,");
            sb.append(" 'POS' as system_name, '电子发票' as model_name, '第三方外卖' as para_name, 'dzfp_dsfwmsfkjdzfp' as ");
            if(StringUtils.equals(wmsfkfp,"Y")) {
                sb.append(" para_code,").append(1);
            } else {
                sb.append(" para_code,").append(0);
            }

            sb.append(" as para_value, '0' as para_defaut, '常规' as para_type, '1' as valid_state,");
            sb.append(" '0表示不支持1表示支持' as values_name, 'rif同步而来' as para_remark where NOT ");
            sb.append(" EXISTS(SELECT id FROM sys_parameter  WHERE para_code ='dzfp_dsfwmsfkjdzfp' and store_id = ");
            sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organid").trim()));
            sb.append("  ) LIMIT 1");
            listStringAdd.add(sb.toString());

            sb = new StringBuffer();
            //堂食外卖参数设置
            String tssfkfp = result_obj.optString("tssfkfp");
            sb.append("INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, ");
            sb.append(" para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark) ");
            sb.append(" select '").append(tenancyId).append("' as tenancy_id,");
            sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organid").trim()));
            sb.append(" as store_id,");
            sb.append(" 'POS' as system_name, '电子发票' as model_name, 'POS点餐是否支持开具电子发票' as para_name, 'dzfp_posdcsfkjdzfp' as ");
            if(StringUtils.equals(tssfkfp,"Y")) {
                sb.append(" para_code,").append(1);
            } else {
                sb.append(" para_code,").append(0);
            }
            sb.append(" as para_value, '0' as para_defaut, '常规' as para_type, '1' as valid_state,");
            sb.append(" '0表示不支持1表示支持' as values_name, 'rif同步而来' as para_remark where NOT ");
            sb.append(" EXISTS(SELECT id FROM sys_parameter  WHERE para_code ='dzfp_posdcsfkjdzfp' and store_id = ");
            sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organid").trim()));
            sb.append("  ) LIMIT 1");
            listStringAdd.add(sb.toString());

            //二维码有效期参数的设置
            sb = new StringBuffer();
            String dzfpyxq = result_obj.optString("dzfpyxq");
            sb.append("INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, ");
            sb.append(" para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark) ");
            sb.append(" select '").append(tenancyId).append("' as tenancy_id,");
            sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organid").trim()));
            sb.append(" as store_id,");
            sb.append(" 'POS' as system_name, '电子发票' as model_name, '二维码有效期' as para_name, 'dzfp_ewmdyxq' as ");
            if(StringUtils.isNotBlank(dzfpyxq)) {
                sb.append(" para_code,").append(dzfpyxq);
            } else {
                sb.append(" para_code,").append(0);
            }
            sb.append(" as para_value, '0' as para_defaut, '常规' as para_type, '1' as valid_state,");
            sb.append(" '0表示不支持1表示支持' as values_name, 'rif同步而来' as para_remark where NOT ");
            sb.append(" EXISTS(SELECT id FROM sys_parameter  WHERE para_code ='dzfp_ewmdyxq' and store_id = ");
            sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organid").trim()));
            sb.append("  ) LIMIT 1");
            listStringAdd.add(sb.toString());

            //电子发票秘钥
            sb = new StringBuffer();
            String dzfpmy = result_obj.optString("dzfpmy");
            sb.append("INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, ");
            sb.append(" para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark) ");
            sb.append(" select '").append(tenancyId).append("' as tenancy_id,");
            sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organid").trim()));
            sb.append(" as store_id,");
            sb.append(" 'POS' as system_name, '电子发票' as model_name, '电子发票本地秘钥' as para_name, 'dzfp_ewmdyfpmy' as para_code,'");
            if(StringUtils.isNotBlank(dzfpyxq)) {
                sb.append(dzfpmy);
            }
            sb.append("' as para_value, '0' as para_defaut, '常规' as para_type, '1' as valid_state,");
            sb.append(" '' as values_name, 'rif同步而来' as para_remark where NOT ");
            sb.append(" EXISTS(SELECT id FROM sys_parameter  WHERE para_code ='dzfp_ewmdyfpmy' and store_id = ");
            sb.append(SynIdUtils.getIdBytableFakeId(mapIds, "organ", result_obj.optString("organid").trim()));
            sb.append("  ) LIMIT 1");
            listStringAdd.add(sb.toString());
        }
        return listStringAdd;
    }

}
