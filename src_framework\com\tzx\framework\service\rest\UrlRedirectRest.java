package com.tzx.framework.service.rest;

import com.tzx.framework.bo.SystemParameterService;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

import net.sf.json.JSONObject;

import org.jasig.cas.client.authentication.AttributePrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * SSO登录后重定向
 * Created by jzq1999 on 2018-01-12.
 */
@Controller("urlRedirect")
@RequestMapping("/framework/sso")
public class UrlRedirectRest {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Value("${server.homepage}")
    private String serverHomePage;

    @Value("${saas.homepage}")
    private String saasHomePage;

    @Resource
    private SystemParameterService systemParameterService;

    @RequestMapping(value = "/redirect")
    public void urlRedirect(HttpServletRequest request, HttpServletResponse response)
    {
        logger.info("SSO登录成功进行URL重定向");
        String redirectUrl = "";

        //从统一登录系统中获取商户id
        AttributePrincipal principal = (AttributePrincipal) request.getUserPrincipal();
        if(principal != null) {
            String loginName = principal.getName();
            if(!StringUtils.isEmpty(loginName)) {
                String[] userInfos = loginName.split("_");
                String tenantId = userInfos[0];
                DBContextHolder.setTenancyid(tenantId);

                try {
                    if(StringUtils.isEmpty(tenantId)) {
                        logger.error("tenantId为空, 重定向失败");
                    } else {
                        DBContextHolder.setTenancyid(tenantId);
                        JSONObject queryResult = systemParameterService.querySystemParameterByTenantId(tenantId);

                        if(queryResult != null) {
                            String paraValue = queryResult.optString("para_value");
                            if(paraValue.equals("1")) {
                                redirectUrl = serverHomePage;
                                logger.info("Redirect url: {}", redirectUrl);
                                response.sendRedirect(redirectUrl);
                                return;
                            }
                        }

                        redirectUrl = saasHomePage;
                        logger.info("Redirect url: {}", redirectUrl);
                        response.sendRedirect(redirectUrl);
                    }
                } catch (Exception e) {
                    logger.error("SSO登录成功重定向URL异常, tenantId: {}", tenantId, e);
                }
            } else {
                logger.error("loginName为空!");
            }
        } else {
            logger.error("没取到SSO登录用户信息");
        }
    }
    
    
    @Value("${saas_url}")
	private String saasUrl;
    
    /**
     * 获取选择跳转页
    * @Title: getRedirectUrl 
    * @Description: TODO 
    * @param @param request
    * @param @param response
    * @param @return    设定文件 
    * @return JSONObject    返回类型 
    * @throws
     */
    @ResponseBody
    @RequestMapping(value = "/getRedirectUrl")
    public JSONObject getRedirectUrl(HttpServletRequest request, HttpServletResponse response){
    	response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		JSONObject returnJson = new JSONObject();
		try{
			returnJson.put("redirectUrl", saasUrl + "/framework/sso/redirect");
			logger.info("getRedirectUrl> url:"+returnJson.optString("redirectUrl"));
		}catch (Exception e){
			e.printStackTrace();
		}
		return returnJson;
    }
    
    
    /**
     * report获取选择跳转页
    * @Title: getRedirectUrl 
    * @Description: TODO 
    * @param @param request
    * @param @param response
    * @param @return    设定文件 
    * @return JSONObject    返回类型 
    * @throws
     */
    @RequestMapping(value = "/getReportUrl")
    public void getReportUrl(HttpServletRequest request, HttpServletResponse response){
    	response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		JSONObject returnJson = new JSONObject();
		try{
			response.sendRedirect(serverHomePage);
			logger.info("report :"+serverHomePage);
		}catch (Exception e){
			e.printStackTrace();
		}
    }

}
