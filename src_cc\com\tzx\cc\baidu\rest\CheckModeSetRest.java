package com.tzx.cc.baidu.rest;

import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.tzx.cc.baidu.bo.CheckModeService;
import com.tzx.cc.baidu.bo.DishService;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
@Controller("CheckModeSetRest")
@RequestMapping("/thirdparty/checkModeSetRest")
public class CheckModeSetRest
{
	
	@Resource(name = CheckModeService.NAME)
	private CheckModeService checkModeService;
	
	@Resource(name = DishService.NAME)
	private DishService dishService;
	/**
	 * 获取结算方式列表
	 */
	@RequestMapping(value = "/loadCheckModeList")
	public void loadCheckModeList(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			
			String conditions = (String) session.getAttribute("user_organ_codes_group");//权限
			obj.put("authority_organ", conditions);
			result = checkModeService.loadCheckModeList(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	/**
	 * 获取结算方式列表
	 */
	@RequestMapping(value = "/getShopCheckMode")
	public void getShopCheckMode(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			
			String conditions = (String) session.getAttribute("user_organ_codes_group");//权限
			obj.put("authority_organ", conditions);
			result = checkModeService.getShopCheckMode(tenantId, obj);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	/**
	 *添加结算方式
	 */
	@RequestMapping(value = "/saveCheckMode")
	public void saveCheckMode(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			if(obj.containsKey("id")&&StringUtils.isNotEmpty(obj.optString("id"))){
				obj.put("last_operator", session.getAttribute("employeeName"));
				obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			}else{
			obj.put("create_operator", session.getAttribute("employeeName"));
			obj.put("create_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			}
			
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			obj.put("tenancy_id", tenantId);
			result = checkModeService.saveCheckMode(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 * 查询付款方式
	 * 
	 */
	@RequestMapping(value = "/sysPayemnt")
	public void find(HttpServletRequest request, HttpServletResponse response)
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result =checkModeService.findShopPayment((String) session.getAttribute("tenentid"),Integer.parseInt(p.get("type").toString()),p);

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	/**
	 * 查询商家实收取整方式
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/sysRoundingMode")
	public void findRoundingMode(HttpServletRequest request, HttpServletResponse response)
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		JSONObject result = new JSONObject();
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String,String[]> map = request.getParameterMap();
			
			for(String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			result =checkModeService.findRoundingMode((String) session.getAttribute("tenentid"),p);

		}
		catch (Exception e)
		{
			result.put("success",false);
			result.put("msg","发生错误!");
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	/**
	 * 复制结算方式信息
	 */
	@RequestMapping(value = "/saveCopyCheckModeInfo", method = RequestMethod.POST)
	public void saveCopyCheckModeInfo(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		String code = "";
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);

			}
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id", session.getAttribute("tenentid"));
			Boolean dic = checkModeService.saveCopyCheckModeInfo((String) session.getAttribute("tenentid"), obj);

			if (dic)
			{
				result = "{\"success\": true, \"id\" : \"" + dic.toString() + "\",\"code\" : \"" + code + "\"}";
				//logger.info("[第三方结算方式信息复制返回信息：]"+result);
			}
			else
			{
				result = "{\"success\": false , \"msg\" : \"保存失败！\"}";
			}
		}
		catch (SystemException e)
		{
			ErrorCode error = e.getErrorCode();
			JSONObject jo = JSONObject.fromObject("{}");
			jo.put("msg", PropertiesLoader.getProperty(error.getNumber() + ""));
			jo.put("success", false);
			result = jo.toString();
			//logger.info("[第三方菜品信息复制返回信息：]"+result);
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	@RequestMapping(value = "deleteCheckMode")
	public void deleteCheckMode(HttpServletRequest request, HttpServletResponse response)
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject params = new JSONObject();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				params.put(key, map.get(key)[0]);
			}
			
			params.put("send_operator", session.getAttribute("employeeName"));
			params.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			

			String tenantId = (String) session.getAttribute("tenentid");
			DBContextHolder.setTenancyid(tenantId);

			result = checkModeService.deleteCheckMode(tenantId, params).toString();

			out = response.getWriter();
			out.print(result);
			out.flush();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			if (null != out)
			{
				out.close();
			}
		}

	}
	
}
