package com.tzx.report.bo.payment.service;

import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MeidaPaymentOrderQueryService
{
	String NAME = "com.tzx.report.bo.payment.impl.MeidaPaymentOrderQueryServiceImpl";

	public List<JSONObject> queryOrganTrees(String tenancyId,String storeIds);

	public com.alibaba.fastjson.JSONObject  queryOrderLists(String tenancyId,com.alibaba.fastjson.JSONObject jsonObject);

	public HSSFWorkbook export(String tenancyId,com.alibaba.fastjson.JSONObject p, HSSFWorkbook workBook) throws Exception;
}
