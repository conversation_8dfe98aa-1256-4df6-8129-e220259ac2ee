package com.tzx.boh.bo.imp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.boh.bo.PaymentManagementService;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
@Service(PaymentManagementService.NAME)
public class PaymentManagementServiceImpl implements PaymentManagementService
{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	@Override
	public String  loadLastPaymentRecord(String tenancyID, JSONObject condition) throws Exception
	{
		String lastReading="0";
		if(condition.containsKey("business_date") && condition.containsKey("water_utilit_id")&&!condition.get("water_utilit_id").equals("")&&!condition.get("business_date").equals("")&&!condition.get("business_date").equals("null")){
			StringBuilder sql = new StringBuilder();
			sql.append("select * from boh_water_utility_record");
			sql.append(" where 1=1 and recprd_type='pay' and  water_utilit_id = '" + condition.get("water_utilit_id") + "' and business_date=(select max(business_date) from boh_water_utility_record where water_utilit_id='" + condition.get("water_utilit_id") + "' and business_date < '" + condition.get("business_date") + "' and recprd_type='pay')");
			if(condition.optString("sort")!=null && !"".equals(condition.optString("sort")))
			{
				sql.append(" order by "+condition.optString("sort")+" "+condition.optString("order"));
			}
			else
			{
				sql.append(" order by tenancy_id");
			}
			List<JSONObject> list = this.dao.query4Json(tenancyID,sql.toString());
		
			if(list.size()>=1){
				  for(JSONObject result : list){
					  if(result.getString("reading")!=null&&!"null".equals(result.getString("reading"))&&!"".equals(result.getString("reading"))){
						  lastReading=result.getString("reading");
					  }
					
	              }
			}
		}
		return lastReading;
	}

	@Override
	public JSONObject loadPaymentRecord(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		if(!condition.get("organ_code").equals("0")&&!"".equals(condition.get("organ_code")) &&!"0".equals(condition.get("is_zb"))){
		sql.append("select q.*,o.org_full_name from (");	
		sql.append("SELECT A .tenancy_id,A .id,A .store_id,D.water_utility_code,D.water_utility_name,D.default_coefficient,A .remark,A .last_operator,");
		sql.append(" A .last_updatetime,null as valid_state,D.water_utility_type,D.unit,C .class_item AS water_utility_type_name,");
		sql.append(" b.org_short_name AS organ_name,A .recprd_type,A .business_date,A .water_utilit_id,A .reading ");
		sql.append(" FROM boh_water_utility_record A");
		sql.append(" JOIN organ b ON b. ID = A .store_id");
		sql.append(" JOIN hq_water_utility D ON D. ID = A .water_utilit_id");
		sql.append(" JOIN sys_dictionary C ON C .class_identifier_code = 'water_utility_type'");
		sql.append(" AND C .class_item_code = D.water_utility_type WHERE 1 = 1 and a.recprd_type='pay'   ");
		
		if (condition.containsKey("t1") )
		{
			sql.append(" and  a.business_date >= TO_DATE('" + condition.get("t1") + "','YYYY-MM-DD') ");
		}
		if (condition.containsKey("t2") )
		{
			sql.append(" and  a.business_date <= TO_DATE('" + condition.get("t2") + "','YYYY-MM-DD') ");
		}
		if (condition.containsKey("t3")&&!"".equals(condition.get("t3"))&&!"--全部--".equalsIgnoreCase(condition.optString("t3")))
		{
			sql.append(" and d.water_utility_type = ('" + condition.get("t3")+ "')");
		}
//		sql.append(" and a.store_id in ('" + condition.get("organ_id") + "')");
		sql.append(" union all");
		sql.append(" SELECT e .tenancy_id,e.ID,e .store_id,e .water_utility_code,e .water_utility_name,e .default_coefficient,e .remark,");
		sql.append(" e .last_operator,e .last_updatetime,e .valid_state,e .water_utility_type,e .unit,g .class_item AS water_utility_type_name,");
		sql.append(" f.org_short_name AS organ_name,null as recprd_type,(select to_date(to_char(current_timestamp,'YYYY-MM-dd'), 'YYYY-MM-dd')) as business_date,e.id as water_utilit_id,0 as reading");
		sql.append(" FROM hq_water_utility e");
		sql.append(" JOIN organ f ON f. ID = e .store_id");
		sql.append(" JOIN sys_dictionary g ON g .class_identifier_code = 'water_utility_type'");
		sql.append(" AND g .class_item_code = e .water_utility_type");
		sql.append(" WHERE 1 = 1  AND e .valid_state='1'  and e.id not in (select water_utilit_id from boh_water_utility_record where recprd_type='pay'   )");
		if (condition.containsKey("t3")&&!"".equals(condition.get("t3"))&&!"--全部--".equalsIgnoreCase(condition.optString("t3")))
		{
			sql.append(" and e.water_utility_type = ('" + condition.get("t3")+ "')");
		}
		
		if(condition.optString("sort")!=null && !"".equals(condition.optString("sort")))
		{
			sql.append(" order by "+condition.optString("sort")+" "+condition.optString("order"));
		}
		else
		{
			sql.append(" order by tenancy_id,business_date desc,last_updatetime desc");
		}
			sql.append(" )q,organ o where o.id=q.store_id ");
			sql.append(" and o.organ_code LIKE '"+condition.get("organ_code")+"%' ");
//			sql.append(" and  o.id in (select * from get_oids_bycode('"+condition.get("organ_code").toString().trim()+"')) ");
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenancyID, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
		List<JSONObject> newList = new ArrayList<JSONObject>();
		if(list.size()>=1){
			  for(JSONObject resultNew : list){
				  JSONObject obj = JSONObject.fromObject("{}");
				  obj.put("water_utilit_id", resultNew.getString("water_utilit_id"));
				  obj.put("business_date", resultNew.getString("business_date"));
				  String lastReading=loadLastPaymentRecord(tenancyID,obj);
				  Map<String, Object> map =new HashMap<String, Object>();
				  map.put("lastReading", lastReading);
				  if(!resultNew.getString("reading").equals("")&&resultNew.getString("reading")!=null&&!resultNew.getString("reading").equals("null")){
					  map.put("paymentAmount",Float.valueOf(resultNew.getString("reading"))-Float.valueOf(lastReading));
				  }else{
					  map.put("paymentAmount","0");
				  }
		
				  resultNew.accumulateAll(map);
				  newList.add(resultNew);
            }
		}
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", newList);
		}
		return result;
		
	}

	@Override
	public boolean checkUnique(String tenentId, JSONObject param) throws Exception
	{
		try{

			String business_date = param.optString("business_date");
			String water_utilit_id = param.optString("water_utilit_id");
			String oldId = param.optString("id");
			if (Tools.hv(business_date) && Tools.hv(water_utilit_id))
			{
				StringBuilder sql = new StringBuilder();
				sql.append("select id from boh_water_utility_record info where info.recprd_type='pay'  and info.business_date= '"+business_date+"' and info.water_utilit_id= '"+water_utilit_id+"'");
				long total = this.dao.countSql(tenentId, sql.toString());
				List<JSONObject> list = this.dao.query4Json(tenentId, this.dao.buildPageSql(param,sql.toString()));
				String id="";
				if(list.size()>=1){
					  for(JSONObject result : list){
		                	id=result.getString("id");
		                }
				}
              
                if(!oldId.equals(id)&& total >= 1){
                	return true;
                }else if(oldId.equals(id)&& total ==1){
                	return false;
                }else if( total <1){
                	return false;
                }
			}

			return true;
		}catch(Exception e)
		{
			e.printStackTrace();
			return true;
		}
	}

	@Override
	public void delete(String tenantId, String tableKey, List<JSONObject> keyList) throws Exception
	{
		if (!Tools.hv(tableKey) || !Tools.hv(keyList))
		{
			return;
		}

		this.dao.deleteBatchIgnorCase(tenantId, tableKey, keyList);
	}

}
