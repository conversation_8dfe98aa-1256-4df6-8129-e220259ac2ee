

package com.tzx.cc.invoice.electronic.test;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONObject;

import org.dom4j.DocumentException;
import org.dom4j.Element;

import sun.misc.BASE64Encoder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.Type;
import com.tzx.cc.invoice.electronic.cont.ElectronicInvoiceConst;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoiceWebServiceUtils;
import com.tzx.cc.invoice.electronic.util.KeyUtils;
import com.tzx.cc.invoice.electronic.util.XmlUtils;
import com.tzx.framework.common.util.HttpUtil;

public class Test {
//	String publicUrl = "http://msg.tzx.com.cn/";
	String publicUrl = "http://localhost:9090/";
	@org.junit.Test
	public void test2(){
		
		BigDecimal big = new BigDecimal("0.45178");
		big = big.setScale(2,BigDecimal.ROUND_HALF_UP);
		System.out.println(big.toString());
		
		/*double a = 52.05;
		double b = 0.01;
		System.out.println(a+b);
		
		JSONObject json2 = new JSONObject();
		json2.put("dd", "asdf");
		Double optDouble = json2.optDouble("dd");
		System.out.println(optDouble.isNaN());*/
	    /*{
	        "data":[{"DDRQ":"2016-11-30",
	                 "DH":"23456789"
	                 "data":[{
    	                     "PAYMENT_CLASS":"wechat_pay_md",
    	                     "JE":"22.33"
	                         }]
	                 }
	        ],
	        "store_id":369,
	        "tenancy_id":"hdl",
	        "type":"ISSUE_ELECTRONIC_INVOICE"
	    }*/
		JSONObject json = new JSONObject();
		json.put("aa", "bb");
		String str = json.toString();
		System.out.println(str);;
		Object parse = com.alibaba.fastjson.JSONObject.parse(str);
		com.alibaba.fastjson.JSONObject js = (com.alibaba.fastjson.JSONObject) parse;
		System.out.println(js.toString());
	}
	
	@org.junit.Test
	public void qrCode(){
	    StringBuffer sb = new StringBuffer();
	    sb = new StringBuffer();
//        sb.append("hdl#369#ISSUE_ELECTRONIC_INVOICE#************#2016-12-07#dzfp_wxscqsfkjdzfp#ali_pay;wechart_pay#23;33");
//        sb.append("hdl#344#ISSUE_ELECTRONIC_INVOICE#2016121312678900234#2016-12-24#dzfp_posdcsfkjdzfp#**********#0.03#cash;bankcard;bankcard;bankcard#0.01;0.01;0.01;0.01");
      //tenancy_id#store_id#DDRQ#DH#SERVICE_TYPE#SL#JE
		//hdl#369#********#999999#1#0.03#
//        sb.append("hdl#369#********#************#1#0.06#42.84");
        sb.append("xcj#3621#********#************@xcj3621********140823#1#0.06#40");
        String content = sb.toString();
//        String password = ElectronicInvoicePropertyUtil.getMsg("native_password");
        String password = "TZXSAASZFP2222I9";
        String encryptASE_MD5 = KeyUtils.encryptASE_MD5(content, password);
        encryptASE_MD5 = encryptASE_MD5.substring(0, 4);
        sb.append("#").append(encryptASE_MD5);
        // 加密后的字符串
        BASE64Encoder base64en = new BASE64Encoder();
        String newstr = base64en.encode(sb.toString().getBytes());
        newstr = newstr.replace("\r\n", "");
        System.out.println(sb.toString());
        System.out.println(newstr.toString());
	}
	
	/**
	 * 生成电子发票测试
	 */
	@org.junit.Test 
	public void callback(){
		String url = publicUrl + "invoice/elec/yyOrderCallback";
		String result = HttpUtil.sendPostRequest(url, "{\"data\":{\"bmbBbh\":\"13.0\",\"bz\":\"机器编号:499000144018\",\"corpId\":\"001c4262-3914-43c1-8853-6101430f3092\",\"ewm\":\"01,10,150003521565,54989078,40.15,20170710,02302577551314945197,1A5B\",\"fhr\":\"fh22\",\"fpDm\":\"150003521565\",\"fpHm\":\"54989078\",\"fpjz\":\"0\",\"fplx\":\"1\",\"zsfs\":\"0\",\"fpMw\":\"0306-<73>+<750<50*/5*1+0<1-/*<50>36<83*292*+73933//<9<3<5006-<73>+<750<50*48<77-5401<*9636147>01834-19>2*<4-7250\",\"fpqqlsh\":\"2017071017391500006\",\"gmfMc\":\"天帝星\",\"hjje\":40.15,\"hjse\":2.41,\"jqbh\":\"499000144018\",\"jshj\":42.56,\"jym\":\"02302577551314945197\",\"kplx\":0,\"kpr\":\"12\",\"kprq\":\"20170710174101\",\"orgId\":128,\"skr\":\"sk3\",\"xsfDzdh\":\"12 12121212\",\"xsfMc\":\"测试1\",\"xsfNsrsbh\":\"111222333456111\",\"xsfYhzh\":\"12 12\",\"items\":[{\"fphxz\":0,\"se\":2.41,\"sl\":0.06,\"xmdj\":40.150944,\"xmhsdj\":42.56,\"xmje\":40.15,\"xmjshj\":42.56,\"xmmc\":\"餐饮\",\"spbm\":\"3070401000000000000\",\"zxbm\":\"10\",\"yhzcbs\":0,\"xmsl\":1}]},\"code\":\"0000\",\"msg\":\"开票成功\",\"fpqqlsh\":\"2017071017391500006\",\"pdf\":\"JVB\",\"sharecode\":\"R3Q6\"}");
		System.out.println(result);
	}
	/**
	 * 生成电子发票测试
	 */
	@org.junit.Test
	public void sc(){
		String url = publicUrl + "invoice/elec/post";
		Data data = new Data();
		data.setType(Type.ISSUE_ELECTRONIC_INVOICE);
		data.setTenancy_id("hdl");
		data.setStore_id(369);
		List<net.sf.json.JSONObject> list = new ArrayList<net.sf.json.JSONObject>();
		net.sf.json.JSONObject object = new net.sf.json.JSONObject();
		object.put("DH", "********16260006");
		object.put("DDRQ", "2017-07-07");
		object.put("SERVICE_TYPE", "dzfp_posdcsfkjdzfp");
		
		JSONArray array = new JSONArray();
		net.sf.json.JSONObject innerjson = new net.sf.json.JSONObject();
		innerjson.put("JE", "42.56");
		innerjson.put("PAYMENT_CLASS", "ali_pay");
		array.add(innerjson);
		object.put("data", array);
		
		list.add(object);
		data.setData(list);
		String temp = JSON.toJSONString(data);
		String result = HttpUtil.sendPostRequest(url, temp);
		System.out.println(temp);
		System.out.println(result);
	}
	
	
	
	/**
	 * 查询电子发票测试
	 */
	@org.junit.Test
	public void query(){
		String url = publicUrl + "invoice/elec/post";
		Data data = new Data();
		data.setType(Type.QUERY_INVOICE_INFO);
		data.setTenancy_id("hdl");
		
		data.setStore_id(369);
		List<net.sf.json.JSONObject> list = new ArrayList<net.sf.json.JSONObject>();
		net.sf.json.JSONObject object = new net.sf.json.JSONObject();
//		object.put("FPQQLSH", "11220099887788772213");
		object.put("FPQQLSH", "********16525300000");
		object.put("XSF_NSRSBH", "**********");
		
		list.add(object);
		data.setData(list);
		String temp = JSON.toJSONString(data);
		String result = HttpUtil.sendPostRequest(url, temp);
		System.out.println(temp);
		System.out.println(result);
	}
	
	
	/**
	 * 取消电子发票测试
	 */
	@org.junit.Test
	public void calcel(){
		String url = publicUrl + "invoice/elec/post";
		Data data = new Data();
		data.setType(Type.CANCLE_LECTRONIC_INVOICE_NEWPOS);
		data.setTenancy_id("xcj");
		data.setStore_id(3621);
		List<net.sf.json.JSONObject> list = new ArrayList<net.sf.json.JSONObject>();
		net.sf.json.JSONObject object = new net.sf.json.JSONObject();
		object.put("DH", "************@xcj3621********140823");
		
		list.add(object);
		data.setData(list);
		String temp = JSON.toJSONString(data);
		String result = HttpUtil.sendPostRequest(url, temp);
		System.out.println(temp);
		System.out.println(result);
	}
	
	@org.junit.Test
	public void test() throws Exception{
		String url = "E:\\workspace\\tzxsaas20161118\\tzxsaas\\resource\\invoice\\electronic\\fpkj_templete.xml";
		File file = new File(url);
		Element root = XmlUtils.load(file);
		Element element = XmlUtils.getElement(root, "REQUEST_COMMON_FPKJ","COMMON_FPKJ_FPT");
		element.element("FPQQLSH").setText("11220099887788772213");
		element.element("KPLX").setText("1");
		element.element("XSF_NSRSBH").setText("**********");
		element.element("XSF_MC").setText("北京吉野家快餐厅");
		element.element("XSF_DZDH").setText("北京5582106");
		element.element("GMF_MC").setText("12131120");
		element.element("KPR").setText("系统");
		element.element("JSHJ").setText("-457");
		element.element("HJJE").setText("-415.45");
		element.element("HJSE").setText("-41.55");
		element.element("DDRQ").setText("20161216112121");
		element.element("YFP_DM").setText("111001490082");
		element.element("YFP_HM").setText("00048972");
		
		Element mx = XmlUtils.getElement(root, "REQUEST_COMMON_FPKJ","COMMON_FPKJ_XMXXS","COMMON_FPKJ_XMXX");
		mx.element("FPHXZ").setText("0");
		mx.element("XMMC").setText("餐饮");
		mx.element("XMJE").setText("-415.45");
		mx.element("SL").setText("0.1");
		mx.element("SE").setText("-41.55");
		
		String context = root.asXML();
		System.out.println(context);
		context = context.replaceAll("<", ElectronicInvoiceConst.LESS_THAN_CODE);
		context = context.replaceAll(">", ElectronicInvoiceConst.GREATER_THAN_CODE);
		
		
		context = ElectronicInvoiceWebServiceUtils.FPKJ("**********", context);
		String sendWebService = ElectronicInvoiceWebServiceUtils.sendWebService("http://**************:9026/wxhtkj/services/WKWebService", context);
		System.out.println(sendWebService);
		Element parse = XmlUtils.parse(sendWebService);
		Element aa = XmlUtils.getElement(parse, "Body","xmlStringResponse","return");
		System.out.println(parse.getName());
		System.out.println(aa.getText());
//		String aesEncrypt = CommonUtil.AESEncrypt(context, "UITN25DZFPC222IM");
//		byte[] encrypt = encrypt(context, "UITN25DZFPC222IM");
//		String parseByte2HexStr = parseByte2HexStr(encrypt);
//		System.out.println(parseByte2HexStr);
	}
	
	public String getXml(){
		String requestXml = "";
		requestXml = requestXml+="<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:q0=\"http://impl.service.tax.inspur\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">";
		requestXml = requestXml+="<soapenv:Body><q0:xmlString><q0:xml><business id=\"FPCX\" comment=\"发票查询\">";
		requestXml = requestXml+="<REQUEST_COMMON_FPCX class=\"REQUEST_COMMON_FPCX\">";
		requestXml = requestXml+="<XSF_NSRSBH>**********</XSF_NSRSBH><FPQQLSH>11220099887788772212</FPQQLSH> </REQUEST_COMMON_FPCX> </business></q0:xml> ";
		requestXml = requestXml+="  <q0:nsrsbh>**********</q0:nsrsbh> </q0:xmlString>  </soapenv:Body>  </soapenv:Envelope>";
		return requestXml;
	}
	
	@org.junit.Test
	public void tete() throws DocumentException{
		String sendWebService = ElectronicInvoiceWebServiceUtils.sendWebService("http://**************:9026/wxhtkj/services/WKWebService", ElectronicInvoiceWebServiceUtils.FPCX("**********", "100000000900000"));
		System.out.println(sendWebService);
		Element parse = XmlUtils.parse(sendWebService);
		Element element = XmlUtils.getElement(parse, "Body","xmlStringResponse","return");
		System.out.println(parse.getName());
		System.out.println(element.getText());
	}
	
	 /**
     * 调用接口，更新陪审员
     * @param strParam 参数
     * @throws Exception 
     */
	@org.junit.Test
    public void sendWs() throws Exception {
        HttpURLConnection http = null;
        //此方法默认会自动建立连接，即不需要http.connect()方法
        OutputStream out = null;
        try {
            URL url = new URL("http://**************:9026/wxhtkj/services/WKWebService");//注意后面没有wsdl的后缀
            http = (HttpURLConnection) url.openConnection();
            http.setDoInput(true);//设置输入
            http.setDoOutput(true);//设置输出
            http.setRequestMethod("POST");
            http.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
            http
                    .setRequestProperty("Accept",
                        "application/soap+xml, application/dime, multipart/related, text/*");
            http.setRequestProperty("Cache-Control", "no-cache");
            http.setRequestProperty("Pragma", "no-cache");
            http.setRequestProperty("SOAPAction", "\"\"");//注意必须要传一个空字符串
            http.setRequestProperty("Connection", "keep-alive");
            //定义发出的SOAP
            String xml =ElectronicInvoiceWebServiceUtils.FPCX("**********", "2016120617203300000");
            out = http.getOutputStream();
            out.write(xml.getBytes("UTF-8"));//发出消息
            //以下为接收消息
            int code = http.getResponseCode();
            System.out.println(code);
            System.out.println(xml);
            if (code == 200) {
            	InputStream in = http.getInputStream();
    			byte[] b = new byte[1024];
    			int len = 0;
    			StringWriter sw = new StringWriter();//声明放数据的对象
    			while((len=in.read(b))!=-1){
    				sw.append(new String(b,0,len,"utf-8"));
    			}
    			System.err.println("返回的结果是：\n");
    			System.err.println(sw.toString());
            }
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            if (out != null) {
                out.close();
                out = null;
            }
            if (http != null) {
                http.disconnect();
            }
        }
    }
	
	
	private String getXml2(){
		StringBuffer xml = new StringBuffer();
//		xml.append("<?xml version=\"1.0\" encoding=\"gbk\"?>");
		xml.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:q0=\"http://impl.service.tax.inspur\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">");
		xml.append("<soapenv:Body> <q0:xmlString>");
		String bu = "<business id=\"FPCX\" comment=\"发票查询\"><REQUEST_COMMON_FPCX class=\"REQUEST_COMMON_FPCX\"> <XSF_NSRSBH>**********</XSF_NSRSBH><FPQQLSH>11220099887788772212</FPQQLSH> </REQUEST_COMMON_FPCX> </business>";
		bu = bu.replaceAll("<", "&lt;");
		bu = bu.replaceAll(">", "&gt;");
		xml.append("<q0:xml>").append(bu);
		xml.append("</q0:xml> ");
//		xml.append("<q0:xml>&lt;business id=\"FPCX\" comment=\"发票查询\"&gt;&lt;REQUEST_COMMON_FPCX class=\"REQUEST_COMMON_FPCX\"&gt; &lt;XSF_NSRSBH&gt;**********&lt;/XSF_NSRSBH&gt;&lt;FPQQLSH&gt;1000000009&lt;/FPQQLSH&gt; &lt;/REQUEST_COMMON_FPCX&gt; &lt;/business&gt;</q0:xml>");
		xml.append("<q0:nsrsbh>**********</q0:nsrsbh> ");
		xml.append("</q0:xmlString>  </soapenv:Body> </soapenv:Envelope>");
		return xml.toString();
	}
	
}
