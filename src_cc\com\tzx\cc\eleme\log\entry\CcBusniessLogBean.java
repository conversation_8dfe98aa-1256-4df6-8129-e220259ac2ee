package com.tzx.cc.eleme.log.entry;

public class CcBusniessLogBean {
	private String requestId; //每个URL的请求ID
	private String tenancyId; // 商户id 
	private String shopId; // 店铺id
	private String shopName; // 店铺名
	private String category; // 分类 固定值cc
	private String type;//类型，cancelOrder|reviceOrder|finishOrder｜dish|dishCategory|shop|mapping\grabOrder
	private String channel; // 渠道类型
	private String channelName; // 渠道名称
	private String cmd; // 命令
	private String requestBody; // 请求数据
	private String responseBody; // 响应数据
	private Long createTime; // 请求时间
	private String isThird; // 是否是纯三方接口  支付宝  微信  饿了么
	private String tzxId; // 天子星id
	private String tzxName; // 天子星name
	private String thirdId; // 第三方id
	private String isNormal; // 是否正常执行
	private String errorBody; // 异常消息
	private String operAction; // 操作动作
	



	public String getTenancyId() {
		return tenancyId;
	}

	public void setTenancyId(String tenancyId) {
		this.tenancyId = tenancyId;
	}

	public String getShopId() {
		return shopId;
	}

	public void setShopId(String shopId) {
		this.shopId = shopId;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	public String getCmd() {
		return cmd;
	}

	public void setCmd(String cmd) {
		this.cmd = cmd;
	}

	public String getRequestBody() {
		return requestBody;
	}

	public void setRequestBody(String requestBody) {
		this.requestBody = requestBody;
	}

	public String getResponseBody() {
		return responseBody;
	}

	public void setResponseBody(String responseBody) {
		this.responseBody = responseBody;
	}



	

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTzxId() {
		return tzxId;
	}

	public void setTzxId(String tzxId) {
		this.tzxId = tzxId;
	}

	public String getThirdId() {
		return thirdId;
	}

	public void setThirdId(String thirdId) {
		this.thirdId = thirdId;
	}

	public String getIsNormal() {
		return isNormal;
	}

	public void setIsNormal(String isNormal) {
		this.isNormal = isNormal;
	}

	public String getErrorBody() {
		return errorBody;
	}

	public void setErrorBody(String errorBody) {
		this.errorBody = errorBody;
	}

	public String getIsThird() {
		return isThird;
	}

	public void setIsThird(String isThird) {
		this.isThird = isThird;
	}

	
	public String getOperAction() {
		return operAction;
	}

	public void setOperAction(String operAction) {
		this.operAction = operAction;
	}

	

	public String getTzxName() {
		return tzxName;
	}

	public void setTzxName(String tzxName) {
		this.tzxName = tzxName;
	}

	


	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}




	public static class TypeProperty{
		public final static String ORDER_CANCEL="cancelOrder";
		public final static String ORDER_RECIVE="reviceOrder";
		public final static String ORDER_FINISH="finishOrder";
	}
}
