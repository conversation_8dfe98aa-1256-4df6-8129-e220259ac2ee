package com.tzx.report.bo.imp.boh;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.bo.boh.SpecialAccountStatementService;
import com.tzx.report.po.boh.dao.SpecialAccountStatementDao;

@Service(SpecialAccountStatementService.NAME)
public class SpecialAccountStatementServiceImp implements SpecialAccountStatementService
{
	 @Resource(name = SpecialAccountStatementDao.NAME)
	 SpecialAccountStatementDao specialAccountStatementDao;

	 @Resource
	 private CommonMethodAreaService commonMethodAreaService;
	 
	@Override
	public JSONObject getCashierShift(String attribute, JSONObject p) throws Exception {
		return specialAccountStatementDao.getCashierShift(attribute,p);
	}
}