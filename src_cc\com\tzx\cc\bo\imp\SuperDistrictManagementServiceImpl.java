package com.tzx.cc.bo.imp;

import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.cc.bo.SuperDistrictManagementService;
import com.tzx.framework.common.util.dao.GenericDao;

@Service(SuperDistrictManagementService.NAME)
public class SuperDistrictManagementServiceImpl implements SuperDistrictManagementService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Override
	public JSONObject loadSuperDistrictList(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT b.tenancy_id,b.address,b.five_code,b. ID,b. KEY,b.last_operator,to_char(b.last_updatetime,'yyyy-MM-dd') as last_updatetime,b.phonetic_code,b.remark,b.store_id,b.valid_state,a.org_full_name from cc_super_district b left join organ a on a.id = b. store_id where 1=1 ");
		if (condition.containsKey("key") && !"".equals(condition.optString("key")))
		{
			sql.append(" and  b.key like '%" + condition.optString("key") + "%'");
		}

		if (condition.containsKey("last_updatetime_from") && !"".equals(condition.optString("last_updatetime_from")))
		{
			sql.append(" and  a.last_updatetime >= TO_DATE('" + condition.get("last_updatetime_from") + "','YYYY-MM-DD') ");
		}
		if (condition.containsKey("last_updatetime_to") && !"".equals(condition.optString("last_updatetime_to")))
		{
			sql.append(" and  a.last_updatetime <= TO_DATE('" + condition.get("last_updatetime_to") + "','YYYY-MM-DD') ");
		}

		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenancyID, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));

		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}

}
