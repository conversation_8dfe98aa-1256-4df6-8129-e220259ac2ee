#Tue May 04 19:58:01 CST 2014
# hq or boh
sotreorhq=hq
ifstart=true

#ifstart=false
Url=tcp://BJ-QEMU-SAAS-MQ-NODE-1:6666
User=
Password=
qtoboh=qtoboh
qcometoboh=qtoboh_hdl_a3x3m0znn6eeqerjdud68xp6s7ojuiqz

#for tt
#ttsvr=tcp://*************:1884
tttopic=app_topic

#datasourcetype
datasourcetype=postgres
print.type=80
system_password=15f28fbc8c6e099a1e98b8a555d72374

#smstype\u662F\u7528\u4E8E\u914D\u7F6E\u4F7F\u7528\u5E73\u53F0\u8FD8\u662F\u77ED\u4FE1\u732B\uFF0C\u7528\u732B\u503C\u4E3A\u77ED\u4FE1\u732B\u670D\u52A1\u5668IP \u5982\uFF1A************,\u5982\u679C\u662F\u4F7F\u7528\u77ED\u4FE1\u5E73\u53F0\u76F4\u63A5\u51990\u4E3A\u4E0A\u6D77\u5E0C\u5965\u77ED\u4FE1\u5E73\u53F0\uFF0C\u51991\u662F\u7528\u4EBF\u7F8E\u77ED\u4FE1\u5E73\u53F0,\u51992\u4E3A\u7EBF\u4E0A\u56E2\u961F\u7559\u4E0B\u7684\u65B0\u5E73\u53F0\uFF08\u72D7\u5C4E\uFF09
smstype=2
#fsdw=\u5929\u5B50\u661F

#\u4E0B\u9762\u662F\u4EBF\u7F8E\u5E73\u53F0\u76F8\u5173\u914D\u7F6E\u53C2\u6570
#\u4EBF\u7F8E\u8D2D\u4E70\u7684\u8F6F\u4EF6\u5E8F\u5217\u53F7
softwareSerialNo=3SDK-EMY-0130-MEWSR
#\u4ECE\u4EBF\u7F8E\u8D2D\u4E70\u7684\u8F6F\u4EF6\u5E8F\u5217\u53F7\u6240\u5E26\u7684KEY \u503C
key=026404
#\u4ECE\u4EBF\u7F8E\u8D2D\u4E70\u7684\u8F6F\u4EF6\u5E8F\u5217\u53F7\u6240\u5E26\u7684\u5BC6\u7801
password=055956

#\u4E0B\u9762\u7684\u53C2\u6570\u662F\u914D\u7F6E\u4E0A\u6D77\u5E0C\u5965\u77ED\u4FE1\u5E73\u53F0\u76F8\u5173\u53C2\u6570
#\u77ED\u4FE1\u5E73\u53F0\u5730\u5740
url=http://api.52ao.com
#\u7528\u6237\u540D
user=laitianhua06
#\u5BC6\u7801\u8981\u8FDB\u884CMD5\u52A0\u5BC6,32\u4F4D\uFF0C\u5927\u5C0F\u5199\u90FD\u53EF\u4EE5\uFF0C\u5DE5\u5177\uFF1A  http://tool.chinaz.com/Tools/MD5.aspx?q=123456&md5type=0
pass=E10ADC3949BA59ABBE56E057F20F883E

#\u4E0B\u9762\u662F20150327\u65B0\u77ED\u4FE1\u5E73\u53F0\u63A5\u53E3\u8D26\u53F7\u5BC6\u7801(\u7EBF\u4E0A\u5E73\u53F0)
newkey=SDK-BBX-010-20057
newpassword=tzx68698

#\u4E0B\u9762\u662F20150723pos\u8F6C\u67E5\u4F1A\u5458\u63A5\u53E3url
#crmUrl=http://**************:8080/tzxsaas/crmRest/post
crmUrl=http://www.e7e6.net/crmRest/post

#Whether as a redis task service startup, 1 is yes
taskopen=1

#uploadImgIp
upload_img_ip=http://www.e7e6.mobi:8888/
upload_websiteimg_ip=http://www.e7e6.mobi:8888/


#
hqdata_minite_period=10

#
saas_url=http://www.e7e6.net/tzxsaas
post_url=http://www.e7e6.net/tzxsecondpay/
#
storestatus_minite_period=1

item_photo_path=itemImage
#pos alipay 
pos_payment_url=http://www.e7e6.net/payment/aliPaymentRest/aliPay
#pos wechat
pos_payment_wechat_url=http://www.e7e6.net/payment/wechat/post
#alipay notify url 
alipay_notify_url=http://www.e7e6.net/payment/aliPaymentRest/notify
#wechat notify url 
wechat_notify_url=http://www.e7e6.net/payment/wechat/notify
# \u95E8\u5E97\u5FAE\u4FE1\u652F\u4ED8\u56DE\u8C03
wechat_notify_url_new=http://www.e7e6.net/paymentCallBack/weixin/notify
#APP
app_name=app-release.apk
#
app_path=download/

#for wechat product
product_wechat_service_ip=http://www.e7e6.net
product_wechat_scmip=http://www.e7e6.net/crmRest/post

#wechat service mch message	start
wechat_service_mch_service_ip=http://www.e7e6.net
wechat_service_mch_appid=wxc2864bc7ba5baa5c
wechat_service_mch_secert=0998c4c22fcfd9bd75a94195605500ad
wechat_service_mch_mch_id=10010438
wechat_service_mch_api_secert=tzxsaasweixinpay2015101212150000
wechat_service_mch_cert=cert/apiclient_cert.p12
wechat_service_sub_mch_id = **********
#wechat service mch message end

#alipay service provider id start
alipay_service_provider_id=2088411391202430
#alipay service provider id end
#tenent_id==hdl
#store_id=48

#\u662F\u5426\u5916\u5356
is_delivery=true

# can be local IP
supplier_address=http://sp.e7e6.net/sup/supContraller/post 
saas_supplier=SAAS_SUPPLY_QUEUE


#wechat thrid
wechat_component_appid=wx54221b218387ae71
wechat_component_appsecret=0e92590caffe9c0c31d49231ac73aa6c
wechat_compoment_encodingaeskey=Dlf6NTLNpIZyA3MOrLRlIHV46QcWHcAi0G84aEL86p2
wechat_compoment_token=tzx
#wechat thrid
#wechat thrid redirect_uri
wechat_thrid_redirect_uri=http://www.e7e6.net
#wechat thrid redirect_uri

# \u65B0\u7F8E\u5927\u56E2\u8D2D&\u95EA\u60E0\u63A5\u53E3
xmd_url=http://api.open.cater.meituan.com
xmd_developerid=100113
xmd_signkey=m1fwurrvo09o33c7


# \u5916\u5356\u5E73\u53F0api\u8C03\u7528\u5730\u5740 {

#\u767E\u5EA6\u5916\u5356api
baidu_api_urL=http://api.waimai.baidu.com

#\u7F8E\u56E2\u5916\u5356api
meituan_api_url=http://waimaiopen.meituan.com/api/v1/
#\u7F8E\u56E2\u5916\u5356\u6D4B\u8BD5api
#meituan_api_url=http://test.waimaiopen.meituan.com/api/v1/

#\u997F\u4E86\u4E48api
ele_api_url=http://v2.openapi.ele.me/

#\u5927\u4F17\u70B9\u8BC4
dp_api_url=https://e.51ping.com/mpi/

#yichi
#yichi_address=http://localhost:8081/yichi/yichi/yichiContraller/post
yichi_address=http://*************/yichi/yichi/yichiContraller/post


rif_tzxApply = /tzxApply
rif_tzxApplyBom = /tzxapplyBOM
rif_finish = /tzxDistributionFinish
rif_return = /tzxReturn
rif_supplyin = /tzxSupplierIn
rif_supplyout = /tzxSupplierOut
# }

omUrl = http://om.e7e6.net
#maintaskopen = 1
#childtaskopen = 1
MQFASTDFSURL=http://www.e7e6.mobi/

#elm2.0 appid \u81EA\u52A8\u62C9\u5355\u63A5\u53E3\u65F6\u4F7F\u7528
ele_appid=15616757
#elm2.0 oauth callback used... note:must be https
ele_oauth_callbackUrl=https://www.e7e6.net/thirdpartydown/orderdownstreamrest/elm/authCallBack 

#elm2.0 oauth true|false \u997F\u4E86\u4E48\u8BA4\u8BC1\u65B9\u5F0F:\u6D4B\u8BD5\u8BA4\u8BC1|\u751F\u4EA7\u8BA4\u8BC1
ele_oauth_istest=false
