package com.tzx.report.service.rest.boh;

import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.report.bo.boh.FoodSalesSummaryReportAreaService;
import com.tzx.report.bo.boh.FoodSalesSummaryReportService;
import com.tzx.report.common.util.ConditionUtils;
import jxl.write.WriteException;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

/**
 * 
 * 菜品销售汇总报表-区域
 *
 */

@Controller("FoodSalesSummaryReportAreaRest")
@RequestMapping("/report/foodSalesSummaryAreaReportRest")
public class FoodSalesSummaryReportAreaRest
{
	private static final Logger LOGGER = Logger.getLogger(FoodSalesSummaryReportAreaRest.class);

	@Resource(name = FoodSalesSummaryReportAreaService.NAME)
	private FoodSalesSummaryReportAreaService foodSalesSummaryReportAreaService;

	@Resource
	ConditionUtils conditionUtils;
	
	
	@RequestMapping(value = "/getFoodSalesSummaryAreaReport")
	public void getFoodSalesSummaryAreaReport(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}

			String storeString = "p_store_id";
			if(p.optString(storeString).length()==0 ||
					"0".equals(p.optString(storeString)) ||
					"'0'".equals(p.optString(storeString)) ||
					"99999999".equals(p.optString(storeString)) ||
					"''".equals(p.optString(storeString)) ||
					"'99999999'".equals(p.optString(storeString))
					){
				if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
					// 判断当前是门店还是总部
					if(session.getAttribute("organ_id").equals("0")) {
						//取所有门店
						p.element(storeString, session.getAttribute("user_organ_codes_group"));
					}else {
						// 取门店
						p.element(storeString, session.getAttribute("organ_id"));
					}
				}else{
					p.element(storeString, session.getAttribute("user_organ"));
				}
			}


			result = foodSalesSummaryReportAreaService.getFoodSalesSummaryReportArea((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			LOGGER.error("getFoodSalesSummaryAreaReport接口发生异常>>>"+ ExceptionMessage.getExceptionMessage(e));
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}

}
