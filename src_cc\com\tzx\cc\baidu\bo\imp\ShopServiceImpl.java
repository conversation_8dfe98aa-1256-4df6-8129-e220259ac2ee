package com.tzx.cc.baidu.bo.imp;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.InvalidResultSetAccessException;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.LongSerializationPolicy;
import com.google.gson.reflect.TypeToken;
import com.tzx.cc.baidu.bo.ShopService;
import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.baidu.rest.RedisLock;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.baidu.util.SignHolder;
import com.tzx.cc.common.constant.DishOper;
import com.tzx.cc.common.redis.service.CcRedisService;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.bo.ThirdPartyManager;
import com.tzx.cc.thirdparty.bo.imp.BaiduManager;
import com.tzx.cc.thirdparty.bo.imp.CcManager;
import com.tzx.cc.thirdparty.bo.imp.DefaultThirdPartyManager;
import com.tzx.cc.thirdparty.bo.imp.EleManager;
import com.tzx.cc.thirdparty.bo.imp.EleMeManager;
import com.tzx.cc.thirdparty.bo.imp.MdManager;
import com.tzx.cc.thirdparty.bo.imp.MeiTuanManager;
import com.tzx.cc.thirdparty.bo.imp.WdwmManager;
import com.tzx.cc.thirdparty.bo.imp.WechatManager;
import com.tzx.cc.thirdparty.bo.imp.XinMeiDaManager;
import com.tzx.cc.thirdparty.bo.imp.YichiManager;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.ElmUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.cc.thirdparty.util.MeiTuanHelper;
import com.tzx.cc.thirdparty.util.XmdHelper;
import com.tzx.cc.thirdparty.util.XmdWMUtils;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.wechat.common.util.StringUtils;

import eleme.openapi.sdk.api.entity.product.OCategory;
import eleme.openapi.sdk.api.entity.product.OItem;
import eleme.openapi.sdk.api.entity.product.OSpec;
import eleme.openapi.sdk.api.entity.shop.OShop;
import eleme.openapi.sdk.api.enumeration.shop.OBusyLevel;
import eleme.openapi.sdk.api.exception.BusinessException;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.ProductService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service(ShopService.NAME)
@Scope("prototype")
public class ShopServiceImpl implements ShopService {
	@Resource(name = "genericDaoImpl")
	private GenericDao			dao;
	private ThirdPartyManager	thirdPartyManager;

	private static final Logger	logger							= Logger.getLogger(ShopServiceImpl.class);
	
	@Resource(name = "ccRedisServiceImpl")
	private CcRedisService redis ;
	
	@Resource(name = "saasRedisTemplate")
	private RedisTemplate<String, String> redisTemplate;
	
	private RedisLock lock;
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.tzx.cc.baidu.service.impl.ShopService#getShopInitInfo(java.lang.String
	 * , java.lang.String, java.lang.String)
	 */
	@Override
	public JSONObject getShopInitInfo(String tenantId, String shopId, String channel) throws Exception
	{

		switch (channel)
		{
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId, shopId);
				break;
			case Constant.WECHAT_CHANNEL:
				thirdPartyManager = new WechatManager(tenantId, shopId);
				break;
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId, shopId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager=new EleManager(tenantId, shopId);
				break;
			case Constant.XMDWM_CHANNEL:
				thirdPartyManager=new XinMeiDaManager(tenantId, shopId);
				break;
			default:
				break;
		}

		return thirdPartyManager.getLocalShopInfo();
		// JSONObject data=null;
		// //查询基本信息
		// data=this.dao.getShopInitInfo(tenantId, shopId, channel).get(0);
		// //查询营业时间
		// List<JSONObject> lineup=this.dao.getShopBussinessTime(tenantId,
		// shopId);
		//
		// if(Constant.BAIDU_CHANNEL.equals(channel)){
		// String lineupTimeId="";
		// String bussinessTimeFormat="";
		// List bs=new ArrayList();
		//
		// for(JSONObject json:lineup){
		// lineupTimeId+=json.optString("id")+",";
		//
		// String
		// start=json.optString("start").equals("24:00")?"00:00":json.optString("start");
		// String
		// end=json.optString("end").equals("24:00")?"00:00":json.optString("end");
		// if(!CommonUtil.checkStringIsNotEmpty(start)||!CommonUtil.checkStringIsNotEmpty(end))
		// continue;
		//
		// Map m=new HashMap();
		// m.put("start", start);
		// m.put("end", end);
		// bs.add(m);
		// bussinessTimeFormat+=start+"-"+end+";";
		// }
		// data.put("business_time", bs);
		// if(!bussinessTimeFormat.equals("")){
		// bussinessTimeFormat=bussinessTimeFormat.substring(0,
		// bussinessTimeFormat.length()-1);
		// }
		// if(!lineupTimeId.equals("")){
		// lineupTimeId=lineupTimeId.substring(0, lineupTimeId.length()-1);
		// }
		// data.put("business_time_format", bussinessTimeFormat);
		// data.put("lineup_time_org_id",lineupTimeId);
		// data.put("tenant_id", tenantId);
		//
		// }
		// if(Constant.DIANPING_CHANNEL.equals(channel)){
		//
		// String lineupTimeId="";
		// String bussinessTimeFormat="";
		// List bs=new ArrayList();
		// bussinessTimeFormat +="[";
		// for(JSONObject json:lineup){
		// lineupTimeId+=json.optString("id")+",";
		//
		// String start=json.optString("start");
		// String end=json.optString("end");
		// if(!CommonUtil.checkStringIsNotEmpty(start)||!CommonUtil.checkStringIsNotEmpty(end))
		// continue;
		//
		// Map m=new HashMap();
		// m.put("start", start);
		// m.put("end", end);
		// bs.add(m);
		// bussinessTimeFormat+= "["+start+","+end+"]"+",";
		// }
		// data.put("business_time", bs);
		// if(!bussinessTimeFormat.equals("")){
		// bussinessTimeFormat=bussinessTimeFormat.substring(0,
		// bussinessTimeFormat.length()-1);
		// }
		// if(!lineupTimeId.equals("")){
		// lineupTimeId=lineupTimeId.substring(0, lineupTimeId.length()-1);
		// }
		// bussinessTimeFormat +="]";
		// data.put("business_time_format", bussinessTimeFormat);
		// data.put("lineup_time_org_id",lineupTimeId);
		// data.put("tenant_id", tenantId);
		// }
		//
		// return data;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.tzx.cc.baidu.service.impl.ShopService#shopList(java.lang.String,
	 * java.lang.String, java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject shopList(String tenantId, JSONObject params) throws Exception
	{

		String channel = params.optString("channel");
		String shopId = params.optString("shop_id");
		switch (channel)
		{
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId, shopId);
				break;
			case Constant.WECHAT_CHANNEL:
				thirdPartyManager = new WechatManager(tenantId, shopId);
				break;
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId, shopId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager = new EleManager(tenantId, shopId);
				break;
			case Constant.WDWM_CHANEL:
				thirdPartyManager = new WdwmManager(tenantId, shopId);
				break;
			case Constant.XMDWM_CHANNEL:
				thirdPartyManager=new XinMeiDaManager(tenantId, shopId);
				break;
			default:
				thirdPartyManager=new DefaultThirdPartyManager(tenantId,channel);
				break;
		}

		return thirdPartyManager.getLocalShopList(params);

		// if(null!=params){
		// return this.dao.shopList(tenantId, shopId, channel,
		// params.optInt("page"), params.optInt("rows"));
		// }else{
		// return this.dao.shopList(tenantId, shopId, channel, 1, 999);
		// }

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.tzx.cc.baidu.service.impl.ShopService#shopCreateOrUpdate(java.lang
	 * .String, net.sf.json.JSONObject, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject shopCreateOrUpdate(String tenantId, JSONObject params) throws Exception
	{

		String channel = params.optString("channel");
		String shopId = params.optString("shop_id");

		switch (channel)
		{
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId, shopId);
				break;
			case Constant.WECHAT_CHANNEL:
				thirdPartyManager = new WechatManager(tenantId, null);
				break;
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId, shopId);
				break;
			case Constant.ELE_CHANNEL:
				if(params.optString("version").equals("2.0")){
					thirdPartyManager=new EleMeManager(tenantId, shopId);
				}else{
					thirdPartyManager=new EleManager(tenantId, shopId);
				}
				if("".equals(params.optString("no_agent_fee_total")) || "null".equals(params.optString("no_agent_fee_total"))){
					params.put("no_agent_fee_total", "0");
				}
				if("".equals(params.optString("min_invoice_fee")) || "null".equals(params.optString("min_invoice_fee"))){
					params.put("min_invoice_fee", "0");
				}
				break;
			case Constant.WDWM_CHANEL:
				thirdPartyManager=new WdwmManager(tenantId, shopId);
				break;
			case Constant.XMDWM_CHANNEL:
				thirdPartyManager=new XinMeiDaManager(tenantId, shopId);
				break;
			default:
				break;
		}
		return thirdPartyManager.saveShopInfo(params);
		// if(params.containsKey("channel")&&"BD06".equalsIgnoreCase(params.optString("channel"))){
		// params.put("channel", Constant.BAIDU_CHANNEL);
		// params.put("shop_state", Constant.BAIDU_SHOP_OPEN);
		//
		// Shop shop=generateBaiduShop(params);
		//
		// Map category=shop.getCategorys().get(0);
		//
		// params.put("category1", category.get("category1"));
		// params.put("category2", category.get("category2"));
		// params.put("category3", category.get("category3"));
		//
		// //修改
		// if(CommonUtil.checkStringIsNotEmpty(params.optString("id"))){
		//
		// List bs=new ArrayList();
		// String[]
		// bussinessTimes=params.optString("business_time_format").split(";");
		// for(String t:bussinessTimes){
		// String[] lt=t.split("-");
		// String start=lt[0];
		// String end=lt[1];
		//
		// Map m=new HashMap();
		// m.put("start", start);
		// m.put("end", end);
		// bs.add(m);
		// }
		//
		// shop.setBusiness_time(bs);
		//
		// DefaultTransactionDefinition def = new
		// DefaultTransactionDefinition();
		// def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
		// // 事物隔离级别
		// TransactionStatus status = transactionManager.getTransaction(def); //
		// 获得事务状态
		//
		// try{
		//
		// this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);
		//
		// String cmd=CommonUtil.cmdFactory(params.optString("source"),
		// params.optString("secret"), CmdType.SHOP_UPDATE,
		// shop.JSONFormatBean());
		//
		// String res=CommonUtil.httpPost(Constant.BAIDU_API_URL,cmd);
		//
		// JSONObject resJson=JSONObject.fromObject(res);
		//
		// JSONObject body=(resJson.optJSONObject("body"));
		//
		// if(!body.optString("errno").equals("0")){
		// result.put("errno", body.optString("errno"));
		// result.put("error", body.optString("error"));
		// throw new Exception("百度api调用失败");
		// }
		// transactionManager.commit(status);
		// }catch (Exception e) {
		// transactionManager.rollback(status);
		// }
		// }else{//新建
		//
		//
		//
		// String cmd=CommonUtil.cmdFactory(params.optString("source"),
		// params.optString("secret"), CmdType.SHOP_CREATE,
		// shop.JSONFormatBean());
		//
		// String res=CommonUtil.httpPost(Constant.BAIDU_API_URL,cmd);
		//
		// JSONObject resJson=JSONObject.fromObject(res);
		//
		// JSONObject body=(resJson.optJSONObject("body"));
		//
		//
		// if(body.optString("errno").equals("0")){
		//
		// params.put("third_shop_id",
		// body.optJSONObject("data").optString("baidu_shop_id"));
		// this.dao.insertIgnorCase(tenantId, "cc_third_organ_info", params);
		//
		// }else{
		// result.put("errno", body.optString("errno"));
		// result.put("error", body.optString("error"));
		// }
		// }
		// }else if
		// (params.containsKey("channel")&&"DP07".equalsIgnoreCase(params.optString("channel"))){
		//
		// JSONObject dp_obj = JSONObject.fromObject("{}");
		// dp_obj.put("shopid", params.optString("shop_id")+"@"+tenantId);
		// dp_obj.put("city", params.optString("city"));
		// dp_obj.put("shopname", params.optString("name"));
		// dp_obj.put("address", params.optString("address"));
		// dp_obj.put("phonenumber", params.optString("phone"));
		// dp_obj.put("lat", params.optString("latitude"));
		// dp_obj.put("lng", params.optString("longitude"));
		// dp_obj.put("coordtype", 3);
		// dp_obj.put("interval", params.optString("delivery_time"));
		// dp_obj.put("discount", params.optString("discount"));
		// dp_obj.put("status", params.optString("shop_state"));
		// dp_obj.put("picture", params.optString("shop_logo"));
		// dp_obj.put("minfee", params.optString("min_order_price"));
		// dp_obj.put("mindeliverfee", params.optString("delivery_fee"));
		// dp_obj.put("distance", params.optString("distance"));
		// dp_obj.put("servetimejson",
		// params.optString("business_time_format"));
		// JSONObject geojson_obj =new JSONObject();
		// geojson_obj.put("type", "FeatureCollection");
		//
		// List<JSONObject> features_obj_list =new ArrayList<JSONObject>();
		// JSONObject features_obj =new JSONObject();
		//
		// JSONObject geometry_obj =new JSONObject();
		// geometry_obj.put("type", "Polygon");
		// StringBuffer coordinates_buffer =new StringBuffer();
		// String coordinates[]=params.optString("coordinate").split(";");
		// coordinates_buffer.append("[[");
		// for(int i=0;i<coordinates.length;i++){
		// coordinates_buffer.append("[");
		// coordinates_buffer.append(coordinates[i]);
		// coordinates_buffer.append("]");
		// coordinates_buffer.append(",");
		// }
		// coordinates_buffer.append("]]");
		// geometry_obj.put("coordinates",coordinates_buffer.toString());
		//
		// features_obj.put("geometry", geometry_obj);
		//
		// JSONObject properties_obj =new JSONObject();
		// properties_obj.put("delivery_price",
		// params.optString("min_order_price"));
		// properties_obj.put("coordtype", 3);
		//
		// features_obj.put("properties", properties_obj);
		//
		// features_obj_list.add(features_obj);
		// geojson_obj.put("features", features_obj_list);
		//
		// dp_obj.put("geojson", geojson_obj);
		//
		// dp_obj.put("onlinepayment", params.optString("onlinepayment"));
		//
		// dp_obj.put("invoiceSupported",
		// params.optString("invoice_support_save"));
		//
		// dp_obj.put("minInvoiceFee", params.optString("min_invoice_fee"));
		//
		// params.put("geojson", geojson_obj);
		// params.put("tenant_id", tenantId);
		// params.put("coord_type", 3);
		// params.put("invoice_support",
		// params.optString("invoice_support_save"));
		// JSONObject signJSON =new JSONObject();
		// signJSON.put("source", params.optString("source"));
		// signJSON.put("secret", params.optString("secret"));
		// JSONObject result_obj= DataPost.postData2DP(dp_obj,signJSON);
		// // if(result_obj.optString("errno").equals("0")){
		// // params.put("third_shop_id",
		// result_obj.optJSONObject("data").optString("id"));
		// // }else{
		// // result.put("errno", result_obj.optString("errno"));
		// // result.put("error", result_obj.optString("error"));
		// // }
		// //修改
		// if(CommonUtil.checkStringIsNotEmpty(params.optString("id"))){
		// this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);
		// }else{
		// this.dao.insertIgnorCase(tenantId, "cc_third_organ_info", params);
		// }
		// }
		//
		// return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.tzx.cc.baidu.service.impl.ShopService#shopSetStatus(java.lang.String,
	 * net.sf.json.JSONObject, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject shopSetStatus(String tenantId, JSONObject params) throws Exception
	{

		String channel = params.optString("channel");
		String shopId = params.optString("shopId");

		switch (channel)
		{
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId, shopId);
				break;
			case Constant.WECHAT_CHANNEL:
				thirdPartyManager = new WechatManager(tenantId, shopId);
				break;
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId, shopId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager = new EleManager(tenantId, shopId);
				break;
			case Constant.XMDWM_CHANNEL:
				thirdPartyManager=new XinMeiDaManager(tenantId, shopId);
				break;
			default:
				break;
		}

		return thirdPartyManager.setShopStatus(params);

		// String cmdType = params.optString("cmd_type");
		// String source = params.optString("source");
		// String secret = params.optString("secret");
		// String shopId = params.optString("shop_id");
		// Map map = new HashMap();
		// map.put("shop_id", shopId+"@"+tenantId);
		// CmdType type = null;
		// switch (cmdType) {
		// case "open":
		// type = CmdType.SHOP_OPEN;
		// params.put("shop_state", Constant.BAIDU_SHOP_OPEN);
		// break;
		// case "close":
		// type = CmdType.SHOP_CLOSE;
		// params.put("shop_state", Constant.BAIDU_SHOP_CLOSE);
		// break;
		// default:
		// break;
		// }
		//
		// String cmd = CommonUtil.cmdFactory(source, secret, type, map);
		//
		// String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);
		//
		// JSONObject resJson = JSONObject.fromObject(res);
		//
		// JSONObject body = (resJson.optJSONObject("body"));
		//
		// if (body.optString("errno").equals("0")) {
		//
		// this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);
		//
		// } else {
		// result.put("errno", body.optString("errno"));
		// result.put("error", body.optString("error"));
		// }
		//
		// return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.tzx.cc.baidu.service.impl.ShopService#orderOper(java.lang.String,
	 * net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject orderOper(String tenantId, JSONObject params) throws Exception
	{

		String channel = params.optString("channel");
		String shopId = params.optString("shopId");

		switch (channel)
		{
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId, shopId);
				break;
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;
			case Constant.ELE_CHANNEL:
				//at 2017-06-07从缓存中获取版本信息
				String version=ElmUtils.getVersionByTZXshopId(tenantId,shopId);
				if("2.0".equals(version)){
					thirdPartyManager = new EleMeManager(tenantId, shopId);
				}else{
					thirdPartyManager = new EleManager(tenantId, shopId);
				}
				break;
			case Constant.XMDWM_CHANNEL://changhui 2017-11-21
				thirdPartyManager = new XinMeiDaManager(tenantId, shopId);
				break;
			default:
				break;
		}
		
		JSONObject result=thirdPartyManager.postOrderStatus(params);
		logger.info("[第三方订单状态变更上传数据：]"+params+"[上传数据返回信息：]"+result);
		return result;

		// JSONObject response=new JSONObject();
		// String shopId=params.optString("shopId");
		// String orderId=params.optString("orderId");
		// String operType=params.optString("operType");
		//
		// Sign sign=SignHolder.getShopSign(tenantId, shopId);
		// String source=sign.getSource();
		// String secret=sign.getSecret();
		// CmdType type=null;
		//
		// Map body=new TreeMap();
		//
		// // Map<String,String> map=new HashMap<>();
		// // map.put("order_id", orderId);
		// body.put("order_id", orderId);
		//
		//
		// switch (operType) {
		// case "confirm":
		// type=CmdType.ORDER_CONFIRM;
		// break;
		// case "cancel":
		// type=CmdType.ORDER_CANCEL;
		// body.put("type", params.optInt("type"));
		// body.put("reason", params.optString("reason"));
		// break;
		// case "complete":
		// type=CmdType.ORDER_COMPLETE;
		// break;
		// default:
		// response.put("errno", -1);
		// response.put("error", "未支持的命令类型");
		// return response;
		// }
		//
		// String cmd = CommonUtil.cmdFactory(source, secret, type, body
		// ,"2.0");
		//
		// String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);
		//
		// JSONObject resJson = JSONObject.fromObject(res);
		//
		// JSONObject resbody = (resJson.optJSONObject("body"));
		//
		// response.put("errno", resbody.optString("errno"));
		// response.put("error", resbody.optString("error"));
		//
		// return response;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.tzx.cc.baidu.service.impl.ShopService#initMealsInfo(java.lang.String,
	 * java.lang.String, java.lang.String)
	 */
	@Override
	public List<JSONObject> initMealsInfo(String tenantId, String shopId, String channel) throws Exception
	{
		switch (channel)
		{
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId, shopId);
				break;
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager = new EleManager(tenantId, shopId);
				break;
			case Constant.WECHAT_CHANNEL:
				thirdPartyManager=new WechatManager(tenantId,shopId);
				break;
			case Constant.CC_CHANNEL:
				thirdPartyManager=new CcManager(tenantId,shopId);
				break;
			case Constant.MD_CHANNEL:
				thirdPartyManager=new MdManager(tenantId,shopId);
				break;
			default:
				break;
			
		}
		return thirdPartyManager.getPackageBoxPrice();
	}

	@Override
	public JSONObject orderStatusGet(String tenantId, JSONObject params) throws Exception
	{

		String shopId = params.optString("shopId");
		String channel = params.optString("channel");

		switch (channel)
		{
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId, shopId);
				break;
			//20180129 zhangy 加入各渠道订单状态查询实现
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;
			case Constant.XMDWM_CHANNEL:
				thirdPartyManager = new XinMeiDaManager(tenantId, shopId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager = new EleMeManager(tenantId, shopId);
				break;
			default:
				break;
		}

		return thirdPartyManager.getOrderStatus(params);
		// Sign sign=SignHolder.getShopSign(tenantId, shopId);
		// String source=sign.getSource();
		// String secret=sign.getSecret();
		// JSONObject json=new JSONObject();
		// json.put("order_id", baiduOrderId);
		// String cmd = CommonUtil.cmdFactory(source, secret,
		// CmdType.ORDER_STATUS_GET, json);
		// String res = CommonUtil.httpPost(Constant.BAIDU_API_URL, cmd);
		// return JSONObject.fromObject(res);
	}

	@Override
	public JSONObject getShopCategory(String tenantId, JSONObject params) throws Exception
	{

		String channel = params.optString("channel");

		switch (channel)
		{
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, null);
				break;

			default:
				break;
		}

		return thirdPartyManager.getShopCategory(params);
	}

	@Override
	public JSONObject saveDeliveryTime(String tenantId, JSONObject params) throws Exception
	{

		String shopId = params.optString("shopId");
		String channel = params.optString("channel");

		switch (channel)
		{
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;

			default:
				break;
		}

		return thirdPartyManager.saveDeliveryTime(params);
	}
	
	@Override
	public JSONObject saveDeliveryRegion(String tenantId, JSONObject params) throws Exception
	{

		String shopId = params.optString("shopId");
		String channel = params.optString("channel");

		switch (channel)
		{
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager = new EleManager(tenantId,params.optString("shop_id"));
				String restaurantID=getRestaurantIdBy(tenantId,channel,params.optString("shop_id"));
				params.put("third_shop_id", restaurantID);
				break;
			default:
				break;
		}

		return thirdPartyManager.saveDeliveryRegion(params);
	}
	
	private String getRestaurantIdBy(String tenantId,String channel, String shopId) throws Exception {
		String sql="select third_shop_id from cc_third_organ_info where channel='"+channel+"' AND shop_id='"+shopId+"'";
		List<JSONObject> restaurantIDs=this.dao.query4Json(tenantId, sql);
		String restaurantId=null;
		if(restaurantIDs.size()>0){
			JSONObject obj=restaurantIDs.get(0);
			restaurantId=obj.optString("third_shop_id");
		}
		return restaurantId;
	}
	
	@Override
	public JSONObject getLocalDishCategoryList(String tenantId, JSONObject params) throws Exception
	{

		String channel = params.optString("channel");
		/*
		 * if ("".equals(shopId) || "".equals(channel)) { return new
		 * JSONObject();
		 * 
		 * }
		 */
		switch (channel)
		{
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId);
				break;
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager=new EleManager(tenantId);
				break;
			case Constant.WECHAT_CHANNEL:
				thirdPartyManager=new WechatManager(tenantId);
				break;
			case Constant.XMDWM_CHANNEL:
				thirdPartyManager=new XinMeiDaManager(tenantId);
				break;
			default:
				break;
		}
		return thirdPartyManager.getLocalDishCategoryList(tenantId, params);
	}

	@Override
	public JSONObject getLocalDishCategoryListNoPage(String tenantId, JSONObject params) throws Exception
	{

		String channel = params.optString("channel");
		/*
		 * if ("".equals(shopId) || "".equals(channel)) { return new
		 * JSONObject();
		 * 
		 * }
		 */
		switch (channel)
		{
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId);
				break;
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager=new EleManager(tenantId);
				break;
			case Constant.XMDWM_CHANNEL:
				thirdPartyManager=new XinMeiDaManager(tenantId);
				break;
			default:
				break;
		}

		return thirdPartyManager.loadDishCategoryListNoPage(tenantId, params);
	}

	@Override
	public JSONObject saveDishCategory(String tenantId, JSONObject params) throws Exception
	{
		List<JSONObject> list = null;
		JSONObject result = new JSONObject();
		thirdClassIDtranslateToString(params);
		if (!params.containsKey("dishes_class"))
		{
			try
			{
				JSONObject result_obj = getLocalDishCategoryListNoPage(tenantId, params);
				list = result_obj.optJSONArray("rows");
			}
			catch (Exception e)
			{
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		else
		{
				
			list = (List<JSONObject>) GsonUtil.toT(params.opt("dishes_class").toString(), new TypeToken<List<JSONObject>>()
			{
			}.getType());
		}
		if (list.size() > 0)
		{
			String channel = params.optString("channel");
			// String whether_push_over = "no".equals(params.optString("send"))
			// ? "0" : "1";
			switch (channel)
			{
				case Constant.MEITUAN_CHANNEL:
					thirdPartyManager = new MeiTuanManager(tenantId);
					for (JSONObject dish : list)
					{
						dish.put("requestId", params.optString("requestId"));
						if ("".equals(dish.optString("send_operator")) || "null".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", params.optString("send_operator"));
						}

						if ("".equals(dish.optString("send_time")) || "null".equals(dish.optString("send_time")))
						{
							dish.put("send_time", params.optString("send_time"));
						}

						if ("".equals(dish.optString("update_operator")) || "null".equals(dish.optString("update_operator")))
						{
							dish.put("update_operator", params.optString("update_operator"));
						}

						if ("".equals(dish.optString("update_time")) || "null".equals(dish.optString("update_time")))
						{
							dish.put("update_time", params.optString("update_time"));
						}
					}

					result = thirdPartyManager.saveDishCategory(list);
					break;
				case Constant.YICHI_CHANNEL:
					thirdPartyManager = new YichiManager(tenantId);
					result = thirdPartyManager.saveDishCategory(list);
					break;
				case Constant.ELE_CHANNEL:
					EleManager	thirdPartyManagerV1=new EleManager(tenantId);
					for(JSONObject dish:list){
						dish.put("requestId", params.optString("requestId"));
						if(dish.optString("version").equals("2.0")){
							thirdPartyManager=new EleMeManager(tenantId,Integer.toString(dish.optInt("store_id")));
						}else{
							thirdPartyManager=thirdPartyManagerV1;
						}
						if(dish.containsKey("third_class_id")){
						dish.put("third_class_id", dish.opt("third_class_id").toString());
						}
						if("".equals(dish.optString("last_send_class_name"))||"null".equals(dish.optString("last_send_class_name"))){
							dish.put("last_send_class_name", dish.optString("cur_class_name"));
						}
						if("".equals(dish.optString("rank"))||"null".equals(dish.optString("rank"))){
							dish.put("rank", "1");
						}
						if ("".equals(dish.optString("send_operator")) || "null".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", params.optString("send_operator"));
						}

						if ("".equals(dish.optString("send_time")) || "null".equals(dish.optString("send_time")))
						{
							dish.put("send_time", params.optString("send_time"));
						}

						if ("".equals(dish.optString("update_operator")) || "null".equals(dish.optString("update_operator")))
						{
							dish.put("update_operator", params.optString("update_operator"));
						}

						if ("".equals(dish.optString("update_time")) || "null".equals(dish.optString("update_time")))
						{
							dish.put("update_time", params.optString("update_time"));
						}
						
						if ("".equals(dish.optString("start_sell_time")) || "null".equals(dish.optString("start_sell_time")))
						{
							dish.put("start_sell_time", params.optString("start_sell_time"));
						}
						
						if ("".equals(dish.optString("end_sell_time")) || "null".equals(dish.optString("end_sell_time")))
						{
							dish.put("end_sell_time", params.optString("end_sell_time"));
						}
						dish.put("item_class_id",dish.optInt("class_id"));
						//changhui 2017-12-20 增加判断条件，从菜品映射过来的直接变为已推送状态 start
						if (params.containsKey("isWhether"))
						{
							dish.put("isWhether", params.optString("isWhether"));
						}
						//end
					}
					result=thirdPartyManager.saveDishCategory(list);
					break;
				case Constant.XMDWM_CHANNEL:
					thirdPartyManager = new XinMeiDaManager(tenantId);
					for (JSONObject dish : list)
					{
						dish.put("requestId", params.optString("requestId"));
						if ("".equals(dish.optString("send_operator")) || "null".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", params.optString("send_operator"));
						}

						if ("".equals(dish.optString("send_time")) || "null".equals(dish.optString("send_time")))
						{
							dish.put("send_time", params.optString("send_time"));
						}

						if ("".equals(dish.optString("update_operator")) || "null".equals(dish.optString("update_operator")))
						{
							dish.put("update_operator", params.optString("update_operator"));
						}

						if ("".equals(dish.optString("update_time")) || "null".equals(dish.optString("update_time")))
						{
							dish.put("update_time", params.optString("update_time"));
						}
						if("".equals(dish.optString("rank"))||"null".equals(dish.optString("rank"))){
							dish.put("rank", "1");
						}
						//changhui 2017-11-27 增加判断条件，从菜品映射过来的直接变为已推送状态 start
						if (params.containsKey("isWhether"))
						{
							dish.put("isWhether", params.optString("isWhether"));
						}
						//end
					}

					result = thirdPartyManager.saveDishCategory(list);
					break;
				default:
					break;
			}
		}
		return result;

	}
	
	
	@Override
	public JSONObject setDishCategoryPosition(String tenantId,JSONObject params) throws Exception
	{	
		thirdPartyManager=new EleMeManager(tenantId);
		String shopIDs=params.optString("shop_id");
		String[] shopIds=shopIDs.split(",");
		List<JSONObject> categoryObjects=new ArrayList<JSONObject>();
		if(shopIds.length>1){
			for(int i=0;i<shopIds.length;i++){
				params.put("shop_id", shopIds[i]);
				categoryObjects=thirdPartyManager.getLocalNoPageDishCategoryList(tenantId, params);	
			}
		}else{
			categoryObjects=thirdPartyManager.getLocalNoPageDishCategoryList(tenantId, params);	
		}
		params.put("rows", categoryObjects);
		return thirdPartyManager.setCategoryRank(params);
	}

	private void thirdClassIDtranslateToString(JSONObject params) {
		JSONArray list=new JSONArray();
		if(params.containsKey("dishes_class")||params.containsKey("dishes")){
			if(params.containsKey("dishes")){
				list=params.optJSONArray("dishes");
			}else{
				list=params.optJSONArray("dishes_class");
			}
			for(int i=0;i<list.size();i++){
				JSONObject dishCategory=list.getJSONObject(i);
				if(!(dishCategory.containsKey("third_class_id"))){
					continue;
				}
				if(!(dishCategory.containsKey("store_id"))){
					continue;
				}
				dishCategory.put("third_class_id", dishCategory.opt("third_class_id").toString());
				dishCategory.put("store_id", dishCategory.optInt("store_id")+"");
			}
			
		}
		
	}

	@Override
	public JSONObject saveDish(String tenantId, JSONObject params) throws Exception
	{
		
		//饿了么接口写流水日志，百度其他的三方接口暂时不用写
//		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
//		ccBusniessLogBean.setRequestId(params.optString("requestId"));
//		ccBusniessLogBean.setTenancyId(tenantId);
//		ccBusniessLogBean.setCategory("cc");
//		ccBusniessLogBean.setType("dish");
//		ccBusniessLogBean.setChannel(params.optString("channel"));
//		ccBusniessLogBean.setChannelName(params.optString("channel"));//暂时保持原来结构不变，暂时就不去处理该字段内容值
//		ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.ShopServiceImpl:saveDish");
//		ccBusniessLogBean.setRequestBody(params.toString());
		
		
//		ccBusniessLogBean.setCreateTime(new Date().getTime());
//		ccBusniessLogBean.setIsNormal("1");
//		ccBusniessLogBean.setIsThird("1");
//		//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
//		if(params.containsKey("dishes")){
//		  ccBusniessLogBean.setThirdId(params.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
//		  ccBusniessLogBean.setTzxId(params.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
//	      ccBusniessLogBean.setTzxName(params.optJSONArray("dishes").getJSONObject(0).optString("item_name"));
//	      ccBusniessLogBean.setShopId(params.optJSONArray("dishes").getJSONObject(0).optString("store_id"));
//		}else{
//		  ccBusniessLogBean.setThirdId(params.optString("third_item_id"));
//		  ccBusniessLogBean.setTzxId(params.optString("item_id"));
//	      ccBusniessLogBean.setTzxName(params.optString("item_name"));
//	      ccBusniessLogBean.setShopId(params.optString("store_id"));
//		}
		//params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
//		ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

		List<JSONObject> list = null;
		JSONObject result = new JSONObject();
		String send_operator = params.optString("send_operator");
		String send_time = params.optString("send_time");
		thirdClassIDtranslateToString(params);
		thirdItemIDtranslateToString(params);
		if (!params.containsKey("dishes"))
		{
			try
			{
				params.put("oper", "push");
				JSONObject result_obj = getLocalDishNoPageList(tenantId, params);
				list = result_obj.optJSONArray("rows");
			}
			catch (Exception e)
			{
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		else
		{
			list = (List<JSONObject>) GsonUtil.toT(params.opt("dishes").toString(), new TypeToken<List<JSONObject>>()
			{
			}.getType());
			
		}
		if (list.size() > 0)
		{
			String channel = params.optString("channel");
			// String whether_push_over = "no".equals(params.optString("send"))
			// ? "0" : "1";
			switch (channel)
			{
				case Constant.MEITUAN_CHANNEL:
					thirdPartyManager = new MeiTuanManager(tenantId);
					for (JSONObject dish : list)
					{
						dish.put("requestId", params.optString("requestId"));
						dish.put("shop_id", dish.optInt("store_id"));
						dish.put("item_code", dish.optInt("item_code"));
						dish.put("channel", "MT08");
						if (params.containsKey("send"))
						{
							dish.put("send", params.optString("send"));
						}
						if ("".equals(dish.optString("item_pic")) || "null".equals(dish.optString("item_pic")))
						{
							dish.put("item_pic", dish.optString("wxxt"));
						}
						if ("".equals(dish.optString("photo1")) || "null".equals(dish.optString("photo1")))
						{
							dish.put("photo1", dish.optString("wxxt"));
						}
						if (dish.optInt("min_order_num")==0)
						{
							dish.put("min_order_num", "1");
						}
						if ("".equals(dish.optString("package_box_num")) || "null".equals(dish.optString("package_box_num")))
						{
							dish.put("package_box_num", "1");
						}
						if ("".equals(dish.optString("box_price")) || "null".equals(dish.optString("box_price")))
						{
							dish.put("box_price", "1");
						}
						if ("".equals(dish.optString("channel")) || "null".equals(dish.optString("channel")))
						{
							dish.put("channel", "MT08");
						}
						if(!StringUtils.isNotBlankOrEmpty(dish.optString("is_charge_commission"))){
							dish.put("is_charge_commission","1");
						}
						if ("".equals(dish.optString("send_operator")) || "null".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", send_operator);
						}

						if ("".equals(dish.optString("send_time")) || "null".equals(dish.optString("send_time")))
						{
							dish.put("send_time", send_time);
						}
						if ("".equals(dish.optString("is_sold_out")) || "null".equals(dish.optString("is_sold_out")))
						{
							dish.put("is_sold_out", "0");
						}
						
						if (!dish.containsKey("available_times_start")||"".equals(dish.optString("available_times_start")) || "null".equals(dish.optString("available_times_start")))
						{
							dish.put("available_times_start", "10:00");
						}
						if (!dish.containsKey("available_times_end")||"".equals(dish.optString("available_times_end")) || "null".equals(dish.optString("available_times_end")))
						{
							dish.put("available_times_end", "22:00");
						}
						if ("".equals(dish.optString("priceList")))
						{
							JSONObject condition = new JSONObject();
							condition.put("item_id", dish.optInt("item_id"));
							condition.put("shop_id", dish.optInt("shop_id"));
							dish.put("priceList", thirdPartyManager.getLocalPriceSystem(condition).opt("rows"));
						}
					}
//					try{
						result = thirdPartyManager.saveDish(list);
//						ccBusniessLogBean.setResponseBody(result.toString());
//					}catch(Exception ex){
//						ccBusniessLogBean.setErrorBody(ex.getMessage());
//						ccBusniessLogBean.setIsNormal("0");
//						ex.printStackTrace();
//					}
					
//					CcBusinessLogUtils.log(ccBusniessLogBean);
					
					break;
				case Constant.YICHI_CHANNEL:
						thirdPartyManager = new YichiManager(tenantId);
						result = thirdPartyManager.saveDish(list);	
					break;
				case Constant.ELE_CHANNEL:
					if(params.optString("version").equals("2.0")){
						thirdPartyManager=new EleMeManager(tenantId,params.optString("store_id"));
					}else{
						thirdPartyManager=new EleManager(tenantId);
					}
					
					for (JSONObject dish : list)
					{
						if(params.optString("version").equals("2.0")){
							thirdPartyManager=new EleMeManager(tenantId,Integer.toString(dish.optInt("store_id")));
						}else{
							thirdPartyManager=new EleManager(tenantId);
						}
						dish.put("requestId", params.optString("requestId"));
						dish.put("shop_id", dish.optInt("store_id"));
						dish.put("item_code", dish.optInt("item_code"));
						dish.put("channel", "EL09");
						dish.put("tenancy_id", tenantId);
						
						if (params.containsKey("send"))
						{
							dish.put("send", params.optString("send"));
						}
						if ("".equals(dish.optString("item_pic")) || "null".equals(dish.optString("item_pic")))
						{
							dish.put("image", dish.optString("wxxt"));
						}else{
							dish.put("image", dish.optString("item_pic"));
						}
						if ("".equals(dish.optString("photo1")) || "null".equals(dish.optString("photo1")))
						{
							dish.put("photo1", dish.optString("wxxt"));
						}
						if (dish.optInt("min_order_num")==0)
						{
							dish.put("min_order_num", "1");
						}
						if ("".equals(dish.optString("package_box_num")) || "null".equals(dish.optString("package_box_num")))
						{
							dish.put("package_box_num", "1");
						}
						if ("".equals(dish.optString("box_price")) || "null".equals(dish.optString("box_price")))
						{
							dish.put("box_price", "1");
						}
						if ("".equals(dish.optString("channel")) || "null".equals(dish.optString("channel")))
						{
							dish.put("channel", "EL09");
						}
						if ("".equals(dish.optString("send_operator")) || "null".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", send_operator);
						}

						if ("".equals(dish.optString("send_time")) || "null".equals(dish.optString("send_time")))
						{
							dish.put("send_time", send_time);
						}
						if ("".equals(dish.optString("is_sold_out")) || "null".equals(dish.optString("is_sold_out")))
						{
							dish.put("is_sold_out", "0");
						}
						if("".equals(dish.optString("stock"))||"null".equals(dish.optString("stock" ))){
							dish.put("stock", "999");
						}
						if("".equals(dish.optString("max_stock"))||"null".equals(dish.optString("max_stock"))){
							dish.put("max_stock", "9999");
						}
						if("".equals(dish.optString("description"))||"null".equals(dish.optString("description"))){
							dish.put("description", dish.optString("item_name"));
						}
						if("".equals(dish.optString("is_gum"))||"null".equals(dish.optString("is_gum"))){
							dish.put("is_gum", "0");
						}
						if("".equals(dish.optString("is_new"))||"null".equals(dish.optString("is_new"))){
							dish.put("is_new", "0");
						}
						if("".equals(dish.optString("is_featured"))||"null".equals(dish.optString("is_featured"))){
							dish.put("is_featured", "0");
						}
						if("".equals(dish.optString("is_spicy"))||"null".equals(dish.optString("is_spicy"))){
							dish.put("is_spicy", "0");
						}
						if (!dish.containsKey("available_times_start")||"".equals(dish.optString("available_times_start")) || "null".equals(dish.optString("available_times_start")))
						{
							dish.put("available_times_start", "10:00");
						}
						if (!dish.containsKey("available_times_end")||"".equals(dish.optString("available_times_end")) || "null".equals(dish.optString("available_times_end")))
						{
							dish.put("available_times_end", "22:00");
						}
						if(!StringUtils.isNotBlankOrEmpty(dish.optString("is_charge_commission"))){
							dish.put("is_charge_commission","0");
						}
						if(!CommonUtil.checkStringIsNotEmpty(dish.optString("image"))&&CommonUtil.checkStringIsNotEmpty(dish.optString("photo1"))){
							dish.put("image",  dish.optString("photo1"));
						}
						JSONObject condition = new JSONObject();
						condition.put("item_id", dish.optInt("item_id"));
						condition.put("shop_id", dish.optInt("shop_id"));
						String boxPrice=Double.toString(thirdPartyManager.getLocalPriceSystem(condition).optDouble("box_price",0.0)*dish.optInt("package_box_num",1));
						dish.put("box_price", boxPrice);
						//changhui 2017-12-20 增加判断条件，从菜品映射过来的直接变为已推送状态 start
						if (params.containsKey("isWhether"))
						{
							dish.put("isWhether", params.optString("isWhether"));
						}
						//end
					}
//					try{
						result=thirdPartyManager.saveDish(list);
//						ccBusniessLogBean.setResponseBody(result.toString());
//					}catch(Exception e){
//						ccBusniessLogBean.setErrorBody(e.getMessage());
//						ccBusniessLogBean.setIsNormal("0");
//						e.printStackTrace();
//					}
						//记录接口访问日志，便于定位问题分析
//						KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
//						CcBusinessLogUtils.log(ccBusniessLogBean);
						break;
				case Constant.XMDWM_CHANNEL:
					thirdPartyManager = new XinMeiDaManager(tenantId);
					for (JSONObject dish : list)
					{
						dish.put("requestId", params.optString("requestId"));
						dish.put("shop_id", dish.optInt("store_id"));
						dish.put("item_code", dish.optInt("item_code"));
						dish.put("channel", Constant.XMDWM_CHANNEL);
						if (params.containsKey("send"))
						{
							dish.put("send", params.optString("send"));
						}
						if ("".equals(dish.optString("item_pic")) || "null".equals(dish.optString("item_pic")))
						{
							dish.put("item_pic", dish.optString("wxxt"));
						}
						if ("".equals(dish.optString("photo1")) || "null".equals(dish.optString("photo1")))
						{
							dish.put("photo1", dish.optString("wxxt"));
						}
						if (dish.optInt("min_order_num")==0)
						{
							dish.put("min_order_num", "1");
						}
						if ("".equals(dish.optString("package_box_num")) || "null".equals(dish.optString("package_box_num")))
						{
							dish.put("package_box_num", "1");
						}
						if ("".equals(dish.optString("box_price")) || "null".equals(dish.optString("box_price")))
						{
							dish.put("box_price", "1");
						}
						if ("".equals(dish.optString("channel")) || "null".equals(dish.optString("channel")))
						{
							dish.put("channel", Constant.XMDWM_CHANNEL);
						}
						if(!StringUtils.isNotBlankOrEmpty(dish.optString("is_charge_commission"))){
							dish.put("is_charge_commission","1");
						}
						if ("".equals(dish.optString("send_operator")) || "null".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", send_operator);
						}

						if ("".equals(dish.optString("send_time")) || "null".equals(dish.optString("send_time")))
						{
							dish.put("send_time", send_time);
						}
						if ("".equals(dish.optString("is_sold_out")) || "null".equals(dish.optString("is_sold_out")))
						{
							dish.put("is_sold_out", "0");
						}
						
						if (!dish.containsKey("available_times_start")||"".equals(dish.optString("available_times_start")) || "null".equals(dish.optString("available_times_start")))
						{
							dish.put("available_times_start", "10:00");
						}
						if (!dish.containsKey("available_times_end")||"".equals(dish.optString("available_times_end")) || "null".equals(dish.optString("available_times_end")))
						{
							dish.put("available_times_end", "22:00");
						}
						if ("".equals(dish.optString("priceList")))
						{
							JSONObject condition = new JSONObject();
							condition.put("item_id", dish.optInt("item_id"));
							condition.put("shop_id", dish.optInt("shop_id"));
							dish.put("priceList", thirdPartyManager.getLocalPriceSystem(condition).opt("rows"));
						}
						//changhui 2017-11-27 增加判断条件，从菜品映射过来的直接变为已推送状态 start
						if (params.containsKey("isWhether"))
						{
							dish.put("isWhether", params.optString("isWhether"));
						}
						//end
					}
						result = thirdPartyManager.saveDish(list);
					break;
				default:
					break;
			}
		}
		return result;
	}
	
	private void thirdItemIDtranslateToString(JSONObject params) {
		JSONArray list=new JSONArray();
		if(params.containsKey("dishes")){
			list=params.optJSONArray("dishes");
			for(int i=0;i<list.size();i++){
				JSONObject dishCategory=list.getJSONObject(i);
				if(!(dishCategory.containsKey("third_item_id"))){
					continue;
				}
				dishCategory.put("third_item_id", dishCategory.opt("third_item_id").toString());
			}
			
		}
		
	}
	
	@Override
	public JSONObject deleteDish(String tenancyId, JSONObject data) throws Exception
	{
		
//		ccBusniessLogBean.setRequestId(data.optString("requestId"));
//		ccBusniessLogBean.setTenancyId(tenancyId);
//		ccBusniessLogBean.setShopId(data.optString("store_id"));
//		ccBusniessLogBean.setCategory("cc");
//		ccBusniessLogBean.setType("dish");
//		ccBusniessLogBean.setChannel(data.optString("channel"));
//		ccBusniessLogBean.setChannelName(data.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
//		ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.ShopServiceImpl:deleteDish");
//		ccBusniessLogBean.setRequestBody(data.toString());
//		
//		ccBusniessLogBean.setCreateTime(new Date().getTime());
//		ccBusniessLogBean.setIsNormal("1");
//		ccBusniessLogBean.setIsThird("1");
		
		//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
//		if(data.containsKey("dishes")){
//		  ccBusniessLogBean.setThirdId(data.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
//		  ccBusniessLogBean.setTzxId(data.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
//	      ccBusniessLogBean.setTzxName(data.optJSONArray("dishes").getJSONObject(0).optString("item_name"));				
//		}else{
//		  ccBusniessLogBean.setThirdId(data.optString("third_item_id"));
//		  ccBusniessLogBean.setTzxId(data.optString("item_id"));
//	      ccBusniessLogBean.setTzxName(data.optString("item_name"));					
//		}
//
//		// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
//		ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

		JSONObject result = new JSONObject();
		String channel = data.optString("channel");
		int success = 0, fail = 0;
		if (data != null)
		{

			List<JSONObject> list = null;
			thirdItemIDtranslateToString(data);
			if (!data.containsKey("dishes"))
			{
				try
				{
					data.put("oper", "delete");
					JSONObject result_obj = getLocalDishNoPageList(tenancyId, data);
					list = result_obj.optJSONArray("rows");
				}
				catch (Exception e)
				{
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			else
			{

				list = (List<JSONObject>) GsonUtil.toT(data.opt("dishes").toString(), new TypeToken<List<JSONObject>>()
				{
				}.getType());
			}
			if (list.size() > 0)
			{
				switch (channel)
				{
					case Constant.MEITUAN_CHANNEL:
						thirdPartyManager = new MeiTuanManager(tenancyId);
						
						for (JSONObject o : list)
						{
							try
							{
								o.put("requestId", data.optString("requestId"));
								o.put("store_id", o.optInt("store_id"));
								o.put("item_code", o.optInt("item_code"));
								JSONObject res = thirdPartyManager.deleteDish(o);
								if ("ok".equals(res.optString("data")))
								{
									success++;
									result.put("success", true);
									result.put("msg", "删除成功");
								}
								else
								{
									fail++;
									result.put("success", false);
									result.put("msg", "删除失败");
								}
//								ccBusniessLogBean.setResponseBody(result.toString());
							}
							catch (Exception e)
							{
								fail++;
								e.printStackTrace();
//								ccBusniessLogBean.setErrorBody(e.getMessage());
//								ccBusniessLogBean.setIsNormal("0");
							}
							
//							CcBusinessLogUtils.log(ccBusniessLogBean);
						}
						break;
					case Constant.YICHI_CHANNEL:
						thirdPartyManager = new YichiManager(tenancyId);
						for (JSONObject o : list)
						{
							try
							{
								result = thirdPartyManager.deleteDish(o);
							}
							catch (Exception e)
							{
								e.printStackTrace();
							}
						}
						break;
					case Constant.ELE_CHANNEL:
						ThirdPartyManager thirdPartyManagerV2=new EleMeManager(tenancyId);
						ThirdPartyManager thirdPartyManagerV1 = new EleManager(tenancyId);
						
						for (JSONObject o : list)
						{	
//							if("2.0".equals(o.optString("version"))){
//								thirdPartyManager = thirdPartyManagerV2;
//							}else{
//								thirdPartyManager=thirdPartyManagerV1;
//							}
							//2017-11-29 饿了么去掉1.0的方法 都在2.0
							thirdPartyManager = thirdPartyManagerV2;
							try
							{
								o.put("requestId", data.optString("requestId"));
								o.put("store_id", o.optInt("store_id"));
								o.put("item_code", o.optInt("item_code"));
								try{
									JSONObject res = thirdPartyManager.deleteDish(o);
									if ("ok".equals(res.optString("message")))
									{
										success++;
										result.put("success", true);
										result.put("msg", "删除成功");
									}
									else
									{
										fail++;
										result.put("success", false);
										result.put("msg", "删除失败");
									}
//									ccBusniessLogBean.setResponseBody(res.toString());
								}catch(Exception e){
//									ccBusniessLogBean.setErrorBody(e.getMessage());
//									ccBusniessLogBean.setIsNormal("0");
									e.printStackTrace();
								}finally{
									// 记录接口访问日志，便于定位问题分析
//									CcBusinessLogUtils.log(ccBusniessLogBean);
								}
							}
							catch (Exception e)
							{
								fail++;
								e.printStackTrace();
							}
						}
						break;
					case Constant.XMDWM_CHANNEL:
						thirdPartyManager = new XinMeiDaManager(tenancyId);
						
						for (JSONObject o : list)
						{
							try
							{
								o.put("requestId", data.optString("requestId"));
								o.put("store_id", o.optInt("store_id"));
								o.put("item_code", o.optInt("item_code"));
								JSONObject res = thirdPartyManager.deleteDish(o);
								if ("ok".equals(res.optString("data")))
								{
									success++;
									result.put("success", true);
									result.put("msg", "删除成功");
								}
								else
								{
									fail++;
									result.put("success", false);
									result.put("msg", "删除失败");
								}
							}
							catch (Exception e)
							{
								fail++;
								e.printStackTrace();
							}
						}
						break;
					default:
						break;
				}
			}
		}
		
		return result;
	}

	@Override
	public JSONObject getLocalDishList(String tenantId, JSONObject params) throws Exception
	{
		String channel = params.optString("channel");

		if ("".equals(channel))
		{
			return new JSONObject();
		}
		switch (channel)
		{
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager=new EleManager(tenantId);
				break;
			case Constant.WECHAT_CHANNEL:
				thirdPartyManager=new WechatManager(tenantId);
			//2017-11-3 刘娟 start
			case Constant.XMDWM_CHANNEL:
				thirdPartyManager=new XinMeiDaManager(tenantId);
			//2017-11-3 刘娟 end
			default:
				break;
		}

		return thirdPartyManager.getLocalDishList(params);
	}

	@Override
	public JSONObject getLocalPriceSystem(String tenantId, JSONObject params) throws Exception
	{
		String shopId = params.optString("shop_id");
		String channel = params.optString("channel");

		if ("".equals(shopId) || "".equals(channel))
		{
			return new JSONObject();
		}
		switch (channel)
		{
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId, shopId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId, shopId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager=new EleManager(tenantId, shopId);
				break;
			default:
				break;
		}
		return thirdPartyManager.getLocalPriceSystem(params);
	}

	@Override
	public JSONObject imageUpload(String tenantId, JSONObject params) throws Exception
	{
		String shopId = params.optString("shop_id");
		String imageUrl = params.optString("item_pic");
		return new MeiTuanManager(tenantId, shopId).imageUpload(imageUrl);
	}

	@Override
	public JSONObject getLocalDishNoPageList(String tenantId, JSONObject params) throws Exception
	{
		String channel = params.optString("channel");
		switch (channel)
		{
			case Constant.BAIDU_CHANNEL:
				thirdPartyManager = new BaiduManager(tenantId);
				break;
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager=new EleManager(tenantId);
				break;
			case Constant.XMDWM_CHANNEL:
				thirdPartyManager = new XinMeiDaManager(tenantId);
				break;
			default:
				break;
		}
		return thirdPartyManager.getLocalDishNoPageList(tenantId, params);
	}
	
	@Override
	public List<JSONObject> getDishClassIsMapList(String tenantId, JSONObject params) throws Exception
	{
		String channel = params.optString("channel");
		switch (channel)
		{
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager=new EleManager(tenantId);
				break;
			default:
				break;
		}
		return thirdPartyManager.getDishClassIsMapList(tenantId, params);
	}
	
	@Override
	public List<JSONObject> getDishIsMapList(String tenantId, JSONObject params) throws Exception
	{
		String channel = params.optString("channel");
		switch (channel)
		{
			case Constant.MEITUAN_CHANNEL:
				thirdPartyManager = new MeiTuanManager(tenantId);
				break;
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId);
				break;
			case Constant.ELE_CHANNEL:
				thirdPartyManager=new EleManager(tenantId);
				break;
			default:
				break;
		}
		return thirdPartyManager.getDishIsMapList(tenantId, params);
	}
	
	

	@Override
	public Boolean saveCopyDishInfo(String tenancyID, JSONObject obj) throws Exception
	{
		Boolean flag = true;
		if (obj.containsKey("copy_dishs") && obj.get("copy_dishs") != "")
		{
			@SuppressWarnings("unchecked")
			List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(obj.get("copy_dishs").toString(), new TypeToken<List<JSONObject>>()
			{}.getType());
			String item_ids="";
			if(list.size()>0){
				for(JSONObject dish_obj:list){
					item_ids += String.valueOf(dish_obj.optInt("item_code"))+",";
				}
				if(obj.optString("torgan_id").length()>0){//查询目标机构餐谱中是否包含复制菜品信息
					String sql="select a.item_id,b.store_id from hq_item_menu_details a LEFT JOIN hq_item_menu_class c on c.details_id=a.id  LEFT JOIN hq_item_menu_organ b on b.item_menu_id=a.item_menu_id where a.item_id in ("+item_ids.substring(0, item_ids.length()-1)+") and b.store_id in("+obj.optString("torgan_id")+") and c.chanel='"+obj.optString("channel")+"' AND valid_state='1'";
					List<JSONObject> all_dish_menu_to_org_list =this.dao.query4Json(tenancyID, sql);
					Map<String, String> all_dish_menu_to_org_map = new HashMap<String, String>();
					
					if(all_dish_menu_to_org_list.size()>0){
						for(JSONObject to_org_dish_obj:all_dish_menu_to_org_list){
							if(all_dish_menu_to_org_map.size()>0){
								if(!all_dish_menu_to_org_map.containsKey(to_org_dish_obj.optString("store_id")+"_"+to_org_dish_obj.optString("item_id"))){
									all_dish_menu_to_org_map.put(to_org_dish_obj.optString("store_id")+"_"+to_org_dish_obj.optString("item_id"), "true");
								}
							}else{								
								all_dish_menu_to_org_map.put(to_org_dish_obj.optString("store_id")+"_"+to_org_dish_obj.optString("item_id"), "true");
							}
						}
					}
					Map<String, String> all_third_item_to_org_map = getThirdToOrganExistItemInfo(tenancyID, obj, item_ids);//查询第三方渠道目标机构中所包含的复制菜品信息
					List<String> add_update_third_item_list = contactUpdateSql(obj,list, all_dish_menu_to_org_map,all_third_item_to_org_map);
					if(add_update_third_item_list.size()>0){
						String[] sql_Array = null;
						int size = add_update_third_item_list.size();
						sql_Array = (String[]) add_update_third_item_list.toArray(new String[size]);
						dao.getJdbcTemplate(tenancyID).batchUpdate(sql_Array);
						logger.info("[第三方菜品信息复制修改数据：]"+sql_Array.toString());
					}
				}
			}
		}
		return flag;
	}

	//根据餐谱表与第三菜品信息表的查询结果，拼接复制菜品信息是insert还是update并合并SQL
	private List<String> contactUpdateSql(JSONObject obj, List<JSONObject> list,Map<String, String> all_dish_menu_to_org_map,Map<String, String> all_third_item_to_org_map) {
		List<String> add_update_third_item_list = new ArrayList<String>();
		String add_sql_prefix="insert into cc_third_item_info (shop_id,min_order_num,package_box_num,available_times_start,available_times_end,description,item_pic,item_code,channel,rank,whether_push_over,is_sold_out,box_price,is_charge_commission) values";
		String add_sql_values="";
		for(JSONObject dish_obj:list){
			String toIds = obj.getString("torgan_id");
			String[] ids = toIds.split(",");
			String thirdExistShopIds="";
			String update_sql="";
			for (String store_id : ids)
			{
				if(all_dish_menu_to_org_map.containsKey(store_id+"_"+dish_obj.optInt("item_code"))){
					if(all_third_item_to_org_map.containsKey(store_id+"_"+dish_obj.optInt("item_code"))){
						String whether_push_over=dish_obj.optString("whether_push_over");
						if("1".equals(whether_push_over)){
							whether_push_over="3";
						}
						thirdExistShopIds+=store_id+",";
					}else{
						add_sql_values+=" ("+store_id+","+dish_obj.optInt("min_order_num")+","+dish_obj.optInt("package_box_num")+",'"+dish_obj.optString("available_times_start")+"','"+dish_obj.optString("available_times_end")+"','"+dish_obj.optString("description")+"','"+dish_obj.optString("item_pic")+"','"+dish_obj.optInt("item_code")+"','"+obj.optString("channel")+"','"+dish_obj.optString("rank")+"','0','0','"+(Double.isNaN(dish_obj.optDouble("box_price",0.0)) ? 0.0 : dish_obj.optDouble("box_price",0.0))+"','"+dish_obj.optInt("is_charge_commission",1)+"'), ";
					}
				}
			
			}
			if(thirdExistShopIds.length()>0){
				update_sql="update cc_third_item_info set min_order_num="+dish_obj.optInt("min_order_num")+",package_box_num="+dish_obj.optInt("package_box_num")+",available_times_start='"+dish_obj.optString("available_times_start")+"',available_times_end='"+dish_obj.optString("available_times_end")+"',is_sold_out='"+dish_obj.optString("is_sold_out")+"',box_price='"+(Double.isNaN(dish_obj.optDouble("box_price",0.0)) ? 0.0 : dish_obj.optDouble("box_price",0.0))+"' ,update_operator='"+obj.optString("update_operator")+"',update_time='"+obj.optString("update_time")+"',rank='"+dish_obj.optString("rank")+"',description='"+dish_obj.optString("description")+"' where item_code='"+dish_obj.optInt("item_code")+"' and shop_id in("+thirdExistShopIds.substring(0, thirdExistShopIds.lastIndexOf(","))+") and channel='"+obj.optString("channel")+"' ";
			}
			if(update_sql.length()>0){
				add_update_third_item_list.add(update_sql);
			}
		}
		if(add_sql_values.length()>0){
			add_update_third_item_list.add(add_sql_prefix+add_sql_values.substring(0, add_sql_values.lastIndexOf(",")).toString()+";");
		}
		return add_update_third_item_list;
	}

	//第三方表中目标机构中已经包含的需复制到目标机构的菜品信息
	private Map<String, String> getThirdToOrganExistItemInfo(String tenancyID,JSONObject obj, String item_ids) throws Exception {
		String sql;
		sql="SELECT shop_id,item_code from cc_third_item_info a where cast(a.item_code AS INT) in ("+item_ids.substring(0, item_ids.length()-1)+") and channel='"+obj.optString("channel")+"' AND A.shop_id IN ("+obj.optString("torgan_id")+") ";
		List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(tenancyID, sql);
		Map<String, String> all_third_item_to_org_map = new HashMap<String, String>();
		if(all_third_item_to_org_list.size()>0){
			for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
				if(all_third_item_to_org_map.size()>0){
					if(!all_third_item_to_org_map.containsKey(to_org_dish_obj.optString("shop_id")+"_"+to_org_dish_obj.optString("item_code"))){
						all_third_item_to_org_map.put(to_org_dish_obj.optString("shop_id")+"_"+to_org_dish_obj.optString("item_code"), "true");
					}
				}else{
					all_third_item_to_org_map.put(to_org_dish_obj.optString("shop_id")+"_"+to_org_dish_obj.optString("item_code"), "true");	
				}
			}
		}
		return all_third_item_to_org_map;
	}
	
	
	/*@Override
	public Boolean saveCopyDishInfo(String tenancyID, JSONObject obj) throws Exception
	{
		Boolean flag = true;
		if (obj.containsKey("copy_dishs") && obj.get("copy_dishs") != "")
		{
			@SuppressWarnings("unchecked")
			List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(obj.get("copy_dishs").toString(), new TypeToken<List<JSONObject>>()
			{}.getType());
			String item_ids="";
			long startTime=System.currentTimeMillis();
			if(list.size()>0){
				for(JSONObject dish_obj:list){
					item_ids += String.valueOf(dish_obj.optInt("item_code"))+",";
				}
				if(obj.optString("torgan_id").length()>0){
					String sql="select * from hq_item_menu_details a LEFT JOIN hq_item_menu_class c on c.details_id=a.id  LEFT JOIN hq_item_menu_organ b on b.item_menu_id=a.item_menu_id where a.item_id in ("+item_ids.substring(0, item_ids.length()-1)+") and b.store_id in("+obj.optString("torgan_id")+") and c.chanel='"+obj.optString("channel")+"' AND valid_state='1'";
					List<JSONObject> all_dish_menu_to_org_list =this.dao.query4Json(tenancyID, sql);
					Map<String, String> all_dish_menu_to_org_map = new HashMap<String, String>();
					
					if(all_dish_menu_to_org_list.size()>0){
						for(JSONObject to_org_dish_obj:all_dish_menu_to_org_list){
							all_dish_menu_to_org_map.put(to_org_dish_obj.optString("store_id")+"_"+to_org_dish_obj.optString("item_id"), "true");
						}
					}
					sql="SELECT * from cc_third_item_info a where cast(a.item_code AS INT) in ("+item_ids.substring(0, item_ids.length()-1)+") and channel='"+obj.optString("channel")+"' AND A.shop_id IN ("+obj.optString("torgan_id")+") ";
					List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(tenancyID, sql);
					Map<String, String> all_third_item_to_org_map = new HashMap<String, String>();
					
					if(all_third_item_to_org_list.size()>0){
						for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
							all_third_item_to_org_map.put(to_org_dish_obj.optString("shop_id")+"_"+to_org_dish_obj.optString("item_code"), "true");
						}
					}
					List<String> add_update_third_item_list = new ArrayList<String>();
					for(JSONObject dish_obj:list){
						String toIds = obj.getString("torgan_id");
						String[] ids = toIds.split(",");
						for (String store_id : ids)
						{
							String add_or_update_sql="";
							if(all_dish_menu_to_org_map.containsKey(store_id+"_"+dish_obj.optInt("item_code"))){
								if(all_third_item_to_org_map.containsKey(store_id+"_"+dish_obj.optInt("item_code"))){
									String whether_push_over=dish_obj.optString("whether_push_over");
									if("1".equals(whether_push_over)){
										whether_push_over="3";
									}
									add_or_update_sql="update cc_third_item_info set min_order_num="+dish_obj.optInt("min_order_num")+",package_box_num="+dish_obj.optInt("package_box_num")+",available_times_start='"+dish_obj.optString("available_times_start")+"',available_times_end='"+dish_obj.optString("available_times_end")+"',is_sold_out='"+dish_obj.optString("is_sold_out")+"',box_price='"+(Double.isNaN(dish_obj.optDouble("box_price")) ? 0.0 : dish_obj.optDouble("box_price"))+"' ,update_operator='"+obj.optString("update_operator")+"',update_time='"+obj.optString("update_time")+"',rank='"+dish_obj.optString("rank")+"',description='"+dish_obj.optString("description")+"' where item_code='"+dish_obj.optInt("item_code")+"' and shop_id='"+store_id+"'and channel='"+obj.optString("channel")+"' ";
								}else{
									add_or_update_sql="insert into cc_third_item_info (shop_id,min_order_num,package_box_num,available_times_start,available_times_end,description,item_pic,item_code,channel,rank,whether_push_over,is_sold_out,box_price,is_charge_commission) " +
											" values("+store_id+","+dish_obj.optInt("min_order_num")+","+dish_obj.optInt("package_box_num")+",'"+dish_obj.optString("available_times_start")+"','"+dish_obj.optString("available_times_end")+"','"+dish_obj.optString("description")+"','"+dish_obj.optString("item_pic")+"','"+dish_obj.optInt("item_code")+"','"+obj.optString("channel")+"','"+dish_obj.optString("rank")+"','0','0','"+(Double.isNaN(dish_obj.optDouble("box_price")) ? 0.0 : dish_obj.optDouble("box_price"))+"','"+dish_obj.optInt("is_charge_commission",1)+"') ";
								}
							}
							if(add_or_update_sql.length()>0){
								add_update_third_item_list.add(add_or_update_sql);
							}
						}
					}
					if(add_update_third_item_list.size()>0){
						String[] sql_Array = null;
						int size = add_update_third_item_list.size();
						sql_Array = (String[]) add_update_third_item_list.toArray(new String[size]);
						dao.getJdbcTemplate(tenancyID).batchUpdate(sql_Array);
						logger.info("[第三方菜品信息复制修改数据：]"+sql_Array.toString());
					}
				}
			}
		
		}
		return flag;
	}*/

	@Override
	public Boolean saveCopyDishCategoryInfo(String tenancyID, JSONObject obj) throws Exception, SystemException
	{

		Boolean flag = true;
		if (obj.containsKey("copy_dishs_category") && obj.get("copy_dishs_category") != "")
		{
			
			@SuppressWarnings("unchecked")
			List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(obj.get("copy_dishs_category").toString(), new TypeToken<List<JSONObject>>()
			{}.getType());
			String class_ids="";
			String class_names="'";
			if(list.size()>0){
				for(JSONObject dish_obj:list){
					//class_ids += String.valueOf(dish_obj.optInt("class_id"))+",";
					class_names += String.valueOf(dish_obj.optString("cur_class_name"))+"','";
				}
				//String sql="select b.store_id,c.class as item_class from hq_item_menu_details a LEFT JOIN hq_item_menu_class c on c.details_id=a.id  LEFT JOIN hq_item_menu_organ b on b.item_menu_id=a.item_menu_id where c.class in ("+class_ids.substring(0, class_ids.length()-1)+") and b.store_id in("+obj.optString("torgan_id")+") and c.chanel='"+obj.optString("channel")+"' AND valid_state='1'";
				String sql="SELECT A.class as item_class,C.store_id,e.itemclass_name FROM hq_item_menu_class A LEFT JOIN hq_item_menu_details b ON A .details_id = b. ID LEFT JOIN hq_item_menu_organ C ON C .item_menu_id = b.item_menu_id	LEFT JOIN cc_third_item_class_info d ON d.item_class_id = A . CLASS	AND d.shop_id = C .store_id LEFT JOIN hq_item_class e ON e. ID = A . CLASS LEFT JOIN organ f ON f. ID = C .store_id WHERE C .store_id in ("+obj.optString("torgan_id")+") AND A .chanel = '"+obj.optString("channel")+"' AND e.itemclass_name in ("+class_names.substring(0, class_names.length()-2)+") group by A.class,C .store_id,e.itemclass_name";
				
				List<JSONObject> all_dish_menu_to_org_list =this.dao.query4Json(tenancyID, sql);
				Map<String, String> all_dish_menu_to_org_map = new HashMap<String, String>();
				
				if(all_dish_menu_to_org_list.size()>0){
					for(JSONObject to_org_dish_obj:all_dish_menu_to_org_list){
						String class_id=to_org_dish_obj.optString("item_class");
						class_ids+=class_id+",";
						
						all_dish_menu_to_org_map.put(to_org_dish_obj.optString("store_id")+"_"+to_org_dish_obj.optString("itemclass_name"), class_id);
					}
				}
				sql="SELECT * from cc_third_item_class_info a where item_class_id in ("+class_ids.substring(0, class_ids.length()-1)+") and channel='"+obj.optString("channel")+"' AND A.shop_id IN ("+obj.optString("torgan_id")+") ";
				List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(tenancyID, sql);
				Map<String, String> all_third_item_to_org_map = new HashMap<String, String>();
				
				if(all_third_item_to_org_list.size()>0){
					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
						all_third_item_to_org_map.put(to_org_dish_obj.optString("shop_id")+"_"+to_org_dish_obj.optString("cur_class_name"), to_org_dish_obj.optString("item_class_id"));
					}
				}
				List<String> add_update_third_item_list = new ArrayList<String>();
				for(JSONObject dish_obj:list){
					String toIds = obj.getString("torgan_id");
					String[] ids = toIds.split(",");
					for (String store_id : ids)
					{
						String add_or_update_sql="";
						
						String key=store_id+"_"+dish_obj.optString("cur_class_name");
						
						if(all_dish_menu_to_org_map.containsKey(key)){
							if(all_third_item_to_org_map.containsKey(key)){
								add_or_update_sql="update cc_third_item_class_info set rank='"+dish_obj.optString("rank")+"',start_sell_time='"+dish_obj.optString("start_sell_time")+"',end_sell_time='"+dish_obj.optString("end_sell_time")+"' where item_class_id='"+all_third_item_to_org_map.get(key)+"' and shop_id='"+store_id+"'and channel='"+obj.optString("channel")+"' ";
							}else{
								add_or_update_sql="insert into cc_third_item_class_info (shop_id,item_class_id,rank,whether_push_over,update_operator,update_time,channel,start_sell_time,end_sell_time) " +
										" values("+store_id+","+all_dish_menu_to_org_map.get(key)+",'"+dish_obj.optString("rank")+"','0','"+obj.optString("update_operator")+"','"+obj.optString("update_time")+"','"+obj.optString("channel")+"','"+obj.optString("start_sell_time")+"','"+obj.optString("end_sell_time")+"') ";
							}
						}
						if(add_or_update_sql.length()>0){
							add_update_third_item_list.add(add_or_update_sql);
						}
					}
				}
				if(add_update_third_item_list.size()>0){
					String[] sql_Array = null;
					int size = add_update_third_item_list.size();
					sql_Array = (String[]) add_update_third_item_list.toArray(new String[size]);
					dao.getJdbcTemplate(tenancyID).batchUpdate(sql_Array);
					logger.info("[第三方菜品分类批量复制数据:]"+sql_Array);
				}
		
			}
		
		}
		return flag;
	
	}

	@Override
	public JSONObject loadXmdAccountInfo(String tenantId) throws Exception
	{
		JSONObject result= new JSONObject();
		JSONObject json_object= new JSONObject();
		String sql="";
		sql ="select source,secret from cc_third_organ_info where channel='XMD' limit 1";
		List<JSONObject> xmdList=this.dao.query4Json(tenantId, sql);
		if(xmdList.size()>0){
			json_object=xmdList.get(0);
			result.put("source_xmd", json_object.optString("source"));
			result.put("secret_xmd", json_object.optString("secret"));
		}
		return result;
	}

	@Override
	public JSONObject loadAccountInfo(String tenantId) throws Exception
	{
		JSONObject result= new JSONObject();
		JSONObject json_object= new JSONObject();
		String sql="";
		sql ="select source,secret from cc_third_organ_info where channel='BD06' limit 1";
		List<JSONObject> baidu_list=this.dao.query4Json(tenantId, sql);
		if(baidu_list.size()>0){
			json_object=baidu_list.get(0);
			result.put("source_bd", json_object.optString("source"));
			result.put("secret_bd", json_object.optString("secret"));
		}
		sql ="select source,secret from cc_third_organ_info where channel='MT08' limit 1";
		List<JSONObject> mt_list=this.dao.query4Json(tenantId, sql);
		if(mt_list.size()>0){
			json_object=mt_list.get(0);
			result.put("source_mt", json_object.optString("source"));
			result.put("secret_mt", json_object.optString("secret"));
		}
		sql ="select source,secret,source2,secret2,application_auth_type from cc_third_organ_info where channel='EL09'";
		List<JSONObject> ele_list=this.dao.query4Json(tenantId, sql);
		if(ele_list.size()>0){
			json_object=ele_list.get(0);
			result.put("source_ele_add", json_object.optString("source"));
			result.put("secret_ele_add", json_object.optString("secret"));
			result.put("source_ele_add2", json_object.optString("source2"));
			result.put("secret_ele_add2", json_object.optString("secret2"));
			result.put("application_auth_type", json_object.optString("application_auth_type"));
		}
		
		sql ="select source,secret from cc_third_organ_info where channel='"+Constant.XMDWM_CHANNEL+"' limit 1";
		List<JSONObject> xmdwm_list=this.dao.query4Json(tenantId, sql);
		if(xmdwm_list.size()>0){
			json_object=xmdwm_list.get(0);
			result.put("source_xmdwm", json_object.optString("source"));
			result.put("secret_xmdwm", json_object.optString("secret"));
		}
		//新美大验券
		sql ="select source,secret from cc_third_organ_info where channel='"+Constant.XMD_CHANNEL+"' limit 1";
		List<JSONObject> xmd_list=this.dao.query4Json(tenantId, sql);
		if(xmd_list.size()>0){
			json_object=xmd_list.get(0);
			result.put("source_xmd", json_object.optString("source"));
			result.put("secret_xmd", json_object.optString("secret"));
		}
	
	   return result;
	}


    @Override
    public Boolean saveXmdAccountInfo(String tenantId, JSONObject obj) throws Exception
    {
        Boolean flag = true;
//    	不需要在这里配置
//        StringBuilder sql=new StringBuilder();
//        sql.append("INSERT INTO cc_third_organ_info (\n" +
//                "	tenant_id,\n" +
//                "	shop_id,\n" +
//                "	\"name\",\n" +
//                "	\"source\",\n" +
//                "	secret,\n" +
//                "	channel,\n" +
//                "	push_state\n" +
//                ") SELECT\n" +
//                "	tenancy_id tenant_id,\n" +
//                "	\"id\" shop_id,\n" +
//                "	org_full_name \"name\",\n" +
//                "	'"+XmdHelper.DEVELOPER_ID+"' \"source\",\n" +
//                "	'"+XmdHelper.SIGN_KEY+"' secret,\n" +
//                "	'"+Constant.XMD_CHANNEL+"' channel,\n" +
//                "	'0' push_state\n" +
//                "FROM\n" +
//                "	organ t\n" +
//                "WHERE\n" +
//                "  t.id not in (select shop_id from cc_third_organ_info where channel='"+Constant.XMD_CHANNEL+"');");
//        this.dao.execute(tenantId, sql.toString());
        return true;
    }

	@Override
	public Boolean saveAccountInfo(String tenantId, JSONObject obj) throws Exception
	{
		Boolean flag = true;

		String sql="update cc_third_organ_info set source='"+obj.optString("source_bd").trim()+"',secret='"+obj.optString("secret_bd").trim()+"' where channel='BD06' ";
		this.dao.execute(tenantId, sql);
		sql="update cc_third_organ_info set source='"+obj.optString("source_mt").trim()+"',secret='"+obj.optString("secret_mt").trim()+"' where channel='MT08' ";
		this.dao.execute(tenantId, sql);
		sql="update cc_third_organ_info set source='"+obj.optString("source_ele_add").trim()+"',secret='"+obj.optString("secret_ele_add").trim()+"' where channel='EL09' ";
		this.dao.execute(tenantId, sql);
		//2017-5-18饿了么2.0版本帐号密钥
		sql="update cc_third_organ_info set source2='"+obj.optString("source_ele_add2").trim()+"',secret2='"+obj.optString("secret_ele_add2").trim()+"',application_auth_type='"+obj.optString("application_auth_type").trim()+"' where channel='EL09' ";
		this.dao.execute(tenantId, sql);
		
		//2017-11-10 新美大外卖
		sql="update cc_third_organ_info set source='"+obj.optString("source_xmdwm").trim()+"',secret='"+obj.optString("secret_xmdwm").trim()+"' where channel='"+Constant.XMDWM_CHANNEL+"' ";
		this.dao.execute(tenantId, sql);
		//2017-11-15 清除XMD Sign 标记信息
		XmdWMUtils.clearXMDSign(tenantId);
		
		//2018-1-5 配置新美大验证券
		sql="update cc_third_organ_info set source='"+obj.optString("source_xmd").trim()+"',secret='"+obj.optString("secret_xmd").trim()+"' where channel='"+Constant.XMD_CHANNEL+"' ";
		this.dao.execute(tenantId, sql);
//		//2018-1-5  清除XMD Sign 标记信息
//		SignHolder.cleanSign(XmdHelper.CC_XMD_TUANGOU_KEY_PRE+"_"+Constant.XMD_CHANNEL+"_"+tenantId);
		
		List<String> add_update_third_organ_list = new ArrayList<String>();
		if(!"".equals(obj.optString("source_bd"))&&!"".equals(obj.optString("secret_bd"))){
			sql ="select id,org_full_name from organ where org_type='3' and id not in(select shop_id from cc_third_organ_info where channel='BD06')";
			List<JSONObject> bd_list =this.dao.query4Json(tenantId, sql);

			if(bd_list.size()>0){
				for(JSONObject bd_obj:bd_list){
					String add__sql="";
					add__sql = "insert into cc_third_organ_info(tenant_id,shop_id,name,source,secret,channel,push_state) values ('"+tenantId+"','"+bd_obj.optInt("id")+"','"+bd_obj.optString("org_full_name")+"','"+obj.optString("source_bd").trim()+"','"+obj.optString("secret_bd").trim()+"','BD06','0')";
					add_update_third_organ_list.add(add__sql);
				}
			};
		}
		if(!"".equals(obj.optString("source_mt"))&&!"".equals(obj.optString("secret_mt"))){
			sql ="select id ,org_full_name from organ where org_type='3' and id not in(select shop_id from cc_third_organ_info where channel='MT08')";
			List<JSONObject> mt_list =this.dao.query4Json(tenantId, sql);
			if(mt_list.size()>0){
				for(JSONObject mt_obj:mt_list){
					String add__sql="";
					add__sql = "insert into cc_third_organ_info(tenant_id,shop_id,name,source,secret,channel,push_state) values ('"+tenantId+"','"+mt_obj.optInt("id")+"','"+mt_obj.optString("org_full_name")+"','"+obj.optString("source_mt").trim()+"','"+obj.optString("secret_mt").trim()+"','MT08','0')";
					add_update_third_organ_list.add(add__sql);
				}
			};
		}
		//at 20170706 合并条件，解决新加机构，生成两条的bug
		if((!"".equals(obj.optString("source_ele_add2"))&&!"".equals(obj.optString("source_ele_add2")))||(!"".equals(obj.optString("source_ele_add"))&&!"".equals(obj.optString("secret_ele_add")))){
			sql ="select id ,org_full_name from organ where org_type='3' and id not in(select shop_id from cc_third_organ_info where channel='EL09')";
			List<JSONObject> ele_list =this.dao.query4Json(tenantId, sql);
			if(ele_list.size()>0){
				for(JSONObject ele_obj:ele_list){
					String add__sql="";
					add__sql = "insert into cc_third_organ_info(tenant_id,shop_id,name,source,secret,source2,secret2,channel,push_state,application_auth_type) values ('"+tenantId+"','"+ele_obj.optInt("id")+"','"+ele_obj.optString("org_full_name")+"','"+obj.optString("source_ele_add").trim()+"','"+obj.optString("secret_ele_add").trim()+"','"+obj.optString("source_ele_add2").trim()+"','"+obj.optString("secret_ele_add2").trim()+"','EL09','0','"+obj.optString("application_auth_type").trim()+"')";
					add_update_third_organ_list.add(add__sql);
				}
			};
		}
		//2017-11-09 添加第三方机构（新美大外卖渠道）数据
		if(!"".equals(obj.optString("source_xmdwm"))&&!"".equals(obj.optString("secret_xmdwm"))){
			sql ="select id,org_full_name from organ where org_type='3' and id not in(select shop_id from cc_third_organ_info where channel='"+Constant.XMDWM_CHANNEL+"')";
			List<JSONObject> list =this.dao.query4Json(tenantId, sql);

			if(list.size()>0){
				for(JSONObject bd_obj:list){
					String add__sql="";
					add__sql = "insert into cc_third_organ_info(tenant_id,shop_id,name,source,secret,channel,push_state) values ('"+tenantId+"','"+bd_obj.optInt("id")+"','"+bd_obj.optString("org_full_name")+"','"+obj.optString("source_xmdwm").trim()+"','"+obj.optString("secret_xmdwm").trim()+"','"+Constant.XMDWM_CHANNEL+"','0')";
					add_update_third_organ_list.add(add__sql);
				}
			};
		}
		
		//2018-1-5 添加第三方机构（新美大团购渠道）数据
				if(!"".equals(obj.optString("source_xmd"))&&!"".equals(obj.optString("secret_xmd"))){
					sql ="select id,org_full_name from organ where org_type='3' and id not in(select shop_id from cc_third_organ_info where channel='"+Constant.XMD_CHANNEL+"')";
					List<JSONObject> list =this.dao.query4Json(tenantId, sql);

					if(list.size()>0){
						for(JSONObject bd_obj:list){
							String add__sql="";
							add__sql = "insert into cc_third_organ_info(tenant_id,shop_id,name,source,secret,channel,push_state) values ('"+tenantId+"','"+bd_obj.optInt("id")+"','"+bd_obj.optString("org_full_name")+"','"+obj.optString("source_xmd").trim()+"','"+obj.optString("secret_xmd").trim()+"','"+Constant.XMD_CHANNEL+"','0')";
							add_update_third_organ_list.add(add__sql);
						}
					};
				}
				

		if(add_update_third_organ_list.size()>0){
			String[] sql_Array = null;
			int size = add_update_third_organ_list.size();
			sql_Array = (String[]) add_update_third_organ_list.toArray(new String[size]);
			dao.getJdbcTemplate(tenantId).batchUpdate(sql_Array);
		}
//		return flag;
		
		//add by qinhulin on 2016.8.26
		StringBuilder sqlBind=new StringBuilder();
		String sqlTemp="UPDATE cc_third_organ_info SET source = '#source#', secret = '#secret#', third_shop_id = '#third_shop_id#', push_state = '#push_state#' WHERE shop_id = '#shop_id#' and tenant_id ='#tenant_id#' and channel='#channel#'; INSERT INTO cc_third_organ_info (shop_id, source, secret, third_shop_id,tenant_id,channel,push_state) SELECT '#shop_id#', '#source#', '#secret#', '#third_shop_id#','#tenant_id#','#channel#','#push_state#' WHERE NOT EXISTS (SELECT 1 FROM cc_third_organ_info WHERE shop_id = '#shop_id#' and tenant_id='#tenant_id#' and channel='#channel#');";
		//百度
		ThirdPartyManager manager=new BaiduManager(tenantId);
		JSONObject result=manager.getBindShopList(obj.optString("source_bd").trim(),obj.optString("secret_bd").trim());
		logger.info("[百度批量获取商户列表,返回信息:]"+result);
		if(null!=result&&ThirdPartyManager.SUCCESS==result.optInt("errno")){
			JSONArray shopArrays=result.optJSONArray("data");
			for(Object o:shopArrays){
				
				JSONObject shop=JSONObject.fromObject(o);
				
				try
				{
					String[] ids=shop.optString("shop_id").split("@");
					
					String shopId=ids[0];
					String tenId=ids[1];
					
					if(tenId.equals(tenantId)){
						sqlBind.append(sqlTemp.replaceAll("#shop_id#", Integer.valueOf(shopId).toString())
						.replaceAll("#tenant_id#", tenantId)
						.replaceAll("#channel#", "BD06")
						.replaceAll("#source#",  obj.optString("source_bd").trim())
						.replaceAll("#secret#", obj.optString("secret_bd").trim())
						.replaceAll("#third_shop_id#", shop.optString("baidu_shop_id"))
						.replaceAll("#push_state#", "1"));
						}
					
				}
				catch (Exception e)
				{
					logger.info(shop.optString("shop_id")+"已绑定的商户ID不符合规范[正确格式:shop_id@tenant_id]");
					Logger.getLogger(this.getClass()).warn(shop.optString("shop_id")+"已绑定的商户ID不符合规范[正确格式:shop_id@tenant_id]");
					continue;
				}
			}
		}
		// 美团
		manager=new MeiTuanManager(tenantId);
		result=manager.getBindShopList(obj.optString("source_mt").trim(),obj.optString("secret_mt").trim());
		logger.info("[美团批量获取商户列表,返回信息:]"+result);
		if(null!=result&&ThirdPartyManager.SUCCESS==result.optInt("errno")){
			JSONArray shopArrays=result.optJSONArray("data");
			for(Object o:shopArrays){
				
				try
				{
					String thirdShopId=o.toString();
					String[] ids=thirdShopId.split("@");
					
					String shopId=ids[0];
					String tenId=ids[1];
					
					if(tenId.equals(tenantId)){
						sqlBind.append(sqlTemp.replaceAll("#shop_id#", Integer.valueOf(shopId).toString())
						.replaceAll("#tenant_id#", tenantId)
						.replaceAll("#channel#", "MT08")
						.replaceAll("#source#",  obj.optString("source_mt").trim())
						.replaceAll("#secret#", obj.optString("secret_mt").trim())
						.replaceAll("#third_shop_id#", "")
						.replaceAll("#push_state#", "1"));
					}	
				}
				catch (Exception e)
				{
					logger.info("已绑定的商户ID不符合规范[正确格式:shop_id@tenant_id]");
					Logger.getLogger(this.getClass()).warn("已绑定的商户ID不符合规范[正确格式:shop_id@tenant_id]");
					continue;
				}
			}
		}
	/*  //饿了么
		JSONObject response=new JSONObject();
		manager=new EleManager(tenantId);
		List<JSONObject> lists=getLocalShopIDs(tenantId);
		JSONArray eleShopArrays=new JSONArray();
		JSONObject elObj=new JSONObject();
		elObj.put("source", obj.optString("source_ele_add").trim());
		elObj.put("secret", obj.optString("secret_ele_add").trim());
		for(JSONObject object:lists){
			obj.put("shop_id", object.optString("id"));
			elObj.put("shop_id", object.optString("id"));
			response=getShopSynchronizationInfo(manager, elObj,tenantId);
			logger.info("[饿了么获取已推送商户,返回信息:]"+response);
			if(response.optString("error").equals("Restaurant "+elObj.optString("shop_id")+"@"+tenantId+" not found")){
				continue;
			}
			if(response.optString("message").equals("ok")){
				JSONObject eleObject=new JSONObject();
				eleObject.put("shopId", object.optString("id"));
				eleObject.put("third_shop_id", response.optJSONObject("data").optString("restaurant_id"));
				eleShopArrays.add(eleObject);
			}
		}
		for(int i=0;i<eleShopArrays.size();i++){
			
			try
			{
				
				JSONObject ele=eleShopArrays.getJSONObject(i);
					sqlBind.append(sqlTemp.replaceAll("#shop_id#", Integer.valueOf(ele.optInt("shopId")).toString())
					.replaceAll("#tenant_id#", tenantId)
					.replaceAll("#channel#", "EL09")
					.replaceAll("#source#",  obj.optString("source_ele_add").trim())
					.replaceAll("#secret#", obj.optString("secret_ele_add").trim())
					.replaceAll("#third_shop_id#", ele.optString("third_shop_id"))
					.replaceAll("#push_state#", "1"));
			}
			catch (Exception e)
			{
				logger.info("已绑定的商户ID不符合规范[正确格式:shop_id@tenant_id]");
				Logger.getLogger(this.getClass()).warn("已绑定的商户ID不符合规范[正确格式:shop_id@tenant_id]");
				continue;
			}
		}*/
		
		
		
		dao.execute(tenantId, sqlBind.toString());
		saveRedisSignObject(tenantId);
		return true;
	}
	
	private void saveRedisSignObject(String tenantId) throws Exception{
		String sql="SELECT source,secret,app_auth_token,channel,shop_id FROM cc_third_organ_info";
		List<JSONObject> list=dao.query4Json(tenantId, sql);
		
		if(!list.isEmpty()){
			for(JSONObject object:list){
				String key=object.optString("shop_id")+"@"+object.optString("channel")+"@"+tenantId;
				JSONObject o=new JSONObject();
				String source=object.optString("source");
				String secret=object.optString("secret");
	            String appAuthToken=object.optString("app_auth_token");
	            o.put("source", source);
	            o.put("secret", secret);
	            o.put("app_auth_token", appAuthToken);
				redis.saveBykv(key, o.toString(), 0);
			}
			
		}
		
	}

	private JSONObject getShopSynchronizationInfo(ThirdPartyManager manager,JSONObject elObj,String tenantId) throws Exception {
		JSONObject response=new JSONObject();
		logger.debug("开始获取饿了么同步信息日志:"+elObj.optString("shop_id")+"@"+tenantId);
		response=manager.bindRestaurantId(elObj, "get");
		logger.debug("获取饿了么同步信息返回信息:"+elObj.optString("shop_id")+"@"+tenantId+"返回结果:"+response.optString("message"));
		int count=0;
		if(!response.optString("error").equals("Restaurant "+elObj.optString("shop_id")+"@"+tenantId+" not found")){
		while(!response.optString("message").equals("ok")){
			try {
				response=manager.bindRestaurantId(elObj,"get");
				System.out.println(response.optString("message").equals("Restaurant "+elObj.optString("shop_id")+"@"+tenantId+" not found"));
			} catch (Exception e1) {
				e1.printStackTrace();
			}
			if (!response.optString("message").equals("ok")||!response.optString("message").contains("Restaurant "+elObj.optString("shop_id")+"@"+tenantId+" not found")) {
				try {
					count++;
					logger.debug("获取饿了么同步信息日志:"+elObj.optString("shop_id")+"@"+tenantId+"失败，重新获取"+response.optString("message"));
					Thread.sleep(2000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
			if (count > 2) {
				break;
			}
		}
		}
		return response;
	}

	private List<JSONObject> getLocalShopIDs(String tenantId) throws Exception {
		String sql="SELECT DISTINCT \"id\" FROM organ WHERE org_type='3' AND tenancy_id='"+tenantId+"'";
		List<JSONObject> list=this.dao.query4Json(tenantId, sql);
		return list;
	}

	@Override
	public JSONObject updateDishState(String tenantId, JSONObject params) throws Exception
	{
		String channel = params.optString("channel");
		String shopId = params.optString("shop_id");

		switch (channel)
		{
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId, shopId);
				break;
			default:
				break;
		}
		return thirdPartyManager.updateDishState(params);
	}
	
	@Override
	public JSONObject deleteDishCategory(String tenantId, JSONObject params) throws Exception
	{
		String channel = params.optString("channel");
		String shopId = params.optString("shop_id");

		switch (channel)
		{
			case Constant.YICHI_CHANNEL:
				thirdPartyManager = new YichiManager(tenantId, shopId);
				break;
			default:
				break;
		}

		return thirdPartyManager.deleteDishCategory(params);
	}
	
	@Override
	public String getEleRestaurantIDs(String tenantId, JSONObject params)
	{
		String channel = params.optString("channel");
		String shopId = params.optString("store_id");

		switch (channel)
		{
			case Constant.ELE_CHANNEL:
				if(params.optString("version").equals("2.0")){
					thirdPartyManager = new EleMeManager(tenantId, shopId);
				}else{
					thirdPartyManager = new EleManager(tenantId, shopId);
				}
				params.put("tenantId", tenantId);
				break;
			default:
				break;
		}
		JSONObject dataObj=null;
		JSONArray restaurantObj=null;
		JSONObject returnObject=null;
		try{
		returnObject=thirdPartyManager.getRestaurantOwn(params);
		if(returnObject.optJSONObject("data")!=null||returnObject.optJSONArray("data").size()>0){
		if(params.optString("version").equals("2.0")){
			restaurantObj=returnObject.optJSONArray("data");
		}else{
			dataObj=returnObject.optJSONObject("data");
			restaurantObj=dataObj.optJSONArray("restaurants");
		}
			List<JSONObject> list=new ArrayList<JSONObject>();
			for(int i=0;i<restaurantObj.size();i++){
				JSONObject resultObj=new JSONObject();
				if(params.optString("version").equals("2.0")){
					resultObj.put("id", JSONObject.fromObject(restaurantObj.get(i)).optString("id"));
					resultObj.put("text", JSONObject.fromObject(restaurantObj.get(i)).optString("id"));
				}else{
					resultObj.put("id", restaurantObj.get(i));
					resultObj.put("text", restaurantObj.get(i).toString());
				}
				list.add(resultObj);
			}
			return  com.tzx.framework.common.util.JsonUtils.list2json(list);
		}else{
			return returnObject.toString();
		}
		}catch(Exception e){
			e.printStackTrace();
		}
		return returnObject.toString();
	}

    @Override
    public JSONObject saveXmdCallBackToken(JSONObject params) throws Exception {
        logger.info("新美大商户绑定回调："+params);
        JSONObject result=new JSONObject();
        try {
        	String ePoiId = params.optString("ePoiId");
        	String appAuthToken = params.optString("appAuthToken");
        	String businessId = params.optString("businessId");
        	if(ePoiId.isEmpty() || appAuthToken.isEmpty()){
        		throw new Exception("绑定回调接收参数为空");
        	}
            String []ids = ePoiId.split("@");
            String tenantId = ids[1];
            String shopId = ids[0];
            logger.info("绑定回调类型是businessId:"+businessId);
            if("1".equals(businessId)){
				logger.info("绑定团购[bind tuangou]");
				String saveSql = ("UPDATE cc_third_organ_info\n" +
						"SET app_auth_token = '" + appAuthToken + "',\n" +
						" shop_state = '"+ Constant.XMD_SHOP_STATE_BIND +"'\n" +
						"WHERE\n" +
						"	shop_id = " + shopId + "\n" +
						"AND channel = '"+Constant.XMD_CHANNEL+"'\n" +
						"AND tenant_id = '" + tenantId + "';");

				DBContextHolder.setTenancyid(tenantId);
				dao.execute(tenantId, saveSql);
			}
            result.put("data","success");
            //清除过期缓存的Sign
            if(!"2".equalsIgnoreCase(businessId)){
            	SignHolder.cleanSign(shopId+"@"+Constant.XMD_CHANNEL+"@"+tenantId);
            }else{
            	//添加新美大外卖商店解绑回调函数处理逻辑
				logger.info("绑定外卖[bind waimai]");
            	XmdWMUtils.bindShop(tenantId,shopId,appAuthToken);
            	//获取新美大商户信息保存到本地
            	//initXmdWMShopInfo(tenantId,shopId,appAuthToken);
            }
        } catch (Exception e) {
            result.put("data","error");
            result.put("msg",e.getMessage());
            logger.error("新美大商户绑定回调异常：",e);
        }
        logger.info("新美大商户绑定回调返回："+result);
        return result;
    }
    
    /**
     * 新美大外卖初始化 商户信息
     * @param appAuthToken 
     * @param shopId 
     * @param tenantId 
     */
    @Override
    public JSONObject initShopInfoFromXMD(JSONObject obj) {
    	JSONObject result = new JSONObject();
		Map<String, String> param = new HashMap<>();
		String tenantId = obj.optString("tenantId");
		String shopId = obj.optString("shop_id");
    	String ePoiId = shopId+"@"+tenantId;
    	param.put("ePoiIds", ePoiId);
    	GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
    	
    	try {
    		
    		String xmdResult = XmdWMUtils.execCmd(tenantId, shopId, XmdWMUtils.CMD_SHOP_GETINFO, param,XmdWMUtils.HTTP_REQUEST_GET);
    		JSONObject json = JSONObject.fromObject(xmdResult);
    		if (json.containsKey("error")) {
    			logger.info("获取商铺[tenantID:"+tenantId+" shopID:"+shopId+"]信息失败,reason:"+json);
    			return result;
    		}
    		
    		JSONArray arr =  json.optJSONArray("data");
    		if(arr!=null && arr.size()>0){
    			Iterator<JSONObject> iterator = arr.iterator();
    			while(iterator.hasNext()){
    				JSONObject shop = iterator.next();
    				String update = "update cc_third_organ_info set "
									//+ "address='"+ shop.optString("address")+"',"
									//+ "org_full_name='"+ shop.optString("name")+"',"
									+ "category1='"+ shop.optString("tagName")+"',"
									//+ "latitude='"+ shop.optString("latitude")+"',"
									//+ "longitude='"+ shop.optString("longitude")+"',"
									+ "phone='"+ shop.optString("phone")+"',"
									+ "shop_state='"+ shop.optString("isOnline")+"',"
									+ "open_level='"+ shop.optString("isOpen")+"',"
									+ "business_time_format='"+ shop.optString("openTime")+"',"
									+ "delivery_fee="+ shop.optString("shippingFee")+","
									+ "invoice_support='"+ shop.optString("invoiceSupport")+"',"
									+ "min_invoice_fee='"+ shop.optString("invoiceMinPrice")+"',"
									+ "invoice_description='"+ shop.optString("invoiceDescription")+"',"
									+ "pre_book='"+ shop.optString("preBook")+"',"
									+ "time_select='"+ shop.optString("timeSelect")+"'" 								
									+ " where channel='"+Constant.XMDWM_CHANNEL+"' and shop_id=" + shopId
									+ " and tenant_id='" + tenantId + "'";
    				dao.execute(tenantId,update);
    			}
    		}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("获取新美大 商铺信息失败",e);
		}
    	return result;
	}

	@Override
    public JSONObject saveXmdReleaseCallBack(JSONObject params) throws Exception {
        logger.info("新美大商户解除绑定回调："+params);
        JSONObject result=new JSONObject();
        try {
        	String developerId = params.optString("developerId");
        	String epoiId = params.optString("epoiId");
        	String businessId = params.optString("businessId");
        	if(developerId.isEmpty() || epoiId.isEmpty()){
        		throw new Exception("解除绑定回调接收参数为空");
        	}
            String []ids = epoiId.split("@");
            String tenantId = ids[1];
            String shopId = ids[0];
			logger.info("解绑回调类型是businessId:"+businessId);
			if("1".equals(businessId)) {
				logger.info("解绑团购");
				String saveSql = ("UPDATE cc_third_organ_info\n" +
						"SET app_auth_token = null,\n" +
						" shop_state = '" + Constant.XMD_SHOP_STATE_RELEASEBIND + "'\n" +
						"WHERE\n" +
						"	shop_id = " + shopId + "\n" +
						"AND channel = '" + Constant.XMD_CHANNEL + "'\n" +
						"AND tenant_id = '" + tenantId + "';");
				DBContextHolder.setTenancyid(tenantId);
				dao.execute(tenantId, saveSql);
			}
            result.put("data","success");
            //清除过期缓存的Sign
            if(!"2".equalsIgnoreCase(businessId)){
            	SignHolder.cleanSign(shopId+"@"+Constant.XMD_CHANNEL+"@"+tenantId);
            }else{
				logger.info("解绑外卖");
            	XmdWMUtils.unBindShop(tenantId,shopId);
            }
        } catch (Exception e) {
            result.put("data","error");
            result.put("msg",e.getMessage());
            logger.error("新美大商户解除绑定回调异常：", e);
        }
        logger.info("新美大商户解除绑定回调返回："+result);
        return result;
    }
    
    @Override
    public JSONObject couponQueryById(String tenantId,String storeId, JSONObject params) throws Exception {
    	Sign sign = SignHolder.getShopSign(tenantId,storeId, Constant.XMD_CHANNEL);
        String appAuthToken = sign.getAppAuthToken();
        if (!StringUtils.isNotBlankOrEmpty(appAuthToken)) {
            throw new Exception("未找到appAuthToken,请确认商户是否正确绑定！");
        }
        String couponCode = params.optString("couponCode");
        Map<String, String> businessParams = new HashMap<>();
        businessParams.put("couponCode", couponCode);
        //传业务参数
        JSONObject result = XmdHelper.coupon(XmdHelper.TUANGOU_COUPON_QUERY_BY_ID, sign.getSecret(), appAuthToken, businessParams);
        return result;
    }

    @Override
    public JSONObject couponQueryListByDate(String tenantId, String storeId, JSONObject params) throws Exception {
    	Sign sign = SignHolder.getShopSign(tenantId, storeId,Constant.XMD_CHANNEL);
        String appAuthToken = sign.getAppAuthToken();
        if (!StringUtils.isNotBlankOrEmpty(appAuthToken)) {
            throw new Exception("未找到appAuthToken,请确认商户是否正确绑定！");
        }
        String date = params.optString("date");
        String offset = params.optString("offset");
        String limit = params.optString("limit");
        Map<String, String> businessParams = new HashMap<>();
        businessParams.put("date", date);
        businessParams.put("offset", offset);
        businessParams.put("limit", limit);
        JSONObject result = XmdHelper.coupon(XmdHelper.TUANGOU_COUPON_QUERY_LIST_BY_DATE, sign.getSecret(), appAuthToken, params);
        return result;
    }

    @Override
    public JSONObject couponPrepare(String tenantId, String storeId,JSONObject params) throws Exception {
    	Sign sign = SignHolder.getShopSign(tenantId,storeId, Constant.XMD_CHANNEL);
        String appAuthToken = sign.getAppAuthToken();
        if (!StringUtils.isNotBlankOrEmpty(appAuthToken)) {
            throw new Exception("未找到appAuthToken,请确认商户是否正确绑定！");
        }
        String couponCode = params.optString("couponCode");
        Map<String, String> businessParams = new HashMap<>();
        businessParams.put("couponCode", couponCode);
      //传业务参数
        JSONObject result = XmdHelper.coupon(XmdHelper.TUANGOU_COUPON_PREPARE, sign.getSecret(), appAuthToken, businessParams);
        JSONObject data=result.optJSONObject("data");
        if(null!=data) {
           JSONObject coupon=new JSONObject();
            coupon.put("tenancy_id",tenantId);
            coupon.put("store_id",storeId);
            coupon.put("coupon_code",couponCode);
            coupon.put("count",data.opt("count"));
            coupon.put("coupon_buy_price",data.opt("couponBuyPrice"));
            coupon.put("coupon_end_time",data.opt("couponEndTime"));
            coupon.put("deal_begin_time",data.opt("dealBeginTime"));
            coupon.put("deal_id",data.opt("dealId"));
            coupon.put("deal_price",data.opt("dealPrice"));
            coupon.put("deal_title",data.opt("dealTitle"));
            coupon.put("deal_value",data.opt("dealValue"));
            coupon.put("min_consume",data.opt("minConsume"));
            coupon.put("channel",Constant.XMD_CHANNEL);
            coupon.put("status",Constant.COUPON_STATUS_PREPAE);
            coupon.put("prepare_time", CommonUtil.getNowTimeString("yyyy-MM-dd HH:mm:ss"));

            try {
                this.dao.execute(tenantId,CommonUtil.insertJSONParamsToSql("cc_third_coupon",coupon).toString());
            } catch (Exception e) {
                e.printStackTrace();
                logger.warn("[THIRD_COUPONS]劵码（"+couponCode+"）已验证或保存失败!\n"+e.getMessage());
            }
        }
        return result;
    }

    @Override
    public JSONObject couponConsume(String tenantId, String storeId,JSONObject params) throws Exception {
        Sign sign = SignHolder.getShopSign(tenantId,  storeId,Constant.XMD_CHANNEL);
        //查询机构名称
        String query_store_info_sql="select * from organ where id="+storeId+"";
        List<JSONObject> storList=this.dao.query4Json(tenantId, query_store_info_sql);

        String appAuthToken = sign.getAppAuthToken();
        if (!StringUtils.isNotBlankOrEmpty(appAuthToken)) {
            throw new Exception("未找到appAuthToken,请确认商户是否正确绑定！");
        }
        String couponCode = params.optString("couponCode");
        String count = params.optString("count");
//        String eId = params.optString("eId");
        String eId =storeId+"@"+tenantId;
//        String eName = params.optString("eName");
        String eName =storList.get(0).optString("org_full_name");
        String eOrderId = params.optString("eOrderId");
        Map<String, String> businessParams = new HashMap<>();
        businessParams.put("couponCode", couponCode);
        businessParams.put("count", count);
        businessParams.put("eId",eId);
        businessParams.put("eName",eName);
        businessParams.put("eOrderId", eOrderId);
        JSONObject result = XmdHelper.coupon(XmdHelper.TUANGOU_COUPON_CONSUME, sign.getSecret(), appAuthToken, businessParams);

        JSONObject data=result.optJSONObject("data");
        if(null!=data) {
            String sql="UPDATE cc_third_coupon\n" +
                    "SET coupon_codes = '"+CommonUtil.jsonArrayToString(data.optJSONArray("couponCodes"))+"',\n" +
                    " consume_time = '"+CommonUtil.getNowTimeString(null)+"',\n" +
                    " poi_id = '"+data.opt("poiid")+"',\n" +
                    " consume_count = "+count+",\n" +
                    " status = '"+Constant.COUPON_STATUS_CONSUME+"',\n" +
                    " e_id = '"+storeId+"@"+tenantId+"',\n" +
                    " e_name = '"+eName+"',\n" +
                    " e_order_id = '"+eOrderId+"'\n" +
                    "WHERE\n" +
                    "\tcoupon_code = '"+couponCode+"'\n" +
                    "AND tenancy_id = '"+tenantId+"'\n" +
                    "AND store_id = "+storeId+"\n" +
                    "AND channel = '"+Constant.XMD_CHANNEL+"';";
            try {
                this.dao.execute(tenantId,sql);
            } catch (Exception e) {
                e.printStackTrace();
                logger.warn("[THIRD_COUPONS]劵码（"+couponCode+"）验劵信息保存失败!\n"+e.getMessage());
            }
        }

        return result;
    }
     @Override
     public JSONObject couponCancel(String tenantId, String storeId,JSONObject params) throws Exception {
    	 Sign sign = SignHolder.getShopSign(tenantId,storeId, Constant.XMD_CHANNEL);
        String appAuthToken = sign.getAppAuthToken();
        if (!StringUtils.isNotBlankOrEmpty(appAuthToken)) {
            throw new Exception("未找到appAuthToken,请确认商户是否正确绑定！");
        }
        //查询机构名称
        String query_store_info_sql="select * from organ where id="+storeId+"";
        List<JSONObject> storList=this.dao.query4Json(tenantId, query_store_info_sql);
        String couponCode = params.optString("couponCode");
        String type = params.optString("type");
        String eId = params.optString("eId");
        String eName = params.optString("eName");
        Map<String, String> businessParams = new HashMap<>();
        businessParams.put("couponCode", couponCode);
        businessParams.put("type", type);
        businessParams.put("eId",storeId+"@"+tenantId);
        businessParams.put("eName", storList.get(0).optString("org_full_name"));
        JSONObject result = XmdHelper.coupon(XmdHelper.TUANGOU_COUPON_CANCEL, sign.getSecret(), appAuthToken, businessParams);

         JSONObject data=result.optJSONObject("data");
         if("1".equals(type)&&null!=data) {
             String sql="UPDATE cc_third_coupon\n" +
                     "SET cancel_time = '"+CommonUtil.getNowTimeString(null)+"',\n" +
                     " status = '"+Constant.COUPON_STATUS_CANCEL+"'\n" +
                     "WHERE\n" +
                     "\tcoupon_code = '"+couponCode+"'\n" +
                     "AND tenancy_id = '"+tenantId+"'\n" +
                     "AND store_id = "+storeId+"\n" +
                     "AND channel = '"+Constant.XMD_CHANNEL+"';";
             try {
                 this.dao.execute(tenantId,sql);
             } catch (Exception e) {
                 e.printStackTrace();
                 logger.warn("[THIRD_COUPONS]劵码（"+couponCode+"）取消信息保存失败!\n"+e.getMessage());
             }
         }
        return result;
    }
     
     @Override
     public JSONObject couponTradeDetail(String tenantId, String storeId,JSONObject params) throws Exception {
    	 Sign sign = SignHolder.getShopSign(tenantId, storeId,Constant.XMD_CHANNEL);
        String appAuthToken = sign.getAppAuthToken();
        if (!StringUtils.isNotBlankOrEmpty(appAuthToken)) {
            throw new Exception("未找到appAuthToken,请确认商户是否正确绑定！");
        }
        //查询机构名称
//        String query_store_info_sql="select * from organ where id="+storeId+"";
//        List<JSONObject> storList=this.dao.query4Json(tenantId, query_store_info_sql);
        String couponCode = params.optString("couponCode");
//        String type = params.optString("type");
//        String eId = params.optString("eId");
//        String eName = params.optString("eName");
        Map<String, String> businessParams = new HashMap<>();
        businessParams.put("couponCode", couponCode);
//        businessParams.put("type", type);
//        businessParams.put("eId",storeId+"@"+tenantId);
//        businessParams.put("eName", storList.get(0).optString("org_full_name"));
        JSONObject result = XmdHelper.coupon(XmdHelper.TUANGOU_COUPON_QUERY_TRADE_DETAIL, sign.getSecret(), appAuthToken, businessParams);
         JSONObject data= result.optJSONArray("data").getJSONObject(0);
         if(null!=data) {
//        	 券面值
        	 BigDecimal  dealValue=new  BigDecimal(data.optDouble("dealValue"));
//        	 券购买价
        	 BigDecimal  couponBuyPrice=new  BigDecimal(data.optDouble("couponBuyPrice"));
//        	 结算价
        	 BigDecimal  buyPrice=new  BigDecimal(data.optDouble("buyPrice"));
        	 BigDecimal  bizCost=new  BigDecimal(data.optDouble("bizCost"));
        	 
        	 //优惠  优惠=dealValue(券面值)-couponBuyPrice(券购买价)
        	 BigDecimal cost=dealValue.subtract(couponBuyPrice);
        	 
//        	 服务费
        	 BigDecimal serviceFee=couponBuyPrice.subtract(buyPrice);
        	 
//        	 due(净收) = dealValue(券面值)-(优惠+bizCost(促销金额))-服务费
        	 BigDecimal netRevenue=dealValue.subtract(  cost.add( bizCost )   ).subtract( serviceFee  );
        	 
        	 netRevenue=netRevenue.setScale(2, BigDecimal.ROUND_HALF_UP);
        	 // 服务费
        	 serviceFee=serviceFee.setScale(2, BigDecimal.ROUND_HALF_UP);
//        	 服务费
        	 cost=cost.setScale(2, BigDecimal.ROUND_HALF_UP);
        	 
        	 //修改返回数据
        	 JSONObject data2=new JSONObject();
        	 //用户支付
        	 data2.put("userPay", data.optDouble("couponBuyPrice"));
        	 //净收
        	 data2.put("netRevenue", netRevenue.doubleValue());
        	 //优惠
        	 data2.put("cost", cost.doubleValue());
        	 //第三方服务费
        	 data2.put("thirdCost", 0);
        	 //服务费
        	 data2.put("serviceFee", serviceFee.doubleValue());
        	 //
        	 data2.put("couponCode", couponCode);
        	 
        	 result.put("data", data2);
         }
        return result;
    }
     //算法测试
     public static void main(String[] args) {
    	 String str="{\"data\": [{\"bizCost\": 0.0,\"buyPrice\": 0.01,\"couponBuyPrice\": 69.0,\"couponCode\": \"274004910361\",\"dealId\": 48940110,\"dealValue\": 100.0,\"due\": 0.01,\"orderId\": 4159923732,\"useTime\": 1515645792}]}";
    	 JSONObject result= JSONObject.fromObject(str);
    	 JSONObject data= result.optJSONArray("data").getJSONObject(0);
//        	 券面值
        	 BigDecimal  dealValue=new  BigDecimal(data.optDouble("dealValue"));
//        	 券购买价
        	 BigDecimal  couponBuyPrice=new  BigDecimal(data.optDouble("couponBuyPrice"));
//        	 结算价
        	 BigDecimal  buyPrice=new  BigDecimal(data.optDouble("buyPrice"));
        	 BigDecimal  bizCost=new  BigDecimal(data.optDouble("bizCost"));
        	 
        	 //优惠  优惠=dealValue(券面值)-couponBuyPrice(券购买价)
        	 BigDecimal cost=dealValue.subtract(couponBuyPrice);
//        	 服务费
        	 BigDecimal serviceFee=couponBuyPrice.subtract(buyPrice);
        	 
//        	 due(净收) = dealValue(券面值)-(优惠+bizCost(促销金额))-服务费
        	 BigDecimal netRevenue=dealValue.subtract(  cost.add( bizCost )   ).subtract( serviceFee  );
        	 
        	 //修改返回数据
        	 JSONObject data2=new JSONObject();
        	 //用户支付
        	 data2.put("userPay", data.optDouble("couponBuyPrice"));
        	 //净收
        	 data2.put("netRevenue", netRevenue.doubleValue());
        	 //优惠
        	 data2.put("cost", cost.doubleValue());
        	 //第三方服务费
        	 data2.put("thirdCost", 0);
        	 //服务费
        	 data2.put("serviceFee", serviceFee.doubleValue());
        	 System.out.println(data2.toString());
	}
     

	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.bo.ShopService#validAdd(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public List<JSONObject> getThrirdShopInfo(String tenantId,JSONObject params) throws Exception {
		StringBuffer sql = new StringBuffer();
		sql.append("select * from cc_third_organ_info where 1=1 ");
		if(params.containsKey("channel")&&StringUtils.isNotBlankOrEmpty(params.optString("channel"))){
			sql.append(" and channel='"+params.optString("channel")+"' ");
		}
		if(params.containsKey("store_id")&&StringUtils.isNotBlankOrEmpty(params.optString("store_id"))){
			sql.append(" and shop_id='"+params.optString("store_id")+"' ");
		}
		List<JSONObject> list = dao.query4Json(tenantId, sql.toString());
		return list;
	}

	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.bo.ShopService#query4suweiNoCanT(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public List<JSONObject> query4suweiNoCanT(String tenantId, JSONObject param)
			throws Exception {
		String organs = param.optString("organs");
		String channel = param.optString("channel");
		if(org.apache.commons.lang.StringUtils.isBlank(organs)) {
			return new ArrayList<JSONObject>();
		}
		organs = organs.replaceAll(";", ",");
		StringBuffer sql = new StringBuffer();
		sql.append("select suwei.id,organ.id as shop_id,organ.org_full_name as name from  organ");
		sql.append(" left join cc_third_organ_info suwei  on organ.id = suwei.shop_id and suwei.channel = '").append(channel).append("'");
		sql.append(" where  suwei.suwei_organ_id is null and organ.id in (");
		sql.append(organs);
		sql.append(")");
		return dao.query4Json(tenantId, sql.toString());
	}

	@Override
	public JSONObject shopBind(String tenantId, JSONObject params)throws Exception {
		String shopId = params.optString("shop_id");
		JSONObject result=new JSONObject();
		if(params.optString("version").equals("2.0")){
			thirdPartyManager = new EleMeManager(tenantId, shopId);
			result = thirdPartyManager.bindRestaurantId(params, "post"); 
		}else{
			thirdPartyManager=new EleManager(tenantId, shopId);
			result=thirdPartyManager.bindRestaurantId(params, "post");
		}
		this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);
		return result;
	}

	@Override
	public JSONObject shopUnBind(String tenantId, JSONObject params)throws Exception {
		String shopId = params.optString("shop_id");
		JSONObject result=new JSONObject();
		try {
			ElmUtils.unbundShopToken(tenantId, shopId,params.optString("third_shop_id"));
			result.put("errno", "0");
		} catch (BusinessException e) {
			result.put("errno", e.getCode());
			result.put("error", e.getMessage());
			e.printStackTrace();
		}
		return result;
	}
	
	@Override
	public JSONObject platformSaaSDisheIsMapped(String tenantId,
			JSONObject params) throws Exception {

		List<JSONObject> list = null;
		JSONObject result = new JSONObject();
		String channel = params.optString("channel");
		if (!params.containsKey("dishes"))
		{
			try
			{
				boolean dish_is_map_flag=false;
				//先判断菜品类别信息是否已经映射完毕 先不处理
				//循环选择的门店 
				String[] store_id_array=params.optString("shop_id").split(",");
				for(String store_id:store_id_array){
					JSONObject dish_class_query_obj=new JSONObject();
					dish_class_query_obj.put("shop_id", store_id);
					dish_class_query_obj.put("channel", params.optString("channel"));
					//根据门店查询已经绑定的分类
					List<JSONObject> dish_class_is_map_list= getDishClassIsMapList(tenantId, dish_class_query_obj);
					for(JSONObject dish_class_is_map_obj:dish_class_is_map_list){
						JSONObject dish_obj=new JSONObject();
						dish_obj.put("shop_id", store_id);	
						dish_obj.put("third_class_id", dish_class_is_map_obj.optString("third_class_id"));	
						dish_obj.put("channel", params.optString("channel"));
						//循环已经绑定的分类 查询本地菜品是否已经绑定完毕
						List<JSONObject> dish_is_map_list= getDishIsMapList(tenantId, dish_obj);
						//查询平台的菜品
						String	sql="select source,secret,third_shop_id,shop_id from cc_third_organ_info where channel='"+channel+"' and shop_id in("+dish_obj.optString("shop_id")+")";
						JSONObject source_secret_obj=this.dao.query4Json(tenantId, sql).get(0);//查询账户密钥
						source_secret_obj.put("third_class_id", dish_class_is_map_obj.optString("third_class_id"));	
						JSONObject dataResponse=getPlatDishInfos(params,source_secret_obj);//获取平台数据
						JSONArray plat_foods_array=dataResponse.getJSONObject("data").getJSONArray("foods");
						logger.info("比较获取饿了么平台菜品数量："+plat_foods_array.size());
						if(plat_foods_array.size()>dish_is_map_list.size()){
							dish_is_map_flag=true;
						
						}
						if(!dish_is_map_flag){
							break;
						}
						}
					}
				result.put("dish_is_map_flag", dish_is_map_flag);
				return result;
				}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		else
		{
			list = (List<JSONObject>) GsonUtil.toT(params.opt("dishes").toString(), new TypeToken<List<JSONObject>>()
			{
			}.getType());
		}
		if (list.size() > 0)
		{}
		return result;
	}

	@Override
	public JSONObject saveDishCategoryAutomaticMap(String tenantId,JSONObject params) throws Exception {
		String channel = params.optString("channel");
		String sql="";
		switch (channel)
		{
			case Constant.ELE_CHANNEL:
				sql="select source,secret,third_shop_id,shop_id from cc_third_organ_info where channel='"+channel+"' and shop_id in("+params.optString("shop_id")+")";
				thirdPartyManager=new EleManager(tenantId);
				break;
			default:
				break;
		}
		Set<JSONObject> thirdShopInfoList=getThirdShopIDs(this.dao.query4Json(tenantId, sql));//查本地数据
		Map<String,JSONArray> dataMap=new HashMap<String,JSONArray>();
		for(JSONObject thirdShopInfo:thirdShopInfoList){
			JSONObject dataResponse=getPlatDishCategoryInfos(params,thirdShopInfo);//获取平台数据
			dataMap.put(thirdShopInfo.optString("shop_id"), dataResponse.getJSONObject("data").getJSONArray("food_categories"));
		}
		return saveAutomaticData(tenantId,params,dataMap);//处理数据
	}

	public JSONObject getPlatDishCategoryInfos(JSONObject params, JSONObject thirdShopInfo)throws Exception {
		params.put("restaurant_id", thirdShopInfo.get("third_shop_id"));
		params.put("source", thirdShopInfo.get("source"));
		params.put("secret", thirdShopInfo.get("secret"));
		return thirdPartyManager.getFoodCategories(params);
	}
	
	
	public JSONObject getPlatDishInfos(JSONObject params, JSONObject thirdShopInfo)throws Exception {
		params.put("food_category_id", thirdShopInfo.get("third_class_id"));
		params.put("source", thirdShopInfo.get("source"));
		params.put("secret", thirdShopInfo.get("secret"));
		return thirdPartyManager.getFoods(params);
	}

	private JSONObject saveAutomaticData(String tenantId,JSONObject params,Map<String, JSONArray> dataMap) throws Exception {
		JSONObject result=new JSONObject();
		String[] shopIDs=params.optString("shop_id").split(",");
		Set<String> dishCategoryMsg=new HashSet<String>();
		List<JSONObject> dishCategorys=new ArrayList<JSONObject>();
		for(int i=0;i<shopIDs.length;i++){
			List<JSONObject> localDishCategorys=thirdPartyManager.getLocalNoPageDishCategoryList(tenantId, params);//this.dao.query4Json(tenantId, sql);
			List<JSONObject> localDishCategoryNames=new ArrayList<JSONObject>();
			List<JSONObject> platDishCategoryObjs=new ArrayList<JSONObject>();
			List<String> localThirdlassIDs=new ArrayList<String>();
			Map<String,JSONObject> updateMap=new HashMap<String, JSONObject>();
			Map<String,String> paltMap=new HashMap<String, String>();
			String organName="";
			if(localDishCategorys.size()>0){
				organName=localDishCategorys.get(0).optString("org_full_name");
			for(JSONObject dishCategoryName:localDishCategorys){
				JSONObject locDishCategory=new JSONObject();
				String className="";
				if(CommonUtil.checkStringIsNotEmpty(dishCategoryName.optString("cur_class_name"))){
					className=dishCategoryName.optString("cur_class_name");
				}else{
					className=dishCategoryName.optString("last_send_class_name");
				}
				locDishCategory.put("name", className);
				JSONObject organ=new JSONObject();
				organ.put("org_full_name", dishCategoryName.optString("org_full_name"));
				organ.put("third_class_id", dishCategoryName.optString("third_class_id"));
				organ.put("id", dishCategoryName.optString("id"));
				organ.put("class_id", dishCategoryName.optString("class_id"));
				updateMap.put(className,organ);
				localThirdlassIDs.add(dishCategoryName.optString("third_class_id"));
				localDishCategoryNames.add(locDishCategory);
			}
			}
			if(dataMap.size()>0){
				JSONArray platDishCategorys=dataMap.get(shopIDs[i]);//平台数据
				List<JSONObject> platResultDishCategoryObjs=new ArrayList<JSONObject>();
				if(platDishCategorys!=null){
					for(int j=0;j<platDishCategorys.size();j++){//平台菜品名称
						JSONObject platDishCategory=platDishCategorys.getJSONObject(j);
						JSONObject tmpPlatDishCategory=new JSONObject();
						tmpPlatDishCategory.put("name", platDishCategory.optString("name"));
						paltMap.put(platDishCategory.optString("name"), platDishCategory.optString("food_category_id"));
						platDishCategoryObjs.add(tmpPlatDishCategory);
						platResultDishCategoryObjs.add(tmpPlatDishCategory);
					}
						
				}
				
				if(platDishCategoryObjs.size()>0){
					platDishCategoryObjs.retainAll(localDishCategoryNames);//返回相同部分	
					for(JSONObject locaObj:platDishCategoryObjs){//相同部分做映射
						JSONObject locaDishCateID=updateMap.get(locaObj.opt("name"));
						String third_class_id=paltMap.get(locaObj.optString("name"));
						String modifySql="";
						if(locaDishCateID!=null&&CommonUtil.checkStringIsNotEmpty(locaDishCateID.optString("id"))){
							modifySql="UPDATE cc_third_item_class_info SET third_class_id='"+third_class_id+"' WHERE id='"+locaDishCateID.optString("id")+"'";	
						}else if(CommonUtil.checkStringIsNotEmpty(third_class_id)){
							modifySql="INSERT INTO cc_third_item_class_info(shop_id,item_class_id,last_send_class_name,rank,channel,tenancy_id,third_class_id) VALUES('"+shopIDs[i]+"','"+locaDishCateID.optString("class_id")+"','"+locaObj.opt("name")+"','1','EL09','"+tenantId+"','"+third_class_id+"')";
						}
						this.dao.execute(tenantId, modifySql);
					}
					platResultDishCategoryObjs.removeAll(platDishCategoryObjs);//删除做完映射的相同部分
					if(platResultDishCategoryObjs.size()>0){
						for(JSONObject resultData:platResultDishCategoryObjs){
							JSONObject dishCategory=new JSONObject();
							if(!localThirdlassIDs.isEmpty()&&CommonUtil.checkStringIsNotEmpty(paltMap.get(resultData.optString("name")))){
								if(!localThirdlassIDs.contains(paltMap.get(resultData.optString("name")))){//判断是否是已经做过映射菜品类别在本地修改了菜品名称
									dishCategoryMsg.add(organName+":"+resultData.optString("name"));
									dishCategory.put("id", paltMap.get(resultData.optString("name")));
									dishCategory.put("text", resultData.optString("name"));
									dishCategorys.add(dishCategory);
								}	
							}
						}	
					}
				}
				
			}
			
		}

		if(dishCategoryMsg.size()>0){
			if(params.optBoolean("binding")){
//				result.put("errno", "0");
				result.put("data",dishCategorys.toArray());
			}else{
				result.put("msg", "请先将下列菜品分类做映射:"+dishCategoryMsg);
			}
		}else{
			result.put("errno", "0");
			result.put("error", "success");	
		}
		return result;
	}

	private Set<JSONObject> getThirdShopIDs(List<JSONObject> list) {
		Set<JSONObject> results=new HashSet<JSONObject>();
		for(JSONObject obje:list){
			results.add(obje);
		}
		return results;
	}

	@Override
	public JSONObject saveDishAutomaticMap(String tenantId, JSONObject params)
			throws Exception {

		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		String channel = params.optString("channel");
		if (!params.containsKey("dishes"))
		{
			try
			{
				//boolean dish_class_is_map_flag=true;
				//先判断菜品类别信息是否已经映射完毕 先不处理
				//循环选择的门店 
				String[] store_id_array=params.optString("shop_id").split(",");
				String	sql="select source,secret,third_shop_id,shop_id from cc_third_organ_info where channel='"+channel+"' and shop_id in("+store_id_array[0]+")";
				JSONObject source_secret_obj=this.dao.query4Json(tenantId, sql).get(0);//查询账户密钥
				for(String store_id:store_id_array){//循环门店
					JSONObject dish_class_query_obj=new JSONObject();
					dish_class_query_obj.put("shop_id", store_id);
					dish_class_query_obj.put("channel", params.optString("channel"));
					//根据门店查询已经绑定的分类
					List<JSONObject> dish_class_is_map_list= getDishClassIsMapList(tenantId, dish_class_query_obj);
					for(JSONObject dish_class_is_map_obj:dish_class_is_map_list){
						JSONObject dish_obj=new JSONObject();
						dish_obj.put("shop_id", store_id);	
						dish_obj.put("third_class_id", dish_class_is_map_obj.optString("third_class_id"));	
						dish_obj.put("channel", params.optString("channel"));
						//循环已经绑定的分类 查询本地菜品是否已经绑定完毕
						List<JSONObject> dish_is_map_list= getDishIsMapList(tenantId, dish_obj);
						Map<String,String> dish_is_map_list_map=new HashMap<String,String>();
						for(JSONObject dish_is_map_obj:dish_is_map_list ){
							dish_is_map_list_map.put(dish_is_map_obj.optString("third_item_id"), dish_is_map_obj.optString("id"));
						}
						//查询平台的菜品
						source_secret_obj.put("third_class_id", dish_class_is_map_obj.optString("third_class_id"));	
						JSONObject dataResponse=getPlatDishInfos(params,source_secret_obj);//获取平台数据
						List<JSONObject> plat_dish_list=JSONArray.toList(dataResponse.getJSONObject("data").getJSONArray("foods"), JSONObject.class);
						logger.info("自动映射获取饿了么平台菜品数量："+plat_dish_list.size());
						/*if(plat_dish_list.size()>dish_is_map_list.size()){
							dish_class_is_map_flag=false;
						
						}*/
						//if(!dish_class_is_map_flag){
							String third_item_ids="";
							//开始菜品自动映射
							//去除已经映射过的菜品
							
							for(int i=0;i<plat_dish_list.size();i++){
								if(org.apache.commons.lang.StringUtils.isNotEmpty(dish_is_map_list_map.get(plat_dish_list.get(i).optString("food_id")))){
									plat_dish_list.remove(i);
									i--;
								}else{
									third_item_ids += "'"+plat_dish_list.get(i).optString("food_id")+"'"+",";									
								}
							}
							//平台待映射的菜品信息
							Map<String,String> plat_dish_no_map_list_map=new HashMap<String,String>();
							
							for(JSONObject plat_dish_obj:plat_dish_list){
							
								plat_dish_no_map_list_map.put(plat_dish_obj.optString("food_name"), plat_dish_obj.optString("food_id"));
							}
							//查询没有做过映射的菜品信息
							JSONObject dish_no_map_obj_query=new JSONObject();
							dish_no_map_obj_query.put("shop_id", store_id);
							dish_no_map_obj_query.put("channel", channel);
							if(third_item_ids.length()>0){
								dish_no_map_obj_query.put("third_item_ids", third_item_ids.substring(0, third_item_ids.length()-1));
							}
							JSONObject dish_no_map_obj_result=thirdPartyManager.getLocalDishList(dish_no_map_obj_query);
							if(dish_no_map_obj_result.optJSONArray("rows")!=null){
								List<JSONObject> dish_no_map_list=dish_no_map_obj_result.optJSONArray("rows");
								for(JSONObject dish_no_map_obj:dish_no_map_list){
									if(org.apache.commons.lang.StringUtils.isNotEmpty(plat_dish_no_map_list_map.get(dish_no_map_obj.optString("item_name")))){
										dish_no_map_obj.put("min_order_num", 1);
										dish_no_map_obj.put("package_box_num", 1);
										dish_no_map_obj.put("available_times_start", "10:00");
										dish_no_map_obj.put("available_times_end", "22:00");
										dish_no_map_obj.put("whether_push_over", "1");
										dish_no_map_obj.put("item_code", dish_no_map_obj.optString("item_id"));
										dish_no_map_obj.put("third_item_id",plat_dish_no_map_list_map.get(dish_no_map_obj.optString("item_name")));
										if(dish_no_map_obj.containsKey("id")&&org.apache.commons.lang.StringUtils.isNotEmpty(dish_no_map_obj.optString("id"))&&!"null".equals(dish_no_map_obj.optString("id"))){
											this.dao.updateIgnorCase(tenantId, "cc_third_item_info", dish_no_map_obj);
										}else{
											this.dao.insertIgnorCase(tenantId, "cc_third_item_info", dish_no_map_obj);
										
										}
								
									}
								}
						
							}
						
							
						//}
						}
					}
				}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		else
		{
			list = (List<JSONObject>) GsonUtil.toT(params.opt("dishes").toString(), new TypeToken<List<JSONObject>>()
			{
			}.getType());
		}

		result.put("errno", "0");
		result.put("error", "success");	
		if (list.size() > 0)
		{}
		return result;
	}

	@Override
	public JSONObject getPlatNoMapDishList(JSONObject params) throws Exception {
		JSONObject result=new JSONObject();
		String	sql="select source,secret,third_shop_id,shop_id from cc_third_organ_info where channel='"+params.optString("channel")+"' and shop_id in("+params.optString("shop_id")+")";
		JSONObject source_secret_obj=this.dao.query4Json(params.optString("tenancy_id"), sql).get(0);//查询账户密钥
		source_secret_obj.put("third_class_id", params.optString("third_class_id"));	
		JSONObject dataResponse=getPlatDishInfos(params,source_secret_obj);//获取平台数据
		List<JSONObject> plat_dish_list=JSONArray.toList(dataResponse.getJSONObject("data").getJSONArray("foods"), JSONObject.class);
		logger.info("手动映射获取饿了么平台菜品数量："+plat_dish_list.size());
		JSONObject dish_obj=new JSONObject();
		dish_obj.put("shop_id", params.optString("shop_id"));	
		dish_obj.put("third_class_id", params.optString("third_class_id"));	
		dish_obj.put("channel", params.optString("channel"));
		//循环已经绑定的分类 查询本地菜品是否已经绑定完毕
		List<JSONObject> dish_is_map_list= getDishIsMapList(params.optString("tenancy_id"), dish_obj);
		Map<String,String> dish_is_map_list_map=new HashMap<String,String>();
		for(JSONObject dish_is_map_obj:dish_is_map_list ){
			dish_is_map_list_map.put(dish_is_map_obj.optString("third_item_id"), dish_is_map_obj.optString("id"));
		}
		//去除已经映射过的菜品
			for(int i=0;i<plat_dish_list.size();i++){
				if(org.apache.commons.lang.StringUtils.isNotEmpty(dish_is_map_list_map.get(plat_dish_list.get(i).optString("food_id")))){
					plat_dish_list.remove(i);
					i--;
				}
			}
			result.put("data", plat_dish_list);
			return result;
	}

	@Override
	public JSONObject saveDishCategoryThirdClassID(String tenantId,
			JSONObject params) throws Exception {
		String modifySql="";
		if(CommonUtil.checkStringIsNotEmpty(params.optString("id"))){
			modifySql="UPDATE cc_third_item_class_info SET third_class_id='"+params.optString("handwork")+"' WHERE id='"+params.optString("id")+"'";	
		}else if(CommonUtil.checkStringIsNotEmpty(params.optString("handwork"))){
			modifySql="INSERT INTO cc_third_item_class_info(shop_id,item_class_id,last_send_class_name,rank,channel,tenancy_id,third_class_id) VALUES('"+params.optString("store_id")+"','"+params.optString("class_id")+"','"+params.optString("cur_class_name")+"','1','EL09','"+tenantId+"','"+params.optString("handwork")+"')";	
		}
		
		JSONObject result=new JSONObject();
		try {
			this.dao.execute(tenantId, modifySql);
			result.put("errno", "0");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
	
	@Override
	public JSONObject saveDishThirdItemId(String tenantId,
			JSONObject params) throws Exception {
		JSONObject result=new JSONObject();
		String sql="";
		if(params.containsKey("id")&&org.apache.commons.lang.StringUtils.isNotEmpty(params.optString("id"))){
			sql="UPDATE cc_third_item_info SET third_item_id='"+params.optString("handwork")+"' WHERE id='"+params.optString("id")+"'";
			try {
				this.dao.execute(tenantId, sql);
				result.put("errno", "0");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			try{
			params.put("min_order_num", 1);
			params.put("package_box_num", 1);
			params.put("available_times_start", "10:00");
			params.put("available_times_end", "22:00");
			params.put("whether_push_over", "1");
			params.put("item_code", params.optString("item_id"));
			params.put("third_item_id",params.optString("handwork"));
			Object dic=this.dao.insertIgnorCase(tenantId, "cc_third_item_info", params);
			if (dic != null){
				result.put("errno", "0");
			}
			}
			catch (Exception e)
			{
				result.put("errno", "1");
				e.printStackTrace();
			}
		}
		
		return result;
	}

	@Override
	public JSONObject postOrderModel(String tenantId, JSONObject param)
			throws Exception {
		thirdPartyManager=new EleManager(tenantId);
		JSONObject result=thirdPartyManager.postOrderModelStatus(param, "put");
		String sql="update cc_third_organ_info set order_mode='"+param.optString("order_mode")+"' where id='"+param.optString("id")+"'";
		this.dao.execute(tenantId, sql);
		return result;
	}

	@Override
	public JSONObject saveOrUpdateDishInfo(String tenantId, JSONObject params)
			throws Exception {
		List<JSONObject> listParam = (List<JSONObject>) GsonUtil.toT(params.opt("dishes").toString(), new TypeToken<List<JSONObject>>(){}.getType());
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		String send_operator = params.optString("send_operator");
		String send_time = params.optString("send_time");
		String dish_available_time = params.optString("dish_available_time");
		thirdClassIDtranslateToString(params);
		thirdItemIDtranslateToString(params);
		for(JSONObject jsonObj:listParam){
			if(("1").equals(params.optString("time_type"))){
				jsonObj.put("dish_available_time",dish_available_time);
			}else{
				if(CommonUtil.checkStringIsNotEmpty(dish_available_time)){
					jsonObj.put("dish_available_time", dish_available_time);
				}
			}
			list.add(jsonObj);
		}
			
		if (list.size() > 0)
		{
			String channel = params.optString("channel");
			switch (channel)
			{
				case Constant.BAIDU_CHANNEL:
					thirdPartyManager = new BaiduManager(tenantId);
					for (JSONObject dish : list)
					{
					if ("".equals(dish.optString("photo1")))
					{
						dish.put("photo1", dish.optString("wxxt"));
					}
					if(params.containsKey("package_box_num")){
						dish.put("package_box_num", params.optString("package_box_num"));
					}
					if (params.containsKey("available_times_start")&&CommonUtil.checkStringIsNotEmpty(params.optString("available_times_start")))
					{
						dish.put("available_times_start", params.optString("available_times_start"));
					}

					if (params.containsKey("available_times_end")&&CommonUtil.checkStringIsNotEmpty(params.optString("available_times_end")))
					{
						dish.put("available_times_end", params.optString("available_times_end"));
					}
					if (dish.optInt("min_order_num")==0)
					{
						dish.put("min_order_num", "1");
					}
					if ("".equals(dish.optString("channel")))
					{
						dish.put("channel", "BD06");
					}

					if ("".equals(dish.optString("send_operator")))
					{
						dish.put("send_operator", send_operator);
					}

					if ("".equals(dish.optString("send_time")))
					{
						dish.put("send_time", send_time);
					}
					dish.put("store_id", dish.optInt("store_id"));
					dish.put("item_code", dish.optInt("item_code"));
					}
					result = thirdPartyManager.saveOrUpdateDishInfo(list);
					break;
				case Constant.MEITUAN_CHANNEL:
					thirdPartyManager = new MeiTuanManager(tenantId);
					for (JSONObject dish : list)
					{
						dish.put("shop_id", dish.optInt("store_id"));
						dish.put("item_code", dish.optInt("item_code"));
						dish.put("channel", "MT08");
						if (params.containsKey("package_box_num") && CommonUtil.checkStringIsNotEmpty(params.optString("package_box_num")))
						{
							dish.put("package_box_num", params.optString("package_box_num"));
						}
						if (params.containsKey("available_times_start")&&CommonUtil.checkStringIsNotEmpty(params.optString("available_times_start")))
						{
							dish.put("available_times_start", params.optString("available_times_start"));
						}
						if (params.containsKey("available_times_end")&&CommonUtil.checkStringIsNotEmpty(params.optString("available_times_end")))
						{
							dish.put("available_times_end", params.optString("available_times_end"));
						}
						if ("".equals(dish.optString("item_pic")) || "null".equals(dish.optString("item_pic")))
						{
							dish.put("item_pic", dish.optString("wxxt"));
						}
						if ("".equals(dish.optString("photo1")) || "null".equals(dish.optString("photo1")))
						{
							dish.put("photo1", dish.optString("wxxt"));
						}
						if (dish.optInt("min_order_num")==0)
						{
							dish.put("min_order_num", "1");
						}
						if ("".equals(dish.optString("box_price")) || "null".equals(dish.optString("box_price")))
						{
							dish.put("box_price", "1");
						}
						if ("".equals(dish.optString("channel")) || "null".equals(dish.optString("channel")))
						{
							dish.put("channel", "MT08");
						}
						if(!StringUtils.isNotBlankOrEmpty(dish.optString("is_charge_commission"))){
							dish.put("is_charge_commission","1");
						}
						if ("".equals(dish.optString("send_operator")) || "null".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", send_operator);
						}

						if ("".equals(dish.optString("send_time")) || "null".equals(dish.optString("send_time")))
						{
							dish.put("send_time", send_time);
						}
						if ("".equals(dish.optString("is_sold_out")) || "null".equals(dish.optString("is_sold_out")))
						{
							dish.put("is_sold_out", "0");
						}
						
						
						if ("".equals(dish.optString("priceList")))
						{
							JSONObject condition = new JSONObject();
							condition.put("item_id", dish.optInt("item_id"));
							condition.put("shop_id", dish.optInt("shop_id"));
							dish.put("priceList", thirdPartyManager.getLocalPriceSystem(condition).opt("rows"));
						}
					}
					result = thirdPartyManager.saveOrUpdateDishInfo(list);
					break;
				case Constant.ELE_CHANNEL:
					thirdPartyManager=new EleManager(tenantId);
					for (JSONObject dish : list)
					{
						dish.put("shop_id", dish.optInt("store_id"));
						dish.put("item_code", dish.optInt("item_code"));
						dish.put("channel", "EL09");
						dish.put("tenancy_id", tenantId);
						if (params.containsKey("package_box_num") && CommonUtil.checkStringIsNotEmpty(params.optString("package_box_num")))
						{
							dish.put("package_box_num", params.optString("package_box_num"));
						}
						if (params.containsKey("available_times_start")&&CommonUtil.checkStringIsNotEmpty(params.optString("available_times_start")))
						{
							dish.put("available_times_start", params.optString("available_times_start"));
						}

						if (params.containsKey("available_times_end")&&CommonUtil.checkStringIsNotEmpty(params.optString("available_times_end")))
						{
							dish.put("available_times_end", params.optString("available_times_end"));
						}
						if ("".equals(dish.optString("item_pic")) || "null".equals(dish.optString("item_pic")))
						{
							dish.put("image", dish.optString("wxxt"));
						}else{
							dish.put("image", dish.optString("item_pic"));
						}
						if ("".equals(dish.optString("photo1")) || "null".equals(dish.optString("photo1")))
						{
							dish.put("photo1", dish.optString("wxxt"));
						}
						if (dish.optInt("min_order_num")==0)
						{
							dish.put("min_order_num", "1");
						}
						if ("".equals(dish.optString("box_price")) || "null".equals(dish.optString("box_price")))
						{
							dish.put("box_price", "1");
						}
						if ("".equals(dish.optString("channel")) || "null".equals(dish.optString("channel")))
						{
							dish.put("channel", "EL09");
						}
						if ("".equals(dish.optString("send_operator")) || "null".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", send_operator);
						}

						if ("".equals(dish.optString("send_time")) || "null".equals(dish.optString("send_time")))
						{
							dish.put("send_time", send_time);
						}
						if ("".equals(dish.optString("is_sold_out")) || "null".equals(dish.optString("is_sold_out")))
						{
							dish.put("is_sold_out", "0");
						}
						if("".equals(dish.optString("stock"))||"null".equals(dish.optString("stock"))){
							dish.put("stock", "999");
						}
						if("".equals(dish.optString("max_stock"))||"null".equals(dish.optString("max_stock"))){
							dish.put("max_stock", "9999");
						}
						if("".equals(dish.optString("description"))||"null".equals(dish.optString("description"))){
							dish.put("description", dish.optString("item_name"));
						}
						if("".equals(dish.optString("is_gum"))||"null".equals(dish.optString("is_gum"))){
							dish.put("is_gum", "1");
						}
						if("".equals(dish.optString("is_new"))||"null".equals(dish.optString("is_new"))){
							dish.put("is_new", "1");
						}
						if("".equals(dish.optString("is_featured"))||"null".equals(dish.optString("is_featured"))){
							dish.put("is_featured", "1");
						}
						if("".equals(dish.optString("is_spicy"))||"null".equals(dish.optString("is_spicy"))){
							dish.put("is_spicy", "1");
						}
						if(!StringUtils.isNotBlankOrEmpty(dish.optString("is_charge_commission"))){
							dish.put("is_charge_commission","1");
						}
						JSONObject condition = new JSONObject();
						condition.put("item_id", dish.optInt("item_id"));
						condition.put("shop_id", dish.optInt("shop_id"));
						String boxPrice=Double.toString(thirdPartyManager.getLocalPriceSystem(condition).optDouble("box_price",0.0)*dish.optInt("package_box_num",1));
						dish.put("box_price", boxPrice);
					}
					result=thirdPartyManager.saveOrUpdateDishInfo(list);
					break;
				case Constant.XMDWM_CHANNEL:
					thirdPartyManager = new XinMeiDaManager(tenantId);
					for (JSONObject dish : list)
					{
						dish.put("shop_id", dish.optInt("store_id"));
						dish.put("item_code", dish.optInt("item_code"));
						dish.put("channel", Constant.XMDWM_CHANNEL);
						if (params.containsKey("package_box_num") && CommonUtil.checkStringIsNotEmpty(params.optString("package_box_num")))
						{
							dish.put("package_box_num", params.optString("package_box_num"));
						}
						if (params.containsKey("available_times_start")&&CommonUtil.checkStringIsNotEmpty(params.optString("available_times_start")))
						{
							dish.put("available_times_start", params.optString("available_times_start"));
						}
						if (params.containsKey("available_times_end")&&CommonUtil.checkStringIsNotEmpty(params.optString("available_times_end")))
						{
							dish.put("available_times_end", params.optString("available_times_end"));
						}
						if ("".equals(dish.optString("item_pic")) || "null".equals(dish.optString("item_pic")))
						{
							dish.put("item_pic", dish.optString("wxxt"));
						}
						if ("".equals(dish.optString("photo1")) || "null".equals(dish.optString("photo1")))
						{
							dish.put("photo1", dish.optString("wxxt"));
						}
						if (dish.optInt("min_order_num")==0)
						{
							dish.put("min_order_num", "1");
						}
						if ("".equals(dish.optString("box_price")) || "null".equals(dish.optString("box_price")))
						{
							dish.put("box_price", "1");
						}
						if ("".equals(dish.optString("channel")) || "null".equals(dish.optString("channel")))
						{
							dish.put("channel", Constant.XMDWM_CHANNEL);
						}
						if(!StringUtils.isNotBlankOrEmpty(dish.optString("is_charge_commission"))){
							dish.put("is_charge_commission","1");
						}
						if ("".equals(dish.optString("send_operator")) || "null".equals(dish.optString("send_operator")))
						{
							dish.put("send_operator", send_operator);
						}

						if ("".equals(dish.optString("send_time")) || "null".equals(dish.optString("send_time")))
						{
							dish.put("send_time", send_time);
						}
						if ("".equals(dish.optString("is_sold_out")) || "null".equals(dish.optString("is_sold_out")))
						{
							dish.put("is_sold_out", "0");
						}
						
						
						if ("".equals(dish.optString("priceList")))
						{
							JSONObject condition = new JSONObject();
							condition.put("item_id", dish.optInt("item_id"));
							condition.put("shop_id", dish.optInt("shop_id"));
							dish.put("priceList", thirdPartyManager.getLocalPriceSystem(condition).opt("rows"));
						}
					}
					result = thirdPartyManager.saveOrUpdateDishInfo(list);
					break;
				default:
					break;
			}
		}
		return result;
	}

	@Override
	public JSONObject validateThirdShopIDisEffective(String tenantId,JSONObject param) {
		JSONObject result=new JSONObject();
		String sql="select name,shop_id,third_shop_id from cc_third_organ_info where channel='"+param.optString("channel")+"' and third_shop_id='"+param.optString("third_shop_id")+"'";
		try{
		List<JSONObject> restaurantIDs=this.dao.query4Json(tenantId, sql);
		Map<String,String> map=new HashMap<String,String>();
		if(restaurantIDs.size()>0){
			for(JSONObject obj:restaurantIDs){
				String shopID=obj.optString("shop_id");
				if(!shopID.equals(param.optString("shop_id"))){
					map.put(obj.optString("shop_id"), obj.optString("name"));
				}
			}
		}
		if(map.values().size()>0){
			result.put("errno", "-1");
			result.put("error", map.values());	
		}else{
			result.put("errno", "0");
		}
		
		}catch(Exception e){
		result.put("errno", "-1");
		result.put("error", e.getMessage());
			e.printStackTrace();
		}
		return result;
	}
	
	private Object gegLock(){
		return null;
	}
	/**
	 * 定时抓取店铺评论记录(美团，饿了么平台的数据)
	 *  需要在spring-task 中进行配置执行
	 * @param tenantId
	 */
	public void lockGrabMeituanComments(){
		logger.info("李先生抓取第三方评论数据开始!");
		// 商户ID 需要 修改
		String tenantId = "waimai";
		// 间隔分钟数，外部获取
		int min = Integer.parseInt(com.tzx.framework.common.constant.Constant.systemMap.get("comment.task.min"));
		int expireTime = Integer.parseInt(com.tzx.framework.common.constant.Constant.systemMap.get("comment.task.expiretime"));
		if (lock == null)
			lock = new RedisLock(redisTemplate, "GET_COMMENT_TASK", 1, expireTime * 60 * 1000);
		try {
			if (lock.lock()) {
				String sql = "select a.id,c.channel,c.third_shop_id  from organ a left join cc_third_organ_info c on a.id=c.shop_id where a.org_type='3' and a.valid_state='1' and c.channel=";
				//切换数据源
				DBContextHolder.setTenancyid(tenantId);
				// 美团店铺
				SqlRowSet meituanRs = this.dao.query(tenantId, sql + "'" + Constant.MEITUAN_CHANNEL + "'");
				while (meituanRs.next()) {
					Integer shop_id = meituanRs.getInt("id");
					grabMeituanComments(tenantId, String.valueOf(shop_id), min);
				}
				// 饿了么店铺评论
				SqlRowSet eleRs = this.dao.query(tenantId, sql + "'" + Constant.ELE_CHANNEL + "'");
				while (eleRs.next()) {
					Integer third_shop_id = eleRs.getInt("third_shop_id");
					Integer shop_id = eleRs.getInt("id");
					if (third_shop_id != null) {
						getEleShopComments(tenantId, Integer.toString(shop_id), Integer.toString(third_shop_id), min);
					}
				}
			}else{
				logger.info("["+Thread.currentThread().getName()+"]"+"执行抓取李先生 对应第三方平台评论,未获取到锁!");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			lock.unlock();
		}
	}
	
//	public static void main(String[] args) {
//		System.out.println(OBusyLevel.busyLevelClosed.toString()  .equals("busyLevelClosed"));
//	}
//	
	/**
	 * 获取饿了么店铺状态
	 * @param tenantId
	 * @param shopId
	 * @return
	 */
	public Integer[] grabEleStatus(String tenantId,String shopId){
//		logger.info(shopId+"[获取饿了么店铺状态]");
//		try {
//			String sql="select count(1) from cc_third_tenanttoken_info cct WHERE cct.shopid='%s' and cct.accectoken <> '' and cct.accectoken is not null and cct.channel='%s'";
//			sql=String.format(sql, new Object[]{shopId,Constant.ELE_CHANNEL});
//			//查询配置
//			long l=this.dao.countSql(tenantId, sql);
//			if(l==0)
//				return -1;
//		} catch (Exception e2) {
//			// TODO Auto-generated catch block
//			e2.printStackTrace();
//			return -1;
//		}
		//返回状态
		Integer[] errRet=new Integer[] {-1,-1};
		Integer[] statusRet=new Integer[] {-1,-1};
		
		try {
			String third_shop_id=null;
			SqlRowSet eleRs =this.dao.query(tenantId, "select third_shop_id  from cc_third_organ_info where  channel='EL09' and  shop_id="+shopId);
			if(eleRs.next())
				third_shop_id=eleRs.getString("third_shop_id");
			
			eleme.openapi.sdk.api.service.ShopService shopService = new eleme.openapi.sdk.api.service.ShopService(ElmUtils.getConfig(tenantId,
					third_shop_id), ElmUtils.getToken(tenantId, shopId));
			OShop shop=shopService.getShop(Long.parseLong(third_shop_id));
			int isOpen=shop.getIsOpen();
			OBusyLevel level=shop.getBusyLevel();
			logger.info(tenantId+":"+shopId+"[店铺状态返回]: "+isOpen+"--"+level.toString());
			
			if(isOpen==0){}//休息 
				statusRet[0]=0;
			if(isOpen==1)
				statusRet[0]=1;
			
			if(OBusyLevel.busyLevelFree.equals(level)){
				statusRet[1]= 1;
			}else if( OBusyLevel.busyLevelClosed.equals(level)){
				statusRet[1]= 2;
			}else if(OBusyLevel.busyLevelHoliday.equals(level)){
				statusRet[1]= 3;
			}else if(OBusyLevel.busyLevelNetworkUnstable .equals(level)){
				statusRet[1]= 4;
			} else if(OBusyLevel.others .equals(level)){
				statusRet[1]= 5;
			} 
			return statusRet;
		} catch (NumberFormatException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (ServiceException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return errRet;
		
	}
	
	/**
	 * 百度获店铺状态
	 * @param tenantId
	 * @param shop_id
	 * @return
	 */
	public Integer[] grabBaiduStatus(String tenantId,String shop_id){
//		logger.info(shop_id+"[获取百度店铺状态]");
		String channel= Constant.BAIDU_CHANNEL;
		Integer[] errRet=new Integer[] {-1,-1};
		Integer[] statusRet=new Integer[] {-1,-1};
//		try {
//			//查询配置
//			String cSql= "SELECT count(1) FROM cc_third_organ_info  where shop_id='"+shop_id+" ' and channel='"+channel+"' and source <> '' and  source is not null and secret <> '' and  secret is not null   ";
//			long l=this.dao.countSql(tenantId,cSql);
//			if(l==0)
//				return -1;
//		} catch (Exception e2) {
//			// TODO Auto-generated catch block
//			e2.printStackTrace();
//			return -1;
//		}
		String app_poi_code =shop_id+"@"+tenantId;
		Sign sign=null;
		try {
			sign = SignHolder.getShopSign(tenantId, shop_id, channel);
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
			logger.info(shop_id+"[获取百度API配置]：失败");
			return errRet;
		}
		String appId = sign.getSource();
		String secret = sign.getSecret();
		
		BaiduManager baidu=new BaiduManager(tenantId,shop_id);
		try {
			JSONObject json=baidu.getShop(appId, secret,app_poi_code);
			
			logger.info(tenantId+":"+shop_id+"[百度店铺状态查询]: "+json.toString());
			if(json!=null){
				if( json.optInt("errno")==0&&json.optString("error","").equals("success")){
					JSONObject data=json.optJSONObject("data");
					if(data!=null){
//						JSONObject data=dataArr.getJSONObject(0);
						int status=data.optInt("status");
						if(status==9)//下线
						{
							statusRet[0]=0;
							statusRet[1]=2;
						}
						if(status==1)//营业
						{
							statusRet[0]=1;
							statusRet[1]=1;
						}
						if(status==3) //休息
						{
							statusRet[0]=1;
							statusRet[1]=2;
						}
					}
				}
			}
			
			return statusRet;
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		logger.info(shop_id+"[获取百度店铺状态]：失败");
		return errRet;
	}
	/**
	 * 美团获取店铺营业状态
	 * @param tenantId
	 * @param shop_id
	 * @return
	 */
	public Integer[] grabMeituanStatus(String tenantId,String shop_id){
//		logger.info(shop_id+"[获取美团店铺状态]");
		String channel= Constant.MEITUAN_CHANNEL;
		Integer[] errRet=new Integer[] {-1,-1};
//		try {
//			//查询配置
//			String cSql= "SELECT count(1) FROM cc_third_organ_info  where shop_id="+shop_id+"  and channel='"+channel+"' and source <> '' and  source is not null and secret <> '' and  secret is not null   ";
//			long l=this.dao.countSql(tenantId,cSql);
//			if(l==0)
//				return -1;
//		} catch (Exception e2) {
//			// TODO Auto-generated catch block
//			e2.printStackTrace();
//			return -1;
//		}
		String app_poi_code =shop_id+"@"+tenantId;
		Sign sign=null;
		try {
			sign = SignHolder.getShopSign(tenantId, shop_id, channel);
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
			logger.info(shop_id+"[获取美团API配置]：失败");
			return errRet;
		}
		String appId = sign.getSource();
		String secret = sign.getSecret();
		if(appId==null||appId.length()==0)
			return errRet;
		if(secret==null||secret.length()==0)
			return errRet;
		String cmd="poi/mget";
		JSONObject parmas=new JSONObject();
		parmas.put("app_poi_codes", app_poi_code);
		String result=null;
		try {
			result=MeiTuanHelper.sendRequest(cmd, appId, secret, parmas, "get");
			logger.info(app_poi_code+"[查询美团店铺状态] ："+result );
			JSONObject json=JSONObject.fromObject(result);
			JSONArray dataArr=json.optJSONArray("data");
			JSONObject data=dataArr.optJSONObject(0);
			if(data==null)
				return errRet;
//			JSONObject data=dataArr.getJSONObject(0);
			int open_level=   data.optInt("open_level");
			int is_online=   data.optInt("is_online");
			//返回状态
			Integer[] statusArr=new Integer[]{-1,-1};
			
			if(is_online==0){
				statusArr[0]=0;
			}else{
				statusArr[0]=1;
			}
				
			if(open_level==1){
				statusArr[1]= 1;
			}else if(open_level==3){
				statusArr[1]= 2;
			}else{
				statusArr[1]= -1;
			}
			return statusArr;
			
//			if(is_online==1){//上线
//				if(open_level==1){
//					return 1;//营业
//				}else{
//					return 2;//非营业
//				}
//			}else if(is_online==-1){//下线
//				return 0;
//			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return errRet;
		}
	
	/**
	 * 定时任务 获取美团评论数据
	 */
	public void grabMeituanComments(String tenantId,String shop_id,int min){
//		String shop_id="6";
//		String tenantId="mrtian"; 
		String channel= Constant.MEITUAN_CHANNEL;
		String tableName="cc_comments";
		
		String app_poi_code =shop_id+"@"+tenantId;
		Sign sign=null;
		try {
			sign = SignHolder.getShopSign(tenantId, shop_id, channel);
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		String appId = sign.getSource();
		if(org.apache.commons.lang.StringUtils.isEmpty(appId)){
			return;
		}
		String secret = sign.getSecret();
		
		String cmd="poi/comment/app_poi_code";
//		String appId="321";
//		String secret="01838de512c19ac1f9bcd4b4a08e911b";
		
		JSONObject parmas=new JSONObject();
//		parmas.put("app_poi_code", "6@mianxiang1");
		parmas.put("app_poi_code", app_poi_code);
//		parmas.put("start_time", "1505865600");
//		parmas.put("end_time", "1506729600");
		parmas.put("start_time", (new Date().getTime() -(min*60)*1000)/1000+"");
		parmas.put("end_time", (new Date().getTime()/1000)+"");
		parmas.put("pagesize", "20");
		
		for(int i=0;i<10000;i++){
			System.out.println("正在处理第"+(i+1)+"页");
			parmas.put("pageoffset", (i*20)+"");
			String result=null;
			try {
				result=MeiTuanHelper.sendRequest(cmd, appId, secret, parmas, "get");
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			if(StringUtils.isNotBlankOrEmpty(result)){
				JSONObject json=JSONObject.fromObject(result);
				if(json.containsKey("error")){
					logger.info("[获取店铺评论失败：]"+json);
					return;
				};
				
				JSONArray data=JSONArray.fromObject(json.optString("data"));
				
				if(data!=null){
					Iterator<JSONObject> it=data.iterator();
					List jsonList=new LinkedList();
					while(it.hasNext()){
						JSONObject obj=it.next();
						int commentId=obj.optInt("comment_id");
						//查询数据库是否已经存在了
						long len=0;
						try {
							len = this.dao.countSql(tenantId, "select id from cc_comments where type =1 And comment_id="+commentId);
						} catch (Exception e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
						if(len>0){
	//						log
							continue;
						}
						
						SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
						JSONObject jsonParam=new JSONObject();
						jsonParam.put("type", 1);
						jsonParam.put("store_comment", obj.optString("comment_content"));
						jsonParam.put("store_score", obj.optInt("order_comment_score"));
						jsonParam.put("comment_time", sdf.format(DateUtil.currentTimestamp()));
						jsonParam.put("comment_id", commentId);
						jsonParam.put("shop_id", shop_id);
						jsonParam.put("replyed", 0);
						
						jsonList.add(jsonParam);
					}
					try {
						Object[] oarr=this.dao.insertBatchIgnorCase(tenantId, tableName, jsonList);
						System.out.println(oarr.length);
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				
				//最后一页
				if(data.size()<20){
					break;
				}
			}
			
			try {
				Thread.sleep(500);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 饿了么获取商铺评论调度函数
	 * 	需要获取本地店铺资源的商铺ID先
	 */
	public void getEleShopComments(String tenantId,String shop_id,String thirdShopID,int min){
		
		String tableName="cc_comments";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");  
		EleMeManager manager=new EleMeManager(tenantId);
		
		Date startTime =null;
		Date endTime =null;
		
		int pageSize = 20;
		System.out.println("[开始获取饿了么  李先生商户评论信息]");
		for (int i = 0; i < 10000; i++) {
			System.out.println("正在处理第"+(i+1)+"页");
			List<JSONObject> result = null;
			try {
				int offset = i*20;
				endTime = DateUtil.currentTimestamp();
				startTime = new Date(endTime.getTime()-min*60*1000);
				
				//result = manager.getEleShopComments(tenantId, shop_id,thirdShopID, startTime, endTime,offset,pageSize);
				
			} catch (Exception e1) {
				e1.printStackTrace();
				return ;
			}
			// 没有数据则返回
			if (CollectionUtils.isEmpty(result)) {
				return;
			}
			List jsonList = new LinkedList<>();
			for (JSONObject param : result) {
				int commentId = param.optInt("id");

				// 查询数据库是否已经存在了
				long len = 0;
				try {
					len = this.dao.countSql(tenantId, "select id from cc_comments where type =2 And comment_id=" + commentId);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				if (len > 0) {
					// log
					continue;
				}
				JSONObject jsonParam = new JSONObject();
				jsonParam.put("type", 2);
				jsonParam.put("store_comment", param.optString("rateContent"));
				jsonParam.put("store_score", param.optInt("rating"));
				jsonParam.put("comment_time", sdf.format(param.optString("ratedAt")));
				jsonParam.put("comment_id", commentId);
				jsonParam.put("shop_id", shop_id);
				jsonParam.put("replyed", 0);

				jsonList.add(jsonParam);
			}

			try {
				Object[] oarr = this.dao.insertBatchIgnorCase(tenantId, tableName, jsonList);
				System.out.println(oarr.length);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			//最后一页
			if(result.size()<20){
				break;
			}
		}
	}
	
	/**
	 * 回复评论
	 * @param id
	 * @param content
	 * @return
	 */
	public JSONObject addReply(String tenantId,String[] idArr,String content,JSONObject param){
		//String tenantId="mrtian";
		JSONObject result=new JSONObject();
		List<JSONObject> res=null;
		StringBuffer sb = new StringBuffer();
		StringBuffer sbfail = new StringBuffer();
		int suc = 0;
		int fail = 0;
		String userName = param.getString("user");
		if(org.apache.commons.lang.StringUtils.isEmpty(userName)){
			userName="";
		}
		for(String idStr:idArr){
			try {
				res=this.dao.query4Json(tenantId,"select * from cc_comments where id=? AND (replyed = 0 OR replyed = 2)",new Object[]{Integer.parseInt(idStr)});
			} catch (Exception e) {
				e.printStackTrace();
			}
			if(res!=null&res.size()>0){
				long  comment_id=res.get(0).optLong("comment_id");
				String shop_id = res.get(0).optString("shop_id");
				String type = res.get(0).optString("type");
				String channel = "1".equals(type)?Constant.MEITUAN_CHANNEL:Constant.ELE_CHANNEL;
				if(addCommentReply(channel,tenantId, comment_id,shop_id, content)){
					suc++;
					sb.append(idStr+",");
				}else{
					fail++;
					sbfail.append(idStr+",");
				}
			}
		}
		if(sb.length()>0){
			sb.append("-1");
			
			String sql =String.format("update cc_comments set replyed=1,reply_name='%s',reply_content='%s',reply_time='%s' where id in ("+sb+")",userName, content,DateUtil.currentTimestamp());
			try {
				this.dao.execute(tenantId, sql);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		if(sbfail.length()>0){
			sbfail.append("-1");
			String sql ="update cc_comments set replyed=2,reply_name='"+userName+"' where id in ("+sbfail+")";
			try {
				this.dao.execute(tenantId, sql);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
		Map<String, Object> data = new HashMap<>();
		data.put("suc", suc);
		data.put("fail", fail);
		result.put("code", "0");
		result.put("data", data);
		return result;
	}
	
	/**
	 * 添加评论的回复
	 */
	public boolean addCommentReply(String channel,String tenantId,long commentId,String shopId,String content){
		
		boolean flag = false;

		if(channel.equals(Constant.MEITUAN_CHANNEL)){
			JSONObject params=new JSONObject();
			params.put("shop_id", shopId);
			params.put("comment_id", commentId);
			params.put("reply", content);
			try {
			 JSONObject ret= new MeiTuanManager(tenantId, shopId).addCommentReply(params);
			 if(ret.optInt("code")==0){
				 flag = true;
			 }
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("ReplyExcep type:[meituan] shop_id:["+shopId+"] comment_id:["
						+ commentId+"]\n Exception:"+e.getMessage());
			}
		}else if(channel.equals(Constant.ELE_CHANNEL)){
			try {
				flag = new EleMeManager(tenantId).replyEleShopComment(tenantId, shopId,commentId+"", content);
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("ReplyExcep type:[Ele] shop_id:["+shopId+"] comment_id:["
						+ commentId+"]\n Exception:"+e.getMessage());
			}
		}
		 return flag;
	}

	/**
	 * 定时任务 获取饿了么评论数据
	 */
	public void grabElemeComments(){
		System.out.println("grabMeituanComments...");
	}
	
	/**
	 * 获取评论统计信息
	 * @param tenantId
	 * @param timeBegin
	 * @param timeEnd
	 * @param channel
	 * @return
	 */
	public JSONObject getCommentStat(String tenantId,String timeBegin,String timeEnd,int channel,String organCode){
		JSONObject result=new JSONObject();
		long channel_0=getChannelCount(tenantId,timeBegin,timeEnd, 0,organCode);
		result.put("allChannel", channel_0);
		long channel_1=getChannelCount(tenantId,timeBegin,timeEnd, 1,organCode);
		result.put("metuan", channel_1);
		long channel_2=getChannelCount(tenantId,timeBegin,timeEnd, 2,organCode);
		result.put("eleme", channel_2);
		
		JSONObject meituan = new JSONObject();
		
		for(int i =1;i<=5;i++){
			long star= getStarCount(tenantId,timeBegin,timeEnd,1,i,organCode);
			meituan.put("star_"+i, star);
		}
		JSONObject ele = new JSONObject();
		
		for(int i =1;i<=5;i++){
			long star= getStarCount(tenantId,timeBegin,timeEnd,2,i,organCode);
			ele.put("star_"+i, star);
		}
		result.put("star_mt", meituan);
		result.put("star_ele", ele);
		return result;
	}
	/**
	 * 获取channel的评论统计
	 * @param tenantId
	 * @param timeBegin
	 * @param timeEnd
	 * @param channel
	 * @return
	 */
	private long getChannelCount(String tenantId,String timeBegin,String timeEnd,int channel,String organCode){
		String channelStr=channel==0?"1=1": (" type="+channel);
		
		String sql="";
		
		 sql="SELECT count(1) from cc_comments where  "+channelStr+"  and comment_time >= '"+timeBegin+"' and comment_time <= '"+timeEnd
				+"' and shop_id in (select a.id  from organ a where a.organ_code like '"+organCode+"%' and a.org_type='3')";
		
		try {
			return this.dao.countSql(tenantId, sql);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return 0;
		}
	}
	/**
	 * 获取打分的统计
	 * @param tenantId
	 * @param timeBegin
	 * @param timeEnd
	 * @param channel
	 * @param star
	 * @return
	 */
	private long getStarCount(String tenantId,String timeBegin,String timeEnd,int channel,int star,String organCode){
		String channelStr=channel==0?" ": (" and type="+channel);
		String sql="SELECT count(1) from cc_comments where store_score= "+star+channelStr+"  and comment_time >= '"+timeBegin+"' and comment_time <= '"+timeEnd
				+"' and shop_id in (select a.id  from organ a where a.organ_code like '"+organCode+"%' and a.org_type='3')";;
		try {
			return this.dao.countSql(tenantId, sql);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return 0;
		}
	}
	
	
	/**
	 *     获取门店的第三方店状态信息
	 * @param tenantId
	 * @param orgCode
	 * @param condition
	 * @return
	 */
	public JSONObject getThirdShop(String tenantId,JSONObject condition){
		StringBuffer sql=new StringBuffer();
		sql.append("select *  from organ ");
		sql.append(" where org_type='3' and valid_state='1' ");
		if (condition.containsKey("organ_code1") && !"".equals(condition.get("organ_code1")))
		{
			sql.append(" and organ_code like'");
			sql.append(condition.get("organ_code1").toString());
			sql.append("%'");
		}
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		try {
			logger.info(sql.toString());
			long total = this.dao.countSql(tenantId, sql.toString());

			List<JSONObject> list = this.dao.query4Json(tenantId, this.dao.buildPageSql(condition, sql.toString()));

			JSONObject result = new JSONObject();

			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
			return result;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}
	}
	/**
	 * 获取单个店铺的第三方外卖列表
	 * @param tenantId
	 * @param shopId
	 * @return
	 */
	public JSONArray getThirdShopByShopId(String tenantId,int shopId){
		JSONArray arr=new JSONArray();
		try {
			String sql="(select a.channel, a.shop_state from cc_third_organ_info a where   channel in ('MT08') and a.shop_id="+shopId+" and secret <> '' and  secret is not null    )";
			String sql2="(select a.channel, a.shop_state from cc_third_organ_info a where   channel in ('BD06') and a.shop_id="+shopId+" and secret <> '' and  secret is not null )";
			String sql3="(select a.channel, a.shop_state from cc_third_organ_info a where   channel in ('EL09') and a.shop_id=%s and   ( select count(1) from cc_third_tenanttoken_info where tenancyid='%s' and channel='EL09' and  (shopid='%s' or shopid is null ) )>0    )";
			sql3=String.format(sql3, new Object[]{shopId,tenantId,shopId});
			
			String sql4="(" +sql+" union "+sql2+ " union "+sql3 +")";
			logger.info("sql "+sql4);
			SqlRowSet rs=this.dao.query(tenantId, sql4);
			while(rs.next()){
				JSONObject obj=new JSONObject();
				obj.put("channel", rs.getString("channel"));
				obj.put("shop_state", rs.getString("shop_state"));
				arr.add(obj);
			}
			logger.info("arr: "+arr.size());
			
			
			
			
		} catch (InvalidResultSetAccessException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return arr;
	}
	/**
	 * 更新三方店铺状态
	 * @param shopId
	 * @param channel
	 * @param status
	 * @return
	 */
	public int  storeShopStatus(String tenentid,String shopId,String channel,String status){
		String sql="update cc_third_organ_info set shop_state ='%s'  where shop_id=%s and channel='%s' and is_valid='1'";
		sql=String.format(sql, new Object[]{status,shopId, channel});
		try {
			boolean flag=this.dao.execute(null, sql);
			return flag?1:0;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return 0;
	}
	
	
	
	
	
	

	@Override
	public JSONObject getCommentsList(JSONObject params) throws Exception {

		StringBuilder sql = new StringBuilder("select * from cc_comments");
		String tenantId =params.optString("tenantId");
		
		if (params.containsKey("comment_time_begin")) {
			String comment_time_begin = params.optString("comment_time_begin");
			if (org.apache.commons.lang.StringUtils.isNotBlank(comment_time_begin)) {
				sql.append(" where comment_time >= '").append(comment_time_begin).append("'");
			}
		}
		if (params.containsKey("comment_time_end")) {
			String comment_time_end = params.optString("comment_time_end");
			if (org.apache.commons.lang.StringUtils.isNotBlank(comment_time_end)) {
				sql.append(" AND comment_time <= '").append(comment_time_end).append("'");
			}
		}
		
		if (params.containsKey("replyType")) {
			String replyType = params.optString("replyType");
			if (org.apache.commons.lang.StringUtils.isNotBlank(replyType) && !"3".equals(replyType)) {
				sql.append(" AND replyed = '").append(replyType).append("'");
			}
		}
		
		//通过机构code 获取shop_id
		List<JSONObject> shoplist = null;
		if(params.containsKey("organ_code")){
			String sql_organ = "select a.id  from organ a where a.organ_code like '"+params.optString("organ_code")+"%' and a.org_type='3'";
			shoplist= this.dao.query4Json(tenantId, sql_organ);
		}
		
		
		if (CollectionUtils.isNotEmpty(shoplist)) {
			StringBuilder str = new StringBuilder("(");
			for(JSONObject it : shoplist){
				str.append("'").append(it.opt("id")).append("',");
				
			}
			str.deleteCharAt(str.lastIndexOf(","));
			str.append(")");
			sql.append(" AND shop_id in ").append(str);
		}
		
		
		StringBuilder groupby = new StringBuilder("");
		if (params.containsKey("sort1")) {
			groupby.append(" ").append(params.optString("sort1"));
			if (params.containsKey("sort1_dir")) {
				groupby.append(" ").append(params.optString("sort1_dir")).append(",");
			}else{
				groupby.append(" ASC,");
			}
		}
		
		if (params.containsKey("sort2")) {
			groupby.append(" ").append(params.optString("sort2"));
			if (params.containsKey("sort2_dir")) {
				groupby.append(" ").append(params.optString("sort2_dir")).append(",");
			}else{
				groupby.append(" ASC,");
			}
		}
		
		if (params.containsKey("sort3")) {
			groupby.append(" ").append(params.optString("sort3"));
			if (params.containsKey("sort3_dir")) {
				groupby.append(" ").append(params.optString("sort3_dir")).append(",");
			}else{
				groupby.append(" ASC,");
			}
		}
		if(groupby.length() >0){
			groupby.deleteCharAt(groupby.lastIndexOf(","));
			sql.append(" ORDER BY "+groupby.toString());
		}
		
		int pagenum = params.containsKey("page") ? (params.getInt("page") == 0 ? 1 : params.getInt("page")) : 1;
		long total = this.dao.countSql(tenantId, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenantId, this.dao.buildPageSql(params, sql.toString()));
		logger.info("[查询饿了么店铺评论信息  (返回:"+total+"条数据)]");
		JSONObject result = new JSONObject();
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}
	
	@Override
    public JSONObject updateXmdWhetherPushOver(JSONObject params, JSONArray dishList) throws Exception {
        logger.info("新美大外卖更改菜品映射信息推送状态回调函数："+params);
        JSONObject result=new JSONObject();
        CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
        try {
        	//先去判断已映射完成的菜品，在erp系统上的菜品信息和平台上的菜品信息是否一致[判断商家是否在做映射时选错菜品，如果选错菜品给予相应提示]
        	JSONArray selectMappingJsonList = selectAchieveMappingParamsCondition(params, dishList);
        	String msg = "";
        	if(selectMappingJsonList.size() > 0){
        		for (int i = 0; i < selectMappingJsonList.size(); i++)
        		{
        			JSONArray dlList = selectMappingJsonList.getJSONArray(i);
        			for(int j = 0; j < dlList.size(); j++)
            		{
        				JSONObject dl = dlList.getJSONObject(j);
        				//拼装提示消息
    	        			if(!dl.isNullObject() && !dl.isEmpty()){
    	        				boolean f = false;
    	        				if(!dl.opt("twoDishName").equals("")){
    	        					msg += dl.opt("twoDishName");
    	        					f = true;
    	        				}
    	        				
    	        				if(!dl.opt("categoryName").equals("")){
    	        					msg += dl.opt("categoryName");
    	        					f = true;
    	        				}
    	        				
    	        				if(!dl.opt("dishName").equals("")){
    	        					msg += dl.opt("dishName");
    	        					f = true;
    	        				}
    	        				
    	        				if(!dl.opt("unit").equals("")){
    	        					msg += dl.opt("unit");
    	        					f = true;
    	        				}
    	        				
    	        				if(!dl.opt("price").equals("")){
    	        					msg += dl.opt("price");
    	        					f = true;
    	        				}
    	        				
    	        				if(!dl.opt("boxPrice").equals("")){
    	        					msg += dl.opt("boxPrice");
    	        					f = true;
    	        				}
    	        				
    	        				if(f){
    	        					msg += "不一致!</br>";
    	        				}
    	        			}
    	        		}
            		}
        		}
        	if(!msg.equals("")){
        		result.put("data", "error");
                result.put("msg", msg);
        	}else{
        		result.put("data", "ok");
                result.put("msg", "");
    		}
        	//将已映射完成的菜品在erp系统中修改推送状态
        		JSONArray resDishInfoList = new JSONArray();
            	JSONArray resDishCategoryInfoList = new JSONArray();
            	//菜品完成映射操作后，根据商户ID、商家ID、渠道信息来将所有符合条件的菜品推送状态更改为未推送状态
            	JSONObject resObject = updateAllDishWhetherPushOver(params);
            	//修改成功后执行
            	if(resObject.opt("data").equals("ok")){
            		resDishInfoList = selectDishHqContainCC(params);
            		resDishCategoryInfoList = selectDishCategoryHqContainCC(params);
            		List<String> add_update_third_organ_list = new ArrayList<String>();
            		for (int i = 0; i < resDishInfoList.size(); i++)
            		{
            			JSONObject dlDishInfo = resDishInfoList.getJSONObject(i);
            			if(!dlDishInfo.isNullObject() && !dlDishInfo.isEmpty()){
            				String sql_item="";
            				//判断cc_third_item_info表中是否已经存在此菜品信息
            				if(dlDishInfo.optString("isNull").equals("0")){
            					sql_item = "UPDATE cc_third_item_info SET whether_push_over = 1 WHERE id = " + dlDishInfo.optString("id");
            				}else{
            					JSONObject paramsDishInfo = new JSONObject();
            					//查询菜品的class信息
            					String sql_class = "select DISTINCT(class) from hq_item_menu_class where details_id in (SELECT id from hq_item_menu_details where item_id = '"+ dlDishInfo.optString("eDishCode") +"') AND chanel = '"+ params.optString("channel") +"'";
            					List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(params.optString("tenantId"), sql_class);
            					if(all_third_item_to_org_list.size()>0){
                					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
                						paramsDishInfo.put("class_id",to_org_dish_obj.optString("class"));
                					}
                				}
            					paramsDishInfo.put("send_operator", "system");
            					paramsDishInfo.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
            					paramsDishInfo.put("channel", params.optString("channel"));
            					paramsDishInfo.put("shop_id", params.optString("shop_id"));
            					paramsDishInfo.put("item_id", dlDishInfo.optString("eDishCode"));
            					paramsDishInfo.put("send", "no");
            					paramsDishInfo.put("isWhether", "yes");
            					//推送此菜品信息到cc_third_item_info表，并且将状态设置为已推送状态
                				JSONObject jsonStr = saveDish(params.optString("tenantId"), paramsDishInfo);
            				}
            				//新美大外卖菜品映射后，将成功映射的菜品推送状态批量变更为“1”已推送
            				if(sql_item != ""){
            					add_update_third_organ_list.add(sql_item);
            				}
            			}
            		}
            		for (int i = 0; i < resDishCategoryInfoList.size(); i++)
            		{
            			JSONObject dlDishCategory = resDishCategoryInfoList.getJSONObject(i);
            			if(!dlDishCategory.isNullObject() && !dlDishCategory.isEmpty()){
            				String sql_item_class = "";
            				//判断cc_third_item_class_info表中是否已经存在此菜品分类信息
            				if(dlDishCategory.optString("isNull").equals("0")){
            					//判断此菜品分类信息是否是已推送状态
            					if(!dlDishCategory.optString("whether_push_over").equals("1")){
            						sql_item_class = "UPDATE cc_third_item_class_info SET whether_push_over = 1,last_send_class_name=cur_class_name,remark='' where id = " + dlDishCategory.optString("id");
            					}else{
                					//查询菜品分类的class信息
                					String sql_class = "SELECT aa.*,b.itemclass_name from (select DISTINCT(class) from hq_item_menu_class where details_id in (SELECT id from hq_item_menu_details where item_id = '"+ dlDishCategory.optString("eDishCode") +"') AND chanel = '"+ params.optString("channel") +"')aa LEFT JOIN hq_item_class b on b.id=aa.class";
                					List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(params.optString("tenantId"), sql_class);
                					JSONObject paramsDishInfo = new JSONObject();
                					if(all_third_item_to_org_list.size()>0){
                    					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
                    						paramsDishInfo.put("class_id",to_org_dish_obj.optString("class"));
                    						paramsDishInfo.put("last_send_class_name",to_org_dish_obj.optString("itemclass_name"));
                    						paramsDishInfo.put("cur_class_name",to_org_dish_obj.optString("itemclass_name"));
                    					}
                    				}
                					paramsDishInfo.put("channel", params.optString("channel"));
                					paramsDishInfo.put("shop_id", params.optString("shop_id"));
                					paramsDishInfo.put("send", "no");
                					paramsDishInfo.put("isWhether", "yes");
                					
                					//推送此菜品信息到cc_third_item_class_info表，并且将状态设置为已推送状态
                					JSONObject jsonStr = saveDishCategory(params.optString("tenantId"), paramsDishInfo);
                				
            					}
            				}else{
            					//查询菜品分类的class信息
            					String sql_class = "select DISTINCT(class) from hq_item_menu_class where details_id in (SELECT id from hq_item_menu_details where item_id = '"+ dlDishCategory.optString("eDishCode") +"') AND chanel = '"+ params.optString("channel") +"'";
            					List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(params.optString("tenantId"), sql_class);
            					JSONObject paramsDishInfo = new JSONObject();
            					if(all_third_item_to_org_list.size()>0){
                					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
                						paramsDishInfo.put("class_id",to_org_dish_obj.optString("class"));
                						paramsDishInfo.put("last_send_class_name",to_org_dish_obj.optString("itemclass_name"));
                						paramsDishInfo.put("cur_class_name",to_org_dish_obj.optString("itemclass_name"));
                					}
                				}
            					paramsDishInfo.put("channel", params.optString("channel"));
            					paramsDishInfo.put("shop_id", params.optString("shop_id"));
            					paramsDishInfo.put("send", "no");
            					paramsDishInfo.put("isWhether", "yes");
            					
            					//推送此菜品信息到cc_third_item_class_info表，并且将状态设置为已推送状态
            					JSONObject jsonStr = saveDishCategory(params.optString("tenantId"), paramsDishInfo);
            				}
            				//新美大外卖菜品映射后，将成功映射的菜品的分类推送状态变更为“1”已推送
            				if(sql_item_class != ""){
            					add_update_third_organ_list.add(sql_item_class);
            				}
            			}
            		}
            		
            		//批量修改菜品及菜品分类的推送状态
            		if(add_update_third_organ_list.size()>0){
            			String[] sql_Array = null;
            			int size = add_update_third_organ_list.size();
            			sql_Array = (String[]) add_update_third_organ_list.toArray(new String[size]);
            			dao.getJdbcTemplate(params.optString("tenantId")).batchUpdate(sql_Array);
            			logger.info("[新美大外卖更改菜品映射信息推送状态修改数据：]"+sql_Array.toString());
            		}

        			ccBusniessLogBean.setRequestId(requestId.toString());
        			ccBusniessLogBean.setTenancyId(params.optString("tenantId"));
        			ccBusniessLogBean.setCategory("cc");
        			ccBusniessLogBean.setType("mapping");
        			ccBusniessLogBean.setChannel(params.optString("channel"));
        			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
        			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.ShopServiceImpl:updateXmdWhetherPushOver");
        			ccBusniessLogBean.setRequestBody(params.toString());
        			
        			
        			ccBusniessLogBean.setCreateTime(new Date().getTime());
        			ccBusniessLogBean.setIsNormal("1");
        			ccBusniessLogBean.setIsThird("0");
        			
        				
        		    ccBusniessLogBean.setShopId(params.optString("shop_id"));

        			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());
        			
        			ccBusniessLogBean.setThirdId("");
        			ccBusniessLogBean.setTzxId("");
        		    ccBusniessLogBean.setTzxName("");
        			
        			//将映射中，不一致的菜品信息存入到菜品的备注信息中
            		if(selectMappingJsonList.size() > 0){
                		for (int i = 0; i < selectMappingJsonList.size(); i++)
                		{
                			JSONArray dlList = selectMappingJsonList.getJSONArray(i);
                			for(int j = 0; j < dlList.size(); j++)
                    		{
                				JSONObject dl = dlList.getJSONObject(j);
        	        			if(!dl.isNullObject() && !dl.isEmpty()){
        	        				//获取历史的备注信息
				            		String oldRemark = "";
									String sql_sel_item_class = "SELECT remark FROM cc_third_item_info where tenancy_id = '"+ params.optString("tenantId") +"' AND channel = '"+ params.optString("channel") +"' AND item_code = '"+ dl.optString("eDishCode") +"' AND shop_id = " + params.optString("shop_id");
									List<JSONObject> dishRemark =this.dao.query4Json(params.optString("tenantId"), sql_sel_item_class);
				    				if(dishRemark.size()>0){
				    					for(JSONObject to_dish_remark:dishRemark){
				    						oldRemark = to_dish_remark.optString("remark");
				    						//加上[errorMsgBegin]前缀和[errorMsgEnd]后缀是用作更新使用，如果同一菜品多次映射错信息，
				    						//就会存入很长的备注信息，加入前缀和后缀就会将之前的不一致菜品信息被覆盖掉而不是新添加。
				    						//前端展示的时候将其过滤掉。
				    						int numA = oldRemark.indexOf("[errorMsgBegin]");
				    						int numB = oldRemark.indexOf("[errorMsgEnd]");
				    						if(numA >= 0 && numB >= 0){
				    							String strRemark = oldRemark.substring(numA, numB + 13);
				    							oldRemark = oldRemark.replace(strRemark, "");
				    						}
				    					}
				    				}
				    				//将新的备注信息和之前旧的备注信息一同存入到备注信息栏
				    				String strErrMsgAll = dl.optString("strErrMsg") + oldRemark;
									String sql_up_item_class = "UPDATE cc_third_item_info SET remark = '"+ strErrMsgAll +"' where tenancy_id = '"+ params.optString("tenantId") +"' AND channel = '"+ params.optString("channel") +"' AND item_code = '"+ dl.optString("eDishCode") +"' AND shop_id = " + params.optString("shop_id");
									GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
									dao.execute(params.optString("tenantId"), sql_up_item_class);
									ccBusniessLogBean.setResponseBody("OK");
        	        			}
                    		}
                		}
            		}
            	}
        } catch (Exception e) {
        	ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
            result.put("data","error");
            result.put("msg",e.getMessage());
            logger.error("新美大外卖更改菜品映射信息推送状态回调异常：",e);
        }finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
        logger.info("新美大外卖更改菜品映射信息推送状态回调返回："+result);
        return result;
    }
	
	/**
	 * 用于判断新美大外卖菜品映射成功后返回的菜品参数信息是否从hq_item_info表中的菜品信息推送到cc_third_item_info表中，
	 * hq_item_info表中的菜品信息cc_third_item_info表中未必会有
	 *  changhui add 2017-11-23
	 * @param params
	 * @return
	 */
	public JSONArray  selectDishHqContainCC(JSONObject params){
		JSONArray paramsDishInfoList = new JSONArray();
		JSONArray resDishInfoList = new JSONArray();
		JSONObject resJson = new JSONObject(); 
		try {
			paramsDishInfoList = params.optJSONArray("value");
			for (int i = 0; i < paramsDishInfoList.size(); i++)
    		{
    			JSONObject dl = paramsDishInfoList.getJSONObject(i);
    			if(!dl.isNullObject() && !dl.isEmpty()){
    				resJson.put("eDishCode", dl.optString("eDishCode"));
    				//根据商户ID、商家ID、渠道信息、菜品编号查询是否存在此菜品信息
    				String sql_item = "SELECT id FROM cc_third_item_info WHERE item_code = '" + dl.optString("eDishCode") + "' AND shop_id = " + params.optString("shop_id") + " AND tenancy_id = '"+ params.optString("tenantId") +"'  AND channel = '"+ params.optString("channel") +"'";
    				List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(params.optString("tenantId"), sql_item);
    				if(all_third_item_to_org_list.size()>0){
    					//"0"代表有数据
    					resJson.put("isNull", "0");
    					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
    						resJson.put("id",to_org_dish_obj.optString("id"));
    					}
    				}else{
    					//“1”代表没有数据
    					resJson.put("isNull", "1");
    					resJson.put("id","");
    				}
    				resDishInfoList.add(resJson);
    			}
    		}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return resDishInfoList;
	}
	
	/**
	 * 用于判断新美大外卖菜品映射成功后返回的菜品类别参数信息是否从hq_item_menu_class表中的菜品信息推送到cc_third_item_class_info表中，
	 * hq_item_menu_class表中的菜品信息cc_third_item_class_info表中未必会有
	 *  changhui add 2017-11-23
	 * @param params
	 * @return
	 */
	public JSONArray  selectDishCategoryHqContainCC(JSONObject params){
		JSONArray paramsDishInfoList = new JSONArray();
		JSONArray resDishInfoList = new JSONArray();
		JSONObject resJson = new JSONObject(); 
		try {
			paramsDishInfoList = params.optJSONArray("value");
			for (int i = 0; i < paramsDishInfoList.size(); i++)
    		{
    			JSONObject dl = paramsDishInfoList.getJSONObject(i);
    			if(!dl.isNullObject() && !dl.isEmpty()){
    				resJson.put("eDishCode", dl.optString("eDishCode"));
    				////根据商户ID、商家ID、渠道信息、菜品编号查询是否存在此菜品分类信息
    				String sql_item = "select id,whether_push_over from cc_third_item_class_info where item_class_id in (select CLASS from hq_item_menu_class where details_id in (SELECT id from hq_item_menu_details where item_id = '" + dl.optString("eDishCode") + "')) AND channel = '"+ params.optString("channel") +"' AND shop_id = " + params.optString("shop_id") + " AND tenancy_id = '"+ params.optString("tenantId") +"'";
    				List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(params.optString("tenantId"), sql_item);
    				if(all_third_item_to_org_list.size()>0){
    					//"0"代表有数据
    					resJson.put("isNull", "0");
    					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
    						resJson.put("id", to_org_dish_obj.optString("id"));
    						resJson.put("whether_push_over",to_org_dish_obj.optString("whether_push_over"));
    					}
    				}else{
    					//"1"代表无数据
    					resJson.put("isNull", "1");
    					resJson.put("id", "");
    					resJson.put("whether_push_over", "");
    				}
    				resDishInfoList.add(resJson);
    			}
    		}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return resDishInfoList;
	}
	
	/**
	 * 是否达到菜品映射要求，判断菜品的参数是否一致
	 *  changhui add 2017-11-23
	 * @param params
	 * @return
	 */
	public JSONArray  selectAchieveMappingParamsCondition(JSONObject params, JSONArray dishList){
		JSONArray paramsDishInfoList = new JSONArray();
		JSONArray resErrorJsonList = new JSONArray();
		try {
			paramsDishInfoList = params.optJSONArray("value");
			//把菜品编号用数组存储起来
			String[] ePoiIdList =  new String[paramsDishInfoList.size()]; 
			for (int i = 0; i < paramsDishInfoList.size(); i++)
    		{
    			JSONObject dl = paramsDishInfoList.getJSONObject(i);
    			if(!dl.isNullObject() && !dl.isEmpty()){
    				ePoiIdList[i] = dl.optString("eDishCode");
    			}
    		}
			
			if(ePoiIdList.length > 0){
				//根据eDishCode批量查询外卖菜品信息，传入的edishcode过多，不宜超过100个
				int cou = 100;
				//如果菜品数量超过100个那么就要对其分批查询
				int num = ePoiIdList.length/cou;
				int numy = ePoiIdList.length - cou * num;
				//查询的菜品数量在100个以内
				if(num ==0){
					String ePoiIds = "";
					for(int a = 0; a < numy; a++){
						ePoiIds += ePoiIdList[a].toString() + ",";
					}
					
					resErrorJsonList.add(getErrorMessage( params, dishList, ePoiIds));
				}else{
					//查询的菜品数量超出100个，做分批查询
					for(int a = 0; a < num; a++){
						String ePoiIds = "";
						for(int b = a * cou;b < (a + 1) * cou; b++){
							ePoiIds += ePoiIdList[b].toString() + ",";
						}
						
						resErrorJsonList.add(getErrorMessage( params, dishList, ePoiIds));
					}
					String ePoiIds = "";
					for(int a = 0; a < numy; a++){
						ePoiIds += ePoiIdList[a].toString() + ",";
					}
					
					resErrorJsonList.add(getErrorMessage( params, dishList, ePoiIds));
				}
				
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return resErrorJsonList;
	}
	
	/**
	 * 判断菜品参数不一致的项有哪些
	 *  changhui add 2017-11-23
	 * @param params
	 * @param dishList
	 * @return
	 */
	public JSONArray getErrorMessage(JSONObject params, JSONArray dishList, String ePoiIds){
		JSONArray resErrorJsonList = new JSONArray();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			Map<String, String> paramMap = new HashMap<>();
			String tenantId = params.optString("tenantId");
			String shopId = params.optString("shop_id");
	    	String ePoiId = shopId+"@"+tenantId;
			paramMap.put("ePoiId",  ePoiId);
			//erp方要查询菜品id（多个，以逗号隔开，最多100个）
			paramMap.put("eDishCodes",  ePoiIds.substring(0, ePoiIds.length() - 1));
			
			
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.ShopServiceImpl:getErrorMessage");
			ccBusniessLogBean.setRequestBody(params.toString());
			
			
			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");
			
			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
		    ccBusniessLogBean.setTzxName("");	
		    ccBusniessLogBean.setShopId(shopId);

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());

			//根据eDishCode批量查询外卖菜品信息
			String xmdResult = XmdWMUtils.execCmd(params.optString("tenantId"), params.optString("shop_id"), XmdWMUtils.CMD_FOOD_QUERYLISTBYEDISHCODES,paramMap);
			
			JSONArray logResultList = new JSONArray();
			
			JSONObject resDishJson = JSONObject.fromObject(xmdResult);
			JSONObject resDishDataJson = resDishJson.optJSONObject("data");
			if(resDishDataJson.optString("status").equals("0")){
				JSONArray dishJsonList = resDishDataJson.optJSONArray("list");
				
				
				for (int i = 0; i < dishJsonList.size(); i++)
				{
					JSONObject dishJson = dishJsonList.getJSONObject(i);
					if(!dishJson.isNullObject() && !dishJson.isEmpty()){
						for (int j = 0; j < dishList.size(); j++)
			    		{ 
			    			JSONObject dish = dishList.getJSONObject(j);
			    			if(!dish.isNullObject() && !dish.isEmpty()){
			    				if(dishJson.optString("eDishCode").equals(dish.optString("item_id"))){
			    					JSONObject errMsg = new JSONObject();
			    					JSONObject logResult = new JSONObject();
			    					boolean f = false;
			    					//String strErrMsg = "[errorMsgBegin]";
			    					String strErrMsg = "";
			    					//菜品分类
			    					if(!dishJson.optString("categoryName").equals(dish.optString("last_send_class_name"))){
			    						errMsg.put("categoryName", "[菜品分类]");
			    						strErrMsg += "[菜品分类]";
			    						logResult.put("xmdCategoryName", dishJson.optString("categoryName"));
			    						logResult.put("erpCategoryName", dish.optString("last_send_class_name"));
			    						f = true;
			    					}else{
			    						errMsg.put("categoryName", "");
			    					}
			    					//菜名
			    					if(!dishJson.optString("dishName").equals(dish.optString("item_name"))){
			    						errMsg.put("dishName", "[菜名]");
			    						strErrMsg += "[菜名]";
			    						logResult.put("xmdDishName", dishJson.optString("dishName"));
			    						logResult.put("erpDishName", dish.optString("item_name"));
			    						f = true;
			    					}else{
			    						errMsg.put("dishName", "");
			    					}
			    					//单位/规格
			    					if(!dishJson.optString("unit").equals(dish.optString("unit"))){
			    						errMsg.put("unit", "[单位/规格]");
			    						strErrMsg += "[单位/规格]";
			    						logResult.put("xmdUnit", dishJson.optString("unit"));
			    						logResult.put("erpUnit", dish.optString("unit"));
			    						f = true;
			    					}else{
			    						errMsg.put("unit", "");
			    					}
			    					//价格
									if(!(dishJson.optDouble("price") == dish.optDouble("default_price"))){
										errMsg.put("price", "[价格]");
										strErrMsg += "[价格]";
										logResult.put("xmdPrice", dishJson.optDouble("price"));
			    						logResult.put("erpPrice", dish.optDouble("default_price"));
										f = true;
									}else{
										errMsg.put("price", "");
									}
									//餐盒单价
									if(!(dishJson.optDouble("boxPrice",0.0) == dish.optDouble("box_price",0.0))){
										errMsg.put("boxPrice", "[餐盒单价]");
										strErrMsg += "[餐盒单价]";
										logResult.put("xmdBoxPrice", dishJson.optDouble("boxPrice",0.0));
			    						logResult.put("erpBoxPrice", dish.optDouble("box_price",0.0));
										f = true;
									}else{
										errMsg.put("boxPrice", "");
									}
									if(f){
										//strErrMsg += "[errorMsgEnd]";
										errMsg.put("twoDishName", "erp菜品:[" + dish.optString("item_name") + "]与平台菜品:[" + dishJson.optString("dishName") + "] 的 ");
			    						strErrMsg = "[errorMsgBegin] erp菜品:[" + dish.optString("item_name") + "]与平台菜品:[" + dishJson.optString("dishName") + "] 的 " + strErrMsg + "不一致![errorMsgEnd]";
										errMsg.put("eDishCode", dishJson.optString("eDishCode"));
										errMsg.put("strErrMsg", strErrMsg);
										logResult.put("平台菜品", dishJson.optString("dishName"));
			    						logResult.put("erp菜品", dish.optString("item_name"));
										logResultList.add(logResult);
										resErrorJsonList.add(errMsg);
									}
			    				}
			    			}
			    			
			    		}
						
					}
				}
			}else{
				
			}
			ccBusniessLogBean.setResponseBody("[平台返回信息:]" + xmdResult + "[不一致信息:]" + logResultList.toString());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		return resErrorJsonList;
	}
	
	/**
	 * 是否达到菜品映射要求，判断菜品数量是否一致【此方法暂时不用】
	 *  changhui add 2017-11-23
	 * @param params
	 * @return
	 */
	@Override
	public String  isAchieveMappingCondition(JSONObject params, JSONArray dishList) throws Exception {
		String rest = "";
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			Map<String, String> paramMap = new HashMap<>();
			String tenantId = params.optString("tenantId");
			String shopId = params.optString("shop_id");
	    	String ePoiId = shopId+"@"+tenantId;
			paramMap.put("ePoiId",  ePoiId);
				
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.ShopServiceImpl:isAchieveMappingCondition");
			ccBusniessLogBean.setRequestBody(params.toString());
			
			
			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");
			
			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
		    ccBusniessLogBean.setTzxName("");	
		    ccBusniessLogBean.setShopId(shopId);

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());
			
			String xmdResult = XmdWMUtils.execCmd(params.optString("tenantId"), params.optString("shop_id"), XmdWMUtils.CMD_FOOD_QUERYBASELISTBYEPOIID,paramMap,XmdWMUtils.HTTP_REQUEST_GET);
			
			ccBusniessLogBean.setResponseBody(xmdResult);
			
			JSONObject resDish = JSONObject.fromObject(xmdResult);
			JSONArray resDishList = JSONArray.fromObject(resDish.optJSONArray("data"));
			if(resDishList.size() == dishList.size()){
				//erp菜品和新美大外卖菜品数量一致
				rest = "0";
			}else{
				if(resDishList.size() > dishList.size()){
					//erp菜品比新美大外卖菜品数量少
					rest = "1";
				}else{
					//erp菜品比新美大外卖菜品数量多
					rest = "2";
				}
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		return rest;
	}
	
	/**
	 * 新美大外卖批量修改菜品推送状态
	 *  changhui add 2017-11-25
	 * @param params
	 * @return
	 */
	public JSONObject  updateAllDishWhetherPushOver(JSONObject params){
		JSONObject resJson = new JSONObject();
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			String sql = "UPDATE cc_third_item_info SET whether_push_over = 0 WHERE shop_id = " + params.optString("shop_id") + " AND tenancy_id = '"+ params.optString("tenantId") +"'  AND channel = '"+ params.optString("channel") +"'";
			logger.info("[新美大外卖批量修改菜品推送状态：]"+sql);
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(params.optString("tenantId"));
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.ShopServiceImpl:updateAllDishWhetherPushOver");
			ccBusniessLogBean.setRequestBody(params.toString());
			
			
			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");
			
			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId("");
		    ccBusniessLogBean.setTzxName("");	
		    ccBusniessLogBean.setShopId(params.optString("shop_id"));

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());
			dao.execute(params.optString("tenantId"), sql);
			resJson.put("data", "ok");
			resJson.put("msg", "");
			ccBusniessLogBean.setResponseBody("OK");
		} catch (Exception e) {
			logger.error(e.getMessage(), e.getCause());
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			resJson.put("data", "error");
			resJson.put("msg", e.getMessage());
		}finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		
		return resJson;
	}
	
	/**
	 * 保存菜品类别及菜品的映射关系
	 *  changhui add 2017-12-15
	 * @param params
	 * @return
	 */
	@Override
	public JSONObject  saveEleDishAndDishCategoryMapping(JSONObject params, JSONArray dishList) throws Exception {
		JSONObject rest = new JSONObject();
		rest.put("result", "ok");
		rest.put("msg", "");
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try {
			Map<String, String> paramMap = new HashMap<>();
			String tenantId = params.optString("tenantId");
			String shopId = params.optString("shop_id");
	    	String ePoiId = shopId+"@"+tenantId;
			paramMap.put("ePoiId",  ePoiId);
			
			List<String> update_third_item_id_list = new ArrayList<String>();
			List<String> update_third_item_class_id_list = new ArrayList<String>();
			String strClassId = ",";
			for (int i = 0; i < dishList.size(); i++)
			{
				JSONObject dl = dishList.getJSONObject(i);
				if(!dl.isNullObject() && !dl.isEmpty()){
					if((dl.optString("third_item_id").equals(dl.optString("eleId")) && !dl.optString("eleId").equals("")) 
							|| dl.optString("item_name").equals(dl.optString("eleItemName"))){
						String update_third_item_id = "UPDATE cc_third_item_info SET third_item_id = '" + dl.optString("eleId") + "',whether_push_over = '1' "
								+ ",norms = '" + dl.optString("eleSpecs") + "',remark = null WHERE id = " + dl.optString("id");
						if(dl.optString("eleSpecs").equals("") || dl.optString("eleSpecs").equals("[]")){
							update_third_item_id = "UPDATE cc_third_item_info SET third_item_id = '" + dl.optString("eleId") + "',whether_push_over = '1'"
									+ ",norms = null,remark = null WHERE id = " + dl.optString("id");
						}
						update_third_item_id_list.add(update_third_item_id);
					}
					if((dl.optString("third_class_id").equals(dl.optString("eleClassId")) && !dl.optString("eleClassId").equals("") || dl.optString("last_send_class_name").equals(dl.optString("eleLastSendClassName"))) && strClassId.indexOf("," + dl.optString("eleClassId") + ",") < 0){
						strClassId += dl.optString("eleClassId") + ",";
						String update_third_item_class_id = "UPDATE cc_third_item_class_info SET third_class_id = '" + dl.optString("eleClassId") + "',whether_push_over = '1' WHERE id = " + dl.optString("last_send_class_id");
						if(!update_third_item_class_id_list.contains(update_third_item_class_id)){
							update_third_item_class_id_list.add(update_third_item_class_id);
						}
					}
				}
			}

			//将饿了么的菜品分类和菜品信息的第三方编号全部清空
			List<String> update_third_id_null = new ArrayList<String>();
			String update_third_item_id_null = "UPDATE cc_third_item_info SET third_item_id = NULL,whether_push_over = '0',norms = NULL WHERE tenancy_id = '" + params.optString("tenantId") + "' AND shop_id = '" + params.optString("shop_id") + "' AND channel = '" + params.optString("channel") + "'";
			String update_third_item_class_id_null = "UPDATE cc_third_item_class_info SET third_class_id = NULL,whether_push_over = '0' WHERE tenancy_id = '" + params.optString("tenantId") + "' AND shop_id = '" + params.optString("shop_id") + "' AND channel = '" + params.optString("channel") + "'";
			update_third_id_null.add(update_third_item_id_null);
			update_third_id_null.add(update_third_item_class_id_null);
			if(update_third_id_null.size()>0){
				String[] sql_Array = null;
				int size = update_third_id_null.size();
				sql_Array = (String[]) update_third_id_null.toArray(new String[size]);
				dao.getJdbcTemplate(params.optString("tenantId")).batchUpdate(sql_Array);
				logger.info("[更改菜品类别和菜品信息第三方编号为空的修改数据：]"+sql_Array.toString());
			}


			
			//批量修改菜品分类的映射信息
    		if(update_third_item_class_id_list.size()>0){
    			String[] sql_Array = null;
    			int size = update_third_item_class_id_list.size();
    			sql_Array = (String[]) update_third_item_class_id_list.toArray(new String[size]);
    			dao.getJdbcTemplate(params.optString("tenantId")).batchUpdate(sql_Array);
    			logger.info("[更改饿了么菜品类别映射信息推送状态修改数据：]"+sql_Array.toString());
    		}
    		
    		//批量修改菜品的映射信息
    		if(update_third_item_id_list.size()>0){
    			String[] sql_Array = null;
    			int size = update_third_item_id_list.size();
    			sql_Array = (String[]) update_third_item_id_list.toArray(new String[size]);
    			dao.getJdbcTemplate(params.optString("tenantId")).batchUpdate(sql_Array);
    			logger.info("[更改饿了么菜品映射信息推送状态修改数据：]"+sql_Array.toString());
    		}
    		
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("mapping");
			ccBusniessLogBean.setChannel(params.optString("channel"));
			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.ShopServiceImpl:isAchieveMappingCondition");
			ccBusniessLogBean.setRequestBody(params.toString());
			
			
			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");
			
			ccBusniessLogBean.setThirdId("");
			ccBusniessLogBean.setTzxId(params.optString("class_id"));
		    ccBusniessLogBean.setTzxName("");	
		    ccBusniessLogBean.setShopId(shopId);

			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());
			
			ccBusniessLogBean.setResponseBody(rest.toString());
			
			
		} catch (Exception e) {
			rest.put("result", "no");
			rest.put("msg", e.getMessage());
			ccBusniessLogBean.setResponseBody(rest.toString());
			logger.error(e.getMessage(),e.getCause());
			e.printStackTrace();
		}finally {
			//KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		return rest;
	}
	
	@Override
    public JSONObject updateWhetherPushOver(JSONObject params, JSONArray dishList) throws Exception {
        logger.info("饿了么外卖更改菜品映射信息推送状态回调函数："+params);
        JSONObject result=new JSONObject();
        CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
        try {
        	//将已映射完成的菜品在erp系统中修改推送状态
        		JSONArray resDishInfoList = new JSONArray();
            	JSONArray resDishCategoryInfoList = new JSONArray();
            	//菜品完成映射操作后，根据商户ID、商家ID、渠道信息来将所有符合条件的菜品推送状态更改为未推送状态
            	JSONObject resObject = updateAllDishWhetherPushOver(params);
            	//修改成功后执行
            	if(resObject.opt("data").equals("ok")){
            		resDishInfoList = selectEleDishHqContainCC(params, dishList);
            		resDishCategoryInfoList = selectEleDishCategoryHqContainCC(params, dishList);
            		List<String> add_update_third_organ_list = new ArrayList<String>();
            		for (int i = 0; i < resDishInfoList.size(); i++)
            		{
            			JSONObject dlDishInfo = resDishInfoList.getJSONObject(i);
            			if(!dlDishInfo.isNullObject() && !dlDishInfo.isEmpty()){
            				String sql_item="";
            				//判断cc_third_item_info表中是否已经存在此菜品信息
            				if(dlDishInfo.optString("isNull").equals("0")){
            					sql_item = "UPDATE cc_third_item_info SET whether_push_over = 1 WHERE id = " + dlDishInfo.optString("id");
            				}else{
            					JSONObject paramsDishInfo = new JSONObject();
            					//查询菜品的class信息
            					String sql_class = "select DISTINCT(class) from hq_item_menu_class where details_id in (SELECT id from hq_item_menu_details where item_id = '"+ dlDishInfo.optString("eDishCode") +"') AND chanel = '"+ params.optString("channel") +"'";
            					List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(params.optString("tenantId"), sql_class);
            					if(all_third_item_to_org_list.size()>0){
                					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
                						paramsDishInfo.put("class_id",to_org_dish_obj.optString("class"));
                					}
                				}
            					paramsDishInfo.put("send_operator", "system");
            					paramsDishInfo.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
            					paramsDishInfo.put("channel", params.optString("channel"));
            					paramsDishInfo.put("shop_id", params.optString("shop_id"));
            					paramsDishInfo.put("item_id", dlDishInfo.optString("eDishCode"));
            					paramsDishInfo.put("send", "no");
            					paramsDishInfo.put("isWhether", "yes");
            					//推送此菜品信息到cc_third_item_info表，并且将状态设置为已推送状态
                				JSONObject jsonStr = saveDish(params.optString("tenantId"), paramsDishInfo);
            				}
            				//新美大外卖菜品映射后，将成功映射的菜品推送状态批量变更为“1”已推送
            				if(sql_item != ""){
            					add_update_third_organ_list.add(sql_item);
            				}
            			}
            		}
            		for (int i = 0; i < resDishCategoryInfoList.size(); i++)
            		{
            			JSONObject dlDishCategory = resDishCategoryInfoList.getJSONObject(i);
            			if(!dlDishCategory.isNullObject() && !dlDishCategory.isEmpty()){
            				String sql_item_class = "";
            				//判断cc_third_item_class_info表中是否已经存在此菜品分类信息
            				if(dlDishCategory.optString("isNull").equals("0")){
            					//判断此菜品分类信息是否是已推送状态
            					if(!dlDishCategory.optString("whether_push_over").equals("1")){
            						sql_item_class = "UPDATE cc_third_item_class_info SET whether_push_over = 1 where id = " + dlDishCategory.optString("id");
            					}
            				}else{
            					//查询菜品分类的class信息
            					String sql_class = "select DISTINCT(class) from hq_item_menu_class where details_id in (SELECT id from hq_item_menu_details where item_id = '"+ dlDishCategory.optString("eDishCode") +"') AND chanel = '"+ params.optString("channel") +"'";
            					List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(params.optString("tenantId"), sql_class);
            					JSONObject paramsDishInfo = new JSONObject();
            					if(all_third_item_to_org_list.size()>0){
                					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
                						paramsDishInfo.put("class_id",to_org_dish_obj.optString("class"));
                					}
                				}
            					paramsDishInfo.put("channel", params.optString("channel"));
            					paramsDishInfo.put("shop_id", params.optString("shop_id"));
            					paramsDishInfo.put("send", "no");
            					paramsDishInfo.put("isWhether", "yes");
            					
            					//推送此菜品信息到cc_third_item_class_info表，并且将状态设置为已推送状态
            					JSONObject jsonStr = saveDishCategory(params.optString("tenantId"), paramsDishInfo);
            				}
            				//新美大外卖菜品映射后，将成功映射的菜品的分类推送状态变更为“1”已推送
            				if(sql_item_class != ""){
            					add_update_third_organ_list.add(sql_item_class);
            				}
            			}
            		}
            		
            		//批量修改菜品及菜品分类的推送状态
            		if(add_update_third_organ_list.size()>0){
            			String[] sql_Array = null;
            			int size = add_update_third_organ_list.size();
            			sql_Array = (String[]) add_update_third_organ_list.toArray(new String[size]);
            			dao.getJdbcTemplate(params.optString("tenantId")).batchUpdate(sql_Array);
            			logger.info("[饿了么外卖更改菜品映射信息推送状态修改数据：]"+sql_Array.toString());
            		}

        			ccBusniessLogBean.setRequestId(requestId.toString());
        			ccBusniessLogBean.setTenancyId(params.optString("tenantId"));
        			ccBusniessLogBean.setCategory("cc");
        			ccBusniessLogBean.setType("mapping");
        			ccBusniessLogBean.setChannel(params.optString("channel"));
        			ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
        			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.ShopServiceImpl:updateXmdWhetherPushOver");
        			ccBusniessLogBean.setRequestBody(params.toString());
        			
        			
        			ccBusniessLogBean.setCreateTime(new Date().getTime());
        			ccBusniessLogBean.setIsNormal("1");
        			ccBusniessLogBean.setIsThird("0");
        			
        				
        		    ccBusniessLogBean.setShopId(params.optString("shop_id"));

        			ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());
        			
        			ccBusniessLogBean.setThirdId("");
        			ccBusniessLogBean.setTzxId("");
        		    ccBusniessLogBean.setTzxName("");
        		    result.put("data","ok");
                    result.put("msg","");
            	}
        } catch (Exception e) {
        	ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
            result.put("data","error");
            result.put("msg",e.getMessage());
            logger.error("饿了么外卖更改菜品映射信息推送状态回调异常：",e);
        }finally {
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
        logger.info("饿了么外卖更改菜品映射信息推送状态回调返回："+result);
        return result;
    }
	
	public JSONArray  selectEleDishCategoryHqContainCC(JSONObject params, JSONArray dishList){
		JSONArray resDishInfoList = new JSONArray();
		JSONObject resJson = new JSONObject(); 
		try {
			for (int i = 0; i < dishList.size(); i++)
    		{
    			JSONObject dl = dishList.getJSONObject(i);
    			if(!dl.isNullObject() && !dl.isEmpty()){
    				resJson.put("eDishCode", dl.optString("eDishCode"));
    				////根据商户ID、商家ID、渠道信息、菜品编号查询是否存在此菜品分类信息
    				String sql_item = "select id,whether_push_over from cc_third_item_class_info where item_class_id in (select CLASS from hq_item_menu_class where details_id in (SELECT id from hq_item_menu_details where item_id = '" + dl.optString("eDishCode") + "')) AND channel = '"+ params.optString("channel") +"' AND shop_id = " + params.optString("shop_id") + " AND tenancy_id = '"+ params.optString("tenantId") +"'";
    				List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(params.optString("tenantId"), sql_item);
    				if(all_third_item_to_org_list.size()>0){
    					//"0"代表有数据
    					resJson.put("isNull", "0");
    					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
    						resJson.put("id", to_org_dish_obj.optString("id"));
    						resJson.put("whether_push_over",to_org_dish_obj.optString("whether_push_over"));
    					}
    				}else{
    					//"1"代表无数据
    					resJson.put("isNull", "1");
    					resJson.put("id", "");
    					resJson.put("whether_push_over", "");
    				}
    				resDishInfoList.add(resJson);
    			}
    		}
		} catch (Exception e) {
			logger.error(e.getMessage(), e.getCause());
			e.printStackTrace();
		}
		return resDishInfoList;
	}
	
	public JSONArray  selectEleDishHqContainCC(JSONObject params, JSONArray dishList){
		JSONArray resDishInfoList = new JSONArray();
		JSONObject resJson = new JSONObject(); 
		try {
			for (int i = 0; i < dishList.size(); i++)
    		{
    			JSONObject dl = dishList.getJSONObject(i);
    			if(!dl.isNullObject() && !dl.isEmpty()){
    				resJson.put("eDishCode", dl.optString("eDishCode"));
    				//根据商户ID、商家ID、渠道信息、菜品编号查询是否存在此菜品信息
    				String sql_item = "SELECT id FROM cc_third_item_info WHERE item_code = '" + dl.optString("eDishCode") + "' AND shop_id = " + params.optString("shop_id") + " AND tenancy_id = '"+ params.optString("tenantId") +"'  AND channel = '"+ params.optString("channel") +"'";
    				List<JSONObject> all_third_item_to_org_list =this.dao.query4Json(params.optString("tenantId"), sql_item);
    				if(all_third_item_to_org_list.size()>0){
    					//"0"代表有数据
    					resJson.put("isNull", "0");
    					for(JSONObject to_org_dish_obj:all_third_item_to_org_list){
    						resJson.put("id",to_org_dish_obj.optString("id"));
    					}
    				}else{
    					//“1”代表没有数据
    					resJson.put("isNull", "1");
    					resJson.put("id","");
    				}
    				resDishInfoList.add(resJson);
    			}
    		}
		} catch (Exception e) {
			logger.error(e.getMessage(), e.getCause());
			e.printStackTrace();
		}
		return resDishInfoList;
	}
	
	@Override
	public JSONArray getEleDishInfoList(String tenantId, JSONObject params){
		JSONArray resDishInfoList = new JSONArray();
		try {
			JSONObject result = getLocalDishNoPageList(tenantId, params);

			resDishInfoList = JSONArray.fromObject(result.optString("rows"));
			
			boolean isSave = true;
			if(resDishInfoList != null && resDishInfoList.size() > 0){
				ProductService productService = new ProductService(ElmUtils.getConfig(tenantId, params.optString("shop_id")), ElmUtils.getToken(tenantId, params.optString("shop_id")));
				Map<String, String> mapPara = ElmUtils.getELeDeveloperAndKey(tenantId, params.optString("shop_id"));
				List<OCategory> strResul = productService.getShopCategories(Long.valueOf(mapPara.get("thirdShopId")));
				if(strResul != null && strResul.size() > 0){
					for(OCategory o : strResul){
						Map<Long, OItem> reslutMap = productService.getItemsByCategoryId(Long.valueOf(o.getId()));
						if(!reslutMap.isEmpty() && reslutMap.size() > 0){
							for(OItem item : reslutMap.values()){
								//商品规格列表
								List<OSpec> specs = item.getSpecs();
								
								for (int i = 0; i < resDishInfoList.size(); i++)
								{
									JSONObject dl = resDishInfoList.getJSONObject(i);
									if(!dl.isNullObject() && !dl.isEmpty()){
										if(dl.optString("last_send_class_name").equals(o.getName()) && dl.optString("item_name").equals(item.getName())){
											dl.put("eleLastSendClassName", o.getName());
											dl.put("eleItemName", item.getName());
											dl.put("eleId", item.getId());
											dl.put("eleClassId", o.getId());
											//20180306保存平台菜品规格简要信息==
											if(specs!=null && !specs.isEmpty()){
												List<JSONObject> eleSpecs = new ArrayList<JSONObject>();
												for(OSpec ospec : specs){
													if(ospec != null){
														JSONObject espec = new JSONObject();
														NumberFormat df = NumberFormat.getInstance();
														df.setGroupingUsed(false);
														espec.put("specId", df.format(ospec.getSpecId()));//规格id
														espec.put("name", ospec.getName());
														espec.put("price", ospec.getPrice());
														eleSpecs.add(espec);
													}
												}
												dl.put("eleSpecs", JSONArray.fromObject(eleSpecs).toString());
											}
										}
									}
								}
							}
						}else{
							boolean f = true;
							for (int i = 0; i < resDishInfoList.size(); i++) {
								JSONObject dl = resDishInfoList.getJSONObject(i);
								if (!dl.isNullObject() && !dl.isEmpty()) {
									if((dl.optString("last_send_class_name").equals(o.getName()) || dl.optString("thirdClassId").equals(o.getId())) && f){
										f = false;
										dl.put("eleLastSendClassName", o.getName());
										dl.put("eleItemName", "");
										dl.put("eleId", "");
										dl.put("eleClassId", o.getId());
									}
								}
							}
						}
					}
				}
			}
		}catch (Exception e) {
			logger.error(e.getMessage(), e.getCause());
			e.printStackTrace();
		}
		return resDishInfoList;
	}

	@Override
	public JSONObject getSysParameterBySystemName(String tenantId, JSONObject sysParam) {
		JSONObject jsonObject = new JSONObject();
		try{
			StringBuffer sql  = new StringBuffer();
			sql.append(" SELECT ");
			sql.append(" 	ID, ");
			sql.append(" 	model_name, ");
			sql.append(" 	para_name, ");
			sql.append(" 	para_code, ");
			sql.append("    para_value ");
			sql.append(" FROM ");
			sql.append(" 	sys_parameter ");
			sql.append(" WHERE ");
			sql.append(" 	valid_state = '1' ");
			sql.append(" AND system_name = '");
			sql.append(sysParam.optString("system_name")); 
			sql.append("'  AND para_code = '");
			sql.append(sysParam.getString("para_code"));   //wx_themes
			sql.append("'");
			List<JSONObject> list = this.dao.query4Json(tenantId, sql.toString());
			if(list.size() > 0){
				jsonObject = list.get(0);
			}
		}catch(Exception e){
			e.printStackTrace();
		}finally {
			return jsonObject;
		}
		
		
	}

    @Override
    public  JSONArray getXmdwmDishInfoList(String tenantId, String shopId, JSONObject params){
        JSONArray resDishInfoList = new JSONArray();
        try {
            Map<String, String> paramMap = new HashMap<>();
            String ePoiId = shopId+"@"+tenantId;
            paramMap.put("ePoiId",  ePoiId);

            JSONObject result = getLocalDishNoPageList(tenantId, params);

            resDishInfoList = JSONArray.fromObject(result.optString("rows"));

            JSONArray mappingArray = new JSONArray();

            boolean isSave = true;
            if(resDishInfoList != null && resDishInfoList.size() > 0){
                String xmdResult = XmdWMUtils.execCmd(tenantId, shopId, XmdWMUtils.CMD_FOOD_QUERYBASELISTBYEPOIID,paramMap,XmdWMUtils.HTTP_REQUEST_GET);
                if(xmdResult != null && !"".equals(xmdResult)) {
                    JSONArray arrayResult = JSONObject.fromObject(xmdResult).optJSONArray("data");
                    if (!arrayResult.isEmpty() && arrayResult.size() > 0) {
                        for (int y = 0; y < arrayResult.size(); y++) {
                            JSONObject jsonInfo = arrayResult.getJSONObject(y);
							/*JSONArray waimaiSkuBases = jsonInfo.getJSONArray("waiMaiDishSkuBases");*/
                            for (int i = 0; i < resDishInfoList.size(); i++) {
                                JSONObject dl = resDishInfoList.getJSONObject(i);
                                if (!dl.isNullObject() && !dl.isEmpty()) {
                                    if (dl.optString("last_send_class_name").equals(jsonInfo.optString("categoryName")) && dl.optString("item_name").equals(jsonInfo.optString("dishName"))) {
                                        dl.put("xmdwmLastSendClassName", jsonInfo.optString("categoryName"));
                                        dl.put("xmdwmItemName", jsonInfo.optString("dishName"));
                                        dl.put("xmdwmId", jsonInfo.optInt("dishId"));

                                        JSONObject mappingJson = new JSONObject();
                                        mappingJson.put("dishId", jsonInfo.optInt("dishId"));
                                        mappingJson.put("eDishCode", dl.optString("item_id"));
                                        mappingArray.add(mappingJson);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if(!mappingArray.isEmpty() && mappingArray.size() > 0){
                paramMap.put("dishMappings", mappingArray.toString());
                String mappingResult = XmdWMUtils.execCmd(tenantId, shopId, XmdWMUtils.CMD_DISH_DISHMAPPING,paramMap,XmdWMUtils.HTTP_REQUEST_POST);
                if(mappingResult != null && !"".equals(mappingResult)){
                    JSONObject mapJson = JSONObject.fromObject(mappingResult);
                    if(!"OK".equals(mapJson.optString("data"))){
                        resDishInfoList = new JSONArray();
                    }
                }
            }
        }catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return resDishInfoList;
    }

    /**
     * 保存新美大外卖菜品类别及菜品的映射关系
     *  changhui add 2017-12-15
     * @param params
     * @return
     */
    @Override
    public JSONObject  saveXmdwmDishAndDishCategoryMapping(JSONObject params, JSONArray dishList) throws Exception {
        JSONObject rest = new JSONObject();
        rest.put("result", "ok");
        rest.put("msg", "");
        CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
        UUID requestId=UUID.randomUUID();
        try {
            Map<String, String> paramMap = new HashMap<>();
            String tenantId = params.optString("tenantId");
            String shopId = params.optString("shop_id");
            String ePoiId = shopId+"@"+tenantId;
            paramMap.put("ePoiId",  ePoiId);

            List<String> update_third_item_id_list = new ArrayList<String>();
            List<String> update_third_item_class_id_list = new ArrayList<String>();
            String strClassId = ",";
            for (int i = 0; i < dishList.size(); i++)
            {
                JSONObject dl = dishList.getJSONObject(i);
                if(!dl.isNullObject() && !dl.isEmpty()){
                    if((dl.optString("third_item_id").equals(dl.optString("xmdwmId")) && !dl.optString("v").equals("")) || dl.optString("item_name").equals(dl.optString("xmdwmItemName"))){
                        String update_third_item_id = "UPDATE cc_third_item_info SET third_item_id = '" + dl.optString("xmdwmId") + "',whether_push_over = '1' WHERE id = " + dl.optString("id");
                        update_third_item_id_list.add(update_third_item_id);
                    }
                    if(dl.optString("last_send_class_name").equals(dl.optString("xmdwmLastSendClassName")) && strClassId.indexOf("," + dl.optString("xmdwmLastSendClassName") + ",") < 0){
                        strClassId += dl.optString("xmdwmLastSendClassName") + ",";
                        String update_third_item_class_id = "UPDATE cc_third_item_class_info SET whether_push_over = '1' WHERE id = " + dl.optString("last_send_class_id");
                        if(!update_third_item_class_id_list.contains(update_third_item_class_id)){
                            update_third_item_class_id_list.add(update_third_item_class_id);
                        }
                    }
                }
            }

            //将新美大外卖的菜品分类和菜品信息的第三方编号全部清空
            List<String> update_third_id_null = new ArrayList<String>();
            String update_third_item_id_null = "UPDATE cc_third_item_info SET third_item_id = NULL,whether_push_over = '0' WHERE tenancy_id = '" + params.optString("tenantId") + "' AND shop_id = '" + params.optString("shop_id") + "' AND channel = '" + params.optString("channel") + "'";
            //String update_third_item_class_id_null = "UPDATE cc_third_item_class_info SET third_class_id = NULL,whether_push_over = '0' WHERE tenancy_id = '" + params.optString("tenantId") + "' AND shop_id = '" + params.optString("shop_id") + "' AND channel = '" + params.optString("channel") + "'";
            update_third_id_null.add(update_third_item_id_null);
            //update_third_id_null.add(update_third_item_class_id_null);
            if(update_third_id_null.size()>0){
                String[] sql_Array = null;
                int size = update_third_id_null.size();
                sql_Array = (String[]) update_third_id_null.toArray(new String[size]);
                dao.getJdbcTemplate(params.optString("tenantId")).batchUpdate(sql_Array);
                logger.info("[更改菜品类别和菜品信息第三方编号为空的修改数据：]"+sql_Array.toString());
            }



            //批量修改菜品分类的映射信息
            if(update_third_item_class_id_list.size()>0){
                String[] sql_Array = null;
                int size = update_third_item_class_id_list.size();
                sql_Array = (String[]) update_third_item_class_id_list.toArray(new String[size]);
                dao.getJdbcTemplate(params.optString("tenantId")).batchUpdate(sql_Array);
                logger.info("[更改新美大外卖菜品类别映射信息推送状态修改数据：]"+sql_Array.toString());
            }

            //批量修改菜品的映射信息
            if(update_third_item_id_list.size()>0){
                String[] sql_Array = null;
                int size = update_third_item_id_list.size();
                sql_Array = (String[]) update_third_item_id_list.toArray(new String[size]);
                dao.getJdbcTemplate(params.optString("tenantId")).batchUpdate(sql_Array);
                logger.info("[更改新美大外卖菜品映射信息推送状态修改数据：]"+sql_Array.toString());
            }

            ccBusniessLogBean.setRequestId(requestId.toString());
            ccBusniessLogBean.setTenancyId(tenantId);
            ccBusniessLogBean.setCategory("cc");
            ccBusniessLogBean.setType("mapping");
            ccBusniessLogBean.setChannel(params.optString("channel"));
            ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
            ccBusniessLogBean.setCmd("com.tzx.cc.baidu.bo.imp.ShopServiceImpl:saveXmdwmDishAndDishCategoryMapping");
            ccBusniessLogBean.setRequestBody(params.toString());


            ccBusniessLogBean.setCreateTime(new Date().getTime());
            ccBusniessLogBean.setIsNormal("1");
            ccBusniessLogBean.setIsThird("0");

            ccBusniessLogBean.setThirdId("");
            ccBusniessLogBean.setTzxId(params.optString("class_id"));
            ccBusniessLogBean.setTzxName("");
            ccBusniessLogBean.setShopId(shopId);

            ccBusniessLogBean.setOperAction(DishOper.batchDishMapping.toString());

            ccBusniessLogBean.setResponseBody(rest.toString());


        } catch (Exception e) {
            rest.put("result", "no");
            rest.put("msg", e.getMessage());
            ccBusniessLogBean.setResponseBody(rest.toString());
            // TODO Auto-generated catch block
            e.printStackTrace();
        }finally {
            //KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
        }
        return rest;
    }

    @Override
    public JSONArray getXmdwmDishInfos(String tenantId, JSONObject params) throws Exception {

        int offset = 0;
        int limit = 200;
        JSONArray resultList = new JSONArray();
        for(int i = 1; i < 10; i++) {
            Map<String, String> paramMap = new HashMap<>();
            String shopId = params.optString("shop_id");
            String ePoiId = shopId+"@"+tenantId;
            paramMap.put("ePoiId",  ePoiId);
            paramMap.put("offset",  String.valueOf(offset));
            paramMap.put("limit",  String.valueOf(limit));
            String xmdResult = XmdWMUtils.execCmd(tenantId, shopId, XmdWMUtils.CMD_FOOD_QUERYLISTBYEPOIID, paramMap, XmdWMUtils.HTTP_REQUEST_GET);
            if(xmdResult != null && !"".equals(xmdResult)){
                JSONArray arrayResult = JSONObject.fromObject(xmdResult).optJSONArray("data");
                if(!arrayResult.isEmpty() && arrayResult.size() > 0) {
                    for (int y = 0; y < arrayResult.size(); y++) {
                        JSONObject jsonInfo = arrayResult.getJSONObject(y);
                        JSONObject json = new JSONObject();
                        json.put("dishId", jsonInfo.optInt("dishId", 0));
                        json.put("categoryName", jsonInfo.optString("categoryName"));
                        json.put("dishName", jsonInfo.optString("dishName"));
                        json.put("unit", jsonInfo.optString("unit"));
                        json.put("price", jsonInfo.optDouble("price", 0));
                        json.put("eDishCode", jsonInfo.optString("eDishCode"));
                        resultList.add(json);
                    }
                    offset = limit * i;
                }else{
                    break;
                }
            }else{
                break;
            }
        }
        return resultList;
    }
	
	@Override
	public JSONObject getShopCode(String tenantId, JSONObject params)
	{

		String shopId = params.optString("shop_id");
		BaiduManager	baiduManager= new BaiduManager(tenantId, shopId);
		JSONObject thirdPatyResult=baiduManager.getShopCode(tenantId, params);
		JSONObject result=null;
		if(thirdPatyResult!=null) {
			result=thirdPatyResult.optJSONObject("body");
		}

		return result;
	}
	
}
