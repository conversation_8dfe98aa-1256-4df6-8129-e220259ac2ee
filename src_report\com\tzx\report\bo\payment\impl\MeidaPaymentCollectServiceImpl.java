package com.tzx.report.bo.payment.impl;

import com.alibaba.fastjson.JSONObject;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.payment.service.MeidaPaymentCollectService;
import com.tzx.report.common.util.ReportExportUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2018/6/14
 */
@Service
public class MeidaPaymentCollectServiceImpl implements MeidaPaymentCollectService {

    private static final Logger log	= Logger.getLogger(MeidaPaymentCollectServiceImpl.class);

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Override
    public net.sf.json.JSONObject selectList(String tenancyId,net.sf.json.JSONObject paramJson) throws Exception {
        net.sf.json.JSONObject resultJson = new net.sf.json.JSONObject();

        //String merchantIds = getMerchantIds(storeId);
        int findType = 1;
        if(paramJson.containsKey("findType")){
            findType =paramJson.getInt("findType");
        }
        net.sf.json.JSONObject pageJson = new net.sf.json.JSONObject();
        pageJson.put("page",paramJson.getInt("page"));
        pageJson.put("rows",paramJson.getInt("rows"));

        String searchStores = "";
        String SelectStoreId = paramJson.optString("storeId");
        if(StringUtils.isNotBlank(SelectStoreId)){
            String[] stores = SelectStoreId.split(",");
            String storeIds = "";
            for(int i=0;i<stores.length;i++){
                storeIds+=",'"+stores[i]+"'";
            }
            storeIds = storeIds.substring(1);
            if(StringUtils.isNotBlank(storeIds)){
                searchStores = " and ppo.store_id in ("+storeIds+") ";
            }
        }
        String selected = "";
        if(paramJson.containsKey("selected")){
            selected = paramJson.opt("selected")+"";
        }
        StringBuffer footerSql = new StringBuffer();

        String groupBySql = "";
        StringBuffer sb = new StringBuffer();
        if(1==findType){
            sb.append("SELECT (case ppo.store_id when '0' then '总部' else o.org_full_name end) as org_full_name,xsd.merchant_id,xsd.trade_time as tradetime,substr(ppo.bill_num, 1,CASE WHEN POSITION ('wx_vip' IN ppo.bill_num) = 1 THEN (LENGTH (ppo.bill_num)) WHEN position('_' in ppo.bill_num)>0 THEN (position('_' in ppo.bill_num)-1) ");
            sb.append("WHEN position('@' in ppo.bill_num)>0 THEN (position('@' in ppo.bill_num)-1) ELSE length(ppo.bill_num) END)   as billNum,");
            sb.append(" (case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end) as poundage_fee,");
            sb.append("xsd.order_fee,xsd.settle_fee,xsd.out_trade_no,ppo.report_date::DATE as cut_day,o.id as storeid,xsd.pay_type as type, ");
            sb.append("ppo.total_amount as totalamount,ppo.final_state as status,ppo.create_time as createtime,xsd.pay_status as paystatus,xsd.payment_status,xsd.payment_finish_time ");
            sb.append("FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no=ppo.out_trade_no LEFT JOIN organ o ON ppo.store_id=o.id::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");

            footerSql.append("SELECT sum(xsd.order_fee) as order_fee,sum((case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end)) as poundage_fee,sum(xsd.settle_fee) as settle_fee,sum(s.total_amount) as totalamount ");
            footerSql.append("FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no=ppo.out_trade_no " +
                    "left join (select total_amount,out_trade_no from pos_payment_order where final_state=1) s on s.out_trade_no=xsd.out_trade_no "+
                    "LEFT JOIN organ o ON ppo.store_id=o.id::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");

            if(StringUtils.isNotBlank(searchStores)){
                sb.append(searchStores);
                footerSql.append(searchStores);
            }

            if(StringUtils.isNotBlank(selected)){
                sb.append(" and ppo.out_trade_no in (");
                footerSql.append(searchStores).append(" and ppo.out_trade_no in (");
                String s = "";
                String[] selects = selected.split(",");
                for(int i=0;i<selects.length;i++){
                    String select = selects[i];
                    s += ",'"+select+"'";
                }
                s = s.substring(1)+") ";
                sb.append(s);
                footerSql.append(searchStores).append(s);
            }

            pageJson.put("sort","xsd.trade_time");
            pageJson.put("order","desc");
        }else if(2 == findType){
            String type = paramJson.optString("type");
            String storeId = paramJson.optString("selectStoreId");
            if(StringUtils.isBlank(storeId)){
                storeId = "0";
            }
            if(StringUtils.isBlank(type)){
                sb.append("SELECT o.id as storeid,(case ppo.store_id when '0' then '总部' else o.org_full_name end) as org_full_name,null as merchant_id,null as cut_day,NULL as trade_time,NULL AS billNum,sum(xsd.order_fee) as order_fee,SUM((case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end)) as poundage_fee,");
                sb.append(" SUM(xsd.settle_fee) as settle_fee,NULL as out_trade_no,sum(s.total_amount) as totalamount  FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no left join (select total_amount,out_trade_no from pos_payment_order where final_state=1) s on s.out_trade_no=xsd.out_trade_no ");
                sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");

                footerSql.append("SELECT sum(xsd.order_fee) as order_fee,sum((case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end)) as poundage_fee,sum(xsd.settle_fee) as settle_fee,sum(s.total_amount) as totalamount ");
                footerSql.append("FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no=ppo.out_trade_no " +
                        "left join (select total_amount,out_trade_no from pos_payment_order where final_state=1) s on s.out_trade_no=xsd.out_trade_no "+
                        "LEFT JOIN organ o ON ppo.store_id=o.id::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");

                if(StringUtils.isNotBlank(searchStores)){
                    sb.append(searchStores);
                    footerSql.append(searchStores);
                }
                if(StringUtils.isNotBlank(selected)){
                    sb.append(" and ppo.store_id in (");
                    footerSql.append(" and ppo.store_id in (");
                    String s = "";
                    String[] selects = selected.split(",");
                    for(int i=0;i<selects.length;i++){
                        String select = selects[i];
                        s += ",'"+select+"'";
                    }
                    s = s.substring(1)+") ";
                    sb.append(s);
                    footerSql.append(s);
                }

                groupBySql = " GROUP BY o.id,ppo.store_id,o.org_full_name ";
                pageJson.put("sort","o.id");
                pageJson.put("order","desc");
            }else if(StringUtils.equals("2",type)){
                sb.append("SELECT o.id as storeid,(case ppo.store_id when '0' then '总部' else o.org_full_name end) as org_full_name,null as merchant_id,ppo.report_date::DATE as cut_day,NULL as trade_time,NULL AS billNum,sum(xsd.order_fee) as order_fee,SUM((case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end)) as poundage_fee,");
                sb.append(" SUM(xsd.settle_fee) as settle_fee,NULL as out_trade_no,sum(s.total_amount) as totalamount  FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no left join (select total_amount,out_trade_no from pos_payment_order where final_state=1) s on s.out_trade_no=xsd.out_trade_no ");
                sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");
                if(StringUtils.isNotBlank(storeId)){
                    sb.append(" and ppo.store_id="+storeId+"::TEXT ");
                }else {
                    sb.append(" and ppo.store_id not in (select id::TEXT from organ)");
                }

                groupBySql = " group by o.id,ppo.store_id,ppo.report_date::DATE";
                pageJson.put("sort","ppo.report_date::DATE");
                pageJson.put("order","desc");
            }else if(StringUtils.equals("3",type)){
                String cutDate = paramJson.getString("selectDate");
                sb.append("SELECT o.id as storeid ,(case ppo.store_id when '0' then '总部' else o.org_full_name end) as org_full_name,xsd.merchant_id,ppo.report_date::DATE as cut_day,xsd.trade_time as tradetime,substr(ppo.bill_num, 1,CASE WHEN POSITION ('wx_vip' IN ppo.bill_num) = 1 THEN (LENGTH (ppo.bill_num)) WHEN position('_' in ppo.bill_num)>0 THEN (position('_' in ppo.bill_num)-1) ");
                sb.append(" WHEN position('@' in ppo.bill_num)>0 THEN (position('@' in ppo.bill_num)-1) ELSE length(ppo.bill_num) END)   as billNum,");
                sb.append(" (case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end) as poundage_fee,");
                sb.append(" xsd.order_fee,xsd.settle_fee,xsd.out_trade_no,xsd.pay_type as type, ");
                sb.append("ppo.total_amount as totalamount,ppo.final_state as status,ppo.create_time as createtime,xsd.pay_status as paystatus,xsd.payment_status,xsd.payment_finish_time ");
                sb.append(" FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no ");
                sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");
                if(StringUtils.isNotBlank(storeId)){
                    sb.append(" and ppo.store_id="+storeId+"::TEXT ");
                }else {
                    sb.append(" and ppo.store_id not in (select id::TEXT from organ)");
                }
                sb.append(" and ppo.report_date::DATE='"+cutDate+"'::DATE ");
                pageJson.put("sort","xsd.trade_time");
                pageJson.put("order","desc");
            }

        }else if(3 == findType){
            String type =  paramJson.optString("type");
            String cutDate = paramJson.optString("selectDate");
            if(StringUtils.isBlank(type)){
                sb.append("SELECT null as org_full_name,null as merchant_id,ppo.report_date::DATE as cut_day,NULL as trade_time,NULL AS billNum,sum(xsd.order_fee) as order_fee,SUM((case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end)) as poundage_fee,");
                sb.append(" SUM(xsd.settle_fee) as settle_fee,NULL as out_trade_no,sum(s.total_amount) as totalamount FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no left join (select total_amount,out_trade_no from pos_payment_order where final_state=1) s on s.out_trade_no=xsd.out_trade_no ");
                sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");

                footerSql.append("SELECT sum(xsd.order_fee) as order_fee,sum((case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end)) as poundage_fee,sum(xsd.settle_fee) as settle_fee,sum(s.total_amount) as totalamount ");
                footerSql.append("FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no=ppo.out_trade_no " +
                        "left join (select total_amount,out_trade_no from pos_payment_order where final_state=1) s on s.out_trade_no=xsd.out_trade_no "+
                        "LEFT JOIN organ o ON ppo.store_id=o.id::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");

                if(StringUtils.isNotBlank(searchStores)){
                    sb.append(searchStores);
                    footerSql.append(searchStores);
                }
                if(StringUtils.isNotBlank(selected)){
                    sb.append(" and ppo.report_date::DATE::TEXT in (");
                    footerSql.append(" and ppo.report_date::DATE::TEXT in ( ");
                    String s = "";
                    String[] selects = selected.split(",");
                    for(int i=0;i<selects.length;i++){
                        String select = selects[i];
                        s += ",'"+select+"'";
                    }
                    s = s.substring(1)+") ";
                    sb.append(s);
                    footerSql.append(s);
                }

                groupBySql = "GROUP BY ppo.report_date::DATE ";
                pageJson.put("sort","ppo.report_date::DATE");
                pageJson.put("order","desc");
            }else if(StringUtils.equals("2",type)){
                sb.append("SELECT o.id as storeid ,(case ppo.store_id when '0' then '总部' else o.org_full_name end) as org_full_name,null as merchant_id,ppo.report_date::DATE as cut_day,NULL as trade_time,NULL AS billNum,sum(xsd.order_fee) as order_fee,SUM((case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end)) as poundage_fee,");
                sb.append(" SUM(xsd.settle_fee) as settle_fee,NULL as out_trade_no,sum(s.total_amount) as totalamount FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no left join (select total_amount,out_trade_no from pos_payment_order where final_state=1) s on s.out_trade_no=xsd.out_trade_no ");
                sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");
                sb.append(" and ppo.report_date::DATE::TEXT='"+cutDate+"' ");
                if(StringUtils.isNotBlank(searchStores)){
                    sb.append(searchStores);
                }
                groupBySql = " group by o.id,ppo.store_id,ppo.report_date::DATE";
                pageJson.put("sort","o.id");
                pageJson.put("order","desc");
            }else if(StringUtils.equals("3",type)){
                String storeId = paramJson.optString("selectStoreId");
                if(StringUtils.isBlank(storeId)){
                    storeId = "0";
                }
                sb.append("SELECT o.id as storeid ,(case ppo.store_id when '0' then '总部' else o.org_full_name end) as org_full_name,xsd.merchant_id,ppo.report_date::DATE as cut_day,xsd.trade_time as tradetime,substr(ppo.bill_num, 1,CASE WHEN POSITION ('wx_vip' IN ppo.bill_num) = 1 THEN (LENGTH (ppo.bill_num)) WHEN position('_' in ppo.bill_num)>0 THEN (position('_' in ppo.bill_num)-1) ");
                sb.append(" WHEN position('@' in ppo.bill_num)>0 THEN (position('@' in ppo.bill_num)-1) ELSE length(ppo.bill_num) END)   as billNum,");
                sb.append(" (case xsd.pay_status when '2' then concat('-',xsd.poundage_fee)::NUMERIC  else xsd.poundage_fee end) as poundage_fee,");
                sb.append(" xsd.order_fee,xsd.settle_fee,xsd.out_trade_no,xsd.pay_type as type, ");
                sb.append("ppo.total_amount as totalamount,ppo.final_state as status,ppo.create_time as createtime,xsd.pay_status as paystatus,xsd.payment_status,xsd.payment_finish_time ");
                sb.append(" FROM xmd_settlement_detail xsd JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no ");
                sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1 AND ppo.report_date is not NULL AND ppo.report_date<>'' ");
                if(StringUtils.isNotBlank(storeId)){
                    sb.append(" and ppo.store_id="+storeId+"::TEXT ");
                }else {
                    sb.append(" and ppo.store_id not in (select id::TEXT from organ)");
                }
                sb.append(" and ppo.report_date::DATE::TEXT='"+cutDate+"' ");
                pageJson.put("sort","xsd.trade_time");
                pageJson.put("order","desc");
            }

        }

        if(paramJson.containsKey("startDate") && StringUtils.isNotBlank(paramJson.getString("startDate"))){
            String startDate = paramJson.getString("startDate");
            sb.append(" AND ppo.create_time>='"+startDate+"'::TIMESTAMP ");
            if(StringUtils.isNotBlank(footerSql.toString())){
                footerSql.append(" AND ppo.create_time>='"+startDate+"'::TIMESTAMP ");
            }
        }
        if(paramJson.containsKey("endDate")  && StringUtils.isNotBlank(paramJson.getString("endDate"))){
            String endDate = paramJson.getString("endDate");
            sb.append(" AND ppo.create_time<='"+endDate+"'::TIMESTAMP ");
            if(StringUtils.isNotBlank(footerSql.toString())) {
                footerSql.append(" AND ppo.create_time<='" + endDate + "'::TIMESTAMP ");
            }
        }

        if(paramJson.containsKey("billNum") && StringUtils.isNotBlank(paramJson.getString("billNum"))){
            sb.append(" and ppo.bill_num like concat('%','"+paramJson.getString("billNum")+"','%') ");
            if(StringUtils.isNotBlank(footerSql.toString())) {
                footerSql.append(" and ppo.bill_num like concat('%','" + paramJson.getString("billNum") + "','%') ");
            }
        }

        if(paramJson.containsKey("selectType") && StringUtils.isNotBlank(paramJson.getString("selectType"))){
            String selectType = paramJson.getString("selectType");
            if(!StringUtils.equals("-1",selectType)){
                String[] payTypes = selectType.split(",");
                String paytype = "";
                for(int i=0;i<payTypes.length;i++){
                    paytype += ",'"+payTypes[i]+"'";
                }
                paytype = paytype.substring(1);
                sb.append(" and xsd.pay_type in ("+paytype+") ");
                if(StringUtils.isNotBlank(footerSql.toString())) {
                    footerSql.append(" and xsd.pay_type in (" + paytype + ") ");
                }
            }

        }

        sb.append(groupBySql);
        if(StringUtils.isNotBlank(footerSql.toString())){
            footerSql.append(groupBySql);
        }
        long total = this.dao.countSql(null,sb.toString());
        List<net.sf.json.JSONObject> list = this.dao.query4Json(null,this.dao.buildPageSql(pageJson,sb.toString()));

        resultJson.put("rows",list);
        resultJson.put("total",total);

        if(StringUtils.isNotBlank(footerSql.toString()) && StringUtils.isBlank(selected)){
            List<net.sf.json.JSONObject> footerList =new ArrayList<net.sf.json.JSONObject>();
            net.sf.json.JSONObject jsonCount = new net.sf.json.JSONObject() ;

            List<net.sf.json.JSONObject> countList = this.dao.query4Json(null,footerSql.toString());
            double totalamount = 0.00;
            double order_fee = 0.00;
            double poundage_fee = 0.00;
            double settle_fee = 0.00;

            if(countList!=null && countList.size()>0){
                net.sf.json.JSONObject countJson = countList.get(0);
                if(countJson.containsKey("totalamount") && countJson.get("totalamount")!=null && !"null".equals(countJson.optString("totalamount"))){
                    totalamount = new BigDecimal(countJson.optString("totalamount")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                }
                if(countJson.containsKey("order_fee") && countJson.get("order_fee")!=null && !"null".equals(countJson.optString("order_fee"))){
                    order_fee = new BigDecimal(countJson.optString("order_fee")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                }
                if(countJson.containsKey("poundage_fee") && countJson.get("poundage_fee")!=null && !"null".equals(countJson.optString("poundage_fee"))){
                    poundage_fee = new BigDecimal(countJson.optString("poundage_fee")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                }
                if(countJson.containsKey("settle_fee") && countJson.get("settle_fee")!=null && !"null".equals(countJson.optString("settle_fee"))){
                    settle_fee = new BigDecimal(countJson.optString("settle_fee")).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                }
            }

            jsonCount.put("totalamount", totalamount);
            jsonCount.put("order_fee",order_fee);
            jsonCount.put("poundage_fee",poundage_fee);
            jsonCount.put("settle_fee", settle_fee);

            jsonCount.put("org_full_name", "合计");
            footerList.add(jsonCount);
            resultJson.put("footer", footerList);
        }
        return resultJson;
    }


    public void convertJson(net.sf.json.JSONObject  json){
        if(json.containsKey("type")){
            String type = json.optString("type");
            if(StringUtils.isNotBlank(type)){
                if(StringUtils.equals("ali_barcode_pay",type)){
                    json.put("typeName","支付宝条码支付");
                }else if(StringUtils.equals("ali_scan_pay",type)){
                    json.put("typeName","支付宝扫码支付");
                }else if(StringUtils.equals("wx_barcode_pay",type)){
                    json.put("typeName","微信条码支付");
                }else if(StringUtils.equals("wx_scan_pay",type)){
                    json.put("typeName","微信扫码支付");
                }
            }
        }

        if(StringUtils.isNotBlank(json.optString("paystatus"))){
            String paystatus = json.optString("paystatus");
            if("0".equals(paystatus)){
                json.put("paystatusName","支付");
            }else if("1".equals(paystatus)){
                json.put("paystatusName","撤销");
            }else if("2".equals(paystatus)){
                json.put("paystatusName","退款");
            }else if("4".equals(paystatus)){
                json.put("paystatusName","押金扣款");
            }else if("6".equals(paystatus)){
                json.put("paystatusName","押金扣款");
            }else if("7".equals(paystatus)){
                json.put("paystatusName","银行退单");
            }else if("8".equals(paystatus)){
                json.put("paystatusName","隔日退款");
            }else if("9".equals(paystatus)){
                json.put("paystatusName","调单暂缓");
            }else if("10".equals(paystatus)){
                json.put("paystatusName","调单暂缓");
            }else if("16".equals(paystatus)){
                json.put("paystatusName","活动_支付");
            }else if("17".equals(paystatus)){
                json.put("paystatusName","活动_撤销");
            }else if("18".equals(paystatus)){
                json.put("paystatusName","活动_退款");
            }
        }

        if(StringUtils.isNotBlank(json.optString("status"))){
            String status = json.optString("status");
            if("1".equals(status)){
                json.put("statusName","支付成功");
            }else if("2".equals(status)){
                json.put("statusName","处理中");
            }else if("3".equals(status)){
                json.put("statusName","未知");
            }else if("4".equals(status)){
                json.put("statusName","已退款");
            }else if("5".equals(status)){
                json.put("statusName","退款中");
            }else if("6".equals(status)){
                json.put("statusName","已取消");
            }else if("7".equals(status)){
                json.put("statusName","退款失败");
            }else if("8".equals(status)){
                json.put("statusName","取消中");
            }else if("9".equals(status)){
                json.put("statusName","取消失败");
            }
        }
    }

    @Override
    public HSSFWorkbook exportAccountData(String attribute, net.sf.json.JSONObject p, HSSFWorkbook workBook) throws Exception {
        Integer rowNum=2;
        Integer jin=0;
        net.sf.json.JSONObject paramData =new net.sf.json.JSONObject();
        paramData.put("rowNum", rowNum);
        paramData.put("jin",jin);
        paramData.put("strtIndex",1);
        net.sf.json.JSONObject findResult= selectList(attribute, p);
        List<net.sf.json.JSONObject> list =(List<net.sf.json.JSONObject>) findResult.opt("rows");
        Integer stratIndex =null;
        net.sf.json.JSONObject out1Result =null;
        net.sf.json.JSONObject out1Result2 =null;
        net.sf.json.JSONObject out1Result3 =null;

        String findType = p.optString("findType");

        //创建sheet 表格   同时还可以设置名字!
        HSSFSheet sheet1=workBook.createSheet("美大支付汇总报表");
        String [] listTitleName ={};
        String [] dataName ={};//{"item_num","item_name","dl_name","xl_name","sd","sale_num","sale_amount","item_amount_num","item_amount","back_num","back_money","free_num","free_money","zkzr","real_num","real_amount","ts_num","ts_amount","wm_num","wm_amount","wd_num","wd_amount"};
        String [] dataType ={};//{"String","String","String","String","String","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00"};

        if(StringUtils.equals("3",findType)){
            listTitleName = new String[]{"交易日期","交易机构","新美大ID","账单编号","支付类型","订单金额","交易状态","交易时间","原订单金额","手续费","结算金额","打款状态","打款时间","订单唯一编号","交易状态","交易时间"};
            dataName = new String[]{"cut_day","org_full_name","merchant_id","billnum","typeName","totalamount","statusName","trade_time","order_fee","poundage_fee","settle_fee","payment_status","payment_finish_time","out_trade_no","paystatusName","tradetime"};
            dataType = new String[]{"String","String","String","String","String","0.00","String","String","0.00","0.00","0.00","String","String","String","String","String"};
        }else {
            listTitleName = new String[]{"交易机构","交易日期","新美大ID","账单编号","支付类型","订单金额","交易状态","交易时间","原订单金额","手续费","结算金额","打款状态","打款时间","订单唯一编号","交易状态","交易时间"};
            dataName = new String[]{"org_full_name","cut_day","merchant_id","billnum","typeName","totalamount","statusName","trade_time","order_fee","poundage_fee","settle_fee","payment_status","payment_finish_time","out_trade_no","paystatusName","tradetime"};
            dataType = new String[]{"String","String","String","String","String","0.00","String","String","0.00","0.00","0.00","String","String","String","String","String"};
        }
        if(list.size()>0){
            for(net.sf.json.JSONObject json:list){

                convertJson(json);
                // 调用到处方法；
                out1Result = ReportExportUtils.out1(json,workBook,sheet1,listTitleName,dataName,dataType,paramData);
                paramData.put("rowNum", out1Result.opt("rowNum"));
                paramData.put("jin", out1Result.optInt("jin"));
                stratIndex=out1Result.optInt("rowNum");
                if(StringUtils.equals("2",findType)){
                    p.put("type", 2);
                    if(json.containsKey("storeid") && !StringUtils.equals("null",json.optString("storeid"))){
                        p.put("selectStoreId", json.optString("storeid"));
                    }else {
                        p.put("selectStoreId", "");
                    }

                    net.sf.json.JSONObject findResult2= selectList(attribute, p);
                    // 机构-日期 汇总 第二级
                    List<net.sf.json.JSONObject> list2 =(List<net.sf.json.JSONObject>) findResult2.opt("rows");
                    for(net.sf.json.JSONObject json2 : list2) {
                        json2.put("org_full_name",null);
                        convertJson(json2);
                        // 调用到处方法；
                        out1Result2 =ReportExportUtils.out1(json2,workBook,sheet1,listTitleName,dataName,dataType,paramData);
                        paramData.put("rowNum", out1Result2.opt("rowNum"));
                        paramData.put("jin", out1Result2.optInt("jin"));

                        p.put("type",3);
                        p.put("selectDate",json2.optString("cut_day"));
                        net.sf.json.JSONObject findResult3= selectList(attribute, p);
                        //第三级
                        List<net.sf.json.JSONObject> list3 =(List<net.sf.json.JSONObject>) findResult3.opt("rows");
                        for(net.sf.json.JSONObject json3:list3){
                            json3.put("org_full_name",null);
                            json3.put("cut_day",null);

                            convertJson(json3);

                            out1Result3 =ReportExportUtils.out1(json3,workBook,sheet1,listTitleName,dataName,dataType,paramData);
                            paramData.put("rowNum", out1Result3.opt("rowNum"));
                            paramData.put("jin", out1Result3.optInt("jin"));
                        }
                    }
                    sheet1.groupRow(out1Result.optInt("rowNum"),out1Result2.optInt("rowNum"));
                    sheet1.groupRow(stratIndex,out1Result3.optInt("rowNum"));
                }else if(StringUtils.equals("3",findType)){
                    p.put("type", 2);
                    p.put("selectDate",json.optString("cut_day"));

                    net.sf.json.JSONObject findResult2= selectList(attribute, p);
                    // 日期-机构 汇总 第二级
                    List<net.sf.json.JSONObject> list2 =(List<net.sf.json.JSONObject>) findResult2.opt("rows");
                    for(net.sf.json.JSONObject json2 : list2) {
                        String cutDay = json2.getString("cut_day");
                        json2.put("cut_day",null);
                        // 调用到处方法；
                        out1Result2 =ReportExportUtils.out1(json2,workBook,sheet1,listTitleName,dataName,dataType,paramData);
                        paramData.put("rowNum", out1Result2.opt("rowNum"));
                        paramData.put("jin", out1Result2.optInt("jin"));

                        p.put("type",3);
                        p.put("selectDate",cutDay);
                        if(json2.containsKey("storeid") && !StringUtils.equals("null",json2.optString("storeid"))){
                            p.put("selectStoreId", json2.optString("storeid"));
                        }else {
                            p.put("selectStoreId", "");
                        }
                        net.sf.json.JSONObject findResult3= selectList(attribute, p);
                        //第三级
                        List<net.sf.json.JSONObject> list3 =(List<net.sf.json.JSONObject>) findResult3.opt("rows");
                        for(net.sf.json.JSONObject json3:list3){
                            json3.put("org_full_name",null);
                            json3.put("cut_day",null);

                            convertJson(json3);
                            out1Result3 =ReportExportUtils.out1(json3,workBook,sheet1,listTitleName,dataName,dataType,paramData);
                            paramData.put("rowNum", out1Result3.opt("rowNum"));
                            paramData.put("jin", out1Result3.optInt("jin"));
                        }
                    }
                    sheet1.groupRow(out1Result2.optInt("rowNum"),out1Result3.optInt("rowNum"));
                    sheet1.groupRow(stratIndex,out1Result3.optInt("rowNum"));
                }
            }
        }
        if(StringUtils.equals("1",findType)){
            sheet1.groupRow(1,out1Result.optInt("rowNum"));
        }
        sheet1.setRowSumsBelow(false);
        sheet1.setRowSumsRight(false);

       // HSSFCellStyle style =  ReportExportUtils.getTitleStyle(workBook);
        return workBook;
    }

    public String getMerchantIds(String storeIds){
        StringBuilder msb = new StringBuilder("select DISTINCT merchant_id as poiid from sys_payment_account_config where store_id in("+storeIds+") and  payment_channel='6' and valid_state='1'");
        String merchantIds = "";
        try {
            List<net.sf.json.JSONObject> list =  this.dao.query4Json(null,msb.toString());
            if(list.size()>0)
            {
                for(net.sf.json.JSONObject poiJson:list){
                    String poiid = poiJson.getString("poiid");
                    if(StringUtils.isNotBlank(poiid) && !"null".equals(poiid)){
                        merchantIds +=",'"+poiid+"'";
                    }
                }
                merchantIds = merchantIds.substring(1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return merchantIds;
    }

    @Override
    public net.sf.json.JSONObject selectSecondList(JSONObject paramJson) throws Exception {
        net.sf.json.JSONObject resultJson = new net.sf.json.JSONObject();

//        StringBuffer sb = new StringBuffer();
//        String merchantIds = getMerchantIds(paramJson.getString("storeId"));
//        int findType = 2;
//        if(paramJson.containsKey("findType")){
//            findType =paramJson.getInteger("findType");
//        }
//        String groupBySql = "";
//        net.sf.json.JSONObject pageJson = new net.sf.json.JSONObject();
//        pageJson.put("page",paramJson.getInteger("page"));
//        pageJson.put("rows",paramJson.getInteger("rows"));
//
//        if(2 == findType){
//            String type = paramJson.getString("type");
//            String storeId = paramJson.getString("selectStoreId");
//            if(StringUtils.isNotBlank(type)){
//                //第二級显示机构日期汇总
//                if("2".equals(type)){
//                    sb.append("SELECT o.id,o.org_full_name,null as merchant_id,xsd.cut_day,NULL as trade_time,NULL AS billNum,sum(xsd.order_fee) as order_fee,SUM(xsd.poundage_fee) as poundage_fee,");
//                    sb.append(" SUM(xsd.settle_fee) as settle_fee,NULL as out_trade_no FROM xmd_settlement_detail xsd LEFT JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no ");
//                    sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1");
//                    if(StringUtils.isNotBlank(storeId)){
//                        sb.append(" and ppo.store_id="+storeId+"::TEXT ");
//                    }
//
//                    groupBySql = " group by o.id,xsd.cut_day";
//                    pageJson.put("sort","xsd.cut_day");
//                    pageJson.put("order","desc");
//                }else {//三级显示机构日期的明细
//                    String cutDate = paramJson.getString("selectDate");
//                    sb.append("SELECT o.id,o.org_full_name,xsd.merchant_id,xsd.cut_day,xsd.trade_time,substr(ppo.bill_num, 1,CASE WHEN position('_' in ppo.bill_num)>0 THEN (position('_' in ppo.bill_num)-1) ");
//                    sb.append(" WHEN position('@' in ppo.bill_num)>0 THEN (position('@' in ppo.bill_num)-1) ELSE length(ppo.bill_num) END)   as billNum,");
//                    sb.append(" xsd.order_fee,xsd.poundage_fee,xsd.settle_fee,xsd.out_trade_no ");
//                    sb.append(" FROM xmd_settlement_detail xsd LEFT JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no ");
//                    sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1");
//                    if(StringUtils.isNotBlank(storeId)){
//                        sb.append(" and ppo.store_id="+storeId+"::TEXT ");
//                    }
//                    sb.append(" and xsd.cut_day='"+cutDate+"' ");
//                    pageJson.put("sort","xsd.trade_time");
//                    pageJson.put("order","desc");
//                }
//            }
//        }else {
//            String type = paramJson.getString("type");
//            String cutDate = paramJson.getString("selectDate");
//            if(StringUtils.isNotBlank(type)){
//                //二级
//                if(StringUtils.equals("2",type)){
//                    sb.append("SELECT o.id,o.org_full_name,null as merchant_id,xsd.cut_day,NULL as trade_time,NULL AS billNum,sum(xsd.order_fee) as order_fee,SUM(xsd.poundage_fee) as poundage_fee,");
//                    sb.append(" SUM(xsd.settle_fee) as settle_fee,NULL as out_trade_no FROM xmd_settlement_detail xsd LEFT JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no ");
//                    sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1");
//                    sb.append(" and xsd.cut_day='"+cutDate+"' ");
//
//                    groupBySql = " group by o.id,xsd.cut_day";
//                    pageJson.put("sort","o.id");
//                    pageJson.put("order","desc");
//                }else {
//                    String storeId = paramJson.getString("selectStoreId");
//                    sb.append("SELECT o.id,o.org_full_name,xsd.merchant_id,xsd.cut_day,xsd.trade_time,substr(ppo.bill_num, 1,CASE WHEN position('_' in ppo.bill_num)>0 THEN (position('_' in ppo.bill_num)-1) ");
//                    sb.append(" WHEN position('@' in ppo.bill_num)>0 THEN (position('@' in ppo.bill_num)-1) ELSE length(ppo.bill_num) END)   as billNum,");
//                    sb.append(" xsd.order_fee,xsd.poundage_fee,xsd.settle_fee,xsd.out_trade_no ");
//                    sb.append(" FROM xmd_settlement_detail xsd LEFT JOIN pos_payment_order ppo ON xsd.out_trade_no = ppo.out_trade_no ");
//                    sb.append(" LEFT JOIN organ o ON ppo.store_id = o.ID::TEXT where 1=1");
//                    if(StringUtils.isNotBlank(storeId)){
//                        sb.append(" and ppo.store_id="+storeId+"::TEXT ");
//                    }
//                    sb.append(" and xsd.cut_day='"+cutDate+"' ");
//                    pageJson.put("sort","xsd.trade_time");
//                    pageJson.put("order","desc");
//                }
//            }
//        }
//
//        if(paramJson.containsKey("startDate") && StringUtils.isNotBlank(paramJson.getString("startDate"))){
//            String startDate = paramJson.getString("startDate");
//            sb.append(" AND xsd.trade_time::DATE>='"+startDate+"'::DATE");
//        }
//        if(paramJson.containsKey("endDate")  && StringUtils.isNotBlank(paramJson.getString("endDate"))){
//            String endDate = paramJson.getString("endDate");
//            sb.append(" AND xsd.trade_time::DATE<='"+endDate+"'::DATE");
//        }
//        if(StringUtils.isNotBlank(merchantIds)){
//            sb.append(" and xsd.merchant_id in ("+merchantIds+") ");
//        }
//
//        if(paramJson.containsKey("billNum") && StringUtils.isNotBlank(paramJson.getString("billNum"))){
//            sb.append(" and ppo.bill_num like concat('%','"+paramJson.getString("billNum")+"','%') ");
//        }
//
//        if(paramJson.containsKey("selectType") && StringUtils.isNotBlank(paramJson.getString("selectType"))){
//            String selectType = paramJson.getString("selectType");
//            if("-1".equals(selectType.trim())){
//                sb.append(" and xsd.pay_type in ('wx_barcode_pay','ali_barcode_pay','wx_scan_pay','ali_scan_pay')");
//            }else if("0".equals(selectType.trim())){
//                sb.append(" and xsd.pay_type in ('ali_barcode_pay','ali_scan_pay')");
//            }else if("1".equals(selectType.trim())){
//                sb.append(" and xsd.pay_type in ('wx_barcode_pay','wx_scan_pay')");
//            }
//        }
//        sb.append(groupBySql);
//
//        long total = this.dao.countSql(null,sb.toString());
//        List<net.sf.json.JSONObject> list = this.dao.query4Json(null,this.dao.buildPageSql(pageJson,sb.toString()));
//
//        resultJson.put("rows",list);
//        resultJson.put("total",total);
        return resultJson;
    }
}
