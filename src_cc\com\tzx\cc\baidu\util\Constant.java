package com.tzx.cc.baidu.util;


/**常量类
 * <AUTHOR>
 *
 */
public class Constant {
	
	/**
	 * 百度外卖api
	 */
	public static final String BAIDU_API_URL=com.tzx.framework.common.constant.Constant.systemMap.get("baidu_api_urL");
	/**
	 * 美团外卖api
	 */
	public static final String MEITUAN_API_URL=com.tzx.framework.common.constant.Constant.systemMap.get("meituan_api_url");
	/**
	 * 饿了么api
	 */
	public static final String ELE_API_URL=com.tzx.framework.common.constant.Constant.systemMap.get("ele_api_url");
	/**
	 * 易吃平台api
	 */
	public static final String YICHI_API_URL=com.tzx.framework.common.constant.Constant.systemMap.get("yichi_address");
	/**
	 * 大众点评api
	 */
	public static final String DP_API_URL=com.tzx.framework.common.constant.Constant.systemMap.get("dp_api_url");
	/**
	 * 百度外卖渠道代码
	 */
	public static final String BAIDU_CHANNEL="BD06";
	/**
	 * 大众点评渠道代码
	 */
	public static final String DIANPING_CHANNEL="DP07";
	/**
	 * 美团外卖渠道代码
	 */
	public static final String MEITUAN_CHANNEL="MT08";
	/**
	 * 微信渠道代码
	 */
	public static final String WECHAT_CHANNEL = "WM10";
	/**
	 * 饿了吗渠道代码
	 */
	public static final String ELE_CHANNEL = "EL09";
	/**
	 * CC渠道代码
	 */
	public static final String CC_CHANNEL = "CC04";
	/**
	 * 默认门店渠道代码
	 */
	public static final String MD_CHANNEL = "MD01";
	/**
	 * 易吃渠道代码
	 */
	public static final String YICHI_CHANNEL = "YC09";
	
	/**
	 * 微店外卖渠道代码
	 */
	public static final String WDWM_CHANEL = "WX02";
	
    public static final String XMD_CHANNEL ="XMD";
    
    /**
     * 新美大外卖渠道代码
     */
    public static final String XMDWM_CHANNEL ="MT11";
	
	/**
	 * 易吃平台下线
	 */
	public static final String YICHI_SHOP_OFFLINE="0";
	
	/**
	 * 易吃平台开业
	 */
	public static final String YICHI_SHOP_OPEN="1";
	
	/**
	 * 易吃平台停业
	 */
	public static final String YICHI_SHOP_CLOSE="2";
	/**
	 * 百度外卖下线
	 */
	public static final String BAIDU_SHOP_OFFLINE="0";
	
	/**
	 * 百度外卖开业
	 */
	public static final String BAIDU_SHOP_OPEN="1";
	
	/**
	 * 百度外卖停业
	 */
	public static final String BAIDU_SHOP_CLOSE="2";
	
	/**
	 * 新美大 团购闪惠 门店绑定 状态
	 */
	public static final String XMD_SHOP_STATE_BIND = "1";
	
	/**
	 * 新美大 团购闪惠 门店解绑 状态
	 */
	public static final String XMD_SHOP_STATE_RELEASEBIND = "0";
	
	/**
	 * 错误信息0x1
	 */
	public static final String MSG_1="系统异常！";
	
	public static final String CATEGORY1="餐饮";
	
	/**
	 * Http 请求方法
	 */
	public static final String HTTP_GET="get",HTTP_PUT="put",HTTP_POST="post",HTTP_DELETE="delete";

    /**
     * 第三方优惠卷状态
     */
    public static final String COUPON_STATUS_PREPAE="0", COUPON_STATUS_CONSUME="1", COUPON_STATUS_CANCEL="2";

    /**
     * 配送方式(1:平台配送 2：自配送 3：众包配送)
     */
    public static final int THIRD_DELIVERY=1,SELF_DELIVERY=2,PARTTEN_DELIVERY=3;
    
    /**
     * 饿了么接口版本号（自定义）
     */
    public static final String ELM_API_VERSION_V2="2";
    
    /**
     * KafKa集群服务地址
     */
    public static final String KAFKA_SERVER_URL=com.tzx.framework.common.constant.Constant.systemMap.get("zookeeper.connect");
    /**
     * KafKa的TOPIC
     */
    public static final String KAFKA_TOPIC=com.tzx.framework.common.constant.Constant.systemMap.get("kafka_topic");
    
    /**
     * KafKa集群地址
     */
    public static final String KAFKA_META=com.tzx.framework.common.constant.Constant.systemMap.get("metadata.broker.list");
    /**
     * KafKa的consumer auto.offset.reset
     */
    public static final String KAFKA_CONSUMER_RESET=com.tzx.framework.common.constant.Constant.systemMap.get("auto.offset.reset");
    /**
     * KafKa的consumer group.id
     */
    public static final String KAFKA_CONSUMER_GROUP_ID=com.tzx.framework.common.constant.Constant.systemMap.get("group.id");
    /**
     * KafKa的consumer enable.auto.commit
     */
    public static final String KAFKA_CONSUMER_AUTO_COMMIT=com.tzx.framework.common.constant.Constant.systemMap.get("enable.auto.commit");
    /**
     * KafKa的consumer auto.commit.interval.ms
     */
    public static final String KAFKA_CONSUMER_INTERVAL_MS=com.tzx.framework.common.constant.Constant.systemMap.get("auto.commit.interval.ms");
    
    /**
     * KafKa的kafka_open
     */
    public static final String KAFKA_OPEN=com.tzx.framework.common.constant.Constant.systemMap.get("kafka_open");
    
    /**
     * KafKa的order的日志信息
     */
    public static final String ORDER_LOG=com.tzx.framework.common.constant.Constant.systemMap.get("order_log");
    
    /**
     * redis的缓存 tenantIds 键
     */
    public static final String KEY_COMMENT_TENANTS="KEY_COMMENT_TENANTS";

	public static final String DELIVER_DATA_TYPE = "1";
	public static final int BUSINESS_TYPE = 3;
	public static final int BUSINESS_TYPE_FOUR = 4;
	/**
	 * 订单推送键值
	 */
	public static final String PUSH_PLATFORM_MODE = "PUSH_PLATFORM_MODE";

	/**
	 * 系统参数
	 */
	public static final String SYS_PARAM = "SYS";
	
	/**
	 * 系统参数(多品牌、单品牌)
	 */
	public static final String SYS_PARAM_BRAND = "IS_MULTIBRAND";
}
