package com.tzx.cc.datasync.bo.util.strategy;

import com.tzx.cc.datasync.bo.dto.DataTransferDaoHelper;
import com.tzx.cc.datasync.bo.dto.PlanetVersionDataTransferDao;
import com.tzx.cc.datasync.bo.util.DriverUtils;
import com.tzx.cc.datasync.bo.util.SynIdUtils;
import com.tzx.cc.datasync.bo.util.SynUtils;
import com.tzx.cc.datasync.bo.util.TempTableUtils;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.util.StringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 价格明细比较器，通过item_unit_id，price_system,price进行比较
 * 从rif中取到数据后会向其他渠道都复制一份。
 * Created by XUGY on 2017-02-28.
 */
public class PriceSystemDetailsCompareModified implements CompareModified {
    /**
     * logger
     */
    private static final Logger logger = Logger.getLogger(PriceSystemDetailsCompareModified.class);

    @Override
    public Object compare(Object... objs) throws Exception {
        String tenantId = (String) objs[0];
        Map<String, Map<String, Object>> mapIds = (Map<String, Map<String, Object>>) objs[1];
        List<JSONObject> rifData = (List<JSONObject>) objs[2];
        String fromTable = (String) objs[3];
        List<JSONObject> dic_result_list = (List<JSONObject>) objs[4];
        String sql = StringUtils.EMPTY;
        if(objs.length>5) {
            sql = (String) objs[5];
        }

        String delSql = StringUtils.EMPTY;
        if(objs.length>6) {
            delSql = (String) objs[6];
        }

        Map<String, List<JSONObject>> resultMap = new HashMap<String, List<JSONObject>>();
        if (rifData == null || rifData.size() == 0) {
            return resultMap;
        }
        processData(mapIds, rifData, dic_result_list);

        //创建临时表 存储同步过来的数据  格式同源库
        String createTempTable = TempTableUtils.createTempTable(tenantId, rifData, fromTable);

        try {
            //将数据插入临时表中
            TempTableUtils.insertTempTable(tenantId, createTempTable, rifData);
            String chanelStr = SynUtils.getChanelStr(dic_result_list);
            List<JSONObject> updateList = getUpdateData(tenantId, fromTable, createTempTable,chanelStr,sql);
            List<JSONObject> insertList = getInsertData(tenantId, fromTable, createTempTable,chanelStr,sql);
            List<JSONObject> deleteList = getDeleteData(tenantId, fromTable, createTempTable,chanelStr,delSql);
            resultMap.put("add", insertList);
            resultMap.put("update", updateList);
            resultMap.put("delete", deleteList);
        } catch (Exception e) {
            logger.error(e);
            throw new Exception(e);
        } finally {
            TempTableUtils.dropTempTable(tenantId, createTempTable);
        }
        return resultMap;
    }

    private void processData(Map<String, Map<String, Object>> mapIds, List<JSONObject> rifData
            , List<JSONObject> dic_result_list) {
        String item_unit_id_temp = StringUtils.EMPTY;
        String price_system_temp = StringUtils.EMPTY;
        for (JSONObject json : rifData) {
            item_unit_id_temp = SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_unit", json.optString("item_id").trim());
            price_system_temp = SynIdUtils.getIdBytableFakeId(mapIds, "hq_price_system", json.optString("price_system").trim());
            json.put("item_unit_id_temp", item_unit_id_temp);
            json.put("price_system_temp", price_system_temp);
        }
        SynUtils.addQd(rifData,dic_result_list);
    }

    /**
     * 获取rif到saas要删除的数据
     *
     * @param tenantId
     * @param fromTable
     * @param createTempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getDeleteData(String tenantId, String fromTable,
                                           String createTempTable,String chanelStr,String assistSql) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }
        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();

        sql.append("select dest.* from ");
        sql.append(destName);
        sql.append(" dest left join ").append(createTempTable);
        sql.append(" sou on dest.chanel = sou.chanel");
        sql.append(" and to_number(sou.item_unit_id_temp,'9999999999') = dest.item_unit_id");
        sql.append(" and to_number(sou.price_system_temp,'9999999999') = dest.price_system");
        sql.append(" where sou.id is null and sou.id is not null");
        sql.append(" and sou.chanel in ('");
        sql.append(chanelStr);
        sql.append("')" );
        sql.append(assistSql);

        logger.info("价格体系明细与rif对比删除数据sql为");
        logger.info(sql.toString());
        return TempTableUtils.executeQuery(sql.toString());

    }

    /**
     * 获取rif到saas要新增的数据
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getInsertData(String tenantId, String fromTable,
                                           String tempTable,String chanelStr,String assistSql) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }
        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();

        sql.append("select sou.* from ");
        sql.append(tempTable);
        sql.append(" sou left join ").append(destName);
        sql.append(" dest on sou.chanel = dest.chanel ");
        sql.append(" and to_number(sou.item_unit_id_temp,'9999999999') = dest.item_unit_id");
        sql.append(" and to_number(sou.price_system_temp,'9999999999') = dest.price_system");
        sql.append(" where dest.id is null");
        sql.append(" and sou.chanel in ('");
        sql.append(chanelStr);
        sql.append("') ");
        sql.append(assistSql);

        logger.info("价格体系明细与rif对比新增数据sql为");
        logger.info(sql.toString());
        return TempTableUtils.executeQuery(sql.toString());
    }

    /**
     * 获取rif到saas要更更新的数据
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getUpdateData(String tenantId, String fromTable,
                                           String tempTable,String chanelStr,String assistSql) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }


        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();
        sql.append("select distinct dest.id as price_id, sou.* from ");
        sql.append(destName).append(" dest ,");
        sql.append(tempTable).append(" sou");
        sql.append(" where sou.chanel = dest.chanel");
        sql.append(" and to_number(sou.item_unit_id_temp,'9999999999') = dest.item_unit_id");
        sql.append(" and to_number(sou.price_system_temp,'9999999999') = dest.price_system");
        sql.append(" and sou.price::NUMERIC <> dest.price");
        sql.append(" and sou.chanel in ('");
        sql.append(chanelStr);
        sql.append("') ");
        sql.append(assistSql);

        logger.info("价格体系明细与rif对比修改数据sql为");
        logger.info(sql.toString());
        return TempTableUtils.executeQuery(sql.toString());
    }
}
