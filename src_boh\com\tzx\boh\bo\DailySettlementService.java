package com.tzx.boh.bo;

import net.sf.json.JSONObject;

public interface DailySettlementService
{
	String	NAME	= "com.tzx.boh.bo.imp.DailySettlementServiceImpl";

	/**
	 * store_id day_count employeeName dstype
	 * status
	 * @param tenantid
	 * @param param
	 * @throws Exception
	 */
	void dailySettlement(String tenantid, JSONObject param) throws Exception;

    /**
     * 批量日结
     * @param tenantid
     * @param param
     * @throws Exception
     */
	void batchDailySettlement(String tenantid, JSONObject param) throws Exception;

	
	JSONObject findDailySettlementStatus(String tenantid, JSONObject param) throws Exception;

	/**
	 * 获取跨天日结信息
	 *
	 * @param tenentid
	 * @param store_id
	 * @return
	 * @throws Exception
	 */
	JSONObject getCrossValue(String tenentid, String store_id) throws Exception;

	void beforedailySettlement(String tenentid, JSONObject param) throws Exception;

	JSONObject checkOpenHq(String attribute, JSONObject obj)throws Exception;

    JSONObject checkOpenScm(String tenentid, JSONObject obj) throws Exception;
}
