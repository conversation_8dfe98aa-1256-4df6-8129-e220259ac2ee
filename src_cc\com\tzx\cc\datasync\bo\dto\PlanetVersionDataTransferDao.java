package com.tzx.cc.datasync.bo.dto;

import net.sf.json.JSONObject;
/**
 * 
 * 行星表取数逻辑 DB2数据库
 *
 */
public interface PlanetVersionDataTransferDao
{
	String		SELECTOR			= "selector";			// 源where
	// 条件
	String		FROMTABLENAME		= "fromtablename";		// 源表名
	String		FROMTABLENICKNAME	= "fromtablenickname";	// 源表别名
	String		FROMFIELDS			= "fromfields";		// 源字段

	String		SELECTOR4TARGET		= "selector4target";	// 目的where
	// 条件
	String		TOTABLENAME			= "totablename";		// 目的表名
	String		TOTABLENICKNAME		= "totablenickname";	// 目的表别名
	String		TOFIELDS			= "tofields";			// 目的字段

	String		PRIMARYKEY			= "primarykey";		// 表主键
	String		VALUES				= "values";			// 值信息
	String		PARAMSLIST			= "paramslist";		// 参数列表

	String		FROMDB				= "1";					// MultiDatasourceContext.SQLSERVER_DATASOURCE;
	String		TODB				= "0";					// MultiDatasourceContext.POSTGRES_DATASOURCE;

	// 初始化时tofields,totablenickname没用到
	JSONObject	INIT_TABLE			= JSONObject
											.fromObject("{"
													// 门店数据
													+ "\"TZXUUS.UUS_ORGAN\": {"
													+ "    \"totablename\": \"organ\","
													+ "    \"tofields\": \"id,organ_code,org_full_name,org_short_name,valid_state,top_org_id,org_type,price_system,complain_phone,phone,address,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"u.id AS id,(SELECT p.simplified FROM tzxpl.platform_language p  WHERE p.id=u.org_name) AS org_full_name,( SELECT  p.simplified FROM tzxpl.platform_language p  WHERE  p.id=u.org_name) AS org_short_name, CASE  WHEN u.org_type='PLATFORM_ORGAN_DEPARTMENT' OR  u.org_type='PLATFORM_ORGAN_FILIALE' THEN 1 WHEN u.org_type='PLATFORM_ORGAN_SHOP' THEN 3 END AS org_type ,CASE WHEN Z.parent_id IS NULL THEN 0 ELSE Z.parent_id END AS TOp_org_id ,u.ADDRESS,u.org_code AS organ_code,'1'        AS valid_state ,CASE WHEN LENGTH(u.ORG_CODE) =2  THEN 1  WHEN LENGTH(u.ORG_CODE) =4 THEN 2   WHEN LENGTH(u.ORG_CODE) =6 THEN 3 WHEN LENGTH(u.ORG_CODE) =8 THEN 4  WHEN LENGTH(u.ORG_CODE) =10 THEN 5 END AS LEVEL,u.id AS fake_id\","
													+ "    \"selector\": \" u LEFT JOIN (SELECT ORGAN_PROPERTY_REF.* FROM tzxuus.UUS_ORGAN_PROPERTY_REF AS ORGAN_PROPERTY_REF WHERE  ORGAN_PROPERTY_REF.ORGAN_PROPERTY_ID =( SELECT ag.ID AS ORGAN_PROPERTY_id FROM tzxuus.UUS_ORGAN_PROPERTY ag LEFT JOIN TZXPL.PLATFORM_LANGUAGE bg ON bg.ID=ag.PROPERTY_NAME WHERE  bg.SIMPLIFIED='行政'))z ON z.organ_id=u.id WHERE u.STATUS='PLATFORM_STATUS_ENABLE'AND u.ID !=0 \"" 
													+ "},"
													// 机构人员数据
													+ "\"TZXUUS.UUS_EMP_POST_VIEW\": {"
													+ "    \"totablename\": \"employee\","
													+ "    \"tofields\": \"id,name,sex,store_id,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"u.id as id,u.EMP_ID as user_name,u.EMP_NAME as NAME,case when u.gender ='UUS_MALE' then 'man' when u.gender ='UUS_FEMALE' then  'woman' end as sex,u.ORGAN_ID as store_id,u.id as fake_id \","
													+ "    \"selector\": \" u where u.STATUS='PLATFORM_STATUS_ENABLE'and u.ORGAN_PRO_ID=5 ORDER BY u.ID ASC \"" 
													+ "},"
													
													//同步桌位类型 @TODO  
													+ "\"TZXERP.ERP_ORGBUSFIT\": {"
													+ "    \"totablename\": \"sys_dictionary\","
													+ "    \"tofields\": \"valid_state,class_item,application_model,model_name,class_identifier,class_identifier_code,is_sys,fake_id\","
													+ "    \"selector4target\": \" where class_identifier='餐位类型' and fake_id is not null\","
													+ "    \"fromfields\": \"  case e.cwzt when 'ERP_SYSTEM_DISABLE_Y' then '1' else '0' end as valid_state,t.simplified as class_item,'桌位库' as application_model,'hq' as model_name,'餐位类型' as class_identifier,'table_property' as class_identifier_code,'N' as is_sys,e.id as fake_id\","
													+ "    \"selector\": \"left join tzxpl.PLATFORM_LANGUAGE t on t.id = e.cwlxmc\","
													+ "    \"fromtablenickname\": \" e\","
													+ "},"
													//桌位信息
													+ "\"TZXERP.ERP_ORGBUSINFO\": {"
													+ "    \"totablename\": \"tables_info\","
													+ "    \"tofields\": \" id,organ_id,table_code,table_property_id,business_area_id,table_name,valid_state,fwfz_id,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \" u.id,u.jgxh as organ_id,u.zwbh as table_code,CWLXID as table_property_id,12095 as business_area_id,bb.SIMPLIFIED as table_name,'1' as valid_state,u.fwfid as  fwfz_id,u.id as fake_id\","
													+ "    \"selector\": \" u  LEFT JOIN tzxpl.PLATFORM_LANGUAGE bb on bb.ID=u.MCID WHERE u.state='ERP_SYSTEM_DISABLE_Y' \""
													+ "},"									
													/// 菜品类别信息
													+ "\"TZXERP.ERP_ORGSORTS\": {"
													+ "    \"totablename\": \"hq_item_class\","
													+ "    \"tofields\": \"id,itemclass_code,itemclass_name,father_id,chanel,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"u.id ,u.lbbh as itemclass_code,bb.SIMPLIFIED as itemclass_name,case when u.sjid =1 then 0 else u.sjid end as father_id,'MD01' as chanel,u.id as fake_id \","
													+ "    \"selector\": \" u  left join tzxpl.PLATFORM_LANGUAGE bb on bb.ID=u.MCID  where u.zt='Y' and u.jgid=0 and u.id != 1\""
													+ "},"
													// 菜品信息 rif列和saas列不一致
													+ "\"TZXERP.ERP_ITEMINFO\": {"
													+ "    \"totablename\": \"hq_item_info\","
													+ "    \"tofields\": \"id,item_code,item_name,item_class,is_combo,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"u.id ,u.bhdm as item_code,(select p.simplified from tzxpl.platform_language p where p.id=u.mcid) as item_name,lbid as item_class,case when u.xmsx ='ERP_ITEM_PROPERTY_SINGLE' then 'N' when u.xmsx ='ERP_ITEM_PROPERTY_WHOLE' then  'Y' end as is_combo,l.simplified as dw_name,u.xsdj,u.id as fake_id\","
													+ "    \"selector\": \" u left join tzxerp.erp_units k on k.id=u.dwid left join tzxpl.platform_language l on l.id=k.mcid WHERE U.ID !=1 AND u.SFYX = 'Y'\""
													+ "},"
													//菜品明细
													+ "\"TZXERP.ERP_ORGITEMINFO\": {"
													+ "    \"totablename\": \"hq_item_pricesystem\","
													+ "    \"tofields\": \"id,item_unit_id,price_system,price,chanel,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromfields\": \"u.ID,u.XMID as item_id,u.JGXH as price_system,u.XSDJ as price,'MD01' as chanel,u.id as fake_id\","
													+ "    \"selector\": \" u inner join tzxpl.platform_language f on u.MCID = f.id inner join TZXERP.ERP_ITEMINFO g on u.XMID = g.id where u.SHBZ = 'Y'\""
													+ "},"
													//餐谱明细
													+ "\"TZXERP.ERP_MEALNOTESINFO\": {"
													+ "    \"totablename\": \"hq_item_menu_class\","
													+ "    \"tofields\": \"details_id as id,chanel,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null \","
													+ "    \"fromtablename\": \"select * from TZXERP.ERP_ORGITEMINFO\","
													+ "    \"fromfields\": \"u.ID,f.simplified as item_name,g.lbid as class_id,u.XMID as item_id,u.JGXH as JGXH,'MD01' as chanel,u.id as fake_id\","
													+ "    \"selector\": \" u inner join tzxpl.platform_language f on u.MCID = f.id inner join TZXERP.ERP_ITEMINFO g on u.XMID = g.id where u.SHBZ = 'Y'\""
													+ "},"
													// 项目组表hq_item_group
													+ "\"TZXERP.ERP_ITEMGROUP\": {"
													+ "    \"totablename\": \"hq_item_group\","
													+ "    \"tofields\": \"id,item_group_code,item_group_name,five_code,phonetic_code,valid_state,item_group_price,fake_id\","
													+ "    \"fromfields\": \" u.id,u.igno as item_group_code ,u.igname as item_group_name,u.wbjm as five_code ,u.pyjm as phonetic_code,case when u.statu ='ERP_SYSTEM_DISABLE_Y' then '1' else '0' end as valid_state ,u.xmdj as item_group_price,u.id as fake_id\","
													+ "    \"selector\": \" u where u.statu ='ERP_SYSTEM_DISABLE_Y' \""
													+ "},"

													// 项目组明细表hq_item_group_details
													// 'Y' as isdefault,0.0 as makeup_money,0.0 as quantity_limit   这三个是oracle中的，DB2中没有，造假数据
													+ "\"TZXERP.ERP_ITEMGROUPLIST\": {"
													+ "    \"totablename\": \"hq_item_group_details\","
													+ "    \"tofields\": \"id,item_group_id,item_id,isdefault,makeup_money,quantity_limit,fake_id\","
													+ "    \"fromfields\": \" id,igid as item_group_id,xmid as item_id,'N' as isdefault,0.0 as makeup_money,0.0 as quantity_limit,id as fake_id   \","
													+ "    \"selector\": \" u \""
													+ "},"

													// 套餐信息
													+ "\"TZXERP.ERP_ORGSILIST\": {"
													+ "    \"totablename\": \"hq_item_combo_details\","
													+ "    \"tofields\": \"id,iitem_id,is_itemgroup,details_id,combo_num,standardprice,combo_order,fake_id\","
													+ "    \"fromfields\": \"u.id ,u.xmid as iitem_id,case when u.mxlx ='ERP_MXLX_GROUP' then 'Y' when u.mxlx ='ERP_MXLX_SINGLE' then  'N' end as is_itemgroup , u.mxid as details_id,u.xmsl as combo_num,u.xsdj as standardprice,u.xmph as combo_order,u.id as fake_id \","
													+ "    \"selector\": \" u \"" 
													+ "},"
													
													//口味类型
													+ "\"TZXERP.TASTETYPE\": {"
													+ "    \"totablename\": \"item_taste\","
													+ "    \"tofields\": \"id,name,code,pinyin_sy,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null and fake_type = 1\","
													+ "    \"fromtablename\": \"select * from TZXERP.ERP_TASTETYPE\","
													+ "    \"fromfields\": \"TAST.ID as ID,'000' || trim(char(TAST.ID)) as code,ln.SIMPLIFIED as name,TAST.YWMC2 as pinyin_sy,TAST.ID as fake_id \","
													+ "    \"selector\": \" TAST left join TZXPL.PLATFORM_LANGUAGE LN ON TAST.MCID = ln.ID \"" 
													+ "},"
													
													//口味
													+ "\"TZXERP.ERP_TASTE\": {"
													+ "    \"totablename\": \"item_taste\","
													+ "    \"tofields\": \"id,name,code,pinyin_sy,father_id,fake_id\","
													+ "    \"selector4target\": \" where fake_id is not null and fake_type = 2\","
													+ "    \"fromfields\": \"ET.ID as id,TAST.ID as father_id,('000'||trim(char(TAST.ID))||trim(ET.KWBH)) AS code,PL.SIMPLIFIED AS name, left(ltrim(ET.PYDM),16) as pinyin_sy,ET.ID as fake_id \","
													+ "    \"selector\": \" ET left join TZXPL.PLATFORM_LANGUAGE PL on ET.MCID=PL.ID left join TZXERP.ERP_TASTETYPE TAST ON ET.KWLXID = TAST.ID left join TZXPL.PLATFORM_LANGUAGE LN ON TAST.MCID = ln.ID \"" 
													+ "}"
										
													+ "}");

}
