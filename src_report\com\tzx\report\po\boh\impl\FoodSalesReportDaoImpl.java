package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import net.sf.json.JSONObject;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.FoodSalesReportDao;

@Repository(FoodSalesReportDao.NAME)
public class FoodSalesReportDaoImpl implements FoodSalesReportDao{
 
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
 
	List<JSONObject> list =null;
	StringBuilder sb = new StringBuilder();
	 
	@Override
	public JSONObject getFoodSalesReport(String tenancyID, JSONObject condition) throws Exception{	
		Integer type = condition.optInt("type");
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		StringBuilder footer = new StringBuilder();
		long total = 0L;
			
		switch (type)
			{
				//按菜品汇总
				case 1:
					if(condition.optInt("hierarchytype") ==1){
						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_ACCORDING_TO_THE_FIRST_LAYER_OF_DISHES);
						System.out.println("菜品销售时段1："+completionSql);
						total = this.dao.countSql(tenancyID,completionSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
						
						footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_ACCORDING_TO_DISHES_SUMMARY));
					}else if(condition.optInt("hierarchytype") ==2 && condition.containsKey("item_num")){
						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_ACCORDING_TO_THE_SECOND_LAYERS_OF_DISHES_SUMMARY);
						System.out.println("菜品销售时段2："+completionSql);
						total = this.dao.countSql(tenancyID,completionSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
					}
				break;
				//按时段汇总
				case 2:
					if(condition.optInt("hierarchytype") ==1){
						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_ACCORDING_TO_THE_TIME_OF_THE_FIRST_LAYER);
						total = this.dao.countSql(tenancyID,completionSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
						
						footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_ACCORDING_TO_TIME_SUMMARY));
					}else if(condition.optInt("hierarchytype") ==2 && condition.containsKey("sd")){
						String completionSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_SECOND_LAYERS_ACCORDING_TO_THE_TIME_SUMMARY);
						total = this.dao.countSql(tenancyID,completionSql.toString());
						list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,completionSql.toString()));
					}
				break;
				default:
					break;
			}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		return result;
		}
}
