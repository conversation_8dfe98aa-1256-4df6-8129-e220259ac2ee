package com.tzx.cc.datasync.bo.util.strategy;

import com.tzx.cc.datasync.bo.dto.DataTransferDaoHelper;
import com.tzx.cc.datasync.bo.dto.PlanetVersionDataTransferDao;
import com.tzx.cc.datasync.bo.util.DriverUtils;
import com.tzx.cc.datasync.bo.util.SynIdUtils;
import com.tzx.cc.datasync.bo.util.SynUtils;
import com.tzx.cc.datasync.bo.util.TempTableUtils;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.util.StringUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 餐谱比较器，用于oracle，根据餐谱的 item_id,item_menu_id,class_id进行比较
 * Created by XUGY on 2017-02-24.
 */
public class MealMenuCompareModified implements CompareModified {

    /**
     * logger
     */
    private static final Logger logger = Logger.getLogger(MealMenuCompareModified.class);

    String tempTableName;

    @Override
    public Object compare(Object... objs) throws Exception {
        String tenantId = (String) objs[0];
        Map<String, Map<String, Object>> mapIds = (Map<String, Map<String, Object>>) objs[1];
        List<JSONObject> rifData = (List<JSONObject>) objs[2];
        String fromTable = (String) objs[3];
        List<JSONObject> dic_result_list = (List<JSONObject>) objs[4];
        String sql = StringUtils.EMPTY;
        if(objs.length>5) {
            sql = (String) objs[5];
        }
        String delsql = StringUtils.EMPTY;
        if (objs.length > 6) {
            delsql = (String) objs[6];
        }

        Map<String, List<JSONObject>> resultMap = new HashMap<String, List<JSONObject>>();
        if (rifData == null || rifData.size() == 0) {
            return resultMap;
        }
        processData(mapIds, rifData);
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            SynUtils.addQd(rifData,dic_result_list);
        }
        //创建临时表 存储同步过来的数据  格式同源库
        String createTempTable = TempTableUtils.createTempTable(tenantId, rifData, fromTable);
        tempTableName = createTempTableASDetailsAndClass(tenantId);
        try {
            //将数据插入临时表中
            TempTableUtils.insertTempTable(tenantId, createTempTable, rifData);
            String chanelStr = SynUtils.getChanelStr(dic_result_list);

            List<JSONObject> updateList = getUpdateData(tenantId, fromTable, createTempTable,chanelStr,sql);
            List<JSONObject> insertList = getInsertData(tenantId, fromTable, createTempTable,chanelStr,sql);
            List<JSONObject> deleteList = getDeleteData(tenantId, fromTable, createTempTable,chanelStr,delsql);
            resultMap.put("add", insertList);
            resultMap.put("update", updateList);
            resultMap.put("delete", deleteList);
        } catch (Throwable e) {
            logger.error(e);
            throw new Exception(e);
        } finally {
            TempTableUtils.dropTempTable(tenantId, createTempTable);
            TempTableUtils.dropTempTable(tenantId, tempTableName);
        }
        return resultMap;
    }

    /**
     * 创建餐谱关联关系明细表
     * @param tenantId
     * @return
     * @throws Exception
     */
    private String createTempTableASDetailsAndClass(String tenantId) throws Exception {
        StringBuffer sql = new StringBuffer();
        String name = "hq_item_menu_details_temp"+System.currentTimeMillis();
        sql.append("create temp table ");
        sql.append(name);
        sql.append(" as select cl.id as menu_class_id,de.id,(de.item_menu_id||'') item_menu_id,");
        sql.append(" (de.item_id||'') item_id,(cl.class||'') class_id,cl.chanel,cl.item_name,cl.menu_class_rank,de.menu_item_rank");
        sql.append(" from hq_item_menu_details de");
        sql.append(" inner join hq_item_menu_class cl");
        sql.append(" on de.id = cl.details_id");
        logger.info("创建餐谱关系临时表sql为");
        logger.info(sql.toString());
        TempTableUtils.execute(sql.toString());
        return name;
    }

    private void processData(Map<String, Map<String, Object>> mapIds, List<JSONObject> rifData) {
        String driver = DriverUtils.getDriver();
        String itemMenuId = null;
        String classId = null;
        String itemId = null;
        for (JSONObject json : rifData) {
            itemId = SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info", json.optString("item_id").trim());
            if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
                itemMenuId = SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_menu", json.optString("jgxh").trim());
                classId = SynIdUtils.getIdBytableChannelFakeId(mapIds, "hq_item_class", json.optString("chanel").trim(), json.optString("class_id"));
            } else {
                itemMenuId = SynIdUtils.getIdBytableFakeId(mapIds,
                        "hq_item_menu", json.optString("item_menu_id").trim());
                classId = (StringUtils.equals("MD01", json.optString("chanel").trim())
                        ? SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_info_class_id", json.optString("item_id").trim())
                        : SynIdUtils.getIdBytableFakeId(mapIds, "hq_item_class_3", json.optString("class_id").trim()));
            }

            json.put("item_menu_id_temp", itemMenuId);
            json.put("item_id_temp", itemId);
            json.put("class_id_temp", classId);
        }

    }

    /**
     * 获取rif到saas要删除的数据
     *
     * @param tenantId
     * @param fromTable
     * @param createTempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getDeleteData(String tenantId, String fromTable,
                                           String createTempTable,String chanelStr,String assistSql) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }
        StringBuffer sql = new StringBuffer();

        sql.append("select dest.* from ");
        sql.append(tempTableName);
        sql.append(" dest left join ");
        sql.append(createTempTable);
        sql.append(" sou on (");
        sql.append(" dest.item_menu_id = sou.item_menu_id_temp");
        sql.append(" and dest.item_id = sou.item_id_temp");
//        sql.append(" and dest.class_id = sou.class_id_temp");
        sql.append(" and dest.chanel = sou.chanel");
        sql.append(" ) where  sou.id is null and dest.id is not null");
        sql.append(" and dest.chanel in ('");
        sql.append(chanelStr);
        sql.append("') ");
        sql.append(assistSql);
        logger.info("餐谱与rif对比删除数据sql为");
        logger.info(sql.toString());

        return TempTableUtils.executeQuery(sql.toString());
    }

    /**
     * 获取rif到saas要新增的数据
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getInsertData(String tenantId, String fromTable,
                                           String tempTable,String chanelStr,String assistSql) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }
        StringBuffer sql = new StringBuffer();

        sql.append("select sou.* from ");
        sql.append(tempTable);
        sql.append(" sou left join ");
        sql.append(tempTableName);
        sql.append(" as dest");
        sql.append(" on (sou.item_menu_id_temp = dest.item_menu_id");
        sql.append(" and sou.item_id_temp = dest.item_id");
//        sql.append(" and sou.class_id_temp = dest.class_id");
        sql.append(" and sou.chanel = dest.chanel)");
        sql.append(" where dest.id is null");
        sql.append(" and sou.chanel in ('");
        sql.append(chanelStr);
        sql.append("') ");
        sql.append(assistSql);
        logger.info("餐谱与rif对比新增数据sql为");
        logger.info(sql.toString());
        return TempTableUtils.executeQuery(sql.toString());
    }

    /**
     * 获取rif到saas要更更新的数据
     *
     * @param tenantId
     * @param fromTable
     * @param tempTable
     * @return
     * @throws Exception
     */
    private List<JSONObject> getUpdateData(String tenantId, String fromTable,
                                           String tempTable,String chanelStr,String assistSql) throws Exception {
        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver")) {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }

        String destName = tablesMap.optJSONObject(fromTable).optString("totablename");
        StringBuffer sql = new StringBuffer();
        sql.append("select distinct dest.id as menu_details_id,dest.menu_class_id,sou.* from ");
        sql.append(tempTableName).append(" dest ,");
        sql.append(tempTable).append(" sou");
        sql.append(" where 1=1");
        sql.append(" and dest.item_menu_id = sou.item_menu_id_temp");
        sql.append(" and dest.item_id = sou.item_id_temp");
//        sql.append(" and dest.class_id = sou.class_id_temp");
        sql.append(" and dest.chanel = sou.chanel");
        sql.append(" and (dest.item_name <> sou.item_name");
        if (driver.equals("oracle.jdbc.driver.OracleDriver")) {
            sql.append(" or dest.menu_class_rank <> sou.lbsx or dest.menu_item_rank<>sou.cpsx");
        }

        sql.append(" )and dest.chanel in ('");
        sql.append(chanelStr);
        sql.append("') ");
        sql.append(assistSql);

        logger.info("餐谱与rif对比修改数据sql为");
        logger.info(sql.toString());
        return TempTableUtils.executeQuery(sql.toString());
    }
}
