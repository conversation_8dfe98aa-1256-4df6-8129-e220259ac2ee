package com.tzx.cc.baidu.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.common.redis.service.CcRedisService;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;

import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;

/**
 * <AUTHOR>
 *
 */
public class SignHolder {

    static Logger logger = Logger.getLogger(SignHolder.class);

    static Map<String, Sign> map = new HashMap<String, Sign>();
    static Map<String, String> systemPara = new HashMap<>();
    static Map<String, String> storeOrderDistribute = new HashMap<>();
	
	static CcRedisService redis = (CcRedisService) SpringConext.getBean("ccRedisServiceImpl");
	
	/**获取百度商户签名参数source，secret
	 * @param tenantId
	 * @param shopId
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public static Sign getShopSign(String tenantId,String shopId) throws Exception{
		
		return getShopSign(tenantId,shopId,Constant.BAIDU_CHANNEL);
	}
	
	/**获取第三方接口签名参数source，secret
	 * @param tenantId
	 * @param shopId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public static Sign getShopSign(String tenantId,String shopId,String channel) throws Exception{
		
		String key=shopId+"@"+channel+"@"+tenantId;
		
//		if(map.containsKey(key)){
//			return map.get(key);
//		}
		if(CommonUtil.checkStringIsNotEmpty(redis.getByKey(key))){
			return getSign(key);
		}
		
		GenericDao dao=(GenericDao) SpringConext.getBean("genericDaoImpl");
		
		String sql="SELECT source,secret,app_auth_token FROM cc_third_organ_info where shop_id='"+shopId+"' and channel='"+channel+"'";
		
		List<JSONObject> list=dao.query4Json(tenantId, sql);
		
		Sign sign=new Sign();
		
		if(list.isEmpty()){
			throw new Exception("shopId["+shopId+"] is not exits!");
		}else{
			JSONObject o=list.get(0);
			String source=o.optString("source");
			String secret=o.optString("secret");
            String appAuthToken=o.optString("app_auth_token");
			sign.setSource(source);
			sign.setSecret(secret);
            sign.setAppAuthToken(appAuthToken);
            redis.saveBykv(key, o.toString(), 0);
			map.put(key, sign);
		}
		
		return sign;
	}
	
	private static Sign getSign(String key){
		Sign sign=new Sign();
		try {
			String signObject=redis.getByKey(key);
			JSONObject object=JSONObject.fromObject(signObject);
			String source=object.optString("source");
			String secret=object.optString("secret");
            String appAuthToken=object.optString("app_auth_token");
			sign.setSource(source);
			sign.setSecret(secret);
            sign.setAppAuthToken(appAuthToken);
		} catch (Exception e) {
			e.printStackTrace();
		}
	  return sign;
	}

	/**
	 * 删除指定key对应的redis缓存
	 * @param key
	 */
	public static void cleanSign(String key) {
		try {
			redis.del(key);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			//e.printStackTrace();
		}
		return;
	}
	
    public static String getSystemPara(String tenancyId, String code) {

        if (!systemPara.containsKey(code)) {
            String sql = "select para_value  from sys_parameter where para_code = ? and valid_state='1'";
            try {
                DBContextHolder.setTenancyid(tenancyId);
                GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
                SqlRowSet rs = dao.getJdbcTemplate(tenancyId).queryForRowSet(sql, new Object[]{code});
                while (rs.next()) {
                    String value = rs.getString("para_value");
                    systemPara.put(code, value);
                }
            } catch (Exception e) {
                logger.error("获取系统参数错误！", e);
            }
        }
        return systemPara.get(code);
    }

    public static int getSystemParaCastInt(String tenancyId, String code) {
        String value = getSystemPara(tenancyId, code);
        int result = Integer.MIN_VALUE;
        if (StringUtils.isNotEmpty(value)) {
            result = Integer.valueOf(value);
        }
        return result;
    }

    public static String getStoreOrderDistributeType(String tenentId, String storeId) {
        String key = storeId + "@" + tenentId;

        if (storeOrderDistribute.containsKey(key)) {
            return storeOrderDistribute.get(key);
        }

        String orderDistributeType = "ALL";

        try {
            GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");

            String sql = "SELECT order_distribute_type from organ where tenancy_id='" + tenentId + "' and id=" + storeId + ";";

            List<JSONObject> list = dao.query4Json(tenentId, sql);

            JSONObject o = list.get(0);
            orderDistributeType=o.optString("order_distribute_type");

            storeOrderDistribute.put(key, o.optString("order_distribute_type"));

        } catch (Exception e) {
            logger.error(e);
        }

        return orderDistributeType;
    }
}

