package com.tzx.report.bo.imp.report;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.report.RegionVarietyOfDishesService;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;


@Service(RegionVarietyOfDishesService.NAME)
public class RegionVarietyOfDishesServiceImp implements RegionVarietyOfDishesService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;

	@Override
	public JSONObject find(String tenancyID, JSONObject condition) throws Exception
	{ 
		JSONObject  result = new JSONObject();
		String startDate = condition.optString("start_date");
		String endDate =condition.optString("end_date");
		List<JSONObject> list = new ArrayList<>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure =new ArrayList<JSONObject>();
		Long total = 0L;
		if(endDate != null && !endDate.equals(null) && startDate != null && !startDate.equals(null)) {
			condition.put("select_type", condition.containsKey("customerClass")?"Z1":"L1");
			String sql =getQuerySQL( tenancyID , condition);
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
				list = this.dao.query4Json(tenancyID, sql.toString());
				structure = conditionUtils.getSqlStructure(tenancyID,sql);
			}else {
				list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
				condition.put("select_type", condition.containsKey("customerClass")?"Z0":"L0");
				sql =getQuerySQL( tenancyID , condition);
				footerList = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
				total = this.dao.countSql(tenancyID,sql.toString());
			}
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}
	
	public String getQuerySQL(String tenancyID ,  JSONObject condition) {
		String reportSql = "";
		List<JSONObject> list = new ArrayList<JSONObject>();
		try {
			reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.REGION_VARRIETY_FUNCTION_F1);
			list = this.dao.query4Json(tenancyID, reportSql);
			if(list.size() > 0) {
				reportSql = list.get(0).getString("f_rpt_business_area_item");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return reportSql;
	}

	
	
	
	
}
