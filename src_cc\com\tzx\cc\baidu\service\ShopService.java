package com.tzx.cc.baidu.service;

import java.util.List;

import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * 
 * 商户相关接口
 *
 */
@Deprecated
public interface ShopService {
	
	 final static String NAME="com.tzx.cc.baidu.service.impl.ShopServiceImpl";
	
	/**获取商户信息
	 * @param tenantId
	 * @param shopId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	JSONObject getShopInitInfo(String tenantId, String shopId, String channel) throws Exception;

	/**查看商户列表
	 * @param tenantId
	 * @param shopId
	 * @param channel
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject shopList(String tenantId, String shopId, String channel, JSONObject params) throws Exception;

	/**创建/修改商户
	 * @param tenantId
	 * @param params
	 * @param result
	 * @return
	 * @throws Exception
	 */
	JSONObject shopCreateOrUpdate(String tenantId, JSONObject params, JSONObject result) throws Exception;

	/**更新商户状态
	 * @param tenantId
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	JSONObject shopSetStatus(String tenantId, JSONObject params, JSONObject result) throws Exception;

	/**订单操作
	 * @param tenantId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	JSONObject orderOper(String tenantId, JSONObject params) throws Exception;

	/**获取餐盒费信息
	 * @param tenantId
	 * @param shopId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> initMealsInfo(String tenantId, String shopId, String channel) throws Exception;
	
	/**查看订单状态
	 * @return
	 * @throws Exception
	 */
	JSONObject orderStatusGet(String tenantId,String shopId,String baiduOrderId) throws Exception;
    
}
