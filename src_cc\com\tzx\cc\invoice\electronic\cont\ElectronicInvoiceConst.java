package com.tzx.cc.invoice.electronic.cont;

/**
 * <AUTHOR>
 * 
 */
public class ElectronicInvoiceConst {
	public static final String LESS_THAN_CODE = "&lt;";
	public static final String GREATER_THAN_CODE = "&gt;";
	
	public static final int ELECTRON_ICINVOICE_ERROR_CODE_INVALID_PARAM = 1;
	public static final int ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL = 2;
	public static final int ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS = 0;
	public static final int ELECTRON_ICINVOICE_ERROR_CODE_INVALID_CONNECT = 99;
	
	/**
	 * redis中存储瑞红 tenancy_id 的键
	 */
	public static final String ELECTRON_ICINVOICE_REDIS_CODE = "ELECTRON_ICINVOICE_CODE_TENANCY_ID";
	
	/**
	 * redis中存储百旺 tenancy_id 的键
	 */
	public static final String BW_ELECTRON_ICINVOICE_REDIS_CODE = "BW_ELECTRON_ICINVOICE_CODE_TENANCY_ID";
	
	/**
	 * redis中存储用友 tenancy_id 的键
	 */
	public static final String YY_ELECTRON_ICINVOICE_REDIS_CODE = "YY_ELECTRON_ICINVOICE_CODE_TENANCY_ID";
	
	/**
	 * redis中存储票通 tenancy_id 的键
	 */
	public static final String PT_ELECTRON_ICINVOICE_REDIS_CODE = "PT_ELECTRON_ICINVOICE_CODE_TENANCY_ID";
	
	/**
	 * redis中存储百旺 BW_ELECTRON_ICINVOICE_REDIS_CODE 的键
	 */
	public static final String BW_EWM_ELECTRON_ICINVOICE_REDIS_CODE = "BW_EWM_ELECTRON_ICINVOICE_CODE";
	
	/**
	 *redis中键的过期时间  以小时为单位
	 */
	public static final long ELECTRON_ICINVOICE_REDIS_CODE_EXPIRE = 24;
	
	/**
	 * 项目名称
	 */
	public static final String ELECTRON_ICINVOICE_XMMC = "餐饮";
	
	/**
	 * 项目名称
	 */
	public static final String BW_ELECTRON_ICINVOICE_XMMC = "餐饮服务";

	/**
	 * 无效的发票号，未找到该张发票
	 */
	public static final String ELECTRON_ICINVOICE_STATUS_INVALID = "0";
	
	/**
	 * 电子发票状态-待开票
	 */
	public static final String ELECTRON_ICINVOICE_STATUS_WAIT = "1";

	/**
	 * 电子发票状态-开票失败
	 */
	public static final String ELECTRON_ICINVOICE_STATUS_FAIL = "2";

	/**
	 * 电子发票状态-开票成功
	 */
	public static final String ELECTRON_ICINVOICE_STATUS_SUCCESS = "3";

	/**
	 * 电子发票状态-取消中
	 */
	public static final String ELECTRON_ICINVOICE_STATUS_CANCEL_PROCESS = "4";
	
	/**
	 * 电子发票状态-取消失败
	 */
	public static final String ELECTRON_ICINVOICE_STATUS_CANCEL_FAIL = "5";

	/**
	 * 电子发票状态-取消开票
	 */
	public static final String ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS = "6";
	
	/**
	 * 电子发票状态-已取消  我们系统中的取消，没有和第三方打交道。没有掉第三方开具发票的接口，是我们系统中的业务。
	 */
	public static final String ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL = "7";
	
	/**
	 * 电子发票状态-取消冲正
	 */
	public static final String ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS = "8";

	/**
	 * 电子发票选择类型的KEY
	 */
	public static final String ELECTRIC_USE_CHOOSE_KEY = "electric_use_choose";

	/**
	 * 电子发票-百旺
	 */
	public static final String ELECTRIC_USE_CHOOSE_BW = "2";
	/**
	 * 电子发票-瑞红
	 */
	public static final String ELECTRIC_USE_CHOOSE_RH = "3";
	
	/**
	 * 电子发票-用友
	 */
	public static final String ELECTRIC_USE_CHOOSE_YY = "1";
	
	/**
	 * 电子发票-票通
	 */
	public static final String ELECTRIC_USE_CHOOSE_PT = "4";
	
	/**
	 * 电子发票-篮字发票
	 */
	public static final String ELECTRIC_KPLX_LP = "0";
	
	/**
	 * 电子发票-红字发票
	 */
	public static final String ELECTRIC_KPLX_HP = "1";
	
	
	/**
	 * 电子发票餐饮服务 商品编号
	 */
	public static final String ELECTRIC_MEAL_BH = "3070401000000000000";
	
}
