package com.tzx.cc.common.constant.util;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.druid.util.StringUtils;
import com.tzx.cc.base.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

/**
 * 处理外卖数据分区后执行的sql统一处理方式
 * 
 * @see 统一在执行SQL中的加入日期条件，以匹配分区的范围定位
 * 
 * <AUTHOR>
 * 
 */
public class CcPartitionUtils {

	private static Logger log = LoggerFactory.getLogger(CcPartitionUtils.class);

	/** 天子星订单号 */
	public final static int TYPE_ORDERCODE_TZX = 1;
	/** 不存在天子星订单号 */
	public final static int TYPE_ORDERCODE_NO = 2;

	/** 时间条件：大于等于 */
	public final static int TYPE_SINGLETIME_GREATE_EQ = 3;
	/** 时间条件：小于等于 */
	public final static int TYPE_SINGLETIME_LESS_EQ = 4;
	/** 时间条件：等于 */
	public final static int TYPE_SINGLETIME_EQ = 5;
	/** 时间条件：没有时间条件时采用 当前时间减2月，加7天的规则 */
	public final static int TYPE_SINGLETIME_NO = 6;

	/** 时间条件：系统时间 */
	public final static int TYPE_SINGLETIME_SYSTEM = 7;

	private final static int pre_month = -2;// 当前时间减2个月
	private final static int next_day = 7;// 当前时间加7天

	private final static String COLUMN_NAME = "create_date";

	// 存储分区标识到内存中
	private static Map<String, Boolean> is_partion_cacheMap = new HashMap<String, Boolean>();
	private static Map<String, Long> is_partion_time_cacheMap = new HashMap<String, Long>();

	/**
	 * 功能：针对SQL统一处理，以满足分区后所需的条件
	 * 
	 * @see 注意：多个SQL间请以";"分隔
	 * 
	 * @param isSplitEnd
	 *            是否以";"结尾返回，必填
	 * @param prefix
	 *            分区时间前缀，可以为空，不允许为null
	 * @param sql
	 *            SQL语句，必填
	 * @param orderCodeOrTime
	 *            订单号或时间，type为TYPE_SINGLETIME_SYSTEM 可以为空
	 * @param type
	 *            类型：CcPartitionUtils.TYPE_ORDERCODE_TZX|CcPartitionUtils.
	 *            TYPE_ORDERCODE_NO ，是否为天子星订单号,是|否
	 * @return
	 */
	public static String makeSQL(String tanencyId, boolean isSplitEnd,
			String prefix, String sql, final String orderCodeOrTime, int type) {
		try {
			if (!isWaimaiPartitionStart(tanencyId)) {
				return isSplitEnd?(sql+";"):sql;
			}

			if (StringUtils.isEmpty(prefix)) {
				prefix = "";
			} else {
				prefix += ".";
			}
			String result = "";

			if (sql.indexOf(";") == -1) {
				sql += ";";
			}

			String[] sqlArray = sql.split(";");

			for (String sql_tmp : sqlArray) {

				String onesql_result = "";// 临时存储一条格式化后的SQL

				String timeCondition = "";// 临时存储时间条件SQL

				if (type == TYPE_ORDERCODE_TZX) {
					timeCondition = getSingleTimeConditionSQL(prefix,
							orderCodeOrTime);
				} else if (type == TYPE_ORDERCODE_NO) {
					timeCondition = getSingleTimeConditionSQL(prefix, "",
							TYPE_SINGLETIME_NO);
				} else if (type == TYPE_SINGLETIME_GREATE_EQ) {
					timeCondition = getSingleTimeConditionSQL(prefix,
							orderCodeOrTime, TYPE_SINGLETIME_GREATE_EQ);
				} else if (type == TYPE_SINGLETIME_LESS_EQ) {
					timeCondition = getSingleTimeConditionSQL(prefix,
							orderCodeOrTime, TYPE_SINGLETIME_LESS_EQ);
				} else if (type == TYPE_SINGLETIME_EQ) {
					timeCondition = getSingleTimeConditionSQL(prefix,
							orderCodeOrTime, TYPE_SINGLETIME_EQ);
				} else if (type == TYPE_SINGLETIME_SYSTEM) {
					timeCondition = getSingleTimeConditionSQL(prefix,
							orderCodeOrTime, TYPE_SINGLETIME_SYSTEM);
				} else {
					return sql;
				}
				// 转换成小写
				String sql_tmp_lower = sql_tmp.toLowerCase();

				String orderBy = "";// 截取sql中的order by部分字符串

				// 存储
				if (sql_tmp_lower.indexOf("cc_order_list") > -1
						|| sql_tmp_lower.indexOf("cc_order_item") > -1
						|| sql_tmp_lower.indexOf("cc_order_discount") > -1
						|| sql_tmp_lower.indexOf("cc_order_repayment") > -1
						|| sql_tmp_lower.indexOf("cc_order_item_details") > -1) {

					if (sql_tmp_lower.contains(" order by ")) {
						int len = sql_tmp_lower.indexOf(" order by ");
						orderBy = sql_tmp.substring(len);

						onesql_result += sql_tmp.substring(0, len);

					} else if (sql_tmp_lower.contains(" group by ")) {
						int len = sql_tmp_lower.indexOf(" group by ");
						orderBy = sql_tmp.substring(len);

						onesql_result += sql_tmp.substring(0, len);
					} else {

						onesql_result = sql_tmp;
					}

					onesql_result += timeCondition + orderBy + ";";
				}

				result += onesql_result;
			}

			// 如果为
			if (!isSplitEnd) {
				return result.replaceAll(";", " ");
			}

			log.info("组装分区SQL结果：{}", result);

			return result;
		} catch (Exception ex) {
			ex.printStackTrace();
			return sql;
		}
	}

	/**
	 * 功能：针对SQL统一处理，以满足分区后所需的条件
	 * 
	 * @see 注意：多个SQL间请以";"分隔
	 * @param sql
	 * @param orderCode
	 * @param type
	 * @return
	 */
	public static String makeSQL(String tanencyId, String sql,
			final String orderCode, int type) {
		return makeSQL(tanencyId, true, "", sql, orderCode, type);
	}

	/**
	 * 功能：通过天子星订单号解析出下单日期，组装分区查询条件。
	 * 
	 * @param tzxOrderCode
	 * @return
	 */
	private static String getSingleTimeConditionSQL(String prefix,
			String tzxOrderCode) {
		StringBuffer condition = new StringBuffer();
		try {

			String time = getCreateDateByTzxOrderCode(tzxOrderCode);
			if (!StringUtils.isEmpty(time)) {
				condition.append(" and ").append(prefix).append(COLUMN_NAME)
						.append("='").append(time).append("'::DATE ");

				return condition.toString();
			} else {
				return "";
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			return "";
		}

	}

	private static String getCreateDateByTzxOrderCode(String tzxOrderCode)
			throws Exception {
		int len = tzxOrderCode.length();
		String time = "";
		StringBuffer timeFormat = new StringBuffer();

		if (tzxOrderCode.startsWith("EL09")) {// 饿了么的订单号是年月日时分秒，固多减两位
			time = tzxOrderCode.substring(len - 18, len - 4);
		} else if (tzxOrderCode.startsWith("WX")||tzxOrderCode.startsWith("CC")||tzxOrderCode.startsWith("WM10")) {// 微信点餐, 电话外卖,微信外卖
			time = tzxOrderCode.substring(len - 14, len - 6) + " 00:01";
		} else {
			time = tzxOrderCode.substring(len - 16, len - 4);
		}

		timeFormat.append(time.substring(0, 4)).append("-")
				.append(time.substring(4, 6)).append("-")
				.append(time.substring(6, 8)).append(" ")
				.append(time.substring(8, 10)).append(":")
				.append(time.substring(10, 12));

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date d = sdf.parse(timeFormat.toString());

		return sdf.format(d);
	}

	/**
	 * 功能：针对无天子星订单号作条件情况，进行特殊组装
	 * 
	 * 
	 * @return
	 */
	private static String getSingleTimeConditionSQL(String prefix, String time,
			int type) {
		StringBuffer condition = new StringBuffer();
		try {
			if (TYPE_SINGLETIME_NO == type) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

				Date d = new Date();
				Calendar ca = Calendar.getInstance();
				ca.setTime(d);
				ca.add(Calendar.MONTH, pre_month);

				String pre_date = sdf.format(ca.getTime());

				ca.setTime(d);
				ca.add(Calendar.DAY_OF_MONTH, next_day);

				String next_date = sdf.format(ca.getTime());

				condition.append(" and ").append(prefix).append(COLUMN_NAME)
						.append(" between '").append(pre_date)
						.append("'::DATE and '").append(next_date)
						.append("'::DATE ");

			} else if (type == TYPE_SINGLETIME_GREATE_EQ) {
				condition.append(" and ").append(prefix).append(COLUMN_NAME)
						.append(">='").append(time).append("'::DATE ");
			} else if (type == TYPE_SINGLETIME_LESS_EQ) {
				condition.append(" and ").append(prefix).append(COLUMN_NAME)
						.append("<='").append(time).append("'::DATE ");
			} else if (type == TYPE_SINGLETIME_EQ) {
				condition.append(" and ").append(prefix).append(COLUMN_NAME)
						.append("='").append(time).append("'::DATE ");
			} else if (type == TYPE_SINGLETIME_SYSTEM) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

				condition.append(" and ").append(prefix).append(COLUMN_NAME)
						.append("='").append(sdf.format(new Date()))
						.append("'::DATE ");
			}

			return condition.toString();
		} catch (Exception ex) {
			ex.printStackTrace();
			return "";
		}

	}

	public static String lackInsertSQL(String tanencyId, String sql,
			String tzxOrderCode) {
		if (isWaimaiPartitionStart(tanencyId)) {
			try {
				StringBuffer sb = new StringBuffer();

				if (sql.indexOf(";") == -1) {
					sql += ";";
				}

				String[] sqlArray = sql.split(";");

				for (String tmpSQL : sqlArray) {

					String time = getCreateDateByTzxOrderCode(tzxOrderCode);
					if (StringUtils.isEmpty(time)) {
						return sql;
					}

					String tmp_cols = tmpSQL.substring(0, tmpSQL.indexOf(")"));
					String tmp_values = tmpSQL.substring(
							tmpSQL.indexOf("values"), tmpSQL.length() - 1);

					sb.append(tmp_cols).append(",").append(COLUMN_NAME)
							.append(") ");
					sb.append(tmp_values).append(",'").append(time)
							.append("')").append(";");

				}
				return sb.toString();
			} catch (Exception ex) {
				ex.printStackTrace();
				return sql;
			}
		} else {
			return sql;
		}
	}

	/**
	 * 功能：执行添加SQL时，补充因分区问题所需的必要参数；
	 * 
	 * 
	 * 
	 */
	public static void lackInsertParam(String tanencyId, JSONObject param) {
		lackInsertParam(tanencyId, param, param);
	}

	/**
	 * 功能：从源json中取出single_time时间，存放到目标target的json结构中
	 * 
	 * @param source
	 * @param target
	 */
	public static void lackInsertParam(String tanencyId, JSONObject source,
			JSONObject target) {
		if (isWaimaiPartitionStart(tanencyId)) {
			String time = getlackInsertCreateDate(source);

			if (!StringUtils.isEmpty(time)) {
				target.put(COLUMN_NAME, time.substring(0, 10));
			}
		}
	}

	/**
	 * 功能：从源json中取出single_time时间，存放到目标target数组中的json结构中
	 * 
	 * @param source
	 * @param target
	 */
	public static void lackInsertParam(String tanencyId, JSONObject source,
			List<JSONObject> targetList) {
		if (isWaimaiPartitionStart(tanencyId)) {
			String time = getlackInsertCreateDate(source);

			if (!StringUtils.isEmpty(time)) {
				for (JSONObject target : targetList) {
					target.put(COLUMN_NAME, time.substring(0, 10));
				}
			}

		}
	}

	private static String getlackInsertCreateDate(JSONObject source) {
		try {
			String single_time = source.optString("single_time");
			String orderCode = source.optString("order_code");
			if (!StringUtils.isEmpty(single_time)) {
				return single_time.substring(0, 10);
			} else if (!StringUtils.isEmpty(orderCode)) {
				String time = getCreateDateByTzxOrderCode(orderCode);

				if (!StringUtils.isEmpty(time)) {
					return time;
				}
			} else {
				// 不存在信息则返回当前系统时间
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				return sdf.format(new Date());
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}

		return "";
	}

	private static boolean isWaimaiPartitionStart(String tanencyId) {
		
		try {
			Long currentTimeMillis = 0L;
			
			if (is_partion_time_cacheMap.containsKey(tanencyId)) {
				currentTimeMillis = is_partion_time_cacheMap.get(tanencyId);
			}

			Long sysCurrtime = System.currentTimeMillis();

			if (sysCurrtime - currentTimeMillis >= 60 * 60 * 1000) {

				boolean is_start = false;
				String sql = "select para_value from sys_parameter where para_code = 'waimai_partition_start' and store_id=0 and valid_state='"
						+ Constant.VALID_STATE_TRUE + "'";
				List<net.sf.json.JSONObject> list;
				try {
					DBContextHolder.setTenancyid(tanencyId);
					GenericDao dao = (GenericDao) SpringConext
							.getBean("genericDaoImpl");
					list = dao.query4Json(tanencyId, sql.toString());
					if (list == null || list.size() == 0) {
						is_start = false;
					} else {
						for (net.sf.json.JSONObject json : list) {
							if ("1".equals(json.optString("para_value"))) {
								is_start = true;
							} else {
								is_start = false;
							}
						}
					}
					is_partion_time_cacheMap.put(tanencyId, sysCurrtime);
				} catch (Exception e) {
					is_start = false;
					e.printStackTrace();
				}

				is_partion_cacheMap.put(tanencyId, is_start);
			}

			return is_partion_cacheMap.get(tanencyId);
		} catch (Exception e) {
			// e.printStackTrace();
			log.info("获取商户分区信息失败：{}-{}", tanencyId, "false");
			return false;
		}
	}

	public static void main(String[] args) throws Exception {
		String tanencyId = "aa";
		// String time = "20170509121806";
		// StringBuffer timeFormat = new StringBuffer();
		// timeFormat.append(time.substring(0, 4)).append("-")
		// .append(time.substring(4, 6)).append("-")
		// .append(time.substring(6, 8)).append(" ")
		// .append(time.substring(8, 10)).append(":")
		// .append(time.substring(10, 12));
		//
		// System.out.println("=====>>>" + timeFormat.toString());

		// System.out
		// .println(getSingleTimeConditionSQL("EL093767201705091218060016"));
		// System.out.println(getSingleTimeConditionSQL("BD06112016110710520001"));
		// System.out.println(getSingleTimeConditionSQL("MT08112016111212070005"));
		//
		// System.out.println(getSingleTimeConditionSQL());
		// String orderCode = "EL093767201705091218060016";
		// int len = orderCode.length();
		// System.out.println(orderCode.substring(len - 18, len - 4));

		String tmpSQL = "select * from cc_order_List where order_code='DB08' GROUP by aa=WW;select * from cc_order_LIST where order_code='WX';";
		tmpSQL = makeSQL(tanencyId, false, "b", tmpSQL, "2017-09-13",
				CcPartitionUtils.TYPE_SINGLETIME_EQ);
		System.out.println(tmpSQL);

		List<JSONObject> targetList = new ArrayList<JSONObject>();
		JSONObject t = new JSONObject();
		t.put("aa", "cc");
		targetList.add(t);

		JSONObject source = new JSONObject();
		source.put("single_time", "2017-09-10 12:12");
		// System.out.println("2017-08-10 12:12".substring(0, 10));

		lackInsertParam(tanencyId, source, targetList);
		for (JSONObject a : targetList) {
			System.out.println(a);
		}

		System.out
				.println(lackInsertSQL(
						tanencyId,
						"insert into aa(VV,c2,c3,c4) values (v1,v2,v3,v4);insert into(c1) values (v1);",
						"WX022220170628000025"));

	}
}
