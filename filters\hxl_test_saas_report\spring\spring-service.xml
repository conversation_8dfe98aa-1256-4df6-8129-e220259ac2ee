<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
       http://www.springframework.org/schema/mvc
       http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd"
       default-autowire="byName">

    <!-- Spring Session parameter interceptor 
    <aop:config>
        <aop:aspect id="aspect" ref="paramInterceptor">
            <aop:pointcut id="pointCut" expression="execution(* com.tzx.*.bo.imp.*.*(..,net.sf.json.JSONObject,..))"/>
            <aop:before pointcut-ref="pointCut" method="filterMethodParam"/>
        </aop:aspect>
    </aop:config>
    <bean id="paramInterceptor" class="com.tzx.framework.interceptor.ParamInterceptor"/>-->

    <!-- 请求参数封装拦截器 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/hq/rest/**"/>
            <bean class="com.tzx.hq.service.interceptor.ParamsInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>

    <!-- Spring Session -->
    <bean id="stringRedisSerializer"
          class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
<!--
    <bean id="redisHttpSessionConfiguration"
          class="org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration">
        <property name="maxInactiveIntervalInSeconds" value="36000" />
    </bean>
-->

</beans>
