package com.tzx.report.bo.imp.boh;

import com.tzx.report.bo.boh.SummaryOfBusinessDaily;
import com.tzx.report.po.boh.dao.SummaryOfBusinessDailyDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */

@Service(SummaryOfBusinessDaily.NAME)
public class SummaryOfBusinessDailyImp implements SummaryOfBusinessDaily {


    @Resource(name = SummaryOfBusinessDailyDao.NAME)
    private SummaryOfBusinessDailyDao summaryOfBusinessDailyDao;

    @Override
    public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
        return summaryOfBusinessDailyDao.find(tenancyID, condition);
    }

    @Override
    public List<JSONObject> getPayMentType(String tenancyID, JSONObject condition) throws Exception {
        return summaryOfBusinessDailyDao.getPayMentType(tenancyID, condition);
    }
}
