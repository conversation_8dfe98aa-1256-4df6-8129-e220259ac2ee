package com.tzx.report.bo.payment.impl;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.UUIDUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.payment.service.XmdApiService;
import com.tzx.report.common.util.XmdUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 * <AUTHOR>
 * @date 2018/6/6
 */
@Service
public class XmdApiServiceImpl implements XmdApiService {

    protected org.slf4j.Logger log = LoggerFactory.getLogger(this.getClass());
    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Override
    public JSONArray checkAndQueryApiDate(int chad,int type) throws Exception{
        //StringBuffer sb = new StringBuffer("SELECT array_to_string(ARRAY(SELECT DISTINCT merchant_id as merchantId FROM sys_payment_account_config WHERE valid_state = '1' AND payment_channel = '6' AND merchant_id IS NOT NULL),',') as merchantId");
        StringBuffer sb = new StringBuffer();
        JSONArray array = new JSONArray();
        try {
            List<String> dateList = XmdUtils.compute3MonthDates();
            StringBuffer sbDate = new StringBuffer();
            for(String date : dateList)
            {
                sbDate.append(",'"+date+"'");
            }
            if(sbDate.length()>0)
            {
                sbDate.delete(0,1);
            }
            sb.append("select sacd.merchant_id as poiid ,sacd.b_date as date  from (select * FROM ( SELECT DISTINCT merchant_id,min(valid_date) as valid_date FROM sys_payment_account_config ");
            sb.append("WHERE valid_state = '1' AND payment_channel = '6' AND merchant_id IS NOT NULL GROUP BY merchant_id)  sac LEFT JOIN unnest(array[");
            sb.append(sbDate.toString());
            sb.append(" ]) as b_date on 1=1 AND sac.valid_date::DATE<= b_date::DATE ");
            sb.append(") sacd LEFT JOIN xmd_api_dates  as xad on sacd.merchant_id::text =xad.merchant_id AND xad.status=1 AND xad.type="+type+" AND xad.cut_date=sacd.b_date::DATE where (xad.id is null or (xad.sync_times <"+chad+" and xad.data_nums=0)) ORDER BY sacd.merchant_id asc, sacd.b_date asc");

            List<JSONObject> list  =dao.query4Json(null,sb.toString());
            for(JSONObject jo: list)
            {
                array.add(jo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return array;
    }


    @Override
    public void updateSettlementDetil(String merchantId, String date2) throws ParseException {
        log.info("updateSettlementDetil===========》start==");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateFormat sdf3 = new SimpleDateFormat("yyyyMMdd");
        DateFormat sdf4 = new SimpleDateFormat("yyyy-MM-dd");
        String[] datearr = date2.split(",");
        for(String date:datearr)
        {
            if("".equals(date))
            {
                break;
            }

            Date time = sdf4.parse(date);
            String cutDay = sdf3.format(time);
            try {
                StringBuilder storeIdSql = new StringBuilder();
                String storeId = "";
                String storeName = "";

                String keyValue = Constant.getSystemMap().get("payment_xmd_key");
                String domain =  Constant.getSystemMap().get("payment_xmd_url");
                String appid = Constant.getSystemMap().get("app_id");

                //获取其他服务商的新美大app_id app_key  service_provider 0 代表天子星 其他>0
                String xsql = "SELECT DISTINCT service_provider FROM sys_payment_account_config WHERE merchant_id='"+merchantId+"'";
                List<JSONObject> xlist = this.dao.query4Json(null,xsql);
                if(xlist.size()>0){
                    JSONObject object = xlist.get(0);
                    String serviceProvider = object.getString("service_provider");
                    if(StringUtils.isNotBlank(serviceProvider) && !"null".equals(serviceProvider) && Integer.parseInt(serviceProvider)>0){
                        List<JSONObject> listProvider = dao.query4Json(null,"select service_app_id,service_app_key from sys_payment_service_provider where type='"+serviceProvider+"'");
                        if(listProvider.size()>0)
                        {
                            JSONObject providerJo = listProvider.get(0);
                            keyValue = providerJo.getString("service_app_key");
                            appid = providerJo.getString("service_app_id");
                        }
                    }
                }

                //String http = "https://openpay.meituan.com/";
                String url = domain + "/api/finance/settle/query";
                SortedMap<String, Object> treeMap = new TreeMap<>();
                treeMap.put("random", UUIDUtil.generateGUID());
                treeMap.put("merchantId", merchantId);
                treeMap.put("cutDay", cutDay);
                treeMap.put("appId", appid);
                String sign = XmdUtils.createSign(keyValue, treeMap);
                treeMap.put("sign", sign);
                String str = JSONObject.fromObject(treeMap).toString();
                log.info("updateSettlementDetil===========request:"+str);
                String result = HttpUtil.sendPostRequest(url, str);
                log.info("updateSettlementDetil===========response:"+result);
                JSONObject resultJsonObj = JSONObject.fromObject(result);
                String status = resultJsonObj.getString("status");
                ArrayList<JSONObject> settlementDetilList = new ArrayList<>();
                int dataNums = 0;
                if ("SUCCESS".equals(status)) {
                    JSONArray settleDetailarray = resultJsonObj.getJSONArray("settleDetailList");
                    if(settleDetailarray.size()>0){
                        log.info("delete xmd_settlement_detail,cut_day:"+date+",merchant_id:"+merchantId);
                        String delsql = "DELETE FROM xmd_settlement_detail WHERE cut_day='"+date+"' AND merchant_id='"+merchantId+"'";
                        this.dao.execute(null,delsql);
                    }
                    for (int i = 0; i < settleDetailarray.size(); i++) {
                        JSONObject jsonObject = settleDetailarray.getJSONObject(i);
                        JSONObject json = new JSONObject();
                        json.put("merchant_id",merchantId);
                        json.put("cut_day",date);
                        Long order_fee = jsonObject.optLong("orderFee");
                        json.put("order_fee",order_fee/100.0);
                        Long settle_fee = jsonObject.optLong("settleFee");
                        json.put("settle_fee",settle_fee/100.0);
                        Long poundage_fee = jsonObject.optLong("poundageFee");
                        json.put("poundage_fee",poundage_fee/100.0);
                        json.put("pay_type",jsonObject.optString("payType"));
                        json.put("trade_no",jsonObject.optString("tradeNo"));
                        json.put("out_trade_no",jsonObject.optString("outTradeNo"));
                        json.put("pay_status",jsonObject.optInt("payStatus"));
                        if (StringUtils.isNotBlank(jsonObject.optString("tradeTime"))){

                            json.put("trade_time",jsonObject.optString("tradeTime"));
                        }
                        json.put("app_id",jsonObject.optString("appId"));
                        json.put("trade_source",jsonObject.optString("tradeSource"));
                        json.put("settle_status",jsonObject.optString("settleStatus"));
                        json.put("store_id",storeId);
                        json.put("store_name",storeName);
                        settlementDetilList.add(json);
                    }
                    if (settlementDetilList.size() != 0) {
                        this.dao.insertBatchIgnorCase(null,"xmd_settlement_detail",settlementDetilList);
                        dataNums = settlementDetilList.size();
                    }

                    String sql = "SELECT *  FROM xmd_api_dates WHERE merchant_id='"+merchantId+"' AND type=1 AND cut_date='"+date+"'";
                    List<JSONObject> list = this.dao.query4Json(null,sql);
                    if(list.size()>0){
                        JSONObject apiDate = list.get(0);

                        String lastDiffDates = apiDate.getString("last_diff_dates");
                        if(!XmdUtils.compareCurrentDate(lastDiffDates)){
                            int syncTimes = apiDate.optInt("sync_times");
                            apiDate.put("sync_times",syncTimes+1);
                            apiDate.put("data_nums",dataNums);
                            apiDate.put("last_diff_dates",sdf4.format(new Date()));
                            this.dao.updateIgnorCase(null,"xmd_api_dates",apiDate);
                        }
                    }else {
                        JSONObject dateJson = new JSONObject();
                        dateJson.put("merchant_id",merchantId);
                        dateJson.put("cut_date",date);
                        dateJson.put("status",1);
                        dateJson.put("type",1);
                        dateJson.put("data_nums",dataNums);
                        dateJson.put("last_diff_dates",sdf4.format(new Date()));
                        dateJson.put("sync_times",1);
                        this.dao.insertIgnorCase(null,"xmd_api_dates",dateJson);
                    }
                }



            } catch (Exception e) {
                e.printStackTrace();
                log.info("updateSettlementDetil===========》error:"+e.getMessage());
            }
            log.info("updateSettlementDetil===========》end==");
        }
    }

    @Override
    public void syncDate(String poiid, String date) throws Exception {
        if(StringUtils.isNotBlank(poiid)){
            updateSettlementDetil(poiid,date);
        }else {
            StringBuffer sb = new StringBuffer("SELECT DISTINCT merchant_id as merchantId FROM sys_payment_account_config WHERE valid_state = '1' AND payment_channel = '6' AND merchant_id IS NOT NULL");
            List<JSONObject> jsonList = this.dao.query4Json(null,sb.toString());
            for(JSONObject object:jsonList){
                String merchantId = object.getString("merchantid");
                updateSettlementDetil(merchantId,date);
            }
        }
    }

    @Override
    public void syncPaymentData(String poiid, String date) throws Exception {
        if(StringUtils.isNotBlank(poiid)){
            updateRemittance(poiid,date);
        }else {
            StringBuffer sb = new StringBuffer("SELECT DISTINCT merchant_id as merchantId FROM sys_payment_account_config WHERE valid_state = '1' AND payment_channel = '6' AND merchant_id IS NOT NULL");
            List<JSONObject> jsonList = this.dao.query4Json(null,sb.toString());
            for(JSONObject object:jsonList){
                String merchantId = object.getString("merchantid");
                updateRemittance(merchantId,date);
            }
        }

    }

    /**
     * 更新打款信息
     * @param poiid
     * @param date  yyyy-mm-dd
     */
    public void updateRemittance(String poiid,String dates) throws Exception {
        String[] datearr = dates.split(",");
        for(String date:datearr){
            date = date.replaceAll("-","");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            DateFormat sdf3 = new SimpleDateFormat("yyyyMMdd");

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            Date time = calendar.getTime();
            if(StringUtils.isNotBlank(date)){
                try {
                    time = sdf3.parse(date);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
            Integer beginPaymentDate = Integer.valueOf(sdf3.format(time));
            Integer endPaymentDate = Integer.valueOf(sdf3.format(time));

            String keyValue = Constant.getSystemMap().get("payment_xmd_key");
            String domain =  Constant.getSystemMap().get("payment_xmd_url");
            String appid = Constant.getSystemMap().get("app_id");


            //获取其他服务商的新美大app_id app_key  service_provider 0 代表天子星 其他>0
            String xsql = "SELECT DISTINCT service_provider FROM sys_payment_account_config WHERE merchant_id='"+poiid+"'";
            List<JSONObject> xlist = this.dao.query4Json(null,xsql);
            if(xlist.size()>0){
                JSONObject object = xlist.get(0);
                String serviceProvider = object.getString("service_provider");
                if(StringUtils.equals(serviceProvider,"null")) serviceProvider = "0";
                if(StringUtils.isNotBlank(serviceProvider) && Integer.parseInt(serviceProvider)>0){
                    List<JSONObject> listProvider = dao.query4Json(null,"select service_app_id,service_app_key from sys_payment_service_provider where type='"+serviceProvider+"'");
                    if(listProvider.size()>0)
                    {
                        JSONObject providerJo = listProvider.get(0);
                        keyValue = providerJo.getString("service_app_key");
                        appid = providerJo.getString("service_app_id");
                    }
                }
            }

            String url = domain + "/api/finance/payment/query";
            SortedMap<String, Object> treeMap = new TreeMap<>();
            treeMap.put("random", UUIDUtil.generateGUID());
            treeMap.put("merchantId", poiid);
            treeMap.put("beginPaymentDate", beginPaymentDate);
            treeMap.put("endPaymentDate", endPaymentDate);
            treeMap.put("appId", appid);
            String sign = XmdUtils.createSign(keyValue, treeMap);
            treeMap.put("sign", sign);
            String str =JSONObject.fromObject(treeMap).toString();
            log.info("updateRemittanceDetil===========request:"+str);
            String result = HttpUtil.sendPostRequest(url, str);
            log.info("updateRemittanceDetil===========response:"+result);
            JSONObject resultJsonObj = JSONObject.fromObject(result);
            String status = resultJsonObj.getString("status");
            String existSql = "SELECT * FROM xmd_remittance_detail WHERE payment_id=";
            if ("SUCCESS".equals(status)) {
                JSONArray remittanceDetailarray = resultJsonObj.getJSONArray("paymentList");
                int dataNums = 0;
                for (int i = 0; i < remittanceDetailarray.size(); i++) {
                    JSONObject jsonObject = remittanceDetailarray.getJSONObject(i);

                    JSONObject json = new JSONObject();
                    json.put("merchant_id",poiid);

                    String periodStartTime = "";
                    if(StringUtils.isNotBlank(jsonObject.optString("periodStartTime")) && !StringUtils.equals("null",jsonObject.optString("periodStartTime"))){
                        periodStartTime = sdf2.format(jsonObject.optLong("periodStartTime"));
                    }
                    json.put("period_start_time",periodStartTime);

                    String periodEndTime = "";
                    if(StringUtils.isNotBlank(jsonObject.optString("periodEndTime")) && !StringUtils.equals("null",jsonObject.optString("periodEndTime"))){
                        periodEndTime = sdf2.format(jsonObject.optLong("periodEndTime"));
                    }
                    json.put("period_end_time",periodEndTime);
                    json.put("trade_money",BigDecimal.valueOf(jsonObject.optLong("tradeMoney") / 100.0));
                    json.put("poundage_money",BigDecimal.valueOf(jsonObject.optLong("poundageMoney") / 100.0));
                    json.put("settle_money",BigDecimal.valueOf(jsonObject.optLong("settleMoney") / 100.0));
                    json.put("payment_date",jsonObject.optString("paymentDate"));
                    json.put("pay_channel",jsonObject.optString("payChannel"));
                    json.put("pay_status",jsonObject.optInt("payStatus"));
                    json.put("card_no",jsonObject.optString("cardNo"));
                    json.put("name",jsonObject.optString("name"));
                    String paymentId = jsonObject.optString("paymentId");
                    json.put("payment_id",paymentId);
                    existSql += "'"+paymentId+"'";

                    String paymentFinishTime = null;
                    if(StringUtils.isNotBlank(jsonObject.optString("paymentFinishTime")) && !StringUtils.equals("null",jsonObject.optString("paymentFinishTime"))){
                        paymentFinishTime = jsonObject.optString("paymentFinishTime");
                        json.put("payment_finish_time",jsonObject.optString("paymentFinishTime"));
                    }
                    json.put("create_time",sdf2.format(DateUtil.currentTimestamp()));
                    json.put("status","1");

                    int payStatus = jsonObject.optInt("payStatus");
                    if(5 == payStatus){
                        json.put("fail_reason_code",jsonObject.optString("failReasonCode"));
                        json.put("fail_reason_rategory",jsonObject.optString("failReasonCategory"));
                        json.put("fail_show_reason",jsonObject.optString("failShowReason"));
                        json.put("fail_detail_reason",jsonObject.optString("failDetailReason"));
                    }else if(4 == payStatus){
                       /* //打款成功获取成功打款的明细 更新结算数据
                        String queryUrl = domain + "/api/finance/settle/query/byId";
                        SortedMap<String, Object> queryTreeMap = new TreeMap<>();
                        queryTreeMap.put("random", UUIDUtil.generateGUID());
                        queryTreeMap.put("merchantId", poiid);
                        queryTreeMap.put("paymentId", paymentId);
                        queryTreeMap.put("pageSize", Integer.MAX_VALUE);
                        queryTreeMap.put("appId", appid);
                        String signStr = XmdUtils.createSign(keyValue, queryTreeMap);
                        queryTreeMap.put("sign", signStr);
                        String requestStr =JSONObject.fromObject(queryTreeMap).toString();
                        log.info("finance/settle/query/byId ===========request:"+requestStr);
                        String resultStr = HttpUtil.sendPostRequest(queryUrl, requestStr);
                        log.info("finance/settle/query/byId ===========response:"+resultStr);
                        JSONObject resultObj = JSONObject.fromObject(resultStr);
                        String rStatus = resultObj.getString("status");
                        if("SUCCESS".equals(rStatus)){

                        }*/
                       //如果当日更新成功
                    }


                    String paymentStatus = "打款成功";
                    String upSql = "";

                    switch (payStatus){
                        case 1:
                            paymentStatus = "新建打款";
                            break;
                        case 2:
                            paymentStatus = "打款已受理";
                            break;
                        case 4:
                            if(StringUtils.isNotBlank(paymentFinishTime)){
                                upSql = "UPDATE xmd_settlement_detail SET payment_finish_time='"+paymentFinishTime+"'::TIMESTAMP,payment_status='"+paymentStatus+"' WHERE trade_time>='"+periodStartTime+"'::TIMESTAMP  and trade_time<='"+periodEndTime+"'::TIMESTAMP";
                            }
                            dataNums = 1;
                            break;
                        case 5:
                            paymentStatus="打款失败,"+jsonObject.optString("failDetailReason");
                            break;
                    }
                    if(StringUtils.isEmpty(upSql)){
                        upSql = "UPDATE xmd_settlement_detail SET payment_status='"+paymentStatus+"' WHERE trade_time>='"+periodStartTime+"'::TIMESTAMP  and trade_time<='"+periodEndTime+"'::TIMESTAMP";
                    }
                    log.debug("update  settlement sql:"+upSql);
                    this.dao.execute(null,upSql);

                    List<JSONObject> list = this.dao.query4Json(null,existSql);
                    //存在就更新
                    if (list.size()>0) {
                        this.dao.updateIgnorCase(null,"xmd_remittance_detail",json);
                    } else {
                        this.dao.insertIgnorCase(null,"xmd_remittance_detail",json);
                    }
                }

                String sql = "SELECT *  FROM xmd_api_dates WHERE merchant_id='"+poiid+"' AND type=2 AND cut_date='"+date+"'";
                List<JSONObject> slist = this.dao.query4Json(null,sql);
                if(slist.size()>0){
                    JSONObject apiDate = slist.get(0);

                    String lastDiffDates = apiDate.getString("last_diff_dates");
                    if(!XmdUtils.compareCurrentDate(lastDiffDates)){
                        int syncTimes = apiDate.optInt("sync_times");
                        apiDate.put("sync_times",syncTimes+1);
                        apiDate.put("data_nums",dataNums);
                        apiDate.put("last_diff_dates",sdf3.format(new Date()));
                        this.dao.updateIgnorCase(null,"xmd_api_dates",apiDate);
                    }
                }else {
                    JSONObject dateJson = new JSONObject();
                    dateJson.put("merchant_id",poiid);
                    dateJson.put("cut_date",date);
                    dateJson.put("status",1);
                    dateJson.put("type",2);
                    dateJson.put("data_nums",dataNums);
                    dateJson.put("last_diff_dates",sdf3.format(new Date()));
                    dateJson.put("sync_times",1);
                    this.dao.insertIgnorCase(null,"xmd_api_dates",dateJson);
                }
            }
        }
    }


    @Override
    public void repairReportDate() throws Exception {
        String checkSql = "select id from pos_payment_order where report_date ='' or report_date is null LIMIT 1";
        List<JSONObject> list = this.dao.query4Json(null,checkSql);
        if(list.size()<=0) return;

        String exesql1 = "update pos_payment_order set report_date=last_updatetime::date where store_id='0' and (report_date='' or report_date is null)";
        String exesql2 = "update pos_payment_order a set report_date=tt.report_date from (select b.report_date,split_part(a.order_num,'-',2) as bill_num from pos_payment_order a" +
                " LEFT JOIN pos_bill2 b on  (split_part(a.order_num,'-',2) =b.bill_num) or (substr(split_part(a.order_num,'-',2),3)=b.bill_num) " +
                " where a.store_id<>'0' and a.report_date is null) tt where  a.store_id<>'0' and a.report_date is null " +
                "and split_part(a.order_num,'-',2) =tt.bill_num";
        String exesql3 = "update pos_payment_order a set report_date=tt.report_date from (select b.report_date,split_part(a.order_num,'-',2) as bill_num from pos_payment_order a" +
                " LEFT JOIN pos_bill2 b on  (split_part(a.order_num,'-',2) =b.bill_num) or (substr(split_part(a.order_num,'-',2),3)=b.bill_num) " +
                " where a.store_id<>'0' and a.report_date='') tt where  a.store_id<>'0' and a.report_date='' " +
                "and split_part(a.order_num,'-',2) =tt.bill_num";
        String select = "SELECT id FROM pos_payment_order WHERE (report_date is NULL or report_date='') AND out_trade_no in (select DISTINCT out_trade_no from xmd_settlement_detail)";
        String updateSettle="UPDATE pos_payment_order a  set report_date=b.cut_day  from xmd_settlement_detail b  where a.out_trade_no=b.out_trade_no " +
                    " and (a.report_date is NULL or a.report_date='')";

        this.dao.execute(null,exesql1);
        this.dao.execute(null,exesql2);
        this.dao.execute(null,exesql3);

        List<JSONObject> slist = this.dao.query4Json(null,select);
        if(slist.size()<=0){
            this.dao.execute(null,updateSettle);
        }
    }
}
