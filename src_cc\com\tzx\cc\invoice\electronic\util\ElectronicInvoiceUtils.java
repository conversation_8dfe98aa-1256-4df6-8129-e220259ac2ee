package com.tzx.cc.invoice.electronic.util;

import com.tzx.cc.invoice.electronic.cont.ElectronicInvoiceConst;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.om.po.OmRedisTaskDao;
import com.tzx.om.po.imp.OmRedisTaskDaoImpl;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.apache.poi.util.StringUtil;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.Set;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 *
 */
public class ElectronicInvoiceUtils {
    private static final Logger logger = Logger
            .getLogger(ElectronicInvoiceUtils.class);
    /**
     *  上传数据到OM
     */
	public static void uploadOm(JSONObject json) throws Exception{
		JSONObject dest = new JSONObject();
		dest.put("store_id",json.optString("organ_id"));
		copySrc2Dest4jsonOption(json,dest,"tenancy_id","invoice_flow_number","invoice_type","total_tax_amount",
				"tax_amount","total_amount","invoice_service_type","order_code");
		OmRedisTaskDao au = SpringConext.getApplicationContext().getBean(OmRedisTaskDaoImpl.class);
		if(StringUtils.equals(json.optString("invoice_state"),
				ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS)) {
			dest.put("invoice_time",json.optString("invoice_time"));
			//1为开票  0为取消
			dest.put("invoice_om_state","1");
			au.checkoutInvoiseSuccess(json.optString("tenancy_id"),dest);
		} else {
			dest.put("invoice_cancel_time",json.optString("invoice_cancel_time"));
			dest.put("invoice_om_state","0");
			au.cancelInvoise(json.optString("tenancy_id"),dest);
		}
		logger.info("电子发票给om上传数据的json串儿为"+dest.toString());
    }
	/**
	 * 看传递过来的订单日期是否已过期
	 * @param ddrq   yyyy-MM-dd
	 * @param day   几天
	 * @return
	 * @throws ParseException 
	 */
	public static boolean isExpire(String ddrq,int day) throws ParseException{
		Date ddrqDate = DateUtils.parseDate(ddrq, new String[]{"yyyy-MM-dd"});
		Calendar ddrqcal = Calendar.getInstance();
		ddrqcal.setTime(ddrqDate);
		ddrqcal.add(Calendar.DAY_OF_MONTH, day);
		if(ddrqcal.compareTo(Calendar.getInstance()) < 0){
			return true;
		}
		return false;
	}
	
	/**
	 * 把Ali包下的json转换为net下的json
	 * @param json
	 * @return
	 */
	public static JSONObject convertAli2netJson(com.alibaba.fastjson.JSONObject json){
		JSONObject netJson = new JSONObject();
		
		Set<Entry<String, Object>> entrySet = json.entrySet();
		Iterator<Entry<String, Object>> iterator = entrySet.iterator();
		while(iterator.hasNext()) {
			Entry<String, Object> next = iterator.next();
			netJson.put(next.getKey(), next.getValue());
		}
		return netJson;
	}
	
	/**
	 * 两个json可选择的进行copy键值
	 * @param src 源
	 * @param dest 目标
	 * @param names 要copy的json的key, 若names为空，则是全部copy
	 */
	@SuppressWarnings("rawtypes")
	public static void copySrc2Dest4jsonOption(JSONObject src,JSONObject dest,String ... names){
		Set keySet = src.keySet();
		Iterator iterator = keySet.iterator();
		while(iterator.hasNext()){
			String key = (String) iterator.next();
			if(names==null || names.length == 0) {
				Object value = src.opt(key);
				dest.put(key, value);
			} else {
				for(String name:names) {
					if(StringUtils.equals(name, key)) {
						Object value = src.opt(key);
						dest.put(key, value);
						break;
					}
				}
			}
		}
	}
	/**
     * 获取当前时间字符串格式，用于存在数据库中  Timestamp类型
     * @return
     */
    public static String currentTime2Str(){
        Timestamp current = new Timestamp(System.currentTimeMillis());
        return current.toString();
    }
}
