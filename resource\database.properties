jdbc.druid.url=*****************************************************
jdbc.druid.username=postgres
jdbc.druid.password=q1w2e3r4

# 连接池初始、最小、最大连接数
jdbc.druid.initialSize=2
jdbc.druid.minIdle=2
jdbc.druid.maxActive=16
# 获取连接最大等待时间
jdbc.druid.maxWait=3000
# 连接池连接检测策略，只在空闲时检测
jdbc.druid.validationQuery=SELECT 1
jdbc.druid.testWhileIdle=true
jdbc.druid.testOnBorrow=false
jdbc.druid.testOnReturn=false
# 回收空闲连接的检测时间间隔
jdbc.druid.timeBetweenEvictionRunsMillis=60000
# 连接保持空闲而不被驱逐的最长时间，配置较大的值避免频繁创建/关闭连接
jdbc.druid.minEvictableIdleTimeMillis=300000
# 对于mysql，官方建议不打开
jdbc.druid.poolPreparedStatements=false
