package com.tzx.report.bo.imp.crm;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.crm.MemberRechargeConsumptionService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.common.util.ReportExportUtils;

@Service(MemberRechargeConsumptionService.NAME)
public class MemberRechargeConsumptionServiceImp implements MemberRechargeConsumptionService
{
	// 主查询
	private final String sqlMain0 ="select sql from saas_report_engine where  report_num ='SAAS_BI_2016_32' and sql_type='L1'";
	// 明细查询
	private final String sqlInfor1 ="select sql from saas_report_engine where  report_num ='SAAS_BI_2016_32' and sql_type='L2'";
	// 明细合计查询
	private final String sqlInfor0 ="select sql from saas_report_engine where  report_num ='SAAS_BI_2016_32' and sql_type='L0'";

	// 主查询合计
	private final String sqlMainSum ="select sql from saas_report_engine where  report_num ='SAAS_BI_2016_32' and sql_type='L3'";
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	
	@Override
	public JSONObject getMemberRechargeConsumption(String tenancyID,
			JSONObject condition) throws Exception {
		// TODO Auto-generated method stub
		Integer type = condition.optInt("selectType");
		List<JSONObject> list = new ArrayList<JSONObject>();
		String begindate = condition.optString("begin_date");
		String enddate = condition.optString("end_date");
		String reportSql = "";
		String reportCount = "";
		JSONObject result = new JSONObject();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		Long total =0L;
		if(type==1) {
			if(begindate.length()>0 && enddate.length()>0 )
			{
				reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlMain0);
				total = this.dao.countSql(tenancyID,reportSql.toString());
				list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));

				// 合计
				reportCount= parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlMainSum);
				footerList = this.dao.query4Json(tenancyID, reportCount.toString());
			}
		}
		if(type==2){
			reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlInfor1);
			total = this.dao.countSql(tenancyID,reportSql.toString());
			list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
			
			// 合计
			reportCount= parameterUtils.parameterAutomaticCompletion(tenancyID, condition,sqlInfor0);
			footerList = this.dao.query4Json(tenancyID, reportCount.toString());
		}
		
		if (type==3) {
			
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("rows", list);
		result.put("total",total);	
		result.put("footer", footerList);
		return result;
	}

	@Override
	public HSSFWorkbook exportDate(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception {
		// TODO Auto-generated method stub
		Integer rowNum=2;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",1);
		JSONObject findResult= getMemberRechargeConsumption(attribute, p);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		  JSONObject out1Result =null;
		  
				//创建sheet 表格   同时还可以设置名字!  
				  HSSFSheet sheet1=workBook.createSheet("会员充值消费报表");
//				  String [] listTitleName ={"注册机构","会员姓名","会员号码","会员卡号","充值次数","主帐户","赠送帐户","消费次数","主帐户","赠送帐户","主帐户","赠送帐户",};
//				  String [] dataName ={"register_name","name","mobil","card_num","cz_num","cz_main","cz_reward","xf_num","xf_main","xf_reward","main_balance","reward_balance"};
//				  String [] dataType ={"String","String","String","String","0","0.00","0.00","0","0.00","0.00","0.00","0.00"};
				 
//				  String [] listTitleName ={"注册机构","交易机构","会员姓名","入会日期","最后一次消费时间","会员级别","会员状态","当前积分","累计积分","消费频率","手机号码","会员卡号","充值次数","主帐户","赠送帐户","消费次数","主帐户","赠送帐户","主帐户","赠送帐户"};
//				  String [] dataName ={"register_name","trade_name","name","begin_date","last_paydate","grade","state","now_jf","all_jf","trade_rete","mobil","card_num","cz_num","cz_main","cz_reward","xf_num","xf_main","xf_reward","main_balance","reward_balance"};
//				  String [] dataType ={"String","String","String","String","String","String","String","0.00","0.00","0.00","String","String","0","0.00","0.00","0","0.00","0.00","0.00","0.00"};
		String [] listTitleName ={"注册机构","交易机构","会员姓名","入会日期","最后一次消费时间","会员级别","会员状态","当前积分","累计积分","手机号码","会员卡号","充值次数","主帐户","赠送帐户","消费次数","主帐户","赠送帐户","主帐户","赠送帐户"};
		String [] dataName ={"register_name","trade_name","name","begin_date","last_paydate","grade","state","now_jf","all_jf","mobil","card_num","cz_num","cz_main","cz_reward","xf_num","xf_main","xf_reward","main_balance","reward_balance"};
		String [] dataType ={"String","String","String","String","String","String","String","0.00","0.00","String","String","0","0.00","0.00","0","0.00","0.00","0.00","0.00"};
				  
				if(list1.size()>0){
					for(JSONObject json1 : list1) {
						// 调用到处方法；
						out1Result =ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
						paramData.put("rowNum", out1Result.opt("rowNum"));
						paramData.put("jin", out1Result.optInt("jin"));
					}
				}
				
				HSSFRow rowtitle =sheet1.createRow(0);
				
				List<JSONObject> titleArr = new ArrayList() ;
				JSONObject sonJson =new JSONObject();
				sonJson.put("titleName", "充值金额");
				sonJson.put("index", 13);
				sonJson.put("end", 14);
				titleArr.add(sonJson);
				
				JSONObject sonJson2 =new JSONObject();
				sonJson2.put("titleName", "余额");
				sonJson2.put("index", 18);
				sonJson2.put("end", 19);
				titleArr.add(sonJson2);
				
				JSONObject sonJson3 =new JSONObject();
				sonJson3.put("titleName", "消费金额");
				sonJson3.put("index", 16);
				sonJson3.put("end", 17);
				titleArr.add(sonJson3);
				
				sheet1 =ReportExportUtils.mergrColumn(workBook,sheet1,rowtitle,titleArr);
				
				HSSFRow rowtitle2 =sheet1.getRow(1);
				Integer [] valueNum ={0,1,2,3,4,5,6,7,8,9,10,11,12,15};
				//合并上下的行 限制一列
				sheet1 =ReportExportUtils.upOrDownMergr(workBook, sheet1, valueNum, rowtitle2, rowtitle, 0, 1);
				
				sheet1.groupRow(1,out1Result.optInt("rowNum"));
				sheet1.setRowSumsBelow(false);
				sheet1.setRowSumsRight(false);
		return workBook;
	}
	
}
