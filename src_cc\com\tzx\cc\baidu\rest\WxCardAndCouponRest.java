package com.tzx.cc.baidu.rest;

import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.tzx.cc.baidu.bo.WxCardAndCouponService;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.framework.common.util.DateUtil;

@Controller("WxCardAndCouponRest")
@RequestMapping("/cc/wxCardAndCoupon")
public class WxCardAndCouponRest {
	
	@Resource(name = WxCardAndCouponService.NAME)
	private WxCardAndCouponService wxCardAndCouponService;
	

	@RequestMapping(value = "/findWXKQdglist")
	public @ResponseBody JSONObject thirdShopCreateOrUpdate(HttpServletRequest request, HttpServletResponse response)
	{
		HttpSession session = request.getSession();
		JSONObject obj = JSONObject.fromObject("{}");
		Map<String, String[]> map = request.getParameterMap();
		for (String key : map.keySet())
		{
			obj.put(key, map.get(key)[0]);
		}

		String tenantId = (String) session.getAttribute("tenentid");

		JSONObject json = new JSONObject();

		try{

			json = wxCardAndCouponService.findWXKQdglist(tenantId, obj);
			
		}catch (Exception e){
			e.printStackTrace();
		}

		return json;
	}
	
	
	@RequestMapping(value = "/findProvinceDetails")
	public  void findProvinceDetails(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		JSONObject obj = JSONObject.fromObject("{}");
		Map<String, String[]> map = request.getParameterMap();
		PrintWriter out = null;
		for (String key : map.keySet())
		{
			obj.put(key, map.get(key)[0]);
		}

		String tenantId = (String) session.getAttribute("tenentid");

		JSONObject json = new JSONObject();
		json.put("success", true);
		List<JSONObject> list = null;
		try{

			list = wxCardAndCouponService.findProvinceDetails(tenantId, obj);
			out = response.getWriter();
		}catch (Exception e){
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(list.toString());
				out.flush();
				out.close();
			}
		}
	}
	
	
	@RequestMapping(value = "/findStoreCategories")
	public  void findStoreCategories(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		JSONObject obj = JSONObject.fromObject("{}");
		Map<String, String[]> map = request.getParameterMap();
		PrintWriter out = null;
		for (String key : map.keySet())
		{
			obj.put(key, map.get(key)[0]);
		}

		String tenantId = (String) session.getAttribute("tenentid");

		JSONObject json = new JSONObject();
		json.put("success", true);
		List<JSONObject> list = null;
		try{

			list = wxCardAndCouponService.findStoreCategories(tenantId, obj);
			out = response.getWriter();
		}catch (Exception e){
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(list.toString());
				out.flush();
				out.close();
			}
		}
	}
	
	
	
	@RequestMapping(value = "/saveInfoUrl")
	public  void saveInfoUrl(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		JSONObject obj = JSONObject.fromObject("{}");
		Map<String, String[]> map = request.getParameterMap();
		PrintWriter out = null;
		for (String key : map.keySet())
		{
			obj.put(key, map.get(key)[0]);
		}
		obj.put("last_operator", session.getAttribute("employeeName"));
		obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
		String tenantId = (String) session.getAttribute("tenentid");
		obj.put("tenancy_id", tenantId);
		obj.put("valid_state", "1");
		
		JSONObject json = new JSONObject();
		json.put("success", true);
		json.put("msg", "保存成功");
		List<JSONObject> list = null;
		try{

			wxCardAndCouponService.saveInfoUrl(tenantId, obj);
			out = response.getWriter();
		}catch (Exception e){
			json.put("success", false);
			json.put("msg", "保存成功");
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(json.toString());
				out.flush();
				out.close();
			}
		}
	}
	
	
	@RequestMapping(value = "/findAreaDetailsUrl")
	public  void findAreaDetailsUrl(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		JSONObject obj = JSONObject.fromObject("{}");
		Map<String, String[]> map = request.getParameterMap();
		PrintWriter out = null;
		for (String key : map.keySet())
		{
			obj.put(key, map.get(key)[0]);
		}

		String tenantId = (String) session.getAttribute("tenentid");

		JSONObject json = new JSONObject();

		List<JSONObject> list = null;
		try{

			json = wxCardAndCouponService.findAreaDetailsUrl(tenantId, obj);
			json.put("success", true);
			out = response.getWriter();
		}catch (Exception e){
			json.put("success", false);
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(json.toString());
				out.flush();
				out.close();
			}
		}
	}
	
	
	
	
	
	
	@RequestMapping(value = "/checkSynchroResultUrl")
	public  void checkSynchroResult(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		JSONObject obj = JSONObject.fromObject("{}");
		Map<String, String[]> map = request.getParameterMap();
		PrintWriter out = null;
		for (String key : map.keySet())
		{
			obj.put(key, map.get(key)[0]);
		}
		obj.put("last_operator", session.getAttribute("employeeName"));
		obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
		String tenantId = (String) session.getAttribute("tenentid");
		obj.put("tenancy_id", tenantId);
		obj.put("valid_state", "1");
		
		JSONObject json = new JSONObject();
		json.put("success", true);
		json.put("msg", "查询成功");
		List<JSONObject> list = null;
		try{

			json = wxCardAndCouponService.checkSynchroResult(tenantId, obj);
			out = response.getWriter();
		}catch (Exception e){
			json.put("success", false);
			json.put("msg", "查询失败");
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(json.toString());
				out.flush();
				out.close();
			}
		}
	}
	
}
