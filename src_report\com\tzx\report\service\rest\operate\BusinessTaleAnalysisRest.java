package com.tzx.report.service.rest.operate;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.operate.BusinessTaleAnalysisService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ReportExportUtils;


@Controller("BusinessTaleAnalysisRest")
@RequestMapping("/report/BusinessTaleAnalysisRest")
public class BusinessTaleAnalysisRest
{
	
	@Resource(name = BusinessTaleAnalysisService.NAME)
	private BusinessTaleAnalysisService businessTaleAnalysisService;
	
	
	@Resource(name = "conditionUtils")
	ConditionUtils conditionUtils;
	
	@RequestMapping(value = "/find")
	@ApiOperation(value = "营业桌位分析",consumes= "multipart/form-data" ,httpMethod="POST",notes = "营业桌位分析")
	@ApiImplicitParams({
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "report_date_begin",value = "开始日期",required = true,defaultValue="2017-01-01"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "report_date_end",value = "结束日期",required = true,defaultValue="2017-12-31"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "store_ids",value = "交易门店",defaultValue="9,11"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "business_area_ids",value = "营业区域",defaultValue="744,746"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "table_property_ids",value = "营业餐位",defaultValue="10045,10046,754"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "table_codes",value = "营业桌位",defaultValue="'001,002,005,006'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "duty_order_ids",value = "营业班次",defaultValue="'2,3,4'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "sort",value = "排序字段",defaultValue="id"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "order",value = "升/降序",defaultValue="ASC"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "prenttotal",value = "",defaultValue="000000"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "iszore",value = "包含零值0/1",defaultValue="0"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "report_type",value = "未知参数",defaultValue="TABLE"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "hierarchytype",value = "查询层级",required = true,defaultValue="1"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "exportdataexpr",value = "导出",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "page",value = "页码",required = true,defaultValue="1"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "rows",value = "每页行数",required = true,defaultValue="10")
	})
	public void find(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
					p.put(key, map.get(key)[0]);
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
				if(p.optString("store_ids").length()==0){
					p.element("store_ids", session.getAttribute("user_organ_codes_group"));
				}
			}else{
				if(p.optString("store_ids").length()==0){
					p.element("store_ids", session.getAttribute("user_organ"));
				}
			}
			result = businessTaleAnalysisService.find((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	@RequestMapping(value = "/getModeOfPayment")
	public void getModeOfPayment(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			result = conditionUtils.getModeOfPaymentCommon((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}

	@RequestMapping(value = "/exportDate")
	public void exportDate(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{

			workBook = new HSSFWorkbook();
		       
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			if(p.optString("store_id").length()==0){
				p.element("store_id", session.getAttribute("user_organ_codes_group"));
			}	
			
			workBook = exportDate((String) session.getAttribute("tenentid"), p ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			ReportExportUtils.download(workBook,response,"营业桌位分析");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}

	/**
	 * 班次
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getloadDutyOrderNew")
	public void getloadDutyOrderNew(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();
			
			for (String key : map.keySet())
			{
				if(!"".equals(map.get(key)[0]) && map.get(key)[0]!=null)
				{
					obj.put(key, map.get(key)[0]);
				}
			}
			obj.put("store_id",session.getAttribute("store_id"));
			result = conditionUtils.loadDutyOrderNew((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\":false,\"msg\":\"" + e.getMessage() + "\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}
			
			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 营业区域
	 * @param request
	 * @param response
	 * @throws IOException
	 * @throws WriteException
	 */
	@RequestMapping(value = "/getBusinessArea")
	public void getBusinessArea(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			result = conditionUtils.getBusinessAreaCommon((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	//餐位类别
	@RequestMapping(value = "/getMealPositionType")
	public void getMealPositionType(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			result = conditionUtils.getMealPositionTypeCommon((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	// 餐位座位getBusinessSeat
	@RequestMapping(value = "/getBusinessSeat")
	public void getBusinessSeat(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			result = conditionUtils.getBusinessSeat((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	public HSSFWorkbook exportDate(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception {
		// TODO Auto-generated method stub
		Integer rowNum=2;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",1);
		Integer stratIndex = 0 ;
		Integer stratIndex2 = 0 ;
		JSONObject findResult= (JSONObject) businessTaleAnalysisService.find(attribute, p);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		  JSONObject out1Result =null;
		  JSONObject out1Result2 =null;
		  JSONObject out1Result3 =null;
		  String serviceOrBao ="服务费";
		  String tenentIdVar = "";//截取商户号
		  String tableCodes = new String(p.optString("table_codes")); 
		  if(attribute.length()>3){
			  tenentIdVar = attribute.substring(0, 4);
		  }
		  if(tenentIdVar.equals("judh")||tenentIdVar.equals("jdht")||attribute.equals("emeijiujia")||attribute.equals("dianmenemeijiujia")||attribute.equals("deneiemeijiujia")) {
			  serviceOrBao="包间费";
		  }
				//创建sheet 表格   同时还可以设置名字!  
				  HSSFSheet sheet1=workBook.createSheet("营业桌位分析");
				  String [] listTitleName ={"交易门店","桌位编号","桌位名称","报表日期","账单数量","账单均值","消费客数","人均消费","桌台数","翻台率","座位数","上座率","应收占比","营业应收","消费流水","菜品消费",serviceOrBao,"退菜","折扣","折让","奉送","抹零","多收礼券","营业实收"};
				  String [] dataName ={"org_full_name","table_code","table_name","report_date","bill_num","sale_billaverage","guest","average_amount","tables_num","num_ft","seat_num","num_sz","sale_total_zb","sale_total","item_sale","item_amount","service_fee_income","back_money_item","discount_money","reduction_money","free_money","moling_money","coupons_ds","real_amount"};
				  String [] dataType ={"String","String","String","String","0.00","0.00","0","0.00","0","0.00%","0","0.00%","0.00%","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00"};
				  
				if(list1.size()>0){
					for(JSONObject json1 : list1) {
						p.put("table_codes", tableCodes);
						// 调用到处方法；
						json1.put("table_code", "");
						json1.put("table_name", "");
						json1.put("REPORT_DATE", "");
						out1Result =ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
						stratIndex =out1Result.optInt("rowNum");
						paramData.put("rowNum", out1Result.opt("rowNum"));
						paramData.put("jin", out1Result.optInt("jin"));
						p.put("hierarchytype",2);
						p.put("prenttotal", json1.opt("sale_total"));
						p.put("store_ids", json1.opt("id"));
						p.put("rows", 999999);
						JSONObject findResult2=(JSONObject) businessTaleAnalysisService.find(attribute, p);
						List<JSONObject> list2=(List<JSONObject>) findResult2.opt("rows");
						if(list2.size()>0) {
							
							for(JSONObject json2 : list2) {
								//System.out.println(json2);
								json2.put("org_full_name", "");
								json2.put("REPORT_DATE", "");
								out1Result2 = ReportExportUtils.out1(json2,workBook,sheet1,null,dataName,dataType,paramData);
								stratIndex2=out1Result2.optInt("rowNum");
								paramData.put("rowNum", out1Result2.opt("rowNum"));
								paramData.put("jin", out1Result2.optInt("jin"));
								p.put("hierarchytype",3);
								p.put("table_codes", "'"+json2.opt("table_code")+"'");
								p.put("prenttotal", json2.opt("sale_total"));
								p.put("rows", 999999);
								JSONObject findResult3=(JSONObject) businessTaleAnalysisService.find(attribute, p);
								List<JSONObject> list3 =(List<JSONObject>) findResult3.opt("rows");
								for(JSONObject json3 : list3) {
									
									json3.put("org_full_name", "");
									json3.put("table_code", "");
									json3.put("table_name", "");
									 out1Result3 =ReportExportUtils.out1(json3,workBook,sheet1,null,dataName,dataType,paramData);
									 paramData.put("rowNum", out1Result3.opt("rowNum"));
									 paramData.put("jin", out1Result3.optInt("jin"));
									
								}
								
								sheet1.groupRow(stratIndex2,out1Result3.optInt("rowNum"));
							}
							sheet1.groupRow(stratIndex,out1Result3.optInt("rowNum"));
						}
					}
				}
				 
				    
		            HSSFCellStyle style = workBook.createCellStyle();      
		            HSSFFont font =workBook.createFont();
				    //设置字体格式
				    font.setFontName("Aharoni");
				    font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
				    style.setFont(font);
		            style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直      
		            style.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平      
		            
				HSSFRow rowtitle =sheet1.createRow(0);
				
				HSSFCell cellTitle = rowtitle.createCell(4);
				cellTitle.setCellValue("营业指标");
				cellTitle.setCellStyle(style);
				sheet1.addMergedRegion(new CellRangeAddress(0,0,4,12));
				
				HSSFCell cellTitle2 = rowtitle.createCell(18);
				cellTitle2.setCellValue("优惠");
				cellTitle2.setCellStyle(style);
				sheet1.addMergedRegion(new CellRangeAddress(0,0,18,21));
				
				HSSFRow rowtitle2 =sheet1.getRow(1);
				Integer [] valueNum ={0,1,2,3,13,14,15,16,17,22,23};
				
				HSSFCellStyle style1 = workBook.createCellStyle(); 
			//	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
				style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
				style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
				style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
				style1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直      
	            style1.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平      	
				    HSSFFont font1 =workBook.createFont();
				    //设置字体格式
				    font1.setFontName("Aharoni");
				    font1.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
				style1.setFont(font);
				for(Integer value :valueNum) {
					String cellValue = rowtitle2.getCell(value).getStringCellValue();
					HSSFCell createCell = rowtitle.createCell(value);
					createCell.setCellValue(cellValue);
					createCell.setCellStyle(style1);
					sheet1.addMergedRegion(new CellRangeAddress(0,1,value,value));
					 
				}
				
				sheet1.groupRow(1,out1Result3.optInt("rowNum"));
				sheet1.setRowSumsBelow(false);
				sheet1.setRowSumsRight(false);
				//按门店查询
		return workBook;
	
	}
	
}
