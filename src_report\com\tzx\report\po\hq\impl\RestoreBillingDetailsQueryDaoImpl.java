package com.tzx.report.po.hq.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.hq.dao.RestoreBillingDetailsQueryDao;

@Repository
public class RestoreBillingDetailsQueryDaoImpl implements RestoreBillingDetailsQueryDao{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	
	List<JSONObject> list = new ArrayList<JSONObject>();
	List<JSONObject> footerList =new ArrayList<JSONObject>();
	List<JSONObject> structure = new ArrayList<JSONObject>();
	JSONObject result = new JSONObject();
	long total = 0L;
 

	@Override
	public JSONObject getRestoreBillingDetailsQuery(String tenantId,JSONObject condition) throws Exception {
		String Csql = "";
		if(condition.containsKey("exportdataexpr")){
			String exp = condition.optString("exportdataexpr");
			if(exp.equals("")){
				condition.element("exportdataexpr", "''");
			}else if(!exp.equals("''")){
				condition.element("exportdataexpr", ConditionUtils.spilt(exp.substring(1, exp.length()-1)));
			}
		}
 		String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenantId, condition,EngineConstantArea.ENGINE_RESTORE_BILLING_DETAILS_QUERY);
 		Csql = reportSql.substring(0,reportSql.lastIndexOf("limit"));
		if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){ 
			list=this.dao.query4Json(tenantId,Csql.toString());
			structure = conditionUtils.getSqlStructure(tenantId,Csql.toString());
		}else{
			total = this.dao.countSql(tenantId,Csql.toString());
			list = this.dao.query4Json(tenantId,reportSql.toString());
		}
		//footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.ENGINE_TOTAL_THIRD_PARTY_PAYMENT_RECONCILIATION_QUERY).toString());
		 
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}

	@Override
	public List<JSONObject> getRestoreBillingSummaryQuery(String tenantId,JSONObject condition) throws Exception {
		return dao.query4Json(tenantId, parameterUtils.parameterAutomaticCompletionUpgrade(tenantId, condition,EngineConstantArea.ENGINE_RESTORE_BILLING_SUMMARY_QUERY).toString());
	}
	
	
	@Override
	public List<JSONObject> getResumeBillsDishesDetailsInquiriesQuery(String tenantId, JSONObject condition) throws Exception {
		return dao.query4Json(tenantId, parameterUtils.parameterAutomaticCompletionUpgrade(tenantId, condition,EngineConstantArea.ENGINERESUME_BILLS_DISHES_DETAILS_INQUIRIES_QUERY).toString());
	}

	@Override
	public List<JSONObject> getResumeBillingDishesInquiriesQuery(String tenantId, JSONObject condition) throws Exception {
		return dao.query4Json(tenantId, parameterUtils.parameterAutomaticCompletionUpgrade(tenantId, condition,EngineConstantArea.ENGINE_RESUME_BILLING_DISHES_INQUIRIES_QUERY).toString());
 
	}

	@Override
	public List<JSONObject> getResumeBillingDetailInquiriesQuery(String tenantId, JSONObject condition) throws Exception {
		return dao.query4Json(tenantId, parameterUtils.parameterAutomaticCompletionUpgrade(tenantId, condition,EngineConstantArea.ENGINE_RESUME_BILLING_DETAIL_INQUIRIES_QUERY).toString());
	}
}
