package com.tzx.cc.baidu.util;

import java.util.Map;
import java.util.TreeMap;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.Constant;

/**
 * <AUTHOR> 处理参数的工具类
 */
public class SuweiParamsUtil {

	private static final Logger logger = Logger.getLogger(SuweiParamsUtil.class);

	/**
	 * 处理参数的工具类
	 * 
	 * @param param
	 * @param columns
	 * @return
	 */
	public static Map<String, String> pakMap(JSONObject param,
			String... columns) {
		Map<String, String> map = new TreeMap<String, String>();
		for (String column : columns) {
			if (param.containsKey(column)) {
				map.put(column, param.getString(column));
			}
		}
		return map;
	}

	/**
	 * 验证传进的参数格式是否正确
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public static boolean validate(JSONObject param, Data result,
			String... columns) {
		for (String column : columns) {
			if (!param.containsKey(column)
					|| StringUtils.isBlank(param.getString(column))) {
				result.setMsg("参数：" + column + "不允许为空");
				result.setCode(Constant.CODE_PARAM_FAILURE);
				logger.info("参数：" + column + "不允许为空");
				return false;
			}
		}
		return true;
	}
}
