package com.tzx.report.service.rest.boh;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.bo.boh.SpecialAccountStatementService;
import com.tzx.report.common.util.ConditionUtils;

/**
 * 
 * 特殊账务收银员交班报表
 *
 */

@Controller("SpecialAccountStatementRest")
@RequestMapping("/report/SpecialAccountStatementRest")
public class SpecialAccountStatementRest
{
	@Resource(name = SpecialAccountStatementService.NAME)
	private SpecialAccountStatementService specialAccountStatementService;
	
	@Resource
	private CommonMethodAreaService commonMethodAreaService;
	@Resource
	ConditionUtils conditionUtils;
	
	@RequestMapping(value = "/getCashierShift")
	@ApiOperation(value = "特殊账务收银员交班",consumes= "multipart/form-data" ,httpMethod="POST",notes = "特殊账务收银员交班报表")
	@ApiImplicitParams({
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "p_report_date_begin",value = "开始日期",required = true,defaultValue="'2017-01-01'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "p_report_date_end",value = "结束日期",required = true,defaultValue="'2017-12-31'"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "p_store_id",value = "交易门店",required = true,defaultValue="0,1,9,11"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "selectType",value = "按日期1/收银员2",required = true,defaultValue="1"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "p_shift_id",value = "营业班次",defaultValue="2,3,4"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "p_cashier_num",value = "营业桌位",defaultValue="99999999"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "hierarchytype",value = "查询层级",required = true,defaultValue="1"),
		@ApiImplicitParam(dataType = "String",paramType = "form",name = "exportdataexpr",value = "导出",required = true,defaultValue="''"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "page",value = "页码",required = true,defaultValue="1"),
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "rows",value = "每页行数",required = true,defaultValue="10")
	})
	public void getCashierShift(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
				if(p.optString("p_store_id").length()==0){
					p.element("p_store_id", session.getAttribute("user_organ_codes_group"));
				}
			}else{
				if(p.optString("p_store_id").length()==0){
					p.element("p_store_id", session.getAttribute("user_organ"));
				}
			}
			
			result = specialAccountStatementService.getCashierShift((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	
	/**
	 * 班次
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getloadDutyOrderNew")
	public void getloadDutyOrderNew(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();
			
			for (String key : map.keySet())
			{
				if(!"".equals(map.get(key)[0]) && map.get(key)[0]!=null)
				{
					obj.put(key, map.get(key)[0]);
				}
			}
			obj.put("store_id",session.getAttribute("store_id"));
			result = conditionUtils.loadDutyOrderNew((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\":false,\"msg\":\"" + e.getMessage() + "\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}
			
			try
			{
				out = response.getWriter();
				out.print(result.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

}
