
log4j.rootLogger=INFO,D,A1

# A1 is set to be a ConsoleAppender which outputs to System.out.
log4j.appender.A1=org.apache.log4j.ConsoleAppender
log4j.appender.A1.layout=org.apache.log4j.PatternLayout
log4j.appender.A1.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} [%p] [ %l ] %m %n

# sass to the file appender
log4j.appender.D=org.apache.log4j.DailyRollingFileAppender
log4j.appender.D.layout=org.apache.log4j.PatternLayout
log4j.appender.D.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%t] - [%p] %37c(:%L) %3x %m%n
log4j.appender.D.File=${catalina.base}/logs/sass
log4j.appender.D.DatePattern='_'yyyy-MM-dd'.log'

log4j.logger.org.springframework = WARN,D 

# print to the file appender
log4j.logger.com.tzx.framework.common.print=INFO,print
log4j.appender.print=org.apache.log4j.DailyRollingFileAppender
log4j.appender.print.File=${catalina.base}/logs/print
log4j.appender.print.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.print.layout=org.apache.log4j.PatternLayout
log4j.appender.print.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%t] - [%p] %37c(:%L) %3x %m%n

# pos to the file appender
log4j.logger.com.tzx.pos=INFO,pos
log4j.appender.pos=org.apache.log4j.DailyRollingFileAppender
log4j.appender.pos.File=${catalina.base}/logs/pos
log4j.appender.pos.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.pos.layout=org.apache.log4j.PatternLayout
log4j.appender.pos.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%t] - [%p] %37c(:%L) %3x %m%n

# ordersmanager to the file appender
log4j.logger.com.tzx.orders=INFO,pos

# tip log for Console.jar
log4j.logger.tip=INFO,tip
log4j.appender.tip=org.apache.log4j.RollingFileAppender
log4j.appender.tip.File=${catalina.base}/logs/tip.log
log4j.appender.tip.MaxFileSize=512KB
log4j.appender.tip.MaxBackupIndex=2
log4j.appender.tip.layout=org.apache.log4j.PatternLayout
log4j.appender.tip.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%t] - [%p] %37c(:%L) %3x %m%n

log4j.logger.pos_to_crm=INFO,pos_to_crm
log4j.appender.pos_to_crm=org.apache.log4j.DailyRollingFileAppender
log4j.appender.pos_to_crm.File=${catalina.base}/logs/pos_to_crm
log4j.appender.pos_to_crm.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.pos_to_crm.layout=org.apache.log4j.PatternLayout
log4j.appender.pos_to_crm.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%t] - [%p] %37c(:%L) %3x %m%n

log4j.logger.pos_third_payment=INFO,pos_third_payment
log4j.appender.pos_third_payment=org.apache.log4j.DailyRollingFileAppender
log4j.appender.pos_third_payment.File=${catalina.base}/logs/pos_third_payment
log4j.appender.pos_third_payment.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.pos_third_payment.layout=org.apache.log4j.PatternLayout
log4j.appender.pos_third_payment.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%t] - [%p] %37c(:%L) %3x %m%n

# payment to the file appender
log4j.logger.com.tzx.payment=INFO,alipay
log4j.appender.alipay=org.apache.log4j.RollingFileAppender
log4j.appender.alipay.MaxFileSize=20480KB
log4j.appender.alipay.MaxBackupIndex=5
log4j.appender.alipay.layout=org.apache.log4j.PatternLayout
log4j.appender.alipay.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%c]-[%p] %m%n
log4j.appender.alipay.File=${catalina.base}/logs/payment.log

# wechat to the file appender
log4j.logger.com.tzx.weixin=INFO,wechat
log4j.appender.wechat=org.apache.log4j.RollingFileAppender
log4j.appender.wechat.Threshold=INFO
log4j.appender.wechat.Append=true
log4j.appender.wechat.ImmediateFlush=true
log4j.appender.wechat.layout=org.apache.log4j.PatternLayout
log4j.appender.wechat.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%c]-[%p] %m%n
log4j.appender.wechat.File=${catalina.base}/logs/wechat.log

# \u8BA2\u5355\u4E2D\u5FC3\u65E5\u5FD7
log4j.logger.com.tzx.cc=INFO,ccorder
log4j.appender.ccorder=org.apache.log4j.RollingFileAppender
log4j.appender.ccorder.Threshold=INFO
log4j.appender.ccorder.Append=true
log4j.appender.ccorder.ImmediateFlush=true
log4j.appender.ccorder.layout=org.apache.log4j.PatternLayout
log4j.appender.ccorder.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%c (:%L)]-[%p] %m%n
log4j.appender.ccorder.File=${catalina.base}/logs/ccorder.log

# scm\u65E5\u5FD7
log4j.logger.com.tzx.scm=INFO,scmlog
log4j.appender.scmlog=org.apache.log4j.RollingFileAppender
log4j.appender.scmlog.Threshold=INFO
log4j.appender.scmlog.ImmediateFlush=true
log4j.appender.scmlog.Append=true
log4j.appender.scmlog.File=${catalina.base}/logs/scm.log
log4j.appender.scmlog.MaxFileSize=20MB
log4j.appender.scmlog.MaxBackupIndex=10
log4j.appender.scmlog.layout=org.apache.log4j.PatternLayout
log4j.appender.scmlog.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss.SSS} [%p] - [%t] --> %l %n ===== %n %m %n ===== %n %x %n
