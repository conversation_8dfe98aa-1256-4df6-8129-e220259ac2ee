package com.tzx.report.service.rest.payment;

import com.tzx.report.bo.payment.service.MeidaPaymentOrderQueryService;
import com.tzx.report.common.util.ReportExportUtils;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller("XmdQueryListContraller")
@RequestMapping("/report/XmdQueryListContraller")
public class XmdQueryListContraller
{
	@Resource(name = MeidaPaymentOrderQueryService.NAME)
	public MeidaPaymentOrderQueryService meidaPaymentOrderQueryService;

	@RequestMapping("queryOrganTree")
	@ResponseBody
	public List<JSONObject> queryOrganTree(HttpServletRequest request){
		HttpSession session = request.getSession();
		String tenancyId = (String) session.getAttribute("tenentid");
		String storeIds = session.getAttribute("user_organ_codes_group_invalid")+"";
		return meidaPaymentOrderQueryService.queryOrganTrees(tenancyId,storeIds);
	}


	@RequestMapping(value = "/load")
	@ResponseBody
	public com.alibaba.fastjson.JSONObject load(HttpServletRequest request, HttpServletResponse response)
	{
			response.setContentType("text/html; charset=UTF-8");
			response.setContentType("text/html");
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = null;
			InputStream in = null;
			HttpSession session = request.getSession();
			com.alibaba.fastjson.JSONObject result = new com.alibaba.fastjson.JSONObject();
			try
			{
				com.alibaba.fastjson.JSONObject obj = new  com.alibaba.fastjson.JSONObject();

				Map<String, String[]> map = request.getParameterMap();

				for (String key : map.keySet())
				{
					obj.put(key, map.get(key)[0]);
				}

				String tenancyId = (String) session.getAttribute("tenentid");
				result = meidaPaymentOrderQueryService.queryOrderLists(tenancyId,obj);

			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		return result;
	}


	@RequestMapping("export")
	public void export(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{
			workBook = new HSSFWorkbook();
			String tenancyId = (String) session.getAttribute("tenentid");

			com.alibaba.fastjson.JSONObject obj = new  com.alibaba.fastjson.JSONObject();
			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			workBook = meidaPaymentOrderQueryService.export(tenancyId, obj ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			ReportExportUtils.download(workBook,response,"美大实时订单");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}


}
