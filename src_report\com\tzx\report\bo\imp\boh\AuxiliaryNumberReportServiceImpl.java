package com.tzx.report.bo.imp.boh;

import com.tzx.framework.common.util.SpringConext;
import com.tzx.report.bo.boh.AuxiliaryNumberReportService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ExportUtils;
import com.tzx.report.common.util.ReportExportUtils;
import com.tzx.report.po.boh.dao.AuxiliaryNumberReportDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;


@Service(AuxiliaryNumberReportService.NAME)
public class AuxiliaryNumberReportServiceImpl implements AuxiliaryNumberReportService{

	private final Logger logger = LoggerFactory.getLogger(getClass());

	@Resource(name = AuxiliaryNumberReportDao.NAME)
	private AuxiliaryNumberReportDao auxiliaryNumberReportDao;

	@Override
	public JSONObject getAuxiliaryNumberReport(String tenancyId, JSONObject json) throws Exception{
		return auxiliaryNumberReportDao.getAuxiliaryNumberReport(tenancyId,json);
	}

	/**
	 * 导出全部的
	 * @param tenancyId
	 * @param p
	 * @param workBook
	 * @return
	 * @throws Exception
	 */
	@Override
	public HSSFWorkbook exportData(String tenancyId, JSONObject p, HSSFWorkbook workBook) throws Exception {
		Integer rowNum=1;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		JSONObject findResult= getAuxiliaryNumberReport(tenancyId, p);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		JSONObject out1Result =null;
		//创建sheet 表格   同时还可以设置名字!
		HSSFSheet sheet1=workBook.createSheet("菜品辅助数量销售报表");

		String [] listTitleName = {"门店","菜品编号","菜品名称","规格","单价","辅助数量","实收金额"};
		String [] dataName ={"org_full_name","item_code","item_name","item_unit_name","item_price","item_assist_num","real_amount"};
		String [] dataType ={"String","String","String","String","0.00","0.00","0.00"};

		if(list1.size()>0){
			for(JSONObject json1 : list1) {
				// 调用到处方法；
				out1Result = ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
				paramData.put("rowNum", out1Result.opt("rowNum"));
				paramData.put("jin", out1Result.optInt("jin"));
			}
		}
/*				sheet1.groupRow(1,out1Result.optInt("rowNum"));
				sheet1.setRowSumsBelow(false);
				sheet1.setRowSumsRight(false);
*/				return workBook;
	}

	/**
	 * 导出选中的
	 * @param tenancyID
	 * @param json
	 * @param workBook
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("null")
	public HSSFWorkbook exportDataNew(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception {
		Integer rowNum=1;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		List<JSONObject> list1 =(List<JSONObject>) json.opt("fes");
		JSONObject out1Result =null;
		//创建sheet 表格   同时还可以设置名字!
		HSSFSheet sheet1=workBook.createSheet("菜品辅助数量销售报表");

		String [] listTitleName = {"门店","菜品编号","菜品名称","规格","单价","辅助数量","实收金额"};
		String [] dataName ={"org_full_name","item_code","item_name","item_unit_name","item_price","item_assist_num","real_amount"};
		String [] dataType ={"String","String","String","String","0.00","0.00","0.00"};

		if(list1.size()>0){
			for(JSONObject json1 : list1) {
				// 调用到处方法；
				out1Result = ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
				paramData.put("rowNum", out1Result.opt("rowNum"));
				paramData.put("jin", out1Result.optInt("jin"));
			}
		}
/*				sheet1.groupRow(1,out1Result.optInt("rowNum"));
				sheet1.setRowSumsBelow(false);
				sheet1.setRowSumsRight(false);
*/				return workBook;
	}

}
