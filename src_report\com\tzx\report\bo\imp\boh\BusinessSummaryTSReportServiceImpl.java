package com.tzx.report.bo.imp.boh;

import com.tzx.report.bo.boh.BusinessSummaryTSReportService;
import com.tzx.report.po.boh.dao.BusinessSummaryTSReportDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by zds on 2018-12-03.
 */
@Service(BusinessSummaryTSReportService.NAME)
public class BusinessSummaryTSReportServiceImpl implements BusinessSummaryTSReportService {
    @Resource(name = BusinessSummaryTSReportDao.NAME)
    private BusinessSummaryTSReportDao businessSummaryTSReportDao;

    @Override
    public JSONObject getBusinessSummaryTS(String tenancyID, JSONObject condition) throws Exception {
        return businessSummaryTSReportDao.getBusinessSummaryTS(tenancyID, condition);
    }
}
