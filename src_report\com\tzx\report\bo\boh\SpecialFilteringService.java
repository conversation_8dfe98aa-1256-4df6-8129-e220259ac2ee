package com.tzx.report.bo.boh;

import java.util.List;

import com.tzx.ehk.helper.Trace;
import net.sf.json.JSONObject;

public interface SpecialFilteringService
{
   String NAME = "com.tzx.report.bo.imp.boh.SpecialFilteringServiceImpl";
	
	public JSONObject loadOperationInformation(String tenancyID,JSONObject condition) throws Exception;

	public String getCheakTree(String tenentid, JSONObject p)throws Exception;

	void delete(String string, String tableName, JSONObject obj)
			throws Exception;

	public Object updateSaveRun(String attribute, String string, JSONObject obj)throws Exception;

	public JSONObject selectRun(String attribute, JSONObject obj)throws Exception;

	/**
	 * 保存特殊账务配置信息
	 * @param tenancyID
	 * @param param
	 * @param list
	 * @param result
	 * @throws Exception
	 */
	void saveOperationInformation(String tenancyID, JSONObject param, List<JSONObject> list, JSONObject result) throws Exception;

	/**
	 * 查询有关账单的信息
	 * @param tenentid
	 * @param obj
	 * @return
	 * @throws Exception
	 */
    public JSONObject  getBillInfo(String tenentid, JSONObject obj) throws Exception;

	/**
	 * 获取机构树，将已生成规则的门店去掉
	 * @param tenentid
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public Object getOrgantree(String tenentid, JSONObject obj) throws Exception;

	/**
	 * 保存B帐规则
	 * @param tenentid
	 * @param obj
	 * @return
	 */
	public JSONObject saveBaccountRule(String tenentid, JSONObject obj) throws  Exception;

	/**
	 * 查询B帐规则
	 * @param tenentid
	 * @param obj
	 * @return
	 */
	public Object findBaccountRule(String tenentid, JSONObject obj) throws  Exception;

	/**
	 * 生成账务金额
	 * @param tenentid
	 * @param obj
	 * @return
	 */
	public JSONObject  getAccountingAmount(String tenentid, JSONObject obj) throws  Exception;

	/**
	 * 查询规则明细
	 * @param tenentid
	 * @param obj
	 * @return
	 */
    public JSONObject findBaccountRuleDetails(String tenentid, JSONObject obj) throws  Exception;

	/**
	 * 查询系统参数
	 * @param tenentid
	 * @param obj
	 * @return
	 */
    public JSONObject getSysParam(String tenentid, JSONObject obj) throws  Exception;

	/**
	 * 删除规则
	 * @param tenentid
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public JSONObject deleteSpecialFilter(String tenentid, JSONObject obj) throws  Exception;
}
