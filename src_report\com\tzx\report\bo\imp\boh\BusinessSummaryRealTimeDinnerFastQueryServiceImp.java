package com.tzx.report.bo.imp.boh;


import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.report.bo.boh.AbnormityOrderForTakeoutQueryService;
import com.tzx.report.bo.boh.BusinessSummaryRealTimeDinnerFastQueryService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.po.boh.dao.BusinessSummaryRealTimeDinnerFastQueryDao;
import com.tzx.report.po.boh.dao.BusinessSummaryStatisticsFastfoodDao;

@Service(BusinessSummaryRealTimeDinnerFastQueryService.NAME)
public class BusinessSummaryRealTimeDinnerFastQueryServiceImp implements BusinessSummaryRealTimeDinnerFastQueryService
{
	
	@Resource(name = BusinessSummaryRealTimeDinnerFastQueryDao.NAME)
	private BusinessSummaryRealTimeDinnerFastQueryDao businessSummaryRealTimeDinnerFastQueryDao;
	
	
	@Resource(name = BusinessSummaryStatisticsFastfoodDao.NAME)
	private BusinessSummaryStatisticsFastfoodDao businessSummaryStatisticsFastfoodDao;
	@Resource(name=AbnormityOrderForTakeoutQueryService.NAME)
	private AbnormityOrderForTakeoutQueryService abnormityOrderForTakeoutQueryService;
	
	@Resource
	ConditionUtils conditionUtils;

	@Override
	public JSONObject getBusinessSummaryRealTimeDinnerFastQuery(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = JSONObject.fromObject("{}");
		result.element("rows", businessSummaryRealTimeDinnerFastQueryDao.getBusinessSummaryRealTimeDinnerFastQuery(tenancyID, condition));
		result.element("success", true);
		
		// 增加外卖付款方式at 20180920 zhangy
		condition.put("begin_date", condition.optString("p_report_date"));
		condition.put("end_date", condition.optString("p_report_date"));
		
		condition.put("store_id", condition.optString("p_store_id"));
		result.element("takeOutaymentDetail", businessSummaryStatisticsFastfoodDao.getBusinessSummaryTakeOutaymentDetail(tenancyID, condition));
		//增加外卖异常订单查询at 20180920 zhangy
		condition.put("store_ids", condition.optString("p_store_id"));
		result.element("takeOutwayExceptionDetail", abnormityOrderForTakeoutQueryService.getAbnormityBill(tenancyID, condition).optString("rows"));

		
		return result;
	}
}
