package com.tzx.cc.common.redis.service;

import java.util.List;
import java.util.Map;

/**
 * redis 操作类
 * 
 * <AUTHOR>
 * 
 */
public interface CcRedisService {
	/**
	 * key-value 存储字符串类型的数据到redis
	 * 
	 * @param key
	 *            键
	 * @param value
	 *            值
	 * @param expires
	 *            有效期，如果是0 表示无限（单位：秒）
	 * @throws Exception
	 */
	public void saveBykv(String key, String value, int expires)
			throws Exception;

	/**
	 * 根据键值获取存储的内容
	 * 
	 * @param key
	 *            键
	 * @return String
	 * @throws Exception
	 */
	public String getByKey(String key) throws Exception;
	/**
	 * 模糊查询key列表
	 * @param patternKey 表达式
	 * @return
	 * @throws Exception
	 */
	public List<String> getByPatternKey(String patternKey)throws Exception;
	/**
	 * 根据键值从redis中删除
	 * @param key
	 * @throws Exception
	 */
	public void del(String key) throws Exception;
	
	public void saveByMap(String entryKey,Map<String,String> map)throws Exception;
	public String getByMapField(String entryKey,String Field)throws Exception;
	public void delByMapField(String entryKey,String... fields)throws Exception;
	
	/**
	 * 把数据存储到list的右侧
	 * @param key
	 * @param value
	 * @throws Exception
	 */
	public void rightPush(final String key, final String value)throws Exception;
	/**
	 * 把数据从数组的左侧开始读取
	 * @param key
	 * @throws Exception
	 */
	public String leftPop(final String key)throws Exception;
	/**
	 * 把数据从数组的右侧开始存储
	 * @param key
	 * @throws Exception
	 */
	public void leftPush(final String key,final String value)throws Exception;
	/**
	 * 把数据从数组的右侧开始读取
	 * @param key
	 * @throws Exception
	 */
	public String rightPop(final String key)throws Exception;
	/**
	 * 获取List的大小
	 * @param key
	 * @return int
	 * @throws Exception
	 */
	public long getListSize(final String key)throws Exception;

	/**
	 * 获取Set列表
	 * @param key
	 * @param start
	 * @param end
	 * @return
	 */
	public List<String> range(String key, long start, long end);
	
	/**
	 * 添加zset信息
	 * @param key
	 * @param value
	 */
	public void zsetSave(String key,long score,String value);
	/**
	 * 根据key获取分数从小到大的排序数据zset
	 * @param key
	 * @param startIndex 0
	 * @param endIndex -1
	 * @return
	 */
	public List<String> zrange(final String key,final long startIndex,final long endIndex);
	
	/**
	 * 获取zset中分值区间的值列表
	 * @param key
	 * @param startScore
	 * @param endScore
	 * @return
	 */
	public List<String> zrangebyscore(final String key,final long startScore,final long endScore);
	
	/**
	 * 删除key下的某个值
	 * @param key
	 * @param value
	 */
	public void removeZsetByValue(final String key,final String value);
}
