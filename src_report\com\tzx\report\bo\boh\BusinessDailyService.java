package com.tzx.report.bo.boh;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */
public interface BusinessDailyService
{

    String NAME = "com.tzx.report.bo.imp.boh.BusinessDailyServiceImp";

    JSONObject find(String tenancyID, JSONObject condition) throws Exception;

    List<JSONObject> getPayTypeItems(String tenancyID, JSONObject condition) throws Exception;
}
