package com.tzx.cc.base;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import net.sf.json.JSONObject;
import com.tzx.framework.common.exception.CcErrorCode;
import com.tzx.framework.common.exception.CustomErrorCode;
import com.tzx.framework.common.exception.SystemCustomException;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;

public class CcExceptionUtil
{
	
	public static SimpleDateFormat	format	= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	public static SimpleDateFormat	format1	= new SimpleDateFormat("yyyy-MM-dd");

	public static void checkCardState(String trueCardState, String paramState) throws SystemException
	{
		if (!trueCardState.equals(paramState))
		{}
	}
	public static void checkEndDate(String enddate, CcErrorCode ecode) throws SystemException
	{
		if(null!= enddate && !"".equals(enddate))
		{
			try
			{
				Date now  = new Date();
				Date endData = format1.parse(enddate);
				if(now.after(endData))
				{
					throw new SystemException(ecode);
				}
			}
			catch (ParseException e)
			{
				// TODO Auto-generated catch block
				//e.printStackTrace();
			}
			
			
		}
		
		
	}

	public static void validContains(JSONObject params, String key, CcErrorCode ecode) throws SystemException
	{
		if ("".equals(params.optString(key)))
		{
			throw new SystemException(ecode);
		}
	}
	
	public static void validContainsNum(JSONObject params, String key, CcErrorCode ecode) throws SystemException
	{
		if (params.optInt(key)==0)
		{
			throw new SystemException(ecode);
		}
	}
	
	public static void validContainsDouble(JSONObject params, String key, CcErrorCode ecode) throws SystemException
	{
		if (params.optDouble(key,0.0)==0)
		{
			throw new SystemException(ecode);
		}
	}


	public static void checkListSizeZero(List<JSONObject> list, CcErrorCode ecode) throws SystemException
	{
		if (list == null || list.size() == 0)
		{
			throw new SystemException(ecode);
		}
	}
	
	public static void checkListSizeZeroC(List<JSONObject> list, CustomErrorCode ecode) throws SystemException
	{
		if (list == null || list.size() == 0)
		{
			throw new SystemCustomException(ecode);
		}
	}
	
	public static void checkListSizeNotZeroC(List<JSONObject> list, CustomErrorCode ecode) throws SystemException
	{
		if (list != null && list.size() > 0)
		{
			throw new SystemCustomException(ecode);
		}
	}
	
	public static void checkListMapSizeZero(List<Map<String,Object>> list, CcErrorCode ecode) throws SystemException
	{
		if (list == null || list.size() == 0)
		{
			throw new SystemException(ecode);
		}
	}

	public static void checkListSizeNotZero(List<JSONObject> list, CcErrorCode ecode) throws SystemException
	{
		if (list != null && list.size() > 0)
		{
			throw new SystemException(ecode);
		}
	}

	public static void validContainsMap(Map<String, Object> params, Map<String, CcErrorCode> map) throws SystemException
	{
		Set<String> keyset = map.keySet();

		for (String key : keyset)
		{
			if (!params.containsKey(key) || params.get(key).equals(""))
			{
				throw new SystemException(map.get(key));
			}
		}
	}
	
	public static boolean isValidDate(String str) throws SystemException
	{
		boolean convertSuccess = true;

		try
		{
			// 设置lenient为false.
			// 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
			format.setLenient(false);
			format.parse(str);
		}
		catch (ParseException e)
		{
			// 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
			convertSuccess = false;
			throw new SystemException(CcErrorCode.DATE_FORMAT_ERROR);
		}
		return convertSuccess;
	}

	public static boolean isValidDate1(String str) throws SystemException
	{
		boolean convertSuccess = true;

		try
		{
			// 设置lenient为false.
			// 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
			format1.setLenient(false);
			format1.parse(str);
		}
		catch (ParseException e)
		{
			// 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
			convertSuccess = false;
			throw new SystemException(CcErrorCode.DATE_FORMAT_ERROR);
		}
		return convertSuccess;
	}
	
	public static boolean isCouldUsedIn(String usecycle,String begin,String end) throws Exception
	{
		Date now1 = DateUtil.getNowHHMMSSDate();
		try
		{
			if(usecycle!=null && !"".equals(usecycle) && !"null".equals(usecycle))
			{
				String nw = Calendar.getInstance().get(Calendar.DAY_OF_WEEK)+"";
				if(usecycle.indexOf(nw)==-1)
				{
					return false;
				}
			}
			if(begin!=null && !"".equals(begin) && !"null".equals(usecycle))
			{
				Date b = DateUtil.parseDate(begin);
				if(b.after(now1))
				{
					return false;
				}
			}
			if(end!=null && !"".equals(end)&& !"null".equals(usecycle))
			{
				Date ee = DateUtil.parseDate(end);
				if(ee.before(now1))
				{
					return false;
				}
			}
		}
		catch (Exception e)
		{
			
		}
		return true;
	}

}
