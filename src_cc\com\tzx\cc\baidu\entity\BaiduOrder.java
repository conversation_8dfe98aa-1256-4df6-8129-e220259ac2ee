package com.tzx.cc.baidu.entity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class BaiduOrder {
	
	private Map<String,Map<String,String>> shop;
	
	private Order order;
	
	private User user;
	
	private List<Product> products;
	
	private List<Discount> discounts;

	public Map<String, Map<String, String>> getShop() {
		return shop;
	}

	public void setShop(Map<String, Map<String, String>> shop) {
		this.shop = shop;
	}

	public Order getOrder() {
		return order;
	}

	public void setOrder(Order order) {
		this.order = order;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public List<Product> getProducts() {
		return products;
	}

	public void setProducts(List<Product> products) {
		this.products = products;
	}

	public List<Discount> getDiscounts() {
		return discounts;
	}

	public void setDiscounts(List<Discount> discounts) {
		this.discounts = discounts;
	}

}
class Order{
	private String order_id;
	private int send_immediately;
	private String send_time;
	private int send_fee;
	private int package_fee;
	private int discount_fee;
    private int total_fee;
    private int shop_fee;
    private int user_fee;
    private int pay_type;
    private int pay_status;
    private int need_invoice;
    private String invoice_title;
    private String remark;
    private int delivery_party;
    private String create_time;
	public String getOrder_id() {
		return order_id;
	}
	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}
	public int getSend_immediately() {
		return send_immediately;
	}
	public void setSend_immediately(int send_immediately) {
		this.send_immediately = send_immediately;
	}
	public String getSend_time() {
		return send_time;
	}
	public void setSend_time(String send_time) {
		this.send_time = send_time;
	}
	public int getSend_fee() {
		return send_fee;
	}
	public void setSend_fee(int send_fee) {
		this.send_fee = send_fee;
	}
	public int getPackage_fee() {
		return package_fee;
	}
	public void setPackage_fee(int package_fee) {
		this.package_fee = package_fee;
	}
	public int getDiscount_fee() {
		return discount_fee;
	}
	public void setDiscount_fee(int discount_fee) {
		this.discount_fee = discount_fee;
	}
	public int getTotal_fee() {
		return total_fee;
	}
	public void setTotal_fee(int total_fee) {
		this.total_fee = total_fee;
	}
	public int getShop_fee() {
		return shop_fee;
	}
	public void setShop_fee(int shop_fee) {
		this.shop_fee = shop_fee;
	}
	public int getUser_fee() {
		return user_fee;
	}
	public void setUser_fee(int user_fee) {
		this.user_fee = user_fee;
	}
	public int getPay_type() {
		return pay_type;
	}
	public void setPay_type(int pay_type) {
		this.pay_type = pay_type;
	}
	public int getPay_status() {
		return pay_status;
	}
	public void setPay_status(int pay_status) {
		this.pay_status = pay_status;
	}
	public int getNeed_invoice() {
		return need_invoice;
	}
	public void setNeed_invoice(int need_invoice) {
		this.need_invoice = need_invoice;
	}
	public String getInvoice_title() {
		return invoice_title;
	}
	public void setInvoice_title(String invoice_title) {
		this.invoice_title = invoice_title;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public int getDelivery_party() {
		return delivery_party;
	}
	public void setDelivery_party(int delivery_party) {
		this.delivery_party = delivery_party;
	}
	public String getCreate_time() {
		return create_time;
	}
	public void setCreate_time(String create_time) {
		this.create_time = create_time;
	}
	
}

class User{
	private String name;
    private String phone;
    private int gender;
    private String address;
    private Map<String,Map<String,String>> coord;
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public int getGender() {
		return gender;
	}
	public void setGender(int gender) {
		this.gender = gender;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public Map<String, Map<String, String>> getCoord() {
		return coord;
	}
	public void setCoord(Map<String, Map<String, String>> coord) {
		this.coord = coord;
	}
}

class Product{
	  private String product_id;
      private String product_name;
      private int product_price;
      private int product_amount;
      private int product_fee;
      private int package_price;
      private int package_amount;
      private int package_fee;
      private int total_fee;
      private String upc;
	public String getProduct_id() {
		return product_id;
	}
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	public String getProduct_name() {
		return product_name;
	}
	public void setProduct_name(String product_name) {
		this.product_name = product_name;
	}
	public int getProduct_price() {
		return product_price;
	}
	public void setProduct_price(int product_price) {
		this.product_price = product_price;
	}
	public int getProduct_amount() {
		return product_amount;
	}
	public void setProduct_amount(int product_amount) {
		this.product_amount = product_amount;
	}
	public int getProduct_fee() {
		return product_fee;
	}
	public void setProduct_fee(int product_fee) {
		this.product_fee = product_fee;
	}
	public int getPackage_price() {
		return package_price;
	}
	public void setPackage_price(int package_price) {
		this.package_price = package_price;
	}
	public int getPackage_amount() {
		return package_amount;
	}
	public void setPackage_amount(int package_amount) {
		this.package_amount = package_amount;
	}
	public int getPackage_fee() {
		return package_fee;
	}
	public void setPackage_fee(int package_fee) {
		this.package_fee = package_fee;
	}
	public int getTotal_fee() {
		return total_fee;
	}
	public void setTotal_fee(int total_fee) {
		this.total_fee = total_fee;
	}
	public String getUpc() {
		return upc;
	}
	public void setUpc(String upc) {
		this.upc = upc;
	}
}

class Discount{
	private String type;//discount.{i}.type	string	是	优惠类型，枚举值参见附录
	private int fee;//discount.{i}.fee	int	是	优惠金额，单位：分
	private String activity_id;//discount.{i}.activity_id	string	是	活动 ID
	private String rate;//discount.{i}.baidu_rate	string	是	百度承担金额
	private String shop_rate;//discount.{i}.shop_rate	string	是	商户承担金额
	private String agent_rate;//discount.{i}.agent_rate	string	是	代理商承担金额
	private String logistics_rate;//discount.{i}.logistics_rate	string	是	物流承担金额
	private String desc;//discount.{i}.desc	
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public int getFee() {
		return fee;
	}
	public void setFee(int fee) {
		this.fee = fee;
	}
	public String getActivity_id() {
		return activity_id;
	}
	public void setActivity_id(String activity_id) {
		this.activity_id = activity_id;
	}
	public String getRate() {
		return rate;
	}
	public void setRate(String rate) {
		this.rate = rate;
	}
	public String getShop_rate() {
		return shop_rate;
	}
	public void setShop_rate(String shop_rate) {
		this.shop_rate = shop_rate;
	}
	public String getAgent_rate() {
		return agent_rate;
	}
	public void setAgent_rate(String agent_rate) {
		this.agent_rate = agent_rate;
	}
	public String getLogistics_rate() {
		return logistics_rate;
	}
	public void setLogistics_rate(String logistics_rate) {
		this.logistics_rate = logistics_rate;
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
}