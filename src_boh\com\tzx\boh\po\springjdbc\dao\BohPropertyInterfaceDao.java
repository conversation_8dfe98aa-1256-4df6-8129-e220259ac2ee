package com.tzx.boh.po.springjdbc.dao;

import java.util.List;

import net.sf.json.JSONObject;

public interface BohPropertyInterfaceDao {
	  String Name = "com.tzx.boh.po.springjdbc.dao.impl.BohPropertyInterfaceDaoImpl";
	    
	    public JSONObject load(String tenancyID,JSONObject condition,String oids) throws Exception;
		   
	   	public JSONObject find(String tenancyID,JSONObject condition) throws Exception;
		   
	    public JSONObject save(String tenancyID,JSONObject condition) throws Exception;
		   
		public List<JSONObject>  getProperty(String tenancyID,JSONObject condition) throws Exception;
		   
		public JSONObject upload(String tenancyID,JSONObject condition) throws Exception;
		//上传V332m接口数据
		public JSONObject uploadV332m(String tenancyID, JSONObject condition) throws Exception;

		public JSONObject getUploadDetails(String tenancyID,JSONObject condition) throws Exception;
		
		public List<JSONObject>  getList(String tenancyID,JSONObject condition) throws Exception;
		
		public List<JSONObject>  getMonth(String tenancyID,JSONObject condition) throws Exception;
		
		public JSONObject  stop(String tenancyID,JSONObject condition) throws Exception;
		// 查询商户对应的接口类型
		public JSONObject findTypeId(String attribute, JSONObject p) throws Exception;

		public JSONObject findOrgan(String attribute, JSONObject p) throws Exception;

		public JSONObject getLoginSession(JSONObject params) throws Exception;

		public JSONObject sendGetMessage(String tenantId, JSONObject bill,JSONObject params,JSONObject obj) throws Exception;
}
