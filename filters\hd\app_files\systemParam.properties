#Tue May 04 19:58:01 CST 2014
# hq or boh
sotreorhq=hq
#ifstart=true
ifstart=false
Url=tcp://bj-qemu-saas-hdmq-node-1:6666
User=
Password=
qtoboh=qtoboh
qcometoboh=qtoboh_hdl_a3x3m0znn6eeqerjdud68xp6s7ojuiqz

#for tt
#ttsvr=tcp://*************:1884
tttopic=app_topic

#datasourcetype
datasourcetype=postgres
print.type=80
system_password=1db88c018878aef3187e7710aea95c40

#smstype\u662f\u7528\u4e8e\u914d\u7f6e\u4f7f\u7528\u5e73\u53f0\u8fd8\u662f\u77ed\u4fe1\u732b\uff0c\u7528\u732b\u503c\u4e3a\u77ed\u4fe1\u732b\u670d\u52a1\u5668IP \u5982\uff1a************,\u5982\u679c\u662f\u4f7f\u7528\u77ed\u4fe1\u5e73\u53f0\u76f4\u63a5\u51990\u4e3a\u4e0a\u6d77\u5e0c\u5965\u77ed\u4fe1\u5e73\u53f0\uff0c\u51991\u662f\u7528\u4ebf\u7f8e\u77ed\u4fe1\u5e73\u53f0,\u51992\u4e3a\u7ebf\u4e0a\u56e2\u961f\u7559\u4e0b\u7684\u65b0\u5e73\u53f0\uff08\u72d7\u5c4e\uff09
smstype=2
#fsdw=\u5929\u5b50\u661f

#\u4e0b\u9762\u662f\u4ebf\u7f8e\u5e73\u53f0\u76f8\u5173\u914d\u7f6e\u53c2\u6570
#\u4ebf\u7f8e\u8d2d\u4e70\u7684\u8f6f\u4ef6\u5e8f\u5217\u53f7
softwareSerialNo=3SDK-EMY-0130-MEWSR
#\u4ece\u4ebf\u7f8e\u8d2d\u4e70\u7684\u8f6f\u4ef6\u5e8f\u5217\u53f7\u6240\u5e26\u7684KEY \u503c
key=026404
#\u4ece\u4ebf\u7f8e\u8d2d\u4e70\u7684\u8f6f\u4ef6\u5e8f\u5217\u53f7\u6240\u5e26\u7684\u5bc6\u7801
password=055956

#\u4e0b\u9762\u7684\u53c2\u6570\u662f\u914d\u7f6e\u4e0a\u6d77\u5e0c\u5965\u77ed\u4fe1\u5e73\u53f0\u76f8\u5173\u53c2\u6570
#\u77ed\u4fe1\u5e73\u53f0\u5730\u5740
url=http://api.52ao.com
#\u7528\u6237\u540d
user=laitianhua06
#\u5bc6\u7801\u8981\u8fdb\u884cMD5\u52a0\u5bc6,32\u4f4d\uff0c\u5927\u5c0f\u5199\u90fd\u53ef\u4ee5\uff0c\u5de5\u5177\uff1a  http://tool.chinaz.com/Tools/MD5.aspx?q=123456&md5type=0
pass=E10ADC3949BA59ABBE56E057F20F883E

#\u4e0b\u9762\u662f20150327\u65b0\u77ed\u4fe1\u5e73\u53f0\u63a5\u53e3\u8d26\u53f7\u5bc6\u7801(\u7ebf\u4e0a\u5e73\u53f0)
newkey=SDK-BBX-010-20057
newpassword=tzx68698

#\u4e0b\u9762\u662f20150723pos\u8f6c\u67e5\u4f1a\u5458\u63a5\u53e3url
#crmUrl=http://**************:8080/tzxsaas/crmRest/post
crmUrl=http://hd.e7e6.net/crmRest/post

#Whether as a redis task service startup, 1 is yes
taskopen=1

#uploadImgIp
upload_img_ip=
upload_websiteimg_ip=

#
hqdata_minite_period=10

#
saas_url=http://hd.e7e6.net/
post_url=http://hd.e7e6.net/tzxsecondpay/
#
storestatus_minite_period=1

item_photo_path=itemImage
#pos alipay 
pos_payment_url=http://hd.e7e6.net/payment/aliPaymentRest/aliPay
#pos wechat
pos_payment_wechat_url=http://hd.e7e6.net/payment/wechat/post
#alipay notify url 
alipay_notify_url=http://hd.e7e6.net/payment/aliPaymentRest/notify
#wechat notify url 
wechat_notify_url=http://hd.e7e6.net/payment/wechat/notify
# 门店微信支付回调
wechat_notify_url_new=http://hd.e7e6.net/paymentCallBack/weixin/notify

#APP
app_name=app-release.apk
#
app_path=download/

#for wechat product
product_wechat_service_ip=http://hd.e7e6.net
product_wechat_scmip=http://hd.e7e6.net/crmRest/post

#wechat service mch message	start
wechat_service_mch_service_ip=http://www.e7e6.net
wechat_service_mch_appid=wxc2864bc7ba5baa5c
wechat_service_mch_secert=0998c4c22fcfd9bd75a94195605500ad
wechat_service_mch_mch_id=10010438
wechat_service_mch_api_secert=tzxsaasweixinpay2015101212150000
wechat_service_mch_cert=cert/apiclient_cert.p12
wechat_service_sub_mch_id = **********
#wechat service mch message end

#alipay service provider id start
alipay_service_provider_id=2088411391202430
#alipay service provider id end
#tenent_id==hdl
#store_id=48

#\u662f\u5426\u5916\u5356
is_delivery=true

# can be local IP
supplier_address=http://sp.e7e6.net/sup/supContraller/post 
saas_supplier=SAAS_SUPPLY_QUEUE

#wechat thrid
wechat_component_appid=wx54221b218387ae71
wechat_component_appsecret=0e92590caffe9c0c31d49231ac73aa6c
wechat_compoment_encodingaeskey=Dlf6NTLNpIZyA3MOrLRlIHV46QcWHcAi0G84aEL86p2
wechat_compoment_token=tzx
#wechat thrid
#wechat thrid redirect_uri
wechat_thrid_redirect_uri=http://hd.e7e6.net
#wechat thrid redirect_uri

# 新美大团购&闪惠接口
xmd_url=http://api.open.cater.meituan.com
xmd_developerid=100113
xmd_signkey=m1fwurrvo09o33c7


# 外卖平台api调用地址 {

#百度外卖api
baidu_api_urL=http://api.waimai.baidu.com

#美团外卖api
meituan_api_url=http://waimaiopen.meituan.com/api/v1/
#美团外卖测试api
#meituan_api_url=http://test.waimaiopen.meituan.com/api/v1/

#饿了么api
ele_api_url=http://v2.openapi.ele.me/

#大众点评
dp_api_url=https://e.51ping.com/mpi/

#yichi
#yichi_address=http://localhost:8081/yichi/yichi/yichiContraller/post
#yichi_address=http://*************/yichi/yichi/yichiContraller/post


rif_tzxApply = /tzxApply
rif_tzxApplyBom = /tzxapplyBOM
rif_finish = /tzxDistributionFinish
rif_return = /tzxReturn
rif_supplyin = /tzxSupplierIn
rif_supplyout = /tzxSupplierOut

# }

omUrl = http://hdom.e7e6.net
#maintaskopen = 1
#childtaskopen = 1

MQFASTDFSURL=http://www.e7e6.mobi/

#elm2.0 appid \u81EA\u52A8\u62C9\u5355\u63A5\u53E3\u65F6\u4F7F\u7528
ele_appid=15616757
#elm2.0 oauth callback used... note:must be https
ele_oauth_callbackUrl=https://hd.e7e6.net/thirdpartydown/orderdownstreamrest/elm/authCallBack 

#elm2.0 oauth true|false \u997F\u4E86\u4E48\u8BA4\u8BC1\u65B9\u5F0F:\u6D4B\u8BD5\u8BA4\u8BC1|\u751F\u4EA7\u8BA4\u8BC1
ele_oauth_istest=false

# SSO Redirect Url
server.homepage=http://hd.e7e6.net/pages/framework/layout/main.jsp
sso.homepage=http://sso.hd.e7e6.net

# Upload file configure
imgServerType=0
fileBaseUri=http://www.e7e6.mobi/


