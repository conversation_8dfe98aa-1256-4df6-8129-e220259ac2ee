package com.tzx.cc.invoice.electronic.dao.impl;

import java.math.BigDecimal;
import java.util.List;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao;
import com.tzx.weixin.waimai.dao.BaseDao;

@Repository(ElecInvoiceDao.NAME)
public class ElecInvoiceDaoImpl extends BaseDao implements ElecInvoiceDao {
    /* (non-Javadoc)
     * @see com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao#updateIgnorCaseSyn(java.lang.String, java.lang.String, net.sf.json.JSONObject)
     */
    public synchronized int updateIgnorCaseSyn(String tenantId,String tableName,JSONObject json) throws Exception{
        return this.updateIgnorCase(tenantId, tableName, json);
    }
    
	@Override
	public JSONObject getRequestInfo(String tenancy_id, int store_id)
			throws Exception {
	    StringBuffer sql = new StringBuffer();
	    sql.append("select legal.cnpj as XSF_NSRSBH,legal.tax_rate as SL,legal.* from hq_legal_per legal");
	    sql.append(" inner join hq_legal_per_organ_ref legal_ref on legal.id = legal_ref.legal_per_id");
	    sql.append(" where legal.valid_state='1' and legal_ref.organ_id = ?");
	    List<JSONObject> query4Json = this.query4Json(tenancy_id, sql.toString(), new Object[]{store_id});
	    if(query4Json.isEmpty()) {
	        return null;
	    }
	    JSONObject jsonObject = query4Json.get(0);
	    
		JSONObject json = new JSONObject();
		json.put("XSF_NSRSBH", jsonObject.optString("xsf_nsrsbh"));
		String sl = jsonObject.optString("sl");
		
		if(StringUtils.isNotBlank(sl) && !StringUtils.equals("null", sl)) {
			BigDecimal slbig = new BigDecimal(sl);
			if(slbig!=null) {
				slbig = slbig.setScale(2,BigDecimal.ROUND_HALF_UP);
				json.put("SL", slbig.toString());
			}
		}
		json.put("XMMC",jsonObject.optString("invoice_name"));
		json.put("seller_name", jsonObject.optString("seller_name"));
		json.put("seller_address", jsonObject.optString("seller_address"));
		json.put("seller_number", jsonObject.optString("seller_number"));
		json.put("bank", jsonObject.optString("bank"));
		return json;
	}

	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao#queryInfoByOrderNo(java.lang.String, java.lang.String)
	 */
	@Override
	public JSONObject queryInfoByOrderNo(String tenancy_id, String dh) throws Exception {
		String sql = "select * from hq_electronic_invoice_info where order_code = ? order by id desc";
		List<JSONObject> query4Json = this.query4Json(tenancy_id, sql, new Object[]{dh});
		if(query4Json.isEmpty()) {
			return null;
		}
		return query4Json.get(0);
	}

    /* (non-Javadoc)
     * @see com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao#queryPaymentWay(java.lang.String, int, java.lang.String)
     */
    @Override
    public List<JSONObject> queryPaymentWay(String tenancy_id, int store_id,
            String paymentClassStr) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append("select pw.* from payment_way pw");
        if(store_id!=0){
        	sql.append(" inner join payment_way_of_ogran pworgan on pw.id = pworgan.payment_id");
        	sql.append(" where pw.payment_class in ('");
        	sql.append(paymentClassStr).append("')");
        	sql.append(" and pworgan.organ_id = ").append(store_id);
        } else {
        	sql.append(" where 1=1 ");
        }
    	sql.append(" and pw.status = '1'");
        return query4Json(tenancy_id, sql.toString());
    }

	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao#queryInfoByFlowNumber(java.lang.String, java.lang.String)
	 */
	@Override
	public JSONObject queryInfoByFlowNumber(String tenancy_id, String flowNumber)
			throws Exception {
		String sql = "select * from hq_electronic_invoice_info where invoice_flow_number = ? order by id desc";
		List<JSONObject> query4Json = this.query4Json(tenancy_id, sql, new Object[]{flowNumber});
		if(query4Json.isEmpty()) {
			return null;
		}
		return query4Json.get(0);
	}

	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao#queryDetailsByFlowNumber(java.lang.String, java.lang.String)
	 */
	@Override
	public JSONObject queryDetailsByFlowNumber(String tenancy_id,
			int electricId) throws Exception {
		String sql = "select * from hq_electronic_invoice_details where electronic_id = ? order by id desc";
		List<JSONObject> query4Json = this.query4Json(tenancy_id, sql, new Object[]{electricId});
		if(query4Json.isEmpty()) {
			return null;
		}
		return query4Json.get(0);
	}
	
	/**
	 * 根据票通发票请求流水号查询(票通使用)
	 * @param tenancy_id
	 * @param dh
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 */
	public JSONObject queryInfoByReqserialno(String tenancy_id, String reqserialno) throws Exception{
		String sql = "select * from hq_electronic_invoice_info where pt_invoicereqserialno = ? order by id desc";
		List<JSONObject> query4Json = this.query4Json(tenancy_id, sql, new Object[]{reqserialno});
		if(query4Json.isEmpty()) {
			return null;
		}
		return query4Json.get(0);
	}
}
