package com.tzx.report.service.rest.userreport;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.io.InputStream;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSONException;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.framework.common.util.redis.BaseRedisUtil;

import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.report.bo.userreport.UserReportService;


@Controller("UserReportRest")
@RequestMapping("/report/UserReportRest")
public class UserReportRest
{
	@Resource(name = UserReportService.NAME)
	private UserReportService userReportService;
	
	@RequestMapping("/menu")
	@ApiOperation(value = "首页菜单",consumes= "multipart/form-data" ,httpMethod="POST",notes = "首页菜单")
	@ApiImplicitParams({
//		@ApiImplicitParam(dataType = "String",paramType = "form",name = "begin_date",value = "开始日期"),
	})
	public void menu(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		PrintWriter out = null;
		InputStream in = null;
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");
			DBContextHolder.setTenancyid((String) session.getAttribute("tenentid"));
			p.put("tenancy_id", (String) session.getAttribute("tenentid"));
			p.put("id", (String) session.getAttribute("employeeId"));
			p.put("store_id", (String) session.getAttribute("store_id"));
			List<JSONObject> list = userReportService.menu((String) session.getAttribute("tenentid"), p);
			if (list != null && list.size() > 0)
			{
				result = list.toString();
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping("/indexTarget")
	@ApiOperation(value = "首页指标",consumes= "multipart/form-data" ,httpMethod="POST",notes = "首页指标")
	@ApiImplicitParams({
//		@ApiImplicitParam(dataType = "String",paramType = "form",name = "begin_date",value = "开始日期",defaultValue="2017-07-06"),
//		@ApiImplicitParam(dataType = "String",paramType = "form",name = "end_date",value = "结束日期",defaultValue="2017-07-07"),
//		@ApiImplicitParam(dataType = "String",paramType = "form",name = "begin_time",value = "开始时间",defaultValue="2017-07-06 00:00:00"),
//		@ApiImplicitParam(dataType = "String",paramType = "form",name = "end_time",value = "结束时间",defaultValue="2017-07-06 23:59:59"),
//		@ApiImplicitParam(dataType = "String",paramType = "form",name = "store_ids",value = "门店id",required = true,defaultValue="0,1,9,11")
	})
	public void indexTarget(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String tenancy_id  = (String) session.getAttribute("tenentid");
		JSONObject result = null;
		JSONObject conditions = new JSONObject();
		try {
			out = response.getWriter();
			DBContextHolder.setTenancyid((String) session.getAttribute("tenentid"));
			conditions.put("store_id", (String)session.getAttribute("user_organ_codes_group"));
			result = userReportService.indexTarget(tenancy_id, conditions);
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(result.toString());
				out.flush();
				out.close();
			}
		}
	}
	
	/**
	 * 首页更新日志/公告
	 */
	@RequestMapping(value = "/loadNoticeInfoForHomePage")
	@ApiOperation(value = "首页更新日志",consumes= "multipart/form-data" ,httpMethod="POST",notes = "首页更新日志")
	@ApiImplicitParams({
	})
	public void loadNoticeInfoForHomePage(HttpServletRequest request, HttpServletResponse response){
		
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String returnJSONstring = "";
		try
		{
			JSONObject condition = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();
			
			for(String key : map.keySet()){
				if(map.get(key)[0] != null && !"".equals(map.get(key)[0])){
					condition.put(key, map.get(key)[0]);
				}
			}
			DBContextHolder.setTenancyid((String) session.getAttribute("tenentid"));
			condition.put("store_ids", (String)session.getAttribute("user_organ_codes_group"));

			returnJSONstring = userReportService.getNoticeInfoForHomePage((String) session.getAttribute("tenentid"), condition);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.println(returnJSONstring);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	@RequestMapping("/news")
	@ApiOperation(value = "首页新闻列表",consumes= "multipart/form-data" ,httpMethod="POST",notes = "首页新闻列表")
	@ApiImplicitParams({
	})
	public void news(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String tenancy_id  = (String) session.getAttribute("tenentid");
		JSONObject result = null;
		JSONObject conditions = new JSONObject();
		try {
			out = response.getWriter();
			DBContextHolder.setTenancyid((String) session.getAttribute("tenentid"));
			result = userReportService.news(tenancy_id, conditions);
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(result.toString());
				out.flush();
				out.close();
			}
		}
	}
	
	@RequestMapping("/newsDetails")
	@ApiOperation(value = "新闻详情",consumes= "multipart/form-data" ,httpMethod="POST",notes = "新闻详情")
	@ApiImplicitParams({
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "news_id",value = "新闻id",required=true)
	})
	public void newsDetails(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String tenancy_id  = (String) session.getAttribute("tenentid");
		DBContextHolder.setTenancyid(tenancy_id);
		JSONObject result = null;
		JSONObject conditions = new JSONObject();
		
		Map<String, String[]> map = request.getParameterMap();

		for (String key : map.keySet())
		{
			conditions.put(key, map.get(key)[0]);
		}
		
		try {
			out = response.getWriter();
			DBContextHolder.setTenancyid((String) session.getAttribute("tenentid"));
			result = userReportService.newsDetails(tenancy_id, conditions);
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(result.toString());
				out.flush();
				out.close();
			}
		}
	}
	
	@RequestMapping("/hotNews")
	@ApiOperation(value = "热点新闻",consumes= "multipart/form-data" ,httpMethod="POST",notes = "热点新闻")
	@ApiImplicitParams({
		@ApiImplicitParam(dataType = "Long",paramType = "form",name = "news_id",value = "新闻id",required=true)
	})
	public void hotNews(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		HttpSession session = request.getSession();
		String tenancy_id  = (String) session.getAttribute("tenentid");
		DBContextHolder.setTenancyid(tenancy_id);
		String result = null;
		JSONObject conditions = new JSONObject();
		
		Map<String, String[]> map = request.getParameterMap();

		for (String key : map.keySet())
		{
			conditions.put(key, map.get(key)[0]);
		}
		
		try {
			out = response.getWriter();
			DBContextHolder.setTenancyid((String) session.getAttribute("tenentid"));
			result = userReportService.hotNews(tenancy_id, conditions).toString();
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(null != out){
				out.print(result.toString());
				out.flush();
				out.close();
			}
		}
	}
 
}
