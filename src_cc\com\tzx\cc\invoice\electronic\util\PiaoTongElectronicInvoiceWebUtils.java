package com.tzx.cc.invoice.electronic.util;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import com.google.gson.GsonBuilder;
import com.tzx.cc.invoice.electronic.cache.PiaoTongConfigCache;
import com.tzx.cc.invoice.electronic.cache.YonyouConfigCache;
import com.tzx.cc.invoice.electronic.util.JwtParamBuilder;
import com.tzx.framework.common.util.DateUtil;
import com.vpiaotong.openapi.OpenApi;
import com.vpiaotong.openapi.util.HttpUtils;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.compression.CompressionCodecs;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

public class PiaoTongElectronicInvoiceWebUtils {
	private static final Logger logger = Logger.getLogger(PiaoTongElectronicInvoiceWebUtils.class);

	private static final String encodeCharacter = "UTF-8";

	/**
	 * 
	 * 查询二维码开票信息
     * @param tenancyId
     * @param organId
     * @param body
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 */
	public static JSONObject queryInvoiceQrStatus(String tenancyId,int organId,String body) throws Exception {
		 return call(tenancyId,organId,body,"/queryInvoiceQrCode.pt");
	}
	
	/**
	 * 
	 * 查询发票信息
     * @param tenancyId
     * @param organId
     * @param body
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 */
	public static JSONObject queryInvoiceStatus(String tenancyId,int organId,String body) throws Exception {
		 return call(tenancyId,organId,body,"/queryInvoice.pt");
	}

	//调用票通接口统一方法
	public static JSONObject call(String tenancyId,int organId,String body,String path) throws Exception {
		
		String privateKey = PiaoTongConfigCache.getElementText("privateKey");				//票通 privateKey
		
		String ptPublicKey = PiaoTongConfigCache.getElementText("ptPublicKey");				//票通 ptPublicKey

	    String password = PiaoTongConfigCache.getElementText("password");					//票通 提供的password

	    String prefix =PiaoTongConfigCache.getElementText("prefix");						//平台前缀(简称)
	    
	    String platformCode =PiaoTongConfigCache.getElementText("platformCode");			//请求平台编码
	    
	    String url = PiaoTongConfigCache.getElementText("root_url")+PiaoTongConfigCache.getElementText("total_url")+path;
        
        String buildRequest = new OpenApi(password, platformCode,prefix,privateKey).buildRequest(body);
        String response = HttpUtils.postJson(url, buildRequest);
        //logger.info("调用票通接口请求返回信息"+response);
        String result =new OpenApi(password,platformCode,prefix,privateKey).disposeResponse(response, ptPublicKey);
        logger.info("调用票通接口返回解密后信息：");
        logger.info(result);
		JSONObject resultJo = new JSONObject();
		
		resultJo = JSONObject.fromObject(result, new JsonConfig());
		return resultJo;
		
	}

    /**
     * 红冲电子发票
     * @param tenancyId
     * @param organId
     * @param body
     * @return
     * @throws Exception
     */
    public static JSONObject red(String tenancyId,int organId,String body) throws Exception {
        return call(tenancyId,organId,body,"/invoiceRed.pt");
    }

    /**
     * 取消电子发票(批量作废开票二维码)
     * @param tenancyId
     * @param organId
     * @param body
     * @return
     * @throws Exception
     */
    public static JSONObject cancel(String tenancyId,int organId,String body) throws Exception {
        return call(tenancyId,organId,body,"/deleteInvoiceQrCode.pt");
    }


	/**
	 * 
	 * 获取开票二维码和提取码接口
     * @param tenancyId
     * @param organId
     * @param body
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 */
	public static JSONObject invoice(String tenancyId,int organId,String body) throws Exception {
		 return call(tenancyId,organId,body,"/getQrCodeByItems.pt");
	}
	

	/**
	 * 签名
	 *
	 * @param paramsMap
	 *            表单参数
	 * @return 签名值
	 * @throws Exception
	 */
	/*private static String sign(String certificate_url,String certificate_password,Map<String, String> paramsMap) throws Exception {

		// 读取CA证书与PEM格式证书需要根据实际证书使用情况而定,目前这两种都支持
		PrivateKey privateKey = loadPrivateKeyOfCA(certificate_url,certificate_password);
		// PrivateKey privateKey = loadPrivateKeyOfPem();

		Map<String, Object> claims = JwtParamBuilder.build().setSubject("tester").setIssuer("einvoice")
				.setAudience("einvoice").addJwtId().addIssuedAt().setExpirySeconds(300).setNotBeforeSeconds(300)
				.getClaims();

		// 需要将表单参数requestdatas的数据进行md5加密，然后放到签名数据的requestdatas中。
		// 此签名数据必须存在，否则在验证签名时会不通过。
		String value = paramsMap.get("requestdatas");
		if(value != null && value.length()>0){
			claims.put("requestdatas", getMD5(value));
		}

		// 使用jdk1.6版本时，删除下面代码的中.compressWith(CompressionCodecs.DEFLATE)
		String compactJws = Jwts.builder().signWith(SignatureAlgorithm.RS512, privateKey).setClaims(claims)
				.compressWith(CompressionCodecs.DEFLATE).compact();

		return compactJws;
	}
*/
	/**
	 * 读取证书私钥
	 * 
	 * @return
	 * @throws UnrecoverableKeyException
	 * @throws KeyStoreException
	 * @throws NoSuchAlgorithmException
	 * @throws CertificateException
	 * @throws IOException
	 */
	/*protected static PrivateKey loadPrivateKeyOfCA(String certificate_url,String certificate_password) throws UnrecoverableKeyException, KeyStoreException,
			NoSuchAlgorithmException, CertificateException, IOException {
		InputStream in = RequestUtils.httpDownload(certificate_url);
		if(in==null) {
			logger.info("未找到所需要的证书，地址为："+certificate_url);
		}
		KeyStore ks = KeyStore.getInstance("pkcs12");
		String pwd = certificate_password;//"123456"; 证书密码
		ks.load(in, pwd.toCharArray());
		String alias = ks.aliases().nextElement();
		PrivateKey caprk = (PrivateKey) ks.getKey(alias, pwd.toCharArray());
		return caprk;
	}*/
	
	/**
	 * 计算MD5
	 * 
	 * @param str
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws NoSuchAlgorithmException
	 */
	/*private static String getMD5(String str) throws UnsupportedEncodingException, NoSuchAlgorithmException {
		byte[] buf = null;
		buf = str.getBytes("utf-8");
		MessageDigest md5 = null;
		md5 = MessageDigest.getInstance("MD5");
		md5.update(buf);
		byte[] tmp = md5.digest();
		StringBuilder sb = new StringBuilder();
		for (byte b : tmp) {
			sb.append(String.format("%02x", b & 0xff));
		}
		return sb.toString();
	}*/
	
}
