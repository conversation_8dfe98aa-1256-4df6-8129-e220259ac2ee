package com.tzx.cc.thirdparty.bo.imp;

import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.common.constant.DishOper;
import com.tzx.cc.common.redis.service.CcRedisService;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.bo.ThirdPartyManager;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.cc.thirdparty.util.PropertiesUtils;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.*;

public abstract class AbstractThirdPartyManager implements ThirdPartyManager
{

	protected static final Logger			logger		= LoggerFactory.getLogger(AbstractThirdPartyManager.class);
	/**
	 * 返回消息
	 */
	protected JSONObject					response	= JSONObject.fromObject("{\"errno\":\"0\"}");

	protected String						tenantId;
	protected String						shopId;
	protected String						channel;

	protected GenericDao					dao;
	protected DataSourceTransactionManager	transactionManager;
	
	private CcRedisService redis=(CcRedisService) SpringConext.getBean("ccRedisServiceImpl");

	public AbstractThirdPartyManager(String tenantId, String shopId, String channel)
	{
		this.tenantId = tenantId;
		this.shopId = shopId;
		this.channel = channel;

		dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
		transactionManager = (DataSourceTransactionManager) SpringConext.getBean("transactionManager");
	}

	public AbstractThirdPartyManager(String tenantId, String channel)
	{
		this.tenantId = tenantId;
		this.channel = channel;

		dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
		transactionManager = (DataSourceTransactionManager) SpringConext.getBean("transactionManager");
	}

	@Override
	public JSONObject getLocalShopInfo() throws Exception
	{

		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT shop_info.* FROM (");
		sql.append(" SELECT org_dd_ps.*, qj.\"id\" min_order_price_id, qj.min_order_price FROM ( ");
		sql.append(" SELECT org_dd.*, ps.\"id\" delivery_fee_id, ps.delivery_fee FROM ( ");
		sql.append(" SELECT org_dis.*, h.room_times delivery_time FROM ( ");
		sql.append(" SELECT org.*, d. ID district_id, d.coordinate, d. NAME region_name FROM ( ");
		sql.append(" SELECT ID shop_id, organ.org_full_name \"name\", organ.image1 shop_logo, dq.province, dq.city, dq.county, organ.regionalism, organ.address, organ.brand, organ.phone, organ.complain_phone service_phone, organ.longitude, organ.latitude FROM organ ");
		sql.append(" LEFT JOIN ( ");
		sql.append(" SELECT pc.province, REPLACE (pc.city, '市辖区', '') AS city, c2.reg_name AS county, C2.reg_code AS reg_code FROM sys_regionalism C2 ");
		sql.append(" LEFT JOIN ( ");
		sql.append(" SELECT P .reg_code, P .reg_name AS province, C .father_id, C .reg_code AS city_code, C .reg_name AS city FROM sys_regionalism AS P LEFT JOIN sys_regionalism AS C ON C .father_id = P .reg_code ");
		sql.append(" ) AS PC ON C2.father_id = PC.city_code ");
		sql.append(" ) AS dq ON organ.regionalism = dq.reg_code ");
		sql.append(" ) org LEFT JOIN crm_district d ON org.shop_id = d.store_id and d.valid_state='1' ) org_dis ");
		sql.append(" LEFT JOIN hq_organ h ON org_dis.shop_id = h.organ_id ) org_dd ");
		sql.append(" LEFT JOIN (SELECT \"id\", store_id, MONEY AS delivery_fee FROM cc_meals_info WHERE meals_type = 'PS01'AND channel = '" + channel + "') AS PS ON org_dd.shop_id = ps.store_id ) org_dd_ps  ");
		sql.append(" LEFT JOIN (SELECT \"id\", store_id, MONEY AS min_order_price FROM cc_meals_info WHERE meals_type = 'QJ02' ");
		sql.append(" AND channel = '" + channel + "') AS QJ ON org_dd_ps.shop_id = QJ.store_id ) shop_info WHERE shop_id ='" + shopId + "'");
		String source_secret_sql ="select source,secret from cc_third_organ_info where channel='" + channel + "'";
		List<JSONObject> source_secret_list=this.dao.query4Json(tenantId, source_secret_sql);
		JSONObject init_obj=new JSONObject();
		init_obj=this.dao.query4Json(tenantId, sql.toString()).get(0);
		if(source_secret_list.size()>0){
			JSONObject source_secret_obj=source_secret_list.get(0);
			init_obj.put("source", source_secret_obj.optString("source"));
			init_obj.put("secret", source_secret_obj.optString("secret"));
		}
		return init_obj;
	}

	@Override
	public List<JSONObject> getBussinessTime() throws Exception
	{
		String sql = "SELECT id,lineup_starttime as start,lineup_endtime as end FROM hq_lineup_time_org where valid_state='1' and store_id='" + shopId + "'";
		return this.dao.query4Json(tenantId, sql);
	}

	@Override
	public List<JSONObject> getPackageBoxPrice() throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT DISTINCT A .*, d.item_name||'('||e.unit_name||')'  as item_name FROM cc_meals_info_default A LEFT JOIN cc_meals_info b ON b. ID = A .meals_id LEFT JOIN hq_item_menu_details C ON C .item_id = A .item_id LEFT JOIN hq_item_menu_class d ON d.details_id = C . ID LEFT JOIN hq_item_unit e on e.id=a.unit_id WHERE b.store_id =");
		sql.append(shopId);
		sql.append(" AND b.channel = '");
		sql.append(channel);
		sql.append("' AND d.chanel = '");
		sql.append(channel);
		sql.append("' AND b.meals_type='MR03' ");
		sql.append(" ORDER BY A . ID");
		return this.dao.query4Json(tenantId, sql.toString());
	}

	@Override
	public JSONObject getLocalShopList(JSONObject params) throws Exception
	{
		StringBuilder sql = new StringBuilder("select cto.*,org.organ_code,org.org_full_name from cc_third_organ_info cto left join organ org on cto.shop_id=org.id where 1=1 and cto.channel='" + channel + "'");
		
		//添加门店 权限    2016年8月12日14:39:44 xgy  begin
		if(params.containsKey("authority_organ")) {
			String authority = params.optString("authority_organ");
			if(StringUtils.isNotBlank(authority)) {
				sql.append(" and cto.shop_id in (").append(authority).append(")");
			}
		}
		//添加门店 权限    2016年8月12日14:39:44 xgy  end
		if (CommonUtil.checkStringIsNotEmpty(params.optString("organ_code")))
		{
			sql.append(" and org.organ_code like'%" + params.optString("organ_code")+"%'");//机构门店加添加复选框
		}
		if (CommonUtil.checkStringIsNotEmpty(shopId) && !"0".equals(shopId))
		{
			sql.append(" and cto.shop_id in(" + shopId+")");//机构门店加添加复选框
		}
		/*else if(CommonUtil.checkStringIsNotEmpty(getAlreadyPushShopIDs(tenantId, channel))&&!"0".equals(getAlreadyPushShopIDs(tenantId, channel))){
			sql.append(" and shop_id in(" + getAlreadyPushShopIDs(tenantId, channel)+")");
		}*/
		if (params.containsKey("whether_push_over") && CommonUtil.checkStringIsNotEmpty(params.optString("whether_push_over")))
		{
			sql.append(" and cto.whether_push_over='" + params.optString("whether_push_over") + "'");
		}
		if(params.containsKey("push_state")&&CommonUtil.checkStringIsNotEmpty(params.optString("push_state"))&&!params.optString("push_state").equals("9999")){
			sql.append(" and cto.push_state='" + params.optString("push_state") + "'");
		}
		
		//新美大团购&闪惠 渠道 增加筛选“绑定状态”
		if(Constant.XMD_CHANNEL.equals(channel)){
			if(params.containsKey("shop_state") && Constant.XMD_SHOP_STATE_BIND.equals(params.optString("shop_state"))){
				sql.append(" and cto.shop_state='" + Constant.XMD_SHOP_STATE_BIND + "'");
			}else if(params.containsKey("shop_state")&&CommonUtil.checkStringIsNotEmpty(params.optString("shop_state"))&&!params.optString("shop_state").equals("9999")){
				sql.append(" and coalesce(cto.shop_state,'"+ Constant.XMD_SHOP_STATE_RELEASEBIND +"')!='" + Constant.XMD_SHOP_STATE_BIND + "'");
			}
		}
				
		if(params.containsKey("version")&&CommonUtil.checkStringIsNotEmpty(params.optString("version"))&&!params.optString("version").equals("9999")){
			sql.append(" and cto.version='" + params.optString("version") + "'");
		}

		int pagenum = params.containsKey("page") ? (params.getInt("page") == 0 ? 1 : params.getInt("page")) : 1;
		long total = this.dao.countSql(tenantId, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenantId, this.dao.buildPageSql(params, sql.toString()));

		JSONObject result = new JSONObject();
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}

	@Override
	public JSONObject saveShopInfo(JSONObject params)
	{
		TransactionStatus status = getTransctionStatus();//因数据库保存第三方异常原因
		if (channel.equals("BD06"))
		{
			String category23 = params.optString("category23");
			String[] category = category23.split(">");
			params.put("category2", category[0]);
			params.put("category3", category[1]);
		}
		try
		{	
			if(channel.equals("EL09")&&!params.optString("push_state").equals("1")){
				bindingEleRestaurant(params);
			}
			postShopInfo(params);
			if(!channel.equals("YC09"))
			{
				if (response.optInt("errno") != SUCCESS)
				{
					if (CommonUtil.checkStringIsNotEmpty(params.optString("id"))&&("0".equals(params.optString("push_state"))||"".equals(params.optString("push_state"))))
					{
						params.put("push_state", "2");
						this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);
					}else if(CommonUtil.checkStringIsNotEmpty(params.optString("id"))&&("1".equals(params.optString("push_state")))){

						params.put("push_state", "2");
						params.put("online_flag", "1");
						this.dao.insertIgnorCase(tenantId, "cc_third_organ_info", params);
					}
					logger.info("[第三方商户推送失败异常信息：]"+response);
					throw new Exception("Invoke third party open api fail!" + response);
				}else{
					if(response.containsKey("data")&&response.optJSONObject("data")!=null&&channel.equals(Constant.BAIDU_CHANNEL)){
						String third_shop_id=response.optJSONObject("data").optString("baidu_shop_id");
						params.put("third_shop_id", third_shop_id);
					}
					if (CommonUtil.checkStringIsNotEmpty(params.optString("id")))
					{
						params.put("push_state", "1");
						this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);
					}
					else
					{
						params.put("push_state", "1");
						this.dao.insertIgnorCase(tenantId, "cc_third_organ_info", params);
					}
				}
			}
			transactionManager.commit(status);
			saveRedisSignObject(tenantId,channel,params.optString("shop_id"));//保存rredis
		}
		catch (Exception e)
		{
			e.printStackTrace();
			System.out.println(e.getMessage());
			transactionManager.rollback(status);
			if(response.containsKey("errno") && response.optInt("errno")==0)
			{
				response.put("errno", -1);
				response.put("msg", e);
			}
			logger.info("保存门店信息错误：" + response);
		}

		return response;
	}

	protected void saveRedisSignObject(String tenantId,String channel,String shopId) throws Exception{
		String sql="SELECT source,secret,app_auth_token FROM cc_third_organ_info WHERE channel='"+channel+"' AND shop_id='"+shopId+"'";
		List<JSONObject> list=dao.query4Json(tenantId, sql);
		
		if(list.size()==0){
			throw new Exception("third_organ_info shops not exits!");
		}else{
			JSONObject object=list.get(0);
			String key=shopId+"@"+channel+"@"+tenantId;
			JSONObject o=new JSONObject();
			String source=object.optString("source");
			String secret=object.optString("secret");
	        String appAuthToken=object.optString("app_auth_token");
	        o.put("source", source);
	        o.put("secret", secret);
	        o.put("app_auth_token", appAuthToken);
			redis.saveBykv(key, o.toString(), 0);
		}
		
	}
	
	private void bindingEleRestaurant(JSONObject params) throws Exception {
		JSONObject response=new JSONObject();
		if(channel.equals("EL09")&&!params.getString("third_shop_id").isEmpty()){
			response=bindRestaurantId(params,"put");
			int count=0;
			String thirdID="tp_restaurant_id "+params.getString("shop_id")+"@"+tenantId+" has existed";
			while(!response.optString("message").equals("ok")||response==null){
				if(response.optString("error").equals(thirdID)){
					response.remove("error");
					response.put("errno", "0");
					response.put("message", "ok");
					break;
				}
				try {
					response=bindRestaurantId(params,"put");
				} catch (Exception e1) {
					e1.printStackTrace();
				}
				if (!response.optString("message").equals("ok")) {
					try {
						count++;
						logger.debug("获取绑定商户信息ID:"+params.optString("shop_id")+"@"+tenantId+"失败，重新获取"+response.optString("message").equals("ok"));
						Thread.sleep(2000);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
				if (count > 1) {
					break;
				}
			}
		}
	}

	@Override
	public JSONObject saveDeliveryTime(JSONObject params) throws Exception
	{
		TransactionStatus status = getTransctionStatus();

		try
		{

			this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);

			postDeliveryTime(params);

			if (response.optInt("errno") != SUCCESS)
			{
				throw new Exception("Invoke third party open api fail!" + response);
			}
			transactionManager.commit(status);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			transactionManager.rollback(status);
			logger.info("保存美团配送时长信息错误：" + response);
		}

		return response;
	}

	@Override
	public JSONObject saveDeliveryRegion(JSONObject params) throws Exception
	{
		TransactionStatus status = getTransctionStatus();

		try
		{

			this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);

			postDeliveryRegion(params);

			if (response.optInt("errno") != SUCCESS)
			{
				throw new Exception("Invoke third party open api fail!" + response);
			}
			transactionManager.commit(status);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			transactionManager.rollback(status);
			logger.info("保存配送范围信息错误：" + response);
		}

		return response;
	}

	@Override
	public JSONObject setShopStatus(JSONObject params)
	{

		TransactionStatus status = getTransctionStatus();

		try
		{

			this.dao.updateIgnorCase(tenantId, "cc_third_organ_info", params);

			postShopStatus(params);

			if (response.optInt("errno") != SUCCESS)
			{
				throw new Exception("Invoke third party open api fail!" + response);
			}
			transactionManager.commit(status);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			if(response.containsKey("errno") && response.optInt("errno")==0)
			{
				response.put("errno", -1);
				response.put("msg", e);
			}
			logger.info("保存商户状态信息错误：" + response);
			transactionManager.rollback(status);
		}

		return response;
	}

	@Override
	public JSONObject getLocalDishCategoryList(String tenancyID, JSONObject condition) throws Exception
	{

		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		StringBuilder update_sql = new StringBuilder();
		// 菜品类别名称不同修改推送状态
		update_sql.append("UPDATE  cc_third_item_class_info SET whether_push_over = '3'  WHERE id IN (SELECT a.id FROM cc_third_item_class_info A ");
		update_sql.append(" LEFT JOIN hq_item_class b ON A .item_class_id = b. ID LEFT join hq_item_class e on e.id=a.item_class_id");
		update_sql.append(" WHERE A .whether_push_over = '1' and b.chanel='" + condition.optString("channel") + "' AND A .last_send_class_name != b.itemclass_name ");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			update_sql.append(" and A.shop_id in (" + condition.optString("shop_id") + " ) ");
		}
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			update_sql.append(" and a.channel = 'BD06'  ");
		}
		else
		{
			update_sql.append(" and a.channel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id")))
		{
			update_sql.append(" and e.itemclass_name =  '" + condition.optString("class_id") + "' ");
//			update_sql.append(" and a.item_class_id =  " + condition.optString("class_id") + " ");
		}
		update_sql.append(")");
		this.dao.execute(tenancyID, update_sql.toString());
		update_sql.delete(0, update_sql.length());
		// 菜品类别的显示顺序不同修改推送状态
		update_sql.append("UPDATE cc_third_item_class_info SET whether_push_over = '3'  WHERE id IN (SELECT a.id FROM cc_third_item_class_info A ");
		update_sql.append(" LEFT JOIN hq_item_menu_class b ON A .item_class_id = b.class LEFT JOIN hq_item_menu_details c on c.id=b.details_id LEFT JOIN hq_item_menu_organ d on d.item_menu_id=c.item_menu_id LEFT join hq_item_class e on e.id=a.item_class_id");
		update_sql.append(" WHERE A .whether_push_over = '1'  AND  a.rank != b.menu_class_rank");

		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			update_sql.append(" and A.shop_id in (" + condition.optString("shop_id") + " ) ");
		}
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			update_sql.append(" and a.channel = 'BD06'  ");
		}
		else
		{
			update_sql.append(" and a.channel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id")))
		{
			update_sql.append(" and e.itemclass_name =  '" + condition.optString("class_id") + "' ");
//			update_sql.append(" and a.item_class_id =  " + condition.optString("class_id") + " ");
		}
		//2017-3-15修改对应机构启用的餐谱下菜品类别的显示顺序不同修改推送状态
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			update_sql.append(" and d.store_id in (" + condition.optString("shop_id") + " ) ");
		}
		update_sql.append(")");
		this.dao.execute(tenancyID, update_sql.toString());
		update_sql.delete(0, update_sql.length());
		// 20160513修改
		sql.append("SELECT d.third_class_id,d.remark,l.third_shop_id,l.app_auth_token,l.version,d.id,e.itemclass_code,d.third_class_id,A.class as class_id,e.itemclass_name as cur_class_name,d.last_send_class_name,CASE WHEN A .menu_class_rank  IS NULL THEN d.rank ELSE A .menu_class_rank END AS rank, CASE WHEN d.whether_push_over IS NULL THEN '0' ELSE d.whether_push_over END AS whether_push_over,f.id as store_id,f.org_full_name,a.chanel as channel");
		
		//moidfy at 2017-04-27 start
		sql.append(",d.start_sell_time,d.end_sell_time");
		//moidfy at 2017-04-27 end
		
		sql.append(" FROM hq_item_menu_class A");
		sql.append(" LEFT JOIN hq_item_menu_details b ON A .details_id = b. ID");
		sql.append(" LEFT JOIN hq_item_menu_organ  c on c.item_menu_id= b.item_menu_id");
		sql.append(" LEFT JOIN cc_third_item_class_info d on d.item_class_id=a.class and d.shop_id=c.store_id");
		sql.append(" LEFT JOIN hq_item_class e on e.id=a.class");
		sql.append(" LEFT JOIN cc_third_organ_info l on l.shop_id=c.store_id");
		// 20170208修改 显示没有做过数据推送的店（之前版本不显示）by
		sql.append(" LEFT JOIN organ f on f.id=c.store_id where 1=1 ");
		//sql.append(" LEFT JOIN organ f on f.id=c.store_id where 1=1 and l.third_shop_id is not null ");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			sql.append(" and c.store_id in (" + condition.optString("shop_id") + " ) ");
		}
		//20170209修改 显示没有做过数据推送的店（之前版本不显示）by
		/**else if(CommonUtil.checkStringIsNotEmpty(getAlreadyPushShopIDs(tenantId, channel))&&!"0".equals(getAlreadyPushShopIDs(tenantId, channel))){
			sql.append(" and c.store_id in (" + getAlreadyPushShopIDs(tenantId, channel) + " ) ");
		}**/
		
		//添加门店 权限      2016年8月12日13:40:05   xgy    begin
		if(condition.has("authority_organ")) {
			String authority_organ = condition.optString("authority_organ");
			if(StringUtils.isNotBlank(authority_organ)) {
				sql.append(" and c.store_id in (").append(authority_organ).append(") ");
			}
		}
		//添加门店 权限      2016年8月12日13:40:05   xgy    end
		
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			sql.append(" and a.chanel = 'BD06'  ");
			
			sql.append(" and l.channel = 'BD06'  ");
		}
		else
		{
			sql.append(" and a.chanel = '" + condition.optString("channel") + "'  ");
			sql.append(" and l.channel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("whether_push_over") && !StringUtils.isEmpty(condition.optString("whether_push_over")))
		{
			if (!condition.optString("whether_push_over").equals("9999") && !condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and d.whether_push_over = '" + condition.optString("whether_push_over") + "'  ");
			}
			//20170414此if判断语句影响第三方菜品分类信息按条件推送状态过滤未推送状态分类查询因此注释;请勿随意修改
			else if (condition.optString("whether_push_over").equals("0"))
			{	
				sql.append(" and (d.whether_push_over='0' or d.whether_push_over IS NULL) ");
			}
		}
		/*
		 * if (condition.containsKey("cur_class_name") &&
		 * !StringUtils.isEmpty(condition.optString("cur_class_name"))) {
		 * sql.append(" and e.itemclass_name like  '%" +
		 * condition.optString("cur_class_name") + "%'  "); }
		 */
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id")))
		{
//			sql.append(" and a.class =  " + condition.optString("class_id") + " ");
			sql.append(" and e.itemclass_name =  '" + condition.optString("class_id") + "' ");
		}
		if (condition.containsKey("third_class_id") && CommonUtil.checkStringIsNotEmpty(condition.optString("third_class_id")))
		{
			if (condition.optString("third_class_id").equals("0")){
				sql.append(" and (d.third_class_id is null or d.third_class_id ='') ");
			}else if(condition.optString("third_class_id").equals("1")){
				sql.append(" and (d.third_class_id is not null or d.third_class_id!='') ");
			}
		}
		// sql.append(" where c.store_id in (40,183) and a.chanel='BD06' and d.whether_push_over in('1') and e.itemclass_name='盖饭'");
		sql.append(" GROUP BY  f.id ,f.org_full_name,d.whether_push_over,d.id,A.class,e.itemclass_name,d.last_send_class_name,a.menu_class_rank,a.chanel,e.itemclass_code,l.third_shop_id,l.version,l.app_auth_token");
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenancyID, sql.toString());

		
		//添加排序 先按门店 后按序号     xgy   2016年8月8日16:24:00    begin
//		condition.put("sort", "f.id ,CASE WHEN A .menu_class_rank  IS NULL THEN d.rank ELSE A .menu_class_rank END");
//		condition.put("order", "ASC");
		//添加排序 先按门店 后按序号     xgy   2016年8月8日16:24:00    end
		
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, sql.toString()));
		if (list.size() > 0)
		{
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
		}
		else
		{
			result.put("page", "1");
			result.put("total", "0");
			result.put("rows", "[]");
		}

		logger.info("[获取菜品分类列表]" + result);
		return result;

	}
	
	@Override
	public JSONObject loadDishCategoryListNoPage(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();

		// 20160513修改
		sql.append("SELECT l.third_shop_id,l.version,d.id,A.class as class_id,e.itemclass_name as cur_class_name,d.last_send_class_name,CASE WHEN A .menu_class_rank  IS NULL THEN d.rank ELSE A .menu_class_rank END AS rank, CASE WHEN d.whether_push_over IS NULL THEN '0' ELSE d.whether_push_over END AS whether_push_over,f.id as store_id,f.org_full_name,a.chanel as channel");
		
		sql.append(",d.start_sell_time,d.end_sell_time");
		
		sql.append(" FROM hq_item_menu_class A");
		sql.append(" LEFT JOIN hq_item_menu_details b ON A .details_id = b. ID");
		sql.append(" LEFT JOIN hq_item_menu_organ  c on c.item_menu_id= b.item_menu_id");
		sql.append(" LEFT JOIN cc_third_item_class_info d on d.item_class_id=a.class and d.shop_id=c.store_id");
		sql.append(" LEFT JOIN hq_item_class e on e.id=a.class");
		sql.append(" LEFT JOIN cc_third_organ_info l on l.shop_id=c.store_id");
		
		//为了获取目标部门和源部门共有的菜品类型所添加 xgy begin 2016年8月5日15:29:15
		if (condition.containsKey("dest_shop_ids") && !StringUtils.isEmpty(condition.optString("dest_shop_ids")))
		{
			String destShopIds = condition.optString("dest_shop_ids");
			sql.append(" inner join (");
			sql.append(" SELECT e.itemclass_name as cur_class_name");
			sql.append(" FROM hq_item_menu_class A");
			sql.append(" LEFT JOIN hq_item_menu_details b ON A .details_id = b. ID");
			sql.append(" LEFT JOIN hq_item_menu_organ  c on c.item_menu_id= b.item_menu_id");
			sql.append(" LEFT JOIN cc_third_item_class_info d on d.item_class_id=a.class and d.shop_id=c.store_id");
			sql.append(" LEFT JOIN hq_item_class e on e.id=a.class LEFT JOIN organ f on f.id=c.store_id");
			sql.append(" where 1=1 and c.store_id in (").append(destShopIds).append(")");
			sql.append(" and a.chanel = '").append(condition.optString("channel")).append("'");
			sql.append(" ) as qq on e.itemclass_name = qq.cur_class_name");
		}
		//为了获取目标部门和源部门共有的菜品类型所添加 xgy end 2016年8月5日15:29:15
		
		sql.append(" LEFT JOIN organ f on f.id=c.store_id where 1=1");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			sql.append(" and c.store_id in (" + condition.optString("shop_id") + " ) ");
		}else{
			if(CommonUtil.checkStringIsNotEmpty(getAlreadyPushShopIDs(tenancyID, channel))&&!"0".equals(getAlreadyPushShopIDs(tenancyID, channel))){
				sql.append(" and c.store_id in (" + getAlreadyPushShopIDs(tenancyID, channel) + " ) ");
			}
			
		}
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			sql.append(" and a.chanel = 'BD06'  ");
			sql.append(" and l.channel='BD06' ");
		}
		else
		{
			sql.append(" and a.chanel = '" + condition.optString("channel") + "'  ");
			sql.append(" and l.channel = '" + condition.optString("channel") + "'  ");
		}
		if(!condition.containsKey("oper_copy")){
			sql.append(" and (d.whether_push_over is null or d.whether_push_over !='1')");
		}
		if (condition.containsKey("whether_push_over") && !StringUtils.isEmpty(condition.optString("whether_push_over")))
		{
			if (!condition.optString("whether_push_over").equals("9999") && !condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and d.whether_push_over = '" + condition.optString("whether_push_over") + "'  ");
			}
			else if (condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and (d.whether_push_over IS NULL  or d.whether_push_over='0') ");
			}
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id")))
		{
			sql.append(" and a.class =  " + condition.optString("class_id") + " ");
		}
		sql.append(" GROUP BY  f.id ,f.org_full_name,d.whether_push_over,d.id,A.class,e.itemclass_name,d.last_send_class_name,a.menu_class_rank,a.chanel,l.third_shop_id,l.version");
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		if (list.size() > 0)
		{
			result.put("rows", list);
		}
		else
		{
			result.put("rows", "[]");
		}
		logger.info("[获取百度菜品分类列表]" + result);
		return result;
	}
	@Override
	public JSONObject saveDishCategory(List<JSONObject> params) throws Exception
	{

		JSONObject result = new JSONObject();
		int success = 0, fail = 0;
	//	TransactionStatus status = getTransctionStatus();//因数据库保存第三方异常原因
		try
		{
			for (JSONObject p : params)
			{
				p.put("requestId", p.optString("requestId"));
				p.put("shop_id", p.optString("store_id"));
				p.put("item_class_id", p.optString("class_id"));
				p.put("xmd_last_send_class_name", p.optString("last_send_class_name"));
				p.put("last_send_class_name", p.optString("cur_class_name"));
				//modify at 2017-04-27 start
				p.put("start_sell_time", p.optString("start_sell_time"));
				p.put("end_sell_time", p.optString("end_sell_time"));
				//modify at 2017-04-27 end
				
				if (p.optString("send").equals("no") && p.containsKey("send"))
				{
					if(Constant.XMDWM_CHANNEL.equals(channel)){
						p.put("last_send_class_name", p.get("xmd_last_send_class_name"));
					}

					Object id = null;
					if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
					{
						if (p.optString("whether_push_over").equals("0"))
						{
							p.put("whether_push_over", "0");
						}
						else if (p.optString("whether_push_over").equals("1"))
						{
							p.put("whether_push_over", "3");
						}
						id = this.dao.updateIgnorCase(tenantId, "cc_third_item_class_info", p);
					}
					else
					{
						//changhui 2017-11-27 增加判断条件，从菜品映射过来的直接变为已推送状态 start
						if (p.optString("isWhether").equals("yes") && p.containsKey("isWhether"))
						{
							p.put("whether_push_over", "1");
						}else{
							p.put("whether_push_over", "0");
						}
						//end
						//p.put("whether_push_over", "0");changhui 注释
						p.remove("id");
						p.put("tenancy_id", tenantId);
						id = this.dao.insertIgnorCase(tenantId, "cc_third_item_class_info", p);
					}
					if (id != null)
					{
						result.put("success", true);
						result.put("msg", "保存成功");
					}
					else
					{
						result.put("success", false);
						result.put("msg", "保存失败");
					}

				}
				else
				{
					if (Constant.ELE_CHANNEL.equals(channel)&&CommonUtil.checkStringIsNotEmpty(p.optString("third_class_id")))
					{
						updateDishCategory(p);
					}else{
						postDishCategory(p);
						if(Constant.ELE_CHANNEL.equals(channel)){
							if(response.containsKey("data")){
								String third_class_id=response.optJSONObject("data").optString("food_category_id");
								if(CommonUtil.checkStringIsNotEmpty(third_class_id)){
									p.put("third_class_id", third_class_id);
								}
							}
						}else if(Constant.MEITUAN_CHANNEL.equals(channel)&&response.optInt("errno")==SUCCESS){
							p.put("third_class_id", response.optInt("errno"));
						}else if(Constant.XMDWM_CHANNEL.equals(channel)&&response.optInt("errno")==SUCCESS){
							p.put("third_class_id", response.optInt("errno"));
						}
					}
					
					if (!"no".equals(p.optString("send")) && p.containsKey("send"))
					{
						if (response.optInt("errno") != SUCCESS)
						{
							String errMsg = "";
							if(channel.equals(Constant.XMDWM_CHANNEL)){
//								if(response.containsKey("errno") &&  response.optString("error").contains(":")){
//									String info = response.optString("error").substring(response.optString("error").indexOf("[")+1,response.optString("error").lastIndexOf("]"));
//										errMsg = JSONObject.fromObject(info).optString("error_msg");
//								}else{
									errMsg = response.optString("error");
//								}
//								p.put("rank", p.optString("rank").equals("") ? "1"
//										: p.optString("rank"));
								p.put("last_send_class_name", p.get("xmd_last_send_class_name"));
							}else{
							// 解析错误代码
								errMsg = PropertiesUtils.getErrMsgByErrno(channel, response.optString("errno"), response.optString("error"));
							}
							if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
							{
								p.put("whether_push_over", "2");
								p.put("online_flag", "1");
								//p.put("remark", response.optString("error"));
								// 修改推送失败后错误信息返回英文的问题
								p.put("remark", errMsg);
								this.dao.updateIgnorCase(tenantId, "cc_third_item_class_info", p);
							}
							else
							{
								p.put("whether_push_over", "2");
								//p.put("remark", response.optString("error"));
								// 修改推送失败后错误信息返回英文的问题
								p.put("remark", errMsg);
								p.put("tenancy_id", tenantId);
								this.dao.insertIgnorCase(tenantId, "cc_third_item_class_info", p);
							}
							result.put("success", false);
							result.put("msg", "推送失败");
						}
						else
						{
							if(channel.equals(Constant.XMDWM_CHANNEL)){
								p.put("rank", p.optString("rank").equals("") ? "1"
										: p.optString("rank"));
							}
							if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
							{
								p.put("whether_push_over", "1");
								// 2017024修改推送菜品类别成功后，之前的推送失败的错误原因也一并清除 by
								p.put("remark","");
								this.dao.updateIgnorCase(tenantId, "cc_third_item_class_info", p);
							}
							else
							{
								p.put("whether_push_over", "1");
								p.put("tenancy_id", tenantId);
								// 2017024修改推送菜品类别成功后，之前的推送失败的错误原因也一并清除 by
								p.put("remark","");
								this.dao.insertIgnorCase(tenantId, "cc_third_item_class_info", p);
							}
							//2017-03-15 START (rif同步过来的分类新与第三方分类列表中顺序不一致时，以rif系统同步的顺序为准修改第三方分类顺序)
							updateSaasDishCategoryRank(tenantId, p);
							//2017-03-15 END
							result.put("success", true);
							result.put("msg", "推送成功");
						}
					}
					else
					{
						if (response.optInt("errno") != SUCCESS)
						{
							fail++;
							String errMsg = "";
							if(channel.equals(Constant.XMDWM_CHANNEL)){
								errMsg = response.optString("error");
//								p.put("rank", p.optString("rank").equals("") ? "1"
//										: p.optString("rank"));
								p.put("last_send_class_name", p.get("xmd_last_send_class_name"));
							}else{
							// 解析错误代码
								errMsg = PropertiesUtils.getErrMsgByErrno(channel, response.optString("errno"), response.optString("error"));
							}
							if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
							{
								p.put("whether_push_over", "2");
								p.put("online_flag", "1");
								//p.put("remark", response.optString("error"));
								p.put("remark", errMsg);
								this.dao.updateIgnorCase(tenantId, "cc_third_item_class_info", p);
							}
							else
							{
								p.put("whether_push_over", "2");
								//p.put("remark", response.optString("error"));
								p.put("remark", errMsg);
								p.put("tenancy_id", tenantId);
								this.dao.insertIgnorCase(tenantId, "cc_third_item_class_info", p);
							}

							logger.info("========同步菜品类别第三方返回错误提示========"+response);
							//throw new Exception("Invoke third party open api fail!" + response);
							// 20170313修改推送失败后，抛异常导致事物回滚，影响错误信息不能插入和修改
							result.put("success", false);
							result.put("msg", "推送失败");
						}
						else
						{
							if(channel.equals(Constant.XMDWM_CHANNEL)){
								p.put("rank", p.optString("rank").equals("") ? "1"
										: p.optString("rank"));
							}
							success++;
							if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
							{
								p.put("whether_push_over", "1");
								// 2017024修改推送菜品类别成功后，之前的推送失败的错误原因也一并清除 by
								p.put("remark","");
								this.dao.updateIgnorCase(tenantId, "cc_third_item_class_info", p);
							}
							else
							{
								p.put("whether_push_over", "1");
								p.put("tenancy_id", tenantId);
								// 2017024修改推送菜品类别成功后，之前的推送失败的错误原因也一并清除 by
								p.put("remark","");
								this.dao.insertIgnorCase(tenantId, "cc_third_item_class_info", p);
							}
							result.put("success", true);
							result.put("msg", "成功:" + success + "\t 失败:" + fail);
						}
					}
				}
			}
			//transactionManager.commit(status);
			if(params.size()>0 && params.get(0).optString("channel").equals(Constant.YICHI_CHANNEL))
			{
				response.put("success", result.optBoolean("success"));
				return response;
			}
			return result;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			//transactionManager.rollback(status);
			result = JSONObject.fromObject(response);
			if(response.containsKey("errno") && response.optInt("errno")==0)
			{
				result.put("errno", -1);
				if(e.toString().contains("token结果没空")){
					result.put("msg", "token结果为空,请先授权!");
				}else{
					result.put("msg", e);
				}
				
			}
			logger.info("外卖保存菜品类别信息错误：" + response);
		}

		return result;

	}

	@Override
	public JSONObject setCategoryRank(JSONObject params) {
		List<JSONObject> dishCategorys=params.optJSONArray("rows");
		List<JSONObject> categoryIDs=new ArrayList<JSONObject>();
		long thirdShopID=0L;
		for(JSONObject dishCategory:dishCategorys){
			JSONObject category=new JSONObject();
			if(dishCategory.containsKey("third_class_id")&&CommonUtil.checkStringIsNotEmpty(dishCategory.optString("third_class_id"))){
				thirdShopID=dishCategory.optLong("third_shop_id");
				category.put("id", dishCategory.optLong("third_class_id"));
				category.put("rank", dishCategory.optInt("rank"));
				categoryIDs.add(category);
			}
			
		}
		 Collections.sort(categoryIDs, new Comparator<JSONObject>(){  
		        public int compare(JSONObject category1, JSONObject category) {  
		          
		            //按照学生的年龄进行升序排列  
		            if(category1.getInt("rank") > category.getInt("rank")){  
		                return 1;  
		            }  
		            if(category1.getInt("rank") > category.getInt("rank")){  
		                return 0;  
		            }  
		            return -1;  
		        }  
		    });
		 try {
			 JSONObject categoryObject=new JSONObject();
			 List<Long> categoryIds=new ArrayList<Long>();
			 for(JSONObject object:categoryIDs){
				 categoryIds.add(object.optLong("id"));
			 }
			 categoryObject.put("categoryIds", categoryIds);
			 categoryObject.put("third_shop_id", thirdShopID);
			 setCategoryPositions(categoryObject);
			response.put("errno", "0");
		} catch (Exception e) {
			e.printStackTrace();
		}
		 return response;
	}

	private void updateSaasDishCategoryRank(String tenancyID,JSONObject condition) throws Exception {
		String store_id=String.valueOf(condition.optInt("store_id"));
		String sql="SELECT a.id,b.menu_class_rank FROM cc_third_item_class_info A LEFT JOIN hq_item_menu_class b ON A .item_class_id = b.class LEFT JOIN hq_item_menu_details c on c.id=b.details_id LEFT JOIN hq_item_menu_organ d on d.item_menu_id=c.item_menu_id LEFT join hq_item_class e on e.id=a.item_class_id WHERE A .whether_push_over = '1'  AND  a.rank != b.menu_class_rank and a.channel='"+condition.optString("channel")+"' and a.shop_id='"+store_id+"' and d.store_id='"+store_id+"'";
		List<JSONObject> item_class_list=dao.query4Json(tenancyID, sql);
		if(item_class_list.size()>0){
			for(JSONObject itemclass:item_class_list){
				JSONObject rifDishCategory=new JSONObject();
				rifDishCategory.put("id", itemclass.optString("id"));
				rifDishCategory.put("rank", itemclass.optString("menu_class_rank"));
				this.dao.updateIgnorCase(tenancyID, "cc_third_item_class_info", rifDishCategory);
			}
		}
	}
	
	@Override
	public JSONObject getLocalDishList(JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		//加载列表数据量大时执行很慢  （为提升菜品信息加载效率注释该sql）
		StringBuilder update_sql = new StringBuilder();
//		update_sql
//				.append("UPDATE cc_third_item_info SET whether_push_over = '3' WHERE ID IN (SELECT A . ID FROM cc_third_item_info A LEFT JOIN hq_item_info b on cast(b.id as VARCHAR)=a.item_code LEFT JOIN hq_item_class f on f.id=b.item_class LEFT JOIN hq_item_menu_details c on b.id=c.item_id LEFT JOIN hq_item_menu_class e on e.details_id=c.id LEFT join hq_item_menu_organ d on d.item_menu_id=c.item_menu_id");
//		if (condition.containsKey("shop_id"))
//		{
//			update_sql.append(" and a.shop_id in (" + condition.optString("shop_id") + ")");
//		}
//		update_sql.append(" where (b.last_updatetime>a.send_time or c.menu_item_rank!=a.rank  OR f.last_updatetime > A.send_time) and a.whether_push_over='1' and a.channel='" + condition.optString("channel") + "')");
//		this.dao.execute(tenantId, update_sql.toString());
		update_sql.delete(0, update_sql.length());
		/*
		 * sql.append( //
		 * "select DISTINCT(C.item_id),g.g_unit_name as unit,e.rank,e.is_sold_out,e.id, i.package_box_price as box_price,d.store_id,a.id as item_code,b.item_name,a.item_barcode,g.g_price as default_price,e.min_order_num,e.package_box_num,e.whether_push_over,e.description,a.photo1 as wxxt,e.item_pic as photo1,e.item_pic_id,e.available_times_start,e.available_times_end,concat (f.itemclass_name,'-',ll.itemclass_name)as last_send_class_name,a.is_combo,case when a.is_combo='Y' then '套餐' else '单品' end as attr_name,e.item_status from hq_item_menu_class b inner join hq_item_menu_details c on b.details_id = c.id inner join hq_item_menu_organ d on c.item_menu_id = d.item_menu_id inner join hq_item_menu h on c.item_menu_id = h.id inner join cc_third_organ_info i on i.shop_id = d.store_id and i.channel = '"
		 * "select DISTINCT(C.item_id),g.g_unit_name as unit,e.rank,e.is_sold_out,e.id, i.package_box_price as box_price,d.store_id,a.id as item_code,b.item_name,a.item_barcode,g.g_price as default_price,e.min_order_num,e.package_box_num,e.whether_push_over,e.description,a.photo1 as wxxt,e.item_pic as photo1,e.item_pic_id,e.available_times_start,e.available_times_end,f.itemclass_name as last_send_class_name,a.is_combo,case when a.is_combo='Y' then '套餐' else '单品' end as attr_name,e.item_status from hq_item_menu_class b inner join hq_item_menu_details c on b.details_id = c.id inner join hq_item_menu_organ d on c.item_menu_id = d.item_menu_id inner join hq_item_menu h on c.item_menu_id = h.id inner join cc_third_organ_info i on i.shop_id = d.store_id and i.channel = '"
		 * + channel +
		 * "' left join hq_item_info a on a.id = c.item_id left join cc_third_item_info e on e.item_code = CAST(A.id as VARCHAR) and e.shop_id="
		 * + shopId + "  and e.channel=b.chanel"); sql.append(
		 * " LEFT JOIN hq_item_class f ON b. CLASS = f.id LEFT JOIN hq_item_class ll ON f.father_id = ll.id"
		 * ); sql.append(
		 * " left join ( select c1.unit_name as g_unit_name,a1.price as g_price,	c1.item_id as g_item_id from hq_item_pricesystem a1 left join organ b1 on cast(a1.price_system as varchar) = b1.price_system left join hq_item_unit c1 on a1.item_unit_id = c1.id where b1.id="
		 * + shopId + " and a1.chanel = '" + channel +
		 * "' and c1.valid_state = '1' and c1.is_default = 'Y' ) g on g.g_item_id = c.item_id "
		 * ); sql.append("where d.store_id = " + shopId + " and b.chanel = '" +
		 * channel + "' and h.valid_state = '1'");
		 */
		if(condition.containsKey("channel")&&condition.optString("channel").equals("WM10")){
			sql=weXDishSql(condition, sql);
		}else{
			//at 20170822 			sql.append("SELECT a.tenancy_id,e.remark,e.stock,e.max_stock,e.is_new,e.is_featured,e.is_gum,e.is_spicy,kk.third_class_id,C .item_id,a.item_code as item_code_yc,e.third_item_id,f.id as item_class,ci.third_shop_id,ci.version,e.is_charge_commission,bb.price AS box_price,b.chanel as channel,G .g_unit_name AS unit,G .g_unit_id AS unit_id, CASE WHEN C .menu_item_rank IS NULL THEN e.rank ELSE C .menu_item_rank END AS RANK,e.is_sold_out,e. ID,d.store_id,d.store_id as shop_id,hh.org_full_name,A . ID AS item_code,b.item_name,A .item_barcode,G .g_price AS default_price,e.min_order_num,e.package_box_num,CASe WHEN e.whether_push_over IS NULL THEN '0' ELSE e.whether_push_over END AS whether_push_over,e.description,A .photo1 AS item_pic,A .photo1 AS wxxt,e.item_pic AS photo1,e.item_pic_id,e.available_times_start,e.available_times_end,f.itemclass_name AS last_send_class_name,A .is_combo,CASE WHEN A .is_combo = 'Y' THEN '套餐' ELSE '单品' END AS attr_name,e.item_status,CASE WHEN C .menu_item_rank IS NULL THEN e. RANK ELSE C .menu_item_rank END AS menu_item_rank,CASE WHEN b .menu_class_rank IS NULL THEN kk.RANK ELSE b .menu_class_rank END AS menu_class_rank,A.remark AS description ");
			sql.append("SELECT a.tenancy_id,e.remark,e.stock,e.max_stock,e.is_new,e.is_featured,e.is_gum,e.is_spicy,kk.third_class_id,C .item_id,a.item_code as item_code_yc,e.third_item_id,f.id as item_class,ci.third_shop_id,ci.app_auth_token,ci.version,e.is_charge_commission,");
			sql.append("e.dish_available_time,bb.price AS box_price,b.chanel as channel,G .g_unit_name AS unit,G .g_unit_id AS unit_id, e.rank,e.is_sold_out,e. ID,d.store_id,d.store_id as shop_id,hh.org_full_name,A . ID AS item_code,b.item_name,A .item_barcode,G .g_price AS default_price,");
			sql.append("e.min_order_num,e.package_box_num,CASe WHEN e.whether_push_over IS NULL THEN '0' ELSE e.whether_push_over END AS whether_push_over,e.description,A .photo1 AS item_pic,A .photo1 AS wxxt,e.item_pic AS photo1,e.item_pic_id,e.available_times_start,e.available_times_end,f.itemclass_name AS last_send_class_name,");
			sql.append("A .is_combo,CASE WHEN A .is_combo = 'Y' THEN '套餐' ELSE '单品' END AS attr_name,e.item_status,CASE WHEN C .menu_item_rank IS NULL THEN e. RANK ELSE C .menu_item_rank END AS menu_item_rank,CASE WHEN b .menu_class_rank IS NULL THEN kk.RANK ELSE b .menu_class_rank END AS menu_class_rank,A.remark AS description ,");
			sql.append("CASE WHEN kk.whether_push_over IS NULL THEN '0' ELSE kk.whether_push_over END AS category_status,COALESCE(e.norms,'[]') AS norms ");
			sql.append(" FROM hq_item_menu_class b");
			sql.append(" INNER JOIN hq_item_menu_details C ON b.details_id = C.ID");
			sql.append(" INNER JOIN hq_item_menu_organ d ON C .item_menu_id = d.item_menu_id");
			sql.append(" INNER JOIN cc_third_organ_info ci on ci.shop_id = d.store_id and ci.channel='"+condition.optString("channel")+"'");
			sql.append(" INNER JOIN hq_item_menu h ON C .item_menu_id = h. ID");
			sql.append(" LEFT JOIN hq_item_info A ON A . ID = C .item_id");
			sql.append(" LEFT JOIN cc_third_item_info e ON e.item_code = CAST (A . ID AS VARCHAR) and e.shop_id = d.store_id AND e.channel = b.chanel");
			if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
			{
				sql.append(" and e.shop_id in (" + condition.optString("shop_id") + " ) ");
			}
			sql.append(" LEFT JOIN hq_item_class f ON b. CLASS = f. ID");
			sql.append(" LEFT JOIN hq_item_class ll ON f.father_id = ll. ID");
			sql.append(" LEFT JOIN organ hh on d.store_id=hh.id");
			sql.append(" LEFT JOIN cc_meals_info aa on aa.store_id=hh.id and aa.meals_type='MR03' ");
			sql.append(" LEFT JOIN cc_meals_info_default bb on bb.meals_id=aa.id");
			sql.append(" LEFT JOIN cc_third_item_class_info kk ON kk.item_class_id=b.class and kk.channel=b.chanel and kk.shop_id=hh.id");
			sql.append(" LEFT JOIN (SELECT c1.id AS g_unit_id,c1.unit_name AS g_unit_name,a1.price AS g_price,c1.item_id AS g_item_id,b1.id as g_store_id FROM hq_item_pricesystem a1 LEFT JOIN organ b1 ON CAST (a1.price_system AS VARCHAR) = b1.price_system LEFT JOIN hq_item_unit c1 ON a1.item_unit_id = c1. ID");
			sql.append(" WHERE c1.valid_state = '1' AND c1.is_default = 'Y' ");
			if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
			{
				sql.append(" and b1.id in (" + condition.optString("shop_id") + " ) ");
			}
			if (condition.containsKey("channel") && !StringUtils.isEmpty(condition.optString("channel")))
			{
				sql.append(" and a1.chanel = '" + condition.optString("channel") + "'  ");
			}

			sql.append(" ) G ON G .g_item_id = C .item_id and g.g_store_id=d.store_id");
			sql.append(" WHERE  h.valid_state = '1' ");
			if(!channel.equals(Constant.MEITUAN_CHANNEL)){
				// 20170316修改 放开菜品查询时与菜品类别的关联，修改后，菜品类别没有推送，菜品也可以查看
				//sql.append("  and kk.third_class_id is not null");
			}
			
			if(StringUtils.isNotEmpty(condition.optString("third_item_ids"))){
				sql.append("  and (e.third_item_id is  null or  e.third_item_id ='')");
			}
			
		}
		
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			sql.append(" and d.store_id in (" + condition.optString("shop_id") + " ) ");
		}else{
			//添加门店 权限   2016年8月12日13:53:54   xgy  begin
			if(condition.containsKey("authority_organ")) {
				String authority = condition.optString("authority_organ");
				if(StringUtils.isNotBlank(authority)) {
					sql.append(" and d.store_id in (").append(authority).append(")");
				}
			}
			//添加门店 权限   2016年8月12日13:53:54   xgy  end
		}
		/*else if(CommonUtil.checkStringIsNotEmpty(getAlreadyPushShopIDs(tenantId, channel))&&!"0".equals(getAlreadyPushShopIDs(tenantId, channel))){
			sql.append(" and d.store_id in (" + getAlreadyPushShopIDs(tenantId, channel) + " ) ");
		}*/
		if (condition.containsKey("channel") && !StringUtils.isEmpty(condition.optString("channel"))&&!condition.optString("channel").equals("WM10"))
		{
			sql.append(" and b.chanel = '" + condition.optString("channel") + "' and ci.channel = '" + condition.optString("channel") + "'  and aa.channel='" + condition.optString("channel") + "' ");
		}else{
			sql.append(" and b.chanel = '" + condition.optString("channel") +"'  ");
		}
		if (condition.containsKey("whether_push_over") && !StringUtils.isEmpty(condition.optString("whether_push_over")))
		{
			if (!condition.optString("whether_push_over").equals("9999") && !condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and e.whether_push_over = '" + condition.optString("whether_push_over") + "'  ");
			}
			else if (condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and (e.whether_push_over='0' or e.whether_push_over IS NULL) ");
			}
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id")))
		{
//			sql.append(" and b.class =  " + condition.optString("class_id") + " ");
			sql.append(" and f.itemclass_name in ('" + condition.optString("class_id") + "') ");
		}
		if (condition.containsKey("item_name") && !StringUtils.isEmpty(condition.optString("item_name")))
		{
			sql.append(" and a.item_name like '%" + condition.optString("item_name") + "%' ");
		}
		if (condition.containsKey("item_code") && !StringUtils.isEmpty(condition.optString("item_code")))
		{
			sql.append(" and a.id ='" + condition.optString("item_code") + "' ");
		}
		if (condition.containsKey("third_item_id") && CommonUtil.checkStringIsNotEmpty(condition.optString("third_item_id")))
		{
			if (condition.optString("third_item_id").equals("0")){
				sql.append(" and (e.third_item_id is null or e.third_item_id ='') ");
			}else if(condition.optString("third_item_id").equals("1")){
				sql.append(" and (e.third_item_id is not null or e.third_item_id!='') ");
			}
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		long total = this.dao.countSql(tenantId, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenantId, this.dao.buildPageSql(condition, sql.toString()));
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);

		return result;
	}

	private StringBuilder weXDishSql(JSONObject condition, StringBuilder sql) {
		sql.append("SELECT C .item_id,a.item_code as item_code_yc,e.third_item_id,e.dish_available_time,f.id as item_class,e.is_charge_commission,e.box_price,b.chanel as channel, CASE WHEN C .menu_item_rank IS NULL THEN e.rank ELSE C .menu_item_rank END AS RANK,e.is_sold_out,e. ID,d.store_id,hh.org_full_name,A . ID AS item_code,b.item_name,A .item_barcode,e.min_order_num,e.package_box_num,CASe WHEN e.whether_push_over IS NULL THEN '0' ELSE e.whether_push_over END AS whether_push_over,e.description,A .photo1 AS wxxt,e.item_pic AS photo1,e.item_pic_id,e.available_times_start,e.available_times_end,f.itemclass_name AS last_send_class_name,A .is_combo,CASE WHEN A .is_combo = 'Y' THEN '套餐' ELSE '单品' END AS attr_name,e.item_status,CASE WHEN C .menu_item_rank IS NULL THEN e. RANK ELSE C .menu_item_rank END AS menu_item_rank");
		sql.append(" FROM hq_item_menu_class b");
		sql.append(" INNER JOIN hq_item_menu_details C ON b.details_id = C.ID");
		sql.append(" INNER JOIN hq_item_menu_organ d ON C .item_menu_id = d.item_menu_id");
		sql.append(" INNER JOIN hq_item_menu h ON C .item_menu_id = h. ID");
		sql.append(" LEFT JOIN hq_item_info A ON A . ID = C .item_id");
		sql.append(" LEFT JOIN cc_third_item_info e ON e.item_code = CAST (A . ID AS VARCHAR) and e.shop_id = d.store_id AND e.channel = b.chanel");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			sql.append(" and e.shop_id in (" + condition.optString("shop_id") + " ) ");
		}
		sql.append(" LEFT JOIN hq_item_class f ON b. CLASS = f. ID");
		sql.append(" LEFT JOIN hq_item_class ll ON f.father_id = ll. ID");
		sql.append(" LEFT JOIN organ hh on d.store_id=hh.id");
		sql.append(" WHERE  h.valid_state = '1' ");
		return sql;
	}

	//查询已经推送的商户ID
	public String getAlreadyPushShopIDs(String tenantId,String channel) throws Exception{
		String sql="SELECT shop_id FROM cc_third_organ_info WHERE third_shop_id is not null and tenant_id='"+tenantId+"' AND channel='"+channel+"'";
		List<JSONObject> list=this.dao.query4Json(tenantId, sql);
		String shopIDs="";
		if(list.size()>0){
			for(JSONObject jsonObject:list){
				shopIDs+=jsonObject.optInt("shop_id")+",";
			}
			if(shopIDs.length()>0){
				shopIDs=shopIDs.substring(0, shopIDs.length()-1);
			}
		}
		return shopIDs;
	}
	
	@Override
	public JSONObject getLocalDishNoPageList(String tenancyID, JSONObject condition) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();
		//增加”,G .g_unit_id AS unit_id“字段和”,G .g_unit_name AS unit“字段，用作新美大外卖菜品映射使用 changhui 2017-11-21
		//增加",kk.id as last_send_class_id"字段，用做菜品分类做映射时存储第三方外卖类别id使用 changhui 2017-12-15
		sql.append("SELECT DISTINCT(C .item_id),kk.third_class_id,a.item_code as item_code_yc,e.is_charge_commission,e.dish_available_time,e.third_item_id,f.id as item_class,ci.third_shop_id,e.box_price,b.chanel as channel,G .g_unit_name AS unit,e. RANK,e.is_sold_out,e.box_price,e. ID,d.store_id,hh.org_full_name,");
	    sql.append("A . ID AS item_code,b.item_name,A .item_barcode,G .g_price AS default_price,e.min_order_num,e.package_box_num,CASe WHEN e.whether_push_over IS NULL THEN '0' ELSE e.whether_push_over END AS whether_push_over,e.description,A .photo1 AS wxxt,e.item_pic AS photo1,e.item_pic_id,e.available_times_start,e.available_times_end,");
	    sql.append("f.itemclass_name AS last_send_class_name,A .is_combo,CASE WHEN A .is_combo = 'Y' THEN '套餐' ELSE '单品' END AS attr_name,e.item_status,ci.version,G .g_unit_id AS unit_id,kk.id as last_send_class_id,COALESCE(e.norms,'[]') AS norms ");
		sql.append(" FROM hq_item_menu_class b");
		sql.append(" INNER JOIN hq_item_menu_details C ON b.details_id = C.ID");
		sql.append(" INNER JOIN hq_item_menu_organ d ON C .item_menu_id = d.item_menu_id");
		sql.append(" INNER JOIN cc_third_organ_info ci on ci.shop_id = d.store_id and ci.channel='"+condition.optString("channel")+"'");
		sql.append(" INNER JOIN hq_item_menu h ON C .item_menu_id = h. ID");
		
		//为了获取目标部门和源部门共有的菜品所添加
		if (condition.containsKey("dest_shop_ids") && !StringUtils.isEmpty(condition.optString("dest_shop_ids")))
		{
			String destShopIds = condition.optString("dest_shop_ids");
			sql.append(" INNER JOIN ( SELECT dest_b.item_id as item_id FROM hq_item_menu_class dest_a");
			sql.append(" inner join hq_item_menu_details dest_b on dest_a.details_id = dest_b.id");
			sql.append(" INNER JOIN hq_item_menu_organ dest_c ON dest_b .item_menu_id = dest_c.item_menu_id ");
			
			sql.append(" where dest_c.store_id in (").append(destShopIds).append(")");
			sql.append(" and dest_a.chanel = '").append(condition.optString("channel")).append("'");
			sql.append(" ) qq on  qq.item_id = c.item_id");
		}
		
		sql.append(" LEFT JOIN hq_item_info A ON A . ID = C .item_id");
		sql.append(" LEFT JOIN cc_third_item_info e ON e.item_code = CAST (A . ID AS VARCHAR) AND e.channel = b.chanel");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			sql.append(" and e.shop_id in (" + condition.optString("shop_id") + " ) ");
		}
		sql.append(" LEFT JOIN hq_item_class f ON b. CLASS = f. ID");
		sql.append(" LEFT JOIN hq_item_class ll ON f.father_id = ll. ID");
		sql.append(" LEFT JOIN organ hh on d.store_id=hh.id");
		sql.append(" LEFT JOIN cc_third_item_class_info kk ON kk.item_class_id = b. CLASS AND kk.channel = b.chanel AND kk.shop_id = hh. ID");
		//增加”c1.id AS g_unit_id,“字段，用作新美大外卖菜品映射使用 changhui 2017-11-21
		sql.append(" LEFT JOIN (SELECT c1.id AS g_unit_id,c1.unit_name AS g_unit_name,a1.price AS g_price,c1.item_id AS g_item_id,b1.id as g_store_id FROM hq_item_pricesystem a1 LEFT JOIN organ b1 ON CAST (a1.price_system AS VARCHAR) = b1.price_system LEFT JOIN hq_item_unit c1 ON a1.item_unit_id = c1. ID");
		sql.append(" WHERE c1.valid_state = '1' AND c1.is_default = 'Y' ");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			sql.append(" and b1.id in (" + condition.optString("shop_id") + " ) ");
		}
		if (condition.containsKey("channel") && !StringUtils.isEmpty(condition.optString("channel")))
		{
			sql.append(" and a1.chanel = '" + condition.optString("channel") + "'  ");
		}

		sql.append(" ) G ON G .g_item_id = C .item_id and g.g_store_id=d.store_id");
		sql.append(" WHERE  h.valid_state = '1' ");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			sql.append(" and d.store_id in (" + condition.optString("shop_id") + " ) ");
		}
		if (condition.containsKey("channel") && !StringUtils.isEmpty(condition.optString("channel")))
		{
			sql.append(" and b.chanel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("whether_push_over") && !StringUtils.isEmpty(condition.optString("whether_push_over")))
		{
			if (!condition.optString("whether_push_over").equals("9999") && !condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and e.whether_push_over = '" + condition.optString("whether_push_over") + "'  ");
			}
			else if (condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and (e.whether_push_over='0' or e.whether_push_over IS NULL) ");
			}
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id")))
		{
			sql.append(" and f.itemclass_name in('" + condition.optString("class_id") + "') ");//2017-3-17推送菜品信息有查询条件按下面的执行方法报错修改(jhy)
//			sql.append(" and b.class in('" + condition.optString("class_id") + "') ");
		}
		if (condition.containsKey("oper") && !StringUtils.isEmpty(condition.optString("oper")))
		{
			if (condition.optString("oper").equals("delete"))
			{
				sql.append(" and e.whether_push_over ='1' ");
			}
			if (condition.optString("oper").equals("offline"))
			{
				sql.append(" and e.item_status ='1' ");
			}
			if (condition.optString("oper").equals("online"))
			{
				sql.append(" and e.item_status ='0'  and e.whether_push_over ='1' ");
			}
			//修改已推送的菜品也可以重复推送
		/*	if (condition.optString("oper").equals("push"))
			{
				sql.append(" and  (e.whether_push_over != '1' or e.whether_push_over is null)   ");
			}*/
			if (condition.containsKey("item_name") && !StringUtils.isEmpty(condition.optString("item_name")))
			{
				sql.append(" and a.item_name like '%" + condition.optString("item_name") + "%' ");
			}
			
			//changhui  2017-11-24 add 用于新美大外卖菜品映射后向cc_third_item_info表添加信息  strat
			if (condition.containsKey("item_id") && !StringUtils.isEmpty(condition.optString("item_id")))
			{
				sql.append(" and c.item_id = '" + condition.optString("item_id") + "'  ");
			}
			//end
		}
		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		if (list.size() > 0)
		{
			result.put("rows", list);
		}
		else
		{
			result.put("rows", "[]");
		}
		String msg="";
		if(channel==Constant.MEITUAN_CHANNEL){
			msg="[获取美团菜品列表]";	
		}
		if(channel==Constant.ELE_CHANNEL){
			msg="[获取饿了么菜品列表]";
		}
		logger.info(msg + result);
		return result;

	}
	
	
	
	@Override
	public List<JSONObject> getDishClassIsMapList(String tenancyID, JSONObject condition) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		String channel = condition.optString("channel");
		switch (channel)
		{
			case "EL09":
				sql.append("select * from cc_third_item_class_info where third_class_id is not null and third_class_id!='' ");
				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and shop_id in (" + condition.optString("shop_id") + " ) ");
				}
				sql.append(" and channel = '" + channel + "' ");
				
				break;
			default:
				sql.append("select * from cc_third_item_class_info where third_class_id is not null and third_class_id!='' ");
				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and shop_id in (" + condition.optString("shop_id") + " ) ");
				}
				sql.append(" and channel = '" + channel + "' ");	
		}

		List<JSONObject> dish_class_is_map_list = this.dao.query4Json(tenancyID, sql.toString());
		logger.info("查询已绑定的分类传入的参数为：" +condition.toString()+"返回的信息为："+dish_class_is_map_list.toString() );
		return dish_class_is_map_list;
	}
	@Override
	public List<JSONObject> getDishIsMapList(String tenancyID, JSONObject condition)
	{
		StringBuilder sql = new StringBuilder();
		String channel = condition.optString("channel");
		switch (channel)
		{
			case "EL09":
				sql.append("SELECT e.remark,e.stock,e.max_stock,e.is_new,e.is_featured,e.is_gum,e.is_spicy,kk.third_class_id,C .item_id,a.item_code as item_code_yc,e.third_item_id,f.id as item_class,ci.third_shop_id,e.is_charge_commission,e.box_price,b.chanel as channel,G .g_unit_name AS unit,	CASE WHEN C .menu_item_rank IS NULL THEN e.rank ELSE C .menu_item_rank END AS RANK,e.is_sold_out,e.id,d.store_id,hh.org_full_name,A . ID AS item_code,b.item_name,A .item_barcode,G .g_price AS default_price,e.min_order_num,e.package_box_num,CASe WHEN e.whether_push_over IS NULL THEN '0' ELSE e.whether_push_over END AS whether_push_over,e.description,A .photo1 AS wxxt,e.item_pic AS photo1,e.item_pic_id,e.available_times_start,e.available_times_end,f.itemclass_name AS last_send_class_name,A .is_combo,CASE WHEN A .is_combo = 'Y' THEN '套餐' ELSE '单品' END AS attr_name,e.item_status,CASE WHEN C .menu_item_rank IS NULL THEN e. RANK ELSE C .menu_item_rank END AS menu_item_rank,CASE WHEN b .menu_class_rank IS NULL THEN kk.RANK ELSE b .menu_class_rank END AS menu_class_rank");
				sql.append(" FROM hq_item_menu_class b");
				sql.append(" INNER JOIN hq_item_menu_details C ON b.details_id = C.ID");
				sql.append(" INNER JOIN hq_item_menu_organ d ON C .item_menu_id = d.item_menu_id");
				sql.append(" INNER JOIN cc_third_organ_info ci on ci.shop_id = d.store_id");
				sql.append(" INNER JOIN hq_item_menu h ON C .item_menu_id = h. ID");
				sql.append(" LEFT JOIN hq_item_info A ON A . ID = C .item_id");
				sql.append(" LEFT JOIN cc_third_item_info e ON e.item_code = CAST (A . ID AS VARCHAR) and e.shop_id = d.store_id AND e.channel = b.chanel");
				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and e.shop_id in (" + condition.optString("shop_id") + " ) ");
				}
				sql.append(" LEFT JOIN hq_item_class f ON b. CLASS = f. ID");
				sql.append(" LEFT JOIN hq_item_class ll ON f.father_id = ll. ID");
				sql.append(" LEFT JOIN organ hh on d.store_id=hh.id");
				sql.append(" LEFT JOIN cc_third_item_class_info kk ON kk.item_class_id=b.class and kk.channel=b.chanel and kk.shop_id=hh.id");
				sql.append(" LEFT JOIN (SELECT c1.unit_name AS g_unit_name,a1.price AS g_price,c1.item_id AS g_item_id,b1.id as g_store_id FROM hq_item_pricesystem a1 LEFT JOIN organ b1 ON CAST (a1.price_system AS VARCHAR) = b1.price_system LEFT JOIN hq_item_unit c1 ON a1.item_unit_id = c1. ID");
				sql.append(" WHERE c1.valid_state = '1' AND c1.is_default = 'Y' ");
				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and b1.id in (" + condition.optString("shop_id") + " ) ");
				}
				if (condition.containsKey("channel") && !StringUtils.isEmpty(condition.optString("channel")))
				{
					sql.append(" and a1.chanel = '" +channel+ "'  ");
				}

				sql.append(" ) G ON G .g_item_id = C .item_id and g.g_store_id=d.store_id");
				sql.append(" WHERE  h.valid_state = '1' ");
				sql.append(" AND kk.third_class_id IS NOT NULL and third_class_id='"+condition.optString("third_class_id")+"' ");
				if(!channel.equals(Constant.MEITUAN_CHANNEL)){
					sql.append("  and kk.third_class_id is not null");
				}
				sql.append(" AND e.third_item_id is not null and e.third_item_id!='' ");
				if (condition.containsKey("channel") && !StringUtils.isEmpty(condition.optString("channel")))
				{
					sql.append(" and ci.channel = '" +channel+ "'  ");
				}
				break;
			default:
				sql.append("select * from cc_third_item_class_info where third_class_id is not null and third_class_id!='' ");
				if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
				{
					sql.append(" and shop_id in (" + condition.optString("shop_id") + " ) ");
				}
				sql.append(" and channel = '" + channel + " ' ");	
		}

		List<JSONObject> dish_class_is_map_list =new ArrayList<JSONObject>();
		try {
			dish_class_is_map_list = this.dao.query4Json(tenancyID, sql.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
		logger.info("查询已绑定的菜品信息传入的参数为：" +condition.toString()+"返回的信息为："+dish_class_is_map_list.toString() );
		return dish_class_is_map_list;
	}
	@Override
	public JSONObject getLocalPriceSystem(JSONObject params) throws Exception
	{
		JSONObject result = new JSONObject();
		StringBuilder sql = new StringBuilder();

		
		
		sql.append("select c.id,c.unit_name,c.is_default,a.price from hq_item_pricesystem a left join organ b on cast(a.price_system as varchar) = b.price_system left JOIN hq_item_unit c on a.item_unit_id = c.id ");
		sql.append("where c.is_default='Y' and c.item_id='" + params.optInt("item_id") + "' and b.id = '" + params.optInt("shop_id") + "' and a.chanel = '" + channel + "' and c.valid_state = '1'");

		int pagenum = params.containsKey("page") ? (params.getInt("page") == 0 ? 1 : params.getInt("page")) : 1;
		long total = this.dao.countSql(tenantId, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenantId, this.dao.buildPageSql(params, sql.toString()));
		
		String query_meal_sql= "SELECT B.item_id,B.price from cc_meals_info a LEFT JOIN cc_meals_info_default  b on b.meals_id=a.id where a.store_id=" + params.optInt("shop_id") + " and a.channel='" + channel + "' AND A.meals_type='MR03'";
		List<JSONObject> query_meal_list = this.dao.query4Json(tenantId, query_meal_sql.toString());
		if(query_meal_list.size()>0){
			result.put("box_price", query_meal_list.get(0).optDouble("price",0.0));
			}
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);

		return result;
	}

	@Override
	public JSONObject saveDish(List<JSONObject> params) throws Exception
	{
		JSONObject result = new JSONObject();
		int success = 0, fail = 0;
		JSONObject errorObject=new JSONObject();
		//2017-10-09因为推送菜品信息添加日志信息，异常时，在第三方接口的方法调用中捕获了异常信息，导致推送菜品信息页面无错误提示因此取消事务属性
//		TransactionStatus status = getTransctionStatus();//因数据库保存第三方异常原因
		try
		{
			for (JSONObject p : params)
			{
				errorObject=JSONObject.fromObject(p);
				if (p.optString("send").equals("no") && p.containsKey("send"))
				{

					Object id = null;
					if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
					{
						if (p.optString("whether_push_over").equals("0"))
						{
							p.put("whether_push_over", "0");
						}
						else if (p.optString("whether_push_over").equals("1"))
						{
							p.put("whether_push_over", "3");
						}
						id = this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
					}
					else
					{
						//changhui 2017-11-17 增加判断条件，从菜品映射过来的直接变为已推送状态 start
						if (p.optString("isWhether").equals("yes") && p.containsKey("isWhether"))
						{
							p.put("whether_push_over", "1");
						}else{
							p.put("whether_push_over", "0");
						}
						//end

						//changhui 2018-1-10 注释
						//id = this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);

						//changhui 2018-1-10 添加判断第三方菜品信息是否重复 begin
						String strId = getDisRepeatCount(tenantId, p);
						if("".equals(strId)){
							id = this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);
						}else{
							p.put("id", strId);
							id = this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
						}
						//end
					}
					if (id != null)
					{
						result.put("success", true);
						result.put("msg", "保存成功");
					}
					else
					{
						result.put("success", false);
						result.put("msg", "保存失败");
					}

				}
				else
				{
					response.clear();
					if (Constant.ELE_CHANNEL.equals(channel)&&CommonUtil.checkStringIsNotEmpty(p.optString("third_item_id")))
					{
						updateDish(p);
						// 20170310 修改，平台删除菜品后，则应该调用平台的新增方法。由于不能判断平台菜品是否存在，所以当调用平台修改接口返回错误代码为1020时，则说明平台菜品记录被删除。
						if(response.optInt("errno") == 1020){
							postDishSingle(p);
							// 注意这里推送成功后，平台会返回一个平台的菜品ID，需要将返回的菜品ID存储在数据库中
							if(response.containsKey("data")&&channel.equals(Constant.ELE_CHANNEL)){
								String third_item_id=response.optJSONObject("data").optString("food_id");
								if(CommonUtil.checkStringIsNotEmpty(third_item_id)){
									p.put("third_item_id", third_item_id);
								}
								/*20180307增加norms规格信息的回写  */
								String norms = response.optJSONObject("data").optString("norms");
								if(CommonUtil.checkStringIsNotEmpty(norms)){
									p.put("norms", norms);
								}
							}
						}

					} else {
						postDishSingle(p);
						if (response.containsKey("data") && channel.equals(Constant.ELE_CHANNEL)) {
							String third_item_id = response.optJSONObject("data").optString("food_id");
							if (CommonUtil.checkStringIsNotEmpty(third_item_id)) {
								p.put("third_item_id", third_item_id);
							}
							
							/*20180307增加norms规格信息的回写  */
							String norms = response.optJSONObject("data").optString("norms");
							if(CommonUtil.checkStringIsNotEmpty(norms)){
								p.put("norms", norms);
							}
						}
						// 20170316 修改，saas未推送的菜品，饿了么平台存在与之同名菜品，则推送调用平台新增接口时报错误代码1016：菜品已存在，那么应该调用平台的修改接口，可是这时saas后台并无法获取这个平台菜品的ID（saas端为third_item_id），则查询
						/*if (response.optInt("errno") == 1016) {
							getFoodCategoryFoods(p);

							if (response.containsKey("data")) {
								
								JSONArray foods = JSONArray.fromObject(response.optJSONObject("data").optJSONArray("foods"));
								if (foods.size() > 0) {

									for (int i = 0; i < foods.size(); i++) {
										JSONObject food = (JSONObject) foods.get(i);
										String food_name = food.optString("food_name");
										
										if (p.optString("item_name").equals(food_name)) {
											p.put("third_item_id",food.optString("food_id"));
											response.remove("data");
										}
									}
									updateDish(p);
								} else {
									//response.put("errno", "9001");
								}
							}
						}*/
					}
					if (!"no".equals(p.optString("send")) && p.containsKey("send"))
					{
						if (response.optInt("errno") != SUCCESS)
						{
							String errMsg = "";
							if(channel.equals(Constant.XMDWM_CHANNEL)){
//								if(response.containsKey("errno") &&  response.optString("error").contains(":")){
//									String info = response.optString("error").substring(response.optString("error").indexOf("[")+1,response.optString("error").lastIndexOf("]"));
//										errMsg = JSONObject.fromObject(info).optString("error_msg");
//								}else{
									errMsg = response.optString("error");
//								}
							}else{
								// 解析错误信息
								errMsg = PropertiesUtils.getErrMsgByErrnoNew(channel, response);
							}
								if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
								{
									p.put("whether_push_over", "2");
									p.put("online_flag", "1");
									// p.put("remark", response.optString("error"));
									// 修改批量推送失败后显示中文提示信息
									p.put("remark", errMsg);
									this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
								}
								else
								{
									p.put("whether_push_over", "2");
									// p.put("remark", response.optString("error"));
									// 修改批量推送失败后显示中文提示信息
									p.put("remark", errMsg);
									// 如果
									if (response.optInt("errno") == 1004) {
										p.put("third_item_id", "");
									}


									//this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);

									//changhui 2018-1-10 添加判断第三方菜品信息是否重复 begin
									String strId = getDisRepeatCount(tenantId, p);
									if("".equals(strId)){
										this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);
									}else{
										p.put("id", strId);
										this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
									}
									//end
								}
								result.put("success", false);
								result.put("msg", "推送失败："+errMsg);
							
						}
						else
						{
							if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
							{
								p.put("whether_push_over", "1");
								// p.put("remark", response.optString("data"));
								p.put("remark","");
								this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
							}
							else
							{
								p.put("whether_push_over", "1");
								// p.put("remark", response.optString("data"));
								p.put("remark","");


								//this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);

								//changhui 2018-1-10 添加判断第三方菜品信息是否重复 begin
								String strId = getDisRepeatCount(tenantId, p);
								if("".equals(strId)){
									this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);
								}else{
									p.put("id", strId);
									this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
								}
								//end
							}
							result.put("success", true);
							result.put("msg", "推送成功");
						}
					}
					else
					{
						if (response.optInt("errno") != SUCCESS)
						{
							fail++;
							String errMsg = "";
							if(channel.equals(Constant.XMDWM_CHANNEL)){
//								if(response.containsKey("errno") &&  response.optString("error").contains(":")){
//									String info = response.optString("error").substring(response.optString("error").indexOf("[")+1,response.optString("error").lastIndexOf("]"));
//										errMsg = JSONObject.fromObject(info).optString("error_msg");
//								}else{
									errMsg = response.optString("error");
//								}
							}else{
								// 解析错误信息
								errMsg = PropertiesUtils.getErrMsgByErrnoNew(channel, response);
							}
								if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
								{
									p.put("whether_push_over", "2");
									p.put("online_flag", "1");
									// p.put("remark", response.optString("error"));
									// 修改批量推送失败后显示中文提示信息
									p.put("remark", errMsg);
									this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
								}
								else
								{
									p.put("whether_push_over", "2");
									// p.put("remark", response.optString("error"));
									// 修改批量推送失败后显示中文提示信息
									p.put("remark", errMsg);


									//this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);

									//changhui 2018-1-10 添加判断第三方菜品信息是否重复 begin
									String strId = getDisRepeatCount(tenantId, p);
									if("".equals(strId)){
										this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);
									}else{
										p.put("id", strId);
										this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
									}
									//end
								}
								
								logger.info("==========第三方菜品推送失败,异常信息:================"+response);
								//throw new Exception("Invoke third party open api fail!" + response);
								result.put("success", false);
								result.put("msg", "推送失败："+response.optString("error"));
						}
						else
						{
							success++;
							if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
							{
								p.put("whether_push_over", "1");
								//p.put("remark", response.optString("data"));
								// 推送菜品成功后,清除之前推送失败原因信息
								p.put("remark", "");
								this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
							}
							else
							{
								p.put("whether_push_over", "1");
								//p.put("remark", response.optString("data"));
								// 推送菜品成功后,清除之前推送失败原因信息
								p.put("remark", "");


								//this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);

								//changhui 2018-1-10 添加判断第三方菜品信息是否重复 begin
								String strId = getDisRepeatCount(tenantId, p);
								if("".equals(strId)){
									this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);
								}else{
									p.put("id", strId);
									this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
								}
								//end
							}
							result.put("success", true);
							result.put("msg", "成功:" + success + "\t 失败:" + fail);
						}
						// result.put("success", true);
						// result.put("msg", "成功:" + success + "\t 失败:" + fail);
					}
				}

			}
			//2017-10-09因为推送菜品信息添加日志信息，异常时，在第三方接口的方法调用中捕获了异常信息，导致推送菜品信息页面无错误提示因此取消事务属性
//			transactionManager.commit(status);
			
			if(params.size()>0 && params.get(0).optString("channel").equals(Constant.YICHI_CHANNEL))
			{
				response.put("success", result.optBoolean("success"));
				return response;
			}
			return result;
		}
		catch (Exception e)
		{
			e.printStackTrace();
//			transactionManager.rollback(status);
			response.put("errno", -1);
			response.put("msg", e);
			if(e.getMessage().contains("token结果没空")){
				errorObject.put("remark", "token结果为空,请先授权!");
			}else{
				errorObject.put("remark", e.getMessage());
			}
			errorObject.put("whether_push_over", "2");
			this.dao.updateIgnorCase(tenantId, "cc_third_item_info", errorObject);
			result = JSONObject.fromObject(response);
			logger.info("保存菜品信息错误：" + response);
		}
		return result;
	}

	//changhui 2018-1-10 判断第三方菜品信息是否已经存在，如果存在不允许做插入操作
	public String getDisRepeatCount(String strTenantId, JSONObject p){
		String result = "";
		try{
			String sql = "SELECT id from cc_third_item_info where item_code = '" + p.optInt("item_id", 0) + "' AND tenancy_id = '" + strTenantId + "' AND shop_id = " + p.optInt("shop_id", 0) + " AND channel = '" + p.optString("channel")+ "'";
			List<JSONObject> resultList = this.dao.query4Json(strTenantId, sql);
			if(resultList != null && resultList.size() > 0){
				result = resultList.get(0).optString("id");
			}
		}catch (Exception e) {

		}
		return result;
	}

	@Override
	public JSONObject deleteDish(JSONObject params) throws Exception
	{

		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		
		ccBusniessLogBean.setRequestId(params.optString("requestId"));
		ccBusniessLogBean.setTenancyId(tenantId);
		ccBusniessLogBean.setShopId(params.optString("store_id"));
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dish");
		ccBusniessLogBean.setChannel(params.optString("channel"));
		ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
		ccBusniessLogBean.setCmd("com.tzx.cc.thirdparty.bo.imp.AbstractThirdPartyManager:deleteDish");
		ccBusniessLogBean.setRequestBody(params.toString());
		
		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("1");
		
		//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		if(params.containsKey("dishes")){
		  ccBusniessLogBean.setThirdId(params.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
		  ccBusniessLogBean.setTzxId(params.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
	      ccBusniessLogBean.setTzxName(params.optJSONArray("dishes").getJSONObject(0).optString("item_name"));				
		}else{
		  ccBusniessLogBean.setThirdId(params.optString("third_item_id"));
		  ccBusniessLogBean.setTzxId(String.valueOf(params.optLong("item_id")));
	      ccBusniessLogBean.setTzxName(params.optString("item_name"));					
		}

		// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
		ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());
		
		TransactionStatus status = getTransctionStatus();
		String errMsg="";

		try
		{
			String sql = "delete from cc_third_item_info where shop_id='" + params.optInt("store_id") + "' and item_code='" + params.optInt("item_code") + "' and channel='" + channel + "'";

			dao.execute(tenantId, sql);

			delDish(params);
			if (response.optInt("errno") != SUCCESS)
				{
					throw new Exception("Invoke third party open api fail!" + response);
				}
			transactionManager.commit(status);
			
			ccBusniessLogBean.setResponseBody(response.toString());
		}
		catch (Exception e)
		{
			transactionManager.rollback(status);
			if(response.containsKey("errno") && response.optInt("errno")==0)
			{	
				if(channel.equals(Constant.XMDWM_CHANNEL)){
//					if(response.containsKey("errno") &&  response.optString("error").contains(":")){
//						String info = response.optString("error").substring(response.optString("error").indexOf("[")+1,response.optString("error").lastIndexOf("]"));
//							errMsg = JSONObject.fromObject(info).optString("error_msg");
//					}else{
						errMsg = response.optString("error");
//					}
				}else{
					errMsg = PropertiesUtils.getErrMsgByErrno(channel, response.optString("errno"), response.optString("error"));
				}
				if(CommonUtil.checkStringIsNotEmpty(errMsg)){
				String sql="update cc_third_item_info set remark='"+errMsg+"' where shop_id='"+params.optString("store_id")+"' and channel='"+channel+"' and item_code='"+params.optString("item_code")+"';";
				dao.execute(tenantId, sql);
				}
				response.put("errno", -1);
				response.put("msg", errMsg);
			}else{
				if(channel.equals(Constant.XMDWM_CHANNEL)){
					errMsg = response.optString("error");
					if(CommonUtil.checkStringIsNotEmpty(errMsg)){
						String sql="update cc_third_item_info set remark='"+errMsg+"' where shop_id='"+params.optString("store_id")+"' and channel='"+channel+"' and item_code='"+params.optString("item_code")+"';";
						dao.execute(tenantId, sql);
					}
					response.put("errno", -1);
					response.put("msg", errMsg);
				}
				
			}
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			logger.info("删除菜品信息错误：" + response);
			e.printStackTrace();
		}finally{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
//		CcBusinessLogUtils.log(ccBusniessLogBean);
		}
		return response;
	}

	public JSONObject deleteDishCategory(JSONObject params) throws Exception
	{
		TransactionStatus status = getTransctionStatus();

		try
		{
			delDishCategory(params);

			if (response.optInt("errno") != SUCCESS)
			{
				throw new Exception("Invoke third party open api fail!" + response);
			}
			transactionManager.commit(status);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			transactionManager.rollback(status);
			if(response.containsKey("errno") && response.optInt("errno")==0)
			{
				response.put("errno", -1);
				response.put("msg", e);
			}
			logger.info("删除菜品类别信息错误：" + response);
		}

		return response;
	}
	
	/**
	 * 删除菜品类别
	 * 
	 * @param params
	 */
	protected abstract void delDishCategory(JSONObject params) throws Exception;
	
	/**
	 * 删除菜品
	 * 
	 * @param params
	 */
	protected abstract void delDish(JSONObject params) throws Exception;

	/**
	 * 上传第三方菜品
	 * 
	 * @param params
	 * @throws Exception
	 */
	protected abstract void postDish(List<JSONObject> params) throws Exception;

	/**
	 * 上传第三方菜品单个
	 * 
	 * @param p
	 * @throws Exception
	 */
	protected abstract void postDishSingle(JSONObject p) throws Exception;

	/**
	 * 上传第三方菜品分类
	 * 
	 * @param params
	 * @throws Exception
	 */
	protected abstract void postDishCategory(JSONObject params) throws Exception;

	/**
	 * 上传第三方商户信息
	 * 
	 * @param params
	 * @return
	 * @throws Exception
	 */
	protected abstract void postShopInfo(JSONObject params) throws Exception;

	/**
	 * 设置第三方商户营业状态
	 * 
	 * @param params
	 * @return
	 * @throws Exception
	 */
	protected abstract void postShopStatus(JSONObject params) throws Exception;

	/**
	 * 上传第三方商户预计送达时间
	 * 
	 * @param params
	 */
	protected abstract void postDeliveryTime(JSONObject params) throws Exception;
	
	/**
	 * 修改菜品库存 单个
	 * 
	 * @param p
	 * @throws Exception
	 */
	protected abstract void updateDishStockSingle(JSONObject p) throws Exception;

	/**
	 * 上传第三方商户配送范围
	 * 
	 * @param params
	 */
	protected abstract void postDeliveryRegion(JSONObject params) throws Exception;

	private TransactionStatus getTransctionStatus()
	{
		DefaultTransactionDefinition def = new DefaultTransactionDefinition();
		def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
		TransactionStatus status = transactionManager.getTransaction(def);
		return status;
	}

	@Override
	public JSONObject updateDishState(JSONObject params)
	{

		TransactionStatus status = getTransctionStatus();

		try
		{
			postDishState(params);

			if (response.optInt("errno") != SUCCESS)
			{
				throw new Exception("Invoke third party open api fail!" + response);
			}
			transactionManager.commit(status);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			if(response.containsKey("errno") && response.optInt("errno")==0)
			{
				response.put("errno", -1);
				response.put("msg", e);
			}
			transactionManager.rollback(status);
		}

		return response;
	}
	
	protected void postDishState(JSONObject params) throws Exception { }
	
	@Override
	public JSONObject getRestaurantOwn(JSONObject params) throws Exception 
	{
		return response;
	}
	
	@Override
	public JSONObject bindRestaurantId(JSONObject params,String method) throws Exception 
	{
		return response;
	}

	@Override
	public JSONObject getOrderDetail(JSONObject params) throws Exception 
	{
		return response;
	}
	@Override
	public JSONObject getBindShopList (String source, String secret) throws Exception
	{
		return response;
	}

	protected void updateDishCategory(JSONObject params) throws Exception {
		
	}

	protected void updateDish(JSONObject params) throws Exception {
		
	}

	//获取平台食物分类列表
	@Override
	public JSONObject getFoodCategories(JSONObject params) throws Exception {
		
		return response;
	}

	//获取平台食物列表
	@Override
	public JSONObject getFoods(JSONObject params) throws Exception {
		
		return response;
	}
	
	@Override
	public List<JSONObject>  getLocalNoPageDishCategoryList(String tenancyID, JSONObject condition) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		StringBuilder update_sql = new StringBuilder();
		// 菜品类别名称不同修改推送状态
		update_sql.append("UPDATE  cc_third_item_class_info SET whether_push_over = '3'  WHERE id IN (SELECT a.id FROM cc_third_item_class_info A ");
		update_sql.append(" LEFT JOIN hq_item_class b ON A .item_class_id = b. ID LEFT join hq_item_class e on e.id=a.item_class_id");
		update_sql.append(" WHERE A .whether_push_over = '1' and b.chanel='" + condition.optString("channel") + "' AND A .last_send_class_name != b.itemclass_name ");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			update_sql.append(" and A.shop_id in (" + condition.optString("shop_id") + " ) ");
		}
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			update_sql.append(" and a.channel = 'BD06'  ");
		}
		else
		{
			update_sql.append(" and a.channel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id")))
		{
			update_sql.append(" and e.itemclass_name =  '" + condition.optString("class_id") + "' ");
		}
		update_sql.append(")");
		this.dao.execute(tenancyID, update_sql.toString());
		update_sql.delete(0, update_sql.length());
		// 菜品类别的显示顺序不同修改推送状态
		update_sql.append("UPDATE cc_third_item_class_info SET whether_push_over = '3'  WHERE id IN (SELECT a.id FROM cc_third_item_class_info A ");
		update_sql.append(" LEFT JOIN hq_item_menu_class b ON A .item_class_id = b.class LEFT JOIN hq_item_menu_details c on c.id=b.details_id LEFT JOIN hq_item_menu_organ d on d.item_menu_id=c.item_menu_id LEFT join hq_item_class e on e.id=a.item_class_id");
		update_sql.append(" WHERE A .whether_push_over = '1'  AND  a.rank != b.menu_class_rank");

		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			update_sql.append(" and A.shop_id in (" + condition.optString("shop_id") + " ) ");
		}
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			update_sql.append(" and a.channel = 'BD06'  ");
		}
		else
		{
			update_sql.append(" and a.channel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id")))
		{
			update_sql.append(" and e.itemclass_name =  '" + condition.optString("class_id") + "' ");
		}
		update_sql.append(")");
		this.dao.execute(tenancyID, update_sql.toString());
		update_sql.delete(0, update_sql.length());
		// 20160513修改
		sql.append("SELECT d.remark,l.third_shop_id,d.id,e.itemclass_code,d.third_class_id,A.class as class_id,e.itemclass_name as cur_class_name,d.last_send_class_name,CASE WHEN A .menu_class_rank  IS NULL THEN d.rank ELSE A .menu_class_rank END AS rank, CASE WHEN d.whether_push_over IS NULL THEN '0' ELSE d.whether_push_over END AS whether_push_over,f.id as store_id,f.org_full_name,a.chanel as channel");
		sql.append(" FROM hq_item_menu_class A");
		sql.append(" LEFT JOIN hq_item_menu_details b ON A .details_id = b. ID");
		sql.append(" LEFT JOIN hq_item_menu_organ  c on c.item_menu_id= b.item_menu_id");
		sql.append(" LEFT JOIN cc_third_item_class_info d on d.item_class_id=a.class and d.shop_id=c.store_id");
		sql.append(" LEFT JOIN hq_item_class e on e.id=a.class");
		sql.append(" LEFT JOIN cc_third_organ_info l on l.shop_id=c.store_id");
		sql.append(" LEFT JOIN organ f on f.id=c.store_id where 1=1 and l.third_shop_id is not null ");
		// 20170208修改 放开权限
		//sql.append(" and l.third_shop_id is not null ");
		if (condition.containsKey("shop_id") && !StringUtils.isEmpty(condition.optString("shop_id")))
		{
			sql.append(" and c.store_id in (" + condition.optString("shop_id") + " ) ");
		}else if(CommonUtil.checkStringIsNotEmpty(getAlreadyPushShopIDs(tenantId, channel))&&!"0".equals(getAlreadyPushShopIDs(tenantId, channel))){
			sql.append(" and c.store_id in (" + getAlreadyPushShopIDs(tenantId, channel) + " ) ");
		}
		
		//添加门店 权限      2016年8月12日13:40:05   xgy    begin
		if(condition.has("authority_organ")) {
			String authority_organ = condition.optString("authority_organ");
			if(StringUtils.isNotBlank(authority_organ)) {
				sql.append(" and c.store_id in (").append(authority_organ).append(") ");
			}
		}
		//添加门店 权限      2016年8月12日13:40:05   xgy    end
		
		if (!condition.containsKey("channel") || StringUtils.isEmpty(condition.optString("channel")))
		{
			sql.append(" and a.chanel = 'BD06'  ");
			
			sql.append(" and l.channel = 'BD06'  ");
		}
		else
		{
			sql.append(" and a.chanel = '" + condition.optString("channel") + "'  ");
			sql.append(" and l.channel = '" + condition.optString("channel") + "'  ");
		}
		if (condition.containsKey("whether_push_over") && !StringUtils.isEmpty(condition.optString("whether_push_over")))
		{
			if (!condition.optString("whether_push_over").equals("9999") && !condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and d.whether_push_over = '" + condition.optString("whether_push_over") + "'  ");
			}
			else if (condition.optString("whether_push_over").equals("0"))
			{
				sql.append(" and d.whether_push_over is null ");
			}
		}
		if (condition.containsKey("class_id") && !StringUtils.isEmpty(condition.optString("class_id"))&&!"0".equals(condition.optString("class_id")))
		{
			sql.append(" and e.itemclass_name =  '" + condition.optString("class_id") + "' ");
		}
		sql.append(" GROUP BY  f.id ,f.org_full_name,d.whether_push_over,d.id,A.class,e.itemclass_name,d.last_send_class_name,a.menu_class_rank,a.chanel,e.itemclass_code,l.third_shop_id");

		List<JSONObject> list = this.dao.query4Json(tenancyID, sql.toString());
		return list;

	}
	

	@Deprecated
	@Override
	public JSONObject getRestaurantMenu(JSONObject param) throws Exception{
		return response;
	}
	
	@Override
	public JSONObject getFoodCategoryFoods(JSONObject param) throws Exception{
		return response;
	}
	
	@Override
	public JSONObject saveOrUpdateDishInfo(List<JSONObject> params) throws Exception
	{
		JSONObject result = new JSONObject();
		TransactionStatus status = getTransctionStatus();
		try
		{
			for (JSONObject p : params)
			{

					Object id = null;
					if (CommonUtil.checkStringIsNotEmpty(p.optString("id")))
					{
						if (p.optString("whether_push_over").equals("0"))
						{
							p.put("whether_push_over", "0");
						}
						else if (p.optString("whether_push_over").equals("1"))
						{
							p.put("whether_push_over", "3");
						}
						id = this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
					}
					else
					{
						p.put("whether_push_over", "0");


						//id = this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);

						//changhui 2018-1-10 添加判断第三方菜品信息是否重复 begin
						String strId = getDisRepeatCount(tenantId, p);
						if("".equals(strId)){
							id = this.dao.insertIgnorCase(tenantId, "cc_third_item_info", p);
						}else{
							p.put("id", strId);
							id = this.dao.updateIgnorCase(tenantId, "cc_third_item_info", p);
						}
						//end
					}
					if (id != null)
					{
						result.put("success", true);
						result.put("msg", "保存成功");
					}
					else
					{
						result.put("success", false);
						result.put("msg", "保存失败");
					}

				}

			transactionManager.commit(status);
			return result;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			transactionManager.rollback(status);
			if(response.containsKey("errno") && response.optInt("errno")==0)
			{
				response.put("errno", -1);
				response.put("msg", e);
			}
			result = JSONObject.fromObject(response);
			logger.info("保存菜品信息错误：" + response);
		}
		return result;
	}
	
	public List<String> queryThirdOrderList(String tenantId,String tzxShopId,String date,int type,List<String> lists)throws Exception{
		return null;
	}
	
	@Override
	public JSONObject updateDishStock(JSONObject params) {
		Object id = null;
		try {
			response.clear();
			if(params.containsKey("stockList")  && params.getJSONArray("stockList").size() > 0){
				updateDishStockSingle(params);
				List<JSONObject> paramList = new ArrayList<JSONObject>();
				if (response.optInt("stockResult") != SUCCESS){
					List<JSONObject> jsonArray = params.getJSONArray("stockList");
					List<JSONObject> updateList = new ArrayList<JSONObject>();
					for(JSONObject jo : jsonArray){
						updateList.add(jo);
					}
					//饿了么
					if(response.containsKey("errorThirdIds")){
						List<Long> errList = new ArrayList<Long>();
						errList = response.getJSONArray("errorThirdIds");
						System.out.println("除去失败之前的list长度："+jsonArray.size());
						for(JSONObject json : jsonArray){
							for(Long l : errList){
								if(json.optLong("third_item_id") == l){
									updateList.remove(json);
								}
							}
						}
						
						
					}else if(response.containsKey("errorItemCodes")){ //美团、新美大
						List<String> errList = new ArrayList<String>();
						errList = response.getJSONArray("errorItemCodes");
						
						System.out.println("除去失败之前的list长度："+jsonArray.size());
						for(String error : errList){
							for(JSONObject json : jsonArray){
								if(json.optString("item_code").equals(error.substring(1, error.length()-1))){
									updateList.remove(json);
								}
							}
						}
						
					}
					
					for(JSONObject json : updateList){
						json.put("stock", params.get("stock"));
						paramList.add(json);
					}
					if(paramList.size() > 0){
						System.out.println("除去失败之后的list长度："+paramList.size());
						id = this.dao.updateBatchIgnorCase(tenantId, "cc_third_item_info", paramList);
						if(id == null){
							response.put("flag", false);
						}else{
							response.put("flag", true);
						}
						response.put("flag", false);
					}
					
				}else{
					List<JSONObject> jsonArray = params.getJSONArray("stockList");
					for(JSONObject json : jsonArray){
						json.put("stock", params.get("stock"));
						paramList.add(json);
					}
					id = this.dao.updateBatchIgnorCase(tenantId, "cc_third_item_info", paramList);
					if(id == null){
						response.put("flag", false);
					}else{
						response.put("flag", true);
					}
				}
			}else{
				updateDishStockSingle(params);
				if (response.optInt("stockResult") != SUCCESS){
					response.put("flag", false);
				}else{
					id = this.dao.updateIgnorCase(tenantId, "cc_third_item_info", params);
					if(id == null){
						response.put("flag", false);
					}else{
						response.put("flag", true);
					}
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("更新菜品库存信息错误：" + response);
		}
		return response;
	}
	
}
