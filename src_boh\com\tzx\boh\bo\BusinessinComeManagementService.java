package com.tzx.boh.bo;

import net.sf.json.JSONObject;

public interface BusinessinComeManagementService
{
	   String NAME = "com.tzx.boh.bo.imp.BusinessinComeManagementServiceImpl";
		
		public JSONObject loadBusinessinComeInformation(String tenancyID,JSONObject condition) throws Exception;

		public boolean checkUnique(String tenentId, JSONObject param)throws Exception;
		
		public String findInOutTypeName(String tenancyId, String type, Object param) throws Exception;
		
		public String findPayStyle(String tenancyId, String type, Object param) throws Exception;
}
