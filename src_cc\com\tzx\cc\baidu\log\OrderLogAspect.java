package com.tzx.cc.baidu.log;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.httpclient.HttpException;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.aspectj.lang.JoinPoint;

import com.tzx.cc.baidu.cont.SuweiConst;
import com.tzx.cc.baidu.util.SuweiPropertyUtil;
import com.tzx.cc.bo.OrderUpdateManagementService;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

/**
 * 接口调用拦截类
 * <AUTHOR>
 * 2016年11月4日15:51:44
 */

//先注释掉用时再开，速位暂时不用
//@Component
//@Aspect
public class OrderLogAspect {
    /**
     * LOG
     */
    public static final Log LOGGER = LogFactory.getLog(OrderLogAspect.class);
    
    //@Resource(name = "genericDaoImpl")//先注释掉用时再开，速位暂时不用
	private GenericDao	dao;
    
    //@Resource(name=OrderUpdateManagementService.NAME)//先注释掉用时再开，速位暂时不用
    private OrderUpdateManagementService orderUpdateManagementService;

 
//    @Pointcut("execution(* com.tzx.cc.baidu.service.SuweiService.thirdSuweiApplayOrderReq(..))")   //先注释掉用时再开，速位暂时不用
	public void aspect(){}
    
    /**
     * 拦截速位
     * @param point 
     * @throws Throwable 
     */
//    @After("aspect()")  //先注释掉用时再开，速位暂时不用
    public void afterAdvice(JoinPoint point) throws NumberFormatException, HttpException, IOException {
    	try {
			Object[] objs=point.getArgs();
			JSONObject param = (JSONObject) objs[0];
			JSONObject param2 = (JSONObject) objs[1];
			String requestInfo = SuweiPropertyUtil.getMsg(SuweiConst.THIRD_SUWEI_APPLAY_ORDER) + "?" + param2.toString();
			String responseInfo = ResultThreadlocalUtil.get();
			String orderCode = param.optString("order_code");
			com.alibaba.fastjson.JSONObject resultJson = com.alibaba.fastjson.JSONObject
					.parseObject(responseInfo);
			String thirdOrderCode = StringUtils.EMPTY;
			String requestResult = StringUtils.EMPTY;
			if(resultJson!=null && resultJson.containsKey("oid")) {
				thirdOrderCode = resultJson.getString("oid");
				requestResult = "成功";
			} else {
				suweiFailedRemedy(param,param2);
				requestResult = "失败";
			}
			
			JSONObject dataJson = new JSONObject();
			dataJson.put("order_code", orderCode);
			dataJson.put("third_order_code", thirdOrderCode);
			dataJson.put("request_info", requestInfo);
			dataJson.put("response_info", responseInfo);
			dataJson.put("request_result", requestResult);
			String currentTime = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss");
			dataJson.put("request_time", currentTime);
			DBContextHolder.setTenancyid(param.optString("tenant_id"));
			dao.insertIgnorCase(param.optString("tenant_id"), "cc_order_log", dataJson);
		} catch (Exception e) {
			LOGGER.info("记录速位日志出错",e);
			e.printStackTrace();
		}
    }

	/**
	 * 错误补救
	 * @param param
	 * @param param2
	 * @throws Exception
	 */
	private void suweiFailedRemedy(final JSONObject param, final JSONObject param2) throws Exception {
		String orderNo = param.optString("order_code");
		final StringBuffer sql = new StringBuffer();
		sql.append("select count(*),sum(case request_result when '成功' then 1 else 0 end) as success_num from cc_order_log ");
		sql.append(" where order_code = ?");
		sql.append(" group by order_code");
		DBContextHolder.setTenancyid(param.optString("tenant_id"));
		List<JSONObject> list = dao.query4Json(param.optString("tenant_id"), sql.toString(),new Object[]{orderNo});
		if(list.isEmpty()) {
				ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
				Runnable run = new FailedRemedy(executor,orderUpdateManagementService,param,param2);
			    executor.scheduleAtFixedRate(  
	    		run,  
	    		Long.valueOf(SuweiPropertyUtil.getMsg("push_time_lag")),  
	            Long.valueOf(SuweiPropertyUtil.getMsg("push_time_lag")),  
	            TimeUnit.SECONDS);  
		}
	}
}
