package com.tzx.boh.bo;

import java.util.List;

import net.sf.json.JSONObject;

public interface MeterreadingManagementService
{

	   String NAME = "com.tzx.boh.bo.imp.MeterreadingManagementServiceImpl";
		
		public String loadMeterreadingInformation(String tenancyID,JSONObject condition) throws Exception;
		
		public JSONObject loadAddMeterreadingInformation(String tenancyID,JSONObject condition) throws Exception;

		public boolean checkUnique(String tenentId, JSONObject param)throws Exception;
		
		public void delete(String tenantId, String tableKey, List<JSONObject> keyList) throws Exception;

}
