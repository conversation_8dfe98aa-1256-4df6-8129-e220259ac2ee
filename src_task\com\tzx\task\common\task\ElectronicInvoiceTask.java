package com.tzx.task.common.task;

import io.netty.util.internal.StringUtil;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.Map.Entry;

import javax.annotation.Resource;

import com.tzx.cc.invoice.electronic.service.impl.PiaotongElecInvoiceServiceImpl;
import com.tzx.cc.invoice.electronic.service.impl.YonyouElecInvoiceServiceImpl;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoiceUtils;
import com.tzx.cc.invoice.electronic.util.PiaoTongElectronicInvoiceWebUtils;
import com.tzx.cc.invoice.electronic.util.YonyouElectronicInvoiceWebUtils;
import com.tzx.framework.common.util.SpringConext;

import net.sf.json.JSONArray;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StopWatch;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.GsonBuilder;
import com.tzx.cc.invoice.electronic.cache.PiaoTongConfigCache;
import com.tzx.cc.invoice.electronic.cache.SysparamCache;
import com.tzx.cc.invoice.electronic.cont.ElectronicInvoiceConst;
import com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao;
import com.tzx.cc.invoice.electronic.rest.ElecInvoiceRest;
import com.tzx.cc.invoice.electronic.service.ElecInvoiceService;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoiceWebServiceUtils;
import com.tzx.cc.invoice.electronic.util.XmlUtils;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

/**.
 * 电子发票轮询查询任务
 * <AUTHOR>
 *
 */
public class ElectronicInvoiceTask implements Runnable {
    /**.
     * redisTemplete
     */
    @Resource(name = "saasRedisTemplate")
    private RedisTemplate < String, Object > redisTemplate;

    /**.
     * logger
     */
    private static final Logger	logger	= Logger.getLogger(ElecInvoiceRest.class);

	@Resource(name=ElecInvoiceService.NAME)
	private ElecInvoiceService elecInvoiceService;
	
	@Resource(name=ElecInvoiceDao.NAME)
	private ElecInvoiceDao elecInvoiceDao;
	
	@Resource(name = "genericDaoImpl")
	private GenericDao		dao;
	
	/**
	 * 真实状态信息sql
	 */
	private final String STATESQL = "statesql";
	
	/**
	 * 填充信息 的键
	 */
	private final String FILLDATA = "filldata";
	
	/**
	 * 填充明细信息的键
	 */
	private final String FILLDATADETAIL = "filldatadetail";
	
	@Override
	public void run() {
		logger.info("同步电子发票真实状态开始");
		StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        startGetRealData();
		stattSynYYData();
		startSynPTData();
        stopWatch.stop();
		logger.info("同步电子发票真实状态结束");
		logger.info("用时:"+stopWatch.prettyPrint());
	}

	private void stattSynYYData() {
		if(!redisTemplate.hasKey(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE)) {
			return;
		}
		//在redis缓存中获取未确认状态的数据
		BoundHashOperations<String, Object, Object> boundHashOps = redisTemplate.boundHashOps(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE);
		List<Object> values = boundHashOps.values();
		if(values==null || values.isEmpty()) {
			return;
		}
        YonyouElecInvoiceServiceImpl yyElecInvoiceService = SpringConext.getApplicationContext().getBean(YonyouElecInvoiceServiceImpl.class);
        Map<String, net.sf.json.JSONObject> saveMap = new HashMap<String, net.sf.json.JSONObject>();
        for(Object obj:values){
            String objstr = (String) obj;
            Object parse = com.alibaba.fastjson.JSONObject.parse(objstr);
            com.alibaba.fastjson.JSONObject param = (JSONObject) parse;
            try {
                //开始处理，封装真实状态信息和完整电子发票信息
                String FPQQLSH = param.getString("FPQQLSH");
                int organid = param.getInteger("ORGAN");
                String tenancyId = param.getString("tenancy_id");
                DBContextHolder.setTenancyid(tenancyId);
                net.sf.json.JSONObject returnJson = YonyouElectronicInvoiceWebUtils.queryInvoiceStatus(tenancyId, organid, FPQQLSH);
                if(StringUtils.equals(returnJson.optString("code"),"0000") && StringUtils.equals("4",returnJson.optString("statuscode"))) {
                    yyElecInvoiceService.orderCallback(returnJson.optString("data"),null);
                }
            } catch (Exception e) {
                logger.error(e);
            }
        }
	}

	/**
	 * 票通电子发票真实状态同步
	 * <AUTHOR>
	 */
	private void startSynPTData() {
		logger.info("开始执行票通电子发票状态轮询......");
		if(!redisTemplate.hasKey(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE)) {
			return;
		}
		//在redis缓存中获取未确认状态的数据
		BoundHashOperations<String, Object, Object> boundHashOps = redisTemplate.boundHashOps(ElectronicInvoiceConst.PT_ELECTRON_ICINVOICE_REDIS_CODE);
		List<Object> values = boundHashOps.values();
		if(values==null || values.isEmpty()) {
			return;
		}
        PiaotongElecInvoiceServiceImpl ptElecInvoiceService = SpringConext.getApplicationContext().getBean(PiaotongElecInvoiceServiceImpl.class);
        Map<String, net.sf.json.JSONObject> saveMap = new HashMap<String, net.sf.json.JSONObject>();
        for(Object obj:values){
            String objstr = (String) obj;
            Object parse = com.alibaba.fastjson.JSONObject.parse(objstr);
            com.alibaba.fastjson.JSONObject param = (JSONObject) parse;
            try {
                //开始处理
                String FPQQLSH = param.getString("FPQQLSH");				//saas记录的发票流水号
                String XSF_NSRSBH = param.getString("XSF_NSRSBH");			//纳税人识别号
                String tenancyId = param.getString("tenancy_id");			//商户号
                int organid = param.getInteger("ORGAN");
                DBContextHolder.setTenancyid(tenancyId);
                logger.info("从redis获取发票流水号为["+FPQQLSH+"]的记录处理");
                net.sf.json.JSONObject fpInfo = this.elecInvoiceDao.queryInfoByFlowNumber(tenancyId,FPQQLSH);
        		if(fpInfo!=null){
        			logger.info("发票流水号["+FPQQLSH+"]对应的订单编号为["+fpInfo.optString("order_code")+"]");
        			if(StringUtils.equals(fpInfo.optString("invoice_state"),ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT)){	//待开票状态  执行轮询票通接口查询发票最终开票状态
        				logger.info("总部发票状态==》待开票");
        				JSONObject reqParam = new JSONObject();
                        reqParam.put("taxpayerNum",XSF_NSRSBH);						//销售方纳税人识别号
                        reqParam.put("tradeNo",FPQQLSH);							//交易单号(发票请求流水号)
                        reqParam.put("invoiceAmount", fpInfo.optDouble("total_tax_amount",0.00));//发票金额(含税)
                        //调用查询二维码开票信息接口
                		net.sf.json.JSONObject respResult = PiaoTongElectronicInvoiceWebUtils.queryInvoiceQrStatus(tenancyId,organid,reqParam.toString());
                		if(StringUtils.equals(respResult.optString("code"),"0000")) {
                			String content=respResult.optString("content");
                	        com.alibaba.fastjson.JSONObject alijson = com.alibaba.fastjson.JSONObject.parseObject(content);
                	        net.sf.json.JSONObject returnJson = ElectronicInvoiceUtils.convertAli2netJson(alijson);
                	        JSONArray itemList = returnJson.getJSONArray("itemList");
                	        net.sf.json.JSONObject returnObj=itemList.getJSONObject(0);
                	        String invoiceReqSerialNo=returnObj.optString("invoiceReqSerialNo");		//票通蓝票的发票请求流水号
                	        if(!StringUtil.isNullOrEmpty(invoiceReqSerialNo)){			//二维码已开票
                	        	logger.info("二维码已开票");
                	        	fpInfo.put("pt_invoicereqserialno", invoiceReqSerialNo);
                	            dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info",fpInfo);	//更新invoice_flow_number关联pt_invoicereqserialno
                	            //根据票通返回的发票请求流水号调用查询发票接口判断开票状态
                	            JSONObject reqJson = new JSONObject();
                	            reqJson.put("taxpayerNum",XSF_NSRSBH);					//纳税人识别号
                	            reqJson.put("invoiceReqSerialNo",invoiceReqSerialNo);					
                	            GsonBuilder builder = new GsonBuilder();
                	            String reqStr = builder.create().toJson(reqJson);
                	            //调用查询发票状态信息接口
                	            net.sf.json.JSONObject reObj = PiaoTongElectronicInvoiceWebUtils.queryInvoiceStatus(tenancyId,organid,reqStr);
                	            if(reObj!=null) {
                	            	String code = reObj.optString("code");
                	            	 if(StringUtils.equals("0000",code)) {
                	            		 net.sf.json.JSONObject returnContent=net.sf.json.JSONObject.fromObject(reObj.optString("content"));
                	                     if(StringUtils.equals(returnContent.optString("code"),"0000")) {	//开票成功
                	                    	 logger.info("发票状态==》开票成功");
                	                    	 net.sf.json.JSONObject result=new net.sf.json.JSONObject();
                	                    	 result.put("tenancy_id", tenancyId);
                	                    	 ptElecInvoiceService.orderCallback(reObj.optString("content"),result);	//开票成功更新状态
                	                     }else if(StringUtils.equals(returnContent.optString("code"),"9999")){//开票失败，不做处理直接取消成功
                	                    	 logger.info("发票状态==》开票失败");
                	                    	 fpInfo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_FAIL);	
                	                         dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info",fpInfo);
                	                    }
                	                }
                	           }
                	       }
                	   }
        			}else if(StringUtils.equals(fpInfo.optString("invoice_state"),ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_PROCESS)){	//取消中的发票，执行轮询票通接口查询发票最终开票状态
        				//取消中的发票记录，根据票通的发票请求流水号重新查询发票状态，如果开票成功，则执行红冲
        				logger.info("总部发票状态==》取消中");
        				String invoiceReqSerialNo=fpInfo.optString("pt_invoicereqserialno");		//票通蓝票的发票请求流水号
        				JSONObject reqJson = new JSONObject();
        	            reqJson.put("taxpayerNum",XSF_NSRSBH);					//纳税人识别号
        	            reqJson.put("invoiceReqSerialNo",invoiceReqSerialNo);					
        	            GsonBuilder builder = new GsonBuilder();
        	            String reqStr = builder.create().toJson(reqJson);
        	            //调用查询发票状态信息接口
        	            net.sf.json.JSONObject reObj = PiaoTongElectronicInvoiceWebUtils.queryInvoiceStatus(tenancyId,organid,reqStr);
        	            if(reObj!=null) {
        	            	String code = reObj.optString("code");
        	            	 if(StringUtils.equals("0000",code)) {
        	            		 net.sf.json.JSONObject returnContent=net.sf.json.JSONObject.fromObject(reObj.optString("content"));
        	                     if(StringUtils.equals(returnContent.optString("code"),"0000")) {	//开票成功 ,执行红冲
        	                    	logger.info("发票状态==》开票成功");
        	                    	JSONObject cancelParam = new JSONObject();
        	                 		cancelParam.put("taxpayerNum", returnContent.optString("taxpayerNum"));			//纳税人识别号
        	                 		String hc_invoiceReqSerialNo=getHCinvoiceReqSerialNo();							//生成红冲发票请求流水号（4位平台简称pt**+16位随机数）
        	                 		logger.info("生成红冲发票请求流水号==》"+hc_invoiceReqSerialNo);
        	                 		cancelParam.put("invoiceReqSerialNo",hc_invoiceReqSerialNo);					//红冲发票请求流水号
        	                        cancelParam.put("invoiceCode", returnContent.optString("invoiceCode"));			//发票代码
        	                        cancelParam.put("invoiceNo", returnContent.optString("invoiceNo"));				//发票号码
        	                        cancelParam.put("redReason", "取消开票");
        	                        BigDecimal noTaxAmount =new BigDecimal(returnContent.optString("noTaxAmount")); //合计金额
        	                        BigDecimal tax_amount =new BigDecimal(returnContent.optString("taxAmount"));	//合计税额
        	                        BigDecimal total_tax=noTaxAmount.add(tax_amount);								//价税合计金额
        	                        double total_tax_amount =total_tax.multiply(new BigDecimal("-1")).doubleValue(); 
        	                        cancelParam.put("amount",total_tax_amount);										//价税合计金额(原发票的价税合计金额,的负数值)
        	                         
        	                		String reqstr = builder.create().toJson(cancelParam);
        	                        //调用票通发票红冲请求
        	                        net.sf.json.JSONObject redJo = PiaoTongElectronicInvoiceWebUtils.red(tenancyId,organid,reqstr);
        	                		String hccode = redJo.optString("code");
        	                		if(StringUtils.equals(hccode, "0000")) {
        	                			logger.info("执行发票红冲成功，准备更新总部发票记录状态......");
        	                			//红冲完成更新状态
        	                			fpInfo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS);	//取消冲正
        	                			fpInfo.put("invoice_cancel_time",ElectronicInvoiceUtils.currentTime2Str());
        	                	        this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", fpInfo);
        	                			
        	                	        net.sf.json.JSONObject elecInfoDetail = this.elecInvoiceDao.queryDetailsByFlowNumber(tenancyId,fpInfo.optInt("id"));
        	                	        net.sf.json.JSONObject hcelecInfo = this.elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fpInfo.optString("invoice_flow_number"));
        	                	        hcelecInfo.put("pt_invoicereqserialno", redJo.optString("invoiceReqSerialNo"));	//红冲成功，替换蓝票发票请求流水号为红票发票请求流水号
        	                			if(hcelecInfo==null) {
        	                				hcelecInfo = fpInfo;
        	                				hcelecInfo.remove("id");
        	                			}
        	                			//增加红冲记录
        	                			hcelecInfo.put("original_invoice_number", fpInfo.optString("invoice_number"));
        	                			hcelecInfo.put("original_invoice_code", fpInfo.optString("invoice_code"));
        	                			hcelecInfo.remove("invoice_cancel_time");
        	                			hcelecInfo.remove("invoice_time");

        	                			hcelecInfo.put("total_tax_amount", "-"+fpInfo.optString("total_tax_amount"));
        	                			hcelecInfo.put("tax_amount", "-"+fpInfo.optString("tax_amount"));
        	                			hcelecInfo.put("total_amount", "-"+fpInfo.optString("total_amount"));
        	                			hcelecInfo.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_HP);
        	                			hcelecInfo.put("invoice_flow_number",fpInfo.optString("invoice_flow_number"));
        	                			
        	                			if(StringUtils.isBlank(hcelecInfo.optString("id"))) {
        	                				int insertIgnorCase = (int) this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_info", hcelecInfo);
        	                				net.sf.json.JSONObject details = new net.sf.json.JSONObject();
        	                				details.put("electronic_id", insertIgnorCase);
        	                				details.put("invoice_type",  ElectronicInvoiceConst.ELECTRIC_KPLX_HP);
        	                				details.put("name",  elecInfoDetail.optString("name"));
        	                				details.put("tax_rate",  fpInfo.optString("tax_rate"));
        	                				this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_details", details);
        	                			} else {
        	                				this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", hcelecInfo);
        	                			}
        	                			logger.info("更新总部发票记录状态完成......");
        	                		}
        	                     }else if(StringUtils.equals(returnContent.optString("code"),"9999")){//开票失败，不做处理直接取消成功
        	                    	 logger.info("发票状态==》开票失败");
        	                    	 fpInfo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS);	
        	                         dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info",fpInfo);
        	                         logger.info("更新总部发票记录状态完成......");
        	                    }
        	                }
        	           }
        			}
                }
            } catch (Exception e) {
                logger.error(e);
            }
        }
	}

	
	
	/**
	 * 开始获取真实数据
	 */
	private void startGetRealData() {
		if(!redisTemplate.hasKey(ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE)) {
			return;
		}
		//在redis缓存中获取未确认状态的数据
		BoundHashOperations<String, Object, Object> boundHashOps = redisTemplate.boundHashOps(ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE);  
		List<Object> values = boundHashOps.values();
		if(values==null || values.isEmpty()) {
			return;
		}
		//这个map存放 订单电子发票的真实订单信息  和 订单的完整信息
		Map<String, Map<String, List<Object>>> stateMap = new HashMap<String, Map<String,List<Object>>>();
		//遍历每一个不确定订单的信息，矫正电子发票状态，补全信息
		for(Object obj:values){
			String objstr = (String) obj;
			Object parse = com.alibaba.fastjson.JSONObject.parse(objstr);
			com.alibaba.fastjson.JSONObject param = (JSONObject) parse;
			try {
			    //开始处理，封装真实状态信息和完整电子发票信息
				process(param,stateMap);
			} catch (DocumentException e) {
				logger.error(e);
			} catch (Exception e) {
				logger.error(e);
			}
		}
		
		//为每个商户更新数据
		for(Entry<String, Map<String, List<Object>>> entry:stateMap.entrySet()){
		    String key = entry.getKey();
		    Map<String, List<Object>> map2 = entry.getValue();
		    List<Object> list = map2.get(STATESQL);
		    String []array = null;
		    if(list!=null && !list.isEmpty()) {
		    	array = new String[list.size()];
		    }
			for(int i=0;list!=null && i<list.size();i++){
			    array[i] = (String) list.get(i);
			}
			
			List<Object> listInfo = map2.get(FILLDATA);
			List<net.sf.json.JSONObject> listInfoJ = new ArrayList<net.sf.json.JSONObject>();
			
			if(listInfo!=null) {
				for(Object obj:listInfo){
					net.sf.json.JSONObject json = (net.sf.json.JSONObject) obj;
					listInfoJ.add(json);
				}
			}
			
			List<Object> listDetail = map2.get(FILLDATADETAIL);
			String []arrayDetail = null;
			if(listDetail!=null && !listDetail.isEmpty()) {
				arrayDetail = new String[listDetail.size()];
			}
			for(int i=0;arrayDetail!=null && i<listDetail.size();i++){
				Object obj = listDetail.get(i);
				arrayDetail[i] = (String) obj;
			}
			
			try {
			    //设置当前的数据源
			    DBContextHolder.setTenancyid(key);
			    if(array!=null && array.length>0) {
			    	dao.getJdbcTemplate(key).batchUpdate(array);
			    }
			    if(listInfoJ!=null && !listInfoJ.isEmpty()) {
			    	dao.updateBatchIgnorCase(key, "hq_electronic_invoice_info", listInfoJ);
			    }
				uploadOmData(key,listInfoJ);
			    if(arrayDetail!=null && arrayDetail.length>0) {
			    	dao.getJdbcTemplate(key).batchUpdate(arrayDetail);
			    }
			    setInvalid4Success(key,listInfo);
				//将过期的状态置为无效
	            setInvalidStatus(key);
			} catch (Exception e) {
				logger.error(e);
			}
		}
	}

	/**
	 * 上传数据到OM
	 * @param key
	 * @param listInfoJ
     */
	private void uploadOmData(String tenancyId, List<net.sf.json.JSONObject> listInfoJ) throws Exception {
		if(listInfoJ==null || listInfoJ.isEmpty()) {
			logger.info("没有要给OM上传的数据");
			return;
		}
		StringBuffer ids = new StringBuffer();
		for (net.sf.json.JSONObject json : listInfoJ) {
			if(json.containsKey("id")) {
				ids.append(json.getString("id")).append(",");
			}
		}
		String idstr = ids.toString().substring(0,ids.length()-1);
		StringBuffer sql = new StringBuffer("select * from hq_electronic_invoice_info where id in (");
		sql.append(idstr);
		sql.append(")");
		List<net.sf.json.JSONObject> list = dao.query4Json(tenancyId, sql.toString());
		for(net.sf.json.JSONObject json:list){
			//只有是开票成功才会上传OM，冲正不上传（已实时上传）
			if(StringUtils.equals(json.optString("invoice_state"),
				ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS)) {
				ElectronicInvoiceUtils.uploadOm(json);
			}
		}
	}

	/**
	 * 将开具发票成功的从redis中移除
	 * 
	 * @param key
	 * @param listInfo
	 * @throws Exception 
	 */
	private void setInvalid4Success(String tenancyId, List<Object> listInfo) throws Exception {
		StringBuffer ids = new StringBuffer();
		for (int i = 0; listInfo != null && i < listInfo.size(); i++) {
			Object object = listInfo.get(i);
			net.sf.json.JSONObject json = (net.sf.json.JSONObject) object;
			String id = json.optString("id");
			if (StringUtils.isNotBlank(id)) {
				ids.append(id).append(",");
			}
		}
		String idstr = ids.toString();
		if(StringUtils.isBlank(idstr)) {
			return;
		}
		idstr = idstr.substring(0, idstr.length()-1);
		StringBuffer sql = new StringBuffer();
		sql.append("select invoice_flow_number from hq_electronic_invoice_info where invoice_state in ('3','6','8') and id in (");
		sql.append(idstr).append(")");
		List<net.sf.json.JSONObject> query4Json = this.dao.query4Json(tenancyId, sql.toString());
		if(query4Json==null || query4Json.isEmpty()) {
			return;
		}
		Object[] flowArr = new Object[query4Json.size()];
		for(int i=0;i<query4Json.size();i++){
			net.sf.json.JSONObject json = query4Json.get(i);
			Object flowNumber = json.optString("invoice_flow_number");
			flowArr[i] = flowNumber;
		}
		redisTemplate.opsForHash().delete(ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE, flowArr);
	}

	/**
     * 将过期的数据的状态置为无效
     * @param tenancyId
     * @throws Exception 
     */
    private void setInvalidStatus(String tenancyId) throws Exception {
        Calendar cal = Calendar.getInstance();
        String dzfp_ewmdyxq = null;
        try {
            dzfp_ewmdyxq = SysparamCache.getSysparam(tenancyId,
                    "dzfp_ewmdyxq");
        } catch (Exception e1) {
            logger.error(e1);
        }
        if (StringUtils.isBlank(dzfp_ewmdyxq)) {
            dzfp_ewmdyxq = "7";
        }
        dzfp_ewmdyxq = "-"+dzfp_ewmdyxq;
        cal.add(Calendar.DAY_OF_MONTH, Integer.parseInt(dzfp_ewmdyxq));
        String datestr = DateFormatUtils.format(cal, "yyyy-MM-dd");
        StringBuffer sql = new StringBuffer();
        sql.append("update hq_electronic_invoice_info set invoice_state = '0' where order_date < '");
        sql.append(datestr);
        sql.append("' and invoice_state='1'");
        dao.execute(tenancyId, sql.toString());
    }

	/**
	 * 处理数据
	 * @param param
	 * @param map 
	 * @throws Exception 
	 */
	private void process(com.alibaba.fastjson.JSONObject param, Map<String, Map<String, List<Object>>> map)
			throws Exception {
		String XSF_NSRSBH = param.getString("XSF_NSRSBH");
		String FPQQLSH = param.getString("FPQQLSH");
		//发起webservice请求第三方的查询接口，查询此订单号所对应流水号真正状态及完整信息
		String xml = ElectronicInvoiceWebServiceUtils.sendWebService(ElectronicInvoiceWebServiceUtils.FPCX(XSF_NSRSBH, FPQQLSH));
		String xmlData = XmlUtils.getReturnData(xml);
		Element rootdata = XmlUtils.parse(xmlData);
		Element returncode = XmlUtils.getElement(rootdata, "RESPONSE_COMMON_FPCX","RETURNCODE");
		if(!StringUtils.equals(returncode.getTextTrim(), "0000")) {
			return;
		}
		//封装真实状态信息sql的update语句
		stateSql2Map(param, map);
		fillData2Map(param,rootdata,map);
		fillDataDetail2Map(param,rootdata,map);
	}

    /**
     * 填充电子信息明细
     * @param param
     * @param rootdata
     * @param map
     */
    private void fillDataDetail2Map(JSONObject param, Element rootdata,
			Map<String, Map<String, List<Object>>> map) {
    	String ID = param.getString("ID");
    	if(StringUtils.isBlank(ID)) {
    		return;
    	}
    	Element eleInfoDetailElement = XmlUtils.getElement(rootdata, "RESPONSE_COMMON_FPCX","COMMON_FPCX_XMXXS","COMMON_FPCX_XMXX");
    	String invoice_type = eleInfoDetailElement.element("FPHXZ")==null?StringUtils.EMPTY:eleInfoDetailElement.element("FPHXZ").getTextTrim();
    	String model = eleInfoDetailElement.element("GGXH")==null?StringUtils.EMPTY:eleInfoDetailElement.element("GGXH").getTextTrim();
    	String unit = eleInfoDetailElement.element("DW")==null?StringUtils.EMPTY:eleInfoDetailElement.element("DW").getTextTrim();
    	String number = eleInfoDetailElement.element("XMSL")==null?null:eleInfoDetailElement.element("XMSL").getTextTrim();
    	String price = eleInfoDetailElement.element("XMDJ")==null?null:eleInfoDetailElement.element("XMDJ").getTextTrim();
    	String amount = eleInfoDetailElement.element("XMJE")==null?null:eleInfoDetailElement.element("XMJE").getTextTrim();
    	String tax_rate = eleInfoDetailElement.element("SL")==null?null:eleInfoDetailElement.element("SL").getTextTrim();
    	String tax_amount = eleInfoDetailElement.element("SE")==null?null:eleInfoDetailElement.element("SE").getTextTrim();
    	String sn_no = eleInfoDetailElement.element("SN")==null?StringUtils.EMPTY:eleInfoDetailElement.element("SN").getTextTrim();
    	if(StringUtils.isBlank(number)) {
			number = "null";
		}
    	if(StringUtils.isBlank(price)) {
			price = "null";
		}
    	if(StringUtils.isBlank(amount)) {
			amount = "null";
		}
    	if(StringUtils.isBlank(tax_rate)) {
			tax_rate = "null";
		}
    	if(StringUtils.isBlank(tax_amount)) {
			tax_amount = "null";
		}
		StringBuffer sql = new StringBuffer();
    	sql.append("update hq_electronic_invoice_details set invoice_type='").append(invoice_type);
    	sql.append("',model='").append(model);
    	sql.append("',unit='").append(unit);
    	sql.append("',number=").append(number);
    	sql.append(",price=").append(price);
    	sql.append(",amount=").append(amount);
    	sql.append(",tax_rate=").append(tax_rate);
    	sql.append(",tax_amount=").append(tax_amount);
    	sql.append(",sn_no='").append(sn_no);
    	sql.append("' where electronic_id=").append(ID);
    	String tenancyId = param.getString("tenancy_id");
    	setMapValue(map, tenancyId, FILLDATADETAIL, sql.toString());
	}

	/**
     * 封装填充map
     * @param param
     * @param rootdata
     * @param map
	 * @throws ParseException 
     */
    private void fillData2Map(JSONObject param, Element rootdata,
            Map<String, Map<String, List<Object>>> map) throws ParseException {
    	 String ID = param.getString("ID");
    	 if(StringUtils.isBlank(ID)) {
    		 return;
    	 }
    	 Element eleInfoElement = XmlUtils.getElement(rootdata, "RESPONSE_COMMON_FPCX","COMMON_FPCX_FPT");
    	 String invoice_type = eleInfoElement.element("KPLX")==null?StringUtils.EMPTY:eleInfoElement.element("KPLX").getTextTrim();
    	 String seller_name = eleInfoElement.element("XSF_MC")==null?StringUtils.EMPTY:eleInfoElement.element("XSF_MC").getTextTrim();
    	 String seller_address = eleInfoElement.element("XSF_DZDH")==null?StringUtils.EMPTY:eleInfoElement.element("XSF_DZDH").getTextTrim();
    	 String buyer_name = eleInfoElement.element("GMF_MC")==null?StringUtils.EMPTY:eleInfoElement.element("GMF_MC").getTextTrim();
    	 String drawer = eleInfoElement.element("KPR")==null?StringUtils.EMPTY:eleInfoElement.element("KPR").getTextTrim();
    	 String original_invoice_code = eleInfoElement.element("YFP_DM")==null?StringUtils.EMPTY:eleInfoElement.element("YFP_DM").getTextTrim();
    	 String original_invoice_number = eleInfoElement.element("YFP_HM")==null?StringUtils.EMPTY:eleInfoElement.element("YFP_HM").getTextTrim();
    	 String total_tax_amount = eleInfoElement.element("JSHJ")==null?StringUtils.EMPTY:eleInfoElement.element("JSHJ").getTextTrim();
    	 String total_amount = eleInfoElement.element("HJJE")==null?StringUtils.EMPTY:eleInfoElement.element("HJJE").getTextTrim();
    	 String tax_amount = eleInfoElement.element("HJSE")==null?StringUtils.EMPTY:eleInfoElement.element("HJSE").getTextTrim();
    	 String control_device_code = eleInfoElement.element("JQBH")==null?StringUtils.EMPTY:eleInfoElement.element("JQBH").getTextTrim();
    	 String invoice_code = eleInfoElement.element("FP_DM")==null?StringUtils.EMPTY:eleInfoElement.element("FP_DM").getTextTrim();
    	 String invoice_number = eleInfoElement.element("FP_HM")==null?StringUtils.EMPTY:eleInfoElement.element("FP_HM").getTextTrim();
    	 String invoice_time = eleInfoElement.element("KPRQ")==null?StringUtils.EMPTY:eleInfoElement.element("KPRQ").getTextTrim();
    	 net.sf.json.JSONObject json = new net.sf.json.JSONObject();
    	 json.put("invoice_type", invoice_type);
    	 json.put("seller_name", seller_name);
    	 json.put("seller_address", seller_address);
    	 json.put("buyer_name", buyer_name);
    	 json.put("drawer", drawer);
    	 json.put("original_invoice_code", original_invoice_code);
    	 json.put("original_invoice_number", original_invoice_number);
    	 json.put("total_tax_amount", total_tax_amount);
    	 json.put("total_amount", total_amount);
    	 json.put("tax_amount", tax_amount);
    	 json.put("control_device_code", control_device_code);
    	 json.put("invoice_code", invoice_code);
    	 json.put("invoice_number", invoice_number);
    	 if(StringUtils.isNotBlank(invoice_time)) {
    		 Date parseDate = DateUtils.parseDate(invoice_time, new String[]{"yyyyMMddHHmmss"});
    		 invoice_time = DateFormatUtils.format(parseDate, "yyyy-MM-dd HH:mm:ss");
    	 }
    	 json.put("invoice_time", invoice_time);
    	 json.put("id", ID);
    	 String tenancyId = param.getString("tenancy_id");
    	 setMapValue(map, tenancyId, FILLDATA, json);
    }

    /**
     * 把矫正的sql存放在map中
     * @param param
     * @param map
     */
    private void stateSql2Map(com.alibaba.fastjson.JSONObject param,
            Map<String, Map<String, List<Object>>> map) {
        String FPQQLSH = param.getString("FPQQLSH");
        StringBuffer sql = new StringBuffer();
		sql.append("update hq_electronic_invoice_info set invoice_state = '3'");
		sql.append(" where invoice_flow_number = '").append(FPQQLSH);
		sql.append("' and invoice_state='1'");
		logger.info(sql.toString());
		String tenancyId = param.getString("tenancy_id");
		setMapValue(map, tenancyId, STATESQL, sql.toString());
    }
	
	/**
	 * 为Map设置值
	 * @param map
	 * @param tenancyId
	 * @param key
	 * @param value
	 */
    private void setMapValue(Map<String, Map<String, List<Object>>> map,String tenancyId,String key,Object value){
	    if(map.containsKey(tenancyId)) {
	        Map<String, List<Object>> map2 = map.get(tenancyId);
	        if(map2.containsKey(key)) {
	            List<Object> list = map2.get(key);
	            list.add(value);
	        } else {
	            List<Object> list = new ArrayList<Object>();
	            map2.put(key, list);
	            list.add(value);
	        }
	    } else {
	        Map<String, List<Object>> map2 = new HashMap<String, List<Object>>();
	        map.put(tenancyId, map2);
	        List<Object> list = new ArrayList<Object>();
	        map2.put(key, list);
	        list.add(value);
	    }
	}
    
    //票通生成红冲发票请求流水号（4位平台简称pt**+16位随机数）
  	private String getHCinvoiceReqSerialNo(){
  		StringBuilder sb=new StringBuilder();
  		String prefix =PiaoTongConfigCache.getElementText("prefix");						//平台前缀(简称)
          Random rand=new Random();
          for(int i=0;i<16;i++)
          {
              sb.append(rand.nextInt(10));
          }
          return prefix+sb.toString();
  	}
}
