package com.tzx.framework.bo;

import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

public interface SystemUserService
{
	String NAME = "com.tzx.framework.bo.imp.SystemUserServiceImp";
	
	public JSONObject getSystemUser(String tenancyID, JSONObject condition, String conditions);
	
	/**
	 * 用户登录验证
	 * @param tenentid
	 * @param longinJsonObject
	 * @return
	 */
	public JSONObject chekUserLogin(String tenentid, JSONObject longinJsonObject) throws Exception;
	/**
	 * 根据用户信息加载权限信息
	 * @param tenentid
	 * @param userid 用户id
	 * @param storeid 机构id
	 * @return
	 */
	public Object getRoleAuthoruty(String tenentid, String sysuser, JSONObject p) throws Exception;

	public List<JSONObject> loadFirstLevel(String attribute, String sysuser, JSONObject p) throws Exception;

	public List<JSONObject> loadChild(String attribute, String sysuser, JSONObject p)throws Exception;
	
	/**
	 * 
	 * @param sysuser 用于判断是管理员还是普通用户  1管理员 0普通用户
	 * @param tenentid 
	 * @param userInfoJosonObje 用来获取普通用户id
	 * @return organCodes字符串
	 */
	public String findOrganCodes(String tenentid, String sysuser, JSONObject userInfoJosonObje)throws Exception;

	/**
	 * 查询用户所有store_id包括特殊权限及非特殊权限
	 * @param tenancy_id
	 * @param eid
	 * @return
	 */
	public List<JSONObject> findAllStoreIds(String tenancy_id, String eid)throws Exception;
	
	/**
	 * 查询用户权限 其中包括已经停用的门店
	 * @param tenentid
	 * @param sysuser
	 * @param userInfoJosonObje
	 * @return
	 * @throws Exception
	 */
	public String findOrganCodesWithInvalid(String tenentid, String sysuser,
											JSONObject userInfoJosonObje) throws Exception;

	public Object getRoleAuthorutyModule(String tenentid, String sysuser, JSONObject p)throws Exception;
	
	/**
	 * 查询报表按钮权限
	 * @param attribute
	 * @param sysuser
	 * @param p
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> loadReportButton(String attribute, String sysuser, JSONObject p) throws Exception;
	
	/**
	 * 查询报表按钮权限（新版系统）
	 * @param attribute
	 * @param sysuser
	 * @param p
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> loadReportButtonNew(String attribute, String sysuser, JSONObject p) throws Exception;

	public int checkIsMultiBrand(String tenancyId)throws Exception;
	/**
	 * <p>
	 * 		功能描述：获取登录用户模块信息
	 * </p>
	 * @param p
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryUserAuthModulesInfo(JSONObject p) throws Exception;

	/**
	 * 获取登录用户信息
	 * @param tenantId
	 * @param longinJsonObject
     * @return
     */
	JSONObject getEmpInfo(String tenantId, JSONObject longinJsonObject) throws Exception;


	JSONObject findDefaultBrand(String tenantId, String userName) throws Exception;


	List<JSONObject> findUserAuthModul(JSONObject param) throws Exception ;
	
	/**
	 * 功能描述：获取用户一级模块信息
	 * @param p
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryUserModule(JSONObject p) throws Exception;

}
