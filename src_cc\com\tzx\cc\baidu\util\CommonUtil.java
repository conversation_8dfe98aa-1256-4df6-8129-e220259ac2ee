package com.tzx.cc.baidu.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLConnection;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.UUID;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import com.tzx.cc.baidu.entity.Cmd;
import com.tzx.cc.baidu.entity.CmdType;
import com.tzx.framework.common.util.HttpUtil;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;

/**
 * 百度外卖通用工具类
 * 
 * <AUTHOR>
 * 
 */

public class CommonUtil
{

	/**
	 * 会引起意外错误的特殊字符(正则表达式)
	 */
	public static final String EVIL_CHARACTER="[']";
	/**
	 * 格式化时间戳
	 * 
	 * @param timestamp
	 * @param format
	 * @return
	 */
	public static String formatTimestamp(Long timestamp, String format)
	{
		if (!checkStringIsNotEmpty(format))
		{
			format = "yyyy-MM-dd HH:mm:ss";
		}
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
		Date date = new Date(timestamp*1000);
		return simpleDateFormat.format(date);
	}

	/**
	 * 计算MD5
	 * 
	 * @param input
	 * @return
	 */
	public static String getMD5(String input)
	{
		try
		{
			MessageDigest md = MessageDigest.getInstance("MD5");
			byte[] messageDigest = md.digest(input.getBytes());
			BigInteger number = new BigInteger(1, messageDigest);
			String hashtext = number.toString(16);
			// Now we need to zero pad it if you actually want the full 32
			// chars.
			while (hashtext.length() < 32)
			{
				hashtext = "0" + hashtext;
			}
			return hashtext.toUpperCase();
		}
		catch (NoSuchAlgorithmException e)
		{
			throw new RuntimeException(e);
		}
	}

	/**
	 * 把中文转成Unicode码
	 * 
	 * @param str
	 * @return
	 */
	public static String chinaToUnicode(String str)
	{
		String result = "";
		for (int i = 0; i < str.length(); i++)
		{
			int chr1 = (char) str.charAt(i);
			if ((chr1 >= 19968 && chr1 <= 171941) || (chr1 == 12289))
			{// 汉字范围 \u4e00-\u9fa5 (中文)+加、
				result += "\\u" + Integer.toHexString(chr1);
			}
			else
			{
				result += str.charAt(i);
			}
		}
		return result;
	}

	/**
	 * 生成请求唯一标识
	 * 
	 * @return
	 */
	public static String nextTicket()
	{
		return UUID.randomUUID().toString().toUpperCase();
	}

	/**
	 * 生成时间戳
	 * 
	 * @return
	 */
	public static int nextTimestamp()
	{
		return (int) (System.currentTimeMillis() / 1000);
	}

	/**
 * post请求
 *
 * @param url
 * @param param
 * @return
 */
public static String httpPost(String url, String param)
{
	// post请求返回结果
	DefaultHttpClient httpClient = new DefaultHttpClient();
	String str = "";
	HttpPost method = new HttpPost(url);
	try
	{
		if (null != param)
		{
			// 解决中文乱码问题
			StringEntity entity = new StringEntity(param, "utf-8");
			entity.setContentEncoding("UTF-8");
			entity.setContentType("application/json");
			method.setEntity(entity);
		}
		HttpResponse result = httpClient.execute(method);
		// 请求发送成功，并得到响应
		if (result.getStatusLine().getStatusCode() == 200)
		{

			try
			{
				// 读取服务器返回过来的json字符串数据
				str = EntityUtils.toString(result.getEntity());
				// 把json字符串转换成json对象
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
	}
	catch (IOException e)
	{
		e.printStackTrace();
	}
	return str;
}

	/**
	 * changhui 2018-1-15 百度3.0版本 post请求
	 *
	 * @param url
	 * @param param
	 * @return
	 */
	public static String httpBaidu3Post(String url, String param)
	{
		PrintWriter out = null;
		BufferedReader in = null;
		String result = "";
		try {
			URL realUrl = new URL(url);
			// 打开和URL之间的连接
			URLConnection conn = realUrl.openConnection();
			// 设置通用的请求属性
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("Content-Type","application/x-www-form-urlencoded");
			conn.setRequestProperty("user-agent",
					"Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			// 发送POST请求必须设置如下两行
			conn.setDoOutput(true);
			conn.setDoInput(true);
			// 获取URLConnection对象对应的输出流
			out = new PrintWriter(conn.getOutputStream());
			// 发送请求参数
			out.print(param);
			// flush输出流的缓冲
			out.flush();
			// 定义BufferedReader输入流来读取URL的响应
			in = new BufferedReader(
					new InputStreamReader(conn.getInputStream()));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			System.out.println("发送 POST 请求出现异常！"+e);
			e.printStackTrace();
		}
		//使用finally块来关闭输出流、输入流
		finally{
			try{
				if(out!=null){
					out.close();
				}
				if(in!=null){
					in.close();
				}
			}
			catch(IOException ex){
				ex.printStackTrace();
			}
		}
		return result;
	}

	/**
	 * 生成命令字符串
	 * 
	 * @param SOURCE
	 * @param SECRET
	 * @param type
	 * @param body
	 * @return
	 */
	public static String cmdFactory(final String SOURCE, final String SECRET, CmdType type, Object body)
	{
		Cmd cmd = new Cmd();

		cmd.setCmd(String.valueOf(type).toLowerCase().replace("_", "."));
		cmd.setSource(SOURCE);
		cmd.setSecret(SECRET);
		cmd.setTicket(nextTicket());
		cmd.setTimestamp(nextTimestamp() + "");
		cmd.setVersion(2 + "");
		if(body!=null){
			JSONObject body_json_obj;
			try {
				body_json_obj = JSONObject.fromObject(body);
				if(!CommonUtil.checkStringIsNotEmpty(body_json_obj.optString("shop_logo"))){
					body_json_obj.discard("shop_logo");
				}
				cmd.setBody(body_json_obj);
			} catch (Exception e) {
				e.printStackTrace();
			}
			}else{
				cmd.setBody(body);
			}
		
		// cmd.setSign(null);
		JSONObject signObject = JSONObject.fromObject(cmd);
		signObject.discard("sign");// 去除sign

		String signJson = signObject.toString();
		// 对所有/进行转义
		signJson = signJson.replace("/", "\\/");
		// 中文字符转为unicode
		signJson = chinaToUnicode(signJson);
		String sign = getMD5(signJson);
		// 准备生成请求数据
		cmd.setSign(sign);
		// cmd.setSecret(null);

		JSONObject requestObject = JSONObject.fromObject(cmd);
		// 此处注意secret不参与传递
		requestObject.discard("secret");

		String requestJson = requestObject.toString();
		// 对所有/进行转义
		requestJson = requestJson.replace("/", "\\/");
		// 中文字符转为unicode
		requestJson = chinaToUnicode(requestJson);

		return requestJson;
	}

	
	
	public static String cmdFactory1(final String SOURCE, final String SECRET, CmdType type, Object body)
	{
		Map<String,Object> cmdMap=new TreeMap<String, Object>();
		cmdMap.put("cmd", String.valueOf(type).toLowerCase().replace("_", "."));
		cmdMap.put("source", SOURCE);
		cmdMap.put("secret", SECRET);
		cmdMap.put("ticket",nextTicket());
		cmdMap.put("timestamp",nextTimestamp() + "");
		cmdMap.put("version", 2 + "");
		if(body!=null){
			JSONObject body_json_obj;
			try {
				body_json_obj = JSONObject.fromObject(body);
				if(!CommonUtil.checkStringIsNotEmpty(body_json_obj.optString("shop_logo"))){
					body_json_obj.discard("shop_logo");
				}
				//如果pic等于-1，表示不上传图片，去掉pic参数.at 2017-05-08
				if("-1".equals(body_json_obj.optString("pic"))){
					body_json_obj.discard("pic");
				}
				cmdMap.put("body", sortMapByKey(body_json_obj));
			} catch (Exception e) {
				e.printStackTrace();
			}
			}else{
				cmdMap.put("body", body);
			}
		
	     Map<String, Object> resultMap = sortMapByKey(cmdMap);
		String signJson = JSONObject.fromObject(resultMap).toString();
		// 对所有/进行转义
		signJson = signJson.replace("/", "\\/");
		
		// 中文字符转为unicode
		signJson = chinaToUnicode(signJson);
		String sign = getMD5(signJson);
		// 准备生成请求数据
		cmdMap.put("sign", sign);

		JSONObject requestObject = JSONObject.fromObject(cmdMap);
//		// 此处注意secret不参与传递
		requestObject.discard("secret");
//
		String requestJson = requestObject.toString();
//		// 对所有/进行转义
		requestJson = requestJson.replace("/", "\\/");
//		// 中文字符转为unicode
		requestJson = chinaToUnicode(requestJson);

		return requestJson;
	}
	/**
	 * 百度换新jar修改cmd构成方法
	 * 
	 * @param SOURCE
	 * @param SECRET
	 * @param type
	 * @param body
	 * @return
	 */
	public static String cmdFactory1(final String SOURCE, final String SECRET, CmdType type, Object body, String version)
	{
		Map<String,Object> cmdMap=new TreeMap<String, Object>();
		cmdMap.put("cmd", String.valueOf(type).toLowerCase().replace("_", "."));
		cmdMap.put("source", SOURCE);
		cmdMap.put("secret", SECRET);
		cmdMap.put("ticket",nextTicket());
		cmdMap.put("timestamp",nextTimestamp() + "");
		cmdMap.put("version", version);
		if(body!=null){
			JSONObject body_json_obj;
			try {
				body_json_obj = JSONObject.fromObject(body);
				if(!CommonUtil.checkStringIsNotEmpty(body_json_obj.optString("shop_logo"))){
					body_json_obj.discard("shop_logo");
				}
				cmdMap.put("body", sortMapByKey(body_json_obj));
			} catch (Exception e) {
				e.printStackTrace();
			}
			}else{
				cmdMap.put("body", body);
			}
		
	     Map<String, Object> resultMap = sortMapByKey(cmdMap);
		String signJson = JSONObject.fromObject(resultMap).toString();
		// 对所有/进行转义
		signJson = signJson.replace("/", "\\/");
		
		// 中文字符转为unicode
		signJson = chinaToUnicode(signJson);
		String sign = getMD5(signJson);
		// 准备生成请求数据
		cmdMap.put("sign", sign);

		JSONObject requestObject = JSONObject.fromObject(cmdMap);
//		// 此处注意secret不参与传递
		requestObject.discard("secret");
//
		String requestJson = requestObject.toString();
//		// 对所有/进行转义
		requestJson = requestJson.replace("/", "\\/");
//		// 中文字符转为unicode
		requestJson = chinaToUnicode(requestJson);

		return requestJson;
	}
	/**
	 * 生成命令字符串
	 * 
	 * @param SOURCE
	 * @param SECRET
	 * @param type
	 * @param body
	 * @return
	 */
	public static String cmdFactory(final String SOURCE, final String SECRET, CmdType type, Object body, String version)
	{
		Cmd cmd = new Cmd();

		cmd.setCmd(String.valueOf(type).toLowerCase().replace("_", "."));
		cmd.setSource(SOURCE);
		cmd.setSecret(SECRET);
		cmd.setTicket(nextTicket());
		cmd.setTimestamp(nextTimestamp() + "");
		cmd.setVersion(version);
		cmd.setBody(body);
		// cmd.setSign(null);
		JSONObject signObject = JSONObject.fromObject(cmd);
		signObject.discard("sign");// 去除sign

		String signJson = signObject.toString();
		// 对所有/进行转义
		signJson = signJson.replace("/", "\\/");
		// 中文字符转为unicode
		signJson = chinaToUnicode(signJson);
		String sign = getMD5(signJson);
		// 准备生成请求数据
		cmd.setSign(sign);
		// cmd.setSecret(null);

		JSONObject requestObject = JSONObject.fromObject(cmd);
		// 此处注意secret不参与传递
		requestObject.discard("secret");

		String requestJson = requestObject.toString();
		// 对所有/进行转义
		requestJson = requestJson.replace("/", "\\/");
		// 中文字符转为unicode
		requestJson = chinaToUnicode(requestJson);

		return requestJson;
	}

	/**
	 * changhui 2018-1-15 百度换新jar修改cmd构成方法 版本3.0
	 *
	 * @param SOURCE
	 * @param SECRET
	 * @param type
	 * @param body
	 * @return
	 */
	public static String cmdFactory3(final String SOURCE, final String SECRET, CmdType type, Object body, String version)
	{
		Map<String,Object> cmdMap=new TreeMap<String, Object>();
		cmdMap.put("cmd", String.valueOf(type).toLowerCase().replace("_", "."));
		cmdMap.put("source", SOURCE);
		cmdMap.put("secret", SECRET);
		String ticket = nextTicket();
		cmdMap.put("ticket",ticket);
		String timestamp = nextTimestamp() + "";
		cmdMap.put("timestamp",timestamp);
		cmdMap.put("version", version);
		cmdMap.put("encrypt", "des.v1");
		if(body!=null){
			JSONObject body_json_obj;
			try {
				body_json_obj = JSONObject.fromObject(body);
				if(!CommonUtil.checkStringIsNotEmpty(body_json_obj.optString("shop_logo"))){
					body_json_obj.discard("shop_logo");
				}
				cmdMap.put("body", sortMapByKey(body_json_obj));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			cmdMap.put("body", body);
		}

		Map<String, Object> resultMap = sortMapByKey(cmdMap);
		String signJson = JSONObject.fromObject(resultMap).toString();
		// 对所有/进行转义
		signJson = signJson.replace("/", "\\/");



		String  md5Input = "";
		md5Input+="body="+body+"&";
		md5Input+="cmd="+String.valueOf(type).toLowerCase().replace("_", ".")+"&";
		md5Input+="encrypt=des.v1&";
		md5Input+="secret="+SECRET+"&";
		md5Input+="source="+SOURCE+"&";
		md5Input+="ticket="+ticket+"&";
		md5Input+="timestamp="+timestamp+"&";
		md5Input+="version="+version;

		// 中文字符转为unicode
		signJson = chinaToUnicode(md5Input);

		String sign = getMD5(signJson);
		// 准备生成请求数据
		cmdMap.put("sign", sign);

		JSONObject requestObject = JSONObject.fromObject(cmdMap);
//		// 此处注意secret不参与传递
		requestObject.discard("secret");
//
		String requestJson = requestObject.toString();
//		// 对所有/进行转义
		requestJson = requestJson.replace("/", "\\/");
//		// 中文字符转为unicode
		requestJson = chinaToUnicode(requestJson);

		String  requestParam = "";
		requestParam+="cmd="+String.valueOf(type).toLowerCase().replace("_", ".")+"&";
		requestParam+="version="+version+"&";
		requestParam+="timestamp="+timestamp+"&";
		requestParam+="ticket="+ticket+"&";
		requestParam+="source="+SOURCE+"&";
		requestParam+="sign="+sign+"&";
		requestParam+="body="+body+"&";
		requestParam+="encrypt=des.v1";

		return requestParam;
	}

	/**
	 * 将hstore转为json
	 * 
	 * @param hstore
	 * @return
	 */
	public static String hstore2Json(String hstore)
	{
		return "{" + hstore.replace("=>", ":").replace("\"null\"", "null") + "}";
	}

	/**
	 * 元转分
	 * 
	 * @param yuan
	 * @return
	 */
	public static int yuan2Fen(double yuan)
	{
		return BigDecimal.valueOf(yuan).multiply(BigDecimal.valueOf(100)).intValue();
	}

	/**
	 * 分转元
	 * 
	 * @param fen
	 * @return
	 */
	public static String fen2Yuan(int fen)
	{
		return new BigDecimal(fen).divide(new BigDecimal(100)).toString();
	}

	/**
	 * 检查字符串为非null非空串
	 * 
	 * @param str
	 * @return
	 */
	public static boolean checkStringIsNotEmpty(String str)
	{
		if (null != str && !"".equals(str.trim()) && !"null".equals(str))
		{
			return true;
		}
		return false;
	}

	/**
	 * 多个数值字符串运算
	 * 
	 * @param params
	 * @return
	 */
	public static String multiFloatValueOfString(String... params)
	{
		float f = 1f;
		for (String s : params)
		{
			f *= Float.valueOf(s);
		}
		return String.valueOf(f);
	}

	/**
	 * aes加密
	 * 
	 * @param plainText
	 *            明文内容
	 * @param aesKey
	 *            key
	 * @return 密文内容
	 * @throws Exception
	 */
	public static String AESEncrypt(String plainText, String aesKey) throws Exception
	{
		return new SimpleStringCypher(aesKey).encrypt(plainText);
	}

	/**
	 * aes解密
	 * 
	 * @param codedText
	 *            密文内容
	 * @param aesKey
	 *            key
	 * @return 明文内容
	 * @throws Exception
	 */
	public static String AESDecrypt(String codedText, String aesKey) throws Exception
	{
		return new SimpleStringCypher(aesKey).decrypt(codedText);
	}

	/**
	 * 把一个文件转化为字节
	 * 
	 * @param file
	 * @return byte[]
	 * @throws Exception
	 */
	public static byte[] getFileBytes(String path) throws Exception
	{
		byte[] bytes = null;
		if (path != null)
		{
			URL url = new URL(path);
			URLConnection connection = url.openConnection(); // 打开连接
			connection.setDoOutput(true);
			connection.setReadTimeout(10000);
			connection.setConnectTimeout(3000);
			InputStream fileInputStream = connection.getInputStream();
			int length = fileInputStream.available();
			if (length > Integer.MAX_VALUE)
			{
				return null;
			}
			bytes = new byte[length];
			int offset = 0;
			int numRead = 0;
			while (offset < length && (numRead = fileInputStream.read(bytes, offset, length - offset)) >= 0)
			{
				offset += numRead;
			}
			// 如果得到的字节长度和file实际的长度不一致就可能出错了
			if (offset < length)
			{
				return null;
			}
			fileInputStream.close();
		}
		return bytes;
	}

	/**
	 * 大众点评发送/接收消息请求
	 * 
	 * @param token
	 * @param appKey
	 * @param content
	 * @return
	 * @throws Exception
	 */
	public static String DPHttpPost(String token, String appKey, String content) throws Exception
	{
		Map param = new HashMap();
		param.put("token", token);

		MessageDigest md = MessageDigest.getInstance("SHA-1");
		// appKey经过SHA-1 128位摘要算法后取前128位生成摘要签名sign
		byte[] messageDigest = md.digest(appKey.getBytes());
		// sign应经过Base64转码成字符串
		String sign = new String(Base64.encodeBase64String(messageDigest));
		// (sign+token)继续经过SHA-1 128位摘要算法后取前128位生成真正的AES加密秘钥 key S2
		byte[] key = md.digest((sign + token).getBytes());
		// S2应经过Base64转码成字符串
		String aesKey = new String(Base64.encodeBase64String(key));

		Map m = new HashMap();
		m.put("content", content);
		m.put("sign", sign);
		m.put("ts", nextTimestamp());
		System.out.println(aesKey);
		String contentJson = AESEncrypt(JSONObject.fromObject(m).toString(), aesKey);
		param.put("content", contentJson);
		param.put("version", "v1.0.0");

		String resJson = HttpUtil.sendPostRequest(Constant.DP_API_URL, param);

		return resJson;
	}
	
	/** 替换特殊字符
	 * @param arg
	 * @return
	 */
	public static String replaceEvilChar(String arg){
			return arg==null?null:arg.replaceAll(EVIL_CHARACTER, " ");
	}

	public static int getSecondsFromDate(String expireDate)
	{
		if (expireDate == null || expireDate.trim().equals(" ")) return 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = null;
		try
		{
			date = sdf.parse(expireDate);
			return (int) (date.getTime() / 1000);
		}
		catch (ParseException e)
		{
			e.printStackTrace();
			return 0;
		}
	}

	public static String getDateFromSeconds(String seconds)
	{
		if (seconds == null) return " ";
		else
		{
			Date date = new Date();
			try
			{
				date.setTime(Long.parseLong(seconds) * 1000);
			}
			catch (NumberFormatException nfe)
			{
				nfe.printStackTrace();
			}
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			return sdf.format(date);
		}
	}
	//数字补零 
   public static String zeroFill(int number){
		    // 0 代表前面补充0     
		    // 4 代表长度为6     
		    // d 代表参数为正数型     
		    String str = String.format("%04d", number);
			return str;     
   }
   
   public static String insertJSONParamsToSql(String tableName,JSONObject params){
	   Set<String> columnSet=params.keySet();
	   StringBuilder sql=new StringBuilder("INSERT INTO "+tableName+" (");
	   String columnStr="";
	   String valueStr="";
 	   for(String column:columnSet){
 		  columnStr+=","+column;
 		  valueStr+=",'"+params.optString(column)+"'";  
	   }
 	   sql.append(columnStr.replaceFirst(",", ""));
 	   sql.append(") VALUES (");
 	   sql.append(valueStr.replaceFirst(",", ""));
 	   sql.append(");");
	  return sql.toString(); 
	  
   }
	/** 获取当前时间
	 * @param format
	 * @return
	 */
	public static String getNowTimeString(String format)
	{

		if (null == format || "".equals(format.trim()))
		{
			format="yyyy-MM-dd HH:mm:ss";
		}
		SimpleDateFormat sdf = new SimpleDateFormat(format);

		return sdf.format(new Date());

		
   }
	
	/**
	 * 检查字符串是否是纯数字
	 * @return true 纯数字
	 */
	public static boolean checkIsNumber(String param){
		return param.matches("[0-9]+");
	}
	
	public static String getInvalidTimeMap(String time) {
		if (null == time || "".equals(time.trim())) {
			return getNowTimeString(null);
		} else {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			try {
				Date date=sdf.parse(time);
				return sdf.format(date);
			} catch (ParseException e) {
				e.printStackTrace();
				return getNowTimeString(null);
			}
		}

	}

    /** SHA1散列加密
     * @param data
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String sha1(String data){
        MessageDigest md = null;
        StringBuffer buf = new StringBuffer();
        try {
            md = MessageDigest.getInstance("SHA1");
            try {
				md.update(data.getBytes("utf-8"));
			} catch (UnsupportedEncodingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
            byte[] bits = md.digest();
            for(int i=0;i<bits.length;i++){
                int a = bits[i];
                if(a<0) a+=256;
                if(a<16) buf.append("0");
                buf.append(Integer.toHexString(a));
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return buf.toString();
    }

    public static String jsonArrayToString(JSONArray jsonArray){
        Object[] arr=  jsonArray.toArray();
        String s="";
        for (Object o:arr ) {
            s+=","+o;
        }
        return s.replaceFirst(",","");
    }
    public static void main(String args[]){

       JSONArray a= JSONArray.fromObject("[\"1\",\"2\"]");
       System.out.print( jsonArrayToString(a));
    }
    //百度渠道签名前对Object的key进行排序
    public static Map<String, Object> sortMapByKey(Map<String, Object> cmdMap) {
    	if (cmdMap == null || cmdMap.isEmpty()) {
    		return null;
	    }
    	Map<String, Object> sortMap = new TreeMap<String, Object>(new MapKeyComparator());
    	sortMap.putAll(cmdMap);
	    return sortMap;
    }
    
    //计算公式结果保留两位小数
    public static double keepTwoDecimal(Double param){
    	BigDecimal bg=new BigDecimal(param);
    	double result=bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    	return result;
    }
    

}
class MapKeyComparator implements Comparator<String>{

    @Override
    public int compare(String str1, String str2) {
        return str1.compareTo(str2);
    }
}
