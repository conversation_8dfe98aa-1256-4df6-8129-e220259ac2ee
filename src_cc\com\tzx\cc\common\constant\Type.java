package com.tzx.cc.common.constant;

public enum Type
{
	//电话外卖接口相关
	/**
	 * 登录（账号验证）
	 */
	oc_login,
	/**
	 * //应用程序版本检测
	 */
	oc_version,
	/**
	 * 基础数据版本验证
	 */
	oc_basic_version,
	/**
	 * 基础信息同步
	 */
	oc_basicdata,
	/**
	 * 查询会员相关信息
	 */
	oc_customerinfo,
	/**
	 * 地址信息维护
	 */
	oc_customeraddress,
	/**
	 * 订单信息维护
	 */
	oc_orderlist,
	/**
	 * 统计订单总数
	 */
	oc_ordertotal,
	
	/**
	 * 订单
	 */
	ORDER,
	
	/**
	 * 第三方验劵
	 */
	THIRD_COUPONS,
	
	//--------------------------------------------
	/*
	 * 创建商户
	 */
	shop_create,
	/*
	 * 修改商户
	 */
	shop_update,
	/*
	 * 商户下线
	 */
	shop_offline,
	/*
	 * 商户开业
	 */
	shop_open,
	/*
	 * 商户歇业
	 */
	shop_close,
	/*
	 * 商户订单阈值设置
	 */
	shop_threshold_set,
	/*
	 * 创建菜品分类
	 */
	dish_category_create,
	/*
	 * 修改菜品分类
	 */
	dish_category_update,
	/*
	 * 删除菜品分类
	 */
	dish_category_delete,
	/*
	 * 创建菜品组
	 */
	dish_group_create,
	/*
	 * 修改菜品组
	 */
	dish_group_update,
	/*
	 * 删除菜品组
	 */
	dish_group_delete,
	/*
	 * 创建菜品
	 */
	dish_create,
	/*
	 * 批量菜品创建/更新
	 */
	dish_batch_initdata,
	/*
	 * 修改菜品
	 */
	dish_update,
	/*
	 * 删除菜品
	 */
	dish_delete,
	/*
	 * 菜品上线
	 */
	dish_online,
	/*
	 * 菜品下线
	 */
	dish_offline,
	/*
	 * 菜品阈值设置
	 */
	dish_threshold_set,
	/*
	 * 满减活动创建
	 */
	full_cut_activities_create,
	/*
	 * 满减活动修改
	 */
	full_cut_activities_update,
	/*
	 * 满免活动创建
	 */
	full_free_activities_create,
	/*
	 * 满免活动修改
	 */
	full_free_activities_update,
	/*
	 * 活动删除
	 */
	activity_delete,
	/*
	 * 活动停用
	 */
	activity_stop,
	/*
	 * 订单确认
	 */
	order_confirm,
	/*
	 * 订单取消
	 */
	order_cancle,
	/*
	 * 完成订单
	 */
	order_complete,
	/*
	 * 订单创建
	 */
	order_create, 
	/**
	 * 生成电子发票信息
	 */
	ISSUE_ELECTRONIC_INVOICE,
	/**
	 * 生成电子发票直接跳转
	 */
	ISSUE_ELECTRONIC_INVOICE_SCC,
	/**
	 * 取消电子发票
	 */
	CANCLE_LECTRONIC_INVOICE,
	/**
	 * 取消电子发票 -- newpos专用
	 */
	CANCLE_LECTRONIC_INVOICE_NEWPOS,
	/**
	 * 查询电子发票
	 */
	QUERY_INVOICE_INFO,

    /**
     * 版本管理
     */
    VERSION;
}
