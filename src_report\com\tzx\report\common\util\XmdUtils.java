package com.tzx.report.common.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 * <AUTHOR>
 * @date 2018/6/6
 */
public class XmdUtils {
    public static SimpleDateFormat simpleyyyyMMddFormat	= new SimpleDateFormat("yyyy-MM-dd");

    public static void main(String[] args) {
        String s = "{164410767,164599639,164601692,164638793,164641002,165639913,165688775,166118045,1260781001,2147483647}";
        System.out.println(s.substring(1,s.length()-1));
    }

    /**
     * 计算两个日期相差天数
     * @param startDate  开始
     * @param endDate  结束
     * @return
     */
    public static int computeDiffDay(String startDate){
        try {
            Calendar calendar = Calendar.getInstance();
            Date cdate = calendar.getTime();
            String endDate = simpleyyyyMMddFormat.format(cdate);
            Date d1 = simpleyyyyMMddFormat.parse(startDate);
            Date d2 = simpleyyyyMMddFormat.parse(endDate);
            int diffd = (int)((d2.getTime()-d1.getTime())/(1000*3600*24));
            return diffd;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 比较和当前日期是否同一天
     * @return
     *         true:是
     *         false：否
     */
    public static boolean compareCurrentDate(String date1){
        try {
            Date d1 = simpleyyyyMMddFormat.parse(date1);
            Date d2 = simpleyyyyMMddFormat.parse(simpleyyyyMMddFormat.format(new Date()));
            if(d1.compareTo(d2)==0){
                return true;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 计算昨天开始往前推三个月
     * @return
     */
    public static List<String> compute3MonthDates(){
        List<String> dateList = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        Date cdate = calendar.getTime();
        calendar.add(Calendar.MONTH,-3);

        while (calendar.getTime().before(cdate)){
            Date d = calendar.getTime();
            dateList.add(simpleyyyyMMddFormat.format(d));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        dateList.add(simpleyyyyMMddFormat.format(cdate));
        return dateList;
    }


    /**
     * sha256
     * */
    public static String sha256Hex(String data){
        return DigestUtils.sha256Hex(data);
    }

    /**
     * 新美大加密sign
     * @return
     */
    public static String  createSign(String key, SortedMap<String,Object> map){
        StringBuffer sb = new StringBuffer();
        Set set = map.entrySet();
        Iterator it = set.iterator();
        while (it.hasNext()){
            Map.Entry entry = (Map.Entry)it.next();
            String k = (String) entry.getKey();
            Object val = (Object) entry.getValue();
            if(StringUtils.isNotBlank(k) && null!=val){
                sb.append(k+"="+val+"&");
            }
        }

        sb.append("key="+key);
        String sign = sha256Hex(sb.toString());
        return sign;
    }
}
