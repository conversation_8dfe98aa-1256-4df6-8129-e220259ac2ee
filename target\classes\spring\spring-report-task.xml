<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/tx
       http://www.springframework.org/schema/tx/spring-tx-2.5.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context-2.5.xsd
       http://www.springframework.org/schema/task
       http://www.springframework.org/schema/task/spring-task-3.1.xsd"
	default-autowire="byName">

	<task:scheduler id="scheduler" pool-size="10" />
	<!-- 支持异步方法执行 -->
	<task:annotation-driven executor="executorWithCallerRunsPolicy"
		scheduler="scheduler" />
	<task:executor id="executorWithCallerRunsPolicy"
		pool-size="50-150" queue-capacity="50" rejection-policy="CALLER_RUNS" />
  
	<bean id="notifySpringScheduledExecutorFactoryBean"
		class="org.springframework.scheduling.concurrent.ScheduledExecutorFactoryBean">
		<property name="scheduledExecutorTasks">
			<list>
				<!-- 报表新闻点击率 -->
				<ref bean="clickRateSpringScheduledExecutorTask" />
			</list>
		</property>
	</bean>
	 
    <!-- 报表新闻点击率 -->
	 <bean id="clickRateSpringScheduledExecutorTask" class="org.springframework.scheduling.concurrent.ScheduledExecutorTask">
        <property name="runnable" ref="newsClickRateSpringScheduledExecutorTask"/>
        <!-- 初次执行任务delay时间，单位为ms，默认值为0，代表首次加载任务时立即执行；比如1min -->
        <!-- <property name="delay" value="310000"/>5分钟 -->
        <property name="delay" value="62000"/><!-- 5分钟-->
        <!-- 间隔时间，单位为ms，默认值为0，代表任务只执行一次；比如2min -->
        <property name="period" value="310000"/><!-- 2小时-->
        <!-- 是否采用fixedRate方式进行任务调度，默认为false，即采用fixedDelay方式 -->
        <!-- fixedRate:定时间隔执行，不管上次任务是否已执行完毕；fixedDelay:每次任务执行完毕之后delay固定的时间 -->
        <property name="fixedRate" value="true"/>
     </bean>
    <bean id="newsClickRateSpringScheduledExecutorTask" class="com.tzx.report.common.util.redisUtil.NewsClickRateTask"/>
</beans>