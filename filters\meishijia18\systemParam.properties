#Tue May 04 19:58:01 CST 2014
# hq or boh
sotreorhq=hq
ifstart=true
#ifstart=false
Url=tcp://*************:6666
#Url=tcp://msg.tzx.com.cn:6612
User=
Password=
qtoboh=qtoboh
qcometoboh=qtoboh_hdl_7x7t38j3mjuh3a2guhx8a3xlstg5utbe

#for tt
ttsvr=tcp://*************:1884
tttopic=app_topic

#datasourcetype
datasourcetype=postgres
print.type=80
system_password=15f28fbc8c6e099a1e98b8a555d72374

#smstype\u662F\u7528\u4E8E\u914D\u7F6E\u4F7F\u7528\u5E73\u53F0\u8FD8\u662F\u77ED\u4FE1\u732B\uFF0C\u7528\u732B\u503C\u4E3A\u77ED\u4FE1\u732B\u670D\u52A1\u5668IP \u5982\uFF1A************,\u5982\u679C\u662F\u4F7F\u7528\u77ED\u4FE1\u5E73\u53F0\u76F4\u63A5\u51990\u4E3A\u4E0A\u6D77\u5E0C\u5965\u77ED\u4FE1\u5E73\u53F0\uFF0C\u51991\u662F\u7528\u4EBF\u7F8E\u77ED\u4FE1\u5E73\u53F0,\u51992\u4E3A\u7EBF\u4E0A\u56E2\u961F\u7559\u4E0B\u7684\u65B0\u5E73\u53F0\uFF08\u72D7\u5C4E\uFF09
smstype=2
#fsdw=\u5929\u5B50\u661F

#\u4E0B\u9762\u662F\u4EBF\u7F8E\u5E73\u53F0\u76F8\u5173\u914D\u7F6E\u53C2\u6570
#\u4EBF\u7F8E\u8D2D\u4E70\u7684\u8F6F\u4EF6\u5E8F\u5217\u53F7
softwareSerialNo=3SDK-EMY-0130-MEWSR
#\u4ECE\u4EBF\u7F8E\u8D2D\u4E70\u7684\u8F6F\u4EF6\u5E8F\u5217\u53F7\u6240\u5E26\u7684KEY \u503C
key=026404
#\u4ECE\u4EBF\u7F8E\u8D2D\u4E70\u7684\u8F6F\u4EF6\u5E8F\u5217\u53F7\u6240\u5E26\u7684\u5BC6\u7801
password=055956

#\u4E0B\u9762\u7684\u53C2\u6570\u662F\u914D\u7F6E\u4E0A\u6D77\u5E0C\u5965\u77ED\u4FE1\u5E73\u53F0\u76F8\u5173\u53C2\u6570
#\u77ED\u4FE1\u5E73\u53F0\u5730\u5740
url=http://api.52ao.com
#\u7528\u6237\u540D
user=laitianhua06
#\u5BC6\u7801\u8981\u8FDB\u884CMD5\u52A0\u5BC6,32\u4F4D\uFF0C\u5927\u5C0F\u5199\u90FD\u53EF\u4EE5\uFF0C\u5DE5\u5177\uFF1A  http://tool.chinaz.com/Tools/MD5.aspx?q=123456&md5type=0
pass=E10ADC3949BA59ABBE56E057F20F883E

#\u4E0B\u9762\u662F20150327\u65B0\u77ED\u4FE1\u5E73\u53F0\u63A5\u53E3\u8D26\u53F7\u5BC6\u7801(\u7EBF\u4E0A\u5E73\u53F0)
newkey=SDK-BBX-010-20057
newpassword=tzx68698

#\u4E0B\u9762\u662F20150723pos\u8F6C\u67E5\u4F1A\u5458\u63A5\u53E3url
#crmUrl=http://**************:8080/tzxsaas/crmRest/post
crmUrl=http://*************:8081/crmRest/post
#crmUrl=http://cs.meishijia.com/crmRest/post

#Whether as a redis task service startup, 1 is yes
taskopen=1

#uploadImgIp
upload_img_ip=
upload_websiteimg_ip=

#
hqdata_minite_period=10

#
saas_url=http://cs.meishijia.com
post_url=http://test.e7e6.net/tzxsecondpay/
#
storestatus_minite_period=1

item_photo_path=itemImage
#pos alipay 
pos_payment_url=http://cs.meishijia.com/payment/aliPaymentRest/aliPay
#pos wechat
pos_payment_wechat_url=http://cs.meishijia.com/payment/wechat/post
#alipay notify url 
alipay_notify_url=http://cs.meishijia.com/payment/aliPaymentRest/notify
#wechat notify url 
wechat_notify_url=http://cs.meishijia.com/payment/wechat/notify
# \u95E8\u5E97\u5FAE\u4FE1\u652F\u4ED8\u56DE\u8C03
wechat_notify_url_new=http://cs.meishijia.com/paymentCallBack/weixin/notify

#APP
app_name=app-release.apk
#
app_path=download/

#for wechat product
product_wechat_service_ip=http://cs.meishijia.com
product_wechat_scmip=http://cs.meishijia.com/crmRest/post

#wechat service mch message	start
wechat_service_mch_service_ip=http://www.e7e6.net
wechat_service_mch_appid=wxc2864bc7ba5baa5c
wechat_service_mch_secert=0998c4c22fcfd9bd75a94195605500ad
wechat_service_mch_mch_id=10010438
wechat_service_mch_api_secert=tzxsaasweixinpay2015101212150000
wechat_service_mch_cert=cert/apiclient_cert.p12
wechat_service_sub_mch_id = **********
#wechat service mch message end

#alipay service provider id start
alipay_service_provider_id=2088411391202430
#alipay service provider id end
#tenent_id==hdl
#store_id=48

#\u662F\u5426\u5916\u5356
is_delivery=true

# can be local IP
#supplier_address=http://127.0.0.1:8180/supplier/sup/supContraller/post
#supplier_address=http://*********/supplier/sup/supContraller/post
#supplier_address=http://cs.meishijia.com/supplier/sup/supContraller/post
supplier_address=http://*************:8180/supplier/sup/supContraller/post

#wechat thrid
wechat_component_appid=wx54221b218387ae71
wechat_component_appsecret=0e92590caffe9c0c31d49231ac73aa6c
wechat_compoment_encodingaeskey=Dlf6NTLNpIZyA3MOrLRlIHV46QcWHcAi0G84aEL86p2
wechat_compoment_token=tzx
#wechat thrid
#wechat thrid redirect_uri
wechat_thrid_redirect_uri=http://cs.meishijia.com
#wechat thrid redirect_uri

# \u65B0\u7F8E\u5927\u56E2\u8D2D&\u95EA\u60E0\u63A5\u53E3
xmd_url=http://api.open.cater.meituan.com
xmd_developerid=100113
xmd_signkey=m1fwurrvo09o33c7

#\u4E3B\u8981\u7528\u4E8E\u8C03\u7528\u65B0\u7F8E\u5927\u7684UISDK
xmdwm_url=https://open-erp.meituan.com


# \u5916\u5356\u5E73\u53F0api\u8C03\u7528\u5730\u5740 {


#\u767E\u5EA6\u5916\u5356api
baidu_api_urL=http://api.waimai.baidu.com

#\u7F8E\u56E2\u5916\u5356api
meituan_api_url=http://waimaiopen.meituan.com/api/v1/
#\u7F8E\u56E2\u5916\u5356\u6D4B\u8BD5api
#meituan_api_url=http://test.waimaiopen.meituan.com/api/v1/

#\u997F\u4E86\u4E48api
ele_api_url=http://v2.openapi.ele.me/

#\u5927\u4F17\u70B9\u8BC4
dp_api_url=https://e.51ping.com/mpi/

#yichi
#yichi_address=http://localhost:8081/yichi/yichi/yichiContraller/post
yichi_address=http://cs.meishijia.com/yichi/yichi/yichiContraller/post


rif_tzxApply = /tzxApply
rif_tzxApplyBom = /tzxapplyBOM
rif_finish = /tzxDistributionFinish
rif_return = /tzxReturn
rif_supplyin = /tzxSupplierIn
rif_supplyout = /tzxSupplierOut


push_order =/ecsc-extension-rpc/order/pushOrder
download_invoice =/ecsc-extension-rpc/deliveryOrderShip/pullDeliveryOrderShip
update_delivery_status =/ecsc-extension-rpc/deliveryOrderShip/updateBillStatic
push_dcin =/ecsc-extension-rpc/dcIn/pushDcIn
push_dcout =/ecsc-extension-rpc/dcOut/pushDcOut
download_data =/ecsc-extension-rpc/dataSync/dataSync


# }

omUrl = http://cs.meishijia.com/om
maintaskopen = 1
childtaskopen = 1

saas_supplier = SAAS_SUPPLY_QUEUE

#fastdfs_url 4 mq to use
MQFASTDFSURL=http://fastdfs.meishijia.com/

#WebSocket \u63A8\u9001 url
websocket_push_url=http://push.meishijia.com:28080/push/bizapi/message

#the url to save operate Log 
OPERATE_LOG_URL=https\://cs.e7e6.net\:28002/operate/logger/info

#elm2.0 oauth callback used... note:must be https
ele_oauth_callbackUrl=http://cs.meishijia.com/thirdpartydown/orderdownstreamrest/elm/authCallBack
#elm2.0 oauth true|false \u997F\u4E86\u4E48\u8BA4\u8BC1\u65B9\u5F0F:\u6D4B\u8BD5\u8BA4\u8BC1|\u751F\u4EA7\u8BA4\u8BC1
ele_oauth_istest=true

#\u662F\u5426\u542F\u7528\u65B0\u63A5\u5355\u6A21\u5F0F
waimai_start=1
#\u5916\u5356\u961F\u5217\u540D\u79F0
waimai_queue=waimai
#\u5916\u5356\u7EBF\u7A0B\u6570
waimai_queue_thread_size=100

#waimai db partition
waimai_partition_start=1

##KafKa Log
#\u6B64\u503C\u4E3A1\u65F6\u6253\u5F00\u65E5\u5FD7\uFF0C0\u4F4D\u5173\u95ED
order_log=1
kafka_open=1
zookeeper.connect=*********:2181,*********:2182,*********:2181
kafka_topic=CcTopic
metadata.broker.list=*********:9092
#metadata.broker.list=*********:9092,*********:9093,*********:9092
auto.offset.reset=earliest
group.id=cc_log
enable.auto.commit=true
auto.commit.interval.ms=1000

#\u674e\u5148\u751f\u8bc4\u8bba\u5b9a\u65f6\u5206\u949f\u6570
comment.task.min=1440
#\u674e\u5148\u751f\u8bc4\u8bba\u83b7\u53d6\u89e6\u53d1\u65f6\u95f4
comment.task.triggertime=3
#\u674e\u5148\u751f\u8bc4\u8bba\u5206\u5e03\u5f0f\u9501\u8fc7\u671f\u65f6\u95f4
comment.task.expiretime=10
#\u8bc4\u8bba\u6570\u636e\u83b7\u53d6 \u7ebf\u7a0b\u6c60\u914d\u7f6e
comment.threadpool.max=500
comment.threadpool.core=50
comment.threadpool.alive=3

cc_api_url=http://cs.meishijia.com
#cc_api_url=http://localhost:8080/tzxsaas

# SSO Redirect Url
server.homepage=http://cs.meishijia.com/pages/framework/layout/main.jsp
sso.homepage=http://sso.meishijia.com


#\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u05A7\uFFFD\uFFFD - key
payment_xmd_key=c5d4753ca66cd5442bd7c211e0dbf10f
#\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u05A7\uFFFD\uFFFD - url\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u053B\uFFFD\uFFFD\uFFFD - \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
payment_xmd_url=http://payfront.zc.st.meituan.com
#xmd pay appid
app_id=31101