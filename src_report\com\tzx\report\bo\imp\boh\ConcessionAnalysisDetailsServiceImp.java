package com.tzx.report.bo.imp.boh;

import com.tzx.framework.bo.dto.Roles;
import com.tzx.report.bo.boh.ConcessionAnalysisDetailsService;
import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.po.boh.dao.ConcessionAnalysisDetailsDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */

@Service(ConcessionAnalysisDetailsService.NAME)
public class ConcessionAnalysisDetailsServiceImp implements ConcessionAnalysisDetailsService {


    @Resource(name = ConcessionAnalysisDetailsDao.NAME)
    private ConcessionAnalysisDetailsDao concessionAnalysisDetailsDao;

    @Resource
    private CommonMethodAreaService commonMethodAreaService;

    @Override
    public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
        condition.put("p_day", "");
        String tenancyId = condition.optString("ss_temp_tenentid");
        String employeeId = condition.optString("ss_temp_employeeId");
        Roles roles = this.commonMethodAreaService.getUserRoles(tenancyId, employeeId);
        if(roles != null) {
            int dayType = roles.getReportDayType().intValue();
            if(1 == dayType) {
                int day = roles.getReportDay().intValue();
                if(day > 0) {
                    condition.put("p_day", Integer.valueOf(day));
                }
            }
        }
        return concessionAnalysisDetailsDao.find(tenancyID, condition);
    }

    @Override
    public List<JSONObject> getConcessionTitle(String tenancyID, JSONObject condition) throws Exception{
        return concessionAnalysisDetailsDao.getConcessionTitle(tenancyID, condition);
    }
}
