package com.tzx.report.po.boh.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.BusinessSummaryStatisticsReportDao;
import com.tzx.report.po.boh.dao.BusinessSummaryStatisticshualaishiReportDao;

@Repository(BusinessSummaryStatisticshualaishiReportDao.NAME)
public class BusinessSummaryStatisticshualaishiReportDaoImpl implements BusinessSummaryStatisticshualaishiReportDao{


	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	
	@Override
	public JSONObject getBusinessSummaryStatistics(String tenancyID, JSONObject condition) throws Exception {
		Integer type = condition.optInt("type");
		
		StringBuilder sb =new StringBuilder();
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		String begindate = condition.optString("begin_date");
		String enddate = condition.optString("end_date");
		String separate =condition.optString("separate");
		JSONObject result = new JSONObject();
		String reportSql = "";
		String reportSqlCount = "";
		long total = 0L;
		if(begindate.length()>0 && enddate.length()>0 )
		{
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
				condition.put("rows", "10000");
			}
			switch (type)
			{
				//按日期汇总
				case 1:
					if(condition.optInt("hierarchytype") ==1){
						condition.put("select_type", "RQJGL1");
						reportSql = getQuerySQL(tenancyID, condition );
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							condition.put("sort", "report_date");
							list=this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
						}else{
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							
							condition.put("select_type", "RQJGL0");
							reportSqlCount = getQuerySQL(tenancyID, condition );
							footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString());
						}
						
					}else if(condition.optInt("hierarchytype") ==2 ){
						condition.put("select_type", "RQJGL2");
						reportSql =  getQuerySQL(tenancyID, condition );
						
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
						}else{
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						}
					}
					 
				break;
				//按机构汇总
				case 2:
					if(condition.optInt("hierarchytype") ==1){
						condition.put("select_type", "JGRQL1");
						reportSql =  getQuerySQL(tenancyID, condition );
						condition.put("sort", "store_id");
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							list=this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
						}else{
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							
							condition.put("select_type", "JGRQL0");
							reportSqlCount = getQuerySQL(tenancyID, condition );
							footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString());
						}
						
					}else if(condition.optInt("hierarchytype") ==2 && condition.containsKey("store_id")){
						condition.put("select_type", "JGRQL2");
						
						if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
							condition.put("store_id", condition.optString("store_id").replaceAll("'", ""));
							reportSql =  getQuerySQL(tenancyID, condition );
							condition.put("sort", "report_date");
							list=this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
							structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
						}else{
							reportSql =  getQuerySQL(tenancyID, condition );
							total = this.dao.countSql(tenancyID,reportSql.toString());
							list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
						}
						
					}
					 
				break;
				default:
					break;
			}
		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
 	}
	
	public String getQuerySQL(String tenancyID ,  JSONObject condition) {
		String reportSql = "";
		List<JSONObject> list = new ArrayList<JSONObject>();
		try {
			reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,EngineConstantArea.BUSINESS_COUNUT_HUALAISHI_FUNCTION_F1);
			list = this.dao.query4Json(tenancyID, reportSql);
			if(list.size() > 0) {
				reportSql = list.get(0).getString("f_rpt_hls_business_summary");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return reportSql;
	}
	 
}
