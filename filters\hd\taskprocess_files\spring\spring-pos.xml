<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.1.xsd"
       default-autowire="byName">

    <!-- 配置事物管理器 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <!-- 配置处理事务的通知 -->
    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <!-- 对get/load/search/find开头的方法要求只读事务 -->
            <tx:method name="get*" propagation="SUPPORTS" read-only="true"/>
            <tx:method name="load*" propagation="SUPPORTS" read-only="true"/>
            <tx:method name="search*" propagation="SUPPORTS" read-only="true"/>
            <tx:method name="find*" propagation="SUPPORTS" read-only="true"/>
            <!-- 卡充值、消费、新建事务   -->
            <tx:method name="customerCard*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="requiresnew*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="bonusPointConsume*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="customerCredit*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="coupons*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <!-- 对其它方法要求事务 -->
            <tx:method name="*" propagation="REQUIRED" rollback-for="Exception"/>
        </tx:attributes>
    </tx:advice>
    <aop:config proxy-target-class="true">
        <!-- 前面配置的transactionManager是专对Hibernate的事务管理器, 对所有com.tzx.bo包及其子包下的所有方法添加事务管理 。 -->
        <aop:pointcut id="serviceMethods" expression="execution(* com.tzx.*.bo..*.*(..))"/>
        <!-- 织入 -->
        <aop:advisor advice-ref="txAdvice" pointcut-ref="serviceMethods"/>
    </aop:config>

	<!-- <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate" scope="prototype">
        <property name="dataSource" ref="dynamicDataSource"/>
        <aop:scoped-proxy/>
    </bean> -->
    
   <!--  <bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>java:comp/env/jdbc/multi_tenancydbjndi</value>
        </property>
    </bean> -->
    
    <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource"/>
    </bean>    
    
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <!--建议加Name属性，方便监控报警，如果多个dataSource，主要的dataSource Name为default-->
        <property name="name" value="default"/>
        <!-- 基本属性 url、user、password -->
        <property name="url" value="${jdbc_url}"/>
        <property name="username" value="${jdbc_username}"/>
        <property name="password" value="${jdbc_password}"/>
        <!--  <property name="password" ref="dbPassword"/>  -->
        <!--<property name="password" value="${jdbc_password}"/>-->

        <property name="driverClassName" value="${jdbc_driverClassName}"/>

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="${jdbc_initialSize}"/>
        <property name="minIdle" value="${jdbc_minIdle}"/>
        <property name="maxActive" value="${jdbc_maxActive}"/>

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="${jdbc_maxWait}"/>

        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="${jdbc_timeBetweenEvictionRunsMillis}"/>

        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="${jdbc_minEvictableIdleTimeMillis}"/>

        <property name="testWhileIdle" value="${jdbc_testWhileIdle}"/>
        <property name="testOnBorrow" value="${jdbc_testOnBorrow}"/>
        <property name="testOnReturn" value="${jdbc_testOnReturn}"/>

        <property name="validationQuery" value="${jdbc_validationQuery}"/>
<!--
        <property name="filters" value="stat"/>
-->
        <!-- 如果需要支持utf8mb4编码的话打开 -->
        <!--<property name="connectionInitSqls" value="${jdbc_connectionInitSqls}"/>-->
    </bean>
    <!--

        <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
            <property name="driverClassName" value="org.postgresql.Driver"/>
            <property name="url" value="**************************************************"/>
            <property name="username" value="postgres"/>
            <property name="password" value="q1w2e3r4"/>
        </bean>
    -->
    <bean id="dynamicDataSource"
          class="com.tzx.base.datasource.dao.DynamicDataSource">
        <property name="targetDataSources">
            <map></map>
        </property>
        <property name="defaultTargetDataSource" ref="dataSource"/>
    </bean>

    <!-- Druid Spring statis -->

   <!--  <bean id="druid-stat-interceptor"
          class="com.alibaba.druid.support.spring.stat.DruidStatInterceptor">
    </bean>

    <bean id="druid-stat-pointcut" class="org.springframework.aop.support.JdkRegexpMethodPointcut"
          scope="prototype">
        <property name="patterns">
            <list>
                <value>com.tzx.*.bo.imp.*</value>
                <value>com.tzx.*.po.*.dao.imp.*</value>
            </list>
        </property>
    </bean>

    <aop:config>
        <aop:advisor advice-ref="druid-stat-interceptor" pointcut-ref="druid-stat-pointcut" />
    </aop:config> -->
    
    <!-- 2017-06-21增加，裘浙洪 -->
    <bean id="springUtils" class="com.tzx.pos.base.util.SpringContextUtils"/>
     
</beans>
