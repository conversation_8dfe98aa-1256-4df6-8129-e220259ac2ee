package com.tzx.cc.takeaway.task;

import java.util.List;
import java.util.concurrent.TimeUnit;

import net.sf.json.JSONObject;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.druid.util.StringUtils;
import com.tzx.cc.takeaway.service.CcTokenRefreshTaskRegisterServcie;
import com.tzx.cc.thirdparty.util.ElmUtils;
import com.tzx.framework.common.util.SpringConext;

public class TokenRefreshTask implements Runnable {
	private Logger log = LoggerFactory.getLogger(this.getClass());

	private final String lockKey = "waimai_token_all_lock";// 分布式锁

	@Override
	public void run() {
		RedissonClient redissonClient = (RedissonClient) SpringConext
				.getBean("redissonClient");

		RLock lock = redissonClient.getLock(lockKey);
		try {
			boolean flag = lock.tryLock(2, TimeUnit.MINUTES);
			if (flag) {
				log.info("正在运行token刷新业务....");

				CcTokenRefreshTaskRegisterServcie r = (CcTokenRefreshTaskRegisterServcie) SpringConext
						.getBean("ccTokenRefreshTaskRegisterServcieImpl");

				long currTime = System.currentTimeMillis();

				List<String> taskIdList = r.getReisterTaskByScore(0L, currTime);

				for (String taskId : taskIdList) {
					String taskContext = r.getRegisterTaskById(taskId);
					if (!StringUtils.isEmpty(taskContext)) {
						log.info("任务ID：{},开始进行刷新操作,任务内容：{}   ....", taskId,
								taskContext);

						JSONObject json = JSONObject.fromObject(taskContext);
						String tenanId = json.optString("tencanyId");
						String shopId = json.optString("shopId");

						boolean isSuccess = false;
						try {
							ElmUtils.getToken(tenanId, shopId, true);

							isSuccess = true;
						} catch (Exception ignore) {

						}
						if (isSuccess) {
							r.removeTaskById(taskId);

							r.registerTokenTask(tenanId, shopId);
						}
						log.info("任务ID：{},结束进行刷新操作....", taskId);
					}
				}

			} else {
				log.info("未获取到锁业务结束");
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			log.error("刷新token发生错误->{}", ex.getMessage());
		} finally {

			lock.unlock();
		}

	}

}
