package com.tzx.cc.baidu.bo;

import net.sf.json.JSONObject;

/**
 * 设置结算方式
 * <AUTHOR>
 *
 */
public interface CheckModeService {
	public final String NAME="com.tzx.cc.baidu.bo.CheckModeService";
  
	/**获取结算方式列表
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject loadCheckModeList(String tenancyID,JSONObject condition) throws Exception;
	
  
	/**新增结算方式
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject saveCheckMode(String tenancyID,JSONObject condition) throws Exception;

	/**
	 * 检验机构对应渠道结算方式的唯一性
	 * @param tenantId
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public JSONObject getShopCheckMode(String tenantId, JSONObject obj) throws Exception;


	/**
	 * 查询付款方式
	 * @param tenancyId
	 * @param type
	 * @param param
	 * @return
	 */
	public String findShopPayment(String tenancyId, Integer type, Object param) throws Exception;

	/**
	 * 查询商家实收取整方式
	 * @param tenancyId
	 * @param type
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject findRoundingMode(String tenancyId,Object param)throws Exception;

	/**
	 * 复制结算方式
	 * @param attribute
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public Boolean saveCopyCheckModeInfo(String attribute, JSONObject obj)throws Exception;


	/**
	 * 删除结算方式
	 * @param tenantId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public Object deleteCheckMode(String tenantId, JSONObject params) throws Exception;

	
} 