package com.tzx.cc.datasync.bo.dto.newpos;


// Generated 2014-1-18 2:16:16 by Hibernate Tools 3.4.0.CR1

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * BtYdd generated by hbm2java
 */
public class BtYdd extends BaseEntity implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;

	private String				yddh;

	private String				hybh;

	private String				tel1;

	private Long				men;

	private Long				women;

	private String				ydrq;

	private String				ydbc;

	private Long				ydrs;

	private String				ddsj;

	private String				blrq;

	private String				blsj;

	private String				ddzt;

	private String				czybh;

	private String				czymc;

	private String				bz;
	private String				sex;
	private String				provinces;
	private String				csbz;
	private String				pdsj;
	// 签单时间
	private String				qdsj;
	
	private String              qdsj1;
	
	private String             wcsj;
	
	private String             sbsqsj;
	private String         createUserName;
	
	private String         		ydsj;

	
	private String         		isBooked;
	private String         		isShopOrdered;
	
	
	public String getIsShopOrdered() {
		return isShopOrdered;
	}

	public void setIsShopOrdered(String isShopOrdered) {
		this.isShopOrdered = isShopOrdered;
	}

	public String getIsBooked() {
		return isBooked;
	}

	public void setIsBooked(String isBooked) {
		this.isBooked = isBooked;
	}
	
	public String getYdsj() {
		return ydsj;
	}

	public void setYdsj(String ydsj) {
		this.ydsj = ydsj;
	}

	public String getSbsqsj()
	{
		return sbsqsj;
	}

	public void setSbsqsj(String sbsqsj)
	{
		this.sbsqsj = sbsqsj;
	}

	public String getWcsj()
	{
		return wcsj;
	}

	public void setWcsj(String wcsj)
	{
		this.wcsj = wcsj;
	}

	public String getQdsj1()
	{
		return qdsj1;
	}

	public void setQdsj1(String qdsj1)
	{
		this.qdsj1 = qdsj1;
	}

	private String              yshsj;
	public String getYshsj()
	{
		return yshsj;
	}

	public void setYshsj(String yshsj)
	{
		this.yshsj = yshsj;
	}

	// 签单人编号和名称
	private String				qdrbh;

	private String				qdrmc;
	
    private String              shrbh;
    
    private String              qdrbh1;
    
    private String              qdrmc1;
    private String        createUserId;
    
    public String getCreateUserId()
	{
		return createUserId;
	}

	public void setCreateUserId(String createUserId)
	{
		this.createUserId = createUserId;
	}

	private String     syrbh;
    
    private String     syrmc;
    
  
    
	public String getSyrmc()
	{
		return syrmc;
	}

	public void setSyrmc(String syrmc)
	{
		this.syrmc = syrmc;
	}

	public String getSyrbh()
	{
		return syrbh;
	}

	public void setSyrbh(String syrbh)
	{
		this.syrbh = syrbh;
	}

	public String getQdrmc1()
	{
		return qdrmc1;
	}

	public void setQdrmc1(String qdrmc1)
	{
		this.qdrmc1 = qdrmc1;
	}

	public String getQdrbh1()
	{
		return qdrbh1;
	}

	public void setQdrbh1(String qdrbh1)
	{
		this.qdrbh1 = qdrbh1;
	}

	public String getShrbh()
	{
		return shrbh;
	}

	public void setShrbh(String shrbh)
	{
		this.shrbh = shrbh;
	}
    private String            shrmc;
	public String getShrmc()
	{
		return shrmc;
	}
	

	public void setShrmc(String shrmc)
	{
		this.shrmc = shrmc;
	}

	private String              xsqxrbh;
	private String              xsqxrmc;
	
	private String				needreciept;

	private String				recieptdept;

	private String				paychannel;

	private String				paystatus;

	private String				hardtomove;

	private BigDecimal			totalprice;

	private String				khjlbh;

	private String				khjlmc;

	private String				lxr;

	private String				lxrdh;

	private String				sfyyf;

	private String				sfxyq;

	private String				beforeOrderSource;

	private String				beforeOrderType;

	private String				merchantId;

	private String				shopsId;

	private String				horsemanId;
	
	private String				horsemanName;

	public String getHorsemanName()
	{
		return horsemanName;
	}

	public void setHorsemanName(String horsemanName)
	{
		this.horsemanName = horsemanName;
	}

	private String				memberAddress;

	private String				zlbh;

	private Long				ydzs;

	private String				jk;

	private String				mobile;

	private String				scqy;
	
	private String             complaints;

	public String getComplaints()
	{
		return complaints;
	}

	public void setComplaints(String complaints)
	{
		this.complaints = complaints;
	}

	private String				sfyd;

	private Integer				zkfa;

	private String				ydlx;

	private String				kwxh;

	private String				khtmtz;

	private BigDecimal			yl1;

	private BigDecimal			yl2;

	private String				yl3;

	private String				yl4;

	private String				yl5;

	private Integer				eldernum;

	private Integer				childnum;

	private String				qsbh;
	// 存放商圈
	private String				superdistrict;
	
	 private String             ynkpi;
	 
	 
	 private String            sbsqrbh;
	 private String            sbsqrmc;
	 
	 private String            beforesource;
	 public String getBeforesource()
	{
		return beforesource;
	}

	public void setBeforesource(String beforesource)
	{
		this.beforesource = beforesource;
	}

	public String getSbsqrmc()
	{
		return sbsqrmc;
	}

	public void setSbsqrmc(String sbsqrmc)
	{
		this.sbsqrmc = sbsqrmc;
	}

	public String getSbsqrbh()
	{
		return sbsqrbh;
	}

	public void setSbsqrbh(String sbsqrbh)
	{
		this.sbsqrbh = sbsqrbh;
	}

	private String            failtype;
	public String getFailtype()
	{
		return failtype;
	}

	public void setFailtype(String failtype)
	{
		this.failtype = failtype;
	}

	public String getYnkpi()
	{
		return ynkpi;
	}

	public void setYnkpi(String ynkpi)
	{
		this.ynkpi = ynkpi;
	}

	// 派单人
	private String				pdrbh;

	private String				pdrmc;
	private Integer				parkingnum;

	private String				destinestate;

	private BigDecimal			bzjfs;

	private Integer				cancelType;
	private String				cancelTypeOnline;
	
    private String              xsqxsj;
	
	private String				warning;

	private Integer				shippingmethod;

	private Date				createTime;

	private String				backupReceiver;
	private String				backupReceiverMobile;
    private String              sysj;
	public String getSysj()
	{
		return sysj;
	}

	public void setSysj(String sysj)
	{
		this.sysj = sysj;
	}

	public BtYdd()
	{
	}

	public BtYdd(String yddh, String csbz)
	{
		this.yddh = yddh;
		this.csbz = csbz;
	}

	public BtYdd(String yddh, String hybh, Long men, Long women, String ydrq, String ydbc, Long ydrs, String ddsj, String blrq, String blsj, String ddzt, String czybh, String czymc, String bz, String csbz, String needreciept, String recieptdept, String paychannel, String paystatus,
			String hardtomove, BigDecimal totalprice, String khjlbh, String khjlmc, String lxr, String lxrdh, String sfyyf, String sfxyq, String beforeOrderSource, String beforeOrderType, String merchantId, String shopsId, String horsemanId, String memberAddress, String zlbh, Long ydzs, String jk,
			String mobile, String scqy, String sfyd, Integer zkfa, String ydlx, String kwxh, String khtmtz, BigDecimal yl1, BigDecimal yl2, String yl3, String yl4, String yl5, Integer eldernum, Integer childnum, String qsbh, Integer parkingnum, String destinestate, BigDecimal bzjfs,
			Integer cancelType, String warning, Integer shippingmethod)
	{
		this.yddh = yddh;
		this.hybh = hybh;
		this.men = men;
		this.women = women;
		this.ydrq = ydrq;
		this.ydbc = ydbc;
		this.ydrs = ydrs;
		this.ddsj = ddsj;
		this.blrq = blrq;
		this.blsj = blsj;
		this.ddzt = ddzt;
		this.czybh = czybh;
		this.czymc = czymc;
		this.bz = bz;
		this.csbz = csbz;
		this.needreciept = needreciept;
		this.recieptdept = recieptdept;
		this.paychannel = paychannel;
		this.paystatus = paystatus;
		this.hardtomove = hardtomove;
		this.totalprice = totalprice;
		this.khjlbh = khjlbh;
		this.khjlmc = khjlmc;
		this.lxr = lxr;
		this.lxrdh = lxrdh;
		this.sfyyf = sfyyf;
		this.sfxyq = sfxyq;
		this.beforeOrderSource = beforeOrderSource;
		this.beforeOrderType = beforeOrderType;
		this.merchantId = merchantId;
		this.shopsId = shopsId;
		this.horsemanId = horsemanId;
		this.memberAddress = memberAddress;
		this.zlbh = zlbh;
		this.ydzs = ydzs;
		this.jk = jk;
		this.mobile = mobile;
		this.scqy = scqy;
		this.sfyd = sfyd;
		this.zkfa = zkfa;
		this.ydlx = ydlx;
		this.kwxh = kwxh;
		this.khtmtz = khtmtz;
		this.yl1 = yl1;
		this.yl2 = yl2;
		this.yl3 = yl3;
		this.yl4 = yl4;
		this.yl5 = yl5;
		this.eldernum = eldernum;
		this.childnum = childnum;
		this.qsbh = qsbh;
		this.parkingnum = parkingnum;
		this.destinestate = destinestate;
		this.bzjfs = bzjfs;
		this.cancelType = cancelType;
		this.warning = warning;
		this.shippingmethod = shippingmethod;
	}

	public String getYddh()
	{
		return yddh;
	}

	public void setYddh(String yddh)
	{
		this.yddh = yddh;
	}

	public String getHybh()
	{
		return hybh;
	}

	public void setHybh(String hybh)
	{
		this.hybh = hybh;
	}

	public Long getMen()
	{
		return men;
	}

	public void setMen(Long men)
	{
		this.men = men;
	}

	public String getPdrbh()
	{
		return pdrbh;
	}

	public void setPdrbh(String pdrbh)
	{
		this.pdrbh = pdrbh;
	}

	public String getPdrmc()
	{
		return pdrmc;
	}

	public void setPdrmc(String pdrmc)
	{
		this.pdrmc = pdrmc;
	}

	public String getSuperdistrict()
	{
		return superdistrict;
	}

	public void setSuperdistrict(String superdistrict)
	{
		this.superdistrict = superdistrict;
	}

	public Long getWomen()
	{
		return women;
	}

	public void setWomen(Long women)
	{
		this.women = women;
	}

	public String getYdrq()
	{
		return ydrq;
	}

	public void setYdrq(String ydrq)
	{
		this.ydrq = ydrq;
	}

	public String getYdbc()
	{
		return ydbc;
	}

	public void setYdbc(String ydbc)
	{
		this.ydbc = ydbc;
	}

	public Long getYdrs()
	{
		return ydrs;
	}

	public void setYdrs(Long ydrs)
	{
		this.ydrs = ydrs;
	}

	public String getDdsj()
	{
		return ddsj;
	}

	public void setDdsj(String ddsj)
	{
		this.ddsj = ddsj;
	}

	public String getBlrq()
	{
		return blrq;
	}

	public void setBlrq(String blrq)
	{
		this.blrq = blrq;
	}

	public String getBlsj()
	{
		return blsj;
	}

	public void setBlsj(String blsj)
	{
		this.blsj = blsj;
	}

	public String getDdzt()
	{
		return ddzt;
	}

	public void setDdzt(String ddzt)
	{
		this.ddzt = ddzt;
	}

	public String getCzybh()
	{
		return czybh;
	}

	public void setCzybh(String czybh)
	{
		this.czybh = czybh;
	}

	public String getCzymc()
	{
		return czymc;
	}

	public void setCzymc(String czymc)
	{
		this.czymc = czymc;
	}

	public String getBz()
	{
		return bz;
	}

	public void setBz(String bz)
	{
		this.bz = bz;
	}

	public String getCsbz()
	{
		return csbz;
	}

	public void setCsbz(String csbz)
	{
		this.csbz = csbz;
	}

	public String getNeedreciept()
	{
		return needreciept;
	}

	public void setNeedreciept(String needreciept)
	{
		this.needreciept = needreciept;
	}

	public String getRecieptdept()
	{
		return recieptdept;
	}

	public void setRecieptdept(String recieptdept)
	{
		this.recieptdept = recieptdept;
	}

	public String getPaychannel()
	{
		return paychannel;
	}

	public void setPaychannel(String paychannel)
	{
		this.paychannel = paychannel;
	}

	public String getPaystatus()
	{
		return paystatus;
	}

	public void setPaystatus(String paystatus)
	{
		this.paystatus = paystatus;
	}

	public String getHardtomove()
	{
		return hardtomove;
	}

	public void setHardtomove(String hardtomove)
	{
		this.hardtomove = hardtomove;
	}

	public BigDecimal getTotalprice()
	{
		return totalprice;
	}

	public void setTotalprice(BigDecimal totalprice)
	{
		this.totalprice = totalprice;
	}

	public String getKhjlbh()
	{
		return khjlbh;
	}

	public void setKhjlbh(String khjlbh)
	{
		this.khjlbh = khjlbh;
	}

	public String getKhjlmc()
	{
		return khjlmc;
	}

	public void setKhjlmc(String khjlmc)
	{
		this.khjlmc = khjlmc;
	}

	public String getLxr()
	{
		return lxr;
	}

	public void setLxr(String lxr)
	{
		this.lxr = lxr;
	}

	public String getLxrdh()
	{
		return lxrdh;
	}

	public void setLxrdh(String lxrdh)
	{
		this.lxrdh = lxrdh;
	}

	public String getSfyyf()
	{
		return sfyyf;
	}

	public void setSfyyf(String sfyyf)
	{
		this.sfyyf = sfyyf;
	}

	public String getSfxyq()
	{
		return sfxyq;
	}

	public void setSfxyq(String sfxyq)
	{
		this.sfxyq = sfxyq;
	}

	public String getBeforeOrderSource()
	{
		return beforeOrderSource;
	}

	public void setBeforeOrderSource(String beforeOrderSource)
	{
		this.beforeOrderSource = beforeOrderSource;
	}

	public String getBeforeOrderType()
	{
		return beforeOrderType;
	}

	public void setBeforeOrderType(String beforeOrderType)
	{
		this.beforeOrderType = beforeOrderType;
	}

	public String getMerchantId()
	{
		return merchantId;
	}

	public void setMerchantId(String merchantId)
	{
		this.merchantId = merchantId;
	}

	public String getShopsId()
	{
		return shopsId;
	}

	public void setShopsId(String shopsId)
	{
		this.shopsId = shopsId;
	}

	public String getHorsemanId()
	{
		return horsemanId;
	}

	public void setHorsemanId(String horsemanId)
	{
		this.horsemanId = horsemanId;
	}

	public String getMemberAddress()
	{
		return memberAddress;
	}

	public void setMemberAddress(String memberAddress)
	{
		this.memberAddress = memberAddress;
	}

	public String getZlbh()
	{
		return zlbh;
	}

	public void setZlbh(String zlbh)
	{
		this.zlbh = zlbh;
	}

	public Long getYdzs()
	{
		return ydzs;
	}

	public void setYdzs(Long ydzs)
	{
		this.ydzs = ydzs;
	}

	public String getJk()
	{
		return jk;
	}

	public void setJk(String jk)
	{
		this.jk = jk;
	}

	public String getMobile()
	{
		return mobile;
	}

	public void setMobile(String mobile)
	{
		this.mobile = mobile;
	}

	public String getScqy()
	{
		return scqy;
	}

	public void setScqy(String scqy)
	{
		this.scqy = scqy;
	}

	public String getSfyd()
	{
		return sfyd;
	}

	public void setSfyd(String sfyd)
	{
		this.sfyd = sfyd;
	}

	public Integer getZkfa()
	{
		return zkfa;
	}

	public void setZkfa(Integer zkfa)
	{
		this.zkfa = zkfa;
	}

	public String getYdlx()
	{
		return ydlx;
	}

	public void setYdlx(String ydlx)
	{
		this.ydlx = ydlx;
	}

	public String getKwxh()
	{
		return kwxh;
	}

	public void setKwxh(String kwxh)
	{
		this.kwxh = kwxh;
	}

	public String getKhtmtz()
	{
		return khtmtz;
	}

	public void setKhtmtz(String khtmtz)
	{
		this.khtmtz = khtmtz;
	}

	public BigDecimal getYl1()
	{
		return yl1;
	}

	public void setYl1(BigDecimal yl1)
	{
		this.yl1 = yl1;
	}

	public BigDecimal getYl2()
	{
		return yl2;
	}

	public void setYl2(BigDecimal yl2)
	{
		this.yl2 = yl2;
	}

	public String getYl3()
	{
		return yl3;
	}

	public void setYl3(String yl3)
	{
		this.yl3 = yl3;
	}

	public String getYl4()
	{
		return yl4;
	}

	public void setYl4(String yl4)
	{
		this.yl4 = yl4;
	}

	public String getYl5()
	{
		return yl5;
	}

	public void setYl5(String yl5)
	{
		this.yl5 = yl5;
	}

	public Integer getEldernum()
	{
		return eldernum;
	}

	public void setEldernum(Integer eldernum)
	{
		this.eldernum = eldernum;
	}

	public Integer getChildnum()
	{
		return childnum;
	}

	public void setChildnum(Integer childnum)
	{
		this.childnum = childnum;
	}

	public String getQsbh()
	{
		return qsbh;
	}

	public void setQsbh(String qsbh)
	{
		this.qsbh = qsbh;
	}

	public Integer getParkingnum()
	{
		return parkingnum;
	}

	public void setParkingnum(Integer parkingnum)
	{
		this.parkingnum = parkingnum;
	}

	public String getDestinestate()
	{
		return destinestate;
	}

	public void setDestinestate(String destinestate)
	{
		this.destinestate = destinestate;
	}

	public BigDecimal getBzjfs()
	{
		return bzjfs;
	}

	public void setBzjfs(BigDecimal bzjfs)
	{
		this.bzjfs = bzjfs;
	}

	public Integer getCancelType()
	{
		return cancelType;
	}

	public void setCancelType(Integer cancelType)
	{
		this.cancelType = cancelType;
	}

	public String getWarning()
	{
		return warning;
	}

	public void setWarning(String warning)
	{
		this.warning = warning;
	}

	public Integer getShippingmethod()
	{
		return shippingmethod;
	}

	public void setShippingmethod(Integer shippingmethod)
	{
		this.shippingmethod = shippingmethod;
	}

	public Date getCreateTime()
	{
		return createTime;
	}

	public void setCreateTime(Date createTime)
	{
		this.createTime = createTime;
	}

	public String getBackupReceiver()
	{
		return backupReceiver;
	}

	public void setBackupReceiver(String backupReceiver)
	{
		this.backupReceiver = backupReceiver;
	}

	public String getBackupReceiverMobile()
	{
		return backupReceiverMobile;
	}

	public void setBackupReceiverMobile(String backupReceiverMobile)
	{
		this.backupReceiverMobile = backupReceiverMobile;
	}

	public String getSex()
	{
		return sex;
	}

	public void setSex(String sex)
	{
		this.sex = sex;
	}

	public String getProvinces()
	{
		return provinces;
	}

	public void setProvinces(String provinces)
	{
		this.provinces = provinces;
	}

	public String getPdsj()
	{
		return pdsj;
	}

	public void setPdsj(String pdsj)
	{
		this.pdsj = pdsj;
	}

	public String getTel1()
	{
		return tel1;
	}

	public void setTel1(String tel1)
	{
		this.tel1 = tel1;
	}

	

	

	public String getQdsj()
	{
		return qdsj;
	}

	public void setQdsj(String qdsj)
	{
		this.qdsj = qdsj;
	}

	public String getQdrbh()
	{
		return qdrbh;
	}

	public void setQdrbh(String qdrbh)
	{
		this.qdrbh = qdrbh;
	}

	public String getQdrmc()
	{
		return qdrmc;
	}

	public void setQdrmc(String qdrmc)
	{
		this.qdrmc = qdrmc;
	}
	public String getCancelTypeOnline()
	{
		return cancelTypeOnline;
	}

	public void setCancelTypeOnline(String cancelTypeOnline)
	{
		this.cancelTypeOnline = cancelTypeOnline;
	}
	public String getXsqxsj()
	{
		return xsqxsj;
	}

	public void setXsqxsj(String xsqxsj)
	{
		this.xsqxsj = xsqxsj;
	}
	public String getXsqxrbh()
	{
		return xsqxrbh;
	}

	public void setXsqxrbh(String xsqxrbh)
	{
		this.xsqxrbh = xsqxrbh;
	}

	public String getXsqxrmc()
	{
		return xsqxrmc;
	}

	public void setXsqxrmc(String xsqxrmc)
	{
		this.xsqxrmc = xsqxrmc;
	}
	public String getCreateUserName()
	{
		return createUserName;
	}

	public void setCreateUserName(String createUserName)
	{
		this.createUserName = createUserName;
	}


}
