package com.tzx.boh.bo;

import java.util.List;

import net.sf.json.JSONObject;

public interface MeterPayManagementNewService
{

	   String NAME = "com.tzx.boh.bo.imp.MeterPayManagementNewServiceImpl";
		
		public String loadMeterreadingInformation(String tenancyID,JSONObject condition) throws Exception;
		
		public JSONObject loadAddMeterreadingInformation(String tenancyID,JSONObject condition) throws Exception;

		public boolean checkUnique(String tenentId, JSONObject param)throws Exception;
		
		public void delete(String tenantId, String tableKey, List<JSONObject> keyList) throws Exception;

		public JSONObject findMeterTypeByStoreId(String tenancyId,JSONObject obj)throws Exception;

		public List<JSONObject> findLastDataByStoreId(String tenancyId,
				JSONObject obj)throws Exception;

		public void saveNewInfo(String tenancyId, JSONObject obj)throws Exception;
		
		public JSONObject checkBsDate(String tenancyId, JSONObject obj)throws Exception;

		public void updateUrl(String tenancyId, JSONObject obj) throws Exception;

		public void deleteNew(String tenancyId, JSONObject obj)throws Exception;

		public void updateBeginningUrl(String tenancyId, JSONObject obj)throws Exception;

}
