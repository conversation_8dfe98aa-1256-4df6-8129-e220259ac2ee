package com.tzx.boh.bo.imp;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.boh.bo.GoldReserveManagementService;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;

@Service(GoldReserveManagementService.NAME)
public class GoldReserveManagementServiceImpl implements GoldReserveManagementService
{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	@SuppressWarnings("unchecked")
	@Override
	public JSONObject loadGoldReserveInformation(String tenancyID, JSONObject condition) throws Exception
	{
		
		StringBuilder sql = new StringBuilder();
		JSONObject result = new JSONObject();
		if(!condition.get("organ_code").equals("0")&&!"".equals(condition.get("organ_code")) &&!"0".equals(condition.get("is_zb"))){
			sql.append("select a.*,b.org_full_name from boh_imprest_check A,organ b where b.id=a.store_id");
			sql.append(" and b.organ_code LIKE '"+condition.get("organ_code")+"%' ");
//			sql.append(" and b.id in (select * from get_oids_bycode('"+condition.get("organ_code").toString().trim()+"')) ");
			if (condition.containsKey("t1") )
			{
				sql.append(" and  a.check_date >= TO_DATE('" + condition.get("t1") + "','YYYY-MM-DD') ");
			}
			if (condition.containsKey("t2") )
			{
				sql.append(" and  a.check_date <= TO_DATE('" + condition.get("t2") + "','YYYY-MM-DD') ");
			}
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			Set<String> keys = condition.keySet();
			for(String s:keys)
			{
				if("tableName".equals(s)||"page".equals(s)||"rows".equals(s)||"sort".equals(s)||"order".equals(s)||"sortName".equals(s))
				{
					continue;
				}
				if(!"t1".equals(s)&&!"t2".equals(s)&&!"organ_code".equals(s)&&!"is_zb".equals(s)){
					sql.append(" and a."+s+" like '"+condition.optString(s)+"%'");
				}
				
			}
			if(condition.optString("sort")!=null && !"".equals(condition.optString("sort")))
			{
				sql.append(" order by "+condition.optString("sort")+" "+condition.optString("order"));
			}
			else
			{
				sql.append(" order by tenancy_id");
			}
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
			
			if(list.size()>=1){
			  for(JSONObject resultNew : list){
				  Map<String, Object> map =new HashMap<String, Object>();
				  map.put("cash_holdings_amount", Float.valueOf(resultNew.getString("one_hundred_yuan"))+Float.valueOf(resultNew.getString("fifty_yuan"))
						  +Float.valueOf(resultNew.getString("twenty_yuan")) +Float.valueOf(resultNew.getString("ten_yuan"))+Float.valueOf(resultNew.getString("five_yuan"))
						  +Float.valueOf(resultNew.getString("two_yuan"))+Float.valueOf(resultNew.getString("one_yuan"))+Float.valueOf(resultNew.getString("five_jiao"))
						  +Float.valueOf(resultNew.getString("two_jiao")) +Float.valueOf(resultNew.getString("one_jiao"))
						  );
				  map.put("total_count",map.get("cash_holdings_amount"));
				  map.put("total_money",resultNew.getString("cash_holdings"));
				  map.put("one_hundred_yuan_money",Float.valueOf(resultNew.getString("one_hundred_yuan"))*100);
				  map.put("fifty_yuan_money",Float.valueOf(resultNew.getString("fifty_yuan"))*50);
				  map.put("twenty_yuan_money",Float.valueOf(resultNew.getString("twenty_yuan"))*20);
				  map.put("ten_yuan_money",Float.valueOf(resultNew.getString("ten_yuan"))*10);
				  map.put("five_yuan_money",Float.valueOf(resultNew.getString("five_yuan"))*5);
				  map.put("two_yuan_money",Float.valueOf(resultNew.getString("two_yuan"))*2);
				  map.put("one_yuan_money",Float.valueOf(resultNew.getString("one_yuan"))*1);
				  map.put("five_jiao_money",Float.valueOf(resultNew.getString("five_jiao"))*0.5);
				  map.put("two_jiao_money",Float.valueOf(resultNew.getString("two_jiao"))*0.2);
				  map.put("one_jiao_money",Float.valueOf(resultNew.getString("one_jiao"))*0.1);
				  map.put("adjust_balances",Float.valueOf(resultNew.getString("cash_holdings"))
						  +Float.valueOf(resultNew.getString("j1je"))*Float.valueOf(resultNew.getString("j1sl"))
						  +Float.valueOf(resultNew.getString("j2je"))*Float.valueOf(resultNew.getString("j2sl"))
						  +Float.valueOf(resultNew.getString("j3je"))*Float.valueOf(resultNew.getString("j3sl"))
						  +Float.valueOf(resultNew.getString("j4je"))*Float.valueOf(resultNew.getString("j4sl"))
						  +Float.valueOf(resultNew.getString("j5je"))*Float.valueOf(resultNew.getString("j5sl"))
						  +Float.valueOf(resultNew.getString("j6je"))*Float.valueOf(resultNew.getString("j6sl"))
						  -Float.valueOf(resultNew.getString("j1wsje"))*Float.valueOf(resultNew.getString("j1wjsl"))
						  );
				  resultNew.accumulateAll(map);
	    }
		}
			
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);
			
			}
		return result;
	}

	@Override
	public boolean checkUnique(String tenentId, JSONObject param) throws Exception
	{
		try{

			String check_date = param.optString("check_date");
			String oldId = param.optString("id");
			if (Tools.hv(check_date))
			{
				StringBuilder sql = new StringBuilder();
				sql.append("select id from boh_imprest_check info where info.check_date= '"+check_date+"' and info.store_id="+param.optString("store_id")+"");
				long total = this.dao.countSql(tenentId, sql.toString());
				List<JSONObject> list = this.dao.query4Json(tenentId, this.dao.buildPageSql(param,sql.toString()));
				String id="";
				if(list.size()>=1){
					  for(JSONObject result : list){
		                	id=result.getString("id");
		                }
				}
              
                if(oldId.equals("")&& total <1){
                	return false;
                }
                if(!id.equals("")&&oldId.equals(id)&& total >=1){
                	return false;
                }
                if(!oldId.equals(id)&& total >=1){
                	return true;
                }
                if(!oldId.equals(id)&& total <1){
                	return false;
                }
			}

			return true;
		}catch(Exception e)
		{
			e.printStackTrace();
			return true;
		}
	}

}
