package com.tzx.cc.bo.dto;

import java.io.Serializable;
import java.util.List;
/**
 * 订餐异常原因
 */

public class OrderingAbnormalReason implements Serializable
{
	
	private static final long	serialVersionUID	= 6282289444605552678L;
	
	private String tenancyId;
	
	private Integer id; 
	
	private String unusualType;
	
	private Integer fatherId;
	
	private String reasonName;
	
	private String classItem;
	
	private String phoneticCode;
	
	private String fiveCode;
	
	private String remark;
	
	private String validState;
	
	private String lastOperator;
	
	private String updatetime;
	
	private String code;
	
	private String state;
	
	private List<OrderingAbnormalReason> children;
	
	public String getTenancyId()
	{
		return tenancyId;
	}

	public void setTenancyId(String tenancyId)
	{
		this.tenancyId = tenancyId;
	}


	public String getUnusualType()
	{
		return unusualType;
	}

	public void setUnusualType(String unusualType)
	{
		this.unusualType = unusualType;
	}

	public Integer getFatherId()
	{
		return fatherId;
	}

	public void setFatherId(Integer fatherId)
	{
		this.fatherId = fatherId;
	}

	public String getReasonName()
	{
		return reasonName;
	}

	public void setReasonName(String reasonName)
	{
		this.reasonName = reasonName;
	}

	public String getPhoneticCode()
	{
		return phoneticCode;
	}

	public void setPhoneticCode(String phoneticCode)
	{
		this.phoneticCode = phoneticCode;
	}

	public String getFiveCode()
	{
		return fiveCode;
	}

	public void setFiveCode(String fiveCode)
	{
		this.fiveCode = fiveCode;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public String getValidState()
	{
		return validState;
	}

	public void setValidState(String validState)
	{
		this.validState = validState;
	}

	public String getLastOperator()
	{
		return lastOperator;
	}

	public void setLastOperator(String lastOperator)
	{
		this.lastOperator = lastOperator;
	}

	public String getUpdatetime()
	{
		return updatetime;
	}

	public void setUpdatetime(String updatetime)
	{
		this.updatetime = updatetime;
	}

	public String getCode()
	{
		return code;
	}

	public void setCode(String code)
	{
		this.code = code;
	}


	public List<OrderingAbnormalReason> getChildren()
	{
		return children;
	}

	public void setChildren(List<OrderingAbnormalReason> children)
	{
		this.children = children;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public String getClassItem()
	{
		return classItem;
	}

	public void setClassItem(String classItem)
	{
		this.classItem = classItem;
	}

	public String getState()
	{
		return state;
	}

	public void setState(String state)
	{
		this.state = state;
	}
	
}
