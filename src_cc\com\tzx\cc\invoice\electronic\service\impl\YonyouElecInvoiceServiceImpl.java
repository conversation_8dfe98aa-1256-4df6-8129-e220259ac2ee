package com.tzx.cc.invoice.electronic.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.gson.GsonBuilder;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.Type;
import com.tzx.cc.invoice.electronic.cache.SysparamCache;
import com.tzx.cc.invoice.electronic.cache.YonyouConfigCache;
import com.tzx.cc.invoice.electronic.cont.ElectronicInvoiceConst;
import com.tzx.cc.invoice.electronic.dao.ElecInvoiceDao;
import com.tzx.cc.invoice.electronic.service.ElecInvoiceService;
import com.tzx.cc.invoice.electronic.util.ElectronicInvoiceUtils;
import com.tzx.cc.invoice.electronic.util.UUIDUtils;
import com.tzx.cc.invoice.electronic.util.YonyouElectronicInvoiceWebUtils;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.payment.news.cont.Contant;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 
 * 用友电子发票相关
 * <AUTHOR>
 * 2017-06-28
 */
@Service("com.tzx.cc.invoice.electronic.service.impl.YonyouElecInvoiceServiceImpl")
public class YonyouElecInvoiceServiceImpl implements ElecInvoiceService {
	private static final Logger logger = Logger.getLogger(YonyouElecInvoiceServiceImpl.class);
	
	@Autowired
	private ElecInvoiceDao elecInvoiceDao;
	
	@Resource(name = "genericDaoImpl")
	private GenericDao dao;
	
	@Resource(name="saasRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
	
	/**
	 * 验证data属性
	 * @param data
	 * @param result
	 * @return
	 */
	private boolean validaData(Data data,Data result){
		String tenancyId = data.getTenancy_id();
		if(StringUtils.isBlank(tenancyId)) {
			result.setMsg("参数：tenancy_id不允许为空");
			result.setCode(Contant.ILLEGAL_PARAM);
			logger.info("参数：tenancy_id不允许为空");
			return false;
		}
		return true;
	}
	
	/**
	 * 生成电子发票
	 * @param data
	 * @param result
	 * @throws Exception
	 */
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#issueElectronicInvoice(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void issueElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		String serviceType = param.optString("SERVICE_TYPE");
		if(StringUtils.isBlank(serviceType)) {
			result.setMsg("业务类型不能为空");
            return;
		}
		
		Type type = data.getType();
		
		JSONObject json = elecInvoiceDao.getRequestInfo(data.getTenancy_id(),data.getStore_id());
		if(json==null) {
			result.setMsg("总部未配置纳税人识别号，税率等信息");
			return;
		}

		if(Type.ISSUE_ELECTRONIC_INVOICE.equals(type)) {
			String isEleServiceType = SysparamCache.getSysparam(data.getTenancy_id(), data.getStore_id(), serviceType);
			if(!(StringUtils.equals(isEleServiceType, "1")||StringUtils.equals(isEleServiceType, "4"))) {
				result.setMsg("对应业务类型不支持开通电子发票");
				return;
			}
			ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param);
		} else {
			ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param,"XSF_NSRSBH","XMMC");
		}
		
		
		//验证参数是否全
		boolean validate = validate(param, result,"SERVICE_TYPE","XSF_NSRSBH","data","SL","DDRQ");
		if(!validate) {
			return;
		}
		//查库判断是否已经取消
		validate = issueElectronicInvoiceValid(data,result,param);
		if(!validate) {
			return;
		}
		//组装h5 URL 
		String reqUrl = buildUrl(data,result,param);
		if(StringUtils.isBlank(reqUrl)) {
			return;
		}
		System.out.println(reqUrl);
		
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		JSONObject resultJson = new JSONObject();
		resultJson.put("url", reqUrl);
		returnList.add(resultJson);
		result.setData(returnList);
	}
	
	/**
	 * 
	 * 返回二维码扫码开票URL
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private String buildUrl(Data data, Data result, JSONObject param) throws Exception {
		String respUrl = "";
		JSONObject reqParam = new JSONObject();

		reqParam.put("FPQQLSH", param.optString("FPQQLSH"));//发票请求流水号
		reqParam.put("XSF_NSRSBH", param.optString("XSF_NSRSBH"));//销售方纳税人识别号
		reqParam.put("JSHJ", param.optString("JSHJ"));//价税合计，两位小数。明细价税合计之和必须与总的价税合计一致
		reqParam.put("RQSJ", param.optString("DDRQ"));//消费时间 -订单日期
		reqParam.put("SHMC", data.getTenancy_id());//商户名称
		reqParam.put("URL", YonyouConfigCache.getElementText("native_url"));//开票成功后的回调url,非必填
		reqParam.put("ORGCODE", SysparamCache.getSysparam(data.getTenancy_id(),data.getStore_id(),"dzfp_yydzfp_jgwybs"));

		List<Object> items = new ArrayList<>();
		JSONObject item = new JSONObject();
		item.put("XMMC", param.optString("XMMC"));//项目名称
		item.put("XMSL", 1);//项目数量
		item.put("XMJSHJ", param.optString("JSHJ"));//项目价税合计
		item.put("SL", param.optString("SL"));//税率，6位小数
		items.add(item);
		reqParam.put("items", items);//发票明细

		GsonBuilder builder = new GsonBuilder();
		String reqstr = builder.create().toJson(reqParam);

		JSONObject respResult = YonyouElectronicInvoiceWebUtils.invoice(data.getTenancy_id(),data.getStore_id(),reqstr);
		String code = respResult.optString("code");//状态码
		String msg = respResult.optString("msg");
		if("0000".equals(code)){
			//操作成功
			JSONObject drawObj = respResult.optJSONObject("datas");
			String invoicecode = drawObj.optString("invoicecode");//发票提取码(同发票请求流水号)
			String qrcode = drawObj.optString("qrcode");
			respUrl = qrcode;

			logger.info("调用电子发票扫码开票接口返回：" + msg + "["+code+"] ,发票提取码："+invoicecode);
		}else {
            result.setMsg(msg);
        }
		
		return respUrl;
	}
	
	/**
	 * 验证发票是否符合规则
	 * @param data
	 * @param result
	 * @param param
	 * @return
	 * @throws Exception 
	 */
	private boolean issueElectronicInvoiceValid(Data data, Data result,
			JSONObject param) throws Exception {
		String dh = param.optString("DH");
//		String billNo="";
//		if(dh.contains("@")){
//			billNo=dh.split("@")[0];
////			int  size = elecInvoiceDao.queryInfoSizeByOrderNo(data.getTenancy_id(),dh.split("@")[0]+"%");
////			if(size>0){
////				result.setMsg("此发票单号已经取消.");
////	            return false;
////			}
//		}
		
		JSONObject json = elecInvoiceDao.queryInfoByOrderNo(data.getTenancy_id(),dh);
		String jshj = calsJSHJ(data,result,param);
		String bwflownum = null;
		if(json==null) {
			String FPQQLSH = String.valueOf(UUIDUtils.next());
			param.put("FPQQLSH", FPQQLSH);
			//计算价税合计
			param.put("JSHJ", jshj);
			json = issueElectronicInvoiceSave(data,result,param);
		} else {
            String FPQQLSH = json.optString("invoice_flow_number");
            //如果是待开票，删除之前redis中的发票流水号，再产生一个新的
            if(StringUtils.equals(json.optString("invoice_state"),ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT)) {
            	
            	/*
            	//查询用友接口 wangjh
            	String searchCode="";
            	try {
            		JSONObject respResult = YonyouElectronicInvoiceWebUtils.queryInvoiceStatus(data.getTenancy_id(),json.optInt("organ_id"),FPQQLSH);
            		searchCode= respResult.optString("code","");
        		} catch (Exception e) {
        			// TODO Auto-generated catch block
        			e.printStackTrace();
        		}
            	if(searchCode.equals("1002")||searchCode.equals("9999")){//用友方没有数据 
            		redisTemplate.opsForHash().delete(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE,FPQQLSH);
            		FPQQLSH = String.valueOf(UUIDUtils.next());
            	}else{//用友方有数据
//            		result.setMsg("此发票单号开具中.");
//    	            return false;
            	}
            	*/ 
            	
                redisTemplate.opsForHash().delete(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE,FPQQLSH);
                FPQQLSH = String.valueOf(UUIDUtils.next());
            }
            param.put("FPQQLSH", FPQQLSH);
            json.put("invoice_flow_number",FPQQLSH);
            this.dao.updateIgnorCase(data.getTenancy_id(),"hq_electronic_invoice_info",json);
        }
        json.put("total_tax_amount", jshj);
        if(StringUtils.isBlank(jshj) || StringUtils.equals(jshj, "null")) {
            result.setMsg("计算出来的价税合计为空");
            return false;
        }
        //把计算的价税合计存入param中
		param.put("JSHJ", jshj);
		String state = json.optString("invoice_state");
		if(StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS, state) //取消开票
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL, state)//已取消 业务状态
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS, state)//取消冲正
				) {
			result.setMsg("此发票单号已经取消");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return false;
		}
		if(StringUtils.equals(state, ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS)) {
			result.setMsg("此单号已开具过");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return false;
		}
		
		//二维码打印期限
		String ewmdyxq = SysparamCache.getSysparam(data.getTenancy_id(), "dzfp_ewmdyxq");
		String dataorstr = param.optString("DDRQ");
		if(StringUtils.isBlank(ewmdyxq)) {
			ewmdyxq = "7";
		}
		boolean expire = ElectronicInvoiceUtils.isExpire(dataorstr, Integer.parseInt(ewmdyxq));
		if(expire) {
			result.setMsg("二维码有效期为"+ewmdyxq+"天，现在已经失效了");
			return false;
		}
		
		//向redis中存储流水号和商户号的对应关系
		JSONObject redisJson = new JSONObject();
		redisJson.put("ID", json.optString("id"));
		redisJson.put("ORGAN", data.getStore_id());
		redisJson.put("FPQQLSH", param.optString("FPQQLSH"));
		redisJson.put("XSF_NSRSBH", param.optString("XSF_NSRSBH"));
		redisJson.put("tenancy_id", data.getTenancy_id());
		//设置发票流水号与商户的对应关系到redis中
		redisTemplate.opsForHash().put(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE, redisJson.optString("FPQQLSH"), redisJson.toString());
		//设置期限为24小时
		redisTemplate.expire(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE, ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE_EXPIRE, TimeUnit.HOURS);
		
		return true;
	}
	
	/**
	 * 生成电子发票保存到数据库
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private JSONObject issueElectronicInvoiceSave(Data data, Data result,
			JSONObject param) throws Exception {
		JSONObject json = new JSONObject();
		json.put("tenancy_id", data.getTenancy_id());
		json.put("organ_id", data.getStore_id());
		json.put("invoice_service_type",param.optString("SERVICE_TYPE"));
		//流水号
		json.put("invoice_flow_number",param.optString("FPQQLSH"));
		//纳税识别号
		json.put("tax",param.optString("XSF_NSRSBH"));
		//价税合计
		json.put("total_tax_amount",param.optString("JSHJ"));
		//税率
		json.put("tax_rate", param.optString("SL"));
		//订单号
		json.put("order_code",param.optString("DH"));
		json.put("third_order_code",param.optString("EWMFPQQLSH"));
		json.put("order_date",param.optString("DDRQ"));
        json.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_LP);
		json.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT);
		json.put("electric_use_choose", ElectronicInvoiceConst.ELECTRIC_USE_CHOOSE_YY);//电子发票-用友
		
		String billNo="";
		if(param.optString("DH","").contains("@"))
			billNo=param.optString("DH").split("@")[0];
		json.put("bill_no", billNo);
		
		Integer id = (Integer) dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", json);
		json.put("id", id);
		JSONObject json2 = new JSONObject();
		json2.put("electronic_id", id);
		//税率
		json2.put("tax_rate", param.optString("SL"));
		//项目名称
		json2.put("name", param.optString("XMMC"));
		dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_details", json2);
		return json;
	}
	
	/**
	 * 计算价税合计
	 * @param data
	 * @param result
	 * @param param
	 * @throws Exception 
	 */
	private String calsJSHJ(Data data, Data result, JSONObject param) throws Exception {
	    String paymentclassJeStr = param.optString("data");
	    if(StringUtils.isBlank(paymentclassJeStr)) {
	        return null;
	    }
	    com.alibaba.fastjson.JSONArray paymentclassJeArray = JSON.parseArray(paymentclassJeStr);
	    if(paymentclassJeArray==null || paymentclassJeArray.isEmpty()) {
	        return null;
	    }
	    StringBuffer paymentClassBuffer = new StringBuffer();
	    
	    //如果是门店传递过来的付款方式和金额，不用验证总部这边是否配置，直接把金额加起来即可
	    if(Type.ISSUE_ELECTRONIC_INVOICE_SCC.equals(data.getType())) {
	    	 BigDecimal bigDecimal = new BigDecimal(0);
	 	    //遍历传过来的paymentClass
	 	    for(Object jsonObject :paymentclassJeArray){
	 	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	 	        BigDecimal je = json.getBigDecimal("JE");
	 	        bigDecimal = bigDecimal.add(je);
	 	    }
	 	    if(bigDecimal.compareTo(BigDecimal.ZERO)==0) {
	 	    	return null;
	 	    }
	 	    bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
	    	return bigDecimal.toString();
		} 
	    
	    
	    //遍历paymentclassJeArray 拼接paymentClass的sql条件
	    for(Object jsonObject : paymentclassJeArray){
	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	        if(!json.containsKey("PAYMENT_CLASS") || !json.containsKey("JE")) {
	            return null;
	        }
	        String paymentClass = json.getString("PAYMENT_CLASS");
	        paymentClassBuffer.append(paymentClass).append(",");
	    }
	    String paymentClassStr = paymentClassBuffer.toString();
	    paymentClassStr = paymentClassStr.substring(0,paymentClassStr.length()-1);
	    paymentClassStr = paymentClassStr.replaceAll(",", "','");
	    List<JSONObject> queryPaymentWay = elecInvoiceDao.queryPaymentWay(data.getTenancy_id(),data.getStore_id(),paymentClassStr);
	    
	    BigDecimal bigDecimal = new BigDecimal(0);
	    //遍历传过来的paymentClass
	    for(Object jsonObject :paymentclassJeArray){
	        com.alibaba.fastjson.JSONObject json = (com.alibaba.fastjson.JSONObject) jsonObject;
	        String paymentClass = json.getString("PAYMENT_CLASS");
	        //遍历查出来的paymentClass
	        for(JSONObject queryJson:queryPaymentWay){
	            String ifInvoicing = queryJson.optString("if_invoicing");
	            String queryPaymentClass = queryJson.optString("payment_class");
	            //如果查出来的和传进来的一样并且已开启，就把金额相加
	            if(StringUtils.equals(paymentClass, queryPaymentClass) && StringUtils.equals(ifInvoicing, "1")) {
	                BigDecimal je = json.getBigDecimal("JE");
	                bigDecimal = bigDecimal.add(je);
	                break;
	            }
	        }
	    }
	    if(bigDecimal.compareTo(BigDecimal.ZERO)==0) {
 	    	return null;
 	    }
	    bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
        return bigDecimal.toString();
    }
	
	/**
	 * 验证传进的参数格式是否正确
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public boolean validate(JSONObject param, Data result,
			String... columns) {
		for (String column : columns) {
			if (!param.containsKey(column)
					|| StringUtils.isBlank(param.getString(column))) {
				result.setMsg("参数：" + column + "不允许为空");
				result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_PARAM);
				logger.info("参数：" + column + "不允许为空");
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 
	 * 请求开票 返回待开票界面(输入购买方名称等信息 开票)
	 * 
	 */
	@Override
	public void issueElectronicInvoice(Data data, Data result, HttpServletResponse response) throws Exception {
		issueElectronicInvoice(data, result);
		JSONObject param = getParam(result);
		if(param==null) {
			return;
		}
		String url = param.optString("url");
		response.sendRedirect(url);
	}

	/* (non-Javadoc)
	 * 取消电子发票接口
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#cancelElectronicInvoice(com.tzx.framework.common.entity.Data, com.tzx.framework.common.entity.Data)
	 */
	@Override
	public void cancelElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		JSONObject json = elecInvoiceDao.getRequestInfo(data.getTenancy_id(),data.getStore_id());
		if(json==null) {
		    result.setMsg("取消订单总部未配置纳税人识别号，税率等信息");
		    return;
		}
        ElectronicInvoiceUtils.copySrc2Dest4jsonOption(json, param);
		boolean validate = validate(param, result, "DH","XSF_NSRSBH");
		if(!validate) {
			return;
		}
		JSONObject queryInfoByOrderNo = elecInvoiceDao.queryInfoByOrderNo(data.getTenancy_id(), param.optString("DH"));


		//开票信息不存在 直接在系统中取消
		if(queryInfoByOrderNo==null
				) {
			if(queryInfoByOrderNo==null) {
				result.setMsg("订单号无效，创建一个订单号");
				queryInfoByOrderNo = new JSONObject();
			}
	        queryInfoByOrderNo.put("tenancy_id", data.getTenancy_id());
	        queryInfoByOrderNo.put("organ_id", data.getStore_id());
			queryInfoByOrderNo.put("order_code", param.optString("DH"));
			queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL);
			queryInfoByOrderNo.put("invoice_cancel_time", ElectronicInvoiceUtils.currentTime2Str());
			if(StringUtils.isBlank(queryInfoByOrderNo.optString("id"))) {
				this.dao.insertIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
			} else {
				this.dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);
			}

			List<JSONObject> returnList = new ArrayList<JSONObject>();
	        JSONObject resultJson = new JSONObject();
	        resultJson.put("code", "0000");
			resultJson.put("msg", "取消成功");
			returnList.add(resultJson);
			result.setData(returnList);
			result.setSuccess(Boolean.TRUE);
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
			result.setMsg("调用成功");
		    return;
		}
		String state = queryInfoByOrderNo.optString("invoice_state");
		if(StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_ALREADY_CANCEL, state)
				|| StringUtils.equals(ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS, state)
				) {
			result.setMsg("此发票单号已经取消");
			result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_FAIL);
			return;
		}
		
		String FPQQLSH = queryInfoByOrderNo.optString("invoice_flow_number");
        JSONObject queryJsonObject = YonyouElectronicInvoiceWebUtils.queryInvoiceStatus(data.getTenancy_id(), data.getStore_id(), FPQQLSH);


        if(!StringUtils.equals(queryJsonObject.optString("code"),"0000")) {
            result.setMsg("第三方没有该条记录");
            return;
        }
        if(!StringUtils.equals(queryJsonObject.optString("statuscode"),"4")) {
            JSONObject reqJson = new JSONObject();
            reqJson.put("XSF_NSRSBH",queryInfoByOrderNo.optString("tax"));
            reqJson.put("FPQQLSH",queryInfoByOrderNo.optString("invoice_flow_number"));
            JSONArray reqJsonArray = new JSONArray();
            reqJsonArray.add(reqJson);
            JSONObject cancel = YonyouElectronicInvoiceWebUtils.cancel(data.getTenancy_id(), data.getStore_id(), reqJsonArray.toString());
            if(cancel==null) {
                result.setSuccess(Boolean.FALSE);
                result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_INVALID_CONNECT);
                result.setMsg("连接超时");
                return;
            }
            String code = cancel.optString("code");
            String msg = cancel.optString("msg");

            if(StringUtils.equals("0000",code)) {
                queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_SUCCESS);
                queryInfoByOrderNo.put("invoice_cancel_time",ElectronicInvoiceUtils.currentTime2Str());
            } else {
                queryInfoByOrderNo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_FAIL);
            }
            dao.updateIgnorCase(data.getTenancy_id(), "hq_electronic_invoice_info", queryInfoByOrderNo);

            List<JSONObject> returnList = new ArrayList<JSONObject>();
            JSONObject resultJson = new JSONObject();
            resultJson.put("code", code);
            resultJson.put("msg", msg);
            returnList.add(resultJson);
            result.setData(returnList);
            result.setSuccess(Boolean.TRUE);
            result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
            result.setMsg("调用成功");
            return;
        }

        orderCallback(queryJsonObject.optString("data"),param);

        JSONObject cancelParam = new JSONObject();
        String NEWFPQQLSH = String.valueOf(UUIDUtils.next());
        cancelParam.put("FPQQLSH", NEWFPQQLSH);
        String data3 = queryJsonObject.optString("data");
        com.alibaba.fastjson.JSONObject alijson = com.alibaba.fastjson.JSONObject.parseObject(data3);

        cancelParam.put("fpDm", alijson.getJSONObject("data").getString("fpDm"));
        cancelParam.put("fpHm", alijson.getJSONObject("data").getString("fpHm"));
        JSONObject resultJson = new JSONObject();
        param.put("reqJson",cancelParam);
        logger.info("访问的内容为："+cancelParam.toString());
		hcfpkj(param, resultJson);

		List<JSONObject> returnList = new ArrayList<JSONObject>();
		String code = resultJson.optString("code");
		String msg =resultJson.optString("msg");
//		ElectronicInvoiceUtils.uploadOm(queryInfoByOrderNo);
		resultJson.put("code", code);
		resultJson.put("msg", msg);
		resultJson.remove("RSCODE");
		resultJson.remove("MSG");
		
		returnList.add(resultJson);
		result.setData(returnList);
		result.setSuccess(Boolean.TRUE);
		result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
		result.setMsg("调用成功");
	}

	/**
	 * 发票红冲请求服务
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void hcfpkj(JSONObject param, JSONObject result) throws Exception {
        JSONObject elecInfo = param.optJSONObject("elecInfo");
        JSONObject reqJson = param.optJSONObject("reqJson");
        JSONObject redisJson = new JSONObject();

        redisJson.put("ID", elecInfo.optString("id"));
        redisJson.put("ORGAN", elecInfo.optString("organ_id"));
        redisJson.put("FPQQLSH", reqJson.optString("FPQQLSH"));
        redisJson.put("tenancy_id", elecInfo.optString("tenancy_id"));
        //设置发票流水号与商户的对应关系到redis中
        redisTemplate.opsForHash().put(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE, redisJson.optString("FPQQLSH"), redisJson.toString());
        //设置期限为24小时
        redisTemplate.expire(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE, ElectronicInvoiceConst.ELECTRON_ICINVOICE_REDIS_CODE_EXPIRE, TimeUnit.HOURS);

        JSONArray reqJsonArray = new JSONArray();
        reqJsonArray.add(reqJson);

        //调用用友发票红冲请求
        JSONObject redJo = YonyouElectronicInvoiceWebUtils.red(elecInfo.optString("tenancy_id"),elecInfo.optInt("organ_id"),reqJsonArray.toString());
        logger.info("访问用友电子发票，返回的内容为");
		logger.info(redJo.toString());
		
		String code = redJo.optString("code");
		String msg = redJo.optString("msg");
		//操作成功
		if(StringUtils.equals(code, "0000")) {
			//通过回调URL 更新开票情况
			hcfpkjReturnUpdate(param,reqJson,redJo);
		}
		result.put("code", code);
		result.put("msg", msg);
	}
	
	/**
	 * 
	 * @param param
	 * @param returnContent
	 * @throws Exception
	 */
	private void hcfpkjReturnUpdate(JSONObject param,JSONObject reqJson,JSONObject returnContent) throws Exception {
		JSONObject elecInfo = param.optJSONObject("elecInfo");

        String tenancyId = elecInfo.optString("tenancy_id");

        //红冲后修改原订单的状态和取消开票时间
        elecInfo.put("invoice_state", ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_CANCEL_HPCZ_SUCCESS);
        elecInfo.put("invoice_cancel_time",ElectronicInvoiceUtils.currentTime2Str());
        this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", elecInfo);

        String fplsh = param.optJSONObject("reqJson").optString("FPQQLSH");
        JSONObject elecInfoDetail = this.elecInvoiceDao.queryDetailsByFlowNumber(tenancyId,elecInfo.optInt("id"));
        JSONObject hcelecInfo = this.elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fplsh);
		if(hcelecInfo==null) {
			hcelecInfo = elecInfo;
			hcelecInfo.remove("id");
		}
		//增加红冲记录
		hcelecInfo.put("original_invoice_number", elecInfo.optString("invoice_number"));
		hcelecInfo.put("original_invoice_code", elecInfo.optString("invoice_code"));
		hcelecInfo.remove("invoice_cancel_time");
		hcelecInfo.remove("invoice_time");

		hcelecInfo.put("total_tax_amount", "-"+elecInfo.optString("total_tax_amount"));
		hcelecInfo.put("tax_amount", "-"+elecInfo.optString("tax_amount"));
		hcelecInfo.put("total_amount", "-"+elecInfo.optString("total_amount"));
		hcelecInfo.put("invoice_type", ElectronicInvoiceConst.ELECTRIC_KPLX_HP);
		hcelecInfo.put("invoice_flow_number", fplsh);
		
		if(StringUtils.isBlank(hcelecInfo.optString("id"))) {
			int insertIgnorCase = (int) this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_info", hcelecInfo);
			JSONObject details = new JSONObject();
			details.put("electronic_id", insertIgnorCase);
			details.put("invoice_type",  ElectronicInvoiceConst.ELECTRIC_KPLX_HP);
			details.put("name",  elecInfoDetail.optString("name"));
			details.put("tax_rate",  elecInfo.optString("tax_rate"));
			this.dao.insertIgnorCase(tenancyId, "hq_electronic_invoice_details", details);
		} else {
			this.dao.updateIgnorCase(tenancyId, "hq_electronic_invoice_info", hcelecInfo);
		}
	}
	
	/**
	 * 开票状态查询服务
	 * 
	 */
	@Override
	public void queryElectronicInvoice(Data data, Data result) throws Exception {
		boolean validaData = validaData(data,result);
		if(!validaData) {
			return;
		}
		JSONObject param = getParam(data);
		boolean validate = validate(param, result, "FPQQLSH");
		if(!validate) {
			return;
		}
		
		JSONObject respResult = YonyouElectronicInvoiceWebUtils.queryInvoiceStatus(data.getTenancy_id(),data.getStore_id(),param.optString("FPQQLSH"));
		List<JSONObject> returnList = new ArrayList<JSONObject>();
		JSONObject resultJson = new JSONObject();
		resultJson.put("code", respResult.optString("code"));
		resultJson.put("msg", respResult.optString("msg"));
		returnList.add(respResult);
		result.setData(returnList);
		result.setSuccess(Boolean.TRUE);
		result.setCode(ElectronicInvoiceConst.ELECTRON_ICINVOICE_ERROR_CODE_SUCCESS);
	}

	@Override
	public void sqkp(JSONObject json, JSONObject result) throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public void fpkj(JSONObject json, JSONObject result) throws Exception {
		//..
	}
	
	/**
	 * 获取redis发票流水号所对应的数据
	 * @param fplsh
	 * @return
	 */
	private JSONObject getRedisJson(String fplsh) {
		if(!redisTemplate.hasKey(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE)) {
			logger.info("redis中没有电子发票"+ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE+"所对应的键");
		}
		if(!redisTemplate.opsForHash().hasKey(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE, fplsh)) {
			logger.info("redis中没有电子发票"+fplsh+"所对应的键");
		}
		String redisJsonStr = (String) redisTemplate.opsForHash().get(ElectronicInvoiceConst.YY_ELECTRON_ICINVOICE_REDIS_CODE, fplsh);
		com.alibaba.fastjson.JSONObject alijson = com.alibaba.fastjson.JSONObject.parseObject(redisJsonStr);
		JSONObject redisJson = ElectronicInvoiceUtils.convertAli2netJson(alijson);
		return redisJson;
	}
	

	/**
	 * 验证传进的参数格式是否正确
	 * 
	 * @param param
	 * @param result
	 * @return
	 */
	public boolean validateParam(JSONObject param, JSONObject result,
			String... columns) {
		for (String column : columns) {
			if (!param.containsKey(column)
					|| StringUtils.isBlank(param.getString(column))) {
				result.put("msg","参数：" + column + "不允许为空");
				result.put("code",Contant.ILLEGAL_PARAM);
				logger.info("参数：" + column + "不允许为空");
				return false;
			}
		}
		return true;
	}
	
	/* (non-Javadoc)
	 * @see com.tzx.cc.invoice.electronic.service.ElecInvoiceService#orderCallback(java.lang.String, net.sf.json.JSONObject)
	 */
	@Override
	public void orderCallback(String xmlParam, JSONObject result) throws Exception {
        com.alibaba.fastjson.JSONObject alijson = com.alibaba.fastjson.JSONObject
                .parseObject(xmlParam);
        if(alijson==null) {
            return;
        }
        if(alijson.containsKey("pdf")) {
            alijson.remove("pdf");
        }
        JSONObject jsonObject = ElectronicInvoiceUtils.convertAli2netJson(alijson);
        String code = jsonObject.optString("code");
        String msg = jsonObject.optString("msg");
        String fpqqlsh = jsonObject.optString("fpqqlsh");
        logger.info("用友电子发票回调结果:fpqqlsh="+fpqqlsh+"@code="+code+"@msg="+msg);
        
        if(!StringUtils.equals("0000",code)) {
            logger.info("用友电子发票开票失败，发票请求流水号为"+fpqqlsh);
        }
        JSONObject data = jsonObject.optJSONObject("data");
        JSONObject redisJson = getRedisJson(fpqqlsh);
        String tenancyId = redisJson.optString("tenancy_id");
        DBContextHolder.setTenancyid(tenancyId);

        JSONObject saveJson = elecInvoiceDao.queryInfoByFlowNumber(tenancyId, fpqqlsh);
        fillElecInfo(data,saveJson);
        if(StringUtils.equals(saveJson.optString("invoice_state"),ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_WAIT)) {//取消失败也要加上，因为有可能之前操作过取消并失败了
            saveJson.put("invoice_state",ElectronicInvoiceConst.ELECTRON_ICINVOICE_STATUS_SUCCESS);
            saveJson.put("invoice_type",ElectronicInvoiceConst.ELECTRIC_KPLX_LP);
        }
		if(result!=null) {
			result.put("elecInfo",saveJson);
		}
		ElectronicInvoiceUtils.uploadOm(saveJson);
        this.dao.updateIgnorCase(tenancyId,"hq_electronic_invoice_info",saveJson);
    }




    /**
	 * 获取到Data对象中的data  josn
	 * @param data
	 * @return
	 */
     public void fillElecInfo(JSONObject data, JSONObject saveJson) throws ParseException {
        String fpHm = data.optString("fpHm");
        String fpDm = data.optString("fpDm");
        String jym = data.optString("jym");
        String kprq = data.optString("kprq");
        Date kprqdate = DateUtils.parseDate(kprq, new String[]{"yyyyMMddHHmmss"});
        String kprqstr = DateFormatUtils.format(kprqdate,"yyyy-MM-dd HH:mm:ss");

        String gmfDzdh = data.optString("gmfDzdh");
        String gmfMc = data.optString("gmfMc");
        String gmfNsrsbh = data.optString("gmfNsrsbh");
        String gmfYhzh = data.optString("gmfYhzh");
        String yfpDm = data.optString("yfpDm");
        String hjje = data.optString("hjje");
        String hjse = data.optString("hjse");
        String jshj = data.optString("jshj");
        String kpr = data.optString("kpr");
        String skr = data.optString("skr");
        String fhr = data.optString("fhr");
        String xsfDzdh = data.optString("xsfDzdh");
        String xsfMc = data.optString("xsfMc");
        String xsfYhzh = data.optString("xsfYhzh");

        String xsfNsrsbh = data.optString("xsfNsrsbh");
        String yfpHm = data.optString("yfpHm");

        saveJson.put("invoice_number",fpHm);
        saveJson.put("invoice_code",fpDm);
        saveJson.put("jym",jym);
        saveJson.put("invoice_time",kprqstr);
        saveJson.put("buyer_address",gmfDzdh);
        saveJson.put("buyer_name",gmfMc);
        saveJson.put("buyer_tax",gmfNsrsbh);
        saveJson.put("bank_no",gmfYhzh);
        saveJson.put("original_invoice_code",yfpDm);
        saveJson.put("total_amount",hjje);
        saveJson.put("tax_amount",hjse);
        saveJson.put("total_tax_amount",jshj);
        saveJson.put("drawer",kpr);
        saveJson.put("payee",skr);
        saveJson.put("review_person",fhr);
        saveJson.put("seller_address",xsfDzdh);
        saveJson.put("seller_name",xsfMc);
        saveJson.put("seller_bank_no",xsfYhzh);
    }



	public JSONObject getParam(Data data) {
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		if(list==null || list.isEmpty()) {
			return null;
		}
		// 获取到传入的参数
		JSONObject param = JSONObject.fromObject(list.get(0));
		return param;
	}
	
	private boolean validReturn(JSONObject returnjson, JSONObject result) {
		if(StringUtils.isBlank(returnjson.optString("fpqqlsh"))) {
			result.put("code", 0000);
			result.put("msg", "fpqqlsh 不能为空 ");
			return false;
		}
		
		return true;
	}
}
