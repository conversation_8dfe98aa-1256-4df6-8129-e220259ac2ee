package com.tzx.report.bo.imp.userreport;

import java.util.List;

import javax.annotation.Resource;

import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.report.bo.userreport.UserReportService;
import com.tzx.report.po.userreport.dao.UserReportDao;


@Service(UserReportService.NAME)
public class UserReportServiceImpl implements UserReportService {
	
	@Resource(name = UserReportDao.NAME)
	private UserReportDao userReportDao;
	
	@Override
	public List<JSONObject> menu(String tenancyID, JSONObject condition)
			throws Exception {
		return userReportDao.menu(tenancyID, condition);
	}

	@Override
	public JSONObject indexTarget(String tenancyID, JSONObject condition)
			throws Exception {
		DBContextHolder.setTenancyid(tenancyID);

		return userReportDao.indexTarget(tenancyID, condition);
	}

	@Override
	public String getNoticeInfoForHomePage(String tenancyId,
			JSONObject condition) throws Exception {
		return userReportDao.getNoticeInfoForHomePage(tenancyId, condition);
	}
	
	@Override
	public JSONObject news(String tenancyID, JSONObject condition)
			throws Exception {
		DBContextHolder.setTenancyid(tenancyID);

		return userReportDao.news(tenancyID, condition);
	}
	
	@Override
	public JSONObject newsDetails(String tenancyID, JSONObject condition)
			throws Exception {
		return userReportDao.newsDetails(tenancyID, condition);
	}

	@Override
	public List<JSONObject> hotNews(String tenancyID, JSONObject condition)
			throws Exception {
		return userReportDao.hotNews(tenancyID, condition);
	}
}
