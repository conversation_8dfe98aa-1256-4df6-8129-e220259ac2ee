package com.tzx.cc.common.redis.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;

import com.alibaba.druid.util.StringUtils;
import com.tzx.cc.common.redis.service.CcRedisService;
import com.tzx.framework.common.util.SpringConext;

import net.sf.json.JSONObject;

public abstract class AbstractCcRedisCommonServiceImpl implements
		CcRedisService {
	/**
	 * 获取不同redis的对象
	 * @return
	 */
	protected abstract RedisTemplate<String, String> getRedisTemplate();
	
	public void saveBykv(final String key, final String value, final int expires)
			throws Exception {
		// if (expires > 0) {
		// redisTemplate.opsForValue().set(key, value, expires,
		// TimeUnit.SECONDS);
		// } else {
		// redisTemplate.opsForValue().set(key, value);
		// }
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				connection.set(key.getBytes(), value.getBytes());
				if (expires > 0) {
					connection.expire(key.getBytes(), expires);
				}
				return null;
			}
		});
	}

	@Override
	public String getByKey(final String key) throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		// return (String) redisTemplate.opsForValue().get(key);
		return redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {

				byte[] value = connection.get(key.getBytes());

				if (value == null) {
					return null;
				}
				return new String(value);
			}
		});
	}

	@Override
	public void del(final String key) throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		// redisTemplate.opsForValue().getOperations().delete(key);
		redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				connection.del(key.getBytes());
				return null;
			}
		});
	}

	@Override
	public void saveByMap(final String entryKey, final Map<String, String> map)
			throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				Iterator<Map.Entry<String, String>> iter = map.entrySet()
						.iterator();

				while (iter.hasNext()) {
					Map.Entry<String, String> entry = iter.next();

					connection.hSet(entryKey.getBytes(), entry.getKey()
							.getBytes(), entry.getValue().getBytes());
				}
				return null;
			}
		});
	}

	@Override
	public String getByMapField(final String entryKey, final String field)
			throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		return redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				byte[] value = connection.hGet(entryKey.getBytes(),
						field.getBytes());

				if (value == null) {
					return null;
				}

				return new String(value);
			}
		});
	}

	@Override
	public void delByMapField(final String entryKey, final String... fields)
			throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				for (String field : fields) {
					connection.hDel(entryKey.getBytes(), field.getBytes());
				}

				return null;
			}
		});

	}

	@Override
	public List<String> getByPatternKey(final String patternKey)
			throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		final List<String> list = new ArrayList<String>();

		redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				Set<byte[]> set = connection.keys(patternKey.getBytes());

				Iterator<byte[]> iter = set.iterator();
				while (iter.hasNext()) {
					byte[] bt = iter.next();
					list.add(new String(bt));
				}

				return null;
			}
		});

		return list;
	}

	@Override
	public void rightPush(final String key, final String value)
			throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				return connection.rPush(key.getBytes(), value.getBytes()) + "";
			}
		});
	}
	
	@Override
	public void leftPush(final String key, final String value)
			throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				return connection.lPush(key.getBytes(), value.getBytes()) + "";
			}
		});
	}

	@Override
	public String leftPop(final String key) throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		return redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				byte[] bt = connection.lPop(key.getBytes());

				if (bt == null) {
					return null;
				}

				return new String(bt);
			}
		});
	}
	
	@Override
	public String rightPop(final String key) throws Exception {
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		return redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				byte[] bt = connection.rPop(key.getBytes());

				if (bt == null) {
					return null;
				}

				return new String(bt);
			}
		});
	}

	@Override
	public long getListSize(final String key) throws Exception {
		if (StringUtils.isEmpty(key)) {
			return 0;
		}
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		String len = redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {

				long len = connection.lLen(key.getBytes());
				return len + "";
			}
		});

		return Long.valueOf(len);
	}
	
	@Override
	public List<String> range(final String key, final long start, final long end) {
		final RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		return redisTemplate.execute(new RedisCallback<List<String>>() {
			@Override
			public List<String> doInRedis(RedisConnection connection) throws DataAccessException {
				List<byte[]> bytes = connection.lRange(key.getBytes(), start, end);
				List<String> listreturn = new ArrayList<>();
				for (byte[] b : bytes) {
					String obj = redisTemplate.getStringSerializer().deserialize(b);
					listreturn.add(obj);
				}
				return listreturn;
			}
		});
	}
	
	public void zsetSave(final String key,final long score,final String value){
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				connection.zAdd(key.getBytes(), score, value.getBytes());
				return null;
			}
		});
	}
	
	public List<String> zrange(final String key,final long startIndex,final long endIndex){
		final RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		return redisTemplate.execute(new RedisCallback<List<String>>() {
			@Override
			public List<String> doInRedis(RedisConnection connection) throws DataAccessException {
				Set<byte[]> bytes = connection.zRange(key.getBytes(), startIndex, endIndex);
				List<String> listreturn = new ArrayList<>();
				for (byte[] b : bytes) {
					String obj = redisTemplate.getStringSerializer().deserialize(b);
					listreturn.add(obj);
				}
				return listreturn;
			}
		});
	}
	
	public List<String> zrangebyscore(final String key,final long startScore,final long endScore){
		final RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		return redisTemplate.execute(new RedisCallback<List<String>>() {
			@Override
			public List<String> doInRedis(RedisConnection connection) throws DataAccessException {
				Set<byte[]> bytes = connection.zRangeByScore(key.getBytes(), startScore, endScore);
				List<String> listreturn = new ArrayList<>();
				for (byte[] b : bytes) {
					String obj = redisTemplate.getStringSerializer().deserialize(b);
					listreturn.add(obj);
				}
				return listreturn;
			}
		});
	}
	
	public void removeZsetByValue(final String key,final String value){
		RedisTemplate<String, String> redisTemplate = getRedisTemplate();
		
		redisTemplate.execute(new RedisCallback<String>() {
			@Override
			public String doInRedis(RedisConnection connection)
					throws DataAccessException {
				connection.zRem(key.getBytes(), value.getBytes());
				return null;
			}
		});
	}
}
