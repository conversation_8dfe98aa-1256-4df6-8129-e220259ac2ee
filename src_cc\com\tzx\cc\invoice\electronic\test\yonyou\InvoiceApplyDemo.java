package com.tzx.cc.invoice.electronic.test.yonyou;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import com.google.gson.GsonBuilder;
import com.tzx.cc.invoice.electronic.cache.YonyouConfigCache;
import com.tzx.cc.invoice.electronic.util.JwtParamBuilder;
import com.tzx.cc.invoice.electronic.util.UUID4EWMUtils;
import com.tzx.framework.common.util.DateUtil;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.compression.CompressionCodecs;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

/**
 * 
 * 
 * <AUTHOR>
 *
 */
public class InvoiceApplyDemo {
	private static final Logger logger = Logger.getLogger(InvoiceApplyDemo.class);
	private String encodeCharacter = "UTF-8";
	
	public static void main(String[] args) {
		try {
//			new InvoiceApplyDemo().callInvoiceApply(); //{"code":"0000","msg":"success"}
			new InvoiceApplyDemo().queryInvoiceStatus();
//			new InvoiceApplyDemo().callQRInvoiceApply();
			//System.out.println(String.valueOf(UUIDUtils.next()));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 
	 * 请求二维码扫码开票接口
	 * {
     *   "code": "0000",
  	 *	 "msg": "操作成功",
     *   "datas": {
     *		"invoicecode": "798797825254998016",
     *		"qrcode": "http://www.yesfp.com:80/mobileinvoice/index.html?lsh=Yk7IzFyYfngwjAWdarGXTr5oa+HA+Iv9&corp=24d0dcd1-7ec5-46a6-843d-224ccf574a0d"
     *   }
     * }
	 * @return
	 * @throws Exception
	 */
	public JSONObject callQRInvoiceApply() throws Exception {
		JSONObject resultJo = new JSONObject();
		
		String root_url = YonyouConfigCache.getElementText("root_url");
		String appid = YonyouConfigCache.getElementText("app_id");
		String url = root_url + YonyouConfigCache.getElementText("total_path") + "/insertForQRInvoice?"
				+ "appid=" + appid + "&ts=" + DateUtil.getYYYYMMDDHHMMSS(new Date());
		HttpClient httpClient = HttpClients.custom().build();
		HttpPost httpPost = new HttpPost(url);

		// 构造POST请求体
		String body = this.buildQrRequestDatas();
		System.out.println(body);
		
		// 签名
		String sign = this.sign(body);
		httpPost.addHeader("sign", sign);
		httpPost.addHeader(HTTP.CONTENT_TYPE, "application/json");
		StringEntity se = new StringEntity(body.toString(), "UTF-8");
		se.setContentType("text/json");
		se.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
		httpPost.setEntity(se);
		// 发送http post请求，并得到响应结果
		HttpResponse response = httpClient.execute(httpPost);
		String result = "";
		if (response != null) {
			HttpEntity resEntity = response.getEntity();
			if (resEntity != null) {
				result = EntityUtils.toString(resEntity, "UTF-8");
				//System.out.println(result);
			}
		}
		
		logger.info("请求响应结果："+result);
		
		resultJo = JSONObject.fromObject(result, new JsonConfig());
		return resultJo;
	}
	
	public void callInvoiceApply() throws Exception {
		//请求开票服务
		String root_url = YonyouConfigCache.getElementText("root_url");
		String appid = YonyouConfigCache.getElementText("app_id");
		String url = root_url + YonyouConfigCache.getElementText("total_path") + "/insertWithArray?"
				+ "appid=" + appid;
		
		
		HttpClient httpClient = HttpClients.custom().build();
		HttpPost httpPost = new HttpPost(url);
		
		// 构造POST表单Map
		String oper = "ISSUE";
		Map<String, String> paramsMap = buildPostParam(oper);
		
		// 签名
		String sign = this.sign(paramsMap);
		httpPost.addHeader("sign", sign);
		
		// 转换POST表单参数
		List<NameValuePair> list = new ArrayList<NameValuePair>();
		Iterator<Entry<String, String>> iterator = paramsMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, String> elem = iterator.next();
			list.add(new BasicNameValuePair(elem.getKey(), elem.getValue()));
		}
		if (list.size() > 0) {
			UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, encodeCharacter);
			httpPost.setEntity(entity);
		}

		// 发送http post请求，并得到响应结果
		HttpResponse response = httpClient.execute(httpPost);
		String result = "";
		if (response != null) {
			HttpEntity resEntity = response.getEntity();
			if (resEntity != null) {
				result = EntityUtils.toString(resEntity, encodeCharacter);
				System.out.println(result);
			}
		}
	}
	
	@org.junit.Test
	public void queryInvoiceStatus() throws Exception {
		String root_url = YonyouConfigCache.getElementText("root_url");
		String appid = YonyouConfigCache.getElementText("app_id");
		String url = root_url + YonyouConfigCache.getElementText("total_path") + "/queryInvoiceStatus?"
				+ "appid=" + appid;
		
		HttpClient httpClient = HttpClients.custom().build();
		HttpPost httpPost = new HttpPost(url);
		
		// 构造POST表单Map
		String oper = "query";
		Map<String, String> paramsMap = buildPostParam(oper);
		
		// 签名
		String sign = this.sign(paramsMap);
		httpPost.addHeader("sign", sign);

		// 转换POST表单参数
		List<NameValuePair> list = new ArrayList<NameValuePair>();
		Iterator<Entry<String, String>> iterator = paramsMap.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<String, String> elem = iterator.next();
			list.add(new BasicNameValuePair(elem.getKey(), elem.getValue()));
		}
		if (list.size() > 0) {
			UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, encodeCharacter);
			httpPost.setEntity(entity);
		}

		// 发送http post请求，并得到响应结果
		HttpResponse response = httpClient.execute(httpPost);
		String result = "";
		if (response != null) {
			HttpEntity resEntity = response.getEntity();
			if (resEntity != null) {
				result = EntityUtils.toString(resEntity, encodeCharacter);
				System.out.println(result);
			}
		}
	}
	
	/**
	 * 获取发票请求流水号
	 * 商户号 + 订单号
	 * @return 发票请求流水号
	 */
	private String buildEWMFpqqlsh() {
		//第一次开票成功 06822017062811300004
		//第二次开票 06822017062811300007
		return String.valueOf(UUID4EWMUtils.next());
	}
	
	/**
	 * 获取发票请求流水号
	 * 商户号 + 订单号
	 * @return 发票请求流水号
	 */
	private String buildFpqqlsh() {
		//第一次开票成功 06822017062811300004
		//第二次开票 06822017062811300007
		//return String.valueOf(UUIDUtils.next());
		return "2017070718033600004";
	}

	/**
	 * 签名
	 * 
	 * @param params
	 *            表单参数
	 * @return 签名值
	 * @throws Exception
	 */
	private String sign(String params) throws Exception {

		// 读取CA证书与PEM格式证书需要根据实际证书使用情况而定,目前这两种都支持
		PrivateKey privateKey = loadPrivateKeyOfCA();
		// PrivateKey privateKey = loadPrivateKeyOfPem();

		Map<String, Object> claims = JwtParamBuilder.build().setSubject("tester").setIssuer("einvoice")
				.setAudience("einvoice").addJwtId().addIssuedAt().setExpirySeconds(300).setNotBeforeSeconds(300)
				.getClaims();

		// 需要将表单参数requestdatas的数据进行md5加密，然后放到签名数据的requestdatas中。
		// 此签名数据必须存在，否则在验证签名时会不通过。
		claims.put("requestdatas", getMD5(params));

		String compactJws = Jwts.builder().signWith(SignatureAlgorithm.RS512, privateKey).setClaims(claims)
				.compressWith(CompressionCodecs.DEFLATE).compact();

		return compactJws;
	}

	/**
	 * 签名
	 * 
	 * @param paramsMap
	 *            表单参数
	 * @return 签名值
	 * @throws Exception
	 */
	private String sign(Map<String, String> paramsMap) throws Exception {

		// 读取CA证书与PEM格式证书需要根据实际证书使用情况而定,目前这两种都支持
		PrivateKey privateKey = loadPrivateKeyOfCA();
		// PrivateKey privateKey = loadPrivateKeyOfPem();

		Map<String, Object> claims = JwtParamBuilder.build().setSubject("tester").setIssuer("einvoice")
				.setAudience("einvoice").addJwtId().addIssuedAt().setExpirySeconds(300).setNotBeforeSeconds(300)
				.getClaims();

		// 需要将表单参数requestdatas的数据进行md5加密，然后放到签名数据的requestdatas中。
		// 此签名数据必须存在，否则在验证签名时会不通过。
		String value = paramsMap.get("requestdatas");
		if(value != null && value.length()>0){
			claims.put("requestdatas", getMD5(value));
		}
		
		// 使用jdk1.6版本时，删除下面代码的中.compressWith(CompressionCodecs.DEFLATE)
		String compactJws = Jwts.builder().signWith(SignatureAlgorithm.RS512, privateKey).setClaims(claims)
				.compressWith(CompressionCodecs.DEFLATE).compact();

		return compactJws;
	}

	/**
	 * 计算MD5
	 * 
	 * @param str
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws NoSuchAlgorithmException
	 */
	private String getMD5(String str) throws UnsupportedEncodingException, NoSuchAlgorithmException {
		byte[] buf = null;
		buf = str.getBytes("utf-8");
		MessageDigest md5 = null;
		md5 = MessageDigest.getInstance("MD5");
		md5.update(buf);
		byte[] tmp = md5.digest();
		StringBuilder sb = new StringBuilder();
		for (byte b : tmp) {
			sb.append(String.format("%02x", b & 0xff));
		}
		return sb.toString();
	}
	
	/**
	 * 读取证书私钥
	 * 
	 * @return
	 * @throws UnrecoverableKeyException
	 * @throws KeyStoreException
	 * @throws NoSuchAlgorithmException
	 * @throws CertificateException
	 * @throws IOException
	 */
	protected PrivateKey loadPrivateKeyOfCA() throws UnrecoverableKeyException, KeyStoreException,
			NoSuchAlgorithmException, CertificateException, IOException {
		String keypath = "D:\\keys\\yongyou.pfx";
		FileInputStream in = new FileInputStream(keypath);
		KeyStore ks = KeyStore.getInstance("pkcs12");
		String pwd = "password";//"123456"; 证书密码
		ks.load(in, pwd.toCharArray());
		String alias = ks.aliases().nextElement();
		PrivateKey caprk = (PrivateKey) ks.getKey(alias, pwd.toCharArray());
		return caprk;
	}

	/**
	 * post表单数据
	 * 
	 * @return
	 */
	private Map<String, String> buildPostParam(String oper) {
		Map<String, String> paramsMap = new HashMap<String, String>();
		//开具
		if("ISSUE".equalsIgnoreCase(oper)){
			paramsMap.put("requestdatas", this.buildRequestDatas());
		//	paramsMap.put("email", this.buildEmailConfigs());
		//	paramsMap.put("sms", this.buildSmsConfigs());
		//	paramsMap.put("url", this.buildUrlConfigs());
			paramsMap.put("autoAudit", "true");//开启自动审核
		}else if("Cancle".equalsIgnoreCase(oper)){
			
		}else if("query".equalsIgnoreCase(oper)){
			paramsMap.put("fpqqlsh", buildFpqqlsh());
		}else if("Create".equalsIgnoreCase(oper)){
			
		}
		

		return paramsMap;
	}

	/**
	 * url回调配置
	 * 
	 * @return
	 */
	private String buildUrlConfigs() {
		List<Object> datas = new ArrayList<>();
		Map<String, Object> data = new HashMap<>();
		data.put("fpqqlsh", buildFpqqlsh());
		data.put("url", "http://127.0.0.1:7787/EinvoiceRESTService/CallBackEInvoices/");
		datas.add(data);

		GsonBuilder builder = new GsonBuilder();
		return builder.create().toJson(datas);
	}

	/**
	 * 构造短信发送信息
	 * 
	 * @return
	 */
	private String buildSmsConfigs() {
		List<Object> datas = new ArrayList<>();
		Map<String, Object> data = new HashMap<>();
		data.put("fpqqlsh", buildFpqqlsh());
		data.put("address", "13511111111,13511111112");
		datas.add(data);

		GsonBuilder builder = new GsonBuilder();
		return builder.create().toJson(datas);
	}

	/**
	 * 构造email发送信息
	 * 
	 * @return
	 */
	private String buildEmailConfigs() {
		List<Object> datas = new ArrayList<>();
		Map<String, Object> data = new HashMap<>();
		data.put("fpqqlsh", buildFpqqlsh());
		data.put("address", "<EMAIL>");
		datas.add(data);

		GsonBuilder builder = new GsonBuilder();
		return builder.create().toJson(datas);
	}

	/**
	 * 构造requestdatas
	 * 
	 * @return
	 */
	private String buildQrRequestDatas() {
		Map<String, Object> data = new HashMap<>();
		data.put("FPQQLSH", buildEWMFpqqlsh());//发票请求流水号
		data.put("XSF_NSRSBH", "111222333456111");//销售方纳税人识别号
		data.put("JSHJ", 117);//价税合计，两位小数。明细价税合计之和必须与总的价税合计一致
		//data.put("LYID", "");//来源ID，订单编号
		data.put("ORGCODE", "Y003");//门店标识，开票点编码，支持一个公司多个开票点场景。如果为空，获取默认的开票点
		data.put("RQSJ", "2017-06-29");//消费时间 -订单日期
		data.put("SHMC", "TZX测试门店2");//商户名称
//		data.put("BZ", "");//备注
//		data.put("EMAIL", "");//收票邮箱,非必填
//		data.put("URL", "");//开票成功后的回调url,非必填
		//data.put("GMF_MC", "测试1");//购买方名称,非必填
//		data.put("GMF_NSRSBH", "");//购买方纳税人识别号,非必填
//	    data.put("GMF_DZDH", "");//购买方地址电话,非必填
//		data.put("GMF_YHZH", "");//购买方银行账号,非必填
		
		data.put("items", buildItems());//发票明细
		
		GsonBuilder builder = new GsonBuilder();
		return builder.create().toJson(data);
	}

	private String buildRequestDatas() {
		List<Object> datas = new ArrayList<>();
		Map<String, Object> data = new HashMap<>();
		data.put("FPQQLSH", buildFpqqlsh());//发票请求流水号
		data.put("XSF_NSRSBH", "111222333456111");//纳税人识别税号
		data.put("GMF_MC", "测试1");//购买方名称
		data.put("JSHJ", 117);//价税合计，两位小数。明细价税合计之和必须与总的价税合计一致
		data.put("ORGCODE", "Y003");//开票点编码，支持一个公司多个开票点场景。如果为空，获取默认的开票点
		data.put("items", buildItems());//发票明细
		datas.add(data);

		GsonBuilder builder = new GsonBuilder();
		return builder.create().toJson(datas);
		
	}
	
	/**
	 * 构造request发票明细
	 * 
	 * @return
	 */
	private List<Object> buildItems() {
		List<Object> items = new ArrayList<>();
		Map<String, Object> data = new HashMap<>();
		data.put("XMMC", "项目名称");
		data.put("XMSL", 1);
		data.put("XMJSHJ", 117);
		data.put("SL", 0.17);
		items.add(data);
		return items;
	}

}