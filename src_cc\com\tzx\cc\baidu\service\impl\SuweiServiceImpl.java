package com.tzx.cc.baidu.service.impl;

import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.cc.baidu.cont.SuweiConst;
import com.tzx.cc.baidu.log.ResultThreadlocalUtil;
import com.tzx.cc.baidu.service.SuweiService;
import com.tzx.cc.baidu.util.SuweiPropertyUtil;
import com.tzx.cc.bo.OrderManagementHqService;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.weixin.bo.NewSendTemplateMasgService;
import com.tzx.weixin.po.redis.dao.RedisTemplateDao;
/**
 * <AUTHOR>
 *
 */
@Service(SuweiService.NAME)
public class SuweiServiceImpl implements SuweiService{
	@Resource(name = "genericDaoImpl")
	private GenericDao		dao;
	
	@Resource(name = RedisTemplateDao.NAME)
	private RedisTemplateDao redisDao;
	
	private static final Logger logger = Logger
			.getLogger(SuweiServiceImpl.class);
	
	@Resource(name = NewSendTemplateMasgService.NAME)
	private NewSendTemplateMasgService newSendTemplateMasgService;
	
	@Resource(name = OrderManagementHqService.NAME)
	private OrderManagementHqService orderManagementHqService;

	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.service.SuweiService#thirdSuweiApplayOrder(net.sf.json.JSONObject, net.sf.json.JSONObject)
	 */
	@Override
	public JSONObject thirdSuweiApplayOrder(JSONObject param, JSONObject result)
			throws Exception {
		String orderNo = param.optString("order_code");
		String tenantId = param.optString("tenant_id");
		JSONObject json = new JSONObject();
		json.put("package", param.optString("package"));
		json.put("deliver_now", param.optString("deliver_now"));
		if(StringUtils.isBlank(json.optString("package"))) {
			json.put("package", SuweiConst.N);
		}
		if(StringUtils.isBlank(json.optString("deliver_now"))) {
			json.put("package", SuweiConst.Y);
		}
		List<JSONObject> queryMeals = queryMeals(tenantId,orderNo);
		json.put("meals", queryMeals);
		return json;
	}

	/**
	 * 生成订单并申请速位平台订单专用，不允许其他任何地方调用
	 * @param json
	 * @return
	 */
	public String thirdSuweiApplayOrderReq(JSONObject param,JSONObject json) {
//		String resultstr = HttpUtil.sendPostRequest(SuweiPropertyUtil.getMsg(SuweiConst.THIRD_SUWEI_APPLAY_ORDER), json);
		//模拟已经掉通速位接口
		String resultstr="{\"oid\":\"323223\"}";
		long currentTimeMillis = System.currentTimeMillis();
		if(currentTimeMillis%2==1) {
			resultstr = StringUtils.EMPTY;
		} else {
			resultstr = "{\"oid\":\""+currentTimeMillis+"\"}";
		}
		ResultThreadlocalUtil.set(resultstr);
		return resultstr;
	}
	
	@Override
	public void thirdSuweiApplayOrderAfter(JSONObject jsobj,
			JSONObject responseJson, String result) throws Exception {
		com.alibaba.fastjson.JSONObject resultJson = com.alibaba.fastjson.JSONObject
				.parseObject(result);
		boolean containsKey = false;
		if(resultJson!=null) {
			containsKey = resultJson.containsKey("oid");
		}
		if(containsKey) {
			String oid = resultJson.getString("oid");
			//将速位的id所对应的商户号存到redis缓存中
			redisDao.set(SuweiConst.REDIS_KEY_NAME_PRIVISOUS_SUWEI+oid, jsobj.optString("tenant_id"));
			//设置过期时间为1天
			redisDao.expire(SuweiConst.REDIS_KEY_NAME_PRIVISOUS_SUWEI+oid, SuweiConst.REDIS_EXPIRE_SUWEI);
			
			//将速位ID存入数据库中
			JSONObject json = new JSONObject();
			String orderCode = jsobj.optString("order_code");
			String sql = "select * from cc_order_list_suwei where order_code = ?";
			List<JSONObject> query4Json = dao.query4Json(jsobj.optString("tenant_id"), sql,new Object[]{orderCode});
			if(!query4Json.isEmpty()) {
				json = query4Json.get(0);
				
			}
			
			json.put("order_code", orderCode);
			json.put("suwei_order_id", oid);
			json.put("tenancy_id", jsobj.optString("tenant_id"));
			if(query4Json.isEmpty()) {
				dao.insertIgnorCase(jsobj.optString("tenant_id"), "cc_order_list_suwei", json);
			} else {
				dao.updateIgnorCase(jsobj.optString("tenant_id"), "cc_order_list_suwei", json);
			}
		}
		
	}
	
	/**
	 * 设置错误码和错误消息
	 * @param result
	 * @param errCode
	 */
	public void setMsg(JSONObject result,int errCode) {
		result.put(SuweiConst.ERR_CODE, errCode);
		result.put(SuweiConst.ERR_DESC, SuweiPropertyUtil.getMsg(String.valueOf(errCode)));
	}

	/**
	 * 获得菜品列表按照接口给的格式
	 * @param tenantId
	 * @param orderNo
	 * @throws Exception
	 */
	private List<JSONObject> queryMeals(String tenantId, String orderNo) throws Exception {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT A.item_name as name,A.price ,A.number as amount");
		sql.append(" FROM cc_order_item A WHERE A .order_code = ?");
//		DBContextHolder.setTenancyid(tenantId);
		return this.dao.query4Json(tenantId, sql.toString(), new Object[]{orderNo});
	}
	
	/* (non-Javadoc)
	 * @see com.tzx.cc.baidu.service.SuweiService#thirdSuweiNotifySaas(net.sf.json.JSONObject, net.sf.json.JSONObject)
	 */
	@Override
	public void thirdSuweiNotifySaas(JSONObject jsobj, JSONObject responseJson)
			throws Exception {
		String oid = jsobj.optString("oid");
		String key = SuweiConst.REDIS_KEY_NAME_PRIVISOUS_SUWEI + oid;
		String tenantId = null;
		if(redisDao.hasKey(key)) {
			tenantId = (String) redisDao.get(key);
		} else {
			logger.error("订单状态变更通知在redis中未找到"+oid+"所对应的商户");
			return;
		}
		DBContextHolder.setTenancyid(tenantId);
		
		String sql = "select * from cc_order_list_suwei where suwei_order_id = ?";
		List<JSONObject> query4Json = dao.query4Json(tenantId, sql,new Object[]{oid});
		if(query4Json.isEmpty()) {
		}
		JSONObject jsonObject = query4Json.get(0);
		String status = jsobj.optString("status");
		String boxNum = jsobj.optString("box_num");
		String pass = jsobj.optString("pass");
		String des = jsobj.optString("des");
		jsonObject.put("tenancy_id", tenantId);
		jsonObject.put("suwei_status", status);
		jsonObject.put("box_num", boxNum);
		jsonObject.put("pass", pass);
		jsonObject.put("des", des);
		jsonObject.put("last_updatetime", DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
		dao.updateIgnorCase(tenantId, "cc_order_list_suwei", jsonObject);
		
		
		String orderCode = jsonObject.optString("order_code");
		sql = "select * from cc_order_list where order_code = ?";
		if(StringUtils.equals(SuweiConst.THIRD_SUWEI_STATUS_NOTIFY_PUT_MEAL, status)) {
			//成功是推送消息
			List<JSONObject> query4Json2 = dao.query4Json(tenantId, sql,new Object[]{orderCode});
			if(!query4Json2.isEmpty()) {
				JSONObject orderInfo = query4Json2.get(0);
				newSendTemplateMasgService.sendTemplatMsg(tenantId, orderInfo.optString("openid"), orderCode);
			}
		} else if(StringUtils.equals(SuweiConst.THIRD_SUWEI_STATUS_NOTIFY_CANCEL_MEAL, status)) {
			//取消订单
			List<JSONObject> query4Json2 = dao.query4Json(tenantId, sql,new Object[]{orderCode});
			if(!query4Json2.isEmpty()) {
				JSONObject orderInfo = query4Json2.get(0);
				JSONObject json = new JSONObject();
				json.put("channel", orderInfo.optString("chanel"));
				json.put("order_code", orderInfo.optString("order_code"));
				json.put("store_id", orderInfo.optString("store_id"));
				json.put("tenancy_id", orderInfo.optString("tenancy_id"));
//				json.put("operator", "admin");
				orderManagementHqService.orderCancel(tenantId, json);
			}
		}
	}

	@Override
	public JSONObject getSuweiStoreConfig(String tenantId, String storeId)
			throws Exception {
		String sql = "select * from cc_third_organ_info where shop_id = ? and channel = 'WX02'";
		List<JSONObject> query4Json = this.dao.query4Json(tenantId, sql,new Object[]{Integer.parseInt(storeId)});
		if(!query4Json.isEmpty()) {
			return query4Json.get(0);
		}
		return null;
	}
}
