package com.tzx.report.po.boh.impl;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.FoodSalesSummaryReportAreaDao;
import com.tzx.report.po.boh.dao.FoodSalesSummaryReportDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Repository(FoodSalesSummaryReportAreaDao.NAME)
public class FoodSalesSummaryReportDaoAreaImpl implements FoodSalesSummaryReportAreaDao{
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
 
	@Override
	public JSONObject getFoodSalesSummaryReportArea(String tenancyID,JSONObject condition) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		String reportSql = "";
		//菜品汇总查询
		if(condition.optInt("type_columns")==1){
			condition.element("p_report_type", "L1J0");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					condition.element("p_report_type", "L0");
					footerList = this.dao.query4Json(tenancyID, this.dao.getString(tenancyID,parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA)));
				}
			}
		// 按照菜品类别查询	
		else if (condition.optInt("type_columns")==2) {
			if(condition.optInt("hierarchytype") ==1){
				condition.element("p_report_type", "L1M0");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					condition.element("p_report_type", "L0");
					footerList = this.dao.query4Json(tenancyID, this.dao.getString(tenancyID,parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA)));
				}
				
			}else if(condition.optInt("hierarchytype") ==2){
				condition.element("p_report_type", "L1M1");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level1")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				
			}else if(condition.optInt("hierarchytype") ==3){
				condition.element("p_report_type", "L1M2");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level2")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
			}
		// 按照机构+日期查询
		}else if (condition.optInt("type_columns")==3) {
			if(condition.optInt("hierarchytype") ==1){
				condition.element("p_report_type", "L1N0");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					condition.element("p_report_type", "L0");
					footerList = this.dao.query4Json(tenancyID, this.dao.getString(tenancyID,parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA)));
				}
				
			}else if(condition.optInt("hierarchytype") ==2){
				condition.element("p_report_type", "L1N1");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level1")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				
			}else if(condition.optInt("hierarchytype") ==3){
				condition.element("p_report_type", "L1N2");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level2")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				
			}
		// 按照日期+机构查询	
		}else if (condition.optInt("type_columns")==4) {
			if(condition.optInt("hierarchytype") ==1){
				condition.element("p_report_type", "L1F0");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
					condition.element("p_report_type", "L0");
					footerList = this.dao.query4Json(tenancyID, this.dao.getString(tenancyID,parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA)));
				}
				
			}else if(condition.optInt("hierarchytype") ==2){
				condition.element("p_report_type", "L1F1");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level1")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				
			}else if(condition.optInt("hierarchytype") ==3){
				condition.element("p_report_type", "L1F2");
				reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition,EngineConstantArea.FOOD_SALES_SUMMARY_REPORT_AREA);
				reportSql = this.dao.getString(tenancyID, reportSql);
				if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReportlLevel(condition,reportSql.toString(),condition.optInt("level2")));
					structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());
				}else{
					total = this.dao.countSql(tenancyID,reportSql.toString());
					list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,reportSql.toString()));
				}
				
			}
		}
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);	
		result.put("rows", list);
		result.put("footer", footerList);
		result.put("structure", structure);
		return result;
	}

}
