package com.tzx.report.po.hq.impl;

import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.hq.dao.AnalysisOfSalesOfVegetablesDao;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

@Repository(AnalysisOfSalesOfVegetablesDao.NAME)
public class AnalysisOfSalesOfVegetablesDaoImpl implements AnalysisOfSalesOfVegetablesDao {
 
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = "parameterUtils")
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils conditionUtils;
	
	@Override
	public JSONObject getAnalysisOfSalesOfVegetables(String tenancyID, JSONObject condition) throws Exception {

        String p_report_date_begin = condition.optString("p_report_date_begin");
        String p_report_date_end = condition.optString("p_report_date_end");
        String p_type = condition.optString("p_type");
        if(p_report_date_begin.length() == 0 || "day".equals(p_type)) {
            condition.element("p_report_date_begin_year", 9999);
            condition.element("p_report_date_begin_month", 13);
        }else {
            String [] dataArr = p_report_date_begin.split("-");
            if(dataArr.length > 0){
                condition.element("p_report_date_begin_year", dataArr[0]);
                if(dataArr.length == 2){
                    condition.element("p_report_date_begin_month", dataArr[1]);
                }else{
                    condition.element("p_report_date_begin_month", 13);
                }
            }
            condition.element("p_report_date_begin", "2099-12-31");
        }

        if(p_report_date_end.length() == 0 || "day".equals(p_type)){
            //condition.element("p_report_date_end_notday", 10000);
        }else {
            condition.element("p_report_date_end", "2099-12-31");
        }

        if(!condition.containsKey("exportdataexpr")) {
            condition.element("exportdataexpr", "''");
        }else{
        	String exp = condition.optString("exportdataexpr");
        	condition.element("exportdataexpr", conditionUtils.spilt(exp.substring(1,exp.length()-1)));
        }

        List<JSONObject> list = new ArrayList<JSONObject>();
        List<JSONObject> footerList = new ArrayList<JSONObject>();
        List<JSONObject> structure = new ArrayList<JSONObject>();
        JSONObject result = new JSONObject();
        long total = 0L;
        String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_ANALYSIS_OF_SALES_OF_VEGE_TABLES);
        total = this.dao.countSql(tenancyID, reportSql.toString());
        if (condition.containsKey("derivedtype") && condition.optInt("derivedtype") == 2) {
            list = this.dao.query4Json(tenancyID, parameterUtils.buildPageSqlReport(condition, reportSql.toString()));
            structure = conditionUtils.getSqlStructure(tenancyID, reportSql.toString());
        } else {
            list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, reportSql.toString()));
        }

        footerList = this.dao.query4Json(tenancyID, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.ENGINE_TOTAL_ENGINE_ANALYSIS_OF_SALES_OF_VEGE_TABLES).toString());

        int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
        result.put("page", pagenum);
        result.put("total", total);
        result.put("rows", list);
        result.put("footer", footerList);
        result.put("structure", structure);
        return result;
    }
}
