package com.tzx.cc.common.redis.service.impl;

import javax.annotation.Resource;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class CcWroRedisServiceImpl extends AbstractCcRedisCommonServiceImpl {	
	@Resource(name = "wroRedisTemplate")
	private RedisTemplate<String, String> redisTemplate;

	@Override
	protected RedisTemplate<String, String> getRedisTemplate() {
		return redisTemplate;
	}

}
