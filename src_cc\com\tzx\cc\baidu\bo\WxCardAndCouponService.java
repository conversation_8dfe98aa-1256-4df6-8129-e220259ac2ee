package com.tzx.cc.baidu.bo;

import java.util.List;

import com.alibaba.fastjson.JSONArray;

import net.sf.json.JSONObject;

public interface WxCardAndCouponService {

	final String NAME = "com.tzx.cc.baidu.bo.imp.WxCardAndCouponServiceImpl";

	JSONObject findWXKQdglist(String tenantId, JSONObject obj)throws Exception;

	List<JSONObject> findProvinceDetails(String tenantId, JSONObject obj)throws Exception;

	List<JSONObject> findStoreCategories(String tenantId, JSONObject obj)throws Exception;

	void saveInfoUrl(String tenantId, JSONObject obj)throws Exception;

	JSONObject findAreaDetailsUrl(String tenantId, JSONObject obj)throws Exception;

	JSONObject checkSynchroResult(String tenantId, JSONObject obj)throws Exception;
	
}
