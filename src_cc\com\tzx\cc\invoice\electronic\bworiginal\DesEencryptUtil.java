package com.tzx.cc.invoice.electronic.bworiginal;

import java.security.Key;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;

/**
 * Created by Administrator on 2016/4/19.
 */
public class DesEencryptUtil {
    private static final String ALGORITHM_DES = "DES/ECB/PKCS5Padding";
    private static final String ALGORITHM_DES2 = "DES/ECB/NoPadding";

    private static Key toKey(byte[] key) throws Exception {
        DESKeySpec dks = new DESKeySpec(key);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey secretKey = keyFactory.generateSecret(dks);
        return secretKey;
    }

    /**
     * 加密数据
     *
     * @param src 注意：这里的数据长度只能为8的倍数
     * @param key
     * @return
     * @throws Exception
     */
    public static String encryptDES(String src, String key) throws Exception {
        Key sk = toKey(key.getBytes());
        Cipher cip = Cipher.getInstance(ALGORITHM_DES);
        cip.init(Cipher.ENCRYPT_MODE, sk);
        String dest = BASE64New.encode(cip.doFinal(src.getBytes("UTF-8")));
        return dest;
    }

    /**
     * 解密
     *
     * @param src
     * @param key 注意：这里的数据长度只能为8的倍数
     * @return
     * @throws Exception
     */
    public static String decryptString(String src, String key) throws Exception {
        Key sk = toKey(key.getBytes());
        Cipher cipher = Cipher.getInstance(ALGORITHM_DES);
        //cipher.init(Cipher.DECRYPT_MODE, sk,random);
        cipher.init(Cipher.DECRYPT_MODE, sk);
        return new String(cipher.doFinal((BASE64New.base64Decoder(src))), "utf-8");
    }

    /**
     * 解密
     *
     * @param src
     * @param key 注意：这里的数据长度只能为8的倍数
     * @return
     * @throws Exception
     */
    public static String decryptCode(String src, String key) throws Exception {
        String result = null;
        try {
            Key sk = toKey(key.getBytes());
            Cipher cipher = Cipher.getInstance(ALGORITHM_DES);
            cipher.init(Cipher.DECRYPT_MODE, sk);
            result =  new String(cipher.doFinal((BASE64New.base64Decoder(src))),"utf-8");
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception(e);
        }
        return result;
    }

    public static byte[] hexStringToByte(String hex) {
        int len = (hex.length() / 2);
        byte[] result = new byte[len];
        char[] achar = hex.toCharArray();
        for (int i = 0; i < len; i++) {
            int pos = i * 2;
            result[i] = (byte) (toByte(achar[pos]) << 4 | toByte(achar[pos + 1]));
        }
        return result;
    }

    private static byte toByte(char c) {
        byte b = (byte) "0123456789ABCDEF".indexOf(c);
        return b;
    }

    /**
     * 把字节数组转换成16进制字符串
     *
     * @param bArray
     * @return
     */
    public static final String bytesToHexString(byte[] bArray) {
        StringBuffer sb = new StringBuffer(bArray.length);
        String sTemp;
        for (int i = 0; i < bArray.length; i++) {
            sTemp = Integer.toHexString(0xFF & bArray[i]);
            if (sTemp.length() < 2)
                sb.append(0);
            sb.append(sTemp.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 加密数据
     *
     * @param src 注意：这里的数据长度只能为8的倍数
     * @param key
     * @return 16进制字符串
     * @throws Exception
     */
    public static String encryptDESNoPadding(String src, String key) throws Exception {
        Key sk = toKey(key.getBytes());
        Cipher cip = Cipher.getInstance(ALGORITHM_DES2);
        cip.init(Cipher.ENCRYPT_MODE, sk);
        String dest = bytesToHexString(cip.doFinal(src.getBytes("UTF-8")));
        return dest;
    }

    /**
     * 解密
     *
     * @param src
     * @param key 注意：这里的数据长度只能为8的倍数
     * @return
     * @throws Exception
     */
    public static String decryptDESNoPadding(String src, String key) throws Exception {
        Key sk = toKey(key.getBytes());
        Cipher cipher = Cipher.getInstance(ALGORITHM_DES2);
        //cipher.init(Cipher.DECRYPT_MODE, sk,random);
        cipher.init(Cipher.DECRYPT_MODE, sk);
        return new String(cipher.doFinal((hexStringToByte(src))), "utf-8");
    }

    /**
     * 衫德加密方式
     * @param src 注意：src定义的固定长度为51位  后面补13个0进行加密  组成64位的长度
     * @param key
     * @return 16进制字符串
     * @throws Exception
     */
    public static String encryptSand(String src, String key) throws Exception {
        Key sk = toKey(key.getBytes());
        Cipher cip = Cipher.getInstance(ALGORITHM_DES2);
        cip.init(Cipher.ENCRYPT_MODE, sk);
        src = src + "0000000000000";
        char[] chars = src.toCharArray();
        byte[] bytes = new byte[chars.length / 2];
        for (int i = 0; i < chars.length; i = i + 2) {
            bytes[i/2] =(byte)Integer.parseInt(String.valueOf(chars[i]) + String.valueOf(chars[i + 1]),16);
        }
        String dest = bytesToHexString(cip.doFinal(bytes));
        return dest;
    }

    /**
     * 衫德解密方式
     * @param dest 注意：src定义的固定长度为51位  后面补13个0进行加密  组成64位的长度
     * @param key
     * @return 16进制字符串
     * @throws Exception
     */
    public static String decryptSand(String dest, String key) throws Exception {
        Key sk = toKey(key.getBytes());
        Cipher cip = Cipher.getInstance(ALGORITHM_DES2);
        cip.init(Cipher.DECRYPT_MODE, sk);
        byte[] s = cip.doFinal(hexStringToByte(dest));
        return bytesToHexString(s);
    }
}
