package com.tzx.framework.common.filter;

import com.tzx.framework.bo.SystemUserService;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.AESEncrypt;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class CheckUserFilter implements Filter{
	private final Logger logger = LoggerFactory.getLogger(getClass());

	private String loginPage;
	protected FilterConfig	filterConfig;
	private String USERINFO_API_URL;
	private String saasUrl;
	private String logoutUrl;
	private String excludeUrl;
	
	private SystemUserService		systemUserService;


	public void init(FilterConfig config) throws ServletException{
		this.filterConfig = config;
		this.loginPage = config.getInitParameter("loginPage");
		USERINFO_API_URL = Constant.getSystemMap().get("saas_url") + "/framework/systemUserRest/userLogin";
		logoutUrl =  Constant.getSystemMap().get("sso.logout.url")+"/pages/framework/layout/redirectlogout.jsp";//登出url
		saasUrl =  Constant.getSystemMap().get("saas_url");
		systemUserService = (SystemUserService) SpringConext.getBean(SystemUserService.NAME);
		excludeUrl = config.getInitParameter("excludeUrl");
	}

	public String getSSOLoginCookie(HttpServletRequest hreq){
		String tenancyIdName = null;
		if(hreq==null){
			return tenancyIdName;
		}

		//20181111 从cookie获取 当前商户信息
		Cookie[] cookieList = hreq.getCookies();
		if(cookieList==null || cookieList.length==0){
			logger.warn("cookieList不存在");
			return tenancyIdName;
		}
		String curUrl = hreq.getRequestURL().toString();
		String key = "";
		if(curUrl.indexOf("test.")>-1){
			key = "test";
		}else if(curUrl.indexOf("hd.")>-1){
			key = "hd";
		}
		logger.info("requestUrl:"+curUrl+",key:"+key);
		String d = null;
		for (int i = 0; i < cookieList.length; i++) {
			Cookie ck = cookieList[i];
			logger.info("ck.name:"+ck.getName()+","+key+"_tenancy");
			if(ck.getName().equals(key+"_tenancy")){
				String v = ck.getValue();
				tenancyIdName = AESEncrypt.getInstance().decrypt(v);
				logger.info("从cookie中获取当前SSO用户为："+tenancyIdName);
			}
			if(ck.getName().equals(key+"_key")){
				String v = ck.getValue();
				d = AESEncrypt.getInstance().decrypt(v);
				logger.info("从cookie中获取登录加密时间戳:"+d);
			}
		}
		if(tenancyIdName!=null && d!=null && tenancyIdName.lastIndexOf("_"+d)>-1){
			tenancyIdName = tenancyIdName.substring(0,tenancyIdName.lastIndexOf("_"+d));
		}
		return tenancyIdName;
	}


	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException{
		HttpServletRequest hreq = (HttpServletRequest) req;
		HttpServletResponse hres = (HttpServletResponse) res;
		
		//添加跨域请求访问
		hres.setHeader("Access-Control-Allow-Origin", "*");
        //允许通过的header信息 ，access_token 是自己添加的字段
		hres.addHeader("Access-Control-Allow-Headers", "Origin, No-Cache, X-Requested-With,access_token");
		HttpSession session = hreq.getSession();
		String tenentid = null;

		try{
			String requestUrl = hreq.getRequestURI();
			logger.info("ReqUrl: {} ", hreq.getRequestURI());
			JSONObject p = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = hreq.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}
			
			String[] urlArray = excludeUrl.split("\\|");
			for (String url:urlArray) {
				if(requestUrl.contains(url)) {
					if(p.containsKey("tenentid")){
						DBContextHolder.setTenancyid(p.getString("tenentid"));
						chain.doFilter(req, res);
						return;
					}
				}
			}

			String tenancyIdName = getSSOLoginCookie(hreq);


			//从统一登录系统中获取商户id
			AttributePrincipal principal = (AttributePrincipal) hreq.getUserPrincipal();
			if(principal != null) {
				String loginName = principal.getName();
				//和cookie 对比
				if(StringUtils.isNotBlank(tenancyIdName) && !loginName.equals(tenancyIdName)){
					logger.warn("cas client 获取用户和 cookie 获取用户信息不一致！！");
					loginName = tenancyIdName;
				}

				if(StringUtils.isNotBlank(loginName)){
					String[] userInfos = loginName.split("_");
					tenentid = userInfos[0];     // 商家ID
					String userName = userInfos[1];     // 用户名
					DBContextHolder.setTenancyid(tenentid);
					if(session.getAttribute("tenentid") == null || !session.getAttribute("tenentid").equals(tenentid)) {
						//放入session信息
						loadSession(hreq, tenentid, userName);
					}
					chain.doFilter(req, res);
				}
			}else{
				logger.info("请重新登入!!!");
			}
		
		}catch (Exception e){
			hres.sendRedirect(logoutUrl);
			logger.error("CheckUserFilter异常, tenentid: {}", tenentid, e);
			logger.info("跳转至登出页面  "+logoutUrl);
		}

	}

	public void setFilterConfig(final FilterConfig filterConfig)
	{
		this.filterConfig = filterConfig;
	}

	public void destroy()
	{
		this.filterConfig = null;
	}
	
	/**
	 * 天子星前端开发模式，测试使用
	 * @param req
	 * @param res
	 * @param tenentid
	 */
	private void developModel(HttpServletRequest req, HttpServletResponse res,String tenentid){
		try {
			SystemUserService systemUserService = (SystemUserService) SpringConext.getBean(SystemUserService.NAME);
			HttpSession session =  req.getSession();
			session.setAttribute("isLogin", "true");
			session.setAttribute("tenentid", tenentid);
			session.setAttribute("employeeName", "system");
			session.setAttribute("userName", "system");
			session.setAttribute("organName", "总部");
			session.setAttribute("organ_code", "0");
			session.setAttribute("organ_id", "0");
			session.setAttribute("store_id", "0");// 机构ID
			session.setAttribute("employeeId", "0");
			session.setAttribute("e_id", "0");
			session.setAttribute("organ_brief_code", "CN0000");
			session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
			session.setAttribute("name", "系统管理员");
			session.setAttribute("org_type", "0");
			session.setAttribute("da_date", "");
			session.setAttribute("day_count", "");
			// 用于判断是否是管理员
			session.setAttribute("sysuser", "1");
			//查询用户是否具有特殊权限,如果没有则返回普通权限
			String sysuser = (String) session.getAttribute("sysuser");
			
			String organ_codes= systemUserService.findOrganCodes(tenentid,sysuser,new JSONObject());
			String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenentid,sysuser,new JSONObject());
			session.setAttribute("user_organ_codes_group_invalid", organ_codes_invalid);
			session.setAttribute("user_organ_codes_group", organ_codes);
			
			session.setAttribute("themes", "metro-gray");
			// session.setAttribute("roles_id", "admin");// 以 ” ，"分开的IDS
			Map<Integer, Integer> map = new HashMap<Integer, Integer>();
			map.put(0, 1);
			session.setAttribute("authMap", map);
			JSONObject p = JSONObject.fromObject("{}");
			p.put("tenancy_id", session.getAttribute("tenentid"));
			p.put("id", session.getAttribute("employeeId"));
			p.put("store_id", session.getAttribute("store_id"));
			session.setAttribute("moduleMap", systemUserService.getRoleAuthorutyModule(tenentid, (String) session.getAttribute("sysuser"), p));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 单点登录加载session信息
	 * @param req
	 * @param tenentid
	 * @param userName
	 */
	private void loadSession(HttpServletRequest req, String tenentid , String userName){
		try {
			JSONObject param = new JSONObject();
		/*	param.put("tenancyId", tenentid);
			param.put("userName", userName);*/
			String themes = "";
			param.put("tenentid", tenentid);
			param.put("loginUserName", userName);
			HttpSession session = req.getSession();
			JSONObject userInfoJosonObje = systemUserService.chekUserLogin(tenentid, param);

			if (param.optString("loginUserName").equals("system")) {
				session.setAttribute("isLogin", "true");
				session.setAttribute("tenentid", tenentid);
				session.setAttribute("employeeName", "system");
				session.setAttribute("userName", "system");
				session.setAttribute("organName", "总部");
				session.setAttribute("organ_code", "0");
				session.setAttribute("organ_id", "0");
				session.setAttribute("store_id", "0");// 机构ID
				session.setAttribute("employeeId", "0");
				session.setAttribute("e_id", "0");
				session.setAttribute("organ_brief_code", "CN0000");
				session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
				session.setAttribute("name", "系统管理员");
				session.setAttribute("org_type", "0");
				session.setAttribute("da_date", "");
				session.setAttribute("day_count", "");
				// 用于判断是否是管理员
				session.setAttribute("sysuser", "1");
				//查询用户是否具有特殊权限,如果没有则返回普通权限
				String sysuser = (String) session.getAttribute("sysuser");
				String organ_codes= systemUserService.findOrganCodes(tenentid,sysuser,userInfoJosonObje);
				String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenentid,sysuser,userInfoJosonObje);
				session.setAttribute("user_organ_codes_group_invalid", organ_codes_invalid);
				session.setAttribute("user_organ_codes_group", organ_codes);
				
				session.setAttribute("themes", (themes == null || themes.isEmpty()) ? "metro-gray" : themes);
				// session.setAttribute("roles_id", "admin");// 以 ” ，"分开的IDS
				Map<Integer, Integer> map = new HashMap<Integer, Integer>();
				map.put(0, 1);
				session.setAttribute("authMap", map);
				JSONObject p = JSONObject.fromObject("{}");
				p.put("tenancy_id", (String) session.getAttribute("tenentid"));
				p.put("id", (String) session.getAttribute("employeeId"));
				p.put("store_id", (String) session.getAttribute("store_id"));
				session.setAttribute("moduleMap", systemUserService.getRoleAuthorutyModule(tenentid, (String) session.getAttribute("sysuser"), p));
				
				//多品牌标识 0单一品牌  1多品牌   saas2.0报表取消多品牌
				//session.setAttribute("valid_state", systemUserService.checkIsMultiBrand(tenentid));
			} else {
				if (userInfoJosonObje != null){
					session.setAttribute("isLogin", "true");
					session.setAttribute("tenentid", tenentid);
					// 用于判断是否是管理员
					if(param.optString("loginUserName").equals(Constant.ADMIN)){
						session.setAttribute("sysuser", "1");
					}else{
						session.setAttribute("sysuser", "0");
					}

					String sysuser = (String) session.getAttribute("sysuser");
					// session.setAttribute("login_account",
					// userInfoJosonObje.getString("login_account"));
					session.setAttribute("employeeName", userInfoJosonObje.optString("user_name"));
					session.setAttribute("userName", userInfoJosonObje.optString("user_name"));
					session.setAttribute("employeeId", userInfoJosonObje.optString("employee_id"));// 用户id
					session.setAttribute("e_id", userInfoJosonObje.optString("employee_id"));
					session.setAttribute("organ_id", userInfoJosonObje.optString("store_id"));
					session.setAttribute("organName", userInfoJosonObje.optString("org_full_name"));
					session.setAttribute("organ_code", userInfoJosonObje.optString("organ_code"));
					session.setAttribute("organ_brief_code", userInfoJosonObje.optString("organ_brief_code"));
					session.setAttribute("name", userInfoJosonObje.optString("name"));
					session.setAttribute("org_type", userInfoJosonObje.optString("org_type"));
					session.setAttribute("da_date", "null".equals(userInfoJosonObje.optString("da_date"))?"":userInfoJosonObje.optString("da_date"));
					session.setAttribute("day_count", "null".equals(userInfoJosonObje.optString("day_count"))?"":userInfoJosonObje.optString("day_count"));

					if ("0".equals(userInfoJosonObje.optString("store_id"))){
						session.setAttribute("org_type", "0");
						session.setAttribute("da_date", "");
						session.setAttribute("day_count", "");
						session.setAttribute("organ_code", "0");
						session.setAttribute("organName", "总部");
						session.setAttribute("organ_brief_code", "CN0000");
					}

					String organ_codes= systemUserService.findOrganCodes(tenentid,sysuser,userInfoJosonObje);
					session.setAttribute("user_organ_codes_group", organ_codes);
					String organ_codes_invalid= systemUserService.findOrganCodesWithInvalid(tenentid,sysuser,userInfoJosonObje);
					session.setAttribute("user_organ_codes_group_invalid", organ_codes_invalid);
					session.setAttribute("user_id", userInfoJosonObje.optInt("id"));
					session.setAttribute("upload_img_ip", Constant.getSystemMap().containsKey("upload_img_ip") ? Constant.getSystemMap().get("upload_img_ip").toString() : "");
					// session.setAttribute("roles_id",
					// userInfoJosonObje.getString("role_id"));//
					session.setAttribute("store_id", userInfoJosonObje.getString("store_id"));// 机构ID
					session.setAttribute("themes", (themes == null || themes.isEmpty()) ? "metro-gray" : themes);
					// 加载用户的权限信息
					JSONObject p = JSONObject.fromObject("{}");
					p.put("tenancy_id", (String) session.getAttribute("tenentid"));
					p.put("id", (String) session.getAttribute("employeeId"));
					p.put("store_id", (String) session.getAttribute("store_id"));
					session.setAttribute("authMap", systemUserService.getRoleAuthoruty(tenentid, (String) session.getAttribute("sysuser"), p));
					session.setAttribute("moduleMap", systemUserService.getRoleAuthorutyModule(tenentid, (String) session.getAttribute("sysuser"), p));
					session.setAttribute(" ", userInfoJosonObje); // 把整个用户信息的JSONObject对象放入JSON
					
					//多品牌标识 0单一品牌  1多品牌   saas2.0报表取消多品牌
					//session.setAttribute("valid_state", systemUserService.checkIsMultiBrand(tenentid));
				}else{
					logger.info("loadSession userInfo is empty!");
				}
			}
			
		} catch (Exception e) {
			logger.error("loadSession异常, tenentid: {}, userName{}", tenentid, userName);
			logger.error("loadSession异常", e);
			throw new IllegalStateException("查询用户信息异常");
		}
	}
	

	public static void main(String[] args) {
		String url = "http://sysconf.meishijia.com/finter/inter/userLogin";
		JSONObject param = new JSONObject();
		String tenentid = "hdl";
		String userName = "260xk";
		param.put("tenancyId", tenentid);
		param.put("userName", userName);
		param.put("version", "2.0");
		String resultJson = HttpUtil.sendPostRequestJson(url, param.toString());
		System.out.println("结果:"+resultJson);
		if(!StringUtils.isEmpty(resultJson)){
			JSONObject result = JSONObject.fromObject(resultJson);
			if(result != null){
				if("0".equals(result.optString("code"))){
					JSONObject sessionJson = result.optJSONObject("data").optJSONObject("sessnioInfo");
					System.out.println("sessionJson:"+sessionJson);
				}
			}
		}
		
	}

}