<?xml version="1.0" encoding="UTF-8"?>
<root>

		 <!--  @param tenancyId
		  @param store_id
		  @param businessSystem 业务系统
		  @param businessModule 业务模块
		  @param businessKey 操作主键
		  @param operateType 操作类型
		  @param operateTime 操作时间
		  @param operateReason 操作原因
		  @param operatePerson 操作人
		  @param operateDetail操作详情 -->
		
	<!-- <aop_unique_url name='/tzxsaas/crmRest/post'>配置拦截路径，最前面务必加/
	  按照如下格式替换对应value值即可
		<element name ="unique_url" value="/tzxsaas/crmRest/post"/>需要拦截的请求地址 ，最前面务必加/
	 	<element name="business_system" value="Saas后台"/>系统名称
	 	<element name="business_module" value="crm接口"/>模块名称
	 	<element name="business_key" value="调用接口"/>操作名称 按照如下规则设置：新增，新增或修改，启用，停用，复制
	 	<element name="operate_type" value="666"/>统一为666
	 	<element name="operate_reason" value="操作原因"/>例如 新增会员、复制桌位等
	</aop_unique_url>	 -->  
		  
		<!-- 路径不加入项目名称 -->
	<aop_unique_url name='/crm/memberShipsGroupRest/saveOrUpdate'>
		<element name ="unique_url" value="/crm/memberShipsGroupRest/saveOrUpdate"/><!--需要拦截的请求地址  -->
	 	<element name="business_system" value="Saas后台"/><!-- 系统名称 -->
	 	<element name="business_module" value="会员群体模块"/><!-- 模块名称 -->
	 	<element name="business_key" value="新增或修改"/><!-- 操作名称 按照如下规则设置：新增，新增或修改，启用，停用，复制 -->
	 	<element name="operate_type" value="666"/><!-- 统一为666-->
	 	<element name="operate_reason" value="新增或修改群体信息"/><!-- 例如 新增会员、复制桌位等-->
	</aop_unique_url>	 



	<aop_unique_url name='/framework/systemUserRest/userlogin'>
		<element name ="unique_url" value="/framework/systemUserRest/userlogin"/><!--需要拦截的请求地址  -->
	 	<element name="business_system" value="Saas后台"/><!-- 系统名称 -->
	 	<element name="business_module" value="用户登录"/><!-- 模块名称 -->
	 	<element name="business_key" value="登录saas"/><!-- 操作名称 按照如下规则设置：新增，新增或修改，启用，停用，复制 -->
	 	<element name="operate_type" value="666"/><!-- 统一为666-->
	 	<element name="operate_reason" value="登录功能"/><!-- 例如 新增会员、复制桌位等-->
	</aop_unique_url>	 

</root>