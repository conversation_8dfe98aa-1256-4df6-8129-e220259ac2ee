package com.tzx.report.po.hq.impl;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.hq.dao.BusinessAllianceReconciliationStatementQueryDao;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

@Repository
public class BusinessAllianceReconciliationStatementQueryDaoImpl implements BusinessAllianceReconciliationStatementQueryDao {
 
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
    @Resource
    ConditionUtils conditionUtils;

    @Resource
    ParameterUtils parameterUtils;


	@Override
	public JSONObject getBusinessAllianceReconciliationStatementQuery(String tenancyID,JSONObject condition) throws Exception {

		List<JSONObject> list = new ArrayList<JSONObject>();
		List<JSONObject> footerList =new ArrayList<JSONObject>();
		List<JSONObject> structure = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		long total = 0L;
		String tmp = "";
		String tmpfooter = "";
		//查询函数
		String completionSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID,condition,EngineConstantArea.BUSINESS_ALLIANCE_RECONCILIATION_STATEMENT);
		//执行函数获取临时表
		List<JSONObject> tmpList = this.dao.query4Json(tenancyID, completionSql.toString());
		if(tmpList.size()>0){
			//获取当前函数临时表
			//明细
			tmp = tmpList.get(0).getString("sql_text");
			//合计
			if(condition.containsKey("hierarchytype") && condition.optInt("hierarchytype")==1){
				tmpfooter =  tmpList.get(1).getString("sql_text");
			}
		}
		if(tmp.length()>0){
			//导出
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
				//具体数据
				list = this.dao.query4Json(tenancyID,tmp);
				//映射实体类型
				structure = conditionUtils.getSqlStructure(tenancyID,tmp);
				
				//执行付款方式拼接
				initPayment(tenancyID,list,condition,footerList);
				
				//付款方式拼接字段类型
				paymentType(tenancyID,condition,structure);
			}else{
				//统计条数
				total = this.dao.countSql(tenancyID,tmp);
				//具体数据
				list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(condition,tmp));
				if(tmpfooter.length()>0){
					//合计
					footerList = this.dao.query4Json(tenancyID, tmpfooter);	
				}
				
				//执行付款方式拼接
				initPayment(tenancyID,list,condition,footerList);
			}
		}
		
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total",total);		
		result.put("rows", list);
		result.put("structure", structure);
		result.put("footer", footerList);
		return result;
	}
	
	
	//付款方式
	/**
	 * 
	 * @param tenancyID 商户号
	 * @param list  数据
	 * @param condition  参数
	 * @param footerList 合计
	 * @throws Exception
	 */
	public void initPayment (String tenancyID,List<JSONObject> list,JSONObject condition,List<JSONObject> footerList) throws Exception{
		StringBuilder sbdate = new StringBuilder();
		StringBuilder sb =new StringBuilder();
		StringBuilder sbstore_id = new StringBuilder();
		String bd = null;
		String field = null;
		//查询方式
		if(condition.optInt("QueryMode")==1){
			//判断是否是导出
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					if(condition.containsKey("expType") && condition.optBoolean("expType")){
						field = condition.optString("layersField");
					}else{
						field = condition.optString("deckField");
						condition.element("expType", true);
					}
			}else{
				//判断查询层级(第一层)
				if(condition.optString("p_report_type").equals("L1J1")){
					field = condition.optString("deckField");
				}else{
					//判断查询层级(第二层)
					field = condition.optString("layersField");
				}
			}
		}else{
			//判断是否是导出
			if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2){
					if(condition.containsKey("expType") && condition.optBoolean("expType")){
						field = condition.optString("layersField");
					}else{
						field = condition.optString("deckField");
						condition.element("expType", true);
					}
			}else{
				//判断查询层级(第一层)
				if(condition.optString("p_report_type").equals("L2J1")){
					field = condition.optString("deckField");
				}else{
					field = condition.optString("layersField");
				}
			}
		}
		 
		if(condition.optString("p_report_type").equals("L1J1") && field.equals("report_date")){
			for (JSONObject jo : list)
			{
				//查询字段
				bd = jo.optString(field);
				if(bd.length()>0)
				{
					sbdate.append(",'"+bd+"'");
				}
			}
			List<JSONObject> listp = new ArrayList<JSONObject>();
			// 是否查询支付数据
			if(sbdate.length()>0 && condition.optString("customtype").equals("1"))
			{
				sbdate.delete(0,1);
				sb.setLength(0);
				sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,day_count from hq_payment_count_new where store_id in(" + condition.optString("store_id") + ") and day_count in  (" + sbdate + ")  GROUP BY payment_id,day_count");
				
				listp = this.dao.query4Json(tenancyID,sb.toString());
				
				for(JSONObject json:list)
				{
					String str  = json.optString(field);
					for(JSONObject jop:listp)
					{
						String b_d = jop.optString("day_count");
						if(b_d.equals(str))
						{
							json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
						}
					}
				}
			}
			
			if(footerList.size()>0){
				List<JSONObject> payfoot = this.dao.query4Json(tenancyID, "SELECT concat('t',payment_id) AS pid,SUM(pay_money) AS local_currency FROM hq_payment_count_new WHERE store_id IN(" + condition.optString("store_id") + ") AND day_count BETWEEN '" + condition.optString("p_begin_date") + "' and '" + condition.optString("p_end_date") + "' GROUP BY payment_id");
				for (JSONObject jso : payfoot)
				{
					footerList.get(0).put(jso.optString("pid"), jso.optDouble("local_currency", 0.00));
				}
			}
		}else if(condition.optString("p_report_type").equals("L1J2") && field.equals("store_id")){
			for (JSONObject jo2 : list)
			{
				String str = jo2.optString(field);
				if(str.length()>0)
				{
					sbstore_id.append(","+str);
				}
			}
			List<JSONObject> listp = new ArrayList<JSONObject>();
			if(sbstore_id.length()>0 && condition.optString("customtype").equals("1"))
			{
				sbstore_id.delete(0,1);
				sb.setLength(0);
				sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,store_id,day_count as report_date from hq_payment_count_new where store_id in(" + sbstore_id + ") and day_count in  ('" + condition.optString("p_begin_date") + "')    GROUP BY payment_id,store_id,day_count");
				
				listp = this.dao.query4Json(tenancyID,sb.toString());
				
				for(JSONObject json:list)
				{
					String storeIdStr  = json.optString("store_id");
					String reportDateStr  = json.optString("report_date");
					for(JSONObject jop:listp)
					{
						String storeIdStrPay = jop.optString("store_id");
						String reportDateStrPay = jop.optString("report_date");
						if(storeIdStrPay.equals(storeIdStr) && reportDateStrPay.equals(reportDateStr))
						{
							json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
						}
					}
				}
			}
		}else if(condition.optString("p_report_type").equals("L2J1") && field.equals("store_id")){
			for (JSONObject jo2 : list)
			{
				String str = jo2.optString("store_id");
				if(str.length()>0)
				{
					sbstore_id.append(","+str);
				}
			}
			List<JSONObject> listp = new ArrayList<JSONObject>();
			if(sbstore_id.length()>0)
			{
				sbstore_id.delete(0,1);
				sb.setLength(0);
				sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,store_id from hq_payment_count_new where store_id in(" + sbstore_id + ") and (day_count between '" + condition.optString("p_begin_date") + "' and '" + condition.optString("p_end_date") + "' )  GROUP BY payment_id,store_id");
				
				listp = this.dao.query4Json(tenancyID,sb.toString());
				
				for(JSONObject json:list)
				{
					String str  = json.optString("store_id");
					for(JSONObject jop:listp)
					{
						String b_d = jop.optString("store_id");
						if(b_d.equals(str))
						{
							json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
						}
					}
				}
			}
			if(footerList.size()>0){
				List<JSONObject> payfoot = this.dao.query4Json(tenancyID, "SELECT concat('t',payment_id) AS pid,SUM(pay_money) AS local_currency FROM hq_payment_count_new WHERE store_id IN(" + condition.optString("store_id") + ") AND day_count BETWEEN '" + condition.optString("p_begin_date") + "' and '" + condition.optString("p_end_date") + "' GROUP BY payment_id");
				for (JSONObject jso : payfoot)
				{
					footerList.get(0).put(jso.optString("pid"), jso.optDouble("local_currency", 0.00));
				}
			}
		}else{
			for (JSONObject jo : list)
			{
				bd = jo.optString("report_date");
				if(bd.length()>0)
				{
					sbdate.append(",'"+bd+"'");
				}
			}
			List<JSONObject> listp = new ArrayList<JSONObject>();
			if(sbdate.length()>0)
			{
				sbdate.delete(0,1);
				sb.setLength(0);
				sb.append("select concat('t',payment_id) as pid,sum(pay_money) as local_currency,day_count as report_date,store_id from hq_payment_count_new where store_id in(" + condition.optString("store_id") + ") and day_count in( " + sbdate + ")  GROUP BY payment_id,day_count,store_id");
				
				listp = this.dao.query4Json(tenancyID,sb.toString());
				
				for(JSONObject json:list)
				{
					String storeIdStr  = json.optString("store_id");
					String reportDateStr  = json.optString("report_date");
					for(JSONObject jop:listp)
					{
						String storeIdStrPay = jop.optString("store_id");
						String reportDateStrPay = jop.optString("report_date");
						if(storeIdStrPay.equals(storeIdStr) && reportDateStrPay.equals(reportDateStr))
						{
							json.put(jop.optString("pid"), jop.optDouble("local_currency", 0.00));
						}
					}
				}
			}
		}
	}
	
	//付款方式类型
	/**
	 * 
	 * @param tenancyID 商户号
	 * @param condition 参数
	 * @param structure 导出数据类型
	 * @throws Exception
	 */
	public void paymentType(String tenancyID,JSONObject condition,List<JSONObject> structure) throws Exception{
		// 获取支付方式的返回类型
		if(condition.containsKey("derivedtype") && condition.optInt("derivedtype")==2) {
			List<JSONObject> paymentDetailsTreeOrderByClaas = conditionUtils.getPaymentDetailsTreeOrderByClaas(tenancyID, condition);
			JSONObject jj =  null;
			for(JSONObject jop:paymentDetailsTreeOrderByClaas){
				jj =  new JSONObject();
				jj.put("fieldname",jop.get("pname"));
				jj.put("fieldtype", "double");
				structure.add(jj);
			}
		}
	}
}
