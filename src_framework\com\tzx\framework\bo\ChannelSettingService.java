package com.tzx.framework.bo;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by jj on 2018-11-01.
 */
public interface ChannelSettingService {

    String NAME = "com.tzx.framework.bo.imp.ChannelSettingServiceImpl";

    List<JSONObject> loadChannelList(String tenentid, JSONObject condition) throws Exception;

    void updateChannel(String tenentid, JSONObject condition, JSONObject returnJson) throws Exception;

}
