package com.tzx.report.bo.imp.crm;

import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.crm.CustomerOperationLogService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: zhanglg
 * @Date: 2019-09-24 16:57
 */
@Service(CustomerOperationLogService.NAME)
public class CustomerOperationLogServiceImp implements CustomerOperationLogService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Override
    public JSONObject find(String tenancyId, JSONObject condition) throws SystemException, Exception {
        String begindate = condition.optString("begin_date");
        String enddate = condition.optString("end_date");
        String operationName = condition.optString("operation_name");
        String content = condition.optString("content");
        String operationType = condition.optString("operationtype");
        String storeIds = condition.optString("store_ids");
        net.sf.json.JSONObject resultJson = new net.sf.json.JSONObject();

        StringBuffer sbSQL = new StringBuffer();
        sbSQL.append("SELECT col.*,o.org_full_name as store_name FROM customer_operation_logs col join organ o on col.store_id=o.id WHERE 1=1");
        if(Tools.hv(begindate)){
            sbSQL.append(" and col.operation_time>='"+begindate+"'::TIMESTAMP");
        }
        if(Tools.hv(enddate)){
            sbSQL.append(" and col.operation_time<='"+enddate+"'::TIMESTAMP");
        }
        if(Tools.hv(operationName)){
            sbSQL.append(" and col.operation_name like concat('%','"+ operationName +"','%') ");
        }
        if(Tools.hv(content)){
            sbSQL.append(" and col.content like concat('%','"+ content +"','%') ");
        }
        if(Tools.hv(operationType)){
            sbSQL.append(" and col.operation_type in ("+operationType+") ");
        }
        if(Tools.hv(storeIds)){
            String[] stores = storeIds.split(",");
            String sids = "";
            for(int i=0;i<stores.length;i++){
                sids+=",'"+stores[i]+"'";
            }
            sids = sids.substring(1);
            sbSQL.append(" and col.store_id in ("+sids+") ");
        }
        sbSQL.append(" order by col.operation_time desc ");
        logger.info(" === customerOperationLog sql:" + sbSQL.toString());
        long total = this.dao.countSql(null,sbSQL.toString());
        List<JSONObject> list = this.dao.query4Json(null,this.dao.buildPageSql(condition,sbSQL.toString()));

        resultJson.put("rows",list);
        resultJson.put("total",total);
        return resultJson;
    }
}
