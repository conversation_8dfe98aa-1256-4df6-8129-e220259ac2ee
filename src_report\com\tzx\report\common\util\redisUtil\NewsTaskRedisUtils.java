package com.tzx.report.common.util.redisUtil;

import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;

/**
 * Created by XUGY on 2017-11-30.
 */
public class NewsTaskRedisUtils {

    /**
     * . logger
     */
    private static final Logger logger = Logger.getLogger(NewsTaskRedisUtils.class);

    private static final String REDIS_LIST_SUFFIX = "_LIST_171201";

    public static String getListKey4Redis(String redisKey) {
        return redisKey + REDIS_LIST_SUFFIX;
    }

    /**
     * 在redis中存入一个hash类型的数据，并且在list队列中添加一份
     *
     * @param redisKey
     * @param hashKey
     * @param value
     */
    public static void add(String redisKey, String hashKey, String value) {
        add2List(redisKey, value);
        NewsRedisUtils.getRedisTemplete().opsForHash().put(redisKey, hashKey, value);
    }

    /**
     * 在redis中并且在list队列中添加一份
     *
     * @param redisKey
     * @param value
     */
    public static void add2List(String redisKey, String value) {
        NewsRedisUtils.getRedisTemplete().opsForList().leftPush(getListKey4Redis(redisKey), value);
    }

    /**
     * 通过hashKey将此元素添加到list中
     *
     * @param redisKey
     * @param hashKey
     */
    public static void addListByHash(String redisKey, String hashKey) {
        Object o = NewsRedisUtils.getRedisTemplete().opsForHash().get(redisKey, hashKey);
        if (o == null) {
            return;
        }
        String s = (String) o;
        add2List(redisKey, s);
    }

    /**
     * 通过hash的键获取hash的值
     *
     * @param redisKey
     * @param hashKey
     * @return
     */
    public static String getValueByHashKey(String redisKey, String hashKey) {
        Object o = NewsRedisUtils.getRedisTemplete().opsForHash().get(redisKey, hashKey);
        if (o == null) {
            return null;
        }
        String s = (String) o;
        return s;
    }

    /**
     * 通过hash的键删除hash
     *
     * @param redisKey
     * @param hashKey
     * @return
     */
    public static void deleteValueByHashKey(String redisKey, String hashKey) {
        NewsRedisUtils.getRedisTemplete().opsForHash().delete(redisKey, hashKey);
    }

    /**
     * 从队列中取出一个数据
     *
     * @param redisKey
     * @return
     */
    public static String pop(String redisKey) {
        Object o = NewsRedisUtils.getRedisTemplete().opsForList().rightPop(getListKey4Redis(redisKey));
        if (o == null) {
            return null;
        }
        String s = (String) o;
        return s;
    }

    public static boolean hasListKey(String redisKey){
       return NewsRedisUtils.getRedisTemplete().hasKey(getListKey4Redis(redisKey));
    }
    /**
     * 获取list的长度
     *
     * @param redisKey
     * @return
     */
    public static long getListSize(String redisKey) {
        Long size = NewsRedisUtils.getRedisTemplete().opsForList().size(getListKey4Redis(redisKey));
        if (size == null) {
            return (long) 0;
        }
        return size.longValue();
    }

    /**
     * 清除两天前垃圾键
     * @param redisKey
     */
    public static void clearHashTwoDayBefore(String redisKey) {
        RedisTemplate redisTemplate = NewsRedisUtils.getRedisTemplete();
        long currentTimeMillis = System.currentTimeMillis();
        //两天
        long removetag = (long) 172800000;
        BoundHashOperations<String, Object, Object> boundHashOps = redisTemplate.boundHashOps(redisKey);
        List<Object> values = boundHashOps.values();
        if (values == null || values.isEmpty()) {
            return;
        }
        for (Object obj : values) {
            try {
                String valuestr = (String) obj;
                JSONObject jsonstr = null;//WechatJsonUtils.parseJson(valuestr);
                long time = jsonstr.getLong("placeTime");
                if (currentTimeMillis - time > removetag) {
                    String orderNo = jsonstr.getString("order_no");
                    deleteValueByHashKey(redisKey, orderNo);
                    logger.info("刪除redis鍵" + redisKey + "所对应的订单号为" + orderNo);
                }
            } catch (Exception e) {
                logger.error(e);
                continue;
            }
        }
    }

}