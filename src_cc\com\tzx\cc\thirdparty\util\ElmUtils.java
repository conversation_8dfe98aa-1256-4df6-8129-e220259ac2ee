package com.tzx.cc.thirdparty.util;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.ThrowsAdvice;

import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.common.redis.service.CcRedisService;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;

/**
 * 
 * <AUTHOR>
 * 
 */
public class ElmUtils {
	private static Logger log = LoggerFactory.getLogger(ElmUtils.class);

	public final static String SCOPE = "all";

	// 饿了么2.0商户token信息
	public final static String prefix_token = "elm_v2_token_";
	// 饿了么2.0商户密钥信息前缀
	public final static String prefix_ks = "elm_v2_ks_";

	private final static String keyToken = "keyToken";
	private final static String keyTokenTime = "keyTokenTime";
	private final static String keyTokenAuthLevel = "keyTokenAuthLevel";
	private final static String keyTokenOauthType = "keyTokenOauthType";

	private final static String pattern = "yyyy-MM-dd HH:mm:ss";

	private final static String elmShopReltion = "elmShopReltion";// 饿了么渠道饿了么的门店对应天子星门店关系
	private final static String elmShopVersion = "elmShopVersion";// 饿了么渠道天子星的门店对应版本关系
	/** 饿了么2.0商户级授权 */
	public final static String ELM_AUTH_LEVEL_TENANCY = "tenancy";
	/** 饿了么2.0门店级授权 */
	public final static String ELM_AUTH_LEVEL_SHOP = "shop";

	/** 饿了么2.0授权类型-企业级应用授权 */
	public final static String ELM_OAUTH_TYPE_ENTERPRISE = "1";
	/** 饿了么2.0授权类型-个人级应用授权 */
	public final static String ELM_OAUTH_TYPE_PERSONAL = "2";

	/** 饿了么2.0商户级授权时，门店shopid的默认值：-1 */
	public final static String ELM_AOUTH_TENANCY_DEFALUT_SHOPID = "-1";

	/**
	 * 获取饿了么的配置信息
	 * 
	 * @param key
	 *            饿了么分配的key
	 * @param appSecret
	 *            饿了么分配的密钥
	 * @return
	 */
	public static Config getConfigBykeyAndappSecret(String key, String appSecret) {
		return new Config(isTestAuth(), key, appSecret);
	}

	/**
	 * 根据商户号获取饿了么的配置对象信息
	 * 
	 * @see 1.根据商户号从内存中获取中 2.验证是否存在在内存中，如果不存在密钥信息，则从数据中查询获取。
	 *      3.根据获取到的密钥与key产生饿了么配置信息对象
	 * 
	 * @param tenanId
	 *            商户号
	 * @return
	 */
	public static Config getConfig(String tenanId) throws Exception {
		Map<String, String> valueMap = getKeySecret(tenanId, "");

		return new Config(isTestAuth(), valueMap.get("key"),
				valueMap.get("secret"));
	}

	/**
	 * 根据商户号与门店id获取饿了么的配置对象信息
	 * 
	 * @see 1.根据商户号从内存中获取中 2.验证是否存在在内存中，如果不存在密钥信息，则从数据中查询获取。
	 *      3.根据获取到的密钥与key产生饿了么配置信息对象
	 * 
	 * @param tenanId
	 *            商户号
	 * @return
	 */
	public static Config getConfig(String tenanId, String shopId)
			throws Exception {
		Map<String, String> valueMap = getKeySecret(tenanId, shopId);

		return new Config(isTestAuth(), valueMap.get("key"),
				valueMap.get("secret"));
	}

	/**
	 * 根据商户获取其密钥
	 * 
	 * @param tenanId
	 *            商户号
	 * @param shopId
	 *            门店id
	 * @return Map<String,String> 格式：Map<"key",键>,Map<"secret",密钥>
	 * @throws Exception
	 */
	public static Map<String, String> getKeySecret(String tenanId, String shopId)
			throws Exception {
		String key = "", appSecret = "", authLevel = "";

		Map<String, String> valueMap = getKeyScretMap(tenanId, shopId);

		if (valueMap.containsKey("key")) {
			return valueMap;
		} else {
			DBContextHolder.setTenancyid(tenanId);
			String condition_shop = " and shop_id=" + shopId;

			if (StringUtils.isEmpty(shopId)
					|| ELM_AOUTH_TENANCY_DEFALUT_SHOPID.equals(shopId)) {
				condition_shop = "";
			}

			GenericDao dao = (GenericDao) SpringConext
					.getBean("genericDaoImpl");
			List<JSONObject> resultJson = dao
					.query4Json(
							tenanId,
							"select channel,source2,secret2,auth_level from cc_third_organ_info where channel='EL09' "
									+ condition_shop
									+ " and source2 is not null group by channel,source2,secret2,auth_level");
			if (resultJson != null && resultJson.size() > 0) {
				JSONObject row = resultJson.get(0);
				key = row.optString("source2");
				appSecret = row.optString("secret2");
				authLevel = row.optString("auth_level");

				valueMap.put("key", key);
				valueMap.put("secret", appSecret);

				saveKeySecretKv(tenanId, shopId, authLevel, key, appSecret);

			}
		}

		return valueMap;
	}

	/**
	 * 获取饿了么token对象信息
	 * 
	 * @see 1.从内存获取Token信息 2.验证token的有效期是否超时 3.超时后重新刷新获取token
	 *      4.如果内存中不存在token信息，则读取数据库中存储的信息 5.读取后执行2，3步
	 * 
	 * @param tenanId
	 *            商户号
	 * @param shopId
	 *            天子星门店id
	 * @throws Exception
	 */
	public static Token getToken(String tenanId, String shopId)
			throws Exception {
		return getToken(tenanId, shopId, false);
	}

	/**
	 * 获取饿了么token对象信息
	 * 
	 * @see 1.从内存获取Token信息 2.验证token的有效期是否超时 3.超时后重新刷新获取token
	 *      4.如果内存中不存在token信息，则读取数据库中存储的信息 5.读取后执行2，3步
	 * 
	 * @param tenanId
	 *            商户号
	 * @param shopId
	 *            天子星门店id
	 * @param isRefresh
	 *            是否立即刷新token true是| false否
	 * @return
	 * @throws Exception
	 */
	public static Token getToken(String tenanId, String shopId,
			boolean isRefresh) throws Exception {
		Map<String, Object> valueMap = getTokenKv(tenanId, shopId);

		if (valueMap.containsKey(keyToken)) {
			Token token = (Token) valueMap.get(keyToken);
			long updateTimel = (long) valueMap.get(keyTokenTime);
			String authLevel = (String) valueMap.get(keyTokenAuthLevel);
			String oauthType = (String) valueMap.get(keyTokenOauthType);

			return getExpiresToken(tenanId, shopId, authLevel, oauthType,
					token, updateTimel, isRefresh);
		} else {
			Token token = new Token();

			DBContextHolder.setTenancyid(tenanId);

			GenericDao dao = (GenericDao) SpringConext
					.getBean("genericDaoImpl");
			List<JSONObject> resultJson = dao
					.query4Json(
							tenanId,
							"select accectoken,refeshtoken,expires_in,tokentype,updatetime,shopid,oauth_type from cc_third_tenanttoken_info where tenancyid='"
									+ tenanId + "' and channel='EL09'");

			if (resultJson != null && resultJson.size() > 0) {
				JSONObject row = null;
				JSONObject row2 = null;

				// 循环查找门店信息，如果不存在门店信息，则取门店id为空的记录（此记录为商户授权）。
				for (int i = 0; i < resultJson.size(); i++) {
					JSONObject rowObj = resultJson.get(i);
					String shopId_ = rowObj.optString("shopid");
					if (shopId_.equals(shopId)) {
						row = rowObj;
						break;
					} else if (StringUtils.isEmpty(shopId_)) {
						row2 = rowObj;
					}
				}
				// 如果门店级授权与商户级授权信息都无法获取到，则抛出异常
				if (row == null && row2 == null) {
					log.error("饿了么2.0，无法从天子星存储中获取token信息,商户：{},门店：{}", tenanId,
							shopId);
					throw new Exception("token结果没空,不能从天子星存储中获取token信息");
				}

				row = (row == null ? row2 : row);

				token.setAccessToken(row.optString("accectoken"));
				token.setExpires(row.optInt("expires_in"));
				token.setRefreshToken(row.optString("refeshtoken"));
				token.setTokenType(row.optString("tokentype"));
				String updateTimeStr = row.optString("updatetime");
				String oauthType = row.optString("oauth_type");

				Date updateTimeDt = pasreDate(updateTimeStr, pattern);
				long updateTimel = updateTimeDt.getTime();

				String tem_authLevel = "";
				if (StringUtils.isEmpty(row.optString("shopid"))) {
					tem_authLevel = ELM_AUTH_LEVEL_TENANCY;
				} else {
					tem_authLevel = ELM_AUTH_LEVEL_SHOP;
				}

				token = getExpiresToken(tenanId, shopId, tem_authLevel,
						oauthType, token, updateTimel, isRefresh);
				// 重新存储,门店级
				saveTokenKv(tenanId, shopId, tem_authLevel, oauthType, token,
						updateTimel);

			}

			return token;
		}

	}

	/**
	 * 获取在有效期内的token信息
	 * 
	 * @param tenanId
	 * @param shopId
	 * @param token
	 * @param updatetime
	 * @return
	 * @throws Exception
	 */
	private static Token getExpiresToken(String tenanId, String shopId,
			String level, String oauthType, Token token, long updatetime,
			boolean isRefresh) throws Exception {
		Date currDate = new Date();

		long currSecond = currDate.getTime() / 1000;// 当前秒

		long expires = token.getExpires();

		boolean istest =isTestAuth();

		// 当前时间减去获取token的时间，如果大于有效间，则有效，如果小于则表示token失效，需重新获取.为防止并发时出现token失效的情况，在此让token提前2个小时失效
		//如果是测试环境提前2小时失败，如果生产环境提前1天失效
		if (((updatetime / 1000 + expires - 60 * 60 * (istest ? 2 : 26)) <= currSecond)
				|| isRefresh) {
			log.info(tenanId + "," + shopId + "->验证token失效，进入token刷新流程....");
			token = refreshToken(tenanId, shopId, level, oauthType,
					token.getRefreshToken());
		} else {
			log.info(tenanId + "," + shopId + "->token的处在有效期");
		}

		return token;
	}

	/**
	 * 刷新token值
	 * 
	 * @param tenanId
	 * @param shopId
	 * @param level
	 * @param oauthType
	 * @param refreshToken
	 * @return
	 * @throws Exception
	 */
	public synchronized static Token refreshToken(String tenanId,
			String shopId, String level, String oauthType, String refreshToken)
			throws Exception {
		// 使用config对象，实例化一个授权类
		OAuthClient client = new OAuthClient(getConfig(tenanId, shopId));
		Token token = null;
		if (ELM_OAUTH_TYPE_PERSONAL.equals(oauthType)) {
			token = client.getTokenInClientCredentials();
			log.info("商户号：{},门店号:{},授权级别：{},个人级授权后->获取token:{}", tenanId,
					shopId, level, token.toString());
		} else {
			// 根据refreshToken,刷新token
			token = client.getTokenByRefreshToken(refreshToken);

			log.info(
					"商户号：{},门店号:{},授权级别：{},企业级授权后->refreshToken:{},获取刷新后的token：{}",
					tenanId, shopId, level, refreshToken, token.toString());
		}
		// 存储刷新后的token信息
		saveToken(false, tenanId, shopId, level, oauthType, token);

		return token;
	}

	/**
	 * 存储token信息
	 * 
	 * @param boolean isOuth 是否是认证存储，true|false
	 * @param String
	 *            tenanId 商户号
	 * @param String
	 *            shopId 天子星门店id
	 * @param String
	 *            level 授权级别 ：tenancy｜shop
	 * @param String
	 *            oauthType 授权类型 :1|2 .企业应用授权|个人应用授权
	 * @param Token
	 *            token 饿了么token信息
	 * @throws Exception
	 */
	public static void saveToken(boolean isOuth, String tenanId, String shopId,
			String level, String oauthType, Token token) throws Exception {
		if (StringUtils.isEmpty(token.getAccessToken())
				|| "null".equals(token.getAccessToken())) {
			throw new Exception("token结果没空，不能进行存储，内容：" + token.toString());
		}

		log.info(
				"饿了么2.0存储token,参数：isOuth：{},tenanId:{},shopId:{},level:{},oauthType:{},token:{}->",
				isOuth, tenanId, shopId, level, oauthType, token);

		String condition_shopId = " and shopid='" + shopId + "'";
		String condition_org_shopId = " and shop_id="
				+ (StringUtils.isEmpty(shopId) ? ELM_AOUTH_TENANCY_DEFALUT_SHOPID
						: shopId);

		if (ELM_AUTH_LEVEL_TENANCY.equals(level)) {
			condition_shopId = " and (shopid is null or shopid='')";
			condition_org_shopId = "";
		}

		DBContextHolder.setTenancyid(tenanId);

		GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
		List<JSONObject> resultJson = dao
				.query4Json(
						tenanId,
						"select count(1) as size from cc_third_tenanttoken_info where channel='EL09' and tenancyid='"
								+ tenanId + "'" + condition_shopId);

		Date currDate = new Date();
		String updatetime = date2str(currDate, pattern);

		if (resultJson != null && resultJson.size() > 0) {
			JSONObject row = resultJson.get(0);
			int size = row.optInt("size");
			if (size > 0) {

				dao.execute(
						tenanId,
						"update cc_third_tenanttoken_info set accectoken='"
								+ token.getAccessToken()
								+ "',refeshtoken='"
								+ token.getRefreshToken()
								+ "',expires_in="
								+ token.getExpires()
								+ ",tokentype='"
								+ token.getTokenType()
								+ "',updatetime=to_timestamp('"
								+ updatetime
								+ "','yyyy-mm-dd hh24:mi:ss'),oauth_type='"
								+ oauthType
								+ "',shopid='"
								+ (ELM_AUTH_LEVEL_SHOP.equals(level) ? shopId
										: "") + "' where tenancyid='" + tenanId
								+ "' and channel='EL09' " + condition_shopId);
			} else {

				dao.execute(
						tenanId,
						"insert into cc_third_tenanttoken_info(tenancyid,channel,accectoken,refeshtoken,expires_in,tokentype,updatetime,shopid,oauth_type) values('"
								+ tenanId
								+ "','EL09','"
								+ token.getAccessToken()
								+ "','"
								+ token.getRefreshToken()
								+ "',"
								+ token.getExpires()
								+ ",'"
								+ token.getTokenType()
								+ "',to_timestamp('"
								+ updatetime
								+ "','yyyy-mm-dd hh24:mi:ss'),'"
								+ (ELM_AUTH_LEVEL_SHOP.equals(level) ? shopId
										: "") + "','" + oauthType + "')");
			}
		}

		// 如果为真代表，从授权链路过来的请求，则进行信息更新操作
		if (isOuth) {
			dao.execute(tenanId, "update cc_third_organ_info set auth_level='"
					+ level + "' where channel='EL09'" + condition_org_shopId);
		}
		saveTokenKv(tenanId, shopId, level, oauthType, token,
				currDate.getTime());

	}

	private static Date pasreDate(String time, String pattern)
			throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat(pattern);
		Date strtodate = formatter.parse(time);
		return strtodate;
	}

	private static String date2str(Date time, String pattern)
			throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat(pattern);
		return formatter.format(time);
	}

	/**
	 * 存储token信息到redis中
	 * 
	 * @param String
	 *            tenanId 商户id
	 * @param String
	 *            shopId 天子星门店id
	 * @param String
	 *            level 授权级别 ：tenancy｜shop
	 * @param String
	 *            oauthType 授权类型 :1|2 .企业应用授权|个人应用授权
	 * @param Token
	 *            token 饿了么2.0的令牌信息
	 * @param long updateTime 令牌生成时间
	 * @throws Exception
	 */
	private static void saveTokenKv(String tenanId, String shopId,
			String level, String oauthType, Token token, long updateTime)
			throws Exception {
		CcRedisService rd = (CcRedisService) SpringConext
				.getBean("ccRedisServiceImpl");
		JSONObject json = JSONObject.fromObject(token);
		json.put("updateTime", updateTime);
		json.put("oauthType", oauthType);
		json.put("authLevel", level);

		String key = prefix_token + tenanId;

		// 如果门店id为空，表示商户级授权，存在门店id表示门店级授权
		if (ELM_AUTH_LEVEL_SHOP.equals(level)) {
			key += "_" + shopId;
		}

		rd.saveBykv(key, json.toString(), 0);
	}

	/**
	 * 获取redis中的饿了么2.0的key与密钥
	 * 
	 * @param tenanId
	 * @return
	 * @throws Exception
	 */
	private static Map<String, Object> getTokenKv(String tenanId, String shopId)
			throws Exception {
		Map<String, Object> result = new HashMap<String, Object>();

		CcRedisService rd = (CcRedisService) SpringConext
				.getBean("ccRedisServiceImpl");
		String key = prefix_token + tenanId;

		String value = rd.getByKey(key + "_" + shopId);

		if (!StringUtils.isEmpty(value) && !"null".equals(value)) {
			JSONObject token = JSONObject.fromObject(value);
			result.put(keyToken, JSONObject.toBean(token, Token.class));
			result.put(keyTokenTime, token.optLong("updateTime"));
			result.put(keyTokenAuthLevel, token.optString("authLevel"));
			result.put(keyTokenOauthType, token.optString("oauthType"));
		} else {
			// 门店级token不存在，获取商户级token
			String tenancy_value = rd.getByKey(key);

			if (!StringUtils.isEmpty(tenancy_value)
					&& !"null".equals(tenancy_value)) {
				JSONObject token = JSONObject.fromObject(tenancy_value);
				result.put(keyToken, JSONObject.toBean(token, Token.class));
				result.put(keyTokenTime, token.optLong("updateTime"));
				result.put(keyTokenAuthLevel, token.optString("authLevel"));
				result.put(keyTokenOauthType, token.optString("oauthType"));
			}
		}

		return result;
	}

	/**
	 * 存储饿了么分发的key与密钥到redis中
	 * 
	 * @param tenanId
	 *            商户号
	 * @param key
	 *            键
	 * @param secret
	 *            密钥
	 * @throws Exception
	 */
	public static void saveKeySecretKv(String tenanId, String shopId,
			String level, String key, String secret) throws Exception {
		CcRedisService rd = (CcRedisService) SpringConext
				.getBean("ccRedisServiceImpl");

		String auth_key = prefix_ks + tenanId;

		if (ELM_AUTH_LEVEL_SHOP.equals(level)) {
			auth_key += "_" + shopId;
		}
		rd.saveBykv(auth_key, key + "_" + secret, 0);
	}

	/**
	 * 根据商户号查询饿取分配的key与密钥
	 * 
	 * @param tenanId
	 *            商户号
	 * @param shopId
	 *            门店id
	 * @return Map<String,String> 格式：Map<"key",键>,Map<"secret",密钥>
	 * @throws Exception
	 */
	private static Map<String, String> getKeyScretMap(String tenanId,
			String shopId) throws Exception {
		Map<String, String> kvMap = new HashMap<String, String>();

		CcRedisService rd = (CcRedisService) SpringConext
				.getBean("ccRedisServiceImpl");

		String key = prefix_ks + tenanId;

		String value = rd.getByKey(key + "_" + shopId);

		if (!StringUtils.isEmpty(value) && !"null".equals(value)) {
			String[] arr = value.split("_");
			kvMap.put("key", arr[0]);
			kvMap.put("secret", arr[1]);
		} else {
			// 商户级
			String tenancyValue = rd.getByKey(key);
			if (!StringUtils.isEmpty(tenancyValue)
					&& !"null".equals(tenancyValue)) {
				String[] arr = tenancyValue.split("_");
				kvMap.put("key", arr[0]);
				kvMap.put("secret", arr[1]);
			}
		}

		return kvMap;
	}

	public final static String MD5(String s) {
		char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
				'A', 'B', 'C', 'D', 'E', 'F' };
		try {
			byte[] btInput = s.getBytes();
			// 获得MD5摘要算法的 MessageDigest 对象
			MessageDigest mdInst = MessageDigest.getInstance("MD5");
			// 使用指定的字节更新摘要
			mdInst.update(btInput);
			// 获得密文
			byte[] md = mdInst.digest();
			// 把密文转换成十六进制的字符串形式
			int j = md.length;
			char str[] = new char[j * 2];
			int k = 0;
			for (int i = 0; i < j; i++) {
				byte byte0 = md[i];
				str[k++] = hexDigits[byte0 >>> 4 & 0xf];
				str[k++] = hexDigits[byte0 & 0xf];
			}
			return new String(str);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 根据饿取么的门店id查询天子星映身的门店id与商户号
	 * 
	 * @param String
	 *            elmShopId 饿了么门店id
	 * @return String[] ,[门店id,商户号,版本号]
	 */
	public static String[] getTenancyInfoByElmShopId(String elmShopId)
			throws Exception {
		String[] shopId;
		try {
			CcRedisService rd = (CcRedisService) SpringConext
					.getBean("ccRedisServiceImpl");

			shopId = rd.getByMapField(elmShopReltion, elmShopId).split("@");
		} catch (Exception ex) {
			throw new Exception("未找到门店[" + elmShopId + "]绑定关系!");
		}
		return shopId;
	}

	/**
	 * 存储饿了么饿了么与天子星之间的关系.存储格式：天子星门店ID+@+商户号+@+版本号
	 * 
	 * @param String
	 *            tenantId 商户号
	 * @param String
	 *            elmShopId 饿了么门店id
	 * @param String
	 *            tzxShopId 天子星门店id
	 * @param String
	 *            version 版本，默认1.0
	 * @throws Exception
	 */
	public synchronized static void saveShopRelation(String tenantId,
			String elmShopId, String tzxShopId, String version)
			throws Exception {
		String elmShopMapStr = tzxShopId + "@" + tenantId + "@"
				+ (StringUtils.isEmpty(version) ? "1.0" : version);

		CcRedisService rd = (CcRedisService) SpringConext
				.getBean("ccRedisServiceImpl");
		Map<String, String> elmShopMap = new HashMap<String, String>();
		elmShopMap.put(elmShopId, elmShopMapStr);

		// 存储饿了么与天子星门店间的映身关系
		rd.saveByMap(elmShopReltion, elmShopMap);

		Map<String, String> elmShopVersionMap = new HashMap<String, String>();
		elmShopVersionMap.put(tzxShopId + "@" + tenantId, version);
		rd.saveByMap(elmShopVersion, elmShopVersionMap);

		log.info("饿了么2.0商户ID存入redis里的信息：{}", elmShopMapStr);
	}

	/**
	 * 根据天子星商户号与天子星门店id获取其饿了么渠道的版本信息
	 * 
	 * @param tenantId
	 *            商户号
	 * @param tzxShopId
	 *            天子星门店id
	 * @return
	 * @throws Exception
	 */
	public static String getVersionByTZXshopId(String tenantId, String tzxShopId)
			throws Exception {
		CcRedisService rd = (CcRedisService) SpringConext
				.getBean("ccRedisServiceImpl");

		String version = rd.getByMapField(elmShopVersion, tzxShopId + "@"
				+ tenantId);

		return version;
	}

	/**
	 * 获取缓存中的饿了么2.0授权商户列表
	 * 
	 * @see 1.读取缓存中的授权列表 2.获取列表中授权的商户号与天子星门店id 3.如果没有门店id则以-1替代
	 *      4.组合数据然后以List的形式返回最终结果
	 * 
	 * @return List<String> 商户号+"_"+天子星门店id
	 * @throws Exception
	 */
	public static List<String> getAuthorizedTenancyList() throws Exception {
		List<String> tenancyList = new ArrayList<String>();

		CcRedisService rd = (CcRedisService) SpringConext
				.getBean("ccRedisServiceImpl");
		List<String> lists = rd.getByPatternKey(prefix_token + "*");

		for (String list : lists) {
			String[] arr = list.split("_");
			if (arr.length > 4) {
				tenancyList.add(arr[3] + "_" + arr[4]);
			} else {
				tenancyList.add(arr[3] + "_" + "-1");
			}

		}

		return tenancyList;
	}

	/**
	 * 解绑门店饿了么2.0的授权关系
	 * 
	 * @param tenancyId
	 *            商户号
	 * @param shopId
	 *            天子星门店id
	 */
	public static void unbundShopToken(String tenancyId, String shopId,
			String elmShopId) throws Exception {
		CcRedisService rd = (CcRedisService) SpringConext
				.getBean("ccRedisServiceImpl");

		rd.del(prefix_token + tenancyId + "_" + shopId);
		rd.del(prefix_ks + tenancyId + "_" + shopId);
		rd.delByMapField(elmShopVersion, shopId + "@" + tenancyId);
		rd.delByMapField(elmShopReltion, elmShopId);

		DBContextHolder.setTenancyid(tenancyId);

		GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
		dao.execute(
				tenancyId,
				"delete from cc_third_tenanttoken_info where channel='EL09' and tenancyid='"
						+ tenancyId
						+ "' and shopid='"
						+ shopId
						+ "';update cc_third_organ_info set auth_level=NULL,third_shop_id=NULL where channel='EL09' and shop_id='"
						+ shopId + "';");
	}

	public static void main(String[] args) throws Exception {
		Config cf = new Config(true, "8H4mGnPahr",
				"60bc030d6e8de0d48f480c232748b983");
		// 使用config对象，实例化一个授权类
		OAuthClient client = new OAuthClient(cf);
		// 根据refreshToken,刷新token
		// Token token = client
		// .getTokenByRefreshToken("b04d2b628210facfcffa941348f944eb");
		// System.out.println(token.toString());

		// saveToken("mrtian",token);

		// Token token=new Token();
		// token.setAccessToken("4e098f5fa2ca92de1f05944a80aac3e6");
		// UserService userService = new UserService(cf, token);
		// OUser u=userService.getUser();
		// System.out.println(JSONObject.fromObject(u));
	}

	/**
	 * 获取饿了么外卖应用id及外卖key chang hui add start 2017-12-21
	 * 
	 * @param tenancyId
	 *            商户号，目前可空
	 * @param shopId
	 *            天子星门店号,目前可空
	 * @return Map<String,String>
	 *         格式：<developerId,'应用id'>,<third_shop_id,'第三方店铺id'>
	 */
	public static Map<String, String> getELeDeveloperAndKey(String tenancyId,
			String shopId) throws Exception {
		Map<String, String> eleMap = new HashMap<String, String>();
		try {
			DBContextHolder.setTenancyid(tenancyId);
			GenericDao dao = (GenericDao) SpringConext
					.getBean("genericDaoImpl");
			List<JSONObject> resultJson = dao.query4Json(tenancyId,
					"select third_shop_id,source2 from cc_third_organ_info where channel='"
							+ Constant.ELE_CHANNEL
							+ "' and source2 is not null and tenant_id='"
							+ tenancyId + "' and shop_id='" + shopId
							+ "' limit 1");
			if (resultJson != null && resultJson.size() > 0) {
				JSONObject row = resultJson.get(0);
				String source = row.optString("source2");
				String thirdShopId = row.optString("third_shop_id");
				if (StringUtils.isEmpty(source)
						|| StringUtils.isEmpty(thirdShopId)) {
					log.info("无法获取商户:{} 的饿了么外卖账号信息", tenancyId);

					throw new Exception("无法获取商户的饿了么外卖账号信息");
				}
				eleMap.put("source", source);
				eleMap.put("thirdShopId", thirdShopId);
			}
		} catch (Exception ex) {
			log.error("获取商户:{}  饿了么外卖账号发生异常:{}", tenancyId, ex.getMessage());
			ex.printStackTrace();
			throw new Exception("获取商户饿了么外卖账号发生异常");
		}

		return eleMap;
	}

	// end

	private static boolean isTestAuth() {
		return Boolean
				.parseBoolean(com.tzx.framework.common.constant.Constant.systemMap
						.get("ele_oauth_istest"));
	}
}
