<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
       http://www.springframework.org/schema/tx
       http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-4.0.xsd"
       default-autowire="byName">

    <!-- 配置事物管理器 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dynamicDataSource"/>
    </bean>
    <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate" scope="prototype">
        <property name="dataSource" ref="dynamicDataSource"/>
        <aop:scoped-proxy/>
    </bean>

    <!-- 配置处理事务的通知 -->
    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <!-- 对get/load/search/find开头的方法要求只读事务 -->
            <tx:method name="get*" propagation="SUPPORTS" read-only="true"/>
            <tx:method name="load*" propagation="SUPPORTS" read-only="true"/>
            <tx:method name="search*" propagation="SUPPORTS" read-only="true"/>
            <tx:method name="find*" propagation="SUPPORTS" read-only="true"/>
            <!-- 卡充值、消费、新建事务   -->
            <tx:method name="customerCard*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="requiresnew*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="bonusPointConsume*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="customerCredit*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="coupons*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            
            <!-- 编码生成事务添加 -->
            <tx:method name="getCode*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            
            <!-- 对其它方法要求事务 -->
            <tx:method name="*" propagation="REQUIRED" rollback-for="Exception"/>
        </tx:attributes>
    </tx:advice>
    <aop:config proxy-target-class="true">
        <!-- 前面配置的transactionManager是专对Hibernate的事务管理器, 对所有com.tzx.bo包及其子包下的所有方法添加事务管理 。 -->
        <aop:pointcut id="serviceMethods" expression="execution(* com.tzx.*.bo..*.*(..))"/>
        <!-- 织入 -->
        <aop:advisor advice-ref="txAdvice" pointcut-ref="serviceMethods"/>
    </aop:config>

     <bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>java:comp/env/jdbc/multi_tenancydbjndi</value>
        </property>
    </bean>

    <!--
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <property name="url" value="${jdbc.druid.url}" />
        <property name="username" value="${jdbc.druid.username}" />
        <property name="password" value="${jdbc.druid.password}" />
        <property name="initialSize" value="${jdbc.druid.initialSize}" />
        <property name="minIdle" value="${jdbc.druid.minIdle}" />
        <property name="maxActive" value="${jdbc.druid.maxActive}" />
        <property name="maxWait" value="${jdbc.druid.maxWait}" />
        <property name="timeBetweenEvictionRunsMillis" value="${jdbc.druid.timeBetweenEvictionRunsMillis}" />
        <property name="minEvictableIdleTimeMillis" value="${jdbc.druid.minEvictableIdleTimeMillis}" />
        <property name="validationQuery" value="${jdbc.druid.validationQuery}" />
        <property name="testWhileIdle" value="${jdbc.druid.testWhileIdle}" />
        <property name="testOnBorrow" value="${jdbc.druid.testOnBorrow}" />
        <property name="testOnReturn" value="${jdbc.druid.testOnReturn}" />
        <property name="poolPreparedStatements" value="${jdbc.druid.poolPreparedStatements}" />
    </bean>
   -->

    <bean id="dynamicDataSource"
          class="com.tzx.framework.common.util.dao.datasource.DynamicDataSource">
        <property name="targetDataSources">
            <map></map>
        </property>
        <property name="defaultTargetDataSource" ref="dataSource"/>
    </bean>

    <!-- Druid Spring statis -->
    <bean id="druid-stat-interceptor"
          class="com.alibaba.druid.support.spring.stat.DruidStatInterceptor">
    </bean>

    <bean id="druid-stat-pointcut" class="org.springframework.aop.support.JdkRegexpMethodPointcut"
          scope="prototype">
        <property name="patterns">
            <list>
                <value>com.tzx.*.bo.imp.*</value>
                <value>com.tzx.*.po.*.dao.imp.*</value>
            </list>
        </property>
    </bean>

    <aop:config>
        <aop:advisor advice-ref="druid-stat-interceptor" pointcut-ref="druid-stat-pointcut" />
    </aop:config>


</beans>
