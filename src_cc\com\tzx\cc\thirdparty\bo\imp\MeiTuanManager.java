package com.tzx.cc.thirdparty.bo.imp;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONException;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.google.gson.reflect.TypeToken;
import com.tzx.cc.baidu.entity.Sign;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.baidu.util.SignHolder;
import com.tzx.cc.common.constant.DishOper;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.cc.thirdparty.util.MeiTuanHelper;
import com.tzx.cc.thirdparty.util.XmdWMUtils;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.weixin.common.model.Gps;
import com.tzx.weixin.common.util.MapUtil;

public class MeiTuanManager extends AbstractThirdPartyManager
{

	private static final Logger	logger					= Logger.getLogger(MeiTuanManager.class);
	public static final String	CMD_POI_SAVE			= "poi/save";
	public static final String	CMD_POI_TAG_LIST		= "poiTag/list";
	public static final String	CMD_POI_OPEN			= "poi/open";
	public static final String	CMD_POI_CLOSE			= "poi/close";
	public static final String	CMD_POI_SENDTIME_SAVE	= "poi/sendtime/save";
	public static final String	CMD_SHIPPING_SAVE		= "shipping/save";
	public static final String   CMD_POI_GETIDS="poi/getids";

	public static final String	CMD_FOODCAT_UPDATE		= "foodCat/update";
	public static final String	CMD_FOODCAT_DELETE		= "foodCat/delete";

	public static final String	CMD_FOOD_BATCHINITDATA	= "food/batchinitdata";
	public static final String	CMD_FOOD_DELETE			= "food/delete";
	public static final String	CMD_IMAGE_UPLOAD		= "image/upload";
	
	//更新SKU库存
	public static final String	CMD_FOOD_SKU_STOCK		= "food/sku/stock";

	public static final String	CMD_ORDER_CONFIRM		= "order/confirm";
	public static final String	CMD_ORDER_CANCEL		= "order/cancel";
	public static final String	CMD_ORDER_VIEWSTATUS	= "order/viewstatus";
	//获取 
	public static final String	CMD_COMMENT_REPLY	= "poi/comment/add_reply";
	
	
	//根据订单序号获取订单号 
	public static final String	CMD_ORDER_GETORDERIDBYDAYSEQ	= "order/getOrderIdByDaySeq";
	//根据订单号获取订单详情
	public static final String	CMD_ORDER_GETORDERDETAIL	= "order/getOrderDetail";

	public MeiTuanManager(String tenantId, String shopId)
	{
		super(tenantId, shopId, Constant.MEITUAN_CHANNEL);
	}

	public MeiTuanManager(String tenantId)
	{
		super(tenantId, Constant.MEITUAN_CHANNEL);
	}

	@Override
	public JSONObject getBindShopList(String source, String secret) throws Exception
	{
		
		cmdPost(CMD_POI_GETIDS, source, secret, new HashMap<String,String>(),"get");
			
		return super.getBindShopList(source, secret);
	}

	@Override
	public JSONObject getLocalShopInfo() throws Exception
	{
		JSONObject data = super.getLocalShopInfo();
		List<JSONObject> lineup = getBussinessTime();
		String lineupTimeId = "";
		String bussinessTimeFormat = "";
		for (JSONObject json : lineup)
		{
			lineupTimeId += "," + json.optString("id");
			String start = json.optString("start").equals("000:00") ? "000:00" : json.optString("start");
			String end = json.optString("end").equals("000:00") ? "000:00" : json.optString("end");
			if (!CommonUtil.checkStringIsNotEmpty(start) || !CommonUtil.checkStringIsNotEmpty(end)) continue;
			bussinessTimeFormat += start + "-" + end + ",";
		}
		if (bussinessTimeFormat.length() > 0)
		{
			bussinessTimeFormat = bussinessTimeFormat.substring(0, bussinessTimeFormat.length() - 1);
		}
		if (lineupTimeId.length() > 0)
		{
			lineupTimeId = lineupTimeId.substring(0, lineupTimeId.length() - 1);
		}
		data.put("business_time_format", bussinessTimeFormat);
		data.put("lineup_time_org_id", lineupTimeId);
		data.put("tenant_id", tenantId);
		// 百度坐标转换为火星坐标
		double latitude = data.optDouble("latitude");
		double longitude = data.optDouble("longitude");
		if (!Double.isNaN(latitude) && !Double.isNaN(longitude))
		{
			Gps gps = MapUtil.bd09_To_Gcj02(latitude, longitude);
			double lon = new BigDecimal(gps.getWgLon()).setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
			double lat = new BigDecimal(gps.getWgLat()).setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
			data.put("latitude", lat);
			data.put("longitude", lon);
		}
		return data;
	}

	@Override
	public void postShopInfo(JSONObject params) throws Exception
	{

		String app_id = params.optString("source").trim();
		String secret = params.optString("secret").trim();
		String app_poi_code = shopId + "@" + tenantId;

		// 基本信息
		Map<String, String> postJson = new HashMap<>();
		postJson.put("app_poi_code", app_poi_code);
		postJson.put("name", params.optString("name"));
		postJson.put("address", params.optString("address"));
		postJson.put("latitude", params.optString("latitude"));
		postJson.put("longitude", params.optString("longitude"));
		if(CommonUtil.checkStringIsNotEmpty(params.optString("shop_logo"))){
			postJson.put("pic_url", params.optString("shop_logo"));
		}
		// postJson.put("pic_url_large", params.optString(""));
		postJson.put("phone", params.optString("service_phone"));
		postJson.put("standby_tel", params.optString("phone"));
		postJson.put("shipping_fee", params.optString("delivery_fee"));
		postJson.put("shipping_time", params.optString("business_time_format"));
		// postJson.put("promotion_info", params.optString(""));
		postJson.put("open_level", params.optString("open_level"));
		postJson.put("is_online", params.optString("shop_state"));
		postJson.put("invoice_support", params.optString("invoice_support"));
		postJson.put("invoice_min_price", params.optString("min_invoice_fee"));
		// postJson.put("invoice_description", params.optString(""));
		postJson.put("third_tag_name", params.optString("category3"));
		postJson.put("mt_type_id", params.optString("mt_type_id"));
		// postJson.put("pre_book", "1");
		// postJson.put("time_select", "1");
		// postJson.put("app_brand_code", params.optString("brand"));
		// postJson.put("mt_type_id", params.optString(""));
		cmdPost(CMD_POI_SAVE, app_id, secret, postJson);
		postJson.put("operator", params.optString("operator"));
		logger.info("[更新美团商户]上传数据:"+postJson+"\n返回数据:"+response);

	}

	@Override
	public void postShopStatus(JSONObject params) throws Exception
	{
		String cmdType = params.optString("cmd_type");
		String source = params.optString("source");
		String secret = params.optString("secret");
		String shopId = params.optString("shop_id");
		Map<String, String> map = new HashMap<>();
		map.put("app_poi_code", shopId + "@" + tenantId);
		map.put("app_id", source);
		String type = null;
		switch (cmdType)
		{
			case "open":
				type = CMD_POI_OPEN;
				break;
			case "close":
				type = CMD_POI_CLOSE;
				break;
			default:
				break;
		}

		cmdPost(type, source, secret, map);
		map.put("operator", params.optString("operator"));
		logger.info("[更新美团商户状态]上传数据:"+map+"\n返回数据:"+response);
	}

	@Override
	public JSONObject getShopCategory(JSONObject params) throws Exception
	{
		Map<String, String> parmas = new HashMap<>();
		parmas.put("app_poi_codes", "31");
		String source="205";
		String secret="74cfe629227fde892d723b67837abeec";
		String source_secret_sql="select source,secret from cc_third_organ_info a where a.channel='MT08'";
		List<JSONObject> source_secret_list=this.dao.query4Json(tenantId, source_secret_sql);
		if(source_secret_list.size()>0){
			source=String.valueOf(source_secret_list.get(0).optInt("source"));
			secret=source_secret_list.get(0).optString("secret");
		}
		String result = MeiTuanHelper.sendRequest(CMD_POI_TAG_LIST, source, secret, parmas, "post");
		logger.info("[获取美团菜品分类]上传数据:"+parmas+"\n返回数据:"+result);
		return JSONObject.fromObject(result);
	}

	@Override
	protected void postDeliveryTime(JSONObject params) throws Exception
	{

		String app_id = params.optString("source");
		String secret = params.optString("secret");
		String app_poi_code = shopId + "@" + tenantId;
		String sendTime = params.optString("delivery_time");

		Map<String, String> poi_send_time = new HashMap<>();
		poi_send_time.put("send_time", sendTime);
		poi_send_time.put("app_poi_codes", app_poi_code);
		cmdPost(CMD_POI_SENDTIME_SAVE, app_id, secret, poi_send_time);
		
		poi_send_time.put("operator", params.optString("operator"));
		logger.info("[同步美团商户配送时长]上传数据:"+poi_send_time+"\n返回数据:"+response);

	}

	@Override
	protected void postDeliveryRegion(JSONObject params) throws Exception
	{
		String app_id = params.optString("source");
		String secret = params.optString("secret");
		String app_poi_code = shopId + "@" + tenantId;
		String coordinateStr = params.optString("coordinate");

		String area = "";
		if (CommonUtil.checkStringIsNotEmpty(coordinateStr))
		{
			String[] coordinateStrArray = coordinateStr.split(";");

			for (int i = 0; i < coordinateStrArray.length; i++)
			{
				String[] location = coordinateStrArray[i].split(",");
				Gps gps = MapUtil.bd09_To_Gcj02(Double.valueOf(location[1]), Double.valueOf(location[0]));
				String lon = new BigDecimal(gps.getWgLon() * 1000000).setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
				String lat = new BigDecimal(gps.getWgLat() * 1000000).setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
				area += ",{" + "\"x\":" + lat + ",\"y\":" + lon + "}";
			}
			area = "[" + area.replaceFirst(",", "") + "]";
		}

		Map<String, String> shipping = new HashMap<>();
		shipping.put("app_poi_code", app_poi_code);
		shipping.put("app_shipping_code", params.optString("district_id"));
		shipping.put("type", "1");
		shipping.put("area", area);
		shipping.put("min_price", params.optString("min_order_price"));
		shipping.put("shipping_fee", params.optString("delivery_fee"));

		cmdPost(CMD_SHIPPING_SAVE, app_id, secret, shipping);
		shipping.put("operator", params.optString("operator"));
		logger.info("[同步美团商户配送范围]上传数据:"+shipping+"\n返回数据:"+response);

	}

	@Override
	protected void postDishCategory(JSONObject params) throws Exception
	{
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		getCcBusinessLogBean(params, ccBusniessLogBean);
		ccBusniessLogBean.setCmd("com.tzx.cc.thirdparty.bo.imp.MeiTuanManager:postDishCategory");
		Sign sign = SignHolder.getShopSign(tenantId, String.valueOf(params.optInt("store_id")), channel);
		String app_id = sign.getSource();
		String secret = sign.getSecret();

		Map<String, String> foodCat = new HashMap<>();
		foodCat.put("app_poi_code", String.valueOf(params.optInt("store_id")) + "@" + tenantId);
		if(StringUtils.isNotEmpty(params.optString("id"))){
			foodCat.put("category_name_origin", params.optString("last_send_class_name"));
		}
		foodCat.put("category_name", params.optString("cur_class_name"));
		foodCat.put("sequence", params.optString("rank"));
		
		ccBusniessLogBean.setRequestBody(foodCat.toString());
		try{
		cmdPost(CMD_FOODCAT_UPDATE, app_id, secret, foodCat);
		ccBusniessLogBean.setResponseBody(response.toString());
		}catch(Exception ex){
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(ex));
			ccBusniessLogBean.setIsNormal("0");
			ex.printStackTrace();
		}finally{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		foodCat.put("operator", params.optString("update_operator"));
		logger.info("[推送美团菜品分类]上传数据:"+foodCat+"\n返回数据:"+response);
		
	}
	
	public void getCcBusinessLogBean(JSONObject params,CcBusniessLogBean ccBusniessLogBean) {
		ccBusniessLogBean.setRequestId(params.optString("requestId"));
		ccBusniessLogBean.setTenancyId(tenantId);
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dishCategory");
		ccBusniessLogBean.setChannel(params.optString("channel"));
		ccBusniessLogBean.setChannelName(params.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
		
		
		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("0");

		//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		if(params.containsKey("dishes_class")){
		  ccBusniessLogBean.setThirdId(params.optJSONArray("dishes_class").getJSONObject(0).optString("third_class_id"));
		  ccBusniessLogBean.setTzxId(params.optJSONArray("dishes_class").getJSONObject(0).optString("class_id"));
	      ccBusniessLogBean.setTzxName(params.optJSONArray("dishes_class").getJSONObject(0).optString("cur_class_name"));	
	      ccBusniessLogBean.setShopId(shopId);
		}else{
		  ccBusniessLogBean.setThirdId(params.optString("third_class_id"));
		  ccBusniessLogBean.setTzxId(params.optString("class_id"));
	      ccBusniessLogBean.setTzxName(params.optString("cur_class_name"));	
	      ccBusniessLogBean.setShopId(shopId);
		}

		// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
		ccBusniessLogBean.setOperAction(DishOper.pushDishCategory.toString());
	}
	
	@Override
	protected void postDish(List<JSONObject> params) throws Exception
	{

		Sign sign = SignHolder.getShopSign(tenantId, shopId, channel);
		String app_id = sign.getSource();
		String secret = sign.getSecret();

		String app_poi_code = shopId + "@" + tenantId;

		Map<String, String> foods = new HashMap<String, String>();
		foods.put("app_poi_code", app_poi_code);

		List<JSONObject> ff = new ArrayList<JSONObject>();

		for (JSONObject p : params)
		{

			JSONObject food = new JSONObject();
			food.put("app_poi_code", shopId + "@" + tenantId);
			food.put("app_food_code", p.optString("item_code"));
			food.put("name", p.optString("item_name"));
			food.put("description", p.optString("description"));

			String skus = "";

			if (p.containsKey("priceList") && !"".equals(p.get("priceList")))
			{
				List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(p.get("priceList").toString(), new TypeToken<List<JSONObject>>()
				{
				}.getType());
				for (JSONObject sku : list)
				{
					skus += ",{" + "\"sku_id\":" + sku.optInt("id") + ",\"spec\":\"" + sku.optString("unit_name") + "\",\"price\":" + sku.optString("price") + ",\"stock\":\"*\"" + "}";
				}
			}

			skus = "[" + skus.replaceFirst(",", "") + "]";

			food.put("skus", skus);
			food.put("price", "20");
			// food.put("price", p.optString("price"));
			food.put("min_order_count", p.optString("min_order_num"));
			food.put("unit", p.optString("unit"));
			food.put("box_num", p.optString("package_box_num"));
			food.put("box_price", p.optString("box_price"));
			food.put("category_name", p.optString("last_send_class_name"));
			food.put("is_sold_out", p.optString("is_sold_out"));
			// food.put("picture", p.optString("item_pic"));
			food.put("sequence", p.optString("rank"));
			ff.add(food);
		}
		foods.put("food_data", ff.toString());

		cmdPost(CMD_FOOD_BATCHINITDATA, app_id, secret, foods);

	}

	@Override
	protected void delDish(JSONObject params) throws Exception
	{
		Sign sign = SignHolder.getShopSign(tenantId, params.optString("store_id"), channel);
		String app_id = sign.getSource();
		String secret = sign.getSecret();

		String app_poi_code = params.optString("store_id") + "@" + tenantId;
		String app_food_code = params.optString("item_code");

		Map<String, String> postParams = new HashMap<>();
		postParams.put("app_poi_code", app_poi_code);
		postParams.put("app_food_code", app_food_code);

		cmdPost(CMD_FOOD_DELETE, app_id, secret, postParams);
		
		postParams.put("operator", params.optString("operator"));
		logger.info("[美团商户批量删除菜品信息]上传数据:"+postParams+"\n返回数据:"+response);
	}

	public JSONObject imageUpload(String imageUrl)
	{
		try
		{
			Sign sign = SignHolder.getShopSign(tenantId, shopId, channel);
			String app_id = sign.getSource();
			String secret = sign.getSecret();

			String app_poi_code = shopId + "@" + tenantId;
			String img_name = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);

			Map<String, String> postParams = new HashMap<>();
			postParams.put("app_poi_code", app_poi_code);
			postParams.put("img_name", img_name);

			byte[] fileData = CommonUtil.getFileBytes(imageUrl);

			cmdPost(CMD_IMAGE_UPLOAD, app_id, secret, postParams, fileData);
		}
		catch (Exception e)
		{
			response.put("errno", "1");
			response.put("error", Constant.MSG_1);
			response.put("errmsg", e);
		}

		return response;
	}

	@Override
	public JSONObject postOrderStatus(JSONObject params) throws Exception
	{
		String orderId = params.optString("orderId");
		String operType = params.optString("operType");

		Sign sign = SignHolder.getShopSign(tenantId, shopId, channel);
		String source = sign.getSource();
		String secret = sign.getSecret();

		Map<String, String> postParams = new HashMap<>();
		postParams.put("order_id", orderId);

		switch (operType)
		{
			case ORDER_CONFIRM:
				cmdPost(CMD_ORDER_CONFIRM, source, secret, postParams, "get");
				break;
			case ORDER_CANCEL:
				postParams.put("reason_code", params.optString("type"));
				postParams.put("reason", params.optString("reason"));
				cmdPost(CMD_ORDER_CANCEL, source, secret, postParams, "get");
				break;
			default:
				response.put("errno", -1);
				response.put("error", "不支持的订单操作:" + operType);
		}
		logger.info("[美团订单状态变更]上传数据:"+postParams+"\n返回信息:"+response);
		return response;
	}

	@Override
	public JSONObject getOrderStatus(JSONObject params) throws Exception
	{
		String orderId = params.optString("third_order_id");
		Sign sign = SignHolder.getShopSign(tenantId, shopId, channel);
		String source = sign.getSource();
		String secret = sign.getSecret();
		Map<String, String> m = new HashMap<>();
		m.put("order_id", orderId);
		cmdPost(CMD_ORDER_VIEWSTATUS, source, secret, m, "get");
		logger.info("[获取美团订单状态]上传数据:"+m+"\n返回数据:"+response);
		return response;
	}

	private void cmdPost(String cmd, String appId, String secret, Map<String, String> params) throws Exception
	{
		cmdPost(cmd, appId, secret, params, "post");
	}

	private void cmdPost(String cmd, String appId, String secret, Map<String, String> params, byte[] fileData) throws Exception
	{
		String thirdRespose = MeiTuanHelper.sendRequest(cmd, appId, secret, params, fileData);
		JSONObject result = JSONObject.fromObject(thirdRespose);
		if (result.optString("data").equals("ng"))
		{
			JSONObject error = result.optJSONObject("error");
			response.put("errno", error.optString("code"));
			response.put("error", error.optString("msg"));
		}
		else
		{
			response.putAll(result);
		}
	}

	private void cmdPost(String cmd, String appId, String secret, Map<String, String> params, String method) throws Exception
	{
		String thirdRespose = MeiTuanHelper.sendRequest(cmd, appId, secret, params, method);
		logger.info("美团返回的信息是:"+JSONObject.fromObject(thirdRespose));
		JSONObject result=null;
		try
		{
			result = JSONObject.fromObject(thirdRespose);
			if (result.optString("data").equals("ng"))
			{
				JSONObject error = result.optJSONObject("error");
				response.put("errno", error.optString("code"));
				response.put("error", error.optString("msg"));
			}
			else
			{
				response.putAll(result);
			}		
		}
		catch (Exception e1)
		{
			e1.printStackTrace();
			if(e1 instanceof JSONException){
				response.put("errno", ERROR);
				response.put("error","美团接口未正确响应,请稍后重试!");
			}
		}
		
			
	}

	@Override
	protected void postDishSingle(JSONObject p) throws Exception
	{
		String shop_id = p.optString("shop_id");
		Sign sign = SignHolder.getShopSign(tenantId, shop_id, channel);
		String app_id = sign.getSource();
		String secret = sign.getSecret();

		String app_poi_code = shop_id + "@" + tenantId;
		
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		ccBusniessLogBean.setRequestId(p.optString("requestId"));
		ccBusniessLogBean.setTenancyId(tenantId);
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dish");
		ccBusniessLogBean.setChannel(channel);
		ccBusniessLogBean.setChannelName(channel);//暂时保持原来结构不变，暂时就不去处理该字段内容值
		ccBusniessLogBean.setCmd("com.tzx.cc.thirdparty.bo.imp.MeiTuanManager:postDishSingle");
		
		
		
		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("1");
		//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		
		ccBusniessLogBean.setThirdId(p.optString("third_item_id"));
		ccBusniessLogBean.setTzxId(p.optString("item_id"));
	    ccBusniessLogBean.setTzxName(p.optString("item_name"));
	    ccBusniessLogBean.setShopId(shop_id);
		ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());

		Map<String, String> foods = new HashMap<String, String>();
		foods.put("app_poi_code", app_poi_code);

		List<JSONObject> ff = new ArrayList<JSONObject>();

		JSONObject food = new JSONObject();
		food.put("app_poi_code", shop_id + "@" + tenantId);
		food.put("app_food_code", p.optInt("item_code"));
		food.put("name", p.optString("item_name"));
		food.put("description", p.optString("description"));
     
		String skus = "";
		String price = "";
		String unit_name="";
		JSONObject available_times_obj=new JSONObject();
//		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
//		Date d1 = sdf.parse(p.optString("available_times_start"));
//		Date d2 = sdf.parse(p.optString("available_times_end"));
//		long result = d1.getTime()-d2.getTime();
//		if(result>=0&&!p.optString("available_times_end").equals("24:00")){
//			available_times_obj.put("monday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//			available_times_obj.put("tuesday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//			available_times_obj.put("wednesday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//			available_times_obj.put("thursday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//			available_times_obj.put("friday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//			available_times_obj.put("saturday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");
//			available_times_obj.put("sunday", "00:00"+"-"+p.optString("available_times_end")+","+p.optString("available_times_start")+"-"+"23:59");	
//		}else{
//			if(p.optString("available_times_end").equals("24:00")){
//	        	p.put("available_times_end", "23:59");
//	        }
//			available_times_obj.put("monday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//			available_times_obj.put("tuesday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//			available_times_obj.put("wednesday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//			available_times_obj.put("thursday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//			available_times_obj.put("friday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//			available_times_obj.put("saturday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));
//			available_times_obj.put("sunday", p.optString("available_times_start")+"-"+p.optString("available_times_end"));	
//		
//		}
		//多时段售卖 2017-12-19 start 刘娟 
		if(CommonUtil.checkStringIsNotEmpty(p.optString("dish_available_time"))) {
			Map<String, List<JSONObject>> timezone = new HashMap<String, List<JSONObject>>();
			String dish_available_time = p.optString("dish_available_time");
			available_times_obj = JSONObject.fromObject(dish_available_time);
			Iterator<String> iterator = available_times_obj.keys();
			while(iterator.hasNext()){
				String weekDay = iterator.next();
				if(!CommonUtil.checkStringIsNotEmpty(available_times_obj.optString(weekDay))){
					available_times_obj.put(weekDay, "00:00-23:59");
				}
			}
		}
		//end

		if (p.containsKey("priceList") && !"".equals(p.get("priceList")))
		{
			List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(p.get("priceList").toString(), new TypeToken<List<JSONObject>>()
			{
			}.getType());
			for (JSONObject sku : list)
			{
				unit_name=sku.optString("unit_name");
				price=String.valueOf(sku.optDouble("price"));
				if(available_times_obj.toString().length() > 2){
					skus += ",{" + "\"sku_id\":" + sku.optInt("id") + ",\"spec\":\"" + sku.optString("unit_name") + "\",\"price\":" + sku.optDouble("price") + ",\"stock\":\"*\",\"available_times\":" + available_times_obj.toString() + "" + "}";
				}else{
					skus += ",{" + "\"sku_id\":" + sku.optInt("id") + ",\"spec\":\"" + sku.optString("unit_name") + "\",\"price\":" + sku.optDouble("price") + ",\"stock\":\"*\""+ "}";
				}
				
			}
		}

		skus = "[" + skus.replaceFirst(",", "") + "]";

		food.put("skus", skus);
		food.put("price", price);
		// food.put("price", p.optString("price"));
		food.put("min_order_count", p.optInt("min_order_num"));
//		food.put("unit", p.optInt("unit"));
		food.put("unit", unit_name);//2017-03-03：unit取值改为规格名称
		food.put("box_num", p.optInt("package_box_num"));
		food.put("box_price", p.optDouble("box_price",0.0));
		food.put("category_name", p.optString("last_send_class_name"));
		food.put("is_sold_out", p.optString("is_sold_out"));
		food.put("picture", p.optString("item_pic"));
		
		//at 2017-05-08增加前端可选是进行上传图片，1代表上传，0或其它值代表不上传
		if(!"1".equals(p.optString("isUploadImg"))){
			food.discard("picture");
		}
		
		food.put("sequence", p.optString("rank"));
		ff.add(food);
		foods.put("food_data", ff.toString());
		ccBusniessLogBean.setRequestBody(foods.toString());
		try {
			cmdPost(CMD_FOOD_BATCHINITDATA, app_id, secret, foods);
			ccBusniessLogBean.setResponseBody(response.toString());
		} catch (Exception e) {
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.getStackTrace();
		}finally{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
		}
		
		
		food.put("operator", p.optString("send_operator"));
		logger.info("[美团推送菜品信息]上传数据:"+food+"\n返回数据:"+response);

	}
	
	@Override
	protected void delDishCategory(JSONObject params) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	public JSONObject postOrderModelStatus(JSONObject param, String string)
			throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public JSONObject setCategoryPositions(JSONObject params)
			throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	/**
	 * 添加美团评论
	 * @param p
	 * @return
	 * @throws Exception
	 */
	public JSONObject addCommentReply(JSONObject p) throws Exception {
		String shop_id = p.optString("shop_id");
		String comment_id =p.optString("comment_id");
		String reply =p.optString("reply");
		String app_poi_code =shop_id+"@"+tenantId;
		
		Sign sign = SignHolder.getShopSign(tenantId, shop_id, channel);
		String app_id = sign.getSource();
		String secret = sign.getSecret();
		
		JSONObject params=new JSONObject();
		params.put("app_poi_code", app_poi_code);
		
		String thirdRespose = MeiTuanHelper.sendRequest(CMD_COMMENT_REPLY, app_id, secret, params,"post");
		logger.info("美团返回的信息是:"+JSONObject.fromObject(thirdRespose));
		JSONObject result=null;
			try {
				result = JSONObject.fromObject(thirdRespose);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		return result;
	}

	/**
	 * 
	 * @param p
	 * @return
	 * @throws Exception
	 */
	public List<String> queryThirdOrderList(String tenantId,String tzxShopId,String date,int type,List<String> lists)throws Exception{
		List<String> messageList=new ArrayList<String>();
		
		String app_poi_code=tzxShopId+"@"+tenantId;
		
		Sign sign = SignHolder.getShopSign(tenantId, tzxShopId, Constant.MEITUAN_CHANNEL);
		String app_id = sign.getSource();
		String secret = sign.getSecret();
		
		JSONObject params=new JSONObject();
		
		
		for(String orderIdOrSeq:lists){
			
			if(type==1){
				params.put("app_poi_code", app_poi_code);
				params.put("date_time", date.replace("-", ""));
				params.put("day_seq", orderIdOrSeq);
				String thirdRespose = MeiTuanHelper.sendRequest(CMD_ORDER_GETORDERIDBYDAYSEQ, app_id, secret, params,"post");
				if(!StringUtils.isEmpty(thirdRespose)){
					orderIdOrSeq="";
					JSONObject result=JSONObject.fromObject(thirdRespose);
					
					if (result.optString("data").equals("ng")){
						logger.info(date+",商户："+tenantId+",门店："+tzxShopId+",美团没有根据序号["+orderIdOrSeq+"]查到结果");
					}else{
						JSONObject dataJson=result.getJSONObject("data");
						orderIdOrSeq=dataJson.optString("order_id","");
					}
				}
				
			}
			
			if(!StringUtils.isEmpty(orderIdOrSeq)){
				params.clear();
				
				params.put("order_id", orderIdOrSeq);
				params.put("is_mt_logistics", "1");
				String thirdRespose = MeiTuanHelper.sendRequest(CMD_ORDER_GETORDERDETAIL, app_id, secret, params,"get");
				JSONObject result=JSONObject.fromObject(thirdRespose);
				
				if (result.optString("data").equals("ng")){
					logger.info(date+",商户："+tenantId+",门店："+tzxShopId+",美团没有根据序号["+orderIdOrSeq+"]查到结果");
				}else{
					JSONObject dataJson=result.getJSONObject("data");
					//9代表取消
					if("9".equals(dataJson.optString("status"))){
						dataJson.put("tzxStatus", "08");
					}else{
						dataJson.put("tzxStatus", "10");
					}
					
					dataJson.put("tzxCreateAt", CommonUtil.getDateFromSeconds(String.valueOf(dataJson.optString("ctime"))));
					dataJson.put("thirdOrderCode", dataJson.optString("order_id"));
					messageList.add(dataJson.toString());
					
					logger.info("商户："+tenantId+",门店："+tzxShopId+",获取美团订单详情完成->"+dataJson);
				}
			}
		}
		return messageList;
	}

	@Override
	public void updateDishStockSingle(JSONObject p) throws Exception {
		
		String shop_id = String.valueOf(p.optInt("store_id"));
		Sign sign = SignHolder.getShopSign(tenantId, shop_id, channel);
		String app_id = sign.getSource();
		String secret = sign.getSecret();
		String ePoiId = shop_id + "@" + tenantId;
		Map<String, String> foods = new HashMap<String, String>();
		foods.put("app_poi_code", ePoiId);

		List<JSONObject> ff = new ArrayList<JSONObject>();

		if(p.containsKey("stockList") && p.getJSONArray("stockList").size() > 0){
			List<JSONObject> stockList = p.getJSONArray("stockList");
			for(JSONObject json : stockList){
				JSONObject food = new JSONObject();
				food.put("app_food_code", json.optInt("item_code"));
		     
				String skus = "";
				skus += ",{" + "\"sku_id\":" + json.optInt("unit_id") + ",\"stock\":" + p.optInt("stock") + "}";
				skus = "[" + skus.replaceFirst(",", "") + "]";
				food.put("skus", skus);
				ff.add(food);
			}
		}else{
			JSONObject food = new JSONObject();
			food.put("app_food_code", p.optInt("item_code"));
	     
			String skus = "";
			skus += ",{" + "\"sku_id\":" + p.optInt("unit_id") + ",\"stock\":" + p.optInt("stock") + "}";
			skus = "[" + skus.replaceFirst(",", "") + "]";
			food.put("skus", skus);
			ff.add(food);
		}
		
		foods.put("food_data", ff.toString());
		try {
			cmdPost(CMD_FOOD_SKU_STOCK, app_id, secret, foods);
		} catch (Exception e) {
			e.getStackTrace();
		}finally{
			if(response.containsKey("data") && "ok".equals(response.optString("data"))){
				response.put("stockResult", SUCCESS);
			}else{
				response.put("stockResult", ERROR);
				if("805".equals(response.optString("errno"))){
					List<String> listError = new ArrayList<String>();
					String errorStr = response.optString("error").split(":")[1];
					String errorInfo = errorStr.substring(1, errorStr.length()-1);
					String [] errorIds = errorInfo.split(",");
					for(String s : errorIds){
						listError.add(s);
					}
					response.put("errorItemCodes", listError);
				}
			}
		}
		foods.put("operator", p.optString("send_operator"));
		logger.info("[美团更新SKU库存]上传数据:"+foods+"\n返回数据:"+response);
	}
	
}
