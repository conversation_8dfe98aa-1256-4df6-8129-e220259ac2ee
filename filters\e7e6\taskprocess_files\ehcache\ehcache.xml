<?xml version="1.0" encoding="UTF-8"?>
<ehcache>

    <diskStore path="java.io.tmpdir"/>

    <defaultCache eternal="false"
                  maxEntriesLocalHeap="1000"
                  overflowToDisk="false"
                  diskPersistent="false"
                  timeToIdleSeconds="3600"
                  timeToLiveSeconds="3600"/>

    <cache name="defaultDishListCache"
           eternal="false"
           maxEntriesLocalHeap="200"
           overflowToDisk="false"
           diskPersistent="false"
           timeToIdleSeconds="60"
           statistics="true"
           timeToLiveSeconds="60"/>

    <!--
        eternal="false"   // 元素是否永恒,如果是就永不过期(必须设置)
        maxElementsInMemory="1000" // 缓存容量的内存最大值(必须设置)
        overflowToDisk="false"  // 当缓存达到maxElementsInMemory值是,是否允许溢出到磁盘(必须设置)
        diskPersistent="false"  // 磁盘缓存在VM重新启动时是否保持(默认为false)
        timeToIdleSeconds="0" // 导致元素过期的访问间隔(秒为单位). 0表示可以永远空闲,默认为0
        timeToLiveSeconds="600" // 元素在缓存里存在的时间(秒为单位). 0 表示永远存在不过期
        memoryStoreEvictionPolicy="LFU" // 当达到maxElementsInMemory时,如何强制进行驱逐默认使用"最近使用(LRU)"策略,其它还有先入先出FIFO,最少使用LFU,较少使用LRU
    -->
</ehcache>