package com.tzx.cc.datasync.bo.dto.newpos;


public class CallCenterConstant
{

	/**
	 * 订单类型 外送订单
	 */
	public static final String	OUTSIDE_ORDER							= "OUTSIDE_ORDER";
	/**
	 * 订单类型 预定订单
	 */
	public static final String	RESERVATION_ORDER						= "RESERVATION_ORDER";
	/**
	 * 订单类型 点菜订单
	 */
	public static final String	ORDERING_DISHES_ORDER					= "ORDERING_DISHES_ORDER";
	/**
	 * 默认企业来源
	 */
	public static final String	DEFAULT_BEFORE_SOURCE					= "ZKF";
	/**
	 * 订单来源 网站
	 */
	public static final String	WEBSITE									= "WEBSITE";
	/**
	 * 订单来源 APP
	 */
	public static final String	APP										= "APP";
	/**
	 * 订单来源 呼叫中心
	 */
	public static final String	CALLCENTER								= "CALLCENTER";

	/**
	 * 优惠方案 买赠
	 */
	public static final String	SPECIAL_OFFERS_TYPE_BUY$GIFT			= "SPECIAL_OFFERS_TYPE_BUY$GIFT";
	/**
	 * 优惠方案 折扣
	 */
	public static final String	SPECIAL_OFFERS_TYPE_BUY$DISCOUNT		= "SPECIAL_OFFERS_TYPE_BUY$DISCOUNT";
	/**
	 * 优惠方案 优惠价
	 */
	public static final String	SPECIAL_OFFERS_TYPE_BUY$ADDITION		= "SPECIAL_OFFERS_TYPE_BUY$ADDITION";

	/**
	 * 优惠方案给付方式 附加
	 */
	public static final String	SPECIAL_OFFERS_DETAIL_TYPE_ADD			= "SPECIAL_OFFERS_DETAIL_TYPE_ADD";
	/**
	 * 优惠方案给付方式 替换
	 */
	public static final String	SPECIAL_OFFERS_DETAIL_TYPE_MODIFY		= "SPECIAL_OFFERS_DETAIL_TYPE_MODIFY";

	/**
	 * 订单内物品属性 普通
	 */
	public static final String	ORDER_PRODUCT_TYPE_COMMON				= "ORDER_PRODUCT_TYPE_COMMON";
	/**
	 * 订单内物品属性 活动赠品
	 */
	public static final String	ORDER_PRODUCT_TYPE_GIFT					= "ORDER_PRODUCT_TYPE_GIFT";
	/**
	 * 订单内物品属性 活动修改
	 */
	public static final String	ORDER_PRODUCT_TYPE_MODIFY				= "ORDER_PRODUCT_TYPE_MODIFY";

	/**
	 * 安卓APP应用平台
	 */
	public static final String	APP_PLATFORM_TYPE_ANDROID				= "APP_PLATFORM_TYPE_ANDROID";

	/**
	 * IOS APP应用平台
	 */
	public static final String	APP_PLATFORM_TYPE_IOS					= "APP_PLATFORM_TYPE_IOS";

	/**
	 * 餐谱类型-标准餐谱
	 */
	public static final String	SPECTRUM_TYPE_STANDARD					= "STANDARD";

	/**
	 * 安卓 APP下载地址
	 */
	public static final String	APP_DOWNLOAD_URL_ANDROID				= "http://www.zkungfu.com/file/apk/zkungfu.apk";

	/**
	 * IOS APP下载地址
	 */
	public static final String	APP_DOWNLOAD_URL_IOS					= "https://itunes.apple.com/us/app/zhen-gong-fu-gong-fu-song/id832379884?ls=1&mt=8";

	/**
	 * 最低消费多少，可以不收费用
	 */
	public static int			MIN_XF									= 9999999;

	/**
	 * 送餐费用
	 */
	public static int			SC_XF									= 5;

	/**
	 * MQ消息类型 ： 是否启用
	 */
	public static String		MQ_TYPE_ISSUED							= "issued";
	/**
	 * MQ消息类型 ：下单
	 */
	public static String		MQ_TYPE_CALLCENTER_ORDER				= "callcenter_order";
	/**
	 * MQ消息类型 ：更新订单状态
	 */
	public static String		MQ_TYPE_CALLCENTER_ORDER_UPDATE_STATE	= "callcenter_order_update_state";
	/**
	 * MQ消息类型 ：是否在线
	 */
	public static String		MQ_TYPE_ONLINECHECK						= "onlinecheck";
	// 订单来源 总部来源
	public static final String	TOTAL_SOURCE							= "0";
	// 订单来源 门店来源
	public static final String	SHOP_SOURCE								= "1";
	// 在线取消需要的状态
	public static final String	NUM_STR_SEVEN							= "7";

	// 字符串0
	public static final String	ZERO_STR								= "0";
	// 数字-1
	public static final int		NUM_FU_ONE								= -1;
	// 数字字符串1
	public static final String	ONE_STR									= "1";
	// 数字字符串2
	public static final String	TWO_STR									= "2";
	// 数字字符串3
	public static final String	THREE_STR								= "3";
	public static final String	FIVE_STR								= "5";
	public static final String	SIX_STR									= "6";
	public static final String	SEVEN_STR								= "7";
	public static final String	EIGHT_STR								= "8";
	public static final String	ELEVEN_STR								= "11";
	// 12字符串
	public static final String	TWELVE_STR								= "12";

	// 订单下发失败状态
	public static final String	NEGTIVE_ONE_STR							= "-1";

	// 空字符串
	public static final String	BLANK_STR								= "";
	// null空字符串
	public static final String	NULL_STR								= "null";
	// 开始时间
	public static final String	START_TIME								= " 00:00:00";
	// 结束时间
	public static final String	END_TIME								= " 23:59:59";
	// 逗号
	public static final String	COMMA_STR								= ",";
	// 数字1
	public static final int		NUM_ONE									= 1;
	// 数字0
	public static final int		NUM_ZERO								= 0;
	// 数字10
	public static final int		NUM_TEN									= 10;
	// 数字3
	public static final int		NUM_THREE								= 3;
	// 数字10000
	public static final int		NUM_THOUSAND							= 1000;

	// @符号
	public static final String	MARK_AT									= "@";

	// 省市缓存.
	public static final String	PROVINCES_CACHE							= "provincesCache";

	// 区域缓存.
	public static final String	CITY_CACHE								= "cityCache";
	// 百度地图缓存.
	public static final String	BMAP_CACHE								= "BmapCache";

	// 地区缓存版本
	public static final String	CC_REGION_VERSION						= "CC_REGION_VERSION";
	
	
	
	// 数据字典类型-线上取消
	public static final String	CC_DICTIONARY_TYPE_CANCEL_TYPE_ONLINE	= "CANCEL_TYPE_ONLINE";
	
	// 数据字典类型-投诉原因
	public static final String	CC_DICTIONARY_TYPE_COMPLAINTS			= "COMPLAINTS";
		
	
	
}
