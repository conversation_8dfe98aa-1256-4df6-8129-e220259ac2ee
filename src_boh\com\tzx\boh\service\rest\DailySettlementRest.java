package com.tzx.boh.service.rest;

import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.boh.bo.DailySettlementService;
import com.tzx.framework.bo.SupplyChainSynchronizationService;
import com.tzx.hq.bo.ReconciliationService;
import com.tzx.weixin.po.redis.dao.RedisTemplateDao;

import net.sf.json.JSONObject;

/**
 * 日结服务
 *
 * <AUTHOR>
 *
 */
@Controller("DailySettlementRest")
@RequestMapping("/boh/dailySettlementRest")
public class DailySettlementRest
{
    @Resource(name = DailySettlementService.NAME)
    private DailySettlementService	dailySettlementService;

    @Resource(name = RedisTemplateDao.NAME)
    private RedisTemplateDao rdao;
    
    @Resource(name = ReconciliationService.NAME)
    private ReconciliationService reconciliationService;
    
    @Resource(name = SupplyChainSynchronizationService.NAME)
	SupplyChainSynchronizationService supplyChainSynService;

    /** 日结
     * @param request
     * @param response
     */
    @RequestMapping(value = "/dailySettlement")
    public void dailySettlement(HttpServletRequest request, HttpServletResponse response)
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject result = JSONObject.fromObject("{}");
        String jkey = "rijie"+"_"+session.getAttribute("organ_id").toString()+"_"+session.getAttribute("tenentid").toString();
        String okey = "org"+"_"+session.getAttribute("organ_id").toString()+"_"+session.getAttribute("tenentid").toString();
        JSONObject json = new JSONObject();
        try
        {
            if("0".equals(session.getAttribute("organ_id").toString()))
            {
                throw new Exception("请使用门店账号操作门店日结功能！");
            }

            if(rdao.hasKey(okey))
            {
                throw new Exception("门店正在数据整理，请稍后日结!");
            }

            if(rdao.hasKey(jkey))
            {
                result.put("success", false);
                result.put("msg", "当前门店正在日结");
            }
            else
            {
                rdao.set(jkey,"1");
                rdao.expire(jkey, 10*60l);
                Map<String, String[]> map = request.getParameterMap();

                for (String key : map.keySet())
                {
                    result.element(key, map.get(key)[0]);
                }

                if(result.optInt("store_id")==0)
                {
                    result.element("store_id",session.getAttribute("store_id"));
                }
                dailySettlementService.beforedailySettlement((String) session.getAttribute("tenentid"), result);
//                dailySettlementService.dailySettlement((String) session.getAttribute("tenentid"), result);
                rdao.delete(jkey);
                result.element("success", true);
                
                // 检测有问题的渠道支付数据并保存到MongoDB数据库（异步执行） 2017.12.19增加
                int storeId = Integer.parseInt(session.getAttribute("store_id").toString());
                String tenancyId = session.getAttribute("tenentid").toString();
                String dayCount = request.getParameter("day_count");
            	try {
        			reconciliationService.getPaymentOrderList(storeId, tenancyId, dayCount);
        		} catch (Exception e) {
        			e.printStackTrace();
        		}
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            result.element("success", false);
            result.element("msg", e.getMessage());
            if(rdao.hasKey(jkey))
            {
                rdao.delete(jkey);
            }

        }
        finally
        {
            try
            {
            	//鼎王专用
            	if(session.getAttribute("tenentid").equals("xindingwangwulao")){
            		if(result.getBoolean("success")){
            			json.element("sync_time", result.getString("day_count"));
            			json.element("sync_type", "MDRJ");
            			json.element("store_id", session.getAttribute("store_id"));
            			//数据同步
            			supplyChainSynService.syncSupplyChainInfo((String)session.getAttribute("tenentid"), json);
                	}
            	}
                out = response.getWriter();
                out.print(result.toString());
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }

    /** 日结
     * @param request
     * @param response
     */
    @RequestMapping(value = "/batchDailySettlement")
    public void batchDailySettlement(HttpServletRequest request, HttpServletResponse response)
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject result = JSONObject.fromObject("{}");
        String jkey = "rijie"+"_"+session.getAttribute("organ_id").toString()+"_"+session.getAttribute("tenentid").toString();
        try
        {
            if("0".equals(session.getAttribute("organ_id").toString()))
            {
                throw new Exception("请使用门店账号操作门店日结功能！");
            }


            if(rdao.hasKey(jkey))
            {
                result.put("success", false);
                result.put("msg", "当前门店正在日结");
            }
            else
            {
                rdao.set(jkey,"1");
                rdao.expire(jkey, 10*60l);
                Map<String, String[]> map = request.getParameterMap();

                for (String key : map.keySet())
                {
                    result.element(key, map.get(key)[0]);
                }

                if(result.optInt("store_id")==0)
                {
                    result.element("store_id",session.getAttribute("store_id"));
                }

                dailySettlementService.batchDailySettlement((String) session.getAttribute("tenentid"), result);
                rdao.delete(jkey);
                result.element("success", true);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            result.element("success", false);
            result.element("msg", e.getMessage());
            if(rdao.hasKey(jkey))
            {
                rdao.delete(jkey);
            }

        }
        finally
        {
            try
            {
                out = response.getWriter();
                out.print(result.toString());
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }

    /** 查询日结
     * @param request
     * @param response
     */
    @RequestMapping(value = "/findDailySettlementStatus")
    public void findDailySettlementStatus(HttpServletRequest request, HttpServletResponse response)
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject result = JSONObject.fromObject("{}");
        try
        {
            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                result.element(key, map.get(key)[0]);
            }
            if(result.optInt("store_id")==0)
            {
                result.element("store_id",session.getAttribute("store_id"));
            }
            //result.element("store_id",session.getAttribute("store_id"));
            result = dailySettlementService.findDailySettlementStatus((String) session.getAttribute("tenentid"), result);

            result.element("success", true);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            result.element("success", false);
            result.element("msg", e.getMessage());
        }
        finally
        {
            try
            {
                out = response.getWriter();
                out.print(result.toString());
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }

    /**
     * 查询跨天营业信息
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/getCrossValue")
    public void getCrossValue(HttpServletRequest request, HttpServletResponse response)
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject result = null;
        try {
            String store_id = request.getParameter("store_id");
            result = dailySettlementService.getCrossValue((String) session.getAttribute("tenentid"), store_id);
            result.element("success", true);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            result.element("success", false);
            result.element("msg", e.getMessage());
        }
        finally
        {
            try
            {
                out = response.getWriter();
                out.print(result.toString());
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }
    
    
    @RequestMapping(value = "/checkOpenHq")
    public void checkOpenHq(HttpServletRequest request, HttpServletResponse response)
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject result = JSONObject.fromObject("{}");
        try
        {
        	JSONObject obj = JSONObject.fromObject("{}");
        	obj.put("store_id", request.getParameter("store_id"));
        	result = dailySettlementService.checkOpenHq((String) session.getAttribute("tenentid"), obj);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            result.element("msg", e.getMessage());

        }
        finally
        {
            try
            {
                out = response.getWriter();
                out.print(result.toString());
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }


    @RequestMapping(value = "/checkOpenScm")
    public void checkOpenScm(HttpServletRequest request, HttpServletResponse response)
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        HttpSession session = request.getSession();
        JSONObject result = JSONObject.fromObject("{}");
        try
        {
            JSONObject obj = JSONObject.fromObject("{}");
            obj.put("store_id", request.getParameter("store_id"));
            result = dailySettlementService.checkOpenScm((String) session.getAttribute("tenentid"), obj);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            result.element("msg", e.getMessage());

        }
        finally
        {
            try
            {
                out = response.getWriter();
                out.print(result.toString());
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }

}
