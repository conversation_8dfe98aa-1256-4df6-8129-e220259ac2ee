package com.tzx.cc.baidu.util;

import java.io.IOException;
import java.util.Properties;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.core.io.Resource;

import com.tzx.framework.common.util.SpringConext;


/**
 * 配置信息读取工具类
 * <AUTHOR>
 * @since 2016年11月2日18:07:07
 */
public class SuweiPropertyUtil {
	private static final Logger logger = Logger.getLogger(SuweiPropertyUtil.class);
    private static Properties properties;

    /**
     * 获取配置值
     * @param key
     * @return
     */
    private static String get(String key){
        if(properties == null){
            synchronized (SuweiPropertyUtil.class){
                if(properties == null){
                    properties = new Properties();
                    try {
                        Resource[] resources = SpringConext.getApplicationContext().getResources("classpath:/suwei/*.properties");
                        for(Resource r : resources){
                            properties.load(r.getInputStream());
                        }
                    } catch (IOException e) {
                        logger.error("加载响应码配置文件出错!",e);
                    }
                }
            }
        }
        return StringUtils.defaultIfEmpty(properties.getProperty(key),StringUtils.EMPTY);
    }

    /**
     *
     * @param key
     * @param strs
     * @return
     */
    public static String getMsg(String key, String... strs){
        return String.format(get(key),strs);
    }
}
