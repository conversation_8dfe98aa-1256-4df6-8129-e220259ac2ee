package com.tzx.report.bo.imp.boh;

import javax.annotation.Resource;

import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ReportExportUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.bo.boh.MonthlyOperationReportService;
import com.tzx.report.bo.commonreplace.CommonMethodAreaService;
import com.tzx.report.po.boh.dao.MonthlyOperationReportDao;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service(MonthlyOperationReportService.NAME)
public class MonthlyOperationReportServiceImp implements MonthlyOperationReportService
{
	 @Resource(name = MonthlyOperationReportDao.NAME)
	 MonthlyOperationReportDao monthlyOperationReportDao;
	 
	 @Resource
	 private CommonMethodAreaService commonMethodAreaService;
	 
	 @Resource(name = "genericDaoImpl")
	 private GenericDao	dao;

	@Resource
	ConditionUtils conditionUtils;
	 
	@Override
	public JSONObject getMonthlyOperationData(String tenancyID,JSONObject condition) throws Exception {
		return monthlyOperationReportDao.getMonthlyOperationData(tenancyID, condition);
	}
	
	@Override
	public JSONObject getMonthlyOperationTopData(String tenancyID,JSONObject condition) throws Exception {
		return monthlyOperationReportDao.getMonthlyOperationTopData(tenancyID, condition);
	}

	
	@Override
	public JSONObject getClassAndBusinessTypeDetails(String attribute, JSONObject p ) throws Exception { 
		// 查询班次
		
		return null ;
	}

	@Override
	public HSSFWorkbook exportData(String attribute, JSONObject p,
								   HSSFWorkbook workBook) throws Exception {

		// 备份一个json
		String type =p.optString("selectType");
		Integer rowNum=3;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		paramData.put("strtIndex",2);
		//Integer stratIndex = 0 ;
		p.element("exportdataexpr", "");
		JSONObject findResult= getMonthlyOperationData(attribute, p);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		JSONObject out1Result =null;
		//创建sheet 表格   同时还可以设置名字!
		HSSFSheet sheet1=workBook.createSheet("月度营运报告");

		//拼接动态列(餐券,免单,班次)
		JSONObject columnObject = findResult.optJSONObject("columnObject");
		//如果动态列是全不显
		String cqColumn = "",mdColumn = "",cqColumnName = "",mdColumnName = "";
		if (null == columnObject){

		}else {
			//餐券动态列
			cqColumn = columnObject.optString("cqColumn");
			cqColumnName = columnObject.optString("cqColumnName");
			//免单动态列
			mdColumn = columnObject.optString("mdColumn");
			mdColumnName = columnObject.optString("mdColumnName");
			//班次显示所有的班次列

		}

//		String [] listTitleName = new String[] {"星期","日期","特殊事件","天气","￥每日","￥累计","累计预算完成%","同比%","环比%","每日","累计","同比%","环比%",  "￥每日","￥累计","￥同比","￥环比","消费客数","累计客数","人均消费",  "￥退款",  "桌台数","翻台率","座位数","上座率",  "门店代金券","公司代金券","生日券", "超时券","排位券","服务费",  "折扣","折让","奉送",  "招待","试餐","宴请",  "￥营业实收","消费账单","实收占比%",  "￥营业实收","消费账单","实收占比%",  "￥营业实收","消费账单","实收占比%",  "￥营业实收","消费账单","实收占比%",  "￥营业实收","消费账单","实收占比%",  "￥营业实收","消费账单","实收占比%",  "￥营业实收","消费账单","实收占比%",  "￥营业实收","消费账单","实收占比%","￥累计营业实收",  "￥营业实收","消费账单","实收占比%","￥累计营业实收",  "￥营业实收","消费账单","实收占比%","￥累计营业实收",
//				"每日","累计",  "每日","累计",  "每日","累计",  "每日","%",  "金额","%",  "每日","累计",  "用量","￥金额",  "用量","￥金额","万元","累计",  "用量","￥金额","万元","累计",};
		String [] listTitleName = new String[] {"星期","日期","特殊事件","天气","￥每日","￥累计","累计预算完成%","同比%","环比%","每日","累计","同比%","环比%",  "￥每日","￥累计","￥同比","￥环比","消费客数","累计客数","人均消费",  "￥退款",  "桌台数","翻台率","座位数","上座率" };
		String [] listTitleName1 = new String[] {"￥营业实收","消费账单","实收占比%","￥累计营业实收",  "￥营业实收","消费账单","实收占比%","￥累计营业实收",  "￥营业实收","消费账单","实收占比%","￥累计营业实收",
				"每日","累计",  "每日","累计",  "每日","累计",  "每日","%",  "金额","%",  "每日","累计",  "用量","￥金额",  "用量","￥金额","万元","累计",  "用量","￥金额","万元","累计"};

		ArrayList<String> titleNameList = new ArrayList<String>(listTitleName.length);
		Collections.addAll(titleNameList, listTitleName);
		ArrayList<String> titleNameList1 = new ArrayList<String>(listTitleName1.length);
		Collections.addAll(titleNameList1, listTitleName1);
		/*String [] dataName = new String[] {"week","report_date","special_event","weather","real_amount","total_real_amount","real_amount_completion","real_amount_on_year_on_year_basis","real_amount_link_relative_ratio","bill_num","total_bill_num","bill_num_on_year_on_year_basis",
				"bill_num_link_relative_ratio","avg_bill","total_avg_bill","avg_bill_on_year_on_year_basis","avg_bill_link_relative_ratio","person","total_person","avg_person","back_money","table_num","table_turnover_rate",
				"seat_num","seat_turnover_rate","store_cash_coupon","comp_cash_coupon","birthday_coupon","timeout_coupon","line_up_coupon","sevice_fee",
				"discount_k","discount_r","give_away_free","entertain","test_meal","fete","ZB02_bill_amount","ZB02_bill_num",
				"ZB02_payment_rate","ZB01_bill_amount","ZB01_bill_num","ZB01_payment_rate","YB04_bill_amount","YB04_bill_num","YB04_payment_rate","ZB01_bill_amount","ZB01_bill_num","ZB01_payment_rate","ZB02_bill_amount",
				"ZB02_bill_num","ZB02_payment_rate","ZB01_bill_amount","ZB01_bill_num","ZB01_payment_rate","WB03_bill_amount","WB03_bill_num","WB03_payment_rate","ts_payment_amount","ts_bill_num",
				"ts_payment_rate","ts_add_payment_amount","wsm_payment_amount","wsm_bill_num","wsm_payment_rate","wsm_add_payment_amount","ws_payment_amount",
				"ws_bill_num","ws_payment_rate","ws_add_payment_amount","working_hours","total_working_hours","pre_time","total_per_time",
				"salary","total_salary","staff_food","staff_food_rate",
				"drop_money","staff_food_rate","diff_rate","total_diff_rate","water_num","water_money",
				"electric_num","electric_money","electric_million","total_electric_money","gas","gas_money",
				"gas_million","total_gas_money",};*/
		String[] dataName = new String[] {"week","report_date","special_event","weather","real_amount","total_real_amount","real_amount_completion","real_amount_on_year_on_year_basis","real_amount_link_relative_ratio","bill_num","total_bill_num","bill_num_on_year_on_year_basis",
				"bill_num_link_relative_ratio","avg_bill","total_avg_bill","avg_bill_on_year_on_year_basis","avg_bill_link_relative_ratio","person","total_person","avg_person","back_money","table_num","table_turnover_rate",
				"seat_num","seat_turnover_rate",};
		String[] dataName1 = new String[] {"ts_payment_amount","ts_bill_num",
				"ts_payment_rate","ts_add_payment_amount","wsm_payment_amount","wsm_bill_num","wsm_payment_rate","wsm_add_payment_amount","ws_payment_amount",
				"ws_bill_num","ws_payment_rate","ws_add_payment_amount","working_hours","total_working_hours","pre_time","total_per_time",
				"salary","total_salary","staff_food","staff_food_rate",
				"drop_money","staff_food_rate","diff_rate","total_diff_rate","water_num","water_money",
				"electric_num","electric_money","electric_million","total_electric_money","gas","gas_money",
				"gas_million","total_gas_money",};
		ArrayList<String> dataNameList = new ArrayList<String>(dataName.length);
		Collections.addAll(dataNameList, dataName);
		ArrayList<String> dataNameList1 = new ArrayList<String>(dataName1.length);
		Collections.addAll(dataNameList1, dataName1);
		/*String [] dataType = new String[] {"String","String","String","String","0.00","0.00","0.00%","0.00%","0.00%","0.00","0.00","0.00%","0.00%","0.00","0.00","0.00%","0.00%","0.00","0.00","0.00",
				"0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","String","String","String","0.00","0.00","0.00",
				"0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00",
				"0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00",
				"0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00",};*/
		String[] dataType = new String[]{"String","String","String","String","0.00","0.00","0.00%","0.00%","0.00%","0.00",
				"0.00","0.00%","0.00%","0.00","0.00","0.00%","0.00%","0.00","0.00","0.00",
				"0.00","0.00","0.00","0.00","0.00",};
		String[] dataType1 = new String[]{"0.00","0.00","0.00%","0.00","0.00","0.00","0.00%","0.00","0.00","0.00",
				"0.00%","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00",
				"0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00","0.00",
				"0.00","0.00","0.00","0.00",
				};
		ArrayList<String> dataTypeList = new ArrayList<String>(dataType.length);
		Collections.addAll(dataTypeList, dataType);
		ArrayList<String> dataTypeList1 = new ArrayList<String>(dataType1.length);
		Collections.addAll(dataTypeList1, dataType1);


		//定义餐券、免单、班次动态列的数量
		int cqLength = 0,mdLength = 0,bcLength = 0;
		//表头到餐券动态列时的角标
		Integer titleIndexStr =24;
		if (null != cqColumn && !"".equals(cqColumn)){
			String[] cqNames = cqColumnName.split(",");
			String[] cqs = cqColumn.split(",");
			cqLength = cqs.length;
			for (int j=0; j < cqLength; j++){
				titleNameList.add(cqNames[j]);
				dataNameList.add(cqs[j]);
				dataTypeList.add("0.00");
			}

			titleIndexStr += cqLength;
		}
		//到免单动态列的角标
		titleNameList.add("超时券");
		dataNameList.add("timeout_coupon");
		dataTypeList.add("0.00");
		titleNameList.add("排位券");
		dataNameList.add("line_up_coupon");
		dataTypeList.add("0.00");
		titleNameList.add("服务费");
		dataNameList.add("sevice_fee");
		dataTypeList.add("0.00");
		titleNameList.add("折扣");
		dataNameList.add("discount_k");
		dataTypeList.add("0.00");
		titleNameList.add("折让");
		dataNameList.add("discount_r");
		dataTypeList.add("0.00");
		titleNameList.add("奉送");
		dataNameList.add("give_away_free");
		dataTypeList.add("0.00");
		if (null != mdColumn && !"".equals(mdColumn)){
			String[] mdNames = mdColumnName.split(",");
			String[] mds = mdColumn.split(",");
			mdLength = mdNames.length;
			for (int j=0; j < mdLength; j++){
				titleNameList.add(mdNames[j]);
				dataNameList.add(mds[j]);
				dataTypeList.add("0.00");
			}

			titleIndexStr += mdLength;
		}
		//查询所有的班次（班次拼接所有的班次）
		p.element("getName", "yes");
		List<JSONObject> loadDutyOrderNew = conditionUtils.loadDutyOrderNew(attribute, p);

		String [] znameArr = new String[]{"￥营业实收","消费账单","实收占比%"};
		String [] znameCodeArr = new String[]{"bill_amount","bill_num","payment_rate"};

		List<String> pnameList = new ArrayList<String>();
		List<String> zcolumnList = new ArrayList<String>();

		//班次的数量（早班、中班、晚班）
		int length = loadDutyOrderNew.size();
		for(JSONObject class1 :loadDutyOrderNew) {
			pnameList.add(class1.optString("text"));  //第二行，中班、晚班、夜班等
			for(Integer i = 0 ; i<=2 ; i++) {
				titleNameList.add(znameArr[i]);
				dataNameList.add(class1.optString("name")+"_"+znameCodeArr[i]); // 编码_班次的id
				dataTypeList.add("0.00");
				++bcLength;
			}
		}

		titleNameList.addAll(titleNameList1);
		dataNameList.addAll(dataNameList1);
		dataTypeList.addAll(dataTypeList1);

		final int size = titleNameList.size();
		String[] titleNames = (String[])titleNameList.toArray(new String[size]);
		String[] dataNames = (String[])dataNameList.toArray(new String[size]);
		String[] dataTypes = (String[])dataTypeList.toArray(new String[size]);
		//表头的格式，合并列
		HSSFCellStyle style =ReportExportUtils.getTitleStyle(workBook);
		HSSFRow rowtitle =sheet1.createRow(0);
		// 普通合并列
		List<JSONObject> titleArr = new ArrayList() ;
		JSONObject sonJson =new JSONObject();
		sonJson.put("titleName", "营业额收入分析");
		sonJson.put("index", 4);
		sonJson.put("end", 19);
		titleArr.add(sonJson);

		JSONObject sonJson0 =new JSONObject();
		sonJson0.put("titleName", "其他");
		sonJson0.put("index", 20);
		int qtLength = 24 + cqLength + 6 + mdLength + bcLength;
		sonJson0.put("end", 24 + cqLength + 6 + mdLength + bcLength);
		titleArr.add(sonJson0);

		JSONObject sonJson2 =new JSONObject();
		sonJson2.put("titleName", "营业模式收入分析");
		sonJson2.put("index", qtLength + 1);
		sonJson2.put("end", qtLength + 12);
		titleArr.add(sonJson2);

		JSONObject sonJson3 =new JSONObject();
		sonJson3.put("titleName", "人工成本");
		sonJson3.put("index", qtLength + 13);
		sonJson3.put("end", qtLength + 13 + 7);
		titleArr.add(sonJson3);

		JSONObject sonJson4 =new JSONObject();
		sonJson4.put("titleName", "损耗");
		sonJson4.put("index", qtLength + 21);
		sonJson4.put("end", qtLength + 21 + 3);
		titleArr.add(sonJson4);

		JSONObject sonJson5 =new JSONObject();
		sonJson5.put("titleName", "能源");
		sonJson5.put("index", qtLength + 25);
		sonJson5.put("end", qtLength + 25 + 9);
		titleArr.add(sonJson5);

		sheet1 =ReportExportUtils.mergrColumn(workBook,sheet1,rowtitle,titleArr);

		//标题行第二行
		HSSFRow rowtitle1 =sheet1.createRow(1);
		List<JSONObject> titleArr1 = new ArrayList() ;
		JSONObject titleJson =new JSONObject();
		titleJson.put("titleName", "营业实收");
		titleJson.put("index", 4);
		titleJson.put("end", 8);
		titleArr1.add(titleJson);

		JSONObject titleJson1 =new JSONObject();
		titleJson1.put("titleName", "消费账单");
		titleJson1.put("index", 9);
		titleJson1.put("end", 12);
		titleArr1.add(titleJson1);

		JSONObject titleJson2 =new JSONObject();
		titleJson2.put("titleName", "账单均值");
		titleJson2.put("index", 13);
		titleJson2.put("end", 16);
		titleArr1.add(titleJson2);

		JSONObject titleJson3 =new JSONObject();
		titleJson3.put("titleName", "人（数/均）");
		titleJson3.put("index", 17);
		titleJson3.put("end", 19);
		titleArr1.add(titleJson3);

		JSONObject titleJson4 =new JSONObject();
		titleJson4.put("titleName", "翻台率");
		titleJson4.put("index", 21);
		titleJson4.put("end", 24);
		titleArr1.add(titleJson4);

		JSONObject titleJson5 =new JSONObject();
		if (cqLength > 0){
			titleJson5.put("titleName", "餐券");
			titleJson5.put("index", 25);
			titleJson5.put("end", 24 + cqLength);
			titleArr1.add(titleJson5);
		}

		JSONObject titleJson6 =new JSONObject();
		titleJson6.put("titleName", "优惠");
		titleJson6.put("index", 24 + cqLength + 4);
		titleJson6.put("end", cqLength + 30);
		titleArr1.add(titleJson6);

		JSONObject titleJson7 =new JSONObject();
		if (mdLength > 0){
			titleJson7.put("titleName", "免单");
			titleJson7.put("index", cqLength + 31);
			titleJson7.put("end", cqLength + 30 + mdLength);
			titleArr1.add(titleJson7);
		}

		int bcEnd = cqLength + 30 + mdLength;
		//如果班次存在
		if (length > 0){
			int len = cqLength + 30 + mdLength;
			for (int i = 0; i < length; i++){
				JSONObject titleJson8 =new JSONObject();
				titleJson8.put("titleName", pnameList.get(i));
				titleJson8.put("index", len + 1 + i*3);
				titleJson8.put("end", len + (i+1)*3);
				titleArr1.add(titleJson8);
				bcEnd = len + (i+1)*3;
			}
		}

		JSONObject titleJson15 =new JSONObject();
		titleJson15.put("titleName", "堂食");
		titleJson15.put("index",bcEnd + 1);
		titleJson15.put("end",bcEnd + 4);
		titleArr1.add(titleJson15);

		JSONObject titleJson16 =new JSONObject();
		titleJson16.put("titleName", "外送美团");
		titleJson16.put("index",bcEnd + 5);
		titleJson16.put("end", bcEnd + 8);
		titleArr1.add(titleJson16);

		JSONObject titleJson17 =new JSONObject();
		titleJson17.put("titleName", "外带外卖");
		titleJson17.put("index",bcEnd + 9);
		titleJson17.put("end", bcEnd + 12);
		titleArr1.add(titleJson17);

		JSONObject titleJson18 =new JSONObject();
		titleJson18.put("titleName", "总工时");
		titleJson18.put("index",bcEnd + 13);
		titleJson18.put("end", bcEnd + 14);
		titleArr1.add(titleJson18);

		JSONObject titleJson19 =new JSONObject();
		titleJson19.put("titleName", "TCPMH");
		titleJson19.put("index",bcEnd + 15);
		titleJson19.put("end", bcEnd + 16);
		titleArr1.add(titleJson19);

		JSONObject titleJson20 =new JSONObject();
		titleJson20.put("titleName", "工薪%");
		titleJson20.put("index",bcEnd + 17);
		titleJson20.put("end", bcEnd + 18);
		titleArr1.add(titleJson20);

		JSONObject titleJson21 =new JSONObject();
		titleJson21.put("titleName", "员工副食");
		titleJson21.put("index",bcEnd + 19);
		titleJson21.put("end", bcEnd + 20);
		titleArr1.add(titleJson21);

		JSONObject titleJson22 =new JSONObject();
		titleJson22.put("titleName", "￥成品丢弃");
		titleJson22.put("index",bcEnd + 21);
		titleJson22.put("end", bcEnd + 22);
		titleArr1.add(titleJson22);

		JSONObject titleJson23 =new JSONObject();
		titleJson23.put("titleName", "差异率");
		titleJson23.put("index",bcEnd + 23);
		titleJson23.put("end", bcEnd + 24);
		titleArr1.add(titleJson23);

		JSONObject titleJson24 =new JSONObject();
		titleJson24.put("titleName", "水");
		titleJson24.put("index",bcEnd + 25);
		titleJson24.put("end", bcEnd + 26);
		titleArr1.add(titleJson24);

		JSONObject titleJson25 =new JSONObject();
		titleJson25.put("titleName", "电");
		titleJson25.put("index",bcEnd + 27);
		titleJson25.put("end", bcEnd + 30);
		titleArr1.add(titleJson25);

		JSONObject titleJson26 =new JSONObject();
		titleJson26.put("titleName", "气");
		titleJson26.put("index",bcEnd + 31);
		titleJson26.put("end", bcEnd + 34);
		titleArr1.add(titleJson26);

		sheet1 =ReportExportUtils.mergrColumn(workBook,sheet1,rowtitle1,titleArr1);

		if(list1.size()>0){
			for(JSONObject json1 : list1) {
				// 调用到处方法；
				out1Result =ReportExportUtils.out1(json1,workBook,sheet1,titleNames,dataNames,dataTypes,paramData);
				//stratIndex =out1Result.optInt("rowNum");
				paramData.put("rowNum", out1Result.opt("rowNum"));
				paramData.put("jin", out1Result.optInt("jin"));
			}
		}


		HSSFRow rowtitle2 =sheet1.getRow(2);
		Integer [] valueNum ={0,1,2,3};
		//合并上下的行 限制一列
		sheet1 =ReportExportUtils.upOrDownMergr(workBook, sheet1, valueNum, rowtitle2, rowtitle, 0, 2);

		sheet1.groupRow(1,out1Result.optInt("rowNum"));
		sheet1.setRowSumsBelow(false);
		sheet1.setRowSumsRight(false);
		return workBook;
	}

	/*public static void main(String[] args){
		String[] array = new String[]{"如果","假如","你猜","就不"};
		//List<String> list = Arrays.asList(array);
		ArrayList< String> list = new ArrayList<String>(array.length);
		Collections.addAll(list, array);
		System.err.println(list);
		list.add("倔强");
		list.add("石头");
		System.err.println(list.size());
	}*/

}