<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:p="http://www.springframework.org/schema/p" 
    xmlns:mongo="http://www.springframework.org/schema/data/mongo"
	   xmlns:repository="http://www.springframework.org/schema/data/repository"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	   		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
   			http://www.springframework.org/schema/data/mongo
    		http://www.springframework.org/schema/data/mongo/spring-mongo.xsd
    		http://www.springframework.org/schema/data/repository
    		http://www.springframework.org/schema/data/repository/spring-repository-1.5.xsd">
	
	<!-- Default bean name is 'mongo' -->
	<mongo:mongo host="${mongo.host}" port="${mongo.port}"/>
	
	<!-- Offers convenience methods and automatic mapping between MongoDB JSON documents and your domain classes. -->
  	<bean id="mongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
  			<constructor-arg ref="mongo"/>
		    <constructor-arg name="databaseName" value="${mongo.databaseName}"/>
  	</bean>
  	
</beans>