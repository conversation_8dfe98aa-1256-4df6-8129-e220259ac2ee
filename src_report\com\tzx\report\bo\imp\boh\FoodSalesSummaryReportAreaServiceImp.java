package com.tzx.report.bo.imp.boh;

import com.tzx.report.bo.boh.FoodSalesSummaryReportAreaService;
import com.tzx.report.po.boh.dao.FoodSalesSummaryReportAreaDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service(FoodSalesSummaryReportAreaService.NAME)
public class FoodSalesSummaryReportAreaServiceImp implements FoodSalesSummaryReportAreaService
{

	@Resource(name = FoodSalesSummaryReportAreaDao.NAME)
	private FoodSalesSummaryReportAreaDao foodSalesSummaryReportAreaDao;
	
	@Override
	public JSONObject getFoodSalesSummaryReportArea(String tenancyID, JSONObject condition) throws Exception {
		return foodSalesSummaryReportAreaDao.getFoodSalesSummaryReportArea(tenancyID, condition);
	}
	
}