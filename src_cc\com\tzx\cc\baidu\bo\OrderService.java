package com.tzx.cc.baidu.bo;

import net.sf.json.JSONObject;

public interface OrderService {
	
	 final String NAME = "com.tzx.cc.baidu.bo.imp.OrderServiceImp";
	
	/**
	 * 创建订单
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	 JSONObject orderCreate(JSONObject jsonObject,String channel) throws Exception;

	/**
	 * 订单状态查询
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	 JSONObject orderStatusGet(JSONObject jsonObject,String channel) throws Exception;

	/**
	 * 订单状态推送
	 * 
	 * @param param
	 * @returnsssss
	 * @throws Exception
	 */
	JSONObject orderStatusPush(JSONObject params, String channel) throws Exception;
	
	/**
	 * 异常看板 （报表）
	 * 参数 店铺id（store_id） 日期传数字类型的chanelDate天数（今天、昨天、7日内、30日内、90日内）
	 * @param tenantId 
	 * 
	 * @return param
	 * @throws Exception
	 */
	JSONObject orderYcStatus(String code,String tenantId, int chanelDate) throws Exception;
	
	
}
