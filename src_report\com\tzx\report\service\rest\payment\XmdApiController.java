package com.tzx.report.service.rest.payment;

import com.tzx.report.bo.payment.service.XmdApiService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * <AUTHOR>
 * @date 2018/6/6
 */
@Controller
@RequestMapping("/report/xmdApiControlelr")
public class XmdApiController {

    @Autowired
    private XmdApiService xmdApiService;

    /**
     * 检查三月内未同步的数据
     * @param request
     * @return
     */
    @RequestMapping("queryDateData")
    @ResponseBody
    public JSONObject queryDateData(HttpServletRequest request){
        JSONObject jsonObject = new JSONObject();
        try {
            String diffd = request.getParameter("diffd");
            String type = request.getParameter("type");
            JSONArray array = xmdApiService.checkAndQueryApiDate(StringUtils.isBlank(diffd)?2:Integer.parseInt(diffd),StringUtils.isEmpty(type)?1:Integer.parseInt(type));

            jsonObject.put("success",true);
            jsonObject.put("data",array);
        }catch (Exception e){
            e.printStackTrace();
            jsonObject.put("success",false);
            jsonObject.put("msg",e.getMessage());
        }
        return jsonObject;
    }

    /**
     * 结算明细
     * @param request
     * @param date
     * @param poiid
     * @return
     */
    @RequestMapping("syncData")
    @ResponseBody
    public JSONObject syncData(HttpServletRequest request,String date,String poiid){
        JSONObject result = new JSONObject();
        try{
            if(StringUtils.isEmpty(date)){
                result.put("success",false);
                result.put("msg","日期不能为空!");
                return result;
            }

            xmdApiService.syncDate(poiid,date);
            result.put("success",true);
        }catch (Exception e){
            e.printStackTrace();
            result.put("success",false);
            result.put("msg",e.getMessage());
        }
        return result;
    }


    /**
     * 同步打款明细
     * @param date
     * @param poiid
     * @return
     */
    @RequestMapping("syncPaymentData")
    @ResponseBody
    public JSONObject syncPaymentData(String date,String poiid){
        JSONObject result = new JSONObject();
        try{
            if(StringUtils.isEmpty(date)){
                result.put("success",false);
                result.put("msg","日期不能为空!");
                return result;
            }
         /*   if(StringUtils.isEmpty(poiid)){
                result.put("success",false);
                result.put("msg","POIID不能为空!");
                return result;
            }*/
            xmdApiService.syncPaymentData(poiid,date);
            result.put("success",true);
        }catch (Exception e){
            e.printStackTrace();
            result.put("success",false);
            result.put("msg",e.getMessage());
        }
        return result;
    }
    /**
     * 检查并修复pos_payment_order report_date 字段
     * @return
     */
    @RequestMapping("repairReportDate")
    @ResponseBody
    public JSONObject repairReportDate(HttpServletRequest request){
        JSONObject result = new JSONObject();
        try {
            xmdApiService.repairReportDate();
            result.put("success",true);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success",false);
            result.put("msg",e.getMessage());
        }
        return result;
    }
}
