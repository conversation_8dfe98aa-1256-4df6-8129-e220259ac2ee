package com.tzx.cc.baidu.bo.imp;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.cc.baidu.bo.OrderService;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.framework.common.util.dao.GenericDao;

import net.sf.json.JSONObject;

@Service(OrderService.NAME)
public class OrderServiceImp implements OrderService {

	private static final Logger logger = Logger
			.getLogger(OrderServiceImp.class);
	 
	
	@Resource(name = "genericDaoImpl")
	private GenericDao dao;

	@Override
	public JSONObject orderCreate(JSONObject order, String channel)
			throws Exception {
		ThirdPartyOrderReceiver orderReceive=null;
		
		logger.info("收到订单[" + channel + "]:" + order);
		switch (channel) {
		case Constant.BAIDU_CHANNEL:
			orderReceive = new BaiduOrderReceiver(order);
			break;
		case Constant.MEITUAN_CHANNEL:
			orderReceive = new MeiTuanOrderReceiver(order);
			break;
		case Constant.DIANPING_CHANNEL:
			orderReceive = new DpOrderReceiver(order);
			break;
		case Constant.YICHI_CHANNEL:
			orderReceive = new YcOrderReceiver(order);
			break;
		case Constant.ELE_CHANNEL:
			if (order.optString("version").equals(Constant.ELM_API_VERSION_V2)) {
				// 饿了么2.0版 本
				orderReceive = new EleMeOrderReceiverV2(order);
			} else {
				// 饿了么1.0版本
				String[] ids = order.optString("eleme_order_ids").split(",");
				for (String id : ids) {
					order.put("eleme_order_id", id);
					orderReceive = new EleMeOrderReceiver(order);
				}

				logger.info("==========================================="
						+ "饿了么推送ID:" + order.optJSONArray("eleme_order_ids")
						+ "===========================================");
			}
			break;
		case Constant.XMDWM_CHANNEL://changhui add 新美大外卖 2017-11-13
			orderReceive = new XinMeiDaOrderReceiver(order);
			break;
		default:
			break;
		}
		JSONObject result = orderReceive.receive();
		logger.info("订单回复[" + channel + "]:" + result);
		return result;
	}

	@Override
	public JSONObject orderStatusPush(JSONObject params, String channel)
			throws Exception {
		ThirdPartyOrderReceiver orderReceive=null;
		
		switch (channel) {
		case Constant.BAIDU_CHANNEL:
			orderReceive = new BaiduOrderReceiver();
			break;
		case Constant.MEITUAN_CHANNEL:
			orderReceive = new MeiTuanOrderReceiver();
			break;
		case Constant.DIANPING_CHANNEL:
			orderReceive = new DpOrderReceiver();
			break;
		case Constant.YICHI_CHANNEL:
			orderReceive = new YcOrderReceiver();
			break;
		case Constant.ELE_CHANNEL:
			if (params.optString("version").equals(Constant.ELM_API_VERSION_V2)) {
				// 饿了么2.0版 本
				orderReceive = new EleMeOrderReceiverV2();
			} else {
				// 饿了么1.0版本
				orderReceive = new EleMeOrderReceiver();
			}
			break;
		case Constant.XMDWM_CHANNEL://changhui 2017-11-21
			orderReceive = new XinMeiDaOrderReceiver();
			break;
		default:
			break;
		}
		return orderReceive.orderStatusPush(params);
	}

	@Override
	public JSONObject orderStatusGet(JSONObject params, String channel)
			throws Exception {
		ThirdPartyOrderReceiver orderReceive=null;
		
		switch (channel) {
		case Constant.BAIDU_CHANNEL:
			orderReceive = new BaiduOrderReceiver();
			break;
		case Constant.MEITUAN_CHANNEL:
			orderReceive = new MeiTuanOrderReceiver();
			break;
		case Constant.DIANPING_CHANNEL:
			orderReceive = new DpOrderReceiver();
			break;
		default:
			break;
		}

		return orderReceive.orderStatusGet(params);
	}
	
	/**
	 * 异常看板 （报表）
	 * 参数 店铺id（store_id） 日期传数字类型的chanelDate天数（今天、昨天、7日内、30日内、90日内）
	 */
	@SuppressWarnings({ "deprecation", "rawtypes" })
	public JSONObject orderYcStatus(String tenantId,String code,int chanelDate) throws Exception{
    	JSONObject jsb = new JSONObject();
	    	//map存放店铺类型 （百度外卖、美团外卖、饿了么）
			Map<String,List> ycMap = new HashMap<String,List>();
	    	//百度外卖的的类型查询 （未结单超时取消、服务员取消、用户取消、平台取消）
			Date endDate = new Date();
			endDate.setHours(0);
			//获取到的开始的时间从（00：00：00开始）
			Calendar calendar = new GregorianCalendar();
	        calendar.setTime(endDate);
	        calendar.set(Calendar.MINUTE,0);
	        calendar.set(Calendar.SECOND,0);
	        calendar.set(Calendar.MILLISECOND,0);
	        long ts = calendar.getTime().getTime()-chanelDate*24*60*60*1000L;
	        Date startDate = new Date(ts);
	        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        //拼接sql语句
	        //查询百度外卖
	        StringBuffer sql1 = null;
	        if(chanelDate==0){
	        	sql1 =  new StringBuffer("SELECT S.CANCEL_TYPE,S.CHANEL,COUNT(*) FROM cc_order_list S WHERE  S.STORE_ID IN (select a.id from organ a  where a.organ_code like '"+code+"%' and a.org_type='3' and a.valid_state='1') AND S.ORDER_STATE='08' AND ");
		        sql1.append(" S.SINGLE_TIME  BETWEEN '"+sdf.format(startDate)+"' AND '"+sdf.format(new Date())+"' GROUP BY S.CANCEL_TYPE,S.CHANEL" );
	        }else{
	        	sql1 =  new StringBuffer("SELECT S.CANCEL_TYPE,S.CHANEL,COUNT(*) FROM cc_order_list S WHERE  S.STORE_ID IN (select a.id from organ a  where a.organ_code like '"+code+"%' and a.org_type='3' and a.valid_state='1') AND S.ORDER_STATE='08' AND ");
		        sql1.append(" S.SINGLE_TIME  BETWEEN '"+sdf.format(startDate)+"' AND '"+sdf.format(calendar.getTime().getTime())+"' GROUP BY S.CANCEL_TYPE,S.CHANEL" );
	        }
	        
	        //查询百度外卖返回的结束集
	         List<JSONObject> query4Json1 = this.dao.query4Json(tenantId, sql1.toString());
	        ycMap.put("bdList", query4Json1);
	        jsb.put("ycMap", ycMap);
	        return jsb;
    	
    }
    
}
