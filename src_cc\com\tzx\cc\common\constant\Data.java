package com.tzx.cc.common.constant;

import java.io.Serializable;
import java.util.List;

import net.sf.json.JSONObject;


/**
 * 请求参数体
 * <AUTHOR>
 *
 */
public class Data implements Serializable,Cloneable
{
	private static final long	serialVersionUID	= 1L;
	private Oper				oper;
	private Type				type;
	private JSONObject 			body;
	
	public JSONObject getBody()
	{
		return body;
	}
	public void setBody(JSONObject body)
	{
		this.body = body;
	}
	public Oper getOper()
	{
		return oper;
	}
	public void setOper(Oper oper)
	{
		this.oper = oper;
	}
	public Type getType()
	{
		return type;
	}
	public void setType(Type type)
	{
		this.type = type;
	}
}
