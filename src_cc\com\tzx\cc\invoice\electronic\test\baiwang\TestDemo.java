package com.tzx.cc.invoice.electronic.test.baiwang;

import com.tzx.cc.invoice.electronic.util.RequestUtils;

public class TestDemo {
	public static String main(String requestUrl, String xml,String nsrsbh) throws Exception {
		// 初始化参数
		String requestData = null;//初始化请求报文
		String rsData = null;//初始化结果报文
		String requestMethod = null;//初始化请求方法
		String requestUrlMethod = null;//初始化连接方法

		// 通过注释选择语言
		String interfaceLau = Utils.interfaceLau_xml;
//		 String interfaceLau = Utils.interfaceLau_json;
		

		// 通过注释选择接口
		 String interfaceCode = Utils.dfxj1001;//开具
//		 String interfaceCode = Utils.dfxj1004;//查询
//		 String interfaceCode = Utils.dfxj1003;//库存查询
//		String interfaceCode = Utils.dfxj1005;// 获取下载地址
		
		//通过注释选择调用方式
//		String requestInterface = Utils.webservice_axis;//请求方式使用axis的webservice
//		String requestInterface = Utils.webservice_xfire;//请求方式使用xfire的webservice		
		String requestInterface = Utils.post_https;//使用post请求方式
		
		// 组装请求报文
		if (Utils.interfaceLau_xml.equals(interfaceLau)) {
			requestData = xml;
			requestMethod = Utils.requestMethod_xml;//xml的请求方法
			requestUrlMethod = Utils.requestUrlMethod_xml;//xml的连接后缀
		} 
		
		// 调用接口
		if(Utils.webservice_axis.equals(requestInterface)){
			rsData = RequestUtils.webServiceAxis(requestData,requestMethod,requestUrl+requestUrlMethod);
		}else if(Utils.webservice_xfire.equals(requestInterface)){
			rsData = RequestUtils.webServiceXfile(requestData, requestMethod, requestUrl+requestUrlMethod+"?wsdl");
		}else if(Utils.post_https.equals(requestInterface)){
			rsData = RequestUtils.getHttpConnectResult(requestData,requestUrl+"invoice");
		}
         return rsData;
	}
}

