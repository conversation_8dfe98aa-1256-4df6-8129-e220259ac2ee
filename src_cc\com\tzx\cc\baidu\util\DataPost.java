package com.tzx.cc.baidu.util;

import com.tzx.cc.baidu.entity.Sign;

import net.sf.json.JSONObject;

public class DataPost {
	
	/**推送数据到百度
	 * @param param
	 * @return
	 */
	public static JSONObject postData2Baidu(Object param){
		
		return null;
	}
	
	/**推送数据到大众点评
	 * @param signJSON 创建商户必要参数 {token(source),appkey(secret)}
	 * @param thirdJsonData
	 * @return
	 */
	public static JSONObject postData2DP(Object thirdJsonData,JSONObject signJSON){
		JSONObject data=null;
		if(thirdJsonData instanceof JSONObject){
			data=(JSONObject) thirdJsonData;
		}else{
			data=JSONObject.fromObject(thirdJsonData);
		}
		
		JSONObject res=new JSONObject();
		res.put("errno", "0");
		res.put("error", "success");
		
		String[] ids=data.optString("shopid").split("@");
		
		if(ids.length!=2){
			res.put("errno", "2");
			res.put("error", "shopid格式不正确");
			return res;
		}
		Sign sign=null;
		String token,appKey=null;

		if (null == signJSON) {

			try {
				sign = SignHolder.getShopSign(ids[1], ids[0], Constant.DIANPING_CHANNEL);
			} catch (Exception e1) {
				e1.printStackTrace();
				res.put("errno", "3");
				res.put("error", "获取token和appkey发生异常");
				return res;
			}

			token = sign.getSource();
			appKey = sign.getSecret();
		} else {
			token = signJSON.optString("source");
			appKey = signJSON.optString("secret");
		}

		String thirdRes="";
		try {
			thirdRes=CommonUtil.DPHttpPost(token, appKey, data.toString());
			res.put("data", thirdRes);
		} catch (Exception e) {
			e.printStackTrace();
			res.put("errno", "1");
			res.put("error", "调用第三方接口异常");
		}
		
		return res;
	}
}
