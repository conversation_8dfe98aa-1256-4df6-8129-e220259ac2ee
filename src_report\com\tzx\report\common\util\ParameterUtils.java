package com.tzx.report.common.util;

import java.util.Iterator;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;


/**
 * 
 * <AUTHOR> 报表参数公共类
 *
 */
 
@Repository
public class ParameterUtils {
	
	@Resource(name = "genericDaoImpl")
	private  GenericDao	dao;
	

	/**
	 * 横版报表
	 * @param tenancyID
	 * @param condition
	 * @param reportSql
	 * @return
	 * @throws Exception
	 */
	public String parameterAutomaticCompletion(String tenancyID,JSONObject condition,String reportSql) throws Exception{
		String sql="";
		SqlRowSet rs = dao.query(tenancyID, reportSql.toString());
		if(rs!=null && rs.next()){
			sql = rs.getString("sql");
			Iterator<?> it = condition.keys();
			while(it.hasNext()){
				String key = (String) it.next();
				String value = condition.getString(key);
					if(Tools.hv(value)&&!value.equals("''")){
						sql = sql.replaceAll("%"+key+"%", value);
					}else{
						sql = sql.replaceAll("%"+key+"%", "'99999999'");
					}
				}
			}
		//System.out.println(sql);
		return sql;
	}
	
	
	/**
	 * 横版报表(升级版（可以区分数值类型，字符串类型）)
	 * @param tenancyID
	 * @param condition
	 * @param reportSql
	 * @return
	 * @throws Exception
	 */
	public String parameterAutomaticCompletionUpgrade(String tenancyID,JSONObject condition,String reportSql) throws Exception{
		String sql="";
		SqlRowSet rs = dao.query(tenancyID, reportSql.toString());
		if(rs!=null && rs.next()){
			sql = rs.getString("sql");
			Iterator<?> it = condition.keys();
			while(it.hasNext()){
				String key = (String) it.next();
				String value = condition.getString(key);
					if(Tools.hv(value)&&!value.equals("''")){
						sql = sql.replaceAll("%"+key+"%", value);
					}else if(value.equals("''")){
						sql = sql.replaceAll("%"+key+"%", "'99999999'");
					}else if(value.equals("")){
						sql = sql.replaceAll("%"+key+"%", "99999999");
					}
				}
			}
		return sql;
	}
	
	
	
	/**
	 * 横版报表(升级版（可以区分数值类型，字符串类型）)
	 * @param tenancyID
	 * @param condition
	 * @param reportSql
	 * @return
	 * @throws Exception
	 */
	public String parameterAutomaticCompletionUpgradeMD5(String tenancyID,JSONObject condition,String reportSql) throws Exception{
		String sql="";
		SqlRowSet rs = dao.query(tenancyID, reportSql.toString());
		if(rs!=null && rs.next()){
			sql = rs.getString("sql");
			Iterator<?> it = condition.keys();
			while(it.hasNext()){
				String key = (String) it.next();
				String value = condition.getString(key);
					if(Tools.hv(value)&&!value.equals("''")){
						sql = sql.replaceAll("%"+key+"%", value);
					}else if(value.equals("''")){
						sql = sql.replaceAll("%"+key+"%", "'EF775988943825D2871E1CFA75473EC0'");
					}else if(value.equals("")){
						sql = sql.replaceAll("%"+key+"%", "EF775988943825D2871E1CFA75473EC0");
					}
				}
			}
		return sql;
	}
	
	
	
	
	/**
	 * 竖版报表
	 * @param tenancyID
	 * @param condition
	 * @param reportSql
	 * @return
	 * @throws Exception
	 */
	private static final Logger	 log	= Logger.getLogger(ParameterUtils.class);
	
	public List<JSONObject> parameterAutomaticExecution(String tenancyID,JSONObject condition,String reportSql) throws Exception{
		Iterator<?> it = condition.keys();
		while(it.hasNext()){
			String key = (String) it.next();
			String value = condition.getString(key);
				if(Tools.hv(value)&&!value.equals("''")){
					reportSql = reportSql.replaceAll("%"+key+"%", value);
				}else{
					reportSql = reportSql.replaceAll("%"+key+"%", "'99999999'");
				}
			}
		log.info("sql:"+reportSql);
		return dao.query4Json(tenancyID,reportSql);
	}
	
	
	
	public String buildPageSqlReportlLevel(JSONObject condition, String sql,int level) throws Exception
	{
		String sort = condition.optString("sortKey");
		String sort1 = condition.optString("sortKey1");
		String sort2 = condition.optString("sortKey2");
		String order = condition.optString("order");
		String order1 = condition.optString("order1");
		String order2 = condition.optString("order2");
		String sentence = "";
		if(level==1){
			if(!"".equals(sort))
			{
				if("desc".equalsIgnoreCase(order))
				{
					sentence= "select * from (" + sql + " order by " + sort + " desc) datasql";
				}
				else
				{
					sentence= "select * from (" + sql + " order by " + sort + ") datasql";
				}
			}
		}else if(level==2){
				if(!"".equals(sort1)){
					if("desc".equalsIgnoreCase(order1))
					{
						sentence= "select * from (" + sql + " order by " + sort1 + " desc) datasql";
					}
					else
					{
						sentence= "select * from (" + sql + " order by " + sort1 + ") datasql";
					}
				}else{
					sentence= "select * from (" + sql + ") datasql";
				}
			}else if(level==3){
				if(!"".equals(sort2)){
					if("desc".equalsIgnoreCase(order2))
					{
						sentence= "select * from (" + sql + " order by " + sort2 + " desc) datasql";
					}
					else
					{
						sentence= "select * from (" + sql + " order by " + sort2 + ") datasql";
					}
				}
			}else{
				sentence= "select * from (" + sql + ") datasql order by datasql.id desc";
			}
			return sentence;
		}
	
	
	
	public String buildPageSqlReport(JSONObject condition, String sql) throws Exception
	{
		String sort = condition.optString("sortKey");
		String sort1 = condition.optString("sortKey1");
		String sort2 = condition.optString("sortKey2");
		String order = condition.optString("order");
		String order1 = condition.optString("order1");
		String order2 = condition.optString("order2");
		if(!"".equals(sort))
		{
			if("desc".equalsIgnoreCase(order))
			{
				return "select * from (" + sql + " order by " + sort + " desc) datasql";
			}
			else
			{
				return "select * from (" + sql + " order by " + sort + ") datasql";
			}
		}else if(!"".equals(sort1)){
			if("desc".equalsIgnoreCase(order1))
			{
				return "select * from (" + sql + " order by " + sort1 + " desc) datasql";
			}
			else
			{
				return "select * from (" + sql + " order by " + sort1 + ") datasql";
			}
		}else if(!"".equals(sort2)){
			if("desc".equalsIgnoreCase(order2))
			{
				return "select * from (" + sql + " order by " + sort2 + " desc) datasql";
			}
			else
			{
				return "select * from (" + sql + " order by " + sort2 + ") datasql";
			}
		}else{
			return "select * from (" + sql + ") datasql order by datasql.id desc) datasql";
		}
	}
}
