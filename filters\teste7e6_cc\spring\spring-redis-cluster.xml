<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:redisson="http://redisson.org/schema/redisson"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context-4.0.xsd
       http://redisson.org/schema/redisson
       http://redisson.org/schema/redisson/redisson.xsd"
       default-autowire="byName">

    <!--
    <bean id="oMJedisConnectionFactory" name="oMJedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="hostName" value="${omredis.ip}"/>
        <property name="port" value="${omredis.port}"/>
        <property name="poolConfig" ref="jedisPoolConfig"/>
    </bean>
    -->

    <!-- 根据[springframework]官方描述文件修改了这里,如果有问题请修改为 p:connection-factory-ref -->
    <!-- SAAS redis 指定了name 勿用autowire -->
    <bean  name="saasRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate"
           p:connectionFactory-ref="jedisConnectionFactory"/>
    <!-- OM redis 指定了name 勿用autowire -->
    <bean id="omRedisTemplate" name="omRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate"
          p:connectionFactory-ref="jedisConnectionFactory"/>

    <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxTotal" value="${redis.pool.maxTotal}" />
        <property name="maxIdle" value="${redis.pool.maxIdle}"/>
        <property name="maxWaitMillis" value="${redis.pool.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${redis.pool.testOnBorrow}"/>
    </bean>

    <!-- Cluster config -->
    <bean id="redisClusterConfiguration" class="org.springframework.data.redis.connection.RedisClusterConfiguration">
        <property name="maxRedirects" value="${redis.cluster.maxRedirects}"></property>
        <property name="clusterNodes">
            <set>
                <bean class="org.springframework.data.redis.connection.RedisClusterNode">
                    <constructor-arg name="host" value="${redis.host1}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port1}"></constructor-arg>
                </bean>
                <bean class="org.springframework.data.redis.connection.RedisClusterNode">
                    <constructor-arg name="host" value="${redis.host2}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port2}"></constructor-arg>
                </bean>
                <bean class="org.springframework.data.redis.connection.RedisClusterNode">
                    <constructor-arg name="host" value="${redis.host3}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port3}"></constructor-arg>
                </bean>
                <bean class="org.springframework.data.redis.connection.RedisClusterNode">
                    <constructor-arg name="host" value="${redis.host4}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port4}"></constructor-arg>
                </bean>
                <bean class="org.springframework.data.redis.connection.RedisClusterNode">
                    <constructor-arg name="host" value="${redis.host5}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port5}"></constructor-arg>
                </bean>
                <bean class="org.springframework.data.redis.connection.RedisClusterNode">
                    <constructor-arg name="host" value="${redis.host6}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port6}"></constructor-arg>
                </bean>
            </set>
        </property>
    </bean>

    <!-- Redis Cluster Connection Factory -->
    <bean id="jedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory"  >
        <constructor-arg ref="redisClusterConfiguration" />
        <constructor-arg ref="jedisPoolConfig" />
    </bean>

    <!-- RedisUtil : Cluster Server -->
    <bean id="redisUtil" class="com.tzx.framework.common.util.redis.RedisClusterUtil">
        <property name="useCache"  value="${redis.useCache}" />
        <property name="defaultExpireTime"  value="${redis.defaultExpireTime}" />
        <property name="jedisCluster"  ref="jedisCluster" />
    </bean>

	 <!--redission 相关配置 请勿改动 -->
   <redisson:client id="redissonClient" >  
       <redisson:cluster-servers scan-interval="10000">   <!-- //scan-interval:集群状态扫描间隔时间，单位是毫秒 -->  
           <redisson:node-address value="redis://${redis.host1}:${redis.port1}"></redisson:node-address>  
           <redisson:node-address value="redis://${redis.host2}:${redis.port2}"></redisson:node-address>  
           <redisson:node-address value="redis://${redis.host3}:${redis.port3}"></redisson:node-address>  
           <redisson:node-address value="redis://${redis.host4}:${redis.port4}"></redisson:node-address>  
           <redisson:node-address value="redis://${redis.host5}:${redis.port5}"></redisson:node-address>  
           <redisson:node-address value="redis://${redis.host6}:${redis.port6}"></redisson:node-address>  
       </redisson:cluster-servers>  
    </redisson:client> 
    
    <bean id="genericObjectPoolConfig" class="org.apache.commons.pool2.impl.GenericObjectPoolConfig">
        <property name="maxTotal" value="${redis.pool.maxTotal}" />
        <property name="maxIdle" value="${redis.pool.maxIdle}"/>
        <property name="maxWaitMillis" value="${redis.pool.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${redis.pool.testOnBorrow}"/>
    </bean>

    <bean id="jedisCluster" class="redis.clients.jedis.JedisCluster">
        <constructor-arg index="0">
            <set>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg name="host" value="${redis.host1}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port1}"></constructor-arg>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg name="host" value="${redis.host2}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port2}"></constructor-arg>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg name="host" value="${redis.host3}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port3}"></constructor-arg>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg name="host" value="${redis.host4}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port4}"></constructor-arg>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg name="host" value="${redis.host5}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port5}"></constructor-arg>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg name="host" value="${redis.host6}"></constructor-arg>
                    <constructor-arg name="port" value="${redis.port6}"></constructor-arg>
                </bean>
            </set>
        </constructor-arg>
        <constructor-arg index="1" ref="genericObjectPoolConfig" />
    </bean>

</beans>