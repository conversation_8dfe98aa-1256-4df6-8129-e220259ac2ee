package com.tzx.report.bo.imp.hq;

import com.tzx.report.bo.hq.DishesClassificationClassesQueryService;
import com.tzx.report.po.hq.dao.DishesClassificationClassesQueryDao;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service(DishesClassificationClassesQueryService.NAME)
public class DishesClassificationClassesQueryServiceImpl implements DishesClassificationClassesQueryService
{
	
	@Resource
	private DishesClassificationClassesQueryDao dishesClassificationClassesQueryDao;

	@Override
	public JSONObject getDishesClassificationClassesQuery(String tenancyID, JSONObject condition) throws Exception {
		return dishesClassificationClassesQueryDao.getDishesClassificationClassesQuery(tenancyID, condition);
	}
 
}
