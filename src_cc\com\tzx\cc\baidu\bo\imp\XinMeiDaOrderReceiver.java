package com.tzx.cc.baidu.bo.imp;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.cc.baidu.util.Constant;
import com.tzx.cc.common.constant.util.CcPartitionUtils;
import com.tzx.cc.thirdparty.util.ElmUtils;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class XinMeiDaOrderReceiver extends ThirdPartyOrderReceiver{
	
	private JSONArray			discounts;																	// 优惠信息
	private JSONObject orderInfo;																			//订单信息
	private double				discount_fee				= 0.0;											// 优惠总金额
	private String				orderId;
	private String				xmd_id;

	private double				product_amount;															    // 订单商品总金额
	private double				send_fee;																	// 配送费
	private double				discountRate;																// 优惠比例=优惠总金额/订单商品总金额
	private double				comboDiscountRate;															// 套餐明细优惠比例=套餐优惠总金额/套餐商品总金额
	private double				sendFeeRate;																// 配送费比例=配送费/订单商品总金额

	private double				platform_side_discount_fee	= 0.0;											// 平台方承担的优惠总金额
	private double				takeawayBusinessIncome;													// 外卖的营业收入=用户实付-配送费+平台方承担的金额
	private double				food_sharing_date;															// 菜品分摊的比例=菜品总计/用户实付-配送费+平台方承担的金额
	private double 				package_fee=0.0;
	private double total_shopRate=0.0;//商家承担
	private double total_plateformRate=0.0;//平台承担

	private static final Logger	logger						= Logger.getLogger(BaiduOrderReceiver.class);
	public XinMeiDaOrderReceiver()
	{
		super(Constant.XMDWM_CHANNEL);
	}

	public XinMeiDaOrderReceiver(JSONObject order)
	{
		super(order, Constant.XMDWM_CHANNEL);
		try {		
			String[] shopId = order.optString("ePoiId").split("@");
			this.storeId = shopId[0];
			this.tenantId = shopId[1];
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
			//20171218日志中存储商户号、门店号
			logger.info("================tenantId="+storeId+",storeId="+tenantId+",新美大新订单:新订单详情返回结果：["+order+"]===========================");
	}
	
	@Override
	protected void generateOrderCode()
	{
		orderInfo = thirdPartyOrder.optJSONObject("order");
		int orderIndex = orderInfo.optInt("daySeq");
		orderCode = channel+storeId+CommonUtil.formatTimestamp(orderInfo.optLong("ctime"), "yyyyMMddHHmm")+CommonUtil.zeroFill(orderIndex);
//		orderCode = UUID.randomUUID().toString();
		
	}
	
	@Override
	public JSONObject saveOrderList() throws Exception {
		//orderInfo = thirdPartyOrder.optJSONObject("order");
		orderId = orderInfo.optString("orderIdView");
//		orderId =UUID.randomUUID().toString(); 
		xmd_id = orderInfo.optString("orderId");
		// 存redis
		JSONObject dat = new JSONObject();
		dat.put("app_poi_code", thirdPartyOrder.optString("ePoiId"));
		dat.put("third_order_code", orderId);
		logger.info("新美大外卖存入redis里的信息"+dat.toString());
		redis.saveBykv(xmd_id, dat.toString(), 172800);

		JSONObject orderListParam = new JSONObject();
        //默认计算方式(平台结算)
        orderListParam.put("settlement_type",checkMode.getSettlement_type());
		// 商户ID
		orderListParam.put("tenancy_id", tenantId);
		// 商户id
		orderListParam.put("store_id", storeId);
		// 订单号
		orderListParam.put("order_code", orderCode);
		// 每天总的流水号
		orderListParam.put("serial_number", flow_code);

		// 每天总的流水号
//		orderListParam.put("serial_number", flow_code);
		// 每天分渠道的流水号
		orderListParam.put("chanel_serial_number", orderInfo.optString("daySeq"));
		// 美团订单编号
		orderListParam.put("third_order_code", orderId);
		orderListParam.put("meituan_id", xmd_id);
		// 是否立即送餐
		orderListParam.put("send_immediately", orderInfo.optString("deliveryTime").equals("0") ? 1 : 2);
		//取餐类型 0：普通取餐；1：到店取餐 该信息默认不推送，如有需求可联系开放平台工作人员开通 changhui 2017-12-6 add start
		int pickType = orderInfo.optInt("pickType",0);
		orderListParam.put("pick_type", pickType);
     	//end
		if (orderInfo.optString("deliveryTime").equals("0"))
		{
			// 期望送达时间???changhui 2017-12-11 根据海军需求，如果为普通取餐模式send_time为空。
			orderListParam.put("send_time", "立即配送");
			//orderListParam.put("send_time", "");
		}
		else
		{
/*
			long a = thirdPartyOrder.optLong("delivery_time");
			String aa = DateUtil.format(new Timestamp(a));
			String[] aaa = aa.split(" ");
			String b = aaa[1].substring(0, 5);
			// 期望送达时间???
			orderListParam.put("send_time", b.toString());*/
			
			orderListParam.put("send_time", CommonUtil.getDateFromSeconds(String.valueOf(orderInfo.optLong("deliveryTime"))));
		}

		// 配送费
		send_fee = orderInfo.optDouble("shippingFee",0);
	 	
      	//double meiTuanRate=0.0;
		//2017-12-18解决当优惠金额大于订单金额时，订单表中的优惠金额应该为实际的优惠金额，即订单总额，非优惠总额
		double mtDiscountFee=0.0;
      	
		// 优惠总金额
		discounts = JSONArray.fromObject(orderInfo.optString("extras"));
		for (int i = 0; i < discounts.size(); i++)
		{
			JSONObject dc = discounts.getJSONObject(i);
			if(!dc.isNullObject() && !dc.isEmpty()){
				total_plateformRate+=dc.optDouble("mt_charge",0);
				total_shopRate+=dc.optDouble("poi_charge",0);
				discount_fee += dc.optDouble("reduce_fee",0);
				mtDiscountFee += dc.optDouble("reduce_fee",0);
				if(dc.optInt("type")==9){
					dc.put("mt_charge", dc.optDouble("reduce_fee",0));
					dc.put("poi_charge","0");
				}
				if(dc.optInt("type")==101){
					dc.put("mt_charge", "0");
					dc.put("poi_charge",dc.optDouble("reduce_fee",0));
				}
				platform_side_discount_fee += Double.isNaN(dc.optDouble("mt_charge",0)) ? 0.0 : dc.optDouble("mt_charge",0);
			}
		}
		discount_fee +=discountR_amount;//优惠总额=优惠金额+骑手结算时门店的折扣优惠金额
		// 订单总价
		orderListParam.put("total_money", orderInfo.optString("originalPrice"));
        // 物流
		String logisticsCode=orderInfo.optString("logisticsCode");
		String deliveryParty="1";
		if(logisticsCode.equals("0000")){//商家自配送
			deliveryParty="2";
		}
		if(logisticsCode.equals("1003")){//美团众包配送
			deliveryParty="3";
		}
        orderListParam.put("delivery_party", deliveryParty);
        
        orderListParam.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
        
		// 用户实付总价
        double actualPay=orderInfo.optDouble("total",0);
        
        
    	// 订单总价
		double total_fee = orderInfo.optDouble("originalPrice",0);

		total_discount_fee = discount_fee;
		order_total_price = total_fee;
		user_actual_pay=actualPay;
        
//		//2017-12-18订单优惠信息，为商户实际享有的优惠金额
//		if(order_total_price-mtDiscountFee<=0) {
//			//商户实际享有的优惠信息,订单总价-总价
//			 //mtDiscountFee = mtDiscountFee-order_total_price;
//			mtDiscountFee = order_total_price - user_actual_pay; 
//		}
		
		// 优惠虚付 20171221
		double discount_actual_pay = order_total_price - total_discount_fee;
		if (discount_actual_pay <= 0) {
			// 商户实际享受的优惠
			discount_fee = total_discount_fee - Math.abs(user_actual_pay)
					- Math.abs(discount_actual_pay);
		}
        
        JSONObject requestPoiDetail = JSONObject.fromObject(orderInfo.optString("poiReceiveDetail"));
        double platSendFee=CommonUtil.yuan2Fen(requestPoiDetail.optDouble("logisticsFee",0));//获取众包美团承担配送费

        double shopDeliveryFee=send_fee;
        double specialShopFee=orderInfo.optDouble("originalPrice",0)-discount_fee;
        double excess_discount=0;
        if(deliveryParty.equals("1")){//平台配送
        	double platSpecialShopFee=specialShopFee-send_fee;
        	 shopDeliveryFee=0;
        	if(platSpecialShopFee>0){
        		  shopFee = platSpecialShopFee;//商户应收
        	}else{//计算shopFee为负数时
        		  shopFee = 0;//商户应收
        		  discount_fee=discount_fee+platSpecialShopFee;
        		  excess_discount=0-platSpecialShopFee;
        	}
        }else if(deliveryParty.equals("3")){//众包配送
        	double platSpecialShopFee=specialShopFee-platSendFee;
        	 shopDeliveryFee=send_fee-platSendFee;//商户承担的配送费
        	if(platSpecialShopFee>0){
        		  shopFee = platSpecialShopFee;//商户应收
        	}else{//计算shopFee为负数时
        		  shopFee = 0;//商户应收
        		  excess_discount=0-platSpecialShopFee;
        	}
        }else{
        	if(specialShopFee>0){
        		shopFee=specialShopFee;
        	}else{
        		shopFee=0;
        		excess_discount=0-specialShopFee;
        	}
        }
        //at 20170908 解决shopFee因小数位产生计算错误的情况
        shopFee=CommonUtil.keepTwoDecimal(shopFee);
        
        orderListParam.put("discount_amount", CommonUtil.keepTwoDecimal(discount_fee));
        //orderListParam.put("discount_amount", CommonUtil.keepTwoDecimal(discount_fee));
        orderListParam.put("excess_discount", CommonUtil.keepTwoDecimal(excess_discount));
		orderListParam.put("actual_pay", CommonUtil.keepTwoDecimal(actualPay));
        // 商户实收总价
        orderListParam.put("shop_fee", CommonUtil.keepTwoDecimal(shopFee));
        //配送费
        orderListParam.put("meal_costs", CommonUtil.keepTwoDecimal(send_fee));
        //商家承担的配送费金额
        orderListParam.put("shop_delivery_fee",CommonUtil.keepTwoDecimal(shopDeliveryFee));

		//----------------------------------------
		//优惠总额=优惠金额+骑手结算时门店的折扣优惠金额
		if(checkMode.getSettlement_type().equals("RIDER")){
		double tempAmount=shopFee*(100 - Double.valueOf(checkMode.getDiscount_rate())) / 100;
		BigDecimal bg=new BigDecimal(tempAmount);
		discountR_amount=Double.isNaN(bg.setScale(4, RoundingMode.HALF_UP).doubleValue())?0.0:bg.setScale(4, RoundingMode.HALF_UP).doubleValue();
		}
		//----------------------------------------
		
		takeawayBusinessIncome = Double.valueOf(orderInfo.optString("total","0")) - Double.valueOf(send_fee) + platform_side_discount_fee;
		// 应收款receivable_amount
		// .put("receivable_amount",
		// CommonUtil.fen2Yuan(requestOrder.optInt("total_fee")-requestOrder.optInt("discount_fee")-requestOrder.optInt("user_fee")));
		// 支付类型
		String payType = orderInfo.optString("payType");
		String isOnlinePayment = null;
		if (StringUtils.equals("1", payType))
		{
			isOnlinePayment = "0";
		}
		else if (StringUtils.equals("2", payType))
		{
			isOnlinePayment = "1";
		}
		String payment_state = null;
		if (StringUtils.equals("1", payType))
		{
			payment_state = "01";
		}
		else if (StringUtils.equals("2", payType))
		{
			payment_state = "03";
		}
		orderListParam.put("is_online_payment", isOnlinePayment);
		orderListParam.put("payment_state", payment_state);
		// 是否需要发票
		orderListParam.put("need_invoice",CommonUtil.checkStringIsNotEmpty(orderInfo.optString("hasInvoiced"))?orderInfo.optString("hasInvoiced"):"0");
		// 发票抬头
		orderListParam.put("invoice_title", CommonUtil.checkStringIsNotEmpty(orderInfo.optString("invoiceTitle"))?orderInfo.optString("invoiceTitle"):"");
		//纳税人号
				orderListParam.put("taxpayerid",CommonUtil.checkStringIsNotEmpty(orderInfo.optString("taxpayerId")) ? CommonUtil.replaceEvilChar(orderInfo.optString("taxpayerId").trim()) : "");
		// 订单备注
		if(orderInfo.optString("caution").contains("'")){
			orderInfo.put("caution", orderInfo.optString("caution").replaceAll("'", " "));
	        }
		orderListParam.put("remark", orderInfo.optString("caution"));
		// 订单创建时间
		// long singleTime=thirdPartyOrder.optLong("ctime");
//		orderListParam.put("single_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
		orderListParam.put("single_time", CommonUtil.getDateFromSeconds(String.valueOf(orderInfo.optLong("ctime"))));

		if (discount_fee != 0.0)
		{
			orderListParam.put("discount_mode_id", "7");
		}
		// 顾客姓名
		String recipient=orderInfo.optString("recipientName").trim();
		recipient=CommonUtil.replaceEvilChar(recipient);
		//chang hui 2017-12-15 新美大外卖自提的时候，收货人默认为"美团客人"，然后美团自提的时候收货人是空，
		//所以梁海军要求新美大外卖自提的时候也要为空，所以做过滤字符串操作 add start
		if(pickType == 1){
			recipient=recipient.replace("美团客人", "");
		}
		//end
		orderListParam.put("consigner", recipient);
		// 订餐人
		orderListParam.put("order_name", recipient);
		// 顾客性别
		orderListParam.put("sex", recipient.contains("先生") ? "man" : "woman");	
		// 顾客电话
		orderListParam.put("consigner_phone", orderInfo.optString("recipientPhone"));

		// 订餐电话
		orderListParam.put("order_phone", orderInfo.optString("recipientPhone"));
		// 渠道
		orderListParam.put("chanel", channel);
		// 送餐地址
		if(orderInfo.optString("recipientAddress").contains("'")){
			orderListParam.put("recipient_address", orderInfo.optString("recipientAddress").replaceAll("'", " "));
	        }
		orderListParam.put("address", orderInfo.optString("recipientAddress"));
		// 送餐地址百度经度
		orderListParam.put("longitude", orderInfo.optString("longitude"));
		// 送餐地址百度纬度
		orderListParam.put("latitude", orderInfo.optString("latitude"));
		// 订单状态
		orderListParam.put("order_state", "01");
		// 订单类型
		orderListParam.put("order_type", "WM02");

		orderListParam.put("third_order_state", "1");
		orderListParam.put("order_state_desc", "待确认");
		/*	String storeSql = "SELECT a.district_id,a.delivery_fee_id as meals_id,b.service_id from cc_third_organ_info a LEFT JOIN cc_meals_info b on b.id=a.delivery_fee_id where a.shop_id=" + storeId;
		List list = this.dao.query4Json(tenantId, storeSql);
		if (!list.isEmpty())
		{
			orderListParam.put("district_id", ((JSONObject) list.get(0)).optString("district_id"));
			orderListParam.put("meals_id", ((JSONObject) list.get(0)).optString("meals_id"));
//			orderListParam.put("service_id", ((JSONObject) list.get(0)).optString("service_id"));
		}
		String service_sql = "SELECT a.service_id from cc_meals_info a  where a.channel='"+Constant.XMDWM_CHANNEL+"' and meals_type='PS01' and a.store_id=" + storeId ;
		List service_list = this.dao.query4Json(tenantId, service_sql);
		if (!service_list.isEmpty())
		{
			orderListParam.put("service_id", ((JSONObject) service_list.get(0)).optString("service_id"));
		}*/
		
		 //------------start田老师对账报表佣金+实收不等于应收问题修复2017-06-06-------------------------
      	orderListParam.put("platform_rate", CommonUtil.keepTwoDecimal(total_plateformRate));//优惠信息平台承担
      	orderListParam.put("shop_rate", CommonUtil.keepTwoDecimal(total_shopRate));//优惠信息商户承担
      	//------------end田老师对账报表佣金+实收不等于应收问题修复2017-06-06-------------------------
		
      	CcPartitionUtils.lackInsertParam(tenantId,orderListParam);
		
		product_amount = Scm.psub(orderInfo.optDouble("originalPrice",0), send_fee);
		orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_list", orderListParam)) ;

        return orderListParam;
	}

	@Override
	public void saveOrderItem() throws Exception {
		// TODO Auto-generated method stub
		discountRate = (double) (discount_fee+discountR_amount) / product_amount;
		sendFeeRate = (double) send_fee / product_amount;
		food_sharing_date=Scm.pdiv(takeawayBusinessIncome, Double.valueOf(product_amount));
		double discountR_rate=discountR_amount/ product_amount;
		JSONArray requestProductsArray = JSONArray.fromObject(orderInfo.optString("detail"));

		JSONObject orderItemParam = null;
		int group_index = 1;
		
		int boxNumber = 0;
		int productsListSize = requestProductsArray.size();
		double product_discount_amount = 0.0;// 
		double product_send_fee_amount = 0.0;// 
		double share_product_price_total= 0.0;		//
		String item_ids="";
		for (int i = 0; i < productsListSize; i++)
		{
			item_ids += String.valueOf(requestProductsArray.getJSONObject(i).optInt("app_food_code"))+",";
		}
		String query_sql="SELECT item_code,is_charge_commission from cc_third_item_info a where cast(a.item_code as INT) in ("+item_ids.substring(0, item_ids.length()-1)+") and channel='"+Constant.XMDWM_CHANNEL+"' and shop_id="+storeId+" ";
		List<JSONObject> third_item_info_list =this.dao.query4Json(tenantId, query_sql);
		Map<String, String> third_item_info_list_map = new HashMap<String, String>();
		
		if(third_item_info_list.size()>0){
			for(JSONObject third_item_obj:third_item_info_list){
				third_item_info_list_map.put(String.valueOf(third_item_obj.optInt("item_code")), third_item_obj.optString("is_charge_commission"));
			}
		}
		//at 2017-10-16解决数据第一个菜品没有餐盒，第二个菜品有餐盒引起的餐盒费用取餐盒费为0的问题；
		//at 2018-03-23解决数据菜品餐盒数量为0，但餐盒费用不为0，引起餐盒费取值错误的问题；
		double boxPrice = 0;
		for (int j = 0; j < productsListSize; j++)
		{
			JSONObject itemObj=requestProductsArray.getJSONObject(j);
			boxPrice=itemObj.optDouble("box_price",0);
			int bm=itemObj.optInt("box_num");
			if(boxPrice!=0&&bm!=0){
				break;
            }
		}
		//end 2017-10-16
		
		int box_num=0;
		for (int j = 0; j < productsListSize; j++)
		{
			if(requestProductsArray.getJSONObject(j).optDouble("box_price",0)!=0){
				box_num += requestProductsArray.getJSONObject(j).optInt("box_num");
            }
		}
		for (int i = 0; i < productsListSize; i++)
		{
			orderItemParam = new JSONObject();
			
			int productAmount = requestProductsArray.getJSONObject(i).optInt("quantity");
			if(requestProductsArray.getJSONObject(i).optString("food_name").contains("红包")&&requestProductsArray.getJSONObject(i).optDouble("price",0)==0.0){				
				continue;
			}
						
			if(requestProductsArray.getJSONObject(i).optDouble("box_price",0)!=0){
            	boxNumber += requestProductsArray.getJSONObject(i).optInt("box_num");
            }
			
			orderItemParam.put("tenancy_id", tenantId);
			
			orderItemParam.put("group_index", group_index++);
			orderItemParam.put("order_code", orderCode);
			int itemId = requestProductsArray.getJSONObject(i).optInt("app_food_code",0);
			
			//判断item_id是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
			orderItemParam.put("item_id", itemId);
			//end
			orderItemParam.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
			orderItemParam.put("is_commission", CommonUtil.checkStringIsNotEmpty(third_item_info_list_map.get(itemId))?third_item_info_list_map.get(itemId):"0");
			
			//查询餐普ID
			String item_menu_id_sql = "SELECT c.item_menu_id from hq_item_menu_details a  left join hq_item_menu_class d on a.id=d.details_id LEFT JOIN  hq_item_menu  b on b.id=a.item_menu_id LEFT JOIN hq_item_menu_organ c on c.item_menu_id=b.id where c.store_id=" + storeId + " and a.item_id="
						+ itemId + " and d.chanel='"+Constant.XMDWM_CHANNEL+"'";
			List<JSONObject> item_menu_id_list = this.dao.query4Json(tenantId, item_menu_id_sql);
			if (item_menu_id_list.size() > 0)
			{
				orderItemParam.put("item_menu_id", item_menu_id_list.get(0).optInt("item_menu_id"));
			}

			//String[] productNames=requestProductsArray.getJSONObject(i).optString("food_name").split("_");
			String itemName = requestProductsArray.getJSONObject(i).optString("food_name");
			orderItemParam.put("item_name", itemName);
			int unitId = requestProductsArray.getJSONObject(i).optInt("sku_id",0);
			//判断unitId是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
			orderItemParam.put("unit_id", unitId);
			//规格名称
			orderItemParam.put("unit_name", CommonUtil.checkStringIsNotEmpty(requestProductsArray.getJSONObject(i).optString("unit"))?requestProductsArray.getJSONObject(i).optString("unit"):"");
			//end
			orderItemParam.put("number", productAmount);
			
			double discountPrice = requestProductsArray.getJSONObject(i).optDouble("price",0);
			orderItemParam.put("discount_price", CommonUtil.keepTwoDecimal(discountPrice));
			double price=requestProductsArray.getJSONObject(i).optDouble("price",0);
			orderItemParam.put("price", CommonUtil.keepTwoDecimal(price));
			//菜品原价合计
			product_org_total_fee+=price*productAmount;
			orderItemParam.put("product_fee", Scm.pmui(price, productAmount * 1.0));
			Double product_fee = Scm.pmui(price, productAmount * 1.0);
			double discount_amount =Double.valueOf(product_fee)*discountRate;
			double discountk_amount=product_fee*discountR_rate;
			comboDiscountRate = (double) discount_amount / Double.valueOf(product_fee);
			double send_fee_amount = Scm.pmui(Double.valueOf(product_fee), sendFeeRate);
			product_discount_amount += discount_amount;
			product_send_fee_amount += send_fee_amount;
			orderItemParam.put("discount_amount", Double.isNaN(discount_amount)?"0":CommonUtil.keepTwoDecimal(discount_amount));
			orderItemParam.put("discountk_amount", Double.isNaN(discountk_amount)?0.0:CommonUtil.keepTwoDecimal(discountk_amount));
			orderItemParam.put("costs", CommonUtil.keepTwoDecimal(send_fee_amount));
			double real_amount = Scm.psub(Double.valueOf(product_fee), discount_amount);
			orderItemParam.put("real_amount", CommonUtil.keepTwoDecimal(real_amount));
//			orderItemParam.put("share_amount", costs_real_amount);
			if(box_num==0&&share_product_price_total<takeawayBusinessIncome&&i==productsListSize-1){
				orderItemParam.put("share_amount", CommonUtil.keepTwoDecimal(Scm.psub(takeawayBusinessIncome, share_product_price_total)));
			}else{
				orderItemParam.put("share_amount", CommonUtil.keepTwoDecimal(Scm.pmui(Double.valueOf(product_fee), food_sharing_date)));
			}
			share_product_price_total += orderItemParam.optDouble("share_amount",0);
			
//			if(discount_fee!=0.0){
			orderItemParam.put("discount_mode_id", "7");
			orderItemParam.put("store_id", storeId);
//			}
			
			CcPartitionUtils.lackInsertParam(tenantId,orderItemParam);
			
			orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_item", orderItemParam)) ;
			String item_infoSql = "select is_combo from hq_item_info a where a.id=" + itemId;
			List<JSONObject> list = this.dao.query4Json(tenantId, item_infoSql);
			if (list.size() > 0)
			{

				JSONObject item_info_obj = new JSONObject();
				item_info_obj = list.get(0);
				if (item_info_obj.optString("is_combo").equalsIgnoreCase("y"))
				{

					List<JSONObject> order_item_details_list = new ArrayList<JSONObject>();
					String hq_item_combo_details_sql = "select * from hq_item_combo_details a where a.iitem_id=" + itemId;
					List<JSONObject> item_combo_details_list = this.dao.query4Json(tenantId, hq_item_combo_details_sql);
					// 套餐明细表
					if (item_combo_details_list.size() > 0)
					{
						int b = 1;
						for (int k = 0; k < item_combo_details_list.size(); k++)
						{
							b++;
							JSONObject item_combo_details_obj = item_combo_details_list.get(k);
							double combo_discount_amount = 0.0;// 套餐已优惠总金额
							int combo_num = item_combo_details_obj.optInt("combo_num");
							if (b == item_combo_details_list.size())
							{
								// 套餐明细是项目组
								if (item_combo_details_obj.optString("is_itemgroup").equalsIgnoreCase("y"))
								{
									hq_item_combo_details_sql = "select a.* ,b.standard_price from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id  where a.item_group_id=" + item_combo_details_obj.optInt("details_id");
									List<JSONObject> item_group_details_list = this.dao.query4Json(tenantId, hq_item_combo_details_sql.toString());
									if (item_group_details_list.size() > 0)
									{
										for (int l = 0; l < combo_num; l++)
										{
											JSONObject item_group_details_obj = item_group_details_list.get(l);
											item_group_details_obj.put("group_index", group_index - 1);
											item_group_details_obj.put("order_code", orderCode);
											//判断iten_id是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
											item_group_details_obj.put("item_id", item_group_details_obj.optInt("item_id",0));
											//end
											//判断unit_id是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
											item_group_details_obj.put("unit_id", item_group_details_obj.optInt("item_unit_id",0));
											//end
											item_group_details_obj.put("price", CommonUtil.keepTwoDecimal(Double.valueOf(item_combo_details_obj.optString("standardprice","0"))));
											item_group_details_obj.put("number", productAmount);
											item_group_details_obj.put("product_fee", CommonUtil.keepTwoDecimal((item_combo_details_obj.optDouble("standardprice",0)) * productAmount));
											item_group_details_obj.put("discount_amount", CommonUtil.keepTwoDecimal(discount_amount - combo_discount_amount));
											item_group_details_obj.put("real_amount", CommonUtil.keepTwoDecimal(Scm.psub(item_group_details_obj.optDouble("product_fee",0), item_group_details_obj.optDouble("discount_amount",0))));
											item_group_details_obj.put("report_date",  "'"+DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10)+"'");
											item_group_details_obj.remove("id");
											order_item_details_list.add(item_group_details_obj);
										}

									}
								}
								else
								// 套餐明细不是项目组
								{
									item_combo_details_obj.remove("id");
									item_combo_details_obj.put("order_code", orderCode);
									//判断unit_id是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
									item_combo_details_obj.put("unit_id", item_combo_details_obj.optInt("item_unit_id",0));
									//end
									item_combo_details_obj.put("price", CommonUtil.keepTwoDecimal(Double.valueOf(item_combo_details_obj.optString("standardprice","0"))));
									item_combo_details_obj.put("number", productAmount * combo_num);
									item_combo_details_obj.put("product_fee", CommonUtil.keepTwoDecimal((item_combo_details_obj.optDouble("standardprice",0)) * (item_combo_details_obj.optDouble("number",0))));
									item_combo_details_obj.put("discount_amount", CommonUtil.keepTwoDecimal(discount_amount - combo_discount_amount));
									item_combo_details_obj.put("real_amount", CommonUtil.keepTwoDecimal(Scm.psub(item_combo_details_obj.optDouble("product_fee",0), item_combo_details_obj.optDouble("discount_amount",0))));
									//判断item_id是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
									item_combo_details_obj.put("item_id", item_combo_details_obj.optInt("details_id",0));
									//end
									item_combo_details_obj.put("group_index", group_index - 1);
									item_combo_details_obj.put("report_date",  "'"+DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10)+"'");
									order_item_details_list.add(item_combo_details_obj);
								}
							}
							else
							{
								// 套餐明细是项目组
								if (item_combo_details_obj.optString("is_itemgroup").equalsIgnoreCase("y"))
								{
									hq_item_combo_details_sql = "select a.* ,b.standard_price from  hq_item_group_details a left join hq_item_unit b on a.item_unit_id= b.id  where a.item_group_id=" + item_combo_details_obj.optInt("details_id");
									List<JSONObject> item_group_details_list = this.dao.query4Json(tenantId, hq_item_combo_details_sql.toString());

									if (item_group_details_list.size() > 0)
									{
										for (int l = 0; l < combo_num; l++)
										{
											JSONObject item_group_details_obj = item_group_details_list.get(l);
											item_group_details_obj.put("group_index", group_index - 1);
											item_group_details_obj.put("order_code", orderCode);
											//判断item_id是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
											item_group_details_obj.put("item_id", item_group_details_obj.optInt("item_id",0));
											//end
											//判断unit_id是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
											item_group_details_obj.put("unit_id", item_group_details_obj.optInt("item_unit_id",0));
											//end
											item_group_details_obj.put("price", CommonUtil.keepTwoDecimal(Double.valueOf(item_combo_details_obj.optString("standardprice","0"))));
											item_group_details_obj.put("number", productAmount);
											item_group_details_obj.put("product_fee", CommonUtil.keepTwoDecimal((item_combo_details_obj.optDouble("standardprice",0)) * productAmount));
											item_group_details_obj.put("discount_amount", CommonUtil.keepTwoDecimal(Scm.pmui(item_group_details_obj.optDouble("product_fee",0), comboDiscountRate)));
											combo_discount_amount += Scm.pmui(item_group_details_obj.optDouble("product_fee",0), comboDiscountRate);
											item_group_details_obj.put("real_amount", CommonUtil.keepTwoDecimal(Scm.psub(item_group_details_obj.optDouble("product_fee",0), item_group_details_obj.optDouble("discount_amount",0))));
											item_group_details_obj.put("report_date",  "'"+DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10)+"'");
											item_group_details_obj.remove("id");
											order_item_details_list.add(item_group_details_obj);
										}

									}
								}
								else
								// 套餐明细不是项目组
								{
									item_combo_details_obj.remove("id");
									item_combo_details_obj.put("order_code", orderCode);
									//判断unit_id是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
									item_combo_details_obj.put("unit_id", item_combo_details_obj.optInt("item_unit_id",0));
									//end
									item_combo_details_obj.put("price", CommonUtil.keepTwoDecimal(Double.valueOf(item_combo_details_obj.optString("standardprice","0"))));
									item_combo_details_obj.put("number", productAmount * combo_num);
									item_combo_details_obj.put("product_fee", CommonUtil.keepTwoDecimal((item_combo_details_obj.optDouble("standardprice",0)) * (item_combo_details_obj.optDouble("number",0))));
									item_combo_details_obj.put("discount_amount", CommonUtil.keepTwoDecimal(Scm.pmui(item_combo_details_obj.optDouble("product_fee",0), comboDiscountRate)));
									combo_discount_amount += Scm.pmui(item_combo_details_obj.optDouble("product_fee",0), comboDiscountRate);
									item_combo_details_obj.put("real_amount", CommonUtil.keepTwoDecimal(Scm.psub(item_combo_details_obj.optDouble("product_fee",0), item_combo_details_obj.optDouble("discount_amount",0))));
									//判断item_id是否为空 为空置为0;不为空 判断是否为纯数字 不是纯数字 置为-1 刘娟 2017-12-22 start
									item_combo_details_obj.put("item_id", item_combo_details_obj.optInt("details_id",0));
									//end
									
									item_combo_details_obj.put("group_index", group_index - 1);
									item_combo_details_obj.put("report_date",  "'"+DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10)+"'");
									order_item_details_list.add(item_combo_details_obj);
								}
							}

						}
					}
					logger.info("订单号："+orderCode+",产生"+order_item_details_list.size()+" 几套餐明细.");
					//CcPartitionUtils.lackInsertParam(tenantId,orderItemParam,order_item_details_list);
					for(int w=0;w<order_item_details_list.size();w++){
						JSONObject order_item_detail_obj=order_item_details_list.get(w);
						JSONObject order_item_detail_obj2=new JSONObject ();
						order_item_detail_obj2.put("order_code", order_item_detail_obj.optString("order_code"));
						order_item_detail_obj2.put("unit_id", order_item_detail_obj.optString("unit_id"));
						order_item_detail_obj2.put("price", order_item_detail_obj.optString("price"));
						order_item_detail_obj2.put("number", order_item_detail_obj.optString("number"));
						order_item_detail_obj2.put("product_fee", order_item_detail_obj.optString("product_fee"));
						order_item_detail_obj2.put("discount_amount", order_item_detail_obj.optString("discount_amount"));
						order_item_detail_obj2.put("real_amount", order_item_detail_obj.optString("real_amount"));
						order_item_detail_obj2.put("item_id", order_item_detail_obj.optString("item_id"));
						order_item_detail_obj2.put("group_index", order_item_detail_obj.optString("group_index"));
						order_item_detail_obj2.put("report_date", order_item_detail_obj.optString("report_date","").replaceAll("'", ""));
						order_item_detail_obj2.put("tenancy_id", order_item_detail_obj.optString("tenancy_id"));
						order_item_detail_obj2.put("is_itemgroup", order_item_detail_obj.optString("is_itemgroup"));
						
						CcPartitionUtils.lackInsertParam(tenantId, order_item_detail_obj2);
						
						orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_item_details", order_item_detail_obj2)) ;
					}
					
					//this.dao.insertBatchIgnorCase(tenantId, "cc_order_item_details", order_item_details_list);
				}

			}
		 
		}
		
		if(boxNumber>0){
			JSONObject packageBoxFee = new JSONObject();
			packageBoxFee.put("tenancy_id", tenantId);
			packageBoxFee.put("group_index", group_index);
			packageBoxFee.put("order_code", orderCode);
			packageBoxFee.put("price", CommonUtil.keepTwoDecimal(boxPrice));
			packageBoxFee.put("number", boxNumber);
			packageBoxFee.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
			packageBoxFee.put("share_amount", "0.0");
			packageBoxFee.put("is_commission", 0);
			packageBoxFee.put("store_id", storeId);
	//		String sql = "SELECT item_id,unit_id,item_name FROM cc_third_organ_info where shop_id=".concat(storeId);
			String sql1 = "SELECT B.item_id,B.unit_id,c.item_name FROM cc_meals_info a LEFT JOIN cc_meals_info_default b  on a.id=b.meals_id LEFT JOIN hq_item_info c on c.id=b.item_id WHERE a.store_id="+storeId+" and a.channel='"+Constant.XMDWM_CHANNEL+"'  AND A.meals_type='MR03'";
			List<JSONObject> pb = this.dao.query4Json(tenantId, sql1);
			//餐盒不存在的情况也允许入库 2017-12-22 刘娟 start
			if(null==pb||pb.isEmpty()){
				packageBoxFee.put("item_id", "0");
				packageBoxFee.put("unit_id", "0");
				packageBoxFee.put("item_name", "餐盒费");
				logger.info("[新美大外卖]收单失败:未正确获取餐盒费信息!");
//				throw new Exception();
			}else{
				packageBoxFee.putAll(pb.get(0));
			}
			//end
			packageBoxFee.put("product_fee", CommonUtil.keepTwoDecimal(Scm.pmui(boxPrice, boxNumber * 1.0)));
			package_fee = Scm.pmui(boxPrice, boxNumber * 1.0);
			double packageBox_discount_amount = Scm.psub(discount_fee, product_discount_amount);
			packageBoxFee.put("discount_amount", Double.isNaN(package_fee*discountRate)?0.0:CommonUtil.keepTwoDecimal(package_fee*discountRate));
			packageBoxFee.put("discountk_amount", Double.isNaN(discountR_rate*Scm.pmui(boxPrice, boxNumber * 1.0))?0.0:CommonUtil.keepTwoDecimal(discountR_rate*boxPrice*boxNumber * 1.0));
			double packageBox_send_fee_amount = Scm.psub(send_fee, product_send_fee_amount);
			packageBoxFee.put("costs", CommonUtil.keepTwoDecimal(packageBox_send_fee_amount));
			double packageBox_real_amount = Scm.psub(Double.valueOf(package_fee), packageBox_discount_amount);
			double packageBox_send_fee_real_amount = Scm.padd(packageBox_real_amount, packageBox_send_fee_amount);
			packageBoxFee.put("real_amount", CommonUtil.keepTwoDecimal(packageBox_real_amount));
	//		packageBoxFee.put("share_amount", packageBox_send_fee_real_amount);
			packageBoxFee.put("share_amount", CommonUtil.keepTwoDecimal(Scm.psub(takeawayBusinessIncome, share_product_price_total)));
//			packageBoxFee.put("share_amount", Double.isNaN(boxPrice*boxNumber * 1.0*food_sharing_date)?0.0:boxPrice*boxNumber * 1.0*food_sharing_date);
			
			//if(discount_fee!=0.0){
			packageBoxFee.put("discount_mode_id", "7");
			//}
			
			CcPartitionUtils.lackInsertParam(tenantId,packageBoxFee);
			
			orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_item", packageBoxFee)) ;
		}
	}

	@Override
	public void saveOrderRepayment() throws Exception {
		JSONObject requestPoiDetail = JSONObject.fromObject(orderInfo.optString("poiReceiveDetail"));

        int shop_real_amount=requestPoiDetail.optInt("wmPoiReceiveCent");//分
        double shop_real_amount_y=Double.valueOf(CommonUtil.fen2Yuan(shop_real_amount));
          
		// TODO Auto-generated method stub
		JSONObject payment = new JSONObject();

		payment.put("tenancy_id", tenantId);
		payment.put("remark", "meituan_pay");
		payment.put("order_code", orderCode);
		payment.put("pay_money", CommonUtil.keepTwoDecimal(shopFee));
		payment.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
		payment.put("third_bill_code", orderInfo.optString("orderId"));
		payment.put("store_id", storeId);
		payment.put("shop_real_amount", shop_real_amount_y);
		payment.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
//		this.dao.insertIgnorCase(tenantId, "cc_order_repayment", payment);
	
		//at 2017-08-18 此值与pay_money一致
		payment.put("local_currency", CommonUtil.keepTwoDecimal(shopFee));
		
		// 支付类型
		String payType = orderInfo.optString("payType");
		String store_sql = "select remark from organ where id = " + storeId + " ";
		JSONObject store_obj = this.dao.query4Json(tenantId, store_sql).get(0);
		if (store_obj.optString("remark").equals("read_from_rif")||StringUtils.equals("2", payType))
		{
//			payment.put("tenancy_id", tenantId);
			
			//20171129 修改支付式由原来的xmd_pay改为meituan_pay
			String paySql = "SELECT a.id as payment_id FROM payment_way a LEFT JOIN payment_way_of_ogran b on a.id=b.payment_id where a.payment_class='meituan_pay' and b.organ_id=" + storeId;
			List list = this.dao.query4Json(tenantId, paySql);
			if (!list.isEmpty())
			{
				payment.put("payment_id", ((JSONObject) list.get(0)).optString("payment_id"));
			}
		}
		
		CcPartitionUtils.lackInsertParam(tenantId,payment);
		
		orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_repayment", payment)) ;
	}

	@Override
	public void saveOrderDiscount() throws Exception {
		// TODO Auto-generated method stub
		List<JSONObject> discountsList = new ArrayList<>();
		
//		StringBuilder sql=new StringBuilder();

		for (int i = 0; i < discounts.size(); i++)
		{
			JSONObject discount = discounts.getJSONObject(i);
			if(!discount.isNullObject() && !discount.isEmpty()){
				if (discount.optInt("type") != 100)
				{
					JSONObject discountParam = new JSONObject();
					// 商户ID
					discountParam.put("tenancy_id", tenantId);
					// 门店ID
					discountParam.put("store_id", storeId);
					// 订单号
					discountParam.put("order_code", orderCode);
					// 优惠类型
					discountParam.put("discount_type", discount.optString("type"));
					// 优惠金额
					discountParam.put("discount_fee", CommonUtil.keepTwoDecimal(Double.valueOf(discount.optString("reduce_fee","0"))));
					double discountFee=discount.optDouble("reduce_fee",0);
					// 活动ID
					discountParam.put("activity_id", discount.optString("act_detail_id"));
					discountParam.put("report_date",  DateUtil.format(new Timestamp(System.currentTimeMillis())).toString().substring(0, 10));
					// 美团承担金额
					discountParam.put("baidu_rate", CommonUtil.keepTwoDecimal(Double.valueOf(discount.optString("mt_charge","0"))));
					// 商户承担金额
					discountParam.put("shop_rate", CommonUtil.keepTwoDecimal(Double.valueOf(discount.optString("poi_charge","0"))));
					// 代理商承担金额
					// discountParam.put("agent_rate",
					// discounts.getJSONObject(i).optString(""));
					// 物流承担金额
					// discountParam.put("logistics_rate",
					// discounts.getJSONObject(i).optString(""));
					// 优惠描述
					discountParam.put("discount_desc", discount.optString("remark"));
					//操作时间
					discountParam.put("operator_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));

					discountsList.add(discountParam);
					
					CcPartitionUtils.lackInsertParam(tenantId,discountParam);
					
					orderBatchSql.append(CommonUtil.insertJSONParamsToSql("cc_order_discount", discountParam)) ;
				}
			}
		}
		if(discountsList.isEmpty()){
			return;
		}
	}

	@Override
	public JSONObject orderStatusPush(JSONObject params) {
		try
		{
			String status = params.optString("status");
			
			switch (status)
			{
				case ORDER_CANCEL:
					orderInfo = params.optJSONObject("orderCancel");
					break;
				case ORDER_COMPLETE:
					break;
				default:
					break;
			}
			
			xmd_id = orderInfo.optString("orderId");
			//at 20170822改变redis读取方式
			// 读redis
			//JSONObject dat = taskRedisDao.read(meituan_id.getBytes());
			String datStr=redis.getByKey(xmd_id);
			logger.info("新美大外卖从redis里取到==========1521=============店铺的信息"+datStr);
			JSONObject dat=JSONObject.fromObject(datStr);			
			logger.info("新美大外卖从redis里取到的信息"+dat.toString());
			//this.taskRedisDao.save(meituan_id.getBytes(), dat);
			logger.info("新美大外卖从redis里取到信息后执行");
			String[] shopId = dat.optString("app_poi_code").split("@");
			this.storeId = shopId[0];
			this.tenantId = shopId[1];
			DBContextHolder.setTenancyid(tenantId);
			
			String reason_code = orderInfo.optString("reasonCode");
			logger.info("新美大外卖从redis里取到==========1521=============店铺的取消状态"+reason_code);
			logger.info("新美大外卖从redis里取到==========1521=============店铺的取消状态xmd_id"+xmd_id+",third_order_code->"+dat.optString("third_order_code"));
		    //String reason = params.optString("reason");
			switch (status)
			{
				case ORDER_CANCEL:
					this.xmdCancel(tenantId, reason_code,dat.optString("third_order_code"));
					JSONObject dataObj = new JSONObject();
					dataObj.put("tenancy_id", tenantId);
					dataObj.put("store_id", storeId);
					dataObj.put("reason_code", "999801");
					dataObj.put("third_order_code", dat.optString("third_order_code"));
					orderManagementService.orderCancel(tenantId, dataObj);
					break;
				case ORDER_COMPLETE:
					JSONObject data_complete_Obj = new JSONObject();
					data_complete_Obj.put("tenancy_id", tenantId);
					data_complete_Obj.put("store_id", storeId);
					data_complete_Obj.put("third_order_code", orderId);
					orderManagementService.orderComplete(tenantId, data_complete_Obj);
					break;
				default:
					break;
			}

			err.put("errno", "0");

		}
		catch (Exception e)
		{
			e.printStackTrace();
			err.put("errno", "1");
			err.put("error", e);
			err.put("errmsg", e.getMessage());
		}

		return thirdPartyResponse();
	}
	
	/**
	 * 新增取消订单状态
	 */
    public void xmdCancel(String tenantId,String reason_code,String meituan_id){
			try {
				//新美大外卖 判断平台取消，还是用户取消 
					//查询数据库，看订单状态是否被服务员取消过
					String sql = null;
					//用户取消
					if(
							reason_code.equalsIgnoreCase("1301")||reason_code.equalsIgnoreCase("1101")||reason_code.equalsIgnoreCase("1102")
							||reason_code.equalsIgnoreCase("1103")||reason_code.equalsIgnoreCase("2006")
							){
						sql = "update cc_order_list set cancel_type='1',order_state='08',payment_state='04' where third_order_code='" + meituan_id +"'";
						//平台取消
					}else if(
							reason_code.equalsIgnoreCase("1001")||reason_code.equalsIgnoreCase("1002")||
							reason_code.equalsIgnoreCase("2001")||reason_code.equalsIgnoreCase("2002")||reason_code.equalsIgnoreCase("2003")||
							reason_code.equalsIgnoreCase("2005")||reason_code.equalsIgnoreCase("2007")
							){
						sql = "update cc_order_list set cancel_type='2',order_state='08',payment_state='04' where third_order_code='" + meituan_id +"'";
						//服务员取消
					}else{
						sql = "update cc_order_list set cancel_type='0',order_state='08',payment_state='04' where third_order_code='" + meituan_id +"'";
					}
					
					sql=CcPartitionUtils.makeSQL(tenantId,sql, "", CcPartitionUtils.TYPE_ORDERCODE_NO);
				dao.execute(tenantId, sql);
			} catch (Exception e) {
				e.printStackTrace();
		}
    }
    
  //面香新的佣金计算公式
  	 public void calcCommission() {
  	        try {
  	        	JSONObject param = new JSONObject();
  				param.put("order_code", orderCode);
  				param.put("tenentid", tenantId);
  	        	JSONObject requestPoiDetail = JSONObject.fromObject(orderInfo.optString("poiReceiveDetail"));

  	        	double mtDiscountCommission = Double.valueOf(CommonUtil.fen2Yuan(requestPoiDetail.optInt("foodShareFeeChargeByPoi",0)));//分
  	            int shop_real_amount=requestPoiDetail.optInt("wmPoiReceiveCent");//分
  	            double platform_charge_amount=shopFee-Double.valueOf(CommonUtil.fen2Yuan(shop_real_amount));
  	            
  	            //at 20170904  优惠总额大于等订单总额于时，平台收取金额（platform_charge_amount)加上0.01
  	    		if(total_discount_fee>=order_total_price&&user_actual_pay==0.01){
  	    			platform_charge_amount+=0.01;
  	    		}
  	    		
  	          //at 2017-08-07   start
  	            //本地门店实收=餐盒费+菜品明细-佣金（本地计算的值）-商家承担
  	    		//double local_shop_real_amount=package_fee+product_org_total_fee-total_shopRate-mtDiscountCommission;
  	    		
  	            // 用户实付总价
  	            double actualPay=orderInfo.optDouble("total",0);
  	            double local_shop_real_amount=0;
  	            // 物流
  	    		String logisticsCode=orderInfo.optString("logisticsCode");
  	    		String deliveryParty="1";
  	    		if(logisticsCode.equals("0000")){//商家自配送
  	    			deliveryParty="2";
  	    		}
  	    		if(logisticsCode.equals("1003")){//美团众包配送
  	    			deliveryParty="3";
  	    		}
  	            if(deliveryParty.equals("1")){
  	            	//本地门店实收(平台送)=在线支付+平台承担-配送费-佣金
  	            	local_shop_real_amount=actualPay+total_plateformRate-send_fee-mtDiscountCommission;
  	            }else{
  	            	//本地门店实收(自配送)=在线支付+平台承担-佣金
  	            	local_shop_real_amount=actualPay+total_plateformRate-mtDiscountCommission;
  	            }
  	            
  	            //at 2017-08-07  end
  	    		
  	            JSONObject paramsNew = new JSONObject();
  	            paramsNew.put("order_code", orderCode);
  	            paramsNew.put("tenentid", tenantId);
  	            paramsNew.put("discount_commission", CommonUtil.keepTwoDecimal(mtDiscountCommission));
  	            paramsNew.put("calculate_commission_amount", CommonUtil.keepTwoDecimal(mtDiscountCommission));
  	            paramsNew.put("local_shop_real_amount", CommonUtil.keepTwoDecimal(local_shop_real_amount));
  	            paramsNew.put("commission_rate", 1);
  				paramsNew.put("shop_real_amount", CommonUtil.keepTwoDecimal(Double.valueOf(CommonUtil.fen2Yuan(shop_real_amount))));
  				paramsNew.put("platform_charge_amount",CommonUtil.keepTwoDecimal(platform_charge_amount));
  				 paramsNew.put("product_org_total_fee", CommonUtil.keepTwoDecimal(product_org_total_fee));
  				paramsNew.put("package_box_fee",CommonUtil.keepTwoDecimal(package_fee));
  	            orderDiscountCommissionReportService.updateYjxx4Order(paramsNew);
  	        } catch (Exception e) {
  	            e.printStackTrace();
  	            logger.error(e.getMessage());
  	        }
  	    }

	@Override
	public JSONObject orderStatusGet(JSONObject params) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	JSONObject thirdPartyResponse() {
		JSONObject resp = new JSONObject();
		if (err.optInt("errno") == 0)
		{
			resp.put("data", "ok");
			return resp;
		}
		resp.put("data", "ng");
		Map error = new HashMap();
		error.put("code", err.optString("errno"));
		error.put("msg", err.optString("error"));
		resp.put("error", error);
		return resp;
	}
	
	
		//at 2018-03-05
		@Override
		protected JSONObject createCustomerInfo() {
			String address=orderInfo.optString("recipientAddress");
			String phone=orderInfo.optString("recipientPhone");
			String recipient=orderInfo.optString("recipientName").trim();
			
			JSONObject customer=new JSONObject();
			customer.put("mobil", phone);
			customer.put("add_chanel", Constant.XMDWM_CHANNEL);
			customer.put("sex", "");
			customer.put("name", recipient);
			
			JSONObject addresslist=new JSONObject();		

			addresslist.put("sex", "");
			addresslist.put("province", "");
			addresslist.put("city", "");
			addresslist.put("area", "");
			addresslist.put("address", address);
			addresslist.put("consignee_phone", phone);
			addresslist.put("consignee", recipient);
			addresslist.put("longitude", orderInfo.optString("longitude"));
			addresslist.put("latitude", orderInfo.optString("latitude"));
			addresslist.put("baidu_location", "");
					
			
			customer.put("addresslist", addresslist);
			
			return customer;
		}

}
