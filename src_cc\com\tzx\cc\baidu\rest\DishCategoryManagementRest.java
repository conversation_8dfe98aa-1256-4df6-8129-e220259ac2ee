package com.tzx.cc.baidu.rest;

import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.cc.baidu.bo.DishService;
import com.tzx.cc.common.constant.DishOper;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

@Controller("DishCategoryManagementRest")
@RequestMapping("/thirdparty/dishcategorymanagementrest")
public class DishCategoryManagementRest
{

	@Resource(name = DishService.NAME)
	private DishService	dishService;

	/**
	 * 获取菜品类别列表
	 */
	@RequestMapping(value = "/loadDishCategoryList")
	public void loadDishCategoryList(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			String tenantId = (String) session.getAttribute("tenentid");

			DBContextHolder.setTenancyid(tenantId);

			result = dishService.loadDishCategoryList(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	 * 新增菜品分类
	 */
	@RequestMapping(value = "/dishDategoryCreate")
	public void dishDategoryCreate(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("requestId", requestId.toString());
			String tenantId = (String) session.getAttribute("tenentid");

			DBContextHolder.setTenancyid(tenantId);
			
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("dishCategory");
			ccBusniessLogBean.setChannel(obj.optString("channel"));
			ccBusniessLogBean.setChannelName(obj.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.DishCategoryManagementRest:dishDategoryCreate");
			ccBusniessLogBean.setRequestBody(obj.toString());
			
			
			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
			if(obj.containsKey("dishes_class")){
			  ccBusniessLogBean.setThirdId(obj.optJSONArray("dishes_class").getJSONObject(0).optString("third_class_id"));
			  ccBusniessLogBean.setTzxId(obj.optJSONArray("dishes_class").getJSONObject(0).optString("class_id"));
		      ccBusniessLogBean.setTzxName(obj.optJSONArray("dishes_class").getJSONObject(0).optString("cur_class_name"));	
		      ccBusniessLogBean.setShopId(obj.optJSONArray("dishes_class").getJSONObject(0).optString("store_id"));
			}else{
			  ccBusniessLogBean.setThirdId(obj.optString("third_class_id"));
			  ccBusniessLogBean.setTzxId(obj.optString("class_id"));
		      ccBusniessLogBean.setTzxName(obj.optString("cur_class_name"));	
		      ccBusniessLogBean.setShopId(obj.optString("store_id"));
			}

			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.pushDishCategory.toString());

			result = dishService.dishDategoryCreate(tenantId, obj).toString();
			ccBusniessLogBean.setResponseBody(result.toString());
		}
		catch (Exception e)
		{
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		}
		finally
		{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	 * 修改菜品分类
	 */
	@RequestMapping(value = "/dishDategoryUpdate")
	public void dishDategoryUpdate(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("requestId", requestId.toString());
			String tenantId = (String) session.getAttribute("tenentid");

			DBContextHolder.setTenancyid(tenantId);
			
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("dishCategory");
			ccBusniessLogBean.setChannel(obj.optString("channel"));
			ccBusniessLogBean.setChannelName(obj.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.DishCategoryManagementRest:dishDategoryUpdate");
			ccBusniessLogBean.setRequestBody(obj.toString());
			
			
			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
			if(obj.containsKey("dishes_class")){
			  ccBusniessLogBean.setThirdId(obj.optJSONArray("dishes_class").getJSONObject(0).optString("third_class_id"));
			  ccBusniessLogBean.setTzxId(obj.optJSONArray("dishes_class").getJSONObject(0).optString("class_id"));
		      ccBusniessLogBean.setTzxName(obj.optJSONArray("dishes_class").getJSONObject(0).optString("cur_class_name"));	
		      ccBusniessLogBean.setShopId(obj.optJSONArray("dishes_class").getJSONObject(0).optString("store_id"));
			}else{
			  ccBusniessLogBean.setThirdId(obj.optString("third_class_id"));
			  ccBusniessLogBean.setTzxId(obj.optString("class_id"));
		      ccBusniessLogBean.setTzxName(obj.optString("cur_class_name"));	
		      ccBusniessLogBean.setShopId(obj.optString("store_id"));
			}

			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.pushDishCategory.toString());
			result = dishService.dishCategoryUpdate(tenantId, obj).toString();
			ccBusniessLogBean.setResponseBody(result.toString());
		}
		catch (Exception e)
		{
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		}
		finally
		{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	// 加载机构百度外卖渠道下菜品类别
	@RequestMapping(value = "/findStoreChannelClass")
	public void findStoreChannelClass(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (!"".equals(map.get(key)[0]))
				{
					p.put(key, map.get(key)[0]);
				}

			}
			result = dishService.findStoreChannelClass((String) session.getAttribute("tenentid"), p).toString();

		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"查询会员等级时发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 * 按门店批量推送菜品类别
	 */
	@RequestMapping(value = "/batchDishDategoryPush")
	public void batchDishDategoryPush(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		CcBusniessLogBean ccBusniessLogBean=new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("requestId", requestId.toString());

			String tenantId = (String) session.getAttribute("tenentid");

			DBContextHolder.setTenancyid(tenantId);
			
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("dishCategory");
			ccBusniessLogBean.setChannel(obj.optString("channel"));
			ccBusniessLogBean.setChannelName(obj.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.DishCategoryManagementRest:batchDishDategoryPush");
			ccBusniessLogBean.setRequestBody(obj.toString());
			
			
			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");

			//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
			if(obj.containsKey("dishes_class")){
			  ccBusniessLogBean.setThirdId(obj.optJSONArray("dishes_class").getJSONObject(0).optString("third_class_id"));
			  ccBusniessLogBean.setTzxId(obj.optJSONArray("dishes_class").getJSONObject(0).optString("class_id"));
		      ccBusniessLogBean.setTzxName(obj.optJSONArray("dishes_class").getJSONObject(0).optString("cur_class_name"));	
		      ccBusniessLogBean.setShopId(obj.optJSONArray("dishes_class").getJSONObject(0).optString("store_id"));
			}else{
			  ccBusniessLogBean.setThirdId(obj.optString("third_class_id"));
			  ccBusniessLogBean.setTzxId(obj.optString("class_id"));
		      ccBusniessLogBean.setTzxName(obj.optString("cur_class_name"));	
		      ccBusniessLogBean.setShopId(obj.optString("store_id"));
			}

			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.pushDishCategory.toString());
			result = dishService.batchDishDategoryPush(tenantId, obj).toString();
			ccBusniessLogBean.setResponseBody(result.toString());
		}
		catch (Exception e)
		{
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		}
		finally
		{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 *根据机构id查询要推送的菜品类别
	 */
	@RequestMapping(value = "/loadDishCategoryListByStoreId")
	public void loadDishCategoryListByStoreId(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			result = dishService.loadDishCategoryListNoPage(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
}
