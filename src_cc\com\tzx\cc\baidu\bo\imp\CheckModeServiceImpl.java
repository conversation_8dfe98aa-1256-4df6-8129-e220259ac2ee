package com.tzx.cc.baidu.bo.imp;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.google.gson.reflect.TypeToken;
import com.tzx.cc.baidu.bo.CheckModeService;
import com.tzx.cc.baidu.util.CommonUtil;
import com.tzx.framework.bo.dto.BasicCombobox;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.dao.GenericDao;

@Service(CheckModeService.NAME)
public class CheckModeServiceImpl implements CheckModeService
{
	private static final Logger	logger	= Logger.getLogger(CheckModeServiceImpl.class);
	
	@Resource(name = "genericDaoImpl")
	private GenericDao		dao;

	@Override
	public JSONObject loadCheckModeList(String tenancyID, JSONObject condition){
		JSONObject result = new JSONObject();
		try
		{
			StringBuffer sql=new StringBuffer();
			sql.append("select cc.*,o.org_full_name,pw.payment_name1 as payment_name,pw.id as payment_id from cc_third_check_mode cc left join organ o on cc.store_id=o.id left join payment_way pw on cc.payment_id=pw.id where 1=1 ");
			if(condition.containsKey("shop_id")&&StringUtils.isNotEmpty(condition.optString("shop_id"))&&!"0".equals(condition.optString("shop_id"))){
				sql.append(" and cc.store_id in("+condition.optString("shop_id")+") ");
			}
			if(condition.containsKey("code")&&StringUtils.isNotEmpty(condition.optString("code"))){
				sql.append(" and cc.store_id in(SELECT org.id FROM organ org WHERE org.organ_code LIKE '"+condition.optString("code")+"%' AND org.org_type!='2') ");
			}
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
			
			long total = this.dao.countSql(tenancyID, sql.toString());
			List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);

		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public JSONObject saveCheckMode(String tenancyID, JSONObject condition){
		JSONObject result = new JSONObject();
		Object id=null;
		try {
			/*if(condition.containsKey("payment_id")&&StringUtils.isNotEmpty("payment_id")){
				condition.put("payment_id", "4");//默认值为现金
			}*/
			if(condition.containsKey("id")&&StringUtils.isNotEmpty(condition.optString("id"))){
				id=this.dao.updateIgnorCase(tenancyID, "cc_third_check_mode", condition);	
			}else{
				id=this.dao.insertIgnorCase(tenancyID, "cc_third_check_mode", condition);
			}
			if(id!=null){
				result.put("success", true);
				result.put("msg", "保存成功");
			}else{
				result.put("success", false);
				result.put("msg", "保存失败");
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("[第三方设置结算方式失败]更改数据："+condition);
		}
		return result;
	
	}

	@Override
	public JSONObject getShopCheckMode(String tenantId, JSONObject obj) throws Exception {
		StringBuffer sbf=new StringBuffer();
		JSONObject result = new JSONObject();
		sbf.append("select * from cc_third_check_mode cc where 1=1 ");
		if(obj.containsKey("store_id")&&StringUtils.isNotEmpty(obj.optString("store_id"))){
			sbf.append(" and cc.store_id='"+obj.optString("store_id")+"' ");
		}
		if(obj.containsKey("channel")&&StringUtils.isNotEmpty(obj.optString("channel"))){
			sbf.append(" and cc.channel='"+obj.optString("channel")+"' ");
		}
		
		List<JSONObject> list = this.dao.query4Json(tenantId, sbf.toString());
		if (list.size() > 0)
		{
			result.put("rows", list);
		}
		else
		{
			result.put("rows", "[]");
		}
		return result;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public String findShopPayment(String tenancyId, Integer type, Object param)
	{
		JSONObject jb = (JSONObject) param;
		StringBuilder sb = new StringBuilder();
		// String result = "";
		switch (type)
		{
		// 查询数据字段
			case 0:
				if (jb.containsKey("store_id"))
				{
					sb.append("SELECT pw.id AS id,pw.payment_name1 AS text FROM payment_way pw LEFT JOIN payment_way_of_ogran pwof ON pwof.payment_id=pw.id");
						if(StringUtils.isNotEmpty(jb.optString("store_id"))){
							sb.append(" AND pwof.organ_id='"+jb.optString("store_id")+"' ");
						}

					try
					{
						List<BasicCombobox> list = (List<BasicCombobox>) this.dao.query(tenancyId, sb.toString(), BasicCombobox.class);
						for(BasicCombobox combox:list){
							if(combox.getText().equals("RMB")){
								combox.setText("现金");
							}
						}
						return com.tzx.framework.common.util.JsonUtils.list2json(list);
					}
					catch (Exception e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
		}
		return null;
	}
	
	
	
	@Override
	public JSONObject findRoundingMode(String tenancyId,Object param)throws Exception{
		JSONObject jb = (JSONObject) param;
		StringBuilder sb = new StringBuilder();
		JSONObject result=new JSONObject();
		// 查询数据字段
		sb.append("select para_value as id,values_name as text from sys_parameter WHERE 1=1 ");
		if(jb.containsKey("code")&&CommonUtil.checkStringIsNotEmpty(jb.optString("code"))){
			sb.append(" and para_code='"+jb.getString("code")+"'");
		}
		if(jb.containsKey("store_id")&&CommonUtil.checkStringIsNotEmpty(jb.optString("store_id"))){
			sb.append(" and store_id='"+jb.optString("store_id")+"' ");
		}
		SqlRowSet rs =this.dao.query(tenancyId,  sb.toString());
		if(rs.next()){
			result.put("id", rs.getString("id"));
			result.put("text", rs.getString("text"));
		}else{
			rs=this.dao.query(tenancyId, "select para_value as id,values_name as text from sys_parameter WHERE 1=1 and store_id='0' and para_code='"+jb.getString("code")+"'");
			if(rs.next()){
				result.put("id", rs.getString("id"));
				result.put("text", rs.getString("text"));
			}
		}
			return result;
		
	}

	@Override
	public Boolean saveCopyCheckModeInfo(String tenancyID, JSONObject obj) throws Exception
	{
		Boolean flag = true;
		if (obj.containsKey("copy_checkModes") && obj.get("copy_checkModes") != "")
		{
			@SuppressWarnings("unchecked")
			List<JSONObject> list = (List<JSONObject>) GsonUtil.toT(obj.get("copy_checkModes").toString(), new TypeToken<List<JSONObject>>()
			{}.getType());
			if(list.size()>0){
				if(obj.optString("torgan_id").length()>0){
					String sql="SELECT * from cc_third_check_mode a where 1=1 AND A.store_id IN ("+obj.optString("torgan_id")+") ";
					List<JSONObject> all_third_check_mode_to_org_list =this.dao.query4Json(tenancyID, sql);
					Map<String, String> all_third_check_mode_to_org_map = new HashMap<String, String>();
					
					if(all_third_check_mode_to_org_list.size()>0){
						for(JSONObject to_org_check_mode_obj:all_third_check_mode_to_org_list){
							all_third_check_mode_to_org_map.put(to_org_check_mode_obj.optString("store_id")+"_"+to_org_check_mode_obj.optString("channel"), "true");
						}
					}
					List<String> add_update_third_check_mode_list = new ArrayList<String>();
					for(JSONObject check_mode_obj:list){
						String toIds = obj.getString("torgan_id");
						String[] ids = toIds.split(",");
						for (String store_id : ids)
						{
							String add_or_update_sql="";
								if(all_third_check_mode_to_org_map.containsKey(store_id+"_"+check_mode_obj.optString("channel"))){
									add_or_update_sql="UPDATE cc_third_check_mode SET settlement_type = '"+check_mode_obj.optString("settlement_type")+"', check_mode = '"+check_mode_obj.optString("check_mode")+"',discount_rate = '"+check_mode_obj.optString("discount_rate")+"',rounding_mode = '"+check_mode_obj.optString("rounding_mode")+"',payment_id = "+check_mode_obj.optInt("payment_id")+",channel = '"+check_mode_obj.optString("channel")+"',last_operator = '"+obj.optString("update_operator")+"',last_updatetime = '"+obj.optString("update_time")+"' WHERE id = '"+check_mode_obj.optInt("id")+"' AND store_id = '"+store_id+"' ";
								}else{
									add_or_update_sql="INSERT INTO cc_third_check_mode (tenancy_id,store_id,channel,settlement_type,check_mode,discount_rate,rounding_mode,payment_id,create_operator,create_time) VALUES ('"+tenancyID+"',"+store_id+",'"+check_mode_obj.optString("channel")+"','"+check_mode_obj.optString("settlement_type")+"','"+check_mode_obj.optString("check_mode")+"','"+check_mode_obj.optString("discount_rate")+"','"+check_mode_obj.optString("rounding_mode")+"',"+check_mode_obj.optInt("payment_id")+",'"+obj.optString("update_operator")+"','"+obj.optString("update_time")+"' ) ";
								}
							if(add_or_update_sql.length()>0){
								add_update_third_check_mode_list.add(add_or_update_sql);
								System.out.println(add_or_update_sql);
							}
						}
					}
					if(add_update_third_check_mode_list.size()>0){
						String[] sql_Array = null;
						int size = add_update_third_check_mode_list.size();
						sql_Array = (String[]) add_update_third_check_mode_list.toArray(new String[size]);
						dao.getJdbcTemplate(tenancyID).batchUpdate(sql_Array);
						System.out.println("[结算方式修改数据:]"+add_update_third_check_mode_list.toString());
						logger.info("[第三方结算方式信息复制修改数据：]"+sql_Array);
					}
				}
			}
		
		}
		return flag;
	}

	/**
	 * 删除结算方式
	 */
	@Override
	public Object deleteCheckMode(String tenantId, JSONObject params)throws Exception {
		JSONObject result=new JSONObject();
			try
			{
				String sql="delete from cc_third_check_mode where id="+params.optInt("id");
				dao.execute(tenantId, sql);
				result.put("success", true);
			}
			catch (Exception e)
			{
				e.printStackTrace();
				result.put("success", false);
				result.put("msg", e);
				logger.info("删除菜品类别信息错误：" + result);
			}
			return result;
	}

	
}
