package com.tzx.report.bo.imp.crm;

import com.tzx.report.bo.crm.WaterGroupRechargeQueryService;
import com.tzx.report.common.util.ReportExportUtils;
import com.tzx.report.po.crm.dao.WaterGroupRechargeQueryDao;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class WaterGroupRechargeQueryServiceImpl implements WaterGroupRechargeQueryService{

	@Resource
	private WaterGroupRechargeQueryDao waterGroupRechargeQueryDao;
	
	@Override
	public JSONObject getGroupRechargeQuery(String tenantId,JSONObject condition) throws Exception {
		return waterGroupRechargeQueryDao.getGroupRechargeQuery(tenantId, condition);
	}

	@Override
	public HSSFWorkbook exportData(String attribute, JSONObject p,
			HSSFWorkbook workBook) throws Exception {
		// TODO Auto-generated method stub
		Integer rowNum=1;
		Integer jin=0;
		JSONObject paramData =new JSONObject();
		paramData.put("rowNum", rowNum);
		paramData.put("jin",jin);
		JSONObject findResult= getGroupRechargeQuery(attribute, p);
		List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
		  JSONObject out1Result =null;
				//创建sheet 表格   同时还可以设置名字!  
				  HSSFSheet sheet1=workBook.createSheet("团体充值流水查询");
		
				  String [] listTitleName = {"协议号","团体名称","充值日期","收款机构","充值人","流水单号","充值主帐户","付款方式","付款金额","操作人","操作时间","备注"};
				  String [] dataName ={"contract_no","incorporation_name","business_date","store_name","recharge_person","bill_code","main_recharge","payment_name","local_currency","operator","operate_time","remark"};
				  String [] dataType ={"String","String","String","String","String","String","0.00","String","String","String","String","String"};
					 
				if(list1.size()>0){
					for(JSONObject json1 : list1) {
						// 调用到处方法；
						out1Result =ReportExportUtils.out1(json1,workBook,sheet1,listTitleName,dataName,dataType,paramData);
						paramData.put("rowNum", out1Result.opt("rowNum"));
						paramData.put("jin", out1Result.optInt("jin"));
					}
				}
/*				sheet1.groupRow(1,out1Result.optInt("rowNum"));
				sheet1.setRowSumsBelow(false);
				sheet1.setRowSumsRight(false);
*/				return workBook;
	    }
}
