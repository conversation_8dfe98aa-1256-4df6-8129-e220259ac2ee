package com.tzx.report.po.boh.impl;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.boh.dao.GroupBusinessAnalysisDao;
import net.sf.json.JSONObject;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by gj on 2019-05-30.
 */

@Repository(GroupBusinessAnalysisDao.NAME)
public class GroupBusinessAnalysisDaoImpl implements GroupBusinessAnalysisDao{

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Resource(name = "parameterUtils")
    ParameterUtils parameterUtils;

    @Resource
    ConditionUtils conditionUtils;

    @Override
    public JSONObject find(String tenancyID, JSONObject condition) throws Exception {
        List<JSONObject> list = new ArrayList<JSONObject>();
        List<JSONObject> footerList =new ArrayList<JSONObject>();
        List<JSONObject> structure = new ArrayList<JSONObject>();
        JSONObject result = new JSONObject();
        long total = 0L;
        String begindate = condition.optString("p_date_begin");
        String enddate = condition.optString("p_date_end");
        if(begindate.length()>0 && enddate.length()>0 ) {

            String f_rpt_hxlyyfx01 = null;
            String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.GROUP_BUSINESS_ANALYSIS_01);
            if(!reportSql.equals("")){
                SqlRowSet column = dao.query(tenancyID, reportSql.toString());
                if(column.next()){
                    f_rpt_hxlyyfx01 = column.getString("f_rpt_hxlyyfx01");
                }
            }
            list = this.dao.query4Json(tenancyID, f_rpt_hxlyyfx01);
            total = this.dao.countSql(tenancyID, f_rpt_hxlyyfx01);

            structure = conditionUtils.getSqlStructure(tenancyID, f_rpt_hxlyyfx01);

            String f_rpt_hxlyyfx02 = null;
            reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.GROUP_BUSINESS_ANALYSIS_02);
            if(!reportSql.equals("")){
                SqlRowSet column = dao.query(tenancyID, reportSql.toString());
                if(column.next()){
                    f_rpt_hxlyyfx02 = column.getString("f_rpt_hxlyyfx02");
                }
            }
            footerList = this.dao.query4Json(tenancyID, f_rpt_hxlyyfx02);

            /*
            String reportSql = parameterUtils.parameterAutomaticCompletion(tenancyID, condition, YYTJ01);
            list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition, reportSql.toString()));
            total = this.dao.countSql(tenancyID, reportSql.toString());

            structure = conditionUtils.getSqlStructure(tenancyID,reportSql.toString());

            String reportSqlCount = parameterUtils.parameterAutomaticCompletion(tenancyID, condition,YYTJ00_COUNT);
            footerList = this.dao.query4Json(tenancyID, reportSqlCount.toString());
            */

        }
        int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
        result.put("page", pagenum);
        result.put("total",total);
        result.put("rows", list);
        result.put("footer", footerList);
        result.put("structure", structure);
        return result;
    }

    @Override
    public List<JSONObject> getClassItems(String tenancyID, JSONObject condition) throws Exception {
        List<JSONObject> list = new ArrayList<JSONObject>();
        JSONObject result = new JSONObject();
        String f_rpt_hxlyyfx00 = null;
        String reportSql = parameterUtils.parameterAutomaticCompletionUpgrade(tenancyID, condition, EngineConstantArea.GROUP_BUSINESS_ANALYSIS);
        if(!reportSql.equals("")){
            SqlRowSet column = dao.query(tenancyID, reportSql.toString());
            if(column.next()){
                f_rpt_hxlyyfx00 = column.getString("f_rpt_hxlyyfx00");
            }
        }
        list = this.dao.query4Json(tenancyID, f_rpt_hxlyyfx00);
        return list;
    }
}
