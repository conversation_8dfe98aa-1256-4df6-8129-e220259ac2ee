package com.tzx.boh.bo.imp;

import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.boh.bo.OperationInformationManagementService;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
@Service(OperationInformationManagementService.NAME)
public class OperationInformationManagementServiceImpl implements OperationInformationManagementService
{
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	@SuppressWarnings("unchecked")
	@Override
	public JSONObject loadOperationInformation(String tenancyID, JSONObject condition) throws Exception
	{
		
		StringBuilder sql = new StringBuilder();
		if(!condition.get("organ_code").equals("0")&&!"".equals(condition.get("organ_code"))){
		sql.append("select a.*,b.org_full_name from boh_operate_info A,organ b where b.id=a.store_id");
		sql.append(" and b.organ_code LIKE '"+condition.get("organ_code")+"%' ");
//		sql.append(" and b.id in (select * from get_oids_bycode('"+condition.get("organ_code").toString().trim()+"')) ");
		}else{ 
			sql.append("select a.*,b.org_full_name from boh_operate_info A LEFT JOIN organ b on b.id=a.store_id where 1=1");
			
		}
		if (condition.containsKey("t1") )
		{
			sql.append(" and  a.business_date >= TO_DATE('" + condition.get("t1") + "','YYYY-MM-DD') ");
		}
		if (condition.containsKey("t2") )
		{
			sql.append(" and  a.business_date <= TO_DATE('" + condition.get("t2") + "','YYYY-MM-DD') ");
		}
		
//		if (condition.containsKey("t1") && condition.containsKey("t2")&&!"".equals(condition.get("t1"))&&!"".equals(condition.get("t2")))
//		{
//			sql.append(" and a.business_date between TO_DATE('" + condition.get("t1") + "','YYYY-MM-DD') and TO_DATE('" + condition.get("t2") + "','YYYY-MM-DD') ");
//		}
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		Set<String> keys = condition.keySet();
		for(String s:keys)
		{
			if("tableName".equals(s)||"page".equals(s)||"rows".equals(s)||"sort".equals(s)||"order".equals(s)||"sortName".equals(s))
			{
				continue;
			}
			if(!"t1".equals(s)&&!"t2".equals(s)&&!"organ_code".equals(s)){
				sql.append(" and a."+s+" like '"+condition.optString(s)+"%'");
			}
			
		}
		if(condition.optString("sort")!=null && !"".equals(condition.optString("sort")))
		{
			sql.append(" order by "+condition.optString("sort")+" "+condition.optString("order"));
		}
		else
		{
			sql.append(" order by a.tenancy_id");
		}
		long total = this.dao.countSql(tenancyID, sql.toString());
		List<JSONObject> list = this.dao.query4Json(tenancyID, this.dao.buildPageSql(condition,sql.toString()));
		JSONObject result = new JSONObject();
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result;
	}
	@Override
	public boolean checkUnique(String tenentId, JSONObject param) throws Exception
	{
		try{

			String business_date = param.optString("business_date");
			String oldId = param.optString("id");
			if (Tools.hv(business_date))
			{
				StringBuilder sql = new StringBuilder();
				sql.append("select id from boh_operate_info info where info.business_date= '"+business_date+"' and info.store_id="+param.optString("store_id")+"");
				long total = this.dao.countSql(tenentId, sql.toString());
				List<JSONObject> list = this.dao.query4Json(tenentId, this.dao.buildPageSql(param,sql.toString()));
				String id="";
				if(list.size()>=1){
					  for(JSONObject result : list){
		                	id=result.getString("id");
		                }
				}
              
                if(oldId.equals("")&& total <1){
                	return false;
                }
                if(!id.equals("")&&oldId.equals(id)&& total >=1){
                	return false;
                }
                if(!oldId.equals(id)&& total >=1){
                	return true;
                }
                if(!oldId.equals(id)&& total <1){
                	return false;
                }
			}

			return true;
		}catch(Exception e)
		{
			e.printStackTrace();
			return true;
		}
	}
	

}
