package com.tzx.boh.service.rest;

import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.boh.bo.PettyCashManagementService;
import com.tzx.framework.bo.DataDictionaryService;
import com.tzx.framework.common.util.DateUtil;
/**
 * <AUTHOR>
 */
@Controller("PettyCashManagementContraller")
@RequestMapping("/boh/pettyCashManagementContraller")
public class PettyCashManagementRest
{
	@Resource(name = PettyCashManagementService.NAME)
	private PettyCashManagementService	pettyCashManagementService;

	@Autowired
	private DataDictionaryService	dataDictionaryService;

	/**
	 * 零用金管理列表
	 */
	@RequestMapping(value = "/loadPettyCashManagement")
	public void loadPettyCashManagement(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			if(!obj.containsKey("organ_code")){
				obj.put("organ_code",(String) session.getAttribute("organ_code"));
			}
			if(!obj.containsKey("organ_id")){
				obj.put("organ_id",(String) session.getAttribute("organ_id"));
			}
			obj.put("is_zb",(String) session.getAttribute("organ_code"));
			result = pettyCashManagementService.loadPettyCashManagement((String) session.getAttribute("tenentid"), obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 * 保存零用金信息
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/savePettyCashManagement")
	public void savePettyCashManagement(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "{\"success\": true}";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String,String[]> map = request.getParameterMap();
			for(String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("entry_person", session.getAttribute("employeeName"));
			obj.put("entry_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("tenancy_id",(String) session.getAttribute("tenentid"));
			obj.put("store_id",(String) session.getAttribute("organ_id"));
			obj.put("type","change");
			Object dic = dataDictionaryService.save((String) session.getAttribute("tenentid"), "boh_imprest_change", obj);
			if (dic != null) result = "{\"success\": true, \"id\" : \"" + dic.toString() + "\"}";
		}
		catch (Exception e)
		{
			result = "{\"success\": false, \"msg\" : \"" + e.getMessage() + "\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
}
