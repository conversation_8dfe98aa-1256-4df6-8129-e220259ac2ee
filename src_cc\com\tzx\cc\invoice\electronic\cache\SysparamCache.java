package com.tzx.cc.invoice.electronic.cache;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import common.Logger;
/**
 * 系统参数 缓存
 * <AUTHOR>
 * @since 2016年12月2日18:07:07
 */
public class SysparamCache {
	
    private static final Logger logger  = Logger.getLogger(SysparamCache.class);
	/**
	 * redis存储系统参数的key
	 */
	private static final String key = "electronic_invoice_sys_parameter";
	
	/**
	 * redis存储系统参数的期限
	 */
	private static final int EXPIRE_DAY = 2;
	
	/**
	 * 读取系统参数 机构ID为0的
	 * @param tenancyId
	 * @param paraCode
	 * @return
	 * @throws Exception
	 */
	public static String getSysparam(String tenancyId,String paraCode) throws Exception{
		return getSysparam(tenancyId, 0, paraCode);
	}
	/**
	 * 读取系统参数
	 * @param tenancyId
	 * @param storeId
	 * @param code
	 * @return
	 * @throws Exception 
	 */
	public static String getSysparam(String tenancyId,int storeId,String paraCode) throws Exception{
		/*//组装redis HASH中的key
		StringBuffer sb = new StringBuffer();
		sb.append(tenancyId).append("_").append(storeId).append("_").append(paraCode);
		String key2 = sb.toString();
		//先从redis里面取参数对应的值，若有，直接返回。若没有，去数据库中取，并且存储到redis中
		RedisTemplate<String, Object> redisTemplate = (RedisTemplate<String, Object>) SpringConext.getApplicationContext().getBean("saasRedisTemplate");
		try {
            if(redisTemplate.hasKey(key) && redisTemplate.opsForHash().hasKey(key, key2)) {
            	String value = (String) redisTemplate.opsForHash().get(key, key2);
            	if(StringUtils.isNotBlank(value)) {
            		return value;
            	}
            }
        } catch (Exception e) {
            logger.error("查询系统参数连接reids失败，从数据库里实时查询",e);
        }*/
		
		GenericDao dao = (GenericDao) SpringConext.getApplicationContext().getBean("genericDaoImpl");
		String sql = "select para_value from sys_parameter where valid_state = '1' and (store_id = ? or store_id = 0) and para_code = ? order by store_id desc";
		List<JSONObject> query4Json = dao.query4Json(tenancyId, sql, new Object[]{storeId,paraCode});
		if(query4Json.isEmpty()) {
			return null;
		}
		JSONObject jsonObject = query4Json.get(0);
		String value = jsonObject.optString("para_value");
		/*if(StringUtils.isNotBlank(value)) {
			redisTemplate.opsForHash().put(key, key2, value);
			redisTemplate.expire(key, EXPIRE_DAY, TimeUnit.DAYS);
		}*/
		return value;
	}
}
