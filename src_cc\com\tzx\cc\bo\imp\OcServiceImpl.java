package com.tzx.cc.bo.imp;

import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.cc.base.Constant;
import com.tzx.cc.bo.OcService;
import com.tzx.cc.bo.OrderManagementHqService;
import com.tzx.cc.bo.OrderUpdateManagementService;
import com.tzx.cc.bo.dto.Data;
import com.tzx.cc.common.constant.CcErrorCode;
import com.tzx.cc.common.constant.Oper;
import com.tzx.cc.common.constant.util.CcReqDataUtil;
import com.tzx.cc.common.constant.util.ParamsSignUtil;
import com.tzx.cc.po.springjdbc.dao.OcDao;
import com.tzx.framework.bo.SystemUserService;
import com.tzx.framework.bo.dto.Version;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.PropertiesUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.apkUtil.ApkInfo;
import com.tzx.framework.common.util.apkUtil.ApkUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.weixin.paihao.bo.NewPaihaoService;

@Service(OcService.NAME)
public class OcServiceImpl implements OcService {
	@Resource(name = SystemUserService.NAME)
	private SystemUserService systemUserService;
	
	@Resource(name = OrderManagementHqService.NAME)
	private OrderManagementHqService orderManagementService;
	
	@Resource(name = OrderUpdateManagementService.NAME)
	private OrderUpdateManagementService orderUpdateManagementService;
	
	@Resource(name = NewPaihaoService.NAME)
	private NewPaihaoService newPaihaoService;
	
	
	@Resource(name = "genericDaoImpl")
	private GenericDao dao;

	@Resource(name = OcDao.NAME)
	private OcDao ocDao;
	private static final Logger logger = Logger.getLogger(OcServiceImpl.class);

	@Override
	public void oc_login(Data param) throws SystemException, Exception {

		String tenantId = param.getTenancy_id();
		Oper oper = param.getOper();
		StringBuilder sb = new StringBuilder();
		JSONObject jb = null;
		Integer storeId = param.getStore_id();
		if (param.getData() != null) {
			jb = new JSONObject();
			Map<String, Object> maps = (Map<String, Object>) param.getData()
					.get(0);
			Set<String> set = maps.keySet();
			for (String key : set) {
				jb.put(key, maps.get(key));
			}
			jb.put("tenancy_id", param.getTenancy_id());
			jb.put("last_operator", Constant.LAST_OPERATOR);
			jb.put("last_updatetime",
					DateUtil.format(new Timestamp(System.currentTimeMillis())));
		} else {
			param.setCode(Constant.CODE_DATA_NULL);
			param.setMsg(Constant.CODE_DATA_NULL_MSG);
			param.setSuccess(false);
			return;
		}
		switch (oper) {
		case check:
			sb.setLength(0);
			try {
				String loginUserName = jb.optString("user_code");
				String password = jb.optString("password");
				JSONObject longinJsonObject = new JSONObject();
				longinJsonObject.put("tenentid", tenantId);
				longinJsonObject.put("loginUserName", loginUserName);
				longinJsonObject.put("password", password);

				JSONObject userInfoJosonObje = systemUserService.chekUserLogin(
						tenantId, longinJsonObject);

				if (loginUserName.equals("system")
						&& com.tzx.framework.common.constant.Constant
								.getSystemMap().get("system_password")
								.equals(password)) {
					param.setCode(0);
					param.setMsg("成功");
				} else {
					if (userInfoJosonObje != null) {
						param.setSuccess(true);
						param.setCode(0);
						param.setMsg("成功");

					} else {
						param.setSuccess(false);
						param.setCode(2);
						param.setMsg("用户名或密码错误，请重新输入！");
						return;
					}

				}
				if (userInfoJosonObje.optString("operating_status").equals(
						"suspension_business")) {
					param.setSuccess(false);
					param.setCode(2);
					param.setMsg("该餐厅暂停营业，请联系系统管理员！");
					return;

				} else if (userInfoJosonObje.optString("operating_status")
						.equals("closed")) {
					param.setSuccess(false);
					param.setCode(2);
					param.setMsg("该餐厅已经关店，请联系系统管理员！");
					return;
				}

				if (!userInfoJosonObje.optString("tenant_state").equals("1")) {
					param.setSuccess(false);
					param.setCode(2);
					param.setMsg("机构未审核，请联系系统管理员！");
					return;

				}
				Map<String, Object> obj1 = new HashMap<String, Object>();
				obj1.put("store_id", userInfoJosonObje.optInt("store_id"));
				List<Map<String, Object>> array1 = new ArrayList<Map<String, Object>>();
				array1.add(obj1);
				param.setData(array1);
				param.setStore_id(userInfoJosonObje.optInt("store_id"));
				jb.put("store_id", userInfoJosonObje.optInt("store_id"));
				newPaihaoService.registerOc(tenantId, jb);
			} catch (Exception e) {
				param.setCode(2);
				param.setMsg("商户号错误，请重新输入！");
				e.printStackTrace();
				
			}
		default:
			break;
		}

	}

	@Override
	public void syncBaseData(Data param) throws SystemException {
		Data data = new Data();
		// 没用oper
		List<JSONObject> jsons = null;

		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = CcReqDataUtil.getDataMap(param);


		String dataType = ParamUtil.getStringValue(map, "data_type", true,
				PosErrorCode.NOT_NULL_DATA_TYPE);
		String channel="MD01";
		String day_count="";
		if(!"business_data_list".equals(dataType)&&!"organ_list".equals(dataType)){
			 channel = ParamUtil.getStringValue(map, "channel", true,
					PosErrorCode.NOT_NULL_CHANNEL).toUpperCase();
		}
		if(channel.equals("MD01")&&("business_data_list".equals(dataType)||"organ_list".equals(dataType))){
		    	String sign=ParamsSignUtil.getSignPb(param);  
		    	if(!param.getSign().toString().equals(sign)){
					param.setCode(Constant.CODE_PARAM_FAILURE);
					param.setMsg("签名验证错误");
					param.setSuccess(false);
					return;
		    	}
		     }
		if("business_data_list".equals(dataType)){
			day_count=ParamUtil.getStringValue(map, "day_count", true,
					PosErrorCode.DAY_COUNT);
			if(Tools.isNullOrEmpty(day_count)){
				param.setCode(Constant.CODE_PARAM_FAILURE);
				param.setMsg("营业日期不能为空");
				param.setSuccess(false);
				return;
			}
			
		}
		
		try {
			switch (dataType) {

			case "organ_list":
				// 机构信息
				jsons = ocDao.getOrganInfoByOrganId(tenantId, organId);
				break;
			case "business_data_list":
				// 获取营业信息
				JSONObject query=new JSONObject();
				query.put("day_count", day_count);
				query.put("store_id", organId);
				jsons = ocDao.getBusinessInfo(tenantId, query);
				break;
			case "cancle_reason_list":
				// 取消原因
			/*	List<Combotree> list = orderManagementService.getCancleComplaintTree(tenantId, "hq_unusual_reason", "father_id", "reason_name", "unusual_type", "QX01", "reason_code", organId.toString(), cancle_reason_obj);
			    JSONArray jsonArray = JSONArray.fromObject(list);
			    jsons = JSONArray.toList(jsonArray, JSONObject.class);*/
				jsons = ocDao.getCancle_reason_list(tenantId, organId);
				break;
			case "channel_list":
				//渠道信息
			/*	List<Combotree> list = orderManagementService.getCancleComplaintTree(tenantId, "hq_unusual_reason", "father_id", "reason_name", "unusual_type", "QX01", "reason_code", organId.toString(), cancle_reason_obj);
			    JSONArray jsonArray = JSONArray.fromObject(list);
			    jsons = JSONArray.toList(jsonArray, JSONObject.class);*/
				jsons = ocDao.getChannel_list(tenantId, organId);
				break;	
			case "district_list":
				// 商圈信息
				jsons = ocDao.getDistrictByOrganId(tenantId, organId);
				break;
			case "taste_list":
				// 口味备注
				jsons = ocDao.getItemTastes(tenantId, organId);
				break;
			case "itemclass_list":
				// 菜品类别
				jsons = ocDao
						.getItemClassByTenantId(tenantId, organId, channel);
				break;
			case "item_list":
				// 菜品
				data = ocDao.getDishByTenantId(tenantId, organId, channel,
						param.getPagination());
				param.setPagination(data.getPagination());
				break;
			case "itemunit_list":
				// 规格
				data = ocDao.getItemUnitsByTenantId(tenantId, organId, channel,
						param.getPagination());
				param.setPagination(data.getPagination());
				break;
				//套餐明细
			case "combo_details":
				data = ocDao.getItemComboDetailsByOrganId(tenantId, organId,
						channel,param.getPagination());
				param.setPagination(data.getPagination());
				break;
				//项目组---没有用
			case "item_group":
				data = ocDao.getItemGroup(tenantId, organId,param.getPagination());
				param.setPagination(data.getPagination());
				break;
				//项目组明细
			case "item_group_details":
				data = ocDao.getItemGroupDetailsByOrganId(tenantId, organId,
						channel,param.getPagination());
				param.setPagination(data.getPagination());
				break;
			//以上为订单外卖用到的接口
			case "tables_list":
				// 桌位信息
				jsons = ocDao.getTablesByTenantIdAndOrganId(tenantId, organId);
				break;
			case "table_property":
				// 餐位类型
				jsons = ocDao.getTablePropertyByOrganId(tenantId, organId);
				break;
			case "business_area":
				// 营业区域
				jsons = ocDao.getBussinessArea(tenantId, organId);
				break;
			case "duty_order":
				// 班次
				jsons = ocDao.getDutyOrderByOrganId(tenantId, organId);
				break;
			case "service_type":
				// 服务费种
				jsons = ocDao.getServiceFeeTypeByOrganId(tenantId, organId);
				break;
				
			case "timeprice":
				jsons = ocDao.getTimePriceByOrganId(tenantId, organId, channel);
				break;
			case "timeprice_item":
				jsons = ocDao.getTimePriceItemByOrganId(tenantId, organId,
						channel);
				break;
			case "method":
				// 做法
				jsons = ocDao.getItemMethodsByTenantId(tenantId, organId);
				break;
			case "reason":
				// 退菜、奉送、优惠、恢复账单、免单原因
				jsons = ocDao.getUnusualReasonByTenantId(tenantId, organId);
				break;
			case "discountcase":
				// 折扣方案
				jsons = ocDao.getDiscountCaseByTenantId(tenantId, organId);
				break;
			case "discount_detail":
				// 折扣方案明细
				jsons = ocDao.getDiscountCaseDetailsByTenantId(tenantId,
						organId);
				break;
			case "payment_way":
				// 付款方式
				jsons = ocDao.getPaymentWayByOrganId(tenantId, organId);
				break;
			case "user":
				jsons = ocDao.getUsersByTenantId(tenantId, organId);
				break;
			case "sys_parameter":
				jsons = ocDao.getSysParameter(tenantId, organId);
				break;
			case "crm_incorporation_info":
				jsons = ocDao.getCrmInCorporations(tenantId, organId);
				break;
			case "crm_incorporation_person":
				jsons = ocDao.getCrmInCorporationPersons(tenantId, organId);
				break;
			case "devices":
				jsons = ocDao.getDevicesByOrgan(tenantId, organId);
				break;
			case "crm_card_class":
				jsons = ocDao.getCrmCardClassByOrganId(tenantId, organId);
				break;
			default:
				throw new SystemException(PosErrorCode.NOT_EXISTS_SYNC_DATATYPE);
			}
			param.setCode(Constant.CODE_SUCCESS);
			param.setMsg(Constant.SYNC_DATA_SUCCESS);
			if (data.getData() != null) {
				param.setData(data.getData());
			} else {
				param.setData(jsons);
			}
		} catch (SystemException se) {
			logger.info("同步数据：" + ExceptionMessage.getExceptionMessage(se));
			new SystemException(PosErrorCode.PARAM_ERROR);
		} catch (Exception e) {
			logger.info("sync:" + ExceptionMessage.getExceptionMessage(e));
			param.setCode(Constant.CODE_INNER_EXCEPTION);
			param.setMsg(Constant.SYNC_DATA_FAILURE);
			e.printStackTrace();
		}
	}

	@Override
	public void findCustomerinfo(Data param) throws SystemException {
		Data data = new Data();
		// 没用oper
		List<JSONObject> jsons = null;

		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = CcReqDataUtil.getDataMap(param);
		JSONObject condition_obj = new JSONObject();
		Oper oper = param.getOper();
		String mobil = ParamUtil.getStringValue(map, "mobil", true,
				CcErrorCode.NOT_NULL_MOBIL);
	/*	String dataType = ParamUtil.getStringValue(map, "data_type", true,
				PosErrorCode.NOT_NULL_DATA_TYPE);*/

		try {
			switch (oper) {

			case find:
				// 获取会员基本信息
				condition_obj.put("mobil", map.get("mobil"));
				jsons = ocDao.getCustomerInfo(tenantId, condition_obj);
				break;
			
			default:
				throw new SystemException(PosErrorCode.NOT_EXISTS_SYNC_DATATYPE);
			}
			param.setCode(Constant.CODE_SUCCESS);
			param.setMsg(Constant.SYNC_DATA_SUCCESS);
			if (data.getData() != null) {
				param.setData(data.getData());
			} else {
				param.setData(jsons);
			}
		} catch (SystemException se) {
			logger.info("同步数据：" + ExceptionMessage.getExceptionMessage(se));
			new SystemException(PosErrorCode.PARAM_ERROR);
		} catch (Exception e) {
			logger.info("sync:" + ExceptionMessage.getExceptionMessage(e));
			param.setCode(Constant.CODE_INNER_EXCEPTION);
			param.setMsg(Constant.SYNC_DATA_FAILURE);
			e.printStackTrace();
		}
	}

	@Override
	public void customerAddress(Data param) throws SystemException, Exception {
		String tenantId = param.getTenancy_id();
		Oper oper = param.getOper();
		StringBuilder sb = new StringBuilder();
		JSONObject jb = null;
		Integer storeId = param.getStore_id();
		if (param.getData() != null) {
			jb = new JSONObject();
			Map<String, Object> maps = (Map<String, Object>) param.getData().get(0);
			Set<String> set = maps.keySet();
			for (String key : set) {
				jb.put(key, maps.get(key));
			}
			jb.put("tenancy_id", param.getTenancy_id());
			jb.put("last_operator", Constant.LAST_OPERATOR);
			jb.put("last_updatetime",DateUtil.format(new Timestamp(System.currentTimeMillis())));
		} else {
			param.setCode(Constant.CODE_DATA_NULL);
			param.setMsg(Constant.CODE_DATA_NULL_MSG);
			param.setSuccess(false);
			return;
		}
		switch (oper) {
		case add:
			sb.setLength(0);
			try {
				ocDao.customerAddress(tenantId, jb);
			} catch (Exception e) {
				e.printStackTrace();
				param.setCode(2);
				param.setMsg("新增失败");
			}
		break;
		case update:
			sb.setLength(0);
			try {
				ocDao.customerAddress(tenantId, jb);
			} catch (Exception e) {
				e.printStackTrace();
				param.setCode(2);
				param.setMsg("修改失败");
			}
		break;
		default:
			break;
		}

	}

	@Override
	public void orderInfo(Data param) throws SystemException, Exception {
		String tenantId = param.getTenancy_id();
		Oper oper = param.getOper();
		StringBuilder sb = new StringBuilder();
		JSONObject jb = null;
		Integer storeId = param.getStore_id();
		if (param.getData() != null) {
			jb = new JSONObject();
			Map<String, Object> maps = (Map<String, Object>) param.getData().get(0);
			Set<String> set = maps.keySet();
			for (String key : set) {
				jb.put(key, maps.get(key));
			}
			jb.put("tenancy_id", param.getTenancy_id());
			jb.put("last_operator", Constant.LAST_OPERATOR);
			jb.put("last_updatetime",DateUtil.format(new Timestamp(System.currentTimeMillis())));
		} else {
			param.setCode(Constant.CODE_DATA_NULL);
			param.setMsg(Constant.CODE_DATA_NULL_MSG);
			param.setSuccess(false);
			return;
		}
		switch (oper) {
		case add:
			sb.setLength(0);
			try {
				orderUpdateManagementService.orderSave(param);
			} catch (Exception e) {
				e.printStackTrace();
				param.setCode(2);
				param.setMsg("新增失败");
			}
		break;
		case update:
			sb.setLength(0);
			try {
				orderManagementService.orderCancel(tenantId, jb);
			} catch (Exception e) {
				e.printStackTrace();
				param.setCode(2);
				param.setMsg("取消订单失败");
			}
		break;
		case find:
			sb.setLength(0);
			try {
				jb.put("store_id", param.getStore_id());
				// 查询订单信息
				ocDao.getOrderingList(tenantId,jb,
						param.getPagination(),param);
				break;
			} catch (Exception e) {
				e.printStackTrace();
			}
		break;
		default:
			break;
		}

	}
	
	@Override
	public void orderTotal(Data param) throws SystemException, Exception {
		String tenantId = param.getTenancy_id();
		Oper oper = param.getOper();
		StringBuilder sb = new StringBuilder();
		JSONObject jb = null;
		Integer storeId = param.getStore_id();
		if (param.getData() != null) {
			jb = new JSONObject();
			Map<String, Object> maps = (Map<String, Object>) param.getData().get(0);
			Set<String> set = maps.keySet();
			for (String key : set) {
				jb.put(key, maps.get(key));
			}
			jb.put("tenancy_id", param.getTenancy_id());
			jb.put("last_operator", Constant.LAST_OPERATOR);
			jb.put("last_updatetime",DateUtil.format(new Timestamp(System.currentTimeMillis())));
		} else {
			param.setCode(Constant.CODE_DATA_NULL);
			param.setMsg(Constant.CODE_DATA_NULL_MSG);
			param.setSuccess(false);
			return;
		}
		switch (oper) {
		case find:
			sb.setLength(0);
			try {
				System.out.println("统计订单总数传入"+jb.toString());
				jb.put("store_id", storeId);
				// 查询订单信息
				List<JSONObject> result_list=new ArrayList<JSONObject>();
				JSONObject  result_obj=ocDao.orderTotal(tenantId,jb);
				result_list.add(result_obj);	
				param.setData(result_list);
				break;
			} catch (Exception e) {
				e.printStackTrace();
				param.setCode(2);
				param.setMsg("修改失败");
			}
		break;
		/*case find:
			sb.setLength(0);
			try {
				// 查询订单信息
				ocDao.getOrderingList(tenantId,jb,
						param.getPagination());
				break;
			} catch (Exception e) {
				e.printStackTrace();
				param.setCode(2);
				param.setMsg("修改失败");
			}
		break;*/
		default:
			break;
		}

	}

	@Override
	public List<JSONObject> basicVersion(Data param) throws Exception {
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		List<Map<String, Object>> maps =CcReqDataUtil.getDataList(param);

		return ocDao.basicVersion(tenantId, organId, maps);
	}

	@Override
	public List<Version> findAppVersion(HttpServletRequest request,
			List<?> param) throws Exception {
		Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();

		Version v = new Version();
		v.setIsnewest(false);

		String result = request.getHeader("User-Agent");

		if (null != result && result.toLowerCase().contains("android"))
		{
			String apkPath = System.getProperty("contextPath") + systemMap.get("app_path").toString() + systemMap.get("app_name").toString();

			ApkUtil apkUtil = new ApkUtil();
			apkUtil.setmAaptPath(System.getProperty("contextPath") + "WEB-INF/lib/aapt");

			ApkInfo apkInfo = apkUtil.getApkInfo(apkPath);
			v.setVersionno(apkInfo.getVersionCode());
			v.setUrl(request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/" + systemMap.get("app_path").toString() + systemMap.get("app_name").toString());

			if (param != null && !param.isEmpty())
			{
				JSONObject paraJson = JSONObject.fromObject(param.get(0));

				if (v.getVersionno().equals(paraJson.optString("apkno")))
				{
					v.setIsnewest(true);
				}
			}
		}
		else
		{
			String filePath = System.getProperty("catalina.base") + File.separator + "system.properties";
			logger.info("***************************************************************" + filePath);

			v.setVersionno(PropertiesUtil.readProperties(filePath).getProperty("version", ""));
			v.setUrl(request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/download/TZXPOS.zip");

			if (param != null && !param.isEmpty())
			{
				JSONObject paraJson = JSONObject.fromObject(param.get(0));

				if (v.getVersionno().equals(paraJson.optString("apkno")))
				{
					v.setIsnewest(true);
				}
			}
		}

		List<Version> list = new ArrayList<Version>();
		list.add(v);
		return list;
	}
	
}
