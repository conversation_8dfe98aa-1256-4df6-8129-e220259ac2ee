package com.tzx.report.bo.imp.hq;

import com.tzx.report.bo.hq.BusinessHoursComparedSameMonthLastYearService;
import com.tzx.report.bo.hq.DishesClassificationClassesQueryService;
import com.tzx.report.po.hq.dao.BusinessHoursComparedSameMonthLastYearDao;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service(BusinessHoursComparedSameMonthLastYearService.NAME)
public class BusinessHoursComparedSameMonthLastYearServiceImpl implements BusinessHoursComparedSameMonthLastYearService
{
	
	@Resource
	private BusinessHoursComparedSameMonthLastYearDao businessHoursComparedSameMonthLastYearDao;

	@Override
	public JSONObject getBusinessHoursComparedSameMonthLastYear(String tenancyID, JSONObject condition) throws Exception {
		return businessHoursComparedSameMonthLastYearDao.getBusinessHoursComparedSameMonthLastYear(tenancyID, condition);
	}
 
}
