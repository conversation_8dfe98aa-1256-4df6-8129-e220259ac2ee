package com.tzx.report.bo.imp.commonreplace;

import com.tzx.framework.common.util.SpringConext;
import com.tzx.report.bo.commonreplace.ExportReportService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ExportUtils;
import com.tzx.report.common.util.ReportExportUtils;
import com.tzx.report.po.boh.dao.AuxiliaryNumberReportDao;
import com.tzx.report.po.commonreplace.ExportReportDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service(ExportReportService.NAME)
public class ExportReportServiceImpl implements  ExportReportService{

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource(name = ExportReportDao.NAME)
    private ExportReportDao exportReportDao;

    @Resource
    ConditionUtils conditionUtils;

    static final int sheetNum = 20000;

    @Override
    public Object[] exportDataAll(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception {
        HSSFSheet sheet1 = null;
        File f = null;
        String exportName = json.optString("exportName");//名称
        JSONObject paramData = null;
        //如果为第一层不进行创建sheet页
        if(json.optInt("hierarchy")>1){
            sheet1=workBook.createSheet(exportName);
            JSONObject returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
            Integer rowNum= returnTitleJson.optInt("rowNum");
            Integer jin=json.optInt("jin");
            paramData=exportContrastline(rowNum,jin);
        }
        StringBuilder sbdate = new StringBuilder();
        StringBuilder sbdatekey = new StringBuilder();
        Integer stratIndex =0;
        Integer stratIndex2 =0;
        Integer stratIndex3 =0;
        JSONObject out1Result2 =null;
        JSONObject out1Result =null;
        JSONObject out1Result3 =null;
        List<JSONObject> objs = new ArrayList<JSONObject>();
        List<JSONObject> data = null;
        List<JSONObject> data2 = null;
        List<JSONObject> data3 = null;
        String parametertype = "";
        String parametertype1 = "";
        String parametertype2 = "";

        if(json.containsKey("rowcolumns") && json.getJSONObject("rowcolumns")!=null){
            JSONObject obj = json.getJSONObject("rowcolumns");
            JSONArray list=obj.getJSONArray("key");
            for(int i=0;i<list.size();i++){
                JSONObject job = new JSONObject();
                JSONObject info=list.getJSONObject(i);
                job.put("field", info.getString("field"));
                objs.add(job);
            }

            if(json.optInt("hierarchy")==3){
                data =  getMethodOne(tenancyID,json);  //第一层返回集合
                if(data.size()>0){
                    if(json.containsKey("hierarchytype") && json.optInt("hierarchytype")==1){   //是否存在第二层
                        json.put("hierarchytype", 2);
                        if(json.containsKey("p_report_type")){
                            json.put("p_report_type", "L"+json.optInt("selecttype")+"J"+json.optInt("hierarchytype"));
                        }
                    }
                    if(json.containsKey("exportdataexpr") && !json.optString("exportdataexpr").equals("")){    //第一层查询字段
                        json.put(json.optString("key"), json.optString("exportdataexpr"));
                    }
                    if(json.containsKey("sortKey1") && !json.optString("sortKey1").equals("")){      //第二层排序
                        //前台传入是否需要后台进行数据排序
                        json.put("sortKey1",json.optString("sortKey1"));
                    }
                }
                data2 =  getMethodTwo(tenancyID,json,stratIndex2);  //第二层返回集合
                if(data2.size()>0){
                    sbdate.delete( 0,sbdate.length());
                    sbdatekey.delete( 0,sbdatekey.length());
                    if(json.containsKey("hierarchytype") && json.optInt("hierarchytype") ==2){
                        json.put("hierarchytype", 3);

                        /**
                         * 用函数导出时需要传入参数p_report_level_two代表函数sql使用层级
                         * selecttype为查询方式
                         * hierarchytype为当前查询得层级
                         */
                        if(json.containsKey("p_report_type")){
                            json.put("p_report_type", "L"+json.optInt("selecttype")+"J"+json.optInt("hierarchytype"));
                        }
                    }
                    for(int i=0;i<data2.size();i++){
                        String key1 = json.optString("key1");
                        if(data2.get(i).containsKey(key1)){
                            if(!data2.get(i).optString(key1).equals("")&&!data2.get(i).optString(key1).equals(null)){
                                sbdatekey.append(","+data2.get(i).optString(key1));
                            }
                        }
                        if(!data2.get(i).optString("exportdataexpr1").equals("")){
                            sbdate.append(","+data2.get(i).optString("exportdataexpr1"));
                        }
                    }

                    if(sbdatekey.length()>0)
                    {
                        if(json.optString("parametertype1").equals("String")){   //第二层查询是否为字符串
                            sbdatekey.delete(0,1);
                            parametertype1 = ConditionUtils.spilt(sbdatekey.toString());
                            json.put(json.optString("key1"), parametertype1);
                        }else{
                            sbdatekey.delete(0,1);
                            json.put(json.optString("key1"), sbdatekey.toString());
                        }
                    }else{
                        json.put(json.optString("key1"), sbdatekey.toString());
                    }

                    if(sbdate.length()>0)
                    {
                        if(json.optString("parametertype").equals("String")){   //第一层查询是否为字符串
                            sbdate.delete(0,1);
                            parametertype = ConditionUtils.spilt(sbdate.toString());
                            json.put("exportdataexpr1", parametertype);
                        }else{
                            sbdate.delete(0,1);
                            json.put("exportdataexpr1", sbdate.toString());
                        }
                    }else{
                        json.put("exportdataexpr1", sbdate.toString());
                    }
                    if(json.containsKey("sortKey2") && !json.optString("sortKey2").equals("")){
                        //前台传入是否需要后台进行数据排序
                        json.put("sortKey2",json.optString("sortKey2"));
                    }
                }
                data3 =  getMethodThree(tenancyID,json,stratIndex3);  //第三层返回集合
                if(data3.size()>0){
                    sbdate.delete( 0,sbdate.length());   //清空sbdate     (存放第一层数据)
                    sbdatekey.delete( 0,sbdatekey.length()); //清空sbdatekey(存放第二层数据)
                    for(int i=0;i<data3.size();i++){
                        String key2 = json.optString("key1");  //获取第二层导出字段  举例 report_date
                        if(data3.get(i).containsKey(key2)){
                            if(!data3.get(i).optString(key2).equals("")&&!data3.get(i).optString(key2).equals(null)){
                                sbdatekey.append(","+data3.get(i).optString(key2));
                            }
                        }
                        if(!data3.get(i).optString("exportdataexpr2").equals("")){
                            sbdate.append(","+data3.get(i).optString("exportdataexpr2"));
                        }
                    }
				    	/*if(sbdate.length()>0)
						{
				    		if(json.optString("parametertype2").equals("String")){   //第三层查询是否为字符串
				    			sbdate.delete(0,1);
				    			parametertype2 = ConditionUtils.spilt(sbdate.toString());
								json.put("exportdataexpr2", parametertype2);
				    		}else{
				    			sbdate.delete(0,1);
								json.put("exportdataexpr2", sbdate.toString());
				    		}
						}else{
							json.put("exportdataexpr2", sbdate.toString());
						}*/
                    if(sbdatekey.length()>0)
                    {
                        if(json.optString("parametertype1").equals("String")){   //第二层查询是否为字符串
                            sbdatekey.delete(0,1);
                            parametertype1 = ConditionUtils.spilt(sbdatekey.toString());
                            json.put(json.optString("key2"), parametertype1);
                        }else{
                            sbdatekey.delete(0,1);
                            json.put(json.optString("key2"), sbdatekey.toString());
                        }
                    }else{
                        json.put(json.optString("key2"), sbdatekey.toString());
                    }

                    if(sbdate.length()>0)
                    {
                        if(json.optString("parametertype").equals("String")){   //第一层查询是否为字符串
                            sbdate.delete(0,1);
                            parametertype = ConditionUtils.spilt(sbdate.toString());
                            json.put("exportdataexpr2", parametertype);
                        }else{
                            sbdate.delete(0,1);
                            json.put("exportdataexpr2", sbdate.toString());
                        }
                    }else{
                        json.put("exportdataexpr2", sbdate.toString());
                    }
                }
            }else if(json.optInt("hierarchy")==2){
                data =  getMethodOne(tenancyID,json);  //第一层返回集合
                if(data.size()>0){
                    if(json.containsKey("hierarchytype") && json.optInt("hierarchytype")==1){
                        json.put("hierarchytype", 2);
                        if(json.containsKey("p_report_type")){
                            json.put("p_report_type", "L"+json.optInt("selecttype")+"J"+json.optInt("hierarchytype"));
                        }
                    }
                    if(json.containsKey("exportdataexpr") && !json.optString("exportdataexpr").equals("")){
                        json.put(json.optString("key"), json.optString("exportdataexpr"));
                    }
                    if(json.containsKey("sortKey1") && !json.optString("sortKey1").equals("")){
                        //前台传入是否需要后台进行数据排序
                        json.put("sort1",json.optString("sortKey1"));
                    }
                }
                data2 =  getMethodTwo(tenancyID,json,stratIndex2);  //第二层返回集合
            }else{
                data =  getMethodOne(tenancyID,json);  //第一层返回集合
            }
            if(json.containsKey("TenThousandExcle")) {
                paramData.put("TenThousandExcle", json.optString("TenThousandExcle"));
                paramData.put("maxExcleNum", json.optString("maxExcleNum") ==  null ? 100000 : json.optInt("maxExcleNum"));
            }

            if(data.size()>sheetNum){
                f = getinfo(exportName,json,data, data2, data3, sheet1, workBook, stratIndex, stratIndex2, stratIndex3, out1Result, out1Result2, out1Result3, objs, paramData);
            }else{
                f = execlrp(exportName,json,data, data2, data3, sheet1, workBook, stratIndex, stratIndex2, stratIndex3, out1Result, out1Result2, out1Result3, objs, paramData);
            }
        }
        if(json.optInt("hierarchy")>1){
            sheet1.setRowSumsBelow(false);
            sheet1.setRowSumsRight(false);
        }
        Object[] o = new Object[2];
        o[0] = workBook;
        o[1] = f;
        return o;
    }

    public  File getinfo(String exportName,JSONObject json,List<JSONObject> data,List<JSONObject> data2,List<JSONObject> data3,HSSFSheet sheet1, HSSFWorkbook workBook,Integer stratIndex,Integer stratIndex2,Integer stratIndex3,JSONObject out1Result,JSONObject out1Result2,JSONObject out1Result3,List<JSONObject> objs,JSONObject paramData) throws  Exception{
        List<File> files = new ArrayList<File>();
        //服务器下载文件地址
        File gue = fileCatalogue();

        HSSFWorkbook workBooks = null;
        ExecutorService executor = Executors.newCachedThreadPool();

        if(data.size()>0){
            int num = 20;
            int num1 = data.size();
            int num2 = data.size()%20 == 0 ? data.size()/20 : data.size()/20 +1 ;
            JSONObject structrueJo = data.get(data.size()-1);
            if(num1>=20){
                for (int i = 1 ; i <= num2 ; i++){
                    List<JSONObject> row = null;
                    workBooks = new HSSFWorkbook();
                    if(i==num2){
                        row = data.subList(num*(i-1),num1);
                    }else{
                        row = data.subList(num*(i-1),num*i);
                    }
                    row.add(structrueJo);
                    File file = executor.submit(new ExeclrpThread(exportName,json,row, data2, data3, sheet1, workBook, stratIndex, stratIndex2, stratIndex3, out1Result, out1Result2, out1Result3, objs, paramData,gue)).get();
                    files.add(file);
                    row.remove(structrueJo);
                }

            }
        }

//        List<File> listFile = new ArrayList<File>();
//        try{
//            if (files.size() > 0) {
//                for (Future a : files) {
//                    File jo = (File)a.get();
//                    listFile.add(jo);
//                }
//            }
//        }catch (Exception e){
//            e.printStackTrace();
//        }

        executor.shutdown();


        if(files!=null&&files.size()>0){

            List<File> listFile = new ArrayList<File>();
            File file = files.get(files.size()-1);
            listFile.add(file);
            File zip = new File(gue+"/"+exportName+System.currentTimeMillis()+".zip");
            zipFiles(listFile.toArray(new File[listFile.size()]),zip);
            return zip;

        }
        return null;
    }

    public JSONObject exportContrastline(int rowNum,int jin){
        JSONObject paramData =new JSONObject();
        paramData.put("rowNum", rowNum);
        paramData.put("jin",jin);
        paramData.put("strtIndex",rowNum);
        return paramData;
    }


    public List<JSONObject>  getMethodOne(String tenancyID,JSONObject json) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
        List<JSONObject> data1= new ArrayList<JSONObject>();
        List<JSONObject> footerList = null;
        JSONObject findResult= getInvokeMethod(tenancyID,json);
        //明细数据
        List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");

        //数据类型
        List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");

        //合计
        if(json.containsKey("total_line") && json.optInt("total_line")==1){
            footerList =(List<JSONObject>) findResult.opt("footer");
        }
        if(list1.size()>0){
            String key = json.optString("key");
            if(!key.equals("")){
                for(JSONObject json1 : list1) {
                    JSONObject jsona = new JSONObject();
                    //前台传入需要对比的字段
                    jsona.put("exportdataexpr",json1.optString(key));

                    jsona.put("json1", json1);  //获取第一层的选中数据

                    data1.add(jsona);
                }
                JSONObject structures = new JSONObject();
                structures.put("structure", structure);
                structures.put("footerList", footerList);
                data1.add(structures);
            }else{
                return data1;
            }
        }
        return data1;
    }


    public List<JSONObject>  getMethodTwo(String tenancyID,JSONObject json,int stratIndex2) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
        List<JSONObject> data2= new ArrayList<JSONObject>();
        JSONObject findResult= getInvokeMethod(tenancyID,json);
        @SuppressWarnings("unchecked")
        List<JSONObject> list2 =(List<JSONObject>) findResult.opt("rows");
        @SuppressWarnings("unchecked")
        List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");
        if(list2.size()>0){
            String key = json.optString("key");   //和第一级需要对比的字段  举例  item_num
            String key1 = json.optString("key1"); //第三及需要对比的字段  举例 report_date
            if(!key.equals("")){
                for(JSONObject json2 : list2) {
                    JSONObject jsonb = new JSONObject();
                    //前台传入需要对比的字段  第二层
                    jsonb.put("exportdataexpr1",json2.optString(key));   //item_num 的数据存入exportdataexpr1
                    jsonb.put(key1,json2.optString(key1));    //report_date 的数据存入key1
                    jsonb.put("stratIndex2", stratIndex2);
                    jsonb.put("json2", json2);
                    data2.add(jsonb);
                }
                JSONObject structures = new JSONObject();
                structures.put("structure", structure);
                data2.add(structures);
            }else{
                return data2;
            }
        }
        return data2;
    }

    public List<JSONObject>  getMethodThree(String tenancyID,JSONObject json,int stratIndex3) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
        List<JSONObject> data3= new ArrayList<JSONObject>();
        JSONObject findResult= getInvokeMethod(tenancyID,json);
        @SuppressWarnings("unchecked")
        List<JSONObject> list3 =(List<JSONObject>) findResult.opt("rows");
        @SuppressWarnings("unchecked")
        List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");
        if(list3.size()>0){
            String key1 = json.optString("key");  //需要和第一级对比   举例   item_num
            String key2 = json.optString("key1");  //需要和第二层对比  举例    report_date
            //String key1 = json.optString("key2");//如果存在第四层那么获取到key2进行第四层对比
            if(!key2.equals("") && !key1.equals("")){
                for(JSONObject json3 : list3) {
                    JSONObject jsonc = new JSONObject();
                    //前台传入需要对比的字段  第二层
                    jsonc.put(key2, json3.optString(key2));  //report_date的数据 存入key2
                    jsonc.put("exportdataexpr2", json3.optString(key1)); //item_num 的数据存入exportdataexpr2
                    //jsonc.put(key1,json3.optString(key1));
                    jsonc.put("stratIndex3", stratIndex3);
                    jsonc.put("json3", json3);
                    data3.add(jsonc);
                }
                JSONObject structures = new JSONObject();
                structures.put("structure", structure);
                data3.add(structures);
            }else{
                return data3;
            }
        }
        return data3;
    }


    //反射执行java方法
    public JSONObject getInvokeMethod(String tenancyID,JSONObject json) throws ClassNotFoundException, InstantiationException,
            IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
        String className = json.optString("className"); //类路径
        String methodName= json.optString("methodName"); //方法名
        //获取上下文
        Object obj = SpringConext.getApplicationContext().getBean(className);
        //获取方法
        Method m = obj.getClass().getDeclaredMethod(methodName, String.class,JSONObject.class);
        //调用方法
        return (JSONObject) m.invoke(obj, tenancyID,json);
    }


    public File execlrp(String exportName,JSONObject json,List<JSONObject> data1,List<JSONObject> data2,List<JSONObject> data3,HSSFSheet sheet1, HSSFWorkbook workBook,Integer stratIndex,Integer stratIndex2,Integer stratIndex3,JSONObject out1Result,JSONObject out1Result2,JSONObject out1Result3,List<JSONObject> list,JSONObject paramData) throws IOException {
        boolean judge = true;
        int rowNo = 0;      //总行号
        int rowline = 1; //sheet页最大得记录条数
        List<JSONObject>  structure = null;
        List<JSONObject>  structure1= null;
        List<JSONObject>  structure2= null;
        List<File> files = new ArrayList();
        File f = null;
        boolean b = false;
        //合计
        JSONArray footerArr = null;
        JSONObject footer = null;
        //服务器下载文件地址
        File gue = fileCatalogue();

        if(data1!=null&&data1.size()>0){
            structure = structureData(data1);
        }
        if(data2!=null&&data2.size()>0){
            structure1 = structureData(data2);
        }
        if(data3!=null&&data3.size()>0){
            structure2 = structureData(data3);
        }

        try{
            if(data1!=null&&data2!=null&&data3!=null){
                if(data1.size()>0 && data2.size()>0 && data3.size()>0){
                    if(structure.size()>0 && structure1.size()>0 && structure2.size()>0){
                        for(int i=0;i<data1.size();i++){
                            for(int j=0;j<data2.size();j++){
                                if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
                                    JSONObject obj = data1.get(i).getJSONObject("json1");
                                    JSONObject objs = data2.get(j).getJSONObject("json2");
                                    if(obj.size()>0 && objs.size()>0){
                                        if(judge){
                                            out1Result = ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
                                            paramData.put("rowNum", out1Result.opt("rowNum"));
                                            paramData.put("jin", out1Result.optInt("jin"));
                                            stratIndex=out1Result.optInt("rowNum");
                                            judge = false;
                                        }
                                        if(!objs.optString(json.optString("key")).equals("")){
                                            objs.remove(json.optString("key"));
                                        }
                                        out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
                                        stratIndex2=out1Result2.optInt("rowNum");
                                        paramData.put("rowNum", out1Result2.opt("rowNum"));
                                        paramData.put("jin", out1Result2.optInt("jin"));

                                        for(int k=0;k<data3.size();k++){
                                            if(data2.get(j).optString(json.optString("key1")).equals(data3.get(k).optString(json.optString("key1")))){   //第二层对比
                                                if(data2.get(j).optString("exportdataexpr1").equals(data3.get(k).optString("exportdataexpr2"))){  //第一层对比
                                                    JSONObject obj3 = data3.get(k).getJSONObject("json3");
                                                    if(obj3.size()>0){
                                                        if(!obj3.optString(json.optString("key")).equals("")){
                                                            obj3.remove(json.optString("key"));
                                                        }
                                                        if(!obj3.optString(json.optString("key1")).equals("")){
                                                            obj3.remove(json.optString("key1"));
                                                        }
                                                        out1Result3 =ExportUtils.out2(exportName,obj3,workBook,sheet1,list,structure2,paramData);
                                                        stratIndex3=out1Result3.optInt("rowNum");
                                                        paramData.put("rowNum", out1Result3.opt("rowNum"));
                                                        paramData.put("jin", out1Result3.optInt("jin"));
                                                    }
                                                }
                                            }
                                        }
                                        if(json.optInt("groupRow")==2){
                                            sheet1.groupRow(stratIndex2,stratIndex3);
                                            sheet1.setRowGroupCollapsed(stratIndex2, true);
                                        }else{
                                            sheet1.groupRow(stratIndex2,stratIndex3);
                                        }
                                    }
                                }
                            }
                            if(json.optInt("groupRow")==2){
                                sheet1.groupRow(stratIndex,stratIndex3);
                                sheet1.setRowGroupCollapsed(stratIndex, true);
                            }else{
                                sheet1.groupRow(stratIndex,stratIndex3);
                            }
                            judge = true;
                        }
                    }
                }
            }else if(data1!=null && data2!=null && data1.size()>0 && data2.size()>0){
                if(structure.size()>0 && structure1.size()>0){
                    for(int i=0;i<data1.size();i++){
                        for(int j=0;j<data2.size();j++){
                            if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
                                JSONObject obj = data1.get(i).getJSONObject("json1");
                                JSONObject objs = data2.get(j).getJSONObject("json2");
                                if(obj.size()>0 & objs.size()>0){
                                    if(judge){
                                        out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
                                        paramData.put("rowNum", out1Result.opt("rowNum"));
                                        paramData.put("jin", out1Result.optInt("jin"));
                                        stratIndex=out1Result.optInt("rowNum");
                                        judge = false;
                                    }

                                    if(!objs.optString(json.optString("key")).equals("")){
                                        objs.remove(json.optString("key"));
                                    }

                                    out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
                                    stratIndex2=out1Result2.optInt("rowNum");
                                    paramData.put("rowNum", out1Result2.opt("rowNum"));
                                    paramData.put("jin", out1Result2.optInt("jin"));
                                }
                            }
                        }
                        if(json.optInt("groupRow")==2){
                            sheet1.groupRow(stratIndex,stratIndex2);
                            sheet1.setRowGroupCollapsed(stratIndex, true);
                        }else{
                            sheet1.groupRow(stratIndex,stratIndex2);
                        }
                        judge = true;
                    }
                }
            }else{
                if(data1!=null && data1.size()>0){
                    if(data1.get(data1.size()-1).containsKey("footerList")){
                        footerArr = data1.get(data1.size()-1).getJSONArray("footerList");
                        footer = footerArr.getJSONObject(0);
                    }
                    if(structure.size()>0){
                        Integer rowNum = null;
                        Integer jin = null;
                        JSONObject returnTitleJson = null;
                        JSONObject obj = null;
                        for(int i=0;i<data1.size();i++) {
                            obj = data1.get(i).getJSONObject("json1");
                            if(obj.size()>0){
                                if(rowNo%sheetNum==0){
                                    // System.out.println("Current Sheet:" + rowNo/100);
                                    sheet1 = workBook.createSheet(exportName);//建立新的sheet对象
                                    returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
                                    rowNum= returnTitleJson.optInt("rowNum");
                                    jin=json.optInt("jin");
                                    paramData=exportContrastline(rowNum,jin);
                                    //sheet1 = workBook.getSheetAt(rowNo/100);        //动态指定当前的工作表
                                }
                                rowNo++;
                                // 调用导出方法；
                                out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
                                paramData.put("rowNum", out1Result.opt("rowNum"));
                                paramData.put("jin", out1Result.optInt("jin"));

                                if(rowNo%sheetNum==0){
                                    b=true;
                                    //System.out.println("row no: " + rowNo);
                                    int a1 = out1Result==null?0:out1Result.optInt("rowNum");
                                    int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
                                    int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
                                    // 导出印记
                                    JSONObject cc = new JSONObject();
                                    cc.put("man", json.opt("man"));
                                    cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
                                    ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);
                                    if(rowNo >= sheetNum){
                                        f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
                                        OutputStream fout =  new FileOutputStream(f);
                                        workBook.write(fout);
                                        //不分sheet页就必须重新new该对象
                                        workBook  = new HSSFWorkbook();
                                        fout.close();
                                        files.add(f);
                                    }

                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        finally {
            int a1 = out1Result==null?0:out1Result.optInt("rowNum");
            int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
            int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
            // 导出印记
            JSONObject cc = new JSONObject();
            cc.put("man", json.opt("man"));
            cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
            ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);

            if(b){
                f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
                OutputStream fout =  new FileOutputStream(f);
                workBook.write(fout);
                //不分sheet页就必须重新new该对象
                fout.close();
                files.add(f);
            }
            if(files.size()>0){
                File zip = new File(gue+"/"+exportName+System.currentTimeMillis()+".zip");
                zipFiles(files.toArray(new File[files.size()]),zip);
                return zip;
            }

        }
        return null;
    }

    /**
     * 导出生成文件目录
     *
     */

    public File fileCatalogue(){
        File[] roots = File.listRoots();
        // 该方法查询机器的根级 目录 并且在DownExcleFiles文件夹中建立文件夹
        String uuid=UUID.randomUUID().toString();
        String pathFolder = roots[0]+"DownExcleFilesZip\\"+uuid+"\\";// uuid 的文件名字
        File filePath = new File(pathFolder);
        if(!filePath.exists()) {
            logger.info("文件不存在，开始创建文件");
            // 设置权限
            filePath.setWritable(true, false);
            // 如果不存在
            filePath.mkdirs(); //开始创建

            logger.info("导出文件地址为:"+filePath);
        }
        return filePath;
    }


    public List<JSONObject> structureData(List<JSONObject> list){
        List<JSONObject> obj = new ArrayList<JSONObject>();
        if(list.get(list.size()-1)!=null){
            JSONArray arr = list.get(list.size()-1).getJSONArray("structure");
            for(int i=0;i<arr.size();i++){
                JSONObject job = new JSONObject();
                JSONObject info=arr.getJSONObject(i);
                job.put("fieldname", info.getString("fieldname"));
                job.put("fieldtype", info.getString("fieldtype"));
                obj.add(job);
            }
        }
        return obj;
    }



    /**
     *
     * @param srcfile 文件名数组
     * @param zipfile 压缩后文件
     */
    public void zipFiles(java.io.File[] srcfile, java.io.File zipfile) {
        byte[] buf = new byte[1024];
        try {
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(
                    zipfile));
            for (int i = 0; i < srcfile.length; i++) {
                FileInputStream in = new FileInputStream(srcfile[i]);
                out.putNextEntry(new ZipEntry(srcfile[i].getName()));
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
                out.closeEntry();
                in.close();
            }
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

//    @Override
//    public Object[] exportDataAll(String tenancyID, JSONObject json,HSSFWorkbook workBook) throws Exception {
//        long t1 = System.currentTimeMillis();
//        HSSFSheet sheet1 = null;
//        File f = null;
//        String exportName = json.optString("exportName");//名称
//        JSONObject paramData = null;
//        //如果为第一层不进行创建sheet页
//        if(json.optInt("hierarchy")>1){
//            sheet1=workBook.createSheet(exportName);
//            JSONObject returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
//            Integer rowNum= returnTitleJson.optInt("rowNum");
//            Integer jin=json.optInt("jin");
//            paramData=exportContrastline(rowNum,jin);
//        }
//        StringBuilder sbdate = new StringBuilder();
//        StringBuilder sbdatekey = new StringBuilder();
//        Integer stratIndex =0;
//        Integer stratIndex2 =0;
//        Integer stratIndex3 =0;
//        JSONObject out1Result2 =null;
//        JSONObject out1Result =null;
//        JSONObject out1Result3 =null;
//        List<JSONObject> objs = new ArrayList<JSONObject>();
//        List<JSONObject> data = null;
//        List<JSONObject> data2 = null;
//        List<JSONObject> data3 = null;
//        String parametertype = "";
//        String parametertype1 = "";
//        String parametertype2 = "";
//
//        if(json.containsKey("rowcolumns") && json.getJSONObject("rowcolumns")!=null){
//            JSONObject obj = json.getJSONObject("rowcolumns");
//            JSONArray list=obj.getJSONArray("key");
//            for(int i=0;i<list.size();i++){
//                JSONObject job = new JSONObject();
//                JSONObject info=list.getJSONObject(i);
//                job.put("field", info.getString("field"));
//                objs.add(job);
//            }
//
//            if(json.optInt("hierarchy")==3){
//                data =  getMethodOne(tenancyID,json);  //第一层返回集合
//                if(data.size()>0){
//                    if(json.containsKey("hierarchytype") && json.optInt("hierarchytype")==1){   //是否存在第二层
//                        json.put("hierarchytype", 2);
//                        if(json.containsKey("p_report_type")){
//                            json.put("p_report_type", "L"+json.optInt("selecttype")+"J"+json.optInt("hierarchytype"));
//                        }
//                    }
//                    if(json.containsKey("exportdataexpr") && !json.optString("exportdataexpr").equals("")){    //第一层查询字段
//                        json.put(json.optString("key"), json.optString("exportdataexpr"));
//                    }
//                    if(json.containsKey("sortKey1") && !json.optString("sortKey1").equals("")){      //第二层排序
//                        //前台传入是否需要后台进行数据排序
//                        json.put("sortKey1",json.optString("sortKey1"));
//                    }
//                }
//                data2 =  getMethodTwo(tenancyID,json,stratIndex2);  //第二层返回集合
//                if(data2.size()>0){
//                    sbdate.delete( 0,sbdate.length());
//                    sbdatekey.delete( 0,sbdatekey.length());
//                    if(json.containsKey("hierarchytype") && json.optInt("hierarchytype") ==2){
//                        json.put("hierarchytype", 3);
//
//                        /**
//                         * 用函数导出时需要传入参数p_report_level_two代表函数sql使用层级
//                         * selecttype为查询方式
//                         * hierarchytype为当前查询得层级
//                         */
//                        if(json.containsKey("p_report_type")){
//                            json.put("p_report_type", "L"+json.optInt("selecttype")+"J"+json.optInt("hierarchytype"));
//                        }
//                    }
//                    for(int i=0;i<data2.size();i++){
//                        String key1 = json.optString("key1");
//                        if(data2.get(i).containsKey(key1)){
//                            if(!data2.get(i).optString(key1).equals("")&&!data2.get(i).optString(key1).equals(null)){
//                                sbdatekey.append(","+data2.get(i).optString(key1));
//                            }
//                        }
//                        if(!data2.get(i).optString("exportdataexpr1").equals("")){
//                            sbdate.append(","+data2.get(i).optString("exportdataexpr1"));
//                        }
//                    }
//
//                    if(sbdatekey.length()>0)
//                    {
//                        if(json.optString("parametertype1").equals("String")){   //第二层查询是否为字符串
//                            sbdatekey.delete(0,1);
//                            parametertype1 = ConditionUtils.spilt(sbdatekey.toString());
//                            json.put(json.optString("key1"), parametertype1);
//                        }else{
//                            sbdatekey.delete(0,1);
//                            json.put(json.optString("key1"), sbdatekey.toString());
//                        }
//                    }else{
//                        json.put(json.optString("key1"), sbdatekey.toString());
//                    }
//
//                    if(sbdate.length()>0)
//                    {
//                        if(json.optString("parametertype").equals("String")){   //第一层查询是否为字符串
//                            sbdate.delete(0,1);
//                            parametertype = ConditionUtils.spilt(sbdate.toString());
//                            json.put("exportdataexpr1", parametertype);
//                        }else{
//                            sbdate.delete(0,1);
//                            json.put("exportdataexpr1", sbdate.toString());
//                        }
//                    }else{
//                        json.put("exportdataexpr1", sbdate.toString());
//                    }
//                    if(json.containsKey("sortKey2") && !json.optString("sortKey2").equals("")){
//                        //前台传入是否需要后台进行数据排序
//                        json.put("sortKey2",json.optString("sortKey2"));
//                    }
//                }
//                data3 =  getMethodThree(tenancyID,json,stratIndex3);  //第三层返回集合
//                if(data3.size()>0){
//                    sbdate.delete( 0,sbdate.length());   //清空sbdate     (存放第一层数据)
//                    sbdatekey.delete( 0,sbdatekey.length()); //清空sbdatekey(存放第二层数据)
//                    for(int i=0;i<data3.size();i++){
//                        String key2 = json.optString("key1");  //获取第二层导出字段  举例 report_date
//                        if(data3.get(i).containsKey(key2)){
//                            if(!data3.get(i).optString(key2).equals("")&&!data3.get(i).optString(key2).equals(null)){
//                                sbdatekey.append(","+data3.get(i).optString(key2));
//                            }
//                        }
//                        if(!data3.get(i).optString("exportdataexpr2").equals("")){
//                            sbdate.append(","+data3.get(i).optString("exportdataexpr2"));
//                        }
//                    }
//				    	/*if(sbdate.length()>0)
//						{
//				    		if(json.optString("parametertype2").equals("String")){   //第三层查询是否为字符串
//				    			sbdate.delete(0,1);
//				    			parametertype2 = ConditionUtils.spilt(sbdate.toString());
//								json.put("exportdataexpr2", parametertype2);
//				    		}else{
//				    			sbdate.delete(0,1);
//								json.put("exportdataexpr2", sbdate.toString());
//				    		}
//						}else{
//							json.put("exportdataexpr2", sbdate.toString());
//						}*/
//                    if(sbdatekey.length()>0)
//                    {
//                        if(json.optString("parametertype1").equals("String")){   //第二层查询是否为字符串
//                            sbdatekey.delete(0,1);
//                            parametertype1 = ConditionUtils.spilt(sbdatekey.toString());
//                            json.put(json.optString("key2"), parametertype1);
//                        }else{
//                            sbdatekey.delete(0,1);
//                            json.put(json.optString("key2"), sbdatekey.toString());
//                        }
//                    }else{
//                        json.put(json.optString("key2"), sbdatekey.toString());
//                    }
//
//                    if(sbdate.length()>0)
//                    {
//                        if(json.optString("parametertype").equals("String")){   //第一层查询是否为字符串
//                            sbdate.delete(0,1);
//                            parametertype = ConditionUtils.spilt(sbdate.toString());
//                            json.put("exportdataexpr2", parametertype);
//                        }else{
//                            sbdate.delete(0,1);
//                            json.put("exportdataexpr2", sbdate.toString());
//                        }
//                    }else{
//                        json.put("exportdataexpr2", sbdate.toString());
//                    }
//                }
//            }else if(json.optInt("hierarchy")==2){
//                data =  getMethodOne(tenancyID,json);  //第一层返回集合
//                if(data.size()>0){
//                    if(json.containsKey("hierarchytype") && json.optInt("hierarchytype")==1){
//                        json.put("hierarchytype", 2);
//                        if(json.containsKey("p_report_type")){
//                            json.put("p_report_type", "L"+json.optInt("selecttype")+"J"+json.optInt("hierarchytype"));
//                        }
//                    }
//                    if(json.containsKey("exportdataexpr") && !json.optString("exportdataexpr").equals("")){
//                        json.put(json.optString("key"), json.optString("exportdataexpr"));
//                    }
//                    if(json.containsKey("sortKey1") && !json.optString("sortKey1").equals("")){
//                        //前台传入是否需要后台进行数据排序
//                        json.put("sort1",json.optString("sortKey1"));
//                    }
//                }
//                data2 =  getMethodTwo(tenancyID,json,stratIndex2);  //第二层返回集合
//            }else{
//                long t3 = System.currentTimeMillis();
//                data =  getMethodOne(tenancyID,json);  //第一层返回集合
//                long t4 = System.currentTimeMillis();
//                System.out.println("获取查询数据用时："+(t4-t3)/1000+"秒");
//            }
//            if(json.containsKey("TenThousandExcle")) {
//                paramData.put("TenThousandExcle", json.optString("TenThousandExcle"));
//                paramData.put("maxExcleNum", json.optString("maxExcleNum") ==  null ? 100000 : json.optInt("maxExcleNum"));
//            }
//            long t2 = System.currentTimeMillis();
//            System.out.println("处理全部数据耗时："+(t2-t1)/1000+"秒");
//
//            long t5 = System.currentTimeMillis();
//            f = execlrp(exportName,json,data, data2, data3, sheet1, workBook, stratIndex, stratIndex2, stratIndex3, out1Result, out1Result2, out1Result3, objs, paramData);
//            long t6 = System.currentTimeMillis();
//            System.out.println("全部数据写入文件耗时："+(t6-t5)/1000+"秒");
//        }
//
//        if(json.optInt("hierarchy")>1){
//            sheet1.setRowSumsBelow(false);
//            sheet1.setRowSumsRight(false);
//        }
//        Object[] o = new Object[2];
//        o[0] = workBook;
//        o[1] = f;
//        return o;
//    }
//
//    public JSONObject exportContrastline(int rowNum,int jin){
//        JSONObject paramData =new JSONObject();
//        paramData.put("rowNum", rowNum);
//        paramData.put("jin",jin);
//        paramData.put("strtIndex",rowNum);
//        return paramData;
//    }
//
//
//    public List<JSONObject>  getMethodOne(String tenancyID,JSONObject json) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
//        List<JSONObject> data1= new ArrayList<JSONObject>();
//        List<JSONObject> footerList = null;
//        JSONObject findResult= getInvokeMethod(tenancyID,json);
//        //明细数据
//        List<JSONObject> list1 =(List<JSONObject>) findResult.opt("rows");
//
//        //数据类型
//        List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");
//
//        //合计
//        if(json.containsKey("total_line") && json.optInt("total_line")==1){
//            footerList =(List<JSONObject>) findResult.opt("footer");
//        }
//        if(list1.size()>0){
//            String key = json.optString("key");
//            if(!key.equals("")){
//                for(JSONObject json1 : list1) {
//                    JSONObject jsona = new JSONObject();
//                    //前台传入需要对比的字段
//                    jsona.put("exportdataexpr",json1.optString(key));
//
//                    jsona.put("json1", json1);  //获取第一层的选中数据
//
//                    data1.add(jsona);
//                }
//                JSONObject structures = new JSONObject();
//                structures.put("structure", structure);
//                structures.put("footerList", footerList);
//                data1.add(structures);
//            }else{
//                return data1;
//            }
//        }
//        return data1;
//    }
//
//
//    public List<JSONObject>  getMethodTwo(String tenancyID,JSONObject json,int stratIndex2) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
//        List<JSONObject> data2= new ArrayList<JSONObject>();
//        JSONObject findResult= getInvokeMethod(tenancyID,json);
//        @SuppressWarnings("unchecked")
//        List<JSONObject> list2 =(List<JSONObject>) findResult.opt("rows");
//        @SuppressWarnings("unchecked")
//        List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");
//        if(list2.size()>0){
//            String key = json.optString("key");   //和第一级需要对比的字段  举例  item_num
//            String key1 = json.optString("key1"); //第三及需要对比的字段  举例 report_date
//            if(!key.equals("")){
//                for(JSONObject json2 : list2) {
//                    JSONObject jsonb = new JSONObject();
//                    //前台传入需要对比的字段  第二层
//                    jsonb.put("exportdataexpr1",json2.optString(key));   //item_num 的数据存入exportdataexpr1
//                    jsonb.put(key1,json2.optString(key1));    //report_date 的数据存入key1
//                    jsonb.put("stratIndex2", stratIndex2);
//                    jsonb.put("json2", json2);
//                    data2.add(jsonb);
//                }
//                JSONObject structures = new JSONObject();
//                structures.put("structure", structure);
//                data2.add(structures);
//            }else{
//                return data2;
//            }
//        }
//        return data2;
//    }
//
//    public List<JSONObject>  getMethodThree(String tenancyID,JSONObject json,int stratIndex3) throws ClassNotFoundException, InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
//        List<JSONObject> data3= new ArrayList<JSONObject>();
//        JSONObject findResult= getInvokeMethod(tenancyID,json);
//        @SuppressWarnings("unchecked")
//        List<JSONObject> list3 =(List<JSONObject>) findResult.opt("rows");
//        @SuppressWarnings("unchecked")
//        List<JSONObject> structure =(List<JSONObject>) findResult.opt("structure");
//        if(list3.size()>0){
//            String key1 = json.optString("key");  //需要和第一级对比   举例   item_num
//            String key2 = json.optString("key1");  //需要和第二层对比  举例    report_date
//            //String key1 = json.optString("key2");//如果存在第四层那么获取到key2进行第四层对比
//            if(!key2.equals("") && !key1.equals("")){
//                for(JSONObject json3 : list3) {
//                    JSONObject jsonc = new JSONObject();
//                    //前台传入需要对比的字段  第二层
//                    jsonc.put(key2, json3.optString(key2));  //report_date的数据 存入key2
//                    jsonc.put("exportdataexpr2", json3.optString(key1)); //item_num 的数据存入exportdataexpr2
//                    //jsonc.put(key1,json3.optString(key1));
//                    jsonc.put("stratIndex3", stratIndex3);
//                    jsonc.put("json3", json3);
//                    data3.add(jsonc);
//                }
//                JSONObject structures = new JSONObject();
//                structures.put("structure", structure);
//                data3.add(structures);
//            }else{
//                return data3;
//            }
//        }
//        return data3;
//    }
//
//
//    //反射执行java方法
//    public JSONObject getInvokeMethod(String tenancyID,JSONObject json) throws ClassNotFoundException, InstantiationException,
//            IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
//        String className = json.optString("className"); //类路径
//        String methodName= json.optString("methodName"); //方法名
//        //获取上下文
//        Object obj = SpringConext.getApplicationContext().getBean(className);
//        //获取方法
//        Method m = obj.getClass().getDeclaredMethod(methodName, String.class,JSONObject.class);
//        //调用方法
//        return (JSONObject) m.invoke(obj, tenancyID,json);
//    }
//
//
//    public File execlrp(String exportName,JSONObject json,List<JSONObject> data1,List<JSONObject> data2,List<JSONObject> data3,HSSFSheet sheet1, HSSFWorkbook workBook,Integer stratIndex,Integer stratIndex2,Integer stratIndex3,JSONObject out1Result,JSONObject out1Result2,JSONObject out1Result3,List<JSONObject> list,JSONObject paramData) throws IOException {
//        boolean judge = true;
//        int rowNo = 0;      //总行号
//        int rowline = 1; //sheet页最大得记录条数
//        List<JSONObject>  structure = null;
//        List<JSONObject>  structure1= null;
//        List<JSONObject>  structure2= null;
//        List<File> files = new ArrayList();
//        File f = null;
//        boolean b = false;
//        //合计
//        JSONArray footerArr = null;
//        JSONObject footer = null;
//        //服务器下载文件地址
//        File gue = fileCatalogue();
//
//        if(data1!=null&&data1.size()>0){
//            structure = structureData(data1);
//        }
//        if(data2!=null&&data2.size()>0){
//            structure1 = structureData(data2);
//        }
//        if(data3!=null&&data3.size()>0){
//            structure2 = structureData(data3);
//        }
//
//        try{
//            if(data1!=null&&data2!=null&&data3!=null){
//                if(data1.size()>0 && data2.size()>0 && data3.size()>0){
//                    if(structure.size()>0 && structure1.size()>0 && structure2.size()>0){
//                        for(int i=0;i<data1.size();i++){
//                            for(int j=0;j<data2.size();j++){
//                                if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
//                                    JSONObject obj = data1.get(i).getJSONObject("json1");
//                                    JSONObject objs = data2.get(j).getJSONObject("json2");
//                                    if(obj.size()>0 && objs.size()>0){
//                                        if(judge){
//                                            out1Result = ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
//                                            paramData.put("rowNum", out1Result.opt("rowNum"));
//                                            paramData.put("jin", out1Result.optInt("jin"));
//                                            stratIndex=out1Result.optInt("rowNum");
//                                            judge = false;
//                                        }
//                                        if(!objs.optString(json.optString("key")).equals("")){
//                                            objs.remove(json.optString("key"));
//                                        }
//                                        out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
//                                        stratIndex2=out1Result2.optInt("rowNum");
//                                        paramData.put("rowNum", out1Result2.opt("rowNum"));
//                                        paramData.put("jin", out1Result2.optInt("jin"));
//
//                                        for(int k=0;k<data3.size();k++){
//                                            if(data2.get(j).optString(json.optString("key1")).equals(data3.get(k).optString(json.optString("key1")))){   //第二层对比
//                                                if(data2.get(j).optString("exportdataexpr1").equals(data3.get(k).optString("exportdataexpr2"))){  //第一层对比
//                                                    JSONObject obj3 = data3.get(k).getJSONObject("json3");
//                                                    if(obj3.size()>0){
//                                                        if(!obj3.optString(json.optString("key")).equals("")){
//                                                            obj3.remove(json.optString("key"));
//                                                        }
//                                                        if(!obj3.optString(json.optString("key1")).equals("")){
//                                                            obj3.remove(json.optString("key1"));
//                                                        }
//                                                        out1Result3 =ExportUtils.out2(exportName,obj3,workBook,sheet1,list,structure2,paramData);
//                                                        stratIndex3=out1Result3.optInt("rowNum");
//                                                        paramData.put("rowNum", out1Result3.opt("rowNum"));
//                                                        paramData.put("jin", out1Result3.optInt("jin"));
//                                                    }
//                                                }
//                                            }
//                                        }
//                                        if(json.optInt("groupRow")==2){
//                                            sheet1.groupRow(stratIndex2,stratIndex3);
//                                            sheet1.setRowGroupCollapsed(stratIndex2, true);
//                                        }else{
//                                            sheet1.groupRow(stratIndex2,stratIndex3);
//                                        }
//                                    }
//                                }
//                            }
//                            if(json.optInt("groupRow")==2){
//                                sheet1.groupRow(stratIndex,stratIndex3);
//                                sheet1.setRowGroupCollapsed(stratIndex, true);
//                            }else{
//                                sheet1.groupRow(stratIndex,stratIndex3);
//                            }
//                            judge = true;
//                        }
//                    }
//                }
//            }else if(data1!=null && data2!=null && data1.size()>0 && data2.size()>0){
//                if(structure.size()>0 && structure1.size()>0){
//                    for(int i=0;i<data1.size();i++){
//                        for(int j=0;j<data2.size();j++){
//                            if(data1.get(i).optString("exportdataexpr").equals(data2.get(j).optString("exportdataexpr1"))){
//                                JSONObject obj = data1.get(i).getJSONObject("json1");
//                                JSONObject objs = data2.get(j).getJSONObject("json2");
//                                if(obj.size()>0 & objs.size()>0){
//                                    if(judge){
//                                        out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
//                                        paramData.put("rowNum", out1Result.opt("rowNum"));
//                                        paramData.put("jin", out1Result.optInt("jin"));
//                                        stratIndex=out1Result.optInt("rowNum");
//                                        judge = false;
//                                    }
//
//                                    if(!objs.optString(json.optString("key")).equals("")){
//                                        objs.remove(json.optString("key"));
//                                    }
//
//                                    out1Result2 =ExportUtils.out2(exportName,objs,workBook,sheet1,list,structure1,paramData);
//                                    stratIndex2=out1Result2.optInt("rowNum");
//                                    paramData.put("rowNum", out1Result2.opt("rowNum"));
//                                    paramData.put("jin", out1Result2.optInt("jin"));
//                                }
//                            }
//                        }
//                        if(json.optInt("groupRow")==2){
//                            sheet1.groupRow(stratIndex,stratIndex2);
//                            sheet1.setRowGroupCollapsed(stratIndex, true);
//                        }else{
//                            sheet1.groupRow(stratIndex,stratIndex2);
//                        }
//                        judge = true;
//                    }
//                }
//            }else{
//                if(data1!=null && data1.size()>0){
//                    if(data1.get(data1.size()-1).containsKey("footerList")){
//                        footerArr = data1.get(data1.size()-1).getJSONArray("footerList");
//                        footer = footerArr.getJSONObject(0);
//                    }
//                    if(structure.size()>0){
//                        Integer rowNum = null;
//                        Integer jin = null;
//                        JSONObject returnTitleJson = null;
//                        JSONObject obj = null;
//                        for(int i=0;i<data1.size();i++) {
//                            obj = data1.get(i).getJSONObject("json1");
//                            if(obj.size()>0){
//                                if(rowNo%sheetNum==0){
//                                    // System.out.println("Current Sheet:" + rowNo/100);
//                                    sheet1 = workBook.createSheet(exportName);//建立新的sheet对象
//                                    returnTitleJson = ReportExportUtils.titleActivity(json,sheet1,workBook);
//                                    rowNum= returnTitleJson.optInt("rowNum");
//                                    jin=json.optInt("jin");
//                                    paramData=exportContrastline(rowNum,jin);
//                                    //sheet1 = workBook.getSheetAt(rowNo/100);        //动态指定当前的工作表
//                                }
//                                rowNo++;
//                                // 调用导出方法；
//                                out1Result =ExportUtils.out2(exportName,obj,workBook,sheet1,list,structure,paramData);
//                                paramData.put("rowNum", out1Result.opt("rowNum"));
//                                paramData.put("jin", out1Result.optInt("jin"));
//
//                                if(rowNo%sheetNum==0){
//                                    b=true;
//                                    //System.out.println("row no: " + rowNo);
//                                    int a1 = out1Result==null?0:out1Result.optInt("rowNum");
//                                    int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
//                                    int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
//                                    // 导出印记
//                                    JSONObject cc = new JSONObject();
//                                    cc.put("man", json.opt("man"));
//                                    cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
//                                    ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);
//                                    if(rowNo >= sheetNum){
//                                        f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
//                                        OutputStream fout =  new FileOutputStream(f);
//                                        workBook.write(fout);
//                                        //不分sheet页就必须重新new该对象
//                                        workBook  = new HSSFWorkbook();
//                                        fout.close();
//                                        files.add(f);
//                                    }
//
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            System.out.println(e.getMessage());
//        }
//        finally {
//            int a1 = out1Result==null?0:out1Result.optInt("rowNum");
//            int a2 = out1Result2==null?0:out1Result2.optInt("rowNum");
//            int a3 = out1Result3==null?0:out1Result3.optInt("rowNum");
//            // 导出印记
//            JSONObject cc = new JSONObject();
//            cc.put("man", json.opt("man"));
//            cc.put("rowNum",a1>=a2?(a1>=a3?a1:a3):(a2>=a3?a2:a3));
//            ReportExportUtils.setManAndTime( workBook, sheet1, cc,json,list,structure,footer);
//
//            if(b){
//                f = new File(gue+"/"+exportName+"-"+System.currentTimeMillis()+".xls");
//                OutputStream fout =  new FileOutputStream(f);
//                workBook.write(fout);
//                //不分sheet页就必须重新new该对象
//                fout.close();
//                files.add(f);
//            }
//            //如果大于10W数据进行压缩
//            if(files.size()>0){
//                File zip = new File(gue+"/"+exportName+System.currentTimeMillis()+".zip");
//                zipFiles(files.toArray(new File[files.size()]),zip);
//                return zip;
//            }
//
//        }
//        return null;
//    }
//
//    /**
//     * 导出生成文件目录
//     *
//     */
//
//    public File fileCatalogue(){
//        File[] roots = File.listRoots();
//        // 该方法查询机器的根级 目录 并且在DownExcleFiles文件夹中建立文件夹
//        String uuid=UUID.randomUUID().toString();
//        String pathFolder = roots[0]+"DownExcleFilesZip\\"+uuid+"\\";// uuid 的文件名字
//        File filePath = new File(pathFolder);
//        if(!filePath.exists()) {
//            logger.info("文件不存在，开始创建文件");
//            // 设置权限
//            filePath.setWritable(true, false);
//            // 如果不存在
//            filePath.mkdirs(); //开始创建
//
//            logger.info("导出文件地址为:"+filePath);
//        }
//        return filePath;
//    }
//
//
//    public List<JSONObject> structureData(List<JSONObject> list){
//        List<JSONObject> obj = new ArrayList<JSONObject>();
//        if(list.get(list.size()-1)!=null){
//            JSONArray arr = list.get(list.size()-1).getJSONArray("structure");
//            for(int i=0;i<arr.size();i++){
//                JSONObject job = new JSONObject();
//                JSONObject info=arr.getJSONObject(i);
//                job.put("fieldname", info.getString("fieldname"));
//                job.put("fieldtype", info.getString("fieldtype"));
//                obj.add(job);
//            }
//        }
//        return obj;
//    }
//
//
//
//    /**
//     *
//     * @param srcfile 文件名数组
//     * @param zipfile 压缩后文件
//     */
//    public void zipFiles(java.io.File[] srcfile, java.io.File zipfile) {
//        byte[] buf = new byte[1024];
//        try {
//            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(
//                    zipfile));
//            for (int i = 0; i < srcfile.length; i++) {
//                FileInputStream in = new FileInputStream(srcfile[i]);
//                out.putNextEntry(new ZipEntry(srcfile[i].getName()));
//                int len;
//                while ((len = in.read(buf)) > 0) {
//                    out.write(buf, 0, len);
//                }
//                out.closeEntry();
//                in.close();
//            }
//            out.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }


}
