package com.tzx.report.bo.imp.hq;

import com.tzx.report.bo.hq.BusinessAllianceReconciliationStatementQueryService;
import com.tzx.report.po.hq.dao.BusinessAllianceReconciliationStatementQueryDao;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service(BusinessAllianceReconciliationStatementQueryService.NAME)
public class BusinessAllianceReconciliationStatementQueryServiceImpl implements BusinessAllianceReconciliationStatementQueryService
{
	
	@Resource
	private BusinessAllianceReconciliationStatementQueryDao businessAllianceReconciliationStatementQueryDao;

	@Override
	public JSONObject getBusinessAllianceReconciliationStatementQuery(String tenancyID, JSONObject condition) throws Exception {
		return businessAllianceReconciliationStatementQueryDao.getBusinessAllianceReconciliationStatementQuery(tenancyID, condition);
	}
 
}
