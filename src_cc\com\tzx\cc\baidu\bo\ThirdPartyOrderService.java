package com.tzx.cc.baidu.bo;

import net.sf.json.JSONObject;

public interface ThirdPartyOrderService {
	
	/**
	 * 取消订单
	 */
	String ORDER_CANCEL="cancel";
	/**
	 * 完成订单
	 */
	String ORDER_COMPLETE="complete";
	
	/**保存订单信息(cc_order_list)
	 * @param data
	 */
	JSONObject saveOrderList() throws Exception;
	
	/**保存订单菜品信息(cc_order_item)
	 * @param data
	 */
	void saveOrderItem() throws Exception; 
	
	/**保存付款信息(cc_order_repayment)
	 * @param data
	 */
	void saveOrderRepayment() throws Exception; 
	
	/**保存优惠信息(cc_order_discount)
	 * @param data
	 */
	void saveOrderDiscount() throws Exception; 
	
}
