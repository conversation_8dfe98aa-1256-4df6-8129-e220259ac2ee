package com.tzx.report.service.rest.crm;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.framework.common.util.JsonUtils;
import com.tzx.report.bo.boh.BusinessBankDishReportService;
import com.tzx.report.bo.boh.BusinessInformationReportService;
import com.tzx.report.bo.boh.BusinessSummaryStatisticsReportService;
import com.tzx.report.bo.boh.BusinessSummaryYearMomReportService;
import com.tzx.report.bo.boh.RestoreBillSummaryReportService;
import com.tzx.report.bo.crm.MemberOverallBusinessReportService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ReportExportUtils;

/**
 * 
 * 营业奉送汇总查询
 *
 */

@Controller("MemberOverallBusinessReportRest")
@RequestMapping("/report/MemberOverallBusinessReportRest")
public class MemberOverallBusinessReportRest
{
	@Resource(name =  MemberOverallBusinessReportService.NAME)
	private MemberOverallBusinessReportService		memberOverallBusinessReportService;
	
	@Resource
	ConditionUtils conditionUtils;
	
	@RequestMapping(value = "/getRestoreBill")
	public void getRestoreBill(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
				if(p.optString("old_store_id").length()==0){
					
					p.element("old_store_id", session.getAttribute("user_organ_codes_group"));
				}
				if(p.optString("store_ids").length()==0) {
					p.element("store_ids", session.getAttribute("user_organ_codes_group"));
				}
			}else{
				if(p.optString("old_store_id").length()==0){
					
					p.element("old_store_id", session.getAttribute("user_organ"));
				}
				if(p.optString("store_ids").length()==0) {
					p.element("store_ids", session.getAttribute("user_organ"));
				}
			}
			
			result = memberOverallBusinessReportService.getRestoreBill((String) session.getAttribute("tenentid"), p).toString();
		}
		catch (Exception e)
		{
			result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();

				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}

	}
	
	
	
	@RequestMapping(value = "/exportData")
	public void exportData(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{

			workBook = new HSSFWorkbook();
		       
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
				if(p.optString("old_store_id").length()==0){
					
					p.element("old_store_id", session.getAttribute("user_organ_codes_group"));
				}
				if(p.optString("store_ids").length()==0) {
					p.element("store_ids", session.getAttribute("user_organ_codes_group"));
				}
			}else{
				if(p.optString("old_store_id").length()==0){
					p.element("old_store_id", session.getAttribute("user_organ"));
				}
				if(p.optString("store_ids").length()==0) {
					p.element("store_ids", session.getAttribute("user_organ"));
				}
			}
			
			workBook = memberOverallBusinessReportService.exportDate((String) session.getAttribute("tenentid"), p ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			ReportExportUtils.download(workBook,response,"会员交易整体分析");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	@RequestMapping(value = "/exportData2")
	public void exportData2(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		HttpSession session = request.getSession();
		HSSFWorkbook workBook = null;
		try
		{

			workBook = new HSSFWorkbook();
		       
			JSONObject p = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if (map.get(key)[0] != "")
				{
					p.put(key, map.get(key)[0]);
				}
			}
			
			if(session.getAttribute("valid_state") == null||Integer.valueOf(session.getAttribute("valid_state").toString()).equals(0)){
				if(p.optString("old_store_id").length()==0){
					
					p.element("old_store_id", session.getAttribute("user_organ_codes_group"));
				}
				if(p.optString("store_ids").length()==0) {
					p.element("store_ids", session.getAttribute("user_organ_codes_group"));
				}
			}else{
				if(p.optString("old_store_id").length()==0){
					p.element("old_store_id", session.getAttribute("user_organ"));
				}
				if(p.optString("store_ids").length()==0) {
					p.element("store_ids", session.getAttribute("user_organ"));
				}
			}
			
			workBook = memberOverallBusinessReportService.exportDate2((String) session.getAttribute("tenentid"), p ,workBook);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			ReportExportUtils.download(workBook,response,"会员交易整体分析");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}

	
	
}
