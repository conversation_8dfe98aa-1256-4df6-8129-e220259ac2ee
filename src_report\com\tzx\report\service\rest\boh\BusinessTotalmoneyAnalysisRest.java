package com.tzx.report.service.rest.boh;
/**
 * Created by gj on 2019-05-30.
 */

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.tzx.report.common.util.ConditionUtils;
import jxl.write.WriteException;
import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.report.bo.boh.BusinessTotalmoneyAnalysisService;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ReportExportUtils;

/**
 *
 * 营业总额对比分析
 *
 */

@Controller("BusinessTotalmoneyAnalysisRest")
@RequestMapping("/report/BusinessTotalmoneyAnalysisRest")
public class BusinessTotalmoneyAnalysisRest
{

    @Resource(name = BusinessTotalmoneyAnalysisService.NAME)
    private BusinessTotalmoneyAnalysisService businessTotalmoneyAnalysisService;

    @Resource
    ConditionUtils conditionUtils;



    @RequestMapping(value = "/find")
    public void find(HttpServletRequest request, HttpServletResponse response) throws IOException, WriteException
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        InputStream in = null;
        HttpSession session = request.getSession();
        String result = "";
        try
        {
            JSONObject p = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                p.put(key, map.get(key)[0]);
            }

            if(p.optString("store_id").length()==0){
                p.element("store_id", session.getAttribute("user_organ_codes_group"));
            }

            result = businessTotalmoneyAnalysisService.find((String) session.getAttribute("tenentid"), p).toString();
        }
        catch (Exception e)
        {
            result = "{\"success\" : false , \"msg\" : \"发生错误!\"}";
            e.printStackTrace();
        }
        finally
        {
            try
            {
                if (in != null)
                {
                    in.close();
                }
            }
            catch (Exception e)
            {
            }

            try
            {
                out = response.getWriter();

                out.print(result);
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
            }
            finally
            {
                if (out != null) out.close();
            }
        }

    }
}
