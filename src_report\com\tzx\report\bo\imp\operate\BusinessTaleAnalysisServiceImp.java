package com.tzx.report.bo.imp.operate;


import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import com.tzx.report.bo.operate.BusinessTaleAnalysisService;
import com.tzx.report.common.util.ReportExportUtils;
import com.tzx.report.po.operate.dao.BusinessTaleAnalysisDao;

@Service(BusinessTaleAnalysisService.NAME)
public class BusinessTaleAnalysisServiceImp implements BusinessTaleAnalysisService
{
	
	@Resource(name = BusinessTaleAnalysisDao.NAME)
	private BusinessTaleAnalysisDao businessTaleAnalysisDao;

	@Override
	public JSONObject find(String attribute, JSONObject p) throws Exception {
		JSONObject json=businessTaleAnalysisDao.find(attribute,p);
		return json;
	}

	

	
}
