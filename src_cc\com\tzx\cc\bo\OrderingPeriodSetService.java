package com.tzx.cc.bo;

import java.util.List;

import com.tzx.framework.common.exception.SystemException;

import net.sf.json.JSONObject;

public interface OrderingPeriodSetService
{
	String	NAME = "com.tzx.cc.bo.imp.OrderingPeriodSetImpl";
	
	public JSONObject loadOrderingPeriod(String tenancyID,JSONObject condition, String conditions) throws Exception;
	
	public Boolean saveOrderingPeriod(String tenancyID, JSONObject obj) throws Exception,SystemException;
	
	public JSONObject loadCopyOrderingPeriod(String tenancyID,JSONObject condition) throws Exception;
	
	public Boolean saveCopyPeriods(String tenancyID, JSONObject obj) throws Exception,SystemException;

	 public String delete(String tenantId, String tableKey, List<JSONObject> keyList) throws Exception;
		
		public String delete(String tenantId, String tableKey, JSONObject key) throws Exception;
}
