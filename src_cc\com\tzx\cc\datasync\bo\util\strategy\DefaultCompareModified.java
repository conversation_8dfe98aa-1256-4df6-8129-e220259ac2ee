package com.tzx.cc.datasync.bo.util.strategy;

import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 默认比较器，通过fake_id比对rif和saas数据是否存在，是否需要更新，（数据多是效率较慢，建议使用CreateTempTableCompareModified)
 * Created by XUGY on 2017-02-28.
 */
public class DefaultCompareModified implements CompareModified {
    @Override
    public Object compare(Object... objs) throws Exception {
        List<JSONObject> rifData = (List<JSONObject>) objs[0];
        List<JSONObject> saasData = (List<JSONObject>) objs[1];
        JSONObject compareFlag = (JSONObject) objs[2];

        Map<String, List<JSONObject>> resultMap = new HashMap<String, List<JSONObject>>();
        List<JSONObject> updateObjTemp = new ArrayList<JSONObject>();
        List<JSONObject> insertObjTemp = new ArrayList<JSONObject>();
        String compareStr = compareFlag.getString("compareflag");
        boolean exchangeId = compareFlag.containsKey("exchangeid") ? compareFlag.getBoolean("exchangeid") : false;

        boolean flag = false;             // 对比标记()
        for (JSONObject rifObj : rifData) // RIF数据和SAAS数据对比
        {
            flag = true;
            String rifId = rifObj.getString(compareStr);
            for (JSONObject saasObj : saasData) {
                if (rifId.equals(saasObj.getString(compareStr))) {
                    if (rifObj.compareTo(saasObj) == 0) {
                        saasData.remove(saasObj);
                    } else {
                        if (exchangeId) {
                            rifObj.put("id", saasObj.getString("id"));
                        }
                        updateObjTemp.add(rifObj);
                        saasData.remove(saasObj);
                    }
                    flag = false;
                    break;
                }
            }

            if (flag) {
                if (exchangeId) {
                    rifObj.remove("id");
                }
                insertObjTemp.add(rifObj);
            }
        }

        if (!insertObjTemp.isEmpty()) {
            resultMap.put("add", insertObjTemp);    // 没有对应数据就添加
        }
        if (!updateObjTemp.isEmpty()) {
            resultMap.put("update", updateObjTemp); // 找到对应数据但不全等,需要修改
        }
        if (!saasData.isEmpty()) {
            resultMap.put("delete", saasData);      // 没有找到对应ID
        }

        return resultMap;
    }
}
