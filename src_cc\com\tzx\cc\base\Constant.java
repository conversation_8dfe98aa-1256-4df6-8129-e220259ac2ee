package com.tzx.cc.base;

public class Constant
{
	public static final String	COMPONS_TASK_KEY		= "a_";
	// code的值
	public static final int		CODE_SUCCESS			= 0;
	public static final String	CODE_SUCCESS_MSG		= "成功";
	public static final int		CODE_PARAM_FAILURE		= 1;
	public static final String	CODE_PARAM_FAILURE_MSG	= "参数失败";
	public static final int		CODE_AUTH_FAILURE		= 2;
	public static final String	CODE_AUTH_FAILURE_MSG	= "认证失败";
	public static final int		CODE_DATA_NULL			= 3;
	public static final String	CODE_DATA_NULL_MSG		= "数据集为空";
	public static final int		CODE_CONN_EXCEPTION		= 99;
	public static final String	CODE_CONN_EXCEPTION_MSG	= "连接异常";

	public static final String	WX_TEMPLATE_TEXT_COLOR	= "#3d3d3d";
	public static final String	LAST_OPERATOR			= "admin";

	public static final Integer	STORE_ID				= 0;

	public static final String	VALID_STATE_TRUE		= "1";

	public static final String	END_DATE				= "2099-01-01";

	public static final String	VALID_STATE_FALSE		= "0";
	/**
	 * 积分清零方式 永久
	 */
	public static final String	ZREO_TYPE_FOREVER		= "forever";

	/**
	 * 积分清零方式 滚动有效
	 */
	public static final String	ZREO_TYPE_ROLLING		= "rolling";

	public static final String	OPERAT_TYPE_REGISTER1	= "register";

	/**
	 * 卡状态：正常
	 */
	public static final String	CARD_STATE_NORMAL		= "1";

	/**
	 * 卡状态：作废
	 */
	public static final String	CARD_STATE_VOID			= "2";

	/**
	 * 卡状态：挂失
	 */
	public static final String	CARD_STATE_LOSS			= "3";

	/**
	 * 卡状态：未激活
	 */
	public static final String	CARD_STATE_NOTACTIVATED	= "0";

	/**
	 * 按权重比例扣减主赠账户
	 */
	public static final String	KJ_PARAMETER_BL			= "weight";
	/**
	 * 先扣减主账户
	 */
	public static final String	KJ_PARAMETER_XZ			= "main";
	/**
	 * 先扣减赠送账户
	 */
	public static final String	KJ_PARAMETER_XS			= "reward";
	/**
	 * 四舍五入
	 */
	public static final String	JS_SSWR					= "half_adjust";
	/**
	 * 全舍去
	 */
	public static final String	JS_QS					= "rounding";
	/**
	 * 进1
	 */
	public static final String	JS_JY					= "one_adjust";

	/**
	 * operat_type 充值
	 */
	public static final String	OPERAT_TYPE_ZC			= "00";
	/**
	 * operat_type 消费
	 */
	public static final String	OPERAT_TYPE_XF			= "03";
	/**
	 * operat_type 反消费
	 */
	public static final String	OPERAT_TYPE_FXF			= "05";
	/**
	 * operat_type 充值
	 */
	public static final String	OPERAT_TYPE_CZ			= "02";
	/**
	 * operat_type 反充值
	 */
	public static final String	OPERAT_TYPE_FCZ			= "04";
	/**
	 * operat_type 补卡
	 */
	public static final String	OPERAT_TYPE_BK			= "06";
	/**
	 * operat_type 并卡转出
	 */
	public static final String	OPERAT_TYPE_BKZC		= "09";
	/**
	 * operat_type 并卡转入
	 */
	public static final String	OPERAT_TYPE_BKZR		= "08";
	/**
	 * operat_type 挂失
	 */
	public static final String	OPERAT_TYPE_GS			= "07";
	/**
	 * operat_type 退卡
	 */
	public static final String	OPERAT_TYPE_TK			= "12";

	public static final String	SMS_ALL					= "<img.*.png\">";
	public static final String  SMS_DIV 				= "</*div>";
	public static final String  SMS_NBSP 				= "&nbsp";
	public static final String  SMS_BR 					= "<br>";
	/**
	 * 短信：商户名
	 */
	public static final String	SMS_SHM					= "<img.*1.png\">";
	/**
	 * 短信：门店名称
	 */
	public static final String	SMS_MDMC				= "<img.*2.png\">";
	/**
	 * 短信：门店电话
	 */
	public static final String	SMS_MDDH				= "<img.*3.png\">";
	/**
	 * 短信：门店地址
	 */
	public static final String	SMS_MDDZ				= "<img.*4.png\">";
	/**
	 * 短信：会员名称
	 */
	public static final String	SMS_HYMC				= "<img.*5.png\">";
	/**
	 * 短信：会员等级
	 */
	public static final String	SMS_HYDJ				= "<img.*6.png\">";
	/**
	 * 短信：手机号
	 */
	public static final String	SMS_SJH					= "<img.*7.png\">";
	/**
	 * 短信：会员卡号
	 */
	public static final String	SMS_HYKH				= "<img.*8.png\">";
	/**
	 * 短信：生日
	 */
	public static final String	SMS_SR					= "<img.*9.png\">";
	/**
	 * 短信：可用积分
	 */
	public static final String	SMS_KYJF				= "<img.*10.png\">";
	/**
	 * 短信：本次交易积分
	 */
	public static final String	SMS_BCJYJF				= "<img.*11.png\">";
	/**
	 * 短信：累计消费金额
	 */
	public static final String	SMS_LJXFJE				= "<img.*12.png\">";
	/**
	 * 短信：账户余额
	 */
	public static final String	SMS_ZHYE				= "<img.*13.png\">";
	/**
	 * 短信：主账户余额
	 */
	public static final String	SMS_ZZHYE				= "<img.*14.png\">";
	/**
	 * 短信：赠送账户余额
	 */
	public static final String	SMS_ZSZHYE				= "<img.*15.png\">";
	/**
	 * 短信：本次交易金额
	 */
	public static final String	SMS_BCJYJE				= "<img.*16.png\">";
	/**
	 * 短信：本次交易主账户金额
	 */
	public static final String	SMS_BCJYZZHJE			= "<img.*17.png\">";
	/**
	 * 短信：本次交易赠送账户金额
	 */
	public static final String	SMS_BCJYZSZHJE			= "<img.*18.png\">";
	/**
	 * 短信：交易时间
	 */
	public static final String	SMS_JYSJ				= "<img.*19.png\">";
	/**
	 * 短信：订单状态
	 */
	public static final String	SMS_DDZT				= "<img.*20.png\">";
	/**
	 * 短信：订单号
	 */
	public static final String	SMS_DDH					= "<img.*21.png\">";
	/**
	 * 短信：下单时间
	 */
	public static final String	SMS_XDSJ				= "<img.*22.png\">";
	
	public static final String SYNC_DATA_FAILURE = "同步数据失败";
	public static final String SYNC_DATA_SUCCESS = "同步数据成功";
	public static final int CODE_INNER_EXCEPTION = 5; //内部错误
	public static final String BASIC_VERSION_FAILURE = "版本验证失败";
	public static final String BASIC_VERSION_SUCCESS = "版本验证成功";

}
