package com.tzx.cc.baidu.rest;

import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.cc.baidu.bo.DishService;
import com.tzx.cc.common.constant.DishOper;
import com.tzx.cc.common.constant.util.CcBusinessLogUtils;
import com.tzx.cc.eleme.log.entry.CcBusniessLogBean;
import com.tzx.cc.thirdparty.log.KafkaProducerLogUtils;
import com.tzx.cc.thirdparty.util.LogUtils;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
@Controller("DishManagementRest")
@RequestMapping("/thirdparty/dishmanagementrest")
public class DishManagementRest
{
	
	@Resource(name = DishService.NAME)
	private DishService dishService;
	
	/**
	 * 获取菜品列表
	 */
	@RequestMapping(value = "/loadDishList")
	public void loadDishList(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			
			//添加门店 权限   2016年8月12日13:53:54   xgy  begin
			String conditions = (String) session.getAttribute("user_organ_codes_group");
			obj.put("authority_organ", conditions);
			//添加门店 权限   2016年8月12日13:53:54   xgy  end
			String classID=obj.optString("class_id");
			if(classID.equals("==全部==")){
				obj.put("class_id", "");
			}
			result = dishService.loadDishList(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 * 获取价格列表
	 */
	@RequestMapping(value = "/getPrice")
	public void getPrice(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			obj.put("store_id", obj.optInt("shop_id"));
			result = dishService.getPrice(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	
	/**
	 *菜品上传
	 */
	@RequestMapping(value = "/dishCreate")
	public void dishCreate(HttpServletRequest request, HttpServletResponse response)
	{
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			UUID requestId=UUID.randomUUID();
			obj.put("requestId", requestId.toString());
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			getCcBusinessLogBean(ccBusniessLogBean, obj);
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.DishManagementRest:dishCreate");
			

			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());
			
			DBContextHolder.setTenancyid(tenantId);
			
			try{
				result = dishService.dishCreate(tenantId, obj).toString();
				ccBusniessLogBean.setResponseBody(result.toString());
			}catch(Exception e){
				ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
				ccBusniessLogBean.setIsNormal("0");
				e.printStackTrace();
				
			}
		
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 *菜品批量推送
	 */
	@RequestMapping(value = "/dishPushBatch")
	public void dishPushBatch(HttpServletRequest request, HttpServletResponse response)
	{
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			UUID requestId=UUID.randomUUID();
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("requestId", requestId.toString());
			
			String tenantId=(String) session.getAttribute("tenentid");
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			getCcBusinessLogBean(ccBusniessLogBean, obj);

			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.DishManagementRest:dishPushBatch");
			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.batchDish.toString());
			
			DBContextHolder.setTenancyid(tenantId);
			
			try{
				result = dishService.dishPushBatch(tenantId, obj).toString();
				ccBusniessLogBean.setResponseBody(result.toString());
			}catch(Exception e){
				ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
				ccBusniessLogBean.setIsNormal("0");
				e.printStackTrace();
			}
		}
		catch (Exception e)
		{
			ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
			ccBusniessLogBean.setIsNormal("0");
			e.printStackTrace();
		}
		finally
		{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	public void getCcBusinessLogBean(CcBusniessLogBean ccBusniessLogBean,
			JSONObject obj) {
		ccBusniessLogBean.setShopId(obj.optString("store_id"));
		ccBusniessLogBean.setCategory("cc");
		ccBusniessLogBean.setType("dish");
		ccBusniessLogBean.setChannel(obj.optString("channel"));
		ccBusniessLogBean.setChannelName(obj.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
		ccBusniessLogBean.setRequestBody(obj.toString());
		
		
		ccBusniessLogBean.setCreateTime(new Date().getTime());
		ccBusniessLogBean.setIsNormal("1");
		ccBusniessLogBean.setIsThird("0");
		
		//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
		if(obj.containsKey("dishes")){
		  ccBusniessLogBean.setThirdId(obj.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
		  ccBusniessLogBean.setTzxId(obj.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
		  ccBusniessLogBean.setTzxName(obj.optJSONArray("dishes").getJSONObject(0).optString("item_name"));	
		  ccBusniessLogBean.setShopId(obj.optJSONArray("dishes").getJSONObject(0).optString("store_id"));
		}else{
		  ccBusniessLogBean.setThirdId(obj.optString("third_item_id"));
		  ccBusniessLogBean.setTzxId(obj.optString("item_id"));
		  ccBusniessLogBean.setTzxName(obj.optString("item_name"));	
		  ccBusniessLogBean.setShopId(obj.optString("store_id"));
		}
	}
	
	/**
	 *根据机构id查询要推送的菜品列表
	 */
	@RequestMapping(value = "/loadDishListByStoreId")
	public void loadDishListByStoreId(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			String classID=obj.optString("class_id");
			if(classID.equals("==全部==")){
				obj.put("class_id", "");
			}
			result = dishService.loadDishListNoPage(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 *菜品批量上线
	 */
	@RequestMapping(value = "/dishOnlineBatch")
	public void dishOnlineBatch(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			
			result = dishService.dishOnlineBatch(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
		
	/**
	 *菜品批量下线
	 */
	@RequestMapping(value = "/dishOfflineBatch")
	public void dishOfflineBatch(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			
			result = dishService.dishOfflineBatch(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 *菜品修改
	 */
	@RequestMapping(value = "/dishUpdate")
	public void dishUpdate(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			Map<String, String[]> map = request.getParameterMap();
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("requestId", requestId.toString());
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);

			getCcBusinessLogBean(ccBusniessLogBean,obj);
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest.DishManagementRest:dishUpdate");
			

			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());		
			
			DBContextHolder.setTenancyid(tenantId);
			
			try{
				result = dishService.dishUpdate(tenantId, obj).toString();
				ccBusniessLogBean.setResponseBody(result.toString());
			}catch(Exception ex){
				ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(ex));
				ccBusniessLogBean.setIsNormal("0");
				ex.printStackTrace();
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			System.out.println("BD06...Controller修改菜品ID："+ccBusniessLogBean.getRequestId()+"..............");
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 *菜品上线
	 */
	@RequestMapping(value = "/dishOnline")
	public void dishOnline(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			
			result = dishService.dishOnline(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 *菜品下线
	 */
	@RequestMapping(value = "/dishOffline")
	public void dishOffline(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			
			result = dishService.dishOffline(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 *菜品批量删除
	 */
	@RequestMapping(value = "/dishDeleteBatch")
	public void dishDeleteBatch(HttpServletRequest request, HttpServletResponse response)
	{
		
		CcBusniessLogBean ccBusniessLogBean = new CcBusniessLogBean();
		UUID requestId=UUID.randomUUID();
		
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("update_operator", session.getAttribute("employeeName"));
			obj.put("update_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			obj.put("requestId", requestId.toString());
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			ccBusniessLogBean.setRequestId(requestId.toString());
			ccBusniessLogBean.setTenancyId(tenantId);
			ccBusniessLogBean.setCategory("cc");
			ccBusniessLogBean.setType("dish");
			ccBusniessLogBean.setChannel(obj.optString("channel"));
			ccBusniessLogBean.setChannelName(obj.optString("channel"));// 暂时保持原来结构不变，暂时就不去处理该字段内容值
			ccBusniessLogBean.setCmd("com.tzx.cc.baidu.rest:dishDeleteBatch");
			
			ccBusniessLogBean.setCreateTime(new Date().getTime());
			ccBusniessLogBean.setIsNormal("1");
			ccBusniessLogBean.setIsThird("0");
			
			//做一个是批量推送和时单个推送触发的事情，两种方式格式还有点不一样
			if(obj.containsKey("dishes")){
			  ccBusniessLogBean.setThirdId(obj.optJSONArray("dishes").getJSONObject(0).optString("third_item_id"));
			  ccBusniessLogBean.setTzxId(obj.optJSONArray("dishes").getJSONObject(0).optString("item_id"));
		      ccBusniessLogBean.setTzxName(obj.optJSONArray("dishes").getJSONObject(0).optString("item_name"));	
		      ccBusniessLogBean.setShopId(obj.optJSONArray("dishes").getJSONObject(0).optString("store_id"));
			}else{
			  ccBusniessLogBean.setThirdId(obj.optString("third_item_id"));
			  ccBusniessLogBean.setTzxId(obj.optString("item_id"));
		      ccBusniessLogBean.setTzxName(obj.optString("item_name"));	
			  ccBusniessLogBean.setShopId(obj.optString("store_id"));
			}

			// params参数中不包含dishes参数，就代表是批量推送，否则就是单个推送
			ccBusniessLogBean.setOperAction(DishOper.pushDish.toString());
			
			DBContextHolder.setTenancyid(tenantId);
			
			try{
				ccBusniessLogBean.setRequestBody(obj.toString());
				result = dishService.dishDeleteBatch(tenantId, obj).toString();
				ccBusniessLogBean.setResponseBody(result.toString());				
			}catch(Exception e){
				ccBusniessLogBean.setErrorBody(LogUtils.getExceptionAllinformation(e));
				ccBusniessLogBean.setIsNormal("0");
				e.printStackTrace();
				
			}
			
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			KafkaProducerLogUtils.producePerfermance(ccBusniessLogBean);
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	/**
	 *批量设置菜品佣金
	 */
	@RequestMapping(value = "/batchIsCollectCommission")
	public void batchIsCollectCommission(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("send_operator", session.getAttribute("employeeName"));
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			
			result = dishService.batchIsCollectCommission(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}

			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 *批量推送菜品项目组
	 */
	@RequestMapping(value = "/batchPushProjectTeam")
	public void batchPushProjectTeam(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();
			
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			result = dishService.batchPushProjectTeam(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"errno\":-1,\"msg\":\""+ e.getMessage() + "\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}
			
			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 *  删除菜品组
	 */
	@RequestMapping(value = "/delProjectTeam")
	public void delProjectTeam(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		InputStream in = null;
		HttpSession session = request.getSession();
		String result = "";
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");
			
			Map<String, String[]> map = request.getParameterMap();
			
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			obj.put("last_operator", session.getAttribute("employeeName"));
			obj.put("last_updatetime", DateUtil.format(new Timestamp(System.currentTimeMillis())));
			
			String tenantId=(String) session.getAttribute("tenentid");
			
			DBContextHolder.setTenancyid(tenantId);
			result = dishService.delProjectTeam(tenantId, obj).toString();
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"errno\":-1,\"msg\":\""+ e.getMessage() + "\"}";
		}
		finally
		{
			try
			{
				if (in != null)
				{
					in.close();
				}
			}
			catch (Exception e)
			{
			}
			
			try
			{
				out = response.getWriter();
				out.print(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
}
