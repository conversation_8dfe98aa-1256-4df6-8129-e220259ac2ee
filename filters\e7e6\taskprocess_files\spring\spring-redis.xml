<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:redisson="http://redisson.org/schema/redisson"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
       http://redisson.org/schema/redisson
       http://redisson.org/schema/redisson/redisson.xsd"
       default-autowire="byName">


	<!--20170810 外卖新的接单模式所需的redis配置 start-->
	<bean id="wroJedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxTotal" value="${wroRedis.pool.maxTotal}" />
        <property name="maxIdle" value="${wroRedis.pool.maxIdle}"/>
        <property name="maxWaitMillis" value="${wroRedis.pool.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${wroRedis.pool.testOnBorrow}"/>
    </bean>
	<bean id="wroJedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="hostName" value="${wroRedis.ip}"/>
        <property name="port" value="${wroRedis.port}"/>
        <property name="poolConfig" ref="wroJedisPoolConfig"/>
    </bean>
    <bean  name="wroRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="wroJedisConnectionFactory" />
    </bean>
    <!--20170810 外卖新的接单模式所需的redis配置 end-->

	<!-- spring session redis -->
    <bean id="connectionFactory" name="connectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="hostName" value="${sessionredis.ip}"/>
        <property name="port" value="${sessionredis.port}"/>
        <property name="poolConfig" ref="jedisPoolConfig"/>
    </bean>
    
    
    <!-- Single Redis Server -->
    <bean id="jedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="hostName" value="${redis.ip}"/>
        <property name="port" value="${redis.port}"/>
        <property name="poolConfig" ref="jedisPoolConfig"/>
    </bean>

    <!-- 根据[springframework]官方描述文件修改了这里,如果有问题请修改为 p:connection-factory-ref -->
    <bean id="stringRedisSerializer"  class="org.springframework.data.redis.serializer.StringRedisSerializer" />

    <!-- SAAS redis 指定了name 勿用autowire -->
    <bean  name="saasRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="jedisConnectionFactory" />
        <property name="keySerializer" ref="stringRedisSerializer" />
        <property name="hashKeySerializer" ref="stringRedisSerializer" />
    </bean>

    <!-- OM redis 指定了name 勿用autowire -->
    <bean id="omRedisTemplate" name="omRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="jedisConnectionFactory" />
        <property name="keySerializer" ref="stringRedisSerializer" />
        <property name="hashKeySerializer" ref="stringRedisSerializer" />
    </bean>

    <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxTotal" value="${redis.pool.maxTotal}" />
        <property name="maxIdle" value="${redis.pool.maxIdle}"/>
        <property name="maxWaitMillis" value="${redis.pool.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${redis.pool.testOnBorrow}"/>
    </bean>

    <!-- RedisUtil : Single Redis Server -->
    <bean id="redisUtil" class="com.tzx.framework.common.util.redis.RedisUtil">
        <property name="shardedJedisPool"  ref="shardedJedisPool"/>
        <property name="useCache"  value="${redis.useCache}" />
        <property name="defaultExpireTime"  value="${redis.defaultExpireTime}" />
    </bean>
    <bean id="jedisShardInfo" class="redis.clients.jedis.JedisShardInfo">
        <constructor-arg name="host" value="${redis.ip}" />
        <constructor-arg name="port" value="${redis.port}" type="int"/>
        <!--
        <property name="password" value="${redis.pass}" />
        -->
    </bean>
    <bean id="shardedJedisPool" class="redis.clients.jedis.ShardedJedisPool">
        <constructor-arg name="poolConfig" ref="jedisPoolConfig" />
        <constructor-arg name="shards">
            <list>
                <ref bean="jedisShardInfo" />
            </list>
        </constructor-arg>
    </bean>
    
	 <!--redission 相关配置 请勿改动 -->
    <redisson:client id="redissonClient">
    	<redisson:single-server address="redis://${redis.ip}:${redis.port}" />
    </redisson:client>

</beans>