package com.tzx.report.po.userreport.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.userreport.dao.UserReportDao;

@Repository(UserReportDao.NAME)
public class UserReportDaoImpl implements UserReportDao{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Override
	public List<JSONObject> menu(String tenancyId, JSONObject condition) throws Exception {
		
//		StringBuilder sql = new StringBuilder();
//		
////		if(sysuser!=null && "1".equals(sysuser))
////		{
////			sql.append("select id,module_name,module_type,create_date,create_person,states,module_level,father_module_id,module_link_url,module_use_img,module_class from sys_modules where  module_level in (1,2) and states='" + Constant.BOOL_TRUE + "' and (module_type is null or module_type!='pos')");
////			sql.append(" order by order_num,module_level, father_module_id, id");
//////			sql.append(" order by module_level, father_module_id, id");
////		}
////		else
////		{
//			//ua.id,
//			sql.append("select distinct (sm.id),ua.store_id,sm.module_name,sm.module_level,sm.module_link_url,sm.father_module_id,sm.if_super_module,sm.states,sm.module_use_img,sm.module_class ");
//			sql.append(",sm.order_num ");
//			sql.append("from user_authority ua join user_authority_roles uar on uar.user_id = ua.id ");
//			sql.append("join roles r on uar.roles_id = r.id ");
//			sql.append("join role_module_ref rmr on rmr.role_id = r.id ");
//			sql.append("join sys_modules sm on rmr.sys_module_id = sm.id ");
//			sql.append("where 1=1  and sm.module_level in (1,2) and sm.states='");
//			sql.append(Constant.BOOL_TRUE);
//			sql.append("'");
//			sql.append(" and (sm.module_type is null or sm.module_type!='pos')");
//			@SuppressWarnings("unchecked")
//			Set<String> keys1 = condition.keySet();
//			for(String key:keys1)
//			{
//				if("store_id".equals(key)&& !"".equals(condition.get(key).toString()))
//				{
//					sql.append(" and  ua.store_id = ");
//					sql.append(condition.get(key).toString());
//				}
//				if("id".equals(key)&& !"".equals(condition.get(key).toString()))
//				{
//					sql.append(" and  ua.id = ");
//					sql.append(condition.get(key).toString());
//				}
//			}
//			sql.append(" order by sm.order_num, sm.module_level, sm.father_module_id, sm.id");
////			sql.append(" order by sm.module_level, sm.father_module_id, sm.id");
////		}
//		List<JSONObject> list = this.dao.query4Json(tenancyId, sql.toString());
//		if(list!=null && list.size()>0){
//			return list;
//		}
//		return null;
		
		StringBuilder sql = new StringBuilder();
		sql.append("select id,module_name,module_type,create_date,create_person,states,module_level,father_module_id,module_link_url as old_url,new_url,module_link_url,new_url,module_use_img,module_class from sys_modules");
		sql.append(" where version='2.0' and states='1'");
		sql.append(" order by id");
		
		List<JSONObject> list = this.dao.query4Json(tenancyId, sql.toString());
		
		Map<String,JSONObject> lv1 = new HashMap<String,JSONObject>();
		Map<String,JSONObject> lv2 = new HashMap<String,JSONObject>();
		Map<String,JSONObject> lv3 = new HashMap<String,JSONObject>();
		Map<String,JSONObject> lv4 = new HashMap<String,JSONObject>();
		for(int i=0;i<list.size();i++){
			JSONObject obj = list.get(i);
			if(obj.optInt("module_level")==1){
				lv1.put(obj.optString("id"), obj);
			}else if(obj.optInt("module_level")==2){
				lv2.put(obj.optString("id"), obj);
			}else if(obj.optInt("module_level")==3){
				lv3.put(obj.optString("id"), obj);
			}else if(obj.optInt("module_level")==4){
				lv4.put(obj.optString("id"), obj);
			}
		}
		//4级放到3级中
		for(JSONObject obj3:lv3.values()){
			for(JSONObject obj4:lv4.values()){
				if(obj3.optInt("id")==obj4.optInt("father_module_id")){
					if(obj3.containsKey("childs")){
						obj3.getJSONArray("childs").add(obj4);
					}else{
						List<JSONObject> tmp = new ArrayList<JSONObject>();
						tmp.add(obj4);
						obj3.put("childs", tmp);
					}
				}
			}
		}
		//3级放到2级中
		for(JSONObject obj2:lv2.values()){
			for(JSONObject obj3:lv3.values()){
				if(obj2.optInt("id")==obj3.optInt("father_module_id")){
					if(obj2.containsKey("childs")){
						obj2.getJSONArray("childs").add(obj3);
					}else{
						List<JSONObject> tmp = new ArrayList<JSONObject>();
						tmp.add(obj3);
						obj2.put("childs", tmp);
					}
				}
			}
		}
		//2级放到1级中
		for(JSONObject obj1:lv1.values()){
			for(JSONObject obj2:lv2.values()){
				if(obj1.optInt("id")==obj2.optInt("father_module_id")){
					if(obj1.containsKey("childs")){
						obj1.getJSONArray("childs").add(obj2);
					}else{
						List<JSONObject> tmp = new ArrayList<JSONObject>();
						tmp.add(obj2);
						obj1.put("childs", tmp);
					}
				}
			}
		}
		if(lv1!=null && lv1.size()>0){
			List<JSONObject> tmp = new ArrayList<JSONObject>();
			for (JSONObject obj1 : lv1.values()) {
				tmp.add(obj1);
			}
			return tmp;
		}
		return null;
	}

	@Override
	public JSONObject indexTarget(String tenancyId, JSONObject condition) throws Exception {
		JSONObject json = new JSONObject();
		String OPERATING_RECEIPTS =  "select  SQL from saas_report_engine a where a.report_num = 'SAAS_MK_2017_06' and sql_type = 'YYSSJE'";  //营业实收金额
		String AMOUNT_OF_CONSUMER_BILLS = "select  SQL from saas_report_engine a where a.report_num = 'SAAS_MK_2017_06' and sql_type = 'XFZD'";   //消费账单数量
		String BILL_AVERAGE = "select  SQL from saas_report_engine a where a.report_num = 'SAAS_MK_2017_06' and sql_type = 'ZDJZ'";   //账单均值
		String NUMBER_OF_CONSUMERS = "select  SQL from saas_report_engine a where a.report_num = 'SAAS_MK_2017_06' and sql_type = 'XFKS'";   //消费客数
		String MEMBERSHIP_RECHARGE_AMOUNT = "select  SQL from saas_report_engine a where a.report_num = 'SAAS_MK_2017_06' and sql_type = 'HYCZJE'"; //会员充值金额
		String NUMBER_OF_NEW_MEMBERS = "select  SQL from saas_report_engine a where a.report_num = 'SAAS_MK_2017_06' and sql_type = 'XZHYS'";  //新增会员数
		String AMOUNT_RECEIVED_CONSUMPTION = "select  SQL from saas_report_engine a where a.report_num = 'SAAS_MK_2017_06' and sql_type = 'HYXFSSJE'"; //会员消费实收金额
		String SINGULAR_CONSUMPTION = "select  SQL from saas_report_engine a where a.report_num = 'SAAS_MK_2017_06' and sql_type = 'HYXFDSZB'"; //会员消费单数占比
		Calendar cal = Calendar.getInstance();
		Date begin_date = cal.getTime();
		cal.add(Calendar.DAY_OF_YEAR, 1);
		//Date end_date = cal.getTime();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		//condition.put("begin_date", sdf.format(begin_date));
		//condition.put("end_date", sdf.format(end_date));
		//condition.put("begin_time", sdf.format(begin_date)+" 00:00:00");
		//condition.put("end_time", sdf.format(begin_date)+" 23:59:59");
		condition.put("start_date", sdf.format(begin_date));
		condition.put("end_date", sdf.format(begin_date));
		
		
		List<JSONObject> OPERATING_RECEIPTS_LIST = this.dao.query4Json(tenancyId, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId, condition, OPERATING_RECEIPTS));
		List<JSONObject> AMOUNT_OF_CONSUMER_BILLS_LIST = this.dao.query4Json(tenancyId, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId, condition, AMOUNT_OF_CONSUMER_BILLS));
		List<JSONObject> BILL_AVERAGE_LIST = this.dao.query4Json(tenancyId, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId, condition, BILL_AVERAGE));
		List<JSONObject> NUMBER_OF_CONSUMERS_LIST = this.dao.query4Json(tenancyId, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId, condition, NUMBER_OF_CONSUMERS));
		List<JSONObject> MEMBERSHIP_RECHARGE_AMOUNT_LIST = this.dao.query4Json(tenancyId, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId, condition, MEMBERSHIP_RECHARGE_AMOUNT));
		List<JSONObject> NUMBER_OF_NEW_MEMBERS_LIST = this.dao.query4Json(tenancyId, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId, condition, NUMBER_OF_NEW_MEMBERS));
		List<JSONObject> AMOUNT_RECEIVED_CONSUMPTION_LIST = this.dao.query4Json(tenancyId, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId, condition, AMOUNT_RECEIVED_CONSUMPTION));
		List<JSONObject> SINGULAR_CONSUMPTION_LIST = this.dao.query4Json(tenancyId, parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId, condition, SINGULAR_CONSUMPTION));
		
		json.put("OPERATING_RECEIPTS", OPERATING_RECEIPTS_LIST);
		json.put("AMOUNT_OF_CONSUMER_BILLS", AMOUNT_OF_CONSUMER_BILLS_LIST);
		json.put("BILL_AVERAGE", BILL_AVERAGE_LIST);
		json.put("NUMBER_OF_CONSUMERS", NUMBER_OF_CONSUMERS_LIST);
		json.put("MEMBERSHIP_RECHARGE_AMOUNT", MEMBERSHIP_RECHARGE_AMOUNT_LIST);
		json.put("NUMBER_OF_NEW_MEMBERS", NUMBER_OF_NEW_MEMBERS_LIST);
		json.put("AMOUNT_RECEIVED_CONSUMPTION", AMOUNT_RECEIVED_CONSUMPTION_LIST);
		json.put("SINGULAR_CONSUMPTION", SINGULAR_CONSUMPTION_LIST);
		 
		return json;
	}

	@Override
	public String getNoticeInfoForHomePage(String tenancyId, JSONObject condition) throws Exception {
		long total = 0L;
		List<JSONObject> list = new ArrayList<JSONObject>();
		StringBuilder sql = new StringBuilder();
		sql.append("select s.id,s.store_id,s.notice_title,s.notice_info,s.last_operator,s.last_updatetime,s.click_record  from sys_notice_info s where 1=1  ");
		sql.append("  and s.notice_type = 6 and s.valid_state='1'");
		Set<String> keys = condition.keySet();
		for(String s:keys){
			if(!"".equals(condition.optInt(s))){
				if("store_ids".equals(s) && !"".equals(condition.optString(s))){
					sql.append(" and s.store_id in (0," + condition.optString(s) + ")");
				}
			}
		}
		sql.append(" order by s.last_updatetime desc");
		total = this.dao.countSql(tenancyId, sql.toString());
		list = this.dao.query4Json(tenancyId, this.dao.buildPageSql(condition, sql.toString()));
		
		if(list != null && list.size() > 0){
			for(int i = 0; i < list.size(); i++){
				JSONObject tmp = list.get(i);
				list.get(i).put("last_updatetime", tmp.optString("last_updatetime").split(" ")[0]);
			}
		}
		
		JSONObject result = new JSONObject();
		int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;
		result.put("page", pagenum);
		result.put("total", total);
		result.put("rows", list);
		return result.toString();
	}
	
	@Override
	public JSONObject news(String tenancyId, JSONObject condition) throws Exception {
		JSONObject result = new JSONObject();
		StringBuffer sb = new StringBuffer();
		sb.append("select id,module_code,module_name,module_pic,module_desc,module_seq from news_module where module_code in ('bigdata_canyin','news_canyin','news_product') order by module_code");
		List<JSONObject> module_list = this.dao.query4Json(tenancyId, sb.toString());

		sb.delete(0, sb.length());
		sb.append("select nl.id,nl.title,nl.sub_title,nl.content,nl.pictureurl,nl.linkurl,nl.ishidden,nl.issystem,nl.ischecked,to_char(nl.last_updatetime,'yyyy-mm-dd') last_updatetime")
		.append(" from news_module nm left join news_list nl on nm.id=nl.module_id")
		.append(" where nm.module_code = 'bigdata_canyin'")//餐饮大数据
		.append(" order by last_updatetime desc")
		.append(" limit 3");
		List<JSONObject> module1_news = this.dao.query4Json(tenancyId, sb.toString());
		
		sb.delete(0, sb.length());
		sb.append("select nl.id,nl.title,nl.sub_title,nl.content,nl.pictureurl,nl.linkurl,nl.ishidden,nl.issystem,nl.ischecked,to_char(nl.last_updatetime,'yyyy-mm-dd') last_updatetime")
		.append(" from news_module nm left join news_list nl on nm.id=nl.module_id")
		.append(" where nm.module_code = 'news_canyin'")//行业新闻
		.append(" order by last_updatetime desc")
		.append(" limit 3");
		List<JSONObject> module2_news = this.dao.query4Json(tenancyId, sb.toString());
		
		sb.delete(0, sb.length());
		sb.append("select nl.id,nl.title,nl.sub_title,nl.content,nl.pictureurl,nl.linkurl,nl.ishidden,nl.issystem,nl.ischecked,to_char(nl.last_updatetime,'yyyy-mm-dd') last_updatetime")
		.append(" from news_module nm left join news_list nl on nm.id=nl.module_id")
		.append(" where nm.module_code = 'news_product'")//产品动态
		.append(" order by last_updatetime desc")
		.append(" limit 3");
		List<JSONObject> module3_news = this.dao.query4Json(tenancyId, sb.toString());
		
		for (JSONObject jsonObject : module_list) {
			if(jsonObject.getString("module_code").equals("bigdata_canyin")){
				jsonObject.put("childs", module1_news);
			}else if(jsonObject.getString("module_code").equals("news_canyin")){
				jsonObject.put("childs", module2_news);
			}else if(jsonObject.getString("module_code").equals("news_product")){
				jsonObject.put("childs", module3_news);
			}
		}
		result.put("module_list", module_list);
		return result;
	}
	
	@Override
	public JSONObject newsDetails(String tenancyId, JSONObject condition) throws Exception {
		StringBuffer sb = new StringBuffer();
		sb.append("select id,title,sub_title,content,pictureurl,linkurl,ishidden,issystem,ischecked,publish_name,to_char(publish_date,'yyyy-mm-dd') publish_date,to_char(last_updatetime,'yyyy-mm-dd') last_updatetime,click_record from news_list where id=").append(condition.getString("news_id"));
		List<JSONObject> list = this.dao.query4Json(tenancyId, sb.toString());
		if (list.size() > 0) {
			return list.get(0);
		}
		return new JSONObject();
	}
	
	@Override
	public List<JSONObject> hotNews(String tenancyId, JSONObject condition) throws Exception {
		StringBuffer sb = new StringBuffer();
		sb.append("select id,title,sub_title,content,pictureurl,linkurl,ishidden,issystem,ischecked,publish_name,to_char(last_updatetime,'yyyy-mm-dd') last_updatetime,click_record")
		.append(" from news_list order by click_record desc limit 6");
		List<JSONObject> list = new ArrayList<JSONObject>();
		list = this.dao.query4Json(tenancyId, sb.toString());
		return list;
	}
	
}
