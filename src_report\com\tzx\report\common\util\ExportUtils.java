package com.tzx.report.common.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExportUtils {

	//下载HSSFWorkBook 
	public static void download(HSSFWorkbook workBook, HttpServletResponse response,String fileName) throws IOException {

      ByteArrayOutputStream os = new ByteArrayOutputStream();
      workBook.write(os);
      byte[] content = os.toByteArray();
      InputStream is = new ByteArrayInputStream(content);
      // 设置response参数，可以打开下载页面
      response.reset();
      response.setContentType("application/vnd.ms-excel;charset=utf-8");
      response.setHeader("Content-Disposition", "attachment;filename="
          + new String((fileName + ".xls").getBytes(), "iso-8859-1"));
      ServletOutputStream out = response.getOutputStream();
      BufferedInputStream bis = null;
      BufferedOutputStream bos = null;
 
      try {
        bis = new BufferedInputStream(is);
        bos = new BufferedOutputStream(out);
        byte[] buff = new byte[2048];
        int bytesRead;
        // Simple read/write loop.
        while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
          bos.write(buff, 0, bytesRead);
        }
      } catch (Exception e) {
        // TODO: handle exception
        e.printStackTrace();
      } finally {
        if (bis != null)
          bis.close();
        if (bos != null)
          bos.close();
      }
	
}
	
	
	public static void download03(XSSFWorkbook workBook, HttpServletResponse response,String fileName) throws IOException {

	      ByteArrayOutputStream os = new ByteArrayOutputStream();
	      workBook.write(os);
	      byte[] content = os.toByteArray();
	      InputStream is = new ByteArrayInputStream(content);
	      // 设置response参数，可以打开下载页面
	      response.reset();
	      response.setContentType("application/vnd.ms-excel;charset=utf-8");
	      response.setHeader("Content-Disposition", "attachment;filename="
	          + new String((fileName + ".xls").getBytes(), "iso-8859-1"));
	      ServletOutputStream out = response.getOutputStream();
	      BufferedInputStream bis = null;
	      BufferedOutputStream bos = null;
	 
	      try {
	        bis = new BufferedInputStream(is);
	        bos = new BufferedOutputStream(out);
	        byte[] buff = new byte[2048];
	        int bytesRead;
	        // Simple read/write loop.
	        while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
	          bos.write(buff, 0, bytesRead);
	        }
	      } catch (Exception e) {
	        // TODO: handle exception
	        e.printStackTrace();
	      } finally {
	        if (bis != null)
	          bis.close();
	        if (bos != null)
	          bos.close();
	      }
		
	}
	
	/**
	 * 创建普通的单元格样式  仅四面带有边框的
	 * @return
	 */
	private static HSSFCellStyle styleString =null;
	private static HSSFCellStyle style000 =null;
	private static HSSFCellStyle style00 =null;
	private static HSSFCellStyle style0 =null;
	private static HSSFCellStyle stylebai =null;

	private static HSSFWorkbook workBookCan =null;
	public static HSSFCellStyle createCellStyle(HSSFWorkbook workBook,String styleType) {
		
		HSSFCellStyle styleReturn= null;
		if(workBookCan==null) {
			workBookCan=workBook;
		}
		if(!workBookCan.equals(workBook)) {
			workBookCan=workBook;
			styleString=null;
			style000 =null;
			style00 =null;
			style0 =null;
			stylebai =null;
		}
		if(styleString==null&&style000==null&&style00==null&&style0==null&&stylebai==null) {
			styleString =workBook.createCellStyle();
	    	//设置前景的背景颜色
		    //style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		    //设置颜色 GREEN 可变
		    //style.setFillForegroundColor(HSSFColor.GREEN.index);
			styleString.setWrapText(true);
			styleString.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			styleString.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			styleString.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			styleString.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			
		    /*HSSFFont font =workBook.createFont();
		    //设置字体格式
		    font.setFontName("Aharoni");
		    font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		    style.setFont(font);*/
			style000 =workBook.createCellStyle();
			style000.setWrapText(true);
			style000.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			style000.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			style000.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			style000.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			style000.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00")); 
			 
			style00 =workBook.createCellStyle();
			style00.setWrapText(true);
			style00.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			style00.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			style00.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			style00.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			style00.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.0"));
			 
			style0 =workBook.createCellStyle();
			style0.setWrapText(true);   
			style0.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			style0.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			style0.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			style0.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			style0.setDataFormat(HSSFDataFormat.getBuiltinFormat("0"));
			 
			stylebai =workBook.createCellStyle();
			stylebai.setWrapText(true);
			stylebai.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			stylebai.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			stylebai.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			stylebai.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			stylebai.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00%"));
			 
		}
		if(styleType.endsWith("String")) {
			styleReturn =	styleString;
		}else if (styleType.endsWith("0.00")) {
			styleReturn =	style000;
		}else if (styleType.endsWith("0.0")) {
			styleReturn =	style00;
		}else if (styleType.endsWith("0")) {
			styleReturn =	style0;
		}else if (styleType.endsWith("0.00%")) {
			styleReturn =	stylebai;
		}
	    return styleReturn;
	}
	
	/**
	 * 
	 * @param outJson report的json
	 * @param workBook HSSFWorkbook对象
	 * @param sheet1 唯一的sheet1
	 * @param ArrHeader[0] 头名
	 * @param ArrHeader[1] 数据字段
	 * @param ArrHeader[2] 数据类型
	 * @return
	 */
	public static JSONObject out1(JSONObject outJson,HSSFWorkbook workBook,HSSFSheet sheet1,String[][] ArrHeader,JSONObject paramData) {
		  
		Integer rowNum =paramData.optInt("rowNum");
		Integer jin =paramData.optInt("jin");
		JSONObject result =new JSONObject();
		    if(paramData.optInt("jin")==0){
		    	// 单例模式
		    	HSSFCellStyle style =getTitleStyle( workBook); ;
		   
		    int  strtIndex = 0;
		    //创建下标为0的一行 就是第一行  循环赋值表头名字
		    if(paramData.containsKey("strtIndex") ) {
		    	strtIndex=paramData.optInt("strtIndex");
		    }
		    HSSFRow row1 =sheet1.createRow(strtIndex);
		    if(ArrHeader[0].length>0) {
		    	for(int y = 0;y<ArrHeader[0].length;y++) {
			    	HSSFCell cell =row1.createCell(y);
				    cell.setCellStyle(style);
				    cell.setCellValue(ArrHeader[0][y]);
				    
			    }
		    }
		    
	       // HSSFCellStyle style1 =createCellStyle(workBook);
		   	/*style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
*/		    
		    HSSFRow row2 =sheet1.createRow(rowNum++);
		    
		  	if(ArrHeader[1].length>0) {
		  		for(int c =0;c<ArrHeader[1].length;c++) {
		    		HSSFCell cell1 =row2.createCell(c);
			    	if(ArrHeader[2][c].equals("String")) {
				    	
				    	cell1.setCellValue((outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals(null)||outJson.opt(ArrHeader[1][c]).equals("")? "":outJson.optString(ArrHeader[1][c])));				    	
				    	
				    	cell1.setCellStyle(createCellStyle(workBook,"String"));
				    }else if(ArrHeader[2][c].equals("0.00")){
				    	if(outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals("")||outJson.opt(ArrHeader[1][c]).equals(null)){
				    		cell1.setCellValue("0.00");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(ArrHeader[1][c]));
				    		
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0.00"));
				    }else if (ArrHeader[2][c].equals("0.0")) {
				    	if(outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals("")||outJson.opt(ArrHeader[1][c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue( outJson.optDouble(ArrHeader[1][c]));
				    		 
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0.0"));
				    }else if (ArrHeader[2][c].equals("0")) {
				    	if(outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals("")||outJson.opt(ArrHeader[1][c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optInt(ArrHeader[1][c]));
				    		 
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0"));
				    }else if(ArrHeader[2][c].equals("0.00%")) {
				    	if(outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals("")||outJson.opt(ArrHeader[1][c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(ArrHeader[1][c]));
				    		 
				    	}
				    	cell1.setCellStyle(createCellStyle(workBook,"0.00%"));
				    }
			  }
		  	}
		   
	         
	        jin++;
		    }else {
		    	
			   //	HSSFCellStyle style1 = createCellStyle(workBook);
			   	/*style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
*/			    
			    HSSFRow row2 =sheet1.createRow(rowNum++);
			  	
			    if(ArrHeader[1].length>0) {
			    	for(int c =0;c<ArrHeader[1].length;c++) {
			    		HSSFCell cell1 =row2.createCell(c);
				    	if(ArrHeader[2][c].equals("String")) {
					    	cell1.setCellValue((outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals(null)||outJson.opt(ArrHeader[1][c]).equals("")? "":outJson.optString(ArrHeader[1][c])));				    	
					    	
					    	cell1.setCellStyle(createCellStyle(workBook,"String"));
					    }else if(ArrHeader[2][c].equals("0.00")){
					    	if(outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals("")||outJson.opt(ArrHeader[1][c]).equals(null)){
					    		cell1.setCellValue("0.00");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(ArrHeader[1][c]));
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0.00"));
					    }else if (ArrHeader[2][c].equals("0.0")) {
					    	if(outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals("")||outJson.opt(ArrHeader[1][c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue( outJson.optDouble(ArrHeader[1][c]));
					    		 
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0.0"));
					    }else if (ArrHeader[2][c].equals("0")) {
					    	if(outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals("")||outJson.opt(ArrHeader[1][c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optInt(ArrHeader[1][c]));
					    		 
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0"));
					    }else if(ArrHeader[2][c].equals("0.00%")) {
					    	if(outJson.opt(ArrHeader[1][c])==null||outJson.opt(ArrHeader[1][c]).equals("")||outJson.opt(ArrHeader[1][c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(ArrHeader[1][c]));
					    		
					    	}
					    	cell1.setCellStyle(createCellStyle(workBook,"0.00%"));
					    }
				  }
			    }
		 }
		    
			HSSFCellStyle style1 = getStyle1(workBook);
		    
		    
		    //cell1.setCellStyle(createCellStyle(workBook,"0.00%"));
		    result.put("rowNum", rowNum);
		    result.put("jin", jin);
		    return result;
	}
	
	
	
	
	/**
	 * 
	 * @param outJson report的json
	 * @param workBook HSSFWorkbook对象
	 * @param sheet1 唯一的sheet1
	 * @param List<JSONObject> 列明
	 * @return
	 * @throws Exception 
	 */
	public static Integer startNum = 0  ;
	public static JSONObject out2(String exportName,JSONObject outJson,HSSFWorkbook workBook,HSSFSheet sheet1,List<JSONObject> objs,List<JSONObject> structure,JSONObject paramData) throws Exception {
		HSSFCell cell1 = null;
		Integer rowNum =paramData.optInt("rowNum");
		
		Integer jin =paramData.optInt("jin");
		JSONObject result =new JSONObject();
		    if(paramData.optInt("jin")==0){
		    startNum = rowNum;
		    HSSFRow row2 =sheet1.createRow(rowNum++);
		    HSSFCell(cell1, objs, structure, outJson, workBook, sheet1, row2);
	        jin++;
		    }else {
		    	if(paramData.containsKey("TenThousandExcle") && paramData.optString("TenThousandExcle").equals("Yes") &&  paramData.optInt("maxExcleNum") == (rowNum - startNum) ) {
					throw new Exception("导出已达到上线  : " +paramData.optInt("maxExcleNum") ); 
				}
			    HSSFRow row2 =sheet1.createRow(rowNum++);
			    HSSFCell(cell1, objs, structure, outJson, workBook, sheet1, row2);
		 }
		    result.put("rowNum", rowNum);
		    result.put("jin", jin);
		    return result;
	}
	
	public static void HSSFCell(HSSFCell cell1,List<JSONObject> objs,List<JSONObject> structure,JSONObject outJson,HSSFWorkbook workBook,HSSFSheet sheet1,HSSFRow row2){
		if(objs.size()>0) {
	    	for(int i =0;i<objs.size();i++) {
	    		   boolean flag = false;
		  		   for(int j = 0;j<structure.size();j++){
		  			 if(outJson.size()>0){
		  			   String field = objs.get(i).getString("field");
		  			   String fieldname = structure.get(j).getString("fieldname");
		  			   String fieldtype = structure.get(j).getString("fieldtype");
		  			   if(field.equals(fieldname)){
		  				   flag = true;
		  				   cell1 =row2.createCell(i);
		  				   if(fieldtype.equals("double")){
		  					   cell1.setCellStyle(createCellStyle(workBook,"0.00")); 
		  					 if("null".equals(outJson.optString(field)) || outJson.optString(field) == null || outJson.optString(field).equals(null)|| outJson.optString(field).equals("")||outJson.optString(field).equals("0")){
						    		cell1.setCellValue("0.00");
						    	}else if(!outJson.containsKey(field)) {
						    		cell1.setCellValue("0.00");
						    	}else {
						    		cell1.setCellValue(outJson.optDouble(field));
						    	}
		  					 sheet1.setColumnWidth(i, 13 * 256);
		  				   }else if(fieldtype.equals("string") || fieldtype.equals("bpchar") || fieldtype.equals("timestamp") || fieldtype.equals("date") || fieldtype.equals("unknown")){
		  					 if(outJson.optString(field).equals("null") || outJson.optString(field).equals("")){
						    		cell1.setCellValue("");
						    	}else {
						    		if(fieldtype.equals("string")){
						    			sheet1.setColumnWidth(i, 23 * 256);
						    		}else if(fieldtype.equals("bpchar")){
						    			sheet1.setColumnWidth(i, 23 * 256);
						    		}else if(fieldtype.equals("timestamp")){
						    			sheet1.setColumnWidth(i, 31 * 256);
						    		}else if(fieldtype.equals("date")){
						    			sheet1.setColumnWidth(i, 21 * 256);
						    		}else{
						    			sheet1.setColumnWidth(i, 23 * 256);
						    		}
						    		cell1.setCellValue(outJson.optString(field));
						    	}
						     cell1.setCellStyle(createCellStyle(workBook,"String"));
		  				   }else if(structure.get(j).getString("fieldtype").equals("int")){
		  					 if(outJson.optString(field).equals("null") || outJson.optString(field).equals("")){
						    		cell1.setCellValue("0");
						    	}else {
						    		cell1.setCellValue(outJson.optInt(field));
						    	}
		  					 sheet1.setColumnWidth(i, 13 * 256);
		  					 cell1.setCellStyle(createCellStyle(workBook,"0"));
		  				  }
		  			  } 
		  		 }
		  	}
		  		   if(!flag) {
		  			 if(outJson.size()>0){
					       sheet1.setColumnWidth(i, 21 * 256);
					       cell1 =row2.createCell(i);
					       String field = objs.get(i).getString("field");
					       // 白名单字段
					       List<String> indexArr = new ArrayList<String>();
					       indexArr.add("t_");
					       indexArr.add("t");
					       indexArr.add("xl_");
					       indexArr.add("shop_real_amount");
					       // 通过前台的字段判断 当前的字段是否为付款方式字段 付款方式字段默认补0.00
					       for (String s : indexArr) {
					    	    if(field.indexOf(s) == 0 ) {
					    	    	 cell1.setCellValue("0.00");
					    	    };		  
					       }
					       cell1.setCellStyle(createCellStyle(workBook,"String"));
			  		  }
		  		   }
	    		}
             }
	      }
	
	
	public static HSSFCellStyle getStyle1(HSSFWorkbook workBook) {
		HSSFCellStyle style1 =workBook.createCellStyle();
		style1 =workBook.createCellStyle();
		style1.setWrapText(true);
	   	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
	    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
	    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
	    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
	    
	    return style1 ;
	}
	
	public static XSSFCellStyle getStyle03(XSSFWorkbook workBook) {
		XSSFCellStyle style1 =workBook.createCellStyle();
		style1 =workBook.createCellStyle();
		style1.setWrapText(true);
	   	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
	    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
	    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
	    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
	    
	    return style1 ;
	}
	public static JSONObject out03(JSONObject outJson, XSSFWorkbook workBook,
			XSSFSheet sheet1, String[] listTitleName, String[] dataName,
			String[] dataType, JSONObject paramData) {
		// TODO Auto-generated method stub

		  
		Integer rowNum =paramData.optInt("rowNum");
		Integer jin =paramData.optInt("jin");
		JSONObject result =new JSONObject();
		    if(paramData.optInt("jin")==0){
	    	XSSFCellStyle style =workBook.createCellStyle();
	    	//设置前景的背景颜色
		    //style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		    //设置颜色 GREEN 可变
		    //style.setFillForegroundColor(HSSFColor.GREEN.index);
	    	style.setWrapText(true);
		    style.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		    style.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		    style.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		    style.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
		    	
		    XSSFFont font =workBook.createFont();
		    //设置字体格式
		    font.setFontName("Aharoni");
		    font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		    style.setFont(font);
		   
		    int  strtIndex = 0;
		    //创建下标为0的一行 就是第一行  循环赋值表头名字
		    if(paramData.containsKey("strtIndex") ) {
		    	strtIndex=paramData.optInt("strtIndex");
		    }
		    XSSFRow row1 =sheet1.createRow(strtIndex);
		    if(listTitleName.length>0) {
		    	for(int y = 0;y<listTitleName.length;y++) {
			    	XSSFCell cell =row1.createCell(y);
				    cell.setCellStyle(style);
				    cell.setCellValue(listTitleName[y]);
				    
			    }
		    }
		    
	        XSSFCellStyle style1 =workBook.createCellStyle();
	        style1.setWrapText(true);
		   	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
		    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
		    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
		    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
		    
		    XSSFRow row2 =sheet1.createRow(rowNum++);
		    
		  	if(dataName.length>0) {
		  		for(int c =0;c<dataName.length;c++) {
		    		XSSFCell cell1 =row2.createCell(c);
			    	if(dataType[c].equals("String")) {
				    	cell1.setCellValue((outJson.opt(dataName[c]).equals(null)||outJson.optString(dataName[c]).equals("")? "":outJson.optString(dataName[c])));				    	
				    	style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("General")); 
				    	cell1.setCellStyle(style1);
				    }else if(dataType[c].equals("0.00")){
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("0.00");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(dataName[c]));
				    		style1 = getStyle03(workBook);
				    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00")); 
				    	}
					    cell1.setCellStyle(style1);
				    }else if (dataType[c].equals("0.0")) {
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue( outJson.optDouble(dataName[c]));
				    		style1 = getStyle03(workBook);
				    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.0")); 
				    	}
					    cell1.setCellStyle(style1);
				    }else if (dataType[c].equals("0")) {
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optInt(dataName[c]));
				    		style1 = getStyle03(workBook);
				    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0")); 
				    	}
					    cell1.setCellStyle(style1);
				    }else if(dataType[c].equals("0.00%")) {
				    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
				    		cell1.setCellValue("");
				    	}else {
				    		cell1.setCellValue(outJson.optDouble(dataName[c]));
				    		style1 = getStyle03(workBook);
				    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00%")); 
				    	}
					    cell1.setCellStyle(style1);
				    }
			  }
		  	}
		   
	         
	        jin++;
		    }else {
		    	
			   	XSSFCellStyle style1 =workBook.createCellStyle();
			   	style1.setWrapText(true);
			   	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			    style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			    style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			    style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
			    
			    XSSFRow row2 =sheet1.createRow(rowNum++);
			  	
			    if(dataName.length>0) {
			    	for(int c =0;c<dataName.length;c++) {
			    		XSSFCell cell1 =row2.createCell(c);
				    	if(dataType[c].equals("String")) {
					    	cell1.setCellValue((outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals(null)||outJson.optString(dataName[c]).equals("")? "":outJson.optString(dataName[c])));				    	
					    	style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("General")); 
					    	cell1.setCellStyle(style1);
					    }else if(dataType[c].equals("0.00")){
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("0.00");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(dataName[c]));
					    		style1 = getStyle03(workBook);
					    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00")); 
					    	}
						    cell1.setCellStyle(style1);
					    }else if (dataType[c].equals("0.0")) {
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue( outJson.optDouble(dataName[c]));
					    		style1 = getStyle03(workBook);
					    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.0")); 
					    	}
						    cell1.setCellStyle(style1);
					    }else if (dataType[c].equals("0")) {
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optInt(dataName[c]));
					    		style1 = getStyle03(workBook);
					    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0")); 
					    	}
						    cell1.setCellStyle(style1);
					    }else if(dataType[c].equals("0.00%")) {
					    	if(outJson.opt(dataName[c])==null||outJson.opt(dataName[c]).equals("")||outJson.opt(dataName[c]).equals(null)){
					    		cell1.setCellValue("");
					    	}else {
					    		cell1.setCellValue(outJson.optDouble(dataName[c]));
					    		style1 = getStyle03(workBook);
					    		style1.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00%")); 
					    	}
						    cell1.setCellStyle(style1);
					    }
				  }
			    }
		 }
		    result.put("rowNum", rowNum);
		    result.put("jin", jin);
		    return result;
	
	}
	
	/**
	 * 
	 * @param workBook work类
	 * @param sheet1  sheet 表
	 * @param payListObject  动态拼接的List<JSOBObject>
	 * @param rowtitle        要写入的行
	 * @param titleEnd1       动态拼接的开始下标
	 * @param dataName 根据那个字段划分区间   列：dataName =payment_class
	 * [{"pname":"t1","zname":"本系统卡","payment_class":"本系统卡","title":"本系统卡"}, 29  29 30 30 
  		{"pname":"t1014","zname":"美团外卖付款","payment_class":"第三方","title":"美团外卖付款"},  30  31 
   		{"pname":"t1015","zname":"美团验券支付","payment_class":"第三方","title":"美团验券支付"},
	 * @return
	 */
	 public static HSSFSheet activeGoData(HSSFWorkbook workBook,HSSFSheet sheet1,List<JSONObject> payListObject,HSSFRow rowtitle,Integer titleEnd1,String dataName) {
		String titleCan =null;
		Integer titleIndex =titleEnd1;
		Integer titleEnd =titleEnd1;
		if(payListObject.size()>0) {
		for(int cc = 0 ;cc<payListObject.size();cc++) {
			if(titleCan==null) {
				titleCan =payListObject.get(cc).optString(dataName);
				continue;
			}
			if(!titleCan.equals(payListObject.get(cc).optString(dataName))) {
				HSSFCell cellTitle5 = rowtitle.createCell(titleIndex);
				cellTitle5.setCellValue(payListObject.get(cc-1).optString(dataName));
				cellTitle5.setCellStyle(getTitleStyle(workBook));
				if(titleIndex!=titleEnd) {
					sheet1.addMergedRegion(new CellRangeAddress(0,0,titleIndex,titleEnd));
				}
				titleEnd++;
				titleIndex=titleEnd;
				titleCan=payListObject.get(cc).optString(dataName);
			}else {
				titleEnd++;
			}
		}
		}
		return sheet1;
	} 
	/**
	 *   该方法只是单纯的上下合并  不支持跨列 只支持 一列
	 * @param workBook work类
	 * @param sheet1   sheet表
	 * @param valueNum 需要第几列上下合  
	 * @param rowtitle2 要合并的数据行数  就好比取数据 
	 * @param rowtitle 要合到的数据行数  放数据   因为 合并单元格的时候  只识别左上角的数值
	 * @param heightStrat 要合并的开始行  例如  heightStrat = 0  heightEnd =1 结果合并一行
	 * @param heightEnd   结束行数
	 * @return
	 */
	public static HSSFSheet upOrDownMergr(HSSFWorkbook workBook,HSSFSheet sheet1, int[] valueNum,HSSFRow rowtitle2 , HSSFRow rowtitle,Integer heightStrat ,Integer heightEnd) {
		
			for(int value :valueNum) {
				String cellValue = rowtitle2.getCell(value).getStringCellValue();
				HSSFCell createCell = rowtitle.createCell(value);
				createCell.setCellValue(cellValue);
				createCell.setCellStyle(getTitleStyle(workBook));
				sheet1.addMergedRegion(new CellRangeAddress(heightStrat,heightEnd,value,value));
				 
			}
			return sheet1;
	}
	
	/**
	 * 获取合并的表头的titleStyle的 样式
	 * @param workBook
	 * @return
	 */
	public static HSSFCellStyle getTitleStyle(HSSFWorkbook workBook) {
		HSSFCellStyle style1 = workBook.createCellStyle(); 
		//	style1.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
			style1.setWrapText(true);
			style1.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
			style1.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
			style1.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框	
			style1.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直      
            style1.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平      	
			    HSSFFont font1 =workBook.createFont();
			    //设置字体格式
			    font1.setFontName("Aharoni");
			    font1.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
			style1.setFont(font1);
		return style1;
	}
	
	/**
	 * 
	 * @param workBook
	 * @param sheet1
	 * @param rowtitle 需要（到）写入的行类
	 * @param titleArr Json集合 json 里面 包含 titleName 头名字 index 起始列树 end 结束列数
	 * @return
	 */
	public static HSSFSheet mergrColumn (HSSFWorkbook workBook,HSSFSheet sheet1,HSSFRow rowtitle,List<JSONObject> titleArr) {
		for(JSONObject titleJson:titleArr) {
			HSSFCell cellTitle = rowtitle.createCell(titleJson.optInt("index"));
			cellTitle.setCellValue(titleJson.optString("titleName"));
			cellTitle.setCellStyle(getTitleStyle(workBook));
			sheet1.addMergedRegion(new CellRangeAddress(0,0,titleJson.optInt("index"),titleJson.optInt("end")));
		}
	return sheet1;
	}
}
