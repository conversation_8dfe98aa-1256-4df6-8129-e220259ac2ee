package com.tzx.cc.datasync.bo.util;

import com.google.gson.JsonObject;
import com.tzx.cc.datasync.bo.dto.DataTransferDaoHelper;
import com.tzx.cc.datasync.bo.dto.PlanetVersionDataTransferDao;
import com.tzx.framework.common.util.dao.datasource.MultiDataSourceManager;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by XUGY on 2017-02-24.
 */
public class TempTableUtils {
    /**
     * logger
     */
    private static final Logger logger = Logger.getLogger(TempTableUtils.class);

    /**
     *
     */
    private static ThreadLocal<Connection> CONNECTION = new ThreadLocal<Connection>();

    /**
     * @return
     */
    public static Connection getConnection() {
        return TempTableUtils.CONNECTION.get();
    }

    /**
     * @param conn
     */
    public static void setConnection(Connection conn) {
        TempTableUtils.CONNECTION.set(conn);
    }

    public static void realizeConnection(){
        Connection conn =  getConnection();
        if(conn!=null) {
            try {
                conn.close();
            } catch (SQLException e) {
                logger.error(e);
            } finally {
                conn = null;
                setConnection(null);
            }
        }
    }

    /**
     * 创建临时表根据同步源处的数据
     * @param tenantId
     * @param rifData
     * @param fromTable
     * @throws Exception
     */
    public static String createTempTable(String tenantId, List<JSONObject> rifData,
                                   String fromTable) throws Exception {

        JSONObject tablesMap = null;
        String driver = DriverUtils.getDriver();
        if (driver.equals("com.ibm.db2.jcc.DB2Driver"))
        {
            tablesMap = PlanetVersionDataTransferDao.INIT_TABLE;
        } else {
            tablesMap = DataTransferDaoHelper.INIT_TABLE;
        }

        String tempName = tablesMap.optJSONObject(fromTable).optString("totablename");
        JSONArray names = null;
        if(rifData!=null && rifData.size()>0) {
            JSONObject jsonnames = new JSONObject();
            for(JSONObject jsonObject:rifData){
                Iterator keys = jsonObject.keys();
                while(keys.hasNext()) {
                    String name = (String) keys.next();
                    jsonnames.put(name,"");
                }
            }
            names = jsonnames.names();
        }
        return createTempTable(tenantId, tempName, names);
    }

    public static void insertTempTable(String tenancyId,String tempName,List<JSONObject> list) throws SQLException {
        if(list==null || list.isEmpty()) {
            return;
        }
        StringBuffer sql = new StringBuffer();
        for(int i=0;i<list.size();i++){
            JSONObject json = list.get(i);
            if(i!=0) {
                sql.append(";");
            }
            sql.append("insert into ").append(tempName);
            StringBuffer namebuf = new StringBuffer("(");
            StringBuffer valuebuf = new StringBuffer("values (");
            Iterator iterator = json.keys();
            while(iterator.hasNext()) {
                String key = (String) iterator.next();
                String value = json.optString(key);
                namebuf.append(key).append(",");
                valuebuf.append("'").append(value).append("'").append(",");
            }
            String namebufstr = namebuf.toString();
            String vluebufstr = valuebuf.toString();
            namebufstr = namebufstr.substring(0,namebufstr.length()-1);
            vluebufstr = vluebufstr.substring(0,vluebufstr.length()-1);
            sql.append(namebufstr).append(")");
            sql.append(vluebufstr).append(")");
        }
        execute(sql.toString());
    }

    /**
     * 创建临时表  字段都为varchar(50)
     * @param tenantId
     * @param tempName
     * @param names
     * @return
     * @throws Exception
     */
    private static String createTempTable(String tenantId, String tempName,
                                   JSONArray names) throws Exception {
        tempName = tempName+new Date().getTime();
        Integer fieldlength = 50;
        StringBuffer createTableSql = new StringBuffer();
        createTableSql.append("create temp table ").append(tempName).append("(");
        for(int i=0;i<names.size();i++){
            Object fieldObject = names.get(i);
            String field = (String) fieldObject;
            createTableSql.append(" ");
            if(i!=0) {
                createTableSql.append(",");
            }

            createTableSql.append(field);
            createTableSql.append(" ");
            createTableSql.append(" varchar").append("(").append(fieldlength).append(")");
        }
        createTableSql.append(")");
        execute(createTableSql.toString());
        return tempName;
    }

    /**
     * 执行临时表执行语句
     * @param sql
     * @throws SQLException
     */
    public static void execute(String sql) throws SQLException {
        PreparedStatement pstmt = null;
        try {
            logger.info("执行临时表sql语句为"+ sql.toString());
            pstmt = MultiDataSourceManager.getPstmt(getConnection(), sql.toString());
            pstmt.execute();
        } catch (Exception e) {
            logger.error(e);
        } finally {
            if(pstmt != null) {
                pstmt.close();
            }
            pstmt = null;
        }
    }

    /**
     * 执行临时表查询语句
     * @param sql
     * @return
     * @throws SQLException
     */
    public static List<JSONObject> executeQuery(String sql) throws SQLException {
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            logger.info("执行临时表sql语句为"+ sql.toString());
            pstmt = MultiDataSourceManager.getPstmt(getConnection(), sql.toString());
            rs = pstmt.executeQuery();
            List<JSONObject> dataList = MultiDataSourceManager.parseResultSet(rs, JSONObject.class);
            return dataList;
        } catch (Exception e) {
            logger.error(e);
        } finally {
            if(pstmt != null) {
                pstmt.close();
            }
            pstmt = null;
        }
        return null;
    }

    /**
     * 创建临时表根据指定列名类型
     * @param tenancyId
     * @param tempName
     * @param columnMap  map中封装的是键为列明  值为列类型的列
     * @return 临时表名字
     * @throws Exception
     */
    public static String createTempTable(String tenancyId, String tempName,
                                   Map<String, String> columnMap) throws Exception {
        tempName = tempName+new Date().getTime();
        StringBuffer createTableSql = new StringBuffer();
        createTableSql.append("create temp table ").append(tempName).append("(");
        boolean flag = false;
        for(Map.Entry<String, String> entry:columnMap.entrySet()){
            String field = entry.getKey();
            String value = entry.getValue();

            createTableSql.append(" ");
            if(flag) {
                createTableSql.append(",");
            }
            flag = true;

            createTableSql.append(field);
            createTableSql.append(" ");
            createTableSql.append(value);
        }
        createTableSql.append(")");
        execute(createTableSql.toString());
        return tempName;
    }

    /**
     * 删除临时表
     * @param tenantId
     * @param createTempTable
     * @throws Exception
     */
    public static void dropTempTable(String tenantId, String createTempTable) throws Exception {
        String sql = "drop table " + createTempTable;
        execute(sql);
    }
}
