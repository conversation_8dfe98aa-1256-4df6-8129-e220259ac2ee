package com.tzx.cc.baidu.service;

import com.tzx.framework.common.entity.Data;

import net.sf.json.JSONObject;

/**订单接口
 * <AUTHOR>
 *
 */
@Deprecated
public interface OrderService {

	public final String NAME="com.tzx.cc.baidu.service.impl.OrderServiceImpl";
	
	/**创建订单
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject orderCreate(JSONObject jsonObject) throws Exception;
	/**订单状态查询
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject orderStatusSelect(JSONObject jsonObject) throws Exception;
	/**订单状态推送
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject orderStatusPush(JSONObject jsonObject) throws Exception;
	
}
