package com.tzx.report.po.commonreplace.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import com.google.gson.JsonArray;
import com.tzx.framework.bo.CodeService;
import com.tzx.framework.bo.dto.BasicCombobox;
import com.tzx.framework.bo.dto.Combotree;
import com.tzx.framework.bo.dto.ItemCombotree;
import com.tzx.framework.bo.dto.TreePo;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.pojo.GenericDaoTableInfo;
import com.tzx.hq.bo.dto.HqItemClass;
import com.tzx.report.common.constant.EngineConstantArea;
import com.tzx.report.common.entity.Organ;
import com.tzx.report.common.util.ConditionUtils;
import com.tzx.report.common.util.ParameterUtils;
import com.tzx.report.po.commonreplace.CommonMethodAreaDao;

@Repository
public class CommonMethodAreaDaoImpl implements CommonMethodAreaDao{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource
	ParameterUtils parameterUtils;
	
	@Resource
	ConditionUtils  conditionUtils;
	
	@Resource(name = "codeService")
	CodeService codeService;
	@Override
	public List<JSONObject> getPaymentDetails(String tenancyID, JSONObject condition) throws Exception {
		List<JSONObject> list = conditionUtils.getPaymentDetailsTree(tenancyID, condition);
		if (list.size() == 0)
		{
			JSONObject jo = new JSONObject();
			jo.put("pname", "x");
			jo.put("title", " ");
			list.add(jo);
		}
		else
		{
			for (JSONObject jo1 : list)
			{
				String zz = jo1.optString("zname");
				int len = zz.length();
				String title = "";
					for (int i = 0; i < len; i++)
					{
						title += zz.charAt(i) + "";
					}
				jo1.put("title", title);
			}
		}
		return list;
	}
	
	public  List<JSONObject> getExplain(String tenancyID, JSONObject condition) throws Exception {
		return  dao.query4Json(tenancyID, "SELECT TEXT FROM SAAS_REPORT_HELP WHERE REPORT_NUM = '"+condition.optString("report_num")+"'");
	}

	public List<JSONObject> getEnergyConsumption(String tenancyID, JSONObject condition) throws Exception {
		// TODO Auto-generated method stub

		List<JSONObject> list = conditionUtils.getEnergyConsumption(tenancyID, condition);
		if (list.size() == 0)
		{
			JSONObject jo = new JSONObject();
			jo.put("pname", "x");
			jo.put("title", " ");
			list.add(jo);
		}
		else
		{
			for (JSONObject jo1 : list)
			{
				String zz = jo1.optString("zname");
				int len = zz.length();
				String title = "";
					for (int i = 0; i < len; i++)
					{
						title += zz.charAt(i) + "";
					}
				jo1.put("title", title);
			}
		}
		return list;
	}

	@Override
	public List<JSONObject> getPaymentDetailsOrderByClass(String tenancyID, JSONObject condition) throws Exception {
		List<JSONObject> list = conditionUtils.getPaymentDetailsTreeOrderByClaas(tenancyID, condition);
		if (list.size() == 0)
		{
			JSONObject jo = new JSONObject();
			jo.put("pname", "x");
			jo.put("title", " ");
			list.add(jo);
		}
		else
		{
			for (JSONObject jo1 : list)
			{
				String zz = jo1.optString("zname");
				int len = zz.length();
				String title = "";
					for (int i = 0; i < len; i++)
					{
						title += zz.charAt(i) + "";
					}
				jo1.put("title", title);
			}
		}
		return list;
	}

	@Override
	public JSONObject getPosDinnerSnack(String tenancyID, JSONObject condition)throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		list = dao.query4Json(tenancyID,"select format_state from organ  where id  = "+condition.optInt("jg"));
		result.put("list", list);
		return result;
	}
	
	@Override
	public List<JSONObject> getPaymentDetailsStatus2(String tenancyID, JSONObject condition) throws Exception {
		List<JSONObject> list = conditionUtils.getPaymentDetailsTreeStatus2(tenancyID, condition);
		if (list.size() == 0)
		{
			JSONObject jo = new JSONObject();
			jo.put("pname", "x");
			jo.put("title", " ");
			list.add(jo);
		}
		else
		{
			for (JSONObject jo1 : list)
			{
				String zz = jo1.optString("zname");
				int len = zz.length();
				String title = "";
					for (int i = 0; i < len; i++)
					{
						title += zz.charAt(i) + "";
					}
				jo1.put("title", title);
			}
		}
		return list;
	}

	@Override
	public List<JSONObject> getTaxRate(String tenancyID, JSONObject condition)throws Exception {
//		String sql = "select * from sys_config_center where data_type = "+condition.getInt("data_type")+" order by id";
		String sql = "select * from sys_config_center order by id";
		List<JSONObject> list  = dao.query4Json(tenancyID, sql.toString());
		
		/*String sqlMaxDate = "select max(update_date) maxDate from sys_config_center "
				+ " where store_id = '"+condition.optString("store_id")+"' "
				+ " and config_code = '10102'";
		List<JSONObject> list2  = dao.query4Json(tenancyID, sqlMaxDate.toString());*/
		
		/*String sql1 = "select * from sys_config_center where 1=1 ";
		sql1+= " and store_id = '"+condition.optString("store_id")+"' ";
		sql1+= " and config_code = '10102' ";
		
		List<JSONObject> list3 = dao.query4Json(tenancyID, sql1.toString());
		list.addAll(list1);
		list.addAll(list3);*/
		return list;
	}
	
	//修改
	@Override
	public Object saveTaxRateModification(String tenancyID, String tableName,List<JSONObject> condition) throws Exception {
		return dao.updateBatchIgnorCase(tenancyID, tableName, condition);
	}

	/**
	 * 查询文件状态
	 * @param attribute
	 * @param p
	 * @return
	 */
	
	public  JSONObject selectFilesStates(String tenancyID, JSONObject p) {
		// TODO Auto-generated method stub
		 JSONObject data = new JSONObject();
		List<JSONObject> list = new ArrayList<>();
		int totalCount = 0;
		String reportSql = " select id,tenancy_id,store_id,filename,zipname,filestate,usernum from hq_export_file where 1=1 ";
		if (p.optJSONObject("fileStateList").size()>0){
			reportSql+= " and zipname in ( '"+p.optJSONObject("fileStateList").optJSONArray("list").getJSONObject(0).optString("zipname")+"')";
		}
		//reportSql+=" order by id";
		try {
			list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(p,reportSql));
			totalCount= (int) this.dao.countSql(tenancyID, reportSql);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		data.put("list", list);
		data.put("totalCount", totalCount);
		return data;
	}

	public JSONObject selectAllFilesStates(String tenancyID, JSONObject p) {
		// TODO Auto-generated method stub
		String exportName = p.optString("exportName");
		JSONObject data = new JSONObject();
		List<JSONObject> list = new ArrayList<>();
		int totalCount = 0;
		String reportSql = " select id,tenancy_id,store_id,filename,zipname,filestate,usernum from hq_export_file where 1=1 ";
		if(p.optString("exportName").length()>0){
			reportSql+= " and exportName = '"+exportName+"'";
			reportSql+= " and store_id = "+p.optInt("store_id");
		}
		//reportSql+=" order by id";
		try {
			list = this.dao.query4Json(tenancyID,this.dao.buildPageSql(p,reportSql));
			totalCount= (int) this.dao.countSql(tenancyID, reportSql);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		data.put("list", list);
		data.put("totalCount", totalCount);
		return data;
	}
	
	public List<JSONObject> selectFilesStatesById(String tenancyID, JSONObject p) {
		// TODO Auto-generated method stub
		List<JSONObject> list = new ArrayList<>();
		String reportSql = " select id,tenancy_id,store_id,filename,zipname,filestate,usernum from hq_export_file where 1=1 ";
		if (p!=null){
			reportSql+= " and id =  "+p.optInt("id")+"";
		}
		try {
			list = this.dao.query4Json(tenancyID,reportSql);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return list;
	}
	
	/**
	 * 菜品选择
	 */
	@Override
	public List<JSONObject> getCategorySelect(String tenancyId, JSONObject obj)throws Exception {
		StringBuffer sb = new StringBuffer();
		sb.append("select class_item_code as id,class_item as text from sys_dictionary where class_identifier_code = 'marketingtype' and valid_state = '1' ");
		List<JSONObject> biglist = dao.query4Json(tenancyId, sb.toString());
		//新增 默认类别
		JSONObject dofultClass = new JSONObject();
		dofultClass.put("id", "00");
		dofultClass.put("text", "默认餐厅");
		biglist.add(0,dofultClass);
		return biglist;
	}
	
	
	/**
	 * 菜品分类
	 */
	@Override
	public String loadCategoryTree(String tenancyId, JSONObject obj)throws Exception {
		StringBuffer sb = new StringBuffer();
		sb.append("select id,class_item as text,class_item_code,class_identifier_code,class_identifier,valid_state,1 as type from sys_dictionary where class_identifier_code = 'marketingtype' and valid_state = '1' and class_item_code = '"+obj.getString("id")+"'");
		List<JSONObject> biglist = dao.query4Json(tenancyId, sb.toString());
		
		sb.setLength(0);
		sb.append(" select id,category_name as text,category_code,category_father_id,category_father_code,category_father_name,valid_state,remark,last_operator,last_updatetime,category_level as type  from hq_item_category_class where valid_state = '1'  ORDER BY id ASC");
		List<JSONObject> smalllist = dao.query4Json(tenancyId, sb.toString());

		// 菜品类别分类管理中,类别树仅加载到第二级，无需with_sub参数
		// 菜品档案菜单，分配类别时，请求参数with_sub==1，需要加载第三级菜品类别到树结构中
		if (obj.optInt("with_sub") == 1) {
			if (null != smalllist && smalllist.size() > 0) {
				for (JSONObject small : smalllist) {
					int small_id = small.optInt("id");
					for (JSONObject smallSub : smalllist) {
						int sub_father_id = smallSub.optInt("category_father_id");
						if (small_id == sub_father_id) {
							if (small.containsKey("children")) {
								JSONArray arr = small.optJSONArray("children");
								arr.add(smallSub);
								small.put("children", arr);
							} else {
								JSONArray arr = new JSONArray();
								arr.add(smallSub);
								small.put("children", arr);
							}
						}
					}
				}
			}
		/*	// 移除没有孩子的小类
			Iterator<JSONObject> it = smalllist.iterator();
			while (it.hasNext()) {
				JSONObject next = it.next();
				if (!next.containsKey("children") || next.optJSONArray("children").size() == 0) {
					it.remove();
				}
			}*/
		}
		if(null != biglist && biglist.size()>0){
			for (JSONObject big : biglist) {
				int big_id = big.optInt("id");
				for(JSONObject small : smalllist){
					int father_id = small.optInt("category_father_id");
					if(big_id == father_id){
						if(big.containsKey("children")){
							JSONArray arr = big.optJSONArray("children");
							arr.add(small);
							big.put("children", arr);
						}else{
							JSONArray arr = new JSONArray();
							arr.add(small);
							big.put("children", arr);
						}
					}
				}
			}
			/*// 移除没有孩子的大类
			if (obj.optInt("with_sub") == 1) {
				Iterator<JSONObject> its = biglist.iterator();
				while (its.hasNext()) {
					JSONObject next = its.next();
					if (!next.containsKey("children") || next.optJSONArray("children").size() == 0) {
						its.remove();
					}
				}
			}*/
		}
		return biglist.toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public String customize(String tenancyId, Integer type, Object param) throws Exception
	{
		String result = "";
		JSONObject jb = (JSONObject) param;
		// int pagenum = jb.containsKey("page") ? (jb.getInt("page") == 0 ? 1 :
		// jb.getInt("page")) : 1;
		// int limit = jb.containsKey("rows") ? jb.getInt("rows") : 20;
		// long total = 0;
		switch (type)
		{
		// 查询菜品渠道类别树状图
			case 0:
				try
				{
					StringBuilder sql = new StringBuilder();

					sql.append("select 0 as id,0 as father_id,0 as num,class_item_code as chanel,class_item_code as code ,class_item,class_item as itemclass_name,class_item as text from sys_dictionary where class_identifier_code = 'chanel'");
					sql.append(" and class_item_code ='MD01'");
					sql.append(" order by chanel");

					List<HqItemClass> listDict = (List<HqItemClass>) dao.query(tenancyId, sql.toString(), HqItemClass.class);

					sql.delete(0, sql.length());

					sql.append("select a.id,a.chanel,a.father_id,a.itemclass_name,a.itemclass_code, a.itemclass_code as code, (select count(*) from hq_item_info info where info.item_class=a.id ");//and info.valid_state='1'
					if(jb.containsKey("classtype") && jb.optInt("classtype") == 1){
						sql.append(" and info.is_combo ='N'");
					}else if(jb.containsKey("classtype") && jb.optInt("classtype") == 2){
						sql.append(" and info.is_combo ='Y'");
					}
					sql.append("  ) as num, b.class_item as class_item from hq_item_class a ");
					sql.append(" join sys_dictionary b on (b.class_item_code=a.chanel and b.class_identifier_code='chanel') ");
					sql.append(" where 1=1  "); //and a.valid_state='1'
					sql.append(" and a.chanel ='MD01'");

					sql.append(" order by itemclass_code");

					List<HqItemClass> listRes = (List<HqItemClass>) dao.query(tenancyId, sql.toString(), HqItemClass.class);

					Map<Object, HqItemClass> map = new HashMap<Object, HqItemClass>();

					for (HqItemClass json : listRes)
					{
						json.setChildren(new ArrayList<HqItemClass>());
						map.put(json.getId(), json);
					}
					// 设置上下级关系
					for (HqItemClass json : listRes)
					{
						String itemclassCode = json.getItemclassCode();
						if ("".equals(itemclassCode))
						{
							json.setText(json.getItemclassName());
						}
						else
						{
							json.setText("(" + json.getItemclassCode() + ")" + json.getItemclassName());
						}

						if (map.containsKey(json.getFatherId()))
						{
							List<HqItemClass> l1 = map.get(json.getFatherId()).getChildren();
							l1.add(json);
						}
					}

					for (HqItemClass gO : listDict)
					{
						gO.setChildren(new ArrayList<HqItemClass>());
						for (HqItemClass j1 : listRes)
						{

							if (j1.getFatherId().equals(0) && j1.getChanel().equals(gO.getChanel()))
							{

								List<HqItemClass> l2 = gO.getChildren();
								l2.add(j1);
							}

						}

					}

					result = com.tzx.framework.common.util.JsonUtils.list2json(listDict);

				}
				catch (Exception e)
				{

					e.printStackTrace();
				}

				break;
			// 查询菜品渠道类别树状图
			case 1:
				List<Combotree> list11 = new ArrayList<Combotree>();
				TreePo temp = new TreePo();
				temp.setId(0);
				temp.setUpId(-1);
				temp.setName("POS");
				temp.setFind("MD01");
				temp.setType("MD01");

				StringBuilder sb11 = new StringBuilder("select tenancy_id,id,CONCAT(itemclass_code,itemclass_name) as text,father_id,chanel as type from hq_item_class where 1=1 ");
				sb11.append(" and valid_state='1' order by itemclass_code");
				try
				{
					List<Combotree> list2 = (List<Combotree>) this.dao.query(tenancyId, sb11.toString(), Combotree.class);
					Map<Integer, Combotree> map = new HashMap<Integer, Combotree>();
					for (Combotree jo2 : list2)
					{

						jo2.setChildren(new ArrayList<Combotree>());
						map.put(jo2.getId(), jo2);
						if (jo2.getFatherId() != null && jo2.getFatherId() == 0)
						{

							if (temp.getType().equals(jo2.getType()))
							{
								jo2.setType(temp.getName());
								list11.add(jo2);
							}
						}
					}
					for (Combotree jo3 : list2)
					{
						if (jo3.getFatherId() != null && jo3.getFatherId() != 0)
						{
							if (map.get(jo3.getFatherId()) != null)
							{
								map.get(jo3.getId()).setType(map.get(jo3.getFatherId()).getType());
								map.get(jo3.getFatherId()).getChildren().add(map.get(jo3.getId()));
							}
						}
					}
					Iterator<Combotree> it1 = list11.iterator();
					while (it1.hasNext())
					{
						Combotree toPo = it1.next();
						if (toPo.getChildren().size() == 0)
						{
							it1.remove();
						}
					}

					result = com.tzx.framework.common.util.JsonUtils.list2json(list11);
				}
				catch (Exception e)
				{

					e.printStackTrace();
				}

				break;
			// 新增查询价格体系 { "店面,MD01", "微信,WX02", "网站/app,WZ03",
			// "CC,CC04","其他,QT05" };
			case 2:

				try
				{
					Double val = jb.optDouble("value", 0.0);
					StringBuilder sBuilder = new StringBuilder("select id,price_system_name from hq_price_system where 1=1 and valid_state='" + Constant.BOOL_TRUE + "'");

					List<JSONObject> list55 = this.dao.query4Json(tenancyId, sBuilder.toString());

					sBuilder.setLength(0);
					sBuilder.append("select class_item_code  from sys_dictionary where class_identifier_code='chanel' and valid_state='1'");
					List<JSONObject> chanel = this.dao.query4Json(tenancyId, sBuilder.toString());
					for (JSONObject jo : list55)
					{
						for (JSONObject joc : chanel)
						{
							String chanela = joc.optString("class_item_code");
							jo.put(chanela, val);
						}
					}

					result = com.tzx.framework.common.util.JsonUtils.list2json(list55);

				}
				catch (Exception e)
				{
					e.printStackTrace();
				}
				break;
			// 查询套餐用到的菜品
			case 3:
				// 查询菜品渠道类别树状图
				try
				{
					if (jb.optInt("id") == 0)
					{
						StringBuilder sb33 = new StringBuilder("select id,class_item as text,class_item_code as code,0 as type from sys_dictionary where class_identifier_code = 'chanel'  and class_item_code='MD01'");
						List<ItemCombotree> listcp = (List<ItemCombotree>) this.dao.query(tenancyId, sb33.toString(), ItemCombotree.class);
						sb33.delete(0, sb33.length());

						sb33 = new StringBuilder("select a.id,a.itemclass_name as text,a.father_id,a.chanel as code,0 as type,(select case when count(b.id)>0 then 'closed' else '' end  from hq_item_info b where b.item_class = a.id and b.valid_state='" + Constant.BOOL_TRUE
								+ "' ) as state from hq_item_class  a where 1=1 and a.valid_state='" + Constant.BOOL_TRUE + "'");

						List<ItemCombotree> list2 = (List<ItemCombotree>) this.dao.query(tenancyId, sb33.toString(), ItemCombotree.class);
						Map<Integer, ItemCombotree> map = new HashMap<Integer, ItemCombotree>();
						// 第一层
						for (ItemCombotree jo2 : list2)
						{

							jo2.setChildren(new ArrayList<ItemCombotree>());
							map.put(jo2.getId(), jo2);
							if (jo2.getFatherId() != null && jo2.getFatherId() == 0)
							{
								for (ItemCombotree icb : listcp)
								{
									if (jo2.getCode().equals(icb.getCode()))
									{
										icb.getChildren().add(jo2);
									}
								}

							}
						}
						// 后面的层
						for (ItemCombotree jo3 : list2)
						{
							if (jo3.getFatherId() != null && jo3.getFatherId() != 0)
							{
								if (map.get(jo3.getFatherId()) != null)
								{
									map.get(jo3.getFatherId()).getChildren().add(map.get(jo3.getId()));
								}
							}
						}

						Iterator<ItemCombotree> it2 = listcp.iterator();
						StringBuilder ids = new StringBuilder("null");
						// 去掉非没有小类的大类的 如果是小类的话 拼id串
						while (it2.hasNext())
						{
							ItemCombotree toPo = it2.next();
							if (toPo.getChildren().size() == 0)
							{
								it2.remove();
							}
							else
							{
								ids.append("," + toPo.getId());
							}
						}
						if (ids.length() > 5)
						{
							ids.delete(0, 5);
						}
						result = com.tzx.framework.common.util.JsonUtils.list2json(listcp);
					}
					else
					{
						StringBuilder sb333 = new StringBuilder();
						// 查询菜品详细

						sb333.append("select b.id,b.item_code,b.item_name as text,a.unit_name,b.item_class as fatherId,a.unit_name,a.standard_price,1 as type,a.id as item_unit_id  from hq_item_unit a left join hq_item_info b on a.item_id = b.id where a.item_id in(select c.id from hq_item_info c where 1=1 and c.valid_state='1' and c.is_combo='N' and c.item_class="
								+ jb.optInt("id") + ") and  a.valid_state='1' ");
						// sb33.append("select h1.id,CONCAT(item_code,item_name)as text,item_class as fatherId,h2.unit_name,standard_price,1 as type,h2.id as item_unit_id from hq_item_info  h1 left join hq_item_unit h2 on h1.id=h2.item_id and h2.tenancy_id = '"
						// + tenancyId +
						// "' and h2.valid_state ='1' where  h1.tenancy_id = '"
						// + tenancyId + "' and h1.valid_state ='1'");

						List<JSONObject> list13 = this.dao.query4Json(tenancyId, sb333.toString());
						result = com.tzx.framework.common.util.JsonUtils.list2json(list13);
					}

				}
				catch (Exception e)
				{

					e.printStackTrace();
				}

				break;
			// 项目组
			case 4:
				StringBuilder sb33 = new StringBuilder("select id,item_group_code as item_code,item_group_name as text,item_group_price as standard_price,1 as type,'组' as unit_name,0 as item_unit_id from hq_item_group where 1=1 and valid_state = '1' order by  item_group_code asc");
				try
				{
					List<JSONObject> list13 = this.dao.query4Json(tenancyId, sb33.toString());

					result = com.tzx.framework.common.util.JsonUtils.list2json(list13);

				}
				catch (Exception e)
				{
					// TODO Auto-generated catch block
					e.printStackTrace();
				}

				break;

			// 修改用到的 查询菜品价格
			case 5:
				StringBuilder sb55 = new StringBuilder("select id,id as zid,unit_name,is_default,standard_price,valid_state from hq_item_unit where item_id = " + jb.getInt("id") + " and 1=1 and valid_state='1' ");
				try
				{
					List<JSONObject> list55 = this.dao.query4Json(tenancyId, sb55.toString());

					result = com.tzx.framework.common.util.JsonUtils.list2json(list55);

				}
				catch (Exception e)
				{
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				break;

			// 修改用到的查询菜品做法
			case 6:

				StringBuilder sb66 = new StringBuilder(
						"select a.method_name_id,a.makeup_way,a.proportion_money,a.is_default,a.valid_state,(select class_item from sys_dictionary where id = a.method_name_id) as zf,c.class_item as jjfs from hq_item_method a LEFT JOIN sys_dictionary b on a.method_name_id = b.id left join  sys_dictionary c on a.makeup_way = c.class_item_code and c.class_identifier_code = 'practice_addition' where  a.item_id ="
								+ jb.getInt("id") + "  and 1=1 ");
				try
				{
					List<JSONObject> list66 = this.dao.query4Json(tenancyId, sb66.toString());

					result = com.tzx.framework.common.util.JsonUtils.list2json(list66);

				}
				catch (Exception e)
				{
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				break;

			// 修改用到的套餐明细
			case 7:

				StringBuilder sb77 = new StringBuilder(
						"select a.item_unit_id,a.is_itemgroup,a.details_id,a.combo_num,a.standardprice,a.combo_order,'组' as zunit,b.item_group_name as zname,d.item_name as iname,c.unit_name as iunit,(case when is_itemgroup='Y' then  b.item_group_price ELSE  c.standard_price end ) as standardprice1 from hq_item_combo_details a LEFT JOIN hq_item_group b  on a.details_id=b.id and a.is_itemgroup='Y'  LEFT JOIN hq_item_unit c on a.item_unit_id =c.id  LEFT JOIN hq_item_info d on a.details_id=d.id  where a.iitem_id="
								+ jb.getInt("id"));

				try
				{
					List<JSONObject> list77 = this.dao.query4Json(tenancyId, sb77.toString());

					for (JSONObject jo : list77)
					{
						Integer num = jo.optInt("combo_num");
						Double standardprice1 = jo.optDouble("standardprice1", 0);
						Double ns = standardprice1 * num;
						jo.put("standardprice1", Scm.pround(ns));
					}

					result = com.tzx.framework.common.util.JsonUtils.list2json(list77);

				}
				catch (Exception e)
				{
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				break;

			// 查询修改用的价格体系
			case 8:

				StringBuilder sb88 = new StringBuilder("select price_system,chanel,price from hq_item_pricesystem  where item_unit_id =" + jb.get("zid") + " and price_system in (select id from hq_price_system where valid_state='1') ");

				try
				{
					List<JSONObject> list88 = this.dao.query4Json(tenancyId, sb88.toString());

					String val = jb.get("value").toString();
					sb88.delete(0, sb88.length());
					sb88.append("select id,price_system_name from hq_price_system where 1=1  and valid_state='1' ");

					List<JSONObject> list55 = this.dao.query4Json(tenancyId, sb88.toString());

					sb88.setLength(0);
					sb88.append("select class_item_code  from sys_dictionary where class_identifier_code='chanel' and valid_state='1'");
					List<JSONObject> chanel = this.dao.query4Json(tenancyId, sb88.toString());
					for (JSONObject jo : list55)
					{
						for (JSONObject joc : chanel)
						{
							String chanela = joc.optString("class_item_code");
							jo.put(chanela, val);
						}
					}

					for (JSONObject jo5 : list55)
					{
						for (JSONObject jo8 : list88)
						{
							if (jo5.getInt("id") == jo8.getInt("price_system") && jo5.containsKey(jo8.get("chanel").toString()))
							{
								jo5.put(jo8.get("chanel").toString(), jo8.optDouble("price", 0.0));

							}
						}
					}

					result = com.tzx.framework.common.util.JsonUtils.list2json(list55);

				}
				catch (Exception e)
				{
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				break;
			// 停用
			case 9:
				StringBuilder sb99 = new StringBuilder();
				sb99.append("select concat(b.item_group_code,' ',b.item_group_name) as name  from hq_item_group_details a LEFT JOIN hq_item_group b on a.item_group_id = b.id where b.valid_state = '1' and a.item_id=" + jb.get("id"));
				List<JSONObject> list91 = this.dao.query4Json(tenancyId, sb99.toString());
				JSONObject re = new JSONObject();
				re.put("success", false);
				if (list91 != null && list91.size() > 0)
				{
					String name = list91.get(0).optString("name");
					re.put("msg", "已在项目组：" + name + "中分配,需先停用该项目组");
					result = re.toString();
					break;
				}
				sb99.setLength(0);
				// 只查询套餐当中非项目组的 项目的单独在项目组停用时会判断
				sb99.append("select concat(hi.item_code,' ',hi.item_name) as name from hq_item_combo_details hd  LEFT JOIN hq_item_info hi on hd.iitem_id = hi.id where hi.valid_state='1' and hd.is_itemgroup='N' and  hd.details_id=" + jb.get("id"));
				List<JSONObject> list92 = this.dao.query4Json(tenancyId, sb99.toString());

				if (list92 != null && list92.size() > 0)
				{
					String name = list92.get(0).optString("name");
					re.put("msg", "已在套餐：" + name + "中分配,需先停用该套餐");
					result = re.toString();
					break;
				}
				sb99.setLength(0);
				sb99.append("select concat(hm.item_menu_code,' ',hm.item_menu_name) as name from hq_item_menu_details hd LEFT JOIN hq_item_menu hm on hd.item_menu_id = hm.id LEFT JOIN hq_item_menu_class himc on hd.id=himc.details_id where hm.valid_state='1' and himc.id>0 and himc.chanel='MD01' and hd.item_id=" + jb.get("id"));
				List<JSONObject> list93 = this.dao.query4Json(tenancyId, sb99.toString());

				if (list93 != null && list93.size() > 0)
				{
					String name = list93.get(0).optString("name");
					re.put("msg", "已在餐谱：" + name + "中分配,需先停用该餐谱");
					result = re.toString();
					break;
				}

				this.dao.execute(tenancyId, "UPDATE hq_item_info  SET valid_state = '0' WHERE id=" + jb.get("id"));
				result = "{\"success\": true}";
				break;

			// 快速生成餐谱按钮
			case 10:
				StringBuilder sb10 = new StringBuilder();
				JSONObject pram = JSONObject.fromObject("{}");
				pram.put("store_id", 0);
				String code = codeService.getCode(tenancyId, Code.HQ_ITEM_MENU_CODE, pram);
				jb.put("item_menu_code", code);
				jb.put("valid_state", Constant.BOOL_TRUE);
				jb.put("entry_person", jb.getString("last_operator"));
				jb.put("entry_time", jb.getString("last_updatetime"));

				int item_menu_id = (Integer) this.dao.insertIgnorCase(tenancyId, "hq_item_menu", jb);
				
				String startdate = jb.optString("startdate");
				if(startdate.length()!=10)
				{
					startdate = DateUtil.getNowDateYYDDMMTomorrow();
				}
				List<JSONObject> listhq_item_menu_organ = new ArrayList<JSONObject>();
				String organIds = jb.optString("organIds"); 
				String[] store_ids = organIds.split(",");
				String last_operator = jb.getString("last_operator");
				for (String id : store_ids)
				{
					JSONObject jo = JSONObject.fromObject("{}");
					jo.put("tenancy_id", tenancyId);
					jo.put("item_menu_id", item_menu_id);
					jo.put("store_id", Integer.parseInt(id));
					jo.put("running_state","1");
					jo.put("start_date" , startdate);
					jo.put("start_person" , last_operator);
					
					listhq_item_menu_organ.add(jo);
				}
				if(organIds.length()>0)
				{
			
					this.dao.execute(tenancyId,"delete from hq_item_menu_organ_task where store_id in("+organIds+") and start_date='"+startdate+"' ");
				}
				
				this.dao.insertBatchIgnorCase(tenancyId, "hq_item_menu_organ_task", listhq_item_menu_organ);
				this.dao.insertBatchIgnorCase(tenancyId, "hq_item_menu_organ_task_list", listhq_item_menu_organ);
				Map<Integer, JSONObject> map10 = new HashMap<Integer, JSONObject>();
				sb10.append("select id,item_name,phonetic_code,five_code,item_class from hq_item_info where item_class in(select id from hq_item_class where chanel = 'MD01' and valid_state = '" + Constant.BOOL_TRUE + "') and valid_state='" + Constant.BOOL_TRUE + "'");
				List<JSONObject> listItems = this.dao.query4Json(tenancyId, sb10.toString());
				//String starttime = jb.getString("startdate") + " " + jb.getString("starttime");
				String valid_state = Constant.BOOL_TRUE;
				
				String last_updatetime = jb.getString("last_updatetime");
				List<JSONObject> listhq_item_menu_details = new ArrayList<JSONObject>();
				for (JSONObject jo2 : listItems)
				{
					JSONObject jo22 = JSONObject.fromObject("{}");
					jo22.put("tenancy_id", tenancyId);
					jo22.put("item_menu_id", item_menu_id);
					jo22.put("item_id", jo2.getInt("id"));
					jo22.put("starttime", "00:00:00");
					jo22.put("endtime", "23:59:59");
					jo22.put("valid_state", valid_state);
					jo22.put("last_updatetime", last_updatetime);
					jo22.put("last_operator", last_operator);
					jo22.put("is_show", 1);
					listhq_item_menu_details.add(jo22);
					map10.put(jo2.getInt("id"), jo2);

				}

				this.dao.insertBatchIgnorCase(tenancyId, "hq_item_menu_details", listhq_item_menu_details);

				sb10.delete(0, sb10.length());
				sb10.append("select id,item_id from hq_item_menu_details where item_menu_id =" + item_menu_id);

				List<JSONObject> newhq_item_menu_details = this.dao.query4Json(tenancyId, sb10.toString());
				List<JSONObject> listhq_item_menu_class = new ArrayList<JSONObject>();
				for (JSONObject newDetails : newhq_item_menu_details)
				{
					if (map10.containsKey(newDetails.getInt("item_id")))
					{
						JSONObject item = map10.get(newDetails.getInt("item_id"));
						JSONObject jo33 = JSONObject.fromObject("{}");
						jo33.put("tenancy_id", tenancyId);
						jo33.put("details_id", newDetails.getInt("id"));
						jo33.put("chanel", "MD01");
						jo33.put("class", item.getInt("item_class"));
						jo33.put("item_name", item.getString("item_name"));
						jo33.put("phonetic_code", item.getString("phonetic_code"));
						jo33.put("five_code", item.getString("five_code"));

						listhq_item_menu_class.add(jo33);

					}
				}

				this.dao.insertBatchIgnorCase(tenancyId, "hq_item_menu_class", listhq_item_menu_class);
				result = "{\"success\": true,\"id\":" + item_menu_id + "}";
				break;
			// 快速生存餐谱并编辑 查询餐谱管理的url id name
			case 11:
				StringBuilder sbs11 = new StringBuilder();
				sbs11.append("select id,module_name as name,module_link_url as href from sys_modules where module_name='餐谱管理'");
				List<JSONObject> list = this.dao.query4Json(tenancyId, sbs11.toString());
				if (list != null && list.size() > 0)
				{
					JSONObject jo1 = list.get(0);
					jo1.put("success", true);
					result = jo1.toString();

				}
				else
				{
					result = "{\"success\": false,\"msg\":'没有找到相关模块'}";
				}

				break;
			case 12:
				StringBuilder sbs12 = new StringBuilder();
				sbs12.append("select id,item_menu_code as code,item_menu_name as name from hq_item_menu where id=" + jb.get("mid"));

				List<JSONObject> listmenu = this.dao.query4Json(tenancyId, sbs12.toString());
				if (listmenu != null && listmenu.size() > 0)
				{
					result = listmenu.get(0).toString();
				}
				else
				{
					result = "{}";
				}
				break;
			// 查询套餐用到的菜品
			case 13:
				// 查询菜品渠道类别树状图
				try
				{
					if (jb.optInt("id") == 0)
					{
						StringBuilder sb133 = new StringBuilder("select id,class_item as text,class_item_code as code,0 as type from sys_dictionary where class_identifier_code = 'chanel'  and class_item_code='MD01'");
						List<ItemCombotree> listcp = (List<ItemCombotree>) this.dao.query(tenancyId, sb133.toString(), ItemCombotree.class);
						sb133.delete(0, sb133.length());

						sb133 = new StringBuilder("select a.id,a.itemclass_name as text,a.father_id,a.chanel as code,0 as type,(select case when count(b.id)>0 then 'closed' else '' end  from hq_item_info b where b.item_class = a.id and b.valid_state='" + Constant.BOOL_TRUE
								+ "' ) as state from hq_item_class  a where 1=1 and a.valid_state='" + Constant.BOOL_TRUE + "'");

						List<ItemCombotree> list2 = (List<ItemCombotree>) this.dao.query(tenancyId, sb133.toString(), ItemCombotree.class);
						Map<Integer, ItemCombotree> map = new HashMap<Integer, ItemCombotree>();
						// 第一层
						for (ItemCombotree jo2 : list2)
						{

							jo2.setChildren(new ArrayList<ItemCombotree>());
							map.put(jo2.getId(), jo2);
							if (jo2.getFatherId() != null && jo2.getFatherId() == 0)
							{
								for (ItemCombotree icb : listcp)
								{
									if (jo2.getCode().equals(icb.getCode()))
									{
										icb.getChildren().add(jo2);
									}
								}

							}
						}
						// 后面的层
						for (ItemCombotree jo3 : list2)
						{
							if (jo3.getFatherId() != null && jo3.getFatherId() != 0)
							{
								if (map.get(jo3.getFatherId()) != null)
								{
									map.get(jo3.getFatherId()).getChildren().add(map.get(jo3.getId()));
								}
							}
						}

						Iterator<ItemCombotree> it2 = listcp.iterator();
						StringBuilder ids = new StringBuilder("null");
						// 去掉非没有小类的大类的 如果是小类的话 拼id串
						while (it2.hasNext())
						{
							ItemCombotree toPo = it2.next();
							if (toPo.getChildren().size() == 0)
							{
								it2.remove();
							}
							else
							{
								ids.append("," + toPo.getId());
							}
						}
						if (ids.length() > 5)
						{
							ids.delete(0, 5);
						}
						result = com.tzx.framework.common.util.JsonUtils.list2json(listcp);
					}
					else
					{
						StringBuilder sb333 = new StringBuilder();
						// 查询菜品详细

						sb333.append("select b.id,b.item_code,b.item_name as text,a.unit_name,b.item_class as fatherId,a.unit_name,a.standard_price,1 as type,a.id as item_unit_id  from hq_item_unit a left join hq_item_info b on a.item_id = b.id where a.item_id in(select c.id from hq_item_info c where 1=1 and c.valid_state='1' and c.item_class="
								+ jb.optInt("id") + ") and  a.valid_state='1' ");
						// sb33.append("select h1.id,CONCAT(item_code,item_name)as text,item_class as fatherId,h2.unit_name,standard_price,1 as type,h2.id as item_unit_id from hq_item_info  h1 left join hq_item_unit h2 on h1.id=h2.item_id and h2.tenancy_id = '"
						// + tenancyId +
						// "' and h2.valid_state ='1' where  h1.tenancy_id = '"
						// + tenancyId + "' and h1.valid_state ='1'");

						List<JSONObject> list13 = this.dao.query4Json(tenancyId, sb333.toString());
						result = com.tzx.framework.common.util.JsonUtils.list2json(list13);
					}

				}
				catch (Exception e)
				{

					e.printStackTrace();
				}

				break;
				
			case 15:
				JSONObject returnJson = new JSONObject();
				String item_name = jb.optString("item_name");
				String third_code = jb.optString("third_code");
				String rowid = jb.optString("id");
				StringBuffer sbcheck = new StringBuffer();
				sbcheck.append("select * from hq_item_info where id <> '"+rowid+"' and valid_state = '1' and (item_name = '"+item_name+"' ");
				if(StringUtils.isNotBlank(third_code)){
					sbcheck.append("  or third_code = '"+third_code+"'");
				}
				sbcheck.append(" ) ");
				List<JSONObject> sbchecklist = this.dao.query4Json(tenancyId, sbcheck.toString());
				
				if(null != sbchecklist && sbchecklist.size()>0){
					for (JSONObject checkresult : sbchecklist) {
						String name = checkresult.optString("item_name");
						String code3 = checkresult.optString("third_code");
						if(name.equals(item_name)){
							returnJson.put("success", false);
							returnJson.put("msg", "菜品名称重复,不能启用");
							result = returnJson.toString();
							break;
						}
						if(code3.equals(third_code)){
							returnJson.put("success", false);
							returnJson.put("msg", "第三方编码重复，不能启用");
							result = returnJson.toString();
							break;
						}
					}
				}else{
					sbcheck.setLength(0);
					sbcheck.append("update hq_item_info set valid_state = '1' where id = '"+rowid+"'");
					this.dao.execute(tenancyId, sbcheck.toString());
					returnJson.put("success", true);
					returnJson.put("msg", "启用成功");
					result = returnJson.toString();
				}
				
				break;
			case 33:
				
				try
				{

					StringBuilder isb33 = new StringBuilder("select id,class_item as text,class_item_code as code,0 as type from sys_dictionary where class_identifier_code = 'chanel'  and class_item_code='MD01'");
					List<ItemCombotree> listcp = (List<ItemCombotree>) this.dao.query(tenancyId, isb33.toString(), ItemCombotree.class);
					isb33.delete(0, isb33.length());

					isb33 = new StringBuilder("select a.id,a.itemclass_name as text,a.father_id,a.chanel as code,0 as type from hq_item_class  a where 1=1 and a.valid_state='" + Constant.BOOL_TRUE + "'");

					List<ItemCombotree> list2 = (List<ItemCombotree>) this.dao.query(tenancyId, isb33.toString(), ItemCombotree.class);

					StringBuilder sb333 = new StringBuilder();
					// 查询菜品详细

					sb333.append("select b.id,b.item_code as code,b.item_name as text,a.unit_name,b.item_class as fatherId,a.unit_name,round(a.standard_price,2) as standard_price,1 as type,a.id as item_unit_id  from hq_item_unit a left join hq_item_info b on a.item_id = b.id where a.item_id in(select c.id from hq_item_info c where 1=1 and c.valid_state='1' and c.is_combo='N' ) and  a.valid_state='1' ");
					// sb33.append("select h1.id,CONCAT(item_code,item_name)as text,item_class as fatherId,h2.unit_name,standard_price,1 as type,h2.id as item_unit_id from hq_item_info  h1 left join hq_item_unit h2 on h1.id=h2.item_id and h2.tenancy_id = '"
					// + tenancyId +
					// "' and h2.valid_state ='1' where  h1.tenancy_id = '"
					// + tenancyId + "' and h1.valid_state ='1'");

					List<ItemCombotree> list13 = (List<ItemCombotree>) this.dao.query(tenancyId, sb333.toString(), ItemCombotree.class);
				
					Map<Integer, ItemCombotree> map = new HashMap<Integer, ItemCombotree>();
					Map<Integer,List<ItemCombotree>> map2 = new HashMap<Integer,List<ItemCombotree>>();
					for(ItemCombotree icb: list13)
					{
						int father_id = icb.getFatherId();
						
						if(map2.containsKey(father_id))
						{
							map2.get(father_id).add(icb);
						}
						else
						{
							List<ItemCombotree> listn = new ArrayList<ItemCombotree>();
							listn.add(icb);
							map2.put(father_id, listn);
						}
					}
					// 第一层
					for (ItemCombotree jo2 : list2)
					{

						jo2.setChildren(new ArrayList<ItemCombotree>());
						map.put(jo2.getId(), jo2);
						if (jo2.getFatherId() != null && jo2.getFatherId() == 0)
						{
							for (ItemCombotree icb : listcp)
							{
								if (jo2.getCode().equals(icb.getCode()))
								{
									icb.getChildren().add(jo2);
								}
							}

						}
					}
					// 后面的层
					for (ItemCombotree jo3 : list2)
					{
						if (jo3.getFatherId() != null && jo3.getFatherId() != 0)
						{
							if (map.get(jo3.getFatherId()) != null)
							{
								if(map2.containsKey(jo3.getId()))
								{
									map.get(jo3.getId()).setChildren(map2.get(jo3.getId()));
								}
							
								map.get(jo3.getFatherId()).getChildren().add(map.get(jo3.getId()));
							}
						}
					}
					
					Iterator<ItemCombotree> it2 = listcp.iterator();
					StringBuilder ids = new StringBuilder("null");
					// 去掉非没有小类的大类的 如果是小类的话 拼id串
					while (it2.hasNext())
					{
						ItemCombotree toPo = it2.next();
						if (toPo.getChildren().size() == 0)
						{
							it2.remove();
						}
						else
						{
							ids.append("," + toPo.getId());
						}
					}
					if (ids.length() > 5)
					{
						ids.delete(0, 5);
					}
					result = com.tzx.framework.common.util.JsonUtils.list2json(listcp);
				

				}
				catch (Exception e)
				{

					e.printStackTrace();
				}

				break;	
			case 44:
				// 查询菜品渠道类别树状图
				try
				{
					if (jb.optInt("id") == 0)
					{
						StringBuilder sb44 = new StringBuilder("select id,class_item as text,class_item_code as code,0 as type from sys_dictionary where class_identifier_code = 'chanel'  and class_item_code='MD01'");
						List<ItemCombotree> listcp = (List<ItemCombotree>) this.dao.query(tenancyId, sb44.toString(), ItemCombotree.class);
						sb44.delete(0, sb44.length());

						sb44 = new StringBuilder("select a.id,a.itemclass_name as text,a.father_id,a.chanel as code,0 as type,(select case when count(b.id)>0 then 'closed' else '' end  from hq_item_info b where b.item_class = a.id and b.valid_state='" + Constant.BOOL_TRUE
								+ "' ) as state from hq_item_class  a where 1=1 and a.valid_state='" + Constant.BOOL_TRUE + "'");

						List<ItemCombotree> list2 = (List<ItemCombotree>) this.dao.query(tenancyId, sb44.toString(), ItemCombotree.class);
						Map<Integer, ItemCombotree> map = new HashMap<Integer, ItemCombotree>();
						// 第一层
						for (ItemCombotree jo2 : list2)
						{

							jo2.setChildren(new ArrayList<ItemCombotree>());
							map.put(jo2.getId(), jo2);
							if (jo2.getFatherId() != null && jo2.getFatherId() == 0)
							{
								for (ItemCombotree icb : listcp)
								{
									if (jo2.getCode().equals(icb.getCode()))
									{
										icb.getChildren().add(jo2);
									}
								}

							}
						}
						// 后面的层
						for (ItemCombotree jo3 : list2)
						{
							if (jo3.getFatherId() != null && jo3.getFatherId() != 0)
							{
								if (map.get(jo3.getFatherId()) != null)
								{
									map.get(jo3.getFatherId()).getChildren().add(map.get(jo3.getId()));
								}
							}
						}

						Iterator<ItemCombotree> it2 = listcp.iterator();
						StringBuilder ids = new StringBuilder("null");
						// 去掉非没有小类的大类的 如果是小类的话 拼id串
						while (it2.hasNext())
						{
							ItemCombotree toPo = it2.next();
							if (toPo.getChildren().size() == 0)
							{
								it2.remove();
							}
							else
							{
								ids.append("," + toPo.getId());
							}
						}
						if (ids.length() > 5)
						{
							ids.delete(0, 5);
						}
						result = com.tzx.framework.common.util.JsonUtils.list2json(listcp);
					}
					else
					{
						StringBuilder sb333 = new StringBuilder();
						// 查询菜品详细

						sb333.append("select b.id,b.item_code,b.item_name as text,a.unit_name,b.item_class as fatherId,a.unit_name,a.standard_price,1 as type,a.id as item_unit_id  from hq_item_unit a left join hq_item_info b on a.item_id = b.id where a.item_id in(select c.id from hq_item_info c where 1=1 and c.valid_state='1'  and c.item_class="
								+ jb.optInt("id") + ") and  a.valid_state='1' ");
						// sb33.append("select h1.id,CONCAT(item_code,item_name)as text,item_class as fatherId,h2.unit_name,standard_price,1 as type,h2.id as item_unit_id from hq_item_info  h1 left join hq_item_unit h2 on h1.id=h2.item_id and h2.tenancy_id = '"
						// + tenancyId +
						// "' and h2.valid_state ='1' where  h1.tenancy_id = '"
						// + tenancyId + "' and h1.valid_state ='1'");

						List<JSONObject> list13 = this.dao.query4Json(tenancyId, sb333.toString());
						result = com.tzx.framework.common.util.JsonUtils.list2json(list13);
					}

				}
				catch (Exception e)
				{

					e.printStackTrace();
				}

				break;
			case 45://折扣和时段特价中设置菜品时的树
				StringBuilder sb44 = new StringBuilder("select id,class_item as text,class_item_code as code,0 as type from sys_dictionary where class_identifier_code = 'chanel'  and class_item_code='MD01'");
				List<JSONObject> listcp = this.dao.query4Json(tenancyId, sb44.toString());
				sb44.delete(0, sb44.length());

				sb44 = new StringBuilder("select a.id,a.itemclass_name as text,a.father_id as fatherId,a.chanel as code,0 as type from hq_item_class  a where 1=1 and a.valid_state='" + Constant.BOOL_TRUE + "' and a.chanel = 'MD01' ORDER BY a.itemclass_code::INT ASC");
				List<JSONObject> list2 = this.dao.query4Json(tenancyId, sb44.toString());
				
				sb44.setLength(0);
				sb44.append("select b.id,b.item_code,b.item_name as text,a.unit_name,b.item_class as fatherId,a.unit_name,a.standard_price,1 as type,a.id as item_unit_id  from hq_item_unit a left join hq_item_info b on a.item_id = b.id where a.item_id in(select c.id from hq_item_info c where 1=1 and c.valid_state='1'  ) and  a.valid_state='1' ");
				if(jb.containsKey("item_unit_ids")&& StringUtils.isNotBlank(jb.optString("item_unit_ids"))){
					sb44.append("and a.id not in ("+jb.optString("item_unit_ids")+")");
				}
				sb44.append("  ORDER BY item_code ASC");
				List<JSONObject> list3 = this.dao.query4Json(tenancyId, sb44.toString());
				
				List<JSONObject> biglist = new ArrayList<JSONObject>();
				List<JSONObject> smalllist = new ArrayList<JSONObject>();
				
				for (JSONObject item : list2) {
					if(item.optInt("fatherid") == 0){
						biglist.add(item);
					}else{
						smalllist.add(item);
					}
				}
				
				for (JSONObject item : smalllist) {
					Integer fatherId = item.optInt("id");
					for(JSONObject iteminfo : list3){
						if(iteminfo.optInt("fatherid") == fatherId){
							 if(item.containsKey("children")){
								JSONArray arr = item.optJSONArray("children");
								arr.add(iteminfo);
								item.put("children", arr);
							 }else{
								 JSONArray arr = new JSONArray();
								 arr.add(iteminfo);
								 item.put("children", arr);
							 }
						}
					}
				}
				Iterator<JSONObject> sit = smalllist.iterator();
				while(sit.hasNext()){
					JSONObject next = sit.next();
					if(!next.containsKey("children") || next.optJSONArray("children").size() == 0){
						sit.remove();
					}
				}
				
				for (JSONObject item : biglist) {
					Integer fatherId = item.optInt("id");
					for(JSONObject iteminfo : smalllist){
						if(iteminfo.optInt("fatherid") == fatherId){
							 if(item.containsKey("children")){
								JSONArray arr = item.optJSONArray("children");
								arr.add(iteminfo);
								item.put("children", arr);
							 }else{
								 JSONArray arr = new JSONArray();
								 arr.add(iteminfo);
								 item.put("children", arr);
							 }
						}
					}
				}
				Iterator<JSONObject> bit = biglist.iterator();
				while(bit.hasNext()){
					JSONObject next = bit.next();
					if(!next.containsKey("children") || next.optJSONArray("children").size() == 0){
						bit.remove();
					}
				}
				
				
				JSONObject jsonObject = listcp.get(0);
				jsonObject.put("children", biglist);
			
				result = com.tzx.framework.common.util.JsonUtils.list2json(listcp);
				
				break;
				
			default:

				break;
		}

		return result;
	}
	
 
	@Override
	public String getChanelType(String tenancyId, Integer type, Object param)
	{
		JSONObject jb = (JSONObject) param;
		StringBuilder sb = new StringBuilder();
		// String result = "";
		switch (type)
		{
		// 查询数据字段
			case 0:
				if (jb.containsKey("code"))
				{
					if (jb.containsKey("y"))
					{
						if (jb.getInt("y") == 1)
						{
							sb.append("select class_item_code as id,class_item as text from sys_dictionary where class_identifier_code = '" + jb.get("code").toString() + "' and is_sys='Y' and valid_state='" + Constant.BOOL_TRUE + "' ");
						}
						else
						{
							sb.append("select class_item_code as id,class_item as text from sys_dictionary where class_identifier_code = '" + jb.get("code").toString() + "' and is_sys='Y'");							
						}

					}
					else
					{
						sb.append("select id,class_item as text from sys_dictionary where class_identifier_code = '" + jb.get("code").toString() + "' and is_sys='N' and valid_state='" + Constant.BOOL_TRUE + "' ");
					}

					try
					{
						List<BasicCombobox> list = (List<BasicCombobox>) this.dao.query(tenancyId, sb.toString(), BasicCombobox.class);
						return com.tzx.framework.common.util.JsonUtils.list2json(list);
					}
					catch (Exception e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				break;

			default:
				break;
		}
		return null;
	}

	@Override
	public String getOrgansTreeByConditios(String tenancyId, String organId, String organCode, JSONObject params, String conditions) throws Exception {

		StringBuilder sb = new StringBuilder();
		Organ organ = new Organ();;
		sb.append("select id,CONCAT(organ_code,org_full_name) as text,top_org_id as fatherId,level,organ_code as code,org_type as type,tenant_state as tenantstate,org_uuid as orguuid,"
				+ " manage_type as managetype , "
				+ " storelevel , "
				+ " arealevel , "
				+ " address "
				+ " from organ where 1=1 and valid_state='1'"); 
		if(null != conditions && StringUtils.isNotBlank(conditions)){
			sb.append("and id in("+conditions+")    ");
		}
		sb.append(" order by organ_code  ");
		
		List<Organ> list = (List<Organ>) this.dao.query(tenancyId, sb.toString(), Organ.class);
		List<Organ> list2 = new ArrayList<Organ>();
		Map<Integer, Organ> map = new HashMap<Integer, Organ>();
		for (Organ jo : list){
			map.put(jo.getId(), jo);
		}

		for (Organ jo1 : list){
			if (map.get(jo1.getFatherId()) == null){

				list2.add(jo1);
			}

			if (map.get(jo1.getFatherId()) != null){

				map.get(jo1.getFatherId()).getChildren().add(map.get(jo1.getId()));

			}
		}
		boolean flag = false;
		
		if("0".equals(organId))
		{
			flag = true;
		}
		else
		{
			String[] oids = conditions.split(",");
			for(String oid:oids)
			{
				if("0".equals(oid))
				{
					flag = true;
					break;
				}
			}
		}
		
		if (flag){
			organ.setChildren(list2);
			organ.setLevel(0);
			organ.setCode("");
			organ.setId(0);
			organ.setType("1");
			organ.setText(organ.getCode() + "总部");
			List<Organ> flist = new ArrayList<Organ>();
			flist.add(organ);	
			return JsonUtils.list2json(flist);
		
		}
		return JsonUtils.list2json(list2);
	}

	/**
	 *  根据账单查询账单的表头数据
	 * @param attribute
	 * @param p
	 * @return
	 * @throws Exception 
	 */
	public JSONObject getBillTitleData(String tenancyId, JSONObject p) throws Exception {
		JSONObject json =  new JSONObject();
		json.put("code","200");
		List<JSONObject> biglist = this.dao.query4Json(tenancyId,parameterUtils.parameterAutomaticCompletionUpgrade(tenancyId,p,EngineConstantArea.SELECT_BILLDATATITLE_PUBLICE));
		
		/*if(biglist.size()>0&&!biglist.get(0).getString("sql").equals(null)) {
			String sb = biglist.get(0).getString("sql");
			biglist =dao.query4Json(tenancyId, sb.toString());
		}else {
			json.put("code","300");
			json.put("msg","查询引擎表无数据");
			return json;
		}*/
		
		json.put("data",biglist);
		return json;
	}
	
	@Override
	public List<JSONObject> getCoupon(String tenancyID, JSONObject condition)throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		list = dao.query4Json(tenancyID,"select id, payment_name1 from payment_way where payment_name1 like '%贵宾券%' order by id");
		return list;
	}
	@Override
	public List<JSONObject> getItemDl(String tenancyID, JSONObject condition)throws Exception {
		StringBuffer sb = new StringBuffer();
		List<JSONObject> list = new ArrayList<JSONObject>();
		sb.append("select DISTINCT dlid as id,dlmc as text from bb_v_cplb");
		list = dao.query4Json(tenancyID,sb.toString());
		return list;
	}
	@Override
	public List<JSONObject> getItemXl(String tenancyID, JSONObject condition)throws Exception {
		StringBuffer sb = new StringBuffer();
		List<JSONObject> list = new ArrayList<JSONObject>();
		sb.append("select DISTINCT xlid as id,xlmc as text from bb_v_cplb");
		if(condition.containsKey("dlid")){
			sb.append(" where dlid in ("+condition.optString("dlid")+")");
		}
		list = dao.query4Json(tenancyID,sb.toString());
		return list;
	}
	@Override
	public List<JSONObject> getItemName(String tenancyID, JSONObject condition)throws Exception {
		StringBuffer sb = new StringBuffer();
		List<JSONObject> list = new ArrayList<JSONObject>();
		sb.append("select DISTINCT xmbh as id,xmmc as text from bb_v_cplb");
		if(condition.containsKey("xlid")){
			sb.append(" where xlid in ("+condition.optString("xlid")+")");
		}
		list = dao.query4Json(tenancyID,sb.toString());
		return list;
	}
	
	@Override
	public List<JSONObject> getReportTH(String tenancyID, JSONObject condition)throws Exception {
		StringBuffer sb = new StringBuffer();
		List<JSONObject> list = new ArrayList<JSONObject>();
		sb.append("select * from rpt_free where 1=1");
		if(condition.containsKey("report_num")){
			sb.append(" and report_num = '").append(condition.optString("report_num")).append("'");
		}
		if(condition.containsKey("inquiry_mode")){
			sb.append(" and inquiry_mode = ").append(condition.optString("inquiry_mode"));
		}
		sb.append(" order by place_sort");
		list = this.dao.query4Json(tenancyID, sb.toString());
		for (int i = 0; i < list.size(); i++) {
			JSONObject obj = list.get(i);
			obj.put("forbid_edit",obj.optBoolean("forbid_edit")==true?1:0);
			obj.put("forbid_sort",obj.optBoolean("forbid_sort")==true?1:0);
			obj.put("enabled",obj.optBoolean("enabled")==true?1:0);
			obj.put("is_sort",obj.optBoolean("is_sort")==true?1:0);
			obj.put("is_fixed",obj.optBoolean("is_fixed")==true?1:0);
		}
		return list;
	}
	
	//新增、修改
	@Override
	public Object addOrUpdTH(String tenancyID, String tableName,List<JSONObject> condition,String type) throws Exception {
		if(type.equals("add")){
			return this.dao.insertBatchIgnorCase(tenancyID, tableName, condition);
		}else{
			return this.dao.updateBatchIgnorCase(tenancyID, tableName, condition);
		}
	}
	
	
	/**
	 * 查询HBASE使用权限
	 * @param tenancyID
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public Boolean getHBASEPrivilege(String tenancyID, JSONObject condition) throws Exception {
		StringBuilder sb = new StringBuilder();
		sb.append("select status from sys_config_center where config_code='10104'");
		List<JSONObject> list = dao.query4Json(tenancyID, sb.toString());
		if(list.size()>0){
			String status = list.get(0).optString("status");
			if(status.equals("1")){
				return true;
			}else{
				return false;
			}
		}
		return false;
	}

	@Override
	public Boolean getHBASEPrivileges() throws Exception {
		return null;
	}

	@Override
	public String getChanelTypeByBrand(String tenancyId, JSONObject param) throws Exception {
		JSONObject jb = (JSONObject) param;
		StringBuilder sb = new StringBuilder();
			String result  = null;
			if(param.containsKey("fatherId")) {
				result = " SELECT hbi.id ,sd.class_item as text from hq_brand_info_channel hbi LEFT JOIN sys_dictionary sd on hbi.channel_id = sd.id where hbi.brand_info_id = "+param.optString("fatherId")+" and sd.class_identifier_code = 'chanel' and sd.is_sys='Y' and sd.valid_state='1' ";
				List<BasicCombobox> list = (List<BasicCombobox>) this.dao.query(tenancyId, result.toString(), BasicCombobox.class);
				return com.tzx.framework.common.util.JsonUtils.list2json(list);
			}else {
				return null; 
			}
	}

	@Override
	public String loadCategoryTreeByBrand(String tenancyId,  Object param) throws Exception {
		String result = "";
		JSONObject jb = (JSONObject) param;
		// 查询菜品渠道类别树状图
				try
				{
					StringBuilder sql = new StringBuilder();

					sql.append("select 0 as id,0 as father_id,0 as num,class_item_code as chanel,class_item_code as code ,class_item,class_item as itemclass_name,class_item as text from sys_dictionary where class_identifier_code = 'chanel'");
					
					// 有渠道的 通过渠道查询菜品类别
					if(jb.containsKey("chanel")) {
						sql.append(" and class_item_code ='"+jb.optString("chanel")+"'");
					}
					//sql.append(" and class_item_code ='MD01'");
					sql.append(" order by chanel");

					List<HqItemClass> listDict = (List<HqItemClass>) dao.query(tenancyId, sql.toString(), HqItemClass.class);

					sql.delete(0, sql.length());

					sql.append(" SELECT A . ID, A .chanel, A .father_id, A .itemclass_name, A .itemclass_code, A .itemclass_code AS code, ");
					sql.append(" ( SELECT COUNT (*) FROM hq_item_info info WHERE info.item_class = A . ID AND info.valid_state = '1' ");
					sql.append(" ) AS num, b.class_item AS class_item FROM  hq_item_class A LEFT JOIN hq_brand_info_channel hbic on hbic.id = a.hq_brand_info_channel_id");
					sql.append(" JOIN sys_dictionary b ON ( b.class_item_code = A .chanel AND b.class_identifier_code = 'chanel' ) ");
					sql.append(" WHERE 1 = 1 AND A .valid_state = '1' ");
					// 有渠道的 通过渠道查询菜品类别
					if(jb.containsKey("chanel") && !jb.optString("chanel").equals(null)   && !jb.optString("chanel").equals("")) {
						sql.append(" AND A .chanel = '"+jb.optString("chanel")+"' ");
					}
					// 通过品牌的id查询
					if(jb.containsKey("fatherId") && !jb.optString("fatherId").equals(null)  && !jb.optString("fatherId").equals("") ) {
						sql.append(" and hbic.brand_info_id in ("+jb.optString("fatherId")+") ");
					}
					sql.append(" ORDER BY itemclass_code; ");

					List<HqItemClass> listRes = (List<HqItemClass>) dao.query(tenancyId, sql.toString(), HqItemClass.class);

					Map<Object, HqItemClass> map = new HashMap<Object, HqItemClass>();

					for (HqItemClass json : listRes)
					{
						json.setChildren(new ArrayList<HqItemClass>());
						map.put(json.getId(), json);
					}
					// 设置上下级关系
					for (HqItemClass json : listRes)
					{
						String itemclassCode = json.getItemclassCode();
						if ("".equals(itemclassCode))
						{
							json.setText(json.getItemclassName());
						}
						else
						{
							json.setText("(" + json.getItemclassCode() + ")" + json.getItemclassName());
						}

						if (map.containsKey(json.getFatherId()))
						{
							List<HqItemClass> l1 = map.get(json.getFatherId()).getChildren();
							l1.add(json);
						}
					}

					for (HqItemClass gO : listDict)
					{
						gO.setChildren(new ArrayList<HqItemClass>());
						for (HqItemClass j1 : listRes)
						{

							if (j1.getFatherId().equals(0) && j1.getChanel().equals(gO.getChanel()))
							{

								List<HqItemClass> l2 = gO.getChildren();
								l2.add(j1);
							}

						}

					}

					result = com.tzx.framework.common.util.JsonUtils.list2json(listDict);
				}
				catch (Exception e)
				{

					e.printStackTrace();
				}
		return result;
	}
	
	
	
	public int[] updateBatchIgnorCase(String tenantId, String tableName, final List<JSONObject> jsonList) throws Exception
	{
		StringBuffer sb = new StringBuffer();
		int[] result = new int[jsonList.size()];
		String sql =GenericDaoTableInfo.updateBatchById(tenantId, tableName, dao, jsonList);
		if(sql.length()>0){
			String[] newsSql = sql.split(";");
			for(int i = 0;i<newsSql.length;i++){
				  String news = newsSql[i]+";";
				  String newsReplace = news.replace("'", "");
				  String rate = newsReplace.replace("click_record=", "click_record=click_record::int+");
				  sb.append(rate);
			   }
		}
		this.dao.execute(tenantId, sb.toString());
		for(int i=0;i<jsonList.size();i++)
		{
			result[i]=1;
		}
		return result;
	}
	
	

	@Override
	public int[] updNewsClickRate(String tenancyID, String tableName,List<JSONObject> array) throws Exception {
		return updateBatchIgnorCase(tenancyID, tableName, array);
	}

	@Override
	public JSONObject getPosLimitReportQueryArea(String tenancyID, JSONObject condition)throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject result = new JSONObject();
		list = dao.query4Json(tenancyID,"select para_value from sys_parameter where para_code = 'limit_report_query_area' and store_id = 0");
		result.put("list", list);
		return result;
	}

	@Override
	public JSONArray getPaymentRange(String tenancyID, JSONObject condition)throws Exception {
		return dao.query4JSONArray(tenancyID,"SELECT jzid as id,name as text FROM pos_bill_payment2 WHERE report_date BETWEEN  to_date("+condition.optString("begin_date")+",'yyyy-mm-dd')  and to_date("+condition.optString("end_date")+",'yyyy-mm-dd') GROUP BY name,jzid");
	}
	 
}
